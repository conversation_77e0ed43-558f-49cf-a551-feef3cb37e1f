package com.bizark.op.common.core.page;

import com.bizark.op.common.util.ServletUtils;

/**
 * 表格数据处理 返回前端需要的格式 按照ehenglin分页返回方式返回数据
 *
 * <AUTHOR>
 */
public class TableSupportInfo
{
    /**
     * 当前记录起始索引
     */
    public static final String PAGE = "page";

    /**
     * 每页显示记录数
     */
    public static final String ROWS = "rows";

    /**
     * 排序列
     */
    public static final String SIDX = "sidx";

    /**
     * 排序的方向 "desc" 或者 "asc".
     */
    public static final String SORD = "sord";

    /**
     * 封装分页对象
     */
    public static PageInfoDomain getPageInfoDomain()
    {
        PageInfoDomain pageInfoDomain = new PageInfoDomain();
        pageInfoDomain.setPage(ServletUtils.getParameterToInt(PAGE));
        pageInfoDomain.setRows(ServletUtils.getParameterToInt(ROWS));
        pageInfoDomain.setSidx(ServletUtils.getParameter(SIDX));
        pageInfoDomain.setSord(ServletUtils.getParameter(SORD));
        return pageInfoDomain;
    }

    public static PageInfoDomain buildPageRequest()
    {
        return getPageInfoDomain();
    }
}
