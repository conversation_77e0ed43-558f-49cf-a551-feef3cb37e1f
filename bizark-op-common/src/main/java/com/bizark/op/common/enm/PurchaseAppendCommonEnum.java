package com.bizark.op.common.enm;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 物流模块新增需求枚举
 * <AUTHOR>
 * @create: 2024-01-15 11:18
 */

public class PurchaseAppendCommonEnum {
    /**
     * 验货人/验货人排期状态
     *
     * <AUTHOR>
     * @date 2024-02-23 10:30:20
     */
    @Getter
    public enum InspectionUserStatusEnum {

        /**
         * 等于
         */
        ABANDONED(0,  " 启用 "),

        /**
         *
         */
        DISABLED(1,  " 禁用 "),

        SUCCESS(2,  " 已完成 "),

        UNKNOWN(-1,  "未知类型")

        ;

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        InspectionUserStatusEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static List<Integer> getStatusList(){
            List<Integer> result = new ArrayList<>();
            result.add(ABANDONED.getValue());
            result.add(SUCCESS.getValue());
            return result;
        }

        public static PurchaseAppendCommonEnum.InspectionUserStatusEnum getByValue(Integer value) {
            for (PurchaseAppendCommonEnum.InspectionUserStatusEnum e : PurchaseAppendCommonEnum.InspectionUserStatusEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }


    /**
     * 验货订单状态
     * <AUTHOR>
     * @date 2024-02-26 10:50:10
     */
    @Getter
    public enum InspectionOrderStatusEnum {

        /**
         * 待申请
         */
        PENDING_APPLICATION(0,  "未申请"),

        /**
         *已申请
         */
        APPLIED_FOR(1,  "已申请"),
        /**
         * 验货中
         */
        DURING_INSPECTION(2,  "验货中"),
        /**
         * 已完成
         */
        COMPLETED(3,  "验货合格"),

        /**
         * 验货不合格
         */
        UNQUALIFIED_INSPECTION(9,  "验货不合格"),
        UNKNOWN(-1,  "未知类型")

        ;

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        InspectionOrderStatusEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static PurchaseAppendCommonEnum.InspectionOrderStatusEnum getByValue(Integer value) {
            for (PurchaseAppendCommonEnum.InspectionOrderStatusEnum e : PurchaseAppendCommonEnum.InspectionOrderStatusEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }


        public static PurchaseAppendCommonEnum.InspectionOrderStatusEnum getByDescribe(String describe) {
            for (PurchaseAppendCommonEnum.InspectionOrderStatusEnum e : PurchaseAppendCommonEnum.InspectionOrderStatusEnum.values()) {
                if (e.getDescribe().equals(describe)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }

    /**
     * 验货结果
     * <AUTHOR>
     * @date 2024-02-26 10:50:10
     */
    @Getter
    public enum InspectionOrderResultEnum {

        /**
         * 默认
         */
        DEFAULT(0,  " 默认 "),


        /**
         * 合格
         */
        QUALIFIED(1,  " 验货合格 "),

        /**
         *已申请
         */
        UNQUALIFIED(9,  " 验货不合格 "),

        UNKNOWN(-1,  "未知结果")

        ;

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        InspectionOrderResultEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static PurchaseAppendCommonEnum.InspectionOrderResultEnum getByValue(Integer value) {
            for (PurchaseAppendCommonEnum.InspectionOrderResultEnum e : PurchaseAppendCommonEnum.InspectionOrderResultEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }

    /**
     * 验货申请状态
     * <AUTHOR>
     * @date 2024-02-26 10:50:10
     */
    @Getter
    public enum InspectionApproveStatusEnum {

        /**
         * 待审核
         */
        PENDING_REVIEW(0,  " 待分配 "),

        /**
         *待验货
         */
        GOODS_TO_BE_INSPECTED(1,  " 待验货 "),

        /**
         * 已完成
         */
        COMPLETED(2,  " 已完成 "),
        /**
         * 已撤销
         */
        REVOKE(3,  " 已撤销 "),
        /**
         * 已驳回
         */
        REJECTED(9,  " 已驳回 "),
        UNKNOWN(-1,  "未知类型")

        ;

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        InspectionApproveStatusEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static PurchaseAppendCommonEnum.InspectionApproveStatusEnum getByValue(Integer value) {
            for (PurchaseAppendCommonEnum.InspectionApproveStatusEnum e : PurchaseAppendCommonEnum.InspectionApproveStatusEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }

    /**
     * 时间范围枚举
     *
     * <AUTHOR>
     * @date 2024-02-23 10:27:36
     */
    @Getter
    public enum InspectionDateRangeTypeEnum {

        /**
         * 本周
         */
        CURRENT_WEEK(0,  "本周"),
        /**
         * 下周
         */
        NEXT_WEEK(1,  "下周"),
        CURRENT_MONTH(2,  "本月"),
        UNKNOWN(-1,  "  ")
        ;

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        InspectionDateRangeTypeEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static PurchaseAppendCommonEnum.InspectionDateRangeTypeEnum getByValue(Integer value) {
            for (PurchaseAppendCommonEnum.InspectionDateRangeTypeEnum e : PurchaseAppendCommonEnum.InspectionDateRangeTypeEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }


    /**
     * 验货人校验类型
     *
     * <AUTHOR>
     * @date 2024-02-23 10:30:20
     */
    @Getter
    public enum InspectionUserCheckEnum {

        /**
         * 等于
         */
        ADD_OR_UPDATE(0, " 新增/编辑校验 "),

        /**
         *
         */
        DELETE(1, " 删除校验 "),
        UNKNOWN(-1, "未知类型");

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;


        InspectionUserCheckEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static PurchaseAppendCommonEnum.InspectionUserCheckEnum getByValue(Integer value) {
            for (PurchaseAppendCommonEnum.InspectionUserCheckEnum e : PurchaseAppendCommonEnum.InspectionUserCheckEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }

    /**
     * 采购消息发布订阅类型
     * <AUTHOR>
     * @date 2024-03-01 11:31:44
     */

    @Getter
    public enum InspectionPurchaseEventEnum {

            /**
             * 等于
             */
            ADD_OR_UPDATE(0,  " 下单消息 "),

            /**
             *
             */
            DELETE(9,  " 作废消息 "),
            UNKNOWN(-1,  "未知类型")

            ;

            /**
             * 标的值
             */
            private final Integer value;

            /**
             * 描述
             */
            private final String describe;



            InspectionPurchaseEventEnum(Integer value, String describe) {
                this.value = value;
                this.describe = describe;
            }

        public static PurchaseAppendCommonEnum.InspectionPurchaseEventEnum getByValue(Integer value) {
                for (PurchaseAppendCommonEnum.InspectionPurchaseEventEnum e : PurchaseAppendCommonEnum.InspectionPurchaseEventEnum.values()) {
                    if (e.getValue().equals(value)) {
                        return e;
                    }
                }
                return UNKNOWN;
            }
    }

    /**
     * 验货来源枚举
     *
     * <AUTHOR>
     * @date 2024-02-23 10:27:36
     */
    @Getter
    public enum PurchaseAuthorityCheckEnum {

        /**
         * 货源地
         */
        MANUFACTURER(0,  "生产商调用"),
        /**
         * 生产商小程序
         */
        MANUFACTURER_MINI_PROGRAM(1,  "生产商小程序"),
        ROOT(2,  "管理端"),
        UNKNOWN(-1,  "  ")
                ;

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        PurchaseAuthorityCheckEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static PurchaseAppendCommonEnum.PurchaseAuthorityCheckEnum getByValue(Integer value) {
            for (PurchaseAppendCommonEnum.PurchaseAuthorityCheckEnum e : PurchaseAppendCommonEnum.PurchaseAuthorityCheckEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }

    /**
     * 通用校验状态枚举
     *
     * <AUTHOR>
     * @date 2024-02-23 10:30:20
     */
    @Getter
    public enum InspectionCommonCheckEnum {

        /**
         * 等于
         */
        NO(0,   "否"),

        /**
         *
         */
        YES(1,  " 是 "),
        UNKNOWN(-1,  "未知类型")

        ;

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        InspectionCommonCheckEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static PurchaseAppendCommonEnum.InspectionCommonCheckEnum getByValue(Integer value) {
            for (PurchaseAppendCommonEnum.InspectionCommonCheckEnum e : PurchaseAppendCommonEnum.InspectionCommonCheckEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN;
        }
    }


}
