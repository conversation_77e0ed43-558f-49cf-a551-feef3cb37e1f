package com.bizark.op.common.enm;

import lombok.Getter;

/**
 * 操作类型枚举(用于操作日志类型)
 *
 * <AUTHOR>
 * @date 2023/11/08
 */
public class OperationTypeEnum {

    /**
     * 发运单操作类型
     *
     * <AUTHOR>
     * @date 2023/11/08
     */
    @Getter
    public enum ShippingOrderEnum {
        /**
         * 创建发运单
         */
        CREATE(0, "创建发运单;", ""),
        /**
         * 提交订舱
         */
        BOOKING(1, "提交订舱;", ""),
        CONFIRM_CONTAINER_LOADING(2, "确认装柜，物流状态变更为  已装柜，发运单状态为    待发运;", ""),
        UPDATE_SAVE(4, "更新发运单;", ""),
        /**
         * 确认订舱
         */
        CONFIRM_CABIN_SPACE(1, "头程物流单号为  ", "确认舱位，舱位状态变更为    分配订舱;"),
        /**
         * 移除发运单
         */
        REMOVE_WAYBILL(2, "头程物流单号为  ", "移除发运单,舱位状态变更为    未提交"),
        /**
         * 待开船状态下关联发运单
         */
        WAIT_ASSOCIATED_SHIPPING_ORDER(3, "头程物流单号为 ", "待开船状态下关联发运单，舱位状态变更为  分配舱位;"),
        /**
         * 关联发运单
         */
        ASSOCIATED_SHIPPING_ORDER(5, "头程物流单号为 ", "关联发运单，舱位状态变更为  已订舱;"),
        UPDATE_ASSOCIATED(8, "头程物流单号为 ", "关联发运单，舱位状态变更为  分配舱位;"),
        /**
         * 待开船状态下移除发运单
         */
        WAIT_REMOVE_WAYBILL(4, "头程物流单号为 ", "待开船状态下移除发运单，舱位状态变更为  未提交;"),

        /**
         * 确认开船
         */
        CONFIRM_DEPARTURE(7, "确认开船，物流状态变更为  已开船，发运单状态为   已发运;", "");

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 日志内容开头
         */
        private final String describeStart;

        /**
         * 日志内容结束
         */
        private final String describeEnd;

        ShippingOrderEnum(Integer value, String describeStart, String describeEnd) {
            this.value = value;
            this.describeStart = describeStart;
            this.describeEnd = describeEnd;
        }

        public static ShippingOrderEnum getByValue(Integer value) {
            for (ShippingOrderEnum e : ShippingOrderEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return null;
        }
    }

    /**
     * 头程物流单操作类型
     *
     * <AUTHOR>
     * @date 2023/11/08
     */
    @Getter
    public enum LogisticsOrderHeadEnum {
        /**
         * 创建头程物流单
         */
        CREATE(0, "创建头程物流单;", ""),
        /**
         * 确认订舱
         */
        CONFIRM_CABIN_SPACE(1, "确认舱位，舱位状态变更为    待开船;", ""),
        /**
         * 移除发运单
         */
        REMOVE_WAYBILL(2, "移除发运单,发运单号为:", ""),
        /**
         * 待开船状态下关联发运单
         */
        WAIT_ASSOCIATED_SHIPPING_ORDER(3, "待开船状态下关联发运单，发运单号为:", ""),
        /**
         * 关联发运单
         */
        ASSOCIATED_SHIPPING_ORDER(9, "关联发运单，发运单号为:", ""),
        /**
         * 确认费用
         */
        CONFIRM_EXPENSES(5, "确认费用,费用状态变更为   已确认;", ""),
        /**
         * 自动更新实际开船日期
         */
        AUTOMATIC_UPDATE_ACTUAL_DEPARTURE_TIME(6, "更新实际开船日期为: ", ""),

        /**
         * 开船
         */
        SETTING_SAIL(7, "确认开船,单据状态变为待到港,同步更新发运单开船节点; ", ""),

        /**
         * 修改舱位
         */
        UPDATE_CABIN_SPACE(8, "修改舱位,提运单号: ", ""),
        /**
         * 待开船状态下移除发运单
         */
        WAIT_REMOVE_WAYBILL(4, "待开船状态下移除发运单，发运单号为:", "");

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 日志内容开头
         */
        private final String describeStart;

        /**
         * 日志内容结束
         */
        private final String describeEnd;


        LogisticsOrderHeadEnum(Integer value, String describeStart, String describeEnd) {
            this.value = value;
            this.describeStart = describeStart;
            this.describeEnd = describeEnd;
        }

        public static LogisticsOrderHeadEnum getByValue(Integer value) {
            for (LogisticsOrderHeadEnum e : LogisticsOrderHeadEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return null;
        }
    }

    /**
     * 头程物流单修改时间节点类型
     *
     * <AUTHOR>
     * @date 2023/11/08
     */
    @Getter
    public enum LogisticsOrderHeadSubmitTypeEnum {

        /**
         * 未知类型
         */
        UNKNOWN_TYPE(-1,  "未知类型!"),
        /**
         * 待订舱
         */
        PENDING_BOOKING(1,  "待订舱"),
        /**
         * 待开船
         */
        TO_BE_SET_SAIL(2,  "待开船"),

        /**
         * 待到港
         */
        WAITING_FOR_ARRIVAL_AT_THE_PORT(3, "待到港"),
        /**
         * 待派送
         */
        TO_BE_DELIVERED(4, "待派送"),
        /**
         * 已妥投
         */
        SUCCESSFULLY_SUBMITTED(5, "已妥投");

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        LogisticsOrderHeadSubmitTypeEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static LogisticsOrderHeadSubmitTypeEnum getByValue(Integer value) {
            for (LogisticsOrderHeadSubmitTypeEnum e : LogisticsOrderHeadSubmitTypeEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN_TYPE;
        }
    }

    /**
     * 加急状态提交节点枚举
     *
     * <AUTHOR>
     * @date 2023-12-11 16:28:45
     */
    @Getter
    public enum UrgentSubmitTypeEnum {

        /**
         * 采购订单
         */
        PURCHASE_ORDER(0,  "采购订单"),
        /**
         * 发运单
         */
        SHIPPING_ORDER(1,  "发运单"),
        /**
         * 头程物流单
         */
        HEAD_ORDER(2,  "头程物流单"),

        /**
         * 入库计划单
         */
        PLAN_ORDER(3, "入库计划单"),
        /**
         * 未知类型
         */
        UNKNOWN_TYPE(-1, "未知类型");

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        UrgentSubmitTypeEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static UrgentSubmitTypeEnum getByValue(Integer value) {
            for (UrgentSubmitTypeEnum e : UrgentSubmitTypeEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN_TYPE;
        }
    }

    /**
     * 修改为完成状态提交节点枚举
     *
     * <AUTHOR>
     * @date 2023-12-11 16:28:45
     */
    @Getter
    public enum UpdateToCompletionStatusEnum {

        /**
         * 采购订单
         */
        PURCHASE_ORDER(1,  "采购订单"),
        /**
         * 收货单
         */
        DELIVERY_NOTE(2,  "收货单"),
        /**
         * 调拨单
         */
        TRANSFER_ORDER(3,  "调拨单"),
        /**
         * 未知类型
         */
        UNKNOWN_TYPE(-1, "未知类型");

        /**
         * 标的值
         */
        private final Integer value;

        /**
         * 描述
         */
        private final String describe;



        UpdateToCompletionStatusEnum(Integer value, String describe) {
            this.value = value;
            this.describe = describe;
        }

        public static UpdateToCompletionStatusEnum getByValue(Integer value) {
            for (UpdateToCompletionStatusEnum e : UpdateToCompletionStatusEnum.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return UNKNOWN_TYPE;
        }
    }
}
