package com.bizark.op.common.core.page;

import com.bizark.op.common.util.StringUtils;

/**
 * 分页数据
 *
 * <AUTHOR>
 */
public class PageInfoDomain
{
    /** 当前记录起始索引 */
    private Integer page;

    /** 每页显示记录数 */
    private Integer rows;

    /** 排序列 */
    private String sidx;

    /** 排序的方向desc或者asc */
    private String sord = "asc";

    public String getOrderBy()
    {
        if (StringUtils.isEmpty(sidx))
        {
            return "";
        }
        return StringUtils.toUnderScoreCase(sidx) + " " + sord;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public String getSidx() {
        return sidx;
    }

    public void setSidx(String sidx) {
        this.sidx = sidx;
    }

    public String getSord() {
        return sord;
    }

    public void setSord(String sord) {
        this.sord = sord;
    }
}
