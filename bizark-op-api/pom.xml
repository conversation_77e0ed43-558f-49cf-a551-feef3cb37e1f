<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>bizark-op</artifactId>
        <groupId>com.bizark</groupId>
        <version>1.0.4-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bizark-op-api</artifactId>
    <packaging>jar</packaging>

    <name>bizark-op-api</name>
    <url>http://maven.apache.org</url>

    <dependencies>
        <dependency>
            <groupId>bizark.amz.vendor</groupId>
            <artifactId>bizark-amz-vendor</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>bizark.amz</groupId>
            <artifactId>bizark-amz</artifactId>
            <version>1.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-usercenter-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- https://mvnrepository.com/artifact/commons-httpclient/commons-httpclient -->
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
        </dependency>



        <!-- COMMON -->
        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-op-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--&lt;!&ndash; DAO&ndash;&gt;-->
        <!--<dependency>-->
        <!--<groupId>com.bizark</groupId>-->
        <!--<artifactId>bizark-op-dao</artifactId>-->
        <!--<version>${project.version}</version>-->
        <!--</dependency>-->

        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- COMMON -->
        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-boss-common</artifactId>
            <version>1.0.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-activiti-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.mybatis</groupId>-->
<!--            <artifactId>mybatis-typehandlers-jsr310</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.apache.geronimo.specs</groupId>
            <artifactId>geronimo-jms_1.1_spec</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.springframework.data/spring-data-elasticsearch -->
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-elasticsearch</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-boss-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-activiti-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-report-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-report-common</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-ad-api</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-ad-common</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
        </dependency>

        <dependency>
            <groupId>io.gsonfire</groupId>
            <artifactId>gson-fire</artifactId>
            <version>1.8.0</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp</groupId>
            <artifactId>okhttp</artifactId>
            <version>2.7.5</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp</groupId>
            <artifactId>logging-interceptor</artifactId>
            <version>2.7.5</version>
        </dependency>

        <!--amazon-->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-signer</artifactId>
            <version>1.11.610</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-sts</artifactId>
            <version>1.11.236</version>
        </dependency>

        <dependency>
            <groupId>org.threeten</groupId>
            <artifactId>threetenbp</artifactId>
            <version>1.3.5</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.smartnews</groupId>
                <artifactId>maven-jpa-entity-generator-plugin</artifactId>
                <version>${maven-jpa-entity-generator-plugin.version}</version>
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>${mysql-connector.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>
</project>
