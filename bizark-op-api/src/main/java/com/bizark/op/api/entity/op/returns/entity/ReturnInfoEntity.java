package com.bizark.op.api.entity.op.returns.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bizark.op.api.enm.refund.OrderRefundLogisticsStatusEnum;
import com.bizark.op.common.annotation.DictConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 退货信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Data
@Accessors(chain = true)
@TableName(value = "return_info",excludeProperty = {"returnReasonCategoryName"})
public class ReturnInfoEntity extends ReturnInfoExtendEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 组织ID
     */
    @TableField("organization_id")
    private Integer organizationId;

    /**
     * 渠道
     */
    @TableField("channel")
    private String channel;

    /**
     * 店铺
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 店铺id
     */
    @TableField("shop_id")
    private Integer shopId;

    /**
     * 退货日期
     */
    @TableField("return_date")
    private LocalDateTime returnDate;

    /**
     * 退货日期
     */
    @TableField(exist = false)
    private Date returnDateDate;

    /**
     * 退货状态
     */
    @TableField("return_status")
    private String returnStatus;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 订单标识 0:FBA 1:FBM
     */
    @TableField("order_sign")
    private Integer orderSign;

    /**
     * 订单日期
     */
    @TableField("order_date")
    private LocalDateTime orderDate;

    /**
     * 订单日期 date
     */
    @TableField(exist = false)
    private Date orderDateDate;

    /**
     * sellerSku
     */
    @TableField("seller_sku")
    private String sellerSku;

    /**
     * sku
     */
    @TableField("sku")
    private String sku;


    /**
     * 型号
     */
    @TableField(exist = false)
    private String productMidModel;

    /**
     * asin
     */
    @TableField("asin")
    private String asin;

    /**
     * 运营id
     */
    @TableField("operate_id")
    private Integer operateId;

    /**
     * 运营名称
     */
    @TableField("operate_name")
    private String operateName;

    /**
     * 退货数量
     */
    @TableField("return_num")
    private Integer returnNum;

    /**
     * 币种
     */
    @TableField("currency")
    private String currency;

    /**
     * 退款金额
     */
    @TableField("return_amount")
    private BigDecimal returnAmount;

    /**
     * RMA
     */
    @TableField("rma")
    private String rma;

    /**
     * 行明细ID
     */
    @TableField("channel_order_item_id")
    private String channelOrderItemId;

    /**
     * Return ID
     */
    @TableField("return_id")
    private String returnId;

    /**
     * 退货原因
     */
    @TableField("return_reason")
    private String returnReason;

    /**
     * 退货原因分类
     */
    @TableField("return_reason_category")
    private String returnReasonCategory;

    /**
     * 退货原因分类名称
     */
    private String returnReasonCategoryName;

    /**
     * 订单金额
     */

    @TableField("order_amount")
    private BigDecimal orderAmount;

    /**
     * 订单数量
     */
    @TableField("order_num")
    private Integer orderNum;

    /**
     * Shipment Request ID
     */
    @TableField("shipment_request_id")
    private String shipmentRequestId;

    /**
     * 回程承运人
     */
    @TableField("return_carrier")
    private String returnCarrier;

    /**
     * 跟踪号
     */
    @TableField("track_no")
    private String trackNo;

    /**
     * 物流状态
     */
    @TableField("logistics_status")
    private String logisticsStatus;

    /**
     * 发票号码
     */
    @TableField("invoice_no")
    private String invoiceNo;

    /**
     * 发票日期
     */
    @TableField("invoice_date")
    private LocalDateTime invoiceDate;

    /**
     * 发票日期
     */
    @TableField(exist = false)
    private Date invoiceDateDate;
    /**
     * 退货计划单号
     */
    @TableField("return_plan_no")
    private String returnPlanNo;

    /**
     * 退货入库单号
     */
    @TableField("return_inbound_no")
    private String returnInboundNo;

    /**
     * 退货送达日期
     */
    @TableField("return_arrive_date")
    private LocalDateTime returnArriveDate;


    /**
     * 退货送达日期
     */
    @TableField(exist = false)
    private Date returnArriveDateDate;

    /**
     * 收货数量
     */
    @TableField("receipt_num")
    private Integer receiptNum;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 工单编号
     */
    @TableField("ticket_no")
    private String ticketNo;

    /**
     * 工单状态
     */
    @TableField("ticket_status")
    @DictConvert(dict = "ticket_status", target = "ticketStatus")
    private String ticketStatus;

    /**
     * FNSKU
     */
    @TableField("fnsku")
    private String fnsku;

    /**
     * 产品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 库存属性
     */
    @TableField("inventory_property")
    private String inventoryProperty;

    /**
     * FULFILLMENT_CENTER_ID
     */
    @TableField("fulfillment_center_id")
    private String fulfillmentCenterId;


    /**
     * 客户评论
     */
    @TableField("customer_comments")
    private String customerComments;

    /**
     * 退货原始用户描述
     */
    @TableField("original_customer_comments")
    private String originalCustomerComments;

    /**
     * walmart退货单据行
     */
    @TableField("return_order_line_number")
    private Integer returnOrderLineNumber;

    /**
     * 创建者id
     */
    @TableField("created_by")
    private Integer createdBy;

    /**
     * 创建者名称
     */
    @TableField("created_name")
    private String createdName;

    /**
     * 创建时间戳
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新者ID
     */
    @TableField("updated_by")
    private Integer updatedBy;

    /**
     * 更新者名称
     */
    @TableField("updated_name")
    private String updatedName;

    /**
     * 更新时间戳
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 删除者ID
     */
    @TableField("disabled_by")
    private Integer disabledBy;

    /**
     * 删除者名称
     */
    @TableField("disabled_name")
    private String disabledName;

    /**
     * 删除标识 0:未删除 1:删除
     */
    @TableField("disabled_at")
    private Integer disabledAt;



    /**
     * 单号有效状态 0 未创建物流记录  1.已创建物流记录
     */
    @TableField("tracking_id_valid_status")
    private Integer trackingIdValidStatus;


    /**
     * 配送状态 0:unkown、1:pre_transit、2:in_transit、3:out_for_delivery、4:delivered、5:available_for_pickup、6:return_to_sender、7:failure、8:cancelled、9:error
     */
    @TableField("tracking_status")
    private Integer trackingStatus;

    /**
     * 退货状态 0退货中 1已退货 -1拒绝退货
     */
    @TableField("return_status_common")
    private Integer returnStatusCommon;

    /**
     * 分类id
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 分类名字
     */
    @TableField("category_name")
    private String categoryName;

    /**
     * 实际退货sku
     */
    private String acSku;

    /**
     * 实际退货sku 数量
     */
    private Integer acSkuReturnNum;

    /**
     * 实际退货数量（erpSKu退货数量）
     */
    private String acSkuReturnNumStr;




    /**
     * @description: 封装物流状态
     * @author: Moore
     * @date: 2024/8/2 11:42
     * @param
     * @return: java.lang.String
    **/
    public String getLogisticsStatus() {
        if (this.getLogisticsStatusCode() != null) {
            return OrderRefundLogisticsStatusEnum.getDesc(this.getLogisticsStatusCode());
        }
        return logisticsStatus;
    }


    /**
     * Description: 对应的平台退货状态
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/8/19
     */
    @TableField("business_status")
    private Integer businessStatus;

    /**
     * 发货跟踪号
     */
    @TableField("shipment_track_no")
    private String shipmentTrackNo;

    /**
     * 发货方式
     */
    @TableField("shipping_method_code")
    private String shippingMethodCode;



    /**
     * 面单来源   0 平台 1商家
     */
    @TableField("bill_source")
    private Integer billSource;


    /**
     * 发货时间
     */
    @TableField("shipment_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shipmentDate;

    /**
     * 售后来源
     */
    private String applicantScene;


    /**
     *TK SELLERSKU对应SKU_ID
     */
    @TableField(exist = false)
    private String skuId;

    /**
     *TK returnType (REFUND,REPLACEMENT,RETURN_AND_REFUND)
     */

    private String returnType;



}
