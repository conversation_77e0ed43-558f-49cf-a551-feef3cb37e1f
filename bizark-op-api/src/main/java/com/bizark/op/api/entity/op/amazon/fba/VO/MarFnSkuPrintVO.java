package com.bizark.op.api.entity.op.amazon.fba.VO;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> @ClassName MarFnSkuPrintVO
 * @description: TODO
 * @date 2025年08月27日
 */
@Data
public class MarFnSkuPrintVO {

    private String commonBusinessId;


    private List<SellerSkuBean> itms;


    public static class SellerSkuBean {


        @JsonIgnore
        private String shipmentConfirmationId;
        @JsonIgnore
        private String inboundPlanId;
        @JsonIgnore
        private Long taskId;

        private String erpSku;
        private String imageUrl;
        private String sellerSku; //amz为MKSKU
        private String fnSku;//
        private String asin; //商品ID
        private String productName; //品名
        private String title;//产品标题
        private Integer applyNum;//数量
        private String shopName; //店铺名
        private String productMidModel; //型号


        public Long getTaskId() {
            return taskId;
        }

        public void setTaskId(Long taskId) {
            this.taskId = taskId;
        }

        public String getShipmentConfirmationId() {
            return shipmentConfirmationId;
        }

        public void setShipmentConfirmationId(String shipmentConfirmationId) {
            this.shipmentConfirmationId = shipmentConfirmationId;
        }

        public String getInboundPlanId() {
            return inboundPlanId;
        }

        public void setInboundPlanId(String inboundPlanId) {
            this.inboundPlanId = inboundPlanId;
        }

        public String getErpSku() {
            return erpSku;
        }

        public void setErpSku(String erpSku) {
            this.erpSku = erpSku;
        }

        public String getImageUrl() {
            return imageUrl;
        }

        public void setImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        public String getSellerSku() {
            return sellerSku;
        }

        public void setSellerSku(String sellerSku) {
            this.sellerSku = sellerSku;
        }

        public String getFnSku() {
            return fnSku;
        }

        public void setFnSku(String fnSku) {
            this.fnSku = fnSku;
        }

        public String getAsin() {
            return asin;
        }

        public void setAsin(String asin) {
            this.asin = asin;
        }

        public String getProductName() {
            return productName;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public Integer getApplyNum() {
            return applyNum;
        }

        public void setApplyNum(Integer applyNum) {
            this.applyNum = applyNum;
        }

        public String getShopName() {
            return shopName;
        }

        public void setShopName(String shopName) {
            this.shopName = shopName;
        }

        public String getProductMidModel() {
            return productMidModel;
        }

        public void setProductMidModel(String productMidModel) {
            this.productMidModel = productMidModel;
        }
    }
}
