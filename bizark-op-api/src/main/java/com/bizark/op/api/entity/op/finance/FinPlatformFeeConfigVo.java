package com.bizark.op.api.entity.op.finance;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class FinPlatformFeeConfigVo implements Serializable {
    private Integer id;
    private Integer organizationId;
    private Integer keyType;
    /**
     * SellerSKu
     */
    private String keyStr;


    private Integer shopId;

    private String shopName;

    /**
     * 销售渠道
     */
    private String saleChannel;
    /**
     * 费用类型 1-佣金 2-AFN尾程配送费
     */
    private Integer feeType;
    /**
     * 配置方式 1-费用比例（%） 2-费用金额
     */
    private Integer configType;
    /**
     * 如果configType=1 config字段则为比例 configType=2 config字段则为比例则为金额
     */
    private BigDecimal config;
    /**
     * 币种
     */
    private String currency;
    /**
     *生效开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date startDate;
    /**
     *生效结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date endDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * sku
     */
    private String sku;
}
