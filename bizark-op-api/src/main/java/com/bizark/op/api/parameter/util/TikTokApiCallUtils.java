package com.bizark.op.api.parameter.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bizark.common.exception.CommonException;
import com.bizark.common.util.LocalDateTimeUtils;
import com.bizark.op.api.enm.TikTokApiEnums;
import com.bizark.op.api.enm.finance.FinanceErrorEnum;
import com.bizark.op.api.enm.sale.temu.TemuShopTypeEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.account.AccountApplication;
import com.bizark.op.api.entity.op.conf.TikTokAuth;
import com.bizark.op.api.entity.op.conf.TikTokAuthPro;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.common.util.AssertUtil;
import com.squareup.okhttp.*;
import com.xxl.conf.core.XxlConfClient;
import com.xxl.conf.core.annotation.XxlConf;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MultipartBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.multipart.MultipartFile;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.*;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/10/13 10:40
 */
@Component
@Slf4j
public class TikTokApiCallUtils {

    public static final String ACCESS_TOKEN = "accessToken";
    public static final String REFRESH_TOKEN = "refreshToken";
    public static final String OPEN_ID = "openId";
    public static final String SHOP_CIPHER = "shopCipher";

    @XxlConf(value = "bizark-multichannel.tiktok.shopId")
    private static String TIKTOK_SHOP_ID;

    @XxlConf(value = "bizark-erp.tiktok.switch",defaultValue = "false")
    public static Boolean TIKTOK_SWITCH;

    @XxlConf(value = "bizark-multichannel.tiktok.appkey",defaultValue = "69qe5l6kd3vog")
    public static String TIKTOK_APP_KEY;
    @XxlConf(value = "bizark-multichannel.tiktok.app.secret",defaultValue= "8566fadfc63f8233cc084981b3c1798ce32aad73")
    public static String TIKTOK_APP_SECRET;
    /**
     * 抖音应用秘密 本地测试使用
     */
//    public static final String tiktokAppSecret = "8566fadfc63f8233cc084981b3c1798ce32aad73";
//    public static final String tiktokAppKey= "69qe5l6kd3vog";

    public static final String SHOP_ID = "shopId";
    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private AccountService accountService;




//    @XxlConf(value = "bizark-multichannel.tiktok.appkey")
//    public static String TIKTOK_APP_KEY;
//    @XxlConf(value = "bizark-multichannel.tiktok.app.secret")
//    public static String TIKTOK_APP_SECRET;




    /**
     * @param
     * @param appKey    appkey
     * @param appSecret
     * @param timestamp 请求时间戳
     * @param path      路径
     * @param param     看接口需求,部分接口必传shop_id
     * @description: 获取签名
     * @author: Moore
     * @date: 2023/9/19 17:42
     * @return: java.lang.String
     **/
    public String getSign(String appKey, String appSecret, Long timestamp, String path, Map<String, String> param) {
        String sign = "";
        String unSign = appSecret + path + "app_key" + appKey;
        if (null != param) {
            for (Map.Entry<String, String> entry : param.entrySet()) {
                if(ObjectUtil.isNotNull(entry.getValue())){
                    unSign = unSign + entry.getKey() + entry.getValue();
                }
            }
        }
        unSign = unSign + "timestamp" + timestamp + appSecret;
//        log.info("tiktok待签名字符串#{}", unSign);
        sign = encrypt(unSign, appSecret);
//        log.info("tiktok签名字符串#{}", sign);
        return sign;
    }

    /**
     * 发送帖子专业版
     *
     * @param dataMap   数据地图
     * @param queryMap  查询地图
     * @param bodyParam 正文参数
     * @param shopId
     * @return {@link String}
     */
    public String sendPostPro(Map dataMap, Map queryMap, String bodyParam, String shopId){
        OkHttpClient client = new OkHttpClient();
        StringBuilder result = new StringBuilder();
        MediaType mediaType = MediaType.parse("application/json");
        BufferedReader in = null;
        String integrityUrl= getIntegrityUrl(dataMap,queryMap,shopId);
//        log.info("body入参:{}",bodyParam);
        RequestBody body = RequestBody.create(mediaType, bodyParam);
        try {
            Request request = new Request.Builder().url(integrityUrl)
                    .addHeader("Content-Type", "application/json")
                    .post(body)
                    .build();
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.error("参数:{}, 调用tiktok接口失败response code: {} and message: {} and shop_id：{}",bodyParam,
                        response.code(), response.message(),shopId);
                throw new CommonException("系统错误,请联系管理员");
            }
            ResponseBody responseBody = response.body();
            InputStream inputStream = responseBody.byteStream();
            in = new BufferedReader(new InputStreamReader(inputStream));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            in.close();
        } catch (Exception e) {
            log.error("参数:{}, 调用tiktok接口异常response code: {} and message: {} and shop_id：{}",bodyParam, e,shopId);
            e.printStackTrace();
        }
        return result.toString();
    }



    /**
     * 发送帖子专业版
     *
     * @param dataMap   数据地图
     * @param queryMap  查询地图
     * @param bodyParam 正文参数
     * @param shopId
     * @return {@link String}
     */
    public String sendPutPro(Map dataMap, Map queryMap, String bodyParam, String shopId){
        String  integrityUrl = getIntegrityUrl(dataMap, queryMap, shopId);
        try {
            HttpRequest httpRequest = HttpUtil.createRequest(Method.PUT, integrityUrl);
            httpRequest.header("Content-Type", "application/json");
            httpRequest.body(bodyParam);
            HttpResponse httpResponse = httpRequest.execute();
            if (httpResponse.isOk()) {
                log.error("tiktok call error : {}", httpResponse.isOk());
                return httpResponse.body();
            }
        } catch (Exception e) {
            log.error("tikok库存修改失败，参数：{}", bodyParam);
            return null;
        }
        return null;

    }

    /**
     * 发送获取专业版
     *
     * @param dataMap   数据地图
     * @param queryMap  查询地图
     * @param bodyParam 正文参数
     * @param shopId
     * @return {@link String}
     */
    public String sendGetPro(Map dataMap, Map queryMap, String bodyParam, String shopId){
        OkHttpClient client = new OkHttpClient();
        StringBuilder result = new StringBuilder();
        BufferedReader in = null;

        String integrityUrl = getIntegrityUrl(dataMap,queryMap, shopId);;
        try {
            Request request = new Request.Builder().url(integrityUrl)
                    .addHeader("Content-Type", "application/json")
                    .get()
                    .build();
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.info("参数:{}, 调用tiktok接口失败response code: {} and message: {}",bodyParam,
                        response.code(), response.message());
                // 需要抛出一个异常来
                return null;
            }
            ResponseBody responseBody = response.body();
            InputStream inputStream = responseBody.byteStream();
            in = new BufferedReader(new InputStreamReader(inputStream));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            in.close();
//            log.info(" tiktok response body result:{}", result);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result.toString();
    }


    /**
     * 获取完整性网址
     *
     * @param map             地图
     * @param queryMap        参数
     * @param tiktokAppKey    抖音应用密钥
     * @param tiktokAppSecret 抖音应用秘密
     * @param shopId
     * @return {@link String}
     */
    private String getIntegrityUrl(Map<String,String> map, Map<String,String>queryMap, String tiktokAppKey, String tiktokAppSecret, String shopId) {
        Long now = LocalDateTimeUtils.parseLocalDateTimeToTimestampInterval(LocalDateTime.now());

      String sign = getSign(tiktokAppKey, tiktokAppSecret,now,map.get("path"),queryMap);
        StringBuilder integrityUrl = new StringBuilder();
        // 按照顺序拼接 url+path
        integrityUrl=appendPrefixInOrder(appendPrefixInOrder(integrityUrl,map,"url"),map,"path");
        // 其余属性拼接
        integrityUrl=appendQueryListInOrder(integrityUrl,map);
//        log.info("完整url路径:{}",integrityUrl);
        Long encryptedShopId = Long.parseLong(queryMap.get("shop_id"));
        TikTokAuth auth = getTikTokAuth(Long.parseLong(shopId));
        integrityUrl.
                append("app_key=").
                append(tiktokAppKey).
                append("&sign=").
                append(sign).
                append("&timestamp=").
                append(now).
                append("&access_token=").
                append(auth.getAccess_token()).
                append("&shop_id=").
                append(encryptedShopId);
//        log.info("tiktok调用完整路径:{}",integrityUrl);
        return integrityUrl.toString();
    }

    /**
     * 获取完整性网址
     *
     * @param map             地图
     * @param queryMap        参数
     * @param shopId
     * @return {@link String}
     */
    private String getIntegrityUrl(Map<String,String> map, Map<String,String>queryMap,String shopId) {
        TikTokAuth auth = getTikTokAuth(Long.parseLong(shopId));
        Long now = LocalDateTimeUtils.parseLocalDateTimeToTimestampInterval(LocalDateTime.now());

        String sign = getSign(auth.getAppClient(), auth.getAppSecret(), now, map.get("path"), queryMap);
        StringBuilder integrityUrl = new StringBuilder();
        // 按照顺序拼接 url+path
        integrityUrl=appendPrefixInOrder(appendPrefixInOrder(integrityUrl,map,"url"),map,"path");
        // 其余属性拼接
        integrityUrl=appendQueryListInOrder(integrityUrl,map);
//        log.info("完整url路径:{}",integrityUrl);
        Long encryptedShopId = Long.parseLong(queryMap.get("shop_id"));
        integrityUrl.
                append("app_key=").
                append(auth.getAppClient()).
                append("&sign=").
                append(sign).
                append("&timestamp=").
                append(now).
                append("&access_token=").
                append(auth.getAccess_token()).
                append("&shop_id=").
                append(encryptedShopId);
//        log.info("tiktok调用完整路径:{}",integrityUrl);
        return integrityUrl.toString();
    }



    /**
     * 按顺序追加
     *
     *
     * @param integrityUrl 完整性网址
     * @param map          地图
     * @param param        参数
     * @return {@link StringBuilder}
     */
    public StringBuilder appendPrefixInOrder(StringBuilder integrityUrl,Map<String,String> map,String param){
        map.entrySet().removeIf(key->{
            if(param.equals(key.getKey())&&ObjectUtil.isNotNull(key.getValue())){
                integrityUrl.append(key.getValue());
                if("path".equals(param)){
                    integrityUrl.append("?");
                }
                return true;
            }
            return false;
        });
        return integrityUrl;
    }

    /**
     * 按顺序追加
     *
     *
     * @param integrityUrl 完整性网址
     * @param map          地图
     * @return {@link StringBuilder}
     */
    public StringBuilder appendQueryListInOrder(StringBuilder integrityUrl,Map<String,String> map){
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if(ObjectUtil.isNotNull(entry.getValue())){
                integrityUrl
                        .append("&").append(entry.getKey()).append("=").append(entry.getValue());
            }
        }
        return integrityUrl;
    }

    public static String encrypt(String message, String secretKey) {
        try {
            // 创建一个HMAC-SHA256算法实例，并指定密钥
            Mac hmacSha256 = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            hmacSha256.init(secretKeySpec);

            // 计算消息的摘要值
            byte[] hash = hmacSha256.doFinal(message.getBytes(StandardCharsets.UTF_8));

            return byte2Hex(hash);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 将byte转为16进制
     *
     * @param bytes
     * @return
     */
    public static String byte2Hex(byte[] bytes) {
        StringBuffer stringBuffer = new StringBuffer();
        String temp = null;
        for (int i = 0; i < bytes.length; i++) {
            temp = Integer.toHexString(bytes[i] & 0xFF);
            if (temp.length() == 1) {
                //1得到一位的进行补0操作
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }

    /**
     * 获取抖音身份验证
     *
     * @param shopId 店铺编号
     * @return {@link TikTokAuth}
     */
    public TikTokAuthPro getTikTokAuth(Long shopId) {
        Account account = accountService.getById(shopId);
        if (Objects.isNull(account)) {
            log.error("tiktok auth error [{}] : account not found ", shopId);
            return null;
        }
        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            log.error("tiktok auth error [{}] : connectStr is empty ", shopId);
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey(ACCESS_TOKEN)) {
            log.error("tiktok auth error [{}] : accessToken is empty ", shopId);
            return null;
        }
        TikTokAuthPro tkAuth = TikTokAuthPro.builder().open_id(jsonObject.getString(OPEN_ID)).connectStr(connectStr).access_token(jsonObject.getString(ACCESS_TOKEN)).refresh_token(jsonObject.getString(REFRESH_TOKEN))
                .shop_cipher(jsonObject.getString(SHOP_CIPHER))
                .build();
        // 设置密钥数据
        return clientAndSecretSetting(account, tkAuth) ? tkAuth : null;
    }

    /**
     * @param shopId 店铺ID
     * @description: 获取TikTok鉴权信息
     * @author: Moore
     * @date: 2023/9/19 18:54
     * @return: com.bizark.op.api.entity.op.conf.TikTokAuth
     **/
    public TikTokAuth getTikTokAuth(Integer shopId) {
        Account account = accountService.getById(shopId);
        if (Objects.isNull(account)) {
            log.error("tiktok auth error [{}] : account not found ", shopId);
            return null;
        }
        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            log.error("tiktok auth error [{}] : connectStr is empty ", shopId);
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey(ACCESS_TOKEN)) {
            log.error("tiktok auth error [{}] : accessToken is empty ", shopId);
            return null;
        }
        TikTokAuth auth = new TikTokAuth(
                jsonObject.getString(ACCESS_TOKEN),
                jsonObject.getString(REFRESH_TOKEN),
                jsonObject.getString(OPEN_ID),
                jsonObject.getString(SHOP_CIPHER)
        );
        // 设置密钥数据
        return clientAndSecretSetting(account, auth) ? auth : null;
    }



    /**
     * 生成签名
     *
     * @param path  路径
     * @param param 请求参数
     */
    public String getSign(TikTokAuth auth, String path, Map<String, Object> param) {
        AssertUtil.isTrue(StrUtil.isNotBlank(auth.getAppSecret()), FinanceErrorEnum.TIKTOK_BASE_ERROR, "秘钥信息", "秘钥信息");
        StringBuilder unSign = new StringBuilder(auth.getAppSecret() + path);
        param.remove("sign");
        if (CollUtil.isNotEmpty(param)) {
            for (Map.Entry<String, Object> entry : param.entrySet()) {
                if (ObjectUtil.isNotNull(entry.getValue())) {
                    unSign.append(entry.getKey()).append(entry.getValue());
                }
            }
        }
        unSign.append(auth.getAppSecret());
//        log.info("tiktok待签名字符串# {}", unSign);
        String sign = encrypt(unSign.toString(), auth.getAppSecret());
//        log.info("tiktok签名字符串# {}", sign);
        return sign;
    }

    /**
     * 拼接完整路径
     *
     * @param tikTokApiEnums 路径
     * @param paramMap       url上的请求参数
     * @param isFormatUrl    是否格式化Url
     * @param formatUrlParam 格式化Url参数
     * @return {@link String}
     */
    private String getIntegrityUrl(TikTokAuth auth, TikTokApiEnums tikTokApiEnums,
                                   Map<String, Object> paramMap,
                                   Boolean isFormatUrl,
                                   Object... formatUrlParam) {
        String path = tikTokApiEnums.getPath();
        if (Boolean.TRUE.equals(isFormatUrl)) {
            path = StrUtil.format(path, formatUrlParam);
        }
        String sign = getSign(auth,path, paramMap);
        paramMap.put("sign", sign);
        // 按照顺序拼接 url+path
        String url = tikTokApiEnums.getUrl() + path;
        StringBuilder integrityUrl = new StringBuilder(url + "?");
        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
            integrityUrl.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        //删除最后一个&
        integrityUrl.deleteCharAt(integrityUrl.length() - 1);
//        log.info("tiktok调用完整路径:{}", integrityUrl);
        return integrityUrl.toString();
    }

    /**
     * 发送请求
     *
     * @param shopId         店铺id
     * @param tikTokApiEnums 路径
     * @param queryParamMap  需要拼接到url上的请求参数
     * @param bodyParam      post类型请求参数
     * @param requestMethod  请求类型
     * @return 响应数据
     */
    public String sendRequest(Integer shopId,
                              TikTokApiEnums tikTokApiEnums,
                              Map<String, Object> queryParamMap,
                              String bodyParam,
                              RequestMethod requestMethod,
                              Boolean isFormatUrl,
                              Object... formatUrlParam) {
        AssertUtil.isTrue(ObjectUtil.isNotNull(shopId), FinanceErrorEnum.TIKTOK_BASE_ERROR, "门店信息", "门店信息");
        AssertUtil.isTrue(ObjectUtil.isNotNull(requestMethod), FinanceErrorEnum.TIKTOK_BASE_ERROR, "请求类型", "请求类型");
        AssertUtil.isTrue(ObjectUtil.isNotNull(tikTokApiEnums), FinanceErrorEnum.TIKTOK_BASE_ERROR, "路径信息", "路径信息");
        AssertUtil.isFalse(RequestMethod.POST.equals(requestMethod) && StrUtil.isBlank(bodyParam), FinanceErrorEnum.POST_REQUEST_BODY_IS_MUST_EXISTS);
        if (CollUtil.isEmpty(queryParamMap)) {
            queryParamMap = CollUtil.newHashMap(4);
        }
        TikTokAuth auth = getTikTokAuth(shopId);
        AssertUtil.isTrue(ObjectUtil.isNotNull(auth) && StrUtil.isNotBlank(auth.getAccess_token()), FinanceErrorEnum.TIKTOK_BASE_ERROR, "鉴权信息", "鉴权信息");
        AssertUtil.isTrue(ObjectUtil.isNotNull(auth) && StrUtil.isNotBlank(auth.getShop_cipher()), FinanceErrorEnum.TIKTOK_BASE_ERROR, "shop_cipher", "shop_cipher");
        queryParamMap.put("app_key", auth.getAppClient());
        queryParamMap.put("shop_cipher", auth.getShop_cipher());
        AssertUtil.isTrue(StrUtil.isNotBlank(auth.getAppClient()), FinanceErrorEnum.TIKTOK_BASE_ERROR, "tiktokAppKey", "tiktokAppKey");
        queryParamMap.put("timestamp", System.currentTimeMillis() / 1000);
        String integrityUrl = getIntegrityUrl(auth,tikTokApiEnums, queryParamMap, isFormatUrl, formatUrlParam);
        HttpRequest request = null;
        switch (requestMethod) {
            default:
                break;
            case GET:
                request = HttpUtil.createGet(integrityUrl);
                break;
            case POST:
                request = HttpUtil.createPost(integrityUrl)
                        .body(bodyParam);
                log.info("调用tiktok参数为：{}", bodyParam);
                break;
        }
        AssertUtil.isTrue(ObjectUtil.isNotNull(request), FinanceErrorEnum.REQUEST_TYPE_ERROR, requestMethod.toString());
        HttpResponse response = request
                .header("Content-Type", "application/json")
                .header("x-tts-access-token", auth.getAccess_token())
                .execute();
        if (!response.isOk()) {
            log.error("调用tiktok接口失败response  message: {}", response.body());
            return null;
        }
//        log.info(" tiktok response body result:{}", response.body());
        return response.body();
    }



    /**
     * @description:V2Post请求
     * @author: Moore
     * @date: 2024/4/1 16:51
     * @param
     * @param dataMap
     * @param shopId
     * @return: java.lang.String
     **/
    public String sendPostProV2(Map dataMap, String jsonReqBody, Long shopId) {

        TikTokAuth auth = getTikTokAuth(shopId);
        if (Objects.isNull(auth)) {
            log.error("Tiktok请求失败 - {} - 授权信息获取异常", shopId);
            return null;
        }

        okhttp3.OkHttpClient client = new okhttp3.OkHttpClient();
        StringBuilder result = new StringBuilder();
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json");
        BufferedReader in = null;

        //封装请求路径
        String integrityUrl = getIntegrityUrlV2ForPost(dataMap, jsonReqBody, auth, shopId);

        log.error("请求Body：{}",jsonReqBody);
        okhttp3.RequestBody body = okhttp3.RequestBody. create(mediaType, jsonReqBody);
        try {
            okhttp3.Request request = new okhttp3.Request.Builder().url(integrityUrl)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("x-tts-access-token", auth.getAccess_token())
                    .post(body)
                    .build();
            okhttp3.Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.error("V2TK接口响应失败，参数:{}, 调用tiktok接口失败: {}", jsonReqBody,
                        JSONObject.toJSONString(response));
                return null;
            }
            okhttp3.ResponseBody responseBody = response.body();
            InputStream inputStream = responseBody.byteStream();
            in = new BufferedReader(new InputStreamReader(inputStream));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            in.close();
            log.info("tiktok requestParam:{},tiktok response body result:{}", jsonReqBody,result);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result.toString();
    }

    /**
     * @description:V2Post请求
     * @author: Moore
     * @date: 2024/4/1 16:51
     * @param
     * @param dataMap
     * @param shopId
     * @return: java.lang.String
     **/
    public String sendPostProV2Upload(Map dataMap, String jsonReqBody, Long shopId, MultipartFile multipartFile) {

        TikTokAuth auth = getTikTokAuth(shopId);


        String integrityUrl = getIntegrityUrlV2ForPost(dataMap, jsonReqBody, auth, shopId);

        try {
            // 使用 OkHttp 3 上传文件
            okhttp3.OkHttpClient client = new okhttp3.OkHttpClient();

            // 创建 MultipartBody.Builder 对象
            MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);
            // 添加文件字段
            // 添加文件字段
            okhttp3.RequestBody fileBody = okhttp3.RequestBody.create(okhttp3.MediaType.parse(multipartFile.getContentType()), multipartFile.getBytes());
            builder.addFormDataPart("data", multipartFile.getOriginalFilename(), fileBody);
            // 构建请求体
            okhttp3.RequestBody requestBody = builder.build();

            // 创建 Request 对象
            okhttp3.Request request = new okhttp3.Request.Builder()
                    .addHeader("x-tts-access-token", auth.getAccess_token())
                    .url(integrityUrl)
                    .post(requestBody)
                    .build();

            // 发送请求并处理响应
            try (okhttp3.Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()){
                    log.info("Tk文件上传失败: {}" + response.body().string());
                    return null;
                }
                return response.body().string();
            }
        } catch (Exception e) {
            log.info("调用TK文件上传失败：{}", e);
        }
        return null;
    }


    /**
     * @description:V2Post请求 NoShopCipher
     * @author: Moore
     * @date: 2024/4/1 16:51
     * @param
     * @param dataMap
     * @param shopId
     * @return: java.lang.String
     **/
    public String sendPostProV2UploadNoShopCipher(Map dataMap, String jsonReqBody, Long shopId, MultipartFile multipartFile) {

        TikTokAuth auth = getTikTokAuth(shopId);


        String integrityUrl = getIntegrityUrlV2ForPostNoShopCipher(dataMap, jsonReqBody, auth, shopId);

        try {
            // 使用 OkHttp 3 上传文件
            okhttp3.OkHttpClient client = new okhttp3.OkHttpClient();

            // 创建 MultipartBody.Builder 对象
            MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);
            // 添加文件字段
            // 添加文件字段
            okhttp3.RequestBody fileBody = okhttp3.RequestBody.create(okhttp3.MediaType.parse(multipartFile.getContentType()), multipartFile.getBytes());
            builder.addFormDataPart("data", multipartFile.getOriginalFilename(), fileBody);
            // 构建请求体
            okhttp3.RequestBody requestBody = builder.build();

            // 创建 Request 对象
            okhttp3.Request request = new okhttp3.Request.Builder()
                    .addHeader("x-tts-access-token", auth.getAccess_token())
                    .url(integrityUrl)
                    .post(requestBody)
                    .build();

            // 发送请求并处理响应
            try (okhttp3.Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()){
                    log.info("Tk文件上传失败: {}" + response.body().string());
                    return null;
                }
                return response.body().string();
            }
        } catch (Exception e) {
            log.info("调用TK文件上传失败：{}", e);
        }
        return null;
    }




    /**
     * 获取完整性网址
     *
     * @param map             路径等参数
     * @param jsonReqBody        jsonReqBody 请求JSON
     * @param shopId
     * @return {@link String}
     */
    private String getIntegrityUrlV2ForPost(Map<String, Object> map, String jsonReqBody, TikTokAuth auth, Long shopId) {
        Date date = new Date();
        long now = date.getTime() / 1000;

        Map<String, Object> getSingMap = new TreeMap<>(Comparator.naturalOrder());
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            if (!"url".equals(key) && !"path".equals(key) && !"shop_id".equals(key)) {
                getSingMap.put(entry.getKey(), entry.getValue());
            }
        }
        getSingMap.put("shop_cipher", auth.getShop_cipher());

        //获取签名
        String sign = getSignForPost(auth.getAppClient(), auth.getAppSecret(), now, String.valueOf(map.get("path")), getSingMap, jsonReqBody);

        //路径
        StringBuilder reqUrl = new StringBuilder();
        reqUrl.append(map.get("url")).
                append(map.get("path") + "?").
                append("app_key=").
                append(auth.getAppClient()).
                append("&sign=").
                append(sign).
                append("&timestamp=").
                append(now).
                append("&access_token=").
                append(auth.getAccess_token());

        //拼接path
        if (getSingMap != null && getSingMap.keySet().size()> 0) {
            for (String reqKey : getSingMap.keySet()) {
                reqUrl.append("&" + reqKey + "=");
                reqUrl.append(getSingMap.get(reqKey));
            }
        }
        log.error("V2完整请求路径：{}",reqUrl.toString());
        return reqUrl.toString();
    }


    /**
     * 获取完整性网址
     *
     * @param map             路径等参数
     * @param jsonReqBody        jsonReqBody 请求JSON
     * @param shopId
     * @return {@link String}
     */
    private String getIntegrityUrlV2ForPostNoShopCipher(Map<String, Object> map, String jsonReqBody, TikTokAuth auth, Long shopId) {
        Date date = new Date();
        long now = date.getTime() / 1000;

        Map<String, Object> getSingMap = new TreeMap<>(Comparator.naturalOrder());
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            if (!"url".equals(key) && !"path".equals(key) && !"shop_id".equals(key)) {
                getSingMap.put(entry.getKey(), entry.getValue());
            }
        }
//        getSingMap.put("shop_cipher", auth.getShop_cipher());

        //获取签名
        String sign = getSignForPost(auth.getAppClient(), auth.getAppSecret(), now, String.valueOf(map.get("path")), getSingMap, jsonReqBody);

        //路径
        StringBuilder reqUrl = new StringBuilder();
        reqUrl.append(map.get("url")).
                append(map.get("path") + "?").
                append("app_key=").
                append(auth.getAppClient()).
                append("&sign=").
                append(sign).
                append("&timestamp=").
                append(now).
                append("&access_token=").
                append(auth.getAccess_token());

        //拼接path
        if (getSingMap != null && getSingMap.keySet().size()> 0) {
            for (String reqKey : getSingMap.keySet()) {
                reqUrl.append("&" + reqKey + "=");
                reqUrl.append(getSingMap.get(reqKey));
            }
        }
        log.error("V2完整请求路径：{}",reqUrl.toString());
        return reqUrl.toString();
    }

    /**
     * @param
     * @param appKey    appkey
     * @param appSecret
     * @param timestamp 请求时间戳
     * @param path      路径
     * @param param     看接口需求,部分接口必传shop_id
     * @param body
     * @description: 获取签名
     * @author: Moore
     * @date: 2023/9/19 17:42
     * @return: java.lang.String
     **/
    public String getSignForPost(String appKey, String appSecret, Long timestamp, String path, Map<String, Object> param,
                                 String body) {

        String sign = "";
        String unSign = appSecret + path + "app_key" + appKey;
        if (null != param) {
            for (Map.Entry<String, Object> entry : param.entrySet()) {
                unSign = unSign + entry.getKey() + entry.getValue();
            }
        }
        unSign = unSign + "timestamp" + timestamp;

        if (StringUtils.isNotEmpty(body)) {
            unSign = unSign + body;
        }
        unSign = unSign + appSecret;
        log.info("tiktok V2版本接口待签名字符串#{}", unSign);
        sign = encrypt(unSign, appSecret);
        log.info("tiktok V2版本接口签名字符串#{}", sign);
        return sign;
    }


    /**
     * 发送获取专业版
     *
     * @param dataMap   数据地图
     * @param bodyParam 正文参数
     * @param shopId
     * @return {@link String}
     */
    public String sendGetProV2(Map dataMap, String bodyParam, Long shopId) {
        okhttp3.OkHttpClient client = new okhttp3.OkHttpClient();
        StringBuilder result = new StringBuilder();
        BufferedReader in = null;

        TikTokAuth auth = getTikTokAuth(shopId);

        String integrityUrl;
        integrityUrl = getIntegrityUrlV2ForGet(dataMap,  auth, shopId);
        try {
            okhttp3.Request request = new okhttp3.Request.Builder().url(integrityUrl)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("x-tts-access-token", auth.getAccess_token())
                    .get()
                    .build();
            okhttp3.Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.info("参数:{}, 调用tiktok接口失败response code: {} and message: {},shop_id:{},path:{}", bodyParam,
                        response.code(), response.message(), shopId, dataMap.get("path"));
                // 需要抛出一个异常来
                return null;
            }
            okhttp3.ResponseBody responseBody = response.body();
            InputStream inputStream = responseBody.byteStream();
            in = new BufferedReader(new InputStreamReader(inputStream));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            in.close();
            log.info(" tiktok response body result:{} shop_id:{} path：{} parem:{}",  result,shopId, dataMap.get("path"), bodyParam);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result.toString();
    }


    private String getIntegrityUrlV2ForGet(Map<String, Object> map, TikTokAuth auth, Long shopId) {
        Date date = new Date();
        long now = date.getTime() / 1000;

        Map<String, Object> getSingMap = new TreeMap<>(Comparator.naturalOrder());
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            if (!"url".equals(key) && !"path".equals(key) && !"shop_id".equals(key)) {
                getSingMap.put(entry.getKey(), entry.getValue());
            }
        }
        getSingMap.put("shop_cipher", auth.getShop_cipher());

        //获取签名
        String sign = getSignForPost(auth.getAppClient(), auth.getAppSecret(), now, String.valueOf(map.get("path")), getSingMap, null);

        //路径
        StringBuilder reqUrl = new StringBuilder();
        reqUrl.append(map.get("url")).
                append(map.get("path") + "?").
                append("app_key=").
                append(auth.getAppClient()).
                append("&sign=").
                append(sign).
                append("&timestamp=").
                append(now).
                append("&access_token=").
                append(auth.getAccess_token());

        //拼接path
        if (getSingMap != null && getSingMap.keySet().size()> 0) {
            for (String reqKey : getSingMap.keySet()) {
                reqUrl.append("&" + reqKey + "=");
                reqUrl.append(getSingMap.get(reqKey));
            }
        }
        log.error("V2完整请求路径：{}",reqUrl.toString());
        return reqUrl.toString();
    }



    public boolean clientAndSecretSetting(Account account, TikTokAuth tkAuth) {


        AccountApplication application = accountService.selectApplication(account.getAmazonApplicationId());
        if (Objects.isNull(application)) {
            log.error("tiktok auth error [{}] : application not found ", account.getFlag());
            return false;
        }

        String clientId = XxlConfClient.get(application.getClientId(), "");
        if (StrUtil.isBlank(clientId)) {
            log.error("tiktok auth error [{}] : clientId is empty ", account.getFlag());
            return false;
        }
        String clientSecret = XxlConfClient.get(application.getClientSecret(), "");
        if (StrUtil.isBlank(clientSecret)) {
            log.error("tiktok auth error [{}] : clientSecret is empty ", account.getFlag());
            return false;
        }
        tkAuth.setAppClient(clientId);
        tkAuth.setAppSecret(clientSecret);
        return true;
    }



    /**
     * @description:V2Put请求
     * @author: Moore
     * @date: 2024/4/1 16:51
     * @param
     * @param dataMap
     * @param shopId
     * @return: java.lang.String
     **/
    public String sendPutProV2(Map dataMap, String jsonReqBody, Long shopId) {

        TikTokAuth auth = getTikTokAuth(shopId);
        if (Objects.isNull(auth)) {
            log.error("Tiktok请求失败 - {} - 授权信息获取异常", shopId);
            return null;
        }

        okhttp3.OkHttpClient client = new okhttp3.OkHttpClient();
        StringBuilder result = new StringBuilder();
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json");
        BufferedReader in = null;

        //封装请求路径
        String integrityUrl = getIntegrityUrlV2ForPost(dataMap, jsonReqBody, auth, shopId);

        log.error("请求Body：{}",jsonReqBody);
        okhttp3.RequestBody body = okhttp3.RequestBody. create(mediaType, jsonReqBody);
        try {
            okhttp3.Request request = new okhttp3.Request.Builder().url(integrityUrl)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("x-tts-access-token", auth.getAccess_token())
                    .put(body)
                    .build();
            okhttp3.Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.error("V2TK接口响应失败，参数:{}, 调用tiktok接口失败: {}", jsonReqBody,
                        JSONObject.toJSONString(response));
                return null;
            }
            okhttp3.ResponseBody responseBody = response.body();
            InputStream inputStream = responseBody.byteStream();
            in = new BufferedReader(new InputStreamReader(inputStream));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            in.close();
            log.info("tiktok requestParam:{},tiktok response body result:{}", jsonReqBody,result);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result.toString();
    }


    /**
     * @description:V2delete请求
     * @author: Moore
     * @date: 2024/4/1 16:51
     * @param
     * @param dataMap
     * @param shopId
     * @return: java.lang.String
     **/
    public String sendDeleteProV2(Map dataMap, String jsonReqBody, Long shopId) {

        TikTokAuth auth = getTikTokAuth(shopId);
        if (Objects.isNull(auth)) {
            log.error("Tiktok请求失败 - {} - 授权信息获取异常", shopId);
            return null;
        }

        okhttp3.OkHttpClient client = new okhttp3.OkHttpClient();
        StringBuilder result = new StringBuilder();
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json");
        BufferedReader in = null;

        //封装请求路径
        String integrityUrl = getIntegrityUrlV2ForPost(dataMap, jsonReqBody, auth, shopId);

        log.error("请求Body：{}",jsonReqBody);
        okhttp3.RequestBody body = okhttp3.RequestBody. create(mediaType, jsonReqBody);
        try {
            okhttp3.Request request = new okhttp3.Request.Builder().url(integrityUrl)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("x-tts-access-token", auth.getAccess_token())
                    .delete(body)
                    .build();
            okhttp3.Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.error("V2TK接口响应失败，参数:{}, 调用tiktok接口失败: {}", jsonReqBody,
                        JSONObject.toJSONString(response));
                return null;
            }
            okhttp3.ResponseBody responseBody = response.body();
            InputStream inputStream = responseBody.byteStream();
            in = new BufferedReader(new InputStreamReader(inputStream));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            in.close();
            log.info("tiktok requestParam:{},tiktok response body result:{}", jsonReqBody,result);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result.toString();
    }
}
