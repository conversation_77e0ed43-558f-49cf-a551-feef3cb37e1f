package com.bizark.op.api.entity.op.finance;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bizark.op.common.core.domain.BaseEntity;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@TableName(value = "platform_fee_config",excludeProperty = {"remark"})
public class FinPlatformFeeConfig extends BaseEntity {
    private Integer id;
    private Integer organizationId;
    private Integer keyType;
    private String keyStr;
    private String saleChannel;
    private Integer shopId;
    @TableField(exist = false)
    private String shopName;
    private Integer feeType;
    private Integer configType;
    private BigDecimal config;
    private String currency;
    private Date startDate;
    private Date endDate;
    private String sku;


    public FinPlatformFeeConfig() {
    }

    public FinPlatformFeeConfig(Integer id, Integer organizationId, Integer keyType, String keyStr, String saleChannel, Integer shopId, String shopName, Integer feeType, Integer configType, BigDecimal config, String currency, Date startDate, Date endDate,String sku) {
        this.id = id;
        this.organizationId = organizationId;
        this.keyType = keyType;
        this.keyStr = keyStr;
        this.saleChannel = saleChannel;
        this.shopId = shopId;
        this.shopName = shopName;
        this.feeType = feeType;
        this.configType = configType;
        this.config = config;
        this.currency = currency;
        this.startDate = startDate;
        this.endDate = endDate;
        this.sku = sku;

    }
}
