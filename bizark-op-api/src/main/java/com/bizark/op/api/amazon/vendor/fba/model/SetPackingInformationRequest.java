package com.bizark.op.api.amazon.vendor.fba.model;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> @ClassName SetPackingInformationRequest
 * @description: amz设置包装信息
 * @date 2025年08月11日
 */
public class SetPackingInformationRequest {


    private List<PackageGroupingsBean> packageGroupings;

    public List<PackageGroupingsBean> getPackageGroupings() {
        return packageGroupings;
    }

    public void setPackageGroupings(List<PackageGroupingsBean> packageGroupings) {
        this.packageGroupings = packageGroupings;
    }

    public static class PackageGroupingsBean {
        /**
         * boxes : [{"contentInformationSource":"BOX_CONTENT_PROVIDED","dimensions":{"height":5,"length":3,"unitOfMeasurement":"CM","width":4},"items":[{"expiration":"2024-01-01","labelOwner":"AMAZON","manufacturingLotCode":"manufacturingLotCode","msku":"Sunglasses","prepOwner":"AMAZON","quantity":10}],"quantity":2,"weight":{"unit":"KG","value":5.5}}]
         * packingGroupId : pg1234abcd-1234-abcd-5678-1234abcd5678
         * shipmentId : sh1234abcd-1234-abcd-5678-1234abcd5678
         */

        private String packingGroupId;
        private String shipmentId;
        private List<BoxesBean> boxes;

        public String getPackingGroupId() {
            return packingGroupId;
        }

        public void setPackingGroupId(String packingGroupId) {
            this.packingGroupId = packingGroupId;
        }

        public String getShipmentId() {
            return shipmentId;
        }

        public void setShipmentId(String shipmentId) {
            this.shipmentId = shipmentId;
        }

        public List<BoxesBean> getBoxes() {
            return boxes;
        }

        public void setBoxes(List<BoxesBean> boxes) {
            this.boxes = boxes;
        }

        public static class BoxesBean {
            /**
             * contentInformationSource : BOX_CONTENT_PROVIDED
             * dimensions : {"height":5,"length":3,"unitOfMeasurement":"CM","width":4}
             * items : [{"expiration":"2024-01-01","labelOwner":"AMAZON","manufacturingLotCode":"manufacturingLotCode","msku":"Sunglasses","prepOwner":"AMAZON","quantity":10}]
             * quantity : 2
             * weight : {"unit":"KG","value":5.5}
             */

            private String contentInformationSource;
            private DimensionsBean dimensions;
            /**
             * 箱数
             */
            private Integer quantity;
            private WeightBean weight;
            private List<ItemsBean> items;

            public String getContentInformationSource() {
                return contentInformationSource;
            }

            public void setContentInformationSource(String contentInformationSource) {
                this.contentInformationSource = contentInformationSource;
            }

            public DimensionsBean getDimensions() {
                return dimensions;
            }

            public void setDimensions(DimensionsBean dimensions) {
                this.dimensions = dimensions;
            }


            public Integer getQuantity() {
                return quantity;
            }

            public void setQuantity(Integer quantity) {
                this.quantity = quantity;
            }

            public WeightBean getWeight() {
                return weight;
            }

            public void setWeight(WeightBean weight) {
                this.weight = weight;
            }

            public List<ItemsBean> getItems() {
                return items;
            }

            public void setItems(List<ItemsBean> items) {
                this.items = items;
            }

            public static class DimensionsBean {
                /**
                 * height : 5
                 * length : 3
                 * unitOfMeasurement : CM
                 * width : 4
                 */

                private Integer height;
                private Integer length;
                private String unitOfMeasurement;
                private Integer width;

                public Integer getHeight() {
                    return height;
                }

                public void setHeight(Integer height) {
                    this.height = height;
                }

                public Integer getLength() {
                    return length;
                }

                public void setLength(Integer length) {
                    this.length = length;
                }

                public String getUnitOfMeasurement() {
                    return unitOfMeasurement;
                }

                public void setUnitOfMeasurement(String unitOfMeasurement) {
                    this.unitOfMeasurement = unitOfMeasurement;
                }

                public Integer getWidth() {
                    return width;
                }

                public void setWidth(Integer width) {
                    this.width = width;
                }
            }

            public static class WeightBean {
                /**
                 * unit : KG
                 * value : 5.5
                 */

                private String unit;
                private BigDecimal value;

                public String getUnit() {
                    return unit;
                }

                public void setUnit(String unit) {
                    this.unit = unit;
                }

                public BigDecimal getValue() {
                    return value;
                }

                public void setValue(BigDecimal value) {
                    this.value = value;
                }
            }

            public static class ItemsBean {
                /**
                 * expiration : 2024-01-01
                 * labelOwner : AMAZON
                 * manufacturingLotCode : manufacturingLotCode
                 * msku : Sunglasses
                 * prepOwner : AMAZON
                 * quantity : 10
                 */

                private String expiration;
                private String labelOwner;
                private String manufacturingLotCode;
                private String msku;
                private String prepOwner;
                //单箱数量
                private Integer quantity;

                public String getExpiration() {
                    return expiration;
                }

                public void setExpiration(String expiration) {
                    this.expiration = expiration;
                }

                public String getLabelOwner() {
                    return labelOwner;
                }

                public void setLabelOwner(String labelOwner) {
                    this.labelOwner = labelOwner;
                }

                public String getManufacturingLotCode() {
                    return manufacturingLotCode;
                }

                public void setManufacturingLotCode(String manufacturingLotCode) {
                    this.manufacturingLotCode = manufacturingLotCode;
                }

                public String getMsku() {
                    return msku;
                }

                public void setMsku(String msku) {
                    this.msku = msku;
                }

                public String getPrepOwner() {
                    return prepOwner;
                }

                public void setPrepOwner(String prepOwner) {
                    this.prepOwner = prepOwner;
                }

                public Integer getQuantity() {
                    return quantity;
                }

                public void setQuantity(Integer quantity) {
                    this.quantity = quantity;
                }
            }
        }
    }
}
