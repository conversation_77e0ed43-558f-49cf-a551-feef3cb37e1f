package com.bizark.op.api.vo.mar;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.bizark.op.common.annotation.DictConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description:
 * @Author: Fountain
 * @Date: 2024/2/18
 */
@ExcelIgnoreUnannotated
@Data
@ColumnWidth(value = 35)
public class MarProductPopularizeShopVo {

    /**
     * 渠道
     */
    private String channel;

    @ExcelProperty(value = "渠道")
    @DictConvert(dict = "account_sale_channel", target = "channel")
    private String channelDict;

    private Integer accountId;

    /**
     * 店铺ID
     */
    @ExcelProperty(value = "店铺")
    private String accountName;
    /**
     * 国家CODE
     */
    @ExcelProperty(value = "国家")
    private String countryCode;

    /**
     * 退货量
     */
    @ExcelProperty(value = "退货量")
    private BigDecimal sumReturnNum;
    /**
     * 退款量
     */
    @ExcelProperty(value = "退款量")
    private BigDecimal sumRefundNum;
    /**
     * 销量
     */
    @ExcelProperty(value = "销量")
    private BigDecimal sumQuantity;

    /**
     * 上个周期销量
     */
    @ExcelProperty(value = "销量环比")
    private BigDecimal preSumQuantity;

    /**
     * 销量环比
     */
    private BigDecimal quantityQoq;


    /**
     * 销售额
     */
    @ExcelProperty(value = "销售额")
    private BigDecimal sumItemAmount;

    /**
     * 上个周期销售额
     */
    @ExcelProperty(value = "销售额环比")
    private BigDecimal preSumItemAmount;

    /**
     * 销售额环比
     */
    private BigDecimal itemAmountQoq;

    /**
     * 订单量
     */
    @ExcelProperty(value = "订单量")
    private BigDecimal sumOrderNum;

    /**
     * 上个周期订单量
     */
    @ExcelProperty(value = "订单量环比")
    private BigDecimal preSumOrderNum;

    /**
     * 订单量环比
     */

    private BigDecimal orderNumQoq;

    /**
     * 退货率
     */
    @ExcelProperty(value = "退货率")
    private BigDecimal returnRate;



    /**
     * 利润
     */
    @ExcelProperty(value = "利润")
    private BigDecimal sumProfit;

    /**
     * 利润率
     */
    @ExcelProperty(value = "利润")
    private BigDecimal profitRate;

    /**
     * 收入
     */
    private BigDecimal sumIncomeFee;

    /**
     * 广告花费
     */
    @ExcelProperty(value = "广告花费")
    private BigDecimal sumAdCost;


    /**
     * 广告ACOS
     */
    @ExcelProperty(value = "广告ACOS")
    private BigDecimal acos;

    /**
     * 广告销售额
     */
    @ExcelProperty(value = "广告销售额")
    private BigDecimal sumAdSaleAmount;

    /**
     * 广告订单量
     */
    @ExcelProperty(value = "广告订单量")
    private BigDecimal sumAdOrderNum;

    /**
     * 广告销售额占比
     */
    @ExcelProperty(value = "广告销售额占比")
    private BigDecimal adSaleAmountRatio;

    /**
     * 广告订单量占比
     */
    @ExcelProperty(value = "广告订单量占比")
    private BigDecimal adOrderNumRatio;

    /**
     * 广告CPC
     */
    @ExcelProperty(value = "广告CPC")
    private BigDecimal adCpc;
    /**
     * 广告点击量
     */
    @ExcelProperty(value = "广告点击量")
    private BigDecimal sumAdClick;
    /**
     * 广告曝光量
     */
    @ExcelProperty(value = "广告曝光量")
    private BigDecimal sumAdViews;

    /**
     * 广告点击率
     */
    @ExcelProperty(value = "广告点击率")
    private BigDecimal adClickRate;

    /**
     * 广告转化率(ad单/点击)
     */
    @ExcelProperty(value = "广告转化率")
    private BigDecimal adConversion;

    /**
     * 订单日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date pstChannelCreatedDate;

    private Integer sessionTotal;

    private Integer sessionBrowser;

    private Integer sessionMobile;

    private Integer pvTotal;

    private Integer pvBrowser;

    private Integer pvMobile;

    private Integer glanceView;


}

