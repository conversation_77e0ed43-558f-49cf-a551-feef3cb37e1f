package com.bizark.op.api.entity.op.qywx;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.bizark.op.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 
 * @TableName qywx_orgainzation_config
 */
@TableName(value ="qywx_orgainzation_config",excludeProperty = {
        "searchValue","params", "remark"
})
@Data
public class QywxOrgainzationConfig extends BaseEntity {
    /**
     * 企业微信组织配置表主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;


    private Integer createOrgId;
    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 企业ID
     */
    private String corpId;

    /**
     * 应用的凭证密钥
     */
    private String corpsecret;

    /**
     * 企业应用的Id
     */
    private Integer agentId;

    private String token;


    private String EncodingAesKey;

}