package com.bizark.op.api.entity.op.sale;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.*;
import com.bizark.op.api.annotation.LogConvert;
import com.bizark.op.api.annotation.excel.ExcelField;
import com.bizark.op.api.enm.sale.ApprovalEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.inventory.ProductChannelsInventory;
import com.bizark.op.api.entity.op.product.SkuChildren;
import com.bizark.op.api.entity.op.sale.vo.AccountsSellerResponse;
import com.bizark.op.api.entity.op.sale.vo.ErpSkuStockVO;
import com.bizark.op.api.entity.op.sale.vo.SkuInventoryShowVO;
import com.bizark.op.api.entity.op.task.PublishTask;
import com.bizark.op.api.service.sale.ProductChannelApprovalService;
import com.bizark.op.api.service.sale.ProductChannelRelateService;
import com.bizark.op.api.service.sale.ProductChannelsService;
import com.bizark.op.common.annotation.BindDBEntity;
import com.bizark.op.common.annotation.BindDBField;
import com.bizark.op.common.annotation.DictConvert;
import com.bizark.op.common.annotation.ExcelMapping;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.context.annotation.Description;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * SKu 映射
 */
@Data
@TableName(value = "dashboard.product_channels",excludeProperty = {
        "params","searchValue"
})
@Accessors(chain = true)
@Description("SKU映射")
public class ProductChannels implements Serializable {


    @TableId(type = IdType.AUTO)
    @ExcelMapping(relation = "sellerSku=sellerSku", service = ProductChannelsService.class, mappingField = "id")
    private Integer id;

    /**
     * 'single','bundle','virtual' default 'single'
     */
    @TableField(value = "type")
    @Schema(description = "产品类型")
//    @LogConvert({"single:简单产品","bundle:组合产品","virtual:虚拟产品"})
    @LogConvert(dict = "sku_mapping_type")
    @ExcelField(title = "typeDict",dict = "sku_mapping_type")
    private String type;


    // amazon/wayfair
    @Schema(description = "sourceType")
    private String sourceType;

    @ExcelProperty(value = "产品类型*",order = 5)
    @BindDBField(dict = "sku_mapping_type",relation = "type")
    @TableField(exist = false)
    @DictConvert(dict = "sku_mapping_type", target = "type")
    private String typeDict;

    @TableField(value = "org_id")
    @Schema(description = "组织")
    private Integer orgId;


    /**
     * 店铺ID
     */
    @TableField(exist = false)
    private Integer shopId;


    @TableField(value = "data_type")
    @Schema(description = "dataType")
    private Integer dataType;

    @TableField(value = "sku_type")
    @Schema(description = "skuType")
    private Integer skuType;

    @TableField(value = "sku_order_type")
    @Schema(description = "skuOrderType")
    private Integer skuOrderType;

    @TableField(exist = false)
    @DictConvert(dict = "delivery_method", target = "skuOrderType")
    private String skuOrderTypeDict;


    @TableField(value = "account_id")
    @ExcelProperty(value = "店铺名称*", order = 1)
    @Schema(description = "店铺标记")
    private String accountId;

    @ExcelProperty(value = "账号名称", order = 0)
    @TableField(exist = false)
    public String accountInit;

    @TableField(value = "listing_id")
    @Schema(description = "listingId")
    private String listingId;

    @TableField(value = "name")
    @Schema(description = "名称")
    private String name;

    @TableField(value = "currency_code")
    @Schema(description = "币种")
    private String currencyCode;

    @TableField(value = "currency_exchange_rate")
    @Schema(description = "币种汇率")
    private BigDecimal currencyExchangeRate;

    @TableField(value = "price")
    @Schema(description = "价格")
    private BigDecimal price;

    @TableField(value = "quantity")
    @Schema(description = "数量")
    private BigDecimal quantity;


    @Schema(description = "库存ID")
    private String inventoryItemId;


    /**
     * 导入导出使用
     */
    @ExcelProperty(value = "数量",order = 7)
    @TableField(exist = false)
    private String quantityStr;


    @TableField(value = "line_id")
    @Schema(description = "项目ID")
    private Integer lineId;

    @TableField(value = "product_id")
    @Schema(description = "产品ID")
    private Integer productId;

    @TableField(value = "erpsku")
    @Schema(description = "ErpSku")
    @ExcelProperty(value = "SKU", order = 6)
    private String erpSku;

    /**
     * sellerSku
     */
    @TableField(value = "seller_sku")
    @Schema(description = "SellerSku")
    @ExcelProperty(value = "SellerSku*", order = -1)
    private String sellerSku;

    @TableField(exist = false)
    private String sellerSkus;

    @TableField(value = "asin")
    @Schema(description = "Asin")
    @ExcelProperty(value = "Asin", order = 9)
    private String asin;

    /**
     * 后边会把parent_asin存下来 用于asin1
     */
    @TableField(value = "asin1")
    @Schema(description = "父Asin")
    @ExcelProperty(value = "父Asin", order = 10)
    private String asin1;

    /**
     * 用于asin的预留
     */
    @TableField(value = "asin2")
    @Schema(description = "Asin2")
    private String asin2;

    @TableField(value = "fnsku")
    @Schema(description = "Item Label")
    @ExcelProperty(value = "Item Label", order = 8)
    private String fnsku;

    /**
     * amaozn上的图片链接
     */
    @TableField(value = "image_url")
    @Schema(description = "图片链接")
    private String imageUrl;

    /**
     * 平台发货等级
     */
    @TableField(value = "freight_class")
    @Schema(description = "freightClass")
    private String freightClass;


    /**
     * amaozn上的产品标题
     */
    @TableField(value = "title")
    @Schema(description = "产品标题")
    private String title;

    /**
     * ('Off Shelve','Hit Shelve','Not Sell')  default 'Hit Shelve',
     */
//    @TableField(value = "sale_state")
//    @Schema(description = "销售状态")
//    @LogConvert({"Off Shelve:停售","Hit Shelve:在售","Not Sell:不可售"})
//    @ExcelField(title = "saleStateDict",dict = "sku_sale_status")
    private String saleState;

    @TableField(value = "sell_status")
    @Schema(description = "销售状态")
    @LogConvert(dict = "sku_sale_status")
    @ExcelField(title = "sellStatusDict",dict = "sku_sale_status")
    private String sellStatus;

    @TableField(exist = false)
    @DictConvert(dict = "sku_sale_status", target = "sellStatus")
    @BindDBField(relation = "sellStatus",dict = "sku_sale_status")
    @ExcelProperty(value = "销售状态*", order = 2)
    private String sellStatusDict;


//    @TableField(exist = false)
//    @DictConvert(dict = "sku_sale_status", target = "saleState")
//    @ExcelProperty(value = "销售状态", order = 2)
//    @BindDBField(relation = "saleState",dict = "sku_sale_status")
//    private String saleStateDict;

    @Schema(description = "销售渠道")
    @TableField(value = "sale_channel")
    @LogConvert(dict = "account_sale_channel")
    @ExcelField(title = "saleChannelDict",dict = "account_sale_channel")
    private String saleChannel;

    @TableField(exist = false)
    @DictConvert(dict = "account_sale_channel", target = "saleChannel")
    @ExcelProperty(value = "销售渠道", order = 4)
    @BindDBField(relation = "saleChannel",dict = "account_sale_channel")
    private String saleChannelDict;

    @TableField(value = "ean")
    @Schema(description = "Ean")
    private String ean;

    @TableField(value = "upc")
    @ExcelProperty(value = "UPC", order = 14)
    @Schema(description = "Upc")
    private String upc;

    @Schema(description = "gtin")
    @TableField(value = "gtin")
    private String gtin;

    @Schema(description = "isbn")
    @TableField(value = "isbn")
    private String isbn;

    @TableField(value = "your_price")
    @Schema(description = "yourPrice")
    private BigDecimal yourPrice;

    @TableField(value = "sale_price")
    @Schema(description = "销售价格")
    private BigDecimal salePrice;

    @TableField(value = "follow_sale_no")
    @Schema(description = "followSaleNo")
    private Integer followSaleNo;

    @TableField(value = "follow_sale_price")
    @Schema(description = "followSalePrice")
    private BigDecimal followSalePrice;

    @TableField(value = "comment_no")
    @Schema(description = "commentNo")
    private Integer commentNo;

    @TableField(value = "reply_no")
    @Schema(description = "replyNo")
    private Integer replyNo;

    @TableField(value = "your_price_usd")
    @Schema(description = "yourPriceUsd")
    private BigDecimal yourPriceUsd;

    @TableField(value = "business_price_flag")
    @Schema(description = "businessPriceFlag")
    private Boolean businessPriceFlag;

    @TableField(value = "status")
    @Schema(description = "status")
    private String status;

    /**
     * 渠道产品链接
     */
    @TableField(value = "channel_url")
    @Schema(description = "渠道产品链接")
    private String channelUrl;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField(value = "listing_price_date")
    @Schema(description = "listingPriceDate")
    private Date listingPriceDate;

    /**
     * 是否告警
     */
    @TableField(value = "is_warn")
    @Schema(description = "是否告警")
    private Integer isWarn;

    @TableField(value = "property")
    @Schema(description = "property")
    private String property;

    /**
     * 模板名称
     */
    @TableField(value = "template_id")
    @Schema(description = "模板")
    private Integer templateId;


    @TableField("restricted_reason")
    @Schema(description = "受限原因")
    private String restrictedReason;

    /**
     * 是否告警
     */
    @TableField(value = "is_restricted")
    @Schema(description = "是否受限")
    @LogConvert({"0:否","1:是"})
    private Integer isRestricted;


    /**
     * 渠道产品当前价格
     */
    @TableField(value = "listing_price")
    @Schema(description = "listingPrice")
    private BigDecimal listingPrice;

    /**
     * 渠道产品前一次价格
     */
    @TableField(value = "pre_listing_price")
    @Schema(description = "preListingPrice")
    private BigDecimal preListingPrice;

    /**
     * 是否推送渠道
     */
    @TableField(value = "is_push")
    @Schema(description = "是否推送库存")
    @ExcelField(title="isPushDict",map = {"是:Y","否:N"})
    private String isPush;


    @TableField(exist = false)
    @ExcelProperty("是否推送库存")
    private String isPushDict;

    /**
     * 用于计算的比例
     */
    @TableField(value = "push_proportion")
    @ExcelProperty("推送比例(%)")
    @Schema(description = "推送比例(%)")
    private Integer pushProportion;

    /**
     * 0:按比例推送; 1:按固定值推送
     */
    @TableField(value = "push_type")
    @Schema(description = "推送类型")
    @LogConvert({"0:按比例推送","1:按固定值推送"})
    private Integer pushType;

    @TableField(value = "handeling_time")
    @ExcelProperty("Handing time")
    private Integer handelingTime;

    /**
     * 产品是否为FBAONSITE属性 1:是;0:否
     */
    @TableField(value = "fba_onsite")
    @Schema(description = "是否为FBAONSITE属性")
    @LogConvert({"0:否","1:是"})
    private Integer fbaOnSite;

    /**
     * 是否启用amazon透明计划 0:不启用，1：启用
     */
    @TableField(value = "amazon_product_serial_status")
    @Schema(description = "是否启用amazon透明计划")
    @LogConvert({"0:不启用","1：启用"})
    private Integer amazonProductSerialStatus;

    @TableField(value = "amazon_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    /**
     * 更新时间
     */
    private Date amazonTime;


    @TableField(value = "listing_name")
    private String listingName;

    @TableField(value = "listing_manage")
    private String listingManage;

    @TableField(value = "listing_attr1")
    private String listingAttr1;

    @TableField(value = "listing_attr2")
    private String listingAttr2;

    @TableField(value = "listing_attr3")
    private String listingAttr3;

    /**
     * 运营id
     */
    @TableField(value = "operation_user_id")
    @Schema(description = "运营id")
    private Integer operationUserId;


    @Schema(description = "平台库存")
    @TableField(exist = false)
    private Integer inventory;

    @Schema(description = "平台库存状态")
    private Integer inventoryStatus;

    @Schema(description = "平台库存状态描述")
    @TableField(exist = false)
    private String inventoryStatusLabel;

    /**
     * 前端多选查询使用
     */
    @TableField(exist = false)
    private String operationUserIds;
    /**
     * 前端多选查询使用
     */
    @TableField(exist = false)
    private String approvalUserIds;


    @TableField(exist = false)
    private List<Integer> operationUserIdQuery;



    @Schema(description = "运营名称")
    @ExcelProperty(value = "运营*", order = 15)
    private String operationUserName;

    /**
     * 配送方式
     */
    @TableField(value = "distribution_mode")
    @LogConvert(dict = "distribution_mode")
    @Schema(description = "配送方式")
    private String distributionMode;

    @TableField(exist = false)
    @DictConvert(dict = "distribution_mode", target = "distributionMode")
    @LogConvert(dict = "distribution_mode")
    private String distributionModeDict;

    /**
     * 审批状态
     */
    @TableField("approval_status")
    @Schema(description = "审批状态")
    @LogConvert(dict = "approval_status")
    @ExcelField(title = "approvalStatusDict",dict = "approval_status")
    private String approvalStatus;


    @DictConvert(dict = "approval_status",target = "approvalStatus")
    @TableField(exist = false)
    @BindDBField(relation = "approvalStatus",dict = "approval_status")
    @ExcelProperty(value = "审核状态",order = 3)
    private String approvalStatusDict;

    @Schema(description = "sellerSkuPrice")
    private BigDecimal sellerSkuPrice;


    @ExcelField(title = "isSeedDict", map = {"是:Y", "否:N"})
    @Schema(description = "是否种子")
    @LogConvert(value = {"是:Y", "否:N"})
    private String isSeed;

    @TableField(exist = false)
    @ExcelProperty(value = "是否种子", order = 12)
    private String isSeedDict;

//    @ExcelProperty(value = "是否VINE", order = 13)
    @Schema(description = "是否VINE")
    @ExcelField(title = "isVineDict", map = {"是:Y", "否:N"})
    private String isVine;


    @ExcelProperty(value = "是否VINE", order = 13)
    @TableField(exist = false)
    @LogConvert(value = {"是:Y", "否:N"})
    private String isVineDict;

//    @Column(name = "created", nullable = false, insertable = false, updatable = false)
//    private LocalDateTime created;
//
//    @Column(name = "updated", nullable = false, insertable = false, updatable = false)
//    private LocalDateTime updated;
//
    @Schema(description = "active")
    private String active;
//
//    @Column(name = "unactive_time", nullable = false)
    @TableField("unactive_time")
    @Schema(description = "unActiveTime")
    private Integer unActiveTime;

    @TableField("cost_price")
    @Schema(description = "成本价")
    private BigDecimal costPrice;



    @TableField(exist = false)
    private String lineName;

    @TableField(exist = false)
    private Boolean isTiggerStockVirtual = Boolean.FALSE;




//    @OneToOne(
//            mappedBy = "productChannelEntity"
//            , fetch = FetchType.EAGER
//    )
//    @TableField(exist = false)
//    private ProductChannelBusinessEntity productChannelBusiness;

    //是否触发埋点
    @TableField(exist = false)
    private Boolean maiDian = Boolean.FALSE;


    @TableField("brand")
    @ExcelProperty(value = "品牌", order = 11)
    @Schema(description = "品牌")
    private String brand;


    @BindDBEntity(service = ProductChannelRelateService.class,relation = "id=productChannelId")
    @TableField(exist = false)
    private List<ProductChannelRelate> productRelates;


//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private String beginTime;

//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private String endTime;


    @TableField(exist = false)
//    @ExcelProperty(value = "部门名称", order = 16)
    public String deptName;

    @TableField(exist = false)
//    @ExcelProperty(value = "部门名称", order = 16)
    public Integer deptId;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date openDate;

    @TableField(exist = false)
    private List<Products> productsList;


    @TableField(exist = false)
    private String categoryName;



    @TableField(exist = false)
    public String shopName;


    @TableField(exist = false)
    public String country;

    private String skcId;


    private Integer catId;


    private String catName;

    @Schema(description = "平台仓库存")
    private Integer availToSellQty;


    private String instanceId;

    private String investment;

    @TableField(exist = false)
    private String model;

    /**
     * 标签
     */
    private Integer tag;
    /**
     * 审批记录
     */
    @TableField(exist = false)
    @BindDBEntity(relation = "id=productChannelId", service = ProductChannelApprovalService.class)
    private List<ProductChannelApproval> approvals;

    /**
     * 上传信息
     */
    @Schema(description = "上传信息")
    private String meta;

    @TableField(exist = false)
    private String accountTitle;


    @TableField(exist = false)
    private List<String> saleChannelQuery;

    @TableField(exist = false)
    private List<String> accountIdQuery;

    @TableField(exist = false)
    private List<String> sellStatusQuery;

    @TableField(exist = false)
    private List<String> restrictedReasonQuery;
    @TableField(exist = false)
    private List<String> approvalStatusQuery;

    @TableField(exist = false)
    private List<String> investmentQuery;

    @TableField(fill = FieldFill.INSERT)
    private Integer createdBy;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createdName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer updatedBy;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.UPDATE)
    private Date updatedAt;

    /**
     * 更新者
     */
    private Integer disabledBy;

    /**
     * 更新者
     */
    private String disabledName;

    /**
     * 禁用时间
     */
    private Long disabledAt;
    @TableField(exist = false)
    private String remark;

    /**
     * 是否跳过日志
     */
    @TableField(exist = false)
    private Boolean skipInterceptor;


    @TableField(exist = false)
    private List<String> sellerSkuQuery;

    @TableField(exist = false)
    private List<String> asinsQuery;


    @TableField(exist = false)
    private List<String> erpSkuQuery;

    @TableField(exist = false)
    private List<String> parentAsinQuery;

    /**
     * 是否为接口获取数据
     */
    @TableField("is_interface_data")
    private Boolean isInterfaceData;

    @TableField("item_id")
    private String itemId;

    @TableField("parent_item_id")
    private String parentItemId;

    @TableField("sku_id")
    private String skuId;

    @TableField("spu_id")
    private String spuId;

    private String skuVersion;
    private Integer skuVersionSerial;


    private String fulfillment;


    @TableField(exist = false)
    private Account account;

    @TableField(exist = false)
    private String identifierType;

    @TableField(exist = false)
    private String identifier;

    @TableField(exist = false)
    private String publishedStatus;

    /**
     * 临时字段，店铺销售渠道
     */
    @TableField(exist = false)
    private String accountType;

    @TableField(exist = false)
    private boolean isInventoryInSync;


    /**
     * 排序
     */
    @TableField(exist = false)
    private String orderBy;

    @TableField(exist = false)
    private List<String> orderByParam;

    @TableField(exist = false)
    private Integer minInventory;

    @TableField(exist = false)
    private Integer maxInventory;

    @TableField(exist = false)
    private List<ProductChannelsRestrict> restricts;

    @TableField(exist = false)
    private List<ErpSkuStockVO> skuInventories;

    @TableField(exist = false)
    private List<SkuInventoryShowVO> overShow;


    @TableField(exist = false)
    private Integer skuMinInventory;
    @TableField(exist = false)
    private Integer reviewsCount;
    @TableField(exist = false)
    private String averageRating;
    @TableField(exist = false)
    private String erpSkuQuantity;
    @TableField(exist = false)
    private List<String> erpSkuList = new ArrayList<>();

    /**
     * 是否为主变体
     */
    @Schema(name = "是否为主变体")
    @LogConvert(value = {"是:1", "否:0"})
    private Integer isPrimary;


    @TableField(exist = false)
    private List<String> brandQuery;


    @TableField(exist = false)
    private String currencyPrice;

    @TableField(exist = false)
    private String erpSkuCategory;


    @TableField(exist = false)
    private List<String> erpSkuCategoryQuery;


    @TableField(exist = false)
    private List<String> erpSkuModelQuery;


    @TableField(exist = false)
    private String erpSkuModel;

    @TableField(exist = false)
    private Integer notExistCompany;




    public void settingDBDefaultValue() {
        approvalStatus = Objects.isNull(approvalStatus) ? ApprovalEnum.NEW.getValue() : approvalStatus;
//        price = Objects.isNull(price) ? BigDecimal.ZERO : price;
        name = Objects.isNull(name) ? StrUtil.EMPTY : name;
        currencyCode = Objects.isNull(currencyCode) ? "USD" : currencyCode;
        status = Objects.isNull(status) ? "Y" : status;
        active = Objects.isNull(active) ? "Y" : active;
        channelUrl = Objects.isNull(channelUrl) ? StrUtil.EMPTY : channelUrl;
        quantity = Objects.isNull(quantity) ? BigDecimal.ZERO : quantity;
        yourPrice = Objects.isNull(yourPrice) ? BigDecimal.ZERO : yourPrice;
        salePrice = Objects.isNull(salePrice) ? BigDecimal.ZERO : salePrice;
        listingPrice = Objects.isNull(listingPrice) ? BigDecimal.ZERO : listingPrice;
        preListingPrice = Objects.isNull(preListingPrice) ? BigDecimal.ZERO : preListingPrice;
        yourPriceUsd = Objects.isNull(yourPriceUsd) ? BigDecimal.ZERO : yourPriceUsd;
        lineId = Objects.isNull(lineId) ? 0 : lineId;
        productId = Objects.isNull(productId) ? 0 : productId;
        pushType = Objects.isNull(pushType) ? 0 : pushType;
        fbaOnSite = Objects.isNull(fbaOnSite) ? 0 : fbaOnSite;
        unActiveTime = Objects.isNull(unActiveTime) ? 0 : unActiveTime;
        handelingTime = Objects.isNull(handelingTime) ? 3 : handelingTime;
        property = Objects.isNull(property) ? "NORMAL" : property;
        isWarn = Objects.isNull(isWarn) ? 0 : isWarn;
        businessPriceFlag = !Objects.isNull(businessPriceFlag) && businessPriceFlag;
        erpSku = Objects.isNull(erpSku) ? StrUtil.EMPTY : erpSku;
        asin = Objects.isNull(asin) ? StrUtil.EMPTY : asin;
        asin1 = Objects.isNull(asin1) ? StrUtil.EMPTY : asin1;
        asin2 = Objects.isNull(asin2) ? StrUtil.EMPTY : asin2;
        fnsku = Objects.isNull(fnsku) ? StrUtil.EMPTY : fnsku;
        imageUrl = Objects.isNull(imageUrl) ? StrUtil.EMPTY : imageUrl;
        title = Objects.isNull(title) ? StrUtil.EMPTY : title;
        amazonProductSerialStatus = Objects.isNull(amazonProductSerialStatus) ? 0 : amazonProductSerialStatus;
        ean = Objects.isNull(ean) ? StrUtil.EMPTY : ean;
        upc = Objects.isNull(upc) ? StrUtil.EMPTY : upc;
        if (Objects.isNull(getCreatedBy())) setCreatedBy(0);
        if (Objects.isNull(getUpdatedBy())) setUpdatedBy(0);
        if (Objects.isNull(getDisabledBy())) setDisabledBy(0);
        if (Objects.isNull(getCreatedAt())) setCreatedAt(new Date());
        if (Objects.isNull(getUpdatedAt())) setUpdatedAt(new Date());
        if (Objects.isNull(getCreatedName())) setCreatedName(StrUtil.EMPTY);
        if (Objects.isNull(getUpdatedName())) setUpdatedName(StrUtil.EMPTY);
        if (Objects.isNull(getDisabledName())) setDisabledName(StrUtil.EMPTY);
        disabledAt = Objects.isNull(disabledAt) ? 0 : disabledAt;
        sellerSku = Objects.isNull(sellerSku) ? StrUtil.EMPTY : sellerSku.trim();
        accountId = Objects.isNull(accountId) ? StrUtil.EMPTY : accountId.trim();
    }


    public void settingQueryParam() {
        if (StrUtil.isNotBlank(accountId)) {
            String[] split = accountId.split(",");
            accountIdQuery = Arrays.asList(split);
            accountId = null;
        }
        if (StrUtil.isNotBlank(brand)) {
            String[] split = brand.split(",");
            brandQuery = Arrays.asList(split);
            brand = null;
        }

        if (StrUtil.isNotBlank(investment)) {
            String[] split = investment.split(",");
            investmentQuery = Arrays.asList(split);
            investment = null;
        }

        if (StrUtil.isNotBlank(tags)) {
            tagArr = Stream.of(tags.split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            tags = null;
        }

        if (StrUtil.isNotBlank(restrictedReason)) {
            String[] split = restrictedReason.split(",");
            restrictedReasonQuery = Arrays.asList(split);
            restrictedReason = null;
        }
        if (StrUtil.isNotBlank(saleChannel)) {
            String[] split = saleChannel.split(",");
            saleChannelQuery = Arrays.asList(split);
            saleChannel = null;
        }
        if (StrUtil.isNotBlank(approvalStatus)) {
            String[] split = approvalStatus.split(",");
            approvalStatusQuery = Arrays.asList(split);
            approvalStatus = null;
        }
        if (StrUtil.isNotBlank(sellerSkus)) {
            if (sellerSkus.contains(",")) {
                String[] split = sellerSkus.split(",");
                sellerSkuQuery = Arrays.asList(split);
                sellerSku = null;
            } else {
                sellerSku = sellerSkus;
            }
        }
        if (StrUtil.isNotBlank(operationUserIds)) {
            operationUserIdQuery = Stream.of(operationUserIds.split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            operationUserIds = null;
        }
        if (StrUtil.isNotBlank(asin) && asin.contains(",")) {
            asinsQuery = Stream.of(asin.split(","))
                    .collect(Collectors.toList());
            asin = null;
        }
        if (StrUtil.isNotBlank(erpSku)) {
            erpSkuQuery = Stream.of(erpSku.split(","))
                    .collect(Collectors.toList());
            erpSku = null;
        }
        if (StrUtil.isNotBlank(asin1) && asin1.contains(",")) {
            parentAsinQuery = Stream.of(asin1.split(","))
                    .collect(Collectors.toList());
            asin1 = null;
        }
        if (StrUtil.isNotBlank(sellStatus)) {
            sellStatusQuery = Stream.of(sellStatus.split(","))
                    .collect(Collectors.toList());
            sellStatus = null;
        }
        if (StrUtil.isNotBlank(orderBy)) {
            orderByParam = new ArrayList<>();
            String[] orderBySplit = orderBy.split(",");
            for (String s : orderBySplit) {
                String[] split = s.split("-");
                orderByParam.add(StrUtil.toUnderlineCase(split[0]) + " " + split[1]);
            }
        }
        if (StrUtil.isNotBlank(skcId) && skcId.contains(",")) {
            skcIdQuery = Arrays.asList(skcId.split(","));
        }

        if (StrUtil.isNotBlank(company)) {
            companyQuery = new ArrayList<>();
            for (String s : Arrays.asList(company.split(","))) {
                if (Objects.equals(s, "无")) {
                    notExistCompany = 1;
                }
                companyQuery.add(s);
            }
            company = null;
        }

        if (StrUtil.isNotBlank(erpSkuCategory)) {
            erpSkuCategoryQuery = Arrays.asList(erpSkuCategory.split(","));
            erpSkuCategory = null;
        }
        if (StrUtil.isNotBlank(erpSkuModel)) {
            erpSkuModelQuery = Arrays.asList(erpSkuModel.split(","));
            erpSkuModel = null;
        }


        if (StrUtil.isNotBlank(model)) {
            erpSkuModelQuery = Arrays.asList(model.split(","));
            model = null;
        }

    }


    public void settingMeta(){
        if (StrUtil.isBlank(imageUrl)) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        AccountsSellerResponse.ImageItem imageItem = new AccountsSellerResponse.ImageItem();
        imageItem.setMain(true);
        imageItem.setAttachUrl(imageUrl);
        imageItem.setAttachId(String.valueOf(System.currentTimeMillis()));
        imageItem.setAttachOpAt(now);
        imageItem.setAttachBrief("logo.png");
        imageItem.setAttachTitle("logo.png");

        AccountsSellerResponse.Image image = new AccountsSellerResponse.Image(Arrays.asList(imageItem), now, orgId);
        this.meta = JSON.toJSONString(image);
    }


    @TableField(exist = false)
    private List<ProductChannelsInventory> inventories;


    /**
     * 是否检查接口数据
     */
    @TableField(exist = false)
    private Boolean check;



    @TableField(exist = false)
    private Integer rowIndex;


    @TableField(exist = false)
    private String skuAntQuantity;


    @TableField(exist = false)
    private boolean source;

    @TableField(exist = false)
    private List<Integer> queryIds;

    @TableField(exist = false)
    private List<String> skcIdQuery;


    @TableField(exist = false)
    private List<Long> catIds;

    @TableField(exist = false)
    private BigDecimal minSellerSkuPrice;
    @TableField(exist = false)
    private BigDecimal maxSellerSkuPrice;


    @TableField(exist = false)
    private String[] categoryNameList = new String[]{};


    @TableField(exist = false)
    private boolean isImportData;


    /**
     * 审批意见
     */
    @TableField(exist = false)
    private String approvalAdvice;

    /**
     * 是否可以审批
     */
    @TableField(exist = false)
    private boolean canApproved;

    /**
     * 是否可退回
     */
    @TableField(exist = false)
    private boolean returnable;


    @TableField(exist = false)
    private boolean skip;


    private String tags;

    @TableField(exist = false)
    private List<Integer> tagArr;


    @TableField(exist = false)
    private List<String> instanceIds;
    @TableField(exist = false)
    private List<String> companyQuery;


    /**
     * 更新ASIN信息时使用
     */
    @TableField(exist = false)
    private String apiParentAsin;
    /**
     * 更新ASIN信息时使用
     */
    @TableField(exist = false)
    private String sourceParentAsin;
    @TableField(exist = false)
    private String channelType;


    @TableField(exist = false)
    private PublishTask task;
    @TableField(exist = false)
    private String storeName;

    /**
     * 供货价拼接
     */
    @TableField(exist = false)
    private String sellerSkuPriceStr;


    @TableField(exist = false)
    private String company;

    @TableField(exist = false)
    private String priceWarningType;

    @TableField(exist = false)
    private Integer categoryType;


    private Integer isParentSku;

    @TableField(exist = false)
    private List<SkuChildren> children;

    @TableField(exist = false)
    private List<String> fulfillments;
}
