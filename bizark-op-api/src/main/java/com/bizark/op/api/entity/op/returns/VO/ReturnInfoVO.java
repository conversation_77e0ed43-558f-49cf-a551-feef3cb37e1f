package com.bizark.op.api.entity.op.returns.VO;

import com.bizark.op.api.entity.op.ticket.ScTicketReturnAddress;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 退货信息中框信息VO
 *
 * @Author: Ailill
 * @Date: 2024/3/8 10:11
 */
@Data
public class ReturnInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 组织ID
     */
    private Integer organizationId;

    /**
     * 店铺flag
     */
    private String channelFlag;

    /**
     * 退货订单号(return id)
     */
    private String reverseOrderId;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 退款金额
     */
    private BigDecimal refundTotal;

    /**
     * 币种
     */
    private String currency;

    /**
     * 退货类型
     * CANCEL=1 REFUND_ONLY = 2 RETURN_AND_REFUND = 3 REQUEST_CANCEL = 4
     */
    private Integer reverseType;


    /**
     * 退货类型
     */
    private String returnType;

    /**
     * 状态
     */
    private String status;

    /**
     * 客户单号
     */
    private String customerNumber;

    /**
     * RMA
     */
    private String rma;

    /**
     * 退货原因
     */
    private String returnReason;


    /**
     * 退货描述-原文
     */
    private String originalCustomerComments;

    /**
     * AFTERSALE_APPLYING = 1 AFTERSALE_REJECT_APPLICATION = 2 AFTERSALE_RETURNING = 3 AFTERSALE_BUYER_SHIPPED = 4 AFTERSALE_SELLER_REJECT_RECEIVE = 5 AFTERSALE_SUCCESS = 50 CANCEL_SUCCESS = 51 CLOSED = 99 COMPLETE = 100
     */
    private Integer reverseStatusValue;


    /**
     * 退货请求日期 LocalDateTime
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate reverseRequestTime;


    /**
     * 退货请求日期 LocalDateTime
     */
    private LocalDateTime reverseUpdateTime;

    /**
     * 退货tracking号
     */
    private String returnTrackingId;


    /**
     * 客户评论
     */
    private String customerReviews;


    /**
     * 物流状态
     */
    private String logisticsStatus;


    /**
     * 退货计划单号
     */
    private String saleReturnPlanOrder;


    /**
     * 收货数量
     */
    private Integer receiveNumber;


    /**
     * tracking号流转状态
     */
    private Integer trackingStatus;


    /**
     * 退货状态  0退货中 1已退货 -1拒绝退货
     */
    private Integer returnStatus;


    /**
     * 买家名称
     */
    private String buyName;
    /**
     * 退货承运商
     */
    private String returnCarrier;

    /**
     * 收货数量
     */
    private Integer receiveNum;

    /**
     * 退款状态
     */
    private String refundStatus;


    /**
     * 发票号码
     */
    private String invoiceNum;


    /**
     * 明细数据
     */
    private List<ReturnItemInfoVO> returnItemList;

    /**
     * 退款总金额详情
     */
    private ReturnTotalAmount returnTotalAmount;

    /**
     * 退货地址
     */
    private ScTicketReturnAddress scTicketReturnAddress;

    @Data
    public static class ReturnTotalAmount implements Serializable{

        private String itemPrice;

        private String shipping;

        private String taxes;

        private String refundAmount;
    }
}
