package com.bizark.op.api.enm.mar.claim;

import lombok.Getter;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023年7月4日  15:59
 * @description: 工单节点
 */
@Getter
public enum MarClaimRpaUrlEnum {
    CLAIM_PROVE_GET(1, "证明获取", "/logistics/screenshoot","CLAIM_PROVE_GET"),
    SUB_CLAIM(2, "提交索赔", "/logistics/screenshoot","SUB_CLAIM"),
    GET_CLAIM_RESULT(3, "获取索赔最终结果", "/logistics/screenshoot","GET_CLAIM_RESULT"),
    WFS_RPA_SERVICE(4, "WFS货件是否开启RPA服务", "/wfs/shipmentservice","WFS_RPA_SERVICE"),
    WFS_SET_SHIPMENT(5, "WFS设置货件", "/wfs/setshipment","WFS_SET_SHIPMENT"),
    WFS_CANCEL_FREIGHT(6, "WFS取消运费", "/wfs/cancelfreight","WFS_CANCEL_FREIGHT"),
    ;

    private Integer rpcType;
    private String value;
    private String url;
    private String code;


    MarClaimRpaUrlEnum(Integer rpcType, String value, String url, String code) {
        this.rpcType = rpcType;
        this.value = value;
        this.url = url;
        this.code = code;
    }


    public static MarClaimRpaUrlEnum getByValue(Integer value) {
        return Stream.of(values()).filter(TicketNodeEnum -> TicketNodeEnum.getRpcType().equals(value))
                .findFirst().
                        orElse(null);
    }

}
