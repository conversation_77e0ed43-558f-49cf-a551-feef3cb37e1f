package com.bizark.op.api.service.returns;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.entity.op.order.SaleOrderCancel;
import com.bizark.op.api.entity.op.returns.VO.ReturnReasonPageVO;
import com.bizark.op.api.entity.op.returns.entity.ReturnInfoEntity;
import com.bizark.op.api.entity.op.ticket.VO.ReturnInfoAnalysisVo;
import com.bizark.op.api.entity.op.ticket.VO.ReturnInfoEntityVo;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023年9月19日  14:55
 * @description:
 */
public interface ReturnInfoService extends IService<ReturnInfoEntity> {

    /**
     * 退货信息分页
     *
     * @param returnInfoEntityVo
     * @return
     */
    List<ReturnInfoEntity> getReturnInfoPageList(ReturnInfoEntityVo returnInfoEntityVo);



    /**
     * 更新备注
     *
     * @param contextId
     * @param id
     * @param remark
     */
    void updateRemark(Integer contextId, Integer id, String remark);

    /**
     * 退货原因分页
     *
     * @param contextId
     * @param pageSize
     * @param pageNum
     * @param returnReason
     * @return
     */
    List<ReturnReasonPageVO> getReturnReasonPage(Integer contextId, Integer pageSize, Integer pageNum, String returnReason);

    /**
     * 更新退货原因分类
     *
     * @param contextId
     * @param returnReason
     * @param returnReasonCategory
     * @param authUserEntity
     */
    void updateReturnReasonCategory(Integer contextId, String returnReason, String returnReasonCategory, UserEntity authUserEntity);


    /**
     * Description: 退货原因任务导出
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/8/30
     */
    void returnInfoListExport(HttpServletResponse response, ReturnInfoEntityVo returnInfoEntityVo, UserEntity authUserEntity);




    /**
     *  退货方式：退货方式分析导出
     * @param returnInfoAnalysisVo
     */
    void analysisMethodReport(ReturnInfoAnalysisVo returnInfoAnalysisVo);

    /**
     * 换货信息分页
     *
     * @param returnInfoEntityVo
     * @return
     */
    List<ReturnInfoEntity> getReplaceInfoPageList(ReturnInfoEntityVo returnInfoEntityVo);

    /**
     * Description: 换货信息任务导出
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/8/30
     */
    void replaceInfoListExport(HttpServletResponse response, ReturnInfoEntityVo returnInfoEntityVo, UserEntity authUserEntity);

}
