package com.bizark.op.api.service.promotions;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.entity.op.promotions.AmzPromotionsCoupon;
import com.bizark.op.api.entity.op.seller.AmzCoupon;
import com.bizark.op.api.vo.promotions.AmzPromotionsCouponDetailVO;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 优惠券管理Service接口
 *
 * <AUTHOR>
 * @date 2023-06-13
 */
public interface IAmzPromotionsCouponService extends IService<AmzPromotionsCoupon> {
    /**
     * 查询优惠券管理
     *
     * @param id 优惠券管理ID
     * @return 优惠券管理
     */
    AmzPromotionsCoupon selectAmzPromotionsCouponById(Long id);

    /**
     * 查询优惠券管理列表
     *
     * @param amzPromotionsCoupon 优惠券管理
     * @return 优惠券管理集合
     */
    List<AmzPromotionsCoupon> selectAmzPromotionsCouponList(AmzPromotionsCoupon amzPromotionsCoupon);

    /**
     * 新增优惠券管理
     *
     * @param amzPromotionsCoupon 优惠券管理
     * @return 结果
     */
    int insertAmzPromotionsCoupon(AmzPromotionsCoupon amzPromotionsCoupon);

    /**
     * 修改优惠券管理
     *
     * @param amzPromotionsCoupon 优惠券管理
     * @return 结果
     */
    int updateAmzPromotionsCoupon(AmzPromotionsCoupon amzPromotionsCoupon);

    /**
     * 批量删除优惠券管理
     *
     * @param ids 需要删除的优惠券管理ID
     * @return 结果
     */
    int deleteAmzPromotionsCouponByIds(Long[] ids);

    /**
     * 删除优惠券管理信息
     *
     * @param id 优惠券管理ID
     * @return 结果
     */
    //int deleteAmzPromotionsCouponById(Long id);

    /**
     * 删除优惠券管理信息
     *
     * @param id             优惠券管理ID
     * @param authUserEntity
     * @return 结果
     */
    void logicRemoveAmzPromotionsCouponById(Long id, UserEntity authUserEntity);


    /**
     * 查询优惠券详情
     *
     * @param id
     * @return
     */
    AmzPromotionsCouponDetailVO selectAmzPromotionsCouponDetailById(Long id);


    /**
     * 通过优惠券标题查询
     *
     * @param couponTitle
     * @return
     */
    AmzPromotionsCoupon selectAmzPromotionsCouponByCouponTitle(String couponTitle);


    /**
     * 通过优惠券名称查询优惠券
     *
     * @param couponName
     * @return
     */
    AmzPromotionsCoupon selectAmzPromotionsCouponByCouponName(@Param("couponName") String couponName);

    /**
     * 通过优惠券状态查询优惠券
     *
     * @param couponState
     * @return
     */
    List<AmzPromotionsCoupon> selectCouponByCouponState(Integer[] couponState);

    /**
     * 取消优惠券
     *
     * @param id
     * @param createdName
     * @param remark
     * @return
     */
    int cancelCouponById(Long id, String createdName, String couponName, Integer contextId, Integer updatedBy, String remark);

    /**
     * 优惠券状态为提交异常，支持直接提交。
     *
     * @param id
     * @param createdName
     * @return
     */
    int submitExceptionSubmit(Long id, String createdName, Integer contextId, Integer updatedBy);


    /**
     * SC渠道优惠券状态为Running、修改异常.   修改优惠券
     *
     * @param id
     * @param createdName
     * @param budget
     * @param endDate
     * @return
     */
    void runningOrModifyExceptionUpdate(Long id, String createdName, String budget, String endDate, Integer contextId, Integer updatedBy);
    Long processRunningOrModifyExceptionUpdate(Long id, String createdName, String budget, String endDate, Integer contextId, Integer updatedBy);

    int updateExpiringSoon(Long couponId);

    /**
     * 批量更新coupon的到期日
     */
    void batchUpdateExpiringSoon();

    /**
     * 同步coupon
     *
     * @param amzCoupon
     * @return
     */
    int syncSaveCoupon(AmzCoupon amzCoupon, Long couponsId, Date date, Long campaignId);

    /**
     * 同步coupon
     *
     * @param amzCoupon
     * @return
     */
    int syncSaveCouponHistory(AmzCoupon amzCoupon, Long couponsId, Date date, Long campaignId);

    /**
     * 同步coupon每日明细
     *
     * @param amzCoupon amz_coupon
     * @param couponId  amz_promotions_coupon主键id
     * @return
     */
    int syncSaveCouponDailyDetails(AmzCoupon amzCoupon, Long couponId, Date date);

    /**
     * 优惠券状态为提交异常直接提交,或者  Running、修改异常 消息发送
     */
    void runningOrModifyExceptionUpdateCouponMessageSend(Long couponsId, AmzPromotionsCoupon coupon, Integer type);

    /**
     * 修改优惠券时使用
     *
     * @param coupons
     * @param c
     * @param updateType
     */
    public void setCacheKey(Long couponsId, AmzPromotionsCoupon c, Integer updateType);

    void modifyRemark(Long id, String remark);
}
