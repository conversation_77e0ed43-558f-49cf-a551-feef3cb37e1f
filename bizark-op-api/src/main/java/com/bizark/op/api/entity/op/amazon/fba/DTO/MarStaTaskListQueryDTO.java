package com.bizark.op.api.entity.op.amazon.fba.DTO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @ClassName MarStaTaskListQueryDTO
 * @Description STA列表查询请求
 * <AUTHOR>
 * @Date 2025/7/24 10:42
 */
@Data
@Schema(description = "STA列表查询参数")
public class MarStaTaskListQueryDTO {
    /**
     * 组织ID
     */
    @Schema(description = "组织ID")
    private Long orgId;

    @Schema(description = "国家")
    private List<String> countryList;

    @Schema(description = "店铺")
    private List<Long> shopIdList;

    @Schema(description = "状态")
    private List<String> taskStatusList;

    /**
     * 1.选择发货商品 2商品装箱 3.配送服务 4.箱子标签 5.货件追踪
     */
    @Schema(description = "任务步骤")
    private List<Long> taskNodeList;

    @Schema(description = "STA创建时间From")
    private String staCreatedAtFrom;

    @Schema(description = "STA创建时间To")
    private String staCreatedAtTo;

    @Schema(description = "货件单号")
    private List<String> shipmentConfirmationIdList;

    @Schema(description = "任务名")
    private List<String> taskNameList;









}