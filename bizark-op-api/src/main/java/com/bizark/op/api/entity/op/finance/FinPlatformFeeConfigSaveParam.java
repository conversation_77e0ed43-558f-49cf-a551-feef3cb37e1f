package com.bizark.op.api.entity.op.finance;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class FinPlatformFeeConfigSaveParam {
    // 数据ID 更新时为空
    private Integer id;
    private Integer organizationId;
    private Integer keyType;

    //店铺id
    private Integer shopId;
    // SellerSku
    private String keyStr;
    // 销售渠道
    private String saleChannel;
    @Min(1)
    @Max(2)
    // 费用类型 1-佣金, 2-AFN尾程配送费
    private Integer feeType;
    @Min(1)
    @Max(2)
    // 配置方式: 1:比例 2:金额
    private Integer configType;
    // 金额
    private BigDecimal config;
    // 币种
    private String currency;
    // 生效开始时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date startDate;
    // 生效结束日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date endDate;

    /**
     * sku
     */
    private String sku;
}
