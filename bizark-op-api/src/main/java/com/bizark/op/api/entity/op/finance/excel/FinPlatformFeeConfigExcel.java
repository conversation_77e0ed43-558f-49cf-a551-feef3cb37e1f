package com.bizark.op.api.entity.op.finance.excel;

import com.bizark.op.common.annotation.Excel;
import com.bizark.op.common.annotation.ExcelHead;
import lombok.Data;

import java.util.Date;

@Data
public class FinPlatformFeeConfigExcel {
    /**
     * 主键ID
     */
//    @ExcelHead(name = "id", hidden = false, width = 20, required = false)
    @Excel(name = "id")
    private Integer id;
    //    @ExcelHead(name = "SellerSku", hidden = false, width = 20, required = false)
    @Excel(name = "SellerSku")
    private String keyStr;
    //    @ExcelHead(name = "店铺名", hidden = false, width = 20, required = true)
    @Excel(name = "店铺名")
    private String shopName;
    //    @ExcelHead(name = "费用类型", hidden = false, width = 20, required = true)
    @Excel(name = "费用类型")
    private String feeType;
    //    @ExcelHead(name = "配置方式", hidden = false, width = 20, required = true)
    @Excel(name = "配置方式")
    private String configType;
    //    @ExcelHead(name = "费用比例（%）", hidden = false, width = 20, required = false)
    @Excel(name = "费用比例（%）")
    private String feePercentage;
    //    @ExcelHead(name = "费用金额", hidden = false, width = 20, required = false)
    @Excel(name = "费用金额")
    private String fee;
    //    @ExcelHead(name = "币种", hidden = false, width = 20, required = false)
    @Excel(name = "币种")
    private String currency;
    //    @ExcelHead(name = "生效开始时间", hidden = false, width = 20, required = true)
    @Excel(name = "生效开始时间",dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;
    //   @ExcelHead(name = "生效结束时间", hidden = false, width = 20, required = true)
    @Excel(name = "生效结束时间",dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    @Excel(name = "Sku")
    private String sku;

    @Excel(name = "渠道")
    private String saleChannel;
//    /**
//     * 转换后 时间
//     */
//    private Date transitionStartDate;
//    /**
//     * 转换后 时间
//     */
//    private Date transitionEndDate;
}
