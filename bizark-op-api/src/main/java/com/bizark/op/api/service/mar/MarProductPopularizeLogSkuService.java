package com.bizark.op.api.service.mar;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.entity.op.mar.MarListingQuery;
import com.bizark.op.api.entity.op.mar.MarProductPopularizeLogSku;
import com.bizark.op.api.vo.mar.MarProductPopularizeLogAnalysisItemVo;
import com.bizark.op.api.vo.mar.AdAnalysisVo;
import com.bizark.op.api.vo.mar.MarProductPopularizeLogSkuVo;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
*
*/
public interface MarProductPopularizeLogSkuService extends IService<MarProductPopularizeLogSku> {



    /**
     * @description: Listing整合SKU维度数据
     * @author: Moore
     * @date: 2024/2/18 15:38
     * @param
     * @param marListingQuery
     * @return: java.util.List<com.bizark.op.api.entity.op.mar.MarProductPopularizeLogSku>
    **/
    List<MarProductPopularizeLogSkuVo> selectPopularizeLogBySKuList(MarListingQuery marListingQuery);


    /**
     * Description: 综合分析列表
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/2/20
     */
    List<MarProductPopularizeLogAnalysisItemVo> analysisQueryPopularizeLogBySKuList(MarListingQuery marListingQuery);

    /**
     * Description: 广告分析 列表
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/2/20
     */
    List<AdAnalysisVo> adAnalysisQueryPopularizeLogBySKuList(MarListingQuery marListingQuery);


    /**
     * Description: 广告分析 折线图
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/2/21
     */
    JSONObject adAnalysisQueryPopularizeLogBySKuLineChart(MarListingQuery marListingQuery);


    /**
     * Description: 导出
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/2/29
     */
    void listExport(HttpServletResponse response, MarListingQuery marListingQuery, UserEntity authUserEntity);





    /**
     * Description: 根据条件查询环比
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/3/5
     */
    List<MarProductPopularizeLogSkuVo>  selectQoQByParam(MarListingQuery marListingQuery);


    /**
     * Description: 根据条件查询7天平均销量
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/3/5
     */
    List<MarProductPopularizeLogSkuVo>  selectSeventDayAvgByParam(MarListingQuery marListingQuery);

    /**
     * Description: 根据条件查询14天平均销量
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/3/5
     */
    List<MarProductPopularizeLogSkuVo>  selectFourteenDayAvgByParam(MarListingQuery marListingQuery);

    /**
     * Description: 查询七天十四天可售数
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/3/6
     */
    List<MarProductPopularizeLogSkuVo>  selectAbleSaleAvgByParam(MarListingQuery marListingQuery);


    /**
     * Description: 外部接口调用
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/7/17
     */
    JSONObject queryPopularizeLogBySKuList(MarListingQuery marListingQuery);


    void newListExportSku(HttpServletResponse response, MarListingQuery marListingQuery, UserEntity authUserEntity);
}
