package com.bizark.op.api.entity.op.returns;

import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR> @ClassName StatReturnQuery
 * @description: 退货请求Entity
 * @date 2024年04月10日
 */
@Data
public class StatReturnQuery {

    /**
     * 组织ID
     */
    private Long orgId;

    /**
     * 维度信息   0：asin 1:sku 2：店铺 3：渠道 4：退货原因 5:品类,6:平台退货原因
     */
    private Integer type;

    /**
     * 时间维度 DAY（日） WEEK（周） MONTH（月）
     */
    private String dateType;

    /**
     * 店铺
     */
    private List<Integer> shopIdList;


    /**
     * 店铺名
     */
    private List<String> shopNameList;


    /**
     * 开始时间
     */
    private String returnDateStart;


    /**
     * 结束时间
     */
    private String returnDateEnd;


    /**
     * 订单开始时间
     */
    private String orderDateStart;

    /**
     * 订单结束时间
     */
    private String orderDateEnd;


    /**
     * asin
     */
    private List<String> asinList;



    /**
     * asin
     */
    private String asin;


    /**
     * sku空Flag
     */
    private String skuFlag;

    /**
     * sku
     */
    private List<String> skuList;

    /**
     * sku单查询
     */
    private String sku;

    /**
     * 运营ID
     */
    private List<Integer> operateIdList;

    /**
     * 运营名称
     */
    private List<String> operateNameList;


    /**
     * 分类ID
     */
    List<Integer> categoryIdList;

    /**
     * 分类空判断
     */
    private String categoryFlag;




    /**
     * 分类名称
     */
    private List<String> categoryNameList;


    /**
     * 时间分组 日标识
     */
    private String groupTimeFlag;

    /**
     * 时间分组 月标识
     */
    private String groupTimeMonthFlag;



    /**
     * 拼接sql内容
     */
    private String sqlContent;

    /**
     * 排序字段
     */
    private String sidx;

    /**
     * 排序方式
     */
    private String sord;

    /**
     * 渠道
     */
    List<String> channelList;

    /**
     * 退货原因
     */
    List<Integer> problemIdList;


    /**
     * sf需要统计销量有值就统计，没就不统计
     */
    private String ifSalesQuantity;


    private String apiType;//SUM：汇总业务 ，ITEM:为明细行业务

    private String avgFlag;//是否计算月平均值 :是：Y，否：N

    private String withoutAsin;//无asin


    private String withoutSku;//无sku

    private String returnReason;

    private String withoutProblem;//无退货原因

    private String withoutReturnReason;//无平台退货原因

    private String withoutCategory;//无品类

    private Boolean ifExport;//是否是导出调用

}
