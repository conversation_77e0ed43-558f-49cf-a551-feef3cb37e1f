package com.bizark.op.api.entity.op.sale;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bizark.op.common.annotation.DictConvert;
import com.bizark.op.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@TableName(excludeProperty = "remark",value = "dashboard.temu_violation")
public class TemuViolation extends BaseEntity {

    private Long id;

    private Integer orgId;

    @Schema(description = "违规单号")
    private String violationAppealSn;

    @TableField(exist = false)
    @DictConvert(dict = "temu_violation_type", target = "violationType")
    private String violationName;

    @Schema(description = "违规类型")
    private Integer violationType;


    @Schema(description = "违规数量")
    private Integer poCount;

    @Schema(description = "预估违规金额")
    private String exceptedFullAmount;

    @Schema(description = "预估违规金额")
    private BigDecimal exceptedAmount;

    @Schema(description = "实际违规金额")
    private String actualFullAmount;

    @Schema(description = "实际违规金额")
    private BigDecimal actualAmount;

    @DictConvert(dict = "temu_violation_exception_type",target = "hitTypeStatus")
    private String hitType;

    @Schema(description = "异常类型")
    private Integer hitTypeStatus;

    @Schema(description = "申诉时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastAppealTime;

    @Schema(description = "店铺ID")
    private Integer accountId;

    @Schema(description = "店铺Flag")
    private String accountFlag;

    @Schema(description = "违规通知时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date informTime;

    private Integer appealStatus;

    @TableField(exist = false)
    @DictConvert(dict = "temu_violation_handle_process",target = "appealStatus")
    private String appealName;


    @TableLogic(value = "0", delval = "1")
    private boolean isDelete;


    @TableField(exist = false)
    private Integer totalPoCount;

    @TableField(exist = false)
    private BigDecimal totalExceptedAmount;

    @TableField(exist = false)
    private BigDecimal totalActualAmount;

    @TableField(exist = false)
    private String accountTitle;

    @TableField(exist = false)
    private List<TemuViolationDetail> violationDetails;


    @TableField(exist = false)
    private Map<String,Integer> violationCounts;
}
