package com.bizark.op.api.entity.op.amazon.fba.DTO;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-08-19 16:17
 **/
@Data
public class FbaSkuDTO {

    private Long id;

    /**
     * 图片URL
     */
    private String imageUrl;

    /**
     * seller_sku
     */
    private String sellerSku;

    /**
     * FNSKU
     */
    private String fnSku;

    /**
     * ASIN
     */
    private String asin;

    /**
     * ParentASIN
     */
    private String parentAsin;

    /**
     * 标题
     */
    private String title;

    /**
     * 款号
     */
    private String erpSku;

    /**
     * 品名
     */
    private String productName;

    /**
     * 预处理方 AMAZON_ONLY(亚马逊) NONE_ONLY(无)  SELLER_ONLY(卖家)
     */
    private String labelVendor;

    /**
     * 贴标方式 AMAZON_ONLY(亚马逊贴标) NONE_ONLY(无标) SELLER_ONLY(卖家贴标)
     */
    private String labelType;

    private Integer boxedQuantity = 0;

    /**
     * 申报数量
     */
    private Integer applyNum = 0;
}
