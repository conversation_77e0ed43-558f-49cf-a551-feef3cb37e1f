package com.bizark.op.api.entity.op.amazon.fba.DTO;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.List;


/**
 * @ClassName MarStaTaskPackingInfoDTO
 * @Description 预处理校验DTO
 * <AUTHOR>
 * @Date 2025/7/24 10:45
 */
@Schema(description = "预处理校验DTO")
@Data
public class MarStaTaskPreVerifyDTO {

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 校验Type 校验预处理方式 1 为第一步 2 为第二步 （此字段仅校验使用）
     */
    private Integer verifyType;

    /**
     * 商品明细行信息
     */
    private List<MarStaShipmentSkusDTO> shipmentSkusList;

}
