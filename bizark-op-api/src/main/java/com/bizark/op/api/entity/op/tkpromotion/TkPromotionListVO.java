package com.bizark.op.api.entity.op.tkpromotion;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bizark.op.common.annotation.DictConvert;
import com.bizark.op.common.converter.CustomDateConverter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * tk促销列表返回值对象
 * @Author: Ailill
 * @Date: 2025-07-21 18:26
 */
@Data
@ExcelIgnoreUnannotated
@ColumnWidth(40)
public class TkPromotionListVO implements Serializable {


    private Long id;

    /**
     * 组织id
     */
    private Integer organizationId;

    /**
     * 活动名称
     */
    @ExcelProperty(value = "活动名称",order = 1)
    private String activeName;

    /**
     * 活动ID
     */
    @ExcelProperty(value = "活动ID",order = 2)
    private String activeId;

    /**
     * 审批状态
     */
    @ExcelProperty(value = "审批状态",order = 3)
    private String approvalStatus;

    /**
     * 工作流实例ID
     */
    private String instanceId;

    /**
     * 活动状态
     */
    @ExcelProperty(value = "活动状态",order = 4)
    @DictConvert(target = "activityStatus",dict = "tk_promotion_active_status")
    private String activeStatus;

    /**
     * 后台活动类型
     */
    @ExcelProperty(value = "活动类型",order = 5)
    private String activityType;

    /**
     * duration_type
     */
    private String durationType;

    /**
     * 店铺id
     */
    private Integer shopId;

    /**
     * 店铺名称
     */
    @ExcelProperty(value = "店铺",order = 6)
    private String shopName;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "开始时间",converter = CustomDateConverter.class,order = 7)
    private Date beginTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "结束时间",converter = CustomDateConverter.class,order = 8)
    private Date endTime;

    /**
     * 促销对象 VARIATION:商品ID维度,PRODUCT:父商品ID维度,SHOP:店铺维度
     */
    @ExcelProperty(value = "促销对象",order = 9)
    private String productLevel;

    /**
     * 总购买限制
     */
    private String participationLimit;

    /**
     * 活动创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "创建时间",converter = CustomDateConverter.class,order = 27)
    private Date createTime;

    /**
     * 活动更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "更新时间",converter = CustomDateConverter.class,order = 26)
    private Date updateTime;


    /**
     * 备注
     */
    private String remark;

    /**
     * 运营
     */
    @ExcelProperty(value = "运营",order = 25)
    private String operator;

    /**
     * 创建人ID
     */
    private Integer createdBy;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人",order = 28)
    private String createdName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    private List<TkPromotionListDetailParentVO> productItemList;




    /**
     * 商品图片
     */
    @ExcelProperty(value = "商品信息",order = 10)
    @JsonIgnore
    private String productImg;


    /**
     * 父商品ID
     */
    @ExcelProperty(value = "父商品ID",order = 11)
    @JsonIgnore
    private String productId;



    /**
     * 商品ID
     */
    @ExcelProperty(value = "商品ID",order = 12)
    @JsonIgnore
    private String skuId;

    /**
     * sellerSku
     */
    @ExcelProperty(value = "seller sku",order = 13)
    @JsonIgnore
    private String sellerSku;

    /**
     * sku
     */
    @ExcelProperty(value = "sku",order = 14)
    @JsonIgnore
    private String sku;



    /**
     * 供货价
     */
    @ExcelProperty(value = "供货价",order = 15)
    @JsonIgnore
    private BigDecimal supplyPrice;

    /**
     * 核定建议供货价
     */
    @ExcelProperty(value = "核定建议供货价",order = 16)
    @JsonIgnore
    private BigDecimal suggestedSupplyPrice;

    /**
     * 最低建议供货价
     */
    @ExcelProperty(value = "最低建议供货价",order = 17)
    @JsonIgnore
    private BigDecimal lowestSuggestedSupplyPrice;

    /**
     * 库存
     */
    @ExcelProperty(value = "库存",order = 18)
    @JsonIgnore
    private Integer stock;

    /**
     * 达人佣金
     */
    @ExcelProperty(value = "达人佣金",order = 19)
    @JsonIgnore
    private String kolCommission;



    /**
     * 折扣
     */
    @ExcelProperty(value = "折扣",order = 20)
    @JsonIgnore
    private String discount;



    /**
     * 折后价
     */
    @ExcelProperty(value = "折后价格",order = 21)
    @JsonIgnore
    private BigDecimal priceAfterDiscount;

    /**
     * 价格预警
     */
    @ExcelProperty(value = "价格预警",order = 22)
    @JsonIgnore
    private String priceWarn;


    /**
     * 总购买限制
     */
    @ExcelProperty(value = "总购买限制",order = 23)
    private String totalBuyLimit;

    /**
     * 客户购买限制
     */
    @ExcelProperty(value = "客户购买限制",order = 24)
    @JsonIgnore
    private String customerBuyLimit;


}
