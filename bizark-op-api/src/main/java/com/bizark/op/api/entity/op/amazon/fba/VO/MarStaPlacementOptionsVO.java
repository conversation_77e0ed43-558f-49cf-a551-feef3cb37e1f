package com.bizark.op.api.entity.op.amazon.fba.VO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> @ClassName MarStaPlacementOptionsVO
 * @description: 预览弹窗信息
 * @date 2025年08月18日
 */
public class MarStaPlacementOptionsVO {


    /**
     * 归属组信息
     */
    private Integer groupNo;


    /**
     * 货件编号
     */
    private String shipmentId;

    /**
     * 选项ID
     */
    private String placementOptionId;


    /**
     * 费用
     */
    private String fee;


    /**
     * 币种
     */
    private String currency;


   /**
     * 入库区域
     */
    private String warehouseArea;


    /**
     * 物流中心编号 warehouseId
     */
    private String warehouseId;


    /** 联系人
     *
     */
    private String name;


    /**
     * 地址1
     */
    private String addressLine1;

    /**
     * 地址2
     */
    private String addressLine2;

    /**
     * 城市
     */
    private String city;


    /**
     * 州/省/地区
     */
    private String stateOrProvinceCode;

    /**
     * 邮编
     */
    private String postalCode;
    /**
     * 国家
     */
    private String countryCode;


    /**
     * 联系方式
     */
    private String phoneNumber;





    /**
     * 重量
     */
    private BigDecimal weight;


    /**
     * 体积
     */
    private BigDecimal volume;

    /**
     * sku数量
     */
    private Integer skuNum;

    /**
     * 件数
     */
    private Integer pcsNum;


    private List<GoodsInfo> goodsInfoList;


    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getPlacementOptionId() {
        return placementOptionId;
    }

    public void setPlacementOptionId(String placementOptionId) {
        this.placementOptionId = placementOptionId;
    }

    public   static class GoodsInfo{

        private String imageUrl;
        private String msku;
        private String title;
        private String fnSku;
        private String asin;
        private Integer applyNum;

        private BigDecimal length;
        private BigDecimal width;
        private BigDecimal height;
        private BigDecimal weight;


        public String getImageUrl() {
            return imageUrl;
        }

        public void setImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        public String getMsku() {
            return msku;
        }

        public void setMsku(String msku) {
            this.msku = msku;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getFnSku() {
            return fnSku;
        }

        public void setFnSku(String fnSku) {
            this.fnSku = fnSku;
        }

        public String getAsin() {
            return asin;
        }

        public void setAsin(String asin) {
            this.asin = asin;
        }

        public Integer getApplyNum() {
            return applyNum;
        }

        public void setApplyNum(Integer applyNum) {
            this.applyNum = applyNum;
        }

        public BigDecimal getLength() {
            return length;
        }

        public void setLength(BigDecimal length) {
            this.length = length;
        }

        public BigDecimal getWidth() {
            return width;
        }

        public void setWidth(BigDecimal width) {
            this.width = width;
        }

        public BigDecimal getHeight() {
            return height;
        }

        public void setHeight(BigDecimal height) {
            this.height = height;
        }

        public BigDecimal getWeight() {
            return weight;
        }

        public void setWeight(BigDecimal weight) {
            this.weight = weight;
        }
    }


    public Integer getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(Integer groupNo) {
        this.groupNo = groupNo;
    }

    public String getShipmentId() {
        return shipmentId;
    }

    public void setShipmentId(String shipmentId) {
        this.shipmentId = shipmentId;
    }

    public String getFee() {
        return fee;
    }

    public void setFee(String fee) {
        this.fee = fee;
    }

    public String getWarehouseArea() {
        return warehouseArea;
    }

    public void setWarehouseArea(String warehouseArea) {
        this.warehouseArea = warehouseArea;
    }

    public String getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddressLine2() {
        return addressLine2;
    }

    public void setAddressLine2(String addressLine2) {
        this.addressLine2 = addressLine2;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getAddressLine1() {
        return addressLine1;
    }

    public void setAddressLine1(String addressLine1) {
        this.addressLine1 = addressLine1;
    }

    public String getStateOrProvinceCode() {
        return stateOrProvinceCode;
    }

    public void setStateOrProvinceCode(String stateOrProvinceCode) {
        this.stateOrProvinceCode = stateOrProvinceCode;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    public Integer getSkuNum() {
        return skuNum;
    }

    public void setSkuNum(Integer skuNum) {
        this.skuNum = skuNum;
    }

    public Integer getPcsNum() {
        return pcsNum;
    }

    public void setPcsNum(Integer pcsNum) {
        this.pcsNum = pcsNum;
    }

    public List<GoodsInfo> getGoodsInfoList() {
        return goodsInfoList;
    }

    public void setGoodsInfoList(List<GoodsInfo> goodsInfoList) {
        this.goodsInfoList = goodsInfoList;
    }
}
