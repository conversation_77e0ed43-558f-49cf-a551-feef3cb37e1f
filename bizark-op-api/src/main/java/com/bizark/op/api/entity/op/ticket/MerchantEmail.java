package com.bizark.op.api.entity.op.ticket;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bizark.op.common.annotation.BindDBField;
import com.bizark.op.common.annotation.Excel;
import com.bizark.op.common.converter.CustomDateConverterDate;
import com.bizark.op.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 邮箱配置对象 conf_merchant_email
 *
 * <AUTHOR>
 * @date 2022-01-12
 */
@Data
@ExcelIgnoreUnannotated
@ColumnWidth(25)

@TableName(value = "erp.conf_merchant_email",
        excludeProperty = {
                "params", "searchValue", "purchaseOrderNumber", "remark"
        })
public class MerchantEmail extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @Excel(name = "编号")
    private Long id;

    private Integer organizationId;

    /**
     * 邮件地址
     */
    @Schema(description = "邮件地址")
    @Excel(name = "邮件地址")
    private String email;

    /**
     * 邮件密码或授权码
     */
    @Schema(description = "邮件密码或授权码")
    @Excel(name = "邮件密码或授权码")
    private String password;

    /**
     * 邮件类型(1店铺邮箱 2站外邮箱)
     */
    @Schema(description = "邮件类型(1店铺邮箱 2站外邮箱)")
    @Excel(name = "邮件类型", readConverterExp = "1=店铺邮箱,2=站外邮箱")
    private String emailType;

    @TableField(exist = false)
    @BindDBField(dict = "email_type", relation = "emailType")
    private String emailTypeDict;


    /**
     * 邮件服务商
     */
    @Excel(name = "邮件服务商")
    private String serviceCode;

    /**
     * 所属店铺
     */
    @Schema(description = "所属店铺")
    private Long shopId;

    @TableField(exist = false)
    private String accountInit;

    @Excel(name = "所属店铺")
    @TableField(exist = false)
    private String flag;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    private String projectName;

    /**
     * 所属商户
     */
    @Schema(description = "所属商户")
    private Long merchantId;

    /**
     * 启用(Y:启用 N:停用)
     */
    @Schema(description = "启用(Y:启用 N:停用)")
    @Excel(name ="状态", readConverterExp = "Y=启用,N=关闭,y=启用,n=关闭")
    private String enabledFlag;


    @TableField(exist = false)
    private String enabledFlagDict;

    private String emailUse;


    @TableField(exist = false)
    @Excel(name = "邮箱用途")
    private String emailUseName;


    @Schema(description = "项目ID")
    private Integer projectId;

    /**
     * 上次同步开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "上次同步开始时间")
    @Excel(name = "上次同步开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date beginTime;

    /**
     * 最近同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "最近同步时间")
    @Excel(name = "最近同步时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastSyncTime;

    /**
     * 渠道
     */
    @TableField(exist = false)
    private String type;

    /**
     * 渠道
     */
    @TableField(exist = false)
    private String storeName;

    private String clientId;

    private String clientSecret;

    private String refreshToken;

    /**
     * 店铺名
     */
    @TableField(exist = false)
    private String title;


    /**
     * 店铺查询
     */
    @TableField(exist = false)
    private List<Long> shopIds;

}
