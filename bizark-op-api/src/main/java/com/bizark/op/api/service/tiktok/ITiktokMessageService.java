package com.bizark.op.api.service.tiktok;


import com.alibaba.fastjson.JSONObject;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.mar.MarTkCouponInfo;
import com.bizark.op.api.entity.op.mar.contactCustomer.entity.MarSendMessageOrders;
import com.bizark.op.api.entity.op.ticket.*;
import com.bizark.op.api.entity.op.ticket.email.ScEmailSend;
import com.bizark.op.api.entity.op.titok.request.message.MessageSendRequest;
import com.bizark.op.api.entity.op.titok.response.message.Msg;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023 08 29 21 05
 **/
@Service

public interface ITiktokMessageService {


    /**
     * 回复消息
     *
     * @param entity
     * @return
     */
    int sendTiktokConvContentMessage(MessageSendRequest entity);



    /**
     * @param
     * @description: 获取tiktok会话列表
     * @author: Moore
     * @date: 2023/10/16 23:23
     * @return: void
     **/
    void getTiktokConvList();


    /**
     * 手动调用
     *
     * @param speed
     */
    void getTiktokConvManualList(Integer shopId,Integer speed);


    /**
     * @param shopId 店铺ID
     * @param convId 会话ID
     * @description: 根据店铺ID会话ID获取对应会话列表
     * @author: Moore
     * @date: 2023/10/16 23:23
     * @return: void
     **/
    void getTiktokConvListByShopIdAndConvId(Account account, String convId);


    /**
     * @param account     店铺信息
     * @param convShortId 回话ID
     * @description: 获取回话内容
     * @author: Moore
     * @date: 2023/10/18 10:25
     * @return: void
     **/
    void getTikTokConversationContent(Account account, String convShortId);


    /**
     * @param contextId 组织ID
     * @param ticketId  工单ID
     * @param hisFlag   是否为查询历史 N:不查询 Y：查询
     * @description: 获取回话列表
     * @author: Moore
     * @date: 2023/10/18 16:55
     * @return:
     **/
    List<ScStationLetterMessage> selectTikcetConversationContent(Integer contextId,
                                                                 Long ticketId,
                                                                 String hisFlag);


    /**
     * @param ticketId  工单ID
     * @description: 获取未读用户消息
     * @author: Moore
     * @date: 2023/10/18 16:55
     * @return:
     **/
    List<String> selectTikcetConversationConByBuyer(Long ticketId);

    /**
     * @param
     * @param
     * @param contextId 组织ID
     * @param ticketId 工单ID
     * @param convShortId 会话ID
     * @param shopId 店铺ID
     * @description: 刷新TIKTOK会话信息
     * @author: Moore
     * @date: 2023/10/19 15:46
     * @return: java.lang.String
     **/
    void refreshConvContent(Long shopId, Integer contextId, Long sourceId, String convShortId, Long ticketId);



    /**
     * @description: 刷新会话为已读
     * @author: Moore
     * @date: 2025/1/9 11:20
     * @param
     * @param shopId
     * @param contextId
     * @param convShortId
     * @param ticketId
     * @return: void
    **/
    void refreshConvMsgReadFlag(Long shopId, Integer contextId, String convShortId, Long ticketId);

    /**
     * @description: 创建会话并发送消息
     * @author: Moore
     * @date: 2023/10/26 17:03
     * @param
     * @param message  发送消息Body
     * @return: void
     **/
    void createTiktokConvAndSendMessage(Long ticketId,MessageSendRequest message, UserEntity authUserEntity);

    /**
     * @description: 校验是否允许创建会话
     * @author: Moore
     * @date: 2023/10/26 17:03
     * @param
     * @param contextId  组织ID
     * @param ticketId  工单ID
     * @return: void
     **/
    ScTicket createTiktokConvAndSendMessageVerify(Integer contextId, Long ticketId);


    /**
     * @param
     * @param convShortId
     * @description: 获取指定会话ID，会话内容 -（非业务接口）
     * @author: Moore
     * @date: 2023/10/31 23:49
     * @return: void
     **/
    List<Msg> getConvShortIdContent(String convShortId, Integer contentType);


    /**
     * @description: 上传文件至TIKTOKOSS
     * @author: Moore
     * @date: 2023/11/15 18:00
     * @param
     * @param shopId 店铺ID
     * @return: com.alibaba.fastjson.JSONObject
    **/
    List<JSONObject> uploadToTiktokOss(MultipartFile[] file,Long shopId);



    /**
     * @description: 重试发送消息
     * @author: Moore
     * @date: 2023/11/21 16:08
     * @param
     * @param message
     * @param authUserEntity
     * @return: void
    **/
    int sendTiktokConvContentRetryMessage(MessageSendRequest message, UserEntity authUserEntity);



    /**
     * @description: 接收WebHookMessage消息
     * @author: Moore
     * @date: 2023/11/21 16:08
     * @param
     * @param
     * @param
     * @return: void
     **/
    void saveWebHookMessage(JSONObject jsonObject);

    /**
     * @Description:创建会话并发送消息
     * @Author: wly
     * @Date: 2024/6/26 17:48
     * @Params: [ticketId, message, authUserEntity]
     * @Return: void
     **/
    String createTiktokConvAndSendMessageNew(MarSendMessageOrders message, UserEntity authUserEntity);

    /**
     * 回复消息
     *
     * @param request
     * @return
     */
    String sendTiktokConvContentMessageNew(MarSendMessageOrders request);



    /**
     * Description: 接收tk的站内信消息
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/9/9
     */
    void saveTiktokStationLetterMessage(JSONObject tiktokData);


    /**
     * 刷新有用户ID无昵称会话信息
     */
    void updateTikTokConversationNickName();


    /**
     * 订单号 获取tk会话订单信息
     *
     * @param channelStatus
     * @param sourceId
     * @return
     */
    List<TicketMsgCardOrderInfo> selectTikcetConversationOrderList(String  customerOuterId,Long shopId, List<Long> channelStatus, String orderNo);


    /**
     * 获取tiktok订单明细信息（侧边栏 具体订单详情）
     *
     * @param orgId
     * @param orderId
     * @param orderNo
     * @return
     */
    TicketMsgCardDescInfo selectTikcetConversationOrderItemsList(Long orgId,Long orderId,String orderNo,Long shopId);


    /**
     * @description: tiktok 商品明细（侧边栏 商品）
     * @author: Moore
     * @date: 2025/1/17 9:39
     * @param
     * @param contextId
     * @param shopId
     * @param flag
     * @param commonParam
     * @return: com.bizark.op.api.entity.op.ticket.TicketMsgCardInfo.SkuInfo
    **/
    List<TicketMsgCardInfo.SkuInfo> selectTikcetConversationProductList(Long contextId, Long shopId, String flag, String commonParam);


    /**
     * 获取TK促销信息
     *
     * @param contextId
     * @param shopId
     * @param commonParam
     * @return
     */
    List<MarTkCouponInfo> selectTiktokMsgCouponList(Long contextId, Long shopId, String commonParam);


    /**
     * @param
     * @param
     * @description: 销售订单-TK渠道订单-客户消息 创建会话
     * @author: Moore
     * @date: 2023/10/26 17:03
     * @return: void
     **/
    ScTicket customerInfoCreateTiktokConv(Integer contextId,Integer channelId,String orderNo, String customerOuterId);



    /**
     * @param
     * @param
     * @description: 销售订单-TK渠道订单-客户消息 创建会话
     * @author: Moore
     * @date: 2023/10/26 17:03
     * @return: void
     **/
    void customerInfoCreateTiktokConvManual(List<ScEmailSend> scEmailSends);
}
