package com.bizark.op.api.entity.op.mar;

import com.bizark.erp.common.annotation.Excel;
import com.bizark.op.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 沃尔玛payment报汇总对象 wal_payment_report_summary
 *
 * <AUTHOR>
 * @date 2023-06-14
 */
@Data
public class WalPaymentReportSummary extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;


    /**
     * 店铺ID
     */
    @Excel(name = "店铺ID")
    private Long shopId;

    private Long organizationId;

    /**
     * 报表时间
     */
    @Excel(name = "报表时间")
    private String reportTime;

    /**
     * 报表时间转换后
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "报表时间转换后", width = 15, dateFormat = "yyyy-MM-dd")
    private Date convertReportTime;

    /**
     * 开始日期
     */
    @Excel(name = "开始日期")
    private String periodStartDate;

    /**
     * 结束日期
     */
    @Excel(name = "结束日期")
    private String periodEndDate;

    /**
     * 应付总额
     */
    @Excel(name = "应付总额")
    private String totalPayable;

    /**
     * 货币
     */
    @Excel(name = "货币")
    private String currency;

    /**
     * Sales:收到发货确认时 Refunds:开具退货或退款发票时 Adjustments:发出调整时
     */
    @Excel(name = "Sales:收到发货确认时 Refunds:开具退货或退款发票时 Adjustments:发出调整时")
    private String transactionPostedTimestamp;

    /**
     * SALE：销售交易 REFUNDED:退款交易 ADJMNT：费用、更正和调整将显示为贷方或借方
     */
    @Excel(name = "SALE：销售交易 REFUNDED:退款交易 ADJMNT：费用、更正和调整将显示为贷方或借方")
    private String transactionType;

    /**
     * 交易说明
     */
    @Excel(name = "交易说明")
    private String transactionDescription;

}
