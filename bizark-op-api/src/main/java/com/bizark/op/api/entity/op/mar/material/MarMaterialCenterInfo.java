package com.bizark.op.api.entity.op.mar.material;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.bizark.op.api.entity.op.tabcut.TabcutVideoTag;
import com.bizark.op.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * tiktok素材中心表
 * @TableName mar_material_center_info
 */
@TableName(value ="mar_material_center_info")
@Data
@Accessors(chain = true)
public class MarMaterialCenterInfo extends BaseEntity {
    /**
     *  
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 素材编号
     */
    private String materialNum;

    /**
     * 素材类型 IMAGE、VIDEO
     */
    private String contentType;

    /**
     * 达人视频ID
     */
    private String videoId;

    /**
     * 素材URL
     */
    private String materialUrl;

    /**
     * 素材名称(视频、图片名称)
     */
    private String materialName;

    /**
     * 素材标签
     */
    private String materialTag;

    /**
     * erpSku
     */
    private String erpSku;

    /**
     * 型号
     */
    private String model;

    /**
     * 素材来源1.creatify Ai 2.TK_AD_AI 3.达人视频  4.手动
     */
    private Integer materialSource;

    /**
     * 销量
     */
    private Integer quantity;


    /**
     * 销售额
     */
    private BigDecimal saleAmount;

    /**
     * 下载次数
     */
    private Integer downloadTotal;

    /**
     * ai使用次数
     */
    private Integer aiTotal;

    /**
     * 标签
     */
    @TableField(exist = false)
    List<TabcutVideoTag> tabcutVideoTagList;

}