package com.bizark.op.api.entity.op.mar.material;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.bizark.op.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * tiktok素材制作表
 * @TableName mar_material_make_info
 */
@TableName(value ="mar_material_make_info")
@Data
@Accessors(chain = true)
public class MarMaterialMakeInfo extends BaseEntity {
    /**
     *  
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 生成ID
     */
    private String genId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 生成状态  0.失败 1.成功 2.生成中
     */
    private Integer genStatus;

    /**
     * 新素材编号
     */
    private String materialNum;

    /**
     * 素材名称
     */
    private String materialName;

    /**
     * 生成视频内容路径
     */
    private String genVideoUrl;
    /**
     * 原视频地址
     */

    private String originVideoUrl;

    /**
     * 预览，仅creatify Ai 有值
     */
    private String preview;

    /**
     * 视频时长 单位s
     */
    private Long videoDuration;

    /**
     * 生成方式 1.creatify Ai 2.TK_AD_AI
     */
    private Integer genWay;

    /**
     * 原始素材编号,多个逗号拼接
     */
    private String orgMaterialNum;

    /**
     * 素材包ID,接口返回的生成ID
     */
    private String apiMaterialId;

    /**
     * 宽高比
     */
    private String aspectRatio;

    /**
     * 文本风格
     */
    private String textStyle;

    /**
     * 视觉风格
     */
    private String visionStyle;

    /**
     * 是否图文视频 0.否 1是
     */
    private Integer videoFlag;


    /**
     * 生成的视频ID，视频可用于创建广告(tk)
     */
    private String resVideoId;

    /**
     * 广告SCOP(tk)
     */
    private String adScope;


    /**
     * 型号 冗余
     */
    private String model;

    /**
     * Symphony生成视频类型
     */
    private String generateVideoType;
}