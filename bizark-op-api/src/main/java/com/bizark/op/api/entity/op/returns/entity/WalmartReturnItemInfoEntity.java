package com.bizark.op.api.entity.op.returns.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * walmart退货明细数据
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-14
 */
@Data
@Accessors(chain = true)
@TableName("walmart_return_item_info")
public class WalmartReturnItemInfoEntity  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 组织ID
     */
    @TableField("organization_id")
    private Integer organizationId;

    /**
     * 店铺flag
     */
    @TableField("channel_flag")
    private String channelFlag;

    /**
     * 退货订单号
     */
    @TableField("return_order_id")
    private String returnOrderId;

    /**
     * The returns order line number for that return
     */
    @TableField("return_order_line_number")
    private Integer returnOrderLineNumber;

    /**
     * The sales order line number for the return created
     */
    @TableField("sales_order_lineNumber")
    private Integer salesOrderLinenumber;

    /**
     * 卖家订单号
     */
    @TableField("seller_order_id")
    private String sellerOrderId;

    /**
     * 退货原因
     */
    @TableField("return_reason")
    private String returnReason;

    /**
     * The purchase order ID for the return created
     */
    @TableField("purchase_order_id")
    private String purchaseOrderId;

    /**
     * The purchase order line number for the return created
     */
    @TableField("purchase_order_line_number")
    private Integer purchaseOrderLineNumber;

    /**
     * sku
     */
    @TableField("sku")
    private String sku;

    /**
     * 产品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 重量单位
     */
    @TableField("item_weight_unit")
    private String itemWeightUnit;

    /**
     * 订单收入金额
     */
    @TableField("item_weight_value")
    private BigDecimal itemWeightValue;

    /**
     * 退货状态
     */
    @TableField("return_status")
    private String returnStatus;

    /**
     * 退货状态日期
     */
    @TableField("return_status_time")
    private LocalDateTime returnStatusTime;

    /**
     * Determines the current carrier tracking status of the return
     */
    @TableField("current_delivery_status")
    private String currentDeliveryStatus;

    /**
     * 退款状态
     */
    @TableField("current_refund_status")
    private String currentRefundStatus;

    /**
     * 数量单位
     */
    @TableField("quantity_unit")
    private String quantityUnit;

    /**
     * 数量
     */
    @TableField("quantity_value")
    private BigDecimal quantityValue;

    /**
     * 单价
     */
    @TableField("unit_price_currency_amount")
    private BigDecimal unitPriceCurrencyAmount;

    /**
     * 单价单位
     */
    @TableField("unit_price_currency_unit")
    private String unitPriceCurrencyUnit;

    /**
     * 创建者id
     */
    @TableField("created_by")
    private Integer createdBy;

    /**
     * 创建者名称
     */
    @TableField("created_name")
    private String createdName;

    /**
     * 创建时间戳
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新者ID
     */
    @TableField("updated_by")
    private Integer updatedBy;

    /**
     * 更新者名称
     */
    @TableField("updated_name")
    private String updatedName;

    /**
     * 更新时间戳
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 删除者ID
     */
    @TableField("disabled_by")
    private Integer disabledBy;

    /**
     * 删除者名称
     */
    @TableField("disabled_name")
    private String disabledName;

    /**
     * 删除标识 0:未删除 1:删除
     */
    @TableField("disabled_at")
    private Integer disabledAt;
}
