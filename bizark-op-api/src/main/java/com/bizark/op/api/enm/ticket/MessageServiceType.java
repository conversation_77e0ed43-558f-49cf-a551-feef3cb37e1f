package com.bizark.op.api.enm.ticket;


import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName MessageServiceType
 * @Description 消息服务类型
 * <AUTHOR>
 * @Date 2025/5/7 14:50
 */
public enum MessageServiceType {
    PRESALE("PreSale", "Pre-purchase","售前"),
    AFTERSALE("AfterSale","Aftersales", "售后"),
    LOGISTICS("Logistics", "Logistics","物流"),
    ;


    private final String value;

    private final String apiCategory;

    private final String name;


    public String getApiCategory() {
        return apiCategory;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }


    MessageServiceType(String value, String apiCategory, String name) {
        this.value = value;
        this.apiCategory = apiCategory;
        this.name = name;
    }

    /**
     * 通过ApiCategory 查询Value信息，单个查询
     *
     * @param apiCategory
     * @return
     */
    public static String getValueIgnoreCase(String apiCategory) {
        MessageServiceType[] array = MessageServiceType.values();
        for (MessageServiceType item : array) {
            if (item.apiCategory.equalsIgnoreCase(apiCategory)) {
                return item.value;
            }
        }
        return null;
    }


    /**
     * 通过ApiCategory 查询Value信息
     *
     * @param apiCategorys
     * @return
     */
    public static List<String> getValueIgnoreCaseList(List<String> apiCategorys) {
        List<String> apicategorys = Arrays.asList(MessageServiceType.values()).stream().map(MessageServiceType::getApiCategory).collect(Collectors.toList());
        return apicategorys.stream().filter(apiCategorys::contains).distinct().collect(Collectors.toList());
    }

}
