package com.bizark.op.api.entity.op.tkpromotion;

import com.bizark.op.api.enm.tkpromotion.TkPromotionActivityTypeEnum;
import com.bizark.op.common.util.StringUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.bizark.op.api.cons.mar.TkConstant.TK_PROMOTION_FLASH_DEAL_TYPE;
import static com.bizark.op.api.cons.mar.TkConstant.TK_PROMOTION_PRODUCT_DISCOUNT;

/**
 * tk促销查询请求参数实体类
 *
 * @Author: Ailill
 * @Date: 2025-07-21 18:26
 */
@Data
public class TkPromotionQueryRequest implements Serializable {

    private Long id; // 主键id
    private String parentProductId; // 父商品id
    private String productId; // 商品id


    private Integer orgId; // 组织id
    private List<Integer> shopIds; // 店铺id列表
    private List<String> activeStatusList; // 活动状态列表
    private Date beginDateFrom; // 开始时间
    private Date beginDateTo; // 开始时间
    private Date endDateFrom; // 结束时间
    private Date endDateTo; // 结束时间
    private Date createDateFrom; // 创建时间
    private Date createDateTo; // 创建时间
    private Date updateDateFrom; // 更新时间
    private Date updateDateTo; // 更新时间
    private Date effectiveDateFrom; // 生效时间
    private Date effectiveDateTo; // 生效时间

    private List<String> approvalStatusList; // 审批状态

    //Flash Deal，Product Discount
    private String activeType;//活动类型

    private List<String> activityTypeList;
    //PRODUCT为父商品ID维度，VARIATION为商品ID维度
    private String promotionObject; // 促销对象

    private List<Integer> operatorIdList;//运营

    //1【高于等于最低建议供货价】，2【低于最低建议供货价】
    private Integer priceWarn;//价格预警

    private String activeName;//活动名称

    private String activeId;//活动ID

    private List<String> parentProductIdList;//父商品ID

    private List<String> productIdList;//商品ID

    private List<String> sellerSkuList;//seller sku
    private List<String> skuList;//sku

    private List<Long> tkPromotionIdList;//促销ID

    /**
     * 是否过滤已参加的活动
     */
    private Boolean filterJoinedActivity;

    private Integer shopId;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    private String activityType;
    /**
     * 折扣类型 PERCENTAGE:百分比,PRICE:价格  (当activityType为Product Discount必填)
     */
    private String discountType;

    private Integer page;

    private Integer rows;


    public void setActiveType(String activeType) {
        this.activeType = activeType;
        if (StringUtils.isNotEmpty(this.activeType)) {
            if (TK_PROMOTION_FLASH_DEAL_TYPE.equalsIgnoreCase(this.activeType)) {

                this.activityTypeList = Arrays.asList(TkPromotionActivityTypeEnum.FLASHSALE.getType());
            } else if (TK_PROMOTION_PRODUCT_DISCOUNT.equalsIgnoreCase(this.activeType)) {
                this.activityTypeList = Arrays.asList(TkPromotionActivityTypeEnum.FIXED_PRICE.getType(), TkPromotionActivityTypeEnum.DIRECT_DISCOUNT.getType());
            }
        }
    }
}
