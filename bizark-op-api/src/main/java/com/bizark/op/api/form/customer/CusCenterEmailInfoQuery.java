package com.bizark.op.api.form.customer;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @description: 邮件信息查询Query
 * <AUTHOR>
 * @Date 2023/7/18 13:57
 */
@Data
@ApiModel(value = "邮件信息查询Query")
public class CusCenterEmailInfoQuery implements Serializable {


    private Integer contextId;

    /**
     * 邮件类型,批量
     */
    private List<String> emailTypeList;

    /**
     * 事项，批量
     */
    @Schema(description = "事项")
    private List<String> matterList;


    /**
     * 渠道
     */
    private List<String> channelList;

    /**
     * 店铺ID
     */
    private List<String> shopList;


    /**
     * 运营
     */
    @Schema(description = "运营")
    private Long operationIdList;


    /**
     * 工单状态
     */
    private List<String> ticketStatusList;


    /**
     * 处理人
     */
    @Schema(description = "处理人")
    private String handlerBy;


    /**
     * 收件时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date senTime;

    /**
     * 收件时间from
     */
    private String senTimeFrom;

    /**
     * 收件时间To
     */
    private String senTimeTo;

    /**
     * 发件人
     */
    private String senName;


    /**
     * 邮件
     */
    private String email;


    /**
     * 邮件主题
     */
    private String emailTitle;


    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单号List
     */
    private List<String> orderNoList;


    /**
     * asin
     */
    private String asin;

    /**
     * asinList
     */
    private List<String> asinList;


    /**
     * sku
     */
    private String goodsSku;

    /**
     * skuList
     */
    private List<String> goodsSkuList;


    private String channel;

    /**
     * 发件人
     */
    private String fromAddress;


    /**
     * 收件人
     */
    private String toAddress;


    /**
     * 工单ID
     */
    private Long ticketId;

    /**
     * 是否查询历史
     */
    private String hisFlag;


    /**
     * 主键ID
     */
    private Long infoId;


    private String exportId;


}
