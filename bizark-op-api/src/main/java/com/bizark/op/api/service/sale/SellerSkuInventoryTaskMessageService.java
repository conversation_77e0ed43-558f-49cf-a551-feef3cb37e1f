package com.bizark.op.api.service.sale;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.entity.op.inventory.conf.vo.SellerSkuInventoryTaskMessage;

import java.util.List;

public interface SellerSkuInventoryTaskMessageService extends IService<SellerSkuInventoryTaskMessage> {

    int insertOrUpdateQuantitySellerSkuInventoryTaskMessage(SellerSkuInventoryTaskMessage message);

    int insertSellerSkuInventoryTaskMessage(SellerSkuInventoryTaskMessage message);

    List<SellerSkuInventoryTaskMessage> selectInventoryTaskMessageLimit(int limit);

    List<SellerSkuInventoryTaskMessage> selectInventoryTaskMessageByTypeAndLimit(String type, int limit);
}

