package com.bizark.op.api.entity.op.amazon.fba.DTO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @ClassName MarStaTaskListQueryDTO
 * @Description STA列表查询请求
 * <AUTHOR>
 * @Date 2025/7/24 10:42
 */
@Data
@Schema(description = "STA商品列表查询参数")
public class MarStaProductQueryDTO {

    @Schema(description = "组织ID")
    private Long orgId;

    @Schema(description = "店铺ID")
    private Long shopId;


    @Schema(description = "selelrSKu")
    private List<String> sellerSkuList;

    @Schema(description = "fnSKu")
    private List<String> fnSkuList;

    @Schema(description = "商品ID")
    private List<String> asinList;

    @Schema(description = "品名")
    private List<String> productNameList;

    @Schema(description = "SKU")
    private List<String> erpSkuList;

    @Schema(description = "标签")
    private List<String> titleList;

    @Schema(description = "排除的SellerSKu")
    private List<String> excludeSellerSkus;

    @Schema(description = "销售状态，停售传入：Off Shelve")
    private String sellStatus;










}