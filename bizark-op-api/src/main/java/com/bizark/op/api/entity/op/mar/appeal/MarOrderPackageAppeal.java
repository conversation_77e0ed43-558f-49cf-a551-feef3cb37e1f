package com.bizark.op.api.entity.op.mar.appeal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bizark.op.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单包裹申诉信息
 * @TableName mar_order_package_appeal
 */
@TableName(value ="mar_order_package_appeal" , excludeProperty = {"searchValue", "params"})
@Data
public class MarOrderPackageAppeal extends BaseEntity implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 组织id
     */
    private Long organizationId;

    /**
     * 申诉状态
     * 0:获取证明中 1:待提交申诉 2:提交申诉中 3:申诉审核中 4.申诉成功 -1:申诉失败  -2取消申诉
     * AppealStatusEnum
     */
    private Integer appealStatus;

    /**
     * 结算状态 0未结算 1已结算
     */
    private Integer settlementStatus;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名
     */
    @TableField(exist = false)
    @JsonIgnore
    private String shopName;

    /**
     * 承运商
     */
    private String carrierCode;

    /**
     * 承运商名称
     */
    @TableField(exist = false)
    @JsonIgnore
    private String carrierName;

    /**
     * 承运商ID
     */
    @TableField(exist = false)
    @JsonIgnore
    private String carrierId;


    /**
     * 订单号
     */
    private String orderNo;


    /**
     * 订单主键
     */
    private Long orderId;


    /**
     * 订单明细表主键
     */
    private Long orderItemId;

    /**
     * SKU
     */
    private String erpSku;

    /**
     * SellseSK
     */
    private String sellerSku;

    /**
     * 订单时间
     */
    private Date pstChannelCreated;

    /**
     * 发货时间
     */
    private Date shipmentDate;

    /**
     * 发货单号
     */
    private String deliverOrderNo;

    /**
     * 包裹号
     */
    private String packageNo;

    /**
     * 物流单号
     */
    private String trackNo;

    /**
     * 服务CODE，（发货方式）
     */
    private String serviceCode;

    /**
     * 工单ID
     */
    private Long ticketId;

    /**
     * 举证说明
     */
    private String evidenceDesc;


    /**
     * 异常状态 1:正常 -1:异常
     * AppealExceptionEnum
     */
    private Integer exceptionFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人ID
     */
    private Integer createdBy;

    /**
     * 创建人名称
     */
    private String createdName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新人ID
     */
    private Integer updatedBy;

    /**
     * 更新人名称
     */
    private String updatedName;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 删除人ID
     */
    private Integer disabledBy;

    /**
     * 删除人名称
     */
    private String disabledName;

    /**
     * 删除时间
     */
    private Date disabledAt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private Boolean ifTicketComeIn;

    /**
     * 发运数量
     */
    private Integer shipedQuantity;

    /**
     * 1.业务生成 2.前台抓取
     */
    private Integer sourceType;


    /**
     * 申诉时间
     */
    private Date applyTime;


    /**
     * 完结时间
     */
    private Date endTime;

    @TableField(exist = false)
    @JsonIgnore
    private String customerCarrierServiceCode;


    /**
     * 申诉类型
     DELIVERED_UNBALANCED(0, "妥投未回款"),
     DELAY_ARRIVAL(1, "延迟到货违规"),
     DECEITFUL_DELIVERY(4, "虚假发货违规");
     */
    @TableField(exist = false)
    private Integer appealType;



}