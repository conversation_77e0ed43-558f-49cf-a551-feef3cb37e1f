package com.bizark.op.api.entity.op.mar.appeal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bizark.op.common.core.domain.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单包裹举证图片
 * @TableName mar_order_package_appeal_img
 */
@TableName(value ="mar_order_package_appeal_img" , excludeProperty = {"searchValue", "params"})
@Data
public class MarOrderPackageAppealImg extends BaseEntity implements Serializable{
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 举证ID
     */
    private Long appealId;

    /**
     * 图片URL
     */
    private String imageUrl;

    /**
     * 图片来源: 1.回传 2.用户手动上传 3.mq
     */
    private Integer imgSourceFlag;

    /**
     * 签收时间
     */
    private Date signingDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人ID
     */
    private Integer createdBy;

    /**
     * 创建人名称
     */
    private String createdName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新人ID
     */
    private Integer updatedBy;

    /**
     * 更新人名称
     */
    private String updatedName;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 删除人ID
     */
    private Integer disabledBy;

    /**
     * 删除人名称
     */
    private String disabledName;

    /**
     * 删除时间
     */
    private Date disabledAt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}