package com.bizark.op.api.entity.op.amazon;


/**
 * @ClassName AmazonApiURLEnum
 * @Description amz业务调用路径
 * <AUTHOR>
 * @Date 2025/7/31 13:45
 */
public enum AmazonApiURLEnum {
    LIST_PREP_DETAILS("/inbound/fba/2024-03-20/items/prepDetails", "listPrepDetails","获取预处理方,标签类型"),
    CREATED_INBOUND_PLANS("/inbound/fba/2024-03-20/inboundPlans","createinboundplan", "创建入库计划"),
    GET_INBOUND_PLANS("/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}", "getInboundPlan","获取单个货件信息"),
    GET_INBOUND_OPERATION_STATUS("/inbound/fba/2024-03-20/operations/{operationId}", "getInboundOperationStatus","获取操作状态"),
    LIST_PACKING_OPTIONS("/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingOptions", "listPackingOptions","获取包装组方案"),
    LIST_PACKING_GROUP_ITEMS("/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingGroups/{packingGroupId}/items", "listPackingGroupItems","获取包装组方案详细信息"),
    CONFIRM_PACKING_OPTION("/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingOptions/{packingOptionId}/confirmation","confirmPackingOption", "确认包装箱组方案"),
    SET_PACKINGINFORMATION("/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingInformation", "setpackinginformation","设置包装组中具体箱规信息"),
    GENERATE_PLACEMENTOPTIONS("/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/placementOptions","generatePlacementOptions", "生成预览"),
    LIST_PLACEMENT_OPTIONS("/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/placementOptions","listPlacementOptions", "获取预览结果"),
    GET_SHIPMENT("/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}","getShipment", "获取FBA信息"),
    LIST_SHIPMENTBOXES("/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/boxes","listShipmentBoxes", "获取FBA装箱明细信息"),
    GET_LABELS("/fba/inbound/v0/shipments/{}/labels","labels", "获取打印labels"),
    CONFIRM_PLACEMENT_OPTION("/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/placementOptions/{placementOptionId}/confirmation","confirmPlacementOption", "确认申报"),
    UPDATE_SHIPMENT_NAME("/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/name","updateInboundPlanName", "更新FBA名称"),
    GENERATE_DELIVERY_WINDOW_OPTIONS("/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/deliveryWindowOptions","generateDeliveryWindowOptions", "生成送达时间"),
    LIST_DELIVERY_WINDOW_OPTIONS("/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/deliveryWindowOptions","listDeliveryWindowOptions", "送达时间区间信息"),
    GENERATE_TRANSPORTATIONOPTIONS("/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/transportationOptions","generateTransportationOptions", "FBA生成承运方式"),
    LIST_TRANSPORTATION_OPTIONS("/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/transportationOptions", "listTransportationOptions","获取承运商信息"),
    CONFIRM_DELIVERY_WINDOW_OPTIONS("/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/deliveryWindowOptions/{deliveryWindowOptionId}/confirmation", "confirmDeliveryWindowOptions","确认送达时间"),
    CONFIRM_TRANSPORTATION_OPTIONS("/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/transportationOptions/confirmation", "confirmTransportationOptions","确认FBA"),
    UPDATE_BOX_INFO("/inbound/fba/2024-03-20/inboundPlans/{}/shipments/{}/contentUpdatePreviews", "generateShipmentContentUpdatePreviews","更新FBA装箱信息"),
    LOOK_BOX_INFO("/inbound/fba/2024-03-20/inboundPlans/{}/shipments/{}/contentUpdatePreviews", "listShipmentContentUpdatePreviews","查询FBA更新装箱信息报价"),
    CONFIRM_UPDATE_BOX_INFO("/inbound/fba/2024-03-20/inboundPlans/{}/shipments/{}/contentUpdatePreviews/{}/confirmation", "listShipmentContentUpdatePreviews","确认更新FBA装箱信息"),
    CANCEL_INBOUNDPLAN("/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/cancellation", "cancelInboundPlan","取消STA任务"),
    UPDATE_SHIPMENT_TRACKING_DETAILS("/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/trackingDetails", "updateShipmentTrackingDetails","FBA更新Tracking号");

    private String path;
    private String apiName;
    private String desc;


    AmazonApiURLEnum(String path, String apiName, String desc) {
        this.path = path;
        this.apiName = apiName;
        this.desc = desc;
    }

    AmazonApiURLEnum(String path, String desc) {
        this.path = path;
        this.desc = desc;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
