package com.bizark.op.api.entity.op.ticket;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bizark.op.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 工单回复信息实体类
 * @Author: Ailill
 * @Date: 2025-03-10 14:44
 */
@Data
@TableName(value = "sc_ticket_reply_info", excludeProperty = {
        "searchValue", "params"
})
public class ScTicketReplyInfo extends BaseEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Integer organizationId;

    /**
     * 工单id
     */
    private Long ticketId;



    /**
     * 回复状态 回复中 回复成功 回复失败
     */
    @JSONField(name = "replyStatus")
    private String replyStatus;

    /**
     * 回复信息发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date replySendTime;

    /**
     * 回复信息返回回复成功或失败时间
     */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date replyReceiveTime;

    /**
     * 回复内容
     */
    private String replyContent;

    @JSONField(name = "failReason")
    private String replyFailReason;

    /**
     * 留评人
     */
    @TableField(exist = false)
    private String customerName;

    /**
     * 留评时间
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reviewDate;

    /**
     * 留评内容
     */
    @TableField(exist = false)
    private String reviewContent;


    /**
     * 星级
     */
    @TableField(exist = false)
    private String starRating;

    /**
     * 订单号
     */
    @TableField(exist = false)
    private String orderNo;

    /**
     * 父商品ID
     */
    @TableField(exist = false)
    private String parentProductId;

    @TableField(exist = false)
    private String storeId;

}
