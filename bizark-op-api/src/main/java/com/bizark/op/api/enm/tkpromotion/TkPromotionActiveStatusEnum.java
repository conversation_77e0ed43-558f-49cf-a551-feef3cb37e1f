package com.bizark.op.api.enm.tkpromotion;

import java.util.Date;

/**
 * @Author: Ailill
 * @Date: 2025-07-23 14:15
 */
public enum TkPromotionActiveStatusEnum {
    PENDING("PENDING", "待提交"),
    DRAFT("DRAFT", "草稿"),
    NOT_STARTED("NOT_STARTED", "未开始"),
    ONGOING("ONGOING", "进行中"),
    EXPIRED("EXPIRED", "已过期"),
    DEACTIVATED("DEACTIVATED", "已取消"),
    NOT_EFFECTIVE("NOT_EFFECTIVE", "平台已已取消"),
    CREATE_FAIL("CREATE_FAIL", "创建失败"),
    UPDATE_FAIL("UPDATE_FAIL", "更新失败"),
    CANCEL_FAIL("CANCEL_FAIL", "取消失败"),
    ;

    private String value;

    private String description;

    TkPromotionActiveStatusEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据value获取description
     *
     * @param value
     * @return
     */
    public static String getDescriptionByValue(String value) {
        for (TkPromotionActiveStatusEnum status : TkPromotionActiveStatusEnum.values()) {
            if (status.getValue().equals(value)) {
                return status.getDescription();
            }
            return null;
        }
        return null;
    }


    public static String getStatusByDate(Date beginDate, Date endDate, Date nowDate) {
        if (nowDate.before(beginDate)) {
            return TkPromotionActiveStatusEnum.NOT_STARTED.getValue();
        }else {
            return TkPromotionActiveStatusEnum.ONGOING.getValue();
        }
    }
}
