package com.bizark.op.api.entity.op.sale;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bizark.op.common.annotation.DictConvert;
import com.bizark.op.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@TableName(excludeProperty = "remark",value = "dashboard.temu_violation_detail")
public class TemuViolationDetail extends BaseEntity {

    private Long id;

    private Integer orgId;

    /**
     * 提交ID
     */
    private Long subId;

    @Schema(description = "违规主表ID")
    private Long violationId;

    @Schema(description = "违规单号")
    private String violationAppealSn;

    @TableField(exist = false)
    @DictConvert(dict = "temu_violation_type", target = "violationType")
    private String violationName;

    @Schema(description = "违规类型")
    private Integer violationType;

    @Schema(description = "店铺ID")
    private Integer accountId;

    @Schema(description = "店铺Flag")
    private String accountFlag;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "预计送达时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date promiseTime;

    @Schema(description = "预估违规金额")
    private BigDecimal amount;

    @Schema(description = "预估违规金额")
    private String fullAmount;

    @Schema(description = "申诉原因")
    private String appealReason;

    @Schema(description = "包裹号")
    private String packageSn;


    @TableField(exist = false)
    @DictConvert(dict = "temu_violation_check_status", target = "checkStatus")
    private String check;

    /**
     * 订单id
     */
    private Long orderId;

    @Schema(description = "申诉状态")
    private Integer checkStatus;

    @Schema(description = "面单号")
    private String waybillNumber;

    @Schema(description = "异常节点")
    private String abnormalNode;


    @Schema(description = "SKU")
    private String erpSku;

    @Schema(description = "发货方式")
    private String shippingMethod;
    @Schema(description = "跟踪号")
    private String trackingNo;

    @Schema(description = "实际送达时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualDeliveryTime;

    @Schema(description = "违规通知时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date informTime;

    @Schema(description = "订单创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderCreatedTime;

    @Schema(description = "订单创建Pst时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderCreatedPstTime;


    @Schema(description = "是否申诉")
    private Boolean isAppeal;


    @TableLogic(value = "0", delval = "1")
    private boolean isDelete;


    @TableField(exist = false)
    private String accountTitle;

    @TableField(exist = false)
    @DictConvert(dict = "temu_violation_exception_type", target = "hitTypeStatus")
    private String hitType;

    @TableField(exist = false)
    private Integer hitTypeStatus;

    @TableField(exist = false)
    private BigDecimal totalAmount;

    @TableField(exist = false)
    private String actualFullAmount;

    @TableField(exist = false)
    private BigDecimal actualAmount;

    @TableField(exist = false)
    private BigDecimal totalActualAmount;


    @Schema(description = "申诉时间")
    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastAppealTime;


    /**
     * 异常状态  1:正常 -1:异常
     */
    private Integer exceptionFlag;

    @TableField(exist = false)
    private String exceptionDesc;

    /**
     * 业务申诉状态 10:待确认、11:获取证明中、12:待提交申诉、13:提交申诉中、14:接受违规中、15:待申诉、16:待完善资料、
     * 1.平台处理中、2.申诉失败、3.申诉成功、5:超时关闭申诉 6:已主动接受违规
     */
    @Schema(description = "申诉业务状态")
    private Integer businessAppealStatus;

    /**
     * 申诉建议处理方式
     * 1.需接受违规 2.需申诉  0.无
     */
    @Schema(description = "建议处理方式")
    private Integer suggestWay;


    @Schema(description = "截止处理时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date earliestAppealEndTime;



    /**
     * 申诉类型，同violation_type字段值一致，非数据库字段，仅返回是用
     */
    @TableField(exist = false)
    private String appealType;

    /**
     * 申诉图片
     */
    @Schema(description = "申诉图片url")
    @TableField(exist = false)
    private String appealImage;

    @Schema(description = "工单编号")
    @TableField(exist = false)
    private String ticketNumber;

    @Schema(description = "工单状态")
    @TableField(exist = false)
    private String ticketStatus;

    @Schema(description = "工单处理人")
    @TableField(exist = false)
    private String handlerBy;

    @TableField(exist = false)
    private String ticketType;

    private Long ticketId;

    @TableField(exist = false)
    private Long sourceId;

    @TableField(exist = false)
    private String ticketSource;

    @TableField(exist = false)
    private String operateStatus;

    /**
     * 实际送达时间，多个
     */
    @TableField(exist = false)
    List<Date> actualDeliveryTimes;

    /**
     * 订单北京时间
     */
    @TableField(exist = false)
    private Date bjOrderTime;

    /**
     * 订单发货时间 北京
     */
    @TableField(exist = false)
    private Date bjPlatformShipTime;




}
