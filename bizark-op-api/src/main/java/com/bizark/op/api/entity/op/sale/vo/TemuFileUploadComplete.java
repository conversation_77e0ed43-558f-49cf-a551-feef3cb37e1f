package com.bizark.op.api.entity.op.sale.vo;

import cn.hutool.core.util.StrUtil;
import com.bizark.op.api.enm.sale.temu.FileUploadTypeEnum;
import com.bizark.op.api.entity.op.sale.TemuFileUpload;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
@Data
@NoArgsConstructor
public class TemuFileUploadComplete implements Serializable {

    private boolean complete;

    private List<TemuFileUpload> fileUploads;
    private boolean carouselComplete;
    private boolean skuPreviewComplete;
    private boolean detailComplete;
    private boolean instructionComplete;
    private String needUpdateType;


    public TemuFileUploadComplete(boolean carouselComplete, boolean skuPreviewComplete, boolean detailComplete, boolean instructionComplete,List<TemuFileUpload> fileUploads) {
        this.carouselComplete = carouselComplete;
        this.skuPreviewComplete = skuPreviewComplete;
        this.detailComplete = detailComplete;
        this.instructionComplete = instructionComplete;
        this.complete = carouselComplete && skuPreviewComplete && detailComplete && instructionComplete;
        this.fileUploads = fileUploads;

        if (complete) {
            return;
        }

        StringBuilder builder = new StringBuilder();

        if (!carouselComplete) {
            builder.append(FileUploadTypeEnum.CAROUSEL_IMAGE.getCode());
        }
        if (!skuPreviewComplete) {
            builder.append(",").append(FileUploadTypeEnum.PREVIEW_IMAGE.getCode());
        }
        if (!detailComplete) {
            builder.append(",").append(FileUploadTypeEnum.DETAIL_IMAGE.getCode());
        }
        if (!instructionComplete) {
            builder.append(",").append(FileUploadTypeEnum.INSTRUCTION.getCode());
        }
        needUpdateType =  StrUtil.removePrefix(builder.toString(), ",");

//        needUpdateType = builder.toString();
    }

}
