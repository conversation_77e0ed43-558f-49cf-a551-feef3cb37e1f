package com.bizark.op.api.entity.op.amazon.fba;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bizark.op.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.Date;

/**
 * STA货件任务表
 *
 * @TableName mar_sta_task_info
 */

@Schema(description = "运输方式信息")
@TableName(value ="mar_sta_task_info")
@Data
public class MarStaTaskInfo  extends BaseEntity {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 组织ID
     */
    @Schema(description = "组织ID")
    private Long orgId;
    /**
     * 店铺ID
     */
    @Schema(description = "店铺ID")
    private Long shopId;
    /**
     * 任务编号
     */
    @Schema(description = "任务编号")
    @Length(max = 100, message = "编码长度不能超过100")
    private String inboundPlanId;
    /**
     * 任务编号
     */
    @Schema(description = "操作ID")
    private String operationId ;



    /**
     * 任务名称
     */
    @Schema(description = "任务名称")
    @Length(max = 255, message = "编码长度不能超过255")
    private String taskName;
    /**
     * 状态 DRAFT,IN_PROGRESS,CANCELLED,SHIPPED，EXCEPTION
     */
    @Schema(description = "状态 DRAFT,IN_PROGRESS,CANCELLED,SHIPPED，EXCEPTION")
    @Length(max = 30, message = "编码长度不能超过30")
    private String taskStatus;


    /** api原始状态
     * ABANDONED, CANCELLED, CHECKED_IN, CLOSED, DELETED, DELIVERED, IN_TRANSIT, MIXED, READY_TO_SHIP, RECEIVING, SHIPPED, UNCONFIRMED, WORKING
     */
    private String taskStatusApi;

    /**
     * 任务节点 1.选择发货商品 2商品装箱 3.配送服务 4.箱子标签 5.货件追踪
     */
    @Schema(description = "任务节点 1.选择发货商品 2商品装箱 3.配送服务 4.箱子标签 5.货件追踪")
    private Integer taskNode;
    /**
     * STA创建成功时间
     */
    @Schema(description = "STA创建成功时间")
    private Date staCreatedAt;
    /**
     * 分仓方式 1.先装箱在分仓
     */
    @Schema(description = "分仓方式 1.先装箱在分仓")
    private Integer distributionMode;
    /**
     * 申报总数量
     */
    @Schema(description = "申报总数量")
    private Integer applyNum;
    /**
     * 包装组数量
     */
    @Schema(description = "包装组数量")
    private Integer packGroupCount;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Length(max = 255, message = "编码长度不能超过255")
    private String remark;


}
