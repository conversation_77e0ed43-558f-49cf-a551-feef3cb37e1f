package com.bizark.op.api.entity.op.amazon.fba.DTO;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 创建平台仓入参
 */
@Schema(name = "平台仓地址响应")
@Data
public class MarPlatformWarehouseAddressExcelDTO {

    @ExcelProperty("地址别名")
    @Schema(description = "地址别名")
    private String addressAlias;

    @ExcelProperty("店铺名称")
    @Schema(description = "店铺名称")
    private String shopName;

    @ExcelProperty("国家")
    @Schema(description = "国家编号")
    private String countryCode;

    @ExcelProperty("发货国家/地区")
    @Schema(description = "发货国家/地区")
    private String shipmentCountry;

    @ExcelProperty("发货方名称")
    @Schema(description = "发货方名称")
    private String senderName;


    @ExcelProperty("街道详细地址")
    @Schema(description = "详细地址")
    private String detailedAddress;


    @ExcelProperty("城市")
    @Schema(description = "城市")
    private String city;

    @ExcelProperty("区")
    @Schema(description = "区")
    private String district;


    @ExcelProperty("州/省/地区")
    @Schema(description = "州/省/地区")
    private String stateProvince;

    @ExcelProperty("邮政编码")
    @Schema(description = "邮政编码")
    private String postalCode;


    @ExcelProperty("电话号码")
    @Schema(description = "电话号码")
    private String phoneNumber;

    @ExcelProperty("邮箱")
    @Schema(description = "邮箱")
    private String email;


    @ExcelProperty("公司名称")
    @Schema(description = "公司名称")
    private String companyName;
}