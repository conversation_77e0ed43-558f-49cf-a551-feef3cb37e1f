package com.bizark.op.api.entity.op.product;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bizark.common.contract.AuthUserDetails;
import com.bizark.framework.security.AuthContextHolder;
import com.bizark.op.common.core.domain.BaseEntity;
import com.bizark.op.common.util.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.shiro.SecurityUtils;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName(value = "sku_children",excludeProperty = "disabled_at")
public class SkuChildren  implements Serializable {
    private Long id;
    private Integer orgId;
    private Integer productId;
    private String sku;
    private Integer parentSkuId;
    private String parentSku;
    private String warningType;
    private Integer status;
    private Integer suggestedPurchase;
    private Date effectiveTime;
    private String remark;

    /**
     * 优先级，越小越优先
     */
    private Integer priority;


    @TableField(fill = FieldFill.INSERT)
    private Integer createdBy;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createdName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer updatedBy;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.UPDATE)
    private Date updatedAt;

    /**
     * 更新者
     */
    private Integer disabledBy;

    /**
     * 更新者
     */
    private String disabledName;

    /**
     * 禁用时间戳 0表示未禁用
     */
    @TableLogic(value = "0")
    private Integer disabledAt;



    public void settingDefaultValue() {
        AuthUserDetails authUserDetails = AuthContextHolder.getAuthUserDetails();
        String updatedName = authUserDetails == null ? "system" : authUserDetails.getName();
        Integer updatedBy = authUserDetails == null ? -1 : authUserDetails.getId();
        Date now = DateUtils.getNowDate();
        this.setCreatedAt(getCreatedAt() == null ? now : getCreatedAt());
        this.setCreatedBy(getCreatedAt() == null ? updatedBy : getCreatedBy());
        this.setCreatedName(getCreatedName() == null ? updatedName : getCreatedName());
        this.setUpdatedBy(updatedBy);
        this.setUpdatedName(updatedName);
        this.setUpdatedAt(now);
    }

    public void settingUpdated(){
        try {
            AuthUserDetails userDetails = (AuthUserDetails) SecurityUtils.getSubject().getPrincipal();
            this.updatedBy = userDetails.getId();
            this.updatedName = userDetails.getName();
            this.updatedAt = new Date();
        } catch (Exception e) {
            this.updatedAt = new Date();
        }
    }

    public void settingCreated(){
        try {
            AuthUserDetails userDetails = (AuthUserDetails) SecurityUtils.getSubject().getPrincipal();
            this.createdBy = userDetails.getId();
            this.createdName = userDetails.getName();
            this.createdAt = new Date();
        } catch (Exception e) {
            this.createdAt = new Date();
        }
    }

    public void settingDefaultCreate() {
        AuthUserDetails authUserDetails = null;
        try {
            authUserDetails = AuthContextHolder.getAuthUserDetails();
        } catch (Exception e) {
            e.printStackTrace();
        }
        this.setCreatedAt(DateUtils.getNowDate());
        this.setCreatedBy(authUserDetails == null ? -1 : authUserDetails.getId());
        this.setCreatedName(authUserDetails == null ? "system" : authUserDetails.getName());
    }

    public void settingDefaultUpdate() {
        AuthUserDetails authUserDetails = null;
        try {
            authUserDetails = AuthContextHolder.getAuthUserDetails();
        } catch (Exception e) {
            e.printStackTrace();
        }
        this.setUpdatedAt(DateUtils.getNowDate());
        this.setUpdatedBy(authUserDetails == null ? -1 : authUserDetails.getId());
        this.setUpdatedName(authUserDetails == null ? "system" : authUserDetails.getName());
    }


    /**
     * 设置默认创建人为系统
     */
    public void settingDefaultSystemCreate() {
        this.setCreatedAt(DateUtils.getNowDate());
        this.setCreatedBy(-1);
        this.setCreatedName("system");
    }


    public void settingDefaultSystemUpdate() {
        this.setUpdatedAt(DateUtils.getNowDate());
        this.setUpdatedBy(-1);
        this.setUpdatedName("system");
    }
}
