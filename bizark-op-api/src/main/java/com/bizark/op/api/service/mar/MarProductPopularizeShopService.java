package com.bizark.op.api.service.mar;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.entity.op.mar.MarListingQuery;
import com.bizark.op.api.entity.op.mar.MarProductPopularizeShop;
import com.bizark.op.api.vo.mar.MarProductPopularizeLogAnalysisItemVo;
import com.bizark.op.api.vo.mar.MarProductPopularizeShopVo;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface MarProductPopularizeShopService extends IService<MarProductPopularizeShop> {
    /**
     * Description: 查询店铺推广列表
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/2/5
     */
    List<MarProductPopularizeShopVo> queryMarProductPopularizeShopList(MarListingQuery marListingQuery);


    /**
     * Description: 综合分析列表
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/2/20
     */
    List<MarProductPopularizeLogAnalysisItemVo> analysisQueryMarProductPopularizeShopList(MarListingQuery marListingQuery);


    /**
     * Description: 任务导出
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/2/29
     */
    void listExport(HttpServletResponse response, MarListingQuery marListingQuery, UserEntity authUserEntity);

    /**
     * Description: 外部接口调用
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/7/17
     */
    JSONObject queryMarProductPopularizeShopListByOutInterface(MarListingQuery marListingQuery);

    void newlistExportShop(HttpServletResponse response, MarListingQuery marListingQuery, UserEntity authUserEntity);

    /**
     * @desc 运营分析-店铺维度-汇总
     * <AUTHOR>
     * @date 2025/6/12 10:07
     * param
     * @param marListingQuery
     * return
     * @return com.alibaba.fastjson.JSONObject
     */
    JSONObject shopReport(MarListingQuery marListingQuery);
}
