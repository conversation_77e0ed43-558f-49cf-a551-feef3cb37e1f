package com.bizark.op.api.service.sale;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.entity.op.sale.ProductChannelsImage;

import java.util.List;


public interface ProductChannelsImageService extends IService<ProductChannelsImage> {
    void deleteThenInsert(String channelId,String productId, List<ProductChannelsImage> channelsImages);

    void saveOrUpdateImages(String channelId,String productId, List<ProductChannelsImage> channelsImages);

     void convertOssUrl(List<ProductChannelsImage> images);

    void covertUrl();
}
