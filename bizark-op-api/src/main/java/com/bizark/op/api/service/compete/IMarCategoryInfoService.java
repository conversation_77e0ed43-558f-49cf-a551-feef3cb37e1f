package com.bizark.op.api.service.compete;

import com.alibaba.fastjson.JSONObject;
import com.bizark.op.api.entity.op.compete.*;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;

import java.util.List;

/**
 * 营销类目监控信息Service接口
 *
 * <AUTHOR>
 * @date 2022-10-28
 */
public interface IMarCategoryInfoService {
    /**
     * 查询营销类目监控信息
     *
     * @param id 营销类目监控信息ID
     * @return 营销类目监控信息
     */
    MarCategoryInfo selectMarCategoryInfoById(Long id);

    /**
     * 查询营销类目监控信息列表
     *
     * @param marCategoryInfo 营销类目监控信息
     * @return 营销类目监控信息集合
     */
    List<MarCategoryInfo> selectMarCategoryInfoList(MarCategoryInfo marCategoryInfo);

    /**
     * 查询营销类目监控信息列表（历史表）
     *
     * @param marCategoryInfo 营销类目监控信息
     * @return 营销类目监控信息集合
     */
    List<MarCategoryInfoHis> selectMarCategoryInfoHisList(MarCategoryInfo marCategoryInfo);


    /**
     * 获取所有监控类目
     *
     * @param
     * @return 监控类目集合
     */
    List<MarCategory> selectMarCategoryByName();

    /**
     * 新增营销类目监控信息
     *
     * @param marCategoryInfo 营销类目监控信息
     * @return 结果
     */
    int insertMarCategoryInfo(MarCategoryInfo marCategoryInfo);

    /**
     * 新增监控竞品ASIN(多条)
     *
     * @param asins ASIN数据集
     * @return 结果
     */
    String saveMonitoringAsins(String[] asins, UserEntity userEntity);


    /**
     * 新增监控竞品ASIN(单条)
     *
     * @param asin
     * @return 结果
     */
    int saveMonitoringAsin(String asin,UserEntity userEntity);


    /**
     * 修改营销类目监控信息
     *
     * @param marCategoryInfo 营销类目监控信息
     * @return 结果
     */
    int updateMarCategoryInfo(MarCategoryInfo marCategoryInfo);

    /**
     * 批量删除营销类目监控信息
     *
     * @param ids 需要删除的营销类目监控信息ID
     * @return 结果
     */
    int deleteMarCategoryInfoByIds(Long[] ids);

    /**
     * 删除营销类目监控信息信息
     *
     * @param id 营销类目监控信息ID
     * @return 结果
     */
    int deleteMarCategoryInfoById(Long id);

    /**
     * 接收Mq消息，类目监控
     *
     * @param message mq消息体
     * @return 结果
     */
    void saveMqCategoryAsin(String message);

    /**
     * 获取分类下拉时间
     *
     * @param dateStr 时间格式字符串
     * @return 结果
     */
    List<CategoryDateListEntity> selectCategoryDateList(String dateStr);


    /**
     * Description: 营销类目监控趋势图
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2023/12/22
     */
    List<CompeteSectionRes> selectCompeteTendency(CompeteRequest competeRequest);


    /**
     * @description: 返回最新抓取时间
     * @author: Moore
     * @date: 2024/8/15 19:03
     * @param
     * @return: java.lang.String
    **/
    String selectMaxCaptureTime();


    /**
     * 查询营销类目监控信息列表
     *
     * @param marCategoryInfo 营销类目监控信息
     * @return 营销类目监控信息集合
     */
    JSONObject selectMarCategoryInfoBiList(MarCategoryInfo marCategoryInfo);

    /**
     * @desc
     * <AUTHOR>
     * @date 2025/5/12 18:24
     * param
 * @param marCategoryInfo
     * return
     */
    void exportBiList(MarCategoryInfo marCategoryInfo);


    /**
     * Description: 营销类目监控趋势图
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2023/12/22
     */
    JSONObject selectBiCompeteTendency(CompeteRequest competeRequest,Long contextId);

}
