package com.bizark.op.api.service.ticket;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.entity.op.ticket.EmailFolderCount;
import com.bizark.op.api.entity.op.ticket.SyncEmailInfo;
import com.bizark.op.api.entity.op.ticket.SyncEmailInfo;

import java.util.List;

/**
 * 邮件信息Service接口
 *
 * <AUTHOR>
 * @date 2022-05-12
 */
public interface ISyncEmailInfoService extends IService<SyncEmailInfo> {
    /**
     * 查询邮箱信息
     *
     * @param infoId 邮箱信息ID
     * @return 邮箱信息
     */
    SyncEmailInfo selectEmailInfoById(Long infoId);

    /**
     * 查询邮箱信息列表
     *
     * @param emailInfo 邮箱信息
     * @return 邮箱信息集合
     */
//    List<SyncEmailInfo> selectEmailInfoList(SyncEmailInfo emailInfo);

    /**
     * 新增邮箱信息
     *
     * @param emailInfo 邮箱信息
     * @return 结果
     */
    int insertEmailInfo(SyncEmailInfo emailInfo);

    /**
     * 修改邮箱信息
     *
     * @param emailInfo 邮箱信息
     * @return 结果
     */
    int updateEmailInfo(SyncEmailInfo emailInfo);
//
//    /**
//     * 修改邮箱信息
//     *
//     * @param emailInfo 邮箱信息
//     * @return 结果
//     */
//    int updateEmailInfo(SyncEmailInfo emailInfo);
//
//    /**
//     * 批量删除邮箱信息
//     *
//     * @param infoIds 需要删除的邮箱信息ID
//     * @return 结果
//     */
//    int deleteEmailInfoByIds(Long[] infoIds);
//
//    /**
//     * 删除邮箱信息信息
//     *
//     * @param infoId 邮箱信息ID
//     * @return 结果
//     */
//    int deleteEmailInfoById(Long infoId);
//
//    /**
//     * 通过邮箱ID统计各文件夹邮件数量
//     *
//     * @param merchantEmailId
//     * @return
//     */
//    List<EmailFolderCount> selectEmailFolderCountByEmailId(Long merchantEmailId);

    /**
     * 同步邮件信息
     */
    void syncEmailInfo();

}
