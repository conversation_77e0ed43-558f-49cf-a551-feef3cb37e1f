package com.bizark.op.api.service.promotions;

import java.util.Date;

/**
 * @remarks:生成促销费用的数据检查服务
 * @Author: Ailill
 * @Date: 2023/11/27 19:23
 */
public interface IAmzPromotionFeeDataVerifyService {

    /**
     * 同步 16:30 promotion中perFunding为空的
     * @param date
     */
    void promotionFeeDataVerify(Date date);

    /**
     * 16:30查询coupon的asin不存在于mar_listing_info的进行通知，存在的并且list_price为空的进行获取
     * @param date
     */
    void couponFeeDataVerify(Date date);

    /**
     * 通知 17:30 promotion中perFunding为空的
     * @param date
     */
    void promotionFeeDataVerifyAdvice(Date date);


    /**
     * 17:30查询coupon的asin不存在于mar_listing_info的进行通知，存在的并且list_price为空的进行获取,未获取到的进行通知
     * @param date
     */
    void couponFeeDataVerifyAdvice(Date date);

    /**
     * 每隔一小时promotion状态为processing的进行同步
     */
    void promotionStateSync();

    /**
     * 每隔一小时promotion,coupon状态为取消异常修改异常大于2小时且店铺后台已存在的进行同步
     */
    void promotionCouponExceptionStateSync();
}
