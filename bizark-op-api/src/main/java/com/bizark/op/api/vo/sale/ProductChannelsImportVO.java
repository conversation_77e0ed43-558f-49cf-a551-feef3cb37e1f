package com.bizark.op.api.vo.sale;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.*;
import com.bizark.op.api.annotation.LogConvert;
import com.bizark.op.api.annotation.excel.ExcelField;
import com.bizark.op.api.enm.sale.ApprovalEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.inventory.ProductChannelsInventory;
import com.bizark.op.api.entity.op.sale.ProductChannelApproval;
import com.bizark.op.api.entity.op.sale.ProductChannelRelate;
import com.bizark.op.api.entity.op.sale.Products;
import com.bizark.op.api.entity.op.sale.vo.AccountsSellerResponse;
import com.bizark.op.api.service.sale.ProductChannelApprovalService;
import com.bizark.op.api.service.sale.ProductChannelRelateService;
import com.bizark.op.api.service.sale.ProductChannelsService;
import com.bizark.op.common.annotation.BindDBEntity;
import com.bizark.op.common.annotation.BindDBField;
import com.bizark.op.common.annotation.DictConvert;
import com.bizark.op.common.annotation.ExcelMapping;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.context.annotation.Description;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * SKu 映射
 */
@Data
@Description("SKU映射")
public class ProductChannelsImportVO implements Serializable {


    private Integer id;

    /**
     * 'single','bundle','virtual' default 'single'
     */
    private String type;


    // amazon/wayfair
    private String sourceType;

    @ExcelProperty(value = "产品类型*",order = 4)
    private String typeDict;

    private Integer orgId;


    /**
     * 店铺ID
     */
    @TableField(exist = false)
    private Integer shopId;


    private Integer dataType;

    private Integer skuType;

    private Integer skuOrderType;

    private String skuOrderTypeDict;


    @ExcelProperty(value = "店铺名称*", order = 1)
    private String accountId;

    @ExcelProperty(value = "账号名称", order = 0)
    public String accountInit;

    private String listingId;

    private String name;

    private String currencyCode;

    private BigDecimal price;

    private BigDecimal quantity;


    private String inventoryItemId;


    /**
     * 导入导出使用
     */
    @ExcelProperty(value = "数量",order = 6)
    private String quantityStr;


    private Integer lineId;

    private Integer productId;

    @ExcelProperty(value = "SKU", order = 5)
    private String erpSku;

    /**
     * sellerSku
     */
    @ExcelProperty(value = "SellerSku*", order = -1)
    private String sellerSku;


    @ExcelProperty(value = "Asin", order = 8)
    private String asin;

    /**
     * 后边会把parent_asin存下来 用于asin1
     */
    @ExcelProperty(value = "父Asin", order = 9)
    private String asin1;


    @ExcelProperty(value = "Item Label", order = 7)
    private String fnsku;




    private String sellStatus;

    @ExcelProperty(value = "销售状态*", order = 2)
    private String sellStatusDict;


    private String saleChannel;

    @ExcelProperty(value = "销售渠道", order = 4)
    private String saleChannelDict;


    @ExcelProperty(value = "是否推送库存", order = 13)
    private String isPushDict;


    private String isPush;

    /**
     * 用于计算的比例
     */
    @ExcelProperty("推送比例(%)")
    private Integer pushProportion;


    @ExcelProperty("Handing time")
    private Integer handelingTime;


    @ExcelProperty("SellerSku货号")
    private String listingName;




    private Integer operationUserId;



    @ExcelProperty(value = "运营*", order = 14)
    private String operationUserName;


    /**
     * 审批状态
     */
    private String approvalStatus;


    @ExcelProperty(value = "审核状态",order = 3)
    private String approvalStatusDict;




    private String isSeed;

//    @ExcelProperty(value = "是否种子", order = 11)
//    private String isSeedDict;
    @ExcelProperty(value = "标签", order = 11)
    private String tagLabels    ;

    private String isVine;


    @ExcelProperty(value = "是否VINE", order = 12)
    private String isVineDict;


    @ExcelProperty(value = "品牌", order = 10)
    private String brand;


    @TableField(exist = false)
    private Integer rowIndex;
}
