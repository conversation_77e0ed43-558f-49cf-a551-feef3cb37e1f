package com.bizark.op.api.amazon.vendor.fba.model;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> @ClassName GenerateTransportationOptionsRequest
 * @description: TODO
 * @date 2025年08月26日
 */
public class GenerateTransportationOptionsRequest {


    /**
     * placementOptionId : pl1234abcd-1234-abcd-5678-1234abcd5678
     * shipmentTransportationConfigurations : [{"contactInformation":{"email":"<EMAIL>","name":"<PERSON>","phoneNumber":"1234567890"},"freightInformation":{"declaredValue":{"amount":5.5,"code":"CAD"},"freightClass":"FC_50"},"pallets":[{"dimensions":{"height":5,"length":3,"unitOfMeasurement":"CM","width":4},"quantity":2,"stackability":"STACKABLE","weight":{"unit":"KG","value":5.5}}],"readyToShipWindow":{"start":"2024-01-01T00:00Z"},"shipmentId":"sh1234abcd-1234-abcd-5678-1234abcd5678"}]
     */

    private String placementOptionId;
    private List<ShipmentTransportationConfigurationsBean> shipmentTransportationConfigurations;

    public String getPlacementOptionId() {
        return placementOptionId;
    }

    public void setPlacementOptionId(String placementOptionId) {
        this.placementOptionId = placementOptionId;
    }

    public List<ShipmentTransportationConfigurationsBean> getShipmentTransportationConfigurations() {
        return shipmentTransportationConfigurations;
    }

    public void setShipmentTransportationConfigurations(List<ShipmentTransportationConfigurationsBean> shipmentTransportationConfigurations) {
        this.shipmentTransportationConfigurations = shipmentTransportationConfigurations;
    }

    public static class ShipmentTransportationConfigurationsBean {
        /**
         * contactInformation : {"email":"<EMAIL>","name":"John Smithy","phoneNumber":"1234567890"}
         * freightInformation : {"declaredValue":{"amount":5.5,"code":"CAD"},"freightClass":"FC_50"}
         * pallets : [{"dimensions":{"height":5,"length":3,"unitOfMeasurement":"CM","width":4},"quantity":2,"stackability":"STACKABLE","weight":{"unit":"KG","value":5.5}}]
         * readyToShipWindow : {"start":"2024-01-01T00:00Z"}
         * shipmentId : sh1234abcd-1234-abcd-5678-1234abcd5678
         */

        private ContactInformationBean contactInformation;
        private FreightInformationBean freightInformation;
        private ReadyToShipWindowBean readyToShipWindow;
        private String shipmentId;
        private List<PalletsBean> pallets;

        public ContactInformationBean getContactInformation() {
            return contactInformation;
        }

        public void setContactInformation(ContactInformationBean contactInformation) {
            this.contactInformation = contactInformation;
        }

        public FreightInformationBean getFreightInformation() {
            return freightInformation;
        }

        public void setFreightInformation(FreightInformationBean freightInformation) {
            this.freightInformation = freightInformation;
        }

        public ReadyToShipWindowBean getReadyToShipWindow() {
            return readyToShipWindow;
        }

        public void setReadyToShipWindow(ReadyToShipWindowBean readyToShipWindow) {
            this.readyToShipWindow = readyToShipWindow;
        }

        public String getShipmentId() {
            return shipmentId;
        }

        public void setShipmentId(String shipmentId) {
            this.shipmentId = shipmentId;
        }

        public List<PalletsBean> getPallets() {
            return pallets;
        }

        public void setPallets(List<PalletsBean> pallets) {
            this.pallets = pallets;
        }

        public static class ContactInformationBean {
            /**
             * email : <EMAIL>
             * name : John Smithy
             * phoneNumber : 1234567890
             */

            private String email;
            private String name;
            private String phoneNumber;

            public String getEmail() {
                return email;
            }

            public void setEmail(String email) {
                this.email = email;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getPhoneNumber() {
                return phoneNumber;
            }

            public void setPhoneNumber(String phoneNumber) {
                this.phoneNumber = phoneNumber;
            }
        }

        public static class FreightInformationBean {
            /**
             * declaredValue : {"amount":5.5,"code":"CAD"}
             * freightClass : FC_50
             */

            private DeclaredValueBean declaredValue;
            private String freightClass;

            public DeclaredValueBean getDeclaredValue() {
                return declaredValue;
            }

            public void setDeclaredValue(DeclaredValueBean declaredValue) {
                this.declaredValue = declaredValue;
            }

            public String getFreightClass() {
                return freightClass;
            }

            public void setFreightClass(String freightClass) {
                this.freightClass = freightClass;
            }

            public static class DeclaredValueBean {
                /**
                 * amount : 5.5
                 * code : CAD
                 */

                private double amount;
                private String code;

                public double getAmount() {
                    return amount;
                }

                public void setAmount(double amount) {
                    this.amount = amount;
                }

                public String getCode() {
                    return code;
                }

                public void setCode(String code) {
                    this.code = code;
                }
            }
        }

        public static class ReadyToShipWindowBean {
            /**
             * start : 2024-01-01T00:00Z
             */

            private String start;

            public String getStart() {
                return start;
            }

            public void setStart(String start) {
                this.start = start;
            }
        }

        public static class PalletsBean {
            /**
             * dimensions : {"height":5,"length":3,"unitOfMeasurement":"CM","width":4}
             * quantity : 2
             * stackability : STACKABLE
             * weight : {"unit":"KG","value":5.5}
             */

            private DimensionsBean dimensions;
            private Integer quantity;
            private String stackability;
            private WeightBean weight;

            public DimensionsBean getDimensions() {
                return dimensions;
            }

            public void setDimensions(DimensionsBean dimensions) {
                this.dimensions = dimensions;
            }

            public Integer getQuantity() {
                return quantity;
            }

            public void setQuantity(Integer quantity) {
                this.quantity = quantity;
            }

            public String getStackability() {
                return stackability;
            }

            public void setStackability(String stackability) {
                this.stackability = stackability;
            }

            public WeightBean getWeight() {
                return weight;
            }

            public void setWeight(WeightBean weight) {
                this.weight = weight;
            }

            public static class DimensionsBean {
                /**
                 * height : 5
                 * length : 3
                 * unitOfMeasurement : CM
                 * width : 4
                 */

                private BigDecimal height;
                private BigDecimal length;
                private String unitOfMeasurement;
                private BigDecimal width;

                public BigDecimal getHeight() {
                    return height;
                }

                public void setHeight(BigDecimal height) {
                    this.height = height;
                }

                public BigDecimal getLength() {
                    return length;
                }

                public void setLength(BigDecimal length) {
                    this.length = length;
                }

                public String getUnitOfMeasurement() {
                    return unitOfMeasurement;
                }

                public void setUnitOfMeasurement(String unitOfMeasurement) {
                    this.unitOfMeasurement = unitOfMeasurement;
                }

                public BigDecimal getWidth() {
                    return width;
                }

                public void setWidth(BigDecimal width) {
                    this.width = width;
                }
            }

            public static class WeightBean {
                /**
                 * unit : KG
                 * value : 5.5
                 */

                private String unit;
                private BigDecimal value;

                public String getUnit() {
                    return unit;
                }

                public void setUnit(String unit) {
                    this.unit = unit;
                }

                public BigDecimal getValue() {
                    return value;
                }

                public void setValue(BigDecimal value) {
                    this.value = value;
                }
            }
        }
    }
}
