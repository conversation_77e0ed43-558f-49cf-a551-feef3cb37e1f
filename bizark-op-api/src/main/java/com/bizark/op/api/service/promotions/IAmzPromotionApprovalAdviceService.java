package com.bizark.op.api.service.promotions;


import com.bizark.op.api.entity.op.promotions.AmzPromotions;
import com.bizark.op.api.request.msg.his.AmzPromotionsHisPromotionsReceiveMsg;

import java.util.Date;
import java.util.List;

/**
 * @remarks:copuon活动，promotions审批通知企业微信服务
 * @Author: Ailill
 * @Date: 2023/11/24 10:23
 */
public interface IAmzPromotionApprovalAdviceService {


    /**
     * promotion状态流转到待审批，企业微信消息通知
     * @param promotionsId promotion主键Id
     * @param submitDate 提交日期
     * @param submitName 提交时间
     */
    void promotionsApprovalAdviceWx(Long promotionsId, Date submitDate, String submitName,Integer submitId);
    void temuSupplementaryReportPromotionsApprovalAdviceWx(Long promotionsId, Date submitDate, String submitName,Integer submitId);

    /**
     *
     * @param couponsId coupon活动id
     * @param submitDate
     * @param submitName
     */
    void couponsApprovalAdviceWx(Long couponsId, Date submitDate, String submitName, Integer submitId);

    /**
     * coupon优惠券状态流转到待审批，企业微信消息通知
     *
     * @param couponId   coupon优惠券id
     * @param submitDate
     * @param submitName
     */
    void couponApprovalAdviceWx(Long couponId, Date submitDate, String submitName,Integer submitId);

    void promotionGroupChatAdvice();

    void couponsGroupChatAdvice();

    void promotionExceptionListener(AmzPromotionsHisPromotionsReceiveMsg promotionsMsg, AmzPromotions amzPromotions, Integer type);


    /**
     * promotion返回提交异常、提交失败、修改异常、取消异常 返回后通知到ASIN对应的运营
     * 通知对应运营
     * @param id
     */
    void promotionSubmitExceptionAdvice(Long id, String detail);

    /**
     *coupon返回提交异常、修改异常、取消异常 返回后通知到ASIN对应的运营
     * @param couponsId 活动id
     * @param couponId 优惠券id
     * @param detail
     */
    void couponSubmitExceptionAdvice(Long couponsId, Long couponId, String detail, Integer type);


    void temuPromotionApprovalAdviceWx(List<AmzPromotions> promotionsList, Integer type);

    void rpaCancelSuccessAndSyncPromotionAdvice(AmzPromotions amzPromotions, Integer syncReturnState);
}
