package com.bizark.op.api.vo.mar;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description:
 * @Author: Fountain
 * @Date: 2024/9/13
 */
@Data
public class ListingVo {

    /**
     * 组织id
     */
    private Integer orgId;
    /**
     * 组织id
     */
    @JsonIgnore
    private List<Integer> orgIdList;

    /**
     * 国家
     */
    private String countryCode;

    private String asin;
}

