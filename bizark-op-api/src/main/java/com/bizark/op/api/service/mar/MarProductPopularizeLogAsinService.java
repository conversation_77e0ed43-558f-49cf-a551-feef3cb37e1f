package com.bizark.op.api.service.mar;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.entity.op.mar.*;
import com.bizark.op.api.entity.op.mar.vo.*;
import com.bizark.op.api.entity.op.tk.expert.vo.VideoExportTaskVO;
import com.bizark.op.api.vo.mar.AdAnalysisVo;
import com.bizark.op.api.vo.mar.MarProductPopularizeLogAnalysisItemVo;
import com.bizark.op.common.util.UserUtils;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * @ClassName
 * @Description listing整合ASIN维度Service
 * <AUTHOR>
 * @Date 2024/2/18 9:48
 */
public interface MarProductPopularizeLogAsinService extends IService<MarProductPopularizeLogAsin> {


    /**
     * @param
     * @param marListingQuery
     * @description: 父ASIN列表信息查询
     * @author: Moore
     * @date: 2024/2/18 9:48
     * @return:
     **/
    List<MarProductPopularizeLogParentAsinVO> selectPopularizeLogByParentAsinList(MarListingQuery marListingQuery);


    /**
     * @param
     * @param marListingQuery
     * @description: ASIN列表信息查询
     * @author: Moore
     * @date: 2024/2/18 13:41
     * @return: java.util.List<com.bizark.op.api.entity.op.mar.MarProductPopularizeLogAsin>
     **/
    List<MarProductPopularizeLogAsin> selectPopularizeLogByAsinList(MarListingQuery marListingQuery);

    /**
     * 父ASIN的子体对比
     *
     * @param marListingQuery
     * @return
     */
    List<MarProductPopularizeLogAsin> getListGroupByAsin(MarListingQuery marListingQuery);

    /**
     * 父Asin的子体对比中的饼图
     *
     * @param marListingQuery
     * @return
     */
    List<AsinPieDataVO> getListAsinPieDataVO(MarListingQuery marListingQuery);

    /**
     * 父asin - 售后与评价 - 退货列表分布
     *
     * @param marListingQuery
     * @return
     */
    List<AsinReturnVO> getListAsinReturnVO(MarListingQuery marListingQuery);

    /**
     * 退货原因分布
     *
     * @param marListingQuery
     * @return
     */
    List<AsinReturnReasonAnalysisVO> getListAsinReturnReasonAnalysisVO(MarListingQuery marListingQuery);


    /**
     * 父asin - 售后与评价 - 评论占比
     *
     * @param marListingQuery
     * @return
     */
    CommentPercentageVO getCommentPercentageVO(MarListingQuery marListingQuery);

    /**
     * 父asin - 售后与评价 asin评论详情
     *
     * @param marListingQuery
     * @return
     */
    List<AsinReviewVO> getListAsinReviewVO(MarListingQuery marListingQuery);

    /**
     * 店铺feedback占比
     *
     * @param marListingQuery
     * @return
     */
    CommentPercentageVO shopFeedbackCommentPercentage(MarListingQuery marListingQuery);

    /**
     * 店铺feedback详情
     *
     * @param marListingQuery
     * @return
     */
    List<ShopFeedbackDetailVO> shopFeedbackDetail(MarListingQuery marListingQuery);


    /**
     * @param
     * @param marListingQuery
     * @description: 父，子 ASIN综合分析
     * @author: Moore
     * @date: 2024/2/21 13:42
     * @return: java.util.List<com.bizark.op.api.entity.op.mar.MarProductPopularizeLogParentAsinVO>
     **/
    List<MarProductPopularizeLogAnalysisItemVo> analysisSelectPopularizeLogByParentAsinList(MarListingQuery marListingQuery);

    /**
     * @param
     * @param marListingQuery
     * @description: 父，子ASIN广告分析
     * @author: Moore
     * @date: 2024/2/21 15:55
     * @return: java.util.List<com.bizark.op.api.entity.op.mar.MarProductPopularizeLogParentAsinVO>
     **/
    List<AdAnalysisVo> adAnalysisSelectPopularizeLogByAsinList(MarListingQuery marListingQuery);


    /**
     * @param
     * @param marListingQuery
     * @description: 明细中-推广日志查询
     * @author: Moore
     * @date: 2024/2/21 18:41
     * @return: java.util.List<com.bizark.op.api.entity.op.mar.MarProductPopularizeLogAsin>
     **/
    List<MarProductPopularizeLogAsin> getAsinPopularizeLog(MarListingQuery marListingQuery);


    /**
     * @param
     * @param marListingQuery
     * @description: 获取父子AISN，明细基础信息
     * @author: Moore
     * @date: 2024/2/21 23:42
     * @return: java.util.List<com.bizark.op.api.entity.op.mar.MarProductPopularizeLogParentAsinVO>
     **/
    MarProductPopularizeLogParentAsinVO selectAsinAndListingInfo(MarListingQuery marListingQuery);

    /**
     * @param
     * @param marListingQuery
     * @description: 推广日志父ASIN复制
     * @author: Moore
     * @date: 2024/2/27 18:50
     * @return: com.bizark.op.api.entity.op.mar.MarProductPopularizeLogTemplateEntity
     **/
    MarProductPopularizeLogTemplateEntity selectPopularizeLogCopy(MarListingQuery marListingQuery);


    /**
     * @param
     * @param marListingQuery
     * @description: 父ASIN导出
     * @author: Moore
     * @date: 2024/3/15 13:51
     * @return: java.util.List<com.bizark.op.api.entity.op.mar.MarProductPopularizeLogParentAsinVO>
     **/
    List<MarProductPopularizeLogParentAsinVO> selectPopularizeLogByParentAsinListExport(MarListingQuery marListingQuery);


    void listExport(HttpServletResponse response, MarListingQuery marListingQuery, UserEntity authUserEntity);


    void listExportParentAsin(HttpServletResponse response, MarListingQuery marListingQuery, UserEntity authUserEntity);


    /**
     * Description: 根据排序条件查询相关代码
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/3/25
     */
    List<MarProductPopularizeLogParentAsinVO> queryIfOrderCondition(MarListingQuery marListingQuery);



    /**
     * @description: 同步现有映射与ASIN实时表，进行逻辑删除
     * @author: Moore
     * @date: 2024/4/18 14:54
     * @param
     * @return: void
    **/
    void syncProductChannelToAsinCurrentJob();


    void syncMarProductTime();


    /**
     * @description: 父ASIN列表信息查询调用外部接口
     * @author: Moore
     * @date: 2024/2/18 9:48
     * @param
     * @param marListingQuery
     * @return:
     **/
    JSONObject queryPopularizeLogByParentAsinList(MarListingQuery marListingQuery);


    /**
     * Description: 调用外部接口导出
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/7/25
     */
    void newlistExportParentAsin(MarListingQuery marListingQuery);


    /**
     * @description: 子ASIN列表信息查询
     * @author: Moore
     * @date: 2024/2/18 13:41
     * @param
     * @param marListingQuery
     * @return: java.util.List<com.bizark.op.api.entity.op.mar.MarProductPopularizeLogAsin>
     **/
    JSONObject queryPopularizeLogByAsinList(MarListingQuery marListingQuery);



    /**
     * Description: 调用外部接口导出
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/7/25
     */
    void newlistExportAsin(MarListingQuery marListingQuery);



    /**
     * @description: 推广日志父ASIN复制通过接口
     * @author: Moore
     * @date: 2024/2/27 18:50
     * @param
     * @param marListingQuery
     * @return: com.bizark.op.api.entity.op.mar.MarProductPopularizeLogTemplateEntity
     **/
    JSONObject selectPopularizeLogCopyByInterface(MarListingQuery marListingQuery);

    /**
     * Description: 调用外部详情接口。
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/7/25
     */
    JSONObject newAnalysisDetails(MarListingQuery marListingQuery);



    /**
     * Description: 综合分析调用外部接口
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/11/21
     */
    JSONArray analysisList(MarListingQuery marListingQuery);


    /**
     * Description: 综合分析调用外部接口导出
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/11/21
     */
    void analysisListExport(MarListingQuery marListingQuery);

    /**
     * Description: 订单分析
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/11/21
     */
    JSONArray orderAnalysis(MarListingQuery marListingQuery);

    /**
     * 推广日志
     * @param marListingQuery
     * @return
     */
    JSONObject popularizeLog(MarListingQuery marListingQuery);

    /**
     * 推广日志导出
     * @param
     * @return
     */
    void popularizeLogExport(MarListingQuery marListingQuery);

    /**
     * 广告分析
     * @param marListingQuery
     * @return
     */
    JSONArray adAnalysis(MarListingQuery marListingQuery);

    /**
     * 广告分析导出
     * @param marListingQuery
     * @return
     */
    void adAnalysisExport(MarListingQuery marListingQuery);

    /**
     * 子体对比
     * @param marListingQuery
     * @return
     */
    JSONArray childrenContrast(MarListingQuery marListingQuery);

    /**
     * 价格增加趋势图变化分析
     * @param marListingQuery
     */
    JSONObject priceTendencyAnalysis(MarListingQuery marListingQuery);


    /**
     * Description: 调用外部接口（广告分析趋势图）
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2025/3/5
     */
    JSONArray adAnalysisLineChart(MarListingQuery marListingQuery);


    /**
     * 订单量，销量，销售额趋势图变化分析趋势图变化分析
     * @param marListingQuery
     */
    JSONObject orderNumTendencyAnalysis(MarListingQuery marListingQuery);

    /**
     * 广告趋势图变化分析趋势图变化分析
     * @param marListingQuery
     */
    JSONObject adTendencyAnalysis(MarListingQuery marListingQuery);

    /**
     * 保存备注列表
     * @param
     */
    void saveOrUpdateRemarks(List<MarListingRemarkQuery> marListingRemarkQueryList);

    /**
     * 现库详情查询
     * @param
     */
    JSONObject parentAsinInventoryDetails(MarListingQuery marListingQuery);

    /**
     * 现库详情汇总
     * @param
     */
    JSONObject parentAsinInventoryDetailsSum(MarListingQuery marListingQuery);
}
