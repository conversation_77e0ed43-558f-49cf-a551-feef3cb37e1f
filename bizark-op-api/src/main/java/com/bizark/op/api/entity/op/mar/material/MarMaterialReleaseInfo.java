package com.bizark.op.api.entity.op.mar.material;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.bizark.op.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * tiktok素材发布记录表
 * @TableName mar_material_release_info
 */
@TableName(value ="mar_material_release_info")
@Data
@Accessors(chain = true)
public class MarMaterialReleaseInfo extends BaseEntity {
    /**
     *  
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 组织ID
     */
    private Integer orgId;

    /**
     * 发布编号
     */
    private String releaseNum;

    /**
     * 素材编号
     */
    private String materialNum;

    /**
     * 发布账号ID
     */
    private String businessId;

    /**
     * 账号名
     */
    private String businessName;

    /**
     * 视频ID
     */
    private String videoId;

    /**
     * 视频路径
     */
    private String videoUrl;

    /**
     * 发布类型1.AD 2.达人
     */
    private Integer releaseType;


    /**
     * 0失败 1成功 2生成中
     */
    private Integer status;


    /**
     * 失败原因
     */
    private String failMessage;

    /**
     * tk上传草稿成功返回的ID，用来查询视频帖子的发布状态
     */
    private String shareId;

}