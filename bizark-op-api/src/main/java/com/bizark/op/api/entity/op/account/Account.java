package com.bizark.op.api.entity.op.account;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bizark.boss.api.entity.dashboard.amazon.AmazonMarketPlaceEntity;
import com.bizark.op.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@TableName(value = "dashboard.accounts",excludeProperty = {
        "createdAt","updatedAt","disabledBy","disabledName","disabledAt",
        "searchValue","params"
})
@Data
public class Account extends BaseEntity {

    private Integer id;

    private String parentAccountFlag;

    @NotNull
    private Integer orgId;

    private String accountInit;

    /**
     * 店铺标志
     */
    @NotNull
    private String flag;

    /**
     * 店铺标题
     */
    @NotNull
    private String title;

    /**
     * 店铺类型
     */
    private String type;


    private String site;

    /**
     * 渠道站点名
     */
    private String siteName;

    /**
     * 来源渠道
     */
    private String saleChannel;

    /**
     * 默认订单类型
     */
    @NotNull
    private Integer defaultOrderType;

    /**
     * 币种
     */
    private String currency;

    private String amazonApplicationId;
    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    @TableField("fba_subinventory")
    private String fbaSubInventory;

    @TableField("fba_subinventory1")
    private String fbaSubInventory1;

    @TableField("fba_subinventory_new")
    private Integer fbaSubInventoryNew;

    private String remark;

    /**
     * 开始运营时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "UTC")
    private LocalDate beginSalesDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    private LocalDateTime beginOrderDate;

    private Integer erpOrderSourceId;

    private String manageUserEmail;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    private LocalDateTime created;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    private LocalDateTime updated;

    private String active;

    @TableField("unactive_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    private LocalDateTime unActiveTime;

    private String modelType;

    private String ftpUser;

    private String ftpPassword;

    private String ftpUrl;

    private String connectStr;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 区域编码
     */
    private String areaCode;

    private String saleType;

    private String orderOnlyShow;

    /**
     * 招商
     */
    private String investment;

    /**
     * 招商编码
     */
    private String investmentCode;

    /**
     * 发货允许最大天数
     */
    private Integer timeoutDays;

    private String status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    private LocalDateTime inactiveTime;

    @Column(name = "status_comments")
    private String statusComments;

    private Integer channelCategoryId;

    private String isReturn;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "UTC")
    private LocalDate returnBegin;


    @TableField("is_linerelate")
    private String isLineRelate;

    @TableField("linerelate_id")
    private Integer lineRelateId;

    /**
     * VAT税率(%)
     */
    private BigDecimal vatRate;

    /**
     * 广告鉴权信息
     */
    private String advertisement;

    /**
     * 渠道广告费是否已经授权 0:没 1:有
     */
    private Integer advertisementFlag;

    /**
     * 是否默认手动发货  1:是;0:否
     */
    private Integer defaultManMadeShipment;

    /**
     * 补发单是否平台付运费：0 否;1 是
     */
    private Integer issueAccountPay;

    /**
     * 是否渠道支付运费  1:是;0:否
     */
    private Integer isAccountPay;

    /**
     * 是否开启SFP  1:是;0:否
     */
    @TableField("is_open_sfp")
    private Integer isOpenSFP;

    /**
     * 哪些carrier需要上传附件,逗号隔开
     */
    private String bossShippingMethodCodes;

    /**
     * 账号负责号码
     */
    private String manageUserTels;

    /**
     * 授权时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "UTC")
    private LocalDate authorizationAt;


    /**
     * 授权到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "UTC")
    private LocalDate authorizationExpire;


    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "UTC")
    private LocalDate cnAuthorizationExpire;


    /**
     * 浏览器店铺名称
     */
    private String storeName;

    /**
     * 哪些carrier需要上传附件
     */
    @TableField(exist = false)
    private List<String> bossShippingMethodCode;

    @TableField(exist = false)
    private Integer isMain;

    @TableField(exist = false)
    private Integer allowUpdate;

    @TableField(exist = false)
    private Integer isFk;

    @TableField(exist = false)
    private AmazonMarketPlaceEntity marketPlace;

    /**
     * 亚马逊区域表(amazon_market_place)主键
     */
    private Long placeId;

    /**
     * 广告授权状态
     */
    private String adAuthStatus;

    /**
     * 广告授权码
     */
    private String adOauthCode;

    /**
     * AD访问令牌
     */
    private String adAccessToken;

    /**
     * AD刷新令牌
     */
    private String adRefreshToken;

    /**
     * token类型
     */
    private String adTokenType;

    /**
     * token失效时间
     */
    private Integer adExpiresIn;

    private Long operationId;
//    public void updateAccountShipment(AuthUserDetails user) {
//        EventBus.post(new AccountShipmentEvent(this, user));
//    }
//
//    public void updateAccountAddress(AuthUserDetails user) {
//        EventBus.post(new AccountAddressEvent(this, user));
//    }

    public static final String AD_OAUTH_CODE = "NEW";


    /**
     * 问题：
     *      1. 开启同步的数据类型，查看前端传值，选中的同步类型未传递
     *      2. 注册信息是否用 恒健ERP　－－　amz_shop_address
     *
     *
     *  以下为需要新增字段
     */

    /**
     * 账号类型
     */
    private String accountType;

    private String vendorCode;
    @TableField(exist = false)
    private String billingContact;

    /**
     * 0未删除 1删除
     */
//    @TableLogic(value = "0", delval = "1")
    @TableField("is_delete")
    private Integer isDelete;


    /**
     * Seller_ID
     */
    private String sellerId;

    @TableField(exist = false)
    private List<String> typeQuery;
    @TableField(exist = false)
    private List<String> countryCodeQuery;

    private String manageUser;

    private Integer manageUserId;


    @TableField(exist = false)
    private String managerPhone;


    @TableField(exist = false)
    private String registerCompanyName;


    /**
     * 区域店铺域名
     */
    @TableField(exist = false)
    private String domainName;


    public void querySetting(){
        if (StrUtil.isNotBlank(type)) {
            typeQuery = Arrays.asList(type.split(","));
            type = null;
        }
        if (StrUtil.isNotBlank(countryCode)) {
            countryCodeQuery = Arrays.asList(countryCode.split(","));
            countryCode = null;
        }
    }

}
