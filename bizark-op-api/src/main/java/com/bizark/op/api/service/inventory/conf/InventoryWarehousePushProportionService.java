package com.bizark.op.api.service.inventory.conf;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.entity.op.inventory.conf.InventoryWarehousePushProportion;
import com.bizark.op.api.entity.op.inventory.conf.dto.PushProportionDTO;
import com.bizark.op.api.entity.op.inventory.conf.vo.PushProportionVO;
import com.bizark.fbb.api.entity.fbb.WarehouseEntity;

import java.util.List;


public interface InventoryWarehousePushProportionService extends IService<InventoryWarehousePushProportion> {
    void modifyPushProportion(Integer contextId, PushProportionDTO dto);

    PushProportionVO selectPushProportionByOrgId(Integer contextId);

    List<WarehouseEntity> selectWarehouse(Integer contextId);
}
