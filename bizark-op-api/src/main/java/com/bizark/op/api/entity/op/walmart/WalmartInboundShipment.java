package com.bizark.op.api.entity.op.walmart;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bizark.op.api.serializer.DateSerializer;
import com.bizark.op.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@TableName(value = "walmart_inbound_shipment")
public class WalmartInboundShipment extends BaseEntity {

    private Long id;
    private Integer orgId;
    /**
     * 店铺渠道ID
     */
    private String channelId;
    /**
     * 入库订单编号
     */
    private String inboundOrderId;
    /**
     * 货件ID
     */
    private String shipmentId;
    /**
     * 交货ID
     */
    private String deliveryId;
    private String appointmentDate;
    private String appointmentTime;

    /**
     * 预计入库日期
     */
    private Date appointmentDateTime;
    /**
     * 货件状态
     */
    private String status;
    private String shipmentStatus;
    @JSONField(format = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    @JsonSerialize(using = DateSerializer.class)
    private Date createdDate;
    /**
     * 发货量(货件出库数)
     */
    private Integer shipmentUnits;
    /**
     * 收货量(货件收货数)
     */
    private Integer receivedUnits;
    // 入库数(实际入库数)
    private Integer inboundQty;
    // 差异数
    private Integer diffQty;

    /**
     * 期望送达日期
     */
    @JSONField(format = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    @JsonSerialize(using = DateSerializer.class)
    private Date expectedDeliveryDate;
    @JSONField(format = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    @JsonSerialize(using = DateSerializer.class)
    private Date updatedExpectedDeliveryDate;

    /**
     * 实际送达日期
     */
    @JSONField(format = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    @JsonSerialize(using = DateSerializer.class)
    private Date actualDeliveryDate;

    /**
     * 跟踪号
     */
    private String trackingNos;
    /**
     * 承运商
     */
    private String carrierName;
    private Integer receivedUnitsAtFc;
    private String poType;
    private String shipmentCarrierType;
    private Boolean isExceptionOccurred;
    private String exceptionStatus;
    private Boolean isPoBoxEnabled;

    @TableLogic(value = "0", delval = "1")
    private boolean isDelete;


//    private WalmartShipAddress shipAddress;
//    private WalmartShipAddress returnAddress;

    /**
     * BOL编号
     */
    private String bolNumber;

    /**
     * 提货方式 平台卡车提货 自约卡车提货
     */
    private String pickUpType;

    /**
     * 托盘数
     */
    private Integer palletNumber;

    /**
     * 箱数
     */
    private Integer cartonNumber;

    /**
     * 同步附件已完整 1 是 0 否
     */
    private Integer syncAttachmentComplete;

    /**
     * 是否已生成调拨计划单 1 是 0 否
     */
    private Integer transportPlanGenerated;

    /**
     * 发货仓库
     */
    private Integer shipWarehouseId;

    /**
     * 托盘数是否发送变化 1 是 0 否 默认为1
     */
    private Boolean palletTagIsChanged;

    /**
     * 箱数是否发送变化 1 是 0 否 默认为1
     */
    private Boolean cartonTagIsChanged;

    /**
     * BOL标签是否存在 1 是 0 否 默认为0
     */
    private Boolean bolTagExist;

    private Integer actualOutboundNumber;//实际出库数量


    @TableField(exist = false)
    private List<WalmartInboundShipmentItem> items;


    /**
     * 业务状态 ShipmentProcessStatusEnum
     */
    private String shipmentBusinessStatus;

    /**
     * 是否存在异常 1 是 0 否
     */
    private Boolean exceptionExist;

    /**
     * 承运方 1.平台承运  2.自约承运
     */
    private Integer shipmentCarrier;

    /**
     * 货运类型 LTL,FTL,Small parcel,LCL,FCL
     */
    private String shippingType;

    /**
     * 运费预警 1 是 0 否
     */
    private Boolean freightWarning;

    /**
     * 运费
     */
    private BigDecimal shippingPrice;

    /**
     * 期望提货日期
     */
    private Date expectedClaimGoodsDate;

    /**
     * 提货日期
     */
    private Date claimGoodsDate;


    /**
     * 提交时间（rpa提交时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date subDate;

    /**
     * 单一sellerSKu包装数量
     */
    private Integer singleSellerSkuPackQty;

    /**
     * 混合sellerSKu包装数量
     */
    private Integer mixtureSellerSkuPackQty;

    /**
     * 等级
     */
    private String freightLevel;

    /**
     * 报关价值
     */
    private BigDecimal declarationPrice;

    /**
     * 币种
     */
    private String currency;

    @TableField(exist = false)
    private Integer totalItemQty;
    @TableField(exist = false)
    private String fcName;
    @TableField(exist = false)
    private Integer shopId;

    /**
     * 转仓前货件ID
     */
    private String movedShipmentId;

    /**
     * 店铺标题
     */
    @TableField(exist = false)
    private String shopTitle;

    /**
     * 预估运费
     */
    private BigDecimal estimatedFreight;
}
