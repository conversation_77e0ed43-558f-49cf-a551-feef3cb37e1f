package com.bizark.op.api.entity.op.sale;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bizark.op.api.entity.op.sale.vo.ProductImageVO;
import com.bizark.op.api.service.sale.ProductsService;
import com.bizark.op.common.annotation.BindDBEntity;
import com.bizark.op.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(value = "dashboard.product_channel_relates",
        excludeProperty = {
        "remark","createdBy","createdName",
        "createdAt","updatedBy","updatedName","updatedAt",
        "disabledBy","disabledName","disabledAt","remark",
                "params","searchValue"
})
public class ProductChannelRelate extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer productChannelId;
    private Integer productId;
    private String erpsku;
    private Integer qty;
    private Date created;
    private Date updated;
    private String active;

    /**
     * 装柜数量
     */
    @TableField(exist = false)
    private BigDecimal containerQt;

    @TableField(exist = false)
    private String categoryName;

    @TableField(exist = false)
    private String categoryId;
    @TableField(exist = false)
    private String model;

    @TableField(exist = false)
    private String lineName;

    @TableField(exist = false)
    @BindDBEntity(service = ProductsService.class,relation = "id=productId")
    private Products products;


    /**
     * 包装重量
     */
    @TableField(exist = false)
    private BigDecimal itemWeight;

    /**
     * 长
     */
    @TableField(exist = false)
    private BigDecimal itemLength;


    /**
     * 宽
     */
    @TableField(exist = false)
    private BigDecimal itemWidth;


    /**
     * 高
     */
    @TableField(exist = false)
    private BigDecimal itemHeight;


    /**
     * FOB价
     */
    @TableField(exist = false)
    private BigDecimal ddpCost;


    /**
     * 品牌
     */
    @TableField(exist = false)
    private String brand;


    /**
     * 图片信息
     */
    @TableField(exist = false)
    private String picUrls;


    @TableField(exist = false)
    private Integer lineId;


    @TableField(exist = false)
    private Integer categoryType;
    public String getPicUrls() {
        return this.parseImg();
    }

    public String parseImg() {
        try {
            if (StrUtil.isBlank(this.picUrls)) {
                return null;
            }
            ProductImageVO imageVO = JSON.parseObject(this.picUrls, ProductImageVO.class);
            if (imageVO == null || CollectionUtil.isEmpty(imageVO.getImgList())) {
                return null;
            }
            ProductImageVO.Image image = imageVO.getImgList().get(0);
            return image.getUrl();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return this.picUrls;
    }
}
