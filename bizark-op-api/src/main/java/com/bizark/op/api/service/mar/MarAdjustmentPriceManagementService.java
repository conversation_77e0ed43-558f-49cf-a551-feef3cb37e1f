package com.bizark.op.api.service.mar;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.entity.op.mar.MarAdjustmentPriceManagement;
import com.bizark.op.api.entity.op.mar.MarAdjustmentPriceManagementOperateLog;
import com.bizark.op.api.request.mar.MarAdjustmentPriceManagementQuery;
import com.bizark.op.api.vo.mar.MarAdjustmentPriceManagementForExportVo;
import com.bizark.op.api.vo.mar.MarAdjustmentPriceManagementMessage;
import com.bizark.op.api.vo.mar.MarAdjustmentPriceManagementSendOrReceive;
import com.bizark.op.api.vo.mar.MarAdjustmentPriceManagementVo;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;

import java.util.List;

/**
 * 调价管理Service接口
 *
 * @Author: Ailill
 * @Date: 2024/7/30 15:40
 */
public interface MarAdjustmentPriceManagementService extends IService<MarAdjustmentPriceManagement> {
    /**
     * @Description:列表查询
     * @Author: wly
     * @Date: 2024/7/30 17:08
     * @Params: [query]
     * @Return: java.util.List<com.bizark.op.api.vo.mar.MarAdjustmentPriceManagementVo>
     **/
    List<MarAdjustmentPriceManagementVo> selectAdjustmentPriceList(MarAdjustmentPriceManagementQuery query);

    /**
     * <AUTHOR>
     * @Description
     * @Date 15:24 2025/4/23
     * @Param [query]
     * @return java.util.List<com.bizark.op.api.vo.mar.MarAdjustmentPriceManagementVo>
     **/
    List<MarAdjustmentPriceManagementVo> selectAdjustmentPriceListForExport(MarAdjustmentPriceManagementQuery query,Long pageStart,Long pageEnd);

    /**
     * <AUTHOR>
     * @Description
     * @Date 15:24 2025/4/23
     * @Param [query]
     * @return java.util.List<com.bizark.op.api.vo.mar.MarAdjustmentPriceManagementVo>
     **/
    List<MarAdjustmentPriceManagementForExportVo> selectAdjustmentPriceListForExportV2(MarAdjustmentPriceManagementQuery query, Long pageStart, Long pageEnd);



    /**
     * @Description:批量同意或拒绝
     * @Author: wly
     * @Date: 2024/7/30 17:19
     * @Params: [query]
     * @Return: void
     **/
    void batchOperateAgreeOrRefuse(MarAdjustmentPriceManagementQuery query, UserEntity authUserEntity);

    /**
     * @Description:批量审批
     * @Author: wly
     * @Date: 2024/7/30 17:19
     * @Params: [query]
     * @Return: void
     **/
    void batchOperateApproval(MarAdjustmentPriceManagementQuery query);

    /**
     * @Description:批量重新提交
     * @Author: wly
     * @Date: 2024/7/30 17:19
     * @Params: [query]
     * @Return: void
     **/
    void batchOperateReSubmit(MarAdjustmentPriceManagementQuery query);

    /**
     * @Description:获取操作日志记录
     * @Author: wly
     * @Date: 2024/7/30 17:19
     * @Params: [id]
     * @Return: java.util.List<com.bizark.op.api.vo.mar.MarAdjustmentPriceManagementOperateLog>
     **/
    List<MarAdjustmentPriceManagementOperateLog> selectOperateLogList(Long id);

    /**
     * @Description: 调价管理消息处理
     * @Author: wly
     * @Date: 2024/7/31 18:17
     * @Params: [message]
     * @Return: void
     **/
    void processMessage(MarAdjustmentPriceManagementMessage message);

    /**
     * @Description: 处理同意或拒绝
     * @Author: wly
     * @Date: 2024/7/31 18:17
     * @Params: [message]
     * @Return: void
     **/
    void processAgreeOrReject(MarAdjustmentPriceManagementSendOrReceive message);

    void autoAgreeOrRefuse();

    void marAdjustmentPriceManagementExport(MarAdjustmentPriceManagementQuery query, UserEntity userEntity);

    String asyncMarAdjustmentPriceManagementExport(String query, Integer contextId);


    void startApproval(Integer contextId, UserEntity userEntity, List<MarAdjustmentPriceManagement> list, Boolean flag);
    /**
     * Description: 调用activitiapi
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/9/3
     */
    List<MarAdjustmentPriceManagementSendOrReceive> useActivitiInterface(Integer contextId, UserEntity userEntity, Long businessId, String priceAdjustmentNumber,MarAdjustmentPriceManagement t,Boolean flag);

    /**
     * @Description:处理审批回调
     * @Author: wly
     * @Date: 2024/9/5 11:40
     * @Params: [id]
     * @Return: void
     **/
    void processActivityCall(Long id,Integer auditStatus);


    /**
     * Description: 根据商品id,加上待处理状态查询列表
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/11/25
     */
    List<MarAdjustmentPriceManagementVo> selectAdjustmentPriceMultipleList(MarAdjustmentPriceManagementQuery marAdjustmentPriceManagementQuery);


    /**
     * Description: 多个调价确认单，同意，拒绝
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/11/26
     */
    void multipleQueryBatchBatchOperateAgreeOrRefuse(List<MarAdjustmentPriceManagementQuery> queryList, UserEntity authUserEntity);


    /**
     * Description: 根据店铺+商品id,加上待审核状态查询列表
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/11/25
     */
    List<MarAdjustmentPriceManagementVo> multipleApproveList(List<MarAdjustmentPriceManagementQuery> queryList);
}
