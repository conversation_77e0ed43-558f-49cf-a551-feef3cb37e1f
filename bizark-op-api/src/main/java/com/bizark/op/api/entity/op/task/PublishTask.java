package com.bizark.op.api.entity.op.task;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.common.annotation.BindDBField;
import com.bizark.op.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@TableName(value = "publish_task",excludeProperty = {
        "searchParam","remark"
})
@ExcelIgnoreUnannotated
public class PublishTask extends BaseEntity {

    private Long id;

    private Integer orgId;

    @Schema(description = "SKU映射ID")
    private Integer channelId;

    @Schema(description = "销售渠道")
    private String saleChannel;


    @ExcelProperty(value = "销售渠道", order = 3)
    @BindDBField(relation = "saleChannel",dict = "account_sale_channel")
    private String saleChannelDict;

    @Schema(description = "事务ID")
    private String transactionId;


    @Schema(description = "sellerSku")
    @ExcelProperty(value = "SellerSku", order = 1)
    private String sellerSku;

    @Schema(description = "店铺")
    @ExcelProperty(value = "店铺", order = 2)
    private String accountFlag;


    private String accountTitle;


    @ExcelProperty(value = "国家", order = 4)
    @Schema(description = "国家")
    private String countryCode;


    @TableField(exist = false)
    private List<String> countryCodes;

    @Schema(description = "单位")
    private String unit;


    @ExcelProperty(value = "店铺仓库", order = 13)
    private String warehouse;

    @ExcelProperty(value = "仓库标识符", order = 14)
    private String warehouseId;


    private Integer stockAccountId;

    @Schema(description = "ASIN")
    @ExcelProperty(value = "ASIN", order = 5)
    private String asin;

    @Schema(description = "父ASIN")
    @ExcelProperty(value = "父ASIN", order = 6)
    private String parentAsin;

    @Schema(description = "上架原SKC")
    private String skcId;

    @Schema(description = "版本号")
    private Long version;

    @Schema(description = "任务状态")
    private Integer status;

    @TableField(exist = false)
    @BindDBField(dict = "publish_task_status", relation = "status")
    @ExcelProperty(value = "任务状态", order = 9)
    private String statusDict;

    @Schema(description = "推送类型")
    private Integer pushType;

    @TableField(exist = false)
    @ExcelProperty(value = "处理类型",order = 10)
    @BindDBField(dict = "publish_task_push_type", relation = "pushType")
    private String pushTypeDict;


    @Schema(description = "原因")
    @ExcelProperty(value = "原因", order = 11)
    private String reason;


    @Schema(description = "任务类型")
    private Integer type;

    @ExcelProperty(value = "sellersku货号", order = 15)
    public String sellerskuNumber;

    @TableField(exist = false)
    @BindDBField(dict = "publish_task_type",relation = "type")
    @ExcelProperty(value = "任务类型", order = 7)
    private String typeDict;


    @Schema(description = "任务目标")
    private String taskTarget;


    @Schema(description = "任务内容")
    @ExcelProperty(value = "任务内容", order = 8)
    private String content;


    @Schema(description = "完成时间")
    @ExcelProperty(value = "完成时间", order = 12)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date finishedAt;


    @TableLogic(value = "0", delval = "1")
    private boolean isDelete;

    /**
     * 查询排序使用
     */
    @TableField(exist = false)
    private String orderBy;
    @TableField(exist = false)
    private List<String> orderByParam;

    @TableField(exist = false)
    private String beginTime;

    @TableField(exist = false)
    private String endTime;

    @TableField(exist = false)
    private List<String> sellerSkuQuery;


    @TableField(exist = false)
    private Account account;

    @TableField(exist = false)
    private String statusQuery;

    @TableField(exist = false)
    private List<String> statusParam;


    @TableField(exist = false)
    private Integer timeQueryType;

    @TableField(exist = false)
    private List<String> accountFlagQuery;

    @TableField(exist = false)
    private List<String> saleChannelArray;

    @TableField(exist = false)
    private List<String> parentAsinQuery;
    @TableField(exist = false)
    private List<String> asinQuery;
    @TableField(exist = false)
    private List<String> sellerskuNumberQuery;

    /**
     * 通过mq推送多渠道修改库存
     */
    @TableField(exist = false)
    private boolean mqExec;


    @TableField(exist = false)
    private List<Integer> accountId;

    public void queryCondition(){

        if (StrUtil.isNotBlank(sellerskuNumber)) {
            sellerskuNumberQuery = Arrays.asList(sellerskuNumber.split(","));
            sellerskuNumber = null;
        }

        if (StrUtil.isNotBlank(sellerSku)) {
            sellerSkuQuery = Arrays.asList(sellerSku.split(","));
            sellerSku = null;
        }
        if (StrUtil.isNotBlank(statusQuery)) {
            statusParam = Arrays.asList(statusQuery.split(","));
            statusQuery = null;
            status = null;
        }
        if (StrUtil.isNotBlank(accountFlag)) {
            accountFlagQuery = Arrays.asList(accountFlag.split(","));
            accountFlag = null;
        }
        if (StrUtil.isNotBlank(saleChannel)) {
            saleChannelArray = Arrays.asList(saleChannel.split(","));
            saleChannel = null;
        }
        if (StrUtil.isNotBlank(parentAsin)) {
            parentAsinQuery = Arrays.asList(parentAsin.split(","));
            parentAsin = null;
        }
        if (StrUtil.isNotBlank(asin)) {
            asinQuery = Arrays.asList(asin.split(","));
            asin = null;
        }
        if (StrUtil.isNotBlank(beginTime)) {
            beginTime = beginTime + " 00:00:00";
        }
        if (StrUtil.isNotBlank(endTime)) {
            endTime = endTime + " 23:59:59";
        }
        if (StrUtil.isNotBlank(countryCode)) {
            countryCodes = Arrays.asList(countryCode.split(","));
        }
        if (StrUtil.isNotBlank(orderBy)) {
            orderByParam = new ArrayList<>();
            String[] split = orderBy.split(",");
            for (String s : split) {
                String[] orderByCondition = s.split("-");
                String orderByField = StrUtil.toUnderlineCase(orderByCondition[0]);
                String orderBy = orderByCondition[1];
                orderByParam.add(orderByField + " " + orderBy);
            }
        }
    }
}
