package com.bizark.op.api.entity.op.task;

import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.entity.op.sale.ProductChannelsDraft;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PublishTaskInfo implements Serializable {

    /**
     * 立即失败
     */
    private Boolean quickFailure = Boolean.TRUE;


    public PublishTaskInfo(PublishTask task) {
        this.publishTask = task;
    }


    private PublishTask publishTask;

    private ProductChannels channels;

    private Account account;



    private Integer retryCount = 0;


    // 标记当前是否为重试请求
    private boolean isRetry;


    public boolean estimateAndIncrRetryCount(Integer totalCount){
        if (retryCount < totalCount) {
            retryCount++;
            return true;
        }
        return false;
    }



}
