package com.bizark.op.api.service.ticket.email;



import com.bizark.op.api.entity.op.ticket.ScTicket;
import com.bizark.op.api.entity.op.ticket.email.ScEmailSend;

import java.util.List;

/**
 * 邮件发送Service接口
 *
 * <AUTHOR>
 * @date 2022-03-03
 */
public interface IScEmailSendService {
    /**
     * 查询邮件发送
     *
     * @param sendId 邮件发送ID
     * @return 邮件发送
     */
    ScEmailSend selectScEmailSendById(Long sendId);

    /**
     * 查询邮件发送列表
     *
     * @param scEmailSend 邮件发送
     * @return 邮件发送集合
     */
    List<ScEmailSend> selectScEmailSendList(ScEmailSend scEmailSend);

    /**
     * 新增邮件发送
     *
     * @param scEmailSend 邮件发送
     * @return 结果
     */
    int insertScEmailSend(ScEmailSend scEmailSend);

    /**
     * 修改邮件发送
     *
     * @param scEmailSend 邮件发送
     * @return 结果
     */
    int updateScEmailSend(ScEmailSend scEmailSend);

    /**
     * 发送邮件
     *
     * @param scEmailSend
     */
    int sendEmail(ScEmailSend scEmailSend);

    /**
     * 批量删除邮件发送
     *
     * @param sendIds 需要删除的邮件发送ID
     * @return 结果
     */
    int deleteScEmailSendByIds(Long[] sendIds);

    /**
     * 删除邮件发送信息
     *
     * @param sendId 邮件发送ID
     * @return 结果
     */
    int deleteScEmailSendById(Long sendId);


    /**
     * Description: 工单发送邮件
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/1/25
     */
    int ticketSendEmail(ScEmailSend scEmailSend);


    /**
     * Description: 校验nf站外信回复
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/1/25
     */
    ScTicket checkReplyEmail(Integer contextId, Long ticketId, String ticketType);


    /**
     * Description: 第一次站外信回复
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/1/26
     */
    int replyOutSiteMail(ScEmailSend scEmailSend);
}
