package com.bizark.op.api.entity.op.returns.VO;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.bizark.op.api.entity.op.returns.StatReturnQuery;
import com.bizark.op.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年9月19日  11:58
 * @description: 退货分析VO类
 */
@ExcelIgnoreUnannotated
@Data
@ColumnWidth(value = 25)
public class ReturnAnalyzeVO {

    /**
     * 退货日期
     */
    @Excel(name = "日期")
    @ExcelProperty(value = "退货日期")
    private String returnDate;


    /**
     * asin
     */
    @Excel(name = "asin")
    @ExcelProperty(value = "ASIN")
    private String asin;

    /**
     * sku
     */
    @Excel(name = "SKU")
    @ExcelProperty(value = "SKU")
    private String sku;

    /**
     * 店铺id
     */
    private Integer shopId;

    /**
     * 店铺名称
     */
    @Excel(name = "店铺")
    @ExcelProperty(value = "店铺")
    private String shopName;


    /**
     * 渠道
     */
    @Excel(name = "渠道")
    @ExcelProperty(value = "渠道")
    private String channel;

    /**
     * 退货原因
     */

    @ExcelProperty(value = "平台退货原因")
    private String returnReason;

    /**
     * 运营id
     */
    private Integer operateId;

    /**
     * 运营名称
     */
    @Excel(name = "运营")
    @ExcelProperty(value = "运营")
    private String operateName;

    /**
     * 分类查询
     */
    @Excel(name = "品类")
    @ExcelProperty(value = "品类")
    private String categoryName;

    /**
     * 退货数量
     */
    @ExcelProperty(value = "退货数量")
    private Integer returnNum;

    @Excel(name = "退货数量", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal returnNumBig;


    @ExcelProperty(value = "销售数量")
    private Integer saleNum;

    /**
     * 销售数量，VC使用
     */
    @Excel(name = "销售数量", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal saleNumBig;

    /**
     * 退货率
     */
    @Excel(name = "PPM", cellType = Excel.ColumnType.NUMERIC)
    @ExcelProperty(value = "PPM")
    private BigDecimal returnRate;

    /**
     * 收货数量
     */
    private Integer receiptNum;

    /**
     * 收货率
     */
    private BigDecimal receiptRate;

    /**
     * 退货原因分类
     */
    private String returnReasonCategory;

    /**
     * 退货原因分类名称
     */
    @Excel(name = "退货原因")
    @ExcelProperty(value = "退货原因")
    private String returnReasonCategoryName;

    /*
     * 销售数量
     */
    private Integer quantity;

    /*
     * 明细退货数据
     */
    private List<ReturnAnalyzeVO> childTimeRetuns;


    /**
     * 订单日期
     */
    @ExcelProperty(value = "订单日期")
    private String orderDate;

    /**
     * 父类的查询条件
     */
    private StatReturnQuery statReturnQuery;

    private Integer categoryId;

    private String shopIdGroupString;

    private String categoryIdGroupString;

    /**
     * 当前日，或当前周，或当前月销量
     */
    private Integer saleCurrentNum;

    /**
     * 型号
     */
    @ExcelProperty(value = "型号")
    private String productMidModel;
}
