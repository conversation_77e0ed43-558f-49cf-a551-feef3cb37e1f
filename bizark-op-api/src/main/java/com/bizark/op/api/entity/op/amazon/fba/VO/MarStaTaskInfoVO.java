package com.bizark.op.api.entity.op.amazon.fba.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.Date;
import java.util.List;

/**
 * STA货件任务列表
 *
 * @TableName mar_sta_task_info
 */

@Schema(description = "STA任务信息")
@Data
public class MarStaTaskInfoVO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 组织ID
     */
    @Schema(description = "组织ID")
    private Long orgId;
    /**
     * 店铺ID
     */
    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "店铺名")
    private String shopName;

    @Schema(description = "国家")
    private String countryCode;

    /**
     * 任务编号
     */
    @Schema(description = "任务编号")
    @Length(max = 100, message = "编码长度不能超过100")
    private String inboundPlanId;
    /**
     * 任务名称
     */
    @Schema(description = "任务名称")
    @Length(max = 255, message = "编码长度不能超过255")
    private String taskName;
    /**
     * 状态 DRAFT,IN_PROGRESS,CANCELLED,SHIPPED，EXCEPTION
     */
    @Schema(description = "状态 DRAFT,IN_PROGRESS,CANCELLED,SHIPPED，EXCEPTION")
    @Length(max = 30, message = "编码长度不能超过30")
    private String taskStatus;
    /**
     * 任务节点 1.选择发货商品 2商品装箱 3.配送服务 4.箱子标签 5.货件追踪
     */
    @Schema(description = "任务节点 1.选择发货商品 2商品装箱 3.配送服务 4.箱子标签 5.货件追踪")
    private Integer taskNode;


    /**
     * STA创建成功时间(真实STA创建时间，首次为用户暂存时间)
     */
    @Schema(description = "STA创建成功时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date staCreatedAt;

    /**
     * 分仓方式 1.先装箱在分仓
     */
    @Schema(description = "分仓方式 1.先装箱在分仓")
    private Integer distributionMode;

    /**
     * 申报总数量
     */
    @Schema(description = "申报总数量")
    private Integer applyNum;
    /**
     * 包装组数量
     */
    @Schema(description = "包装组数量")
    private Integer packGroupCount;
    /**
     * 承运人 OTHER 其他 AMZ 亚马逊合作承运人
     */
    @Schema(description = "承运人 OTHER 其他 AMZ 亚马逊合作承运人")
    @Length(max = 30, message = "编码长度不能超过30")
    private String carrier;
    /**
     * 承运方式
     */
    @Schema(description = "承运方式")
    @Length(max = 30, message = "编码长度不能超过30")
    private String carrierMethod;
    /**
     * 运输类型 SPD 小包裹快递 LTL 汽运零担
     */
    @Schema(description = "运输类型 SPD 小包裹快递 LTL 汽运零担")
    @Length(max = 30, message = "编码长度不能超过30")
    private String shippingType;
    /**
     * 运输方式 1.空运、2.海运、3.陆运
     */
    @Schema(description = "运输方式 1.空运、2.海运、3.陆运")
    private Integer shippingMethod;
    /**
     * 备注
     */
    @Schema(description = "备注")
    @Length(max = 255, message = "编码长度不能超过255")
    private String remark;

    /**
     * 创建者名称
     */
    @Schema(description = "创建者名称")
    @Length(max = 255, message = "编码长度不能超过255")
    private String createdName;


    /**
     * 更新时间戳
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /**
     * 更新时间戳
     */
    @Schema(description = "更新时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;


    /**
     * FBA货件信息
     */
    private List<FbaInfo> fbaInfoList;


    public static class FbaInfo {

        /**
         * 货件编号
         */
        private String shipmentConfirmationId;

        /**
         * 货件状态
         */
        private String shipmentState;

        public String getShipmentConfirmationId() {
            return shipmentConfirmationId;
        }

        public void setShipmentConfirmationId(String shipmentConfirmationId) {
            this.shipmentConfirmationId = shipmentConfirmationId;
        }

        public String getShipmentState() {
            return shipmentState;
        }

        public void setShipmentState(String shipmentState) {
            this.shipmentState = shipmentState;
        }
    }



}
