package com.bizark.op.api.entity.op.sale.vo;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
@ToString
public class AmazonProductChannelVO implements Serializable {


    private Integer orgId;

    private String saleChannel;

    private String status;

    private String asin;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date openDate;

    private BigDecimal price;


    private BigDecimal salePrice;

    @Schema(description = "店铺标记")
    private String accountId;

    /**
     * 产品标识符
     */
    private String upc;
    private String isbn;
    private String gtin;
    private String ean;

    private String sellerSku;


    /**
     * 下面为新增字段
     **/

    @Schema(description = "销售状态")
    private String sellStatus;


    @Schema(description = "标题")
    private String itemName;

    @Schema(description = "库存")
    private BigDecimal quantity;


    @Schema(description = "图片链接")
    private String imageUrl;


    @Schema(description = "产品标识符类型")
    private String productIdType;


    @Schema(description = "商品 ID（ASIN）")
    private String asin1;

    @Schema(description = "产品标识符")
    private String productId;




    @Schema(description = "配送方式")
    private String fulfillmentChannel;


    @Schema(description = "Cost（子账号抓数据存储）")
    private BigDecimal cost;


    private String brand;

    @Schema(description = "发布状态")
    private String publishedStatus;


    private String itemId;


    private String skuId;




    /**
     * 区分两个队列的消息
     */
    private Integer messageType;

    private BigDecimal listingPrice;


    private String sellerSkuName;

    private Integer skuStock;


    private String skcId;


    private String spuId;

    private String catId;

    private String catName;


    private Boolean isPrimary;



    /**
     * 评价数
     */
    private Integer reviewsCount;
    /**
     * 评分
     */
    private String averageRating;

    /**
     * 评分
     */
    private String priceType;
    /**
     * 促销开始时间
     */
    @JSONField(format = "yyyy-MM-dd")
    private Date promoStartDate;

    /**
     * 促销结束时间
     */
    @JSONField(format = "yyyy-MM-dd")
    private Date promoEndDate;

    /**
     * 比较价格
     */
    private BigDecimal comparisonPrice;

    /**
     * 比较价格类型
     */
    private String comparisonPriceType;

    /**
     * 购物车价格
     */
    private BigDecimal buyBoxItemPrice;

    /**
     * 购物车运费
     */
    private BigDecimal buyBoxShippingPrice;;

    /**
     * 是否符合购物车资格
     */
    private String buyBoxEligible;

    private List<WarehouseInventory> inventories;

    private String currencyCode;


    public ProductChannels convert(){
        ProductChannels channels = new ProductChannels();
        channels.setSellerSku(sellerSku).setPrice(price).setCurrencyCode(currencyCode)
                .setDistributionMode(fulfillmentChannel)
                .setImageUrl(imageUrl).setSellerSkuPrice(price)
                .setSellStatus(sellStatus).setAccountId(accountId)
                .setBrand(brand).setAsin(asin).setUpc(upc)
                .setSaleChannel(saleChannel).setGtin(gtin)
                .setPublishedStatus(publishedStatus).setIdentifierType(productIdType)
                .setIdentifier(productId).setListingPrice(listingPrice)
                .setItemId(itemId).setSkuId(skuId)
                .setSkcId(skcId).setInventory(skuStock)
                .setIsbn(isbn).setTitle(itemName).setAsin1(asin1)
                .setSpuId(spuId)
                .setIsPrimary(isPrimary != null && isPrimary ? 1 : 0)
                .setListingName(sellerSkuName);
        if (StrUtil.isNotBlank(itemId)) {
            channels.setItemId(itemId);
            if ("Ebay".equalsIgnoreCase(saleChannel)) {
                channels.setAsin1(itemId);
            }
        }
        channels.setCatName(catName);
        if (StrUtil.isNotBlank(catId)) {
            channels.setCatId(Integer.parseInt(catId));
        }
        channels.settingMeta();




        return channels;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WarehouseInventory {


        private String warehouseName;

        private Integer quantity;

    }

//    private String id;
//    private String channelId;
//    private String sku;
//    private String channelName;
//    private String reportId;
//    private String listingId;
//    private BigDecimal price;

//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    private Date openDate;

//    private String itemIsMarketplace;

//    private BigDecimal zshopShippingFee;
//    private String itemCondition;
//    private String zshopCategory1;
//    private String zshopStoreFeature;
//    private String asin2;
//    private String asin3;
//    private String willShipInternational;
//    private String expeditedShipping;
//    private String zshopBoldface;

//    private String bidForFeature;
//    private String addDelete;
//    private BigDecimal pendingQuantity;

//    private String marketplace;
//    private String status;
//    private String ZSHOP_BROWSE_PATH;
//    private String MERCHANT_SHIPPING_GROUP;
//    private Date lastUpdatedStamp;
//    private Date lastUpdatedTxStamp;
//    private Date createdStamp;
//    private Date createdTxStamp;
//    private String itemName;
//    private String itemDesc;
//    private String itemNode;


}
