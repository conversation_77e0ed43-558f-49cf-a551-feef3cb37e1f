package com.bizark.op.api.entity.op.returns.VO;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

import java.util.Date;
import java.util.List;

/**
 * 退货申请计划VO
 *
 * @Author: Ailill
 * @Date: 2024/1/24 14:23
 */
@Data
public class TiktokReturnRefundPlanVO implements Serializable {

    /**
     * tiktokReturnRefund id
     */
    private Long refundId;

    /**
     * 退货id
     */
    private String returnId;

    /**
     * 渠道标识
     */
    private String channelId;

    /**
     * 店铺Id
     */
    private Long shopId;

    /**
     * 退货承运商（原始承运商）
     */
//    @NotEmpty(message = "退货承运商不能为空")
    private String returnProviderName;

    /**
     * 组织id
     */
    private Integer contextId;
    /**
     * 订单id (生成退货计划设置的订单主键）
     */
    private Integer saleOrderId;
    /**
     * 亚马逊订单号
     */
    @NotEmpty(message = "订单号不能为空")
    private String saleOrderNo;


    /**
     * 项目Id
     */
    private Long lineId;

    /**
     * 仓库id
     */
    @NotNull(message = "退货仓库不能为空")
    private Integer orgWarehouseId;
    /**
     * 仓库code
     */
    @NotEmpty(message = "退货仓库code不能为空")
    private String orgWarehouseCode;


    /**
     * 发货人
     */
    @NotEmpty(message = "发货人不能为空")
    private String consignor;

    /**
     * 国家
     */
    @NotEmpty(message = "国家不能为空")
    private String country;

    /**
     * 州/省份
     */
    @NotEmpty(message = "州/省份不能为空")
    private String province;

    /**
     * 地址1
     */
    @NotEmpty(message = "地址1不能为空")
    private String addressOne;

    /**
     * 邮编
     */
    @NotEmpty(message = "邮编不能为空")
    private String zipCode;

    /**
     * 城市
     */
    @NotEmpty(message = "城市不能为空")
    private String city;

    /**
     * 电话
     */
    @NotEmpty(message = "电话不能为空")
    private String phone;

    /**
     * 退货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "退货时间不能为空")
    private Date returnRefundDate;

    /**
     * 退货原因
     */
    private String returnReason;

    private String addressTwo;

    private String remark;


    /**
     * 退货sku数组
     */
    private List<TiktokReturnRefundVoItem> productItemRequests;

    @Data
    public static class TiktokReturnRefundVoItem implements Serializable{


        /**
         * 此明细行对应的returnInfo表主键
         */
        private Integer returnInfoId;

        /**
         * erpsku
         */
        private String sku;
        /**
         * 产品id
         */
        private Integer productId;


        /**
         * sellerSKu 退货数量，原始退货数量
         */
        private Integer sellerSkuQty;

        /**
         * erpsku的退货数量（实际用户要求数量)
         */
        private Integer qty;

        /**
         * 退货方式（转换后承运商） 1.AMSP 2.UPSG 3.AMXL 4.FEDEX 5.WLC-UPS....
         */
        private String returnShippingMethod;

        /**
         * 跟踪号
         */
        private String tracking;

        /**
         * 面单 跟踪号类型1.购买面单 0.用户自填
         */
        private Integer trackingType;

        /**
         * 项目id
         */
        private Integer lineId;

        /**
         * SellerSKu
         */
        private String sellerSku;


        /**
         * 商品图片
         */
        private String productImages;

    }
}
