package com.bizark.op.api.entity.op.mar.material.creatify.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.bizark.op.api.entity.op.mar.material.creatify.request.CreatifyCreateVideoFromLinkRequest;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.UUID;

@Data
public class CreatifyCreateVideoFromLinkResponse implements Serializable {


    private String id;
    private String name;

    @JSONField(name = "target_platform")
    private String targetPlatform;

    @JSONField(name = "target_audience")
    private String targetAudience;

    private String language;

    @JSONField(name = "video_length")
    private Integer videoLength;

    @JSONField(name = "aspect_ratio")
    private String aspectRatio;

    @JSONField(name = "script_style")
    private String scriptStyle;

    @JSONField(name = "visual_style")
    private String visualStyle;

    @JSONField(name = "override_avatar")
    private String overrideAvatar;

    @JSONField(name = "override_voice")
    private String overrideVoice;

    @JSONField(name = "override_script")
    private String overrideScript;

    @JSONField(name = "background_music_url")
    private String backgroundMusicUrl;

    @JSONField(name = "background_music_volume")
    private Double backgroundMusicVolume;

    @JSONField(name = "voiceover_volume")
    private Double voiceoverVolume;

    @JSONField(name = "webhook_url")
    private String webhookUrl;

    private String link;

    @JSONField(name = "media_job")
    private String mediaJob;

    private String status;

    @JSONField(name = "failed_reason")
    private String failedReason;

    @JSONField(name = "is_hidden")
    private Boolean isHidden;

    @JSONField(name = "video_output")
    private String videoOutput;

    @JSONField(name = "credits_used")
    private Integer creditsUsed;

    private String progress;

    @JSONField(name = "no_background_music")
    private Boolean noBackgroundMusic;

    @JSONField(name = "no_caption")
    private Boolean noCaption;

    @JSONField(name = "no_emotion")
    private Boolean noEmotion;

    @JSONField(name = "no_cta")
    private Boolean noCta;

    @JSONField(name = "no_stock_broll")
    private Boolean noStockBroll;

    private String preview;

    @JSONField(name = "caption_style")
    private String captionStyle;

    @JSONField(name = "caption_offset_x")
    private String captionOffsetX;

    @JSONField(name = "caption_offset_y")
    private String captionOffsetY;


    @JSONField(name = "caption_setting")
    private CaptionSetting captionSetting;

    public static CreatifyCreateVideoFromLinkResponse test(CreatifyCreateVideoFromLinkRequest fromLinkRequest) {
        CreatifyCreateVideoFromLinkResponse response = new CreatifyCreateVideoFromLinkResponse();
        BeanUtils.copyProperties(fromLinkRequest, response);
        response.setId(UUID.randomUUID().toString());
        return response;
    }


    @Data
    public static class CaptionSetting {

        private String style;
        private Offset offset;
        @JSONField(name = "font_family")
        private String fontFamily;

        @JSONField(name = "font_size")
        private Integer font_size;

        @JSONField(name = "font_style")
        private String fontStyle;

        @JSONField(name = "background_color")
        private String backgroundColor;

        @JSONField(name = "text_color")
        private String textColor;

        @JSONField(name = "highlight_text_color")
        private String highlightTextColor;

        @JSONField(name = "max_width")
        private Integer maxWidth;

        @JSONField(name = "line_height")
        private Integer lineHeight;

        @JSONField(name = "text_shadow")
        private String textShadow;


        private Boolean hidden;
    }


    @Data
    public static class Offset {
        private Double x;
        private Double y;

    }


    /**
     * 自定义字段，返回错误信息使用
     */
    private String errorMsg;
    private boolean success = true;


    public static CreatifyCreateVideoFromLinkResponse fail(String errorMsg) {
        CreatifyCreateVideoFromLinkResponse response = new CreatifyCreateVideoFromLinkResponse();
        response.setSuccess(false);
        response.setErrorMsg(errorMsg);
        response.setFailedReason(errorMsg);
        return response;
    }
}
