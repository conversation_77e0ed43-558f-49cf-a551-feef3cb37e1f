package com.bizark.op.api.entity.op.sale.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class ProductInventory implements Serializable {

    private String accountInit;

    private String accountTitle;

    private String accountFlag;

    private Integer accountId;

    private String sellerSku;

    private String saleChannel;

    private BigDecimal sellerSkuQuantity;

    private Long channelId;

    private String itemId;

    private String wareHouseId;

    private String wareHouse;

    private String sku;

    private String erpsku;

    private String skuQuantity;

    private String mark;

    private String unit;

    private Integer handelingTime;

    public void appendSkuQuantity(Object qty) {
        if (skuQuantity == null) {
            skuQuantity = String.valueOf(qty);
        } else {
            skuQuantity = skuQuantity + "," + qty;
        }
    }

}
