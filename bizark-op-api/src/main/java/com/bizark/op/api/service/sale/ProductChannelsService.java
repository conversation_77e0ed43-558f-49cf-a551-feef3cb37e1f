package com.bizark.op.api.service.sale;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.boss.api.entity.dashboard.stock.StockAccountAddressEntity;
import com.bizark.op.api.dto.sale.*;
import com.bizark.op.api.enm.sale.ApprovalEnum;
import com.bizark.op.api.enm.sale.ProductChannelTypeEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.account.ApprovalDTO;
import com.bizark.op.api.entity.op.product.SkuChildren;
import com.bizark.op.api.entity.op.sale.*;
import com.bizark.op.api.entity.op.sale.dto.ProductChannelExportDTO;
import com.bizark.op.api.entity.op.sale.vo.*;
import com.bizark.op.api.vo.mar.ListingVo;
import com.bizark.op.api.vo.product.ProductChannelsVO;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public interface ProductChannelsService extends IService<ProductChannels> {


    ProductChannels getOne(String sellerSku, String accountId);

    /**
     * 获取SKU映射信息列表数据
     * @param channels
     * @param contextId
     * @return
     */
    List<ProductChannels> selectProductChannels(ProductChannels channels, Integer contextId);

    public void approvalNodeQuerySetting(ProductChannels channels);
    void settingUserInfo(List<ProductChannels> productChannels);

    public void buildCategoryNameWithMultipleVersionSku(List<ProductChannels> productChannels, Map<String, List<SkuChildren>> childrenMap );

    public void skuInventorySettingWithMultipleVersionSku(Integer contextId, Map<String, List<SkuChildren>> childrenMap, List<ProductChannels> productChannels, boolean show);
    public void skuInventorySetting(Integer contextId, List<ProductChannels> productChannels,boolean show);

        /**
         * 新增或修改SKU映射信息
         *
         * @param channels
         * @param contextId
         */
    void saveOrUpdateProductChannels(List<ProductChannels> channels, Integer contextId, UserEntity userEntity,boolean isImport,Map<String, Integer> configMap);

    ChannelImportResultVO saveOrUpdateProductChannels(List<ProductChannels> channels, Integer confirm, Integer contextId, UserEntity userEntit, Map<String, Integer> configMap);

    /**
     * 同步SKU映射信息
     *
     * @param channels
     * @param contextId
     */
    void synoSaveOrUpdateProductChannels(List<ProductChannels> channels, Integer contextId, UserEntity userEntity);


    /**
     * 更新审批状态
     * @param approvalDTO
     * @param contextId
     */
    void updateApproval(ApprovalDTO approvalDTO, Integer contextId);

    /**
     * 提交审批
     * @param id
     * @param contextId
     */
    void commitApproval(Integer[] id, Integer contextId);

    /**
     * 查看明细信息
     * @param id
     * @param contextId
     * @return
     */
    ProductChannels selectProductChannelsById(Integer id, Integer contextId);

    /**
     * 删除
     * @param ids
     */
    void deleteProductChannels(Integer[] ids);

    /**
     * MQ 消息存储
     * @param message
     */
    void saveProductChannels(String message,String type);

    /**
     * 分配运营
     * @param dto
     */
    void allotOperation(ApprovalDTO dto);

    /**
     * 更新销售状态
     * @param approvalDTO
     * @param contextId
     */
    void updateSaleState(ApprovalDTO approvalDTO, Integer contextId);


    void syncProductChannels(List<ProductChannels> channels, Integer contextId);

    /**
     * 更新ASIN信息
     * @param ids
     */
    void updateAsinInfo(List<Integer> ids);

    void updateProductChannelsInfoNoTransactional(List<ProductChannels> productChannels, boolean recordLog,long time, TimeUnit timeUnit);

    void updateProductChannelsInfoWithTransactional(List<ProductChannels> productChannels, boolean recordLog,long time, TimeUnit timeUnit);


    /**
     * 队列接受数据返回，更新ASIN信息
     * @param message
     */
    void updateReturnAsinInfo(String message);

    void exportProductChannel(ProductChannelsExportQuery channels, List<Integer> ids, Integer contextId, HttpServletResponse response) throws IOException;

    String exportProductChannelAsync(String json);
    String exportProductChannelAsync(ProductChannelExportDTO exportDTO);
    String exportProductChannelAsyncDiscard(String json);


//    public void syncScGoodsSkuMap(Integer contextId, List<ProductChannels> list);


    List<ProductChannelsVO> selectByAsinList(List<String> asinList, Integer contextId, Integer type, String saleChannel);


    List<ProductChannelsVO> selectByTkSpuList(List<String> spuListList, Integer contextId, Integer type, String saleChannel);

    /**
     * 导入SKU映射信息
     * @param file
     * @param confirm  是否确认导入
     * @param contextId
     */
    ChannelImportResultVO importProductChannels(MultipartFile file,Integer confirm, Integer contextId,UserEntity userEntity) throws NoSuchFieldException, InvocationTargetException, NoSuchMethodException, IllegalAccessException, IOException;

    void settingLineIdAndProductId(List<ProductChannels> channels);


    /**
     * 数据同步恒健ERP
     * @param ids
     * @param contextId
     */
    void syncData(Integer[] ids, Integer contextId);

    void downLoadProductChannelsExcel(HttpServletResponse response, Integer contextId) throws IOException, IllegalAccessException;


    public void checkSaveData(Integer orgId, List<ProductChannels> channels, Map<String, Integer> configMap);


    void fixChannelsQuantity(Integer contextId);

    List<String[]> buildString(Integer contextId);

    void approvalWithdraw(Integer contextId, Integer[] ids);

    /**
     * Amazon获取SellerSku信息
     *
     * @param sellerSku
     * @return
     */
    AccountsSellerResponse queryAmazonBySellerSku(String flag, String sellerSku, Integer contextId);

    /**
     * 处理多渠道SKU映射数据
     * @param typeEnum
     * @param channelVO
     */
    void multiChannelProductChannels(ProductChannelTypeEnum typeEnum, AmazonProductChannelVO channelVO);

    /**
     * @description: 获取所有ASIN信息
     * @author: Moore
     * @date: 2023/9/21 10:15
     * @param
     * @return: java.util.List<java.lang.String>
     **/
    List<ListingVo> selectAsinListingAll(Integer contextId);


    /**
     * @description: 提供review抓取ASIN
     * @author: Moore
     * @date: 2023/9/21 10:15
     * @param
     * @return: java.util.List<java.lang.String>
     **/
    List<ListingVo> selectReviewAsinList();

    /**
     * 查询当前库存信息
     * @param ids
     * @return
     */
    List<ProductInventoryVO> inventoryInfo(Integer[] ids,boolean checkPush);

    List<ProductInventoryVO> inventoryInfo(List<ProductChannels> channels, boolean checkPush);

    public void buildInventoryItemId(List<ProductChannels> productChannels, Account account);

    List<ProductInventory> selectInVentory(Integer[] ids, Integer contextId);

    public Map<String, StockAccountAddressEntity> getAddressMap(List<Integer> flags);

    void syncInventory(Integer[] ids);

    void updateInventoryWarehouse(List<StockAccountAddressEntity> addresses);

    public List<ProductChannels> check(Integer orgId, List<ProductChannels> channels, List<Account> accounts);

    void updateRestricted(String accountId, String sellerSku, String restrictedReason,boolean eq);
    void updateRestricted(String accountId, List<String> sellerSkus, String restrictedReason,boolean in);

    void updateRestricted(Integer id, String restrictedReason);

    void syncRestricte(Integer... ids);

    ProductChannels asinCountryCheck(AsinCountryCheckDTO dto);

    Map<String, List<String>> queryProductChannelsByAsin(Long orgId, List<String> asinList);

    void repairOrgId();

    ChannelImportResultVO checkAsinSameAsSku(Integer contextId, Integer[] ids);

    List<ProductChannels> getProductChannelList(Long orgId, Collection<String> erpskus, Collection<String> accountIds, Collection<String> asins);

    void repairProduct();

    List<ErpSkuStockVO> selectSkuInventory(Long id);

    void updateListingPrice(Integer orgId, String accountFlag);

    void updateListingPrice(Account account);

    void updateInventoryPushConf(ChannelsInventoryConfReq req);



    ProductChannels selectTemuChannel(String asin, Integer accountId);


    void streamByOrgId(Integer orgId, ResultHandler<ProductChannels> handler);


    void initChannelsPush(Integer orgId);


    void syncWalmartSaleQty(Integer orgId);

    void syncWalmartWfsInventory(Integer accountId);

    List<ProductChannels> selectByIdCursor(Integer cursor, int limit);

    List<ProductChannels> selectByIdCursor(Integer orgId,String saleChannel, Integer cursor, int limit,String sellStatus);


    ProductChannels selectSkuInventoryById(Integer id);

    ProductChannels selectProductChannelById(Integer id);

    /**
     * Description: walmart 商品id查询。
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/7/23
     */
    List<WalmartAsinVo> selectWalmartItemId();


    void updateApprovalStatus(ProductChannels channels, ApprovalEnum approvalEnum, String opinion);

    void allocationInstanceId(Integer id, String instanceId);


    void initApprovalNodeByChannelId(Integer channelId);

    void initApprovalNodeByCondition(Integer orgId, String saleChannel, String sellerStatus);

    void repairSlave();

    void allocateTags(ProductChannelAllocateTagDTO dto);

    List<String> selectBrandsByOrgId(Integer contextId);

    void createExportTask(ProductChannelsExportQuery channels, List<Integer> ids, Integer contextId, UserEntity authUserEntity);


    void initMyDepotSellerSku(String sellerSku);


    ProductChannels selectByAccountIdAndSellerSku(String accountId, String sellerSku);
    List<ErpSkuStockVO> selectInventoryByErpSku(Integer contextId, List<String> erpSkus);

    void initCostPrice();


    void referenceCostPrice(Integer orgId, String erpSku, BigDecimal costPrice);

    List<ProductChannels> selectTiktokProductList(TiktokProductQueryDTO dto);

    Integer resetInventoryStatus();

    void initSkuVersionSerial(Integer orgId);

    void initMultipleSkuVersion();

    void sellerSkuProjectUpdate(SellerSkuUpdateLineDTO dto);

}
