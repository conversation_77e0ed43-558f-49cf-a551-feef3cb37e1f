package com.bizark.op.api.entity.op.sale.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bizark.op.api.annotation.LogConvert;
import com.bizark.op.api.annotation.excel.ExcelField;
import com.bizark.op.common.annotation.BindDBField;
import com.bizark.op.common.annotation.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ExcelIgnoreUnannotated
@ColumnWidth(25)
public class ProductChannelsExportVO implements Serializable {



    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer orgId;

    /**
     * 'single','bundle','virtual' default 'single'
     */
    @Schema(description = "产品类型")
    @LogConvert(dict = "sku_mapping_type")
    @ExcelField(title = "typeDict",dict = "sku_mapping_type")
    private String type;


    @ExcelProperty(value = "平台库存",order = 9)
    private Integer inventory;


    @ExcelProperty(value = "供货价",order = 10)
    private BigDecimal sellerSkuPrice;


    @ExcelProperty(value = "价格预警",order = 11)
    private String priceWarning;


    private BigDecimal costPrice;

    @ExcelProperty(value = "产品类型*",order = 13)
    @BindDBField(dict = "sku_mapping_type",relation = "type")
    @TableField(exist = false)
    @DictConvert(dict = "sku_mapping_type", target = "type")
    private String typeDict;


    @TableField(value = "account_id")
    @ExcelProperty(value = "店铺名称*", order = 4)
    @Schema(description = "店铺标记")
    private String accountTitle;

    @ExcelProperty(value = "企业", order = 5)
    @Schema(description = "企业")
    private String company;


    @ExcelProperty(value = "账号名称", order = 3)
    @TableField(exist = false)
    public String accountInit;

    @TableField(value = "listing_id")
    @Schema(description = "listingId")
    private String listingId;

    @TableField(value = "name")
    @Schema(description = "名称")
    private String name;

    @TableField(value = "currency_code")
    @Schema(description = "币种")
    private String currencyCode;

    @TableField(value = "price")
    @Schema(description = "价格")
    private BigDecimal price;

    @TableField(value = "quantity")
    @Schema(description = "数量")
    private BigDecimal quantity;


    @Schema(description = "库存ID")
    private String inventoryItemId;

    @TableField(exist = false)
    private String accountFlag;


    /**
     * 导入导出使用
     */
    @ExcelProperty(value = "数量",order = 17)
    @TableField(exist = false)
    private String quantityStr;


    @TableField(value = "line_id")
    @Schema(description = "项目ID")
    private Integer lineId;

    @TableField(value = "product_id")
    @Schema(description = "产品ID")
    private Integer productId;

    @TableField(value = "erpsku")
    @Schema(description = "ErpSku")
    @ExcelProperty(value = "SKU", order = 12)
    private String erpSku;


    @ExcelProperty(value = "产品名称", order = 14)
    private String erpSkuName;

    @ExcelProperty(value = "型号", order = 15)
    private String model;


    @ExcelProperty(value = "分类", order = 16)
    private String categoryName;

    /**
     * sellerSku
     */
    @ExcelProperty(value = "SellerSku*", order = 1)
    private String sellerSku;

    @ExcelProperty(value = "SellerSku货号", order = 2)
    private String listingName;


    private Integer isPrimary;


    @ExcelProperty(value = "是否为主变体", order = 18)
    private String primary;


    @TableField(value = "asin")
    @Schema(description = "Asin")
    @ExcelProperty(value = "商品ID", order = 20)
    private String asin;


    @ExcelProperty(value = "Goods Id",order = 21)
    private String itemId;


//    @ExcelProperty(value = "链接", order = 18)
//    private String url;

    /**
     * 超链接
     *
     * @since 3.0.0-beta1
     */
    @ExcelProperty(value = "链接", order = 22)
    @ContentStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 40)
    private WriteCellData<String> url;



    /**
     * 后边会把parent_asin存下来 用于asin1
     */
    @TableField(value = "asin1")
    @Schema(description = "父Asin")
    @ExcelProperty(value = "父Asin", order = 26)
    private String asin1;


    @TableField(value = "fnsku")
    @Schema(description = "Item Label")
    @ExcelProperty(value = "平台商品编码", order = 19)
    private String fnsku;



    @TableField(value = "sell_status")
    @Schema(description = "销售状态")
    @LogConvert(dict = "sku_sale_status")
    @ExcelField(title = "sellStatusDict",dict = "sku_sale_status")
    private String sellStatus;

    @TableField(exist = false)
    @DictConvert(dict = "sku_sale_status", target = "sellStatus")
    @BindDBField(relation = "sellStatus",dict = "sku_sale_status")
    @ExcelProperty(value = "销售状态*", order = 6)
    private String sellStatusDict;


    @Schema(description = "销售渠道")
    @TableField(value = "sale_channel")
    @LogConvert(dict = "account_sale_channel")
    @ExcelField(title = "saleChannelDict",dict = "account_sale_channel")
    private String saleChannel;

    @TableField(exist = false)
    @DictConvert(dict = "account_sale_channel", target = "saleChannel")
    @ExcelProperty(value = "销售渠道", order = 8)
    @BindDBField(relation = "saleChannel",dict = "account_sale_channel")
    private String saleChannelDict;

    @TableField(value = "upc")
    @ExcelProperty(value = "UPC", order = 29)
    @Schema(description = "Upc")
    private String upc;


    /**
     * 是否推送渠道
     */
    @TableField(value = "is_push")
    @Schema(description = "是否推送库存")
    @ExcelField(title="isPushDict",map = {"是:Y","否:N"})
    private String isPush;


    @TableField(exist = false)
    @ExcelProperty(value = "是否推送库存", order = 23)
    private String isPushDict;

    /**
     * 用于计算的比例
     */
    @TableField(value = "push_proportion")
    @ExcelProperty(value = "推送比例(%)", order = 24)
    @Schema(description = "推送比例(%)")
    private Integer pushProportion;


    @TableField(value = "handeling_time")
    @ExcelProperty(value = "Handing time", order = 25)
    private Integer handelingTime;



    @Schema(description = "运营名称")
    @ExcelProperty(value = "运营*", order = 31)
    private String operationUserName;


    @ExcelProperty(value = "部门", order = 32)
    private String deptName;
    /**
     * 审批状态
     */
    @TableField("approval_status")
    @Schema(description = "审批状态")
    @LogConvert(dict = "approval_status")
    @ExcelField(title = "approvalStatusDict",dict = "approval_status")
    private String approvalStatus;


    @DictConvert(dict = "approval_status",target = "approvalStatus")
    @TableField(exist = false)
    @BindDBField(relation = "approvalStatus",dict = "approval_status")
    @ExcelProperty(value = "审核状态",order = 7)
    private String approvalStatusDict;



    @ExcelField(title = "isSeedDict", map = {"是:Y", "否:N"})
    @Schema(description = "是否种子")
    @LogConvert(value = {"是:Y", "否:N"})
    private String isSeed;

    @TableField(exist = false)
//    @ExcelProperty(value = "是否种子", order = 28)
    private String isSeedDict;


    @TableField(exist = false)
    @ExcelProperty(value = "标签", order = 28)
    private String tagsDict;


    private String tags;


    //    @ExcelProperty(value = "是否VINE", order = 13)
    @Schema(description = "是否VINE")
    @ExcelField(title = "isVineDict", map = {"是:Y", "否:N"})
    private String isVine;


    @ExcelProperty(value = "是否VINE", order = 30)
    @TableField(exist = false)
    @LogConvert(value = {"是:Y", "否:N"})
    private String isVineDict;



    @TableField("brand")
    @ExcelProperty(value = "品牌", order = 27)
    @Schema(description = "品牌")
    private String brand;


    @ExcelProperty(value = "招商", order = 21)
    private String investment;


    @ExcelProperty(value = "平台仓库存", order = 33)
    private Integer availToSellQty;

    @ExcelProperty(value = "SKU库存", order = 34)
    private Integer skuMinInventory;


    @ExcelProperty(value = "是否为父级SKU", order = 35)
    private String isParentSku;

}
