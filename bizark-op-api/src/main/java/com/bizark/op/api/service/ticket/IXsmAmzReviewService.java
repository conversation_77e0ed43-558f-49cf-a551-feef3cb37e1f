package com.bizark.op.api.service.ticket;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.entity.op.ticket.ScOrderReview;
import com.bizark.op.api.entity.op.ticket.ScTicket;
import com.bizark.op.api.entity.op.ticket.XsmAmzReview;

import java.util.List;

/**
 * ReviewService接口
 *
 * <AUTHOR>
 * @date 2022-05-10
 */
public interface IXsmAmzReviewService {

    /**
     * 保存亚马逊评论信息-RabbitMQ获取
     *
     * @param payload
     */
    void saveXsmAmzReview(String payload);


    /**
     * Description: 保存walmart评论
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2025/3/12
     */
    void saveWalmartReview(String jsonString);

}
