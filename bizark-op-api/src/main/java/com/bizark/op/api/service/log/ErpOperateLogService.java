package com.bizark.op.api.service.log;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.entity.op.log.ErpLogModify;
import com.bizark.op.api.entity.op.log.ErpOperateLog;
import com.bizark.op.api.entity.op.log.ErpOperateLogDTO;
import com.bizark.op.api.entity.op.tk.expert.dto.TKExpertLogDTO;

import java.lang.reflect.Field;
import java.util.List;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @description 针对表【ad_operate_log(广告操作日志表)】的数据库操作Service
 * @createDate 2023-02-28 18:01:35
 */
public interface ErpOperateLogService extends IService<ErpOperateLog> {


    List<ErpOperateLog> selectOpLogList(ErpOperateLogDTO operateLog);


    <T> void logRecord(T before, T after, String opName, boolean isInclude, boolean checkNull, String... fields);
    <T> void logRecord(Integer userId,String userName,T before, T after, String opName, boolean isInclude, boolean checkNull, String... fields);

    public <T> void logRecord(Supplier<List<T>> supplier, List<T> after,String unique, String opName, boolean isInclude,boolean checkNull, String... fields);
    public <T> void logRecord(List<T> supplier, List<T> after,String unique, String opName, boolean isInclude,boolean checkNull, String... fields);


    <T> void recordInsert(T t,String uniqueField);

    <T> void recordInsert(T t, String tableName, String uniqueField);
    <T> void recordInsert(T t,Integer userId, String userName,String tableName, String uniqueField);


    <T> void recordInsertAsync(T t);
    <T> void recordInsert(T t);

    <T> void recordInsert(List<T> t,String uniqueField);
    <T> void recordInsert(List<T> t);

    <T> void recordInsert(List<T> t, String tableName, String uniqueField);

    <T> boolean compareBean(T one, T other, String... fields);

    public <T> List<Field> getLogFields(T before, T after, List<String> fields, boolean isIncludes, boolean checkNull);

    List<ErpOperateLog> selectExpertLog(TKExpertLogDTO dto);

   <T> List<ErpLogModify> checkFieldsUpdate(List<String> fields, T before, T after);


    <T> List<ErpLogModify> checkFieldsUpdateToSchema(List<String> fields, T before, T after);

    List<ErpOperateLog> selectLogFixedRows(ErpOperateLogDTO operateLog);
}
