package com.bizark.op.api.entity.op.xsm;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.bizark.op.common.annotation.DictConvert;
import com.bizark.op.common.annotation.ExcelHead;
import com.bizark.op.common.converter.CustomDateConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
@ExcelIgnoreUnannotated
@ColumnWidth(25)
@Data
public class XsmReviewCommonExport implements Serializable {
    private Long id;

    @ExcelProperty(value = "图片", index = 0)
    @ExcelHead(name = "图片", hidden = false, width = 20)
    private String imageUrl;


    @ExcelProperty(value = "平台",index = 1)
    @ExcelHead(name = "平台", hidden = false, width = 20)
    private String type;

    @ExcelProperty(value = "店铺", index = 2)
    @ExcelHead(name = "店铺", hidden = false, width = 20)
    private String accountTitle;


    @ExcelProperty(value = "商品ID", index = 3)
    @ExcelHead(name = "商品ID", hidden = false, width = 20)
    private String detailAsin;

    @ExcelProperty(value = "父商品ID", index = 4)
    @ExcelHead(name = "父商品ID", hidden = false, width = 20)
    private String asin1;

    @ExcelProperty(value = "SKU", index = 5)
    @ExcelHead(name = "SKU", hidden = false, width = 20)
    private String erpSku;

    @ExcelProperty(value = "型号", index = 6)
    @ExcelHead(name = "型号", hidden = false, width = 20)
    private String productMidModel;


    @ExcelProperty(value = "商品分类", index = 7)
    @ExcelHead(name = "商品分类", hidden = false, width = 20)
    private String categoryName;


    @ExcelProperty(value = "部门/运营", index = 8)
    @ExcelHead(name = "部门/运营", hidden = false, width = 20)
    private String operationUserName;

    @ExcelProperty(value = "星级", index = 9)
    @ExcelHead(name = "星级", hidden = false, width = 20)
    private String starRating;

    @ExcelProperty(value = "标签", index = 10)
    @ExcelHead(name = "标签", hidden = false, width = 20)
    private String avpBadge;

    @ExcelProperty(value = "点赞数", index = 11)
    @ExcelHead(name = "点赞数", hidden = false, width = 20)
    private String likes;


    @ExcelProperty(value = "留评日期",converter = CustomDateConverter.class, index = 12)
    @ExcelHead(name = "留评日期", hidden = false, width = 20 )
    private Date reviewDate;


    @ExcelProperty(value = "留评人", index = 13)
    @ExcelHead(name = "留评人", hidden = false, width = 20)
    private String customerName;

    @Schema(description = "标题")
    @ExcelProperty(value = "标题", index = 14)
    @ExcelHead(name = "标题", hidden = false, width = 20)
    private String reviewTitle;


    @Schema(description = "评论内容")
    @ExcelProperty(value = "评论内容", index = 14)
    @ExcelHead(name = "评论内容", hidden = false, width = 20)
    private String reviewContent;

    @Schema(description = "翻译后的内容")
    @ExcelProperty(value = "翻译后的内容", index = 14)
    @ExcelHead(name = "翻译后的内容", hidden = false, width = 20)
    private String reviewContentTranslate;

    @Schema(description = "回复内容")
    @ExcelProperty(value = "回复内容", index = 14)
    @ExcelHead(name = "回复内容", hidden = false, width = 20)
    private String reviewResponse;


    @Schema(description = "评论链接")
    @ExcelProperty(value = "评论链接", index = 14)
    @ExcelHead(name = "评论链接", hidden = false, width = 20)
    private String reviewUrl;


    @Schema(description = "订单号")
    @ExcelProperty(value = "订单号", index = 14)
    @ExcelHead(name = "订单号", hidden = false, width = 20)
    private String amazonOrderId;


    @Schema(description = "推广订单")
    @ExcelProperty(value = "推广订单", index = 14)
    @ExcelHead(name = "推广订单", hidden = false, width = 20)
    private String promote;//是否推广订单

    @Schema(description = "是否更新")
    @ExcelProperty(value = "是否更新", index = 14)
    @ExcelHead(name = "是否更新", hidden = false, width = 20)
    private String updated;

    @Schema(description = "更新时间")
    @ExcelProperty(value = "更新时间", index = 14, converter = CustomDateConverter.class)
    @ExcelHead(name = "更新时间", hidden = false, width = 20)
    private Date updateDate;

    @Schema(description = "工单状态")
    @ExcelProperty(value = "工单状态", index = 14)
    @ExcelHead(name = "工单状态", hidden = false, width = 20)
    @DictConvert(dict = "ticket_status",target = "ticketStatus")
    private String ticketStatus;//工单状态


    @Schema(description = "处理人")
    @ExcelProperty(value = "处理人", index = 14)
    @ExcelHead(name = "处理人", hidden = false, width = 20)
    private String handlerBy;//处理人


    @Schema(description = "处理时间")
    @ExcelProperty(value = "处理时间", index = 14,converter = CustomDateConverter.class)
    @ExcelHead(name = "处理时间", hidden = false, width = 20)
    private String handleTime;

    @Schema(description = "跟踪结果")
    @ExcelProperty(value = "根据结果", index = 14)
    @ExcelHead(name = "跟踪结果", hidden = false, width = 20)
    @DictConvert(dict = "sc_ticket_tracking", target = "trackingResult")
    private String trackingResult;//跟踪结果

    @Schema(description = "工单编号")
    @ExcelProperty(value = "工单编号", index = 14)
    @ExcelHead(name = "工单编号", hidden = false, width = 20)
    private String ticketNumber;//工单编号

    @ExcelProperty(value = "卖家", index = 15)
    @ExcelHead(name = "卖家", hidden = false, width = 20)
    private String seller;//卖家

    @ExcelProperty(value = "评价来源", index = 16)
    @ExcelHead(name = "评价来源", hidden = false, width = 20)
    private String reviewSource;//评价来源


    private List<String> categoryNames;



    private String deptName;


}
