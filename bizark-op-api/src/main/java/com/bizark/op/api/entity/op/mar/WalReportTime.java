package com.bizark.op.api.entity.op.mar;

import com.bizark.op.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 沃尔玛报时间状态对象 wal_report_time
 *
 * <AUTHOR>
 * @date 2023-06-14
 */
@Data
public class WalReportTime extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 组织ID
     */
    private Long organizationId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 可供下载的报表时间
     */
    // @Excel(name = "可供下载的报表时间")
    private String reportTime;

    /**
     * 转换后可供下载的报表时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    //@Excel(name = "转换后可供下载的报表时间", width = 15, dateFormat = "yyyy-MM-dd")
    private Date convertReportTime;

    /**
     * 下载状态0未下载 1已下载 2下载失败
     */
    // @Excel(name = "下载状态0未下载 1已下载 2下载失败", cellType = Excel.ColumnType.NUMERIC)
    private Integer downloadFlag;

    /**
     * 同步payment状态 0未同步 1已同步
     */
    private Integer paymentSyncFlag;

    /**
     * 下载报表时间
     */
    private Date downloadTime;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }


    /**
     * 同步费用索赔状态 0未同步 1已同步
     */
    private Integer deliveryFeeClaimFlag;

    /**
     * 查询条件 未同步false 已同步true
     */
    private String ifDeliveryFeeClaimFlag;

}
