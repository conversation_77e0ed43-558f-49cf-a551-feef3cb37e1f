package com.bizark.op.api.service.ticket;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bizark.boss.api.enm.order.BooleanEnum;
import com.bizark.common.exception.CommonException;
import com.bizark.op.api.entity.op.mar.material.MarMaterialCenterInfo;
import com.bizark.op.api.entity.op.refund.OrderRefundRequest;
import com.bizark.op.api.entity.op.refund.OrderRefundRequestInfo;
import com.bizark.op.api.entity.op.returns.DO.MarReturnManuaItemDTO;
import com.bizark.op.api.entity.op.sale.TemuViolation;
import com.bizark.op.api.entity.op.sale.TemuViolationDetail;
import com.bizark.op.api.entity.op.tabcut.vo.TabcutVideoTagRealtionDTO;
import com.bizark.op.api.entity.op.ticket.*;
import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.op.common.util.StringUtils;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工单管理Service接口
 *
 * <AUTHOR>
 * @date 2022-01-12
 */
public interface IScTicketService  {
    /**
     * 查询工单管理
     *
     * @param ticketId 工单管理ID
     * @return 工单管理
     */
    ScTicket selectScTicketById(Long ticketId);

    /**
     * 查询工单管理列表
     *
     * @param scTicket 工单管理
     * @return 工单管理集合
     */
    List<ScTicket> selectScTicketList(ScTicket scTicket);

    /**
     * @Description: 查询工单列表(无子工单)
     * @Author: wly
     * @Date: 2024/5/31 14:22
     * @Params: [scTicket]
     * @Return: java.util.List<com.bizark.op.api.entity.op.ticket.ScTicket>
     **/
    List<ScTicket> selectScTicketListNoParent(ScTicket scTicket);

    /**
     * 通过母工单查询子工单列表
     *
     * @param parentTicketId
     * @return
     */
    List<ScTicket> selectScTicketByParentId(Long parentTicketId,Integer contextId);

    /**
     * 获取子工单列表
     *
     * @param ticketId
     * @param contextId
     * @return
     */
    List<ScTicket> getChildTicket(Long ticketId, Integer contextId);
//

    /**
     * 根据来源单据查询工单
     *
     * @param sourceId
     * @param sourceDocument
     * @return
     */
    ScTicket selectScTicketBySource(Long sourceId, String sourceDocument);



    /**
     * 根据来源单据查询工单,多条
     *
     * @param sourceId
     * @param sourceDocument
     * @return
     */
    List<ScTicket> selectScTicketBySourceList(Long sourceId, String sourceDocument);


    /**
     * 根据来源单据查询工单,多条
     *
     * @param sourceIdList
     * @param sourceDocument
     * @return
     */
    List<ScTicket> selectScTicketBySourceList(List<Long> sourceIdList, String sourceDocument);


    /**
     * 查询工单信息
     *
     * @param ticketId 工单主键
     * @return
     */
    ScTicket selectScTicketByTicketId(Long ticketId);


    /**
     * 新增工单管理
     *
     * @param scTicket 工单管理
     * @return 结果
     */
    int insertScTicket(ScTicket scTicket);



    /**
     * 修改工单处理人
     *
     * @param scTicket
     * @return
     */
    int updateScTicketHandler(ScTicket scTicket);



    /**
     * 修改工单处理人
     *
     * @param scTicket
     * @return
     */
    void updateScTicketHandlerBy(ConfTicketAssign confTicketAssign, ScTicket scTicket);


    /**
     * 修改评论工单订单信息
     *
     * @param scOrderMatchingTicket
     * @return
     */
    int updateReviewTicketOrder(ScOrderMatchingTicket scOrderMatchingTicket);


    /**
     * 修改工单状态
     *
     * @param scTicket
     * @return
     */
    int updateScTicketStatus(ScTicket scTicket);


    /**
     * 保存附加信息
     *
     * @param scTicket
     * @return
     */
    int saveTicketFilet(ScTicket scTicket, Integer contextId, UserEntity authUserEntity);


    /**
     * 修改来源单据信息
     *
     * @param scTicket
     * @return
     */
    int updateScTicketSource(ScTicket scTicket);

    /**
     * 修改工单操作状态
     *
     * @param scTicket
     * @return
     */
    int updateScTicketOperateStatus(ScTicket scTicket);


    int insertBatch(List<ScTicket> scTickets);

    void insertScTickets(List<? extends SyncEmailInfo> emailInfos, List<ScTicket> scTickets);

    List<ScTicket> selectScTicketBySources(Collection<Long> sourceIds, String source);



    /**
     * 工单优先级设置（并判断是否生成工单 true生成）
     *
     * @param organizationId 组织ID
     * @param scTicket       工单信息
     */
    Boolean scTicketPriorityAndOpenStatusSet(Long organizationId, ScTicket scTicket);


    /**
     * 批量分配工单
     *
     * @param handlerId 处理人
     * @param ticketIds 需要分配的工单管理ID
     * @return 结果
     */
    int batchDistributeScTicketByIds(Long handlerId, Long[] ticketIds);

    /**
     * 确定按钮(将工单状态改为已完成)
     * @param
     * @return
     */
    String updateAStatusndSure(Long[] ticketIds);

    /**
     * 关闭按钮(更改工单状态为已关闭)
     * @param
     * @return
     */
    String updateStatusAndShutdown(Long[] ticketIds);


    /**
     * @description: 根据会话系统ID查询工单
     * @author: Moore
     * @date: 2023/10/18 11:20
     * @param
     * @param convShortId
     * @param ticketSourceDocumentStation
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.ScTicket>
    **/
    List<ScTicket> selectScTicketBySourceAndConvShortList(String convShortId, String ticketSourceDocumentStation);


    /**
     * @description: 更新工单信息
     * @author: Moore
     * @date: 2023/10/23 9:53
     * @param
     * @param ticket
     * @return: void
    **/
    void updateScTickeByTicketId(ScTicket ticket);


    /**
     * @description: 更新工单信息
     * @author: Moore
     * @date: 2023/10/23 9:53
     * @param
     * @param ticket
     * @return: void
     **/
    void updateScTicketBusinessType(ScTicket ticket);


    /**
     * 创建并插入 SC 票证
     *
     * @param channelOrderNo            订单编号
     * @param sourceId                  源标识
     * @param ticketTypeOutsideRefund   机票类型外退票
     * @param ticketSourceRefundRequest 机票来源退款申请
     * @param parentTicketId            家长票证编号
     * @param refundType                退款类型
     * @param orderRefundRequest        订单退款请求
     * @param flow
     * @param orderId                   订单编号
     * @param sourceChannel
     * @param ticketNumber
     * @param createdAt
     * @param businessType
     * @return {@link ScTicket}
     */
    ScTicket createdAndInsertScTicket(String channelOrderNo, Long sourceId, String ticketTypeOutsideRefund, String ticketSourceRefundRequest, Long parentTicketId, String refundType, OrderRefundRequest orderRefundRequest, String flow, String shopId, Long orderId, String sourceChannel, String ticketNumber, Date createdAt, String businessType);


    /**
     * @description: 单表查询工单信息
     * @author: Moore
     * @date: 2023/10/27 10:21
     * @param
     * @param ticketId
     * @return: void
    **/
    ScTicket selectScTicketByBaseTicketId(Long ticketId);

    Integer countList(String orderId, String ticketStatusFinish);

    Integer countByStatusAndOrderId(String orderId, String channel);

    /**
     * 通过父类id获取工单
     *
     * @param orderId            订单编号
     * @param ticketStatusFinish 工单状态完成
     * @param ticketId           票证编号
     * @param ticketType         门票种类
     * @return {@link ScTicket}
     */
    ScTicket selectScTicketByParentId(String orderId, String ticketStatusFinish, Long ticketId, String ticketType);

    /**
     * 获取工单业务状态
     *
     * @param ticketId 票证编号
     * @param orderId  订单编号
     * @return {@link String}
     */
    String getTicketBusinessStatus(Long ticketId, String orderId);

    ScTicket  selectScTicketByTicketSource(Long id, String sourceDocument);

    /**
     * 获取SC票进行返回
     *
     * @param ticketId      票证编号
     * @param refundType    退款类型
     * @param ticketStatus  工单状态
     * @param operateStatus 运行状态
     * @param refundStatus
     * @param channel       渠道
     * @param backReason    返回原因
     * @return {@link ScTicket}
     */
    ScTicket getScTicketForReturn(Long ticketId, String refundType, String ticketStatus, String operateStatus, String refundStatus, String channel, String backReason);

    /**
     * 按订单号删除和反向订单 ID 删除
     *
     * @param orderNo        订单号
     * @param reverseOrderId 反向顺序 ID
     */
    void deleteByOrderNoAndReverseOrderId(String orderNo, String reverseOrderId);

    /**
     * 更新 SC 票证
     *
     * @param ticketId             票证编号
     * @param ticketStatus         工单状态
     * @param operateStatus        运行状态
     * @param businessTicketStatus 商务票状态
     * @return {@link Boolean}
     */
    Boolean updateScTicket(Long ticketId, String ticketStatus, String operateStatus, String businessTicketStatus);

    ScTicket selectScticketByOrderNo(String orderNo, String reverseOrderId);







    /**
     * @param
     * @param reverse_order_id  逆向订单号
     * @description: 根据逆向订单ID获取工单
     * @author: Moore
     * @date: 2023/11/8 19:34
     * @return: com.bizark.op.api.entity.op.ticket.ScTicket
     **/
    ScTicket selectScTicketByReverseOrderId(String reverse_order_id);


    /**
     * @param
     * @param ticketId
     * @description: 设置工单状态为处理中
     * @author: Moore
     * @date: 2023/11/10 9:25
     * @return: void
     **/
    void setTikceStatusProcessing(Long ticketId);


    /**
     * @param
     * @param reverseOrderId 逆向订单号
     * @param ticketType     工单类型
     * @description:
     * @author: Moore
     * @date: 2023/12/4 9:42
     * @return: com.bizark.op.api.entity.op.ticket.ScTicket
     **/
    ScTicket selectScTicketByReverseOrderIdAndTicketType(String reverseOrderId, String ticketType);

    /**
     * 批量更新工单状态（根据组织ID/工单类型）
     *
     * @param scTickets 工单数据
     */
    void updateScTicketStatusList(List<ScTicket> scTickets,Long contextId);
    /**
     * 手动创建工单(其他站外信类型)
     * @param scTicket
     * @return
     */
    ScTicket manualSaveScTicket(ScTicket scTicket);

    ScTicket manualSaveScTicketForExpert(ScTicket scTicket);


    /**
     * 查询手动创建工单对应订单
     * @param orderNo
     * @param orgId
     * @return
     */
    List<ScTicketOrderAndOrderItem> selectManualOrder(String orderNo, Long orgId);

    ApiResponseResult getTicketSourceDocument(Long ticketId);


    List<ScTicket> selectTicketByCustomerIdAndTicketType(Long customerId,String ticketType);


    /**
     * 根据来源id及工单类型查询工单
     *
     * @param sourceId
     * @return
     */
    ScTicket selectScTicketBySourceType(Long sourceId,String ticketType);


    /**
     * 根据订单号和组织id获取goodsSku
     * @param orderId
     * @param orgId
     * @return
     */
    String getGoodsSkuByOrgIdAndOrderId(Long orderId, Integer orgId);

    /**
     * Description: 校验联系客户
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/3/18
     */
    ScTicket checkContactCustomers(Integer contextId, Long ticketId, String ticketType,Long shopId,String buyEmail);

    /**
     * 批量更新工单优先级
     *
     * @param scTicketPriority 工单优先级
     */
    void updateScTicketPriorityBatch(List<ScTicketPriority> scTicketPriority,Long contextId);



    /**
     *
     * @param channelOrderNo 订单编号
     * @param sourceId 源标识
     * @param ticketTypeOutsideRefund 机票类型外退票
     * @param ticketSourceRefundRequest 机票来源退款申请
     * @param parentTicketId 家长票证编号
     * @param refundType 退款类型
     * @param orderRefundRequest 订单退款请求
     * @param flow
     * @param shopId
     * @param orderId 订单编号
     * @param sourceChannel
     * @param ticketNumber
     * @param createdAt
     * @param businessType
     * @Author: wly
     * @return
     */
    ScTicket createdAndInsertScTicketNew(String channelOrderNo, Long sourceId, String ticketTypeOutsideRefund, String ticketSourceRefundRequest, Long parentTicketId, String refundType, OrderRefundRequestInfo orderRefundRequest, String flow, String shopId, Long orderId, String sourceChannel, String ticketNumber, Date createdAt, String businessType);


    void originalExportScTicket(ScTicket query, UserEntity authUserEntity);


    /**
     * Description: temu退货工单自动关闭
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/11/28
     */
    void temuReturnTicketAutoClosed();

    /**
     * Description: temu退款工单自动关闭
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/11/28
     */
    void temuRefundTicketAutoClosed();


    /**
     * @description: 创建temu违规工单
     * @author: Moore
     * @date: 2024/11/29 14:48
     * @param
     * @param violation
     * @param violationDetails
     * @return: void
    **/
    void createTemuAppealTicket(TemuViolation violation, List<TemuViolationDetail> violationDetails);


    /**
     * Description: 工单id
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2025/1/17
     */
    void noReply(Long ticketId);

    void deleteScTicket(Long ticketId);

    String checkUpdateMatchOrder(Long ticketId);

    /**
     * 手动退货工单，明细信息
     *
     * @param orderType
     * @param orderId
     * @param orgId
     */
    List<MarReturnManuaItemDTO> selectReturnManualInfoSkuInfo(Integer orderType, Long orderId, Long orgId);


    /**
     * 校验订单号是否存在对应退货相关信息
     *
     * @param ticketType
     * @return
     */
    JSONObject checkTicketOrder(Long contextId, String  amazonOrderId,Long orderId, String ticketType,Long tikcetId);

    /**
     * 工单增加标签
     * @param tagRelation
     * @param contextId
     * @param opType
     */
    void addTag(TabcutVideoTagRealtionDTO tagRelation, Integer contextId, String opType);

    /**
     *  获取工单备注
     * @param ticketIdList
     * @return
     */
    List<ScTicket> selectRemarkByIds(List<Long> ticketIdList);

    /**
     * 更新工单备注
     * @param id
     * @param remark
     */
    void updateScTicketRemark(Long id, String remark, Long contextId);
}
