package com.bizark.op.api.service.mar.material;

import com.bizark.op.api.dto.material.CreatifyRecordGenerateDTO;
import com.bizark.op.api.dto.material.CreatifyVideoResultDTO;
import com.bizark.op.api.enm.mar.mater.MaterialGenStatusEnum;
import com.bizark.op.api.dto.material.CreatifyGeneratePreviewVideoDTO;
import com.bizark.op.api.entity.op.mar.material.MarMaterialMakeInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.entity.op.mar.vo.CreatifyGenPreviewVideoVO;
import com.bizark.op.api.form.mar.material.MarMaterialQuery;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【mar_material_make_info(tiktok素材制作表)】的数据库操作Service
* @createDate 2025-02-10 17:06:17
*/
public interface MarMaterialMakeInfoService extends IService<MarMaterialMakeInfo> {


    /**
     * 查询素材制作列表
     *
     * @param query
     * @return
     */
    List<MarMaterialMakeInfo> queryMaterialMarkList(MarMaterialQuery query);




    /**
     * 生成预览视频
     * @param dto
     */
    List<CreatifyGenPreviewVideoVO> generatePreviewVideo(CreatifyGeneratePreviewVideoDTO dto);

    void recordGenerateLog(CreatifyRecordGenerateDTO dto);

    void generateVideo(CreatifyGeneratePreviewVideoDTO dto);

    /**
     * 查询制作列表
     * @param id
     * @param taskIdList
     * @return
     */
    List<MarMaterialMakeInfo> findByIdAsc(Long id, List<String> taskIdList);


    /** 素材制作调用导出
     *
     * @param query
     * @param authUserEntity
     */
    void exportmaterialMakeList(MarMaterialQuery query, UserEntity authUserEntity);


    /**
     * 素材制作导出
     *
     * @param toJSONString
     * @return
     */
    String asyncExportMakeList(String toJSONString);


    void updateMakeStatus(Long id, MaterialGenStatusEnum statusEnum, String errorMsg);


    void regenerateCreatifyVideos(List<MarMaterialMakeInfo> makeInfos);

    void videoResult(CreatifyVideoResultDTO dto);

    void updateStatusByApiMaterialId(String apiMaterialId, MaterialGenStatusEnum status, String remark);

    void updateStatusByIds(List<Long> ids, MaterialGenStatusEnum status, String remark);


    MarMaterialMakeInfo selectByGenId(String genId);
    void updateMaterialNameByGenId(String genId,String materialName);

}



