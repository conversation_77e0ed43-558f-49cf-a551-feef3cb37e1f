package com.bizark.op.api.entity.op.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bizark.op.common.core.domain.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单取消明细表
 * @TableName sale_order_cancel_items
 */
@TableName(value ="sale_order_cancel_items")
@Data
public class SaleOrderCancelItems  extends BaseEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 组织ID
     */
    private Long organizationId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 逆向单号
     */
    private String reverseOrderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 取消表主键
     */
    private Long cancelId;

    /**
     * 取消行ID
     */
    private String cancelLineItemId;

    /**
     * 订单行ID
     */
    private String orderLineItemId;

    /**
     * sellerSku
     */
    private String sellerSku;

    /**
     * sku_id
     */
    private String skuId;

    /**
     * sku_name
     */
    private String skuName;


    /**
     * SKU
     */
    private String sku;


    /**
     * 取消数量
     */
    private Integer cancelNumber;

    /**
     * 原始数量
     */
    private Integer orderNumber;


    /**
     * AISN
     */
    private String asin;

    /**
     * 拦截单号
     */
    private String interceptNo;

    /**
     * 0.拦截中 1.拦截成功 2.拦截失败 3.部分拦截 4.拦截异常 exception  5.拦截错误 ERROR  6.暂无订单
     */
    private Byte interceptStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者id
     */
    private Integer createdBy;

    /**
     * 创建者名称
     */
    private String createdName;

    /**
     * 创建时间戳
     */
    private Date createdAt;

    /**
     * 更新者ID
     */
    private Integer updatedBy;

    /**
     * 更新者名称
     */
    private String updatedName;

    /**
     * 更新时间戳
     */
    private Date updatedAt;

    /**
     * 删除者ID
     */
    private Integer disabledBy;

    /**
     * 删除者名称
     */
    private String disabledName;

    /**
     * 删除时间戳
     */
    private Date disabledAt;

    /**
     * 是否同意取消订单  0不同意 1同意
     */
    private Boolean rejectStatus;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 订单明细id
     */
    private Integer saleOrderItemId;

    private String parentSku;

}
