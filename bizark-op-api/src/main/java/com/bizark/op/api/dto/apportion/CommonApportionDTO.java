package com.bizark.op.api.dto.apportion;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @remarks: xxx
 * @Author: kyle
 * @Date: 2024/3/28 9:56
 */

@Data
public class CommonApportionDTO implements Serializable {
    //tiktok使用传参广告Id
    private String adId;

    // spu/asin  tiktok对应得是spu集合
    private String businessName;

    //如tiktok , dsp
    private String code;
    //国家对应的币种
    private String currencyCode;

    private Integer organizationId;
    //花费
    private BigDecimal spend = BigDecimal.ZERO;
    //订单量
    private BigDecimal orderNum = BigDecimal.ZERO;
    //销售额
    private BigDecimal adSales = BigDecimal.ZERO;

    private BigDecimal impression = BigDecimal.ZERO;

    private BigDecimal clicks = BigDecimal.ZERO;

    private String date;

    private Boolean once = Boolean.FALSE;

    private Integer shopId;

    private String flag;

    private String saleChannel;

    //SB分摊使用
    private List<String> asinList;

    //amazon使用这个店铺下asin的集合
    private List<CommonApportionDTO> shopAsinGroupList;

    private Boolean isSd = Boolean.FALSE;


    //tk无spu数据传值
    private String storeId;

    private Boolean isOther = Boolean.FALSE;

    private String orderNo;

}
