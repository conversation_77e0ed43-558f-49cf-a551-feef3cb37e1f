package com.bizark.op.api.entity.op.amazon.fba.DTO;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-29 17:38
 **/
@ApiModel(value = "fba货件列表查询参数")
@Data
public class MarFbaShipmentInfoQueryDTO {

    @ApiModelProperty(value = "组织ID")
    private Integer orgId;

    @ApiModelProperty(value = "国家CODE")
    private List<String> countryCodes;

    @ApiModelProperty(value = "店铺ID")
    private List<Long> shopIds;

    @ApiModelProperty(value = "货件状态 ABANDONED, CANCELLED, CHECKED_IN, CLOSED, DELETED, DELIVERED, IN_TRANSIT, MIXED, READY_TO_SHIP, RECEIVING, SHIPPED, UNCONFIRMED, WORKING")
    private String shipmentState;

    @ApiModelProperty("申收差异 1申报量=签收量，2申报量>签收量、3申报量<签收量")
    private Integer claimDifferences;

    @ApiModelProperty("创建人")
    private Integer createdBy;

    @ApiModelProperty("创建开始时间")
    private Date createdStartDate;

    @ApiModelProperty("创建结束时间")
    private Date createdEndDate;

    @ApiModelProperty("创建开始时间")
    private Date signStartDate;

    @ApiModelProperty("创建结束时间")
    private Date signEndDate;

    @ApiModelProperty("发货开始时间")
    private Date shippingStartDate;

    @ApiModelProperty("发货结束时间")
    private Date shippingEndDate;

    @ApiModelProperty("货件单号")
    private List<String> shipmentIds;

    @ApiModelProperty("货件唯一编号")
    private List<String> shipmentConfirmationId;

    @ApiModelProperty(value = "物流中心编码")
    private List<String> logisticsCenters;

    @ApiModelProperty(value = "referece_id")
    private List<String> refereceIds;

    @ApiModelProperty(value = "货件名称")
    private List<String> shipmentNames;

    @ApiModelProperty(value = "任务名称")
    private List<String> taskNames;

    @ApiModelProperty(value = "sku")
    private List<String> skus;
}
