package com.bizark.op.api.entity.op.amazon.fba.DTO;

import lombok.Data;

import javax.swing.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-21 18:37
 **/
@Data
public class BatchUpdateBoxDTO {

    @NotNull(message = "箱子不能为空")
    @Size(min = 1, message = "箱子不能小于1个")
    private List<FbaSkuBoxDetailDTO> skuBoxList;

    private List<FbaBoxDetailDTO> boxList;

    @NotBlank(message = "入库计划ID不能为空")
    private String inboundPlanId;

    @NotBlank(message = "货件ID不能为空")
    private String shipmentId;

    @NotNull(message = "店铺ID不能为空")
    private Long shopId;

    private Integer contextId;
}
