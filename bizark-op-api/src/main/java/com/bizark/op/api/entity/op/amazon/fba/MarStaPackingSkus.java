package com.bizark.op.api.entity.op.amazon.fba;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bizark.op.common.core.domain.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * STA装箱商品SKU表
 * @TableName mar_sta_packing_skus
 */
@TableName(value ="mar_sta_packing_skus")
@Data
public class MarStaPackingSkus extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 组织ID
     */
    private Long orgId;

    /**
     * 任务ID
     */
    private Long taskId;


    /**
     * 组编号
     */
    private String packingGroupId;

    /**
     * 任务编号
     */
    private String inboundPlanId;


    /**
     * 图片URL
     */
    private String imageUrl;

    /**
     * seller_sku
     */
    private String sellerSku;

    /**
     * FNSKU
     */
    private String fnSku;

    /**
     * ASIN
     */
    private String asin;

    /**
     * ParentASIN
     */
    private String parentAsin;

    /**
     * 标题
     */
    private String title;

    /**
     * 款号
     */
    private String erpSku;

    /**
     * 品名
     */
    private String productName;

    /**
     * 预处理方 AMAZON (亚马逊) NONE (无)  SELLER(卖家)
     */
    private String labelVendor;

    /**
     * 贴标方式 AMAZON(亚马逊贴标) NONE(无标) SELLER(卖家贴标)
     */
    private String labelType;

    /**
     * 包装类型 1-原厂包装
     */
    private Integer packType;

    /**
     * 申报数量(原始)
     */
    private Integer applyNum;

    /**
     * 已装箱数量(箱规下总箱数)
     */
    private Integer boxedQuantity;

    /**
     * 有效期
     */
    private Date expireTime;

    /**
     * 备注
     */
    private String remark;

    @TableField(exist = false)
    private String packageId;


}