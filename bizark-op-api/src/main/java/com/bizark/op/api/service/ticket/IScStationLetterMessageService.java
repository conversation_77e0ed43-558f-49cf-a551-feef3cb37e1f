package com.bizark.op.api.service.ticket;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.entity.op.ticket.ScStationLetter;
import com.bizark.op.api.entity.op.ticket.ScStationLetterMessage;
import com.bizark.op.api.entity.op.ticket.TicketAttaches;

import java.util.List;

public interface IScStationLetterMessageService  extends IService<ScStationLetterMessage> {


    /**
     * 新增站内信消息
     *
     * @param scStationLetterMessage 站内信消息
     * @return 结果
     */
    int insertScStationLetterMessage(ScStationLetterMessage scStationLetterMessage);




    /**
     * 新增TikTOK站内信消息
     *
     * @param scStationLetterMessage 站内信消息
     * @return 结果
     */
    int insertScStationLetterTikTokMessage(ScStationLetterMessage scStationLetterMessage);


    /**
     * 批量插入
     * @param messages
     * @return
     */
    int insertBatchScStationLetterMessage(List<ScStationLetterMessage> messages);

    /**
     * 根据回会话id查询历史消息
     * @param stationLetterId 会话id
     * @return
     */
    List<ScStationLetterMessage> selectScStationLetterMessageList(Long stationLetterId);

    /**
     * 根据回会话id查询历史消息
     * @param scStationLetterMessage 查询参数
     * @return
     */
    List<ScStationLetterMessage> selectScStationLetterMessageList(ScStationLetterMessage  scStationLetterMessage);




    /**
     * 根据信息id来修改消息
     * @param scStationLetterMessage 消息
     * @return
     */
    int updateMessage(ScStationLetterMessage scStationLetterMessage);


    /**
     * @description: 获取回话内容，倒序排序
     * @author: Moore
     * @date: 2023/10/18 17:01
     * @param
     * @param scStationLetterMessage
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.ScStationLetterMessage>
    **/
    List<ScStationLetterMessage> selectConversationListBydesc(ScStationLetterMessage scStationLetterMessage);

    /**
     * @description: 获取回话内容，倒序排序
     * @author: Moore
     * @date: 2023/10/18 17:01
     * @param
     * @param scStationLetterMessage
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.ScStationLetterMessage>
     **/
    List<ScStationLetterMessage> selectConversationListByAsc(ScStationLetterMessage scStationLetterMessage);



    /**
     * @description: 获取指定用户所有会话信息,不包含失败消息
     * @author: Moore
     * @date: 2023/10/19 14:44
     * @param
     * @param scStationLetter
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.ScStationLetterMessage>
    **/
    List<ScStationLetterMessage> selectConversationListByConv(ScStationLetter scStationLetter);

    /**
     * @description: 根据msgId获取会话消息
     * @author: Moore
     * @date: 2023/10/19 14:44
     * @param
     * @param msgId 系统消息ID
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.ScStationLetterMessage>
     **/
    ScStationLetterMessage selectScStationLetterMessageByMsgId(String msgId);

    /**
     * @description: 根据msgID更新信息
     * @author: Moore
     * @date: 2023/10/20 1:05
     * @param
     * @param scStationLetterMessage
     * @return: java.lang.Integer
    **/
    Integer updateMessageByMsgId(ScStationLetterMessage scStationLetterMessage,String msgId);


    /**
     * 查询站内信消息列表
     *
     * @param stationLetterId 站内信ID
     * @return 站内信消息集合
     */
    List<ScStationLetterMessage> selectScStationLetterMessages(Long stationLetterId);

    /**
     * @description: 根据msgId查询会话内容
     * @author: Moore
     * @date: 2023/10/20 1:06
     * @param
     * @param messageId
     * @return: com.bizark.op.api.entity.op.ticket.ScStationLetterMessage
     **/
     ScStationLetterMessage selectScStationLetterMessageByMessageId(Long messageId);


    /**
     * Description: 站内信历史
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/5/21
     */
    List<ScStationLetterMessage> selectScStationLetterMessagesHis(Long stationLetterId);


    /**
     * 存储转换后erp oss 内容
     *
     * @param msgId
     * @param uploadResult
     */
    void updateErpContentById(Long msgId, String uploadResult);
}
