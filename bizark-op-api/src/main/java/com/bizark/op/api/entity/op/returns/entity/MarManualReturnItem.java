package com.bizark.op.api.entity.op.returns.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bizark.op.common.core.domain.BaseEntity;

import java.io.Serializable;
import java.util.Date;

/**
 * 手动退货明细表
 * @TableName mar_manual_return_item
 */
@TableName(value ="mar_manual_return_item")
public class MarManualReturnItem extends BaseEntity implements Serializable   {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 组织ID
     */
    private Long orgId;

    /**
     * 手动退货主键
     */
    private Long manualReturnId;

    /**
     * 退货主键
     */
    private Long returnId;

    /**
     * 款号
     */
    private String erpSku;

    /**
     * seller_sku
     */
    private String sellerSku;

    /**
     * 购买数量
     */
    private Integer saleNum;

    /**
     * 退货数量
     */
    private Integer returnNum;

    /**
     * 图片链接
     */
    private String imageUrl;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者id
     */
    private Integer createdBy;

    /**
     * 创建者名称
     */
    private String createdName;

    /**
     * 创建时间戳
     */
    private Date createdAt;

    /**
     * 更新者ID
     */
    private Integer updatedBy;

    /**
     * 更新者名称
     */
    private String updatedName;

    /**
     * 更新时间戳
     */
    private Date updatedAt;

    /**
     * 删除者ID
     */
    private Integer disabledBy;

    /**
     * 删除者名称
     */
    private String disabledName;

    /**
     * 删除时间戳
     */
    private Date disabledAt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 组织ID
     */
    public Long getOrgId() {
        return orgId;
    }

    /**
     * 组织ID
     */
    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    /**
     * 手动退货主键
     */
    public Long getManualReturnId() {
        return manualReturnId;
    }

    /**
     * 手动退货主键
     */
    public void setManualReturnId(Long manualReturnId) {
        this.manualReturnId = manualReturnId;
    }

    /**
     * 退货主键
     */
    public Long getReturnId() {
        return returnId;
    }

    /**
     * 退货主键
     */
    public void setReturnId(Long returnId) {
        this.returnId = returnId;
    }

    /**
     * 款号
     */
    public String getErpSku() {
        return erpSku;
    }

    /**
     * 款号
     */
    public void setErpSku(String erpSku) {
        this.erpSku = erpSku;
    }

    /**
     * seller_sku
     */
    public String getSellerSku() {
        return sellerSku;
    }

    /**
     * seller_sku
     */
    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    /**
     * 购买数量
     */
    public Integer getSaleNum() {
        return saleNum;
    }

    /**
     * 购买数量
     */
    public void setSaleNum(Integer saleNum) {
        this.saleNum = saleNum;
    }

    /**
     * 退货数量
     */
    public Integer getReturnNum() {
        return returnNum;
    }

    /**
     * 退货数量
     */
    public void setReturnNum(Integer returnNum) {
        this.returnNum = returnNum;
    }

    /**
     * 图片链接
     */
    public String getImageUrl() {
        return imageUrl;
    }

    /**
     * 图片链接
     */
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    /**
     * 产品名称
     */
    public String getProductName() {
        return productName;
    }

    /**
     * 产品名称
     */
    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 创建者id
     */
    public Integer getCreatedBy() {
        return createdBy;
    }

    /**
     * 创建者id
     */
    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    /**
     * 创建者名称
     */
    public String getCreatedName() {
        return createdName;
    }

    /**
     * 创建者名称
     */
    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    /**
     * 创建时间戳
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * 创建时间戳
     */
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * 更新者ID
     */
    public Integer getUpdatedBy() {
        return updatedBy;
    }

    /**
     * 更新者ID
     */
    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    /**
     * 更新者名称
     */
    public String getUpdatedName() {
        return updatedName;
    }

    /**
     * 更新者名称
     */
    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    /**
     * 更新时间戳
     */
    public Date getUpdatedAt() {
        return updatedAt;
    }

    /**
     * 更新时间戳
     */
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * 删除者ID
     */
    public Integer getDisabledBy() {
        return disabledBy;
    }

    /**
     * 删除者ID
     */
    public void setDisabledBy(Integer disabledBy) {
        this.disabledBy = disabledBy;
    }

    /**
     * 删除者名称
     */
    public String getDisabledName() {
        return disabledName;
    }

    /**
     * 删除者名称
     */
    public void setDisabledName(String disabledName) {
        this.disabledName = disabledName;
    }

    /**
     * 删除时间戳
     */
    public Date getDisabledAt() {
        return disabledAt;
    }

    /**
     * 删除时间戳
     */
    public void setDisabledAt(Date disabledAt) {
        this.disabledAt = disabledAt;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MarManualReturnItem other = (MarManualReturnItem) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOrgId() == null ? other.getOrgId() == null : this.getOrgId().equals(other.getOrgId()))
            && (this.getManualReturnId() == null ? other.getManualReturnId() == null : this.getManualReturnId().equals(other.getManualReturnId()))
            && (this.getReturnId() == null ? other.getReturnId() == null : this.getReturnId().equals(other.getReturnId()))
            && (this.getErpSku() == null ? other.getErpSku() == null : this.getErpSku().equals(other.getErpSku()))
            && (this.getSellerSku() == null ? other.getSellerSku() == null : this.getSellerSku().equals(other.getSellerSku()))
            && (this.getSaleNum() == null ? other.getSaleNum() == null : this.getSaleNum().equals(other.getSaleNum()))
            && (this.getReturnNum() == null ? other.getReturnNum() == null : this.getReturnNum().equals(other.getReturnNum()))
            && (this.getImageUrl() == null ? other.getImageUrl() == null : this.getImageUrl().equals(other.getImageUrl()))
            && (this.getProductName() == null ? other.getProductName() == null : this.getProductName().equals(other.getProductName()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedName() == null ? other.getCreatedName() == null : this.getCreatedName().equals(other.getCreatedName()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedName() == null ? other.getUpdatedName() == null : this.getUpdatedName().equals(other.getUpdatedName()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()))
            && (this.getDisabledBy() == null ? other.getDisabledBy() == null : this.getDisabledBy().equals(other.getDisabledBy()))
            && (this.getDisabledName() == null ? other.getDisabledName() == null : this.getDisabledName().equals(other.getDisabledName()))
            && (this.getDisabledAt() == null ? other.getDisabledAt() == null : this.getDisabledAt().equals(other.getDisabledAt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOrgId() == null) ? 0 : getOrgId().hashCode());
        result = prime * result + ((getManualReturnId() == null) ? 0 : getManualReturnId().hashCode());
        result = prime * result + ((getReturnId() == null) ? 0 : getReturnId().hashCode());
        result = prime * result + ((getErpSku() == null) ? 0 : getErpSku().hashCode());
        result = prime * result + ((getSellerSku() == null) ? 0 : getSellerSku().hashCode());
        result = prime * result + ((getSaleNum() == null) ? 0 : getSaleNum().hashCode());
        result = prime * result + ((getReturnNum() == null) ? 0 : getReturnNum().hashCode());
        result = prime * result + ((getImageUrl() == null) ? 0 : getImageUrl().hashCode());
        result = prime * result + ((getProductName() == null) ? 0 : getProductName().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedName() == null) ? 0 : getCreatedName().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedName() == null) ? 0 : getUpdatedName().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        result = prime * result + ((getDisabledBy() == null) ? 0 : getDisabledBy().hashCode());
        result = prime * result + ((getDisabledName() == null) ? 0 : getDisabledName().hashCode());
        result = prime * result + ((getDisabledAt() == null) ? 0 : getDisabledAt().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orgId=").append(orgId);
        sb.append(", manualReturnId=").append(manualReturnId);
        sb.append(", returnId=").append(returnId);
        sb.append(", erpSku=").append(erpSku);
        sb.append(", sellerSku=").append(sellerSku);
        sb.append(", saleNum=").append(saleNum);
        sb.append(", returnNum=").append(returnNum);
        sb.append(", imageUrl=").append(imageUrl);
        sb.append(", productName=").append(productName);
        sb.append(", remark=").append(remark);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdName=").append(createdName);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedName=").append(updatedName);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", disabledBy=").append(disabledBy);
        sb.append(", disabledName=").append(disabledName);
        sb.append(", disabledAt=").append(disabledAt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}