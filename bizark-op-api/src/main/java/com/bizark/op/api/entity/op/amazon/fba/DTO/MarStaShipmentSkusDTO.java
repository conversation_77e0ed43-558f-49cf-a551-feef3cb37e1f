package com.bizark.op.api.entity.op.amazon.fba.DTO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bizark.op.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * STA发货商品Sku表
 *
 * @TableName mar_sta_shipment_skus
 */
@Data
@NoArgsConstructor
public class MarStaShipmentSkusDTO  implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 组织ID
     */
    private Long orgId;

    /**
     * 任务ID
     */
    private Long taskId;


    /**
     * 图片URL
     */
    private String imageUrl;

    /**
     * seller_sku
     */
    private String sellerSku;

    /**
     * FNSKU
     */
    private String fnSku;

    /**
     * ASIN
     */
    private String asin;

    /**
     * ParentASIN
     */
    private String parentAsin;

    /**
     * 标题
     */
    private String title;

    /**
     * 款号
     */
    private String erpSku;

    /**
     * 品名
     */
    private String productName;

    /**
     * 预处理方 AMAZON(亚马逊) NONE(无)  SELLER(卖家)
     */
    private String labelVendor;

    /**
     * 贴标方式 AMAZON(亚马逊贴标) NONE(无标) SELLER(卖家贴标
     */
    private String labelType;

    /**
     * 包装类型 1-原厂包装
     */
    private Integer packType;

    /**
     * 生产商
     */
    private String manufacturer;

    /**
     * 长
     */
    private BigDecimal boxLengthCm;

    /**
     * 宽
     */
    private BigDecimal boxWidthCm;

    /**
     * 高
     */
    private BigDecimal boxHeightCm;

    /**
     * 单箱毛重
     */
    private BigDecimal boxWeightKg;

    /**
     * 箱数
     */
    private Integer boxQuantity;

    /**
     * 每箱数量
     */
    private Integer unitsPerBox;

    /**
     * 申报数量
     */
    private Integer applyNum;

    /**
     * 有效期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expireTime;



}