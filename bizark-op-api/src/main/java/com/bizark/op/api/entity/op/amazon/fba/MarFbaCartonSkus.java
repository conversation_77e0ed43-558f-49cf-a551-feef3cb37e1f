package com.bizark.op.api.entity.op.amazon.fba;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import com.bizark.op.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * fba装箱商品sku表
 * @TableName mar_fba_carton_skus
 */
@TableName(value ="mar_fba_carton_skus")
@Data
public class MarFbaCartonSkus  extends BaseEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 组织ID
     */
    @TableField(value = "org_id")
    private Long orgId;

    /**
     * fba箱规主键
     */
    @TableField(value = "fba_carton_id")
    private Long fbaCartonId;

    /**
     * 箱号
     */
    @TableField(value = "box_id")
    private String boxId;



    /**
     * 图片URL
     */
    @TableField(value = "image_url")
    private String imageUrl;

    /**
     * seller_sku
     */
    @TableField(value = "seller_sku")
    private String sellerSku;

    /**
     * FNSKU
     */
    @TableField(value = "fn_sku")
    private String fnSku;

    /**
     * ASIN
     */
    @TableField(value = "asin")
    private String asin;

    /**
     * ParentASIN
     */
    @TableField(value = "parent_asin")
    private String parentAsin;

    /**
     * 标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 款号
     */
    @TableField(value = "erp_sku")
    private String erpSku;

    /**
     * 品名
     */
    @TableField(value = "product_name")
    private String productName;

    /**
     * 预处理方 AMAZON_ONLY(亚马逊) NONE_ONLY(无)  SELLER_ONLY(卖家)
     */
    @TableField(value = "label_vendor")
    private String labelVendor;

    /**
     * 贴标方式 AMAZON_ONLY(亚马逊贴标) NONE_ONLY(无标) SELLER_ONLY(卖家贴标)
     */
    @TableField(value = "label_type")
    private String labelType;

    /**
     * 申报数量
     */
    @TableField(value = "apply_num")
    private Integer applyNum;


    /**
     * 已装箱
     */
    @TableField(value = "boxed_quantity")
    private Integer boxedQuantity;

    /**
     * 有效期
     */
    @TableField(value = "expire_time")
    private Date expireTime;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;


    /**
     * 货件主键ID
     */
    @TableField(value = "shipment_business_id")
    private Long shipmentBusinessId;




}