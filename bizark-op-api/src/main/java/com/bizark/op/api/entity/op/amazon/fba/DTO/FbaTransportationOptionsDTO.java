package com.bizark.op.api.entity.op.amazon.fba.DTO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR> @ClassName FbaTransportationOptions
 * @description: TODO
 * @date 2025年08月25日
 */
public class FbaTransportationOptionsDTO {
    private static final DateTimeFormatter FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");


    private String inboundPlanId; //STA任务ID
    private String placementOptionId; //操作ID FBA操作ID （该ID可为用户提供生成交通方案的选项）
    private String shipmentConfirmationId; //FBA编号
    private String shipmentId; //货件ID
    private String shippingTime; //发货时间（生成承运商使用）
    private String carrier; //承运人 USE_YOUR_OWN_CARRIER 、AMAZON_PARTNERED_CARRIER
    private String shippingType; //运输类型 GROUND_SMALL_PARCEL(小包裹快递)  FREIGHT_LTL(汽运零担)
    private String shippingMethod; //运输方式  运输方式 1.空运、2.海运、3.陆运
    private String carrierMethod;  //承运方式 （承运名称）


    /**
     * 承运商CODE
     */
    private String carrierCode;

    /**
     * 承运商操作ID（即选择的承运商确认ID）
     */
    private String transportationOptionId;


    /**
     * 托拍信息
     */
    private List<pallet> palletList;


    public static class pallet {

        private BigDecimal length; //长

        private String lengthUnit;  //长度单位

        private BigDecimal width; //宽

        private BigDecimal height; //高

        private BigDecimal weight; //重量

        private String weightUnit;//重量单位

        private Integer palletQuantity; //托盘数

        private BigDecimal totalWeight; //总重量

        private Integer stackable; //是否可堆叠


        public BigDecimal getLength() {
            return length;
        }

        public void setLength(BigDecimal length) {
            this.length = length;
        }

        public String getLengthUnit() {
            return lengthUnit;
        }

        public void setLengthUnit(String lengthUnit) {
            this.lengthUnit = lengthUnit;
        }

        public BigDecimal getWidth() {
            return width;
        }

        public void setWidth(BigDecimal width) {
            this.width = width;
        }

        public BigDecimal getHeight() {
            return height;
        }

        public void setHeight(BigDecimal height) {
            this.height = height;
        }

        public BigDecimal getWeight() {
            return weight;
        }

        public void setWeight(BigDecimal weight) {
            this.weight = weight;
        }

        public String getWeightUnit() {
            return weightUnit;
        }

        public void setWeightUnit(String weightUnit) {
            this.weightUnit = weightUnit;
        }

        public Integer getPalletQuantity() {
            return palletQuantity;
        }

        public void setPalletQuantity(Integer palletQuantity) {
            this.palletQuantity = palletQuantity;
        }

        public BigDecimal getTotalWeight() {
            return totalWeight;
        }

        public void setTotalWeight(BigDecimal totalWeight) {
            this.totalWeight = totalWeight;
        }

        public Integer getStackable() {
            return stackable;
        }

        public void setStackable(Integer stackable) {
            this.stackable = stackable;
        }
    }


    public List<pallet> getPalletList() {
        return palletList;
    }

    public void setPalletList(List<pallet> palletList) {
        this.palletList = palletList;
    }

    public String getTransportationOptionId() {
        return transportationOptionId;
    }

    public void setTransportationOptionId(String transportationOptionId) {
        this.transportationOptionId = transportationOptionId;
    }

    /**
     * 直接使用类中的 shippingDate，转换并赋值到 shippingTime
     */
    public void convertShippingTime() {
        if (shippingTime != null) {
            LocalDate localDate = LocalDate.parse(shippingTime);
            LocalDateTime localDateTime = localDate.atStartOfDay();
            this.shippingTime = localDateTime.atOffset(ZoneOffset.UTC).format(FORMATTER);
        }
    }


    public static DateTimeFormatter getFORMATTER() {
        return FORMATTER;
    }

    public String getCarrierCode() {
        return carrierCode;
    }

    public void setCarrierCode(String carrierCode) {
        this.carrierCode = carrierCode;
    }

    public String getShippingTime() {
        return shippingTime;
    }

    public void setShippingTime(String shippingTime) {
        this.shippingTime = shippingTime;
    }

    public String getInboundPlanId() {
        return inboundPlanId;
    }

    public void setInboundPlanId(String inboundPlanId) {
        this.inboundPlanId = inboundPlanId;
    }

    public String getPlacementOptionId() {
        return placementOptionId;
    }

    public void setPlacementOptionId(String placementOptionId) {
        this.placementOptionId = placementOptionId;
    }

    public String getShipmentConfirmationId() {
        return shipmentConfirmationId;
    }

    public void setShipmentConfirmationId(String shipmentConfirmationId) {
        this.shipmentConfirmationId = shipmentConfirmationId;
    }

    public String getShipmentId() {
        return shipmentId;
    }

    public void setShipmentId(String shipmentId) {
        this.shipmentId = shipmentId;
    }

    public String getCarrier() {
        return carrier;
    }

    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }

    public String getShippingType() {
        return shippingType;
    }

    public void setShippingType(String shippingType) {
        this.shippingType = shippingType;
    }

    public String getShippingMethod() {
        return shippingMethod;
    }

    public void setShippingMethod(String shippingMethod) {
        this.shippingMethod = shippingMethod;
    }

    public String getCarrierMethod() {
        return carrierMethod;
    }

    public void setCarrierMethod(String carrierMethod) {
        this.carrierMethod = carrierMethod;
    }
}
