package com.bizark.op.api.entity.op.walmart;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bizark.op.api.serializer.DateSerializer;
import com.bizark.op.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(value = "walmart_inbound_shipment_item", excludeProperty = "remark")
public class WalmartInboundShipmentItem extends BaseEntity {
    private Long id;
    private Integer orgId;
    private String channelId;
    private String inboundOrderId;
    private String shipmentId;
    private String gtin;
    private String sku;
    private String erpSku;
    private String itemDesc;
    private Integer itemQty;

    /**
     * 箱数
     */
    private Integer vendorPackQty;
    /**
     * 每箱个数
     */
    private Integer innerPackQty;
    private Integer receivedQty;
    private Integer damagedQty;

    @Schema(description = "入库数")
    private Integer inboundQty;
    @Schema(description = "差异数")
    private Integer diffQty;

    private BigDecimal fillRate;
    @JSONField(format = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    @JsonSerialize(using = DateSerializer.class)


    /** 期望送达
     *
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectedDeliveryDate;
    @JSONField(format = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = DateSerializer.class)
    private Date updatedExpectedDeliveryDate;
    private String shipNodeName;
    private String imageUrl;
    private Integer receivedUnitsAtFc;


    @TableLogic(value = "0", delval = "1")
    private boolean isDelete;

    private Integer actualOutboundNumber;//实际出库数量

    /**
     * 申报数量 （弃用）
     */
    private Integer declarationQty;

    /**
     * wfs服务 LABEL、BAG、TAPE、WRAP
     */
    private String wfsService;

    /**
     * 商品ID
     */
    private String goodsId;

    /**
     * 商品类型
     */
    private String goodsType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receivingStartDate;

    public String uniqueKey() {
        return this.orgId + channelId + innerPackQty + shipmentId + sku + gtin;
    }
}
