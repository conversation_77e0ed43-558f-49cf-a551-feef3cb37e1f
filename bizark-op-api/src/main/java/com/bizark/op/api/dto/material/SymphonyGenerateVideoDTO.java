package com.bizark.op.api.dto.material;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class SymphonyGenerateVideoDTO implements Serializable {


    @NotNull(message = "视频名称不能为空")
    @Size(min = 1, max = 5, message = "视频名称数量需在1-5个之内,和视频生成数量保持一致")
    private List<String> videoNames;

    @Schema(description = "生成视频类型，字典获取：symphony_video_type")
    @NotNull(message = "生成视频类型不能为空")
    private String videType;


    @NotNull(message = "生成视频个数必须为1-5的整数")
    @Min(value = 1, message = "生成视频个数必须为1-5的整数")
    @Max(value = 5, message = "生成视频个数必须为1-5的整数")
    private Integer videoCount;

    @Schema(description = "生成所使用的素材编码")
    private List<String> materialCode;


    @NotNull(message = "商品信息语言不能为空")
    @Schema(description = "商品信息语言")
    private String productLanguage;

    @NotBlank(message = "商品名称不能为空")
    @Schema(description = "商品名称")
    private String productName;


    @NotBlank(message = "商品标题不能为空")
    @Schema(description = "商品标题")
    private String productTitle;



    @NotBlank(message = "商品描述不能为空")
    @Schema(description = "商品描述")
    private String productDescription;


    @Size(min = 1, max = 10, message = "商品卖点最少传入一个")
    @NotNull(message = "商品卖点最少传入一个")
    @Schema(description = "商品卖点")
    private List<String> sellingPoint;


    @Schema(description = "品牌")
    private String brand;


    @Schema(description = "价格")
    private BigDecimal price;


    @Schema(description = "图片")
//    @Size(min = 3, max = 30, message = "图片数量需为3-30")
    private List<String> images;


    @Schema(description = "数字人ID")
    private Long avatarId;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "父商品ID")
    private String parentProductId;

    @Schema(description = "组织id")
    private Long contextId;


    String a = "{\n" +
            "  \"code\": 0,\n" +
            "  \"message\": \"OK\",\n" +
            "  \"request_id\": \"202503140202464A68448850888A391107\",\n" +
            "  \"data\": {\n" +
            "    \"list\": [\n" +
            "     {\n" +
            "      \"task_id\":\"cv9osi7og65iutf7dmkg\"\n" +
            "     }\n" +
            "    ]\n" +
            "  }\n" +
            "}";

}
