package com.bizark.op.api.entity.op.tk.expert.dto;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.bizark.op.api.entity.op.ticket.ScTicket;
import com.bizark.op.api.entity.op.tk.expert.TkDataExpertAddress;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class TkDataExpertDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;


    /**
     * 达人来源
     */
    private Integer source;

    /**
     * 状态
     */
    private String state;

    private Integer ownAccount;

    private String sampleOrderNo;


    private String creatorId;

    @Schema(description = "达人cid")
    private String creatorCid;

    @Schema(name = "分组")
    private Integer group;

    /**
     * 达人名称
     */
    @Schema(description = "达人名称")
    @NotBlank(message = "达人名称不能为空")
    private String creator;


    @Schema(description = "昵称")
    private String nickName;


    /**
     * 运营ID
     */
    @Schema(description = "运营ID")
    @NotNull(message = "运营ID不能为空")
    private Long operatorId;

    /**
     * 运营名称
     */
    @Schema(description = "运营")
    private String operator;

    /**
     * 运营ID拼接字符串
     */
    private String operators;


    private String uniqueId;

    /**
     * 平均视频时长
     */
    private Integer avgVideoDuration;


    @Schema(description = "进度",example = "10")
    private Integer progress;


    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "Whatsapp")
    private String wechat;

    @Schema(description = "是否可以修改")
    private Integer allowUpdate;


    @Schema(description = "组织ID")
    private Integer contextId;

    @Schema(description = "粉丝数")
    private Long fansCount;


    @Schema(description = "播放量")
    private Long playCount;
    @Schema(description = "点赞量")
    private Long likeCount;

    @Schema(description = "点赞量")
    private Long ShareCount;
    @Schema(description = "点赞量")
    private Long ReviewCount;
    @Schema(description = "原达人地址")
    private String originCreatorUrl;
    @Schema(description = "达人头像地址")
    private String avatarUrl;


    @Schema(description = "区域")
    private String region;

    @Schema(description = "视频数")
    private Long videoCount;

    @Schema(description = "视频数")
    private Long ownVideoCount;

    @Schema(description = "带货视频数")
    private Long itemVideoCount;

    @Schema(description = "视频GPM")
    private BigDecimal gpm;

    @Schema(description = "直播数")
    private Long liveCount;

    @Schema(description = "GMV")
    private BigDecimal gmv;

    @Schema(description = "佣金")
    private BigDecimal commission;


    @Schema(description = "样品情况")
    private Integer sampleStatus;

    @Schema(description = "是否带货")
    private Integer isBringGoods;


    /**
     * 运营ID
     */
    @TableField(exist = false)
    private Long[] operatorIds;


    private boolean isDelete;


    private String groupDict;

    private String progressDict;

    @TableField(exist = false)
    private String categoryNames;

    @TableField(exist = false)
    private String country;

    @TableField(exist = false)
    private Integer rowIndex;


    @TableField(exist = false)
    private ScTicket ticket;
    /**
     * 地址信息
     */
//    @NotNull(message = "收获地址不能为空")
    private TkDataExpertAddress address;

    /**
     * 进度
     */
    private List<Integer> progressQuery;

    /**
     * 分组
     */
    private List<Integer> groupQuery;


    // 自有商品GMV
    private String ownGmvRange;
    private Integer minOwnGmv;
    private Integer maxOwnGmv;


    // GMV数量筛选
    private String gmvRange;
    private Integer minGmv;
    private Integer maxGmv;


    // 粉丝数量筛选
    private String fansRange;
    private Integer minFans;
    private Integer maxFans;

    // 是否带货 1 是 0 否
    private Integer isSaleGoods;



    private String keyword;


    // 是否匹配运营 0否 1是
    private Integer matchOperator;


    // 仅看自有商品 1 是 0 否
    private Integer ownProductQuery;

    // 样品情况
    private Integer sampleCondition;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 排序字段
     */
    private String sidx;
    /**
     * 排序方式
     */
    private String sord;

    /**
     * 运营
     */
    private String operatorIdQuery;

    /**
     * 样品订单号
     */
    private List<String> sampleOrderNos;


    private String order;

    private String sort_by;


    private Long exportId;

    public TkDataExpertDTO querySetting() {
        if (StrUtil.isNotBlank(beginTime)) {
            beginTime = beginTime.replace("-", "");
        }
        if (StrUtil.isNotBlank(endTime)) {
            endTime = endTime.replace("-", "");
        }
        if (StrUtil.isNotBlank(fansRange)) {
            String[] split = fansRange.split(",");
            minFans = Integer.parseInt(split[0]);
            int max = Integer.parseInt(split[1]);
            maxFans = max < 0 ? Integer.MAX_VALUE : max;
        }
        if (StrUtil.isNotBlank(gmvRange)) {
            String[] split = gmvRange.split(",");
            minGmv = Integer.parseInt(split[0]);
            int max = Integer.parseInt(split[1]);
            maxGmv = max < 0 ? Integer.MAX_VALUE : max;
        }
        if (StrUtil.isNotBlank(ownGmvRange)) {
            String[] split = ownGmvRange.split(",");
            minOwnGmv = Integer.parseInt(split[0]);
            int max = Integer.parseInt(split[1]);
            maxOwnGmv = max < 0 ? Integer.MAX_VALUE : max;
        }
//        if (ArrayUtil.isNotEmpty(getOperatorIds())) {
//            Long[] operatorIds = getOperatorIds();
//            operatorIdQuery = Arrays.stream(operatorIds)
//                    .map(String::valueOf)
//                    .collect(Collectors.joining(","));
//
//        }
        if (StrUtil.isNotBlank(sidx)) {
            sort_by = sidx;

        }
        if (StrUtil.isNotBlank(sord)) {
            order = sord;
        }
        return this;
    }

}
