package com.bizark.op.api.amazon.vendor.fba.model;

import java.util.List;

/**
 * @ClassName InboundOperationStatusResponse
 * @Description 货件操作状态响应
 * <AUTHOR>
 * @Date 2025/8/4 19:41
 */
public class InboundOperationStatusResponse {


    /**
     * operationStatus : FAILED
     * operationId : ba8a7e96-9ab4-421a-b95a-f7fbe4fd43d8
     * operation : createInboundPlan
     * operationProblems : [{"severity":"ERROR","code":"FBA_INB_0004","details":"There's an input error with the resource 'CRGR-XZJ-B2-BKVE'.","message":"ERROR: This product is missing necessary information; dimensions need to be provided in the manufacturer's original packaging."},{"severity":"ERROR","code":"FBA_INB_0005","details":"There's an input error with the resource 'CRGR-XZJ-B2-BKVE'.","message":"ERROR: This product is missing necessary information; weight need to be provided in he manufacturer's original packaging."}]
     */


    //FAILED 失败   SUCCESS 成功  IN_PROGRESS 处理中
    private Long taskId; //TASK任务ID，业务主键
    private String operationStatus;
    private String operationId;
    private String operation;
    private List<OperationProblemsBean> operationProblems;

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getOperationStatus() {
        return operationStatus;
    }

    public void setOperationStatus(String operationStatus) {
        this.operationStatus = operationStatus;
    }

    public String getOperationId() {
        return operationId;
    }

    public void setOperationId(String operationId) {
        this.operationId = operationId;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public List<OperationProblemsBean> getOperationProblems() {
        return operationProblems;
    }

    public void setOperationProblems(List<OperationProblemsBean> operationProblems) {
        this.operationProblems = operationProblems;
    }

    public static class OperationProblemsBean {
        /**
         * severity : ERROR
         * code : FBA_INB_0004
         * details : There's an input error with the resource 'CRGR-XZJ-B2-BKVE'.
         * message : ERROR: This product is missing necessary information; dimensions need to be provided in the manufacturer's original packaging.
         */

        private String severity;
        private String code;
        private String details;
        private String message;

        public String getSeverity() {
            return severity;
        }

        public void setSeverity(String severity) {
            this.severity = severity;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getDetails() {
            return details;
        }

        public void setDetails(String details) {
            this.details = details;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }
}
