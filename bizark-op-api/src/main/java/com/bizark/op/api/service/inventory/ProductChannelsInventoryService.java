package com.bizark.op.api.service.inventory;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.entity.op.amazon.AmazonReportData;
import com.bizark.op.api.entity.op.inventory.ProductChannelsInventory;
import com.bizark.op.api.entity.op.inventory.ProductChannelsStockMessage;
import com.bizark.op.api.entity.op.sale.ProductChannels;

import java.util.List;

public interface ProductChannelsInventoryService extends IService<ProductChannelsInventory> {


    void syncProductChannelsInventory(Integer[] ids);


    void syncProductChannelsInventory(List<ProductChannels> channels);


    void generateAmazonInventory(Integer accountId, List<AmazonReportData> amazonReportData);

    List<ProductChannelsInventory> getInventoryByChannelIds(Integer[] channelIds);

    List<ProductChannelsInventory> getInventory(List<Integer> channelIds);

    void syncInventory(Integer orgId,String type);
    void syncInventory(String type);

    List<ProductChannelsInventory> selectTotalInventoryByChannelIds(List<Integer> channels);


    String selectAddressCodeById(Integer stockAccountId);

    void handleInventoryMessage(List<ProductChannelsStockMessage> stockMessage, String type);
    void handleInventoryMessage(ProductChannelsStockMessage stockMessage, String type);

    void repairInventory();

    void handleTemuInventory(ProductChannels channels);

}
