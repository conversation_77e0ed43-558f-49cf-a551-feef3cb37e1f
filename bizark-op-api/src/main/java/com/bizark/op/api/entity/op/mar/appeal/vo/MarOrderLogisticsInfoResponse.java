package com.bizark.op.api.entity.op.mar.appeal.vo;

import java.util.List;

/**
 * <AUTHOR> @ClassName MarOrderLogisticsInfoResponse
 * @description: 物流轨迹信息
 * @date 2024年10月24日
 */
public class MarOrderLogisticsInfoResponse {

    /**
     * statusCode : 200
     * status : 0
     * code : 200
     * message : 物流记录获取成功
     * data : {"carrierCode":"UPS","trackingCode":"1ZGH12630335064771","hasDelivered":true,"status":"DELIVERED ","trackingDetails":[{"status":"pre_transit","message":"<PERSON><PERSON> created a label, UPS has not received the package yet. ","datetime":"2024-07-11T09:07:44.000+0000","trackingLocation":{"city":null,"state":null,"country":"US","zip":null},"statusDetail":"<PERSON><PERSON> created a label, UPS has not received the package yet. "},{"status":"in_transit","message":"Arrived at Facility","datetime":"2024-07-11T11:01:01.000+0000","trackingLocation":{"city":"Ontario","state":"CA","country":"US","zip":null},"statusDetail":"Arrived at Facility"},{"status":"in_transit","message":"Departed from Facility","datetime":"2024-07-15T02:33:00.000+0000","trackingLocation":{"city":"Portland","state":"OR","country":"US","zip":null},"statusDetail":"Departed from Facility"},{"status":"in_transit","message":"Arrived at Facility","datetime":"2024-07-15T04:38:00.000+0000","trackingLocation":{"city":"Albany","state":"OR","country":"US","zip":null},"statusDetail":"Arrived at Facility"},{"status":"in_transit","message":"Processing at UPS Facility","datetime":"2024-07-15T05:55:10.000+0000","trackingLocation":{"city":"Albany","state":"OR","country":"US","zip":null},"statusDetail":"Processing at UPS Facility"},{"status":"in_transit","message":"Loaded on Delivery Vehicle ","datetime":"2024-07-15T06:59:23.000+0000","trackingLocation":{"city":"Albany","state":"OR","country":"US","zip":null},"statusDetail":"Loaded on Delivery Vehicle "},{"status":"in_transit","message":"Out For Delivery Today","datetime":"2024-07-15T08:11:25.000+0000","trackingLocation":{"city":"Albany","state":"OR","country":"US","zip":null},"statusDetail":"Out For Delivery Today"},{"status":"delivered","message":"DELIVERED ","datetime":"2024-07-15T11:26:01.000+0000","trackingLocation":{"city":"CORVALLIS","state":"OR","country":"US","zip":null},"statusDetail":"DELIVERED "}]}
     */

    private int statusCode;
    private int status;
    private int code;
    private String message;
    private DataBean data;

    public int getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * carrierCode : UPS
         * trackingCode : 1ZGH12630335064771
         * hasDelivered : true
         * status : DELIVERED
         * trackingDetails : [{"status":"pre_transit","message":"Shipper created a label, UPS has not received the package yet. ","datetime":"2024-07-11T09:07:44.000+0000","trackingLocation":{"city":null,"state":null,"country":"US","zip":null},"statusDetail":"Shipper created a label, UPS has not received the package yet. "},{"status":"in_transit","message":"Arrived at Facility","datetime":"2024-07-11T11:01:01.000+0000","trackingLocation":{"city":"Ontario","state":"CA","country":"US","zip":null},"statusDetail":"Arrived at Facility"},{"status":"in_transit","message":"Departed from Facility","datetime":"2024-07-15T02:33:00.000+0000","trackingLocation":{"city":"Portland","state":"OR","country":"US","zip":null},"statusDetail":"Departed from Facility"},{"status":"in_transit","message":"Arrived at Facility","datetime":"2024-07-15T04:38:00.000+0000","trackingLocation":{"city":"Albany","state":"OR","country":"US","zip":null},"statusDetail":"Arrived at Facility"},{"status":"in_transit","message":"Processing at UPS Facility","datetime":"2024-07-15T05:55:10.000+0000","trackingLocation":{"city":"Albany","state":"OR","country":"US","zip":null},"statusDetail":"Processing at UPS Facility"},{"status":"in_transit","message":"Loaded on Delivery Vehicle ","datetime":"2024-07-15T06:59:23.000+0000","trackingLocation":{"city":"Albany","state":"OR","country":"US","zip":null},"statusDetail":"Loaded on Delivery Vehicle "},{"status":"in_transit","message":"Out For Delivery Today","datetime":"2024-07-15T08:11:25.000+0000","trackingLocation":{"city":"Albany","state":"OR","country":"US","zip":null},"statusDetail":"Out For Delivery Today"},{"status":"delivered","message":"DELIVERED ","datetime":"2024-07-15T11:26:01.000+0000","trackingLocation":{"city":"CORVALLIS","state":"OR","country":"US","zip":null},"statusDetail":"DELIVERED "}]
         */

        private String carrierCode;
        private String trackingCode;
        private Boolean hasDelivered;
        private String status;
        private List<TrackingDetailsBean> trackingDetails;

        public String getCarrierCode() {
            return carrierCode;
        }

        public void setCarrierCode(String carrierCode) {
            this.carrierCode = carrierCode;
        }

        public String getTrackingCode() {
            return trackingCode;
        }

        public void setTrackingCode(String trackingCode) {
            this.trackingCode = trackingCode;
        }

        public Boolean isHasDelivered() {
            return hasDelivered;
        }

        public void setHasDelivered(Boolean hasDelivered) {
            this.hasDelivered = hasDelivered;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public List<TrackingDetailsBean> getTrackingDetails() {
            return trackingDetails;
        }

        public void setTrackingDetails(List<TrackingDetailsBean> trackingDetails) {
            this.trackingDetails = trackingDetails;
        }

        public static class TrackingDetailsBean {
            /**
             * status : pre_transit
             * message : Shipper created a label, UPS has not received the package yet.
             * datetime : 2024-07-11T09:07:44.000+0000
             * trackingLocation : {"city":null,"state":null,"country":"US","zip":null}
             * statusDetail : Shipper created a label, UPS has not received the package yet.
             */

            private String status;
            private String message;
            private String datetime;
            private TrackingLocationBean trackingLocation;
            private String statusDetail;

            public String getStatus() {
                return status;
            }

            public void setStatus(String status) {
                this.status = status;
            }

            public String getMessage() {
                return message;
            }

            public void setMessage(String message) {
                this.message = message;
            }

            public String getDatetime() {
                return datetime;
            }

            public void setDatetime(String datetime) {
                this.datetime = datetime;
            }

            public TrackingLocationBean getTrackingLocation() {
                return trackingLocation;
            }

            public void setTrackingLocation(TrackingLocationBean trackingLocation) {
                this.trackingLocation = trackingLocation;
            }

            public String getStatusDetail() {
                return statusDetail;
            }

            public void setStatusDetail(String statusDetail) {
                this.statusDetail = statusDetail;
            }

            public static class TrackingLocationBean {
                /**
                 * city : null
                 * state : null
                 * country : US
                 * zip : null
                 */

                private Object city;
                private Object state;
                private String country;
                private Object zip;

                public Object getCity() {
                    return city;
                }

                public void setCity(Object city) {
                    this.city = city;
                }

                public Object getState() {
                    return state;
                }

                public void setState(Object state) {
                    this.state = state;
                }

                public String getCountry() {
                    return country;
                }

                public void setCountry(String country) {
                    this.country = country;
                }

                public Object getZip() {
                    return zip;
                }

                public void setZip(Object zip) {
                    this.zip = zip;
                }
            }
        }
    }
}
