package com.bizark.op.api.service.tkpromotion;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.entity.op.promotions.AmzPromotionsApproval;
import com.bizark.op.api.entity.op.tkpromotion.*;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;

import java.util.List;

/**
 * tk促销服务接口
 *
 * @Author: Ailill
 * @Date: 2025-07-21 18:34
 */
public interface TkPromotionService extends IService<TkPromotion> {

    /**
     * 查询促销列表
     *
     * @param request
     * @return
     */
    List<TkPromotionListVO> selectTkPromotionList(TkPromotionQueryRequest request);

    /**
     * 导出促销列表
     *
     * @param request
     * @param authUserEntity
     */
    void exportTkPromotionList(TkPromotionQueryRequest request, UserEntity authUserEntity);

    /**
     * 促销详情
     *
     * @param request
     * @return
     */
    TkPromotionDetailVO detail(TkPromotionQueryRequest request);

    /**
     * 创建促销
     *
     * @param request
     * @param authUserEntity
     * @return
     */
    String createPromotion(TkPromotionCreateRequest request, UserEntity authUserEntity);

    /**
     * 更新促销
     *
     * @param request
     * @param authUserEntity
     * @return
     */
    String updatePromotion(TkPromotionUpdateRequest request, UserEntity authUserEntity);

    /**
     * 获取可添加商品
     *
     * @param request
     * @return
     */
    List<TkPromotionAddProductVO> getAddGoods(TkPromotionQueryRequest request);

    /**
     * 取消促销
     *
     * @param id
     * @param authUserEntity
     */
    String cancelPromotion(Long id, UserEntity authUserEntity);

    /**
     * 导出促销列表任务
     *
     * @param request
     * @return
     */
    String exportTkPromotionListAsync(String request);

    void pullPromotionFromApi();

    void pullPromotionDetailFromApi();

    void calculate();


    List<JSONObject> approvalTkPromotion(AmzPromotionsApproval amzPromotionsApproval, Integer organizationId, String meg);

    String processTkPromotion(JSONObject jsonObject);
    String processTkPromotionUpdateAndDisable(List<JSONObject> jsonObjects);



}
