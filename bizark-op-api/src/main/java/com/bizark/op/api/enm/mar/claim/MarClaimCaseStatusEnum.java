package com.bizark.op.api.enm.mar.claim;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MarClaimCaseStatusEnum {
    //系统状态
    PENDING( "pending"),
    SUCCESS( "success"),
    FAIL( "fail"),
    //后台状态
    OPEN("Open"),
    WORK_IN_PROGRESS("Work in Progress"),
    NEEDS_INFO("Need Info"),
    RESOLVED("Resolved"),
    CLOSED("Closed"),

    ;

    private String value;
}
