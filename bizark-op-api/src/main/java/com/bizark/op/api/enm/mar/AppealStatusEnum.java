package com.bizark.op.api.enm.mar;


/**
 * Description: 申诉状态
 * @Author: Fountain
 * @Date: 2024/10/15
 */
public enum AppealStatusEnum {
    PROVEING(0,"获取证明中"),
    TOBESUBMITTED(1,"待提交申诉"),
    SUBMITTING(2,"提交申诉中"),
    INREVIEW(3,"申诉审核中"),
    SUCCESS(4,"申诉成功"),
    FAILED(-1,"申诉失败"),
    CANCLE(-2,"取消申诉");


    private Integer value;
    private String name;
    AppealStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static String getName(Integer value) {
        AppealStatusEnum[] array = AppealStatusEnum.values();
        for (AppealStatusEnum item : array) {
            if (item.value.equals(value)) {
                return item.name;
            }
        }
        return "";
    }
}
