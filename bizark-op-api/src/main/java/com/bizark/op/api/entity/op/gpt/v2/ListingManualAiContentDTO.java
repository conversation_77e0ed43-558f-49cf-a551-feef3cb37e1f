package com.bizark.op.api.entity.op.gpt.v2;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> @ClassName ListingManualDTO
 * @description: TODO
 * @date 2025年04月16日
 */
public class ListingManualAiContentDTO {


    /**
     * data : [{"asin":"B08PBP21RW","asin_title":"GABRYLLY Ergonomic Office Chair, High Back Home Desk Chair with Headrest, Flip-Up Arms, 90-120° Tilt Lock and Wide Cushion, Big and Tall Mesh Chairs for Man Woman, Grey Task Chair","describe5":"[\"【ERGONOMIC OFFICE CHAIR】- The ergonomic chair provides 4 supporting points(head/ back/ hips/ hands) and a proper lumbar support. It's easy to adjust seat height, headrest, backrest and flip-up arms to meet different needs, good for sitting long hours. * Note: The grey desk chair is suitable for people of about 5'5\\\" to 6'2\\\".\", \"【ADJUSTABLE FLIP-UP ARMREST】- Folding the armrests up, you can push the executive office chair directly under the desk to use more area. It's easy to raise or lower the folding armrest by pressing the black buttons on the armrest.\", \"【BREATHABLE MESH SEAT】- The office mesh chair is larger than other chairs, and it could accommodate different body build. The whole Chair Dimensions(including the arms): 25.6\\\"W x 22\\\"D x 45.3\\\"-54.9\\\"H, the Seat Dimensions: 20\\\"W x 19.3\\\"D x 18.5\\\"-22.05\\\"H. Loading Capacity: 400 lbs. The recline function makes you tilt the backrest back (90~120°) or sit straight freely.\", \"【 OFFICE MESH CHAIR】- The mesh back and mesh seat keep air circulation for extra comfy. High quality mesh resists abrasion and transformation, it makes the high back computer desk chair good for sitting for 4 ~ 8 hours. PU mute wheels roll smoothly, no harm on wooden floor; the sturdy five-pointed base and chair frame add durability and stylish appearances.\", \"【5 YEARS WALRANTY & EASY INSTALLATION】- All tools and user instructions are included, quick & easy to assemble(15-20 min). 5 years walranty, get in touch with GABRYLLY TEAM to get satisfactory solutions ASAP.\", \"【SUITABLE FOR USER OF 5'5\\\" ~ 6'2\\\"】 - Note: we recommend this mesh office chair for users between 5'5\\\"and 6'2\\\". If you do need a shorter gas cylinder, please get in touch with GABRYLLY team via walranty card. For any parts or function issues, we will actively deal with for you.\"]","product_discription":"[\" Sitting In Style, Working In Comfort \", \" Find Your Perfect Fit - Ergonomic Design \"]","star_rating":"4.5"}]
     * top_keywords : []
     * extracted_keywords : ["ergonomic","chair","office","arms","mesh","headrest","backrest","desk","chairs","tilt","lock","cushion","task","lumbar support","seat height","adjustable","flip-up","armrest","seat","loading capacity","warranty","installation","pu mute wheels","five-pointed base","chair frame","mesh seat","office chair","style","comfort","home","black","desk chair","mesh office chair","gas cylinder","folding","executive office chair","dimensions","recline","tools","instructions","assembly","team","office chairs","wooden floor","computer chair","back","computer","hours","day","parts","function","mesh chair","mesh back","high quality mesh","sturdy base","executive","seat dimensions","recline function","body build","design","sitting","working"]
     * hot_search_keywords : ["desk chair","desk chair comfy","home office desk chairs","office desk chair","desk chairs","desk chair no wheels","desk chair mat","kids desk chair","computer desk chair","desk chair mat for carpet","white desk chair","pink desk chair","small desk chair","desk chair cushion","desk chair with foot rest","cute desk chair","desk chairs with wheels","armless desk chair","comfy desk chair","criss cross desk chair","standing desk chair","home office desk chair","ergonomic desk chair","desk chair cover","wide desk chair","best desk chair for long hours","tall desk chair","desk chair ergonomic","comfortable desk chair","mat for desk chair carpet","gaming desk chair","desk chair mat for hardwood","modern desk chair","cheap desk chair","high back desk chair","black desk chair","desk chair wheels","leather desk chair","armless desk chair with wheels","executive desk chair"]
     * translated_chatprompt : null
     */

    /** ASIN 基础信息
     *
     */
    private List<DataBean> data;
    /**
     * 竞品提取词
     */
    private List<String> extractedKeywords;

    /**
     * 生成内容
     */
    private List<ListingGenContent> genContent;


    /**
     * 标题
     */
    private List<String> titleSearhTerm = new ArrayList<>();

    /**
     * 要点
     */
    private List<String> pointSearhTerm = new ArrayList<>();

    /**
     * 描述
     */
    private List<String> descSearhTerm = new ArrayList<>();

    /**
     * 品类
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 语言
     */
    private String language;

    /**
     * 长度
     */
    @JsonIgnore
    private Integer length;

    /**
     * 长度
     */
    @JsonIgnore
    private Integer num;

    /**
     * 优化文案中，优化asin
     *
     * @return
     */
    private String optimizeAsin;

    /**
     * 1 生成文案 2优化文案
     *
     * @return
     */
    private Integer type;

    /**
     * 生成文案内容信息
     */
    private String chatprompt; //聊天提示

    private String customerDesc;//客户文案描述


    public List<String> getTitleSearhTerm() {
        return titleSearhTerm;
    }

    public void setTitleSearhTerm(List<String> titleSearhTerm) {
        this.titleSearhTerm = titleSearhTerm;
    }

    public List<String> getPointSearhTerm() {
        return pointSearhTerm;
    }

    public void setPointSearhTerm(List<String> pointSearhTerm) {
        this.pointSearhTerm = pointSearhTerm;
    }

    public List<String> getDescSearhTerm() {
        return descSearhTerm;
    }

    public void setDescSearhTerm(List<String> descSearhTerm) {
        this.descSearhTerm = descSearhTerm;
    }

    public String getChatprompt() {
        return chatprompt;
    }

    public void setChatprompt(String chatprompt) {
        this.chatprompt = chatprompt;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getOptimizeAsin() {
        return optimizeAsin;
    }


    public void setOptimizeAsin(String optimizeAsin) {
        this.optimizeAsin = optimizeAsin;
    }

    public List<ListingGenContent> getGenContent() {
        return genContent;
    }

    public void setGenContent(List<ListingGenContent> genContent) {
        this.genContent = genContent;
    }




    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public List<String> getExtractedKeywords() {
        return extractedKeywords;
    }

    public void setExtractedKeywords(List<String> extractedKeywords) {
        this.extractedKeywords = extractedKeywords;
    }


    public void setLength(Integer length) {
        this.length = length;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public Integer getLength() {
        return length;
    }

    public Integer getNum() {
        return num;
    }

    public String getCustomerDesc() {
        return customerDesc;
    }

    public void setCustomerDesc(String customerDesc) {
        this.customerDesc = customerDesc;
    }

    public static class DataBean {
        /**
         * asin : B08PBP21RW
         * asin_title : GABRYLLY Ergonomic Office Chair, High Back Home Desk Chair with Headrest, Flip-Up Arms, 90-120° Tilt Lock and Wide Cushion, Big and Tall Mesh Chairs for Man Woman, Grey Task Chair
         * describe5 : ["【ERGONOMIC OFFICE CHAIR】- The ergonomic chair provides 4 supporting points(head/ back/ hips/ hands) and a proper lumbar support. It's easy to adjust seat height, headrest, backrest and flip-up arms to meet different needs, good for sitting long hours. * Note: The grey desk chair is suitable for people of about 5'5\" to 6'2\".", "【ADJUSTABLE FLIP-UP ARMREST】- Folding the armrests up, you can push the executive office chair directly under the desk to use more area. It's easy to raise or lower the folding armrest by pressing the black buttons on the armrest.", "【BREATHABLE MESH SEAT】- The office mesh chair is larger than other chairs, and it could accommodate different body build. The whole Chair Dimensions(including the arms): 25.6\"W x 22\"D x 45.3\"-54.9\"H, the Seat Dimensions: 20\"W x 19.3\"D x 18.5\"-22.05\"H. Loading Capacity: 400 lbs. The recline function makes you tilt the backrest back (90~120°) or sit straight freely.", "【 OFFICE MESH CHAIR】- The mesh back and mesh seat keep air circulation for extra comfy. High quality mesh resists abrasion and transformation, it makes the high back computer desk chair good for sitting for 4 ~ 8 hours. PU mute wheels roll smoothly, no harm on wooden floor; the sturdy five-pointed base and chair frame add durability and stylish appearances.", "【5 YEARS WALRANTY & EASY INSTALLATION】- All tools and user instructions are included, quick & easy to assemble(15-20 min). 5 years walranty, get in touch with GABRYLLY TEAM to get satisfactory solutions ASAP.", "【SUITABLE FOR USER OF 5'5\" ~ 6'2\"】 - Note: we recommend this mesh office chair for users between 5'5\"and 6'2\". If you do need a shorter gas cylinder, please get in touch with GABRYLLY team via walranty card. For any parts or function issues, we will actively deal with for you."]
         * product_discription : [" Sitting In Style, Working In Comfort ", " Find Your Perfect Fit - Ergonomic Design "]
         * star_rating : 4.5
         */

        private String asin;
        private String asinTitle;
        private String describe5;
        private String productDiscription;
        private String starRating;

        public String getAsin() {
            return asin;
        }

        public void setAsin(String asin) {
            this.asin = asin;
        }

        public String getAsinTitle() {
            return asinTitle;
        }

        public void setAsinTitle(String asinTitle) {
            this.asinTitle = asinTitle;
        }

        public String getDescribe5() {
            return describe5;
        }

        public void setDescribe5(String describe5) {
            this.describe5 = describe5;
        }

        public String getProductDiscription() {
            return productDiscription;
        }

        public void setProductDiscription(String productDiscription) {
            this.productDiscription = productDiscription;
        }

        public String getStarRating() {
            return starRating;
        }

        public void setStarRating(String starRating) {
            this.starRating = starRating;
        }
    }
}
