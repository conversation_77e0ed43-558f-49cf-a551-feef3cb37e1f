package com.bizark.op.api.entity.op.sale;

import com.bizark.op.api.entity.op.document.ConfTicketProblem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ErpCascaderSelect implements Serializable {

    public ErpCascaderSelect(){

    }

    /**
     * 节点ID
     */
    private Integer id;

    /**
     * 节点名称
     */
    private String label;

    /**
     * 节点值
     */
    private String value;

    private Boolean activeStatus;

    private List<ErpCascaderSelect> children;


    public ErpCascaderSelect(ScGoodsCategory goodsCategory) {
        this.id = goodsCategory.getCategoryId();
        this.value = goodsCategory.getCategoryId().toString();
        this.label = goodsCategory.getCategoryName();
    }

    public ErpCascaderSelect(ScAccessories accessories) {
        this.id = accessories.getId();
        this.value = accessories.getId().toString();
        this.label = accessories.getAccessoriesName();
    }

    public ErpCascaderSelect(ConfTicketProblem ticketProblem) {
        this.id = ticketProblem.getProblemId();
        this.value = ticketProblem.getProblemId().toString();
        this.label = ticketProblem.getProblemName();
        this.activeStatus = ticketProblem.getActiveStatus();
    }

}
