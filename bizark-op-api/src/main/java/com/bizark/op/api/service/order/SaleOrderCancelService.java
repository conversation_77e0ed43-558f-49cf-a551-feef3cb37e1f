package com.bizark.op.api.service.order;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.boss.api.entity.dashboard.deliverorder.TrackingChangeOrderEntity;
import com.bizark.boss.api.entity.dashboard.interceptorder.InterceptOrderEntity;
import com.bizark.boss.api.vo.dashboard.intercept.InterceptRequest;
import com.bizark.common.contract.AuthUserDetails;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.order.SaleOrderCancel;
import com.bizark.op.api.entity.op.order.SaleOrderCancelMessageFromTemu;
import com.bizark.op.api.entity.op.order.vo.SaleOrderCancelInterceptReceive;
import com.bizark.op.api.entity.op.order.vo.SaleOrderCancelMessageReceive;
import com.bizark.op.api.entity.op.order.vo.SaleOrderCancelVo;
import com.bizark.op.api.entity.op.order.vo.SaleOrderCancelWalmartMsgReceive;
import com.bizark.op.api.entity.op.ticket.ScTicket;
import com.bizark.op.api.entity.op.ticket.VO.SaleOrderVoTicket;
import com.bizark.op.api.entity.op.ticket.VO.WayFairOrderCancleVO;
import com.bizark.op.api.entity.op.titok.response.cancel.TiktokCancelLineItems;
import com.bizark.op.api.entity.op.titok.webhook.TikTokData;
import com.bizark.op.api.entity.op.wayfair.response.cancel.ConfirmLineItemCancellationRequest;
import com.bizark.op.api.entity.op.wayfair.response.cancel.LineItemCancellationRequestByPurchaseOrders;
import com.bizark.op.api.entity.op.wayfair.response.cancel.WayCancelMutationsResponse;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;

import java.util.List;
import java.util.Map;


/**
 * @ClassName SaleOrderCancelService
 * @Description 订单取消Service
 * <AUTHOR>
 * @Date 2023/11/1 13:35
 */
public interface SaleOrderCancelService extends IService<SaleOrderCancel> {


    /**
     * @param
     * @param ticketId
     * @description: 手动生成拦单
     * @author: Moore
     * @date: 2023/10/27 10:20
     * @return: void
     **/
    String generateInterceptOrder(Long ticketId);

//    /**
//     * 接收订单取消信息
//     *
//     * @param
//     * @param tiktokData
//     * @description:
//     * @author: Moore
//     * @date: 2023/10/25 10:06
//     * @return:
//     **/
//    void saveOrderCancel(JSONObject tiktokData);


    /**
     * 接收订单取消信息
     *
     * @param
     * @param tiktokData
     * @description:
     * @author: Moore
     * @date: 2023/10/25 10:06
     * @return:
     **/
    void saveOrderCancelV2(JSONObject tiktokData);


    /**
     * @param
     * @param interceptRequest
     * @param authUserDetails
     * @param saleOrderCancel
     * @param scTicket
     * @description: 调用拦截单通用
     * @author: Moore
     * @date: 2024/7/15 18:26
     * @return: com.bizark.boss.api.entity.dashboard.interceptorder.InterceptOrderEntity
     **/
    InterceptOrderEntity requestInterceptOrder(InterceptRequest interceptRequest, AuthUserDetails authUserDetails, SaleOrderCancel saleOrderCancel, ScTicket scTicket);


    /**
     * 接收订单取消信息
     *
     * @param
     * @param
     * @description:
     * @author: Moore
     * @date: 2023/10/25 10:06
     * @return:
     **/
    void saveOrderWayfailCancel();



    /**
     * @description: 保存Ebay订单取消列表
     * @author: Moore
     * @date: 2024/10/9 13:47
     * @param
     * @return: void
    **/
    void saveEbayCancelListJob(Long shopId);


    /**
     * 同意订单取消接口
     *
     * @param
     * @param requestIdList 请求ID
     * @description:
     * @author: Moore
     * @date: 2023/10/25 10:06
     * @return:
     **/
    WayCancelMutationsResponse agreeWayfairCancel(List<String> requestIdList, Integer shopId);


    /**
     * 取消单拒绝
     *
     * @param
     * @param rejectReason 拒绝原因
     * @description: 拒绝取消
     * @author: Moore
     * @date: 2024/6/21 13:37
     * @return: void
     **/
    WayCancelMutationsResponse rejectWayfairCancel(List<String> requestIdList, String rejectReason, Integer shopId);


    /**
     * 获取指定单号是否撤回取消
     *
     * @param
     * @param orderNumbers
     * @description:
     * @author: Moore
     * @date: 2023/10/25 10:06
     * @return:
     **/
    List<LineItemCancellationRequestByPurchaseOrders> getOrderWayfairCancel(List<String> orderNumbers, Integer shopId);


    /**
     * @param
     * @param sourceId
     * @description: 获取工单信息
     * @author: Moore
     * @date: 2023/10/26 14:26
     * @return: com.bizark.op.api.entity.op.order.SaleOrderCancel
     **/
    SaleOrderCancelVo selectSaleOrderCancelById(Long sourceId);


    /**
     * @param
     * @param ticketId
     * @description: tikTok同意取消接口
     * @author: Moore
     * @date: 2023/10/27 18:14
     * @return: void
     **/
    void tiktokOrderCannelConsent(Long ticketId);


    /**
     * @param
     * @param ticketId   工单编号
     * @param cancelType 取消类型  1同意取消 0拒绝取消
     * @description: 通用同意接口
     * @author: Moore
     * @date: 2024/7/12 14:58
     * @return: void
     **/
    String orderCannelCommonConsent(Long ticketId, Integer cancelType);


    /**
     * @param
     * @param ticketId
     * @param saleOrderCancel
     * @description: tikTok拒绝
     * @author: Moore
     * @date: 2023/10/30 0:39
     * @return: void
     **/
    void tiktokOrderCannelConsentReject(Long ticketId, SaleOrderCancel saleOrderCancel);


    /**
     * @param
     * @param ticketId
     * @description: 获取拒绝原因
     * @author: Moore
     * @date: 2023/10/31 0:57
     * @return: void
     **/
    JSONArray getRejectReason(Long ticketId);


    /**
     * @param
     * @param
     * @param trackingChangeOrderEntity
     * @description:
     * @author: Moore
     * @date: 2023/11/1 15:54
     * @return: void
     **/
    void saveTrackingChangeByRpc(Long ticketId, AuthUserDetails authUserDetails, TrackingChangeOrderEntity trackingChangeOrderEntity);


    /**
     * @param
     * @param ticketId
     * @description: 获取物流异动单单号
     * @author: Moore
     * @date: 2023/11/1 22:47
     * @return: java.util.List<com.google.gson.JsonObject>
     **/
    List<JSONObject> getTrackingChangetOrders(Long ticketId);




    /**
     * @param
     * @description: 同步取消信息
     * @author: Moore
     * @date: 2024/4/2 13:34
     * @return: void
     **/
    void syncCancelHis(Long shopId);


    /**
     * @param
     * @param scTicket
     * @param account
     * @param cancelId           取消ID，逆向ID
     * @param saleOrderCancel
     * @param saleOrderVoTickets
     * @description: V2获取取消单数据
     * @author: Moore
     * @date: 2024/7/16 16:58
     * @return: java.util.List<com.bizark.op.api.entity.op.titok.response.cancel.TiktokCancelLineItems>
     **/
    List<TiktokCancelLineItems> getTikTokCancelInfoV2(ScTicket scTicket, Account account, String cancelId, SaleOrderCancel saleOrderCancel, Map<String, SaleOrderVoTicket> saleOrderVoTickets);


    /**
     * @Description:保存来自temu渠道的订单取消信息
     * @Author: wly
     * @Date: 2024/6/17 18:14
     * @Params: [message]
     * @Return: void
     **/
    void saveSaleOrderCancelMessageFromTemu(String message);

    /**
     * @Description: 自动同意或拒绝订单取消
     * @Author: wly
     * @Date: 2024/6/17 18:14
     * @Params: []
     * @Return: void
     **/
    void autoAgreeOrRejectOrderCancel();

    /**
     * @Description:查询符合条件的自动同意或拒绝订单取消
     * @Author: wly
     * @Date: 2024/6/17 18:15
     * @Params: []
     * @Return: java.util.List<com.bizark.op.api.entity.op.order.vo.SaleOrderCancelVo>
     **/
    List<SaleOrderCancelVo> selectAutoAgreeOrRejectOrderCancel();

    /**
     * @Description: temu渠道同意订单取消
     * @Author: wly
     * @Date: 2024/6/17 18:16
     * @Params: [ticketId]
     * @Return: void
     **/
    void temuOrderCancelAgreeAndMesaageSend(Long ticketId);

    /**
     * @Description: temu渠道拒绝订单取消
     * @Author: wly
     * @Date: 2024/6/17 18:16
     * @Params: [ticketId]
     * @Return: void
     **/
    void temuOrderCancelRejectAndMessageSend(Long ticketId);


    /**
     * temu渠道同意或拒绝订单取消消息接收
     *
     * @param msg
     */
    void temuOrderCancelMessageReceive(String msg);

    /**
     * @Description:temu渠道同意订单取消消息接收
     * @Author: wly
     * @Date: 2024/6/18 10:41
     * @Params: [msg]
     * @Return: void
     **/
    void temuOrderCancelAgreeMessageReceive(SaleOrderCancelMessageReceive receive, String nowDate);

    /**
     * @Description:temu渠道拒绝订单取消消息接收
     * @Author: wly
     * @Date: 2024/6/18 10:41
     * @Params: [msg]
     * @Return: void
     **/
    void temuOrderCancelRejectMessageReceive(SaleOrderCancelMessageReceive receive, String nowDate);


    void syncWayfairOrder();


    /**
     * @Description:接收订单取消拦截消息
     * @Author: wly
     * @Date: 2024/7/5 16:48
     * @Params: [receive]
     * @Return: void
     **/
    void saleOrderCancelInterceptMessageReceive(SaleOrderCancelInterceptReceive receive, Boolean reyTryFlag);

    /**
     * @Description:接收撤回订单取消消息
     * @Author: wly
     * @Date: 2024/7/5 16:48
     * @Params: [receive]
     * @Return: void
     **/
    void saleOrderCancelReturnApplyCancelMessageReceive(SaleOrderCancelInterceptReceive receive);

    /**
     * @Description:temu渠道订单取消拦截或撤回消息接收
     * @Author: wly
     * @Date: 2024/7/10 11:14
     * @Params: [saleOrderCancelInterceptReceive]
     * @Return: void
     **/
    void saleOrderCancelInterceptOrRevocationMessageReceive(SaleOrderCancelInterceptReceive saleOrderCancelInterceptReceive, Boolean retryFlag);

    /**
     * @Description:定时监控撤回申请取消
     * @Author: wly
     * @Date: 2024/7/10 11:16
     * @Params: []
     * @Return: void
     **/
    void withdrawApplyCancelMonitor();


    void testOrderCancelMessage(SaleOrderCancelMessageFromTemu temu, SaleOrderCancelInterceptReceive receive, SaleOrderCancelMessageReceive cancelMessageReceive, Integer type);

    /**
     * @param
     * @description: 重试TK明细信息
     * @author: Moore
     * @date: 2024/7/29 17:05
     * @return: void
     **/
    void retrySaleOrderTkItems();


    /**
     * @description:刷新无订单信息行
     * @author: Moore
     * @date: 2024/8/6 18:47
     * @param
     * @return: void
    **/
    void refreshCancelOrderJob();


    /**
     * @description: 手动同步取消单状态TK
     * @author: Moore
     * @date: 2024/8/9 16:59
     * @param
     * @param shopId
     * @return: void
     **/
    void syncCancelStatusManual(Long shopId);

    /**
     * @param
     * @param ticketId      工单ID
     * @param interceptId   拦截ID
     * @param cancelOrderId 取消行主表ID
     * @description: 手动调用撤回接口
     * @author: Moore
     * @date: 2024/8/19 12:45
     * @return: void
     **/
    void manualCommonRevocation(Long ticketId, Integer interceptId, Long cancelOrderId, UserEntity authUserEntity);


    /**
     * walmart渠道订单取消消息接收
     *
     * @param msg
     */
    void walmartOrderCancelMessageReceive(String msg);



    /**
     * @description: 接收walmart_DSV 渠道订单取消
     * @author: Moore
     * @date: 2024/11/21 11:33
     * @param
     * @param messageJson
     * @return: void
     **/
    void walmartDsvOrderCancelMessageReceive(String messageJson);

    /**
     * 测试walmart消息接收
     * @param msgReceive
     */
    void testWalmartOrderCancelMessageReceive(SaleOrderCancelWalmartMsgReceive msgReceive);


    /**
     *  针对距离当前时间超过3天的 取消中 状态的订单取消信息（前提条件）
     *  查询订单表channel_status是已取消的，就把订单取消信息 状态 更新为 已取消，并且关闭工单，记录日志。
     */
    void cancelOrderAndCloseTicket();

    void temuSaleOrderCancelAsync(String message);


    void walmartOrderCancelMessageReceiveByEmail(String msg);
    void walmartOrderCancelMessageReceiveHandle(String msg);


    /**
     * 重新发货
     * @param contextId
     * @param ticketId
     * @param authUserDetails
     */
    void redelivery(Integer contextId, Long ticketId, AuthUserDetails authUserDetails);


    void emailSyncWairOrderCancel(WayFairOrderCancleVO wayFairOrderCancleVO);

    /**
     * 订单取消工单统一设置父子sku
     * @param scTicket
     */
    void handleParentSku(ScTicket scTicket);
}
