package com.bizark.op.api.service.mar;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.entity.op.mar.MarListingQuery;
import com.bizark.op.api.entity.op.mar.MarProductPopularizeLogSellerSku;
import com.bizark.op.api.vo.mar.MarProductPopularizeLogAnalysisItemVo;
import com.bizark.op.api.vo.mar.AdAnalysisVo;
import com.bizark.op.api.vo.mar.MarProductPopularizeLogSellerSkuVo;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @ClassName MarProductPopularizeLogSellerSkuService
 * @Description Listing整合信息sellersku维度
 * <AUTHOR>
 * @Date 2024/2/18 14:47
 */
public interface MarProductPopularizeLogSellerSkuService extends IService<MarProductPopularizeLogSellerSku> {


    /**
     * @description: sellersku列表
     * @author: Moore
     * @date: 2024/2/18 14:48
     * @param
     * @param marListingQuery
     * @return: java.util.List<com.bizark.op.api.entity.op.mar.MarProductPopularizeLogSellerSku>
    **/
    List<MarProductPopularizeLogSellerSkuVo> selectPopularizeLogBySellerSkuList(MarListingQuery marListingQuery);

    /**
     * Description: 综合分析sellersku 维度
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/2/20
     */
    List<MarProductPopularizeLogAnalysisItemVo> analysisSelectPopularizeLogBySellerSkuList(MarListingQuery marListingQuery);


    /**
     * Description: 广告分析sellersku 维度
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/2/20
     */
    List<AdAnalysisVo> adAnalysisSelectPopularizeLogBySellerSkuList(MarListingQuery marListingQuery);


    /**
     * Description: 广告分析 sellersku 维度
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/2/21
     */
    JSONObject adAnalysisSelectPopularizeLogBySellerSkuListLineChart(MarListingQuery marListingQuery);


    /**
     * Description: 任务导出
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/3/1
     */
    void listExport(HttpServletResponse response, MarListingQuery marListingQuery, UserEntity authUserEntity);



    /**
     * Description: 根据条件查询环比数据
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/3/5
     */
    List<MarProductPopularizeLogSellerSkuVo> selectQoQByParam(MarListingQuery marListingQuery);




    /**
     * Description: 外部接口调用
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/7/17
     */
    JSONObject queryPopularizeLogBySellerSku(MarListingQuery marListingQuery);

    /**
     * Description:
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/8/30
     */
    void newlistExportSellerSku(HttpServletResponse response, MarListingQuery marListingQuery, UserEntity authUserEntity);

}
