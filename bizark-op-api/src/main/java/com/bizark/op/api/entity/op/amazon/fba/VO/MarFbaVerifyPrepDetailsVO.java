package com.bizark.op.api.entity.op.amazon.fba.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * @ClassName MarFbaVerifyPrepDetailsVO
 * @Description 装箱信息预处理提供方/标签类型
 * <AUTHOR>
 * @Date 2025/7/31 14:45
 */
@Data
public class MarFbaVerifyPrepDetailsVO {


    /**
     * seller_sku
     */
    private String sellerSku;

    /**
     * 类型  预处理方
     */
    private String labelVendor;

    /**
     * 类型  贴标方式
     */
    private String labelType;


    /**
     * 有效期
     */
    private String expireTime;

}