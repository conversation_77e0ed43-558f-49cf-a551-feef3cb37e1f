package com.bizark.op.api.entity.op.sale.response;

import com.bizark.op.api.entity.op.sale.TemuListingFile;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TemuGoodsImageUploadResponse implements Serializable {

    private Integer errorCode;

    private String errorMsg;

    private String requestId;

    private Result result;

    private Boolean success;

    @Data
    public static class Result {

        private String imageUrl;

        private String url;

        private List<String> urls;

        private Integer width;

        private Integer height;

        private Integer sizeMode;

        private Long size;

    }


    public static TemuGoodsImageUploadResponse fail(String message) {
        TemuGoodsImageUploadResponse response = new TemuGoodsImageUploadResponse();
        response.setSuccess(false);
        response.setErrorMsg(message);
        response.setErrorCode(500);
        return response;
    }

    public static TemuGoodsImageUploadResponse successResponse(TemuListingFile listingFile) {
        TemuGoodsImageUploadResponse response = new TemuGoodsImageUploadResponse();
        response.setSuccess(true);
        Result r = new Result();
        r.setImageUrl(listingFile.getUrl());
        r.setWidth(listingFile.getWidth());
        r.setHeight(listingFile.getHeight());
        r.setSizeMode(0);
        r.setSize(listingFile.getSize());

        response.setResult(r);
        return response;
    }

}
