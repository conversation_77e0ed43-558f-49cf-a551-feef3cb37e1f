package com.bizark.op.api.service.amazon.fba;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bizark.op.api.entity.op.amazon.fba.DTO.*;
import com.bizark.op.api.entity.op.amazon.fba.MarFbaShipmentInfo;
import com.bizark.op.api.entity.op.amazon.fba.VO.MarFbaShipmentInfoVO;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * FBA货件信息服务接口
 */
public interface MarFbaShipmentInfoService extends IService<MarFbaShipmentInfo> {

    /**
     * 分页查询FBA货件信息列表
     * @param queryDTO 查询条件
     * @return FBA货件信息列表
     */
    List<MarFbaShipmentInfoVO> pageList(MarFbaShipmentInfoQueryDTO queryDTO);

    /**
     * 创建导出任务
     * @param dto 查询条件
     * @param authUserEntity 当前用户信息
     */
    void createExportTask(MarFbaShipmentInfoQueryDTO dto, UserEntity authUserEntity);

    /**
     * 异步导出数据
     * @param json 查询参数JSON字符串
     * @return 任务ID
     */
    String asyncExport(String json);

    /**
     * 处理PDF文件
     * @param file 上传的PDF文件
     * @param dto 处理参数
     * @param response HTTP响应对象
     */
    void processPdf(MultipartFile file, ProcessPdfDTO dto, HttpServletResponse response);

    /**
     * 获取装箱详情
     * @param shipmentId 货件ID
     * @return 装箱详情列表
     */
    List<FbaBoxDetailDTO> boxDetail(String shipmentId);

    /**
     * 导出装箱详情
     * @param json 查询参数JSON字符串
     * @return 导出结果
     */
    String exportBoxDetail(String json);

    /**
     * 打印标签
     * @param dto 打印参数
     * @return 打印结果
     */
    String printLabel(FbaPrintLabelDTO dto);

    /**
     * 批量打印
     * @param dto 打印参数
     * @return 打印结果
     */
    String batchPrintLabel(FbaBatchPrintLabelDTO dto);

    /**
     * 批量更新箱子信息
     * @param dto 参数
     * @return 更新结果
     */
    List<FbaBoxDetailDTO> batchUpdateBox(BatchUpdateBoxDTO dto);

    Boolean checkAddBox(CheckAddBoxDTO dto);

    List<FbaSkuDTO> getShipmentSkus(CheckAddBoxDTO dto);

    List<FbaSkuBoxDetailDTO> skuBoxDetail(String shipmentId);
}
