package com.bizark.op.api.entity.op.walmart.dto;

import cn.hutool.core.collection.CollectionUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
public class WalmartInboundShipmentQueryDTO {


    @NotNull(message = "组织ID不能为空")
    private Integer contextId;

    @Schema(description = "sku")
    private List<String> erpSkus;

    @Schema(description = "货件ID")
    private List<String> shipmentIds;

    @Schema(description = "货件创建日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdDate;


    public void validate() {
        if (Objects.isNull(contextId)) {
            throw new IllegalArgumentException("组织ID不能为空");
        }

        // 必须要求有参数参入
        if (CollectionUtil.isEmpty(erpSkus) && CollectionUtil.isEmpty(shipmentIds) && Objects.isNull(createdDate)) {
            throw new IllegalArgumentException("SKU、货件ID、货件创建日期必须传入一个");
        }
    }

}
