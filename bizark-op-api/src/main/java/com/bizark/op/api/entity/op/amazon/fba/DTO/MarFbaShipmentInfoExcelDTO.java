package com.bizark.op.api.entity.op.amazon.fba.DTO;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.bizark.op.api.entity.op.amazon.fba.VO.MarFbaCartonSkusVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * FBA货件列表响应
 *
 * <AUTHOR>
 */
@ApiModel("FBA货件列表响应")
@Data
public class MarFbaShipmentInfoExcelDTO {

    @ExcelProperty(value = "货件单号")
    private String shipmentId;

    @ExcelProperty(value = "货件名称")
    private String shipmentName;

    @ExcelProperty(value = "STA任务")
    private String taskName;

    @ExcelProperty(value = "物流中心编码")
    private String logisticsCenter;

    @ExcelProperty(value = "店铺")
    private String shopName;

    @ExcelProperty(value = "国家")
    private String countryCode;

    @ExcelProperty(value = "Reference ID")
    private String refereceId;

    @ExcelProperty(value = "shipment_state")
    private String shipmentState;

    @ExcelProperty(value = "申报量")
    private Integer applyNum;

    @ExcelProperty(value = "签收量")
    private Integer receivedNum;

    @ExcelProperty(value = "申收差异")
    private Integer difference;

    @ExcelProperty(value = "创建人")
    private String createdName;

    @ExcelProperty(value = "创建时间")
    private String createdAt;

    @ExcelProperty(value = "最后更新时间")
    private String updatedAt;

    @ExcelProperty(value = "备注")
    private String remark;

    @ExcelProperty(value = "运输方式")
    private String shippingMethod;

}