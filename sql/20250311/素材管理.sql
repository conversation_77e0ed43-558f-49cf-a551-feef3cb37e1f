-- 表字段调整
ALTER TABLE `dashboard`.`product_channels_slave`
    ADD COLUMN `category_id` varchar(100) NULL COMMENT '分类ID' AFTER `is_delete`,
ADD COLUMN `model` varchar(100) NULL COMMENT '型号' AFTER `category_id`;

ALTER TABLE `erp`.`mar_material_make_info`
    ADD COLUMN `generate_video_type` varchar(50) NULL COMMENT '生成视频类型' AFTER `gen_status`;

-- 字典数据
INSERT INTO `erp`.`sys_dict_type` (`organization_id`, `dict_name`, `dict_type`, `status`, `created_by`, `created_name`, `created_at`, `updated_by`, `updated_name`, `updated_at`, `disabled_by`, `disabled_name`, `disabled_at`, `remark`) VALUES (1000049, 'Symphony生成视频类型', 'symphony_video_type', '0', 0, '', '0000-00-00 00:00:00', 0, NULL, NULL, 0, NULL, 0, NULL);
INSERT INTO `erp`.`sys_dict_type` (`organization_id`, `dict_name`, `dict_type`, `status`, `created_by`, `created_name`, `created_at`, `updated_by`, `updated_name`, `updated_at`, `disabled_by`, `disabled_name`, `disabled_at`, `remark`) VALUES (1000049, '商品信息语言', 'symphony_product_language', '0', 0, '', '0000-00-00 00:00:00', 0, NULL, NULL, 0, NULL, 0, NULL);
INSERT INTO `erp`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `created_by`, `created_name`, `created_at`, `updated_by`, `updated_name`, `updated_at`, `disabled_by`, `disabled_name`, `disabled_at`, `remark`) VALUES (0, '不使用配音或者数字人混剪', '1', 'symphony_video_type', NULL, NULL, 'N', '0', 0, '', '0000-00-00 00:00:00', 0, NULL, NULL, 0, NULL, 0, NULL);
INSERT INTO `erp`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `created_by`, `created_name`, `created_at`, `updated_by`, `updated_name`, `updated_at`, `disabled_by`, `disabled_name`, `disabled_at`, `remark`) VALUES (1, '使用数字人混剪', '2', 'symphony_video_type', NULL, NULL, 'N', '0', 0, '', '0000-00-00 00:00:00', 0, NULL, NULL, 0, NULL, 0, NULL);
INSERT INTO `erp`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `created_by`, `created_name`, `created_at`, `updated_by`, `updated_name`, `updated_at`, `disabled_by`, `disabled_name`, `disabled_at`, `remark`) VALUES (2, '使用配音混剪', '3', 'symphony_video_type', NULL, NULL, 'N', '0', 0, '', '0000-00-00 00:00:00', 0, NULL, NULL, 0, NULL, 0, NULL);
INSERT INTO `erp`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `created_by`, `created_name`, `created_at`, `updated_by`, `updated_name`, `updated_at`, `disabled_by`, `disabled_name`, `disabled_at`, `remark`) VALUES (0, '英语', '1', 'symphony_product_language', NULL, NULL, 'N', '0', 0, '', '0000-00-00 00:00:00', 0, NULL, NULL, 0, NULL, 0, NULL);
INSERT INTO `erp`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `created_by`, `created_name`, `created_at`, `updated_by`, `updated_name`, `updated_at`, `disabled_by`, `disabled_name`, `disabled_at`, `remark`) VALUES (1, '法语', '2', 'symphony_product_language', NULL, NULL, 'N', '0', 0, '', '0000-00-00 00:00:00', 0, NULL, NULL, 0, NULL, 0, NULL);
INSERT INTO `erp`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `created_by`, `created_name`, `created_at`, `updated_by`, `updated_name`, `updated_at`, `disabled_by`, `disabled_name`, `disabled_at`, `remark`) VALUES (2, '德语', '3', 'symphony_product_language', NULL, NULL, 'N', '0', 0, '', '0000-00-00 00:00:00', 0, NULL, NULL, 0, NULL, 0, NULL);
INSERT INTO `erp`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `created_by`, `created_name`, `created_at`, `updated_by`, `updated_name`, `updated_at`, `disabled_by`, `disabled_name`, `disabled_at`, `remark`) VALUES (3, '印度尼西亚语', '4', 'symphony_product_language', NULL, NULL, 'N', '0', 0, '', '0000-00-00 00:00:00', 0, NULL, NULL, 0, NULL, 0, NULL);
INSERT INTO `erp`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `created_by`, `created_name`, `created_at`, `updated_by`, `updated_name`, `updated_at`, `disabled_by`, `disabled_name`, `disabled_at`, `remark`) VALUES (4, '西班牙语', '5', 'symphony_product_language', NULL, NULL, 'N', '0', 0, '', '0000-00-00 00:00:00', 0, NULL, NULL, 0, NULL, 0, NULL);

INSERT INTO `erp`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `created_by`, `created_name`, `created_at`, `updated_by`, `updated_name`, `updated_at`, `disabled_by`, `disabled_name`, `disabled_at`, `remark`) VALUES (2, 'Symphony', '3', 'material_gen_way', NULL, NULL, 'N', '0', 0, '', '0000-00-00 00:00:00', 0, NULL, NULL, 0, NULL, 0, NULL);
INSERT INTO `erp`.`sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `created_by`, `created_name`, `created_at`, `updated_by`, `updated_name`, `updated_at`, `disabled_by`, `disabled_name`, `disabled_at`, `remark`) VALUES (4, 'Symphony', '5', 'material_source', NULL, NULL, 'N', '0', 0, '', '0000-00-00 00:00:00', 0, NULL, NULL, 0, NULL, 0, NULL);




--  新建表
CREATE TABLE erp.`symphony_digital_avatar` (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                           `org_id` int(11) DEFAULT NULL COMMENT '组织ID',
                                           `avatar_id` varchar(100) DEFAULT NULL COMMENT '后台ID',
                                           `avatar_name` varchar(100) DEFAULT NULL COMMENT '数字人名称',
                                           `avatar_thumbnail` varchar(1000) DEFAULT NULL COMMENT '缩略图',
                                           `avatar_preview_url` varchar(1000) DEFAULT NULL COMMENT '预览链接',
                                           `original_preview_url` varchar(1000) DEFAULT NULL COMMENT '原预览地址',
                                           `original_thumbnail` varchar(1000) DEFAULT NULL COMMENT '原缩略图',
                                           `created_by` int(11) DEFAULT NULL COMMENT '创建人ID',
                                           `created_name` varchar(255) DEFAULT NULL COMMENT '创建人名称',
                                           `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `updated_by` int(11) DEFAULT NULL COMMENT '更新人ID',
                                           `updated_name` varchar(255) DEFAULT NULL COMMENT '更新人名称',
                                           `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                           `disabled_by` int(11) DEFAULT NULL COMMENT '删除人ID',
                                           `disabled_name` varchar(255) DEFAULT NULL COMMENT '删除人名称',
                                           `disabled_at` datetime DEFAULT NULL COMMENT '删除时间',
                                           `is_delete` tinyint(1) DEFAULT '0' COMMENT '逻辑删除 0 未删除 1已删除',
                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1212 DEFAULT CHARSET=utf8mb4;



CREATE TABLE erp.`symphony_video_material` (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                           `material_num` varchar(100) DEFAULT NULL COMMENT '素材编码',
                                           `video_id` varchar(100) DEFAULT NULL COMMENT 'Symphony上传后的视频ID',
                                           `created_by` int(11) DEFAULT NULL COMMENT '创建人ID',
                                           `created_name` varchar(255) DEFAULT NULL COMMENT '创建人名称',
                                           `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `updated_by` int(11) DEFAULT NULL COMMENT '更新人ID',
                                           `updated_name` varchar(255) DEFAULT NULL COMMENT '更新人名称',
                                           `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                           `disabled_by` int(11) DEFAULT NULL COMMENT '删除人ID',
                                           `disabled_name` varchar(255) DEFAULT NULL COMMENT '删除人名称',
                                           `disabled_at` datetime DEFAULT NULL COMMENT '删除时间',
                                           `is_delete` tinyint(1) DEFAULT '0' COMMENT '逻辑删除 0 未删除 1已删除',
                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4;


CREATE TABLE dashboard.`product_channels_description` (
                                                `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                                `channel_id` varchar(100) DEFAULT NULL COMMENT '店铺标志',
                                                `channel_type` varchar(50) DEFAULT NULL COMMENT '渠道类型',
                                                `product_id` varchar(100) DEFAULT NULL COMMENT '商品ID',
                                                `product_description` text COMMENT '描述',
                                                `selling_point` text COMMENT '卖点',
                                                `created_by` int(11) DEFAULT NULL COMMENT '创建人ID',
                                                `created_name` varchar(255) DEFAULT NULL COMMENT '创建人名称',
                                                `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `updated_by` int(11) DEFAULT NULL COMMENT '更新人ID',
                                                `updated_name` varchar(255) DEFAULT NULL COMMENT '更新人名称',
                                                `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                `disabled_by` int(11) DEFAULT NULL COMMENT '删除人ID',
                                                `disabled_name` varchar(255) DEFAULT NULL COMMENT '删除人名称',
                                                `disabled_at` datetime DEFAULT NULL COMMENT '删除时间',
                                                `is_delete` tinyint(1) DEFAULT '0' COMMENT '逻辑删除 0 未删除 1已删除',
                                                PRIMARY KEY (`id`),
                                                UNIQUE KEY `idx_unique` (`product_id`,`channel_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14459 DEFAULT CHARSET=utf8mb4;


CREATE TABLE dashboard.`product_channels_image` (
                                          `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                          `channel_id` varchar(100) DEFAULT NULL COMMENT '店铺标志',
                                          `channel_type` varchar(50) DEFAULT NULL COMMENT '渠道类型',
                                          `product_id` varchar(100) DEFAULT NULL COMMENT '商品ID',
                                          `original_url` varchar(1500) DEFAULT NULL COMMENT '原地址',
                                          `url` varchar(1000) DEFAULT NULL COMMENT '地址',
                                          `type` tinyint(4) DEFAULT NULL COMMENT '1正常图片 2缩略图',
                                          `uri` varchar(255) DEFAULT NULL COMMENT 'tiktok商品标识',
                                          `created_by` int(11) DEFAULT NULL COMMENT '创建人ID',
                                          `created_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '创建人名称',
                                          `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                          `updated_by` int(11) DEFAULT NULL COMMENT '更新人ID',
                                          `updated_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '更新人名称',
                                          `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                          `disabled_by` int(11) DEFAULT NULL COMMENT '删除人ID',
                                          `disabled_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '删除人名称',
                                          `disabled_at` datetime DEFAULT NULL COMMENT '删除时间',
                                          `is_delete` tinyint(1) DEFAULT '0' COMMENT '逻辑删除 0 未删除 1已删除',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=33689 DEFAULT CHARSET=utf8;