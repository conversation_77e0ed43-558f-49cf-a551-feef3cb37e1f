# Bizark-OP 项目详细分析报告

## 项目概述

**项目名称**: bizark-op  
**项目描述**: Java运营模块项目  
**项目版本**: 1.0.4-SNAPSHOT  
**代码仓库**: https://gitee.com/henglin/bizark_op  
**项目URL**: http://example-api.ehenglin.com  

## 项目架构

### 模块结构
项目采用Maven多模块架构，包含以下6个子模块：

```
bizark-op/
├── bizark-op-api/          # API接口定义模块
├── bizark-op-common/       # 公共组件模块
├── bizark-op-generator/    # 代码生成器模块
├── bizark-op-service/      # 服务层模块（RPC服务）
├── bizark-op-task/         # 任务调度模块
└── bizark-op-web/          # Web应用模块（REST API）
```

### 技术栈

#### 核心框架
- **Spring Boot**: 2.2.2.RELEASE
- **Spring Framework**: 集成Spring MVC、Spring AOP、Spring JPA
- **Java版本**: JDK 1.8

#### 数据访问层
- **MyBatis Plus**: 3.3.2 (主要ORM框架)
- **Spring Data JPA**: Hibernate实现
- **MySQL**: 5.1.46 (数据库驱动)
- **Druid**: 阿里巴巴数据库连接池

#### 分布式服务
- **Apache Dubbo**: 分布式RPC框架
- **Zookeeper**: 服务注册与发现

#### 缓存与消息队列
- **Redis**: 缓存存储
- **RabbitMQ**: 消息队列
- **阿里云MNS**: 消息通知服务

#### 搜索引擎
- **Elasticsearch**: 全文搜索
- **阿里云OpenSearch**: 搜索服务

#### 任务调度
- **Elastic Job**: 分布式任务调度
- **XXL-CONF**: 配置中心

#### 其他组件
- **WebSocket**: 实时通信
- **Swagger/Knife4j**: API文档
- **Dozer**: 对象映射
- **EasyExcel**: Excel处理
- **iText**: PDF生成
- **ZXing**: 二维码生成

## 启动入口

### 1. Web应用 (bizark-op-web)
**启动类**: `com.bizark.op.ErpWebApplication`
**启动方式**:
```bash
# Maven启动
mvn spring-boot:run -P dev

# JAR包启动
java -jar bizark-op-web-RUN.jar --spring.profiles.active=dev
```

### 2. RPC服务 (bizark-op-service)
**启动类**: `com.bizark.op.service.ServiceBootstrap`
**启动方式**:
```bash
# Maven启动
mvn exec:exec -P dev

# JAR包启动
java -jar bizark-op-service-RUN.jar
```

### 3. 任务调度 (bizark-op-task)
**启动类**: `com.bizark.op.task.TaskBootstrap`
**启动方式**:
```bash
# Maven启动
mvn exec:exec -P dev

# JAR包启动
java -jar bizark-op-task-RUN.jar

# 脚本启动
./bizark-op-task/bin/start_dev.sh    # 开发环境
./bizark-op-task/bin/start_test.sh   # 测试环境
./bizark-op-task/bin/start_prod.sh   # 生产环境
```

## 环境配置

### 支持的环境
- **local**: 本地开发环境
- **dev**: 开发环境
- **test**: 测试环境
- **stage**: 预发布环境
- **prod**: 生产环境

### 配置文件位置
```
bizark-op-web/src/main/resources/
├── application.yml                           # 主配置文件
├── config/
│   ├── application-local_example.properties # 本地环境配置示例
│   ├── application-dev.properties           # 开发环境配置
│   ├── application-test.properties          # 测试环境配置
│   └── application-stage.properties         # 预发布环境配置
```

## 端口配置

### 各环境端口分配

| 服务类型 | 端口名称 | 开发环境 | 测试环境 | 预发布环境 | 说明 |
|---------|---------|---------|---------|-----------|------|
| Web HTTP | web_service_rest.http.port | 6261 | 6261 | 6261 | HTTP端口 |
| Web HTTPS | web_service_rest.https.port | 6262 | 6262 | 6262 | HTTPS端口 |
| RPC HTTP | dubbo_service_rpc.http.port | 16262 | 16262 | 16262 | Dubbo HTTP端口 |
| RPC Dubbo | dubbo_service_rpc.dubbo.port | 26262 | 26262 | 26262 | Dubbo协议端口 |
| QoS | dubbo.application.qos.port | 46262 | 46262 | 46262 | Dubbo QoS端口 |
| Stop | dubbo_service_rpc.stop.port | 6260 | 6260 | 6260 | 停止端口 |

### Docker端口映射
```yaml
# docker-compose.yml 端口映射
bizark_op_service:
  ports:
    - '8247:8247'    # Service HTTP
    - '18249:18249'  # Service RPC HTTP  
    - '28249:28249'  # Service Dubbo
    - '48249:48249'  # Service QoS

bizark_op_web:
  ports:
    - '8248:8248'    # Web HTTP
    - '8249:8249'    # Web HTTPS
```

## 数据库配置

### 数据源配置

#### 开发环境 (dev)
- **主数据库**: 172.16.12.230:3306
- **用户名**: user_business
- **密码**: Office123888#

#### 测试环境 (test)  
- **主数据库**: ************:3306
- **用户名**: user_business
- **密码**: Office123888#

#### 预发布环境 (stage)
- **主数据库**: ************:3306
- **用户名**: user_business
- **密码**: Office123888#

### 数据库实例

| 数据库名 | 用途 | 说明 |
|---------|------|------|
| erp | 主业务数据库 | ERP核心业务数据 |
| fbb | FBB业务数据库 | FBB相关业务数据 |
| tbadbp | TBADBP数据库 | 淘宝数据处理 |
| multichannel | 多渠道数据库 | 多渠道业务数据 |
| dashboard | 仪表板数据库 | 数据展示相关 |

### 连接池配置
```properties
# Druid连接池配置
jdbc.initialSize=10
jdbc.minIdle=10  
jdbc.maxActive=1000
jdbc.maxWait=60000
jdbc.validationQuery=SELECT 'x'
```

## 中间件配置

### Redis配置
```properties
# 开发环境
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.database=0
spring.redis.timeout=10000
spring.redis.maxActive=8
```

### Elasticsearch配置
```properties
# 开发环境
spring.data.elasticsearch.client.reactive.endpoints=************:9200

# 测试环境  
spring.data.elasticsearch.client.reactive.endpoints=************:9200

# 预发布环境
spring.data.elasticsearch.client.reactive.endpoints=************:9200
```

### Zookeeper配置
```properties
# 开发环境
dubbo.zookeeper.address=************:2181

# 测试环境
dubbo.zookeeper.address=************:2181
```

### RabbitMQ配置
```properties
# 支持原生RabbitMQ和阿里云AMQP
spring.rabbitmq.addresses=${spring.rabbitmq.addresses}
spring.rabbitmq.username=${spring.rabbitmq.username}
spring.rabbitmq.password=${spring.rabbitmq.password}
spring.rabbitmq.virtualhost=${spring.rabbitmq.virtualhost}
```

## Docker部署

### Dockerfile
- **基础镜像**: adoptopenjdk:8-jre-hotspot
- **构建工具**: Maven Wrapper
- **支持模块**: service, web, task

### Docker Compose
```yaml
version: '3.6'
services:
  bizark_op_service:   # RPC服务
  bizark_op_web:       # Web应用  
  bizark_op_task:      # 任务调度
```

### 启动命令
```bash
# 启动所有服务
docker-compose up -d

# 启动特定服务
docker-compose up -d bizark_op_web
```

## API文档

### 端口配置分析
根据配置文件分析，存在多层端口配置：

1. **application.yml**: `server.port: 7799` (默认配置)
2. **环境配置文件**: `server.port=${web_service_rest.https.port}` (6262，会覆盖默认配置)
3. **额外HTTP连接器**: `web_service_rest.http.port=6261` (通过代码配置)

**实际运行端口**：
- **主端口 (HTTPS)**: 6262 - Spring Boot主服务端口
- **辅助端口 (HTTP)**: 6261 - 额外的HTTP连接器端口
- **备用端口**: 7799 - 如果环境配置未生效时的默认端口

### 6261端口访问问题排查

**问题原因分析**：
1. **配置加载顺序**：Spring Boot配置优先级可能导致6261端口未正确绑定
2. **环境变量**：需要确认 `spring.profiles.active` 是否正确设置
3. **SSL配置**：SSL证书配置问题可能影响端口绑定
4. **端口冲突**：6261端口可能被其他进程占用

**建议的访问方式**：

#### 方式1：使用主端口6262 (推荐)
```bash
# 检查应用状态
curl http://localhost:6262/status/check

# 访问API文档
http://localhost:6262/doc.html
http://localhost:6262/swagger-ui.html
```

#### 方式2：使用默认端口7799 (备用)
如果环境配置未生效，应用可能运行在默认端口：
```bash
# 检查应用状态
curl http://localhost:7799/status/check

# 访问API文档
http://localhost:7799/doc.html
http://localhost:7799/swagger-ui.html
```

#### 方式3：检查实际运行端口
```bash
# 查看Java进程占用的端口
netstat -tlnp | grep java
# 或者
ss -tlnp | grep java

# 查看应用启动日志中的端口信息
tail -f bizark-logs/web/application.log | grep -i port
```

### API文档配置详情

#### SpringDoc OpenAPI 3.0
- **分组API文档**: 支持多个业务模块分组
  - Supplier: `/api/v2/sup/supplier/**`
  - Finance: `/api/v2/finance/**`
  - Purchase: `/api/v2/purchase/**`
  - Warehouse: `/api/v2/warehouse/**`
  - 等多个业务模块

#### Knife4j增强文档
- **启用状态**: 已启用
- **Basic认证**:
  - **用户名**: admin
  - **密码**: 123456

### SSL配置 (本地环境)
本地环境启用了SSL，需要配置证书：
- **证书路径**: `${HOME}/Code/certs/ehenglin.com/keystore.jks`
- **证书密码**: 114461452870877
- **证书类型**: JKS

### 权限控制
- **权限等级**: high (严格控制)
- **未配置权限的URL**: 作为受保护资源，需要认证访问
- **Swagger相关路径**: 已配置为匿名访问

## 日志配置

### 日志目录
```
./bizark-logs/          # 应用日志目录
./bizark-logs/web/      # Web应用日志
```

### Docker日志
```yaml
logging:
  driver: json-file
  options:
    max-size: "10M"
    max-file: "10"
```

## 外部服务集成

### 阿里云服务
- **OSS对象存储**: ehenglin-public-hz
- **短信服务**: SMS模板和签名配置
- **MNS消息服务**: 消息队列服务
- **MaxCompute**: 大数据分析平台

### 第三方集成
- **金蝶EAS**: ERP系统集成
- **宁波银行**: 银行接口集成
- **Amazon SP-API**: 亚马逊卖家平台API

## 开发工具

### 代码生成
- **模块**: bizark-op-generator
- **模板引擎**: Velocity
- **支持**: MyBatis代码生成

### 构建工具
- **Maven**: 项目构建和依赖管理
- **Maven Wrapper**: ./mvnw 脚本支持

## 监控与运维

### 健康检查
- **Spring Boot Actuator**: 应用监控
- **Dubbo QoS**: 服务质量监控

### 配置中心
- **XXL-CONF**: 分布式配置管理
- **管理地址**: http://local-conf.ehenglin.com:8008/xxl-conf-admin

## 安全配置

### 时区设置
- **默认时区**: UTC
- **JVM参数**: -Duser.timezone=UTC

### 文件上传
```yaml
spring:
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB
```

## 启动建议

### 启动顺序
1. 启动基础服务 (MySQL, Redis, Zookeeper, Elasticsearch)
2. 启动 bizark-op-service (RPC服务)
3. 启动 bizark-op-task (任务调度)  
4. 启动 bizark-op-web (Web应用)

### 环境变量
```bash
export SPRING_PROFILES_ACTIVE=dev
export JAVA_OPTS="-Xms1024m -Xmx1024m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m"
```

### JVM参数建议
```bash
-Xms1024m -Xmx1024m 
-XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m
-Duser.timezone=UTC
-Dspring.profiles.active=dev
```

---

**文档生成时间**: 2025-01-18  
**项目版本**: 1.0.4-SNAPSHOT  
**维护人员**: 项目开发团队
