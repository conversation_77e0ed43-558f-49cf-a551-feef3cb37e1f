# SQL日志测试说明

## 测试目的
验证修复后的SQL日志配置是否正常工作，确保SQL语句和参数能够正确输出到控制台。

## 测试接口

### 1. 系统日志查询接口
**接口地址**: `GET /api/v2/sys/log/list`
**参数**: 
- `contextId`: 组织ID (例如: 1000049)

**示例请求**:
```
GET http://localhost:7799/api/v2/sys/log/list?contextId=1000049
```

### 2. Temu分类同步接口
**接口地址**: `GET /api/v2/temu/syncCategory`
**参数**: 
- `orgId`: 组织ID (例如: 1000049)

**示例请求**:
```
GET http://localhost:7799/api/v2/temu/syncCategory?orgId=1000049
```

### 3. 调试测试接口
**接口地址**: `GET /dev/tmp/test`
**参数**: 
- `id`: 测试ID (例如: 123)

**示例请求**:
```
GET http://localhost:7799/dev/tmp/test?id=123
```

## 预期的SQL日志输出

正确配置后，调用上述接口时，控制台应该显示类似以下的SQL日志：

```
2025-09-18 16:30:15.123 DEBUG  12345 --- [http-nio-7799-exec-1] c.b.o.s.m.SysOpLogMapper.selectSysOpLogList : ==>  Preparing: SELECT id, organization_id, title, business_type, method, request_method, operator_type, oper_name, dept_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, error_msg, oper_time FROM sys_op_log WHERE organization_id = ? ORDER BY oper_time DESC
2025-09-18 16:30:15.124 DEBUG  12345 --- [http-nio-7799-exec-1] c.b.o.s.m.SysOpLogMapper.selectSysOpLogList : ==> Parameters: 1000049(Integer)
2025-09-18 16:30:15.127 DEBUG  12345 --- [http-nio-7799-exec-1] c.b.o.s.m.SysOpLogMapper.selectSysOpLogList : <==      Total: 25
```

## 测试步骤

### 步骤1: 确认配置
1. 确保应用使用开发环境配置 (`spring.profiles.active=dev`)
2. 确认以下配置文件已正确修改：
   - `bizark-op-web/src/main/resources/config/log/logback_dev.xml`
   - `bizark-op-web/src/main/resources/application.yml`
   - `bizark-op-web/src/main/resources/config/application-dev.properties`

### 步骤2: 重启应用
完全重启应用，确保所有配置生效。

### 步骤3: 调用测试接口
使用Postman、curl或浏览器调用上述测试接口。

### 步骤4: 检查控制台输出
观察控制台是否输出SQL日志，包括：
- SQL语句 (`==>  Preparing:`)
- 参数值 (`==> Parameters:`)
- 结果统计 (`<==      Total:`)

## 故障排除

### 如果没有SQL日志输出

1. **检查MyBatis Plus配置**:
   ```yaml
   mybatis-plus:
     configuration:
       log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
   ```

2. **检查日志级别配置**:
   ```properties
   logging.level.com.bizark.op.service.mapper=DEBUG
   logging.level.com.baomidou.mybatisplus=DEBUG
   ```

3. **检查Logback配置**:
   确认相关logger的`additivity="false"`且包含`STDOUT` appender

4. **检查数据库连接**:
   确认数据库连接正常，接口能够正常返回数据

### 如果出现重复日志

检查Logback配置中的`additivity`属性，确保设置为`false`：
```xml
<logger name="com.baomidou.mybatisplus" level="DEBUG" additivity="false">
    <appender-ref ref="SQL_DEBUG"/>
    <appender-ref ref="STDOUT"/>
</logger>
```

### 如果日志级别过多

可以调整特定logger的级别：
```xml
<!-- 只显示SQL语句，不显示参数详情 -->
<logger name="java.sql.PreparedStatement" level="INFO" additivity="false">
    <appender-ref ref="STDOUT"/>
</logger>
```

## 验证成功标准

✅ **SQL语句正确显示**: 能看到完整的SQL语句
✅ **参数正确显示**: 能看到SQL参数及其类型
✅ **结果统计显示**: 能看到查询结果的行数统计
✅ **无重复输出**: 每条SQL日志只出现一次
✅ **格式清晰**: 日志格式清晰易读

## 常用测试命令

### 使用curl测试
```bash
# 测试系统日志接口
curl "http://localhost:7799/api/v2/sys/log/list?contextId=1000049"

# 测试Temu接口
curl "http://localhost:7799/api/v2/temu/syncCategory?orgId=1000049"

# 测试调试接口
curl "http://localhost:7799/dev/tmp/test?id=123"
```

### 使用浏览器测试
直接在浏览器中访问上述URL即可。

## 注意事项

1. **权限问题**: 某些接口可能需要登录或特定权限
2. **数据库数据**: 确保测试环境有相应的测试数据
3. **网络连接**: 确保能够正常访问应用端口
4. **日志文件**: SQL日志同时会写入到 `bizark-logs/web/debug/sql.log` 文件中
