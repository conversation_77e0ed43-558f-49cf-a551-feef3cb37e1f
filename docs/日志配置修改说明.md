# 日志配置修改说明

## 修改目的
为了在调用接口时能够在控制台输出所有的日志信息，方便开发调试。

## 问题修复
**2025-09-18 更新**:
1. 修复了日志重复输出的问题，将相关logger的`additivity`属性设置为`false`，避免日志在多个appender中重复输出。
2. 修复了SQL日志不输出的问题，添加了完整的MyBatis和JDBC日志配置，并在MyBatis Plus配置中启用了SQL日志输出。

## 修改的文件

### 1. bizark-op-web模块

#### 日志配置文件
**文件**: `bizark-op-web/src/main/resources/config/log/logback_dev.xml`

**主要修改**:
- 控制台输出级别从 `DEBUG` 改为 `TRACE`
- Root Logger级别从 `INFO` 改为 `DEBUG`
- 启用了 `FILE_DEBUG` appender
- 添加了完整的SQL日志配置

#### MyBatis Plus配置文件
**文件**: `bizark-op-web/src/main/resources/application.yml`

**新增配置**:
```yaml
mybatis-plus:
  configuration:
    # 开启SQL日志输出
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
```

#### 环境配置文件
**文件**: `bizark-op-web/src/main/resources/config/application-dev.properties`

**新增配置**:
```properties
# SQL日志配置
logging.level.com.bizark.op.service.mapper=DEBUG
logging.level.com.baomidou.mybatisplus=DEBUG
logging.level.org.mybatis.spring=DEBUG
logging.level.org.apache.ibatis=DEBUG
logging.level.java.sql=DEBUG
```

**具体修改内容**:
```xml
<!-- 控制台输出级别修改 -->
<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
    <level>TRACE</level>  <!-- 原来是DEBUG -->
</filter>

<!-- Root Logger级别修改 -->
<root level="DEBUG">  <!-- 原来是INFO -->
    <appender-ref ref="STDOUT"/>
    <appender-ref ref="FILE_INFO"/>
    <appender-ref ref="FILE_DEBUG"/>  <!-- 新增 -->
    <appender-ref ref="FILE_ERROR"/>
</root>

<!-- 完整的SQL日志配置 -->
<!-- MyBatis Plus SQL日志 -->
<logger name="com.baomidou.mybatisplus" level="DEBUG" additivity="false">
    <appender-ref ref="SQL_DEBUG"/>
    <appender-ref ref="STDOUT"/>
</logger>

<!-- MyBatis Spring日志 -->
<logger name="org.mybatis.spring" level="DEBUG" additivity="false">
    <appender-ref ref="SQL_DEBUG"/>
    <appender-ref ref="STDOUT"/>
</logger>

<!-- 业务Mapper日志 -->
<logger name="com.bizark.op.service.mapper" level="DEBUG" additivity="false">
    <appender-ref ref="SQL_DEBUG"/>
    <appender-ref ref="STDOUT"/>
</logger>

<!-- MyBatis核心日志 -->
<logger name="org.apache.ibatis" level="DEBUG" additivity="false">
    <appender-ref ref="SQL_DEBUG"/>
    <appender-ref ref="STDOUT"/>
</logger>

<!-- JDBC日志 -->
<logger name="java.sql" level="DEBUG" additivity="false">
    <appender-ref ref="SQL_DEBUG"/>
    <appender-ref ref="STDOUT"/>
</logger>

<logger name="java.sql.Connection" level="DEBUG" additivity="false">
    <appender-ref ref="SQL_DEBUG"/>
    <appender-ref ref="STDOUT"/>
</logger>

<logger name="java.sql.Statement" level="DEBUG" additivity="false">
    <appender-ref ref="SQL_DEBUG"/>
    <appender-ref ref="STDOUT"/>
</logger>

<logger name="java.sql.PreparedStatement" level="DEBUG" additivity="false">
    <appender-ref ref="SQL_DEBUG"/>
    <appender-ref ref="STDOUT"/>
</logger>
```

### 2. bizark-op-service模块
**文件**: `bizark-op-service/src/main/resources/config/log/dev_logback.xml`

**主要修改**:
- 控制台输出级别从 `DEBUG` 改为 `TRACE`

### 3. bizark-op-task模块
**文件**: `bizark-op-task/src/main/resources/config/log/dev_logback.xml`

**主要修改**:
- 控制台输出级别从 `DEBUG` 改为 `TRACE`
- Root Logger级别从 `INFO` 改为 `DEBUG`
- 启用了 `FILE_DEBUG` appender

## 日志级别说明

日志级别从高到低：
- `OFF` - 关闭所有日志
- `FATAL` - 致命错误
- `ERROR` - 错误信息
- `WARN` - 警告信息
- `INFO` - 一般信息
- `DEBUG` - 调试信息
- `TRACE` - 跟踪信息
- `ALL` - 所有日志

## additivity属性说明

`additivity`属性控制子logger是否继承父logger的appender：
- `additivity="true"` - 子logger会同时使用自己的appender和父logger的appender（可能导致重复输出）
- `additivity="false"` - 子logger只使用自己配置的appender，不继承父logger的appender

**重要**: 为了避免日志重复输出，特定的logger配置都设置了`additivity="false"`。

## 现在的日志输出内容

修改后，控制台将输出以下类型的日志：
1. **业务日志** - `com.bizark.op` 包下的所有DEBUG级别日志
2. **SQL日志** - MyBatis Plus的SQL执行日志
3. **Spring框架日志** - Spring框架的DEBUG级别日志
4. **Web请求日志** - HTTP请求和响应的详细信息
5. **HTTP客户端日志** - 外部API调用的详细信息
6. **系统日志** - 系统级别的调试信息

## 注意事项

1. **性能影响**: DEBUG和TRACE级别的日志会产生大量输出，可能影响性能，仅建议在开发环境使用
2. **日志文件**: 除了控制台输出，日志也会写入到对应的文件中
3. **环境区分**: 这些修改只影响开发环境(`dev`)，生产环境配置未修改
4. **敏感信息**: 注意DEBUG日志可能包含敏感信息，生产环境请谨慎使用

## 如何使用

1. 确保应用使用的是开发环境配置 (`spring.profiles.active=dev`)
2. 重启应用
3. 调用接口时，控制台将显示详细的日志信息

## 验证修复效果

重启应用后，您应该看到：
- ✅ **SQL日志正常输出**（包括SQL语句和参数）
- ✅ **SQL日志只出现一次**（不再重复）
- ✅ **业务日志正常输出**
- ✅ **控制台日志清晰易读**

### SQL日志输出示例
正确配置后，您应该看到类似这样的SQL日志：
```
2025-09-18 15:55:57.771 DEBUG  72368 --- [task-executor-6] c.b.o.s.m.p.S.selectByOrgIdAndParentSku  : ==>  Preparing: SELECT * FROM products WHERE org_id = ? AND parent_sku IN (?, ?, ?)
2025-09-18 15:55:57.772 DEBUG  72368 --- [task-executor-6] c.b.o.s.m.p.S.selectByOrgIdAndParentSku  : ==> Parameters: 1000049(Integer), C-8100-2P-BK(String), SN-TC3L-GN(String)
2025-09-18 15:55:57.775 DEBUG  72368 --- [task-executor-6] c.b.o.s.m.p.S.selectByOrgIdAndParentSku  : <==      Total: 3
```

### 故障排除
如果SQL日志仍然不输出，请检查：
1. **应用是否完全重启**
2. **是否使用了正确的环境配置** (`spring.profiles.active=dev`)
3. **MyBatis Plus配置是否生效**
4. **数据库连接是否正常**
5. **是否有其他日志配置覆盖了当前配置**

### 调试步骤
1. 检查控制台是否有MyBatis Plus的启动日志
2. 确认`log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl`配置已生效
3. 调用一个简单的查询接口进行测试

## 回滚方法

如果需要回滚到原来的配置，可以：
1. 将控制台输出级别改回 `DEBUG`
2. 将Root Logger级别改回 `INFO`
3. 移除新增的logger配置
4. 注释掉 `FILE_DEBUG` appender引用

## 相关配置文件位置

- Web模块日志配置: `bizark-op-web/src/main/resources/config/log/logback_dev.xml`
- Service模块日志配置: `bizark-op-service/src/main/resources/config/log/dev_logback.xml`
- Task模块日志配置: `bizark-op-task/src/main/resources/config/log/dev_logback.xml`
- 环境配置: `bizark-op-web/src/main/resources/config/application-dev.properties`
