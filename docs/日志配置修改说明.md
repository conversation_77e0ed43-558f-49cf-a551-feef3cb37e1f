# 日志配置修改说明

## 修改目的
为了在调用接口时能够在控制台输出所有的日志信息，方便开发调试。

## 修改的文件

### 1. bizark-op-web模块
**文件**: `bizark-op-web/src/main/resources/config/log/logback_dev.xml`

**主要修改**:
- 控制台输出级别从 `DEBUG` 改为 `TRACE`
- Root Logger级别从 `INFO` 改为 `DEBUG`
- 启用了 `FILE_DEBUG` appender
- 添加了业务包的详细日志配置

**具体修改内容**:
```xml
<!-- 控制台输出级别修改 -->
<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
    <level>TRACE</level>  <!-- 原来是DEBUG -->
</filter>

<!-- Root Logger级别修改 -->
<root level="DEBUG">  <!-- 原来是INFO -->
    <appender-ref ref="STDOUT"/>
    <appender-ref ref="FILE_INFO"/>
    <appender-ref ref="FILE_DEBUG"/>  <!-- 新增 -->
    <appender-ref ref="FILE_ERROR"/>
</root>

<!-- 新增的业务日志配置 -->
<logger name="com.bizark.op" level="DEBUG" additivity="true">
    <appender-ref ref="STDOUT"/>
</logger>

<logger name="org.springframework" level="DEBUG" additivity="true">
    <appender-ref ref="STDOUT"/>
</logger>

<logger name="org.springframework.web" level="DEBUG" additivity="true">
    <appender-ref ref="STDOUT"/>
</logger>

<logger name="org.apache.http" level="DEBUG" additivity="true">
    <appender-ref ref="STDOUT"/>
</logger>
```

### 2. bizark-op-service模块
**文件**: `bizark-op-service/src/main/resources/config/log/dev_logback.xml`

**主要修改**:
- 控制台输出级别从 `DEBUG` 改为 `TRACE`

### 3. bizark-op-task模块
**文件**: `bizark-op-task/src/main/resources/config/log/dev_logback.xml`

**主要修改**:
- 控制台输出级别从 `DEBUG` 改为 `TRACE`
- Root Logger级别从 `INFO` 改为 `DEBUG`
- 启用了 `FILE_DEBUG` appender

## 日志级别说明

日志级别从高到低：
- `OFF` - 关闭所有日志
- `FATAL` - 致命错误
- `ERROR` - 错误信息
- `WARN` - 警告信息
- `INFO` - 一般信息
- `DEBUG` - 调试信息
- `TRACE` - 跟踪信息
- `ALL` - 所有日志

## 现在的日志输出内容

修改后，控制台将输出以下类型的日志：
1. **业务日志** - `com.bizark.op` 包下的所有DEBUG级别日志
2. **SQL日志** - MyBatis Plus的SQL执行日志
3. **Spring框架日志** - Spring框架的DEBUG级别日志
4. **Web请求日志** - HTTP请求和响应的详细信息
5. **HTTP客户端日志** - 外部API调用的详细信息
6. **系统日志** - 系统级别的调试信息

## 注意事项

1. **性能影响**: DEBUG和TRACE级别的日志会产生大量输出，可能影响性能，仅建议在开发环境使用
2. **日志文件**: 除了控制台输出，日志也会写入到对应的文件中
3. **环境区分**: 这些修改只影响开发环境(`dev`)，生产环境配置未修改
4. **敏感信息**: 注意DEBUG日志可能包含敏感信息，生产环境请谨慎使用

## 如何使用

1. 确保应用使用的是开发环境配置 (`spring.profiles.active=dev`)
2. 重启应用
3. 调用接口时，控制台将显示详细的日志信息

## 回滚方法

如果需要回滚到原来的配置，可以：
1. 将控制台输出级别改回 `DEBUG`
2. 将Root Logger级别改回 `INFO`
3. 移除新增的logger配置
4. 注释掉 `FILE_DEBUG` appender引用

## 相关配置文件位置

- Web模块日志配置: `bizark-op-web/src/main/resources/config/log/logback_dev.xml`
- Service模块日志配置: `bizark-op-service/src/main/resources/config/log/dev_logback.xml`
- Task模块日志配置: `bizark-op-task/src/main/resources/config/log/dev_logback.xml`
- 环境配置: `bizark-op-web/src/main/resources/config/application-dev.properties`
