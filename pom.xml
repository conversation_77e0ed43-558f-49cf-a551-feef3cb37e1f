<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.bizark</groupId>
  <artifactId>bizark-op</artifactId>
  <packaging>pom</packaging>
  <version>1.0.4-SNAPSHOT</version>


  <modules>
    <module>bizark-op-api</module>
    <module>bizark-op-common</module>
    <module>bizark-op-task</module>
    <module>bizark-op-service</module>
    <module>bizark-op-web</module>
      <module>bizark-op-generator</module>
  </modules>
  <name>bizark-op</name>
  <url>http://example-api.ehenglin.com</url>


  <scm>
    <url>https://gitee.com/henglin/bizark_op</url>
    <connection>scm:git:https://gitee.com/henglin/bizark_op.git</connection>
    <developerConnection>scm:git:*************:henglin/bizark_op.git</developerConnection>
    <tag>HEAD</tag>
  </scm>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <java.version>1.8</java.version>
    <bizark-mvndm.version>1.1.0-SNAPSHOT</bizark-mvndm.version>

    <!--各个maven插件版本号管理-->
    <spring-boot-starter-parent.version>2.2.2.RELEASE</spring-boot-starter-parent.version>
    <spring-boot-maven-plugin.version>2.2.2.RELEASE</spring-boot-maven-plugin.version>
    <mysql-connector.version>5.1.46</mysql-connector.version>
    <apt-maven-plugin.version>1.1.3</apt-maven-plugin.version>
    <jetty-maven-plugin.version>9.4.11.v20180605</jetty-maven-plugin.version>
    <querydsl.version>4.2.2</querydsl.version>
    <exec-maven-plugin.version>1.6.0</exec-maven-plugin.version>
    <mybatis-util-create-plugin.version>1.0-SNAPSHOT</mybatis-util-create-plugin.version>
    <maven-compiler-plugin.version>3.6.2</maven-compiler-plugin.version>
    <maven-source-plugin.version>3.0.1</maven-source-plugin.version>
    <maven-release-plugin.version>2.5.3</maven-release-plugin.version>
    <maven-resources-plugin.version>3.0.2</maven-resources-plugin.version>
    <maven-javadoc-plugin.version>2.10.4</maven-javadoc-plugin.version>
    <maven-jar-plugin.version>3.0.2</maven-jar-plugin.version>
    <maven-war-plugin.version>3.2.0</maven-war-plugin.version>
    <maven-assembly-plugin.version>3.1.1</maven-assembly-plugin.version>
    <maven-shade-plugin.version>3.2.1</maven-shade-plugin.version>
    <maven-jpa-entity-generator-plugin.version>0.99.3</maven-jpa-entity-generator-plugin.version>
    <maven-surefire-plugin.version>2.21.0</maven-surefire-plugin.version>
    <maven-shade-plugin-log4j2-cachefile-transformer.version>2.8.1</maven-shade-plugin-log4j2-cachefile-transformer.version>
    <maven-os-maven-plugin.version>1.6.2</maven-os-maven-plugin.version>
    <maven-protobuf-maven-plugin.version>0.6.1</maven-protobuf-maven-plugin.version>
    <maven-build-helper-maven-plugin.version>1.12</maven-build-helper-maven-plugin.version>
    <springdoc-openapi-maven-plugin.version>1.1</springdoc-openapi-maven-plugin.version>
    <dozer.version>5.5.1</dozer.version>
    <nbsdk.version>2.1.2</nbsdk.version>
    <itextpdf.version>5.5.13</itextpdf.version>
    <xmlworker.version>5.5.13</xmlworker.version>
    <itext-asian.version>5.2.0</itext-asian.version>
    <flying-saucer-pdf.version>9.0.3</flying-saucer-pdf.version>
    <freemarker.version>2.3.28</freemarker.version>
    <zxing.core>3.4.0</zxing.core>
    <zxing.javase>3.4.0</zxing.javase>

    <!--版本兼容性问题的版本号定义-->
    <tomcat.version>9.0.30</tomcat.version>
    <apache-tomcat-embed.version>9.0.30</apache-tomcat-embed.version>
    <curator-framework.version>2.10.0</curator-framework.version>
    <curator-framework4.version>4.0.1</curator-framework4.version>
    <legacy-servlet.version>2.5</legacy-servlet.version>
    <redisson-ng.version>3.7.2</redisson-ng.version>
    <jdom-v1.version>1.1.3</jdom-v1.version>
    <amqp-client.version>5.7.3</amqp-client.version>
    <grpc.version>1.29.0</grpc.version>
    <protobuf-java-util.version>3.6.1</protobuf-java-util.version>
    <protoc.version>3.11.0</protoc.version>
    <dubbo.compiler.version>0.0.1</dubbo.compiler.version>
    <mybatis-plus.version>3.3.2</mybatis-plus.version>
    <velocity.version>1.7</velocity.version>
    <commons.lang3.version>3.9</commons.lang3.version>


    <service-start-class>com.bizark.op.service.ServiceBootstrap</service-start-class>
    <web-start-class>com.bizark.op.ErpWebApplication</web-start-class>
    <task-start-class>com.bizark.op.task.TaskBootstrap</task-start-class>

  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.bizark</groupId>
        <artifactId>bizark-mvndm</artifactId>
        <version>${bizark-mvndm.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <!--velocity代码生成使用模板 -->
      <dependency>
        <groupId>org.apache.velocity</groupId>
        <artifactId>velocity</artifactId>
        <version>${velocity.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>${commons.lang3.version}</version>
      </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>2.1</version>
        </dependency>
        <dependency>
            <groupId>xalan</groupId>
            <artifactId>xalan</artifactId>
            <version>2.7.2</version>
        </dependency>
        <dependency>
            <groupId>org.htmlparser</groupId>
            <artifactId>htmlparser</artifactId>
            <version>2.1</version>
        </dependency>
    </dependencies>
  </dependencyManagement>


  <build>
    <extensions>
      <extension>
        <groupId>kr.motd.maven</groupId>
        <artifactId>os-maven-plugin</artifactId>
        <version>${maven-os-maven-plugin.version}</version>
      </extension>
    </extensions>
    <resources>
      <resource>
        <filtering>true</filtering>
        <directory>${basedir}/src/main/resources</directory>
        <excludes>
          <exclude>**/*.woff</exclude>
          <exclude>**/*.ttf</exclude>
          <exclude>**/*.eot</exclude>
          <exclude>**/*.svg</exclude>
          <exclude>**/*.ttc</exclude>
        </excludes>
      </resource>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>false</filtering>
        <includes>
          <include>**/*.ttc</include>
        </includes>
      </resource>
      <resource>
        <directory>${basedir}/src/main/java</directory>
        <includes>
          <include>**/*.xml</include>
        </includes>
        <excludes>
          <exclude>**/*.java</exclude>
        </excludes>
        <filtering>true</filtering>
      </resource>
      <resource>
        <directory>${basedir}/src/main/resources</directory>
        <filtering>true</filtering>
      </resource>
    </resources>

    <testResources>
      <testResource>
        <directory>${basedir}/src/test/resources</directory>
      </testResource>
    </testResources>

    <!-- 插件管理 -->
    <pluginManagement>
      <plugins>
        <!-- compiler插件, 设定JDK版本 -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${maven-compiler-plugin.version}</version>
          <configuration>
            <source>${java.version}</source><!-- 源代码使用的开发版本 -->
            <target>${java.version}</target><!-- 需要生成的目标class文件的编译版本 -->
            <!-- 一般而言，target与source是保持一致的，但是，有时候为了让程序能在其他版本的jdk中运行(对于
            低版本目标jdk，源代码中需要没有使用低版本jdk中不支持的语法)，会存在target不同于source的情况 -->
            <encoding>${project.build.sourceEncoding}</encoding>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>${maven-release-plugin.version}</version>
          <configuration>
            <tagNameFormat>v@{project.version}</tagNameFormat>
            <autoVersionSubmodules>true</autoVersionSubmodules>
          </configuration>
        </plugin>
        <!-- resource插件, 设定编码 -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>${maven-resources-plugin.version}</version>
          <configuration>
            <encoding>${project.build.sourceEncoding}</encoding>
            <nonFilteredFileExtensions>
              <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
              <nonFilteredFileExtension>woff</nonFilteredFileExtension>
              <nonFilteredFileExtension>woff2</nonFilteredFileExtension>
            </nonFilteredFileExtensions>
          </configuration>
        </plugin>
        <!-- java doc插件, 设定字符集 -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>${maven-javadoc-plugin.version}</version>
          <configuration>
            <encoding>${project.build.sourceEncoding}</encoding>
            <charset>${project.build.sourceEncoding}</charset>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>${maven-jar-plugin.version}</version>
        </plugin>
        <!-- test插件 -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${maven-surefire-plugin.version}</version>
          <configuration>
            <skip>true</skip>
            <includes>
              <include>**/*Test.java</include>
            </includes>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.xolstice.maven.plugins</groupId>
          <artifactId>protobuf-maven-plugin</artifactId>
          <version>${maven-protobuf-maven-plugin.version}</version>
          <configuration>
            <protocArtifact>com.google.protobuf:protoc:${protoc.version}:exe:${os.detected.classifier}</protocArtifact>
            <pluginId>grpc-java</pluginId>
            <pluginArtifact>io.grpc:protoc-gen-grpc-java:${grpc.version}:exe:${os.detected.classifier}</pluginArtifact>
            <outputDirectory>build/generated/source/proto/main/java</outputDirectory>
            <clearOutputDirectory>false</clearOutputDirectory>
            <protocPlugins>
              <protocPlugin>
                <id>dubbo-grpc</id>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-compiler</artifactId>
                <version>${dubbo.compiler.version}</version>
                <mainClass>org.apache.dubbo.gen.grpc.DubboGrpcGenerator</mainClass>
              </protocPlugin>
            </protocPlugins>
          </configuration>
          <executions>
            <execution>
              <goals>
                <goal>compile</goal>
                <goal>compile-custom</goal>
                <goal>test-compile</goal>
                <goal>test-compile-custom</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <version>${maven-build-helper-maven-plugin.version}</version>
          <executions>
            <execution>
              <phase>generate-sources</phase>
              <goals>
                <goal>add-source</goal>
              </goals>
              <configuration>
                <sources>
                  <source>build/generated/source/proto/main/java</source>
                </sources>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.springdoc</groupId>
          <artifactId>springdoc-openapi-maven-plugin</artifactId>
          <version>${springdoc-openapi-maven-plugin.version}</version>
          <executions>
            <execution>
              <id>integration-test</id>
              <goals>
                <goal>generate</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <apiDocsUrl>https://bi-api.ehenglin.com/apidoc/api-docs</apiDocsUrl>
            <outputFileName>openapi.json</outputFileName>
            <outputDir>${project.build.directory}</outputDir>
            <skip>false</skip>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>


    <plugins>
<!--      <plugin>-->
<!--        <groupId>com.mysema.maven</groupId>-->
<!--        <artifactId>apt-maven-plugin</artifactId>-->
<!--        <version>${apt-maven-plugin.version}</version>-->
<!--        <executions>-->
<!--          <execution>-->
<!--            <goals>-->
<!--              <goal>process</goal>-->
<!--            </goals>-->
<!--            <configuration>-->
<!--              <outputDirectory>target/generated-sources/java</outputDirectory>-->
<!--              <processor>com.querydsl.apt.jpa.JPAAnnotationProcessor</processor>-->
<!--            </configuration>-->
<!--          </execution>-->
<!--        </executions>-->
<!--      </plugin>-->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <configuration>
          <tagNameFormat>v@{project.version}</tagNameFormat>
          <autoVersionSubmodules>true</autoVersionSubmodules>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-resources-plugin</artifactId>
        <configuration>
          <useDefaultDelimiters>true</useDefaultDelimiters>
          <delimiters>
            <delimiter>$[*]</delimiter>
          </delimiters>
          <encoding>${project.build.sourceEncoding}</encoding>
          <nonFilteredFileExtensions>
            <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
            <nonFilteredFileExtension>woff</nonFilteredFileExtension>
            <nonFilteredFileExtension>woff2</nonFilteredFileExtension>
          </nonFilteredFileExtensions>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <skip>true</skip>
          <includes>
            <include>**/*Test.java</include>
          </includes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.xolstice.maven.plugins</groupId>
        <artifactId>protobuf-maven-plugin</artifactId>
        <version>${maven-protobuf-maven-plugin.version}</version>
        <configuration>
          <protocArtifact>com.google.protobuf:protoc:${protoc.version}:exe:${os.detected.classifier}</protocArtifact>
          <pluginId>grpc-java</pluginId>
          <pluginArtifact>io.grpc:protoc-gen-grpc-java:${grpc.version}:exe:${os.detected.classifier}</pluginArtifact>
          <outputDirectory>build/generated/source/proto/main/java</outputDirectory>
          <clearOutputDirectory>false</clearOutputDirectory>
          <protocPlugins>
            <protocPlugin>
              <id>dubbo-grpc</id>
              <groupId>org.apache.dubbo</groupId>
              <artifactId>dubbo-compiler</artifactId>
              <version>${dubbo.compiler.version}</version>
              <mainClass>org.apache.dubbo.gen.grpc.DubboGrpcGenerator</mainClass>
            </protocPlugin>
          </protocPlugins>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>compile</goal>
              <goal>compile-custom</goal>
              <goal>test-compile</goal>
              <goal>test-compile-custom</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <version>${maven-build-helper-maven-plugin.version}</version>
        <executions>
          <execution>
            <phase>generate-sources</phase>
            <goals>
              <goal>add-source</goal>
            </goals>
            <configuration>
              <sources>
                <source>build/generated/source/proto/main/java</source>
              </sources>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>

  </build>

  <profiles>
    <profile>
      <id>hjdev</id>
      <properties>
        <repo.id>hjdev</repo.id>
        <repo.name>maven-dev</repo.name>
        <repo.url>http://maven.ehengjian.com/nexus/repository/maven-dev/</repo.url>
        <releases.id>hjdev</releases.id>
        <releases.name>maven-dev</releases.name>
        <releases.url>http://maven.ehengjian.com/nexus/repository/maven-dev/</releases.url>
        <snap.id>hjdev</snap.id>
        <snap.name>maven-dev</snap.name>
        <snap.url>http://maven.ehengjian.com/nexus/repository/maven-dev/</snap.url>
      </properties>
    </profile>
    <profile>
      <id>hjtest</id>
      <properties>
        <repo.id>hjtest</repo.id>
        <repo.name>maven-test</repo.name>
        <repo.url>http://maven.ehengjian.com/nexus/repository/maven-test/</repo.url>
        <releases.id>hjtest</releases.id>
        <releases.name>maven-test</releases.name>
        <releases.url>http://maven.ehengjian.com/nexus/repository/maven-test/</releases.url>
        <snap.id>hjtest</snap.id>
        <snap.name>maven-test</snap.name>
        <snap.url>http://maven.ehengjian.com/nexus/repository/maven-test/</snap.url>
      </properties>
    </profile>
    <profile>
      <id>hjstage</id>
      <properties>
        <repo.id>hjstage</repo.id>
        <repo.name>maven-stage</repo.name>
        <repo.url>http://maven.ehengjian.com/nexus/repository/maven-stage/</repo.url>
        <releases.id>hjstage</releases.id>
        <releases.name>maven-stage</releases.name>
        <releases.url>http://maven.ehengjian.com/nexus/repository/maven-stage/</releases.url>
        <snap.id>hjstage</snap.id>
        <snap.name>maven-stage</snap.name>
        <snap.url>http://maven.ehengjian.com/nexus/repository/maven-stage/</snap.url>
      </properties>
    </profile>
    <profile>
      <id>hlnexus</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <properties>
        <repo.id>hlnexus</repo.id>
        <repo.name>maven-public</repo.name>
        <repo.url>http://maven.ehengjian.com/nexus/repository/maven-public/</repo.url>
        <releases.id>hlnexus</releases.id>
        <releases.name>nexus-maven-releases</releases.name>
        <releases.url>http://maven.ehengjian.com/nexus/repository/maven-releases/</releases.url>
        <snap.id>hlnexus</snap.id>
        <snap.name>nexus-maven-snapshots</snap.name>
        <snap.url>http://maven.ehengjian.com/nexus/repository/maven-snapshots/</snap.url>
      </properties>
    </profile>
  </profiles>

  <repositories>
    <repository>
      <id>${repo.id}</id>
      <name>${repo.name}</name>
      <url>${repo.url}</url>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
      <releases>
        <enabled>true</enabled>
      </releases>
    </repository>
  </repositories>

  <pluginRepositories>
    <pluginRepository>
      <id>${repo.id}</id>
      <name>${repo.name}</name>
      <url>${repo.url}</url>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
      <releases>
        <enabled>true</enabled>
      </releases>
    </pluginRepository>
  </pluginRepositories>

  <distributionManagement>
    <repository>
      <id>${releases.id}</id>
      <name>${releases.name}</name>
      <url>${releases.url}</url>
    </repository>
    <snapshotRepository>
      <id>${snap.id}</id>
      <name>${snap.name}</name>
      <url>${snap.url}</url>
    </snapshotRepository>
  </distributionManagement>

</project>
