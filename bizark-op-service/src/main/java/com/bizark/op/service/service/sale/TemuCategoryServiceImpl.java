package com.bizark.op.service.service.sale;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.common.exception.CommonException;
import com.bizark.op.api.cons.RedisCons;
import com.bizark.op.api.enm.sale.SyncMappingEnum;
import com.bizark.op.api.enm.temu.TemuControlTypeEnum;
import com.bizark.op.api.enm.temu.TemuUrl;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.sale.ProductChannelsDraft;
import com.bizark.op.api.entity.op.sale.ProductChannelsDraftDetailsCategory;
import com.bizark.op.api.entity.op.sale.TemuCategory;
import com.bizark.op.api.entity.op.sale.TemuProductProperty;
import com.bizark.op.api.entity.op.sale.request.TemuBrandRequest;
import com.bizark.op.api.entity.op.sale.request.TemuCategoryAttrRequest;
import com.bizark.op.api.entity.op.sale.request.TemuCategoryRequest;
import com.bizark.op.api.entity.op.sale.response.TemuBrandResponse;
import com.bizark.op.api.entity.op.sale.response.TemuCategoryAttrResponse;
import com.bizark.op.api.entity.op.sale.response.TemuCategoryResponse;
import com.bizark.op.api.entity.op.sale.vo.ProductChannelsDraftDetailsCategoryDataVO;
import com.bizark.op.api.entity.op.sale.vo.ProductChannelsDraftDetailsCategoryVO;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.sale.ProductChannelsDraftDetailsCategoryService;
import com.bizark.op.api.service.sale.ProductChannelsDraftService;
import com.bizark.op.api.service.sale.TemuCategoryService;
import com.bizark.op.api.service.sale.TemuProductPropertyService;
import com.bizark.op.common.util.BeanCopyUtils;
import com.bizark.op.common.util.spring.SpringUtils;
import com.bizark.op.service.api.InventorySelectApi;
import com.bizark.op.service.api.TemuApi;
import com.bizark.op.service.mapper.sale.TemuCategoryMapper;
import com.bizark.op.service.util.XxlConfig;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.ResultHandler;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TemuCategoryServiceImpl extends ServiceImpl<TemuCategoryMapper,TemuCategory> implements TemuCategoryService {

    @Autowired
    private TemuCategoryMapper temuCategoryMapper;

    @Autowired
    private AccountService accountService;

    @Autowired
    private TemuApi temuApi;


    @Autowired
    private ProductChannelsDraftService productChannelsDraftService;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;


    @Autowired
    private TemuProductPropertyService temuProductPropertyService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    @Autowired
    private ProductChannelsDraftDetailsCategoryService productChannelsDraftDetailsCategoryService;

    private static final List<Integer> temuCodeList = Lists.newArrayList(0, 1, 3, 16);




    @Override
    public void syncTemuCategories(Account account) {
        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            return;
        }
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey("cnAccessToken")) {
            return;
        }
        long timeMillis = System.currentTimeMillis() / 1000;
        TemuCategoryRequest request = new TemuCategoryRequest();
        request.setAccessToken(jsonObject.getString("cnAccessToken"));
        request.setAppKey(XxlConfig.TEMU_APP_CN_KEY_ID);
        request.setSiteId(1);
        request.setTimestamp(timeMillis);
        request.setShowHidden(true);
        request.setParentCatId(0);
        String sign = InventorySelectApi.getTemuSign(request);
        request.setSign(sign);
        syncAndHandleTemuCategory(account,request);

    }

    @Override
    public void syncTemuCategories(Integer orgId) {
        String redisKey = "temu:category:" + orgId;
        if (Boolean.FALSE.equals(stringRedisTemplate.hasKey(redisKey))) {
            throw new CommonException("未获取到缓存配置数据");
        }

        String value = stringRedisTemplate.opsForValue().get(redisKey);

        Account account = accountService.getById(Integer.parseInt(value));

        if (Objects.isNull(account)) {
            throw new CommonException("为获取到店铺信息: " + value);
        }
        syncTemuCategories(account);
    }


    @Override
    public void syncAndHandleTemuCategory(Account account,TemuCategoryRequest request) {
        try {
            HttpRequest post = HttpUtil.createPost(TemuUrl.CATEGORY_QUERY.getCnBaseUrl());
            post.body(JSON.toJSONString(request));
            HttpResponse httpResponse = post.execute();
            if (!httpResponse.isOk()) {
                return;
            }
            TemuCategoryResponse response = JSON.parseObject(httpResponse.body(), TemuCategoryResponse.class);

            if (Objects.isNull(response.getResult()) || CollectionUtil.isEmpty(response.getResult().getCategoryDTOList())) {
                return;
            }

            handleCategoryResponse(account,request.getParentCatId(), response.getResult().getCategoryDTOList());

            for (TemuCategoryResponse.Result.Category category : response.getResult().getCategoryDTOList()) {
                request.setParentCatId(category.getCatId());
                request.setSign(null);
                request.setTimestamp(System.currentTimeMillis() / 1000);
                request.setSign(InventorySelectApi.getTemuSign(request));
                syncAndHandleTemuCategory(account,request);
            }
        } catch (HttpException e) {
            e.printStackTrace();
        }
    }

    @Override
    public List<TemuCategory> selectAllParentCategoryByCatId(Integer catId) {
        TemuCategory category = this.lambdaQuery()
                .eq(TemuCategory::getCatId, catId)
                .one();

        if (Objects.isNull(category)) {
            return new ArrayList<>();
        }

        List<TemuCategory> categories = new ArrayList<>();
        categories.add(category);
        selectAllParentCategoryByCatId(category, categories);
        return categories;
    }

    private void selectAllParentCategoryByCatId(TemuCategory category, List<TemuCategory> categories) {
        Integer parentId = category.getParentCatId();
        TemuCategory parentCategory = this.lambdaQuery()
                .eq(TemuCategory::getCatId, parentId)
                .one();
        if (Objects.nonNull(parentCategory)) {
            categories.add(parentCategory);
            selectAllParentCategoryByCatId(parentCategory, categories);
        }
    }

    private void handleCategoryResponse(Account account, Integer parentCatId, List<TemuCategoryResponse.Result.Category> categories) {
        List<TemuCategory> temuCategories = BeanCopyUtils.copyBeanList(categories, TemuCategory.class);


        Long parentId = 0L;
        if (!Objects.equals(parentCatId, 0)) {
            TemuCategory dbCategory = this.lambdaQuery()
                    .eq(TemuCategory::getCatId, parentCatId)
                    .eq(TemuCategory::getAccountId, account.getId())
                    .one();
            if (Objects.nonNull(dbCategory)) {
                parentId = dbCategory.getId();
            }
        }

//        List<Integer> catIds = temuCategories.stream().map(TemuCategory::getCatId).collect(Collectors.toList());
//
//        List<TemuCategory> dbCategories = this.lambdaQuery()
//                .eq(TemuCategory::getAccountId, account.getId())
//                .in(TemuCategory::getCatId, catIds)
//                .list();




        for (TemuCategory category : temuCategories) {
            category.setParentId(parentId);
            category.setAccountFlag(account.getFlag());
            category.setAccountId(account.getId());
            category.setUpdatedBy(0);
            category.setCreatedBy(0);
            category.setCreatedName("System");
            category.setUpdatedName("System");
            temuCategoryMapper.insertOrUpdate(category);
        }


    }


    @Override
    public List<TemuCategory> selectCategoryByParentId(Integer parentId) {
        List<TemuCategory> list = this.lambdaQuery()
                .eq(TemuCategory::getParentCatId, parentId)
                .list();
//        List<TemuCategory> isLeafList = list.stream().filter(e -> e.getIsLeaf()).collect(Collectors.toList());
//        if (CollectionUtil.isEmpty(isLeafList)) return list;
//        List<Integer> catIdList = isLeafList.stream().map(e -> e.getCatId()).distinct().collect(Collectors.toList());
//        Map<Integer, List<ProductChannelsDraftDetailsCategory>> categoryMap = SpringUtils.getBean(ProductChannelsDraftService.class).getCategory(catIdList);
//        list.stream().forEach(e->{
//            if (categoryMap.containsKey(e.getCatId())) {
//                e.setCategoryList(categoryMap.get(e.getCatId()));
//            }
//        });
        return list;
    }

    @Override
    public void streamQueryAllCategory(Integer orgId, ResultHandler<TemuCategory> handler) {
        temuCategoryMapper.streamQueryAllCategory(orgId, handler);
    }

    @Override
    public List<ProductChannelsDraftDetailsCategoryDataVO> selectCategoryAttr(Long draftId, Integer categoryId) {

        ProductChannelsDraft draft = productChannelsDraftService.getById(draftId);

        if (Objects.isNull(draft)) {
            throw new CommonException("未获取到草稿信息");
        }
        Account account = accountService.getById(draft.getNewShopId());
        if (Objects.isNull(account)) {
            throw new CommonException("未获取到店铺信息");
        }
        Map<String, List<TemuProductProperty>> categoryMap = new HashMap<>();
        if (null != categoryId) {
            if (null != draft.getCategoryId() && draft.getCategoryId().equals(categoryId)) {
                List<ProductChannelsDraftDetailsCategory> categories = productChannelsDraftDetailsCategoryService.lambdaQuery().eq(ProductChannelsDraftDetailsCategory::getBindId, draft.getOldParentProductId())
                        .eq(ProductChannelsDraftDetailsCategory::getVersion, draft.getVersion()).list();

                if (!CollectionUtils.isEmpty(categories)) {
                    categoryMap.putAll(categories.stream().collect(Collectors.groupingBy(e->e.getPid()+e.getLabel(), Collectors.mapping(e-> buildTemuProductProperty(e), Collectors.toList()))));
                }
            }
            draft.setCategoryId(categoryId);
        }
        List<ProductChannelsDraftDetailsCategoryDataVO> detailsCategoryVOS = null != categoryId ? getNotFirstUpdatePropsV2(account, draft, categoryMap) : getFirstUpdatePropsV2(account, draft);

        /*if (CollectionUtils.isEmpty(detailsCategoryVOS)) {
            detailsCategoryVOS = new ArrayList<>();
        } else {
            for (ProductChannelsDraftDetailsCategoryDataVO categoryVO : detailsCategoryVOS) {
                ProductChannelsDraftDetailsCategory productChannelsDraftDetailsCategory = categoryMap.get(categoryVO.getPid() + categoryVO.getLabel());
                if (null == productChannelsDraftDetailsCategory) continue;
                categoryVO.getValueList().stream().filter(e -> e.getVid().equals(productChannelsDraftDetailsCategory.getVid())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(vidAttrValue))  continue;
                vidAttrValue.get(0).setDefaultType(Boolean.TRUE);
            }
        }*/
        queryBrand(account, detailsCategoryVOS, categoryMap);

        return detailsCategoryVOS;
    }

    @Override
    public List<TemuCategoryAttrResponse.Result.Property> getCategoryAttrByAll() {
        Account account = accountService.getById(2551);
        Long id = null;
        while (true) {
            List<TemuCategory> categories = temuCategoryMapper.queryById(id);
            if (CollectionUtils.isEmpty(categories)) break;
            id = categories.get(categories.size()-1).getId();
            List<Integer> catIdList = categories.stream().map(e -> e.getCatId()).distinct().collect(Collectors.toList());

            for (Integer catId : catIdList) {
                TemuCategoryAttrResponse attrResponse = temuApi.requestTemu(account, new TemuCategoryAttrRequest(catId), TemuCategoryAttrResponse.class);
                if (attrResponse.getSuccess()) continue;
                List<TemuCategoryAttrResponse.Result.Property> properties = attrResponse.getResult().getProperties().stream().filter(e -> e.getControlType().equals(3)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(properties)) {
                    log.error("当前获取目录:{}", properties);
                    return properties;
                }
            }
        }

        return null;
    }

    @Override
    public List<String> distinctInvestment() {
        return temuCategoryMapper.distinctInvestment();
    }

    private void queryBrand(Account account, List<ProductChannelsDraftDetailsCategoryDataVO> detailsCategoryVOS, Map<String, List<TemuProductProperty>> categoryMap) {
        Integer page = 1;
        Integer pageSize = 100;
        List<ProductChannelsDraftDetailsCategoryDataVO.AttrValue> brandList = new ArrayList<>();
        List<ProductChannelsDraftDetailsCategoryDataVO> existList = detailsCategoryVOS.stream().filter(e -> e.getLabel().equals("品牌名")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(existList)) return;
        while (true) {
            TemuBrandResponse response = temuApi.requestTemu(account, new TemuBrandRequest(page++, pageSize), TemuBrandResponse.class);
            if (!response.getSuccess()) {
                break;
            }
            if (null == response.getResult() || CollectionUtils.isEmpty(response.getResult().getPageItems())) {
                break;
            }
            List<TemuBrandResponse.Items> itemsList = response.getResult().getPageItems();
            Map<Long, List<TemuBrandResponse.Items>> brandMap = itemsList.stream().collect(Collectors.groupingBy(e -> e.getPid()));
            for (Map.Entry<Long, List<TemuBrandResponse.Items>> entry : brandMap.entrySet()) {
                List<ProductChannelsDraftDetailsCategoryDataVO.AttrValue> valueList = entry.getValue().stream().map(e -> {
                    ProductChannelsDraftDetailsCategoryDataVO.AttrValue attrValue = new ProductChannelsDraftDetailsCategoryDataVO.AttrValue();
                    attrValue.setVid(e.getVid()).setValue(e.getBrandNameEn()).setBrandId(e.getBrandId()).setUnique("BRAND" + e.getVid());
                    List<TemuProductProperty> temuProductProperties = categoryMap.get(e.getPid() + "品牌名");
                    if (!CollectionUtils.isEmpty(temuProductProperties)) {
                        TemuProductProperty property = temuProductProperties.get(0);
                        if (null != property && null != e.getVid() && null != property.getVid() && e.getVid().equals(property.getVid())) {
                            attrValue.setDefaultType(true);
                        }
                    }
                    return attrValue;
                }).collect(Collectors.toList());
                brandList.addAll(valueList);
            }
        }

        if (!CollectionUtils.isEmpty(brandList)) {
            List<ProductChannelsDraftDetailsCategoryDataVO.AttrValue> brands = brandList.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(
                            v -> v.getVid()
                    ))), ArrayList::new));
            for (ProductChannelsDraftDetailsCategoryDataVO detailsCategoryVO : detailsCategoryVOS) {
                if (!detailsCategoryVO.getLabel().equals("品牌名")) continue;
                List<ProductChannelsDraftDetailsCategoryDataVO.AttrValue> bindList = brands.stream().filter(e -> e.getDefaultType()).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(bindList)) {
                    detailsCategoryVO.setVid(bindList.get(0).getVid());
                    detailsCategoryVO.setValue(bindList.get(0).getValue());
                }

                detailsCategoryVO.setValueList(brands);
            }
        }
    }

    private TemuProductProperty buildTemuProductProperty(ProductChannelsDraftDetailsCategory e) {
        TemuProductProperty property = new TemuProductProperty();
        property.setPid(e.getPid()).setVid(e.getVid()).setTemplatePid(e.getTemplatePid()).setPropName(e.getLabel()).setPropValue(e.getValue())
                .setValueUnit(e.getValueUnit()).setRefPid(e.getRefPid())
                .setValueExtendInfo(e.getValueExtendInfo())
                .setControlType(e.getControlType())
                .setNumberInputTitle(e.getNumberInputTitle())
                .setNumberInputValue(e.getNumberInputValue())
        ;
        return property;
    }

    private List<ProductChannelsDraftDetailsCategoryVO> getNotFirstUpdateProps(Account account, ProductChannelsDraft draft) {
        TemuCategoryAttrResponse attrResponse = temuApi.requestTemu(account, new TemuCategoryAttrRequest(draft.getCategoryId()), TemuCategoryAttrResponse.class);
        if (attrResponse == null || attrResponse.getSuccess() == null || !attrResponse.getSuccess() || attrResponse.getResult() == null ||CollectionUtil.isEmpty(attrResponse.getResult().getProperties())) {            log.error("当前类目下未获取到类目属性信息");
            return new ArrayList<>();
        }

        List<TemuCategoryAttrResponse.Result.Property> properties = attrResponse.getResult().getProperties()
                .stream().filter(c -> CollectionUtil.isNotEmpty(c.getValues()) && c.getShowType() == 0)
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(properties)) {
            log.error("当前类目下未获取到存在属性值的属性信息");
            return new ArrayList<>();
        }

       return  properties.stream()
                .map(property -> {
                    ProductChannelsDraftDetailsCategoryVO detailsCategory = new ProductChannelsDraftDetailsCategoryVO();
                    List<TemuCategoryAttrResponse.Result.Property.AttrValue> attrValueList = property.getValues();
                    List<ProductChannelsDraftDetailsCategoryVO.AttrValue> list = attrValueList.stream().map(e -> {
                        ProductChannelsDraftDetailsCategoryVO.AttrValue attrValue = new ProductChannelsDraftDetailsCategoryVO.AttrValue();
                        attrValue.setVid(e.getVid());
                        attrValue.setValue(e.getValue());
                        attrValue.setValueExtendInfo(e.getExtendInfo());
                        return attrValue;
                    }).collect(Collectors.toList());
                    detailsCategory.setPid(property.getPid());
                    detailsCategory.setLabel(property.getName());
                    detailsCategory.setValueList(list);
                    detailsCategory.setTemplatePid(property.getTemplatePid());
                    detailsCategory.setRefPid(property.getRefPid());
                    detailsCategory.setRequired(property.getRequired());
                    if (CollectionUtil.isNotEmpty(property.getValueUnit())) {
                        detailsCategory.setValueUnit(
                                property.getValueUnit().stream().collect(Collectors.joining(" "))
                        );
                    }

                    return detailsCategory;
                }).collect(Collectors.toList());
    }


    private List<ProductChannelsDraftDetailsCategoryDataVO> getNotFirstUpdatePropsV2(Account account, ProductChannelsDraft draft,  Map<String, List<TemuProductProperty>> categoryMap) {
        TemuCategoryAttrResponse attrResponse = temuApi.requestTemu(account, new TemuCategoryAttrRequest(draft.getCategoryId()), TemuCategoryAttrResponse.class);
        if (attrResponse == null || attrResponse.getSuccess() == null || !attrResponse.getSuccess() || attrResponse.getResult() == null ||CollectionUtil.isEmpty(attrResponse.getResult().getProperties())) {
            log.error("当前类目下未获取到类目属性信息");
            return new ArrayList<>();
        }

        List<TemuCategoryAttrResponse.Result.Property> properties = attrResponse.getResult().getProperties()
                .stream().filter(c -> c.getName().equals("品牌名") || (null != c.getControlType() && temuCodeList.contains(c.getControlType()) && c.getShowType() != null && c.getParentTemplatePid() != null))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(properties)) {
            log.error("当前类目下未获取到存在属性值的属性信息");
            return new ArrayList<>();
        }

        List<TemuCategoryAttrResponse.Result.Property> onceList = properties.stream().filter(e -> e.getShowType() != null && e.getParentTemplatePid() != null && Objects.equals(e.getShowType(),0)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(onceList)) {
            log.error("无法获取到第一层级数据");
            return new ArrayList<>();
        }
        Map<Long, List<TemuCategoryAttrResponse.Result.Property>> groupByParentTemplatePidMap = properties.stream().collect(Collectors.groupingBy(e -> e.getParentTemplatePid()));
        List<ProductChannelsDraftDetailsCategoryDataVO> resultList = new ArrayList<>();
        for (TemuCategoryAttrResponse.Result.Property property : onceList) {
            ProductChannelsDraftDetailsCategoryDataVO vo =  buildCategoryDataVO(property);
            resultList.add(vo);
            buildChild(vo, groupByParentTemplatePidMap, categoryMap);
        }

        return resultList;
    }

    private List<ProductChannelsDraftDetailsCategoryVO> getFirstUpdateProps(Account account, ProductChannelsDraft draft) {
        // 查询商品信息
        CompletableFuture<List<TemuProductProperty>> f1 = CompletableFuture.supplyAsync(() -> temuProductPropertyService.lambdaQuery()
                .eq(TemuProductProperty::getSkcId, draft.getOldParentProductId())
                .eq(TemuProductProperty::getAccountId, draft.getOldShopId())
                .list(), threadPoolTaskExecutor);

        CompletableFuture<TemuCategoryAttrResponse> f2 = CompletableFuture.supplyAsync(() -> temuApi.requestTemu(account, new TemuCategoryAttrRequest(draft.getCategoryId()), TemuCategoryAttrResponse.class));


        try {
            CompletableFuture.allOf(f1, f2).get(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("获取商品属性失败:{} ", draft.getId(), e);
            return new ArrayList<>();
        }
        List<TemuProductProperty> productProperties = f1.join();
        TemuCategoryAttrResponse attrResponse = f2.join();

        if (attrResponse == null || attrResponse.getSuccess() == null || !attrResponse.getSuccess() || attrResponse.getResult() == null ||CollectionUtil.isEmpty(attrResponse.getResult().getProperties())) {            log.error("未获取到类目属性信息");
            return new ArrayList<>();
        }

        List<TemuCategoryAttrResponse.Result.Property> properties = attrResponse.getResult().getProperties();

        Map<Long, TemuCategoryAttrResponse.Result.Property> propertyMap = properties.stream()
                .filter(c -> CollectionUtil.isNotEmpty(c.getValues()) && c.getShowType() == 0)
                .collect(Collectors.toMap(TemuCategoryAttrResponse.Result.Property::getPid, v -> {
                    v.setValueMap(
                            v.getValues().stream().collect(Collectors.toMap(TemuCategoryAttrResponse.Result.Property.AttrValue::getVid, Function.identity(), (a, b) -> a))
                    );
                    return v;
                }, (a, b) -> a));


        Iterator<TemuProductProperty> iterator = productProperties.iterator();


        List<ProductChannelsDraftDetailsCategoryVO> props = new ArrayList<>();
        while (iterator.hasNext()) {
            TemuProductProperty property = iterator.next();

            // 如果最新属性不存在 直接移除
            if (!propertyMap.containsKey(property.getPid())) {
                iterator.remove();
                continue;
            }

            TemuCategoryAttrResponse.Result.Property attrProperty = propertyMap.get(property.getPid());
            Map<Long, TemuCategoryAttrResponse.Result.Property.AttrValue> valueMap = attrProperty.getValueMap();

            if (!valueMap.containsKey(property.getVid())) {
                // 属性值不存在直接移除
                iterator.remove();
                continue;
            }
            List<TemuCategoryAttrResponse.Result.Property.AttrValue> values = attrProperty.getValues();
            List<ProductChannelsDraftDetailsCategoryVO.AttrValue> attrValueList = values.stream().map(e -> {
                ProductChannelsDraftDetailsCategoryVO.AttrValue attrValue = new ProductChannelsDraftDetailsCategoryVO.AttrValue();
                if (e.getVid().equals(property.getVid())) {
                    attrValue.setDefaultType(true);
                }
                attrValue.setVid(e.getVid());
                attrValue.setValue(e.getValue());
                attrValue.setValueExtendInfo(e.getExtendInfo());
                return attrValue;
            }).collect(Collectors.toList());


            ProductChannelsDraftDetailsCategoryVO detailsCategory = new ProductChannelsDraftDetailsCategoryVO();
            detailsCategory.setPid(property.getPid());
            detailsCategory.setLabel(property.getPropName());
            detailsCategory.setValueList(attrValueList);
            detailsCategory.setRequired(attrProperty.getRequired());
            detailsCategory.setTemplatePid(property.getTemplatePid());
            detailsCategory.setRefPid(property.getRefPid());
            detailsCategory.setValueUnit(property.getValueUnit());
            detailsCategory.setLanguage(property.getLanguage());
            props.add(detailsCategory);
        }
        return props;
    }


    private List<ProductChannelsDraftDetailsCategoryDataVO> getFirstUpdatePropsV2(Account account, ProductChannelsDraft draft) {
        // 查询商品信息
        CompletableFuture<List<TemuProductProperty>> f1 = CompletableFuture.supplyAsync(() -> temuProductPropertyService.lambdaQuery()
                .eq(TemuProductProperty::getSkcId, draft.getOldParentProductId())
                .eq(TemuProductProperty::getAccountId, draft.getOldShopId())
                .list(), threadPoolTaskExecutor);

        CompletableFuture<TemuCategoryAttrResponse> f2 = CompletableFuture.supplyAsync(() -> temuApi.requestTemu(account, new TemuCategoryAttrRequest(draft.getCategoryId()), TemuCategoryAttrResponse.class));


        try {
            CompletableFuture.allOf(f1, f2).get(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("获取商品属性失败:{} ", draft.getId(), e);
            return new ArrayList<>();
        }
        List<TemuProductProperty> productProperties = f1.join();
        TemuCategoryAttrResponse attrResponse = f2.join();

        if (attrResponse == null || attrResponse.getSuccess() == null || !attrResponse.getSuccess() || attrResponse.getResult() == null ||CollectionUtil.isEmpty(attrResponse.getResult().getProperties())) {            log.error("未获取到类目属性信息");
            return new ArrayList<>();
        }
        List<TemuCategoryAttrResponse.Result.Property> propertyList = attrResponse.getResult().getProperties();
        propertyList = propertyList.stream().filter(e->e.getShowType() != null && e.getParentTemplatePid() != null && null != e.getControlType() && temuCodeList.contains(e.getControlType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(propertyList)) {
            return new ArrayList<>();
        }
        //第一层默认展示的
        List<TemuCategoryAttrResponse.Result.Property> onceList = propertyList.stream().filter(e -> e.getShowType() != null && e.getParentTemplatePid() != null && Objects.equals(e.getShowType(),0)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(onceList)) {
            log.error("无法获取到第一层级数据");
            return new ArrayList<>();
        }
        Map<String, List<TemuProductProperty>> defaultValueMap = productProperties.stream().collect(Collectors.groupingBy(e -> e.getPid() + e.getPropName()));

        Map<Long, List<TemuCategoryAttrResponse.Result.Property>> groupByParentTemplatePidMap = propertyList.stream().collect(Collectors.groupingBy(e -> e.getParentTemplatePid()));
        List<ProductChannelsDraftDetailsCategoryDataVO> resultList = new ArrayList<>();
        for (TemuCategoryAttrResponse.Result.Property property : onceList) {
            ProductChannelsDraftDetailsCategoryDataVO vo =  buildCategoryDataVO(property);
            resultList.add(vo);
            buildChild(vo, groupByParentTemplatePidMap, defaultValueMap);
        }

        return resultList;
    }

    private void buildChild(ProductChannelsDraftDetailsCategoryDataVO vo, Map<Long, List<TemuCategoryAttrResponse.Result.Property>> map, Map<String, List<TemuProductProperty>> defaultValueMap ) {
        buildDefaultValue(vo, defaultValueMap);
        List<TemuCategoryAttrResponse.Result.Property> child = map.get(vo.getTemplatePid());
        if (CollectionUtils.isEmpty(child) || CollectionUtils.isEmpty(vo.getValueList())) return;
        for (ProductChannelsDraftDetailsCategoryDataVO.AttrValue attrValue : vo.getValueList()) {
            List<ProductChannelsDraftDetailsCategoryDataVO> childDataVO = child.stream().map(e -> buildCategoryDataVO(e)).collect(Collectors.toList());
            for (ProductChannelsDraftDetailsCategoryDataVO childProperty : childDataVO) {
                buildAttrValue(attrValue, childProperty);
                childProperty.setParentName(attrValue.getValue());
                buildChild(childProperty, map, defaultValueMap);
            }
            log.error("当前{}构造结束", attrValue.getValue());
        }
    }

    private static void buildDefaultValue(ProductChannelsDraftDetailsCategoryDataVO vo, Map<String, List<TemuProductProperty>> defaultValueMap) {
        if (defaultValueMap.containsKey(vo.getPid() + vo.getLabel())) {
            TemuControlTypeEnum temuControlTypeEnum = TemuControlTypeEnum.map.get(vo.getControlType());
            if (null != temuControlTypeEnum) {
                temuControlTypeEnum.getFunction().apply(vo, defaultValueMap);
            }
        }
    }

    private static void buildAttrValue(ProductChannelsDraftDetailsCategoryDataVO.AttrValue attrValue, ProductChannelsDraftDetailsCategoryDataVO childProperty) {
        //controlType  == 0 无getTemplatePropertyValueParentList
        List<ProductChannelsDraftDetailsCategoryDataVO.TemplatePropertyValueParent> valueParents = childProperty.getTemplatePropertyValueParentList();


        if (CollectionUtils.isEmpty(valueParents)) {
            if (!CollectionUtils.isEmpty(childProperty.getShowCondition()) && childProperty.getControlType().equals(TemuControlTypeEnum.ZERO.getType())) {
                for (ProductChannelsDraftDetailsCategoryDataVO.ShowCondition showCondition : childProperty.getShowCondition()) {
                    if (CollectionUtils.isEmpty(showCondition.getParentVids())) continue;
                    if (showCondition.getParentVids().contains(attrValue.getVid())) {
                        attrValue.getChild().add(childProperty);
                        childProperty.setValueList(childProperty.getValueList().stream().filter(e -> showCondition.getParentVids().contains(e.getVid())).collect(Collectors.toList()));
                    }
                }

            }
            return;
        };
        for (ProductChannelsDraftDetailsCategoryDataVO.TemplatePropertyValueParent valueParent : valueParents) {
            if (CollectionUtils.isEmpty(valueParent.getParentVidList())) continue;
            if (valueParent.getParentVidList().contains(attrValue.getVid())) {
                //ProductChannelsDraftDetailsCategoryDataVO newChildVO = initNewChildVO(childProperty);
                attrValue.getChild().add(childProperty);
                childProperty.setValueList(childProperty.getValueList().stream().filter(e -> valueParent.getVidList().contains(e.getVid())).collect(Collectors.toList()));
            }
        }
    }

    @NotNull
    private static ProductChannelsDraftDetailsCategoryDataVO initNewChildVO(ProductChannelsDraftDetailsCategoryDataVO childProperty) {
        ProductChannelsDraftDetailsCategoryDataVO newChildVO = BeanCopyUtils.copyBean(childProperty, ProductChannelsDraftDetailsCategoryDataVO.class);
        newChildVO.setTemplatePropertyValueParentList(new ArrayList<>());
        newChildVO.setValueList(new ArrayList<>());
        return newChildVO;
    }

    private ProductChannelsDraftDetailsCategoryDataVO buildCategoryDataVO(TemuCategoryAttrResponse.Result.Property property) {
        ProductChannelsDraftDetailsCategoryDataVO vo = new ProductChannelsDraftDetailsCategoryDataVO();
        vo.setPid(property.getPid()).setTemplatePid(property.getTemplatePid()).setRequired(property.getRequired())
                .setInputMaxNum(property.getInputMaxNum()).setLabel(property.getName()).setParentTemplatePid(property.getParentTemplatePid())
                .setControlType(property.getControlType())
                .setNumberInputTitle(property.getNumberInputTitle())
                .setValueUnit(CollectionUtils.isEmpty(property.getValueUnit()) ? StrUtil.EMPTY : property.getValueUnit().get(0))
                .setRefPid(property.getRefPid()).setUnique(property.getPid() + property.getName());

        if (!CollectionUtils.isEmpty(property.getTemplatePropertyValueParentList())) {
            vo.setTemplatePropertyValueParentList(property.getTemplatePropertyValueParentList().stream().map(e->{
                ProductChannelsDraftDetailsCategoryDataVO.TemplatePropertyValueParent rs = new ProductChannelsDraftDetailsCategoryDataVO.TemplatePropertyValueParent();
                rs.setVidList(e.getVidList()).setParentVidList(e.getParentVidList());
                return rs;
            }).collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(property.getShowCondition())) {
            vo.setShowCondition(property.getShowCondition().stream().map(e->{
                ProductChannelsDraftDetailsCategoryDataVO.ShowCondition rs = new ProductChannelsDraftDetailsCategoryDataVO.ShowCondition();
                rs.setParentRefPid(e.getParentRefPid());
                rs.setParentVids(e.getParentVids());
                return rs;
            }).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(property.getValues())) {
            List<ProductChannelsDraftDetailsCategoryDataVO.AttrValue> valueList = property.getValues().stream().map(e -> {
                ProductChannelsDraftDetailsCategoryDataVO.AttrValue attrValue = new ProductChannelsDraftDetailsCategoryDataVO.AttrValue();
                attrValue.setVid(e.getVid()).setValue(e.getValue()).setValueExtendInfo(e.getExtendInfo()).setUnique(e.getVid() + "");
                return attrValue;
            }).collect(Collectors.toList());
            vo.setValueList(valueList);
        }
        return vo;
    }


}
