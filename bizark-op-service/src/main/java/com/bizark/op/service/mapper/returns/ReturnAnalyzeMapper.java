package com.bizark.op.service.mapper.returns;

import com.bizark.op.api.entity.op.returns.StatReturnQuery;
import com.bizark.op.api.entity.op.returns.VO.ReturnAnalyzeVO;
import com.bizark.op.api.entity.op.returns.entity.ReturnInfoEntity;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023年9月19日  13:52
 * @description:
 */
public interface ReturnAnalyzeMapper {

    /**
     * 退货分析 VC
     *
     * @param type
     * @param shopIdList
     * @param returnDateStart
     * @param returnDateEnd
     * @param asinList
     * @param skuList
     * @param operateIdList
     * @return
     */
    List<ReturnAnalyzeVO> getVcReturnAnalyzeVO(
            @Param("contextId") Integer contextId,
            @Param("type") Integer type,
            @Param("shopIdList") List<Integer> shopIdList,
            @Param("returnDateStart") LocalDate returnDateStart,
            @Param("returnDateEnd") LocalDate returnDateEnd,
            @Param("asinList") List<String> asinList,
            @Param("skuList") List<String> skuList,
            @Param("operateIdList") List<Integer> operateIdList);




    /**
     * @description: 退货分析V2版本
     * @author: Moore
     * @date: 2024/4/11 10:25
     * @param
     * @param statReturnQuery
     * @return: java.util.List<com.bizark.op.api.entity.op.returns.VO.ReturnAnalyzeVO>
     **/
    List<ReturnAnalyzeVO> getVcReturnAnalyzeVONew(StatReturnQuery statReturnQuery);


    /**
     * @description: 退货分析V2版本
     * @author: Moore
     * @date: 2024/4/11 10:25
     * @param
     * @param statReturnQuery
     * @return: java.util.List<com.bizark.op.api.entity.op.returns.VO.ReturnAnalyzeVO>
     **/
    Long getVcReturnAnalyzeVONew_COUNT(StatReturnQuery statReturnQuery);



    /**
     * 退货分析B2C
     *
     * @param contextId
     * @param type
     * @param shopIdList
     * @param returnDateStart
     * @param returnDateEnd
     * @param asinList
     * @param skuList
     * @param operateIdList
     * @param channelList
     * @param problemIdList
     * @return
     */

    List<ReturnAnalyzeVO> getB2CReturnAnalyze(@Param("contextId") Integer contextId,
                                              @Param("type") Integer type,
                                              @Param("shopIdList") List<Integer> shopIdList,
                                              @Param("returnDateStart") String returnDateStart,
                                              @Param("returnDateEnd") String returnDateEnd,
                                              @Param("orderDateStart") String orderDateStart,
                                              @Param("orderDateEnd") String orderDateEnd,
                                              @Param("asinList") List<String> asinList,
                                              @Param("skuList") List<String> skuList,
                                              @Param("operateIdList") List<Integer> operateIdList,
                                              @Param("channelList") List<String> channelList,
                                              @Param("problemIdList") List<Integer> problemIdList);

    /**
     * 根据参数获取订单数量
     *
     * @param orderDateStart
     * @param orderDateEnd
     * @param asins
     * @param skus
     * @param shopIds
     * @param channelList
     * @param group
     * @return
     */
    List<ReturnAnalyzeVO> getQuantityByParam(@Param("orderDateStart") String orderDateStart,
                                            @Param("orderDateEnd") String orderDateEnd,
                                            @Param("asins")List<String> asins,
                                            @Param("skus")List<String> skus,
                                            @Param("shopIds")List<Integer> shopIds,
                                            @Param("channelList")List<String> channelList,
                                            @Param("categoryIdList")List<Integer> categoryIdList,
                                            @Param("group") String group,
                                            @Param("ifSalesQuantity") String ifSalesQuantity,
                                            @Param("monthGroup") String monthGroup);





    /**
     * Description: 退货状态下拉框
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/3/20
     */
    List<String> getReturnStatusDict(@Param("contextId") Integer contextId,@Param("returnStatus") String returnStatus);



    /**
     * Description: 查询B2c折线图数据
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/15
     */
    List<ReturnAnalyzeVO> getB2CReturnAnalyzeLineChart(StatReturnQuery statReturnQuery);


    /**
     * Description: 查询退货分析数据
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/17
     */
    List<ReturnAnalyzeVO> getB2CReturnAnalyzeList(StatReturnQuery statReturnQuery);

    /**
     * Description: 查询退货分析数据按退货时间
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/17
     */
    List<ReturnAnalyzeVO> getB2CReturnAnalyzeListByReturnDate(StatReturnQuery statReturnQuery);

    /**
     * Description: 查询退货分析数据中退货原因维度数据
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/15
     */
    List<ReturnAnalyzeVO> getB2CReturnReasonAnalyzeList(StatReturnQuery statReturnQuery);

    /**
     * Description: 查询退货分析数据中退货原因维度数据
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/15
     */
    List<ReturnAnalyzeVO> getB2CReturnReasonAnalyzeListByReturnDate(StatReturnQuery statReturnQuery);


    /**
     * Description: 查询退货分析数据detaillist
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/17
     */
    List<ReturnAnalyzeVO> getB2CReturnAnalyzeDetailList(StatReturnQuery statReturnQuery);


    /**
     * Description: 查询退货分析数据detaillist
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/17
     */
    List<ReturnAnalyzeVO> getB2CReturnAnalyzeDetailListByReturnDate(StatReturnQuery statReturnQuery);

    /**
     * Description: 查询退货分析数据中退货原因维度数据detailList
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/15
     */
    List<ReturnAnalyzeVO> getB2CReturnReasonAnalyzeDetailList(StatReturnQuery statReturnQuery);


    /**
     * Description: 查询退货分析数据中退货原因维度数据detailList
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/15
     */
    List<ReturnAnalyzeVO> getB2CReturnReasonAnalyzeDetailListByReturnDate(StatReturnQuery statReturnQuery);




    /**
     * Description: 合计
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/19
     */
    ReturnAnalyzeVO getB2CReturnAnalyzeListSum(StatReturnQuery statReturnQuery);

    /**
     * Description: 合计明细
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/19
     */
    List<ReturnAnalyzeVO> getB2CReturnAnalyzeListSumDetailMonth(StatReturnQuery statReturnQuery);


    /**
     * Description: 合计
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/15
     */
    ReturnAnalyzeVO getB2CReturnReasonAnalyzeListSum(StatReturnQuery statReturnQuery);



    /**
     * Description: 合计通过退货时间。
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/15
     */
    ReturnAnalyzeVO getB2CReturnReasonAnalyzeListSumByReturnDate(StatReturnQuery statReturnQuery);



    /**
     * 根据参数获取订单数量
     *
     * @param orderDateStart
     * @param orderDateEnd
     * @param asins
     * @param skus
     * @param shopIds
     * @param channelList
     * @param group
     * @return
     */
    ReturnAnalyzeVO getQuantityByParamSum(@Param("orderDateStart") String orderDateStart,
                                             @Param("orderDateEnd") String orderDateEnd,
                                             @Param("asins")List<String> asins,
                                             @Param("skus")List<String> skus,
                                             @Param("shopIds")List<Integer> shopIds,
                                             @Param("channelList")List<String> channelList,
                                             @Param("categoryIdList")List<Integer> categoryIdList,
                                             @Param("group") String group,
                                             @Param("ifSalesQuantity") String ifSalesQuantity);



    /**
     * Description: 合计
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/19
     */
    ReturnAnalyzeVO getB2CReturnAnalyzeListSumPage(StatReturnQuery statReturnQuery);

    /**
     * Description: 合计
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/19
     */
    ReturnAnalyzeVO getB2CReturnAnalyzeListSumPageByReturnDate(StatReturnQuery statReturnQuery);

}
