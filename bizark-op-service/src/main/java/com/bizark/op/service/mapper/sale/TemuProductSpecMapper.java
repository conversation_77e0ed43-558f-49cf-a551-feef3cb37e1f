package com.bizark.op.service.mapper.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bizark.op.api.entity.op.sale.TemuProductSpec;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TemuProductSpecMapper extends BaseMapper<TemuProductSpec> {
    int deleteByOrgIdAndSkuIds(@Param("orgId") Integer orgId,@Param("skuIds") List<Long> skuIds);

}
