package com.bizark.op.service.service.mar;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.op.api.cons.mar.MarOperConstant;
import com.bizark.op.api.entity.op.mar.*;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.entity.op.stat.InstantSale;
import com.bizark.op.api.service.mar.MarListingInfoHisService;
import com.bizark.op.api.service.mar.MarListingInfoService;
import com.bizark.op.api.service.order.SaleOrderItemsService;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.service.mapper.mar.*;
import com.bizark.op.service.mapper.order.SaleOrderItemsMapper;
import com.bizark.op.service.mapper.sale.ProductChannelsMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Slf4j
public class MarListingInfoServiceImpl extends ServiceImpl<MarListingInfoMapper, MarListingInfo>
        implements MarListingInfoService {


    @Autowired
    private MarListingInfoMapper marListingInfoMapper;


    /*映射表*/
    @Autowired
    private ProductChannelsMapper productChannelsMapper;

    /*图片*/
    @Autowired
    private MarListingInfoImgMapper marListingInfoImgMapper;

    /*描述*/
    @Autowired
    private MarListingInfoDescMapper marListingInfoDescMapper;

    /*相似商品*/
    @Autowired
    private MarListingInfoSimilarMapper marListingInfoSimilarMapper;

    @Autowired
    private MarListingInfoHisMapper marListingInfoHisMapper;

    @Autowired
    private MarListingInfoHisService marListingInfoHisService;

    @Autowired
    private MarListingInfoImgHisMapper marListingInfoImgHisMapper;

    @Autowired
    private MarListingInfoDescHisMapper marListingInfoDescHisMapper;

    @Autowired
    private MarListingInfoSimilarHisMapper marListingInfoSimilarHisMapper;

    @Autowired
    private MarListingInfoVMapper marListingInfoVMapper;

    @Autowired
    private SaleOrderItemsService saleOrderItemsService;

    @Autowired
    private MarListingSupplementMapper marListingSupplementMapper;

    @Autowired
    private SaleOrderItemsMapper saleOrderItemsMapper;


    @Override
    public MarListingInfo selectMarListingInfoById(Long id) {
        return null;
    }

    @Override
    public List<MarListingInfo> selectMarListingInfoList(MarListingInfo marListingInfo) {
        return null;
    }

    @Override
    public int insertMarListingInfo(MarListingInfo marListingInfo) {
        return 0;
    }

    @Override
    public int updateMarListingInfo(MarListingInfo marListingInfo) {
        return 0;
    }

    @Override
    public int deleteMarListingInfoByIds(Long[] ids) {
        return 0;
    }

    @Override
    public int deleteMarListingInfoById(Long id) {
        return 0;
    }







    /**
     * @description: 增加定时同步discountPrice至明细表逻辑
     * @author: Moore
     * @date: 2023/9/4 11:05
     * @param
     * @return: void
    **/
    @Override
    public void syncListingDiscountPriceToOrderItemJob() {
        LocalDateTime localNow = LocalDateTime.now(TimeZone.getTimeZone("America/Los_Angeles").toZoneId());
        String nowPstTime = localNow.format(DateTimeFormatter.ISO_DATE);
        List<MarListingSupplement> marListingSupplements = marListingSupplementMapper.selectList(null);
        if (!CollectionUtils.isEmpty(marListingSupplements)) {
            for (MarListingSupplement marListingInfo : marListingSupplements) {
                if (marListingInfo.getListPrice() != null&&!StringUtils.isEmpty(marListingInfo.getAsin())) {
                    saleOrderItemsMapper.updateDiscountPrice(marListingInfo.getAsin(), marListingInfo.getListPrice(), nowPstTime);
//                   saleOrderItemsService.updateScOrderItemsDiscountPrice(marListingInfo.getAsin(), marListingInfo.getListPrice(), null, null);

                }
            }
        }
    }


    /**
     * @description: 获取Listing_price价格信息
     * @author: Moore
     * @date: 2024/4/19 15:32
     * @param
     * @param asin
     * @return: java.math.BigDecimal
    **/
    @Override
    public BigDecimal ListingDiscountPriceByAsin(String asin) {
        BigDecimal flagInit = BigDecimal.ZERO;
        if (StringUtils.isEmpty(asin)) {
            return flagInit;
        }

        LambdaQueryWrapper<MarListingSupplement> queryParm = new LambdaQueryWrapper<>();
        queryParm.select(MarListingSupplement::getListPrice);
        queryParm.eq(MarListingSupplement::getAsin, asin);
        List<MarListingSupplement> marListingSupplements = marListingSupplementMapper.selectList(queryParm);
        if (!CollectionUtils.isEmpty(marListingSupplements)){
            log.info("调用获取DiscountPrice,ASIN:{}", asin, marListingSupplements.get(0).getListPrice());
            return marListingSupplements.get(0).getListPrice();
        }
        return flagInit;
}


    /**
     * 存储listing信息
     *
     * @param message 接收mq发送的listing消息
     * @return 结果
     */
    @Override
    @Transactional
    public void savaMqListingInfo(String message) {
        if (StringUtils.isEmpty(message)) {
            return;
        }
        MarListingInfo marListingInfo = JSONObject.parseObject(message, MarListingInfo.class);
        if (marListingInfo == null || StringUtils.isEmpty(marListingInfo.getAsin())||StringUtils.isEmpty(marListingInfo.getCountryCode())) {
            log.info("asin或店铺数据为空：{}", message);
            return;
        }
        String asin = marListingInfo.getAsin();
        String countryCode = marListingInfo.getCountryCode();

        //将折扣价格同步至订单表
//        saleOrderItemsService.updateScOrderItemsDiscountPrice(asin, marListingInfo.getListPrice(), marListingInfo.getDiscountPrice(), marListingInfo.getUsTime());
        //判断主表ASIN是否存在
        LambdaQueryWrapper<MarListingInfo> marListingQuer = new LambdaQueryWrapper<>();
        marListingQuer.eq(MarListingInfo::getAsin, asin);
        marListingQuer.eq(MarListingInfo::getCountryCode,countryCode);
        MarListingInfo listingInfo = marListingInfoMapper.selectOne(marListingQuer);

        //获取ASIN对应的组织信息
        LambdaQueryWrapper<ProductChannels> productChannelsQuery = new LambdaQueryWrapper<>();
        productChannelsQuery.eq(ProductChannels::getAsin, asin);
        List<ProductChannels> channels = productChannelsMapper.selectList(productChannelsQuery);
        Long organizationId = CollectionUtils.isEmpty(channels) ? null : channels.get(0).getOrgId().longValue();
        //不同地区发货，状态信息
        this.deliveryTimeFormat(marListingInfo);


        String initMainRank = NumberUtil.isNumber(marListingInfo.getMainCategoryRank()) ? marListingInfo.getMainCategoryRank() : null; //大类排名
        String initSubRank = NumberUtil.isNumber(marListingInfo.getSubCategoryRank()) ? marListingInfo.getSubCategoryRank() : null; //小类排名


        marListingInfo.setMainCategoryRank(initMainRank);
        marListingInfo.setSubCategoryRank(initSubRank);
        marListingInfo.setConvertFirstDate(this.firstDateFormart(marListingInfo.getFirstDate())); //格式化Lsting上线时间

        //时间转换 Listig上线时间转换
        if (listingInfo == null) {
            marListingInfo.setOrganizationId(organizationId);
            marListingInfo.setCreatedAt(DateUtils.getNowDate());
            marListingInfoMapper.insert(marListingInfo);
            //图片信息
            List<String> images = marListingInfo.getImages();
            if (!CollectionUtils.isEmpty(images)) {
                MarListingInfoImg marListingInfoImg = null;
                for (String image : images) {
                    marListingInfoImg = new MarListingInfoImg();
                    marListingInfoImg.setImgSrc(image);
                    marListingInfoImg.setAsin(asin);
                    marListingInfoImg.setListingId(marListingInfo.getId());
                    marListingInfoImg.setOrganizationId(organizationId);
                    marListingInfoImg.setCreatedAt(DateUtils.getNowDate());
                    marListingInfoImgMapper.insert(marListingInfoImg); //插入图片信息
                }
            }
            //设置描述信息
            List<String> describe5 = marListingInfo.getDescribe5();
            MarListingInfoDesc marListingInfoDesc = null;
            if (!CollectionUtils.isEmpty(describe5)) {
                for (String desc : describe5) {
                    marListingInfoDesc = new MarListingInfoDesc();
                    marListingInfoDesc.setAsin(asin);
                    marListingInfoDesc.setListingId(marListingInfo.getId());
                    marListingInfoDesc.setDescInfo(desc);
                    marListingInfoDesc.setOrganizationId(organizationId);
                    marListingInfoDesc.setCreatedAt(DateUtils.getNowDate());
                    marListingInfoDescMapper.insert(marListingInfoDesc);
                }
            }
            //相似商品信息
            MarListingInfoSimilar consider = marListingInfo.getConsider();
            if (consider != null&&(!StringUtils.isEmpty(consider.getConsiderType())||!StringUtils.isEmpty(consider.getConsiderId())||!StringUtils.isEmpty(consider.getConsiderTitle()))) {
                consider.setConsiderAsin(consider.getConsiderId());
                consider.setAsin(asin);
                consider.setListingId(marListingInfo.getId());
                consider.setOrganizationId(organizationId);
                consider.setCreatedAt(DateUtils.getNowDate());
                marListingInfoSimilarMapper.insert(consider);
            }
        } else {
            MarListingInfo listingInfoOne = this.entityNullTran(marListingInfo);
            listingInfoOne.setSubCategoryRank(null);    //更新主表数据不更新大小类大小类排名，由系统接口更新
            listingInfoOne.setSubCategory(null);
            listingInfoOne.setMainCategoryRank(null);
            listingInfoOne.setMainCategory(null);
            listingInfoOne.setUpdatedAt(DateUtils.getNowDate());
            listingInfoOne.setId(listingInfo.getId());
//            marListingInfoMapper.updateById(listingInfoOne); //根据主键更新主表
            marListingInfoMapper.updateManualById(listingInfoOne); //根据主键更新主表


            //操作ASIN图片
            List<String> images = marListingInfo.getImages();
            //先刪除
            LambdaQueryWrapper<MarListingInfoImg> deleteParm = new LambdaQueryWrapper<>();
            deleteParm.eq(MarListingInfoImg::getAsin, asin);
            marListingInfoImgMapper.delete(deleteParm);
            if (!CollectionUtils.isEmpty(images)) {
                //操作图片信息
                MarListingInfoImg marListingInfoImg = null;
                for (String image : images) {
                    marListingInfoImg = new MarListingInfoImg();
                    marListingInfoImg.setImgSrc(image);
                    marListingInfoImg.setAsin(asin);
                    marListingInfoImg.setListingId(listingInfo.getId());
                    marListingInfoImg.setOrganizationId(organizationId);
                    marListingInfoImg.setCreatedAt(DateUtils.getNowDate()); //创建时间
                    marListingInfoImgMapper.insert(marListingInfoImg); //插入图片信息
                }
            }


            /*5大描述信息*/
            List<String> describe5 = marListingInfo.getDescribe5();

            LambdaQueryWrapper<MarListingInfoDesc> infoDescDeleteParm = new LambdaQueryWrapper<>();
            infoDescDeleteParm.eq(MarListingInfoDesc::getAsin, asin);
            marListingInfoDescMapper.delete(infoDescDeleteParm);
            MarListingInfoDesc marListingInfoDesc = null;
            if (!CollectionUtils.isEmpty(describe5)) {
                for (String desc : describe5) {
                    marListingInfoDesc = new MarListingInfoDesc();
                    marListingInfoDesc.setAsin(asin);
                    marListingInfoDesc.setListingId(listingInfo.getId());
                    marListingInfoDesc.setDescInfo(desc);
                    marListingInfoDesc.setOrganizationId(organizationId);
                    marListingInfoDesc.setCreatedAt(DateUtils.getNowDate());
                    marListingInfoDescMapper.insert(marListingInfoDesc);
                }
            }


            //相似商品
            MarListingInfoSimilar consider = marListingInfo.getConsider();
            LambdaQueryWrapper<MarListingInfoSimilar> similaDeletePam = new LambdaQueryWrapper<>();
            similaDeletePam.eq(MarListingInfoSimilar::getAsin, asin);
            marListingInfoSimilarMapper.delete(similaDeletePam);
            if (consider != null) {
                consider.setConsiderAsin(consider.getConsiderId());
                consider.setAsin(asin);
                consider.setListingId(listingInfo.getId());
                consider.setOrganizationId(organizationId);
                consider.setCreatedAt(DateUtils.getNowDate());
                marListingInfoSimilarMapper.insert(consider);
            }
        }

        //存储报表历史数据
//        this.saveHisInfo(marListingInfo, organizationId);

    }

    /**
     * @description: 保存历史JOb
     * @author: Moore
     * @date: 2024/1/12 16:31
     * @param usTimeStr 指定PST时间
     * @return: void
     **/
    @Override
    public void saveListingHisJob(String  usTimeStr) {
        //查询返回历史信息;
        List<MarListingInfoHis> marListingInfos = marListingInfoMapper.selectListingResHis();
        if (CollectionUtils.isEmpty(marListingInfos)) {
            return;
        }
        DateTime usNowTime = null;
        if (StringUtils.isEmpty(usTimeStr)){
            //当前PST时间
            LocalDateTime localNow = LocalDateTime.now(TimeZone.getTimeZone("America/Los_Angeles").toZoneId());
            String nowPstTime = localNow.format(DateTimeFormatter.ISO_DATE);
            usNowTime = DateUtil.parse(nowPstTime);
        }else{
            usNowTime = DateUtil.parse(usTimeStr);
        }
        DateTime usTime = usNowTime;
        Date utcTime = DateUtils.getNowDate();
        DateTime bJTime = DateUtil.offsetHour(utcTime, 8);
        marListingInfos.forEach(item->{
            item.setUsTime(usTime);
            item.setSutcTime(utcTime);
            item.setBjTime(bJTime);
            item.setCreatedAt(DateUtils.getNowDate());
            item.setUpdatedAt(DateUtils.getNowDate());
        });
        marListingInfoHisService.saveBatch(marListingInfos);
    }






    @Override
    public Map<String, MarListingInfo> selectMarListingInfoListByAsins(List<String> orderAsin) {
        if (CollectionUtils.isEmpty(orderAsin)){
            return new HashMap<String, MarListingInfo>();
        }
        List<MarListingInfo> marListingInfos = marListingInfoMapper.selectMarListingInfoListByAsins(orderAsin);
        marListingInfos.forEach(marListingInfo -> {
            String deliveryTime = marListingInfo.getDeliveryTime();
            if (!StringUtils.isEmpty(deliveryTime) && deliveryTime.contains("$")) {
                marListingInfo.setDeliveryTime(deliveryTime.substring(0, deliveryTime.indexOf(" ")));
            }else{
                marListingInfo.setDeliveryTime(" ");
            }
        });
        return marListingInfos.stream().filter(ss->!StringUtils.isEmpty(ss.getAsin())).collect(Collectors.toMap(MarListingInfo::getAsin, Function.identity()));
    }

    @Override
    public HashMap<String, Object> selectMarListingInfoHisList(MarListingInfoHis marListingInfo) {
        return null;
    }


    /**
     * 查询Listing信息（主页面查询）
     *
     * @param marListingInfo 查询条件
     * @return 结果
     */
    @Override
    public List<MarListingInfoV> selectMarListingInfoVList(MarListingInfoV marListingInfo) {
        return marListingInfoVMapper.selectMarListingInfoVList(marListingInfo);
    }


    /**
     * 查询Listing 明细信息
     *
     * @param marListingInfo 查询条件
     * @return 结果
     */
    @Override
    public List<MarListingItemV> selectMarListingInfoItem(MarListingInfo marListingInfo) {
        if (StringUtils.isEmpty(marListingInfo.getAsin())) {
            throw new CustomException("Asin不能为空");
        }


        //获取当前最新数据
        List<MarListingItemV> marListingItemVS = marListingInfoMapper.selectListingInfoFieldContentList(marListingInfo);

        if (CollectionUtils.isEmpty(marListingItemVS)) {
            return null;
        }

        String searchTime = marListingInfo.getSearchTime();


        if (StringUtils.isEmpty(marListingInfo.getSearchTime())) {
            //计算昨日美国时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar calendar = Calendar.getInstance();
            sdf.setTimeZone(TimeZone.getTimeZone("America/Los_Angeles"));
            String usTime = sdf.format(calendar.getTime());
            DateTime dateTime1 = DateUtil.offsetDay(DateUtil.parseDate(usTime), -1);
            marListingInfo.setSearchTime(DateUtil.format(dateTime1, DatePattern.NORM_DATE_PATTERN));
        }

        //查询历史数据
        List<MarListingItemV> marListingHisItem = marListingInfoHisMapper.selectListingHisInfoFieldContentList(marListingInfo);

        //设置五大描述
        marListingItemVS.add(this.ListingDescTra(marListingInfo));

        //判断历史是否存在
        if (!CollectionUtils.isEmpty(marListingHisItem)) {
            //前后对比封装
            for (MarListingItemV marListingItemV : marListingItemVS) {
                List<MarListingItemV> hisItem = marListingHisItem.stream().filter(item -> item.getFieldName().equalsIgnoreCase(marListingItemV.getFieldName())).collect(Collectors.toList());
                marListingItemV.setAfterContent(CollectionUtils.isEmpty(hisItem) ? null : hisItem.get(0).getContent());
            }

            //监控时间
            marListingItemVS.forEach(item -> item.setMonitoringBeginTime(marListingHisItem.get(0).getMonitoringBeginTime()));
        }


        //查询条件 过滤
        List<MarListingItemV> filterList = null;
        if (!StringUtils.isEmpty(marListingInfo.getMonitortype()) && !StringUtils.isEmpty(marListingInfo.getProject())) {
            filterList = marListingItemVS.stream().filter(ss -> {
                return ss.getMonitortype().contains(marListingInfo.getMonitortype()) && ss.getProject().contains(marListingInfo.getProject());
            }).collect(Collectors.toList());
            return filterList;
        } else if (!StringUtils.isEmpty(marListingInfo.getMonitortype())) {
            filterList = marListingItemVS.stream().filter(ss -> {
                return ss.getMonitortype().contains(marListingInfo.getMonitortype());
            }).collect(Collectors.toList());
            return filterList;
        } else if (!StringUtils.isEmpty(marListingInfo.getProject())) {
            filterList = marListingItemVS.stream().filter(ss -> {
                return ss.getProject().contains(marListingInfo.getProject());
            }).collect(Collectors.toList());
            return filterList;
        }
        return marListingItemVS.stream().sorted(Comparator.comparing(MarListingItemV::getMonitortype)).collect(Collectors.toList()); //最终排序
    }



    /**
     * Listing 导出
     *
     * @param marListingInfoV 查询条件
     * @return 结果
     */
    @Override
    public ArrayList<Map<String, Object>> exportListingInfo(MarListingInfoV marListingInfoV) {
        List<MarListingInfoV> marListingInfoVS = this.selectMarListingInfoVList(marListingInfoV);
        if (CollectionUtils.isEmpty(marListingInfoVS)) {
            throw new CustomException("无可导出信息");
        }
        ArrayList<Map<String, Object>> rows = CollUtil.newArrayList();
        marListingInfoVS.stream().forEach(item->{
            Map<String, Object> row = new LinkedHashMap<>();
            row.put("ASIN", item.getAsin());
            row.put("父ASIN", item.getParentAsin());
            row.put("标题", item.getTitle());
            row.put("款号",item.getSku());
            row.put("分类",item.getCategoryName());
            row.put("运营",item.getNickName());
            row.put("库存状态",item.getSalesStatus());
            row.put("原价",item.getListPrice());
            row.put("discount",item.getDiscount());
            row.put("deal",item.getDealPrice());
            row.put("折扣后价格",item.getDiscountPrice());
            row.put("购物车价格",item.getCartPrice());
            row.put("coupon",item.getCoupon());
            row.put("Listing上线时间",item.getFirstDate());
            row.put("CA:9007送达时间",item.getDeliveryTime());
            row.put("CA:9007发货时间",item.getSalesStatus());
            row.put("GA:30024送达时间", item.getDeliveryTimeGa());
            row.put("GA:30024发货时间", item.getSalesStatusGa());
            row.put("NJ:070302送达时间", item.getDeliveryTimeNj());
            row.put("NJ:070302发货时间", item.getSalesStatusNj());
            row.put("similar item位置", item.getConsiderType());
            rows.add(row);
        });
        return rows;
    }


    /**
     * Listing 五点描述信息
     *
     * @param marListingInfo 查询条件信息
     * @return 结果
     */
    private MarListingItemV ListingDescTra(MarListingInfo marListingInfo) {
        //五点描述
        String asin = marListingInfo.getAsin();
        LambdaQueryWrapper<MarListingInfoDesc> marListingInfoDescLambdaQueryWrapper = new LambdaQueryWrapper<>();
        marListingInfoDescLambdaQueryWrapper.eq(MarListingInfoDesc::getAsin, asin);
        List<String> marListingInfoDesc = marListingInfoDescMapper.selectList(marListingInfoDescLambdaQueryWrapper).stream().map(MarListingInfoDesc::getDescInfo).collect(Collectors.toList());


        LambdaQueryWrapper<MarListingInfoDescHis> marListingInfoDescLambdaQueryWrapperHis = new LambdaQueryWrapper<>();
        marListingInfoDescLambdaQueryWrapperHis.eq(MarListingInfoDescHis::getAsin, asin);

        MarListingInfoDescHis marListingInfoDescHis = new MarListingInfoDescHis();
        marListingInfoDescHis.setAsin(asin);
        marListingInfoDescHis.setSearchTime(marListingInfo.getSearchTime()); //查询时间
        //查询历史五点描述信息
        List<MarListingInfoDescHis> marListingInfoDescHisList = marListingInfoDescHisMapper.selectListingDescHis(marListingInfoDescHis);
        List<String> descHisList = marListingInfoDescHisList.stream().map(MarListingInfoDescHis::getDescInfo).collect(Collectors.toList());


        //封装五大描述信息
        MarListingItemV marListingItemV = new MarListingItemV();
        marListingItemV.setMonitortype(MarOperConstant.MONITOR_TYPE_GOODS_INFO);
        marListingItemV.setProject(MarOperConstant.MONITOR_PROJECT_DESC);
        marListingItemV.setContent(this.ListingDescOrderBy(marListingInfoDesc)); //变更前
        marListingItemV.setAfterContent(this.ListingDescOrderBy(descHisList));  //变更后


        //是否变更判断
        if (marListingInfoDesc.size() != descHisList.size()) {
            marListingItemV.setChangeStatus(true); //变更
        } else {
            //比较内容
            Boolean changeFlag = false;
            for (String marNew : marListingInfoDesc) {
                changeFlag = !descHisList.stream().anyMatch(descHis -> descHis.equals(marNew));
            }
            marListingItemV.setChangeStatus(changeFlag);
        }
        return marListingItemV;
    }

    /**
     * Listing 描述信息转换操作
     *
     * @param descInfo Listing五大描述信息
     * @return 结果
     */
    private String ListingDescOrderBy(List<String> descInfo) {
        StringBuffer stringBuffer = new StringBuffer();
        if (!CollectionUtils.isEmpty(descInfo)) {
            for (String desc : descInfo) {
                stringBuffer.append(desc);
            }
            return stringBuffer.toString();
        } else {
            return null;
        }

    }

    /**
     * 转换listing 上线时间
     *
     * @param firstDate 上线时间
     * @return 结果
     */
    public Date firstDateFormart(String firstDate) {
        if (StringUtils.isEmpty(firstDate)) {
            return null;
        }
        try {

            String usCommonTime = com.bizark.op.common.util.DateUtil.convertOriginalDate(firstDate);
            if (com.bizark.op.common.util.DateUtil.isLegalDate(usCommonTime.length(), usCommonTime, DatePattern.NORM_DATE_PATTERN)) { //格式判断并转换
                return DateUtil.parse(usCommonTime, DatePattern.NORM_DATE_PATTERN);
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 转换listing发货送达时效(不同實現奧)
     *
     * @param marListingInfo Listing信息
     * @return 结果
     */
    private void deliveryTimeFormat(MarListingInfo marListingInfo) {
        List<MarListingDeliveryInfo> receiptDeliveryDetails = marListingInfo.getReceiptDeliveryDetails();
        if (CollectionUtils.isNotEmpty(receiptDeliveryDetails)) {
            for (MarListingDeliveryInfo receiptDeliveryDetail : receiptDeliveryDetails) {
                if (MarOperConstant.REGION_CODE_CA_90007.equals(receiptDeliveryDetail.getRegionId())) {
                    marListingInfo.setSalesStatus(receiptDeliveryDetail.getInventory()); //送达时间,销售状态
                    marListingInfo.setDeliveryTime(receiptDeliveryDetail.getDeliveryTime()); //发货时间
                } else if (MarOperConstant.REGION_CODE_GA_30024.equals(receiptDeliveryDetail.getRegionId())) {
                    marListingInfo.setSalesStatusGa(receiptDeliveryDetail.getInventory());
                    marListingInfo.setDeliveryTimeGa(receiptDeliveryDetail.getDeliveryTime());
                } else if (MarOperConstant.REGION_CODE_NJ_07302.equals(receiptDeliveryDetail.getRegionId())) {
                    marListingInfo.setSalesStatusNj(receiptDeliveryDetail.getInventory());
                    marListingInfo.setDeliveryTimeNj(receiptDeliveryDetail.getDeliveryTime());
                }
            }
        }
    }


    /**
     * Listing 实体类Nul值转换空串操作
     *
     * @param marListingInfo Listing信息
     * @return 结果
     */
    private MarListingInfo entityNullTran(MarListingInfo marListingInfo) {
        marListingInfo.setAsinTitle(StrUtil.isBlank(marListingInfo.getAsinTitle())?null:marListingInfo.getAsinTitle());
        marListingInfo.setCoupon(StrUtil.isBlank(marListingInfo.getCoupon()) ? null :marListingInfo.getCoupon() );
        marListingInfo.setListPrice(StrUtil.isBlank(marListingInfo.getListPrice())?null:marListingInfo.getListPrice());
        marListingInfo.setDiscount(StrUtil.isBlank(marListingInfo.getDiscount())?null:marListingInfo.getDiscount());
        marListingInfo.setDiscountPrice(StrUtil.isBlank(marListingInfo.getDiscountPrice())?null:marListingInfo.getDiscountPrice());
        marListingInfo.setDealPrice(StrUtil.isNotBlank(marListingInfo.getDealPrice())?null:marListingInfo.getDealPrice());
        marListingInfo.setYouSave(StrUtil.isBlank(marListingInfo.getYouSave())?null:marListingInfo.getYouSave());
        marListingInfo.setCartPrice(StrUtil.isBlank(marListingInfo.getCartPrice())?null:marListingInfo.getCartPrice());
        marListingInfo.setSalesStatus(StrUtil.isBlank(marListingInfo.getSalesStatus())?null:marListingInfo.getSalesStatus());
        marListingInfo.setDeliveryTime(StrUtil.isBlank(marListingInfo.getDeliveryTime())?null:marListingInfo.getDeliveryTime());
        marListingInfo.setFirstDate(StrUtil.isBlank(marListingInfo.getFirstDate())?null:marListingInfo.getFirstDate());
        marListingInfo.setStarRatings(marListingInfo.getStarRatings() != null ? marListingInfo.getStarRatings():null);
        return marListingInfo;
    }


    /**
     * 存储listing历史信息表
     *
     * @param marListingInfo Listing信息
     * @return 结果
     */
    @Transactional
    public void saveHisInfo(MarListingInfo marListingInfo, Long organizationId) {

        String asin = marListingInfo.getAsin();

        //当天只存储同一ASIN一条美国时间数据
        MarListingInfoHis marListingInfoHis = new MarListingInfoHis();
        marListingInfoHis.setAsin(asin);
        marListingInfoHis.setSyncTime(marListingInfo.getUsTime()); //美国时间
        List<MarListingInfoHis> marListingInfoHisList = marListingInfoHisMapper.selectMarListingInfoHisList(marListingInfoHis);

        //历史信息插入
        if (CollectionUtils.isEmpty(marListingInfoHisList)) {
            marListingInfo.setId(null); //去除主键

            MarListingInfoHis marListingInfoHisRecod = new MarListingInfoHis();
            BeanUtils.copyProperties(marListingInfo, marListingInfoHisRecod);
            marListingInfoHisRecod.setOrganizationId(organizationId);
            marListingInfoHisRecod.setCreatedAt(DateUtils.getNowDate());//创建时间
            marListingInfoHisMapper.insert(marListingInfoHisRecod);

            //图片信息
            List<String> images = marListingInfo.getImages();
            if (!CollectionUtils.isEmpty(images)) {
                MarListingInfoImgHis marListingInfoImg = null;
                for (String image : images) {
                    marListingInfoImg = new MarListingInfoImgHis();
                    marListingInfoImg.setImgSrc(image);
                    marListingInfoImg.setAsin(asin);
                    marListingInfoImg.setListingId(marListingInfoHisRecod.getId());
                    marListingInfoImg.setOrganizationId(organizationId);
                    marListingInfoImg.setCreatedAt(DateUtils.getNowDate());
                    marListingInfoImgHisMapper.insert(marListingInfoImg); //插入历史图片信息
                }
            }

            //设置描述信息
            List<String> describe5 = marListingInfo.getDescribe5();
            MarListingInfoDescHis marListingInfoDesc = null;
            if (!CollectionUtils.isEmpty(describe5)) {
                for (String desc : describe5) {
                    marListingInfoDesc = new MarListingInfoDescHis();
                    marListingInfoDesc.setAsin(asin);
                    marListingInfoDesc.setListingId(marListingInfoHisRecod.getId());
                    marListingInfoDesc.setDescInfo(desc); //描述信息
                    marListingInfoDesc.setOrganizationId(organizationId);
                    marListingInfoDesc.setCreatedAt(DateUtils.getNowDate());
                    marListingInfoDescHisMapper.insert(marListingInfoDesc);
                }
            }
            //相似商品信息
            MarListingInfoSimilar consider = marListingInfo.getConsider();
            if (consider != null&&(!StringUtils.isEmpty(consider.getConsiderType())||!StringUtils.isEmpty(consider.getConsiderId())||!StringUtils.isEmpty(consider.getConsiderTitle()))) {
                MarListingInfoSimilarHis marListingInfoSimilarHis = new MarListingInfoSimilarHis();
                BeanUtils.copyProperties(consider, marListingInfoSimilarHis);
                marListingInfoSimilarHis.setConsiderAsin(consider.getConsiderId()); //ASIN
                marListingInfoSimilarHis.setAsin(asin);
                marListingInfoSimilarHis.setListingId(marListingInfoHisRecod.getId());
                marListingInfoSimilarHis.setOrganizationId(organizationId);
                marListingInfoSimilarHis.setCreatedAt(DateUtils.getNowDate());
                marListingInfoSimilarHisMapper.insert(marListingInfoSimilarHis);
            }
        } else {
            //更新历史主表
            Long listingId = marListingInfoHisList.get(0).getId();

            MarListingInfo listingInfo = this.entityNullTran(marListingInfo);
            MarListingInfoHis marListingInfoHis1 = new MarListingInfoHis();
            BeanUtils.copyProperties(listingInfo, marListingInfoHis1);
            marListingInfoHis1.setId(listingId);
            marListingInfoHis1.setUpdatedAt(DateUtils.getNowDate());
            marListingInfoHis1.setSubCategory(null); //不更新当天大小类及名称信息，由接口更新
            marListingInfoHis1.setSubCategoryRank(null);
            marListingInfoHis1.setMainCategory(null);
            marListingInfoHis1.setMainCategoryRank(null);
            marListingInfoHisMapper.updateById(marListingInfoHis1);


            //img
            List<String> images = marListingInfo.getImages();
            LambdaQueryWrapper<MarListingInfoImgHis> deleteParm = new LambdaQueryWrapper<>();
            deleteParm.eq(MarListingInfoImgHis::getListingId, listingId);
            marListingInfoImgHisMapper.delete(deleteParm); //删除
            if (!CollectionUtils.isEmpty(images)) {
                //操作图片信息
                MarListingInfoImgHis marListingInfoImg = null;
                for (String image : images) {
                    marListingInfoImg = new MarListingInfoImgHis();
                    marListingInfoImg.setImgSrc(image);
                    marListingInfoImg.setAsin(asin);
                    marListingInfoImg.setListingId(listingId);
                    marListingInfoImg.setOrganizationId(organizationId);
                    marListingInfoImg.setCreatedAt(DateUtils.getNowDate());
                    marListingInfoImgHisMapper.insert(marListingInfoImg); //插入历史图片信息
                }
            }

            //描述信息
            List<String> describe5 = marListingInfo.getDescribe5();
            LambdaQueryWrapper<MarListingInfoDescHis> deleteDescParm = new LambdaQueryWrapper<>();
            deleteDescParm.eq(MarListingInfoDescHis::getListingId, listingId);
            marListingInfoDescHisMapper.delete(deleteDescParm);
            MarListingInfoDescHis marListingInfoDesc = null;
            if (!CollectionUtils.isEmpty(describe5)) {
                for (String desc : describe5) {
                    marListingInfoDesc = new MarListingInfoDescHis();
                    marListingInfoDesc.setAsin(asin);
                    marListingInfoDesc.setListingId(listingId);
                    marListingInfoDesc.setDescInfo(desc);
                    marListingInfoDesc.setOrganizationId(organizationId);
                    marListingInfoDesc.setCreatedAt(DateUtils.getNowDate());
                    marListingInfoDescHisMapper.insert(marListingInfoDesc);
                }
            }

            //相似商品
            LambdaQueryWrapper<MarListingInfoSimilarHis> deleteSimilarDescParm = new LambdaQueryWrapper<>();
            deleteSimilarDescParm.eq(MarListingInfoSimilarHis::getListingId, listingId);
            marListingInfoSimilarHisMapper.delete(deleteSimilarDescParm); //删除当天历史信息
            MarListingInfoSimilar consider = marListingInfo.getConsider();
            if (consider != null&&(!StringUtils.isEmpty(consider.getConsiderType())||!StringUtils.isEmpty(consider.getConsiderId())||!StringUtils.isEmpty(consider.getConsiderTitle()))) {
                MarListingInfoSimilarHis marListingInfoSimilarHis = new MarListingInfoSimilarHis();
                BeanUtils.copyProperties(consider, marListingInfoSimilarHis);
                marListingInfoSimilarHis.setConsiderAsin(consider.getConsiderId());
                marListingInfoSimilarHis.setAsin(asin);
                marListingInfoSimilarHis.setListingId(listingId);
                marListingInfoSimilarHis.setOrganizationId(organizationId);
                marListingInfoSimilarHis.setCreatedAt(DateUtils.getNowDate());
                marListingInfoSimilarHisMapper.insert(marListingInfoSimilarHis);
            }

        }

    }


    /**
     * Description: 保存walmartListingInfo数据
     *
     * @param message
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/7/18
     */
    @Transactional
    @Override
    public void savaWalMartMqListingInfo(String message) {
        if (StringUtils.isEmpty(message)) {
            return;
        }
        MarListingInfo marListingInfo = JSONObject.parseObject(message, MarListingInfo.class);
        marListingInfo.setChannel("walmart");
        if (marListingInfo == null || StringUtils.isEmpty(marListingInfo.getAsin())) {
            return;
        }
        String asin = marListingInfo.getAsin();


        //将折扣价格同步至订单表
//        saleOrderItemsService.updateScOrderItemsDiscountPrice(asin, marListingInfo.getListPrice(), marListingInfo.getDiscountPrice(), marListingInfo.getUsTime());
        //判断主表ASIN是否存在
        LambdaQueryWrapper<MarListingInfo> marListingQuer = new LambdaQueryWrapper<>();
        marListingQuer.eq(MarListingInfo::getAsin, asin);
        MarListingInfo listingInfo = marListingInfoMapper.selectOne(marListingQuer);

        //获取ASIN对应的组织信息
        LambdaQueryWrapper<ProductChannels> productChannelsQuery = new LambdaQueryWrapper<>();
        productChannelsQuery.eq(ProductChannels::getAsin, asin);
        List<ProductChannels> channels = productChannelsMapper.selectList(productChannelsQuery);
        Long organizationId = CollectionUtils.isEmpty(channels) ? null : channels.get(0).getOrgId().longValue();
        marListingInfo.setMainCategoryRank(NumberUtil.isNumber(marListingInfo.getMainCategoryRank()) ? marListingInfo.getMainCategoryRank() : null);

        //时间转换 Listig上线时间转换
        if (listingInfo == null) {
            //主表
            marListingInfo.setOrganizationId(organizationId);
            marListingInfo.setCreatedAt(DateUtils.getNowDate());
            marListingInfoMapper.insert(marListingInfo);
        } else {
            MarListingInfo listingInfoOne = this.entityNullTran(marListingInfo);
            listingInfoOne.setUpdatedAt(DateUtils.getNowDate());
            listingInfoOne.setId(listingInfo.getId());
            marListingInfoMapper.updateById(listingInfoOne); //根据主键更新主表
        }

        //存储报表历史数据

        //当天只存储同一ASIN一条美国时间数据
        MarListingInfoHis marListingInfoHis = new MarListingInfoHis();
        marListingInfoHis.setAsin(asin);
        marListingInfoHis.setSyncTime(marListingInfo.getUsTime()); //美国时间
        List<MarListingInfoHis> marListingInfoHisList = marListingInfoHisMapper.selectMarListingInfoHisList(marListingInfoHis);

        //历史信息插入
        if (CollectionUtils.isEmpty(marListingInfoHisList)) {
            marListingInfo.setId(null); //去除主键

            MarListingInfoHis marListingInfoHisRecod = new MarListingInfoHis();
            BeanUtils.copyProperties(marListingInfo, marListingInfoHisRecod);
            marListingInfoHisRecod.setOrganizationId(organizationId);
            marListingInfoHisRecod.setCreatedAt(DateUtils.getNowDate());//创建时间
            marListingInfoHisMapper.insert(marListingInfoHisRecod);
        } else {
            //更新历史主表
            Long listingId = marListingInfoHisList.get(0).getId();
            MarListingInfo qListingInfo = this.entityNullTran(marListingInfo);
            MarListingInfoHis marListingInfoHis1 = new MarListingInfoHis();
            BeanUtils.copyProperties(qListingInfo, marListingInfoHis1);
            marListingInfoHis1.setId(listingId);
            marListingInfoHis1.setUpdatedAt(DateUtils.getNowDate());
            marListingInfoHisMapper.updateById(marListingInfoHis1);

        }
    }


    public  void  refreshAsin() {
     List<MarListingInfo> marListingInfos = marListingInfoHisMapper.selectUnsynchronizedAsin();

        for (MarListingInfo marListingInfo : marListingInfos) {
            try {
                marListingInfoMapper.insert(marListingInfo);
            } catch (Exception e) {
                log.info("设置子asin失败：{}", e);
            }
        }


    }
}




