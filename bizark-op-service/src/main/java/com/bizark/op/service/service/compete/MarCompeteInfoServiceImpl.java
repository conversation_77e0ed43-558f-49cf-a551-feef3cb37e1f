package com.bizark.op.service.service.compete;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bizark.op.api.cons.MQDefine;
import com.bizark.op.api.entity.op.compete.*;
import com.bizark.op.api.entity.op.gpt.ValidateAsinDataVo;
import com.bizark.op.api.entity.op.gpt.v2.ListingInfoAllAiRes;
import com.bizark.op.api.entity.op.mar.MarProductPopularizeLogAsinCurrent;
import com.bizark.op.api.entity.op.sale.Products;
import com.bizark.op.api.entity.op.tk.expert.vo.VideoExportTaskVO;
import com.bizark.op.api.service.compete.IMarCompeteInfoService;
import com.bizark.op.api.service.compete.IMarMatchCompeteInfoService;
import com.bizark.op.api.service.sale.ProductsService;
import com.bizark.op.common.core.page.PageInfoDomain;
import com.bizark.op.common.core.page.TableSupportInfo;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.common.util.UserUtils;
import com.bizark.op.service.mapper.compete.*;
import com.bizark.op.service.mapper.tabcut.TabcutCreatorVideoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 营销竞品分析Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-10-24
 */
@Service
@Slf4j
public class MarCompeteInfoServiceImpl implements IMarCompeteInfoService {

    @Autowired
    private MarCompeteInfoMapper marCompeteInfoMapper;
    @Autowired
    private MarCompeteInfoHisMapper marCompeteInfoHisMapper;
    @Autowired
    private MarCompeteImgMapper marCompeteImgMapper;
    @Autowired
    private MarCompeteImgHisMapper marCompeteImgHisMapper;
    @Autowired
    private MarCompeteSubCategoryMapper marCompeteSubCategoryMapper;
    @Autowired
    private MarCompeteSubCategoryHisMapper marCompeteSubCategoryHisMapper;
    @Autowired
    private MarCompeteVariantMapper marCompeteVariantMapper;
    @Autowired
    private MarCompeteVariantHisMapper marCompeteVariantHisMapper;
    @Autowired
    private MarCompeteQaMapper marCompeteQaMapper;
    @Autowired
    private MarCompeteAsinMapper marCompeteAsinMapper;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private MarMatchCompeteInfoMapper marMatchCompeteInfoMapper;

    @Autowired
    private IMarMatchCompeteInfoService marMatchCompeteInfoService;

    @Autowired
    private ProductsService productsService;

    @Autowired
    private MarCompeteAsinMatchSkuMapper marCompeteAsinMatchSkuMapper;

    @Autowired
    private TabcutCreatorVideoMapper tabcutCreatorVideoMapper;

    //大数据
    @Value("${home_query_bi_url}")
    private String HOME_QUERY_BI_URL;

    /**
     * 查询营销竞品分析
     *
     * @param id 营销竞品分析ID
     * @return 营销竞品分析
     */
    @Override

    public MarCompeteInfo selectMarCompeteInfoById(Long id) {
        return marCompeteInfoMapper.selectMarCompeteInfoById(id);
    }

    /**
     * 查询营销竞品分析列表
     *
     * @param marCompeteInfo 营销竞品分析
     * @return 营销竞品分析列表
     */
    @Override
    public List<MarCompeteInfo> selectMarCompeteInfoList(MarCompeteInfo marCompeteInfo) {
        List<MarCompeteInfo> marCompeteInfos = marCompeteInfoMapper.selectMarCompeteInfoList(marCompeteInfo);
        return marCompeteInfos;
    }

    /**
     * 新增营销竞品分析
     *
     * @param marCompeteInfo 营销竞品分析
     * @return 结果
     */
    @Override
    public int insertMarCompeteInfo(MarCompeteInfo marCompeteInfo) {
        marCompeteInfo.setCreatedAt(DateUtils.getNowDate());
        return marCompeteInfoMapper.insertMarCompeteInfo(marCompeteInfo);
    }

    /**
     * 修改营销竞品分析
     *
     * @param marCompeteInfo 营销竞品分析
     * @return 结果
     */
    @Override
    public int updateMarCompeteInfo(MarCompeteInfo marCompeteInfo) {
        marCompeteInfo.setUpdatedAt(DateUtils.getNowDate());
        return marCompeteInfoMapper.updateMarCompeteInfo(marCompeteInfo);
    }

    /**
     * 批量删除营销竞品分析
     *
     * @param ids 需要删除的营销竞品分析ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMarCompeteInfoByIds(Long[] ids) {
//        if (null == ids) {
//            throw new CustomException("删除数据不能为空");
//        }
//        List<String> asinList = marCompeteInfoMapper.selectMarCompeteInfoByIds(ids);
//        //删除竞品数据
//        int deleteFlag = marCompeteInfoMapper.deleteMarCompeteInfoByIds(ids);
//        //删除跟卖监控ASIN
//        if (!CollectionUtils.isEmpty(asinList)) {
//            marCompeteAsinMapper.deleteMarCompeteAsinByAsins(asinList);
//        }
//        return deleteFlag;
        return 0;
    }

    /**
     * 删除营销竞品分析信息
     *
     * @param id 营销竞品分析ID
     * @return 结果
     */
    @Override
    public int deleteMarCompeteInfoById(Long id) {
        return marCompeteInfoMapper.deleteMarCompeteInfoById(id);
    }




    /**
     * 竞品变体查询
     *
     * @param
     * @return 结果 变体信息
     */
    @Override
    public List<MarCompeteVariant> selectAsinVariant(String asin) {
        MarCompeteVariant marCompeteVariant = new MarCompeteVariant();
        marCompeteVariant.setAsin(asin);
        return marCompeteVariantMapper.selectMarCompeteVariantList(marCompeteVariant);
    }



    /**
     * 竞品QA信息
     *
     * @param
     * @return 结果 变体信息
     */
    @Override
    public List<MarCompeteQa> selectCompeteQa(MarCompeteQa marCompeteQa) {
        return marCompeteQaMapper.selectMarCompeteQaList(marCompeteQa);
    }


    /**
     * 竞品趋势信息
     *
     * @param
     * @return 结果 竞品趋势信息
     */
    @Override
    public List<CompeteSectionRes> selectCompeteTendency(CompeteRequest competeRequest) {
        if (!StringUtils.isEmpty(competeRequest.getDateFrom()) || !StringUtils.isEmpty(competeRequest.getDateTo())) {
            competeRequest.setDateType(null);
        }



        //响应结果
        List<CompeteSectionRes> competeSectionRes = new ArrayList<>();
        List<MarCompeteInfo> marCompeteInfos = marCompeteInfoMapper.selectMarCompeteInfoTendencyList(competeRequest);

        CompeteSectionRes competeSection = null;
        for (MarCompeteInfo marCompeteInfo : marCompeteInfos) {
            competeSection = new CompeteSectionRes();
            if (null != marCompeteInfo.getCaptureTime()) {
                competeSection.setCreateTime(DateUtil.format(marCompeteInfo.getCaptureTime(), "yyyy-MM-dd"));
            }
            competeSection.setPrice(marCompeteInfo.getListPrice());
            competeSection.setStarRating(marCompeteInfo.getStarRating());
            competeSection.setStarReview(marCompeteInfo.getStarReviews());
            competeSection.setMainRank(StrUtil.nullToEmpty(marCompeteInfo.getMainCategoryRank()));
            competeSection.setMainCategoryStr(StrUtil.nullToEmpty(marCompeteInfo.getMainCategory()));
            competeSectionRes.add(competeSection);
        }
        return competeSectionRes;
    }


    /**
     * 商品变动情况表
     *
     * @param
     * @return 商品变动信息
     */
    @Override
    public List<CompetefieldEntity> selectCompeteChange(MarCompeteInfo marCompeteInfo) {
        String asin = marCompeteInfo.getAsin();
        //查询主表最新
        List<CompetefieldEntity> afterCompeteInfo = marCompeteInfoMapper.selecCompeteInfoFieldContentList(asin);
        //判断空值
        List<CompetefieldEntity> filterCompete = afterCompeteInfo.stream().filter(ss -> !ss.getField().equals("asin")  && !StringUtils.isEmpty(ss.getContent())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterCompete)) {
            return filterCompete;
        }
        //查询昨日历史信息
        List<CompetefieldEntity> beforeCompeteInfo = marCompeteInfoHisMapper.selecCompeteInfoFieldContentHisList(asin, DateUtil.format(DateUtil.yesterday(), "yyyy-MM-dd"));


            for (CompetefieldEntity competefieldEntity : afterCompeteInfo) {
                //查询匹配的字段
                List<CompetefieldEntity> collect = null;
               if (!CollectionUtils.isEmpty(beforeCompeteInfo)){
                   collect = beforeCompeteInfo.stream().filter(ss -> competefieldEntity.getField().equals(ss.getField())).collect(Collectors.toList());
               }
                //与历史表有匹配的字段
                if (!CollectionUtils.isEmpty(collect)) {
                    //图片
                    if (competefieldEntity.getField().equals("img_src")) {
                        List<MarCompeteImg> marCompeteImgs = marCompeteImgMapper.selectMarCompeteImgByAsin(asin);
                        if (!CollectionUtils.isEmpty(marCompeteImgs)) {
                            competefieldEntity.setAfterContent(marCompeteImgs.stream().filter(ss -> !StringUtils.isEmpty(ss.getImgSrc())).map(MarCompeteImg::getImgSrc).collect(Collectors.joining(",")));
                        }
                        List<MarCompeteImgHis> marCompeteImgHis = marCompeteImgHisMapper.selectMarCompeteImgHisByCompeteId(beforeCompeteInfo.get(0).getId());
                        if (!CollectionUtils.isEmpty(marCompeteImgHis)) {
                            competefieldEntity.setBeforContent(marCompeteImgHis.stream().filter(ss -> !StringUtils.isEmpty(ss.getImgSrc())).map(MarCompeteImgHis::getImgSrc).collect(Collectors.joining(",")));
                        }

                        //小类
                    } else if (competefieldEntity.getField().equals("sub_category")) {

                        List<MarCompeteSubCategory> marCompeteSubCategories = marCompeteSubCategoryMapper.selectMarCompeteSubCategoryByAsin(asin);
                        //小类名称
                        if (!CollectionUtils.isEmpty(marCompeteSubCategories)) {
                            competefieldEntity.setAfterContent(marCompeteSubCategories.stream().filter(ss -> !StringUtils.isEmpty(ss.getSubCategoryStr())).map(MarCompeteSubCategory::getSubCategoryStr).collect(Collectors.joining(",")));
                        }
                        List<MarCompeteSubCategoryHis> marCompeteSubCategoryHis = marCompeteSubCategoryHisMapper.selectMarCompeteSubCategoryHisByCompeteId(beforeCompeteInfo.get(0).getId());

                        //小类历史
                        if (!CollectionUtils.isEmpty(marCompeteSubCategoryHis)) {
                            competefieldEntity.setBeforContent(marCompeteSubCategoryHis.stream().filter(ss -> !StringUtils.isEmpty(ss.getSubCategoryStr())).map(MarCompeteSubCategoryHis::getSubCategoryStr).collect(Collectors.joining(",")));
                        }
                        if (!StringUtils.isEmpty(competefieldEntity.getAfterContent()) && !StringUtils.isEmpty(competefieldEntity.getBeforContent())) {
                            competefieldEntity.setUpdateFlag(competefieldEntity.getAfterContent().equals(competefieldEntity.getBeforContent())); //更新状态
                        }

                        //其余列
                    } else {
                        competefieldEntity.setBeforContent(collect.get(0).getContent());
                        competefieldEntity.setAfterContent(competefieldEntity.getContent());
                        if (!StringUtils.isEmpty(collect.get(0).getContent())&&!StringUtils.isEmpty(competefieldEntity.getContent()))
                            competefieldEntity.setUpdateFlag(collect.get(0).getContent().equals(competefieldEntity.getContent()));
                    }
                } else {
                    //图片
                    if (competefieldEntity.getField().equals("img_src")) {
                        List<MarCompeteImg> marCompeteImgs = marCompeteImgMapper.selectMarCompeteImgByAsin(asin);
                        if (!CollectionUtils.isEmpty(marCompeteImgs)) {
                            competefieldEntity.setAfterContent(marCompeteImgs.stream().filter(ss -> !StringUtils.isEmpty(ss.getImgSrc())).map(MarCompeteImg::getImgSrc).collect(Collectors.joining(",")));
                        }
                        //小类
                    } else if (competefieldEntity.getField().equals("sub_category")) {

                        List<MarCompeteSubCategory> marCompeteSubCategories = marCompeteSubCategoryMapper.selectMarCompeteSubCategoryByAsin(asin);
                        if (!CollectionUtils.isEmpty(marCompeteSubCategories)) {
                            competefieldEntity.setAfterContent(marCompeteSubCategories.stream().filter(ss -> !StringUtils.isEmpty(ss.getSubCategoryStr())).map(MarCompeteSubCategory::getSubCategoryStr).collect(Collectors.joining(",")));
                        }
                    } else {
                        competefieldEntity.setAfterContent(competefieldEntity.getContent()); //之后
                    }
                }

        }
        return afterCompeteInfo;
    }

    /**
     * 新增ASIN数量
     *
     * @param marCompeteAsinMatchSkuList 竞品新增ASIN
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addCompeteAsin(List<MarCompeteAsinMatchSku> marCompeteAsinMatchSkuList,Long contextId) {
        //todo asin 如果已经有了的话直接提示已存在
        Set<String> checkAsinSet = marCompeteAsinMatchSkuList.stream().map(i ->i.getAsin()).collect(Collectors.toSet());//去重
        if(CollectionUtils.isNotEmpty(checkAsinSet)) {
            QueryWrapper<MarCompeteAsin> queryWrapper = new QueryWrapper();
            queryWrapper.in("asin",checkAsinSet);
            List<MarCompeteAsin> competeAsins = marCompeteAsinMapper.selectList(queryWrapper);
            if(CollectionUtils.isNotEmpty(competeAsins)) {
                throw new RuntimeException("该asin 已经添加过" + competeAsins.get(0).getAsin());
            }
        }
        //todo 校验erpsku 是不是都是有效的
        List<String> erpSkuList = marCompeteAsinMatchSkuList.stream().map(i -> i.getErpSku()).distinct().collect(Collectors.toList());
        erpSkuList.removeIf(Objects::isNull);// 去除空
        if(CollectionUtils.isNotEmpty(erpSkuList)) {
            QueryWrapper<Products> productsQueryWrapper = new QueryWrapper<>();
            productsQueryWrapper.select("distinct erpsku ");
            productsQueryWrapper.in("erpsku", erpSkuList);
            List<Products> productsList = productsService.list(productsQueryWrapper);
            if (CollectionUtil.isNotEmpty(productsList)) {
                if (erpSkuList.size() != productsList.size()) {
                    throw new RuntimeException("不允许保存不存在的erpSku");
                }
            } else {
                throw new RuntimeException("不允许保存不存在的erpSku");
            }
        }
        //去重操作
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        List<MarCompeteAsinMatchSku> marCompeteAsinMatchSkus = new ArrayList<>();
        //保存asin匹配matchSku表
        for (MarCompeteAsinMatchSku m: marCompeteAsinMatchSkuList) {
            QueryWrapper<MarCompeteAsinMatchSku> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("asin",m.getAsin());
            queryWrapper.eq(StringUtils.isNotEmpty(m.getErpSku()),"erp_sku",m.getErpSku());
            List<MarCompeteAsinMatchSku> matchSkus = marCompeteAsinMatchSkuMapper.selectList(queryWrapper);
            if (CollectionUtils.isNotEmpty(matchSkus)) {
                failureNum++;
                failureMsg.append(" <br/> " + " ASIN："+m.getAsin()+" 已在竞品监控信息表中，添加失败!");
                continue;
            }
            MarCompeteAsinMatchSku newMarCompeteAsinMatchSku = new MarCompeteAsinMatchSku();
            newMarCompeteAsinMatchSku.setAsin(m.getAsin());
            newMarCompeteAsinMatchSku.setOrganizationId(contextId);
            newMarCompeteAsinMatchSku.setErpSku(m.getErpSku());
            newMarCompeteAsinMatchSku.settingDefaultCreate();
            marCompeteAsinMatchSkus.add(m);
            marCompeteAsinMatchSkuMapper.insert(newMarCompeteAsinMatchSku);
            successNum++;
        }
        //记录发送asin 的相关信息
        List<MarCompeteAsin> marCompeteAsinList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(marCompeteAsinMatchSkus)) { //todo 不为空发mq消息通知系统抓取相关数据。
            Set<String> asinSet = marCompeteAsinMatchSkus.stream().map(i ->i.getAsin()).collect(Collectors.toSet());//去重
            //查询是否已经发送过了
            for (String asin : asinSet) {
                //新增至竞品监控信息表
                MarCompeteAsin marCompeteAsin = marCompeteAsinMapper.selectMarCompeteAsinByAsin(asin);
                if (marCompeteAsin!=null) {
                    continue;
                }
                marCompeteAsin = new MarCompeteAsin();
                marCompeteAsin.setAsin(asin);
                marCompeteAsin.setOrganizationId(contextId);
                marCompeteAsin.settingDefaultCreate();
                marCompeteAsinMapper.insertMarCompeteAsin(marCompeteAsin);
                marCompeteAsinList.add(marCompeteAsin);
            }
            //保存记录发送信息
            if(CollectionUtils.isNotEmpty(marCompeteAsinList)) {
                String s = JSONArray.toJSONStringWithDateFormat(marCompeteAsinList,"yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteDateUseDateFormat);
                log.info("发送竞品asin相关数据:{}",s);
                rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_E_ERP, MQDefine.PUSH_ASIN_INFO_KEY,s);
            }

        }
        if (failureNum > 0) {
            failureMsg.insert(0, "共 " + successNum + " 条添加成功， " + failureNum + " 条添加失败，错误如下：");
            return failureMsg.toString();
        } else {
            successMsg.insert(0, "添加成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }


    /**
     * @description:导入 添加ASIN和SKU信息
     * @author: Moore
     * @date: 2024/4/16 18:46
     * @param
     * @param marCompeteAsinMatchSkus
     * @param contextId
     * @return: java.lang.String
    **/
    @Override
    public String addCompeteAsinExport(List<MarCompeteAsinMatchSku> marCompeteAsinMatchSkus, Long contextId) {
        if (CollectionUtils.isEmpty(marCompeteAsinMatchSkus)) {
            throw new CustomException("导入数据不能为空");
        }

        marCompeteAsinMatchSkus = marCompeteAsinMatchSkus.stream().filter(item -> !StringUtils.isEmpty(item.getAsin())).collect(Collectors.toList());

        List<MarCompeteAsinMatchSku> asinFilter2 = marCompeteAsinMatchSkus.stream().filter(item -> item.getAsin().contains(",") || item.getAsin().contains("，")).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(asinFilter2)){
            throw new CustomException("ASIN列不能包含逗号");
        }

        ArrayList<MarCompeteAsinMatchSku> reqMarCompeteAsinList = new ArrayList<>();
        for (MarCompeteAsinMatchSku marCompeteAsinMatchSku : marCompeteAsinMatchSkus) {
            if (!StringUtils.isEmpty(marCompeteAsinMatchSku.getErpSku())) {
                String erpSku = marCompeteAsinMatchSku.getErpSku().replace("，", ",");
                String[] split = erpSku.split(",");
                List<String> skuList = Arrays.asList(split).stream().distinct().collect(Collectors.toList());

                for (String sku : skuList) {
                    MarCompeteAsinMatchSku marCompeteAsinMatchSkuItem = new MarCompeteAsinMatchSku();
                    marCompeteAsinMatchSkuItem.setAsin(marCompeteAsinMatchSku.getAsin());
                    marCompeteAsinMatchSkuItem.setErpSku(sku);
                    reqMarCompeteAsinList.add(marCompeteAsinMatchSkuItem);
                }

            } else {
                reqMarCompeteAsinList.add(marCompeteAsinMatchSku);
            }
        }
        //设置组织ID
        reqMarCompeteAsinList.stream().forEach(item -> item.setOrganizationId(contextId));
        return this.addCompeteAsin(reqMarCompeteAsinList, contextId);
    }


    /**
     * 竞品监控 实体类Null值转换空串操作
     *
     * @param marCompeteInfo 精品监控信息
     * @return 结果
     */
    private MarCompeteInfo entityNullTran(MarCompeteInfo marCompeteInfo) {
        marCompeteInfo.setAsinTitle(StrUtil.nullToEmpty(marCompeteInfo.getAsinTitle())); //标题
        marCompeteInfo.setMainCategory(StrUtil.nullToEmpty(marCompeteInfo.getMainCategory())); //大类名称
        marCompeteInfo.setMainCategoryRank(StrUtil.nullToEmpty(marCompeteInfo.getMainCategoryRank())); //大类排名
        //星级 // reviews
        marCompeteInfo.setBrand(StrUtil.nullToEmpty(marCompeteInfo.getBrand())); //品牌
        marCompeteInfo.setColor(StrUtil.nullToEmpty(marCompeteInfo.getColor())); //颜色
        marCompeteInfo.setSpec(StrUtil.nullToEmpty(marCompeteInfo.getSpec())); //属性（规格）
        marCompeteInfo.setListPrice(StrUtil.nullToEmpty(marCompeteInfo.getListPrice()));//售价
        marCompeteInfo.setCoupon(StrUtil.nullToEmpty(marCompeteInfo.getCoupon())); //优惠
        marCompeteInfo.setDiscount(StrUtil.nullToEmpty(marCompeteInfo.getDiscount()));//折扣
        marCompeteInfo.setShipsFrom(StrUtil.nullToEmpty(marCompeteInfo.getShipsFrom()));//发货方法
        marCompeteInfo.setSoldBy(StrUtil.nullToEmpty(marCompeteInfo.getSoldBy()));//销售方
        marCompeteInfo.setDeliveryTime(StrUtil.nullToEmpty(marCompeteInfo.getDeliveryTime()));//送达时效
        marCompeteInfo.setSalesStatus(StrUtil.nullToEmpty(marCompeteInfo.getSalesStatus()));//发货时效
        marCompeteInfo.setFreight(StrUtil.nullToEmpty(marCompeteInfo.getFreight()));//运费
        marCompeteInfo.setProductDimensions(StrUtil.nullToEmpty(marCompeteInfo.getProductDimensions()));//产品尺寸
        marCompeteInfo.setItemWeight(StrUtil.nullToEmpty(marCompeteInfo.getItemWeight()));//重量
        marCompeteInfo.setStyle(StrUtil.nullToEmpty(marCompeteInfo.getStarRating()));//风格
        marCompeteInfo.setFirstDate(StrUtil.nullToEmpty(marCompeteInfo.getFirstDate()));//Listing上线时间
        return marCompeteInfo;
    }


    /**
     * Description: 保存竞品匹配数据
     *
     * @param marMatchCompeteInfoList
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/2/1
     */
    @Transactional
    @Override
    public void saveMatchCompeteInfo(List<MarMatchCompeteInfo> marMatchCompeteInfoList) {
        if (CollectionUtil.isNotEmpty(marMatchCompeteInfoList)) {
            String source = marMatchCompeteInfoList.get(0).getSource();
            String sourceType = marMatchCompeteInfoList.get(0).getSourceType();
            Long organizationId = marMatchCompeteInfoList.get(0).getOrganizationId();
            QueryWrapper<MarMatchCompeteInfo> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("source", source);
            queryWrapper.eq("source_type", sourceType);
            queryWrapper.eq("organization_id" ,organizationId);
            queryWrapper.isNull("flag");
            List<MarMatchCompeteInfo> queryMatchCompeteInfoList = marMatchCompeteInfoMapper.selectList(queryWrapper);
            if (CollectionUtil.isEmpty(queryMatchCompeteInfoList)) {//第一次保存
                marMatchCompeteInfoService.saveBatch(marMatchCompeteInfoList);
            } else { //去重后保存（这里不做删除操作）
                //取差集
                marMatchCompeteInfoList.removeAll(queryMatchCompeteInfoList);
                marMatchCompeteInfoService.saveBatch(marMatchCompeteInfoList);//新增差集的数据
            }
        }
    }


    /**
     * @description: 根据SKU获取竞品AISN
     * @author: Moore
     * @date: 2024/3/26 18:32
     * @param
     * @param sku
     * @param dateFrom
     * @param dateTo
     * @return: java.util.List<com.bizark.op.api.entity.op.compete.MarCompeteInfo>
    **/
    @Override
    public List<MarCompeteInfo> getSkuCompeteInfo(String sku, String dateFrom, String dateTo) {
        if (StringUtils.isEmpty(sku)||StringUtils.isEmpty(dateFrom)||StringUtils.isEmpty(dateFrom)){
            return Collections.emptyList();
        }
        return marCompeteInfoMapper.selectMarCompeteAsinBySkuGroupAsin(sku,dateFrom,dateTo);
    }

    /**
     * Description: 批量删除竞品及竞品里面的所有关联关系
     *
     * @param marCompeteInfoList
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/6/24
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void competeInfoDelete(List<MarCompeteInfo> marCompeteInfoList) {
        for (MarCompeteInfo m: marCompeteInfoList) {
            //todo 删除竞品表信息
            marCompeteInfoMapper.deleteMarCompeteInfoById(m.getId());
            //todo 删除竞品asin 与sku 关联关系
            QueryWrapper<MarCompeteAsinMatchSku> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("asin",m.getAsin());
            queryWrapper.eq("organization_id",m.getOrganizationId());
            marCompeteAsinMatchSkuMapper.delete(queryWrapper);
            marCompeteAsinMapper.deleteMarCompeteAsinByAsins(Arrays.asList(m.getAsin()),m.getOrganizationId());
            //todo 这里还有疑问这个是asin  维度的asin ，还是说是其他绑定了asin的
            //todo 删除关联竞品信息
            QueryWrapper<MarMatchCompeteInfo> marMatchCompeteInfoQueryWrapper = new QueryWrapper<>();
            marMatchCompeteInfoQueryWrapper.eq("asin",m.getAsin());
            marMatchCompeteInfoQueryWrapper.eq("organization_id",m.getOrganizationId());
            marMatchCompeteInfoMapper.delete(marMatchCompeteInfoQueryWrapper);
        }
    }

    /**
     * Description: 批量删除竞品分析里面的关联关系
     *
     * @param marCompeteInfoList
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/6/24
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void analysisDelete(List<MarCompeteInfo> marCompeteInfoList) {
        for (MarCompeteInfo m: marCompeteInfoList) {
            //todo 查询关联竞品信息是否存在,如果存在则删除，否则记录，后续查询的时候筛查出来
            //todo 删除关联竞品信息
            QueryWrapper<MarMatchCompeteInfo> marMatchCompeteInfoQueryWrapper = new QueryWrapper<>();
            marMatchCompeteInfoQueryWrapper.eq("source_type",m.getSourceType());
            marMatchCompeteInfoQueryWrapper.eq("source",m.getSource());
            marMatchCompeteInfoQueryWrapper.eq("asin",m.getAsin());
            marMatchCompeteInfoQueryWrapper.eq("organization_id",m.getOrganizationId());
            marMatchCompeteInfoQueryWrapper.isNull("flag");
            int i = marMatchCompeteInfoMapper.delete(marMatchCompeteInfoQueryWrapper);
            if(i == 0) {//没有找到不是添加进来的，而是自己带过来的
                MarMatchCompeteInfo marMatchCompeteInfo = new MarMatchCompeteInfo();
                marMatchCompeteInfo.setSource(m.getSource());
                marMatchCompeteInfo.setSourceType(m.getSourceType());
                marMatchCompeteInfo.setOrganizationId(m.getOrganizationId());
                marMatchCompeteInfo.setAsin(m.getAsin());
                marMatchCompeteInfo.setFlag("-1");
                marMatchCompeteInfoService.save(marMatchCompeteInfo);
            }
        }
    }

    /**
     * @desc bi查询竟品信息
     * <AUTHOR>
     * @date 2025/5/9 17:09
     * param
     * @param marCompeteInfo
     * return
     * @return java.util.List<com.bizark.op.api.entity.op.compete.MarCompeteInfo>
     */
    @Override
    public JSONObject queryMarCompeteInfoBiList(MarCompeteInfo marCompeteInfo) {
        PageInfoDomain pageInfoDomain = TableSupportInfo.buildPageRequest();
        Integer page = pageInfoDomain.getPage();
        Integer rows = pageInfoDomain.getRows();
        String sord = pageInfoDomain.getSord();
        String sidx = pageInfoDomain.getSidx();
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("asinList",marCompeteInfo.getAsinList());
        paramMap.put("asinTitle",marCompeteInfo.getAsinTitle());
        paramMap.put("brand",marCompeteInfo.getBrand());
        paramMap.put("dateFrom",marCompeteInfo.getDateFrom());
        paramMap.put("dateTo",marCompeteInfo.getDateTo());
        paramMap.put("erpSkuList",marCompeteInfo.getErpSkuList());
//        paramMap.put("mainCategory",marCompeteInfo.getMainCategory());
        paramMap.put("organizationId",marCompeteInfo.getOrganizationId());
        paramMap.put("createdBy",marCompeteInfo.getCreatedBy());
        paramMap.put("page",page);
        paramMap.put("rows",rows);
        paramMap.put("sidx",sidx);
        paramMap.put("sord",sord);

        //String queryUrl = "http://172.16.12.117:5555/api/v1/competeInfoList";
        String queryUrl = HOME_QUERY_BI_URL + "/api/v1/competeInfoList";
        HttpRequest request = HttpUtil.createPost(queryUrl);
        log.error("bi查询竟品信息地址：{},参数{},", queryUrl,JSON.toJSONString(paramMap));
        request.body(JSON.toJSONString(paramMap));
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            throw new RuntimeException("bi查询竟品信息接口异常");
        }
        log.info("bi查询竟品信息返回结果：{}", response.body());
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        if(jsonObject != null) {
            return jsonObject;
        } else {
            throw new RuntimeException("bi查询竟品信息接口异常");
        }
    }

    /**
     * @param marCompeteInfo return
     * @desc 导出竟品数据
     * <AUTHOR>
     * @date 2025/5/12 11:33
     * param
     */
    @Override
    public void exportQueryMarCompeteInfoBiList(MarCompeteInfo marCompeteInfo) {
        PageInfoDomain pageInfoDomain = TableSupportInfo.buildPageRequest();
        String sord = pageInfoDomain.getSord();
        String sidx = pageInfoDomain.getSidx();
        marCompeteInfo.setSord(sord);
        marCompeteInfo.setSidx(sidx);
        Integer userId = UserUtils.getCurrentUserId(0); //获取当前用户
        String userName = UserUtils.getCurrentUserName("system");
        VideoExportTaskVO vo = new VideoExportTaskVO();
        vo.setExportId(userId + "-" + System.currentTimeMillis());
        marCompeteInfo.setExportId(vo.getExportId());
        this.marCompeteInfoBiListExport(marCompeteInfo);
        Date date = new Date();
        vo.setTaskNo(vo.getExportId()); //任务号
        vo.setTaskCode("erp.bi.export.MarCompeteInfo.time");
        vo.setCreatedAt(date);
        vo.setCreatedBy(userId);
        vo.setCreatedName(userName);
        vo.setUpdatedAt(date);
        vo.setUpdatedBy(userId);
        vo.setUpdatedName(userName);
        vo.setStatus("processing");
        vo.setBody(JSON.toJSONString(marCompeteInfo));
        vo.setOrgId(marCompeteInfo.getOrganizationId().intValue());
        vo.setProcessTime(date);
        vo.setTaskTitle("bi竟品信息导出");
        tabcutCreatorVideoMapper.expoertTaskInsert(vo);
    }


    void marCompeteInfoBiListExport(MarCompeteInfo marCompeteInfo) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("asinList",marCompeteInfo.getAsinList());
        paramMap.put("asinTitle",marCompeteInfo.getAsinTitle());
        paramMap.put("brand",marCompeteInfo.getBrand());
        paramMap.put("dateFrom",marCompeteInfo.getDateFrom());
        paramMap.put("dateTo",marCompeteInfo.getDateTo());
        paramMap.put("erpSkuList",marCompeteInfo.getErpSkuList());
//        paramMap.put("mainCategory",marCompeteInfo.getMainCategory());
        paramMap.put("organizationId",marCompeteInfo.getOrganizationId());
        paramMap.put("createdBy",marCompeteInfo.getCreatedBy());
        paramMap.put("sidx",marCompeteInfo.getSidx());
        paramMap.put("sord",marCompeteInfo.getSord());
        paramMap.put("exportId",marCompeteInfo.getExportId());

        //String queryUrl = "http://172.16.12.117:5555/api/v1/competeInfoExport";
        String queryUrl =  HOME_QUERY_BI_URL + "/api/v1/competeInfoExport";
        HttpRequest request = HttpUtil.createPost(queryUrl);
        log.error("bi竟品信息导出地址：{},参数{},", queryUrl,JSON.toJSONString(paramMap));
        request.body(JSON.toJSONString(paramMap));
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            throw new RuntimeException("bi竟品信息导出接口异常");
        }
        log.info("bi竟品信息导出返回结果：{}", response.body());
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        if(jsonObject == null) {
            throw new RuntimeException("bi竟品信息导出接口异常");
        }
    }

    /**
     * 查询竞品趋势图
     *
     * @param competeRequest@return 结果
     */
    @Override
    public JSONObject selectCompeteTendencyBi(Long contextId,CompeteRequest competeRequest) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("contextId",contextId);
        paramMap.put("asin",competeRequest.getAsin());
        paramMap.put("dateFrom",competeRequest.getDateFrom());
        paramMap.put("dateTo",competeRequest.getDateTo());
        //String queryUrl = "http://172.16.12.117:5555/api/v1/competeTendency";
        String queryUrl = HOME_QUERY_BI_URL + "/api/v1/competeTendency";
        HttpRequest request = HttpUtil.createPost(queryUrl);
        log.error("bi竞品趋势图地址：{},参数{},", queryUrl,JSON.toJSONString(paramMap));
        request.body(JSON.toJSONString(paramMap));
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            throw new RuntimeException("bi竞品趋势图接口异常");
        }
        log.info("bi竞品趋势图返回结果：{}", response.body());
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        if(jsonObject != null) {
           return jsonObject;
        } else {
            throw new RuntimeException("bi竞品趋势图接口异常");
        }
    }

}


