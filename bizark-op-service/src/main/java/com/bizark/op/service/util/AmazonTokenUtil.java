package com.bizark.op.service.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bizark.boss.api.entity.dashboard.amazon.AmazonApplicationEntity;
import com.bizark.op.api.cons.RedisKeyConstant;
import com.bizark.op.api.cons.SaleConstant;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.amazon.AmazonApplicationService;
import com.bizark.op.common.util.StringUtil;
import com.xxl.conf.core.XxlConfClient;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Data
@Component
@Slf4j
public class AmazonTokenUtil {

    private static final String AMAZON_O2_TOEN = "https://api.amazon.com/auth/o2/token/";

    private static final String GRANT_TYPE = "refresh_token";


    @Autowired
    private AccountService accountService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private AmazonApplicationService amazonApplicationService;

    /**
     * 获取AMZ_ACCESSTOKEN信息
     *
     * @param account
     * @param account
     * @return
     */
    public  String getToken(Account account) {
        if (account == null || StringUtil.isEmpty(account.getAmazonApplicationId())) {
            return null;
        }

        JSONObject connObj = JSON.parseObject(account.getConnectStr());
        if (connObj==null||!connObj.containsKey(SaleConstant.REFRESH_TOKEN)) {
            log.info("AMZ鉴权未获取到refreshToken信息:{}", account.getId());
            return null;
        }
        String refreshToken = connObj.getString(SaleConstant.REFRESH_TOKEN);
        //Redis中获取TOKEN
        String redisKey = RedisKeyConstant.AMAZON_ACCESS_TOKEN + account.getFlag();
//        StringRedisTemplate stringRedisTemplate = SpringBeanUtil.getBean(StringRedisTemplate.class);
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(redisKey))) {
            return stringRedisTemplate.opsForValue().get(redisKey);
        }


        //获取XXL
//        AmazonApplicationService amazonApplicationService = SpringBeanUtil.getBean(AmazonApplicationService.class);
        List<AmazonApplicationEntity> amazonApplicationEntities = amazonApplicationService.findAllByApplicationId(account.getAmazonApplicationId());
        if (CollectionUtils.isEmpty(amazonApplicationEntities)) {
            return null;
        }
        String clientId = XxlConfClient.get(amazonApplicationEntities.get(0).getClientId());
        String clientSecret = XxlConfClient.get(amazonApplicationEntities.get(0).getClientSecret());
        // 获取token
        HttpRequest authRequest = HttpUtil.createPost(AMAZON_O2_TOEN);
        Map<String, String> body = new HashMap<>();
        //TODO 生产配置，慎重操作
        body.put("client_id", "amzn1.application-oa2-client.6899b1c170064741ab5ab58be2576cd6");
        body.put("client_secret", "amzn1.oa2-cs.v1.701c0d0fbe69aae5254e0c67ec1449010a9fff83b30372bf4b96bce6ca273e75");
//        body.put("client_secret", clientSecret);
//        body.put("client_secret", clientSecret);
        body.put("grant_type", GRANT_TYPE);
        body.put("refresh_token", refreshToken);
        authRequest.body(JSON.toJSONString(body));
        HttpResponse response = authRequest.execute();
        if (!response.isOk()) {
            log.info("amz获取TOKEN失败，{},{}", account.getFlag(), response.body());
            return null;
        }
        JSONObject responseBody = JSON.parseObject(response.body());
        if (!responseBody.containsKey("access_token")) {
            log.info("amz获取TOKEN失败,未获取到AccessToken - {} - {}", account.getFlag(), responseBody);
            return null;
        }
        String accessToken = responseBody.getString("access_token");
//        Integer expiresIn = responseObj.getInteger("expires_in");
        stringRedisTemplate.opsForValue().set(redisKey, accessToken, 45, TimeUnit.MINUTES);
        return accessToken;
    }

}
