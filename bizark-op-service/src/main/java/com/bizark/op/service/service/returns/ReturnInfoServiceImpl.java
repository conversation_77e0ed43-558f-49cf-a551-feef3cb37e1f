package com.bizark.op.service.service.returns;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.boss.api.service.TaskCenterService;
import com.bizark.boss.api.vo.dashboard.TaskCenterRequest;
import com.bizark.op.api.enm.finance.FinanceErrorEnum;
import com.bizark.op.api.enm.mar.ReturnBusinessStatus;
import com.bizark.op.api.entity.op.document.ConfTicketProblem;
import com.bizark.op.api.entity.op.returns.VO.ReturnReasonPageVO;
import com.bizark.op.api.entity.op.returns.entity.ReturnInfoEntity;
import com.bizark.op.api.entity.op.ticket.VO.ReturnInfoAnalysisVo;
import com.bizark.op.api.entity.op.ticket.VO.ReturnInfoEntityVo;
import com.bizark.op.api.entity.op.ticket.customercomplaint.query.CustomerComplaintMainQuery;
import com.bizark.op.api.entity.op.tk.expert.vo.VideoExportTaskVO;
import com.bizark.op.api.service.returns.ReturnInfoService;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.AssertUtil;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.common.util.UserUtils;
import com.bizark.op.service.mapper.returns.ReturnInfoMapper;
import com.bizark.op.service.mapper.tabcut.TabcutCreatorVideoMapper;
import com.bizark.op.service.mapper.ticket.ConfTicketProblemMapper;
import com.bizark.op.service.util.ConvertUtils;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023年9月19日  14:56
 * @description:
 */
@Slf4j
@Service
public class ReturnInfoServiceImpl extends ServiceImpl<ReturnInfoMapper,ReturnInfoEntity > implements ReturnInfoService  {

    @Autowired
    ReturnInfoMapper returnInfoMapper;

    @Autowired
    private ConfTicketProblemMapper confTicketProblemMapper;

    @Autowired
    private TaskCenterService taskCenterService;

    @Autowired
    private TabcutCreatorVideoMapper tabcutCreatorVideoMapper;

    @Value("${home_query_bi_url}")
    private  String QUERY_SALE_NUM_MASTER_URL;


    @Override
    public List<ReturnInfoEntity> getReturnInfoPageList(ReturnInfoEntityVo returnInfoEntityVo) {
        PageHelper.startPage(returnInfoEntityVo.getPageNum(), returnInfoEntityVo.getPageSize());
        String orderByKey = returnInfoEntityVo.getOrderByKey();
        if (StringUtil.isNotEmpty(orderByKey)) {
            orderByKey = orderByKey.replaceAll("([A-Z])", "_$1").toLowerCase();
            returnInfoEntityVo.setOrderByKey(orderByKey);
        }
        //处理无商品ID
        if (CollectionUtil.isNotEmpty(returnInfoEntityVo.getAsinList()) && "无商品ID".equals(returnInfoEntityVo.getAsinList().get(0))) {
            returnInfoEntityVo.setWithoutAsin("1");
        }
        //处理无sku
        if (CollectionUtil.isNotEmpty(returnInfoEntityVo.getSkuList()) && "无sku".equals(returnInfoEntityVo.getSkuList().get(0))) {
            returnInfoEntityVo.setWithoutSku("1");
        }

        //处理无品类
        if (CollectionUtil.isNotEmpty(returnInfoEntityVo.getCategoryIdList()) && new Integer(-1).equals(returnInfoEntityVo.getCategoryIdList().get(0))) {
            returnInfoEntityVo.setWithoutCategory("1");
        }

        //处理无退货原因
        if (CollectionUtil.isNotEmpty(returnInfoEntityVo.getProblemIdList()) && new Integer(-1).equals(returnInfoEntityVo.getProblemIdList().get(0))) {
            returnInfoEntityVo.setWithoutProblem("1");
        }
        //处理无平台退货原因
        if (StringUtils.isNotEmpty(returnInfoEntityVo.getReturnReasonList()) && "无平台退货原因".equals(returnInfoEntityVo.getReturnReasonList().get(0))) {
            returnInfoEntityVo.setWithoutReturnReason("1");
        }
        List<ReturnInfoEntity> returnInfoList = returnInfoMapper.getReturnInfoList(returnInfoEntityVo);
        return returnInfoList;
    }



    @Override
    public void updateRemark(Integer contextId, Integer id, String remark) {
        returnInfoMapper.updateRemarkById(id, remark);
    }

    @Override
    public List<ReturnReasonPageVO> getReturnReasonPage(Integer contextId, Integer pageSize, Integer pageNum, String returnReason) {
        PageHelper.startPage(pageNum, pageSize);
        return returnInfoMapper.getReturnReasonPage(contextId, returnReason);
    }

    @Override
    public void updateReturnReasonCategory(Integer contextId, String returnReason, String returnReasonCategory, UserEntity authUserEntity) {
        returnInfoMapper.updateReturnReasonCategory(contextId, returnReason, returnReasonCategory, authUserEntity.getId(), authUserEntity.getName(), LocalDateTime.now());
    }

    /**
     * Description: 退货原因任务导出
     *
     * @param response
     * @param returnInfoEntityVo
     * @param authUserEntity
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/8/30
     */
    @Override
    public void returnInfoListExport(HttpServletResponse response, ReturnInfoEntityVo returnInfoEntityVo, UserEntity authUserEntity) {
        TaskCenterRequest request = new TaskCenterRequest();
        request.setTaskCode("erp.returnInfoListExport.export");
        List<Object> list = new ArrayList<>();
        list.add(JSON.toJSONString(returnInfoEntityVo));
        request.setArgs(list);
        if (returnInfoEntityVo.getContextId() != null) {
            request.setOrgId(returnInfoEntityVo.getContextId());
        }
        this.taskCenterService.startTask(request, authUserEntity);
    }



    /**
     *  退货方式：退货方式分析导出
     * @param returnInfoAnalysisVo
     */
    @Override
    public void analysisMethodReport(ReturnInfoAnalysisVo returnInfoAnalysisVo) {
        Integer userId = UserUtils.getCurrentUserId(0); //获取当前用户
        String userName = UserUtils.getCurrentUserName("system");
        VideoExportTaskVO vo = new VideoExportTaskVO();
        vo.setExportId(userId + "-" + System.currentTimeMillis());
        returnInfoAnalysisVo.setExportId(vo.getExportId());
        this.useReturnMethodsAnalysisExportApi(returnInfoAnalysisVo);
        Date date = new Date();
        vo.setTaskNo(vo.getExportId()); //任务号
        vo.setTaskCode("erp.bi.export.returnInfo.method.analysisReport.time");
        vo.setCreatedAt(date);
        vo.setCreatedBy(userId);
        vo.setCreatedName(userName);
        vo.setUpdatedAt(date);
        vo.setUpdatedBy(userId);
        vo.setUpdatedName(userName);
        vo.setTaskCode(UUID.randomUUID().toString().replace("-", ""));
        vo.setStatus("processing");
        vo.setBody(JSONObject.toJSONString(returnInfoAnalysisVo));
        vo.setOrgId(returnInfoAnalysisVo.getOrganizationId().intValue());
        vo.setProcessTime(date);
        vo.setTaskTitle("退货方式分析导出");
        tabcutCreatorVideoMapper.expoertTaskInsert(vo);
    }

    void useReturnMethodsAnalysisExportApi(ReturnInfoAnalysisVo returnInfoAnalysisVo) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("organizationId", returnInfoAnalysisVo.getOrganizationId());
        paramMap.put("startTime", returnInfoAnalysisVo.getStartTime());
        paramMap.put("endTime", returnInfoAnalysisVo.getEndTime());
        paramMap.put("organizationId", returnInfoAnalysisVo.getOrganizationId());
        paramMap.put("exportId",returnInfoAnalysisVo.getExportId());
        String url = QUERY_SALE_NUM_MASTER_URL + "/api/v1/ReturnMethodsAnalysisExport";
        HttpRequest request = HttpUtil.createPost(url);
        request.setReadTimeout(120 * 1000);
        request.body(JSON.toJSONString(paramMap));
        log.info("调用退货方式分析导出接口请求参数：{}", JSON.toJSONString(paramMap));
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            log.error("调用退货方式分析导出接口异常:{}", JSONObject.toJSONString(response));
            throw new CustomException("调用退货方式分析导出接口异常");
        }
    }


    /**
     * 换货信息分页
     *
     * @param returnInfoEntityVo
     * @return
     */
    @Override
    public List<ReturnInfoEntity> getReplaceInfoPageList(ReturnInfoEntityVo returnInfoEntityVo) {
        PageHelper.startPage(returnInfoEntityVo.getPageNum(), returnInfoEntityVo.getPageSize());
        String orderByKey = returnInfoEntityVo.getOrderByKey();
        if (StringUtil.isNotEmpty(orderByKey)) {
            orderByKey = orderByKey.replaceAll("([A-Z])", "_$1").toLowerCase();
            returnInfoEntityVo.setOrderByKey(orderByKey);
        }
        //处理无商品ID
        if (CollectionUtil.isNotEmpty(returnInfoEntityVo.getAsinList()) && "无商品ID".equals(returnInfoEntityVo.getAsinList().get(0))) {
            returnInfoEntityVo.setWithoutAsin("1");
        }
        //处理无sku
        if (CollectionUtil.isNotEmpty(returnInfoEntityVo.getSkuList()) && "无sku".equals(returnInfoEntityVo.getSkuList().get(0))) {
            returnInfoEntityVo.setWithoutSku("1");
        }

        //处理无品类
        if (CollectionUtil.isNotEmpty(returnInfoEntityVo.getCategoryIdList()) && new Integer(-1).equals(returnInfoEntityVo.getCategoryIdList().get(0))) {
            returnInfoEntityVo.setWithoutCategory("1");
        }

        //处理无退货原因
        if (CollectionUtil.isNotEmpty(returnInfoEntityVo.getProblemIdList()) && new Integer(-1).equals(returnInfoEntityVo.getProblemIdList().get(0))) {
            returnInfoEntityVo.setWithoutProblem("1");
        }
        //处理无平台退货原因
        if (StringUtils.isNotEmpty(returnInfoEntityVo.getReturnReasonList()) && "无平台退货原因".equals(returnInfoEntityVo.getReturnReasonList().get(0))) {
            returnInfoEntityVo.setWithoutReturnReason("1");
        }

        List<ReturnInfoEntity> returnInfoList = returnInfoMapper.getReplaceInfoList(returnInfoEntityVo);
        return returnInfoList;
    }
    /**
     * Description: 退货原因任务导出
     *
     * @param response
     * @param returnInfoEntityVo
     * @param authUserEntity
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/8/30
     */
    @Override
    public void replaceInfoListExport(HttpServletResponse response, ReturnInfoEntityVo returnInfoEntityVo, UserEntity authUserEntity) {
        TaskCenterRequest request = new TaskCenterRequest();
        request.setTaskCode("erp.replaceInfoListExport.export");
        List<Object> list = new ArrayList<>();
        list.add(JSON.toJSONString(returnInfoEntityVo));
        request.setArgs(list);
        if (returnInfoEntityVo.getContextId() != null) {
            request.setOrgId(returnInfoEntityVo.getContextId());
        }
        this.taskCenterService.startTask(request, authUserEntity);
        AssertUtil.isFail(FinanceErrorEnum.ASNYC_EXPORT_GENERATED);
    }





}
