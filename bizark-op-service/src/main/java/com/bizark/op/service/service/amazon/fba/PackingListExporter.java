package com.bizark.op.service.service.amazon.fba;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.google.common.collect.Maps;
import lombok.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

public class PackingListExporter {

    public static void exportPackingList(
            File file,
            String groupId,
            String packingMethod,
            List<SkuItem> skuItems,
            List<BoxData> boxLists, int type) {

        if (file == null || skuItems == null || boxLists == null) {
            throw new IllegalArgumentException("关键参数不能为空: file, skuItems, boxLists");
        }

        HorizontalCellStyleStrategy styleStrategy = createStyleStrategy();

        try (ExcelWriter excelWriter = EasyExcel.write(file).build()) {
            // 判断是否需要显示有效期列
            boolean showExpiryDate = boxLists.stream()
                    .anyMatch(box -> box.getExpiryDate() != null && !box.getExpiryDate().isEmpty());
            Map<String, Integer> boxIndex = Maps.newHashMap();
            List<List<String>> head = createHeader(groupId, packingMethod, boxLists, showExpiryDate, type, boxIndex);
            List<List<Object>> data = createData(skuItems, boxLists, showExpiryDate, boxIndex);

            WriteSheet writeSheet = EasyExcel.writerSheet("Sheet1")
                    .registerWriteHandler(styleStrategy)
                    .build();

            excelWriter.write(head, writeSheet);
            excelWriter.write(data, writeSheet);

            // 提取 sheet 对象，避免链式调用过长
            WriteSheetHolder writeSheetHolder = excelWriter.writeContext().writeSheetHolder();
            if (writeSheetHolder == null) {
                throw new IllegalStateException("WriteSheetHolder 为 null，无法获取 Sheet");
            }
            Sheet sheet = writeSheetHolder.getSheet();

            // 设置样式
            setFirstRowStyle(sheet);
            setThirdRowStyle(sheet);
            setSummaryRowStyle(sheet, skuItems.size());

            // 设置公式
            setFormulas(sheet, skuItems.size(), boxLists.size(), showExpiryDate);

            // 合并单元格
            mergeCells(excelWriter, writeSheet, data.size(), skuItems.size(), showExpiryDate);

            // 设置列宽
            setColumnWidth(sheet, boxLists.size(), showExpiryDate);
        } catch (Exception e) {
            // 建议添加日志记录
            throw new RuntimeException("导出装箱单失败", e);
        }
    }

    private static void setFormulas(Sheet sheet, int dataRowsCount, int boxCount, boolean showExpiryDate) {
        // 合计行在第 3(表头) + dataRowsCount(数据行) 行
        int summaryRowIndex = 3 + dataRowsCount;
        Row summaryRow = sheet.getRow(summaryRowIndex);

        if (summaryRow == null) return;

        int firstDataRow = 4; // Excel中的第4行
        int lastDataRow = 3 + dataRowsCount; // 最后一个数据行的行号

        // 设置发货数量合计公式 (F列，索引5)
        Cell fCell = summaryRow.getCell(5);
        if (fCell != null) {
            fCell.setCellFormula("SUM(F" + firstDataRow + ":F" + lastDataRow + ")");
        }

        // 设置已装箱数合计公式 (G列，索引6)
        Cell gCell = summaryRow.getCell(6);
        if (gCell != null) {
            gCell.setCellFormula("SUM(G" + firstDataRow + ":G" + lastDataRow + ")");
        }

        // 设置每个箱子数量的合计公式
        for (int i = 0; i < boxCount; i++) {
            // 计算箱子数量所在的列索引 (H列是第7列索引)
            int quantityColumnIndex = 7 + (i * (showExpiryDate ? 2 : 1));

            Cell cell = summaryRow.getCell(quantityColumnIndex);
            if (cell != null) {
                String columnReference = getColumnReference(quantityColumnIndex);
                cell.setCellFormula("SUM(" + columnReference + firstDataRow + ":" + columnReference + lastDataRow + ")");
            }
        }
    }

    /**
     * 将列索引转换为Excel列引用（如 A, B, ..., Z, AA, AB, ...）
     *
     * @param columnIndex 列索引（从0开始）
     * @return Excel列引用
     */
    private static String getColumnReference(int columnIndex) {
        if (columnIndex < 26) {
            return String.valueOf((char) ('A' + columnIndex));
        } else {
            int firstChar = (columnIndex / 26) - 1;
            int secondChar = columnIndex % 26;
            return String.valueOf((char) ('A' + firstChar)) + String.valueOf((char) ('A' + secondChar));
        }
    }

    private static void setThirdRowStyle(Sheet sheet) {
        Row thirdRow = sheet.getRow(2);
        if (thirdRow != null) {
            for (Cell cell : thirdRow) {
                CellStyle style = sheet.getWorkbook().createCellStyle();
                Font font = sheet.getWorkbook().createFont();
                font.setFontHeightInPoints((short) 10); // 字体大小
                font.setBold(true); // 加粗
                style.setFont(font);
                style.setAlignment(HorizontalAlignment.CENTER); // 居中对齐
                style.setVerticalAlignment(VerticalAlignment.CENTER);
                style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
                style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                cell.setCellStyle(style);
            }
        }
    }

    private static void setSummaryRowStyle(Sheet sheet, int dataRowsCount) {
        // 合计行在第 3(表头) + dataRowsCount(数据行) 行
        int summaryRowIndex = 3 + dataRowsCount;
        Row summaryRow = sheet.getRow(summaryRowIndex);

        if (summaryRow != null) {
            for (Cell cell : summaryRow) {
                CellStyle style = sheet.getWorkbook().createCellStyle();
                Font font = sheet.getWorkbook().createFont();
                font.setFontName("宋体"); // 设置为宋体
                font.setFontHeightInPoints((short) 10); // 字体大小
                font.setBold(true); // 加粗
                style.setFont(font);
                style.setAlignment(HorizontalAlignment.CENTER); // 居中对齐
                style.setVerticalAlignment(VerticalAlignment.CENTER);
                style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
                style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                cell.setCellStyle(style);
            }
        }
    }

    private static void setFirstRowStyle(org.apache.poi.ss.usermodel.Sheet sheet) {
        Row firstRow = sheet.getRow(0);
        if (firstRow != null) {
            Cell cell = firstRow.getCell(0);
            if (cell != null) {
                CellStyle style = sheet.getWorkbook().createCellStyle();
                Font font = sheet.getWorkbook().createFont();
                font.setFontHeightInPoints((short) 16); // 字体放大
                font.setBold(true);
                style.setFont(font);
                style.setAlignment(HorizontalAlignment.LEFT); // 左对齐
                style.setVerticalAlignment(VerticalAlignment.CENTER);
                cell.setCellStyle(style);
            }
        }
    }

    private static void setColumnWidth(Sheet sheet, int size, boolean showExpiryDate) {
        sheet.setColumnWidth(0, 15 * 256); // A列
        sheet.setColumnWidth(1, 25 * 256); // B列
        sheet.setColumnWidth(2, 20 * 256); // C列
        sheet.setColumnWidth(3, 20 * 256); // D列
        sheet.setColumnWidth(4, 20 * 256); // E列
        sheet.setColumnWidth(5, 15 * 256); // F列
        sheet.setColumnWidth(6, 15 * 256); // G列

        int columnCount = showExpiryDate ? size * 2 : size;
        for (int i = 7; i < 7 + columnCount; i++) {
            sheet.setColumnWidth(i, 15 * 256);
        }
    }

    private static HorizontalCellStyleStrategy createStyleStrategy() {
        WriteCellStyle headStyle = new WriteCellStyle();
        headStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        WriteFont headFont = new WriteFont();
        headFont.setFontName("宋体"); // 设置为宋体
        headFont.setFontHeightInPoints((short) 10);
        headStyle.setWriteFont(headFont);
        headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        WriteCellStyle contentStyle = new WriteCellStyle();
        WriteFont contentFont = new WriteFont();
        contentFont.setFontName("宋体"); // 设置为宋体
        contentFont.setFontHeightInPoints((short) 10);
        contentStyle.setWriteFont(contentFont);
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        WriteCellStyle summaryStyle = new WriteCellStyle();
        summaryStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        WriteFont summaryFont = new WriteFont();
        summaryFont.setFontName("宋体"); // 设置为宋体
        summaryFont.setFontHeightInPoints((short) 10);
        summaryStyle.setWriteFont(summaryFont);
        summaryStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        return new HorizontalCellStyleStrategy(headStyle, contentStyle);
    }

    private static List<List<String>> createHeader(String groupId, String packingMethod, List<BoxData> boxList, boolean showExpiryDate, int type, Map<String, Integer> boxIndex) {
        List<List<String>> head = new ArrayList<>();

        // 第一行表头
        head.add(Collections.singletonList(StrUtil.format("{}的装箱清单", packingMethod)));

        // 第二行表头
        head.add(Arrays.asList(type == 1 ? "Group_ID" : "SHIPMENT_ID", groupId, "装箱方式", packingMethod));

        // 第三行表头
        List<String> headerRow = new ArrayList<>();
        headerRow.addAll(Arrays.asList("序号", "SellerSKU", "FNSKU", "品名", "SKU", "发货数量", "已装箱数"));

        // 使用LinkedHashMap保持插入顺序的同时去重
        Map<String, BoxData> uniqueBoxMap = new LinkedHashMap<>();
        for (BoxData boxData : boxList) {
            if (!uniqueBoxMap.containsKey(boxData.getBoxId())) {
                uniqueBoxMap.put(boxData.getBoxId(), boxData);
            }
        }

        int i = 1;
        for (Map.Entry<String, BoxData> entry : uniqueBoxMap.entrySet()) {
            headerRow.add("第" + i + "箱");
            boxIndex.put(entry.getKey(), headerRow.size());
            if (showExpiryDate) {
                headerRow.add("第" + i + "箱-有效期");
            }
            i++;
        }
        head.add(headerRow);

        return head;
    }

    private static List<List<Object>> createData(
            List<SkuItem> skuItems,
            List<BoxData> boxLists,
            boolean showExpiryDate, Map<String, Integer> boxIndex) {

        Map<String, List<BoxData>> boxMap = boxLists.stream().collect(Collectors.groupingBy(BoxData::getSellerSku));
        List<List<Object>> data = new ArrayList<>();
        int offset = 0;
        //记录每个箱子的位置
        Map<String, BoxIndex> boxIndexMap = Maps.newHashMap();
        for (int i = 0; i < skuItems.size(); i++) {
            SkuItem item = skuItems.get(i);
            List<Object> row = new ArrayList<>();

            row.add(i + 1);
            row.add(item.getSellerSku());
            row.add(item.getFnSku());
            row.add(item.getProductName());
            row.add(item.getSku());
            row.add(item.getShipmentQuantity());
            row.add(item.getBoxedQuantity());

            List<BoxData> boxData = boxMap.get(item.getSellerSku());
            if (CollUtil.isNotEmpty(boxData)) {
                int size = boxIndex.size();
                for (int j = 0; j < size; j++) {
                    row.add("");
                }
                //添加偏移
                for (BoxData box : boxData) {
                    int index = boxIndex.get(box.getBoxId()) - 1;
                    row.add(index, box.getBoxQuantity());
                    if (showExpiryDate) {
                        row.add(index + 1, box.getExpiryDate());
                    }
                }
            }
            data.add(row);
        }

        data.add(createSummaryRow(boxLists, showExpiryDate));

        data.add(createBoxPropertyRow("Weight of box (kg)", boxLists, showExpiryDate));
        data.add(createBoxPropertyRow("Box length (cm)", boxLists, showExpiryDate));
        data.add(createBoxPropertyRow("Box width (cm)", boxLists, showExpiryDate));
        data.add(createBoxPropertyRow("Box height (cm)", boxLists, showExpiryDate));

        return data;
    }

    private static List<Object> createBoxInfo(List<BoxData> boxLists) {
        return null;
    }

    private static List<Object> createSummaryRow(List<BoxData> boxDataList, boolean showExpiryDate) {
        List<Object> row = new ArrayList<>();
        row.add(""); // 序号
        row.add(""); // SellerSKU
        row.add(""); // FNSKU
        row.add(""); // 品名
        row.add("合计"); // SKU
        int size = boxDataList.stream().collect(Collectors.groupingBy(BoxData::getBoxId)).size();
        // 使用Excel公式计算总发货数量和已装箱数
        // 数据行从第4行开始(索引3)，到第(3 + skuItems.size() - 1)行结束
        int firstDataRow = 4; // Excel中的第4行
        int lastDataRow = 3 + size; // 最后一个数据行的行号

        // F列是发货数量列，G列是已装箱数列
        row.add("SUM(F" + firstDataRow + ":F" + lastDataRow + ")"); // 发货数量合计公式
        row.add("SUM(G" + firstDataRow + ":G" + lastDataRow + ")"); // 已装箱数合计公式

        // 为每个箱子列添加合计公式
        // 箱子数量从H列开始，每两个列为一个箱子(数量和有效期)
        for (int i = 0; i < size; i++) {
            // 计算箱子数量所在的列索引 (H列是第7列索引)
            int quantityColumnIndex = 7 + (i * (showExpiryDate ? 2 : 1));
            char quantityColumnChar = (char) ('A' + quantityColumnIndex);

            // 添加合计公式
            String formula = "SUM(" + quantityColumnChar + firstDataRow + ":" + quantityColumnChar + lastDataRow + ")";
            row.add(formula); // 第N箱数量合计公式

            if (showExpiryDate) {
                row.add(""); // 第N箱-有效期留空
            }
        }

        return row;
    }

    private static List<Object> createBoxPropertyRow(String label, List<BoxData> boxList, boolean showExpiryDate) {
        List<Object> row = new ArrayList<>();
        row.add(""); // 序号
        row.add(""); // SellerSKU
        row.add(""); // FNSKU
        row.add(""); // 品名位置放置标签
        row.add(label); // SKU
        row.add(""); // 发货数量
        row.add(""); // 已装箱数
        int size = boxList.stream().collect(Collectors.groupingBy(BoxData::getBoxId)).size();
        for (int i = 0; i < size; i++) {
            BoxData boxData = boxList.get(i);
            // 根据label类型返回不同的箱属性值
            switch (label) {
                case "Weight of box (kg)":
                    row.add(boxData.getBoxWeightKg());
                    break;
                case "Box length (cm)":
                    row.add(boxData.getBoxLengthCm());
                    break;
                case "Box width (cm)":
                    row.add(boxData.getBoxWidthCm());
                    break;
                case "Box height (cm)":
                    row.add(boxData.getBoxHeightCm());
                    break;
                default:
                    row.add("");
                    break;
            }

            if (showExpiryDate) {
                row.add(""); // 有效期留空
            }
        }

        return row;
    }

    private static List<Double> extractDimension(List<double[]> boxDimensions, int dimensionIndex) {
        List<Double> result = new ArrayList<>();
        for (double[] dimension : boxDimensions) {
            if (dimension != null && dimension.length > dimensionIndex) {
                result.add(dimension[dimensionIndex]);
            } else {
                result.add(0.0);
            }
        }
        return result;
    }

    private static void mergeCells(ExcelWriter excelWriter, WriteSheet writeSheet, int dataSize, int skuItemsCount, boolean showExpiryDate) {
        Sheet sheet = excelWriter.writeContext().writeSheetHolder().getSheet();

        clearExistingMergedRegions(sheet);

        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 6));

        // 计算箱属性行的行索引
        // 表头: 3行 (0-2)
        // 数据行: skuItemsCount行 (3 到 3+skuItemsCount-1)
        // 合计行: 1行 (3+skuItemsCount)
        // 箱属性行从合计行之后开始
        int boxPropertyStartRow = 3 + skuItemsCount + 1;

        // 合并箱属性行的前3列 (A-C列，索引0-2)
        sheet.addMergedRegion(new CellRangeAddress(boxPropertyStartRow, boxPropertyStartRow, 4, 6));     // Weight of box (kg) 行
        sheet.addMergedRegion(new CellRangeAddress(boxPropertyStartRow + 1, boxPropertyStartRow + 1, 4, 6)); // Box length (cm) 行
        sheet.addMergedRegion(new CellRangeAddress(boxPropertyStartRow + 2, boxPropertyStartRow + 2, 4, 6)); // Box width (cm) 行
        sheet.addMergedRegion(new CellRangeAddress(boxPropertyStartRow + 3, boxPropertyStartRow + 3, 4, 6)); // Box height (cm) 行
    }

    private static void clearExistingMergedRegions(org.apache.poi.ss.usermodel.Sheet sheet) {
        int mergedRegionsCount = sheet.getNumMergedRegions();

        for (int i = mergedRegionsCount - 1; i >= 0; i--) {
            sheet.removeMergedRegion(i);
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class SkuItem {
        private String sellerSku;
        private String fnSku;
        private String boxId;
        private String productName;
        private String sku;
        private Integer shipmentQuantity;
        private Integer boxedQuantity;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class BoxData {
        private String boxId;
        private String sellerSku;
        private Integer boxQuantity;
        private BigDecimal boxLengthCm;
        private BigDecimal boxWidthCm;
        private BigDecimal boxHeightCm;
        private BigDecimal boxWeightKg;
        private String expiryDate;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class BoxIndex {
        private Integer row;
        private Integer offset;
    }

//    public static void main(String[] args) {
//        List<SkuItem> skuItems = new ArrayList<>();
//        List<BoxData> boxList = new ArrayList<>();
//
//        // 定义SKU基本信息
//        String[] sellerSkus = {"ACO-SNTC3L-GN", "COLAU-CZB-MBWT", "PROD-XYZ123", "ITEM-ABC456", "ITEM-DEF789",
//                "GOODS-GHI012", "MERCH-JKL345", "WARE-MNO678", "STOCK-PQR901", "SUPPLY-STU234"};
//        String[] fnSkus = {"X004N1H67X", "B0DX1XHF15", "B0XXXXXX01", "B0XXXXXX02", "B0XXXXXX03",
//                "B0XXXXXX04", "B0XXXXXX05", "B0XXXXXX06", "B0XXXXXX07", "B0XXXXXX08"};
//        String[] productNames = {"Product A", "Product B", "Product C", "Product D", "Product E",
//                "Product F", "Product G", "Product H", "Product I", "Product J"};
//        String[] skus = {"SKU-001", "SKU-002", "SKU-003", "SKU-004", "SKU-005",
//                "SKU-006", "SKU-007", "SKU-008", "SKU-009", "SKU-010"};
//
//        // 生成10个SKU数据
//        for (int i = 0; i < 10; i++) {
//            SkuItem item = new SkuItem();
//            item.setSellerSku(sellerSkus[i]);
//            item.setFnSku(fnSkus[i]);
//            item.setProductName(productNames[i]);
//            item.setSku(skus[i]);
//            item.setBoxedQuantity(100 + i * 10); // 逐渐增加的数量
//            item.setShipmentQuantity(100 + i * 10);
//            skuItems.add(item);
//        }
//
//        // 为每个SKU生成3个箱子数据
//        for (int i = 0; i < 10; i++) {
//            String sellerSku = sellerSkus[i];
//
//            // 为当前SKU生成3个箱子
//            for (int j = 0; j < 3; j++) {
//                BoxData boxData = BoxData.builder()
//                        .sellerSku(sellerSku)
//                        .boxHeightCm(80.0 + j * 5)     // 80, 85, 90
//                        .boxLengthCm(100.0 + j * 5)    // 100, 105, 110
//                        .boxWidthCm(90.0 + j * 5)      // 90, 95, 100
//                        .boxQuantity(10 + j * 5)       // 10, 15, 20
//                        .boxWeightKg(14.5 + j * 2)     // 14.5, 16.5, 18.5
////                        .expiryDate("2026-12-" + String.format("%02d", (j + 1) * 10)) // 2026-12-10, 2026-12-20, 2026-12-30
//                        .build();
//                boxList.add(boxData);
//            }
//        }
//
//        exportPackingList(
//                new File("/Users/<USER>/Downloads/output.xlsx"), // 保存路径
//                "c86084-13f-42e3-ba41-6a94237a", // Group ID
//                "每箱一款SKU", // 装箱方式
//                skuItems,
//                boxList,
//                1);
//    }
}
