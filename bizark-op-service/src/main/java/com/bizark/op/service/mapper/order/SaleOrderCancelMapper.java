package com.bizark.op.service.mapper.order;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bizark.boss.api.entity.dashboard.deliverorder.DeliverOrderEntity;
import com.bizark.op.api.entity.op.order.SaleOrderCancel;
import com.bizark.op.api.entity.op.order.vo.SaleOrderCancelDeliverVo;
import com.bizark.op.api.entity.op.order.vo.SaleOrderCancelOrderIdMapNumVo;
import com.bizark.op.api.entity.op.order.vo.SaleOrderCancelVo;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @Entity 订单取消表
 */
public interface SaleOrderCancelMapper extends BaseMapper<SaleOrderCancel> {


    /**
     * @description: 获取订单取消黄钻涂改
     * @author: Moore
     * @date: 2023/10/26 15:11
     * @param
     * @param sourceId
     * @return: com.bizark.op.api.entity.op.order.SaleOrderCancel
    **/
    SaleOrderCancelVo selectOrderCancelBySaleOrder(Long sourceId);


    /**
     * @param
     * @param trackingNo 多个以逗号分隔
     * @param handleType 1 召回 2 更换地址 3 在途异常
     * @description: 查询获取物流异动单号  (弃用，调整为RPC调用)
     * @author: Moore
     * @date: 2023/11/1 22:54
     * @return: java.util.List<org.json.JSONObject>
     **/
    List<JSONObject> selectTrackingChangeOrderByTrackingNos(@Param("trackingNo") String trackingNo, @Param("handleType") Integer handleType);


    /**
     * @description: 关联查询工单信息表
     * @author: Moore
     * @date: 2023/11/6 17:37
     * @param
     * @param saleOrderCancelRecod
     * @return:
    **/
    List<SaleOrderCancel> selectListByTicket(SaleOrderCancel saleOrderCancelRecod);


    /**
     * @description: 根据订单号查询发货订单
     * @author: Moore
     * @date: 2023/12/21 13:56
     * @param
     * @param orderId
     * @return: java.util.List<com.bizark.boss.api.entity.dashboard.deliverorder.DeliverOrderEntity>
    **/
    List<DeliverOrderEntity> selectDeliverOrders(@Param("orderId") Long orderId);


    /**
     * @param
     * @description: 查询取消及明细信息
     * @author: Moore
     * @date: 2024/4/2 13:36
     * @return: com.bizark.op.api.entity.op.order.SaleOrderItems
     **/
    List<SaleOrderCancel> selectCancelItems(@Param("shopId") Long shopId);

    /**
     * @Description: 查收满足条件的自动同意或拒绝订单取消信息
     * @Author: wly
     * @Date: 2024/6/19 19:00
     * @Params: [date]
     * @Return: java.util.List<com.bizark.op.api.entity.op.order.vo.SaleOrderCancelVo>
     **/
    List<SaleOrderCancelVo> selectAutoAgreeOrRejectOrderCancel(@Param("date") Date date);

    /**
     * @Description:根据店铺id查询主体
     * @Author: wly
     * @Date: 2024/6/19 19:01
     * @Params: [shopId]
     * @Return: java.lang.String
     **/
    String selectRegisterCompanyName(@Param("shopId") Long shopId);

    List<SaleOrderCancel> querySyncWayfairOrderCancel(Long id);

    /**
     * @Description:根据订单明细id查询发货单跟踪号物流商
     * @Author: wly
     * @Date: 2024/7/10 11:08
     * @Params: [idList]
     * @Return: java.util.List<com.bizark.op.api.entity.op.order.vo.SaleOrderCancelDeliverVo>
     **/
    List<SaleOrderCancelDeliverVo> selectSaleOrderCancelDeliverVoList(@Param("idList") List<Integer> idList);

    /**
     * @Description:根据订单id查询订单需要发货的数量和
     * @Author: wly
     * @Date: 2024/7/10 16:16
     * @Params: [idList]
     * @Return: java.util.List<com.bizark.op.api.entity.op.order.vo.SaleOrderCancelOrderIdMapNumVo>
     **/
    List<SaleOrderCancelOrderIdMapNumVo> selectSaleOrderItemProductsList(@Param("idList")List<Long> idList);

    /**
     * @Description:根据订单id查询实际的发货数量和
     * @Author: wly
     * @Date: 2024/7/10 16:16
     * @Params: [idList]
     * @Return: java.util.List<com.bizark.op.api.entity.op.order.vo.SaleOrderCancelOrderIdMapNumVo>
     **/
    List<SaleOrderCancelOrderIdMapNumVo> selectDeliverOrderitemList(@Param("idList")List<Long> idList);

    List<SaleOrderCancel> selectCanceling(@Param("date") Date date);

    List<ProductChannels> selectSellerSkuByCancelItemNumberAndShopId(@Param("cancelItemNumberList") List<String> cancelItemNumberList, @Param("shopId") Long shopId);

    /**
     * 根据订单id、店铺id、sellerSku 完全一致查询订单取消记录
     * @param orderId
     * @param shopId
     * @param sellerSku
     * @return
     */
    SaleOrderCancel selectSaleOrderCancelByOrderIdAndShopIdAndChannelItemId(@Param("orderId") Long orderId, @Param("shopId") Long shopId, @Param("channelItemId") String channelItemId, @Param("sellerSku")String sellerSku);

    /**
     * 根据订单id,店铺id,渠道订单行ID查询订单取消记录
     * @param orderId
     * @param shopId
     * @param channelItemId
     * @return
     */
    SaleOrderCancel selectSaleOrderCancelByOrderIdAndShopIdAndItemId(@Param("orderId") Long orderId, @Param("shopId") Long shopId, @Param("channelItemId") String channelItemId);
}




