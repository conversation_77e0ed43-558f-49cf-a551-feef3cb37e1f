package com.bizark.op.service.service.amazon.fba;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.op.api.entity.op.amazon.fba.DO.MarStaPackingSkuDO;
import com.bizark.op.api.entity.op.amazon.fba.MarStaPackingSkus;
import com.bizark.op.api.entity.op.amazon.fba.VO.MarFnSkuPrintVO;
import com.bizark.op.api.entity.op.amazon.fba.VO.MarStaPackingSkuVO;
import com.bizark.op.api.service.amazon.fba.MarStaPackingSkusService;
import com.bizark.op.service.mapper.amazon.fba.MarStaPackingSkusMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*
*/
@Service
public class MarStaPackingSkusServiceImpl extends ServiceImpl<MarStaPackingSkusMapper, MarStaPackingSkus>
implements MarStaPackingSkusService {



    /**
     * @description: STA任务组下SellerSku信息 一对多封装
     * @author: Moore
     * @date: 2025/8/6 19:23
     * @param
     * @param inboundPlanId
     * @return: java.util.List<com.bizark.op.api.entity.op.amazon.fba.VO.MarStaPackingSkuVO>
    **/
    @Override
    public List<MarStaPackingSkuDO> selectSkusByCartonSpec(String inboundPlanId) {
        return this.baseMapper.selectSkusByCartonSpec(inboundPlanId);
    }


    /**
     * @description: 获取FNSKU信息
     * @author: Moore
     * @date: 2025/8/29 11:40
     * @param
     * @param ids
     * @return: java.util.List<com.bizark.op.api.entity.op.amazon.fba.VO.MarFnSkuPrintVO.SellerSkuBean>
    **/
    @Override
    public List<MarFnSkuPrintVO.SellerSkuBean> selectPrintFnsku(List<Long> ids) {
        List<MarFnSkuPrintVO.SellerSkuBean> fnSkuPrint = this.baseMapper.selectPrintFnsku(ids);
        return fnSkuPrint;
    }


}
