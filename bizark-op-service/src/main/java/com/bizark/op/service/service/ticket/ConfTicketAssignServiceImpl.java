package com.bizark.op.service.service.ticket;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.boss.api.entity.dashboard.AccountEntity;
import com.bizark.boss.api.service.AccountService;
import com.bizark.common.contract.AuthUserDetails;
import com.bizark.op.api.cons.ConfTicketAssignConstant;
import com.bizark.op.api.cons.ScTicketConstant;
import com.bizark.op.api.enm.ticket.ScTicketTypeEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.order.SaleOrders;
import com.bizark.op.api.entity.op.refund.OrderRefund;
import com.bizark.op.api.entity.op.sale.AmzOrderV;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.entity.op.ticket.*;
import com.bizark.op.api.exception.ErpCommonException;
import com.bizark.op.api.service.order.SaleOrderItemsService;
import com.bizark.op.api.service.order.SaleOrdersService;
import com.bizark.op.api.service.sale.ProductChannelsService;
import com.bizark.op.api.service.sys.ISysDictDataService;
import com.bizark.op.api.service.ticket.*;
import com.bizark.op.common.core.domain.sys.SysDictData;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.*;
import com.bizark.op.service.mapper.account.AccountMapper;
import com.bizark.op.service.mapper.refund.OrderRefundMapper;
import com.bizark.op.service.mapper.sale.AmzOrderVMapper;
import com.bizark.op.service.mapper.sys.SysDictDataMapper;
import com.bizark.op.service.mapper.ticket.ConfTicketAssignMapper;
import com.bizark.op.service.mapper.ticket.MerchantEmailMapper;
import com.bizark.op.service.mapper.ticket.ScTicketHandleMapper;
import com.bizark.op.service.util.ExcelTemplateUtils;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.bizark.usercenter.api.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 工单分配规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-18
 */
@Service
@Slf4j
public class ConfTicketAssignServiceImpl extends ServiceImpl<ConfTicketAssignMapper, ConfTicketAssign> implements IConfTicketAssignService {
    @Autowired
    private ConfTicketAssignMapper confTicketAssignMapper;

    @Autowired
    private SaleOrdersService saleOrdersService;

//    @Autowired
//    private AmzShopMapper shopMapper;
//
//    @Autowired
//    private IScGoodsSkuMapService skuMapService;
//
//    @Autowired
//    private ScGoodsSkuMapMapper scGoodsSkuMapMapper;
//
//    @Autowired
//    private SysUserMapper userMapper;

    @Autowired
    @Lazy
    private IScTicketService scTicketService;

    @Autowired
    private ScTicketHandleMapper scTicketHandleMapper;

    @Autowired
    private IScTicketHandleService scTicketHandleService;

    @Autowired
    private AmzOrderVMapper amzOrderVMapper;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private IScTicketLogService scTicketLogService;

    @Autowired
    private ISyncEmailInfoService isyncEmailInfoService;

    @Autowired
    private IScOrderRefundApplyService scOrderRefundApplyService;

    // 数据字段
    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private UserService userService;

    @Autowired
    private SaleOrderItemsService saleOrderItemService;

    @Autowired
    private ProductChannelsService productChannelsService;

    @Autowired
    private OrderRefundMapper orderRefundMapper;

    @Autowired
    private SysDictDataMapper sysDictDataMapper;

    @Autowired
    private MerchantEmailMapper merchantEmailMapper;

    @Autowired
    private AccountMapper accountMapper;

    /**
     * 查询工单分配规则
     *
     * @param assignId 工单分配规则ID
     * @return 工单分配规则
     */
    @Override
    public ConfTicketAssign selectConfTicketAssignById(Long assignId) {
        return confTicketAssignMapper.selectConfTicketAssignById(assignId);
    }


    /**
     * @param
     * @param orgId
     * @param ticketType
     * @description: 根据工单类型查询工单处理人
     * @author: Moore
     * @date: 2023/11/7 16:24
     * @return: com.bizark.op.api.entity.op.ticket.ConfTicketAssign
     **/
    @Override
    public ConfTicketAssign selectConfTicketAssignByTicketType(Long orgId, String ticketType) {
        ConfTicketAssign confTicketAssign = new ConfTicketAssign();
        confTicketAssign.setOrganizationId(orgId); //组织ID
        confTicketAssign.setTicketType(ticketType); //工单类型
        List<ConfTicketAssign> confTicketAssigns = confTicketAssignMapper.selectConfTicketAssignList(confTicketAssign);
        if (CollectionUtils.isEmpty(confTicketAssigns)) {
            return null;
        }
        return confTicketAssigns.get(0);
    }

    /**
     * 查询工单分配规则列表
     *
     * @param confTicketAssign 工单分配规则
     * @return 工单分配规则
     */
    @Override
    public List<ConfTicketAssign> selectConfTicketAssignList(ConfTicketAssign confTicketAssign) {
        return confTicketAssignMapper.selectConfTicketAssignList(confTicketAssign);
    }

    @Override
    public List<ConfTicketAssign> selectConfTicketAssignListV(ConfTicketAssign confTicketAssign) {
        return confTicketAssignMapper.selectConfTicketAssignJoinList(confTicketAssign);
    }

    /**
     * 邮件相关处理人设置
     *
     * @param
     */
    @Override
    public void getTicketHandlersNewVersion(List<ScTicket> scTicketList, Long orgId, Integer userId) {

        if (CollectionUtils.isEmpty(scTicketList)) {
            return;
        }

        //过滤是否可以生成工单
        List<ScTicket> filterTicketList = scTicketList.stream().filter(item -> (scTicketService.scTicketPriorityAndOpenStatusSet(item.getOrganizationId(), item))).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filterTicketList)) {
            return;
        }

        //设置处理人ID
        filterTicketList.forEach(item -> {
            if (StringUtils.isEmpty(item.getHandlerBy())) {
                item.setUserId(this.getTicketHandleNewVersion(item)); //设置处理人ID
            }
        });

        // 过滤处理人不为空数据  设置部门及处理人名称
        //根据处理人分组
        Map<Long, List<ScTicket>> handlerByIdGroup = filterTicketList.stream().filter(item -> null != item.getUserId()).collect(Collectors.groupingBy(ScTicket::getUserId));
        //设置处理人对应部门 及 处理人名称
        this.settingSysUserInfoById(handlerByIdGroup);
        // 设置工单中 GoodsSku
        List<ScTicket> orderTickets = filterTicketList.stream()
                .filter(item -> !StringUtils.isEmpty(item.getAmazonOrderId()))
                .collect(Collectors.toList());
        Map<String, List<ScTicket>> amazonOrderGroup = orderTickets.stream()
                .collect(Collectors.groupingBy(ScTicket::getAmazonOrderId));
        if (CollectionUtil.isNotEmpty(amazonOrderGroup)) {
            List<AmzOrderV> amzOrderVs = amzOrderVMapper.selectAmzOrderByAmazonOrderIds(amazonOrderGroup.keySet());
            if (CollectionUtil.isNotEmpty(amzOrderVs)) {
                Map<String, List<ScTicket>> group = filterTicketList.stream()
                        .collect(Collectors.groupingBy(
                                item -> item.getAmazonOrderId() + item.getOrganizationId()
                        ));
                for (AmzOrderV orderV : amzOrderVs) {
                    if (group.containsKey(orderV.getId() + orderV.getOrganizationId())) {
                        amazonOrderGroup.get(orderV.getId()).forEach(
                                item -> item.setGoodsSku(orderV.getSellerSku())
                        );
                    }
                }
            }
        }


        // 设置操作状态
        List<ScTicket> opTickets = filterTicketList.stream()
                .filter(item -> StringUtils.isEmpty(item.getOperateStatus()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(opTickets)) {
            opTickets.forEach(
                    item -> item.setOperateStatus(ScTicketConstant.TICKET_OPERATE_PROCESS)
            );
        }

        log.error("开始插入工单，工单数量：{}", filterTicketList.size());
        long start = System.currentTimeMillis();

        int count = scTicketService.insertBatch(filterTicketList);
        long end = System.currentTimeMillis();
        log.error("插入工单完毕，条目：{}，执行完毕，耗时：{}", count, end - start);

        //子工单保存父工单处理信息，便于查询统计

        //过滤父ASIN不为空数据
        List<ScTicket> parentTickets = filterTicketList.stream()
                .filter(item -> ObjectUtil.isNotEmpty(item.getParentTicketId()))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(parentTickets)) {
            List<Long> ticketIds = parentTickets.stream()
                    .map(ScTicket::getId)
                    .collect(Collectors.toList());

            List<ScTicketHandle> scTicketHandles = scTicketHandleMapper.selectScTicketHandleByTicketIds(ticketIds);
            if (CollectionUtil.isNotEmpty(scTicketHandles)) {
                Date date = DateUtils.getNowDate();
                List<ScTicketHandle> handles = scTicketHandles.stream().map(item -> {
                    item.setParentHandleId(item.getId());
                    item.setTicketId(item.getTicketId());
                    item.setId(null);
                    try {
//                        getAuthUserEntity().getOrgId()
                        item.setOrganizationId(orgId);
                        item.setCreatedBy(userId);
                    } catch (Exception e) {
                        item.setCreatedBy(-1);
                    }
                    item.setCreatedAt(date);
                    return item;
                }).collect(Collectors.toList());
                int batchCount = scTicketHandleMapper.insertBatchHandle(handles);
                log.error("父工单处理信息插入完毕，条目：{}", batchCount);
            }
        }

        // 生成通知  添加日志
        taskExecutor.execute(() ->
        {
            for (ScTicket ticket : filterTicketList) {
                ScTicketLog ticketLog = new ScTicketLog();
                ticketLog.setTicketId(ticket.getId());
                ticketLog.setOperateType("Create");
                ticketLog.setOperateTime(new Date());
                ticketLog.setOperateContent("生成工单");
                scTicketLogService.insertScTicketLog(ticketLog);
            }
        });
    }

    @Override
    public void getTicketHandlersNewVersion(List<ScTicket> scTicketList) {
        if (CollectionUtils.isEmpty(scTicketList)) {
            return;
        }

        //过滤是否可以生成工单
        List<ScTicket> filterTicketList = scTicketList.stream().filter(item -> (scTicketService.scTicketPriorityAndOpenStatusSet(item.getOrganizationId(), item))).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filterTicketList)) {
            return;
        }

        //设置处理人ID
        filterTicketList.forEach(item -> {
            if (org.springframework.util.StringUtils.isEmpty(item.getHandlerBy())) {
                item.setUserId(this.getTicketHandleNewVersion(item)); //设置处理人ID
            }
        });

        // 过滤处理人不为空数据  设置部门及处理人名称
        //根据处理人分组
        Map<Long, List<ScTicket>> handlerByIdGroup = filterTicketList.stream().filter(item -> null != item.getUserId()).collect(Collectors.groupingBy(ScTicket::getUserId));
        //设置处理人对应部门 及 处理人名称
        this.settingSysUserInfoById(handlerByIdGroup);

        // 设置工单中 GoodsSku
        List<ScTicket> orderTickets = filterTicketList.stream()
                .filter(item -> !org.springframework.util.StringUtils.isEmpty(item.getAmazonOrderId()))
                .collect(Collectors.toList());
        Map<String, List<ScTicket>> amazonOrderGroup = orderTickets.stream()
                .collect(Collectors.groupingBy(ScTicket::getAmazonOrderId));
        if (CollectionUtil.isNotEmpty(amazonOrderGroup)) {
            for (Map.Entry<String, List<ScTicket>> entry : amazonOrderGroup.entrySet()) {
                String key = entry.getKey();
                List<ScTicket> tickets = entry.getValue();
                Long shopId = tickets.get(0).getShopId();
                SaleOrders order = saleOrdersService.lambdaQuery()
                        .eq(SaleOrders::getChannelId, shopId)
                        .eq(SaleOrders::getOrderNo, key)
                        .one();

                if (Objects.isNull(order)) {
                    continue;
                }
                String sku = saleOrderItemService.getSkuByOrgIdAndOrderNo(order.getOrgId().intValue(), order.getOrderNo());

                for (ScTicket ticket : tickets) {
                    ticket.setGoodsSku(sku);
                }
//                Map<String, List<ScTicket>> group = tickets.stream()
//                        .collect(Collectors.groupingBy(
//                                item -> item.getAmazonOrderId() + item.getOrganizationId()
//                        ));
//                for (AmzOrderV orderV : amzOrderVs) {
//                    if (group.containsKey(orderV.getAmazonOrderId() + orderV.getOrganizationId())) {
//                        amazonOrderGroup.get(orderV.getAmazonOrderId()).forEach(
//                                item -> item.setGoodsSku(orderV.getGoodsSku())
//                        );
//                    }
//                }

            }

        }


        // 设置操作状态
        List<ScTicket> opTickets = filterTicketList.stream()
                .filter(item -> org.springframework.util.StringUtils.isEmpty(item.getOperateStatus()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(opTickets)) {
            opTickets.forEach(
                    item -> item.setOperateStatus(ScTicketConstant.TICKET_OPERATE_PROCESS)
            );
        }

        log.error("开始插入工单，工单数量：{}", filterTicketList.size());
        long start = System.currentTimeMillis();

        int count = scTicketService.insertBatch(filterTicketList);
        long end = System.currentTimeMillis();
        log.error("插入工单完毕，条目：{}，执行完毕，耗时：{}", count, end - start);

        //子工单保存父工单处理信息，便于查询统计

        //过滤父ASIN不为空数据
        List<ScTicket> parentTickets = filterTicketList.stream()
                .filter(item -> !org.springframework.util.StringUtils.isEmpty(item.getParentTicketId()))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(parentTickets)) {
            List<Long> ticketIds = parentTickets.stream()
                    .map(ScTicket::getId)
                    .collect(Collectors.toList());

            List<ScTicketHandle> scTicketHandles = scTicketHandleMapper.selectScTicketHandleByTicketIds(ticketIds);
            if (CollectionUtil.isNotEmpty(scTicketHandles)) {
                Date date = DateUtils.getNowDate();
                List<ScTicketHandle> handles = scTicketHandles.stream().map(item -> {
                    item.setParentHandleId(item.getId());
                    item.setTicketId(item.getTicketId());
                    item.setId(null);
                    try {
                        AuthUserDetails principal = (AuthUserDetails) SecurityUtils.getSubject().getPrincipal();
                        Integer orgId = principal.getOrgId();
                        item.setOrganizationId(orgId.longValue());
                        item.setCreatedBy(principal.getId());
                        item.setCreatedName(principal.getName());
                    } catch (Exception e) {
                        item.setCreatedBy(-1);
                    }
                    item.setCreatedAt(date);
                    return item;
                }).collect(Collectors.toList());
                int batchCount = scTicketHandleMapper.insertBatchHandle(handles);
                log.error("父工单处理信息插入完毕，条目：{}", batchCount);
            }
        }

        // 生成通知  添加日志
        taskExecutor.execute(() ->
        {
            for (ScTicket ticket : filterTicketList) {
                ScTicketLog ticketLog = new ScTicketLog();
                ticketLog.setTicketId(ticket.getId());
                ticketLog.setOperateType("Create");
                ticketLog.setOperateTime(new Date());
                ticketLog.setOperateContent("生成工单");
                scTicketLogService.insertScTicketLog(ticketLog);
            }
        });
    }

    /**
     * 查询处理人列表(批量设置使用)
     *
     * @param
     */
    @Override
    public List<UserEntity> selectConfTicketAssignHandlerByList(Long organizationId) {
        ConfTicketAssign confTicketAssign = new ConfTicketAssign();
        confTicketAssign.setOrganizationId(organizationId);
        List<Long> handleByIds = confTicketAssignMapper.selectConfTicketAssignList(confTicketAssign).stream().filter(item -> item.getHandleById() != null).map(ConfTicketAssign::getHandleById).distinct().collect(Collectors.toList());
        List<UserEntity> handleUsers = new ArrayList<>();
        for (Long handleById : handleByIds) {
            UserEntity user = userService.getById(Math.toIntExact(handleById));
            if (user != null) {
                handleUsers.add(user);
            }

        }
        return handleUsers;
    }


    /**
     * 查询工单分配规则列表
     *
     * @param organizationId 组织ID
     */
    @Override
    public ScTicketRuleRes selectConfTicketAllotList(Long organizationId) {
        //查询所有配置
        List<ConfTicketAssign> confTicketAssigns = confTicketAssignMapper.selectConfTickeAllotList(organizationId); //admin用户也只查自己的配置
        //根据
        Map<String, List<ConfTicketAssign>> resList = confTicketAssigns.stream().filter(item -> !StringUtils.isEmpty(item.getSegmentType())).collect(Collectors.groupingBy(ConfTicketAssign::getSegmentType));
        ScTicketRuleRes<ConfTicketAssign> resRulePriorityList = new ScTicketRuleRes<>();
        resList.forEach((Key, value) -> {
            if (Key.equals("1")) { //客服
                resRulePriorityList.setCustomer(value);
            } else if (Key.equals("2")) { //运营
                resRulePriorityList.setOperation(value);
            } else if (Key.equals("3")) { //退款
                resRulePriorityList.setRefund(value);
            } else if (Key.equals("4")) { //发货
                resRulePriorityList.setShipping(value);
            }
        });
        return resRulePriorityList;
    }


    public ScTicketRuleRes selectConfTicketAllotListNewVersion(Long organizationId) {

        //查询所有配置 基础查询
        List<ConfTicketAssign> confTicketAssigns = confTicketAssignMapper.selectConfTickeAllotList(organizationId); //admin用户也只查自己的配置

        //退款类型，拆分多种类型操作（支付宝，微信...）
        List<ConfTicketAssign> refendTypeList = confTicketAssigns.stream().filter(item -> ScTicketTypeEnum.OUTSIDE_REFUND.getValue().equals(item.getTicketType())).collect(Collectors.toList());
        //退款方式字典
        SysDictData sysDictData = new SysDictData();
        sysDictData.setDictType("sc_refund_way"); //获取所有渠道
        List<SysDictData> sysDictDataInfo = dictDataService.selectDictDataList(sysDictData);
        List<String> refundTypeDictList = sysDictDataInfo.stream().map(SysDictData::getDictValue).collect(Collectors.toList());
        //退款只有一条，且无退款类型
        if (refendTypeList.size() == 1 && StringUtils.isEmpty(refendTypeList.get(0).getRefundType())) {
            for (int i = 0; i < refundTypeDictList.size(); i++) {
                if (i == 0) {
                    refendTypeList.get(0).setRefundType(refundTypeDictList.get(i)); //将默认的一行进行设置值
                } else {
                    ConfTicketAssign confTicketAssign = new ConfTicketAssign();
                    BeanUtil.copyProperties(refendTypeList.get(0), confTicketAssign);
                    confTicketAssign.setId(null);
                    confTicketAssign.setTicketType(ScTicketTypeEnum.OUTSIDE_REFUND.getValue());
                    confTicketAssign.setRefundType(refundTypeDictList.get(i));
                    confTicketAssigns.add(confTicketAssign);
                }
            }
        }

        //对同一工单 同一店铺 不同处理人进行合并行操作
        ArrayList<ConfTicketAssign> confTicketAssignsRes = this.confTicketAssignShopEmail(confTicketAssigns);
        Map<String, List<ConfTicketAssign>> resList = confTicketAssignsRes.stream().filter(item -> !StringUtils.isEmpty(item.getSegmentType())).collect(Collectors.groupingBy(ConfTicketAssign::getSegmentType));
        ScTicketRuleRes<ConfTicketAssign> resRulePriorityList = new ScTicketRuleRes<>();
        resList.forEach((Key, value) -> {
            if (Key.equals("1")) { //客服
                resRulePriorityList.setCustomer(value.stream().sorted(Comparator.comparing(ConfTicketAssign::getTicketType, Comparator.reverseOrder())).collect(Collectors.toList()));
            } else if (Key.equals("2")) { //运营
                resRulePriorityList.setOperation(value.stream().sorted(Comparator.comparing(ConfTicketAssign::getTicketType, Comparator.reverseOrder())).collect(Collectors.toList()));
            } else if (Key.equals("3")) { //退款
                resRulePriorityList.setRefund(value.stream().sorted(Comparator.comparing(ConfTicketAssign::getTicketType, Comparator.reverseOrder())).collect(Collectors.toList()));
            } else if (Key.equals("4")) { //发货
                resRulePriorityList.setShipping(value.stream().sorted(Comparator.comparing(ConfTicketAssign::getTicketType, Comparator.reverseOrder())).collect(Collectors.toList()));
            }
        });
        return resRulePriorityList;
    }


    /*其他店铺没有站外退款列表*/
    public ArrayList<ConfTicketAssign> confTicketAssignShopEmail(List<ConfTicketAssign> confTicketAssigns) {
        //查询出的所有项 且无处理人
        List<ConfTicketAssign> handelNullList = confTicketAssigns.stream().filter(item -> null == item.getHandleById()).collect(Collectors.toList());
        //有处理人项目
        List<ConfTicketAssign> confTicketAssignList = confTicketAssigns.stream().filter(item -> null != item.getHandleById()).collect(Collectors.toList());

        //根据工单类型分组
        Map<String, List<ConfTicketAssign>> ticketType = confTicketAssignList.stream().collect(
                Collectors.groupingBy(ConfTicketAssign::getTicketType));

        //重新封装工单分配项
        ArrayList<ConfTicketAssign> confTicketAssignsRecod = new ArrayList<>();

        ticketType.forEach((ticketTypeName, confTicketAssignsList) -> {
            //此类工单只会有一个处理人
            if (ScTicketTypeEnum.INSIDE_REFUND.getValue().equals(ticketTypeName) || //站外退款
                    ScTicketTypeEnum.OUTSIDE_REFUND.getValue().equals(ticketTypeName) || //站内退款
                    ScTicketTypeEnum.REISSUE_SHIPMENT.getValue().equals(ticketTypeName) || //订单发货
                    ScTicketTypeEnum.REISSUE_GOODS.getValue().equals(ticketTypeName) || //补发商品
                    ScTicketTypeEnum.REISSUE_ACCESSORIES.getValue().equals(ticketTypeName)//补发配件
            ) {
                confTicketAssignsRecod.addAll(confTicketAssignsList);
            } else {
                //其余根据名字，进行一对多操作
                Map<Long, List<ConfTicketAssign>> collect2 = confTicketAssignsList.stream().collect(
                        Collectors.groupingBy(ConfTicketAssign::getHandleById));
                collect2.forEach((handelBy, confLists) -> {
                    ConfTicketAssign confTicketAssign = confLists.get(0);

                    //店铺操作
                    List<Long> handleByIds = confLists.stream().filter(item -> null != item.getShopId()).map(ConfTicketAssign::getShopId).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(handleByIds)) {
                        confTicketAssign.setShopIds(handleByIds);
                    }
                    //邮箱操作
                    List<String> emails = confLists.stream().filter(item -> !StringUtils.isEmpty(item.getEmail())).map(ConfTicketAssign::getEmail).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(emails)) {
                        confTicketAssign.setEmails(emails);
                    }
                    confTicketAssignsRecod.add(confTicketAssign);
                });
            }
        });


        //操作处理人为空的项
        if (!CollectionUtils.isEmpty(handelNullList)) {

            //根据工单类型分组
            Map<String, List<ConfTicketAssign>> nullHandleById = handelNullList.stream().collect(
                    Collectors.groupingBy(ConfTicketAssign::getTicketType));


            //只有一个处理人项
            nullHandleById.forEach((ticketTypeName, value) -> {
                if (ScTicketTypeEnum.INSIDE_REFUND.getValue().equals(ticketTypeName) || //站外退款
                        ScTicketTypeEnum.OUTSIDE_REFUND.getValue().equals(ticketTypeName) || //站内退款
                        ScTicketTypeEnum.REISSUE_SHIPMENT.getValue().equals(ticketTypeName) || //订单发货
                        ScTicketTypeEnum.REISSUE_GOODS.getValue().equals(ticketTypeName) || //补发商品
                        ScTicketTypeEnum.REISSUE_ACCESSORIES.getValue().equals(ticketTypeName)//补发配件
                ) {
                    confTicketAssignsRecod.addAll(value); //无需设置
                } else if (ScTicketTypeEnum.EMAIL.getValue().equals(ticketTypeName)) {  //站外信，根据邮箱分配
                    //邮箱操作
                    List<String> emails = value.stream().filter(item -> !StringUtils.isEmpty(item.getEmail())).map(ConfTicketAssign::getEmail).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(emails)) {
                        ConfTicketAssign confTicketAssignhis = value.get(0);
                        ConfTicketAssign confTicketAssign = new ConfTicketAssign();
                        BeanUtils.copyProperties(confTicketAssignhis, confTicketAssign);
                        confTicketAssign.setTicketType(ScTicketTypeEnum.EMAIL.getValue()); //工单类型
                        confTicketAssign.setTicketTypeMain("ticket_type");
                        confTicketAssign.setEmails(emails);//邮箱
                        confTicketAssignsRecod.add(confTicketAssign);
                    } else {
                        confTicketAssignsRecod.addAll(value);
                    }
                } else {
                    //店偶操作
                    List<Long> shopIds = value.stream().filter(item -> null != item.getShopId()).map(ConfTicketAssign::getShopId).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(shopIds)) {
                        ConfTicketAssign confTicketAssignhis = value.get(0);
                        ConfTicketAssign confTicketAssign = new ConfTicketAssign();
                        BeanUtils.copyProperties(confTicketAssignhis, confTicketAssign);
                        confTicketAssign.setTicketType(ticketTypeName);//工单类型
                        confTicketAssign.setTicketTypeMain("ticket_type");
                        confTicketAssign.setShopIds(shopIds);//店偶IDs
                        confTicketAssignsRecod.add(confTicketAssign);
                    } else {
                        confTicketAssignsRecod.addAll(value);
                    }
                }

            });


//            //站外信，过滤邮箱不为空的项
//            List<ConfTicketAssign> emailAndHandleByList = handelNullList.stream().filter(item -> ScTicketTypeEnum.EMAIL.getValue().equals(item.getTicketType())).collect(Collectors.toList());
//
//            if (!CollectionUtils.isEmpty(emailAndHandleByList)) {
//                List<String> emailList = emailAndHandleByList.stream().filter(item -> !StringUtils.isEmpty(item.getEmail())).map(ConfTicketAssign::getEmail).collect(Collectors.toList());
//                if (!CollectionUtils.isEmpty(emailList)) {
//                    ConfTicketAssign confTicketAssignRecod = emailAndHandleByList.get(0);
//                    ConfTicketAssign confTicketAssign = new ConfTicketAssign();
//                    confTicketAssign.setTicketTypeMain(confTicketAssignRecod.getTicketTypeMain());
//                    confTicketAssign.setTicketType(confTicketAssignRecod.getTicketType());
//                    confTicketAssign.setEmails(emailList);
//                    confTicketAssignsRecod.add(confTicketAssign);
//
//                }
//            }
//            //操作店铺项，店铺不能为空的项
//            List<ConfTicketAssign> shopAndHandleByList = handelNullList.stream().filter(item ->
//                    !ScTicketTypeEnum.INSIDE_REFUND.getValue().equals(item.getTicketType())
//                            && !ScTicketTypeEnum.OUTSIDE_REFUND.getValue().equals(item.getTicketType())
//                            && !ScTicketTypeEnum.REISSUE_SHIPMENT.getValue().equals(item.getTicketType())
//                            && !ScTicketTypeEnum.REISSUE_GOODS.getValue().equals(item.getTicketType())
//                            && !ScTicketTypeEnum.REISSUE_ACCESSORIES.getValue().equals(item.getTicketType())
//                            && !ScTicketTypeEnum.EMAIL.getValue().equals(item.getTicketType()) //判断店铺是否设置
//            ).collect(Collectors.toList());
//
//            if (!CollectionUtils.isEmpty(shopAndHandleByList)) {
//                //根据分类分组
//                Map<String, List<ConfTicketAssign>> collect = shopAndHandleByList.stream().collect(
//                        Collectors.groupingBy(ConfTicketAssign::getTicketType));
//                collect.forEach((ticketTypeOne,value)->{
//                    List<Long> shopIds = value.stream().filter(item -> null != item.getShopId()).map(ConfTicketAssign::getShopId).collect(Collectors.toList());
//                    if (!CollectionUtils.isEmpty(shopIds)) {
//                        ConfTicketAssign confTicketAssign = new ConfTicketAssign();
//                        confTicketAssign.setTicketTypeMain("sc_ticket_type");
//                        confTicketAssign.setTicketType(ticketTypeOne);
//                        confTicketAssign.setShopIds(shopIds);
//                        confTicketAssignsRecod.add(confTicketAssign);
//                    }
//                });
//            }


        }

//      confTicketAssignsRecod.addAll(handelNullList);
        return confTicketAssignsRecod;
    }


    /**
     * 批量修改 工单分配配置
     *
     * @param confTicketAssign 组织ID
     */
    @Override
    @Transactional
    public int updateConfTicketAssignList(UserEntity authUserEntity, List<ConfTicketAssign> confTicketAssign) {
        List<ConfTicketAssign> confTicketAssignList = confTicketAssign.stream().filter(ss -> StringUtils.isEmpty(ss.getTicketType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(confTicketAssignList)) {
            throw new CustomException("工单类型获取失败");
        }
        int updateFlag = 0;
        //新增
        List<ConfTicketAssign> insertList = confTicketAssign.stream().filter(confTicketAssignOne -> null == confTicketAssignOne.getId()).collect(Collectors.toList());

        //更新
        List<ConfTicketAssign> updateList = confTicketAssign.stream().filter(confTicketAssignOne -> null != confTicketAssignOne.getId()).collect(Collectors.toList());
        //新增
        for (ConfTicketAssign ticketAssign : insertList) {
            String ticketType = ticketAssign.getTicketType();
            //邮箱判断
            if (ScTicketTypeEnum.EMAIL.getValue().equals(ticketType)) {
                ConfTicketAssign confTicketAssignReq = new ConfTicketAssign();
                confTicketAssignReq.setOrganizationId(Long.valueOf(authUserEntity.getOrgId()));
                confTicketAssignReq.setEmail(ticketAssign.getEmail());
                confTicketAssignReq.setHandleById(ticketAssign.getHandleById());
                confTicketAssignReq.setTicketType(ticketAssign.getTicketType());
                List<ConfTicketAssign> confTicketAssigns = confTicketAssignMapper.selectConfTicketAssignList(confTicketAssignReq);
                if (!CollectionUtils.isEmpty(confTicketAssigns)) {
                    throw new CustomException("工单类型：" + ScTicketTypeEnum.EMAIL.getName() + "已存在对应邮箱与处理人!");
                }
                confTicketAssignReq.setCreatedBy(authUserEntity.getId());
                confTicketAssignReq.setTicketTypeMain("sc_ticket_type"); //默认为工单类型
                updateFlag = confTicketAssignMapper.insertConfTicketAssign(confTicketAssignReq);

                //处理人判断
            } else if (ScTicketTypeEnum.NR.getValue().equals(ticketType) ||   //NR
                    ScTicketTypeEnum.PR.getValue().equals(ticketType)) {//PR
                ConfTicketAssign confTicketAssignReq = new ConfTicketAssign();
                confTicketAssignReq.setOrganizationId(Long.valueOf(authUserEntity.getOrgId()));
                confTicketAssignReq.setHandleById(ticketAssign.getHandleById());
                confTicketAssignReq.setTicketType(ticketAssign.getTicketType());
                List<ConfTicketAssign> confTicketAssigns = confTicketAssignMapper.selectConfTicketAssignList(confTicketAssignReq);

                if (!CollectionUtils.isEmpty(confTicketAssigns)) {
                    throw new CustomException("工单类型：" + ScTicketTypeEnum.getName(ticketAssign.getTicketType()) + "处理人:" + ticketAssign.getHandleBy() + " 已存在!");
                }
                confTicketAssignReq.setCreatedBy(authUserEntity.getId());
                updateFlag = confTicketAssignMapper.insertConfTicketAssign(confTicketAssignReq);

                //店铺 + 处理人判断
            } else if (!ScTicketTypeEnum.INSIDE_REFUND.getValue().equals(ticketType) &&  //站内退款
                    !ScTicketTypeEnum.OUTSIDE_REFUND.getValue().equals(ticketType) &&  //站外退款
                    !ScTicketTypeEnum.REISSUE_SHIPMENT.getValue().equals(ticketType) && //订单发货
                    !ScTicketTypeEnum.REISSUE_ACCESSORIES.getValue().equals(ticketType) && //补发配件
                    !ScTicketTypeEnum.REISSUE_GOODS.getValue().equals(ticketType)) {//补发产品

                ConfTicketAssign confTicketAssignReq = new ConfTicketAssign();
                confTicketAssignReq.setOrganizationId(Long.valueOf(authUserEntity.getOrgId()));
                confTicketAssignReq.setHandleById(ticketAssign.getHandleById());
                confTicketAssignReq.setShopId(ticketAssign.getShopId());
                confTicketAssignReq.setTicketType(ticketAssign.getTicketType());
                List<ConfTicketAssign> confTicketAssigns = confTicketAssignMapper.selectConfTicketAssignList(confTicketAssignReq);
                if (!CollectionUtils.isEmpty(confTicketAssigns)) {
                    throw new CustomException("工单类型：" + ScTicketTypeEnum.getName(ticketAssign.getTicketType()) + "店铺：" + ticketAssign.getShopName() + "处理人：" + ticketAssign.getHandleBy() + " 已存在!");
                }
                confTicketAssignReq.setCreatedBy(authUserEntity.getId());
                updateFlag = confTicketAssignMapper.insertConfTicketAssign(confTicketAssignReq);
            } else {
                ticketAssign.setCreatedBy(authUserEntity.getId());
                updateFlag = confTicketAssignMapper.insertConfTicketAssign(ticketAssign); //此处处理人只会是一个
            }
        }


        //更新
        for (ConfTicketAssign ticketAssign : updateList) {
            String ticketType = ticketAssign.getTicketType();
            //站外信 邮箱判断
            if (ScTicketTypeEnum.EMAIL.getValue().equals(ticketType)) {
                ConfTicketAssign confTicketAssignReq = new ConfTicketAssign();
                confTicketAssignReq.setOrganizationId(Long.valueOf(authUserEntity.getOrgId()));
                confTicketAssignReq.setEmail(ticketAssign.getEmail()); //邮箱
                confTicketAssignReq.setHandleById(ticketAssign.getHandleById()); // 处理人
                confTicketAssignReq.setTicketType(ticketAssign.getTicketType()); //工单类型
                confTicketAssignReq.setId(ticketAssign.getId()); //主键
                List<ConfTicketAssign> confTicketAssigns = confTicketAssignMapper.selectConfTicketAssignListNotId(confTicketAssignReq);
                if (!CollectionUtils.isEmpty(confTicketAssigns)) {
                    throw new CustomException("工单类型：" + ScTicketTypeEnum.EMAIL.getName() + "已存在对应邮箱与处理人!");
                }
                confTicketAssignReq.setUpdatedAt(DateUtils.getNowDate());
                confTicketAssignReq.setUpdatedBy(authUserEntity.getId());
                updateFlag = confTicketAssignMapper.updateConfTicketAssign(confTicketAssignReq);
                //处理人判断
            } else if (ScTicketTypeEnum.NR.getValue().equals(ticketType) ||   //NR
                    ScTicketTypeEnum.PR.getValue().equals(ticketType)) {//PR
                ConfTicketAssign confTicketAssignReq = new ConfTicketAssign();
                confTicketAssignReq.setOrganizationId(Long.valueOf(authUserEntity.getOrgId()));
                confTicketAssignReq.setHandleById(ticketAssign.getHandleById());
                confTicketAssignReq.setTicketType(ticketAssign.getTicketType()); //工单类型
                confTicketAssignReq.setId(ticketAssign.getId());
                List<ConfTicketAssign> confTicketAssigns = confTicketAssignMapper.selectConfTicketAssignListNotId(confTicketAssignReq);

                if (!CollectionUtils.isEmpty(confTicketAssigns)) {
                    throw new CustomException("工单类型：" + ScTicketTypeEnum.getName(ticketAssign.getTicketType()) + " 处理人:" + ticketAssign.getHandleBy() + " 已存在!");
                }
                confTicketAssignReq.setUpdatedAt(DateUtils.getNowDate());
                confTicketAssignReq.setUpdatedBy(authUserEntity.getId());
                updateFlag = confTicketAssignMapper.updateConfTicketAssign(confTicketAssignReq);
                //店铺 + 处理人判断
            } else if (!ScTicketTypeEnum.INSIDE_REFUND.getValue().equals(ticketType) &&  //站内退款
                    !ScTicketTypeEnum.OUTSIDE_REFUND.getValue().equals(ticketType) &&  //站外退款
                    !ScTicketTypeEnum.REISSUE_SHIPMENT.getValue().equals(ticketType) && //订单发货
                    !ScTicketTypeEnum.REISSUE_ACCESSORIES.getValue().equals(ticketType) && //补发配件
                    !ScTicketTypeEnum.REISSUE_GOODS.getValue().equals(ticketType)) {//补发产品

                ConfTicketAssign confTicketAssignReq = new ConfTicketAssign();
                confTicketAssignReq.setOrganizationId(Long.valueOf(authUserEntity.getOrgId()));
                confTicketAssignReq.setHandleById(ticketAssign.getHandleById()); //处理人
                confTicketAssignReq.setShopId(ticketAssign.getShopId());//店铺
                confTicketAssignReq.setTicketType(ticketAssign.getTicketType()); //工单类型
                confTicketAssignReq.setId(ticketAssign.getId());//主键
                List<ConfTicketAssign> confTicketAssigns = confTicketAssignMapper.selectConfTicketAssignListNotId(confTicketAssignReq);
                if (!CollectionUtils.isEmpty(confTicketAssigns)) {
                    throw new CustomException("工单类型：" + ScTicketTypeEnum.getName(ticketAssign.getTicketType()) + "店铺：" + ticketAssign.getShopName() + " 处理人：" + ticketAssign.getHandleBy() + " 已存在!");
                }
                confTicketAssignReq.setUpdatedAt(DateUtils.getNowDate());
                confTicketAssignReq.setUpdatedBy(authUserEntity.getId());
                updateFlag = confTicketAssignMapper.updateConfTicketAssign(confTicketAssignReq);
            } else {
                ticketAssign.setUpdatedAt(DateUtils.getNowDate());
                ticketAssign.setUpdatedBy(authUserEntity.getId());
                updateFlag = confTicketAssignMapper.updateConfTicketAssign(ticketAssign);
            }
        }
        return updateFlag;
    }


    /**
     * 批量修改--工单分配配置
     *
     * @param confTicketAssign 组织ID
     * @param contextId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateConfTicketAssignListBath(UserEntity authUserEntity, List<ConfTicketAssign> confTicketAssign, Integer contextId) {

        log.info("批量分配信息:{}", JSONObject.toJSONString(confTicketAssign));
        //操作本组织下对应处理人信息
        Long organizationId = contextId.longValue(); //组织ID
        if (null == organizationId) {
            throw new CustomException("组织获取失败");
        }

        //工单类型判断
        List<ConfTicketAssign> confTicketAssignList = confTicketAssign.stream().filter(ss -> StringUtils.isEmpty(ss.getTicketType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(confTicketAssignList)) {
            throw new CustomException("工单类型获取失败");
        }


        Integer updateFlag = 0;

        //  根据类型更新处理人 （处理人为空不进行存储或更新）
        List<ConfTicketAssign> handleByList = confTicketAssign.stream().filter(item ->
                ScTicketTypeEnum.INSIDE_REFUND.getValue().equals(item.getTicketType()) //站内退款
                        || ScTicketTypeEnum.REISSUE_SHIPMENT.getValue().equals(item.getTicketType()) //订单发货
                        || ScTicketTypeEnum.REISSUE_GOODS.getValue().equals(item.getTicketType()) //补发产品
                        || ScTicketTypeEnum.REISSUE_ACCESSORIES.getValue().equals(item.getTicketType()) //补发配件
        ).collect(Collectors.toList());


        //根据支付类型 更新处理人（只要退款类型不为空，会保存对应工单类型）
        List<ConfTicketAssign> payAndHandleByList = confTicketAssign.stream().filter(item -> ScTicketTypeEnum.OUTSIDE_REFUND.getValue().equals(item.getTicketType())).collect(Collectors.toList());


        //站外信(邮箱不为空才会保存)
        List<ConfTicketAssign> emailAndHandleByList = confTicketAssign.stream().filter(item -> ScTicketTypeEnum.EMAIL.getValue().equals(item.getTicketType()) && !CollectionUtils.isEmpty(item.getEmails())).collect(Collectors.toList());

        //操作店铺项，店铺不能为空的项 （删除在新增）
        List<ConfTicketAssign> shopAndHandleByList = confTicketAssign.stream().filter(item ->
                !ScTicketTypeEnum.INSIDE_REFUND.getValue().equals(item.getTicketType())
                        && !ScTicketTypeEnum.OUTSIDE_REFUND.getValue().equals(item.getTicketType())
                        && !ScTicketTypeEnum.REISSUE_SHIPMENT.getValue().equals(item.getTicketType())
                        && !ScTicketTypeEnum.REISSUE_GOODS.getValue().equals(item.getTicketType())
                        && !ScTicketTypeEnum.REISSUE_ACCESSORIES.getValue().equals(item.getTicketType())
                        && !ScTicketTypeEnum.EMAIL.getValue().equals(item.getTicketType())
                        && !CollectionUtils.isEmpty(item.getShopIds()) //判断店铺是否设置
        ).collect(Collectors.toList());

        //操作店铺处理人
        for (ConfTicketAssign ticketAssign : handleByList) {
            String ticketType = ticketAssign.getTicketType();
            if (null == ticketAssign.getHandleById()) {   //处理人为空的项不进行存储
                continue;
            }
            //判断类型是否存在
            ConfTicketAssign confTicketAssignRecod = new ConfTicketAssign();
            confTicketAssignRecod.setTicketType(ticketType);
            confTicketAssignRecod.setOrganizationId(organizationId);
            List<ConfTicketAssign> confTicketAssignLists = confTicketAssignMapper.selectConfTicketAssignList(confTicketAssignRecod);
            if (!CollectionUtils.isEmpty(confTicketAssignLists)) {
                ConfTicketAssign confTicketAssignUpdate = confTicketAssignLists.get(0);
                ticketAssign.setId(confTicketAssignUpdate.getId());
                ticketAssign.setUpdatedBy(authUserEntity.getId());
                ticketAssign.setUpdatedName(authUserEntity.getName());
                ticketAssign.setUpdatedAt(DateUtils.getNowDate());
                updateFlag = confTicketAssignMapper.updateConfTicketAssign(ticketAssign); //更新
            } else {
                ticketAssign.setCreatedAt(DateUtils.getNowDate());
                ticketAssign.setCreatedBy(authUserEntity.getId());
                ticketAssign.setCreatedName(authUserEntity.getName());
                ticketAssign.setOrganizationId(organizationId);
                updateFlag = confTicketAssignMapper.insertConfTicketAssign(ticketAssign);//新增
            }
        }

        //站外退款
        for (ConfTicketAssign ticketAssign : payAndHandleByList) {
            if (StringUtils.isEmpty(ticketAssign.getRefundType())) {
                throw new CustomException("站外退款工单 <退款类型> 获取失败!");
            }
            //判断类型 退款类型是否存在
            ConfTicketAssign confTicketAssignRecod = new ConfTicketAssign();
            confTicketAssignRecod.setTicketType(ticketAssign.getTicketType());
            confTicketAssignRecod.setRefundType(ticketAssign.getRefundType()); //退款类型
            confTicketAssignRecod.setOrganizationId(organizationId);
            List<ConfTicketAssign> confTicketAssignLists = confTicketAssignMapper.selectConfTicketAssignList(confTicketAssignRecod);
            if (!CollectionUtils.isEmpty(confTicketAssignLists)) {
                ConfTicketAssign confTicketAssignUpdate = confTicketAssignLists.get(0);
                ticketAssign.setId(confTicketAssignUpdate.getId()); //更新主键
                ticketAssign.setUpdatedBy(authUserEntity.getId()); //更新人
                ticketAssign.setUpdatedAt(DateUtils.getNowDate()); //更新时间
                ticketAssign.setUpdatedName(authUserEntity.getName());
                updateFlag = confTicketAssignMapper.updateConfTicketAssign(ticketAssign); //更新
            } else {
                ticketAssign.setOrganizationId(organizationId);
                ticketAssign.setCreatedAt(DateUtils.getNowDate());
                ticketAssign.setCreatedBy(authUserEntity.getId());
                ticketAssign.setCreatedName(authUserEntity.getName());
                updateFlag = confTicketAssignMapper.insertConfTicketAssign(ticketAssign);//新增
            }
        }


        //操作邮箱(站外信)
        ArrayList<ConfTicketAssign> emailList = new ArrayList<>();
        emailAndHandleByList.stream().forEach(item -> {
            List<String> emails = item.getEmails().stream().distinct().collect(Collectors.toList());//邮箱去重
            emails.forEach(emailItem -> {
                ConfTicketAssign confTicketAssignInsert = new ConfTicketAssign();
                confTicketAssignInsert.setTicketType(item.getTicketType()); //工单类型
                confTicketAssignInsert.setTicketTypeMain("ticket_type"); //工单主类型
                confTicketAssignInsert.setEmail(emailItem);//邮箱
                confTicketAssignInsert.setCreatedBy(authUserEntity.getId()); //创建人
                confTicketAssignInsert.setOrganizationId(organizationId); //组织ID
                confTicketAssignInsert.setHandleById(item.getHandleById());//处理人
                confTicketAssignInsert.setHandleBy(item.getHandleBy());
                confTicketAssignInsert.setCreatedName(authUserEntity.getName());
                confTicketAssignInsert.setCreatedAt(DateUtils.getNowDate());
                confTicketAssignInsert.setDictLabel(item.getDictLabel());
                emailList.add(confTicketAssignInsert);
            });
        });
        //判断邮箱处理人是否重复
        Map<String, List<ConfTicketAssign>> emailMap = emailList.stream().collect(Collectors.groupingBy(ConfTicketAssign::getEmail));
        emailMap.forEach((emailEntity, List) -> {
            if (List.size() > 1) {
                throw new CustomException("工单类型:站外信 " + "邮箱" + emailEntity + " 存在多个处理人！");
            }
        });


        //删除指定类型工单
        List<String> ticketList = Arrays.asList(ScTicketTypeEnum.EMAIL.getValue());
        confTicketAssignMapper.deleteConfTicketAssignByTicketTypes(organizationId, ticketList);
        for (ConfTicketAssign ticketAssign : emailList) {
            updateFlag = confTicketAssignMapper.insertConfTicketAssign(ticketAssign);
        }


        //操作店铺+处理人
        ArrayList<ConfTicketAssign> shopHandelByList = new ArrayList<>();
        shopAndHandleByList.stream().forEach(item -> {
            List<Long> shopIds = item.getShopIds().stream().distinct().collect(Collectors.toList()); //店铺去重
            shopIds.forEach(shopId -> {
                ConfTicketAssign confTicketAssignInsert = new ConfTicketAssign();
                confTicketAssignInsert.setShopId(shopId); //店偶
                confTicketAssignInsert.setCreatedBy(authUserEntity.getId());
                confTicketAssignInsert.setOrganizationId(organizationId); //组织
                confTicketAssignInsert.setTicketType(item.getTicketType()); //工单类型
                confTicketAssignInsert.setTicketTypeMain(item.getTicketTypeMain()); //工单主类型
                confTicketAssignInsert.setHandleById(item.getHandleById()); //处理人
                confTicketAssignInsert.setTicketTypeMain("ticket_type");
                confTicketAssignInsert.setHandleBy(item.getHandleBy());
                confTicketAssignInsert.setCreatedName(authUserEntity.getName());
                confTicketAssignInsert.setCreatedAt(DateUtils.getNowDate());
                confTicketAssignInsert.setDictLabel(item.getDictLabel());
                shopHandelByList.add(confTicketAssignInsert);
            });
        });
        //同一工单下，同一店铺，不允许出现两个处理人
        //根据工单分组
        Map<String, List<ConfTicketAssign>> ticketTypeGroup = shopHandelByList.stream().collect(Collectors.groupingBy(ConfTicketAssign::getTicketType));
        if (!CollectionUtils.isEmpty(ticketTypeGroup)) {
            ticketTypeGroup.forEach((ticketType, confTicketAssigns) -> {
                //每个工单进行循环，根据店偶进行分组
                Map<Long, List<ConfTicketAssign>> shopIdGroup = confTicketAssigns.stream().filter(item -> null != item.getShopId()).collect(Collectors.groupingBy(ConfTicketAssign::getShopId));
                shopIdGroup.forEach((shopId, value) -> {
                    if (value.size() > 1) { //店铺下存在两个处理人
                        AccountEntity accountEntity = accountService.getById(Math.toIntExact(shopId));
                        throw new CustomException("工单类型:" + confTicketAssigns.get(0).getDictLabel() + " 店铺：" + accountEntity.getTitle() + "存在多个处理人");
                    }
                });
            });
        }


        //删除  排除一下类型（其余均为店铺+处理人）
        List<String> ticketTypeList = Arrays.asList(ScTicketTypeEnum.INSIDE_REFUND.getValue(), //站内退款
                ScTicketTypeEnum.OUTSIDE_REFUND.getValue(),  //站外退款
                ScTicketTypeEnum.REISSUE_SHIPMENT.getValue(), //订单发货
                ScTicketTypeEnum.REISSUE_GOODS.getValue(), //补发商品
                ScTicketTypeEnum.REISSUE_ACCESSORIES.getValue(), //补发配件
                ScTicketTypeEnum.EMAIL.getValue());//站外信
        confTicketAssignMapper.deleteConfTicketAssignByNotTicketTypes(organizationId, ticketTypeList);
        //新增 店铺+处理人
        for (ConfTicketAssign ticketAssign : shopHandelByList) {
            updateFlag = confTicketAssignMapper.insertConfTicketAssign(ticketAssign);
        }
        return updateFlag;
    }


    /**
     * 批量修改--工单分配配置
     *
     * @param confTicketAssign 组织ID
     * @param contextId
     */

    @Transactional(rollbackFor = Exception.class)
    public void updateConfTicketAssignListBathV2(UserEntity authUserEntity, List<ConfTicketAssign> confTicketAssign, Integer contextId) {

        log.info("批量分配信息:{}", JSONObject.toJSONString(confTicketAssign));
        //操作本组织下对应处理人信息
        Long organizationId = contextId.longValue(); //组织ID
        if (null == organizationId) {
            throw new CustomException("组织获取失败");
        }

        //工单类型判断
        List<ConfTicketAssign> confTicketAssignList = confTicketAssign.stream().filter(ss -> StringUtils.isEmpty(ss.getTicketType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(confTicketAssignList)) {
            throw new CustomException("工单类型获取失败");
        }



        //  根据类型更新处理人 （处理人为空不进行存储或更新）
        List<ConfTicketAssign> handleByList = confTicketAssign.stream().filter(item ->
                ScTicketTypeEnum.INSIDE_REFUND.getValue().equals(item.getTicketType()) //站内退款
                        || ScTicketTypeEnum.REISSUE_SHIPMENT.getValue().equals(item.getTicketType()) //订单发货
                        || ScTicketTypeEnum.REISSUE_GOODS.getValue().equals(item.getTicketType()) //补发产品
                        || ScTicketTypeEnum.REISSUE_ACCESSORIES.getValue().equals(item.getTicketType()) //补发配件
        ).collect(Collectors.toList());


        //站外退款
        List<ConfTicketAssign> payAndHandleByList = confTicketAssign.stream().filter(item -> ScTicketTypeEnum.OUTSIDE_REFUND.getValue().equals(item.getTicketType())).collect(Collectors.toList());


        //站外信(邮箱不为空才会保存)
        List<ConfTicketAssign> emailAndHandleByList = confTicketAssign.stream().filter(item -> ScTicketTypeEnum.EMAIL.getValue().equals(item.getTicketType()) &&
                !CollectionUtils.isEmpty(item.getEmails())&&item.getHandleById()!=null).collect(Collectors.toList());

        //操作店铺项，店铺不能为空的项 （删除在新增）
        List<ConfTicketAssign> shopAndHandleByList = confTicketAssign.stream().filter(item ->
                !ScTicketTypeEnum.INSIDE_REFUND.getValue().equals(item.getTicketType())  //站内退款
                        && !ScTicketTypeEnum.OUTSIDE_REFUND.getValue().equals(item.getTicketType()) //站外退款
                        && !ScTicketTypeEnum.REISSUE_SHIPMENT.getValue().equals(item.getTicketType()) //订单发货
                        && !ScTicketTypeEnum.REISSUE_GOODS.getValue().equals(item.getTicketType()) //补发产品
                        && !ScTicketTypeEnum.REISSUE_ACCESSORIES.getValue().equals(item.getTicketType()) //补发配件
                        && !ScTicketTypeEnum.EMAIL.getValue().equals(item.getTicketType()) //站外信
                        && !CollectionUtils.isEmpty(item.getShopIds())&&item.getHandleById()!=null //店铺及处理人不等于空
        ).collect(Collectors.toList());

        //操作店铺处理人
        for (ConfTicketAssign ticketAssign : handleByList) {
            String ticketType = ticketAssign.getTicketType();
            if (null == ticketAssign.getHandleById()) {   //处理人为空的项不进行存储
                continue;
            }
            //判断类型是否存在
            ConfTicketAssign confTicketAssignRecod = new ConfTicketAssign();
            confTicketAssignRecod.setTicketType(ticketType);
            confTicketAssignRecod.setOrganizationId(organizationId);
            List<ConfTicketAssign> confTicketAssignLists = confTicketAssignMapper.selectConfTicketAssignList(confTicketAssignRecod);
            if (!CollectionUtils.isEmpty(confTicketAssignLists)) {
                ConfTicketAssign confTicketAssignUpdate = confTicketAssignLists.get(0);
                if (confTicketAssignUpdate.getHandleById().equals(ticketAssign.getHandleById())){
                    //判断是否修改
                    continue;
                }
                ticketAssign.setId(confTicketAssignUpdate.getId());
                ticketAssign.setUpdatedBy(authUserEntity.getId());
                ticketAssign.setUpdatedName(authUserEntity.getName());
                ticketAssign.setUpdatedAt(DateUtils.getNowDate());
                 confTicketAssignMapper.updateConfTicketAssign(ticketAssign); //更新
            } else {
                ticketAssign.setCreatedAt(DateUtils.getNowDate());
                ticketAssign.setCreatedBy(authUserEntity.getId());
                ticketAssign.setCreatedName(authUserEntity.getName());
                ticketAssign.setOrganizationId(organizationId);
                confTicketAssignMapper.insertConfTicketAssign(ticketAssign);//新增
            }
        }

        //站外退款
        for (ConfTicketAssign ticketAssign : payAndHandleByList) {
            if (StringUtils.isEmpty(ticketAssign.getRefundType())) {
                throw new CustomException("站外退款工单 <退款方式> 获取失败!");
            }
            if (ticketAssign.getHandleById() == null) {  //处理人为空的项不进行存储
                continue;
            }

            //判断类型 退款类型是否存在
            ConfTicketAssign confTicketAssignRecod = new ConfTicketAssign();
            confTicketAssignRecod.setTicketType(ticketAssign.getTicketType());//工单类型
            confTicketAssignRecod.setRefundType(ticketAssign.getRefundType()); //退款方式
            confTicketAssignRecod.setOrganizationId(organizationId);
            List<ConfTicketAssign> confTicketAssignLists = confTicketAssignMapper.selectConfTicketAssignList(confTicketAssignRecod);
            if (!CollectionUtils.isEmpty(confTicketAssignLists)) {
                ConfTicketAssign confTicketAssignUpdate = confTicketAssignLists.get(0); //此类型仅存在一条
                if (confTicketAssignUpdate.getHandleById().equals(ticketAssign.getHandleById())){
                    continue;   //判断是否修改
                }
                ticketAssign.setId(confTicketAssignUpdate.getId()); //更新主键
                ticketAssign.setUpdatedBy(authUserEntity.getId()); //更新人
                ticketAssign.setUpdatedAt(DateUtils.getNowDate()); //更新时间
                ticketAssign.setUpdatedName(authUserEntity.getName());
              confTicketAssignMapper.updateConfTicketAssign(ticketAssign); //更新
            } else {
                ticketAssign.setOrganizationId(organizationId);
                ticketAssign.setCreatedAt(DateUtils.getNowDate());
                ticketAssign.setCreatedBy(authUserEntity.getId());
                ticketAssign.setCreatedName(authUserEntity.getName());
               confTicketAssignMapper.insertConfTicketAssign(ticketAssign);//新增
            }
        }


        //操作邮箱(站外信)
        ArrayList<ConfTicketAssign> emailList = new ArrayList<>();
        emailAndHandleByList.stream().forEach(item -> {
            List<String> emails = item.getEmails().stream().distinct().collect(Collectors.toList());//邮箱去重
            emails.forEach(emailItem -> {
                ConfTicketAssign confTicketAssignInsert = new ConfTicketAssign();
                confTicketAssignInsert.setTicketType(item.getTicketType()); //工单类型
                confTicketAssignInsert.setTicketTypeMain("ticket_type"); //工单主类型
                confTicketAssignInsert.setEmail(emailItem);//邮箱
                confTicketAssignInsert.setCreatedBy(authUserEntity.getId()); //创建人
                confTicketAssignInsert.setOrganizationId(organizationId); //组织ID
                confTicketAssignInsert.setHandleById(item.getHandleById());//处理人
                confTicketAssignInsert.setHandleBy(item.getHandleBy());
                confTicketAssignInsert.setCreatedName(authUserEntity.getName());
                confTicketAssignInsert.setCreatedAt(DateUtils.getNowDate());
                confTicketAssignInsert.setDictLabel(item.getDictLabel());
                emailList.add(confTicketAssignInsert);
            });
        });
        //判断邮箱处理人是否重复
        Map<String, List<ConfTicketAssign>> emailMap = emailList.stream().collect(Collectors.groupingBy(ConfTicketAssign::getEmail));
        emailMap.forEach((emailEntity, List) -> {
            if (List.size() > 1) {
                throw new CustomException("工单类型:站外信 " + "邮箱" + emailEntity + " 存在多个处理人！");
            }
        });

        if (!CollectionUtils.isEmpty(emailList)){
            //判断类型 退款类型是否存在
            ConfTicketAssign confTicketAssignRecod = new ConfTicketAssign();
            confTicketAssignRecod.setTicketType(ScTicketTypeEnum.EMAIL.getValue());//工单类型
            confTicketAssignRecod.setOrganizationId(organizationId);
            List<ConfTicketAssign> emailTypeList = confTicketAssignMapper.selectConfTicketAssignList(confTicketAssignRecod);
            if (CollectionUtils.isEmpty(emailTypeList)) {
                for (ConfTicketAssign ticketAssign : emailList) {
                  confTicketAssignMapper.insertConfTicketAssign(ticketAssign);
                }
            }else{
                for (ConfTicketAssign ticketAssign : emailTypeList) {
                    ConfTicketAssign confTicketAssignSave = emailList.stream().filter(item -> item.getEmail().equalsIgnoreCase(ticketAssign.getEmail())).findFirst().orElse(null);
                    if (confTicketAssignSave == null) {
                        //未匹配到库存对应email，即代表删除了email
                        confTicketAssignMapper.deleteConfTicketAssignById(ticketAssign.getId());
                    } else if (!confTicketAssignSave.getHandleById().equals(ticketAssign.getHandleById())) {
                        ticketAssign.setHandleById(confTicketAssignSave.getHandleById()); //设置新的处理人信息
                        ticketAssign.setUpdatedBy(authUserEntity.getId()); //更新人
                        ticketAssign.setUpdatedAt(DateUtils.getNowDate()); //更新时间
                        ticketAssign.setUpdatedName(authUserEntity.getName());
                        confTicketAssignMapper.updateConfTicketAssign(ticketAssign); //更新
                        emailList.removeIf(item -> item.getEmail().equalsIgnoreCase(ticketAssign.getEmail()));
                    }else  {
                        emailList.removeIf(item -> item.getEmail().equalsIgnoreCase(ticketAssign.getEmail()));
                    }
                }

                for (ConfTicketAssign ticketAssign : emailList) {
                    confTicketAssignMapper.insertConfTicketAssign(ticketAssign);
                }
            }
        }



        //操作店铺+处理人
        ArrayList<ConfTicketAssign> shopHandelByList = new ArrayList<>();
        shopAndHandleByList.stream().forEach(item -> {
            List<Long> shopIds = item.getShopIds().stream().distinct().collect(Collectors.toList()); //店铺去重
            shopIds.forEach(shopId -> {
                ConfTicketAssign confTicketAssignInsert = new ConfTicketAssign();
                confTicketAssignInsert.setShopId(shopId); //店偶
                confTicketAssignInsert.setCreatedBy(authUserEntity.getId());
                confTicketAssignInsert.setOrganizationId(organizationId); //组织
                confTicketAssignInsert.setTicketType(item.getTicketType()); //工单类型
                confTicketAssignInsert.setTicketTypeMain(item.getTicketTypeMain()); //工单主类型
                confTicketAssignInsert.setHandleById(item.getHandleById()); //处理人
                confTicketAssignInsert.setTicketTypeMain("ticket_type");
                confTicketAssignInsert.setHandleBy(item.getHandleBy());
                confTicketAssignInsert.setCreatedName(authUserEntity.getName());
                confTicketAssignInsert.setCreatedAt(DateUtils.getNowDate());
                confTicketAssignInsert.setDictLabel(item.getDictLabel());
                shopHandelByList.add(confTicketAssignInsert);
            });
        });
        //同一工单下，同一店铺，不允许出现两个处理人
        //根据工单分组
        Map<String, List<ConfTicketAssign>> ticketTypeGroup = shopHandelByList.stream().collect(Collectors.groupingBy(ConfTicketAssign::getTicketType));
        if (!CollectionUtils.isEmpty(ticketTypeGroup)) {
            ticketTypeGroup.forEach((ticketType, confTicketAssigns) -> {
                //每个工单进行循环，根据店偶进行分组
                Map<Long, List<ConfTicketAssign>> shopIdGroup = confTicketAssigns.stream().filter(item -> null != item.getShopId()).collect(Collectors.groupingBy(ConfTicketAssign::getShopId));
                shopIdGroup.forEach((shopId, value) -> {
                    if (value.size() > 1) { //店铺下存在两个处理人
                        AccountEntity accountEntity = accountService.getById(Math.toIntExact(shopId));
                        throw new CustomException("工单类型:" + confTicketAssigns.get(0).getDictLabel() + " 店铺：" + accountEntity.getTitle() + "存在多个处理人");
                    }
                });
            });
        }


        for (String ticketType : ticketTypeGroup.keySet()) {
            List<ConfTicketAssign> confTicketAssigns = ticketTypeGroup.get(ticketType);
            ConfTicketAssign confTicketAssignRecod = new ConfTicketAssign();
            confTicketAssignRecod.setTicketType(ticketType);//工单类型
            confTicketAssignRecod.setOrganizationId(organizationId);
            List<ConfTicketAssign> ticketTypeList = confTicketAssignMapper.selectConfTicketAssignList(confTicketAssignRecod);
            if (CollectionUtils.isEmpty(ticketTypeList)) {
                for (ConfTicketAssign ticketAssign : confTicketAssigns) {
                    confTicketAssignMapper.insertConfTicketAssign(ticketAssign);
                }
            }else{
                for (ConfTicketAssign ticketAssign : ticketTypeList) {
                    ConfTicketAssign confTicketAssignSave = confTicketAssigns.stream().filter(item -> item.getShopId().equals(ticketAssign.getShopId())).findFirst().orElse(null);
                    if (confTicketAssignSave == null) {
                        //未匹配到库存对应email，即代表删除了email
                        confTicketAssignMapper.deleteConfTicketAssignById(ticketAssign.getId());
                    } else if (!confTicketAssignSave.getHandleById().equals(ticketAssign.getHandleById())) {
                        ticketAssign.setHandleById(confTicketAssignSave.getHandleById()); //设置新的处理人信息
                        ticketAssign.setUpdatedBy(authUserEntity.getId()); //更新人
                        ticketAssign.setUpdatedAt(DateUtils.getNowDate()); //更新时间
                        ticketAssign.setUpdatedName(authUserEntity.getName());
                        confTicketAssignMapper.updateConfTicketAssign(ticketAssign); //更新
                        confTicketAssigns.removeIf(item -> item.getShopId().equals(ticketAssign.getShopId()));
                    }else {
                        confTicketAssigns.removeIf(item -> item.getShopId().equals(ticketAssign.getShopId()));
                    }
                }

                for (ConfTicketAssign ticketAssign : confTicketAssigns) {
                    confTicketAssignMapper.insertConfTicketAssign(ticketAssign);
                }
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateConfTicketAssignHandleBy(ConfTicketAssign confTicketAssign) {
        if (confTicketAssign == null || confTicketAssign.getHandleById() == null || confTicketAssign.getReplaceById() == null || StringUtil.isEmpty(confTicketAssign.getHandleBy())) {
            throw new CustomException("替换信息不能为空");
        }
        return confTicketAssignMapper.updateConfTicketAssignHandleBy(confTicketAssign);
    }


    /**
     * @param
     * @param shopId
     * @param ticketType
     * @description: 根据店铺ID和工单工单类型查询处理人
     * @author: Moore
     * @date: 2023/12/13 14:09
     * @return: com.bizark.op.api.entity.op.ticket.ConfTicketAssign
     **/
    @Override
    public ConfTicketAssign selectConfTicketAssignByshopIdAndTicketType(Long shopId, String ticketType) {
        ConfTicketAssign confTicketAssign = new ConfTicketAssign();
        confTicketAssign.setShopId(shopId);
        confTicketAssign.setTicketType(ticketType);
        List<ConfTicketAssign> confTicketAssigns = confTicketAssignMapper.selectConfTicketAssignList(confTicketAssign);
        if (CollectionUtils.isEmpty(confTicketAssigns)) {
            return null;
        }
        return confTicketAssigns.get(0);
    }


    /**
     * 根据用户ID，设置处理人名称及ID
     *
     * @param handlerByGroup
     * @param handlerByGroup
     */
    private void settingSysUserInfoById(Map<Long, List<ScTicket>> handlerByGroup) {
        //根据用户查询处理人
        Set<Long> handelByIds = handlerByGroup.keySet();
        if (CollectionUtils.isEmpty(handelByIds)) {
            return;
        }
        List<Integer> handelIdList = handelByIds.stream()
                .map(Long::intValue)
                .collect(Collectors.toList());
        List<UserEntity> users = userService.findByIds(handelIdList);
        //设置对应用户部门及ID
        for (UserEntity user : users) {
            if (handlerByGroup.containsKey(user.getId())) {
                handlerByGroup.get(user.getId())
                        .forEach(item -> {
                            item.setDeptId(Long.valueOf(user.getDeptId()));
                            item.setUserId(Long.valueOf(user.getId()));
                            item.setHandlerBy(user.getName());
                        });
            }
        }
    }


    /**
     * 处理shopId 对应 handlerBy
     *
     * @param tickets
     */
    private void handlerByForShopIds(List<ScTicket> tickets, Long orgId) {
        Map<Long, List<ScTicket>> shopTicketGroup = tickets.stream()
                .filter(item -> ObjectUtil.isNotEmpty(item.getShopId()))
                .collect(Collectors.groupingBy(ScTicket::getShopId));
        ArrayList<Long> longs = new ArrayList<>(shopTicketGroup.keySet());
        List<Integer> integerList = longs.stream()
                .map(Long::intValue)
                .collect(Collectors.toList());
        List<AccountEntity> amzShops = accountService.findAllByIds(Math.toIntExact(orgId), integerList);
        if (CollectionUtil.isNotEmpty(amzShops)) {
            for (AccountEntity shop : amzShops) {
                if (!StringUtils.isEmpty(shop.getCreatedName()) && shopTicketGroup.containsKey(shop.getId())) {
                    shopTicketGroup.get(shop.getId())
                            .forEach(item -> {
                                item.setHandlerBy(shop.getCreatedName());
                            });
                }
            }
        }
    }


    @Override
    public String getTicketHandle(ScTicket scTicket, String handleType) {
        ConfTicketAssign record = new ConfTicketAssign();
        record.setShopId(scTicket.getShopId());
        record.setTicketSource(scTicket.getTicketSource());
        record.setTicketType(scTicket.getTicketType());
        record.setHandleType(handleType);
        // 站外信默认处理人
        if (scTicket.getTicketType().equals("EMAIL")) {
            ConfTicketAssign assign = confTicketAssignMapper.selectEmailTicketForHanlerBy();
            if (ObjectUtil.isNotEmpty(assign)) {
                scTicket.setHandlerBy(assign.getHandleBy());
            }
        }
        List<ConfTicketAssign> confTicketAssigns = this.selectConfTicketAssignList(record);
        if (CollectionUtils.isEmpty(confTicketAssigns)) {
            return null;
        } else {
            ConfTicketAssign confTicketAssign = confTicketAssigns.get(0);
            String handlerBy = confTicketAssign.getHandleBy();
            if (ConfTicketAssignConstant.TICKET_ASSIGN_RULE_ASIN.equals(confTicketAssign.getAssignRule())) {
                if (StringUtil.isNotEmpty(scTicket.getAsin())) {
//                    handlerBy = skuMapService.getOperatorByAsin(scTicket.getAsin());
                } else if (StringUtil.isNotEmpty(scTicket.getParentAsin())) {
//                    handlerBy = skuMapService.getOperatorByParentAsin(scTicket.getParentAsin());
                } else if (StringUtil.isNotEmpty(scTicket.getAmazonOrderId())) {
                    handlerBy = getHandlerByByOrder(scTicket.getOrganizationId(), scTicket.getAmazonOrderId());
                }
            } else if (ConfTicketAssignConstant.TICKET_ASSIGN_RULE_SHOP.equals(confTicketAssign.getAssignRule())) {
                if (null != scTicket.getShopId()) {
                    AccountEntity amzShop = accountService.getById(Math.toIntExact(scTicket.getShopId()));
                    handlerBy = null != amzShop.getCreatedName() ? amzShop.getCreatedName() : null;
                }
            } else if (ConfTicketAssignConstant.TICKET_ASSIGN_RULE_ORDER_SHOP.equals(confTicketAssign.getAssignRule())) {
                if (StringUtil.isNotEmpty(scTicket.getAmazonOrderId())) {
                    handlerBy = getHandlerByByOrder(scTicket.getOrganizationId(), scTicket.getAmazonOrderId());
                } else {
                    if (null != scTicket.getShopId()) {
                        AccountEntity amzShop = accountService.getById(Math.toIntExact(scTicket.getShopId()));
                        handlerBy = null != amzShop.getCreatedName() ? amzShop.getCreatedName() : null;
                    }
                }
            } else if (ConfTicketAssignConstant.TICKET_ASSIGN_RULE_FIXED.equals(confTicketAssign.getAssignRule())) {
                handlerBy = confTicketAssign.getHandleBy();
            }

            return confTicketAssigns.stream().map(e -> e.getHandleBy()).collect(Collectors.joining(","));
        }
    }

    /*新版获取处理人*/
    @Override
    public Long getTicketHandleNewVersion(ScTicket scTicket) {
        try {
            //组织ID不能为空
            if (scTicket.getOrganizationId() == null) {
                return null;

            }
            //工单类型
            String ticketType = scTicket.getTicketType();

            if (ScTicketTypeEnum.NR.getValue().equals(scTicket.getTicketType())) {
                if (scTicket.getOrganizationId().equals(1000049L) && "amazon".equals(scTicket.getTicketSource())) {
                    return 3120L;
                } else {
                    if (null != scTicket.getShopId()) {
                        ConfTicketAssign recod = new ConfTicketAssign();
                        recod.setOrganizationId(scTicket.getOrganizationId()); //组织ID
                        recod.setTicketType(ticketType);  //工单类型
                        recod.setShopId(scTicket.getShopId()); //店铺
                        List<ConfTicketAssign> confTicketAssigns = confTicketAssignMapper.selectConfTicketAssignListNotId(recod);
                        if (!CollectionUtils.isEmpty(confTicketAssigns)) {
                            return confTicketAssigns.get(0).getHandleById();
                        }
                    }
                }
            }
            //PR 按照ASIN对应的运营进行分配
            if (ScTicketTypeEnum.PR.getValue().equals(scTicket.getTicketType())) {
                //组织为1000049指定为Lemon处理
                if (scTicket.getOrganizationId().equals(1000049L)) {
                    return 3120L;
                }
                if (!StringUtil.isEmpty(scTicket.getHandlerBy())) { //已存在对应PR 处理人信息
                    return null;
                }

                if (StringUtil.isNotEmpty(scTicket.getAsin())) { //根据ASIn
                    return getOperatorIdByAsin(scTicket.getAsin(), scTicket.getOrganizationId());
                } else if (StringUtil.isNotEmpty(scTicket.getParentAsin())) { //根据父ASIN
                    return getOperatorIdByParentAsin(scTicket.getParentAsin(), scTicket.getOrganizationId());
                } else if (StringUtil.isNotEmpty(scTicket.getAmazonOrderId())) { //根据订单编号
                    return getHandlerIdByOrder(scTicket.getOrganizationId(), scTicket.getAmazonOrderId());
                }
                return null;
            }


            //NR逻辑
            /*if (ScTicketTypeEnum.NR.getValue().equals(scTicket.getTicketType())) {
                if (scTicket.getOrganizationId().equals(1000049L)) {
                    return null;
                } else {
                    if (!StringUtil.isEmpty(scTicket.getHandlerBy())) { //已存在对应PR NR处理人信息
                        return null;
                    }
                    if (StringUtil.isNotEmpty(scTicket.getAsin())) { //根据ASIn
                        return getOperatorIdByAsin(scTicket.getAsin(), scTicket.getOrganizationId());
                    } else if (StringUtil.isNotEmpty(scTicket.getParentAsin())) { //根据父ASIN
                        return getOperatorIdByParentAsin(scTicket.getParentAsin(),scTicket.getOrganizationId());
                    } else if (StringUtil.isNotEmpty(scTicket.getAmazonOrderId())) { //根据订单编号
                        return getHandlerIdByOrder(scTicket.getOrganizationId(), scTicket.getAmazonOrderId());
                    }
                    return null;
                }
            }*/


            //站外信，根据邮件分配  组织ID+工单类型
            if (ScTicketTypeEnum.EMAIL.getValue().equals(ticketType) && null != scTicket.getSourceId()) {
                //收件箱判断
                if (StringUtil.isNotEmpty(scTicket.getToAddress())) {


                    //查询对应处理人
                    ConfTicketAssign recod = new ConfTicketAssign();
                    recod.setOrganizationId(scTicket.getOrganizationId()); //组织ID
                    recod.setTicketType(ticketType); //工单类型
                    List<ConfTicketAssign> confTicketAssignsList = confTicketAssignMapper.selectConfTicketAssignList(recod);//获取所有邮箱规则
                    List<ConfTicketAssign> matchingHandleBy = confTicketAssignsList.stream().filter(confTicketAssign -> !StringUtils.isEmpty(confTicketAssign.getEmail()) && scTicket.getToAddress().toLowerCase().contains(confTicketAssign.getEmail().toLowerCase())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(matchingHandleBy)) {
                        return matchingHandleBy.get(0).getHandleById(); //处理人ID
                    }
                }
                //  NR PR  站内退款  订单发货  补发产品  补发配件   组织ID+工单类型  （均为直接指定处理人）
            } else if (
                    ScTicketTypeEnum.INSIDE_REFUND.getValue().equals(ticketType) || //站内退款
                            ScTicketTypeEnum.REISSUE_SHIPMENT.getValue().equals(ticketType) || //订单发货
                            ScTicketTypeEnum.REISSUE_ACCESSORIES.getValue().equals(ticketType) ||  //补发配件
                            ScTicketTypeEnum.REISSUE_GOODS.getValue().equals(ticketType)  //补发产品
            ) {
                ConfTicketAssign recod = new ConfTicketAssign();
                recod.setOrganizationId(scTicket.getOrganizationId());
                recod.setTicketType(ticketType);
                List<ConfTicketAssign> confTicketAssigns = confTicketAssignMapper.selectConfTicketAssignListNotId(recod); //TODO  是否随机取一个处理人
                if (!CollectionUtils.isEmpty(confTicketAssigns)) {
                    return confTicketAssigns.get(0).getHandleById();
                }
                //站外退款 根据退款类型+ 组织ID+ 工单类型
            } else if (ScTicketTypeEnum.OUTSIDE_REFUND.getValue().equals(ticketType) && null != scTicket.getSourceId()) {
                //获取取站外退款工单 来源数据
                /*ScOrderRefundApply scOrderRefundApply = scOrderRefundApplyService.selectScOrderRefundApplyById(scTicket.getSourceId());
                if (null != scOrderRefundApply) {
                    String refundType = StringUtils.isEmpty(scOrderRefundApply.getRefundWay()) ? "Other" : scOrderRefundApply.getRefundWay();//支付类型 空为其他
                    ConfTicketAssign recod = new ConfTicketAssign();
                    recod.setOrganizationId(scTicket.getOrganizationId()); //组织ID
                    recod.setTicketType(ticketType);  //工单类型
                    recod.setRefundType(refundType); //退款类型
                    List<ConfTicketAssign> confTicketAssigns = confTicketAssignMapper.selectConfTicketAssignListNotId(recod);
                    if (!CollectionUtils.isEmpty(confTicketAssigns)) {
                        return confTicketAssigns.get(0).getHandleById();
                    }
                }*/
                OrderRefund orderRefund = orderRefundMapper.selectById(scTicket.getSourceId());
                if (orderRefund != null) {
                    ConfTicketAssign record = new ConfTicketAssign();
                    record.setOrganizationId(scTicket.getOrganizationId()); //组织ID
                    record.setTicketType(ticketType);  //工单类型
                    record.setRefundType(StringUtils.isEmpty(orderRefund.getRefundWay()) ? "Other" : orderRefund.getRefundWay()); //退款类型
                    List<ConfTicketAssign> confTicketAssigns = confTicketAssignMapper.selectConfTicketAssignListNotId(record);
                    if (!CollectionUtils.isEmpty(confTicketAssigns)) {
                        return confTicketAssigns.get(0).getHandleById();
                    }
                } else {
                    return null;
                }
            } else { //其余工单类型使用  组织+店铺+工单类型
                if (null != scTicket.getShopId()) {
                    ConfTicketAssign recod = new ConfTicketAssign();
                    recod.setOrganizationId(scTicket.getOrganizationId()); //组织ID
                    recod.setTicketType(ticketType);  //工单类型
                    recod.setShopId(scTicket.getShopId()); //店铺
                    List<ConfTicketAssign> confTicketAssigns = confTicketAssignMapper.selectConfTicketAssignListNotId(recod);
                    if (!CollectionUtils.isEmpty(confTicketAssigns)) {
                        return confTicketAssigns.get(0).getHandleById();
                    }
                }
            }
            return null;
        } catch (Exception e) {
            log.error("设置工单处理人失败：{}", e);
            return null;
        }
    }

    private String getHandlerByByOrder(Long organizationId, String amazonOrderId) {
        String asin = saleOrderItemService.getAsinByOrgIdAndOrderNo(Math.toIntExact(organizationId), amazonOrderId);
        if (StringUtils.isNotEmpty(asin)) {
//            return skuMapService.getOperatorByAsin(asin);
        }
        return null;
    }

    private Long getHandlerIdByOrder(Long organizationId, String amazonOrderId) {
        String asin = saleOrderItemService.getAsinByOrgIdAndOrderNo(Math.toIntExact(organizationId), amazonOrderId);
        if (StringUtils.isNotEmpty(asin)) {
            return getOperatorIdByAsin(asin, organizationId);
        }
        return null;
    }

    /**
     * 新增工单分配规则
     *
     * @param confTicketAssign 工单分配规则
     * @return 结果
     */
    @Override
    public int insertConfTicketAssign(ConfTicketAssign confTicketAssign) {
        confTicketAssign.setCreatedAt(DateUtils.getNowDate());
        return confTicketAssignMapper.insertConfTicketAssign(confTicketAssign);
    }

    /**
     * 修改工单分配规则（仅修改处理人）
     *
     * @param confTicketAssign 工单分配规则
     * @return 结果
     */
    @Override
    public int updateConfTicketAssign(UserEntity authUserEntity, ConfTicketAssign confTicketAssign) {
        if (confTicketAssign == null || null == confTicketAssign.getId()) {
            throw new CustomException("获取工单信息失败");
        }
        if (null == confTicketAssign.getHandleById()) {
            throw new CustomException("处理人不能设置为空");
        }
        //判断

        ConfTicketAssign confTicketAssignUpdate = new ConfTicketAssign();
        confTicketAssignUpdate.setUpdatedBy(authUserEntity.getId());
        confTicketAssignUpdate.setUpdatedName(authUserEntity.getName());
        confTicketAssignUpdate.setUpdatedAt(DateUtils.getNowDate());
        confTicketAssignUpdate.setId(confTicketAssign.getId());
        confTicketAssignUpdate.setHandleById(confTicketAssign.getHandleById());
        confTicketAssignUpdate.setHandleBy(confTicketAssign.getHandleBy());
        return confTicketAssignMapper.updateConfTicketAssign(confTicketAssignUpdate);
    }

    /**
     * 批量删除工单分配规则（逻辑删除）
     *
     * @param assignIds 需要删除的工单分配规则ID
     * @return 结果
     */
    @Override
    public int deleteConfTicketAssignByIds(Long[] assignIds) {
        if (assignIds == null || assignIds.length == 0) {
            throw new CustomException("获取删除行失败");
        }
        return confTicketAssignMapper.deleteConfTicketAssignByIds(assignIds);
    }

    /**
     * 删除工单分配规则信息
     *
     * @param assignId 工单分配规则ID
     * @return 结果
     */
    @Override
    public int deleteConfTicketAssignById(Long assignId) {
        return confTicketAssignMapper.deleteConfTicketAssignById(assignId);
    }

    /**
     * 根据asin获取处理人
     *
     * @param asin
     * @param organizationId
     * @return
     */
    @Override
    public Long getOperatorIdByAsin(String asin, Long organizationId) {
        if (StringUtils.isEmpty(asin)) {
            return null;
        }
        List<ProductChannels> list = productChannelsService.lambdaQuery().eq(ProductChannels::getAsin, asin).eq(ProductChannels::getOrgId, organizationId).isNotNull(ProductChannels::getOperationUserId).list();
        if (CollectionUtil.isNotEmpty(list)) {
            return list.get(0).getOperationUserId().longValue();
        } else {
            return null;
        }
    }

    /**
     * 根据父asin获取处理人
     *
     * @param parentAsin
     * @param organizationId
     * @return
     */
    @Override
    public Long getOperatorIdByParentAsin(String parentAsin, Long organizationId) {

        if (StringUtils.isEmpty(parentAsin)) {
            return null;
        }
        List<ProductChannels> list = productChannelsService.lambdaQuery().eq(ProductChannels::getAsin1, parentAsin).eq(ProductChannels::getOrgId, organizationId).isNotNull(ProductChannels::getOperationUserId).list();
        if (CollectionUtil.isNotEmpty(list)) {
            return list.get(0).getOperationUserId().longValue();
        } else {
            return null;
        }
    }


    @Override
    public void downloadTemplate(HttpServletResponse response, Integer contextId) {
        List<String[]> downData = buildTemplateData(contextId);
        int[] downRows = {0, 1, 2, 3};
        int[] dateCells = {};
        String[] title = {
                "工单类型*",
                "店铺名称",
                "邮箱地址",
                "处理人*"
        };
        try {
            ExcelTemplateUtils.createExcelTemplate(this.getClass().getClassLoader().getResource("").getPath() + "templates/ticket/assign.xls",
                    title, downData, downRows, dateCells, response);
        } catch (Exception e) {
            throw new ErpCommonException("下载模板失败");
        }
    }

    private List<String[]> buildTemplateData(Integer contextId) {
        List<String[]> data = new ArrayList<>();
        List<ConfTicketAssign> confTicketAssigns = confTicketAssignMapper.selectImportTemplateTicketType();
        List<String> collect = confTicketAssigns.stream().map(ConfTicketAssign::getTicketType).distinct().collect(Collectors.toList());
        List<SysDictData> handleType = sysDictDataMapper.selectList(Wrappers.lambdaQuery(SysDictData.class).select(SysDictData::getDictLabel).eq(SysDictData::getDictType, "ticket_type").in(SysDictData::getDictValue, collect));
        List<ConfTicketAssign> handle = confTicketAssignMapper.selectHandleByOrgId(contextId);
        String[] handleArray = handle.stream().map(ConfTicketAssign::getHandleBy).distinct().toArray(String[]::new);

        MerchantEmail merchantEmail = new MerchantEmail();
        merchantEmail.setOrganizationId(contextId);
        List<MerchantEmail> merchantEmails = merchantEmailMapper.selectOutsideEmail(merchantEmail);
        List<Account> accounts = accountMapper.selectList(Wrappers.lambdaQuery(Account.class).eq(Account::getOrgId, contextId).eq(Account::getActive, "Y"));

        data.add(handleType.stream().map(SysDictData::getDictLabel).distinct().toArray(String[]::new));
        data.add(CollectionUtil.isEmpty(accounts) ? new String[0] : accounts.stream().map(Account::getTitle).toArray(String[]::new));
        data.add(CollectionUtil.isEmpty(merchantEmails) ? new String[0] : merchantEmails.stream().map(MerchantEmail::getEmail).toArray(String[]::new));

        data.add(handleArray);
        return data;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importTemplate(MultipartFile file, UserEntity authUserEntity, Integer contextId) {

        List<ConfTicketAssign> updateOrInsertList = new ArrayList<>();
        AssertUtil.isFalse(file == null, "请上传文件");
        String originalFilename = file.getOriginalFilename();
        AssertUtil.isFalse(StringUtil.isEmpty(originalFilename), "文件名为空");
        String suffix = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
        if (!suffix.equalsIgnoreCase("xlsx") && !suffix.equalsIgnoreCase("xls")) {
            throw new ErpCommonException("请选择excel文件");
        }
        List<ConTicketAssignImport> conTicketAssignImports = ExcelUtils.excelImportFilterAnnotation(file, ConTicketAssignImport.class);
        AssertUtil.isFalse(CollectionUtil.isEmpty(conTicketAssignImports), "表格数据为空");
        conTicketAssignImports.forEach(t -> t.setRowNum(conTicketAssignImports.indexOf(t) + 1));
        List<String[]> strings = this.buildTemplateData(contextId);
        if (CollectionUtil.isEmpty(strings)) {
            throw new ErpCommonException("模板下拉数据为空");
        }
        strings.forEach(t -> AssertUtil.isFalse(ObjectUtil.isEmpty(t), "模板下拉数据为空"));
        String[] ticketTypeArray = strings.get(0);
        String[] shopNameArray = strings.get(1);
        String[] emailArray = strings.get(2);
        String[] handleByArray = strings.get(3);
        for (ConTicketAssignImport anImport : conTicketAssignImports) {
            if (!Arrays.asList(ticketTypeArray).contains(anImport.getTicketType())) {
                throw new ErpCommonException(String.format("第[%s]行工单类型[%s]不符合条件,请选择客服或运营下的工单类型!", anImport.getRowNum(), anImport.getTicketType()));
            }
            if (StringUtils.isNotEmpty(anImport.getTitle()) && !Arrays.asList(shopNameArray).contains(anImport.getTitle())) {
                throw new ErpCommonException(String.format("第[%s]行店铺名称[%s]不符合条件,请选择正确的店铺名称!", anImport.getRowNum(), anImport.getTitle()));
            }
            if (StringUtils.isNotEmpty(anImport.getEmail()) && !Arrays.asList(emailArray).contains(anImport.getEmail())) {
                throw new ErpCommonException(String.format("第[%s]行邮箱地址[%s]不符合条件,请选择正确的邮箱地址!", anImport.getRowNum(), anImport.getEmail()));
            }
            if (!Arrays.asList(handleByArray).contains(anImport.getHandleBy())) {
                throw new ErpCommonException(String.format("第[%s]行处理人[%s]不符合条件,请选择正确的处理人!", anImport.getRowNum(), anImport.getHandleBy()));
            }
        }
        List<ConTicketAssignImport> email = conTicketAssignImports.stream().filter(t -> "站外信".equals(t.getTicketType()) && StringUtils.isEmpty(t.getEmail())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(email)) {
            throw new ErpCommonException(String.format("第[%s]行站外信类型邮箱不能为空", email.stream().map(t -> t.getRowNum().toString()).collect(Collectors.joining(","))));
        }
        List<ConTicketAssignImport> noEmail = conTicketAssignImports.stream().filter(t -> !"站外信".equals(t.getTicketType()) && StringUtils.isEmpty(t.getTitle())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(noEmail)) {
            throw new ErpCommonException(String.format("第[%s]行店铺名称不能为空", noEmail.stream().map(t -> t.getRowNum().toString()).collect(Collectors.joining(","))));
        }

        List<ConTicketAssignImport> noEmailData = conTicketAssignImports.stream().filter(t -> !"站外信".equals(t.getTicketType()) && StringUtils.isNotEmpty(t.getTitle())).collect(Collectors.toList());
        List<ConTicketAssignImport> emailData = conTicketAssignImports.stream().filter(t -> "站外信".equals(t.getTicketType()) && StringUtils.isNotEmpty(t.getEmail())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(noEmailData)) {
            noEmailData.stream().collect(Collectors.groupingBy(t -> t.getTicketType() + t.getTitle() + t.getHandleBy())).forEach((k, v) -> {
                if (v.size() > 1) {
                    throw new ErpCommonException(String.format("第[%s]行数据相同", v.stream().map(t -> t.getRowNum().toString()).collect(Collectors.joining(","))));
                }
            });
            noEmailData.stream().collect(Collectors.groupingBy(t -> t.getTicketType() + t.getTitle())).forEach((k, v) -> {
                if (v.size() > 1) {
                    throw new ErpCommonException(String.format("第[%s]工单类型店铺相同，处理人不同", v.stream().map(t -> t.getRowNum().toString()).collect(Collectors.joining(","))));
                }
            });


            List<String> ticketTypeList = noEmailData.stream().map(ConTicketAssignImport::getTicketType).distinct().collect(Collectors.toList());
            List<String> titleList = noEmailData.stream().map(ConTicketAssignImport::getTitle).distinct().collect(Collectors.toList());
            List<String> handleByList = noEmailData.stream().map(ConTicketAssignImport::getHandleBy).distinct().collect(Collectors.toList());
            Map<String, Integer> titleMapShopId = accountMapper.selectList(Wrappers.lambdaQuery(Account.class).eq(Account::getOrgId, contextId).eq(Account::getActive, "Y").in(Account::getTitle, titleList)).stream().collect(Collectors.toMap(Account::getTitle, Account::getId, (k, v) -> v));
            Map<String, String> labelMapValue = sysDictDataMapper.selectList(Wrappers.lambdaQuery(SysDictData.class).select(SysDictData::getDictLabel, SysDictData::getDictValue).eq(SysDictData::getDictType, "ticket_type").in(SysDictData::getDictLabel, ticketTypeList)).stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue, (k, v) -> v));
            Map<String, Long> handleByMapHandleById = confTicketAssignMapper.selectHandleByIdAndNameByOrgId(contextId, handleByList).stream().collect(Collectors.toMap(ConfTicketAssign::getHandleBy, ConfTicketAssign::getHandleById, (k, v) -> v));
            List<ConfTicketAssign> fromDbList = this.list(Wrappers.lambdaQuery(ConfTicketAssign.class).eq(ConfTicketAssign::getOrganizationId, contextId)
                    .in(ConfTicketAssign::getShopId, titleMapShopId.values().stream().distinct().collect(Collectors.toList()))
                    .in(ConfTicketAssign::getTicketType, labelMapValue.values().stream().distinct().collect(Collectors.toList())))
                    ;

            for (ConTicketAssignImport noEmailDatum : noEmailData) {
                ConfTicketAssign confTicketAssign = new ConfTicketAssign();
                confTicketAssign.setOrganizationId(contextId.longValue());
                confTicketAssign.setTicketType(labelMapValue.get(noEmailDatum.getTicketType()));
                confTicketAssign.setShopId(titleMapShopId.get(noEmailDatum.getTitle()).longValue());
                confTicketAssign.setHandleBy(noEmailDatum.getHandleBy());
                confTicketAssign.setHandleById(handleByMapHandleById.get(noEmailDatum.getHandleBy()));
                confTicketAssign.settingDefaultUpdate();
                confTicketAssign.settingDefaultCreate();
                if (CollectionUtil.isNotEmpty(fromDbList)) {
                    fromDbList.stream().filter(t -> confTicketAssign.getOrganizationId().equals(t.getOrganizationId()) && confTicketAssign.getShopId().equals(t.getShopId()) && confTicketAssign.getTicketType().equals(t.getTicketType())).findFirst().ifPresent(t -> {
                        confTicketAssign.setId(t.getId());
                        confTicketAssign.setCreatedAt(t.getCreatedAt());
                        confTicketAssign.setCreatedBy(t.getCreatedBy());
                        confTicketAssign.setCreatedName(t.getCreatedName());
                    });
                }
                updateOrInsertList.add(confTicketAssign);
            }
        }
        if (CollectionUtil.isNotEmpty(emailData)) {
            emailData.stream().collect(Collectors.groupingBy(t -> t.getTicketType() + t.getEmail() + t.getHandleBy())).forEach((k, v) -> {
                if (v.size() > 1) {
                    throw new ErpCommonException(String.format("第[%s]行数据相同", v.stream().map(t -> t.getRowNum().toString()).collect(Collectors.joining(","))));
                }
            });
            emailData.stream().collect(Collectors.groupingBy(t -> t.getTicketType() + t.getEmail())).forEach((k, v) -> {
                if (v.size() > 1) {
                    throw new ErpCommonException(String.format("第[%s]行工单类型邮箱地址相同，处理人不同", v.stream().map(t -> t.getRowNum().toString()).collect(Collectors.joining(","))));
                }
            });

            List<String> ticketTypeList = emailData.stream().map(ConTicketAssignImport::getTicketType).distinct().collect(Collectors.toList());
            List<String> eamilList = emailData.stream().map(ConTicketAssignImport::getEmail).distinct().collect(Collectors.toList());
            List<String> handleByList = emailData.stream().map(ConTicketAssignImport::getHandleBy).distinct().collect(Collectors.toList());

            Map<String, String> labelMapValue = sysDictDataMapper.selectList(Wrappers.lambdaQuery(SysDictData.class).select(SysDictData::getDictLabel, SysDictData::getDictValue).eq(SysDictData::getDictType, "ticket_type").in(SysDictData::getDictLabel, ticketTypeList)).stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue, (k, v) -> v));
            Map<String, Long> handleByMapHandleById = confTicketAssignMapper.selectHandleByIdAndNameByOrgId(contextId, handleByList).stream().collect(Collectors.toMap(ConfTicketAssign::getHandleBy, ConfTicketAssign::getHandleById, (k, v) -> v));
            List<ConfTicketAssign> fromDbList = this.list(Wrappers.lambdaQuery(ConfTicketAssign.class).eq(ConfTicketAssign::getOrganizationId, contextId)
                    .in(ConfTicketAssign::getEmail, eamilList)
                    .in(ConfTicketAssign::getTicketType, labelMapValue.values().stream().distinct().collect(Collectors.toList())))
                    ;

            for (ConTicketAssignImport noEmailDatum : emailData) {
                ConfTicketAssign confTicketAssign = new ConfTicketAssign();
                confTicketAssign.setOrganizationId(contextId.longValue());
                confTicketAssign.setTicketType(labelMapValue.get(noEmailDatum.getTicketType()));
                confTicketAssign.setEmail(noEmailDatum.getEmail());
                confTicketAssign.setHandleBy(noEmailDatum.getHandleBy());
                confTicketAssign.setHandleById(handleByMapHandleById.get(noEmailDatum.getHandleBy()));
                confTicketAssign.settingDefaultUpdate();
                confTicketAssign.settingDefaultCreate();
                if (CollectionUtil.isNotEmpty(fromDbList)) {
                    fromDbList.stream().filter(t -> confTicketAssign.getOrganizationId().equals(t.getOrganizationId()) && confTicketAssign.getEmail().equals(t.getEmail()) && confTicketAssign.getTicketType().equals(t.getTicketType())).findFirst().ifPresent(t -> {
                        confTicketAssign.setId(t.getId());
                        confTicketAssign.setCreatedAt(t.getCreatedAt());
                        confTicketAssign.setCreatedBy(t.getCreatedBy());
                        confTicketAssign.setCreatedName(t.getCreatedName());
                    });
                }
                updateOrInsertList.add(confTicketAssign);
            }
        }

        if (CollectionUtil.isNotEmpty(updateOrInsertList)) {
            this.saveOrUpdateBatch(updateOrInsertList);
        }

    }

}
