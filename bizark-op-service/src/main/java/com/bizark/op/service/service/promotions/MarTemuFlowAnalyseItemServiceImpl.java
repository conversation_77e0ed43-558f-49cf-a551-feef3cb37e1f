package com.bizark.op.service.service.promotions;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.op.api.entity.op.inventory.DwsInventoryArrivalPlanInfoEntity;
import com.bizark.op.api.entity.op.order.SaleOrderItems;
import com.bizark.op.api.entity.op.promotions.AmzPromotionCheck;
import com.bizark.op.api.entity.op.promotions.MarTemuFlowAnalyseItem;
import com.bizark.op.api.entity.op.promotions.MarTemuPromotionIdMapSellerSku;
import com.bizark.op.api.entity.op.promotions.MarTemuPromotionUnitSoldQuery;
import com.bizark.op.api.param.inventory.DwsInventoryArrivalPlanInfoPageVOParam;
import com.bizark.op.api.service.inventory.IDwsInventoryArrivalPlanInfoService;
import com.bizark.op.api.service.order.SaleOrderItemsService;
import com.bizark.op.api.service.promotions.MarTemuFlowAnalyseItemService;
import com.bizark.op.api.vo.deliveryschedule.DwsInventoryArrivalPlanInfoPageVO;
import com.bizark.op.common.util.BeanCopyUtils;
import com.bizark.op.common.util.DateUtil;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.service.mapper.promotions.AmzPromotionCheckMapper;
import com.bizark.op.service.mapper.promotions.MarTemuFlowAnalyseItemMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: Ailill
 * @Date: 2024/10/8 18:03
 */
@Service
@Slf4j
public class MarTemuFlowAnalyseItemServiceImpl extends ServiceImpl<MarTemuFlowAnalyseItemMapper, MarTemuFlowAnalyseItem> implements MarTemuFlowAnalyseItemService {

    @Autowired
    private SaleOrderItemsService saleOrderItemsService;

    @Autowired
    private IDwsInventoryArrivalPlanInfoService dwsInventoryArrivalPlanInfoService;

    @Autowired
    private AmzPromotionCheckMapper amzPromotionCheckMapper;



    @Value("${temu.flow.analyse.url}")
    private String QUERY_SALE_NUM_MASTER_URL;
    private static final String LASTED_SALE_UNIT = "/api/v1/SellerSkuSales";
    private static final String PROMOTION_SALE_UNIT = "/api/v1/PromotionSellerSkuSales";

    /**
     * @Description: 根据asins和日期，查询近7天的销售量和销售额
     * @Author: wly
     * @Date: 2024/10/9 10:48
     * @Params: [asins, date]
     * @Return: java.util.List<com.bizark.op.api.entity.op.promotions.MarTemuFlowAnalyseItem>
     **/
    public List<MarTemuFlowAnalyseItem> selectUnitSoldByAsinsAndDate(List<String> asins, Date date,Integer orgId) {

        List<MarTemuFlowAnalyseItem> result = new ArrayList<>();
        List<List<String>> partition = ListUtils.partition(asins, 3000);
        for (List<String> list : partition) {
            String endDate = DateUtil.convertDateToString(DateUtil.addDate(DateUtil.UtcToPacificDate(date), -1), "yyyy-MM-dd") + " 23:59:59";
            String startDate = DateUtil.addDate(endDate, -6) + " 00:00:00";
            JSONObject object = new JSONObject();
            object.put("sellerSkuList", list);
            object.put("dateFrom", startDate);
            object.put("dateTo", endDate);
            object.put("channel", "temu");
            object.put("orgId", orgId);
            HttpResponse response = HttpUtil.createPost(QUERY_SALE_NUM_MASTER_URL +LASTED_SALE_UNIT).body(object.toJSONString()).execute();
            if (response.getStatus() != 200) {
                log.error("查询近7天的销售量和销售额失败，返回状态码：{}", response.getStatus());
                continue;
            }
            List<JSONObject> jsonObjects = JSONObject.parseArray(response.body(), JSONObject.class);
            if (CollectionUtil.isEmpty(jsonObjects)) {
               continue;
            }
            for (JSONObject jsonObject : jsonObjects) {
                MarTemuFlowAnalyseItem item = new MarTemuFlowAnalyseItem();
                item.setShopId(jsonObject.getLong("shopId"));
                item.setProductSkuId(jsonObject.getString("sellerSku"));
                item.setUnitSold(jsonObject.getLong("unitSold"));
                item.setRevenue(jsonObject.getBigDecimal("revenue"));
                result.add(item);
            }
        }

        return result;
    }

    /**
     * @Description: 根据asins和日期，查询近7天的库存信息
     * @Author: wly
     * @Date: 2024/10/9 10:48
     * @Params: [asins, shopIds, orgId]
     * @Return: java.util.List<com.bizark.op.api.entity.op.promotions.MarTemuFlowAnalyseItem>
     **/
    @Override
    public List<MarTemuFlowAnalyseItem> selectStockInfoBySku(List<String> asins, List<Long> shopIds, Integer orgId) {

        List<MarTemuFlowAnalyseItem> result = new ArrayList<>();
        List<AmzPromotionCheck> amzPromotionChecks = amzPromotionCheckMapper.selectListByShopIdAndAsin(shopIds, asins);
        if (CollectionUtil.isEmpty(amzPromotionChecks)) {
            return result;
        }
        List<String> skuList = amzPromotionChecks.stream().map(AmzPromotionCheck::getErpSku).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(skuList)) {
            return result;
        }
       /* List<DwsInventoryArrivalPlanInfoEntity> dwsInventoryArrivalPlanInfoEntityList = this.baseMapper.listDwsInventoryArrivalPlanInfoEntity(orgId, skuList);
        if (CollectionUtil.isEmpty(dwsInventoryArrivalPlanInfoEntityList)) {
            return result;
        }
        for (DwsInventoryArrivalPlanInfoEntity dwsInventoryArrivalPlanInfoEntity : dwsInventoryArrivalPlanInfoEntityList) {
            // 第一层计算库存周转
            calculateTheFirstLevelInventoryTurnover(dwsInventoryArrivalPlanInfoEntity);
            // 单仓预警临时方案，后期正式方案上线可删除
            if (dwsInventoryArrivalPlanInfoEntity.getWarningType() != null && dwsInventoryArrivalPlanInfoEntity.getWarningType() == 1) {
                dwsInventoryArrivalPlanInfoEntity.setWarningTypeName("单仓预警");
            }
        }

        for (DwsInventoryArrivalPlanInfoEntity dwsInventoryArrivalPlanInfoEntity : dwsInventoryArrivalPlanInfoEntityList) {
            MarTemuFlowAnalyseItem marTemuFlowAnalyseItem = new MarTemuFlowAnalyseItem();
            marTemuFlowAnalyseItem.setSku(dwsInventoryArrivalPlanInfoEntity.getSku());
            marTemuFlowAnalyseItem.setNowStock(dwsInventoryArrivalPlanInfoEntity.getCurrentInventory());
            marTemuFlowAnalyseItem.setAllChainStock(dwsInventoryArrivalPlanInfoEntity.getTotalInventoryOfTheEntireChain());
            marTemuFlowAnalyseItem.setTurnoverNum(dwsInventoryArrivalPlanInfoEntity.getCurrentInventoryTurnover());
            marTemuFlowAnalyseItem.setWarningTypeName(dwsInventoryArrivalPlanInfoEntity.getWarningTypeName());
            result.add(marTemuFlowAnalyseItem);
        }*/
        return this.baseMapper.selectStockInfo(orgId, skuList);
    }

    private void calculateTheFirstLevelInventoryTurnover(DwsInventoryArrivalPlanInfoEntity dwsInventoryArrivalPlanInfoEntity) {
        Integer avgDeliveryDay7 = dwsInventoryArrivalPlanInfoEntity.getAvgDeliveryDay7();
        Integer avgDeliveryDay15 = dwsInventoryArrivalPlanInfoEntity.getAvgDeliveryDay15();
        Integer avgDeliveryDay30 = dwsInventoryArrivalPlanInfoEntity.getAvgDeliveryDay30();

        //现库周转计算
        Integer currentInventory = dwsInventoryArrivalPlanInfoEntity.getCurrentInventory();
        String currentInventoryTurnover = calculateInventoryTurnover(currentInventory, avgDeliveryDay7, avgDeliveryDay15, avgDeliveryDay30);
        dwsInventoryArrivalPlanInfoEntity.setCurrentInventoryTurnover(currentInventoryTurnover);

    }

    /**
     * 计算库存周转的公共方法
     * 处理了除数为null和0的情况 防止异常
     */
    private String calculateInventoryTurnover(Integer inventory, Integer avgSalesDay7, Integer avgSalesDay15, Integer avgSalesDay30) {
        if (avgSalesDay7 == null || avgSalesDay7 == 0) {
            avgSalesDay7 = 1;
        }
        if (avgSalesDay15 == null || avgSalesDay15 == 0) {
            avgSalesDay15 = 1;
        }
        if (avgSalesDay30 == null || avgSalesDay30 == 0) {
            avgSalesDay30 = 1;
        }
        BigDecimal inventoryTurnoverDay7 = NumberUtil.div(inventory, avgSalesDay7, 0);
        BigDecimal inventoryTurnoverDay15 = NumberUtil.div(inventory, avgSalesDay15, 0);
        BigDecimal inventoryTurnoverDay30 = NumberUtil.div(inventory, avgSalesDay30, 0);
        return StrUtil.format("{} | {} | {}", inventoryTurnoverDay7, inventoryTurnoverDay15, inventoryTurnoverDay30);
    }

    @Override
    public List<MarTemuPromotionIdMapSellerSku> selectUnitSoldByAsinsAndDateAndShopId(MarTemuPromotionUnitSoldQuery query) {

        List<JSONObject> jsonObjectResult = new ArrayList<>();
        List<Long> idList = query.getIdMapSellerSku().stream().map(t -> t.getId()).collect(Collectors.toList());
        List<List<Long>> partition = ListUtils.partition(idList, 3000);
        for (List<Long> list : partition) {
            JSONObject object = new JSONObject();
            object.put("id", list);
            HttpResponse response = HttpUtil.createPost(QUERY_SALE_NUM_MASTER_URL + PROMOTION_SALE_UNIT).body(object.toJSONString()).execute();
            if (response.getStatus() != 200) {
                log.error("查询近7天的销售量和销售额失败，返回状态码：{}}", response.getStatus());
                continue;
            }
            List<JSONObject> jsonObjects = JSONObject.parseArray(response.body(), JSONObject.class);
            if (CollectionUtil.isEmpty(jsonObjects)) {
                continue;
            }
            jsonObjectResult.addAll(jsonObjects);
        }

        return JSONObject.parseArray(JSONObject.toJSONString(jsonObjectResult), MarTemuPromotionIdMapSellerSku.class);

    }
}
