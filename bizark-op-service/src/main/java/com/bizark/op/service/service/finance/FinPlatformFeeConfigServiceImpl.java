package com.bizark.op.service.service.finance;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.common.exception.AppRuntimeException;
import com.bizark.common.exception.CommonException;
import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.finance.FinPlatformFeeConfig;
import com.bizark.op.api.entity.op.finance.FinPlatformFeeConfigParam;
import com.bizark.op.api.entity.op.finance.FinPlatformFeeConfigSaveParam;
import com.bizark.op.api.entity.op.finance.FinPlatformFeeConfigVo;
import com.bizark.op.api.entity.op.finance.excel.FinPlatformFeeConfigExcel;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.entity.op.ticket.order.AmzVcPoOrderV;
import com.bizark.op.api.service.finance.FinPlatformFeeConfigService;
import com.bizark.op.api.service.sale.ProductChannelsService;
import com.bizark.op.api.service.sys.ISysDictDataService;
import com.bizark.op.api.service.sys.ISysDictTypeService;
import com.bizark.op.common.config.WeChatBootConfigure;
import com.bizark.op.common.core.domain.sys.SysDictData;
import com.bizark.op.common.util.ExcelUtils;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.common.util.WeComRobotUtil;
import com.bizark.op.common.util.poi.ExcelUtil;
import com.bizark.op.common.util.spring.SpringUtils;
import com.bizark.op.service.mapper.account.AccountMapper;
import com.bizark.op.service.mapper.finance.FinPlatformFeeConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FinPlatformFeeConfigServiceImpl extends ServiceImpl<FinPlatformFeeConfigMapper, FinPlatformFeeConfig> implements FinPlatformFeeConfigService {

    @Autowired
    private ISysDictTypeService iSysDictTypeService;

    @Autowired
    private ISysDictDataService iSysDictDataService;

    @Autowired
    private FinPlatformFeeConfigMapper finPlatformFeeConfigMapper;

    @Autowired
    private AccountMapper accountMapper;

    @Autowired
    private WeChatBootConfigure weChatBootConfigure;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    private BigDecimal newDecimal(String decimal){
        if (Objects.isNull(decimal)){
            return null;
        }
        if (!NumberUtil.isNumber(decimal)){
            return null;
        }
        return new BigDecimal(decimal);
    }

    private String getKey(FinPlatformFeeConfig conf){
        return StrUtil.join("-",conf.getKeyType(),conf.getKeyStr(),conf.getFeeType(),conf.getSaleChannel());
    }

    // 内存校验
    private boolean memoryValidate(List<FinPlatformFeeConfig> beforeList,FinPlatformFeeConfig after){
        if (CollUtil.isEmpty(beforeList)){
            return true;
        }
//        for (FinPlatformFeeConfig conf : beforeList){
//            if (after.overlaps(conf)){
//                return false;
//            }
//        }
        return true;
    }
    // 磁盘校验
    private boolean dbValidate(FinPlatformFeeConfig memory){
        List<FinPlatformFeeConfig> configList = this.baseMapper.search(memory);
        return memoryValidate(configList, memory);
    }

    private boolean dbUpdateValidate(FinPlatformFeeConfig memory){
        List<FinPlatformFeeConfig> configList = this.baseMapper.search(memory);
        if (CollUtil.isEmpty(configList)){
            return true;
        }
        int i = 0;
        boolean remove = false;
        for (FinPlatformFeeConfig config : configList){
            if (Objects.equals(config.getId(), memory.getId())){
                remove = true;
                break;
            }
            i++;
        }
        if (remove){
            configList.remove(i);
        }
        return memoryValidate(configList, memory);
    }


    private String alterMsgStr(FinPlatformFeeConfigExcel excelConf){
        return StrUtil.join("-", excelConf.getShopName(),excelConf.getKeyStr(),excelConf.getFeeType(), excelConf.getConfigType());
    }

    @Override
    public  List<FinPlatformFeeConfig> importExcel(MultipartFile file, Integer contextId) {
        ExcelUtil<FinPlatformFeeConfigExcel> util = new ExcelUtil<>(FinPlatformFeeConfigExcel.class);
        List<FinPlatformFeeConfigExcel> finPlatformFeeConfigs = null;
        try {
            finPlatformFeeConfigs = util.importExcel(file.getInputStream());
        } catch (Exception e) {
            throw new AppRuntimeException("解析文件异常", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }

//        List<FinPlatformFeeConfigExcel> finPlatformFeeConfigs = ExcelUtils.excelImportFilterAnnotation(file, FinPlatformFeeConfigExcel.class);
        // 文件内容为空
        if (CollUtil.isEmpty(finPlatformFeeConfigs)){
            throw new AppRuntimeException("文件内容为空", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }

        //过滤演示数据
        finPlatformFeeConfigs = finPlatformFeeConfigs.stream().filter(item -> !"TEST-123".equals(item.getKeyStr())).collect(Collectors.toList());


        //对存在ID的行进行校验
        List<Integer> ids = finPlatformFeeConfigs.stream().filter(item -> item.getId() != null).map(FinPlatformFeeConfigExcel::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ids)){
            List<Integer> dbList = this.lambdaQuery().in(FinPlatformFeeConfig::getId, ids).list().stream().map(FinPlatformFeeConfig::getId).collect(Collectors.toList());
            if (CollUtil.isEmpty(dbList)){
                throw new AppRuntimeException("ID均不存在系统中");
            }
            List<Integer> importList = ids.stream().filter(item -> !dbList.contains(item)).collect(Collectors.toList());
            if (!CollUtil.isEmpty(importList)){
                throw new AppRuntimeException("以下ID不存在系统中,请调整:" + importList.stream().map(String::valueOf).collect(Collectors.joining(",")));
            }
        }

        List<FinPlatformFeeConfigExcel> exFeeTypeFlag = finPlatformFeeConfigs.stream().filter(item -> StringUtils.isEmpty(item.getShopName())
                || StringUtils.isEmpty(item.getFeeType()) || StringUtils.isEmpty(item.getConfigType()) ||
                item.getStartDate()==null ||item.getStartDate()==null
        ).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(exFeeTypeFlag)) {
            throw new AppRuntimeException("店铺/费用类型/配置方式/生效开始和结束时间,不能为空", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }

       if (!finPlatformFeeConfigs.stream().allMatch(item->item.getFeeType().equals("平台仓尾程折扣")||item.getFeeType().equals("佣金") ||item.getFeeType().equals("退款率"))){
            throw new AppRuntimeException("费用类型仅支持：平台仓尾程折扣 或 佣金", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }

        if (!finPlatformFeeConfigs.stream().allMatch(item->item.getConfigType().equals("比例")||item.getConfigType().equals("金额"))){
            throw new AppRuntimeException("配置方式仅支持：比例 或 金额", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }

        List<FinPlatformFeeConfigExcel> exFeeTypeContentFlag = finPlatformFeeConfigs.stream().filter(item -> item.getFeeType().equals("佣金")&&(StringUtils.isEmpty(item.getKeyStr()) && StringUtils.isEmpty(item.getSku()))).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(exFeeTypeContentFlag)) {
            throw new AppRuntimeException("佣金费用类型SellerSku和Sku有且仅有一个存在", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }

        List<FinPlatformFeeConfigExcel> exFeeTypeContentFlagExtra = finPlatformFeeConfigs.stream().filter(item -> item.getFeeType().equals("佣金")&&(!StringUtils.isEmpty(item.getKeyStr()) && !StringUtils.isEmpty(item.getSku()))).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(exFeeTypeContentFlagExtra)) {
            throw new AppRuntimeException("佣金费用类型SellerSku和Sku有且仅有一个存在", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }

        //设置时间
        finPlatformFeeConfigs.forEach(item-> {
            if(item.getStartDate().after(item.getEndDate())){
                throw new AppRuntimeException( alterMsgStr(item)+ "生效开始时间不可大于结束时间", ApiResponseResult.CODE_CLIENT_BADREQUEST);
            }
        });

        //费用类型 校验-比例
        List<FinPlatformFeeConfigExcel>  percentageFlagList= finPlatformFeeConfigs.stream().filter(
                item -> "比例".equals(item.getConfigType()) &&
                        (item.getFeePercentage() == null || (BigDecimal.ZERO.compareTo(new BigDecimal(item.getFeePercentage())) > 0 || new BigDecimal(100).compareTo(new BigDecimal(item.getFeePercentage())) < 0))).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(percentageFlagList)) {
            throw new AppRuntimeException("配置方式:比例,不可小于0或大于100", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }
        //费用类型 校验-金额
        List<FinPlatformFeeConfigExcel>  FeeFlagList= finPlatformFeeConfigs.stream().filter(
                item -> "金额".equals(item.getConfigType()) &&
                        StringUtils.isEmpty(item.getFee())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(FeeFlagList)) {
            throw new AppRuntimeException("配置方式:金额，金额不能为空", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }




        //判断店铺是否存在,根据店铺名查询所有店铺信息
        List<String> shopNames = finPlatformFeeConfigs.stream().map(FinPlatformFeeConfigExcel::getShopName).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<Account> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Account::getId, Account::getTitle,Account::getType,Account::getFlag);
        queryWrapper.in(Account::getTitle, shopNames);
        queryWrapper.in(Account::getActive, "Y");
        queryWrapper.in(Account::getOrgId, contextId);
        List<Account> accounts = accountMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(accounts)) {
            throw new AppRuntimeException("店铺名不存在", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }
        List<@NotNull String> dbShopName = accounts.stream().map(Account::getTitle).collect(Collectors.toList());
        List<String> noShopNames = shopNames.stream().filter(item -> !dbShopName.contains(item)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(noShopNames)) {
            String shopNameFlag = noShopNames.stream().collect(Collectors.joining(","));
            throw new AppRuntimeException("以下店铺名称不存在：" + shopNameFlag, ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }
        Map<@NotNull String, Account> shopMaps = accounts.stream().collect(Collectors.toMap(Account::getTitle, Function.identity(), (a, b) -> a));

        //判断是否有重复行 (店铺+sellerSku+费用类型 +开始时间和结束时间完全相同)
        Map<String, Long> countMap = finPlatformFeeConfigs.stream()
                .collect(Collectors.groupingBy(p -> p.getShopName() + "_" + (StringUtils.isEmpty(p.getKeyStr()) ? null : p.getKeyStr())
                                + p.getFeeType() + "_" + p.getStartDate() + "_" + p.getEndDate()
                        , Collectors.counting()));
        List<String> duplicates = countMap.entrySet().stream()
                .filter(entry -> entry.getValue() > 1)  // 筛选重复项
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(duplicates)) {
            throw new AppRuntimeException("导入文件存在相同时间区间的记录：" + duplicates.stream().collect(Collectors.joining(",")), ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }


        HashMap<String, String> shopSellerSKu = new HashMap<>();
        StringBuilder failureMsg = new StringBuilder();
        //selelrSku根据店铺维度
        Map<String, List<FinPlatformFeeConfigExcel>> shopConfigMap = finPlatformFeeConfigs.stream().filter(item -> !StringUtils.isEmpty(item.getKeyStr())).collect(Collectors.groupingBy(FinPlatformFeeConfigExcel::getShopName));
        List<CompletableFuture<Object>> apportionFutures = shopConfigMap.keySet().stream().map(
                shopName ->
                        CompletableFuture.supplyAsync(() -> {

                            //获取店铺
                            Account account = shopMaps.get(shopName);
                            List<FinPlatformFeeConfigExcel> finPlatformFeeConfigExcels = shopConfigMap.get(shopName);
                            //本次店铺下所有的sellerSKU
                            List<String> sellerSKu = finPlatformFeeConfigExcels.stream().map(FinPlatformFeeConfigExcel::getKeyStr).collect(Collectors.toList());
                            List<ProductChannels> productChannelsList = this.baseMapper.selectSellerSkuShopQuery(account.getFlag(), sellerSKu);

                            if (CollectionUtils.isEmpty(productChannelsList)) {
                                shopSellerSKu.put(account.getTitle(), sellerSKu.stream().collect(Collectors.joining(",")));
                                String msg = "<br/>" + "店铺：" + account.getTitle() + "  SellerSku：" + sellerSKu.stream().collect(Collectors.joining(","));
                                failureMsg.append(msg);
                            } else {
                                List<String> dbSellerSku = productChannelsList.stream().map(ProductChannels::getSellerSku).collect(Collectors.toList());
                                //不在db中的数据
                                String noDbSellerSku = sellerSKu.stream().filter(item -> !dbSellerSku.contains(item)).collect(Collectors.joining(","));
                                if (!StringUtils.isEmpty(noDbSellerSku)) {
                                    shopSellerSKu.put(account.getTitle(), noDbSellerSku);
                                    String msg = "<br/>" + "店铺：" + account.getTitle() + "  SellerSku：" + noDbSellerSku;
                                    failureMsg.append(msg);
                                }
                            }
                            return null;
                        }, taskExecutor)
        ).collect(Collectors.toList());

        CompletableFuture.allOf(apportionFutures.toArray(new CompletableFuture<?>[0])).join();

        failureMsg.insert(0, "以下数据SellerSku系统未维护");

        if (shopSellerSKu.size() >= 1) {
            throw new AppRuntimeException(failureMsg.toString(), ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }





        //错误信息
        List<FinPlatformFeeConfig> finPlatformFeeConfigsError = new ArrayList<>();
        //成功信息
        List<FinPlatformFeeConfig> finPlatformFeeConfigsSuccess = new ArrayList<>();
        // 进行对象转换
        List<FinPlatformFeeConfig> transitionConfig = finPlatformFeeConfigs.stream().map(excelConf -> {
            FinPlatformFeeConfig conf = FinPlatformFeeConfig.builder()
                    .id(excelConf.getId()) //主键ID
                    .keyType(1)//默认为1（代表sellersku）
                    .organizationId(contextId)
                    .keyStr(StringUtils.isEmpty(excelConf.getKeyStr()) ? null : excelConf.getKeyStr()) //SEllserSKu
                    .build();
            Account account = shopMaps.get(excelConf.getShopName());
            if (account != null) {
                conf.setSaleChannel(account.getType());
                conf.setShopId(account.getId());
                conf.setShopName(account.getTitle());
            }
            if (StrUtil.equals("佣金", excelConf.getFeeType())) {
                conf.setFeeType(1);
            } else if (StrUtil.equals("平台仓尾程折扣", excelConf.getFeeType())) {
                conf.setFeeType(2);
            }else if (StrUtil.equals("退款率", excelConf.getFeeType())) {
                conf.setFeeType(3);
            }
            if (StrUtil.equals("比例", excelConf.getConfigType())) {
                conf.setConfigType(1);
                conf.setConfig(newDecimal(excelConf.getFeePercentage()));
            } else if (StrUtil.equals("金额", excelConf.getConfigType())) {
                conf.setConfigType(2);
                conf.setConfig(newDecimal(excelConf.getFee()));
                conf.setCurrency(excelConf.getCurrency());
                if (StrUtil.isBlank(conf.getCurrency())) {
                    conf.setCurrency("USD");
                }
            }

            conf.setStartDate(excelConf.getStartDate());
            conf.setEndDate(excelConf.getEndDate());
            conf.settingDefaultCreate();

            // 补充sku字段
            if (StringUtils.isNotEmpty(excelConf.getSku())){
                conf.setSku(excelConf.getSku());
                conf.setKeyType(2);   // 代表sku
            }
            conf.setSaleChannel(excelConf.getSaleChannel());
            if (conf.getId() != null) {
                finPlatformFeeConfigsSuccess.add(conf);
            }else {
                //查询内容DB校验
                FinPlatformFeeConfig reqQuery = FinPlatformFeeConfig.builder().build();
                reqQuery.setOrganizationId(contextId);
                reqQuery.setShopId(conf.getShopId());
                reqQuery.setFeeType(conf.getFeeType());
                reqQuery.setKeyStr(conf.getKeyStr());
                reqQuery.setSku(conf.getSku());
                reqQuery.setStartDate(conf.getStartDate());
                reqQuery.setEndDate(conf.getEndDate());
                List<FinPlatformFeeConfigVo> resList = finPlatformFeeConfigMapper.searchPlatFromFeeVerify(reqQuery);
                if (!CollectionUtils.isEmpty(resList)) {
                    finPlatformFeeConfigsError.add(conf);
                }else {
                    finPlatformFeeConfigsSuccess.add(conf);
                }
            }
            return conf;
        }).collect(Collectors.toList());


        //文本内容自校验
        Map<String, List<FinPlatformFeeConfig>> groupMap = finPlatformFeeConfigsSuccess.stream()
                .collect(Collectors.groupingBy(p -> p.getShopName() + "_" + (StringUtils.isEmpty(p.getKeyStr()) ? null : p.getKeyStr())
                + p.getFeeType()
                ));
        for (String shopSellerSku : groupMap.keySet()) {
            List<FinPlatformFeeConfig> finPlatformFeeConfigExcels = groupMap.get(shopSellerSku);
            List<FinPlatformFeeConfig> overlappingPersons = finPlatformFeeConfigExcels.stream()
                    .filter(p1 -> finPlatformFeeConfigExcels.stream()
                            .anyMatch(p2 -> p1 != p2 && fileDateVerify(p1, p2))
                    ).collect(Collectors.toList());
            finPlatformFeeConfigsError.addAll(overlappingPersons);
        }




        if (CollectionUtils.isEmpty(finPlatformFeeConfigsError)) {
            List<FinPlatformFeeConfig> update = finPlatformFeeConfigsSuccess.stream().filter(item -> item.getId() != null).collect(Collectors.toList());
            List<FinPlatformFeeConfig> save = finPlatformFeeConfigsSuccess.stream().filter(item -> item.getId() == null).collect(Collectors.toList());

            //新增
            if (CollectionUtils.isNotEmpty(save)) {
                this.saveBatch(save);
            }
            //更新
            if (CollectionUtils.isNotEmpty(update)) {
                update.stream().forEach(item -> {

                    item.settingDefaultUpdate();
                    LambdaUpdateWrapper<FinPlatformFeeConfig> wrapperUpdate = new LambdaUpdateWrapper<>();
                    wrapperUpdate.eq(FinPlatformFeeConfig::getId, item.getId())
                            .set(FinPlatformFeeConfig::getShopId, item.getShopId()) //店铺ID
                            .set(FinPlatformFeeConfig::getKeyStr, item.getKeyStr()) //sellerSKU
                            .set(FinPlatformFeeConfig::getSaleChannel, item.getSaleChannel()) //渠道
                            .set(FinPlatformFeeConfig::getSku,item.getSku())
                            .set(FinPlatformFeeConfig::getFeeType, item.getFeeType()) //费用类型 （佣金，平台仓尾程折扣）
                            .set(FinPlatformFeeConfig::getConfigType, item.getConfigType()) //配置方式 1比例 2金额
                            .set(FinPlatformFeeConfig::getConfig, item.getConfig()) //实际配置值
                            .set(FinPlatformFeeConfig::getCurrency, item.getCurrency()) //币种
                            .set(FinPlatformFeeConfig::getStartDate, item.getStartDate())//开始时间
                            .set(FinPlatformFeeConfig::getEndDate, item.getEndDate())//结束时间
                            .set(FinPlatformFeeConfig::getUpdatedAt, item.getUpdatedAt())//结束时间
                            .set(FinPlatformFeeConfig::getUpdatedBy, item.getUpdatedBy())//结束时间
                            .set(FinPlatformFeeConfig::getUpdatedName, item.getUpdatedName())//结束时间
                            .set(FinPlatformFeeConfig::getSaleChannel, item.getSaleChannel());//渠道
                            this.baseMapper.update(null, wrapperUpdate);
                        }
                );
            }

        }
        return finPlatformFeeConfigsError;
    }




    private static boolean fileDateVerify(FinPlatformFeeConfig p1, FinPlatformFeeConfig p2) {
        return !(p1.getEndDate().before(p2.getStartDate())
                || p1.getStartDate().after(p2.getEndDate()));
    }




    private String convertPercentageStr(BigDecimal amount){
        if (Objects.isNull(amount)){
            return "";
        }
        // 乘以100
        // 格式化
        DecimalFormat df = new DecimalFormat("0.##");
        return df.format(amount) + "%";
    }

    @Override
    public void export(FinPlatformFeeConfigParam searchParam, HttpServletResponse response) {
        List<FinPlatformFeeConfigVo> finPlatformFeeConfigs = this.baseMapper.searchByParam(searchParam);
        if (CollUtil.isEmpty(finPlatformFeeConfigs)){
            return;
        }
        List<SysDictData> dictData = SpringUtils.getBean(ISysDictTypeService.class).selectDictDataByType("account_sale_channel");
        if (CollUtil.isEmpty(dictData)){
            throw new CommonException("销售渠道字典表数据缺失,请联系相关人员维护");
        }
        try {
            List<Map<String, Object>> excelList = finPlatformFeeConfigs.stream().map(tendency -> {
                Map<String, Object> map = new LinkedHashMap<>(16);
                map.put("id", tendency.getId());
                map.put("SellerSku", tendency.getKeyStr());
                map.put("Sku", tendency.getSku()); //渠道
                map.put("店铺名", tendency.getShopName());
                map.put("费用类型", tendency.getFeeType() == 1 ? "佣金" : tendency.getFeeType() ==  2 ?"平台仓尾程折扣" : "退款率");
                map.put("配置方式", tendency.getConfigType() == 1 ? "比例" : "金额");
//                map.put("费用比例", tendency.getConfigType() == 1 ? convertPercentageStr(tendency.getConfig()) : "" );
                map.put("费用比例（%）", tendency.getConfigType() == 1 ? tendency.getConfig() : "" );
                map.put("费用金额", tendency.getConfigType() == 2 ? tendency.getConfig() : "");
                map.put("币种", tendency.getCurrency());
                Date start = tendency.getStartDate();
                String startStr = "";
                if (Objects.nonNull(start)){
                    startStr = DateUtil.format(start, DatePattern.NORM_DATETIME_MINUTE_FORMAT);
                }
                Date end = tendency.getEndDate();
                String endStr = "";
                if (Objects.nonNull(end)){
                    endStr = DateUtil.format(end, DatePattern.NORM_DATETIME_MINUTE_FORMAT);
                }
                map.put("生效开始时间",  startStr);
                map.put("生效结束时间", endStr);
                map.put("创建时间", DateUtil.format(tendency.getCreatedAt(), DatePattern.NORM_DATETIME_PATTERN));
                // 匹配销售渠道
                if (tendency.getSaleChannel() != null && StringUtils.isNotEmpty(tendency.getSaleChannel())){
                    dictData.stream().filter(item -> item.getDictValue().equals(tendency.getSaleChannel())).findFirst().ifPresent(item -> map.put("销售渠道", item.getDictLabel()));
                }else {
                    map.put("销售渠道", "");
                }
                return map;
            }).collect(Collectors.toList());
            ExcelUtils.easyExcelExport(excelList, "FinPlatformFeeConfig" + DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN), response);
        } catch (IOException e) {
            log.error("文件导出错误 : {}", ExceptionUtil.stacktraceToString(e));
            throw new AppRuntimeException("文件导出错误",ApiResponseResult.CODE_SERVER_INTERNALERROR);
        }
    }

    @Override
    public List<FinPlatformFeeConfigVo> list(FinPlatformFeeConfigParam searchParam) {
        List<FinPlatformFeeConfigVo> finPlatformFeeConfigs = this.baseMapper.searchByParam(searchParam);
        if (CollUtil.isEmpty(finPlatformFeeConfigs)){
            return new ArrayList<>();
        }
        return finPlatformFeeConfigs;
    }

    @Override
    public void delete(List<Long> ids) {
        if (CollUtil.isEmpty(ids)){
            throw new AppRuntimeException("未选中数据",ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }
        this.baseMapper.delete(ids);
    }


    @Override
    public void saveOrModify(FinPlatformFeeConfigSaveParam config) {
        FinPlatformFeeConfig conf = FinPlatformFeeConfig.builder().build();
        BeanUtil.copyProperties(config,conf);
        conf.setKeyType(1);//默认TYPE 类型为1
        validate(conf); //非空校验
        conf.setCurrency(config.getConfigType() == 1 ? "" : conf.getCurrency()); // 1.佣金  2.平台仓尾程折扣   不设置比重
        conf.setKeyStr(StringUtils.isEmpty(conf.getKeyStr())?null:conf.getKeyStr());
        if (StringUtils.isNotEmpty(config.getSku())){
            conf.setSku(conf.getSku());
            conf.setKeyType(2);
        }

        FinPlatformFeeConfig reqQuery = FinPlatformFeeConfig.builder().build();
        reqQuery.setOrganizationId(conf.getOrganizationId());
        reqQuery.setShopId(conf.getShopId());
        reqQuery.setKeyStr(conf.getKeyStr());
        reqQuery.setFeeType(conf.getFeeType());
        reqQuery.setStartDate(conf.getStartDate());
        reqQuery.setEndDate(conf.getEndDate());
        reqQuery.setId(config.getId());
        if (StringUtils.isNotEmpty(conf.getSku())){
            reqQuery.setSku(conf.getSku());
            reqQuery.setKeyType(2);
        }


        if (StringUtils.isNotEmpty(conf.getKeyStr())) {
            Account account = accountMapper.selectById(conf.getShopId());
            if (account == null) {
                throw new AppRuntimeException("店铺不存在");
            }
            List<ProductChannels> productChannelsList = this.baseMapper.selectSellerSkuShopQuery(account.getFlag(), Arrays.asList(conf.getKeyStr()));
            if (CollectionUtils.isEmpty(productChannelsList)){
                throw new AppRuntimeException("SellerSku在该店铺下不存在或销售状态非在售、停售");
            }
        }

        List<FinPlatformFeeConfigVo> resList = this.baseMapper.searchPlatFromFeeVerify(reqQuery);
        if (!CollectionUtils.isEmpty(resList)){
            throw new AppRuntimeException("已存在相同时间区间的记录", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }

        if (conf.getId() == null){
            conf.settingDefaultCreate();
            finPlatformFeeConfigMapper.insert(conf);
        }else {
            conf.settingDefaultUpdate();
            finPlatformFeeConfigMapper.updateById(conf);
        }

    }




    private void validate(FinPlatformFeeConfig conf){
        if (conf.getFeeType() == null){
            throw new AppRuntimeException("费用类型不可为空", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }
        //1.佣金 2.平台仓尾程折扣
        //佣金的情况下 SellerSKU必填
        if (new Integer(1).equals(conf.getFeeType())&&StrUtil.isBlank(conf.getKeyStr()) && StrUtil.isBlank(conf.getSku())){
            throw new AppRuntimeException("SellerSku和Sku有且仅有一个存在", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }
        if (!StrUtil.isBlank(conf.getKeyStr()) && !StrUtil.isBlank(conf.getSku())){
            throw new AppRuntimeException("SellerSku和Sku有且仅有一个存在", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }
        if (conf.getShopId()==null){
            throw new AppRuntimeException("店铺不可为空", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }
        if (null == conf.getConfig()){
            throw new AppRuntimeException("费用或费用比例不可为空", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }
        if (null == conf.getStartDate()){
            throw new AppRuntimeException("生效开始时间不可为空", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }
        if (null == conf.getEndDate()){
            throw new AppRuntimeException("生效结束时间不可为空", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }
        if (conf.getConfigType() == null){
            throw new AppRuntimeException("配置类型不可为空", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }

        if (conf.getConfigType() == 1){  //比例
            conf.setCurrency(""); //佣金
            if (BigDecimal.ZERO.compareTo(conf.getConfig()) > 0  || new BigDecimal(100).compareTo(conf.getConfig()) < 0){
                throw new AppRuntimeException("费用比例不可小于0或大于100", ApiResponseResult.CODE_CLIENT_BADREQUEST);
            }
        }
        if (conf.getConfigType() == 2 && StrUtil.isBlank(conf.getCurrency())){ //金额
            throw new AppRuntimeException("币种不可为空", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }

        if (conf.getStartDate().after(conf.getEndDate())) {
            throw new AppRuntimeException("生效开始时间不可大于结束时间", ApiResponseResult.CODE_CLIENT_BADREQUEST);
        }



    }


    /**
     * @description: 费用类型查询
     * @author: Moore
     * @date: 2025/3/26 10:07
     * @param
     * @param finPlatformFeeConfigParam
     * @return: com.bizark.op.api.entity.op.finance.FinPlatformFeeConfigVo
     **/
    @Override
    public FinPlatformFeeConfigVo queryPlaftormFee(FinPlatformFeeConfigParam finPlatformFeeConfigParam) {
        FinPlatformFeeConfigVo finPlatformFeeConfigVo = this.baseMapper.searchPlagFormFee(finPlatformFeeConfigParam);
        if (finPlatformFeeConfigVo == null) {
            finPlatformFeeConfigParam.setKeyStr(null); //去除SellerSKu在查询一次
            finPlatformFeeConfigVo = this.baseMapper.searchPlagFormFee(finPlatformFeeConfigParam);
        }
        try {
            if (finPlatformFeeConfigVo == null) {
                Account account = accountMapper.selectById(finPlatformFeeConfigParam.getShopId());
                WeComRobotUtil.sendTextMsgWithAtNew("店铺：" + (account != null ? account.getTitle() : "") + " 未配置平台仓尾程折扣", "Nancy,Zoe", null, "WALMART_FEE", weChatBootConfigure);
            }
        } catch (Exception e) {
            log.info("发送通知失败：{}",e);
            e.printStackTrace();
        }
        return finPlatformFeeConfigVo;
    }

}
