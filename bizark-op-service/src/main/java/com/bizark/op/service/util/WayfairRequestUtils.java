package com.bizark.op.service.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSON;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.service.account.AccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> @ClassName WayfairRequestUtils
 * @description: TODO
 * @date 2024年06月19日
 */

@Component
@Slf4j
public class WayfairRequestUtils {


    @Autowired
    private AccountService accountService;


    @Autowired
    private AccessTokenUtils accessTokenUtils;
    /**
     * @param
     * @param shopId
     * @description: 获取wayfair Token信息
     * @author: Moore
     * @date: 2024/6/19 18:20
     * @return: java.lang.String
     **/
    public String getAccessToken(Integer shopId) {
        if (shopId == null) {
            return null;
        }
        Account account = accountService.lambdaQuery()
                .select(Account::getAdAccessToken)
                .eq(Account::getId, shopId)
                .one();
        if (Objects.isNull(account)) {
            log.error("wayfair auth error [{}] : account not found ", shopId);
            return null;
        }
        return account.getAdAccessToken();
    }


/*    *//**
     * @param
     * @description: wayfair 修改接口
     * @author: Moore
     * @date: 2024/6/19 18:24
     * @return: java.lang.String
     **//*
    public String sendMutation(String requestUrl, Integer shopId, GraphqlMutation graphqlMutation) {
        GraphqlClient graphqlClient = GraphqlClient.buildGraphqlClient(requestUrl);
        Map<String, String> httpHeaders = new HashMap<>();
        httpHeaders.put("content-type", "application/json");
        httpHeaders.put("Accept", "application/json");
//        httpHeaders.put("authorization", "Bearer " + this.getAccessToken(shopId));
        httpHeaders.put("authorization", "Bearer " + "eyJhbGciOiJSUzI1NiIsImtpZCI6InBlRG5BMWVVVGRVQU00YVdjU3FnZm40ZEJhbFZCYnJ4R2ZEU0ZQYXVQbG8iLCJ0eXAiOiJKV1QiLCJ4NXQiOiI3cC1BTnZudER3QVB1OWh4djNobnlZOVFweE0iLCJqa3UiOiJodHRwczovL3Nzby5hdXRoLndheWZhaXIuY29tLy53ZWxsLWtub3duL29wZW5pZC1jb25maWd1cmF0aW9uL2p3a3MifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.pYGpFD-wOnuC-mIa-xsE9plhSaGGeRpNv2nJyoiTTlcTaSXCqOCdx361Xh98iGkqloqEbfyrldWGXG_xqCO6_z4Vnca1_bus8LSByWgI3KUOh_w7heu71SpqyLIO9GMl9hQ82Pkwf708Z1p1pDNdPE7Ca9VAB-V-_zAm6yxzG8BmqBt0KirsQq86WVJyyR6d-LJejmxyM2frQps7CrY0WIFaswKHMYvn-txYSSiN8tSs__oRZ67qywQlCsjTB4qqccCHAGaArIfsNjOHJXDqnA3GepEPG_N4tQtOYspGoL5_9GBfrCHodb9Ru0SfVFxjDms0jGU33lT7SWEcFe0-iIlrU1eUj9xvpDPEaz5X4jWRggz2B5-KgjJae7wQqyrqWO69einqadOTNRz9RTYlKcVraHJsiMUXme8KT9t5jIKWvDvPSxTLiMtHyha56pH2Y2pI_64eIGAvOG2azl8SiuOniX1v4nneS8o7hdPY_XAM2nnO_Xw2XT6B_65tDeRQg14C_OvUpya-F76zumo6ihqOSEYq2lYivkxM7F5FDnfaGkJhc2yh4s7OMEqLPEcfd-OZ1K4DmneZtmfwMb93sdGbJo6Gy0vqTHbDqe9kC9ymZr6Y7Yya06WUa0LOkK60oKIo6FmBIaeKofY3huXcO9cyDnNpKIZJnCrLYoYJEAE.eyJuYmYiOjE2ODgyNjU5ODksImV4cCI6MTY4ODM1MjM5MCwiaWF0IjoxNjg4MjY1OTkwLCJpc3MiOiJodHRwczovL3Nzby5hdXRoLndheWZhaXIuY29tLyIsImF1ZCI6WyJhdXRoX2lkZW50aXR5IiwiaHR0cHM6Ly9hcGkud2F5ZmFpci5jb20vIl0sImNsaWVudF9pZCI6IlExLV9LaElnNE5meUQ5RWdnNVRRSEEiLCJjbGllbnRfbHJwcyI6IjEwMDAiLCJjbGllbnRfbmFtZSI6IkFPRklTIEVSUCIsInN1YiI6IlExLV9LaElnNE5meUQ5RWdnNVRRSEFAY2xpZW50cyIsImp0aSI6IlhmZ0hLcmp4TzFmZTRfTG5oUkNKaFEiLCJzY29wZSI6ImF1dGgud2F5ZmFpci5jb206Y2xpZW50X3JlYWQgYXV0aC53YXlmYWlyLmNvbTpkZWZhdWx0X3Njb3BlIn0.l_Jgu6P1nC4WCQwHzo8Dai-FAfsgza-GW_p2QhY6cALPeQVzXAT_5XwusvByKGTnFDGCzk2m0VSFgI_8FWJCRtOEdyFU6wjmF4N3GjZUiyTOATm3u_hv49vIjo66FDejePWbWWkoxn5KPhV7euNh8jDLEfTTlSZmuZ24wKsZwtOXhprSzTmFjOq3XOjlXYiKKhbSNWyS-70cPjmlRe6wQ47lIvRvNatgy8TcX3d3GFUJcEXuvs25S8Fg10NzVeBV8as1V5gjm_lZIeF0_fobNJyPQ4gu5gxngbd1jtkSA7BIi6lUU_eobO2g_9rDdizes9xO0ntXoUbseBb92iK3caz9a2HvoMswhf7vQ8EtVbfTcqUrSweo0mBRE-1_FdDyAnxCjn6qxd0JYpJzFtcJZ-pSdaHa66LehrVWSnYp8MH5L5wjXIN3esO2RJKEgKI_GjZbneM-aSrIXIwpbBHK9VUoNrE11ClFJ3ePpLSQZgV6SI3ofddsjCCAlht-aR53HLMeyK-A3J3sriydK_1jsvXNB63p2tdN3_SukNjH9F-YzNu77yZROM3uAetLlm88Qq4hrp5vd313aDVUyfRbzS2Mry75Q2GV5YLwIpuFv2iZefc3tktYBSrBPNrl09gtH0-gFyZxLRth43q4TmlmTYOd8-2nitXgTADpn2W7Fm8");

        graphqlClient.setHttpHeaders(httpHeaders);
        GraphqlResponse response = null;
        try {
            response = graphqlClient.doMutation(graphqlMutation);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (response != null) {
            Map data = response.getData();
            JSONObject jsonObject = new JSONObject(data);
            if (jsonObject.containsKey("errors")) {
                log.error("wayfari调用Mutation失败!RequestName：{},shop{}，errorInfo:{}", graphqlMutation.getRequestName(), shopId, jsonObject.toJSONString());
                return null;
            }
            return jsonObject.toJSONString();
        }
        log.error("wayfair_Mutation_response:null");
        return null;
    }*/


    /**
     * @param
     * @description: wayfair 操作
     * @author: Moore
     * @date: 2024/6/19 18:24
     * @return: java.lang.String
     **/
    public String sendQuery(String requestUrl, Integer shopId, Object requestParam) {
        HttpRequest post = HttpUtil.createRequest(Method.POST, requestUrl);
        post.header("Accept", "application/json");
       post.header("authorization", accessTokenUtils.getToken(shopId));
        post.header("content-type", "application/json");
        post.setConnectionTimeout(8000); //延时毫秒
        String jsonBody = JSON.toJSONString(requestParam);
        log.info("wayfari接口请求参数,店铺ID：{} ,{}", shopId,jsonBody);
        HttpRequest body = post.body(jsonBody);
        try {
            HttpResponse httpResponse = body.execute();
            if (!httpResponse.isOk()) {
                TimeUnit.SECONDS.sleep(2);
                httpResponse = body.execute();
                if (!httpResponse.isOk()) {
                    log.error("wayfair接口请求重试失败：url:{},shop：{},reqParm:{}", requestUrl, shopId, jsonBody);
                    return null;
                }
            }
            return httpResponse.body();
        } catch (Exception e) {
            log.error("wayfair接口请求失败：url:{},shop：{},reqParm:{},errorMsg:{}", requestUrl, shopId, jsonBody,e);
            return null;
        }

    }



}
