package com.bizark.op.service.service.account;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.common.exception.CommonException;
import com.bizark.op.api.enm.TikTokApiEnums;
import com.bizark.op.api.enm.sale.SyncMappingEnum;
import com.bizark.op.api.enm.temu.TemuUrl;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.account.AccountApplication;
import com.bizark.op.api.entity.op.account.AccountCommonWarehouse;
import com.bizark.op.api.entity.op.account.AccountDTO;
import com.bizark.op.api.entity.op.temu.response.TemuWarehouseResponse;
import com.bizark.op.api.entity.op.tiktok.TiktokWarehouseResponse;
import com.bizark.op.api.entity.op.sale.request.BaseTemuRequest;
import com.bizark.op.api.entity.op.sale.response.TemuAccesstokenInfoResponse;
import com.bizark.op.api.parameter.util.TikTokUtil;
import com.bizark.op.api.request.AmzPromotionsShopQueryRequest;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.vo.sale.SelectorTreeNode;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.service.api.TemuApi;
import com.bizark.op.service.mapper.account.AccountMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AccountServiceImpl extends ServiceImpl<AccountMapper, Account> implements AccountService {

    public static final String SELLER_ID = "sellerId";
    @Autowired
    private AccountMapper accountMapper;


    @Autowired
    private TemuApi temuApi;

    @Autowired
    private TikTokUtil tikTokUtil;



    @Override
    public List<Account> selectAccountList(Account account) {
        return accountMapper.selectAccountList(account);
    }

    @Override
    public String getBusShopIdAccountById(Long id) {
        Account account = accountMapper.selectById(id);
        String connectStr = account.getConnectStr();
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (jsonObject.containsKey("shopId")) {
            return (String)jsonObject.get("shopId");
        }
        return null;
    }


    /**
     * @description:获取店铺列表
     * @author: Moore
     * @date: 2023/10/16 16:02
     * @param
     * @param account
     * @return: java.util.List<com.bizark.op.api.entity.op.account.Account>
    **/
    @Override
    public List<Account> selectAccountListRefactor(Account account) {
        return accountMapper.selectAccountListRefactor(account);
    }


    @Override
    public void saveOrUpdate(AccountDTO account) {
        if (CollectionUtil.isNotEmpty(account.getAreaCountryRequest())) {
            throw new CommonException("请补充接口信息！");
        }

        List<Account> accounts = account.buildAccount();
        // 判断当前账号是否为 VC 账号，生成店铺名称和店铺标志
        String saleChannel = account.getSaleChannel();
        if (saleChannel == null) {
            throw new CommonException("销售渠道不能为空！");
        }
        String storeName = saleChannel.toLowerCase().startsWith("vc") ?
                account.getTitle() + "-" + account.getSaleChannel() + "-" + account.getCountryCode() :
                account.getTitle() + "-" + account.getCountryCode();
        account.setFlag(storeName);
        account.setStoreName(storeName);

        // TODO 店铺标题 是否新增金蝶编码字段


    }


    public List<Account> selectAccountListByChannelOrId(AmzPromotionsShopQueryRequest account) {

        return accountMapper.selectAccountListByChannelOrId(account);
    }



   @Override
    public List<String> checkAccountAutoPush(List<Account> accounts) {
        List<String> errorAccountsFlag = new ArrayList<>();
        for (Account account : accounts) {
            String connectStr = account.getConnectStr();
            if (StrUtil.isBlank(connectStr)) {
                errorAccountsFlag.add(account.getFlag());
                continue;
            }
            JSONObject jsonObject = JSON.parseObject(connectStr);
            if (!jsonObject.containsKey("new_flag")) {
                errorAccountsFlag.add(account.getFlag());
                continue;
            }
            JSONObject object = jsonObject.getObject("new_flag", JSONObject.class);
            if (!object.containsKey("stock")) {
                errorAccountsFlag.add(account.getFlag());
                continue;
            }
            if (!Objects.equals(object.getString("stock"), "Y")) {
                errorAccountsFlag.add(account.getFlag());
            }

        }
        return errorAccountsFlag;
    }



    /**
     * @description: 根据店铺ID获取店铺信息
     * @author: Moore
     * @date: 2024/3/8 13:40
     * @param
     * @param shopId
     * @return: com.bizark.op.api.entity.op.account.Account
     **/
    @Override
    public Account selectByAccountId(Integer shopId) {
        Account account = new Account();
        account.setId(shopId);
        List<Account> accounts = accountMapper.selectAccountListRefactor(account);
        if (CollectionUtils.isEmpty(accounts)){
            return null;
        }
        return accounts.get(0);
    }




    @Override
    public Account getAccountByShopId(String shopId) {
        if (StringUtil.isEmpty(shopId)) {
            return null;
        }
        Account account = new Account();
        account.setType("tiktok");
        List<Account> accounts = selectAccountListRefactor(account);
        for (Account accountItem : accounts) {
            JSONObject accountItemJson = JSONObject.parseObject(accountItem.getConnectStr());
            if (null != accountItemJson) {
                if (shopId.equals(accountItemJson.getString("shopId"))) {
                    return accountItem;}
            }
        }
        return null;
    }

    /**
     * @description: 获取退货仓库CODE
     * @author: Moore
     * @date: 2023/12/1 10:40
     * @param
     * @param shopId
     * @return: java.util.List<java.lang.String>
     **/
    @Override
    public List<String> selectAccountRefundInfo(Long shopId) {
        return accountMapper.selectAccountRefundInfo(shopId);
    }

    /**
     * 根据 店铺id 查询同一个店铺账号下的所有店铺id
     *
     * @param shopId
     * @param contextId
     * @return
     */
    @Override
    public List<Long> selectAccountList(Long shopId, Long contextId) {
        return this.baseMapper.selectAccountIdListById(shopId, contextId);
    }


    @Override
    public Account selectByOrgIdAndFlag(Integer orgId, String accountId) {
        return accountMapper.selectByOrgIdAndFlag(orgId, accountId);
    }

    @Override
    public List<String> investmentSelector(Integer contextId) {
        List<Account> accounts = this.lambdaQuery()
                .eq(Account::getOrgId, contextId)
                .isNotNull(Account::getInvestment)
                .ne(Account::getInvestment, "")
                .list();

        return CollectionUtil.isEmpty(accounts) ? new ArrayList<>()
                : accounts.stream().map(Account::getInvestment).distinct().collect(Collectors.toList());
    }

    @Override
    public List<Account> selectByAccountsCountry(Integer contextId, String accountFlag, List<String> types) {
        return accountMapper.selectByAccountsCountry(contextId, accountFlag,types);
    }

    @Override
    public void syncTemuAccessInfo(Integer orgId, Integer id) {
        List<Account> accounts = this.lambdaQuery()
                .eq(Account::getType, SyncMappingEnum.SkuGoodsMapSaleChannelEnum.TEMU.getEhengjian())
                .eq(Objects.nonNull(id), Account::getId, id)
                .eq(Account::getOrgId, orgId)
                .list();
        log.info("TemuMailSyncJob - 店铺条目数:{}", accounts.size());

        for (Account account : accounts) {
            try {
                BaseTemuRequest request = new BaseTemuRequest() {
                    @Override
                    public TemuUrl type() {
                        return TemuUrl.ACCESS_INFO;
                    }
                };
                TemuAccesstokenInfoResponse response = temuApi.requestUsTemu(account, request, TemuAccesstokenInfoResponse.class);

                if (response == null || !response.getSuccess() || Objects.isNull(response.getResult())) {
                    log.info("Temu同步权限信息失败 - {} - {}", account.getFlag(), JSON.toJSONString(response));
                    continue;
                }

                TemuAccesstokenInfoResponse.Result result = response.getResult();
                Long mallId = result.getMallId();
                if (Objects.nonNull(mallId)) {
                    //
                    String connectStr = account.getConnectStr();
                    if (StrUtil.isBlank(connectStr)) {
                        connectStr = "{}";
                    }

                    JSONObject jsonObject = JSON.parseObject(connectStr);
                    // 直接覆盖SellerId数据
                    jsonObject.put(SELLER_ID, mallId);


                    String newConnecStr = jsonObject.toJSONString();

                    log.info("TemuMailSyncJob - 更新店铺数据:{} - {} - {}", account.getFlag(), mallId, newConnecStr);

                    this.lambdaUpdate()
                            .eq(Account::getId, account.getId())
                            .set(Account::getSellerId, String.valueOf(mallId))
                            .set(Account::getConnectStr, newConnecStr)
                            .update();

                }
            } catch (Exception e) {
                log.info("Temu同步权限信息失败 - {} - {}", account.getFlag(), e.getMessage());
            }

        }
    }

    @Override
    public List<Account> selectByOrgIdAndFlags(Integer orgId, List<String> flags) {
        return accountMapper.selectByOrgIdAndFlags(orgId, flags);
    }

    @Override
    public List<AccountCommonWarehouse> getStoreWarehouse(Integer contextId, String accountFlag) {

        List<AccountCommonWarehouse> commonWarehouses = new ArrayList<>();

        Account account = this.lambdaQuery()
                .eq(Account::getOrgId, contextId)
                .eq(Account::getFlag, accountFlag)
                .one();
        if (Objects.isNull(account)) {
            throw new CommonException("未获取到店铺信息");
        }

        SyncMappingEnum.SkuGoodsMapSaleChannelEnum channelType = SyncMappingEnum.SkuGoodsMapSaleChannelEnum.TYPE_MAP.get(account.getType().toLowerCase());
        if (Objects.isNull(channelType)) {
            throw new CommonException("当前渠道不存在:" + account.getFlag());
        }

        switch (channelType) {
            case TEMU:
                TemuWarehouseResponse temuWarehouse = temuApi.getTemuWarehouse(account);
                if (Objects.isNull(temuWarehouse)) {
                    throw new CommonException("店铺仓库查询失败");
                }
                List<AccountCommonWarehouse> warehouses = temuWarehouse.convert();
                if (Objects.isNull(warehouses)) {
                    throw new CommonException("未获取到店铺仓库");
                }
                commonWarehouses = warehouses;
                break;
            case TIKTOK:
                commonWarehouses =  getTiktokWarehouse(account);
                break;
            default:
                throw new CommonException("当前渠道不支持获取仓库:" + channelType.getEhengjian());
        }
        return commonWarehouses;
    }

    @Override
    public AccountApplication selectApplication(Long shopId) {
        Account account = this.getById(shopId);
        if (Objects.isNull(account)) {
            return null;
        }
        if (!Objects.equals(SyncMappingEnum.SkuGoodsMapSaleChannelEnum.TIKTOK.getEhengjian(), account.getType())) {
            throw new CommonException("当前只支持Tiktok渠道查询");
        }
        // 目前只写tk的
        if (StrUtil.isNotBlank(account.getAmazonApplicationId())) {
            throw new CommonException("当前店铺ApplicationId不存在");
        }
        return selectApplication(account.getAmazonApplicationId());
    }

    @Override
    public AccountApplication selectApplication(String applicationId) {
        if (StrUtil.isBlank(applicationId)) {
            return null;
        }
        return accountMapper.selectApplication(applicationId);
    }

    @Override
    public List<SelectorTreeNode<String>> companySelector(Integer contextId) {
        List<String> company = accountMapper.selectAccountCompany(contextId);
        if (CollectionUtil.isEmpty(company)) {
            return new ArrayList<>();
        }
        return company.stream().map(c -> {
            SelectorTreeNode<String> node = new SelectorTreeNode<>();
            node.setName(c);
            node.setValue(c);
            return node;
        }).collect(Collectors.toList());
    }

    @Override
    public List<Account> selectAccountCompanyByFlag(Integer contextId, List<String> flags) {
        return accountMapper.selectAccountCompanyByFlag(contextId, flags);
    }

    @Override
    public Map<String, String> companyMapping(Integer contextId, List<String> accountIds) {
        List<Account> accounts = selectAccountCompanyByFlag(contextId, accountIds);
        if (CollectionUtil.isEmpty(accounts)) {
            return new HashMap<>();
        }
        return accounts.stream().collect(Collectors.toMap(Account::getFlag, Account::getRegisterCompanyName, (v1, v2) -> v1));
    }

    private List<AccountCommonWarehouse> getTiktokWarehouse(Account account) {

        TiktokWarehouseResponse warehouseResponse = tikTokUtil.getTikTokShopReturnV2(
                new HashMap<>(),
                TikTokApiEnums.GET_WAREHOUSE_LIST.getUrl(),
                TikTokApiEnums.GET_WAREHOUSE_LIST.getPath(),
                TiktokWarehouseResponse.class, String.valueOf(account.getId())
        );
        if (Objects.isNull(warehouseResponse) || Objects.isNull(warehouseResponse.getData())) {
            return new ArrayList<>();
        }

        return warehouseResponse.getData().getWarehouses()
                .stream()
                .map(c -> AccountCommonWarehouse.builder()
                        .warehouseId(c.getId()).warehouseName(c.getName()).effectStatus(c.getEffectStatus())
                        .type(c.getType()).build()
                )
                .collect(Collectors.toList());

    }
}
