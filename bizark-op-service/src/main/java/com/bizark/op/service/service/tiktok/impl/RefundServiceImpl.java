package com.bizark.op.service.service.tiktok.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bizark.common.exception.CommonException;
import com.bizark.op.api.cons.ChannelTagEnum;
import com.bizark.op.api.cons.ScTicketConstant;
import com.bizark.op.api.enm.TikTokApiEnums;
import com.bizark.op.api.enm.refund.*;
import com.bizark.op.api.enm.sale.AccountSaleChannelEnum;
import com.bizark.op.api.enm.sale.walmart.WalmartUrlEnum;
import com.bizark.op.api.enm.ticket.TkStationLetterStationRefundReasonEnum;
import com.bizark.op.api.enm.ticket.WalmartStationLetterStationRefundReasonEnum;
import com.bizark.op.api.enm.tiktok.RefundOrderStatusEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.order.SaleOrderItemVO;
import com.bizark.op.api.entity.op.order.SaleOrderItems;
import com.bizark.op.api.entity.op.order.SaleOrders;
import com.bizark.op.api.entity.op.refund.*;
import com.bizark.op.api.entity.op.returns.entity.ReturnInfoEntity;
import com.bizark.op.api.entity.op.returns.entity.ScTicketRefundInfo;
import com.bizark.op.api.entity.op.ticket.ConfTicketAssign;
import com.bizark.op.api.entity.op.ticket.ScTicket;
import com.bizark.op.api.entity.op.ticket.ScTicketLog;
import com.bizark.op.api.entity.op.ticket.VO.SaleOrderVoTicket;
import com.bizark.op.api.entity.op.titok.response.ReverseRecord;
import com.bizark.op.api.entity.op.titok.response.TikTokResponseBaseEntity;
import com.bizark.op.api.exception.ErpCommonException;
import com.bizark.op.api.parameter.util.TikTokUtil;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.amazon.IAmzFeedService;
import com.bizark.op.api.service.order.SaleOrderItemsService;
import com.bizark.op.api.service.refund.*;
import com.bizark.op.api.service.sale.ProductsService;
import com.bizark.op.api.service.ticket.IConfTicketAssignService;
import com.bizark.op.api.service.ticket.IScTicketLogService;
import com.bizark.op.api.service.ticket.IScTicketService;
import com.bizark.op.api.service.tiktok.ITikTokShopApiService;
import com.bizark.op.api.vo.refund.*;
import com.bizark.op.common.factory.RefundAndReturnFactory;
import com.bizark.op.common.handler.handler.platform.AbstractRefundHandler;
import com.bizark.op.common.util.*;
import com.bizark.op.service.annotation.*;
import com.bizark.op.service.api.WalmartApi;
import com.bizark.op.service.manager.refund.RefundManger;
import com.bizark.op.service.mapper.order.SaleOrderItemsMapper;
import com.bizark.op.service.mapper.refund.*;
import com.bizark.op.service.mapper.returns.ReturnInfoMapper;
import com.bizark.op.service.mapper.returns.ScTicketRefundInfoMapper;
import com.bizark.op.service.mapper.ticket.SaleOrdersMapper;
import com.bizark.op.service.mapper.ticket.ScTicketMapper;
import com.bizark.framework.security.AuthContextHolder;
import com.xxl.conf.core.annotation.XxlConf;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.alibaba.fastjson.JSON.toJSONString;

/**
 * lty notes
 * manager层
 *
 * <AUTHOR> Theo
 * @create 2023/10/17 18:13
 */
@Slf4j
@Service
public class RefundServiceImpl implements IRefundService {

//    @Resource
//    private IScTicketProService ticketProService;

    @XxlConf(value = "bizark-erp.tiktok.switch",defaultValue = "false")
    public static Boolean TIKTOK_SWITCH;

    @Resource
    private SaleOrderItemsService saleOrderItemsService;

    @Resource
    private RefundManger refundManger;

    @Resource
    private IScTicketService scTicketService;

    @Resource
    private SaleOrderItemsService itemsService;

    @Resource
    private IReverseRecordService reverseRecordService;

    @Resource
    private IOrderRefundRequestService orderRefundRequestService;

    @Resource
    private AccountService accountService;

    @Autowired
    private IConfTicketAssignService confTicketAssignService;

    @Resource
    private ScTicketMapper scTicketMapper;

    @Resource
    private RefundAndReturnFactory factory;

    @Resource
    private IOrderRefundRequestService requestService;

    @Resource
    private IOrderRefundService orderRefundService;

    @Resource
    private SaleOrdersMapper saleOrdersMapper;

    @Resource
    private OrderRefundRequestMapper orderRefundRequestMapper;

    @Autowired
    private IScTicketLogService scTicketLogService;

    @Resource
    private ITikTokShopApiService tikTokShopApiService;

    @Resource
    private OrderRefundMapper orderRefundMapper;

    @Resource
    private IReverseRecordDetailsService reverseRecordDetailsService;

    @Autowired
    private OrderRefundRequestInfoMapper orderRefundRequestInfoMapper;

    @Autowired
    private OrderRefundRequestInfoItemMapper orderRefundRequestInfoItemMapper;

    @Autowired
    private OrderRefundItemMapper orderRefundItemMapper;

    @Autowired
    private SaleOrderItemsMapper saleOrderItemsMapper;

    @Autowired
    private ReturnInfoMapper returnInfoMapper;

    @Autowired
    private ScTicketRefundInfoMapper scTicketRefundInfoMapper;

    @Autowired
    private IAmzFeedService amzFeedService;

    @Autowired
    private TikTokUtil tikTokUtil;

    @Autowired
    private ProductsService productsService;

    @Autowired
    private WalmartApi walmartApi;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private IOrderRefundRequestInfoService orderRefundRequestInfoService;


    @IsTemporary
    @Override
    public OrderRefundVO getRefundReqBaseMsg(Long sourceId, String orderId, String refundType, Long ticketId) {
        OrderRefund orderRefund = new OrderRefund();
        List<ReverseRecordDetails> details;
        List<ReverseRecord> records;
        OrderRefundVO refundVO = new OrderRefundVO();
        ScTicket scTicket = scTicketService.selectScTicketById(ticketId);
        if (ScTicketConstant.TICKET_TYPE_REFUND_REQUEST.equals(scTicket.getTicketType()) && ScTicketConstant.TICKET_SOURCE_REFUND_REQUEST_INFO.equals(scTicket.getSourceDocument())) {
            return getRefundRefundVo(sourceId,refundVO,scTicket);
        }
//        orderId = String.valueOf(scTicket.getOrderId());
//
//        if (ObjectUtil.isNotNull(scTicket) && ObjectUtil.isNotNull(scTicket.getParentTicketId())) {
//            ScTicket parentTicket = scTicketService.selectScTicketById(scTicket.getParentTicketId());
//            if (ObjectUtil.isNotNull(parentTicket) && ScTicketConstant.TICKET_TYPE_RETURN.equals(parentTicket.getTicketType())) {
//                refundType = OrderRefundEnum.SALES_RETURN_TO_INTERNAL.getCode();
//            }
//        }
//
//        if (OrderRefundEnum.INTERNAL.getCode().equals(refundType)) {
//            // 站内不变,站外明细查询方式不同
//            OrderRefund refund = orderRefundService.getById(sourceId);
//            OrderRefundRequest reverseOrderRequest = new OrderRefundRequest();
//            // 第一次提交 orderRefund不做同步
//            if (ObjectUtil.isNull(refund)) {
//                reverseOrderRequest = orderRefundRequestMapper.selectById(sourceId);
//            } else {
//                reverseOrderRequest = orderRefundRequestMapper.selectById(refund.getOrderRequestId());
//            }
//
//            details = reverseRecordDetailsService.list(new LambdaQueryWrapper<ReverseRecordDetails>().eq(ReverseRecordDetails::getReverseOrderId, reverseOrderRequest.getReverseOrderId()));
//            records = reverseRecordService.list(new LambdaQueryWrapper<ReverseRecord>().eq(ReverseRecord::getOrderRequestId, reverseOrderRequest.getId()));
//            if (ObjectUtil.isNotNull(refund)) {
//                BeanUtil.copyProperties(refund, refundVO);
//                refundVO.setReasonText(reverseOrderRequest.getReturnText());
//            } else {
//                BeanUtil.copyProperties(reverseOrderRequest, refundVO);
//
//                refundVO.setReasonText(reverseOrderRequest.getReturnText());
//            }
//        } else {
//            orderRefund = orderRefundService.getById(sourceId);
//            details = reverseRecordDetailsService.list(new LambdaQueryWrapper<ReverseRecordDetails>().eq(ReverseRecordDetails::getOrderRefundId, orderRefund.getId()));
//            records = reverseRecordService.list(new LambdaQueryWrapper<ReverseRecord>().eq(ReverseRecord::getOrderRefundId, orderRefund.getId()));
//            BeanUtil.copyProperties(orderRefund, refundVO);
//            if (OrderRefundEnum.SALES_RETURN_TO_INTERNAL.getCode().equals(refundType)) {
//                refundVO.setReasonText(orderRefund.getReturnText());
//            }
//        }
//
//
//        // 退款明细
//        details.forEach(i -> {
//            ReverseRecordDetailsVO recordDetailsVO = new ReverseRecordDetailsVO();
////            ProductChannels productChannels = saleOrdersMapper.getProductChannelBySkuId(i.getSellerSku(), i.getAccountFlag());
//            String erpSku = saleOrdersMapper.getErpSkuChannelBySkuId(i.getSellerSku(), i.getAccountFlag());
//            BeanUtil.copyProperties(i, recordDetailsVO);
//            recordDetailsVO.setSkuId(erpSku);
//
//            refundVO.add(recordDetailsVO);
//        });
//        refundVO.setCustomerName(saleOrdersMapper.getCustomerNameByOrderId(orderId));
//        refundVO.addReverseRecord(records);
//        refundVO.setOrderStatus(orderRefund.getRefundOrderStatus());
//        refundVO.setOperatorRemark(orderRefund.getOperatorRemark());
//        if (ObjectUtil.isNotEmpty(orderRefund.getTreatmentState())) {
//            if (OrderRefundEnum.FINISHED_TICKET_ORDER.getCode().equals(orderRefund.getTreatmentState())) {
//                refundVO.setOrderRefundStatus("已退款");
//            } else if (OrderRefundEnum.TO_BE_REFUNDED.getCode().equals(orderRefund.getTreatmentState()) ||
//                    OrderRefundEnum.SUBMIT.getCode().equals(orderRefund.getTreatmentState())) {
//                refundVO.setOrderRefundStatus("待退款");
//            }
//        }
//        refundVO.setRefundType(Integer.valueOf(refundType));
//        // 拿orderRefundId
//        refundVO.setMeta(refundManger.getAttachment(sourceId, ScTicketConstant.TICKET_SOURCE_REFUND));

        return refundVO;

    }

    @Override
    public void isRefunded(Long ticketId) {
        ScTicket scTicket = scTicketService.selectScTicketById(ticketId);
        if (ObjectUtil.isNotNull(scTicket) && ScTicketConstant.TICKET_STATUS_FINISH.equals(scTicket.getTicketStatus())) {
            throw new CommonException("该工单已经退款,请勿重复操作");
        }
    }

    private static void isTimeList(OrderRefund orderRefund, OrderRefundVO refundVO, LocalDateTime reqTime) {
        if (OrderRefundEnum.FINISHED.getCode().equals(orderRefund.getTreatmentState())) {
            refundVO.setTimeLimit(OrderRefundEnum.FINISHED.getMsg());
        } else {
            refundVO.setTimeLimit(MathTimeUtils.computeReceiptTimeLimit(reqTime));
        }
    }

    /**
     * 0 退款请求申请站内退款 1 站内信或其他站外信申请站外退款  4 站内信或其他站外信申请站内退款  判断
     *
     * @param channel    渠道
     * @param orderId    订单编号
     * @param refundType
     * @param ticketId
     * @return {@link Boolean}
     */
    @Override
    @IsTemporary
//    @IsRefusal
    @ReturnIsRefusal
//    @IsEnoughRefund
    public Boolean isApply(String channel, String orderId, String refundType, Long ticketId) {
        if (StringUtils.isEmpty(orderId)) {
            throw new ErpCommonException("无订单无法申请退款");
        }
//        if (!verifyIsSubmitted(ticketId)) {
//            throw new ErpCommonException("退款工单已提交或已退回请进入退款工单操作");
//        }
        ScTicket ticketByTicketId = scTicketMapper.getTicketByTicketId(ticketId);
        if (ScTicketConstant.MATCH_TYPE_MANUAL.equals(ticketByTicketId.getMatchOrderType())) {
            throw new ErpCommonException("手工订单不支持退款");
        }
        //退款请求申请站内退款仅支持tiktok和sc渠道
        if (OrderRefundEnum.INTERNAL.getCode().equals(refundType)) {
//            if (!AccountSaleChannelEnum.TIKTOK.getValue().equals(channel) && !AccountSaleChannelEnum.SC.getValue().equals(channel)) {
//                throw new ErpCommonException("退款请求站内退款仅支持tiktok渠道和sc渠道");
//            }
            //tiktok渠道可以有多个已关闭的站内退款子工单，只能有一个已完成状态的站内退款子工单或者一个进行中的站内退款子工单。
            // 不允许有多个已完成，不允许多个进行中，一个已完成和一个进行中不能同时存在
            //点击站内退款时，查询是否有已经完成的站内退款子工单或者 进行中的（即状态不等于已完成和已关闭）站内退款子工单，有就不允许点开站内退款，只能去站内退款子工单操作
            if (AccountSaleChannelEnum.TIKTOK.getValue().equals(channel)) {
                List<ScTicket> finishTicket = scTicketMapper.selectScTicketFinishStateAByParentTicketIdAndTicketType(ticketId, new String[]{ScTicketConstant.TICKET_TYPE_INSIDE_REFUND}, new String[]{ScTicketConstant.TICKET_STATUS_FINISH});
                if (CollectionUtil.isNotEmpty(finishTicket)) {
                    throw new ErpCommonException("工单编号:" + finishTicket.get(0).getTicketNumber() + "的站内退款工单已经完成!");
                }
//                List<ScTicket> processTicket = scTicketMapper.selectScTicketProcessStateAByParentTicketIdAndTicketType(ticketId, new String[]{ScTicketConstant.TICKET_TYPE_INSIDE_REFUND});
//                if (CollectionUtil.isNotEmpty(processTicket)) {
//                    throw new ErpCommonException("尚有未结束的站内退款工单:" + processTicket.get(0).getTicketNumber() + "请先处理!");
//                }
            } else {
                //sc渠道可以有多个已关闭的站内退款子工单，可以有多个已完成以及一个进行中，不允许多个进行中，一个已完成和一个进行中可以同时存在
                //点击站内退款时，查询是否有进行中的（即状态不等于已完成和已关闭）站内退款子工单，有就不允许点开站内退款，只能去站内退款子工单操作
//                List<ScTicket> processTicket = scTicketMapper.selectScTicketProcessStateAByParentTicketIdAndTicketType(ticketId, new String[]{ScTicketConstant.TICKET_TYPE_INSIDE_REFUND});
//                if (CollectionUtil.isNotEmpty(processTicket)) {
//                    throw new ErpCommonException("尚有未结束的站内退款工单:" + processTicket.get(0).getTicketNumber() + "请先处理!");
//                }
            }
            //站内信或其他站外信 站外退款
        } else if (OrderRefundEnum.EXTERNAL.getCode().equals(refundType)) {
            /*if (!AccountSaleChannelEnum.TIKTOK.getValue().equals(channel) && !AccountSaleChannelEnum.SC.getValue().equals(channel)) {
                throw new ErpCommonException("站外退款仅支持tiktok渠道和sc渠道");
            }*/
//            List<ScTicket> processTicket = scTicketMapper.selectScTicketProcessStateAByParentTicketIdAndTicketType(ticketId, new String[]{ScTicketConstant.TICKET_TYPE_OUTSIDE_REFUND});
//            if (CollectionUtil.isNotEmpty(processTicket)) {
//                throw new ErpCommonException("尚有未结束的站外退款工单:" + processTicket.get(0).getTicketNumber() + "请先处理!");
//            }
            //站内信或其他站外信 站内退款
        } else if (OrderRefundEnum.AMZ_SITEMSG_INSIDE_REFUND.getCode().equals(refundType)) {
//            if (!AccountSaleChannelEnum.SC.getValue().equals(channel)) {
//                if(!((AccountSaleChannelEnum.TIKTOK.getValue().equals(channel) ||AccountSaleChannelEnum.WALMART.getValue().equals(channel))
//                        && ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG.equals(ticketByTicketId.getTicketType()))) { //排除掉tk渠道并且是站内信
//                    throw new ErpCommonException("该工单申请站内退款仅支持sc渠道,tk或者walmart渠道的站内信站内退款或者站外信只支持sc渠道!");
//                }
//            }
//            List<ScTicket> processTicket = scTicketMapper.selectScTicketProcessStateAByParentTicketIdAndTicketType(ticketId, new String[]{ScTicketConstant.TICKET_TYPE_INSIDE_REFUND});
//            if (CollectionUtil.isNotEmpty(processTicket)) {
//                throw new ErpCommonException("尚有未结束的站内退款工单:" + processTicket.get(0).getTicketNumber() + "请先处理!");
//            }
        }
        return Boolean.TRUE;
    }

    private OrderRefundVO isSubmit(String orderId, OrderRefundVO refundVO, String channel) {
        Integer countList = scTicketService.countList(orderId, ScTicketConstant.TICKET_STATUS_FINISH);
        // 1.点击保存可以保存,保存实质上是修改操作,但是第一次提交以后,工单尚未结束的时候,后续又有人进来,进行保存和提交就是不允许的,应该在发起站内申请的时候,直接告诉他,此工单的子工单并未结束
        // 2.退款信息的保存这里,需要进行校验,只有进行保存操作后才能进行提交. tiktok不支持二次修改,金额只有全退。
        // 3.渠道类型是tiktok,并且已经有工单了,就不允许再点击申请站内退款了
        // 4.点击保存以后才能进行 提交操作,

        return refundVO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateOrderRefundApply(String channel, Long orderId, String reverseOrderId, Long sourceId, Long ticketId, String businessStatus) {
        ScTicket scTicket = scTicketService.selectScTicketById(ticketId);
        scTicket.setBusinessTicketStatus(businessStatus);
        scTicket.setTicketStatus(ScTicketConstant.TICKET_STATUS_FINISH);
        scTicket.setOperateStatus(ScTicketConstant.TICKET_OPERATE_VIEW);

        return scTicketService.updateScTicketStatus(scTicket) == 1 ? Boolean.TRUE : Boolean.FALSE;
    }

    /**
     * 1. 站外退款一开始没创建子工单,所以这里会生成一条当前ticketId所关联的orderRefund
     * 2. 但是在退回的动作的时候,传入的ticketId是子工单的;并且,这个id并没有同步到OrderRefund处,所以用这个id找orderRefund是找不到数据的
     * 3. 所以需要,站外退款节点(确保父订单就是站内信)+当前工单没有关联的orderRefund + 来确保流程的唯一性,其实只要scTicketId保存后会返回id就行
     * 4. orderRefund的工单id 存的是子工单的id
     */
    @Override
    @IsRefusal
    @IsEnoughRefund
    public OrderRefundReqVO getInstationRefundMsg(String orderId, Long reverseOrderId, Integer refundType, Long ticketId, Long shopId, String sourceId) {
        ScTicket scTicket = scTicketService.selectScTicketById(ticketId);
        orderId = String.valueOf(scTicket.getOrderId());
        if (OrderRefundEnum.INTERNAL.getCodeType().equals(refundType)) {
            return getOrderRefundReqVO(reverseOrderId, orderId);
        } else if (OrderRefundEnum.EXTERNAL.getCodeType().equals(refundType) ||
                OrderRefundEnum.SALES_RETURN_TO_INTERNAL.getCodeType().equals(refundType)) {
            // 1. 父工单回显页面 2. 子工单页面具体的信息回显
            OrderRefund one = orderRefundService.getOne(new LambdaQueryWrapper<OrderRefund>()
                    .eq(OrderRefund::getTicketId, ticketId)
                    .ne(OrderRefund::getTreatmentState, OrderRefundEnum.FINISHED.getCode()));
            Boolean isHaveSon = refundManger.isHaveSon(ticketId);
            Boolean haveNotRefund = refundManger.isHaveNotRefund(ticketId);
            if (ObjectUtil.isNotNull(one) && !isHaveSon) {
                return getOrderRefundReqVO(one.getTicketId(), one.getId(), refundType);
            } else if (isHaveSon && ObjectUtil.isNull(one)) {
                return refundManger.saveAndReturnForRepeatedly(orderId, refundType, ticketId, shopId);
            } else if (haveNotRefund) {
                // 1.多次退回 -a:
                return getOrderRefundReqVO(one.getTicketId(), one.getId(), refundType);
            } else {
                return refundManger.saveAndReturnOrderRefundReqVO(orderId, refundType, ticketId, shopId, reverseOrderId);
            }
        }
        return null;
    }

    @Override
    public <T> T getInstationRefundMsg(T t) {
        if (t instanceof OrderRefundReqVO) {
            return getOrderRefundReqVO((OrderRefundReqVO) t);
        }
        if (t instanceof OrderRefundVO) {
            return getOrderRefundVO((OrderRefundVO) t);
        }
        return null;
    }


    @NotNull
    private <T> T getOrderRefundVO(OrderRefundVO t) {
        OrderRefundVO vo = t;
        List<ReverseRecordDetailsVO> refundDetails = vo.getRefundDetails();
        OrderRefundReqVO orderRefundReqVOs = saleOrdersMapper.getBaseMsgByOrderId(t.getOrderId());

        List<ReverseDetailsReqVO> details = orderRefundReqVOs.getReverseDetailsReqVO();
        StringBuilder orderProductNumbers = new StringBuilder();
        //站外才有,站内没有
        if (OrderRefundEnum.EXTERNAL.getCodeType().equals(vo.getRefundType())) {
            ConcurrentHashMap<String, Map<Integer, BigDecimal>> totalRefunded = (ConcurrentHashMap<String, Map<Integer, BigDecimal>>) reverseRecordDetailsService.getTotalRefunded(t.getParentTicketId());
            ConcurrentHashMap<Integer, BigDecimal> refunded = new ConcurrentHashMap<>();
            ConcurrentHashMap<Integer, BigDecimal> taxRefunded = new ConcurrentHashMap<>();
            Iterator<Map.Entry<Integer, BigDecimal>> refundedIterator = null;

            Iterator<Map.Entry<Integer, BigDecimal>> taxRefundedIterator = null;

            if (CollUtil.isNotEmpty(totalRefunded)) {
                refunded = (ConcurrentHashMap<Integer, BigDecimal>) totalRefunded.get(OrderRefundFieldEnums.ITEM_PRICE_ADJ_REFUNDED.getField());
                refundedIterator = refunded.entrySet().iterator();
                taxRefunded = (ConcurrentHashMap<Integer, BigDecimal>) totalRefunded.get(OrderRefundFieldEnums.ITEM_TAX_ADJ_REFUNDED.getField());
                taxRefundedIterator = taxRefunded.entrySet().iterator();
            }


            int size = 1;
            for (ReverseRecordDetailsVO i : refundDetails) {
                details.forEach(item -> {
                    if (item.getItemsId().equals(i.getItemsId())) {
                        i.setUnitPrice(Optional.ofNullable(item.getUnitPrice()).orElse(BigDecimal.ZERO));
                        i.setItemTaxAdj(Optional.ofNullable(item.getItemTaxAdj()).orElse(BigDecimal.ZERO));
                        i.setReturnOffer(Optional.ofNullable(item.getReturnOffer()).orElse(BigDecimal.ZERO));
                        i.setRestockingFees(Optional.ofNullable(item.getRestockingFees()).orElse(BigDecimal.ZERO));
                    }
                });
                if (size == refundDetails.size()) {
                    orderProductNumbers.append(i.getSellerSku());
                } else {
                    orderProductNumbers.append(i.getSellerSku()).append(",");
                }
                List<DetailsVO> list = new ArrayList<>();
                DetailsVO commodity = new DetailsVO();
                commodity.setTitle("商品");

                commodity.setAmount(i.getReturnTotalAmount());
                commodity.setAmountRefunded(i.getItemPriceAdjRefunded());
                commodity.setItemsId(i.getItemsId());
                commodity.setReverseDetailsId(i.getId());
                commodity.setCanRefundAmount(BigDecimal.ZERO);
                if (ObjectUtil.isNotNull(refundedIterator)) {
                    while (refundedIterator.hasNext()) {
                        Map.Entry<Integer, BigDecimal> next = refundedIterator.next();
                        if (next.getKey().equals(i.getItemsId())) {
                            commodity.setHistoryAmountRefunded(next.getValue());
                            refunded.remove(next.getKey());
                            break;
                        }
                    }
                } else {
                    commodity.setHistoryAmountRefunded(BigDecimal.ZERO);
                }


                DetailsVO commodityTax = new DetailsVO();
                commodityTax.setTitle("商品税");
                // 商品税
                commodityTax.setAmount(i.getItemTaxAdj());
                commodityTax.setAmountRefunded(i.getItemTaxAdjRefunded());
                commodityTax.setItemsId(i.getItemsId());
                commodityTax.setReverseDetailsId(i.getId());
                commodityTax.setCanRefundAmount(BigDecimal.ZERO);
                if (ObjectUtil.isNotNull(taxRefundedIterator)) {
                    while (taxRefundedIterator.hasNext()) {
                        Map.Entry<Integer, BigDecimal> next = taxRefundedIterator.next();

                        if (ObjectUtil.isNotNull(next) && next.getKey().equals(i.getItemsId())) {
                            commodityTax.setHistoryAmountRefunded(next.getValue());
                            taxRefunded.remove(next.getKey());
                            break;
                        }
                    }
                } else {
                    commodityTax.setHistoryAmountRefunded(BigDecimal.ZERO);
                }


                DetailsVO returnOffer = new DetailsVO();
                returnOffer.setTitle("退货优惠");
                returnOffer.setAmount(i.getReturnOffer());
                returnOffer.setItemsId(i.getItemsId());
                returnOffer.setAmountRefunded(i.getReturnOfferRefunded());
                returnOffer.setReverseDetailsId(i.getId());
                returnOffer.setCanRefundAmount(BigDecimal.ZERO);
//            returnOffer.setHistoryAmountRefunded();


                DetailsVO restockingFees = new DetailsVO();
                restockingFees.setTitle("重新入库费");
                restockingFees.setAmount(i.getRestockingFees());
                restockingFees.setItemsId(i.getItemsId());
                restockingFees.setAmountRefunded(i.getRestockingFeesRefunded());
                restockingFees.setReverseDetailsId(i.getId());
                restockingFees.setCanRefundAmount(BigDecimal.ZERO);
//            restockingFees.setHistoryAmountRefunded();
                // -------------
                DetailsVO total = new DetailsVO();
                total.setTitle("总价");

                total.setAmount(commodity.getAmount().
                        add(commodityTax.getAmount()).
                        add(returnOffer.getAmount().
                                add(restockingFees.getAmount())));

                total.setItemsId(null);
                total.setAmountRefunded(commodity.getAmountRefunded().
                        add(commodityTax.getAmountRefunded()).
                        add(returnOffer.getAmountRefunded()).
                        add(restockingFees.getAmountRefunded()));
                total.setReverseDetailsId(null);
                total.setCanRefundAmount(commodity.getCanRefundAmount().
                        add(commodityTax.getCanRefundAmount()).
                        add(returnOffer.getCanRefundAmount()).
                        add(restockingFees.getCanRefundAmount()));

                total.setHistoryAmountRefunded(Optional.ofNullable(commodity.getHistoryAmountRefunded()).orElse(BigDecimal.ZERO).
                        add(Optional.ofNullable(commodityTax.getHistoryAmountRefunded()).orElse(BigDecimal.ZERO)).
                        add(Optional.ofNullable(returnOffer.getHistoryAmountRefunded()).orElse(BigDecimal.ZERO)).
                        add(Optional.ofNullable(restockingFees.getHistoryAmountRefunded()).orElse(BigDecimal.ZERO)));


                list.add(commodity);
                list.add(commodityTax);
                list.add(returnOffer);
                list.add(restockingFees);
                list.add(total);

                i.addDetails(list);
                size++;
            }
            vo.setOrderProductNumbers(orderProductNumbers.toString());
            vo.setRefundDetails(refundDetails);
        }
        return (T) vo;
    }

    @NotNull
    private <T> T getOrderRefundReqVO(OrderRefundReqVO t) {
        OrderRefundReqVO vo = t;
        List<ReverseDetailsReqVO> reverseDetailsReqVO = t.getReverseDetailsReqVO();

        ConcurrentHashMap<String, Map<Integer, BigDecimal>> totalRefunded = (ConcurrentHashMap<String, Map<Integer, BigDecimal>>) reverseRecordDetailsService.getTotalRefunded(Optional.ofNullable(t.getParentTicketId()).orElse(t.getTicketId()));
        ConcurrentHashMap<Integer, BigDecimal> refunded = new ConcurrentHashMap<>();
        ConcurrentHashMap<Integer, BigDecimal> taxRefunded = new ConcurrentHashMap<>();
        Iterator<Map.Entry<Integer, BigDecimal>> refundedIterator;
        Iterator<Map.Entry<Integer, BigDecimal>> taxRefundedIterator;
        // 附件信息填充
        OrderRefundReqVO orderRefundReqVOs = saleOrdersMapper.getBaseMsgByOrderId(t.getOrderId());

        List<ReverseDetailsReqVO> details = orderRefundReqVOs.getReverseDetailsReqVO();


        if (CollUtil.isNotEmpty(totalRefunded)) {
            refunded = (ConcurrentHashMap<Integer, BigDecimal>) totalRefunded.get(OrderRefundFieldEnums.ITEM_PRICE_ADJ_REFUNDED.getField());
            refundedIterator = refunded.entrySet().iterator();

            taxRefunded = (ConcurrentHashMap<Integer, BigDecimal>) totalRefunded.get(OrderRefundFieldEnums.ITEM_TAX_ADJ_REFUNDED.getField());
            taxRefundedIterator = taxRefunded.entrySet().iterator();


        } else {
            taxRefundedIterator = null;
            refundedIterator = null;
        }


        ConcurrentHashMap<Integer, BigDecimal> finalRefunded = refunded;
        ConcurrentHashMap<Integer, BigDecimal> finalTaxRefunded = taxRefunded;
        reverseDetailsReqVO.forEach(i -> {
            details.forEach(item -> {
                if (item.getItemsId().equals(i.getItemsId())) {
                    i.setUnitPrice(Optional.ofNullable(item.getUnitPrice()).orElse(BigDecimal.ZERO));
                    i.setItemTaxAdj(Optional.ofNullable(item.getItemTaxAdj()).orElse(BigDecimal.ZERO));
                    i.setReturnOffer(Optional.ofNullable(item.getReturnOffer()).orElse(BigDecimal.ZERO));
                    i.setRestockingFees(Optional.ofNullable(item.getRestockingFees()).orElse(BigDecimal.ZERO));
                }
            });
            List<DetailsVO> list = new ArrayList<>();
            DetailsVO commodity = new DetailsVO();
            commodity.setTitle("商品");
            // 拿行金额
            commodity.setAmount(i.getItemAmount());
            // 改成历史refunded叠加金额
            commodity.setAmountRefunded(i.getItemPriceAdjRefunded());
            commodity.setItemsId(i.getItemsId());
            commodity.setReverseDetailsId(i.getId());
            commodity.setCanRefundAmount(BigDecimal.ZERO);
            if (ObjectUtil.isNotNull(refundedIterator)) {
                while (refundedIterator.hasNext()) {
                    Map.Entry<Integer, BigDecimal> next = refundedIterator.next();
                    if (next.getKey().equals(i.getItemsId())) {
                        commodity.setHistoryAmountRefunded(next.getValue());
                        finalRefunded.remove(next.getKey());
                        break;
                    }
                }
            } else {
                commodity.setHistoryAmountRefunded(BigDecimal.ZERO);
            }
            // 商品税
            DetailsVO commodityTax = new DetailsVO();
            commodityTax.setTitle("商品税");
            commodityTax.setAmount(i.getItemTaxAdj());
            commodityTax.setAmountRefunded(i.getItemTaxAdjRefunded());
            commodityTax.setItemsId(i.getItemsId());
            commodityTax.setReverseDetailsId(i.getId());
            commodityTax.setCanRefundAmount(BigDecimal.ZERO);
            if (taxRefundedIterator != null) {
                while (taxRefundedIterator.hasNext()) {
                    Map.Entry<Integer, BigDecimal> next = taxRefundedIterator.next();
                    if (ObjectUtil.isNotNull(next) && next.getKey().equals(i.getItemsId())) {
                        commodityTax.setHistoryAmountRefunded(next.getValue());
                        finalTaxRefunded.remove(next.getKey());
                        break;
                    }
                }
            } else {
                commodityTax.setHistoryAmountRefunded(BigDecimal.ZERO);
            }


            DetailsVO returnOffer = new DetailsVO();
            returnOffer.setTitle("退货优惠");
            returnOffer.setAmount(i.getReturnOffer());
            returnOffer.setItemsId(i.getItemsId());
            returnOffer.setAmountRefunded(i.getReturnOfferRefunded());
            returnOffer.setReverseDetailsId(i.getId());
            returnOffer.setCanRefundAmount(BigDecimal.ZERO);


            DetailsVO restockingFees = new DetailsVO();
            restockingFees.setTitle("重新入库费");
            restockingFees.setAmount(i.getRestockingFees());
            restockingFees.setItemsId(i.getItemsId());
            restockingFees.setAmountRefunded(i.getRestockingFeesRefunded());
            restockingFees.setReverseDetailsId(i.getId());
            restockingFees.setCanRefundAmount(BigDecimal.ZERO);

            list.add(commodity);
            list.add(commodityTax);
            list.add(returnOffer);
            list.add(restockingFees);

            i.addDetails(list);
        });
        vo.setReverseDetailsReqVO(reverseDetailsReqVO);
        return (T) vo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean rejectBuyerRefundReq(String tikTokShopApi, String orderId, Long reverseOrderId, Long ticketId, Long shopId, String refundType, String rejectReason, Long contextId, List<String> imageIdList,String comment) {
        ScTicket scTicket = scTicketMapper.selectScTicketById(ticketId);
        if (OrderRefundEnum.REJECTED_TICKET_ORDER.getCode().equals(scTicket.getBusinessTicketStatus())) {
            throw new ErpCommonException("退款请求已经拒绝退款，请勿重复拒绝");
        }
        List<ScTicket> list = scTicketMapper.selectScTicketByParentId(ticketId);
        List<SaleOrderVoTicket> saleOrderVoTicketList = saleOrdersMapper.selectOrderByOrderId(Long.valueOf(orderId));
        if (AccountSaleChannelEnum.TIKTOK.getValue().equals(saleOrderVoTicketList.get(0).getChannel())) {

            if (CollectionUtil.isNotEmpty(list)) {
                //子工单已经同意退款
                List<ScTicket> finishRefundTicket = list.stream().filter(t -> ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(t.getTicketType()) && ScTicketConstant.TICKET_STATUS_FINISH.equals(t.getTicketStatus())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(finishRefundTicket)) {
                    throw new ErpCommonException("退款请求的站内退款工单已经同意退款了，无法拒绝退款!");
                }
            }
            AbstractRefundHandler invokeStrategy = factory.getInvokeStrategy(tikTokShopApi);
            //        tiktok通了以后放开
            HashMap<String, Object> query = new HashMap();
            query.put("reasonKey", rejectReason);
            query.put("reverseOrderId", reverseOrderId);
            query.put("shopId", shopId);
            try{
                if(TIKTOK_SWITCH){
                    TikTokResponseBaseEntity o = (TikTokResponseBaseEntity)invokeStrategy.rejectReverseRequest(query, imageIdList,comment);
                    if (o == null) {
                        throw new ErpCommonException("拒绝退款申请失败");
                    }
                    if (!new Integer(0).equals(o.getCode())) {
                        throw new ErpCommonException("拒绝退款申请失败" + o.getMessage());
                    }
                }
            }catch (Exception e){
                e.printStackTrace();
                log.error("拒绝退款申请失败,请手动补偿{}", JSONObject.toJSONString(query));
                throw new CommonException("拒绝退款申请失败");
            }
        }

        // 工单状态同步为,拒绝
        Boolean ticketResult = scTicketService.updateScTicket(ticketId, ScTicketConstant.TICKET_STATUS_PROCESSING, null, OrderRefundEnum.REJECTED_TICKET_ORDER.getCode());

        ScTicketLog scTicketLog = new ScTicketLog();
        scTicketLog.setTicketId(ticketId);
        scTicketLog.settingDefaultCreate();
        scTicketLog.settingDefaultUpdate();
        scTicketLog.setOperateContent("拒绝退款请求成功");
        scTicketLog.setOperateType("Update");
        scTicketLog.setOperateTime(DateUtils.getNowDate());
        scTicketLog.setOperatorBy(scTicketLog.getCreatedName());
        scTicketLogService.insertScTicketLog(scTicketLog);
        //关闭子工单
        if (CollectionUtil.isNotEmpty(list)) {
            List<ScTicket> collect = list.stream().filter(t -> ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(t.getTicketType())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)) {
                List<Long> orderRefundId = collect.stream().map(ScTicket::getSourceId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
                List<OrderRefund> orderRefundList = orderRefundService.list(new LambdaQueryWrapper<OrderRefund>().in(OrderRefund::getId, orderRefundId));
                if (CollectionUtil.isNotEmpty(orderRefundList)) {
                    orderRefundList.stream().forEach(refund->{
                        refund.setTreatmentState(OrderRefundEnum.REJECTED_TICKET_ORDER.getCode());
                        refund.setRefundStatus(OrderRefundStateEnum.REJECTED.getCode());
                    });
                    orderRefundService.updateBatchById(orderRefundList);
                }
                Long[] ticketIds = list.stream().map(ScTicket::getId).toArray(Long[]::new);
                scTicketMapper.updateScTicketStatusByIds(ticketIds,ScTicketConstant.TICKET_STATUS_CLOSED);
            }
        }
        return ticketResult;
    }


    @Override
    public List<JSONObject> getUploadImageImageIdList(String tikTokShopApi, MultipartFile[] file, Long shopId) {

        if (ObjectUtil.isEmpty(file)) {
            throw new ErpCommonException("上传图片不能为空");
        }
        //支持格式: JPG, JPEG, PNG, WEBP, HEIC, BMP

        for (MultipartFile multipartFile : file) {
            if (!Arrays.asList("JPG", "JPEG", "PNG", "WEBP", "HEIC", "BMP").contains(multipartFile.getOriginalFilename().substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1).toUpperCase())) {
                throw new ErpCommonException("上传图片格式不正确,请上传JPG, JPEG, PNG, WEBP, HEIC, BMP格式");
            }
            //文件最大为5M
            if (multipartFile.getSize() > 5 * 1024 * 1024) {
                throw new ErpCommonException("上传图片大小不能超过5M");
            }
            //尺寸大小限制
            //最小尺寸100x100 px，最大尺寸20000x20000 px
            if (multipartFile.getSize() < 100 * 100) {
                throw new ErpCommonException("上传图片尺寸不能小于100x100 px");
            }
            if (multipartFile.getSize() > 20000 * 20000) {
                throw new ErpCommonException("上传图片尺寸不能超过20000x20000 px");
            }
        }

        AbstractRefundHandler invokeStrategy = factory.getInvokeStrategy(tikTokShopApi);
        List<TikTokResponseBaseEntity> list = new ArrayList<>();
        if (TIKTOK_SWITCH) {
            list = invokeStrategy.batchUploadImage(file, shopId);
        }else {
            List<JSONObject> result = new ArrayList<>();
            for (MultipartFile fileUrl : file) {
                JSONObject object = new JSONObject();
                object.put("width", 100);
                object.put("height", 100);
                object.put("uri","uri测试001" + fileUrl.getOriginalFilename());
                object.put("url","url测试001" + fileUrl.getOriginalFilename());
                result.add(object);
            }
            return result;
        }

        if (CollectionUtil.isEmpty(list)) {
            throw new ErpCommonException("上传图片异常");
        }
        boolean allFail = list.stream().allMatch(Objects::isNull);
        if (allFail) {
            throw new ErpCommonException("上传图片失败");
        }
        boolean partFail = list.stream().anyMatch(t -> !new Integer(0).equals(t.getCode()));
        if (partFail) {
            throw new ErpCommonException("图片上传失败");
        }
        List<JSONObject> imageIdList = new ArrayList<>();
        list.forEach(t -> {
            JSONObject object = JSONObject.parseObject(JSONObject.toJSONString(t.getData()), JSONObject.class);
            imageIdList.add(object);
        });
        return imageIdList;
    }

    /**
     * 1. 退回后: 工单状态 new 操作状态 处理,
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean sendBack(SendBackVO sendBackVO) {
        //"退回站内退款申请工单"   new + process
        ScTicket ticketForReturn = scTicketService.getScTicketForReturn(sendBackVO.getTicketId(), sendBackVO.getRefundType(), ScTicketConstant.TICKET_STATUS_NEW, ScTicketConstant.TICKET_OPERATE_PROCESS, OrderRefundEnum.RETURN.getMsg(), OrderRefundEnum.INTERNAL.getMsg(), sendBackVO.getBackReason());

        OrderRefund orderRefundForReturn = orderRefundService.getOrderRefundForReturn(sendBackVO.getSourceId(), sendBackVO.getRefundType(), OrderRefundEnum.RETURN.getCode());

//        OrderRefundRequest request = orderRefundRequestService.getOrderRefundForReturn(sendBackVO.getSourceId(), sendBackVO.getRefundType(), OrderRefundEnum.RETURN.getCode());


        ConfTicketAssign confTicketAssign = confTicketAssignService.selectConfTicketAssignByTicketType(ticketForReturn.getOrganizationId(), ticketForReturn.getTicketType());
//
        refundManger.updateScTicketHandlerBy(confTicketAssign, ticketForReturn);
        orderRefundForReturn.setRefundRemark(sendBackVO.getRefundRemark());
        refundManger.updateForReturn(ticketForReturn, orderRefundForReturn, null);

        return Boolean.TRUE;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitForReturnEdit(OrderRefundReqVO vo, String ticketId) {

        // 更新工单状态 OrderRefundReqVO
        ScTicket ticketForReturn = scTicketService.getScTicketForReturn(vo.getTicketId(), String.valueOf(vo.getRefundType()), ScTicketConstant.TICKET_STATUS_PROCESSING, ScTicketConstant.TICKET_OPERATE_APPROVE, OrderRefundEnum.SUBMITTED.getMsg(), OrderRefundEnum.INTERNAL.getMsg(), null);

        OrderRefund orderRefundForReturn = orderRefundService.getOrderRefundForReturn(vo.getSourceId(), String.valueOf(vo.getRefundType()), OrderRefundEnum.SUBMITTED.getCode());

        refundManger.saveInstationRefundMsg(vo, Boolean.TRUE);
        refundManger.updateForReturn(ticketForReturn, orderRefundForReturn, null);

        return Boolean.TRUE;
    }

    @Override
    @IsEnoughRefund
    public void testEnoughRefund(Long ticketId, Integer refundType) {

    }


    /**
     * 站内用
     *
     * @param reverseOrderId 反向顺序 ID
     * @param orderId        订单编号
     * @return {@link OrderRefundReqVO}
     */
    private OrderRefundReqVO getOrderRefundReqVO(Long reverseOrderId, String orderId) {
        // 详情
        List<ReverseDetailsReqVO> detailsReqVO = new ArrayList<>();
        List<ReverseRecordDetails> details = reverseRecordDetailsService.list(new LambdaQueryWrapper<ReverseRecordDetails>().eq(ReverseRecordDetails::getReverseOrderId, reverseOrderId));
        details.forEach(item -> {
            ReverseDetailsReqVO reverseDetailsReqVO = new ReverseDetailsReqVO();
            BeanUtil.copyProperties(item, reverseDetailsReqVO);
            reverseDetailsReqVO.setItemAmount(item.getReturnTotalAmount());
            reverseDetailsReqVO.setItemPriceAdj(item.getReturnTotalAmount());
            detailsReqVO.add(reverseDetailsReqVO);
        });
        BeanUtil.copyProperties(details, detailsReqVO);
        // 主表
        OrderRefundReqVO vo = new OrderRefundReqVO();
        OrderRefundRequest request = orderRefundRequestMapper.selectOne(new LambdaQueryWrapper<OrderRefundRequest>().eq(OrderRefundRequest::getReverseOrderId, reverseOrderId));

        BeanUtil.copyProperties(request, vo);
        vo.addReverseDetailsReqVO(detailsReqVO);
        // 附件表
        List<ReverseRecord> list = reverseRecordService.list(new LambdaQueryWrapper<ReverseRecord>().eq(ReverseRecord::getOrderRequestId, request.getId()));
        vo.addReverseRecord(list);
        vo.setIsSubmit(isSaved(request.getIsSaved()));
        // 存在退款请求,并且退款请求的类型是tiktok
        if (ChannelTagEnum.TIK_TOK.getChannelFlag().equals(request.getChannel())) {
            vo.setIsEditable(Boolean.FALSE);
        } else {
            vo.setIsEditable(Boolean.TRUE);
        }
        vo.setMeta(refundManger.getAttachment(request.getId(), ScTicketConstant.TICKET_SOURCE_REFUND_REQUEST));
        return vo;
    }

    /**
     * @param saved
     * @return {@link Boolean}
     */
    private Boolean isSaved(Boolean saved) {
        if (saved) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }

    private OrderRefundReqVO getOrderRefundReqVO(Long ticketId, Long orderRefundId, Integer refundType) {
        // 主表
        OrderRefundReqVO vo = new OrderRefundReqVO();
        OrderRefund orderRefund = orderRefundMapper.selectById(orderRefundId);

        // 详情
        List<ReverseDetailsReqVO> detailsReqVO = new ArrayList<>();
        List<ReverseRecordDetails> details = reverseRecordDetailsService.list(new LambdaQueryWrapper<ReverseRecordDetails>().eq(ReverseRecordDetails::getOrderRefundId, orderRefundId));
        details.forEach(item -> {
            ReverseDetailsReqVO reverseDetailsReqVO = new ReverseDetailsReqVO();
            BeanUtil.copyProperties(item, reverseDetailsReqVO);
            reverseDetailsReqVO.setItemAmount(item.getReturnTotalAmount());
            detailsReqVO.add(reverseDetailsReqVO);
        });
        BeanUtil.copyProperties(details, detailsReqVO);

        BeanUtil.copyProperties(orderRefund, vo);
        vo.addReverseDetailsReqVO(detailsReqVO);
        // 附件表
        List<ReverseRecord> list = reverseRecordService.list(new LambdaQueryWrapper<ReverseRecord>().eq(ReverseRecord::getOrderRequestId, orderRefund.getId()));
        vo.addReverseRecord(list);
        // 是否可提交,保存过可以进行提交  站外逻辑
        vo.setIsSubmit(isSaved(orderRefund.getIsSaved()));
        if(OrderRefundEnum.SALES_RETURN_TO_INTERNAL.getCodeType().equals(refundType)){
            vo.setIsEditable(Boolean.FALSE);
        }
        if(OrderRefundEnum.EXTERNAL.getCodeType().equals(refundType)){
            vo.setIsEditable(Boolean.TRUE);
        }
        vo.setMeta(refundManger.getAttachment(orderRefund.getId(), ScTicketConstant.TICKET_SOURCE_REFUND));
        return vo;
    }


    private Boolean isOnlyOneUnfinished(String orderId) {
        // 要用orederId去查对应的ticketId 判断工单数量是不是>=2 并且工单状态没结束
        Integer countList = scTicketService.countList(orderId, ScTicketConstant.TICKET_STATUS_FINISH);
        if (countList > 0) {
            return Boolean.FALSE;
        } else {
            return Boolean.TRUE;
        }
    }

    @Override
    @IsRefusal
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveInstationRefundMsg(OrderRefundReqVO vo, Boolean isSubmit, Long contextId) {
        //金额校验
        OrderRefundRequest request = new OrderRefundRequest();
        ScTicket scTicket = scTicketService.selectScTicketById(vo.getTicketId());
        vo.setOrderId(String.valueOf(scTicket.getOrderId()));
        vo.setContextId(contextId);
        // 更新附件表
        OrderRefund orderRefund = new OrderRefund();
        List<ReverseDetailsReqVO> reverseDetailsReqVO = vo.getReverseDetailsReqVO();
        List<ReverseRecordDetails> detailsList = new ArrayList<>();
        reverseDetailsReqVO.forEach(i -> {
            ReverseRecordDetails details = new ReverseRecordDetails();
            BeanUtil.copyProperties(i, details);

            detailsList.add(details);
        });

        BeanUtil.copyProperties(vo, orderRefund);
        BeanUtil.copyProperties(vo, request);
        Map<String, BigDecimal> map = reverseRecordDetailsService.getRefundedForOrder(reverseDetailsReqVO, vo.getRefundType());

//        orderRefund.setId(vo.getId());

        orderRefund.setQuantityCancelled(map.get("quantity").setScale(0, RoundingMode.HALF_UP).intValue());
        orderRefund.setItemPriceAdjRefunded(map.get("itemPriceAdjRefunded"));
        orderRefund.setItemTaxAdjRefunded(map.get("itemTaxAdjRefunded"));
        orderRefund.setReturnOfferRefunded(map.get("returnOfferRefunded"));
        orderRefund.setRestockingFeesRefunded(map.get("restockingFeesRefunded"));
        orderRefund.setRefundOrderStatus(RefundOrderStatusEnum.SAVED_AND_NOT_REFUND.getCode());
        if (ObjectUtil.isNull(orderRefund) || OrderRefundEnum.REJECTED_TICKET_ORDER.getCode().equals(orderRefund.getTreatmentState())) {
            orderRefund.setTreatmentState(OrderRefundEnum.TO_BE_REFUNDED.getCode());
        }

        orderRefund.setIsSaved(Boolean.TRUE);
        // 提交动作的时候才确定类型
        if (isSubmit && OrderRefundEnum.INTERNAL.getCodeType().equals(vo.getRefundType())) {
            orderRefund.setId(vo.getOrderRefundId());
            orderRefund.setRefundType(String.valueOf(vo.getRefundType()));
        } else if (isSubmit && OrderRefundEnum.EXTERNAL.getCodeType().equals(vo.getRefundType())) {
            orderRefund.setRefundType(String.valueOf(vo.getRefundType()));
        } else if (isSubmit && OrderRefundEnum.SALES_RETURN_TO_INTERNAL.getCodeType().equals(vo.getRefundType())) {
            orderRefund.setRefundType(String.valueOf(vo.getRefundType()));
        }

        if (OrderRefundEnum.INTERNAL.getCodeType().equals(vo.getRefundType())) {
            orderRefund.setOrderRequestId(request.getId());
        }
        // 同步退款表
        List<OrderRefund> list = orderRefundService.list(new LambdaQueryWrapper<OrderRefund>().
                eq(OrderRefund::getOrderId, vo.getOrderId()).
                eq(OrderRefund::getReverseOrderId, vo.getReverseOrderId()));

        OrderRefund in = orderRefundService.getOne(new LambdaQueryWrapper<OrderRefund>().
                eq(OrderRefund::getId, vo.getId()));

        if (ObjectUtil.isNotEmpty(in) &&
                (OrderRefundEnum.EXTERNAL.getCodeType().equals(vo.getRefundType()) ||
                        OrderRefundEnum.SALES_RETURN_TO_INTERNAL.getCodeType().equals(vo.getRefundType()))) {
            orderRefundMapper.updateById(orderRefund);
            refundManger.saveMeta(vo.getMeta(), ScTicketConstant.TICKET_SOURCE_REFUND, orderRefund.getId(), vo.getContextId());
        } else if (ObjectUtil.isEmpty(in) && (OrderRefundEnum.EXTERNAL.getCodeType().equals(vo.getRefundType()) ||
                OrderRefundEnum.SALES_RETURN_TO_INTERNAL.getCodeType().equals(vo.getRefundType()))) {
            orderRefundMapper.insert(orderRefund);
            refundManger.saveMeta(vo.getMeta(), ScTicketConstant.TICKET_SOURCE_REFUND, orderRefund.getId(), vo.getContextId());
        }

        if (CollUtil.isNotEmpty(list) && OrderRefundEnum.INTERNAL.getCodeType().equals(vo.getRefundType())) {
            orderRefundMapper.updateById(orderRefund);
            refundManger.saveMeta(vo.getMeta(), ScTicketConstant.TICKET_SOURCE_REFUND, orderRefund.getId(), vo.getContextId());
        } else if (OrderRefundEnum.INTERNAL.getCodeType().equals(vo.getRefundType())) {
            orderRefundMapper.insert(orderRefund);
            refundManger.saveMeta(vo.getMeta(), ScTicketConstant.TICKET_SOURCE_REFUND, orderRefund.getId(), vo.getContextId());
        }

        if (OrderRefundEnum.INTERNAL.getCodeType().equals(vo.getRefundType())) {
            request.setOrderRefundId(orderRefund.getId());
            request.setIsSaved(Boolean.TRUE);
            if (!OrderRefundEnum.REJECTED_TICKET_ORDER.getCode().equals(request.getTreatmentState())) {
                request.setTreatmentState(OrderRefundEnum.SUBMITTED.getCode());
            }
            orderRefundRequestService.updateById(request);
            refundManger.saveMeta(vo.getMeta(), ScTicketConstant.TICKET_SOURCE_REFUND_REQUEST, request.getId(), vo.getContextId());
        }
        List<ReverseRecord> records = vo.getRecords();

        detailsList.forEach(item -> {
            item.setOrderRefundId(orderRefund.getId());

        });
        reverseRecordDetailsService.updateBatchById(detailsList);

        // 附件表
        if (CollUtil.isNotEmpty(records)) {
            records.forEach(i -> {
                i.setOrderRefundId(orderRefund.getId());
                i.setOrderRequestId(request.getId());
            });
            reverseRecordService.saveOrUpdateBatch(records);
        }

        List<String> ids = vo.getNotRefundDetailIds();
        if (CollUtil.isNotEmpty(ids)) {
            // 退款明细
            reverseRecordDetailsService.removeByIds(ids);
        }

        return Boolean.TRUE;
    }

    @Override
    @IsRefusal
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitRefundRequestMsg(OrderRefundReqVO vo, Long ticketId, Long contextId) {

        ScTicket scTicket = scTicketService.selectScTicketById(ticketId);
        vo.setOrderId(String.valueOf(scTicket.getOrderId()));
        if (OrderRefundEnum.EXTERNAL.getCodeType().equals(vo.getRefundType())) {
            vo.setSourceId(vo.getId());
            vo.setShopId(scTicket.getShopId());
        }
        if (OrderRefundEnum.SALES_RETURN_TO_INTERNAL.getCodeType().equals(vo.getRefundType())) {
            vo.setReverseOrderId(scTicket.getReverseOrderId());
            vo.setShopId(scTicket.getShopId());
            vo.setSourceId(vo.getId());
        }
        Long scTicketId = createScTicket(vo.getChannelOrderId(), vo.getRefundType(), vo.getSourceId(), vo.getReverseOrderId(), ticketId, String.valueOf(scTicket.getShopId()), scTicket.getOrderId());
        vo.setParentTicketId(ticketId);
        vo.setTicketId(scTicketId);
        saveInstationRefundMsg(vo, Boolean.TRUE, contextId);
        // 生成工单 return_total

        return Boolean.TRUE;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmReverseRequest(String channel, Long orderId, String reverseOrderId, Long sourceId, Long ticketId, Long shopId, String refundType, String bankAccountId, String bankAccountNumber, String refundRemark,Boolean buyerKeepItem) {
        // 先调用确认退款接口,成功就更新退款表,失败就抛异常
        isRefunded(ticketId);
        List<SaleOrderVoTicket> saleOrderVoTicketList = saleOrdersMapper.selectOrderByOrderId(orderId);
        //tiktok站内退款才会调用tiktok退款接口
        if (ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(refundType) && AccountSaleChannelEnum.TIKTOK.getValue().equals(saleOrderVoTicketList.get(0).getChannel())) {
            ScTicket scTicket = scTicketService.selectScTicketById(ticketId);
            String parentTicketType = null;
            if(scTicket != null && scTicket.getParentTicketId() != null) {
                Long parentTicketId = scTicket.getParentTicketId(); //父工单id
                ScTicket parentScTicket = scTicketService.selectScTicketById(parentTicketId);
                if(parentScTicket != null) {
                    parentTicketType =  parentScTicket.getTicketType();
                }
            }
            if(ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG.equals(parentTicketType)) { //父工单是站内信的站内退款
                try {
                    OrderRefund refund = orderRefundService.getById(sourceId);
                    List<OrderRefundItem> orderRefundItems = orderRefundItemMapper.selectList(Wrappers.lambdaQuery(OrderRefundItem.class).eq(OrderRefundItem::getOrderRefundId, sourceId));
                    BigDecimal oneSellerSkuTotalRefundAmount = orderRefundItems.stream().map(OrderRefundItem::getRefundAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

                    JSONObject reqJsonObject = new JSONObject();
                    SaleOrders saleOrders = saleOrdersMapper.selectById(orderId);
                    reqJsonObject.put("order_id",saleOrders.getOrderNo());
                    JSONArray reqJsonArray = new JSONArray();

                    //todo 这里要筛选出channelOrderItemId 不一样的。
                    Set<String> channelOrderItemIdSet = orderRefundItems.stream().map(i -> i.getChannelOrderItemId()).collect(Collectors.toSet());
                    for(String item:channelOrderItemIdSet) {
                        //todo 查询订单明细表
                        List<SaleOrderItemVO> saleOrderItemVOS = saleOrderItemsMapper.selectQuantityAndSkuIdByParam(item);
                        if(CollectionUtil.isEmpty(saleOrderItemVOS)) {
                            throw new CommonException("退款失败,请联系管理员:查询不到订单明细：" + item);
                        }
                        JSONObject skuObject = new JSONObject();
                        skuObject.put("quantity",saleOrderItemVOS.get(0).getQuantity());
                        if(StringUtils.isNotEmpty(saleOrderItemVOS.get(0).getSkuId())) {
                            skuObject.put("sku_id", saleOrderItemVOS.get(0).getSkuId());
                        } else {
                            throw new CommonException("退款失败,请联系管理员:查询不到skuId：" + item);
                        }
                        reqJsonArray.add(skuObject);
                    }
                    reqJsonObject.put("skus",reqJsonArray);
//                    JSONArray orderLineItemIdsArray = new JSONArray();
//                    orderLineItemIdsArray.add("576886828802937516");
//                    reqJsonObject.put("order_line_item_ids",orderLineItemIdsArray);
                    reqJsonObject.put("return_reason", TkStationLetterStationRefundReasonEnum.getValue(refund.getReturnText()));
                    reqJsonObject.put("return_type","REFUND");
                    reqJsonObject.put("currency",refund.getCurrency());
                    reqJsonObject.put("refund_total",oneSellerSkuTotalRefundAmount.toString());
                    log.error("tk站内信站内退款参数{}，工单id:{}", reqJsonObject.toJSONString(),ticketId);
                    JSONObject tikTokShopReturn = tikTokUtil.postTikTokShopV2(reqJsonObject.toJSONString(), TikTokApiEnums.TIKTOK_RETURNS.getUrl(), TikTokApiEnums.TIKTOK_RETURNS.getPath(), JSONObject.class, shopId);
                    if(tikTokShopReturn != null) {
                        log.error("tk站内信站内退款返回结果{}，工单id:{}", tikTokShopReturn.toJSONString(),ticketId);
                        String message = tikTokShopReturn.getString("message");
                        if("Success".equalsIgnoreCase(message)) { //成功
                            // 退款表单同步,工单同步,当前工单结束
                            Boolean refundResult = orderRefundService.refundFinish(sourceId, bankAccountId, bankAccountNumber, refundRemark);
                            Boolean ticketResult = updateOrderRefundApply(channel, orderId, reverseOrderId, sourceId, ticketId, OrderRefundStateEnum.COMPLETED.getDesc());
                            if (!refundResult || !ticketResult) {
                                log.info("tk站内信站内退款失败,工单id:{}", ticketId);
                                throw new CommonException("tk站内信站内退款失败,请联系管理员");
                            } else {
                                ScTicketLog ticketLog = new ScTicketLog();
                                ticketLog.settingDefaultCreate();
                                ticketLog.setTicketId(ticketId);
                                ticketLog.setOperateType("Update");
                                ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
                                ticketLog.setOperateTime(DateUtils.getNowDate());
                                ticketLog.setOperateContent("已提交退款");
                                scTicketLogService.insertScTicketLog(ticketLog);
                            }
                            return Boolean.TRUE;
                        } else { //失败
                            log.error("工单id:{},逆向单号:{},退款失败{}",ticketId,reverseOrderId, tikTokShopReturn.toJSONString());
                            //更新退款状态为退款失败，记录日志
                            refund.setRefundStatus(OrderRefundStateEnum.REFUND_FAIL.getCode());
                            refund.settingDefaultUpdate();
                            orderRefundService.updateById(refund);
                            ScTicketLog ticketLog = new ScTicketLog();
                            ticketLog.settingDefaultCreate();
                            ticketLog.setTicketId(ticketId);
                            ticketLog.setOperateType("Update");
                            ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
                            ticketLog.setOperateTime(DateUtils.getNowDate());
                            ticketLog.setOperateContent("退款失败:失败原因" + message);
                            scTicketLogService.insertScTicketLog(ticketLog);
                            return false;
                        }
                    } else {
                        log.error("工单id:{},逆向单号:{},退款失败返回值为空",ticketId,reverseOrderId);
                        //更新退款状态为退款失败，记录日志
                        refund.setRefundStatus(OrderRefundStateEnum.REFUND_FAIL.getCode());
                        refund.settingDefaultUpdate();
                        orderRefundService.updateById(refund);
                        ScTicketLog ticketLog = new ScTicketLog();
                        ticketLog.settingDefaultCreate();
                        ticketLog.setTicketId(ticketId);
                        ticketLog.setOperateType("Update");
                        ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
                        ticketLog.setOperateTime(DateUtils.getNowDate());
                        ticketLog.setOperateContent("退款失败");
                        scTicketLogService.insertScTicketLog(ticketLog);
                        return false;
                    }
                } catch (Exception e) {
                    log.error("调用tk站内信站内退款失败工单id:{},逆向单号:{},退款失败原因:{}",ticketId,reverseOrderId,e.getMessage());
                    //更新退款状态为退款失败，记录日志
                    OrderRefund refund = orderRefundService.getById(sourceId);
                    refund.setRefundStatus(OrderRefundStateEnum.REFUND_FAIL.getCode());
                    refund.settingDefaultUpdate();
                    orderRefundService.updateById(refund);
                    ScTicketLog ticketLog = new ScTicketLog();
                    ticketLog.settingDefaultCreate();
                    ticketLog.setTicketId(ticketId);
                    ticketLog.setOperateType("Update");
                    ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
                    ticketLog.setOperateTime(DateUtils.getNowDate());
                    ticketLog.setOperateContent("退款失败");
                    scTicketLogService.insertScTicketLog(ticketLog);
                    return false;
                }
            } else {

                // 已同意退款的不再退款
                HashMap<String, Object> params = new HashMap();
                params.put("reverseOrderId", reverseOrderId);
                params.put("shopId", shopId);

                if (buyerKeepItem != null) {
                    params.put("buyer_keep_item", buyerKeepItem);
                    if (buyerKeepItem) {
                        params.put("decision", "APPROVE_RETURN");
                    }else {
                        params.put("decision", "APPROVE_REFUND");
                    }
                }else {
                    params.put("decision", "APPROVE_REFUND");
                }
                // tiktok
                if (TIKTOK_SWITCH) {
                    try {
                        Object result = confirmTiktok(channel, params);
                        if (result != null) {

                            JSONObject resultObject = JSONObject.parseObject(JSONObject.toJSONString(result));
                            if (!resultObject.getInteger("code").equals(0)) {
                                log.error("工单id:{},逆向单号:{},退款失败原因:{}",ticketId,reverseOrderId,resultObject.get("message"));
                                OrderRefund refund = orderRefundService.getById(sourceId);
                                refund.setRefundStatus(OrderRefundStateEnum.REFUND_FAIL.getCode());
                                refund.settingDefaultUpdate();
                                orderRefundService.updateById(refund);
                                ScTicketLog ticketLog = new ScTicketLog();
                                ticketLog.settingDefaultCreate();
                                ticketLog.setTicketId(ticketId);
                                ticketLog.setOperateType("Update");
                                ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
                                ticketLog.setOperateTime(DateUtils.getNowDate());
                                ticketLog.setOperateContent("退款失败原因:" + resultObject.get("message"));
                                scTicketLogService.insertScTicketLog(ticketLog);
                                return false;
                            }
                        } else {
                            OrderRefund refund = orderRefundService.getById(sourceId);
                            refund.setRefundStatus(OrderRefundStateEnum.REFUND_FAIL.getCode());
                            refund.settingDefaultUpdate();
                            orderRefundService.updateById(refund);
                            ScTicketLog ticketLog = new ScTicketLog();
                            ticketLog.settingDefaultCreate();
                            ticketLog.setTicketId(ticketId);
                            ticketLog.setOperateType("Update");
                            ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
                            ticketLog.setOperateTime(DateUtils.getNowDate());
                            ticketLog.setOperateContent("退款失败");
                            scTicketLogService.insertScTicketLog(ticketLog);
                            return false;
                        }
                    } catch (Exception e) {
                        log.error("工单id:{},逆向单号:{},退款失败原因:{}", ticketId, reverseOrderId, e.getMessage());
                        //更新退款状态为退款失败，记录日志
                        OrderRefund refund = orderRefundService.getById(sourceId);
                        refund.setRefundStatus(OrderRefundStateEnum.REFUND_FAIL.getCode());
                        refund.settingDefaultUpdate();
                        orderRefundService.updateById(refund);
                        ScTicketLog ticketLog = new ScTicketLog();
                        ticketLog.settingDefaultCreate();
                        ticketLog.setTicketId(ticketId);
                        ticketLog.setOperateType("Update");
                        ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
                        ticketLog.setOperateTime(DateUtils.getNowDate());
                        ticketLog.setOperateContent("退款失败");
                        scTicketLogService.insertScTicketLog(ticketLog);
                        return false;
                    }

                }
                // 退款表单同步,工单同步,当前工单结束
                Boolean refundResult = orderRefundService.refundFinish(sourceId, null, null, refundRemark);
//            Boolean requestResult = orderRefundRequestService.refundRequestFinish(reverseOrderId);
                //如果子工单对应父工单为退款请求就更新退款请求的退款业务状态
                updateOrderRequestState(ticketId);
                // 工单 当前工单id 退款表受理状态
                Boolean ticketResult = updateOrderRefundApply(channel, orderId, reverseOrderId, sourceId, ticketId, OrderRefundStateEnum.COMPLETED.getDesc());
                if (!refundResult || !ticketResult) {
                    log.info("退款失败,工单号:{},逆向订单号:{}", ticketId, reverseOrderId);
                    throw new CommonException("退款失败,请联系管理员");
                } else {
                    ScTicketLog ticketLog = new ScTicketLog();
                    ticketLog.settingDefaultCreate();
                    ticketLog.setTicketId(ticketId);
                    ticketLog.setOperateType("Update");
                    ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
                    ticketLog.setOperateTime(DateUtils.getNowDate());
                    ticketLog.setOperateContent("已提交退款");
                    scTicketLogService.insertScTicketLog(ticketLog);
                }
            }

            //站内退款sc渠道
        } else if (ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(refundType) && AccountSaleChannelEnum.SC.getValue().equals(saleOrderVoTicketList.get(0).getChannel())) {

            if (TIKTOK_SWITCH) {
                ScOrderRefundRequest scOrderRefundRequest = getScOrderRefundRequest(orderId, ticketId, shopId, AuthContextHolder.getAuthUserDetails().getOrgId(),sourceId,saleOrderVoTicketList.get(0).getOrderNo());
                log.info("sc站内退款请求:{},当前时间:{}", JSON.toJSONString(scOrderRefundRequest),DateUtils.getNowDate());
                try {
                    amzFeedService.submitOrderRefundFeed(scOrderRefundRequest);
                } catch (Exception e) {
                    log.error("工单id:{},逆向单号:{},退款失败原因:{}",ticketId,reverseOrderId,e.getMessage());
                    //更新退款状态为退款失败，记录日志
                    OrderRefund refund = orderRefundService.getById(sourceId);
                    refund.setRefundStatus(OrderRefundStateEnum.REFUND_FAIL.getCode());
                    refund.settingDefaultUpdate();
                    orderRefundService.updateById(refund);
                    ScTicketLog ticketLog = new ScTicketLog();
                    ticketLog.settingDefaultCreate();
                    ticketLog.setTicketId(ticketId);
                    ticketLog.setOperateType("Update");
                    ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
                    ticketLog.setOperateTime(DateUtils.getNowDate());
                    ticketLog.setOperateContent("退款失败");
                    scTicketLogService.insertScTicketLog(ticketLog);
                    return false;
                }
            }
            // 退款表单同步,工单同步,当前工单结束
            Boolean refundResult = orderRefundService.refundFinish(sourceId, null, null,refundRemark);
            // 工单 当前工单id 退款表受理状态
            Boolean ticketResult = updateOrderRefundApply(channel, orderId, reverseOrderId, sourceId, ticketId, OrderRefundStateEnum.COMPLETED.getDesc());
            if (!refundResult || !ticketResult) {
                log.info("退款失败,工单号:{},逆向订单号:{}", ticketId, reverseOrderId);
                throw new CommonException("退款失败,请联系管理员");
            } else {
                ScTicketLog ticketLog = new ScTicketLog();
                ticketLog.settingDefaultCreate();
                ticketLog.setTicketId(ticketId);
                ticketLog.setOperateType("Update");
                ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
                ticketLog.setOperateTime(DateUtils.getNowDate());
                ticketLog.setOperateContent("已提交退款");
                scTicketLogService.insertScTicketLog(ticketLog);
            }
            //walmart渠道站内信，站内退款
        } else if(ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(refundType) && AccountSaleChannelEnum.WALMART.getValue().equals(saleOrderVoTicketList.get(0).getChannel())) {
            ScTicket scTicket = scTicketService.selectScTicketById(ticketId);
            String parentTicketType = null;
            if(scTicket != null && scTicket.getParentTicketId() != null) {
                Long parentTicketId = scTicket.getParentTicketId(); //父工单id
                ScTicket parentScTicket = scTicketService.selectScTicketById(parentTicketId);
                if(parentScTicket != null) {
                    parentTicketType =  parentScTicket.getTicketType();
                }
            }
            if(ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG.equals(parentTicketType)) {
                try {
                    OrderRefund refund = orderRefundService.getById(sourceId);
                    List<OrderRefundItem> orderRefundItems = orderRefundItemMapper.selectList(Wrappers.lambdaQuery(OrderRefundItem.class).eq(OrderRefundItem::getOrderRefundId, sourceId));
                    SaleOrders saleOrders = saleOrdersMapper.selectById(orderId);
                    Map<String, List<OrderRefundItem>> map = orderRefundItems.stream().collect(Collectors.groupingBy(OrderRefundItem::getChannelOrderItemId));
                    WalmartRefundRequestParam walmartRefundRequestParam = new WalmartRefundRequestParam();
                    WalmartRefundRequestParam.OrderRefundBean orderRefundBean = new WalmartRefundRequestParam.OrderRefundBean();
                    walmartRefundRequestParam.setOrderRefund(orderRefundBean);
                    List<WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean> orderLineBeanList = new ArrayList<>();
                    //todo 判断是否退全款。
                    Boolean isFullRefund = true;
                    for (OrderRefundItem item : orderRefundItems) {
                        if (!item.getAmount().equals(item.getRefundAmount())) { //金额和退款金额相等
                            isFullRefund = false;
                            break;
                        }
                    }
                    for (String key : map.keySet()) {
                        List<OrderRefundItem> orderRefundItemList = map.get(key);
                        orderRefundBean.setPurchaseOrderId(saleOrders.getOrderNo()); //订单号
                        WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean orderLinesBean = new WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean();
                        orderRefundBean.setOrderLines(orderLinesBean);
                        orderLinesBean.setOrderLine(orderLineBeanList);
                        WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean orderLineBean = new WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean();
                        orderLineBeanList.add(orderLineBean);
                        orderLineBean.setLineNumber(key);
                        //todo 是否退全款。
                        orderLineBean.setIsFullRefund(isFullRefund);
                        WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean refundsBean = new WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean();
                        orderLineBean.setRefunds(refundsBean);
                        WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean refundBean = new WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean();
                        List<WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean> refundBeanList = new ArrayList<>();
                        refundBeanList.add(refundBean);
                        refundsBean.setRefund(refundBeanList);
                        WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean refundChargesBean = new WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean();
                        refundBean.setRefundCharges(refundChargesBean);
                        List<WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean> refundChargeBeanList = new ArrayList<>();
                        refundChargesBean.setRefundCharge(refundChargeBeanList);

                        //todo 第一个
                        WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean refundChargeBean = new WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean();
                        refundChargeBeanList.add(refundChargeBean);
                        refundChargeBean.setRefundReason(refund.getReturnText());
                        WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean.ChargeBean charge = new WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean.ChargeBean();
                        refundChargeBean.setCharge(charge);

                        //todo 第二个
                        WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean refundChargeBeanTwo = new WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean();
                        refundChargeBeanList.add(refundChargeBeanTwo);
                        refundChargeBeanTwo.setRefundReason(refund.getReturnText());
                        WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean.ChargeBean chargeTwo = new WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean.ChargeBean();
                        refundChargeBeanTwo.setCharge(chargeTwo);
                        for (OrderRefundItem item : orderRefundItemList) {
                            if (new Integer(1).equals(item.getReturnItemType())) { //商品
                                charge.setChargeType("PRODUCT");
                                charge.setChargeName("Item Price");
                                WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean.ChargeBean.ChargeAmountBean chargeAmountBean = new WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean.ChargeBean.ChargeAmountBean();
                                chargeAmountBean.setCurrency(refund.getCurrency());
                                chargeAmountBean.setAmount(item.getRefundAmount().negate());
                                charge.setChargeAmount(chargeAmountBean);
                            }
                            if (new Integer(2).equals(item.getReturnItemType())) { // 商品税
                                WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean.ChargeBean.TaxBean tax = new WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean.ChargeBean.TaxBean();
                                charge.setTax(tax);
                                tax.setTaxName("ItemPriceTax");
                                WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean.ChargeBean.TaxBean.TaxAmountBean taxAmount = new WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean.ChargeBean.TaxBean.TaxAmountBean();
                                tax.setTaxAmount(taxAmount);
                                taxAmount.setCurrency(refund.getCurrency());
                                taxAmount.setAmount(item.getRefundAmount().negate());
                            }
                            if (new Integer(4).equals(item.getReturnItemType())) { //运费
                                chargeTwo.setChargeType("SHIPPING");
                                chargeTwo.setChargeName("Shipping");
                                WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean.ChargeBean.ChargeAmountBean chargeAmountBean = new WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean.ChargeBean.ChargeAmountBean();
                                chargeAmountBean.setCurrency(refund.getCurrency());
                                chargeAmountBean.setAmount(item.getRefundAmount().negate());
                                chargeTwo.setChargeAmount(chargeAmountBean);
                            }
                            if (new Integer(5).equals(item.getReturnItemType())) { // 运费税
                                WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean.ChargeBean.TaxBean tax = new WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean.ChargeBean.TaxBean();
                                chargeTwo.setTax(tax);
                                tax.setTaxName("ShippingTax");
                                WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean.ChargeBean.TaxBean.TaxAmountBean taxAmount = new WalmartRefundRequestParam.OrderRefundBean.OrderLinesBean.OrderLineBean.RefundsBean.RefundBean.RefundChargesBean.RefundChargeBean.ChargeBean.TaxBean.TaxAmountBean();
                                tax.setTaxAmount(taxAmount);
                                taxAmount.setCurrency(refund.getCurrency());
                                taxAmount.setAmount(item.getRefundAmount().negate());
                            }
                        }

                    }

                    // walmartRefundRequestParam;
                    Account account = accountService.getById(shopId);
                    log.error("walmart站内信站内退款参数{}，工单id:{}", JSONObject.toJSONString(walmartRefundRequestParam), ticketId);
                    JSONObject walmartRefundOrderLinesReturn = walmartApi.walmartRefundOrderLines(account, WalmartUrlEnum.WALMART_REFUND_ORDER_LINES.getValue(),saleOrders.getOrderNo(),JSONObject.toJSONString(walmartRefundRequestParam));
                    if (walmartRefundOrderLinesReturn != null) { //代表退款成功
                        //成功
                        Boolean refundResult = orderRefundService.refundFinish(sourceId, bankAccountId, bankAccountNumber, refundRemark);
                        Boolean ticketResult = updateOrderRefundApply(channel, orderId, reverseOrderId, sourceId, ticketId, OrderRefundStateEnum.COMPLETED.getDesc());
                        if (!refundResult || !ticketResult) {
                            log.info("退款失败,工单号:{}", ticketId);
                            throw new CommonException("退款失败,请联系管理员");
                        } else {
                            ScTicketLog ticketLog = new ScTicketLog();
                            ticketLog.settingDefaultCreate();
                            ticketLog.setTicketId(ticketId);
                            ticketLog.setOperateType("Update");
                            ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
                            ticketLog.setOperateTime(DateUtils.getNowDate());
                            ticketLog.setOperateContent("已提交退款");
                            scTicketLogService.insertScTicketLog(ticketLog);
                        }
                        return Boolean.TRUE;
                    } else {
                        log.error("工单id:{},逆向单号:{},walmart订单行退款失败返回值为空", ticketId, reverseOrderId);
                        //更新退款状态为退款失败，记录日志
                        refund.setRefundStatus(OrderRefundStateEnum.REFUND_FAIL.getCode());
                        refund.settingDefaultUpdate();
                        orderRefundService.updateById(refund);
                        ScTicketLog ticketLog = new ScTicketLog();
                        ticketLog.settingDefaultCreate();
                        ticketLog.setTicketId(ticketId);
                        ticketLog.setOperateType("Update");
                        ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
                        ticketLog.setOperateTime(DateUtils.getNowDate());
                        ticketLog.setOperateContent("退款失败");
                        scTicketLogService.insertScTicketLog(ticketLog);
                        return false;
                    }
                } catch (Exception e) {
                    log.error("调用tk站内退款失败：{}", e);
                    log.error("工单id:{},逆向单号:{},退款失败原因:{}", ticketId, reverseOrderId, e.getMessage());
                    //更新退款状态为退款失败，记录日志
                    OrderRefund refund = orderRefundService.getById(sourceId);
                    refund.setRefundStatus(OrderRefundStateEnum.REFUND_FAIL.getCode());
                    refund.settingDefaultUpdate();
                    orderRefundService.updateById(refund);
                    ScTicketLog ticketLog = new ScTicketLog();
                    ticketLog.settingDefaultCreate();
                    ticketLog.setTicketId(ticketId);
                    ticketLog.setOperateType("Update");
                    ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
                    ticketLog.setOperateTime(DateUtils.getNowDate());
                    ticketLog.setOperateContent("退款失败");
                    scTicketLogService.insertScTicketLog(ticketLog);
                    return false;
                }
            }
            //父工单是退货工单
            if (ScTicketConstant.TICKET_TYPE_RETURN.equals(parentTicketType)) {
                try {
                    OrderRefund refund = orderRefundService.getById(sourceId);
                    List<OrderRefundItem> orderRefundItems = orderRefundItemMapper.selectList(Wrappers.lambdaQuery(OrderRefundItem.class).eq(OrderRefundItem::getOrderRefundId, sourceId));

                    JSONObject refundJsonObejctParam = new JSONObject();
                    SaleOrders saleOrders = saleOrdersMapper.selectById(orderId);
                    refundJsonObejctParam.put("customerOrderId", saleOrders.getOrderNo());
                    JSONArray refundLines = new JSONArray();
                    refundJsonObejctParam.put("refundLines", refundLines);

                    Set<String> channelOrderItemIdSet = orderRefundItems.stream().map(i -> i.getChannelOrderItemId()).collect(Collectors.toSet());
                    for (String item : channelOrderItemIdSet) {
                        //todo 查询订单明细表
                        List<SaleOrderItemVO> saleOrderItemVOS = saleOrderItemsMapper.selectQuantityAndSkuIdByParam(item);
                        if (CollectionUtil.isEmpty(saleOrderItemVOS)) {
                            throw new CommonException("退款失败,请联系管理员:查询不到订单明细：" + item);
                        }
                        JSONObject refundLine = new JSONObject();
                        refundLines.add(refundLine);
                        refundLine.put("returnOrderLineNumber",new Integer(item));
                        JSONObject quantity = new JSONObject();
                        refundLine.put("quantity",quantity);
                        quantity.put("unitOfMeasure","EACH");
                        quantity.put("measurementValue",saleOrderItemVOS.get(0).getQuantity());
                    }
                    Account account = accountService.getById(shopId);
                    log.error("walmart退货站内退款参数{}，工单id:{}", refundJsonObejctParam.toJSONString(), ticketId);
                    JSONObject walmartReturnRefund = walmartApi.walmartReturnRefund(account, WalmartUrlEnum.WALMART_RETURNS_RETURNORDERID_REFUND.getValue(),reverseOrderId,JSONObject.toJSONString(refundJsonObejctParam));
                    if (walmartReturnRefund != null) { //代表退款成功
                        //成功
                        Boolean refundResult = orderRefundService.refundFinish(sourceId, bankAccountId, bankAccountNumber, refundRemark);
                        Boolean ticketResult = updateOrderRefundApply(channel, orderId, reverseOrderId, sourceId, ticketId, OrderRefundStateEnum.COMPLETED.getDesc());
                        if (!refundResult || !ticketResult) {
                            log.info("walmart退货站内退款失败,工单号:{}", ticketId);
                            throw new CommonException("walmart退货站内退款失败,请联系管理员");
                        } else {
                            ScTicketLog ticketLog = new ScTicketLog();
                            ticketLog.settingDefaultCreate();
                            ticketLog.setTicketId(ticketId);
                            ticketLog.setOperateType("Update");
                            ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
                            ticketLog.setOperateTime(DateUtils.getNowDate());
                            ticketLog.setOperateContent("已提交退款");
                            scTicketLogService.insertScTicketLog(ticketLog);
                        }
                        return Boolean.TRUE;
                    } else {
                        log.error("工单id:{},逆向单号:{},walmart退货站内退款失败返回值为空", ticketId, reverseOrderId);
                        //更新退款状态为退款失败，记录日志
                        refund.setRefundStatus(OrderRefundStateEnum.REFUND_FAIL.getCode());
                        refund.settingDefaultUpdate();
                        orderRefundService.updateById(refund);
                        ScTicketLog ticketLog = new ScTicketLog();
                        ticketLog.settingDefaultCreate();
                        ticketLog.setTicketId(ticketId);
                        ticketLog.setOperateType("Update");
                        ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
                        ticketLog.setOperateTime(DateUtils.getNowDate());
                        ticketLog.setOperateContent("退款失败");
                        scTicketLogService.insertScTicketLog(ticketLog);
                        return false;
                    }
                } catch (Exception e) {
                    log.error("调用walmart退货站内退款失败工单id:{},逆向单号:{},退款失败原因:{}", ticketId, reverseOrderId, e.getMessage());
                    //更新退款状态为退款失败，记录日志
                    OrderRefund refund = orderRefundService.getById(sourceId);
                    refund.setRefundStatus(OrderRefundStateEnum.REFUND_FAIL.getCode());
                    refund.settingDefaultUpdate();
                    orderRefundService.updateById(refund);
                    ScTicketLog ticketLog = new ScTicketLog();
                    ticketLog.settingDefaultCreate();
                    ticketLog.setTicketId(ticketId);
                    ticketLog.setOperateType("Update");
                    ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
                    ticketLog.setOperateTime(DateUtils.getNowDate());
                    ticketLog.setOperateContent("退款失败");
                    scTicketLogService.insertScTicketLog(ticketLog);
                    return false;
                }
            }
        } else {
            // 退款表增加 字段      bank_account_id  bank_account_number   bankAccountId
            Boolean refundResult = orderRefundService.refundFinish(sourceId, bankAccountId, bankAccountNumber, refundRemark);
            Boolean ticketResult = updateOrderRefundApply(channel, orderId, reverseOrderId, sourceId, ticketId, OrderRefundStateEnum.COMPLETED.getDesc());
            if (!refundResult || !ticketResult) {
                log.info("退款失败,工单号:{}", ticketId);
                throw new CommonException("退款失败,请联系管理员");
            } else {
                ScTicketLog ticketLog = new ScTicketLog();
                ticketLog.settingDefaultCreate();
                ticketLog.setTicketId(ticketId);
                ticketLog.setOperateType("Update");
                ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
                ticketLog.setOperateTime(DateUtils.getNowDate());
                ticketLog.setOperateContent("已提交退款");
                scTicketLogService.insertScTicketLog(ticketLog);
            }
        }

        return Boolean.TRUE;
    }


    /**
     * 创建 SC 工单
     *
     * @param channelOrderId 订单编号
     * @param refundType     退款类型
     * @param sourceId       源标识
     * @param reverseOrderId 反向顺序 ID
     * @param ticketId       家长票证编号
     * @param shopId
     * @param orderId        订单编号
     * @return
     */
    private Long createScTicket(String channelOrderId, Integer refundType, Long sourceId, String reverseOrderId, Long ticketId, String shopId, Long orderId) {
        ScTicket scTicket = new ScTicket();
        ScTicket ticket = scTicketService.selectScTicketById(ticketId);
        if (OrderRefundEnum.INTERNAL.getCodeType().equals(refundType)) {

            scTicket = scTicketService.createdAndInsertScTicket(channelOrderId, sourceId, ScTicketConstant.TICKET_TYPE_INSIDE_REFUND, ScTicketConstant.TICKET_SOURCE_REFUND, ticketId, OrderRefundEnum.INTERNAL.getCode(), null, "tiktok", null, orderId, AccountSaleChannelEnum.TIKTOK.getValue(), null, null, null);

        } else if (OrderRefundEnum.EXTERNAL.getCodeType().equals(refundType)) {
            scTicket = scTicketService.createdAndInsertScTicket(channelOrderId, sourceId, ScTicketConstant.TICKET_TYPE_OUTSIDE_REFUND, ScTicketConstant.TICKET_SOURCE_REFUND, ticketId, OrderRefundEnum.EXTERNAL.getCode(), null, null, shopId, orderId, ticket.getTicketSource(), null, null, null);
        } else if (OrderRefundEnum.SALES_RETURN_TO_INTERNAL.getCodeType().equals(refundType)) {
            scTicket = scTicketService.createdAndInsertScTicket(channelOrderId, sourceId, ScTicketConstant.TICKET_TYPE_INSIDE_REFUND, ScTicketConstant.TICKET_SOURCE_REFUND, ticketId, OrderRefundEnum.SALES_RETURN_TO_INTERNAL.getCode(), null, null, shopId, orderId, ticket.getTicketSource(), null, null, null);
        }
        scTicketService.insertScTicket(scTicket);
        return scTicket.getId();
    }


    @Override
    public List<String> getRejectReasonList(String channel, String shopId, String returnId) {
        if (StringUtils.isEmpty(returnId)) {
            return new ArrayList<>();
        }
        HashMap<String, Object> params = new HashMap<>();
//        String channelShopId = accountService.getBusShopIdAccountById(Long.valueOf(shopId));
        params.put("shopId", shopId);
        params.put("returnOrCancelId", returnId);
        AbstractRefundHandler invokeStrategy = factory.getInvokeStrategy(channel);

        return invokeStrategy.getRejectReasonList(params);
    }


    /**
     * 确认抖音
     *
     * @param channel 渠道
     * @param params  参数
     * @return {@link Object}
     */
    public Object confirmTiktok(String channel, Map params) {
        AbstractRefundHandler invokeStrategy = factory.getInvokeStrategy(channel);
        Object result = invokeStrategy.confirmReverseRequest(params);
        return result;
    }

    private OrderRefundVO getRefundRefundVo(Long sourceId, OrderRefundVO refundVO, ScTicket scTicket) {
        String orderId = scTicket.getOrderId().toString();
        OrderRefundRequestInfo info = orderRefundRequestInfoMapper.selectById(sourceId);
        refundVO.setCustomerName(info.getBuyName());//用户名称
        refundVO.setOrderId(info.getOrderId());
        refundVO.setRefundType(info.getRefundType()); //退款类型
        refundVO.setShopId(info.getShopId()); //店铺ID
        refundVO.setReverseRequestDate(info.getReverseRequestTime());
        refundVO.setReasonText(info.getReturnReasonText()); //退款原因
        refundVO.setAdditionalMessage(null); //退款客户备注，暂无取值
        refundVO.setOriginalCustomerComments(info.getOriginalCustomerComments()); //退货描述-原文
        LambdaQueryWrapper<OrderRefundRequestItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderRefundRequestItem::getReturnId,info.getReturnId());
        List<OrderRefundRequestItem> items = orderRefundRequestInfoItemMapper.selectList(queryWrapper);
        List<ReverseRecordDetailsVO> detailsVOList = new ArrayList<>();
        items.forEach(i->{
            ReverseRecordDetailsVO detailsVO = new ReverseRecordDetailsVO();
            detailsVO.setProductImages(i.getUrl()); //图片 （TK渠道）
            detailsVO.setSellerSku(i.getSellerSku());//sellersku
            detailsVO.setReturnProductName(i.getProductName()); //产品名称
            detailsVO.setSkuId(i.getErpSku()); //sku
            detailsVO.setReturnQuantity(i.getRefundQuantity()); //退款数量
            detailsVOList.add(detailsVO);
        });
        //退款总金额，（TK取自接口，temu取自订单行销售额）
        refundVO.setRefundTotal(items.stream().map(OrderRefundRequestItem::getRefundTotal).filter(Objects::nonNull).reduce(BigDecimal.ZERO,BigDecimal::add));
        refundVO.setRefundDetails(detailsVOList);
        return refundVO;
    }



    /**
     * 退货工单是否可以进行站内退款申请(或站外退款暂无)
     *
     * @param channel    渠道
     * @param orderId    订单编号
     * @param refundType 0 退款请求申请站内退款 1 站内信或其他工单申请站外退款 2 退货申请站内退款 3 退货申请站外退款
     * @param ticketId
     * <AUTHOR>
     * @return {@link Boolean}
     */
    @Override
//    @IsRefusal
    @ReturnIsRefusal
//    @IsEnoughRefund
    public Boolean ReturnIsApply(String channel, String orderId, String refundType, Long ticketId) {
        if (StringUtils.isEmpty(orderId)) {
            throw new ErpCommonException("无订单无法申请退款");
        }
        if (!verifyIsSubmitted(ticketId)) {
            throw new ErpCommonException("退款工单已提交或已退回请进入退款工单操作");
        }
        //退货工单申请站内或站外退款要进行逻辑判断tiktok渠道退款,是否有没有提交的子工单,如果有,就不允许再次提交
        if (OrderRefundEnum.SALES_RETURN_TO_INTERNAL.getCode().equals(refundType)) {
//            if (!AccountSaleChannelEnum.TIKTOK.getValue().equals(channel) && !AccountSaleChannelEnum.SC.getValue().equals(channel)) {
//                throw new ErpCommonException("站内退款仅支持tiktok渠道和sc渠道");
//            }
            if (AccountSaleChannelEnum.TIKTOK.getValue().equals(channel)) {
                List<ScTicket> finishTicket = scTicketMapper.selectScTicketFinishStateAByParentTicketIdAndTicketType(ticketId, new String[]{ScTicketConstant.TICKET_TYPE_INSIDE_REFUND}, new String[]{ScTicketConstant.TICKET_STATUS_FINISH});
                if (CollectionUtil.isNotEmpty(finishTicket)) {
                    throw new ErpCommonException("工单编号:" + finishTicket.get(0).getTicketNumber() + "的站内退款工单已经完成!");
                }
            }

//            List<ScTicket> processTicket = scTicketMapper.selectScTicketProcessStateAByParentTicketIdAndTicketType(ticketId, new String[]{ScTicketConstant.TICKET_TYPE_INSIDE_REFUND});
//            if (CollectionUtil.isNotEmpty(processTicket)) {
//                throw new ErpCommonException("尚有未结束的站内退款工单:" + processTicket.get(0).getTicketNumber() + "请先处理!");
//            }

        }
        if (OrderRefundEnum.SALES_RETURN_TO_OUT.getCode().equals(refundType)) {

            List<ScTicket> processTicket = scTicketMapper.selectScTicketProcessStateAByParentTicketIdAndTicketType(ticketId, new String[]{ScTicketConstant.TICKET_TYPE_OUTSIDE_REFUND});
            if (CollectionUtil.isNotEmpty(processTicket)) {
                throw new ErpCommonException("尚有未结束的站外退款工单:" + processTicket.get(0).getTicketNumber() + "请先处理!");
            }
        }

        return Boolean.TRUE;
    }


    /**
     * 获得站内退款站外退款弹窗所需信息
     *
     * @param orderId        订单id
     * @param reverseOrderId 逆向订单号
     * @param refundType     0 退款请求申请站内退款 1 站内信或其他站外信申请站外退款 2 退货申请站内退款 3 退货申请站外退款 4 站内信或其他站外信申请站内退款
     * @param ticketId       工单id
     * @param shopId         店铺id
     * @param sourceId       来源 id
     * @param channel
     * @return {@link OrderRefundReqVO}
     * <AUTHOR>
     */
    @Override
    public OrderRefundRequestVO getInstationRefundNewMsg(Long orderId, String reverseOrderId, Integer refundType, Long ticketId, Long shopId, Long sourceId, String channel) {
        OrderRefundRequestVO vo = new OrderRefundRequestVO();
            //如果有退款信息，那就是说明保存后，但是没有提交过，提交过后就会创建退款子工单，无法调用此方法
            //只会有一个进行中的退款信息（即有父工单id无工单id（即提交过后创建的退款子工单id））

        List<Integer> instation = Arrays.asList(OrderRefundEnum.INTERNAL.getCodeType(), OrderRefundEnum.SALES_RETURN_TO_INTERNAL.getCodeType(), OrderRefundEnum.AMZ_SITEMSG_INSIDE_REFUND.getCodeType());
        List<Integer> outside = Arrays.asList(OrderRefundEnum.EXTERNAL.getCodeType(), OrderRefundEnum.SALES_RETURN_TO_OUT.getCodeType());

        String returndType = "";
        if (instation.stream().anyMatch(item->item.equals(refundType))){
            returndType = "INSIDE_REFUND";
        }else if (outside.stream().anyMatch(item->item.equals(refundType))){
            returndType = "OUTSIDE_REFUND";
        }

        LambdaQueryWrapper<OrderRefund> lambdaQueryWrapper = new LambdaQueryWrapper<OrderRefund>().
                    eq(OrderRefund::getParentTicketId, ticketId).
                    eq(OrderRefund::getRefundType,returndType).
                    isNull(OrderRefund::getTicketId);
            OrderRefund one = orderRefundService.getOne(lambdaQueryWrapper);
            if (one != null) {
                vo = getRefundInfo(one, orderId, reverseOrderId, ticketId);
                vo.setChannel(channel);
            } else {
                //退款请求工单查询退款信息从退款请求主表及明细表查询
                if (OrderRefundEnum.INTERNAL.getCodeType().equals(refundType)) {
                    vo = getRefundRequestInsideRefundInfo(orderId, reverseOrderId, refundType, ticketId, shopId,channel);
                } else if (OrderRefundEnum.EXTERNAL.getCodeType().equals(refundType)) {
                    //站内信工单或其他站外信查询站外退款信息，如果已保存了退款信息就显示对应的退款信息，否则从订单表查询出对应数据进行显示
                    vo = getOtherTicketOutSideRefundInfo(orderId, reverseOrderId, refundType, ticketId, shopId,channel);
                } else if (OrderRefundEnum.SALES_RETURN_TO_INTERNAL.getCodeType().equals(refundType)
                        || OrderRefundEnum.SALES_RETURN_TO_OUT.getCodeType().equals(refundType)) {
                    //退货工单查询退款信息，如果已保存了退款信息就显示对应的退款信息，否则从tiktok退货表和明细表查询出对应数据进行显示
                    vo = getReturnRefundInfo(orderId, reverseOrderId, refundType, ticketId, shopId,channel);
                } else if (OrderRefundEnum.AMZ_SITEMSG_INSIDE_REFUND.getCodeType().equals(refundType)) {
                    //站内信或其他站外信申请站内退款
                    vo = getAmzSitemsgInSideRefundInfo(orderId, reverseOrderId, refundType, ticketId, shopId,channel);
                }
            }
            return vo;
    }

    /**
     * 退货工单获取站内退款或站外退款信息
     *
     * @param orderId
     * @param reverseOrderId
     * @param refundType
     * @param ticketId
     * @param shopId
     * @param channel
     * @return
     */
    private OrderRefundRequestVO getReturnRefundInfo(Long orderId, String reverseOrderId, Integer refundType, Long ticketId, Long shopId, String channel) {
        SaleOrders saleOrders = saleOrdersMapper.selectById(orderId);
        Account account = accountService.getById(shopId);
        List<SaleOrderItems> saleOrderItemsList = getSaleOrderItems(orderId);
        List<ScTicketRefundInfo> scTicketRefundInfos = scTicketRefundInfoMapper.selectList(Wrappers.lambdaQuery(ScTicketRefundInfo.class).eq(ScTicketRefundInfo::getTicketId, ticketId));
        List<Long> returnInfoIds = scTicketRefundInfos.stream().map(ScTicketRefundInfo::getReturnInfoId).collect(Collectors.toList());
        List<ReturnInfoEntity> returnInfoList = returnInfoMapper.selectReturnInfoListByIds(returnInfoIds);
        String[] orderSellerSku = saleOrderItemsList.stream().filter(q->StringUtils.isNotEmpty(q.getChannelOrderItemId())).map(q->(StringUtils.isEmpty(q.getSellerSku())  ? "无sellerSku" : q.getSellerSku()) + "|" + q.getChannelOrderItemId()).toArray(String[]::new);

        OrderRefundRequestVO vo = OrderRefundRequestVO.builder().orderNo(saleOrders.getOrderNo()).orderRefundSellerSku(orderSellerSku).orderSellerSku(orderSellerSku)
                .refundWay(null).customerAccount(null).reverseRequestDate(DateUtil.transLocalDateTimeToDate(returnInfoList.get(0).getReturnDate())).returnText(null).currency(returnInfoList.get(0).getCurrency())
                .remark(null).meta(null).shopId(shopId.intValue()).accountFlag(account.getFlag()).ticketId(ticketId)
                .orderId(orderId).reverseOrderId(reverseOrderId).organizationId(saleOrders.getOrgId()).refundType(OrderRefundEnum.SALES_RETURN_TO_INTERNAL.getCodeType().equals(refundType) ? ScTicketConstant.TICKET_TYPE_INSIDE_REFUND : ScTicketConstant.TICKET_TYPE_OUTSIDE_REFUND)
                .channel(channel)
                .build();

        setRefundDetail(saleOrderItemsList, vo);
        return vo;
    }

    /**
     * 站内信等其他工单获取站外退款
     *
     * @param orderId        订单id
     * @param reverseOrderId 逆向订单号
     * @param refundType
     * @param ticketId       工单id
     * @param shopId         店铺id
     * @param channel
     * @return
     */
    private OrderRefundRequestVO getOtherTicketOutSideRefundInfo(Long orderId, String reverseOrderId, Integer refundType, Long ticketId, Long shopId, String channel) {
        SaleOrders saleOrders = saleOrdersMapper.selectById(orderId);
        Account account = accountService.getById(shopId);
        List<SaleOrderItems> saleOrderItemsList = getSaleOrderItems(orderId);

        String[] orderSellerSku = saleOrderItemsList.stream().filter(q->StringUtils.isNotEmpty(q.getChannelOrderItemId())).map(q->(StringUtils.isEmpty(q.getSellerSku())  ? "无sellerSku" : q.getSellerSku()) + "|" + q.getChannelOrderItemId()).toArray(String[]::new);

        OrderRefundRequestVO vo = OrderRefundRequestVO.builder().orderNo(saleOrders.getOrderNo()).orderRefundSellerSku(orderSellerSku).orderSellerSku(orderSellerSku)
                .refundWay(null).customerAccount(null).reverseRequestDate(DateUtils.getNowDate()).returnText(null).currency(null)
                .remark(null).meta(null).shopId(shopId.intValue()).accountFlag(account.getFlag()).ticketId(ticketId)
                .orderId(orderId).reverseOrderId(reverseOrderId).organizationId(saleOrders.getOrgId()).refundType(ScTicketConstant.TICKET_TYPE_OUTSIDE_REFUND)
                .channel(channel)
                .build();

        setRefundDetail(saleOrderItemsList, vo);
        return vo;
    }

    /**
     * @Description: 站内信或其他站外信站内退款
     * @Author: wly
     * @Date: 2024/5/20 15:26
     * @Params: [orderId, reverseOrderId, refundType, ticketId, shopId, channel]
     * @Return: com.bizark.op.api.vo.refund.OrderRefundRequestVO
     **/
    private OrderRefundRequestVO getAmzSitemsgInSideRefundInfo(Long orderId, String reverseOrderId, Integer refundType, Long ticketId, Long shopId, String channel) {

        //tk站内信，站内退款。
        ScTicket ticketByTicketId = scTicketMapper.getTicketByTicketId(ticketId);
        Map<String, Boolean> resultMap = new HashMap<>();
        Map<String,String> resultReasonMap = new HashMap<>();
        SaleOrders saleOrders = saleOrdersMapper.selectById(orderId);
        if(ticketByTicketId != null && AccountSaleChannelEnum.TIKTOK.getValue().equals(channel) && ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG.equals(ticketByTicketId.getTicketType())) {
            //todo tk站内信过来的站内退款 去调用tk的校验接口筛选可以退货的订单明细。
            JSONObject resultJsonObject = tikTokUtil.getTikTokShopReturnV2(new JSONObject(), TikTokApiEnums.TIKTOK_AFTERSALE_ELIGIBILITY.getUrl(), TikTokApiEnums.TIKTOK_AFTERSALE_ELIGIBILITY.getPath().replace("{order_id}", saleOrders.getOrderNo().toString()), JSONObject.class, String.valueOf(ticketByTicketId.getShopId()));
            if (resultJsonObject != null) {
                log.error("tk站内信，站内退款校验是否可以退款接口响应:{},订单id{}",resultJsonObject.toJSONString(),orderId);
                JSONObject data = resultJsonObject.getJSONObject("data");
                JSONArray skuEligibilityArray = data.getJSONArray("sku_eligibility");
                for (Object skuEligibility : skuEligibilityArray) {
                    JSONArray lineItemEligibilityArray = ((JSONObject) skuEligibility).getJSONArray("line_item_eligibility");
                    for(Object item:lineItemEligibilityArray) {
                        if("REFUND".equals(((JSONObject) item).getString("request_type"))) {
                            Boolean eligible = ((JSONObject) item).getBoolean("eligible");
                            String ineligibleReason = ((JSONObject) item).getString("ineligible_reason");
                            JSONArray orderLineItemsIdsArray = ((JSONObject) item).getJSONArray("order_line_items_ids");
                            String itemsId = null;
                            for (Object orderLineItemsId : orderLineItemsIdsArray) {
                                itemsId = ((String) orderLineItemsId);
                                resultMap.put(itemsId, eligible);
                                resultReasonMap.put(itemsId,ineligibleReason);
                            }
                        }
                    }
                }
            } else {
                throw new ErpCommonException("tk站内信站内退款调用校验是否可以退款接口失败");
            }
            //todo 这里进行判断 是否全是false
            boolean allFalse = resultMap.values().stream().allMatch(value -> value.equals(false));
            //todo 如果全是false 判断原因是否是这个 there are processing reverse order exists
//            则查询当前是否有退货或者退款请求的工单（只会存在一种进行中的），
//            如果存在则直接在新标签页打开工单处理页面，
//            如果不存在则报错提示“此客户已发起正式的退款请求，无法直接操作退款”，同时给到退货和退款请求那边去拉取数据。
            if(allFalse) {
                if(resultReasonMap.containsValue("there are processing reverse order exists")) {
                    //todo 查询退货工单 查工单中的订单号
                    ScTicket queryScTicket = new ScTicket();
                    queryScTicket.setOrganizationId(saleOrders.getOrgId());
                    queryScTicket.setTicketType(ScTicketConstant.TICKET_TYPE_RETURN);
                    queryScTicket.setAmazonOrderId(saleOrders.getOrderNo());
                    queryScTicket.setTicketSource(ScTicketConstant.TICKET_SOURCE_TIKTOK);
                    List<ScTicket> returnScTicket = scTicketMapper.queryScTicketByParam(queryScTicket);
                    if(CollectionUtil.isNotEmpty(returnScTicket)) {
                        OrderRefundRequestVO vo = new OrderRefundRequestVO();
                        vo.setScTicket(returnScTicket.get(0));
                        return vo;
                    }

                    //todo 查询退款请求 通过工单中的订单号
                    queryScTicket.setTicketType(ScTicketConstant.TICKET_TYPE_REFUND_REQUEST);
                    List<ScTicket> refundRequestScTicket = scTicketMapper.queryScTicketByParam(queryScTicket);
                    if(CollectionUtil.isNotEmpty(refundRequestScTicket)) {
                        OrderRefundRequestVO vo = new OrderRefundRequestVO();
                        vo.setScTicket(refundRequestScTicket.get(0));
                        return vo;
                    }
                    try{
                        CompletableFuture<Void> f1 = CompletableFuture.runAsync(() -> {
                         orderRefundRequestInfoService.manualRequestTkReturnRefundInfo(orderId,saleOrders.getOrderNo());
                        }, taskExecutor);
                    } catch (Exception e) {
                        log.error("tk站内信站内退款调用拉取接口退货和退款请求那边去拉取数据失败。");
                    }
                    throw new ErpCommonException("此客户已发起正式的退款请求，无法直接操作退款");
                } else {
                    throw new ErpCommonException("当前订单状态不支持退款");
                }
            }
        }
        Account account = accountService.getById(shopId);
        List<SaleOrderItems> saleOrderItemsList = getSaleOrderItems(orderId);
        //todo tk站内信过来的站内退款 去调用tk的校验接口看是否可以申请站内退款。
        if(ticketByTicketId != null && AccountSaleChannelEnum.TIKTOK.getValue().equals(channel) && ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG.equals(ticketByTicketId.getTicketType())) {
            String[] orderSellerSku = saleOrderItemsList.stream()
                    .filter(q -> StringUtils.isNotEmpty(q.getSellerSku()) && StringUtils.isNotEmpty(q.getChannelOrderItemId()))
                    .filter(i -> {
                        if(StringUtils.isNotEmpty(i.getChannelOrderItemId())) {
                            return resultMap.get(i.getChannelOrderItemId()) == null ? false : resultMap.get(i.getChannelOrderItemId());
                        } else {
                            return false;
                        }
                    })
                    .map(q -> q.getSellerSku() + "|" + q.getChannelOrderItemId()).toArray(String[]::new);

            OrderRefundRequestVO vo = OrderRefundRequestVO.builder().orderNo(saleOrders.getOrderNo()).orderRefundSellerSku(orderSellerSku).orderSellerSku(orderSellerSku)
                    .refundWay(null).customerAccount(null).reverseRequestDate(DateUtils.getNowDate()).returnText(null).currency(null)
                    .remark(null).meta(null).shopId(shopId.intValue()).accountFlag(account.getFlag()).ticketId(ticketId)
                    .orderId(orderId).reverseOrderId(reverseOrderId).organizationId(saleOrders.getOrgId()).refundType(ScTicketConstant.TICKET_TYPE_INSIDE_REFUND)
                    .channel(channel)
                    .ticketType(ticketByTicketId.getTicketType())
                    .build();

            //todo 过滤调不能申请退款的
            saleOrderItemsList = saleOrderItemsList.stream().filter(i -> {
                if(StringUtils.isNotEmpty(i.getChannelOrderItemId())) {
                    return resultMap.get(i.getChannelOrderItemId()) == null ? false : resultMap.get(i.getChannelOrderItemId());
                } else {
                    return false;
                }
            }).collect(Collectors.toList());
            setRefundDetail(saleOrderItemsList, vo);
            return vo;
        }else {
            String[] orderSellerSku = saleOrderItemsList.stream().filter(q -> StringUtils.isNotEmpty(q.getSellerSku()) && StringUtils.isNotEmpty(q.getChannelOrderItemId())).map(q -> q.getSellerSku() + "|" + q.getChannelOrderItemId()).toArray(String[]::new);

            OrderRefundRequestVO vo = OrderRefundRequestVO.builder().orderNo(saleOrders.getOrderNo()).orderRefundSellerSku(orderSellerSku).orderSellerSku(orderSellerSku)
                    .refundWay(null).customerAccount(null).reverseRequestDate(DateUtils.getNowDate()).returnText(null).currency(null)
                    .remark(null).meta(null).shopId(shopId.intValue()).accountFlag(account.getFlag()).ticketId(ticketId)
                    .orderId(orderId).reverseOrderId(reverseOrderId).organizationId(saleOrders.getOrgId()).refundType(ScTicketConstant.TICKET_TYPE_INSIDE_REFUND)
                    .channel(channel)
                    .ticketType(ticketByTicketId.getTicketType())
                    .build();

            setRefundDetail(saleOrderItemsList, vo);
            return vo;
        }
    }

    /**
     * 获取退款及退款明细（父工单已有对应退款信息）
     * @param one 退款主表信息
     * @param orderId 订单id
     * @param reverseOrderId 逆向订单号
     * @param ticketId 工单id(如果是在父工单操作就是父工单id,否则就是退款工单id)
     * @return
     */
    private OrderRefundRequestVO getRefundInfo(OrderRefund one, Long orderId, String reverseOrderId,  Long ticketId) {

        SaleOrders saleOrders = orderId == null ? new SaleOrders() : saleOrdersMapper.selectById(orderId);
        List<SaleOrderItems> saleOrderItemsList = orderId == null ? new ArrayList<>() : getSaleOrderItems(orderId);
        List<OrderRefundItem> refundItems = orderRefundItemMapper.selectList(new LambdaQueryWrapper<OrderRefundItem>().eq(OrderRefundItem::getOrderRefundId, one.getId()));
        String meta = refundManger.getAttachment(one.getId(), ScTicketConstant.TICKET_SOURCE_REFUND);
        List<OrderRefundItem> hasRefund = new ArrayList<>();
        //非tiktok渠道或者非站内退款 可能会有多笔退款，只有(tiktok站内退款才能退一笔)已申请退款取父工单下已完成的所有退款工单的已申请退款，退款金额为金额减去已申请退款
        if (!ScTicketConstant.TICKET_SOURCE_TIKTOK.equals(one.getChannel()) || !ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(one.getRefundType())) {
            //此处不能用ticketId,ticketId为当前工单的id
            if (one.getParentTicketId() != null) {
                hasRefund = orderRefundItemMapper.selectOrderRefundItemList(one.getParentTicketId(), OrderRefundStateEnum.COMPLETED.getCode());
            }
        }
        //tk渠道的
        ScTicket ticketByTicketId = scTicketMapper.getTicketByTicketId(ticketId);
        if(ticketByTicketId != null) {
            ScTicket parnetTicket = scTicketMapper.getTicketByTicketId(ticketByTicketId.getId());
            //如果是tk渠道并且是站内信
            if (parnetTicket != null && ScTicketConstant.TICKET_SOURCE_TIKTOK.equals(one.getChannel()) && ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG.equals(parnetTicket.getTicketType())) {
                hasRefund = orderRefundItemMapper.selectOrderRefundItemList(one.getParentTicketId(), OrderRefundStateEnum.COMPLETED.getCode());
            }
        }
        boolean hasRefundFlag = CollectionUtil.isNotEmpty(hasRefund);


        //列转行要去重
        String[] refundSellerSku = refundItems.stream().filter(q->StringUtils.isNotEmpty(q.getChannelOrderItemId())).map(q->(StringUtils.isEmpty(q.getSellerSku())  ? "无sellerSku" : q.getSellerSku()) + "|" + q.getChannelOrderItemId()).distinct().toArray(String[]::new);
        String[] orderSellerSku = CollectionUtil.isEmpty(saleOrderItemsList) ? refundSellerSku :(saleOrderItemsList.stream().filter(q->StringUtils.isNotEmpty(q.getChannelOrderItemId())).map(q->(StringUtils.isEmpty(q.getSellerSku())  ? "无sellerSku" : q.getSellerSku()) + "|" + q.getChannelOrderItemId()).toArray(String[]::new));
        OrderRefundRequestVO vo = OrderRefundRequestVO.builder().orderRefundId(one.getId()).orderNo(saleOrders == null ? null : saleOrders.getOrderNo()).orderSellerSku(orderSellerSku).orderRefundSellerSku(refundSellerSku)
                .refundWay(one.getRefundWay()).customerAccount(one.getAccountNumber()).reverseRequestDate(one.getReverseRequestDate()).returnText(one.getReturnText()).currency(one.getCurrency())
                .remark(one.getRemark()).meta(meta).shopId(one.getAccountId()).accountFlag(one.getAccountFlag()).ticketId(ticketId)
                .orderId(StringUtils.isEmpty(one.getOrderId()) ? null : Long.valueOf(one.getOrderId())).reverseOrderId(reverseOrderId).organizationId(one.getOrganizationId()).refundType(one.getRefundType()).refundAccount(one.getBankAccountNumber())
                .returnStatus(OrderRefundStateEnum.getDesc(one.getRefundStatus()) != null ? OrderRefundStateEnum.getDesc(one.getRefundStatus()).getDesc() : null)
                .refundClass(one.getRefundClass())
                .treatmentState(one.getTreatmentState())
                .refundRemark(one.getRefundRemark())
                .build();

        List<OrderRefundRequestVO.RefundDetailListVO> detailList = new ArrayList<>();
        Map<String, List<OrderRefundItem>> listMap = refundItems.stream().collect(Collectors.groupingBy(w->(StringUtils.isEmpty(w.getSellerSku())  ? "无sellerSku" : w.getSellerSku()) + "|" + w.getChannelOrderItemId()));
        List<OrderRefundItem> finalHasRefund = hasRefund;
        listMap.forEach((sellerSkuAndItemId,list)->{
            String[] split = sellerSkuAndItemId.split("\\|");
            String sellerSku = split[0];
            String channelOrderItemId = split[1];
            //单个sellerSku 金额，已申请退款，退款金额
            OrderRefundRequestVO.RefundDetailListVO detail = new OrderRefundRequestVO.RefundDetailListVO();
            detail.setRefundOrderId(one.getId());
            detail.setSellerSku(sellerSku);

            List<OrderRefundRequestVO.RefundDetailFeeVO> feeVOList = new ArrayList<>();
            for (OrderRefundItem orderRefundItem : list) {
                OrderRefundRequestVO.RefundDetailFeeVO feeVO = BeanCopyUtils.copyBean(orderRefundItem, OrderRefundRequestVO.RefundDetailFeeVO.class);
                feeVO.setOrderRefundItemId(orderRefundItem.getId());
                OrderRefundFeeTypeEnum code = OrderRefundFeeTypeEnum.getCode(orderRefundItem.getReturnItemType());
                feeVO.setReturnItem(code != null ? code.getCode() : null);
                if (hasRefundFlag) {
                    List<OrderRefundItem> collect = finalHasRefund.stream().filter(s -> s.getReturnItemType().equals(feeVO.getReturnItemType()) && s.getChannelOrderItemId().equals(feeVO.getChannelOrderItemId())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(collect)) {
                        BigDecimal applyRefund = collect.stream().map(OrderRefundItem::getApplyRefund).reduce(BigDecimal.ZERO, BigDecimal::add);
                        feeVO.setApplyRefund(applyRefund);
                        //因为已经有了此退款信息，所以取退款明细对应的退款金额用于回显
//                        feeVO.setRefundAmount(feeVO.getAmount().subtract(feeVO.getApplyRefund()));
                    }
                }
                feeVOList.add(feeVO);
            }

            BigDecimal oneSellerSkuTotalAmount = feeVOList.stream().map(OrderRefundRequestVO.RefundDetailFeeVO::getAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal oneSellerSkuTotalApplyRefund = feeVOList.stream().map(OrderRefundRequestVO.RefundDetailFeeVO::getApplyRefund).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal oneSellerSkuTotalRefundAmount = feeVOList.stream().map(OrderRefundRequestVO.RefundDetailFeeVO::getRefundAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            detail.setAmount(oneSellerSkuTotalAmount);
            detail.setApplyRefund(oneSellerSkuTotalApplyRefund);
            detail.setRefundAmount(oneSellerSkuTotalRefundAmount);
            detail.setRefundDetailFeeVOList(feeVOList);
            detailList.add(detail);
        });
        vo.setRefundDetailList(detailList);
        return vo;
    }

    private List<SaleOrderItems> getSaleOrderItems(Long orderId) {
        List<SaleOrderItems> saleOrderItemsList = saleOrderItemsMapper.selectList(new LambdaQueryWrapper<SaleOrderItems>()
                .select(SaleOrderItems::getSellerSku,
                        SaleOrderItems::getItemAmount,
                        SaleOrderItems::getTaxPrice,
                        SaleOrderItems::getPromotionDiscountPrice,
                        SaleOrderItems::getShippingPrice,
                        SaleOrderItems::getShippingTaxPrice,
                        SaleOrderItems::getChannelOrderItemId)
                .eq(SaleOrderItems::getHeadId, orderId).eq(SaleOrderItems::getDisabledAt,0));
        return saleOrderItemsList;
    }


    /**
     * 退款请求获取站内退款信息
     *
     * @param orderId        订单id
     * @param reverseOrderId 逆向订单号
     * @param refundType
     * @param ticketId       工单id
     * @param shopId         店铺id
     * @param channel
     * @return
     */
    private OrderRefundRequestVO getRefundRequestInsideRefundInfo(Long orderId, String reverseOrderId, Integer refundType, Long ticketId, Long shopId, String channel) {
        SaleOrders saleOrders = saleOrdersMapper.selectById(orderId);
        Account account = accountService.getById(shopId);
        List<SaleOrderItems> saleOrderItemsList = getSaleOrderItems(orderId);
        OrderRefundRequestInfo orderRefundRequestInfo = orderRefundRequestInfoMapper.selectOne(new LambdaQueryWrapper<OrderRefundRequestInfo>().eq(OrderRefundRequestInfo::getReturnId, reverseOrderId));
        List<OrderRefundRequestItem> orderRefundRequestItemList = orderRefundRequestInfoItemMapper.selectList(new LambdaQueryWrapper<OrderRefundRequestItem>().eq(OrderRefundRequestItem::getReturnId, reverseOrderId));
        String currency = orderRefundRequestItemList.get(0).getCurrency();
        String[] orderSellerSku = saleOrderItemsList.stream().filter(q->StringUtils.isNotEmpty(q.getChannelOrderItemId())).map(q->(StringUtils.isEmpty(q.getSellerSku())  ? "无sellerSku" : q.getSellerSku()) + "|" + q.getChannelOrderItemId()).toArray(String[]::new);

        OrderRefundRequestVO vo = OrderRefundRequestVO.builder().orderNo(saleOrders.getOrderNo()).orderRefundSellerSku(orderSellerSku).orderSellerSku(orderSellerSku)
                .refundWay(null).customerAccount(null).reverseRequestDate(orderRefundRequestInfo.getReverseRequestTime()).returnText(null).currency(currency)
                .remark(null).meta(null).shopId(shopId.intValue()).accountFlag(account.getFlag()).ticketId(ticketId)
                .orderId(orderId).reverseOrderId(reverseOrderId).organizationId(orderRefundRequestInfo.getOrganizationId()).refundType(ScTicketConstant.TICKET_TYPE_INSIDE_REFUND).channel(channel)
                .build();

        setRefundDetail(saleOrderItemsList, vo);
        return vo;

    }

    private  void setRefundDetail(List<SaleOrderItems> saleOrderItemsList, OrderRefundRequestVO vo) {
        List<OrderRefundRequestVO.RefundDetailListVO> detailList = new ArrayList<>();

        List<OrderRefundItem> itemList = new ArrayList<>();
        //非tiktok渠道或者非站内退款可能会有多次退款
        if (!ScTicketConstant.TICKET_SOURCE_TIKTOK.equals(vo.getChannel()) || !ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(vo.getRefundType())) {
            itemList = orderRefundItemMapper.selectOrderRefundItemList(vo.getTicketId(), OrderRefundStateEnum.COMPLETED.getCode());
        }
        //如果是tk渠道并且是站内信
        if(ScTicketConstant.TICKET_SOURCE_TIKTOK.equals(vo.getChannel()) && ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG.equals(vo.getTicketType())) {
            itemList = orderRefundItemMapper.selectOrderRefundItemList(vo.getTicketId(), OrderRefundStateEnum.COMPLETED.getCode());
        }
        boolean hasRefund = CollectionUtil.isNotEmpty(itemList);

//        Map<String, List<SaleOrderItems>> listMap = saleOrderItemsList.stream().collect(Collectors.groupingBy(SaleOrderItems::getSellerSku));
        List<OrderRefundItem> finalItemList = itemList;
        saleOrderItemsList.forEach(t->{

            OrderRefundRequestVO.RefundDetailListVO detail = new OrderRefundRequestVO.RefundDetailListVO();

            detail.setSellerSku(StringUtils.isEmpty(t.getSellerSku()) ? "无sellerSku" : t.getSellerSku());

            List<OrderRefundRequestVO.RefundDetailFeeVO> feeVOList = new ArrayList<>();
            for (int i = 1; i < OrderRefundFeeTypeEnum.values().length + 1; i++) {
                 if(i == 3) {
                     continue;
                 }
                //walmart 渠道的站内信 退款项只显示 商品、商品税、运费、运费税 4个项目。
                if(ScTicketConstant.TICKET_SOURCE_WALMART.equals(vo.getChannel()) && ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG.equals(vo.getTicketType())) {
                    if(i != 1 && i != 2 && i != 4 && i != 5 ) {
                        continue;
                    }
                }
                OrderRefundRequestVO.RefundDetailFeeVO feeVO = new OrderRefundRequestVO.RefundDetailFeeVO();
                feeVO.setReturnItemType(i);
                feeVO.setReturnItem(OrderRefundFeeTypeEnum.getCode(i).getCode());
                feeVO.setSellerSku(StringUtils.isEmpty(t.getSellerSku()) ? "无sellerSku" : t.getSellerSku());
                feeVO.setChannelOrderItemId(t.getChannelOrderItemId());
                //商品
                if (i == 1) {
                    feeVO.setAmount(t.getItemAmount() != null ? t.getItemAmount() : BigDecimal.ZERO);
                    //商品税
                } else if (i == 2) {
                    feeVO.setAmount(t.getTaxPrice() != null ? t.getTaxPrice() : BigDecimal.ZERO);
                    //商品折扣
                } else if (i == 3) {
                    feeVO.setAmount(t.getPromotionDiscountPrice() != null ? t.getPromotionDiscountPrice() : BigDecimal.ZERO);
                    //运费
                } else if (i == 4) {
                    feeVO.setAmount(t.getShippingPrice() != null ? t.getShippingPrice() : BigDecimal.ZERO);
                    //运费税
                } else if (i == 5) {
                    feeVO.setAmount(t.getShippingTaxPrice() != null ? t.getShippingTaxPrice() : BigDecimal.ZERO);
                    //手续费
                } else if (i == 6){
                    feeVO.setAmount(BigDecimal.ZERO);
                    //其他
                }else {
                    feeVO.setAmount(BigDecimal.ZERO);
                }
                //如果父工单下有其他已完成的退款工单，则已申请退款金额为其他完成退款工单的合计，回显给前端的退款金额为金额减去已申请退款金额
                if (hasRefund) {
                    List<OrderRefundItem> collect = finalItemList.stream().filter(s -> s.getReturnItemType().equals(feeVO.getReturnItemType()) && s.getChannelOrderItemId().equals(feeVO.getChannelOrderItemId())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(collect)) {
                        feeVO.setApplyRefund(collect.stream().map(OrderRefundItem::getApplyRefund).reduce(BigDecimal.ZERO, BigDecimal::add));
                        feeVO.setRefundAmount(feeVO.getAmount().subtract(feeVO.getApplyRefund()));
                    }
                } else {
                    feeVO.setApplyRefund(BigDecimal.ZERO);
                    feeVO.setRefundAmount(feeVO.getAmount());
                }

                feeVOList.add(feeVO);
            }

            //金额，已申请退款，退款金额
            BigDecimal oneSellerSkuTotalAmount = feeVOList.stream().map(OrderRefundRequestVO.RefundDetailFeeVO::getAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal oneSellerSkuTotalApplyRefund = feeVOList.stream().map(OrderRefundRequestVO.RefundDetailFeeVO::getApplyRefund).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);;
            BigDecimal oneSellerSkuRefundAmount = feeVOList.stream().map(OrderRefundRequestVO.RefundDetailFeeVO::getRefundAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);;
            detail.setAmount(oneSellerSkuTotalAmount);
            detail.setApplyRefund(oneSellerSkuTotalApplyRefund);
            detail.setRefundAmount(oneSellerSkuRefundAmount);
            detail.setRefundDetailFeeVOList(feeVOList);
            detailList.add(detail);
        });
        vo.setRefundDetailList(detailList);
    }


    @Override
//    @ReturnIsRefusal
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveInstationRefundNewMsg(OrderRefundRequestVO vo, Long contextId) {
        verifyAmount(vo);
        saveAndSubmittedVerify(vo.getTicketId());
        OrderRefund orderRefund = getOrderRefundInfo(vo);
        orderRefund.setParentTicketId(vo.getTicketId());
        orderRefund.setTreatmentState("SAVED");
        Long orderRefundId = vo.getOrderRefundId();
        int result = 0;
        if (orderRefundId != null) {
            //提交后禁止保存
            if ("SUBMITTED".equals(orderRefundMapper.selectById(orderRefundId).getTreatmentState())) {
                throw new ErpCommonException("退款信息已经提交!");
            }
            orderRefund.setId(orderRefundId);
            orderRefund.setRefundStatus(OrderRefundStateEnum.NO_APPLY.getCode());
            orderRefund.settingDefaultUpdate();
            result = orderRefundMapper.updateById(orderRefund);
        } else {
            orderRefund.setRefundStatus(OrderRefundStateEnum.NO_APPLY.getCode());
            orderRefund.settingDefaultCreate();
            result= orderRefundMapper.insert(orderRefund);
        }
        List<OrderRefundRequestVO.RefundDetailListVO> refundDetailList = vo.getRefundDetailList();
        for (OrderRefundRequestVO.RefundDetailListVO refundDetailListVO : refundDetailList) {
            List<OrderRefundRequestVO.RefundDetailFeeVO> refundDetailFeeVOList = refundDetailListVO.getRefundDetailFeeVOList();
            for (OrderRefundRequestVO.RefundDetailFeeVO refundDetailFeeVO : refundDetailFeeVOList) {
                OrderRefundItem orderRefundItem = BeanCopyUtils.copyBean(refundDetailFeeVO, OrderRefundItem.class);
                orderRefundItem.setOrderRefundId(orderRefund.getId());
                orderRefundItem.setOrganizationId(vo.getOrganizationId());
                if ("无sellerSku".equalsIgnoreCase(orderRefundItem.getSellerSku())) {
                    orderRefundItem.setSellerSku(null);
                }
                if (refundDetailFeeVO.getOrderRefundItemId() != null) {
                    orderRefundItem.setId(refundDetailFeeVO.getOrderRefundItemId());
                    orderRefundItemMapper.updateById(orderRefundItem);
                } else {
                    orderRefundItemMapper.insert(orderRefundItem);
                }
            }
        }
        //保存附件
        refundManger.saveMeta(vo.getMeta(),ScTicketConstant.TICKET_SOURCE_REFUND,orderRefund.getId(),vo.getOrganizationId());
        return result > 0;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitRefundRequestNewMsg(OrderRefundRequestVO vo, Long ticketId, Long contextId) {
        verifyAmount(vo);
        saveAndSubmittedVerify(ticketId);
        if (vo.getOrderRefundId() == null) {
            throw new ErpCommonException("请保存退款信息后提交");
        }
        OrderRefund orderRefund = getOrderRefund(vo);
        //更新申请人申请时间
        OrderRefund orderRefund1 = new OrderRefund();
        orderRefund1.setId(orderRefund.getId());
        orderRefund1.setApplyBy(AuthContextHolder.getAuthUserDetails().getName());
        orderRefund1.setApplyTime(DateUtils.getNowDate());
        orderRefundMapper.updateById(orderRefund1);
        ScTicket ticket = scTicketService.selectScTicketBySourceType(vo.getOrderRefundId(), orderRefund.getRefundType());
        ScTicket scTicket = new ScTicket();
        if (ticket == null) {
            scTicket.setOrganizationId(contextId);
            scTicket.setSourceDocument(ScTicketConstant.TICKET_SOURCE_REFUND);
            scTicket.setTicketName("客户申请订单号:" + vo.getOrderNo() + "退款");
            scTicket.setTicketNumber(getChildTicketNumber(ticketId,orderRefund.getRefundType()));
            scTicket.setTicketStatus(ScTicketConstant.TICKET_STATUS_NEW);
            scTicket.setTicketSource(vo.getChannel());
            scTicket.setSourceId(vo.getOrderRefundId());
            Account account = accountService.getById(vo.getShopId());
            scTicket.setTicketType(orderRefund.getRefundType());
            scTicket.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH);
            scTicket.setShopId(vo.getShopId().longValue());
            scTicket.setAmazonOrderId(vo.getOrderNo());
            scTicket.setOrderId(vo.getOrderId());
            scTicket.setReverseOrderId(vo.getReverseOrderId());
            scTicket.setParentTicketId(vo.getTicketId());
            String customerName = saleOrdersMapper.getCustomerNameByOrderId(String.valueOf(vo.getOrderId()));
            scTicket.setCustomerName(customerName);
            scTicket.setRemark("店铺名称:" + account.getTitle() + " 订单号:" + vo.getOrderNo() + " 买家名称:" + customerName);
            scTicket.setBusinessApplyTime(DateUtils.getNowDate());
            int data = scTicketService.insertScTicket(scTicket);
            try {
                //todo 跟新returnInfo
                ScTicket parentTicket = scTicketMapper.getTicketByTicketId(ticketId);
                if (parentTicket != null && ScTicketConstant.TICKET_TYPE_REFUND_REQUEST.equals(parentTicket.getTicketType())) {
                    OrderRefundRequestInfo orderRefundRequestInfo = new OrderRefundRequestInfo();
                    orderRefundRequestInfo.setId(parentTicket.getSourceId());
                    orderRefundRequestInfo.setUpdatedAt(DateUtils.getNowDate());
                    orderRefundRequestInfo.setRefundStatus(RefundStateEnum.REFUNDING.getCode());
                    orderRefundRequestInfoMapper.updateById(orderRefundRequestInfo);
                }
            }catch (Exception e) {
                log.error(e.getMessage());
            }
        } else {
            scTicket.setId(ticket.getId());
            scTicket.setUpdatedAt(DateUtils.getNowDate());
            scTicket.setTicketStatus(ScTicketConstant.TICKET_STATUS_PROCESSING);
            scTicketService.updateScTickeByTicketId(scTicket);
        }
        //更新退款信息表工单id
        OrderRefund refund = new OrderRefund();
        refund.setId(orderRefund.getId());
        refund.setTicketId(scTicket.getId());
        orderRefundMapper.updateById(refund);
        //保存附件
        refundManger.saveMeta(vo.getMeta(),ScTicketConstant.TICKET_SOURCE_REFUND,orderRefund.getId(),vo.getOrganizationId());
        return Boolean.TRUE;
    }

    public String getChildTicketNumber(Long parentTicketId, String refundType) {

        ScTicket parentTicket = scTicketMapper.getTicketByTicketId(parentTicketId);
        List<ScTicket> childTicketList = scTicketMapper.selectScTicketFinishStateAByParentTicketIdAndTicketType(parentTicketId, new String[]{refundType}, new String[]{ScTicketConstant.TICKET_STATUS_NEW, ScTicketConstant.TICKET_STATUS_PROCESSING, ScTicketConstant.TICKET_STATUS_CLOSED, ScTicketConstant.TICKET_STATUS_FINISH});
        String childTicketNumber = parentTicket.getTicketNumber() + "-" + (ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(refundType) ? "IR" : "OR") + "-" + (CollectionUtil.isNotEmpty(childTicketList) ? (childTicketList.size() + 1) : 1);
        log.info("父工单编号:{}生成{}工单,工单编号为:{}",parentTicket.getTicketNumber(),refundType,childTicketNumber);
        return childTicketNumber;
    }
    @Transactional(rollbackFor = Exception.class)
    public OrderRefund getOrderRefund(OrderRefundRequestVO vo) {
        OrderRefund orderRefund = getOrderRefundInfo(vo);
        orderRefund.setTreatmentState("SUBMITTED");
        orderRefund.setRefundStatus(OrderRefundStateEnum.PENDING.getCode());
        Long orderRefundId = vo.getOrderRefundId();
        orderRefund.setId(orderRefundId);
        orderRefund.settingDefaultUpdate();
        orderRefundMapper.updateById(orderRefund);

        List<OrderRefundRequestVO.RefundDetailListVO> refundDetailList = vo.getRefundDetailList();
        for (OrderRefundRequestVO.RefundDetailListVO refundDetailListVO : refundDetailList) {
            List<OrderRefundRequestVO.RefundDetailFeeVO> refundDetailFeeVOList = refundDetailListVO.getRefundDetailFeeVOList();
            for (OrderRefundRequestVO.RefundDetailFeeVO refundDetailFeeVO : refundDetailFeeVOList) {
                OrderRefundItem orderRefundItem = BeanCopyUtils.copyBean(refundDetailFeeVO, OrderRefundItem.class);
                orderRefundItem.setOrderRefundId(orderRefundId);
                orderRefundItem.setOrganizationId(vo.getOrganizationId());
                orderRefundItem.setId(refundDetailFeeVO.getOrderRefundItemId());
                orderRefundItem.setUpdatedAt(DateUtils.getNowDate());
                if ("无sellerSku".equalsIgnoreCase(orderRefundItem.getSellerSku())) {
                    orderRefundItem.setSellerSku(null);
                }
                orderRefundItemMapper.updateById(orderRefundItem);
            }
        }
        return orderRefund;
    }

    private  OrderRefund getOrderRefundInfo(OrderRefundRequestVO vo) {
        OrderRefund orderRefund = new OrderRefund();
//        orderRefund.setRefundStatus();
        orderRefund.setReverseRequestDate(vo.getReverseRequestDate());
        orderRefund.setChannelOrderId(vo.getOrderNo());
        orderRefund.setAccountId(vo.getShopId());
        orderRefund.setOrganizationId(vo.getOrganizationId());
        orderRefund.setAccountFlag(vo.getAccountFlag());
//        orderRefund.setTotalAmountAdj();
        orderRefund.setOrderId(vo.getOrderId().toString());
        orderRefund.setShopId(vo.getShopId().longValue());
        orderRefund.setReturnText(vo.getReturnText());
        orderRefund.setCurrency(vo.getCurrency());
        orderRefund.setRefundType(vo.getRefundType());
        orderRefund.setRefundWay(vo.getRefundWay());
        orderRefund.setBankAccountNumber(vo.getRefundAccount());
//        orderRefund.setParentTicketId(vo.getTicketId());
        orderRefund.setChannel(vo.getChannel());
        orderRefund.setReverseOrderId(StringUtils.isNotEmpty(vo.getReverseOrderId()) ? Long.valueOf(vo.getReverseOrderId()) : null);
        orderRefund.setRefundClass(vo.getRefundClass());
        orderRefund.setAccountNumber(vo.getCustomerAccount());
        orderRefund.setRemark(vo.getRemark());
        orderRefund.setRefundRemark(vo.getRefundRemark());
        return orderRefund;
    }


    /**
     * 退款工单退回后编辑提交
     * @param vo
     * @param ticketId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitForReturnNewEdit(OrderRefundRequestVO vo, Long ticketId) {
        verifyAmount(vo);
        if (OrderRefundStateEnum.COMPLETED.getCode().equals(orderRefundMapper.selectById(vo.getOrderRefundId()).getRefundStatus())) {
            throw new ErpCommonException("已经退款请勿重复退款");
        }
        OrderRefund orderRefund = getOrderRefund(vo);
        ScTicket scTicket = new ScTicket();
        scTicket.setId(ticketId);
        scTicket.setTicketStatus(ScTicketConstant.TICKET_STATUS_PROCESSING);
        scTicket.setUpdatedAt(DateUtils.getNowDate());
        scTicketMapper.updateScTicket(scTicket);
        ScTicketLog scTicketLog = new ScTicketLog();
        scTicketLog.setTicketId(ticketId);
        scTicketLog.setOperateTime(DateUtils.getNowDate());
        scTicketLog.setOperatorBy(AuthContextHolder.getAuthUserDetails().getName());
        scTicketLog.setOperateContent("退款退回后重新提交");
        scTicketLog.setOperateType("SUBMIT");
        scTicketLog.setOrganizationId(vo.getOrganizationId().intValue());
        scTicketLogService.insertScTicketLog(scTicketLog);
        //保存附件
        refundManger.saveMeta(vo.getMeta(),ScTicketConstant.TICKET_SOURCE_REFUND,orderRefund.getId(),vo.getOrganizationId());
    }


    /**
     * 退款工单获取退款信息
     * @param ticketId
     * @return
     */
    @Override
    public OrderRefundRequestVO getBaseRefundInfo(Long ticketId) {

        ScTicket scTicket = scTicketMapper.selectScTicketById(ticketId);
        OrderRefund orderRefund = orderRefundMapper.selectById(scTicket.getSourceId());
        OrderRefundRequestVO refundInfo = getRefundInfo(orderRefund, scTicket.getOrderId(), scTicket.getReverseOrderId(), ticketId);
        String customerName = scTicket.getOrderId() == null ? null : saleOrdersMapper.getCustomerNameByOrderId(String.valueOf(scTicket.getOrderId()));
        refundInfo.setBuyName(customerName);
        List<SaleOrderVoTicket> saleOrderVoTicketList = scTicket.getOrderId() == null ? new ArrayList<>() : saleOrdersMapper.selectOrderByOrderId(scTicket.getOrderId());
        if (CollectionUtil.isNotEmpty(saleOrderVoTicketList)) {
            refundInfo.setChannel(saleOrderVoTicketList.get(0).getChannel());
        }
        return refundInfo;
    }

    public void verifyAmount(OrderRefundRequestVO vo) {
        if (ObjectUtil.isEmpty(vo.getOrderRefundSellerSku())) {
            throw new ErpCommonException("SellSku不能为空");
        }
        //退款原因仅sc渠道站内退款需要
        if (AccountSaleChannelEnum.SC.getValue().equals(vo.getChannel()) && ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(vo.getRefundType()) && StringUtils.isEmpty(vo.getReturnText())) {
            throw new ErpCommonException("退款原因不能为空");
        }
        if (ScTicketConstant.TICKET_TYPE_OUTSIDE_REFUND.equals(vo.getRefundType())) {
            if (StringUtils.isEmpty(vo.getRefundWay())) {
                throw new ErpCommonException("退款方式不能为空");
            }
            if (StringUtils.isEmpty(vo.getRefundClass())) {
                throw new ErpCommonException("退款类型不能为空");
            }
        }
        ScTicket scTicket = scTicketMapper.selectScTicketById(vo.getTicketId());
        String ticketType = null;
        String parentTicketType = null;
        if(scTicket != null) {
            ticketType = scTicket.getTicketType();
            if(scTicket.getParentTicketId() != null) {
                ScTicket parentScTicket = scTicketMapper.selectScTicketById(scTicket.getParentTicketId());
                if (parentScTicket != null) {
                    parentTicketType = parentScTicket.getTicketType();
                }
            }
        }
        //退款原因tk渠道站内信站内退款需要  tk渠道工单类型是站内信，或者父工单是站内信，然后退款类型是站内退款。
        if (AccountSaleChannelEnum.TIKTOK.getValue().equals(vo.getChannel()) && ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(vo.getRefundType())
                && StringUtils.isEmpty(vo.getReturnText())
                && (ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG.equals(ticketType) || ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG.equals(parentTicketType))
        ) {
            throw new ErpCommonException("tk站内信站内退款原因不能为空");
        }
        //退款原因walmart渠道站内信站内退款需要 walmart渠道工单类型是站内信，或者站内退款 ，然后退款类型是站内退款。
        if (AccountSaleChannelEnum.WALMART.getValue().equals(vo.getChannel()) && ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(vo.getRefundType())
                && StringUtils.isEmpty(vo.getReturnText())
        && (ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(ticketType)
                || ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG.equals(ticketType))
        ) {
            throw new ErpCommonException("walmart站内信站内退款原因不能为空");
        }
        List<OrderRefundRequestVO.RefundDetailListVO> refundDetailList = vo.getRefundDetailList();
        boolean remainRefundHas = false;
        for (OrderRefundRequestVO.RefundDetailListVO refundDetailListVO : refundDetailList) {
            List<OrderRefundRequestVO.RefundDetailFeeVO> refundDetailFeeVOList = refundDetailListVO.getRefundDetailFeeVOList();
            for (OrderRefundRequestVO.RefundDetailFeeVO refundDetailFeeVO : refundDetailFeeVOList) {
                if (StringUtils.isEmpty(refundDetailFeeVO.getChannelOrderItemId())) {
                    throw new ErpCommonException("单据行不能为空");
                }
                BigDecimal remainRefund = refundDetailFeeVO.getRemainRefund();
                BigDecimal refundAmount = refundDetailFeeVO.getRefundAmount();
                if (remainRefund != null && remainRefund.compareTo(BigDecimal.ZERO) > 0) {
                    remainRefundHas = true;
                }

                if (ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(vo.getRefundType())&&remainRefund == null) {
                    if (refundAmount != null && refundAmount.compareTo(BigDecimal.ZERO) > 0) {
                        throw new ErpCommonException("退款金额不可大于剩余可退款金额");
                    }
                } else {
                    if (ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(vo.getRefundType())&&refundAmount != null && refundAmount.compareTo(remainRefund) > 0) {
                        throw new ErpCommonException("退款金额不可大于剩余可退款金额");
                    }
                }
            }
        }
        //去除限制，允许查过实际退款金额进行退款(仅对站内退款校验)
        if (ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(vo.getRefundType())&&!remainRefundHas) {
            throw new ErpCommonException("所有sellerSku无剩余可退款金额，无法继续退款");
        }
    }

    private Boolean verifyIsSubmitted(Long ticketId) {
        List<ScTicket> list = scTicketService.selectScTicketByParentId(ticketId, null);
        if (CollectionUtil.isEmpty(list)) {
            return Boolean.TRUE;
        }
        //子工单已关闭排除校验
        list = list.stream().filter(s -> !ScTicketConstant.TICKET_STATUS_CLOSED.equals(s.getTicketStatus())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(list)) {
            return Boolean.TRUE;
        }
        List<Long> orderRefundIdList = list.stream().filter(s -> ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(s.getTicketType()) || ScTicketConstant.TICKET_TYPE_OUTSIDE_REFUND.equals(s.getTicketType())).map(ScTicket::getSourceId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(orderRefundIdList)) {
            return Boolean.TRUE;
        }
        List<OrderRefund> orderRefunds = orderRefundMapper.selectBatchIds(orderRefundIdList);
        if (CollectionUtil.isEmpty(orderRefunds)) {
            return Boolean.TRUE;
        }
        List<OrderRefund> collect = orderRefunds.stream().filter(s -> "SUBMITTED".equals(s.getTreatmentState()) || OrderRefundEnum.RETURN.getCode().equals(s.getTreatmentState())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(collect)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateOrderRequestState(Long ticketId) {
        ScTicket scTicket = scTicketService.selectScTicketByTicketId(ticketId);
        if (scTicket != null && scTicket.getParentTicketId() != null) {
            ScTicket parentScTicket = scTicketService.selectScTicketByTicketId(scTicket.getParentTicketId());
            if (parentScTicket != null && ScTicketConstant.TICKET_TYPE_REFUND_REQUEST.equals(parentScTicket.getTicketType())) {
                OrderRefundRequestInfo orderRefundRequestInfo = new OrderRefundRequestInfo();
                orderRefundRequestInfo.setId(parentScTicket.getSourceId());
                orderRefundRequestInfo.setUpdatedAt(DateUtils.getNowDate());
                orderRefundRequestInfo.setReturnStatus(OrderRefundStatusEnum.RETURN_OR_REFUND_REQUEST_COMPLETE.getCode());
                orderRefundRequestInfo.setRefundStatus(RefundStateEnum.REFUNDED.getCode());
                orderRefundRequestInfoMapper.updateById(orderRefundRequestInfo);
            }
        }
    }


    /**
     * 站内退款工单信息
     * @param ticketId
     * @return
     */
    @Override
    public InSideRefundInfoVO getInSideRefundInfoVO(Long ticketId) {

        InSideRefundInfoVO infoVO = new InSideRefundInfoVO();
        ScTicket scTicket = scTicketService.selectScTicketByTicketId(ticketId);
        OrderRefund orderRefund = orderRefundMapper.selectById(scTicket.getSourceId());
        List<InSideRefundDetailInfo> detailInfos = new ArrayList<>();
        List<OrderRefundItem> orderRefundItems = orderRefundItemMapper.selectList(new LambdaQueryWrapper<OrderRefundItem>().eq(OrderRefundItem::getOrderRefundId, scTicket.getSourceId()));
        infoVO.setRefundTotalAmount(orderRefundItems.stream().map(OrderRefundItem::getRefundAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
        Map<String, List<OrderRefundItem>> collect = orderRefundItems.stream().collect(Collectors.groupingBy(t -> (StringUtils.isEmpty(t.getSellerSku()) ? "无sellerSku" : t.getSellerSku()) + "|" + t.getChannelOrderItemId()));
        collect.forEach((sellerSkuAndChanelOrderItemId,list)->{
            String[] split = sellerSkuAndChanelOrderItemId.split("\\|");
            List<InSideRefundDetailInfo> inSideRefundDetailInfos = orderRefundMapper.selectRefundDetailBySellerSku("无sellerSku".equalsIgnoreCase(split[0]) ? null : split[0], scTicket.getOrderId(),split[1]);
            if (CollectionUtil.isNotEmpty(inSideRefundDetailInfos)) {
                detailInfos.addAll(inSideRefundDetailInfos);
            }
        });
        String buyName = saleOrdersMapper.getCustomerNameByOrderId(String.valueOf(scTicket.getOrderId()));
        infoVO.setBuyName(buyName);
        infoVO.setApplyTime(orderRefund.getReverseRequestDate());
        infoVO.setCurrency(orderRefund.getCurrency());
        infoVO.setDetail(detailInfos);
        infoVO.setTreatmentState(orderRefund.getTreatmentState());
        return infoVO;
    }

    @Override
    public List<String> getAllRefundReason(Long contextId,Long ticketId) {

        List<OrderRefund> orderRefunds = orderRefundMapper.selectList(new LambdaQueryWrapper<OrderRefund>().select(OrderRefund::getId, OrderRefund::getReturnText).eq(OrderRefund::getOrganizationId, contextId).isNotNull(OrderRefund::getReturnText).ne(OrderRefund::getReturnText, ""));
        List<OrderRefundRequestInfo> orderRefundRequestInfos = orderRefundRequestInfoMapper.selectList(new LambdaQueryWrapper<OrderRefundRequestInfo>().select(OrderRefundRequestInfo::getId,OrderRefundRequestInfo::getReturnReasonText).eq(OrderRefundRequestInfo::getOrganizationId, contextId).isNotNull(OrderRefundRequestInfo::getReturnReasonText).ne(OrderRefundRequestInfo::getReturnReasonText, ""));
        List<ReturnInfoEntity> returnInfoEntityList = returnInfoMapper.selectList(new LambdaQueryWrapper<ReturnInfoEntity>().select(ReturnInfoEntity::getId,ReturnInfoEntity::getReturnReason).eq(ReturnInfoEntity::getOrganizationId,contextId).isNotNull(ReturnInfoEntity::getReturnReason).ne(ReturnInfoEntity::getReturnReason, ""));
        List<String> returnReason = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(orderRefunds)) {
            returnReason.addAll(orderRefunds.stream().map(OrderRefund::getReturnText).distinct().collect(Collectors.toList()));
        }
        if (CollectionUtil.isNotEmpty(orderRefundRequestInfos)) {
            returnReason.addAll(orderRefundRequestInfos.stream().map(OrderRefundRequestInfo::getReturnReasonText).distinct().collect(Collectors.toList()));
        }
        if (CollectionUtil.isNotEmpty(returnInfoEntityList)) {
            returnReason.addAll(returnInfoEntityList.stream().map(ReturnInfoEntity::getReturnReason).distinct().collect(Collectors.toList()));
        }
        if(ticketId != null) {
            ScTicket scTicket = scTicketService.selectScTicketByTicketId(ticketId);
            if(scTicket != null) {
                if("tiktok".equals(scTicket.getTicketSource())) {
                    if (ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG.equals(scTicket.getTicketType())) { //tk站内信
                        //增加tk站内信站内退款原因
                        List<String> newReturnReason = new ArrayList<>();
                        TkStationLetterStationRefundReasonEnum[] array = TkStationLetterStationRefundReasonEnum.values();
                        newReturnReason.addAll(Arrays.stream(array).map(i -> i.getName()).collect(Collectors.toList()));
                        return newReturnReason;
                    } else if (ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(scTicket.getTicketType())) { //tk站内信站内退款
                        ScTicket parentScTicket = scTicketService.selectScTicketByTicketId(scTicket.getParentTicketId());
                        if (parentScTicket != null && ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG.equals(parentScTicket.getTicketType())) {
                            //增加tk站内信站内退款原因
                            List<String> newReturnReason = new ArrayList<>();
                            TkStationLetterStationRefundReasonEnum[] array = TkStationLetterStationRefundReasonEnum.values();
                            newReturnReason.addAll(Arrays.stream(array).map(i -> i.getName()).collect(Collectors.toList()));
                            return newReturnReason;
                        }
                    }
                }

                if("walmart".equals(scTicket.getTicketSource())) {
                    if (ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG.equals(scTicket.getTicketType())) { //walmart站内信
                        //增加walmart站内信站内退款原因
                        List<String> newReturnReason = new ArrayList<>();
                        WalmartStationLetterStationRefundReasonEnum[] array = WalmartStationLetterStationRefundReasonEnum.values();
                        newReturnReason.addAll(Arrays.stream(array).map(i -> i.getValue()).collect(Collectors.toList()));
                        return newReturnReason;
                    } else if (ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(scTicket.getTicketType())) { //walmart站内信站内退款
                        ScTicket parentScTicket = scTicketService.selectScTicketByTicketId(scTicket.getParentTicketId());
                        if (parentScTicket != null && ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG.equals(parentScTicket.getTicketType())) {
                            //增加walmart站内信站内退款原因
                            List<String> newReturnReason = new ArrayList<>();
                            WalmartStationLetterStationRefundReasonEnum[] array = WalmartStationLetterStationRefundReasonEnum.values();
                            newReturnReason.addAll(Arrays.stream(array).map(i -> i.getValue()).collect(Collectors.toList()));
                            return newReturnReason;
                        }
                    }
                }
            }

        }
        if (CollectionUtil.isEmpty(returnReason)) {
            return returnReason;
        }
        return returnReason.stream().distinct().collect(Collectors.toList());
    }

    public void saveAndSubmittedVerify(Long ticketId) {
        ScTicket scTicket = scTicketMapper.selectScTicketById(ticketId);
        //退款申请工单在拒绝站内退款后会更新businessTicketStatus为拒绝
        if (ObjectUtil.isNotNull(scTicket) && OrderRefundEnum.REJECTED_TICKET_ORDER.getCode()
                .equals(scTicket.getBusinessTicketStatus())) {
            throw new CommonException("该工单已经拒绝,请勿重复操作");
        }
        //父工单为退款请求类型 保存提交前，tiktok渠道站内退款只允许有一笔已完成的退款工单
        //其他可能有多笔。
        if (AccountSaleChannelEnum.TIKTOK.getValue().equals(scTicket.getTicketSource())) {
            List<ScTicket> scTickets = scTicketMapper.selectScTicketByParentId(ticketId);
            ScTicket ticketByTicketId = scTicketMapper.getTicketByTicketId(ticketId); //查询父工单
            for (ScTicket ticket : scTickets) {
                if (ScTicketConstant.TICKET_STATUS_FINISH
                        .equals(ticket.getTicketStatus()) && AccountSaleChannelEnum.TIKTOK.getValue().equals(ticket.getTicketSource())
                        && ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(ticket.getTicketType())
                        && ScTicketConstant.TICKET_TYPE_REFUND_REQUEST.equals(ticketByTicketId.getTicketType())) {
                    throw new CommonException("该订单已完成退款,请勿重复操作");
                }
            }
        }

    }


    public ScOrderRefundRequest getScOrderRefundRequest(Long orderId, Long ticketId, Long shopId, Integer orgId, Long sourceId, String orderNo) {
        ScOrderRefundRequest scOrderRefundRequest = new ScOrderRefundRequest();
        scOrderRefundRequest.setShopId(shopId);
        scOrderRefundRequest.setOrderId(orderId);
        scOrderRefundRequest.setOrganizationId(orgId);
        scOrderRefundRequest.setAmazonOrderId(orderNo);
        OrderRefund orderRefund = orderRefundService.getById(sourceId);
        scOrderRefundRequest.setAdjustmentReasonCode(orderRefund.getReturnText());
        scOrderRefundRequest.setCurrency(orderRefund.getCurrency());
        List<OrderRefundItem> orderRefundItems = orderRefundItemMapper.selectList(Wrappers.lambdaQuery(OrderRefundItem.class).eq(OrderRefundItem::getOrderRefundId, sourceId));
        Map<String, List<OrderRefundItem>> collect = orderRefundItems.stream().collect(Collectors.groupingBy(t -> t.getSellerSku() + "|" + t.getChannelOrderItemId()));
        List<ScOrderRefundRequest.SellerSkuAndFee> sellerSkuAndFees = new ArrayList<>();
        collect.forEach((sellerSku,list)->{
            String[] split = sellerSku.split("\\|");
            ScOrderRefundRequest.SellerSkuAndFee sellerSkuAndFee = new ScOrderRefundRequest.SellerSkuAndFee();
            sellerSkuAndFee.setSellerSku(split[0]);
            sellerSkuAndFee.setOrderItemId(split[1]);
            //商品金额
            BigDecimal itemPriceAdj = list.stream().filter(s -> OrderRefundFeeTypeEnum.REFUND_PRODUCT.getType().equals(s.getReturnItemType())).map(OrderRefundItem::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            //商品税
            BigDecimal itemTaxAdj = list.stream().filter(s -> OrderRefundFeeTypeEnum.REFUND_PRODUCT_TAX.getType().equals(s.getReturnItemType())).map(OrderRefundItem::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            //运费
            BigDecimal shippingPriceAdj = list.stream().filter(s -> OrderRefundFeeTypeEnum.REFUND_RESTORE.getType().equals(s.getReturnItemType())).map(OrderRefundItem::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            //运费税
            BigDecimal shippingTaxPriceAdj = list.stream().filter(s -> OrderRefundFeeTypeEnum.REFUND_RESTORE_FEE.getType().equals(s.getReturnItemType())).map(OrderRefundItem::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            sellerSkuAndFee.setItemPriceAdj(itemPriceAdj);
            sellerSkuAndFee.setItemTaxAdj(itemTaxAdj);
            sellerSkuAndFee.setShippingPriceAdj(shippingPriceAdj);
            sellerSkuAndFee.setShippingTaxPriceAdj(shippingTaxPriceAdj);
            sellerSkuAndFees.add(sellerSkuAndFee);
        });
        scOrderRefundRequest.setSellerSkuAndFeeList(sellerSkuAndFees);

        return scOrderRefundRequest;
    }

    @Override
    public Boolean manualRefund(String channel, Long orderId, String reverseOrderId, Long sourceId, Long ticketId, Long shopId, String refundType, String bankAccountId, String bankAccountNumber, String refundRemark, Boolean buyerKeepItem) {
        // 先调用确认退款接口,成功就更新退款表,失败就抛异常
        isRefunded(ticketId);
        // 退款表单同步,工单同步,当前工单结束
        Boolean refundResult = orderRefundService.manualRefundFinish(sourceId, bankAccountId, bankAccountNumber, refundRemark);
        Boolean ticketResult = updateOrderRefundApply(channel, orderId, reverseOrderId, sourceId, ticketId, OrderRefundStateEnum.COMPLETED.getDesc());
        if (!refundResult || !ticketResult) {
            log.info("站内退款工单手动退款失败,工单id:{}", ticketId);
            throw new CommonException("站内退款工单手动退款失败,请联系管理员");
        } else {
            ScTicketLog ticketLog = new ScTicketLog();
            ticketLog.settingDefaultCreate();
            ticketLog.setTicketId(ticketId);
            ticketLog.setOperateType("Update");
            ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
            ticketLog.setOperateTime(DateUtils.getNowDate());
            ticketLog.setOperateContent("已提交退款:退款方式为手动退款");
            scTicketLogService.insertScTicketLog(ticketLog);
        }
        return Boolean.TRUE;
    }
}
