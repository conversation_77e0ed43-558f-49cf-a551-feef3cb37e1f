package com.bizark.op.service.service.promotions;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bizark.op.api.enm.promotions.AmzCouponActiveStateEnum;
import com.bizark.op.api.enm.promotions.AmzCouponStateEnum;
import com.bizark.op.api.enm.promotions.AmzPromotionsStateEnum;
import com.bizark.op.api.entity.op.promotions.*;
import com.bizark.op.api.service.promotions.*;
import com.bizark.op.api.vo.promotions.fee.AmzPromotionFeeCouponVo;
import com.bizark.op.api.vo.promotions.fee.AmzPromotionFeePromotionVo;
import com.bizark.op.common.util.*;
import com.bizark.op.service.mapper.promotions.AmzPromotionsCouponMapper;
import com.bizark.op.service.mapper.promotions.AmzPromotionsCouponsMapper;
import com.bizark.op.service.mapper.promotions.AmzPromotionsMapper;
import com.bizark.op.service.mapper.promotions.fee.AmzPromotionFeeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


import java.util.*;
import java.util.stream.Collectors;

/**
 * @remarks:生成促销费用的数据检查服务
 * @Author: Ailill
 * @Date: 2023/11/27 19:26
 */
@Service
@Slf4j
public class AmzPromotionFeeDataVerifyServiceImpl implements IAmzPromotionFeeDataVerifyService {

    @Autowired
    private IAmzPromotionAndCouponService iAmzPromotionAndCouponService;

    @Autowired
    private AmzPromotionsMapper amzPromotionsMapper;

    @Autowired
    private IAmzPromotionsService iAmzPromotionsService;

    @Autowired
    private AmzPromotionsCouponMapper amzPromotionsCouponMapper;

    @Autowired
    private AmzPromotionsCouponsMapper amzPromotionsCouponsMapper;

    @Autowired
    private AmzPromotionFeeMapper amzPromotionFeeMapper;

    @Autowired
    private IAmzPromotionsOperateLogService amzPromotionsOperateLogService;

    @Autowired
    private IAmzPromotionsCouponOperateLogService amzPromotionsCouponOperateLogService;

    @Value("${promotion.advice.webhook}")
    private  String PROMOTION_MESSAGES;
    private static final String PROMOTION_MESSAGES_TEST = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a03f0ebd-b348-4dc3-b01d-1f5fec0f78b4";

    /**
     * 每天16:30查询perFunding为空的进行同步
     *
     * @param nowDate
     */
    @Override
    public void promotionFeeDataVerify(Date nowDate) {

        Date lastDate = DateUtil.addDate(DateUtil.UtcToPacificDate(nowDate), -1);
        Integer[] stateArray = new Integer[]{
                AmzPromotionsStateEnum.APPROVED.value(),
                AmzPromotionsStateEnum.APPROVED_AND_RUNNING.value(),
                AmzPromotionsStateEnum.APPROVED_BUT_NOT_FEATURED.value(),
                AmzPromotionsStateEnum.APPROVED_BUT_NEEDS_YOUR_ATTENTION.value(),
                AmzPromotionsStateEnum.MODIFYING.value(),
                AmzPromotionsStateEnum.MODIFY_EXCEPTION.value(),
                AmzPromotionsStateEnum.CANCELING.value(),
                AmzPromotionsStateEnum.CANCEL_EXCEPTION.value(),
                AmzPromotionsStateEnum.CANCELED.value(),
                AmzPromotionsStateEnum.APPROVED_AND_ENDED.value()

        };
        List<AmzPromotionFeePromotionVo> fee = amzPromotionFeeMapper.selectPromotionFeePromotionListByDateAndPromotionState(stateArray, lastDate, lastDate);
        if (CollectionUtil.isNotEmpty(fee)) {
            fee = fee.stream().filter(s -> (s.getCompleteDate() == null || DateUtil.compareDate(DateUtils.parseDateToStr("yyyy-MM-dd", lastDate), DateUtils.parseDateToStr("yyyy-MM-dd", s.getCompleteDate())) <= 0)).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(fee)) {
                List<Long> promotionIdList = fee.stream().map(AmzPromotionFeePromotionVo::getPromotionId).distinct().collect(Collectors.toList());
                List<AmzPromotionFeeDataVerify> list = amzPromotionsMapper.selectAmzPromotionListByConditions(promotionIdList.toArray(new Long[promotionIdList.size()]));
                if (CollectionUtil.isNotEmpty(list)) {
//                    sendMsgToGroupChat(list, "的每件商品折扣为空");

                    List<AmzPromotions> lists = new ArrayList<>();
                    Map<Long, List<AmzPromotionFeeDataVerify>> collect = list.stream().collect(Collectors.groupingBy(AmzPromotionFeeDataVerify::getId));
                    for (Map.Entry<Long, List<AmzPromotionFeeDataVerify>> entry : collect.entrySet()) {
                        AmzPromotions promotions = new AmzPromotions();
                        promotions.setId(entry.getKey());
                        List<AmzPromotionFeeDataVerify> value = entry.getValue();
                        promotions.setPromotionsId(value.get(0).getPromotionsId());
                        promotions.setPromotionsName(value.get(0).getPromotionsName());
                        lists.add(promotions);
                    }
                    iAmzPromotionAndCouponService.syncPromotionsNoCondition(lists, "System","promotion促销费用校验");
                }
            }
        }


    }

    /**
     * 16:30查询coupon的asin不存在于mar_listing_info的进行通知，存在的并且list_price为空的进行获取
     *
     * @param nowDate
     */
    @Override
    public void couponFeeDataVerify(Date nowDate) {

        Date lastDate = DateUtil.addDate(DateUtil.UtcToPacificDate(nowDate), -1);

        Integer[] couponsStateArray = new Integer[]{
                AmzCouponActiveStateEnum.ACTIVE.value(),
                AmzCouponActiveStateEnum.ACTIVE_HAS_EXCEPTION.value(),
                AmzCouponActiveStateEnum.CANCELED.value(),
                AmzCouponActiveStateEnum.EXPIRED.value()
        };
        Integer[] couponStateArray = new Integer[]{
                AmzCouponStateEnum.ACTIVE_VC.value(),
                AmzCouponStateEnum.MODIFYING.value(),
                AmzCouponStateEnum.MODIFY_EXCEPTION.value(),
                AmzCouponStateEnum.CANCELING.value(),
                AmzCouponStateEnum.CANCELING_EXCEPTION.value(),
                AmzCouponStateEnum.CANCELED_VC.value(),
                AmzCouponStateEnum.CANCELED.value(),
                AmzCouponStateEnum.EXPIRED.value()

        };
        List<AmzPromotionFeeCouponVo> fee = amzPromotionFeeMapper.selectCouponFeeCouponListByDateAndState(couponsStateArray, couponStateArray, lastDate, lastDate);
        if (CollectionUtil.isEmpty(fee)) {
            return;
        }
        log.info("couponFeeDataVerify data--{}", JSONObject.toJSONString(fee));
        fee = fee.stream().filter(s -> s.getOrganizationId().equals(1000049) && (s.getCompleteDate() == null || DateUtil.compareDate(DateUtils.parseDateToStr("yyyy-MM-dd", lastDate), DateUtils.parseDateToStr("yyyy-MM-dd", s.getCompleteDate())) <= 0)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(fee)) {
            return;
        }
        log.info("couponFeeDataVerify data1--{}", JSONObject.toJSONString(fee));
        List<Long> couponIdList = fee.stream().map(AmzPromotionFeeCouponVo::getCouponId).distinct().collect(Collectors.toList());

        //查询coupon的asin不存在于mar_listing_info的进行通知
        List<AmzPromotionsCoupon> asinNotInMar = amzPromotionsCouponMapper.selectListWhenAsinNotInMarListingInfo(couponIdList.toArray(new Long[couponIdList.size()]));
        if (CollectionUtil.isNotEmpty(asinNotInMar)) {
            log.info("couponFeeDataVerify asinNotInMar--{}", JSONObject.toJSONString(asinNotInMar));
            sendMsgToGroupChatCoupon(asinNotInMar, "asin不存在于MarListingInfo");
        }
        //查询coupon的asin存在于mar_listing_info但是list_price为空的进行获取价格
        List<AmzPromotionsCoupon> asinInMarListPriceIsNull = amzPromotionsCouponMapper.selectListWhenListPriceIsNull(couponIdList.toArray(new Long[couponIdList.size()]));
        if (CollectionUtil.isNotEmpty(asinInMarListPriceIsNull)) {
            log.info("couponFeeDataVerify asinInMarListPriceIsNull--{}", JSONObject.toJSONString(asinInMarListPriceIsNull));
            Map<Long, Set<String>> shopIdMapAsinList = asinInMarListPriceIsNull.stream().filter(t->t.getShopId() != null && StringUtils.isNotEmpty(t.getAsin())).collect(Collectors.groupingBy(AmzPromotionsCoupon::getShopId, Collectors.mapping(AmzPromotionsCoupon::getAsin, Collectors.toSet())));
            shopIdMapAsinList.forEach((k, v) -> {
                try {
                    iAmzPromotionsService.getAsinFrontPrice(v.toArray(new String[0]), k.intValue());
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("couponFeeDataVerify getAsinFrontPrice error--{}", e.getMessage());
                }

            });
        }
    }


    public void sendMsgToGroupChat(String msg) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msgtype", "text");
        JSONObject jsonObjectContent = new JSONObject();
        jsonObjectContent.put("content", msg);
        jsonObject.put("text", jsonObjectContent);
        HttpUtils.doPostByForm(PROMOTION_MESSAGES, jsonObject.toString());
    }

    public void sendMsgToGroupChat(List<AmzPromotionFeeDataVerify> list, String msg) {
        Map<Long, List<AmzPromotionFeeDataVerify>> collect = list.stream().collect(Collectors.groupingBy(AmzPromotionFeeDataVerify::getId));
        for (Map.Entry<Long, List<AmzPromotionFeeDataVerify>> entry : collect.entrySet()) {
            Long id = entry.getKey();
            List<AmzPromotionFeeDataVerify> value = entry.getValue();
            String promotionsId = value.get(0).getPromotionsId();
            String asin = value.stream().map(AmzPromotionFeeDataVerify::getAsin).collect(Collectors.joining(","));
            //发送通知
            sendMsgToGroupChat(String.format("Promotion ID:%s Asin:%s " + msg, StringUtil.isNotEmpty(promotionsId) ? promotionsId : id.toString(), asin));
        }
    }

    public void sendMsgToGroupChatCoupon(List<AmzPromotionsCoupon> list, String msg) {
        List<Long> couponsIdList = list.stream().map(AmzPromotionsCoupon::getCouponId).distinct().collect(Collectors.toList());
        List<AmzPromotionsCoupons> couponsList = amzPromotionsCouponsMapper.selectAmzPromotionsCouponsByIds(couponsIdList.toArray(new Long[couponsIdList.size()]));
        Map<Long, List<AmzPromotionsCoupon>> collect = list.stream().collect(Collectors.groupingBy(AmzPromotionsCoupon::getCouponId));
        for (Map.Entry<Long, List<AmzPromotionsCoupon>> entry : collect.entrySet()) {
            Long couponsId = entry.getKey();
            List<AmzPromotionsCoupon> value = entry.getValue();
            String promotionName = couponsList.stream().filter(r -> couponsId.equals(r.getId())).collect(Collectors.toList()).get(0).getPromotionName();
            String asin = value.stream().map(AmzPromotionsCoupon::getAsin).collect(Collectors.joining(","));
            //发送通知
            sendMsgToGroupChat(String.format("Coupon活动 :%s Asin:%s " + msg, promotionName, asin));
        }
    }


    /**
     * 通知 17:30 promotion中perFunding为空的
     *
     * @param nowDate
     */
    @Override
    public void promotionFeeDataVerifyAdvice(Date nowDate) {

        Date lastDate = DateUtil.addDate(DateUtil.UtcToPacificDate(nowDate), -1);
        Integer[] stateArray = new Integer[]{
                AmzPromotionsStateEnum.APPROVED.value(),
                AmzPromotionsStateEnum.APPROVED_AND_RUNNING.value(),
                AmzPromotionsStateEnum.APPROVED_BUT_NOT_FEATURED.value(),
                AmzPromotionsStateEnum.APPROVED_BUT_NEEDS_YOUR_ATTENTION.value(),
                AmzPromotionsStateEnum.MODIFYING.value(),
                AmzPromotionsStateEnum.MODIFY_EXCEPTION.value(),
                AmzPromotionsStateEnum.CANCELING.value(),
                AmzPromotionsStateEnum.CANCEL_EXCEPTION.value(),
                AmzPromotionsStateEnum.CANCELED.value(),
                AmzPromotionsStateEnum.APPROVED_AND_ENDED.value()

        };
        List<AmzPromotionFeePromotionVo> fee = amzPromotionFeeMapper.selectPromotionFeePromotionListByDateAndPromotionState(stateArray, lastDate, lastDate);
        if (CollectionUtil.isNotEmpty(fee)) {
            fee = fee.stream().filter(s -> (s.getCompleteDate() == null || DateUtil.compareDate(DateUtils.parseDateToStr("yyyy-MM-dd", lastDate), DateUtils.parseDateToStr("yyyy-MM-dd", s.getCompleteDate())) <= 0)).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(fee)) {
                List<Long> promotionIdList = fee.stream().map(AmzPromotionFeePromotionVo::getPromotionId).distinct().collect(Collectors.toList());
                List<AmzPromotionFeeDataVerify> list = amzPromotionsMapper.selectAmzPromotionListByConditions(promotionIdList.toArray(new Long[promotionIdList.size()]));
                if (CollectionUtil.isNotEmpty(list)) {
                    sendMsgToGroupChat(list, "的每件商品折扣为空");
                }
            }
        }
    }

    /**
     * 17:30查询coupon的asin不存在于mar_listing_info的进行通知，存在的并且list_price为空的进行获取,未获取到的进行通知
     *
     * @param nowDate
     */
    @Override
    public void couponFeeDataVerifyAdvice(Date nowDate) {

        Date lastDate = DateUtil.addDate(DateUtil.UtcToPacificDate(nowDate), -1);

        Integer[] couponsStateArray = new Integer[]{
                AmzCouponActiveStateEnum.ACTIVE.value(),
                AmzCouponActiveStateEnum.ACTIVE_HAS_EXCEPTION.value(),
                AmzCouponActiveStateEnum.CANCELED.value(),
                AmzCouponActiveStateEnum.EXPIRED.value()
        };
        Integer[] couponStateArray = new Integer[]{
                AmzCouponStateEnum.ACTIVE_VC.value(),
                AmzCouponStateEnum.MODIFYING.value(),
                AmzCouponStateEnum.MODIFY_EXCEPTION.value(),
                AmzCouponStateEnum.CANCELING.value(),
                AmzCouponStateEnum.CANCELING_EXCEPTION.value(),
                AmzCouponStateEnum.CANCELED_VC.value(),
                AmzCouponStateEnum.CANCELED.value(),
                AmzCouponStateEnum.EXPIRED.value()

        };
        List<AmzPromotionFeeCouponVo> fee = amzPromotionFeeMapper.selectCouponFeeCouponListByDateAndState(couponsStateArray, couponStateArray, lastDate, lastDate);
        if (CollectionUtil.isEmpty(fee)) {
            return;
        }
        log.info("couponFeeDataVerifyAdvice data--{}", JSONObject.toJSONString(fee));
        fee = fee.stream().filter(s -> s.getOrganizationId().equals(1000049) && (s.getCompleteDate() == null || DateUtil.compareDate(DateUtils.parseDateToStr("yyyy-MM-dd", lastDate), DateUtils.parseDateToStr("yyyy-MM-dd", s.getCompleteDate())) <= 0)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(fee)) {
            return;
        }
        log.info("couponFeeDataVerifyAdvice data1--{}", JSONObject.toJSONString(fee));
        List<Long> couponIdList = fee.stream().map(AmzPromotionFeeCouponVo::getCouponId).distinct().collect(Collectors.toList());

        //查询coupon的asin不存在于mar_listing_info的进行通知
        List<AmzPromotionsCoupon> asinNotInMar = amzPromotionsCouponMapper.selectListWhenAsinNotInMarListingInfo(couponIdList.toArray(new Long[couponIdList.size()]));
        if (CollectionUtil.isNotEmpty(asinNotInMar)) {
            log.info("couponFeeDataVerifyAdvice asinNotInMar--{}", JSONObject.toJSONString(asinNotInMar));
            sendMsgToGroupChatCoupon(asinNotInMar, "asin不存在于MarListingInfo");
        }
        //查询coupon asin存在于mar_listing_info的且list_price为空的
        List<AmzPromotionsCoupon> asinInMarListPriceIsNull = amzPromotionsCouponMapper.selectListWhenListPriceIsNull(couponIdList.toArray(new Long[couponIdList.size()]));
        if (CollectionUtil.isNotEmpty(asinInMarListPriceIsNull)) {
            log.info("couponFeeDataVerifyAdvice asinInMarListPriceIsNull--{}", JSONObject.toJSONString(asinInMarListPriceIsNull));
            List<AmzPromotionsSkuDetail> asinFrontPrice = new ArrayList<>();
            Map<Long, Set<String>> shopIdMapAsinList = asinInMarListPriceIsNull.stream().filter(t->t.getShopId() != null && StringUtils.isNotEmpty(t.getAsin())).collect(Collectors.groupingBy(AmzPromotionsCoupon::getShopId, Collectors.mapping(AmzPromotionsCoupon::getAsin, Collectors.toSet())));
            shopIdMapAsinList.forEach((k, v) -> {
                try {
                    List<AmzPromotionsSkuDetail> getResult = iAmzPromotionsService.getAsinFrontPrice(v.toArray(new String[0]), k.intValue());
                    asinFrontPrice.addAll(getResult);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("couponFeeDataVerifyAdvice getAsinFrontPrice error2--{}", e.getMessage());
                }
            });

            if (CollectionUtil.isEmpty(asinFrontPrice)) {
                log.info("couponFeeDataVerifyAdvice asinInMarListPriceIsNull未获取到价格--{}", JSONObject.toJSONString(asinInMarListPriceIsNull));
                sendMsgToGroupChatCoupon(asinInMarListPriceIsNull, "未获取到价格@Sen（张森）@Annie（叶芸）");
            } else {
                //asinInMarListPriceIsNull - asinFrontPrice
                List<String> getListPrice = asinFrontPrice.stream().map(AmzPromotionsSkuDetail::getAsin).distinct().collect(Collectors.toList());
                List<AmzPromotionsCoupon> needSecondAdvice = asinInMarListPriceIsNull.stream().filter(r -> !getListPrice.contains(r.getAsin())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(needSecondAdvice)) {
                    log.info("couponFeeDataVerifyAdvice asinInMarListPriceIsNull未获取到价格未获取到价格--{}", JSONObject.toJSONString(needSecondAdvice));
                    sendMsgToGroupChatCoupon(needSecondAdvice, "未获取到价格@Sen（张森）@Annie（叶芸）");
                }
            }
        }
    }


    @Override
    public void promotionStateSync() {

        //自动对processing状态发起同步
        AmzPromotions promotions = new AmzPromotions();
        promotions.setPromotionsState(AmzPromotionsStateEnum.PROCESSING.value());
        List<AmzPromotions> amzPromotionsList = amzPromotionsMapper.selectAmzPromotionsLists(promotions);
        if (CollectionUtil.isNotEmpty(amzPromotionsList)) {
            iAmzPromotionAndCouponService.syncPromotionsNoCondition(amzPromotionsList, "System","processing状态同步");
        }
    }

    @Override
    public void promotionCouponExceptionStateSync() {

        //每隔一小时promotion,coupon状态为取消异常修改异常大于2小时且店铺后台已存在的进行同步
        Integer[] promotionState = {AmzPromotionsStateEnum.CANCEL_EXCEPTION.value(),AmzPromotionsStateEnum.MODIFY_EXCEPTION.value()};
        List<AmzPromotions> amzPromotionsList = amzPromotionsMapper.selectList(new LambdaQueryWrapper<AmzPromotions>().in(AmzPromotions::getPromotionsState, Arrays.asList(promotionState)));
        if (CollectionUtil.isNotEmpty(amzPromotionsList)) {
            //查询最近日志的操作时间
            List<Long> promotionIdList = amzPromotionsList.stream().map(AmzPromotions::getId).collect(Collectors.toList());
            List<AmzPromotionsOperateLog> promotionsOperateLogList = amzPromotionsOperateLogService.list(new LambdaQueryWrapper<AmzPromotionsOperateLog>().in(AmzPromotionsOperateLog::getPromotionId, promotionIdList));
            List<AmzPromotions> collect = amzPromotionsList.stream().filter(s -> {
                Date max = Collections.max(promotionsOperateLogList.stream().filter(t -> s.getId().equals(t.getPromotionId())).map(AmzPromotionsOperateLog::getCreatedAt).collect(Collectors.toList()));
                boolean result = s.getSubShopBack() != null && s.getSubShopBack() && DateUtil.getBetweenMinute(max, DateUtils.getNowDate()) > 120;
                return result;
            }).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)) {
                iAmzPromotionAndCouponService.syncPromotionsNoCondition(amzPromotionsList, "System","promotion取消异常或修改异常状态自动同步");
            }
        }
        List<AmzPromotionsCoupons> list = new ArrayList<>();
        Integer[] couponsState = {AmzCouponStateEnum.CANCELING_EXCEPTION.value(), AmzCouponStateEnum.MODIFY_EXCEPTION.value()};
        List<AmzPromotionsCoupon> couponList = amzPromotionsCouponMapper.selectListByState(couponsState);
        if (CollectionUtil.isNotEmpty(couponList)) {
            List<Long> initCouponIds = couponList.stream().map(AmzPromotionsCoupon::getId).collect(Collectors.toList());
            List<AmzPromotionsCouponOperateLog> couponOperateLogList = amzPromotionsCouponOperateLogService.list(new LambdaQueryWrapper<AmzPromotionsCouponOperateLog>().in(AmzPromotionsCouponOperateLog::getCouponId, initCouponIds));
            List<Long> couponsIds = couponList.stream().filter(s->{
                Date max = Collections.max(couponOperateLogList.stream().filter(t->s.getId().equals(t.getCouponId())).map(AmzPromotionsCouponOperateLog::getCreatedAt).collect(Collectors.toList()));
                boolean result = s.getCouponState() != null && DateUtil.getBetweenMinute(max, DateUtils.getNowDate()) > 120 && s.getCouponId() != null;
                return result;
            }).map(AmzPromotionsCoupon::getCouponId).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(couponsIds)) {
                List<AmzPromotionsCoupons> couponsList = amzPromotionsCouponsMapper.selectAmzPromotionsCouponsByIds(couponsIds.toArray(new Long[0])).stream().filter(s->s.getSubShopBack() != null && s.getSubShopBack()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(couponsList)) {
                    iAmzPromotionAndCouponService.syncCouponsListNoCondition(list, "System", "coupon取消异常或修改异常状态自动同步");
                }
            }
        }
    }
}
