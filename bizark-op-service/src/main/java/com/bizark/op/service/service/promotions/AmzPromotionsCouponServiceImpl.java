package com.bizark.op.service.service.promotions;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.op.api.cons.AmzPromotionsCouponConstants;
import com.bizark.op.api.cons.MQDefine;
import com.bizark.op.api.enm.promotions.*;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.promotions.*;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.entity.op.seller.AmzCoupon;
import com.bizark.op.api.entity.op.seller.AmzCouponCampaign;
import com.bizark.op.api.exception.ErpCommonException;
import com.bizark.op.api.request.msg.send.AmzPromotionsCouponMsg;
import com.bizark.op.api.request.msg.send.AmzPromotionsCouponsSendMsg;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.promotions.*;
import com.bizark.op.api.vo.promotions.AmzPromotionsCouponDetailFromDb;
import com.bizark.op.api.vo.promotions.AmzPromotionsCouponDetailVO;
import com.bizark.op.api.vo.promotions.AmzPromotionsCouponSkuInfoVO;
import com.bizark.op.common.util.DateUtil;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.service.mapper.account.AccountMapper;
import com.bizark.op.service.mapper.promotions.*;
import com.bizark.op.service.mapper.seller.AmzCouponCampaignMapper;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 优惠券管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-13
 */
@Service
@Slf4j
public class AmzPromotionsCouponServiceImpl extends ServiceImpl<AmzPromotionsCouponMapper, AmzPromotionsCoupon> implements IAmzPromotionsCouponService {

    private static Logger logger = LoggerFactory.getLogger(AmzPromotionsCouponServiceImpl.class);

    @Autowired
    private AmzPromotionsCouponMapper amzPromotionsCouponMapper;

    @Autowired
    private AccountService accountService;

    @Autowired
    private AmzPromotionsCouponOperateLogMapper amzPromotionsCouponOperateLogMapper;

    @Autowired
    private AmzPromotionsCouponsMapper amzPromotionsCouponsMapper;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private AmzPromotionsMapper amzPromotionsMapper;

    @Autowired
    private AccountMapper accountMapper;

    @Autowired
    private AmzCouponsDailyDetailMapper amzCouponsDailyDetailMapper;

    @Autowired
    private AmzCouponCampaignMapper amzCouponCampaignMapper;
    @Autowired
    private AmzPromotionsCouponsHisMapper amzPromotionsCouponsHisMapper;

    @Autowired
    private IAmzPromotionAndCouponService amzPromotionAndCouponService;

    @Autowired
    private IAmzPromotionApprovalAdviceService iAmzPromotionApprovalAdviceService;

    @Autowired
    private IAmzPromotionAccountService amzPromotionAccountService;

    @Autowired
    @Lazy
    private IAmzPromotionsCouponsService iAmzPromotionsCouponsService;


    /**
     * 查询优惠券管理
     *
     * @param id 优惠券管理ID
     * @return 优惠券管理
     */
    @Override
    public AmzPromotionsCoupon selectAmzPromotionsCouponById(Long id) {
        return amzPromotionsCouponMapper.selectAmzPromotionsCouponById(id);
    }

    /**
     * 查询优惠券管理列表
     *
     * @param amzPromotionsCoupon 优惠券管理
     * @return 优惠券管理
     */
    @Override
    public List<AmzPromotionsCoupon> selectAmzPromotionsCouponList(AmzPromotionsCoupon amzPromotionsCoupon) {

        return amzPromotionsCouponMapper.selectAmzPromotionsCouponList(amzPromotionsCoupon);
    }

    /**
     * 新增优惠券管理
     *
     * @param amzPromotionsCoupon 优惠券管理
     * @return 结果
     */
    @Override
    public int insertAmzPromotionsCoupon(AmzPromotionsCoupon amzPromotionsCoupon) {
        return amzPromotionsCouponMapper.insertAmzPromotionsCoupon(amzPromotionsCoupon);
    }

    /**
     * 修改优惠券管理
     *
     * @param amzPromotionsCoupon 优惠券管理
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAmzPromotionsCoupon(AmzPromotionsCoupon amzPromotionsCoupon) {
        return amzPromotionsCouponMapper.updateAmzPromotionsCoupon(amzPromotionsCoupon);
    }


    /**
     * 批量删除优惠券管理
     *
     * @param ids 需要删除的优惠券管理ID
     * @return 结果
     */
    @Override
    public int deleteAmzPromotionsCouponByIds(Long[] ids) {
        return amzPromotionsCouponMapper.deleteAmzPromotionsCouponByIds(ids);
    }

    /**
     * 删除优惠券管理信息
     *
     * @param id             优惠券管理ID
     * @param authUserEntity
     * @return 结果
     */
    @Override
    public void logicRemoveAmzPromotionsCouponById(Long id, UserEntity authUserEntity) {
        AmzPromotionsCoupon amzPromotionsCoupon = amzPromotionsCouponMapper.selectAmzPromotionsCouponById(id);
        amzPromotionsCoupon.setDisabledBy(authUserEntity.getId());
        amzPromotionsCoupon.setDisabledName(authUserEntity.getName());
        amzPromotionsCoupon.setDisabledAt(DateUtils.getNowDate());
        amzPromotionsCouponMapper.logicRemoveAmzPromotionsCouponById(amzPromotionsCoupon);
    }

    /**
     //     * 删除优惠券管理信息
     //     *
     //     * @param id 优惠券管理ID
     //     * @return 结果
     //     */
//    @Override
//    public int deleteAmzPromotionsCouponById(Long id)
//    {
//    //        return amzPromotionsCouponMapper.deleteAmzPromotionsCouponById(id);
//    }

    /**
     * 查询优惠券详情
     *
     * @param id
     * @return
     */
    @Override
    public AmzPromotionsCouponDetailVO selectAmzPromotionsCouponDetailById(Long id) {

        AmzPromotionsCouponDetailFromDb detail = amzPromotionsCouponMapper.selectAmzPromotionsCouponDetailById(id);
        AmzPromotionsCouponDetailVO vo = new AmzPromotionsCouponDetailVO();
        if (detail != null) {
            BeanUtils.copyProperties(detail, vo);
            if (vo.getAmountSpent() != null && vo.getBudget() != null && vo.getBudget().compareTo(BigDecimal.ZERO) != 0) {
                vo.setAmountSpentRatio(vo.getAmountSpent().divide(
                        vo.getBudget(), 4, RoundingMode.HALF_UP
                ).multiply(new BigDecimal(100)));
            }
            if (vo.getConversionNum() != null && vo.getGetNum() != null && vo.getGetNum().compareTo(0L) != 0) {
                vo.setConversionRatio(BigDecimal.valueOf(vo.getConversionNum()).divide(BigDecimal.valueOf(vo.getGetNum()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
            }
            if (StringUtils.isNotEmpty(detail.getDiscountFlag()) && StringUtils.isNotEmpty(detail.getDiscount())) {
                if (detail.getDiscountFlag().equalsIgnoreCase("%")) {
                    vo.setDiscount(detail.getDiscount() + "" + detail.getDiscountFlag());
                } else {
                    vo.setDiscount(detail.getDiscountFlag() + "" + detail.getDiscount());
                }
            }
            if (detail.getCouponState() != null) {
                vo.setCouponState(AmzCouponStateEnum.labelOf(detail.getCouponState()).replaceAll("\\(SC\\)", "").replaceAll("\\(VC\\)", ""));
            }
            Date beginDate = detail.getBeginDate();
            Date endDate = detail.getEndDate();
            StringBuilder s = new StringBuilder();
            if (beginDate != null) {
                String beginDateString = DateUtils.formatDate(beginDate, "yyyy-MM-dd");
                s.append(beginDateString);
            }
            s.append(" ~ ");
            if (endDate != null) {
                String endDateString = DateUtils.formatDate(endDate, "yyyy-MM-dd");
                s.append(endDateString);
            }
            vo.setPromotionDate(s.toString());
            if (detail.getTargeting() != null) {
                vo.setTargeting(AmzCouponTargetEnum.labelOf(detail.getTargeting()));
            }
            //VC的coupon 用兑换数乘以售价（最新vc-df订单该asin的价格）计算。SC的coupon从后台获取显示
            //TODO 销售额, 优惠券显示名称
            if (vo.getChannel() != null) {
                if (AmzPromotionsChannelEnum.SC.value() == vo.getChannel()) {
                    vo.setCouponName(detail.getCouponTitle());
                } else if (AmzPromotionsChannelEnum.VC.value() == vo.getChannel()) {
                    vo.setCouponName(detail.getCouponName());
                    String asin = vo.getSkuDetails().getAsin();
                    //TODO 查询销售额
                } else {

                }
            }
            vo.setRevenue(null);
            AmzPromotionsCouponSkuInfoVO skuDetails = vo.getSkuDetails();
            if (skuDetails != null) {
                if (StringUtils.isNotEmpty(skuDetails.getSellerSku())) {
                    List<AmzPromotionsCouponSkuInfoVO> skuInfoVOS = amzPromotionAccountService.selectSellerSkuByShopId(vo.getShopId(), new String[]{skuDetails.getSellerSku()});
                    if (CollectionUtil.isNotEmpty(skuInfoVOS)) {
                        skuDetails.setTitle(skuInfoVOS.get(0).getTitle());
                        skuDetails.setPrice(skuInfoVOS.get(0).getPrice());
                    }
                }
                AmzPromotionsCouponSkuInfoVO amzPromotionsCouponSkuInfoVO = new AmzPromotionsCouponSkuInfoVO();
                BeanUtils.copyProperties(skuDetails, amzPromotionsCouponSkuInfoVO);
                //TODO asin的库存
                amzPromotionsCouponSkuInfoVO.setStock(null);
                if (vo.getShopId() != null) {
                    amzPromotionsCouponSkuInfoVO.setShopId(vo.getShopId());
                    AccountWithCountry accountWithCountry = amzPromotionsMapper.selectAccountWithCountry(vo.getShopId());
                    amzPromotionsCouponSkuInfoVO.setCountry(accountWithCountry.getNameCn());
                }
                vo.setSkuDetails(amzPromotionsCouponSkuInfoVO);
            }
            if (detail.getCouponType() != null) {
                vo.setCouponType(AmzCouponTypeEnum.labelOf(detail.getCouponType()));
            }
        }

        return vo;
    }


    /**
     * 通过优惠券标题查询
     *
     * @param couponTitle
     * @return
     */
    public AmzPromotionsCoupon selectAmzPromotionsCouponByCouponTitle(String couponTitle) {
        return amzPromotionsCouponMapper.selectAmzPromotionsCouponByCouponTitle(couponTitle, null);
    }


    /**
     * 通过优惠券名称查询优惠券
     *
     * @param couponName
     * @return
     */
    public AmzPromotionsCoupon selectAmzPromotionsCouponByCouponName(String couponName) {
        return amzPromotionsCouponMapper.selectAmzPromotionsCouponByCouponName(couponName, null);
    }

    /**
     * 通过优惠券状态查询优惠券
     *
     * @param couponState
     * @return
     */
    public List<AmzPromotionsCoupon> selectCouponByCouponState(Integer[] couponState) {
        return amzPromotionsCouponMapper.selectCouponByCouponState(couponState, null, null, null,null);
    }


    /**
     * 取消coupon优惠券
     *
     * @param id          优惠券id
     * @param createdName 操作者
     * @param couponName  优惠券名称
     * @param remark
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int cancelCouponById(Long id, String createdName, String couponName, Integer contextId, Integer updatedBy, String remark) {

        Date operateAt = DateUtils.getNowDate();
        AmzPromotionsCoupon coupon = new AmzPromotionsCoupon();
        coupon.setId(id);
        AmzPromotionsCoupon byId = amzPromotionsCouponMapper.selectAmzPromotionsCouponById(id);
        if (AmzCouponStateEnum.CANCELING.value().equals(byId.getCouponState()) || AmzCouponStateEnum.CANCELED.value().equals(byId.getCouponState())) {
            throw new ErpCommonException("优惠券正在取消或已取消");
        }
        Long couponsId = byId.getCouponId();
        if (StringUtil.isEmpty(couponName)) {
            couponName = byId.getCouponName();
        }
        //更新优惠券
        coupon.setCouponState(AmzCouponStateEnum.CANCELING.value());
        coupon.setUpdatedBy(updatedBy);
        coupon.setUpdatedName(createdName);
        coupon.setUpdatedAt(operateAt);
        coupon.setApprovalStatus(AmzPromotionApprovalEnum.APPROVED.getLabel());
        coupon.setRemark(remark);
        int result = amzPromotionsCouponMapper.updateById(coupon);
        AmzPromotionsCoupons couponsById = amzPromotionsCouponsMapper.selectAmzPromotionsCouponsById(byId.getCouponId());
        if (couponsById != null && couponsById.getChannel() != null) {
            //vc渠道下更新该优惠券对应的活动状态为active含异常，sc更新操作时间
            AmzPromotionsCoupons amzPromotionsCoupons = new AmzPromotionsCoupons();
            amzPromotionsCoupons.setId(couponsId);
            amzPromotionsCoupons.setUpdatedBy(updatedBy);
            amzPromotionsCoupons.setUpdatedName(createdName);
            amzPromotionsCoupons.setUpdatedAt(operateAt);
            amzPromotionsCoupons.setApprovalStatus(AmzPromotionApprovalEnum.APPROVED.getLabel());
            if (AmzPromotionsChannelEnum.VC.value().equals(couponsById.getChannel())) {
                amzPromotionsCoupons.setActiveState(AmzCouponActiveStateEnum.ACTIVE_HAS_EXCEPTION.value());
            }
            amzPromotionsCouponsMapper.updateById(amzPromotionsCoupons);
        } else {
            throw new ErpCommonException("活动为空或者渠道为空，取消优惠券失败");
        }
        //写入日志
        Integer operateType = AmzCouponOperateTypeEnum.CANCEL_COUPON.value();
        StringBuilder detail = new StringBuilder();
        detail.append("取消优惠券 ").append(couponName);
        int couponOperateLogResult = insertCouponOperateLog(couponsId, id, updatedBy, createdName, operateAt, operateType, null, detail, contextId);
        insertCouponOperateLog(couponsId, null, updatedBy, createdName, operateAt, operateType, null, detail, contextId);
        updateExpiringSoon(coupon.getId());
        //发送消息
        cancelCouponCouponMessageSend(couponsId, coupon, 4);
        if (couponOperateLogResult + result > 0) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * @param couponsId   活动id
     * @param couponId    优惠券id
     * @param updateBy    操作人id
     * @param createdName 操作人
     * @param operateAt   操作时间
     * @param operateType 操作类型
     * @param result      结果
     * @param detail      详情
     * @param contextId   组织id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertCouponOperateLog(Long couponsId, Long couponId, Integer updateBy, String createdName, Date operateAt, Integer operateType, String result, StringBuilder detail, Integer contextId) {
        AmzPromotionsCouponOperateLog amzPromotionsCouponOperateLog = new AmzPromotionsCouponOperateLog();
        amzPromotionsCouponOperateLog.setCouponsId(couponsId);
        amzPromotionsCouponOperateLog.setCouponId(couponId);
        amzPromotionsCouponOperateLog.setCreatedBy(updateBy);
        amzPromotionsCouponOperateLog.setCreatedAt(operateAt);
        amzPromotionsCouponOperateLog.setCreatedName(createdName);
        amzPromotionsCouponOperateLog.setOperateType(operateType);
        amzPromotionsCouponOperateLog.setResult(result);
        amzPromotionsCouponOperateLog.setOrganizationId(contextId);
        amzPromotionsCouponOperateLog.setDetail(detail.toString());
        int couponOperateLogResult = amzPromotionsCouponOperateLogMapper.insertAmzPromotionsCouponOperateLog(amzPromotionsCouponOperateLog);
        return couponOperateLogResult;
    }


    /**
     * 优惠券状态为提交异常，支持直接提交。
     *
     * @param id          优惠券id
     * @param createdName 操作者
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int submitExceptionSubmit(Long id, String createdName, Integer contextId, Integer updatedBy) {

        Date operateAt = DateUtils.getNowDate();
        AmzPromotionsCoupon amzPromotionsCoupon = amzPromotionsCouponMapper.selectAmzPromotionsCouponById(id);
        if (amzPromotionsCoupon == null) {
            return 0;
        }
        if (amzPromotionsCoupon.getCouponState() != null && AmzCouponStateEnum.SUBMITTING.value().equals(amzPromotionsCoupon.getCouponState())) {
            throw new ErpCommonException("提交异常直接提交操作正在进行中，请勿重复提交");
        }
        if (cn.hutool.core.date.DateUtil.compare(DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", amzPromotionsCoupon.getBeginDate()), "yyyy-MM-dd HH:mm:ss"),
                com.bizark.op.common.util.DateUtil.UtcToPacificDate(operateAt)) <= 0) {
            throw new ErpCommonException("优惠券开始时间需大于当前站点时间");
        }
        AmzPromotionsCoupon coupon = new AmzPromotionsCoupon();
        coupon.setId(id);
        coupon.setCouponState(AmzCouponStateEnum.SUBMITTING.value());
        coupon.setUpdatedName(createdName);
        coupon.setUpdatedBy(updatedBy);
        coupon.setUpdatedAt(operateAt);
        coupon.setApprovalStatus(AmzPromotionApprovalEnum.APPROVED.getLabel());
        int result = amzPromotionsCouponMapper.updateAmzPromotionsCoupon(coupon);
        AmzPromotionsCoupons promotionsCoupons = new AmzPromotionsCoupons();
        promotionsCoupons.setId(amzPromotionsCoupon.getCouponId());
        promotionsCoupons.setUpdatedName(createdName);
        promotionsCoupons.setUpdatedBy(updatedBy);
        promotionsCoupons.setUpdatedAt(operateAt);
        promotionsCoupons.setApprovalStatus(AmzPromotionApprovalEnum.APPROVED.getLabel());
        amzPromotionsCouponsMapper.updateAmzPromotionsCoupons(promotionsCoupons);
        //写入日志
        Integer operateType = AmzCouponOperateTypeEnum.SUBMIT_EXCEPTION_DIRECT_SUBMIT.value();
        StringBuilder detail = new StringBuilder();
        detail.append("优惠券名称：").append(amzPromotionsCoupon.getCouponName())
                .append("，时间：").append(DateUtils.formatDate(amzPromotionsCoupon.getBeginDate(), "yyyy-MM-dd")).append(" 至 ")
                .append(DateUtils.formatDate(amzPromotionsCoupon.getEndDate(), "yyyy-MM-dd")).append("，预算：")
                .append(amzPromotionsCoupon.getFlag()).append(amzPromotionsCoupon.getBudget());
        int couponOperateLogResult = insertCouponOperateLog(amzPromotionsCoupon.getCouponId(), id, updatedBy, createdName, operateAt, operateType, null, detail, contextId);
        if (couponOperateLogResult + result > 0) {
            coupon.setCouponName(amzPromotionsCoupon.getCouponName());
            updateExpiringSoon(id);
            submitExceptionSubmitCouponMessageSend(id);
            return 1;
        } else {
            return 0;
        }
    }


    /***
     * SC渠道优惠券状态为Running、修改异常.   修改优惠券
     * @param id 优惠券id
     * @param createdName 操作者
     * @param budget 预算
     * @param endDate 结束时间
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void runningOrModifyExceptionUpdate(Long id, String createdName, String budget, String endDate, Integer contextId, Integer updatedBy) {

        Long result = this.processRunningOrModifyExceptionUpdate(id, createdName, budget, endDate, contextId, updatedBy);
        iAmzPromotionsCouponsService.startApprovalProcess(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long processRunningOrModifyExceptionUpdate(Long id, String createdName, String budget, String endDate, Integer contextId, Integer updatedBy) {

        Date operateAt = DateUtils.getNowDate();
        AmzPromotionsCoupon amzPromotionsCoupon = amzPromotionsCouponMapper.selectAmzPromotionsCouponById(id);
        if (amzPromotionsCoupon == null) {
            throw new ErpCommonException("数据库无此优惠券");
        }
        //设置缓存，存储原有数据
        setCacheKey(amzPromotionsCoupon.getCouponId(), amzPromotionsCoupon, 6);

        BigDecimal beforeUpdateBudget = amzPromotionsCoupon.getBudget();
        Date beforeUpdateEndDate = amzPromotionsCoupon.getEndDate();
        if (DateUtil.compareDate(DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-MM-dd", beforeUpdateEndDate), "yyyy-MM-dd"), DateUtils.parseDate(endDate, "yyyy-MM-dd")) > 0) {
            throw new ErpCommonException("结束日期必须大于等于原始结束日期");
        }
        AmzPromotionsCoupon coupon = new AmzPromotionsCoupon();
        coupon.setId(id);
        coupon.setApprovalStatus(AmzPromotionApprovalEnum.APPROVING.getLabel());
        if (StringUtil.isNotEmpty(budget)) {
            coupon.setBudget(new BigDecimal(budget));
        }
        if (StringUtil.isNotEmpty(endDate)) {
            coupon.setEndDate(DateUtils.parseDate(endDate, "yyyy-MM-dd"));
        }
        coupon.setUpdatedBy(updatedBy);
        coupon.setUpdatedName(createdName);
        coupon.setUpdatedAt(operateAt);
        amzPromotionsCouponMapper.updateAmzPromotionsCoupon(coupon);
        AmzPromotionsCoupons promotionsCoupons = new AmzPromotionsCoupons();
        promotionsCoupons.setId(amzPromotionsCoupon.getCouponId());
        promotionsCoupons.setUpdatedBy(updatedBy);
        promotionsCoupons.setUpdatedName(createdName);
        promotionsCoupons.setUpdatedAt(operateAt);
        promotionsCoupons.setApprovalStatus(AmzPromotionApprovalEnum.APPROVING.getLabel());
        amzPromotionsCouponsMapper.updateAmzPromotionsCoupons(promotionsCoupons);
        //写入日志
        Integer operateType = AmzCouponOperateTypeEnum.MODIFY_COUPON.value();
        StringBuilder detail = new StringBuilder();
        boolean modify = false;
        if (beforeUpdateBudget != null && beforeUpdateBudget.compareTo(new BigDecimal(budget)) != 0) {
            detail.append("budget：").append(beforeUpdateBudget).append("修改为 ").append(budget).append(";");
            modify = true;
        }
        if (beforeUpdateEndDate != null && DateUtil.compareDate(beforeUpdateEndDate, DateUtils.parseDate(endDate, "yyyy-MM-dd")) != 0) {
            String beforeUpdateEndDateString = DateUtils.parseDateToStr("yyyy-MM-dd", beforeUpdateEndDate);
            detail.append("结束日期：").append(beforeUpdateEndDateString).append("修改为 ").append(endDate).append(";");
            modify = true;
        }
        if (!modify) {
            throw new ErpCommonException("数据无变化");
        }
        insertCouponOperateLog(amzPromotionsCoupon.getCouponId(), id, updatedBy, createdName, operateAt, operateType, null, detail, contextId);
        updateExpiringSoon(id);

        try {
            iAmzPromotionApprovalAdviceService.couponApprovalAdviceWx(id, operateAt, createdName,updatedBy);
        } catch (Exception e) {
            log.error("发送企业微信消息失败:{}", e.getMessage());
        }
        return promotionsCoupons.getId();
    }

    /**
     * 提交异常直接提交消息发送
     *
     * @param couponId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int submitExceptionSubmitCouponMessageSend(Long couponId) {

        if (couponId == null) {
            return 0;
        }
        AmzPromotionsCoupon amzPromotionsCoupon = amzPromotionsCouponMapper.selectAmzPromotionsCouponById(couponId);
        if (amzPromotionsCoupon == null) {
            return 0;
        }
        Long couponsId = amzPromotionsCoupon.getCouponId();
        AmzPromotionsCoupons amzPromotionsCoupons = amzPromotionsCouponsMapper.selectAmzPromotionsCouponsById(couponsId);
        if (amzPromotionsCoupons == null) {
            return 0;
        }
        AmzPromotionsCouponsSendMsg msg = new AmzPromotionsCouponsSendMsg();
        if (amzPromotionsCoupon.getShopId() != null) {
            Account account = accountMapper.selectById(amzPromotionsCoupon.getShopId());
            if (account != null && StringUtils.isNotEmpty(account.getStoreName())) {
                msg.setWebStoreName(account.getStoreName());
            } else {
                throw new ErpCommonException("浏览器店铺名为空");
            }
        } else {
            throw new ErpCommonException("店铺id为空");
        }
        msg.setCampaignId(amzPromotionsCoupon.getCouponId());
        msg.setChannel(amzPromotionsCoupons.getChannel());
        msg.setType(amzPromotionsCoupons.getUploadType());
        msg.setRpaType(1);
        msg.setStoreName(amzPromotionsCoupons.getShopName());
        msg.setShopId(amzPromotionsCoupons.getShopId());
        msg.setTargeting(amzPromotionsCoupons.getTargeting());
        msg.setFlag(amzPromotionsCoupons.getFlag());
        msg.setVendorCode(amzPromotionsCoupons.getVendorCode());
        List<AmzPromotionsCouponMsg> list = new ArrayList<>();
        AmzPromotionsCouponMsg couponMsg = new AmzPromotionsCouponMsg();
        couponMsg.setCouponId(amzPromotionsCoupon.getId());
        couponMsg.setCouponName(amzPromotionsCoupon.getCouponName());
//        couponMsg.setCouponState(AmzCouponStateEnum.labelOf(amzPromotionsCoupon.getCouponState()).replaceAll("\\(VC\\)", "").replaceAll("\\(SC\\)", ""));
        couponMsg.setCouponDiscount(amzPromotionsCoupon.getDiscount());
        couponMsg.setCouponDiscountFlag(amzPromotionsCoupon.getDiscountFlag());
        couponMsg.setConversion(amzPromotionsCoupon.getConversion());
        couponMsg.setTargeting(amzPromotionsCoupons.getTargeting());
        couponMsg.setBudget(amzPromotionsCoupon.getBudget() != null ? amzPromotionsCoupon.getBudget().toString() : null);
        couponMsg.setAsin(amzPromotionsCoupon.getAsin());
        String beginDate = "";
        String endDate = "";
        if (amzPromotionsCoupon.getBeginDate() != null) {
            beginDate = DateUtils.parseDateToStr("yyyy-MM-dd", amzPromotionsCoupons.getBeginDate());
            couponMsg.setCouponBeginDate(beginDate);
        }
        if (amzPromotionsCoupon.getEndDate() != null) {
            endDate = DateUtils.parseDateToStr("yyyy-MM-dd", amzPromotionsCoupons.getEndDate());
            couponMsg.setCouponEndDate(endDate);
        }
        list.add(couponMsg);
        msg.setCouponList(list);
        JSONObject object = JSONObject.parseObject(JSONObject.toJSONString(msg));
        log.info("提交异常直接提交消息发送:{}", JSONObject.toJSONString(msg));
        rabbitTemplate.convertAndSend(MQDefine.COUPON_OR_COUPONS_EXCHANGE, MQDefine.COUPON_CREATE_OR_UPDATE_SENG_ROUTING, object);
        return 1;
    }

    /**
     * 优惠券状态为提交异常直接提交,或者  Running、修改异常 消息发送
     */
    public void runningOrModifyExceptionUpdateCouponMessageSend(Long couponsId, AmzPromotionsCoupon coupon, Integer type) {

        AmzPromotionsCouponsSendMsg msg = new AmzPromotionsCouponsSendMsg();
        msg.setCampaignId(couponsId);
        AmzPromotionsCoupons amzPromotionsCoupons = amzPromotionsCouponsMapper.selectAmzPromotionsCouponsById(couponsId);
        if (amzPromotionsCoupons != null) {
            msg.setCampaignName(amzPromotionsCoupons.getPromotionName());
            msg.setCampaignState(AmzCouponActiveStateEnum.labelOf(amzPromotionsCoupons.getActiveState()).replaceAll("\\(VC\\)", "").replaceAll("\\(SC\\)", ""));
            msg.setChannel(amzPromotionsCoupons.getChannel());
            msg.setRpaType(2);
            msg.setVendorCode(amzPromotionsCoupons.getVendorCode());
        }
        List<AmzPromotionsCouponMsg> list = new ArrayList<>();
        AmzPromotionsCouponMsg couponMsg = new AmzPromotionsCouponMsg();
        couponMsg.setCouponId(coupon.getId());
        couponMsg.setCouponName(coupon.getCouponName());
//        couponMsg.setCouponState(AmzCouponStateEnum.labelOf(coupon.getCouponState()).replaceAll("\\(VC\\)", "").replaceAll("\\(SC\\)", ""));
        if (type == 2) {
            if (coupon.getBudget() != null) {
                couponMsg.setBudget(coupon.getBudget().toString());
            }
            if (coupon.getEndDate() != null) {
                couponMsg.setCouponEndDate(DateUtils.parseDateToStr("yyyy-MM-dd", coupon.getEndDate()));
            }
        }
        list.add(couponMsg);
        msg.setCouponList(list);
        JSONObject object = JSONObject.parseObject(JSONObject.toJSONString(msg));
        log.info("优惠券状态为提交异常直接提交,或者  Running、修改异常 消息发送:{}", JSONObject.toJSONString(msg));
        rabbitTemplate.convertAndSend(MQDefine.COUPON_OR_COUPONS_EXCHANGE, MQDefine.COUPON_CREATE_OR_UPDATE_SENG_ROUTING, object);
    }


    /**
     * 取消coupon消息发送
     *
     * @param couponsId
     * @param coupon
     * @param type
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelCouponCouponMessageSend(Long couponsId, AmzPromotionsCoupon c, Integer type) {

        AmzPromotionsCouponsSendMsg msg = new AmzPromotionsCouponsSendMsg();
        msg.setCampaignId(couponsId);
        msg.setCancelCoupon(true);
        msg.setCancelCampaign(false);
        AmzPromotionsCoupons amzPromotionsCoupons = amzPromotionsCouponsMapper.selectAmzPromotionsCouponsById(couponsId);
        AmzPromotionsCoupon coupon = amzPromotionsCouponMapper.selectAmzPromotionsCouponById(c.getId());
        if (amzPromotionsCoupons != null) {
            if (AmzPromotionsChannelEnum.VC.value() == amzPromotionsCoupons.getChannel()) {
                msg.setCampaignName(amzPromotionsCoupons.getPromotionName());
                msg.setCampaignState(AmzCouponActiveStateEnum.labelOf(amzPromotionsCoupons.getActiveState()).replaceAll("\\(VC\\)", "").replaceAll("\\(SC\\)", ""));
                msg.setVendorCode(amzPromotionsCoupons.getVendorCode());
            }
            msg.setChannel(amzPromotionsCoupons.getChannel());
            msg.setRpaType(type);
            msg.setBackId(amzPromotionsCoupons.getCampaignId());
            if (amzPromotionsCoupons.getShopId() != null) {
                msg.setShopId(amzPromotionsCoupons.getShopId());
                Account account = accountMapper.selectById(amzPromotionsCoupons.getShopId());
                if (account != null && StringUtils.isNotEmpty(account.getStoreName())) {
                    msg.setWebStoreName(account.getStoreName());
                } else {
                    throw new ErpCommonException("浏览器店铺名为空");
                }
            } else {
                throw new ErpCommonException("店铺id为空");
            }
        }
        List<AmzPromotionsCouponMsg> list = new ArrayList<>();
        AmzPromotionsCouponMsg couponMsg = new AmzPromotionsCouponMsg();
        couponMsg.setCouponId(coupon.getId());
        couponMsg.setCouponName(coupon.getCouponName());
        couponMsg.setBackId(coupon.getBackId());
//        couponMsg.setCouponState(AmzCouponStateEnum.labelOf(coupon.getCouponState()).replaceAll("\\(VC\\)", "").replaceAll("\\(SC\\)", ""));
        list.add(couponMsg);
        msg.setCouponList(list);
        JSONObject object = JSONObject.parseObject(JSONObject.toJSONString(msg));
        log.info("取消coupon消息发送:{}", JSONObject.toJSONString(msg));
        rabbitTemplate.convertAndSend(MQDefine.COUPON_OR_COUPONS_EXCHANGE, MQDefine.COUPON_CREATE_OR_UPDATE_SENG_ROUTING, object);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateExpiringSoon() {
        List<AmzPromotionsCoupon> amzPromotionsCoupons = amzPromotionsCouponMapper.selectAmzPromotionsCouponList(new AmzPromotionsCoupon());
        if (CollectionUtil.isEmpty(amzPromotionsCoupons)) {
            return;
        }
        for (AmzPromotionsCoupon amzPromotionsCoupon : amzPromotionsCoupons) {
            updateExpiringSoon(amzPromotionsCoupon.getId());
        }
    }

    /**
     * 更新coupon的ExpiringSoon
     *
     * @param couponId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateExpiringSoon(Long couponId) {
        if (couponId == null) {
            throw new ErpCommonException("优惠券Id为空，更新ExpiringSoon失败");
        }
        AmzPromotionsCoupon coupon = amzPromotionsCouponMapper.selectAmzPromotionsCouponById(couponId);
        if (coupon == null) {
            throw new ErpCommonException("优惠券为空，更新ExpiringSoon失败");
        }
        Boolean expiringSoon = false;
        AmzPromotionsCoupon coupon1 = new AmzPromotionsCoupon();
        coupon1.setId(coupon.getId());
        List<Integer> stateList = new ArrayList<>();
        stateList.add(AmzCouponStateEnum.SUBMITTING.value());
        stateList.add(AmzCouponStateEnum.SUBMIT_EXCEPTION.value());
        stateList.add(AmzCouponStateEnum.CANCELED.value());
        stateList.add(AmzCouponStateEnum.EXPIRED.value());
        stateList.add(AmzCouponStateEnum.COMPLETE_VC.value());
        stateList.add(AmzCouponStateEnum.EXPIRED.value());
        BigDecimal amountSpentRatio = coupon.getAmountSpentRatio();
        Boolean amountSpentRatioFlag = false;
        if (amountSpentRatio != null && amountSpentRatio.compareTo(new BigDecimal("75")) > 0) {
            amountSpentRatioFlag = true;
        }
        Date endDate = coupon.getEndDate();
        //结束日期与当前日期差值是否小于等于3
        Boolean endDateFlag = false;
        //结束日期是否大于当前日期
        Boolean endDateExpiring = false;
        if (endDate != null) {
            Date nowDate = DateUtils.getNowDate();
            int betweenDayNumber = DateUtil.getBetweenDayNumber(nowDate, endDate);
            endDateExpiring = endDate.compareTo(nowDate) >= 0;
            endDateFlag = betweenDayNumber <= 3;
        }
        if (!stateList.contains(coupon.getCouponState()) && endDateExpiring && (amountSpentRatioFlag || endDateFlag)) {
            expiringSoon = true;
        }
        coupon1.setExpiringSoon(expiringSoon);
        return coupon1.getExpiringSoon() != coupon.getExpiringSoon() ? amzPromotionsCouponMapper.updateAmzPromotionsCoupon(coupon1) : 0;

    }

    /**
     * @param amzCoupon  待同步的amz_coupon
     * @param couponsId  该 amz_promotions_coupon所属coupon活动(amz_promotions_coupons)的主键id
     * @param campaignId amz_coupon_campaign主键id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int syncSaveCoupon(AmzCoupon amzCoupon, Long couponsId, Date nowDate, Long campaignId) {
        AmzCouponCampaign amzCouponCampaign = amzCouponCampaignMapper.selectById(campaignId);
        int result = 0;
        int syncFlag = 0;
        AmzPromotionsCoupon tempCoupon = new AmzPromotionsCoupon();
        tempCoupon.setShopId(amzCoupon.getShopId());
        tempCoupon.setOrganizationId(amzCoupon.getOrganizationId());
        tempCoupon.setCouponPageId(amzCoupon.getCouponId());
        tempCoupon.setAsin(amzCoupon.getAsin());
        List<AmzPromotionsCoupon> byId = amzPromotionsCouponMapper.selectAmzPromotionsCouponList(tempCoupon);
        AmzPromotionsCoupon coupon = new AmzPromotionsCoupon();

        coupon.setCouponName(amzCoupon.getCouponName());
        coupon.setCouponTitle(amzCoupon.getCouponName());
        coupon.setAsin(amzCoupon.getAsin());
        coupon.setGetNum(amzCoupon.getClips() != null ? Long.valueOf(amzCoupon.getClips()) : null);
        coupon.setSellerSku(getSkuByAsin(amzCoupon.getAsin(), amzCoupon.getShopId(), amzCoupon.getOrganizationId()));
        coupon.setConversion("true".equalsIgnoreCase(amzCoupon.getIsOncePerCustomer()));
        coupon.setDiscount(amzCoupon.getDiscountAmount() != null ? String.valueOf(amzCoupon.getDiscountAmount()) : null);
        coupon.setBudget(amzCoupon.getBudget());
        coupon.setAmountSpent(amzCoupon.getBudgetSpent());
        coupon.setAmountSpentRatio(amzCoupon.getBudgetUsed());
        coupon.setBudgetRemaining(amzCoupon.getBudgetRemaining());
        coupon.setConversionNum(amzCoupon.getRedemptions() != null ? Long.valueOf(amzCoupon.getRedemptions()) : null);
        coupon.setAmzFrontName(amzCoupon.getWebsiteMessage());
        coupon.setWebsiteDisplayName(amzCoupon.getWebsiteMessage());
//        coupon.setBeginDate(StringUtil.isNotEmpty(amzCoupon.getStartDate()) ? DateUtils.parseDate(convertAmzDate(amzCoupon.getStartDate()), "yyyy-MM-dd HH:mm:ss") : null);
//        coupon.setEndDate(StringUtil.isNotEmpty(amzCoupon.getStartDate()) ? DateUtils.parseDate(convertAmzDate(amzCoupon.getEndDate()), "yyyy-MM-dd HH:mm:ss") : null);
        coupon.setConversion("true".equalsIgnoreCase(amzCoupon.getIsOncePerCustomer()));
        coupon.setDiscountFlag(StringUtil.isEmpty(amzCoupon.getDiscountType()) ? null : ("AMOUNT_OFF_LIST_PRICE".equalsIgnoreCase(amzCoupon.getDiscountType()) ? "$" : "%"));
        coupon.setFlag("$");
        coupon.setShopId(amzCoupon.getShopId());
        //coupon 活动id
        coupon.setCouponId(couponsId);
        //coupon后台Id
//        coupon.setBackId(amzCoupon.getCouponId());
        coupon.setUpdatedAt(amzCoupon.getUpdatedAt() != null ? amzCoupon.getUpdatedAt() : nowDate);
        coupon.setUpdatedName("System");
        coupon.setUpdatedBy(amzCoupon.getUpdatedBy());
        coupon.setOrganizationId(amzCoupon.getOrganizationId());
        coupon.setTotalDiscount(amzCoupon.getTotalDiscount());
        coupon.setApprovalStatus(AmzPromotionApprovalEnum.APPROVED.getLabel());
       /* coupon.setCreatedAt(StringUtil.isNotEmpty(amzCouponCampaign.getConvertCreatedOn()) ? DateUtil.UtcToPacificDate(DateUtils.parseDate(amzCouponCampaign.getConvertCreatedOn(), "yyyy-MM-dd HH:mm:ss")) : nowDate);
        coupon.setCreatedName("System");
        coupon.setCreatedBy(amzCoupon.getCreatedBy());*/
        if (CollectionUtil.isEmpty(byId)) {
            //根据coupon名，店铺id，组织id查询
            AmzPromotionsCoupon tempCoupon1 = new AmzPromotionsCoupon();
            tempCoupon1.setShopId(amzCoupon.getShopId());
            tempCoupon1.setOrganizationId(amzCoupon.getOrganizationId());
            tempCoupon1.setCouponName(amzCoupon.getCouponName());
            tempCoupon1.setAsin(amzCoupon.getAsin());
            tempCoupon1.setCouponId(couponsId);
            List<AmzPromotionsCoupon> byId1 = amzPromotionsCouponMapper.selectAmzPromotionsCouponList(tempCoupon1);
            if (CollectionUtil.isEmpty(byId1)) {
                coupon.setCreatedAt(StringUtil.isNotEmpty(amzCouponCampaign.getConvertCreatedOn()) ? DateUtil.UtcToPacificDate(DateUtils.parseDate(amzCouponCampaign.getConvertCreatedOn(), "yyyy-MM-dd HH:mm:ss")) : nowDate);
                coupon.setCreatedName("System");
                coupon.setCreatedBy(amzCoupon.getCreatedBy());
                coupon.setBeginDate(StringUtil.isNotEmpty(amzCoupon.getStartDate()) ? DateUtil.UtcToPacificDate(DateUtils.parseDate(convertAmzDate(amzCoupon.getStartDate()), "yyyy-MM-dd HH:mm:ss")) : null);
                coupon.setEndDate(StringUtil.isNotEmpty(amzCoupon.getStartDate()) ? DateUtil.UtcToPacificDate(DateUtils.parseDate(convertAmzDate(amzCoupon.getEndDate()), "yyyy-MM-dd HH:mm:ss")) : null);
                result = amzPromotionsCouponMapper.insertAmzPromotionsCoupon(coupon);
                syncFlag = 1;

            } else {
                coupon.setId(byId1.get(0).getId());
                log.info("同步更新coupon:{}",JSONObject.toJSONString(coupon));
                result = amzPromotionsCouponMapper.updateAmzPromotionsCoupon(coupon);
            }
        } else {
            coupon.setId(byId.get(0).getId());
            result = amzPromotionsCouponMapper.updateAmzPromotionsCoupon(coupon);
        }
        updateExpiringSoon(coupon.getId());
        syncSaveCouponDailyDetails(amzCoupon, coupon.getId(), nowDate);
        return syncFlag;
    }

    @Transactional
    @Override
    public int syncSaveCouponHistory(AmzCoupon amzCoupon, Long couponsId, Date nowDate, Long campaignId) {
        //AmzCouponCampaign amzCouponCampaign = amzCouponCampaignMapper.selectById(campaignId);
        int result = 0;
        AmzPromotionsCoupon tempCoupon = new AmzPromotionsCoupon();
        tempCoupon.setShopId(amzCoupon.getShopId());
        tempCoupon.setOrganizationId(amzCoupon.getOrganizationId());
        tempCoupon.setBackId(amzCoupon.getCouponId());
        tempCoupon.setAsin(amzCoupon.getAsin());
        List<AmzPromotionsCoupon> byId = amzPromotionsCouponMapper.selectAmzPromotionsCouponList(tempCoupon);
       if (!CollectionUtil.isEmpty(byId)){
           syncSaveCouponDailyDetails(amzCoupon, byId.get(0).getId(), nowDate);
           return 0;
       }
       return 1;
    }

    private String convertAmzDate(String amzDate) {
        return StringUtil.isEmpty(amzDate) ? null : amzDate.replace("T", " ").replace("Z", " ");
    }

    public String getSkuByAsin(String asin, Long shopId, Integer orgId) {
        List<ProductChannels> productChannels = amzPromotionsCouponMapper.selectSkuByAsinAndShopIdAndOrgId(asin, shopId, orgId);
        if (CollectionUtil.isEmpty(productChannels)) {
            return null;
        }
        return productChannels.get(0).getSellerSku();
    }

    /**
     * @param amzCoupon amz_coupon
     * @param couponId  amz_promotions_coupon主键id
     * @return
     */
    @Override
    public int syncSaveCouponDailyDetails(AmzCoupon amzCoupon, Long couponId, Date nowDate) {
        CouponDailyDetail couponDailyDetail = new CouponDailyDetail();
        couponDailyDetail.setDate(amzCoupon.getRecordDate());
        couponDailyDetail.setAsin(amzCoupon.getAsin());
        couponDailyDetail.setSku(getSkuByAsin(amzCoupon.getAsin(), amzCoupon.getShopId(), amzCoupon.getOrganizationId()));
        couponDailyDetail.setDailyClips(amzCoupon.getDailyClips());
        couponDailyDetail.setDailyRedemptions(amzCoupon.getDailyRedemptions());
        couponDailyDetail.setDailySpent(amzCoupon.getSpent());
        couponDailyDetail.setOrganizationId(amzCoupon.getOrganizationId());
        couponDailyDetail.setPromotionId(couponId);
        couponDailyDetail.setApportionFlag(amzCoupon.getApportionFlag());
        couponDailyDetail.setBackId(amzCoupon.getCouponId());
        couponDailyDetail.setCouponName(amzCoupon.getCouponName());
        couponDailyDetail.setUpdatedAt(amzCoupon.getUpdatedAt() != null ? amzCoupon.getUpdatedAt() : nowDate);
        couponDailyDetail.setUpdatedName("System");
        couponDailyDetail.setUpdatedBy(amzCoupon.getUpdatedBy());
        CouponDailyDetail temp = new CouponDailyDetail();
        temp.setPromotionId(couponId);
        temp.setAsin(amzCoupon.getAsin());
        temp.setDate(amzCoupon.getRecordDate());
        List<CouponDailyDetail> list = amzCouponsDailyDetailMapper.selectCouponDailyDetailList(temp);
        if (CollectionUtil.isEmpty(list)) {
            couponDailyDetail.setCreatedAt(amzCoupon.getCreatedAt() != null ? amzCoupon.getCreatedAt() : nowDate);
            couponDailyDetail.setCreatedName("System");
            couponDailyDetail.setCreatedBy(amzCoupon.getCreatedBy());
            return amzCouponsDailyDetailMapper.insertCouponDailyDetail(couponDailyDetail);
        }
        couponDailyDetail.setId(list.get(0).getId());
        return amzCouponsDailyDetailMapper.updateCouponDailyDetail(couponDailyDetail);
    }


    /**
     * 修改优惠券时使用
     *
     * @param couponsId  coupon活动id
     * @param c          优惠券
     * @param updateType
     */
    @Transactional(rollbackFor = Exception.class)
    public void setCacheKey(Long couponsId, AmzPromotionsCoupon c, Integer updateType) {

        AmzPromotionsCoupons coupons = amzPromotionsCouponsMapper.selectAmzPromotionsCouponsById(couponsId);
        String activeApprovalTypeKey = AmzPromotionsCouponConstants.COUPON_MODIFY_REDIS_KEY + ":" + c.getId();
        AmzPromotionsCouponsHis activeApprovalTypeKeyObject = amzPromotionsCouponsHisMapper.selectOne(Wrappers.<AmzPromotionsCouponsHis>lambdaQuery().eq(AmzPromotionsCouponsHis::getActiveApprovalTypeKey, activeApprovalTypeKey));
        if (activeApprovalTypeKeyObject != null) {
            amzPromotionsCouponsHisMapper.deleteById(activeApprovalTypeKeyObject.getId());
        }
        AmzPromotionsCouponsHis his = new AmzPromotionsCouponsHis();
        his.setActiveApprovalTypeKey(activeApprovalTypeKey);
        his.setActiveApprovalTypeValue(updateType);
        amzPromotionsCouponsHisMapper.insert(his);
//        SpringUtils.getBean(RedisCache.class).setCacheObject(activeApprovalTypeKey, updateType, 30, TimeUnit.DAYS);
        String activeObjectKey = AmzPromotionsCouponConstants.COUPONS_SAVE_REDIS_KEY + ":" + couponsId;
        AmzPromotionsCouponsHis activeObjectKeyObject = amzPromotionsCouponsHisMapper.selectOne(Wrappers.<AmzPromotionsCouponsHis>lambdaQuery().eq(AmzPromotionsCouponsHis::getActiveObjectKey, activeObjectKey));
        if (activeObjectKeyObject != null) {
            amzPromotionsCouponsHisMapper.deleteById(activeObjectKeyObject.getId());
        }
        AmzPromotionsCouponsHis his2 = new AmzPromotionsCouponsHis();
        BeanUtils.copyProperties(coupons, his2, "id");
        his2.setActivePrimaryKey(coupons.getId());
        his2.setActiveObjectKey(activeObjectKey);
        amzPromotionsCouponsHisMapper.insert(his2);
        String couponListKey = AmzPromotionsCouponConstants.COUPON_SAVE_REDIS_KEY + ":" + c.getId();
        AmzPromotionsCouponsHis couponListKeyObject = amzPromotionsCouponsHisMapper.selectOne(Wrappers.<AmzPromotionsCouponsHis>lambdaQuery().eq(AmzPromotionsCouponsHis::getCouponListKey, couponListKey));
        if (couponListKeyObject != null) {
            amzPromotionsCouponsHisMapper.deleteById(couponListKeyObject.getId());
        }
        AmzPromotionsCouponsHis his3 = new AmzPromotionsCouponsHis();
        BeanUtils.copyProperties(c, his3, "id");
        his3.setCouponListKey(couponListKey);
        amzPromotionsCouponsHisMapper.insert(his3);
    }

    @Override
    public void modifyRemark(Long id, String remark) {
        this.lambdaUpdate().set(AmzPromotionsCoupon::getRemark, remark).eq(AmzPromotionsCoupon::getId, id).update();
    }
}
