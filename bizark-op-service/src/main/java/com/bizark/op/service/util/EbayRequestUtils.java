package com.bizark.op.service.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Base64;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> @ClassName WayfairRequestUtils
 * @description: TODO
 * @date 2024年06月19日
 */

@Component
@Slf4j
public class EbayRequestUtils {


    @Autowired
    private AccountService accountService;


    @Autowired
    private AccessTokenUtils accessTokenUtils;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    /**
     * @param
     * @description: wayfair 操作
     * @author: Moore
     * @date: 2024/6/19 18:24
     * @return: java.lang.String
     **/
    public String sendGetRequest(String requestUrl, Integer shopId) {
        Account account = accountService.getById(shopId);
        if (Objects.isNull(account)) {
            return null;
        }
        HttpRequest get = HttpUtil.createGet(requestUrl);
        get.header("Content-Type", "application/json");
        get.header("X-EBAY-C-MARKETPLACE-ID", "EBAY_US");
//        get.header("authorization", "TOKEN " + accessTokenUtils.getToken(shopId));
        String accessToiken = getAccessToken(account);
        if (StringUtil.isEmpty(accessToiken)){
            return null;
        }
        get.header("authorization", "TOKEN " + accessToiken);
        get.timeout(30 * 1000); //重试时间
        HttpResponse httpResponse = get.execute();
        log.error("Ebay接口请求URL：{},店铺:{},token:{}", requestUrl, shopId,accessToiken);
        if (!httpResponse.isOk()) {
            log.info("EbayGet响应数据异常：{},店铺：{},请求URL：{}",httpResponse, shopId, requestUrl);
            try {
                TimeUnit.SECONDS.sleep(2);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            //二次重试
            httpResponse = get.execute();
            if (!httpResponse.isOk()) {
                log.info("EbayGet响应数据重试异常：{},店铺：{},请求URL：{}",httpResponse, shopId, requestUrl);
            }
        }
        return httpResponse.body();
    }


    /**
     * @param
     * @description: wayfair 操作
     * @author: Moore
     * @date: 2024/6/19 18:24
     * @return: java.lang.String
     **/
    public String sendPostRequest(String requestUrl, Integer shopId, Object requestParam) {
        HttpRequest post = HttpUtil.createRequest(Method.POST, requestUrl);
        post.header("Accept", "application/json");
        post.header("authorization", "TOKEN " + accessTokenUtils.getToken(shopId));
        post.header("content-type", "application/json");
        HttpResponse httpResponse;
        if (requestParam == null) {
            httpResponse = post.execute();
        } else {
            String jsonBody = JSON.toJSONString(requestParam);
            log.info("ebay接口请求参数 ：{}", jsonBody);
            HttpRequest body = post.body(jsonBody);
            httpResponse = body.execute();
        }
        if (!httpResponse.isOk()) {
            log.info("Wayfair获取取消单据：{}", httpResponse.body());
            return httpResponse.body();
        }
        return httpResponse.body();
    }



    /**
     * @param
     * @description: wayfair 操作
     * @author: Moore
     * @date: 2024/6/19 18:24
     * @return: java.lang.String
     **/
    public HttpResponse sendPostRequestResHttp(String requestUrl, Integer shopId, Object requestParam) {

        Account account = accountService.getById(shopId);
        if (Objects.isNull(account)) {
            return null;
        }
        HttpRequest post = HttpUtil.createRequest(Method.POST, requestUrl);
        post.header("Accept", "application/json");
        post.header("authorization", "TOKEN " + getAccessToken(account));
        post.header("content-type", "application/json");
        HttpResponse httpResponse;
        if (requestParam == null) {
            httpResponse = post.execute();
        } else {
            String jsonBody = JSON.toJSONString(requestParam);
            log.info("ebayPost接口请求参数 ：{}", jsonBody);
            HttpRequest body = post.body(jsonBody);
            httpResponse = body.execute();
        }
        return httpResponse;
    }


    public String getAccessToken( Account account) {

        String redisKey = "wayfair:accessToken:cancel" + account.getId();

        // eBay 应用的 Client ID 和 Client Secret
        String clientId = XxlConfig.EBAY_APPID;
        String clientSecret =XxlConfig.EBAY_CERTID;


//        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(redisKey))) {
//            return stringRedisTemplate.opsForValue().get(redisKey);
//        }

        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            log.error("自定义Ebay auth error [{}] : connectStr is empty", account.getId());
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey("refreshToken")) {
            log.error("自定义Ebay auth error [{}] : refreshToken not found", account.getId());
            return null;
        }



        // 将 Client ID 和 Client Secret 转换为 Base64
        String credentials = clientId + ":" + clientSecret;
        String encodedCredentials = Base64.getEncoder().encodeToString(credentials.getBytes());

        // 创建 OkHttpClient 实例
//       OkHttpClient client = new OkHttpClient();

        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(40, TimeUnit.SECONDS)  // 设置连接超时
                .readTimeout(15, TimeUnit.SECONDS)     // 设置读取超时
                .build();

        // 设置请求的 URL 和 Body
        String url = "https://api.ebay.com/identity/v1/oauth2/token";
        RequestBody body = new FormBody.Builder()
                .add("grant_type", "refresh_token")
                .add("refresh_token", jsonObject.getString("refreshToken")) // 根据需要替换 scope
                .build();

        // 构建请求
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("Authorization", "Basic " + encodedCredentials)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();

        // 发送请求并处理响应
        String responseBody;
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {

                responseBody = response.body().string();
                log.info("获取到ebay鉴权信息：{},店铺：{}", responseBody,account.getId());
                String access_token = JSON.parseObject(responseBody).getString("access_token");
//                stringRedisTemplate.opsForValue().set(redisKey, access_token, 10 * 60 * 60, TimeUnit.SECONDS);
                return access_token;
            } else {
                // 输出错误信息
                log.info("获取ebay鉴权信息失败:{},responese:{},店铺：{}", response.code(), response.body().string(), account.getId());
            }
        } catch (Exception e) {
            log.error("调用ebay鉴权失败：{}，店铺：{}", e, JSONObject.toJSONString(account));
            e.printStackTrace();
        }
        return null;
    }



}
