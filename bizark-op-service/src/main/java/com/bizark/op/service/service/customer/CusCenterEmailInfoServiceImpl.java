package com.bizark.op.service.service.customer;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.boss.api.service.TaskCenterService;
import com.bizark.boss.api.vo.dashboard.TaskCenterRequest;
import com.bizark.op.api.enm.finance.FinanceErrorEnum;
import com.bizark.op.api.enm.sale.AccountSaleChannelEnum;
import com.bizark.op.api.entity.op.compete.MarCategoryInfo;
import com.bizark.op.api.entity.op.ticket.ScStationLetterMessage;
import com.bizark.op.api.entity.op.ticket.SyncEmailInfo;
import com.bizark.op.api.entity.op.tk.expert.vo.VideoExportTaskVO;
import com.bizark.op.api.form.customer.CusCenterEmailInfoQuery;
import com.bizark.op.api.service.customer.ICusCenterEmailInfoService;
import com.bizark.op.api.service.tiktok.ITiktokMessageService;
import com.bizark.op.api.vo.customer.CusCenterEmailInfoDTO;
import com.bizark.op.api.vo.customer.CusCenterEmailInfoVO;
import com.bizark.op.common.core.page.PageInfoDomain;
import com.bizark.op.common.core.page.TableSupportInfo;
import com.bizark.op.common.function.page.Page;
import com.bizark.op.common.util.*;
import com.bizark.op.service.mapper.customer.CusCenterEmailInfoMapper;
import com.bizark.op.service.mapper.tabcut.TabcutCreatorVideoMapper;
import com.bizark.op.service.util.ConvertUtils;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 邮件信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-12
 */
@Slf4j
@Service
public class CusCenterEmailInfoServiceImpl extends ServiceImpl<CusCenterEmailInfoMapper, SyncEmailInfo> implements ICusCenterEmailInfoService {


    @Autowired
    private ITiktokMessageService tiktokMessageService;


    @Autowired
    private TaskCenterService taskCenterService;

    @Autowired
    private TabcutCreatorVideoMapper tabcutCreatorVideoMapper;


    @Value("${home_query_bi_url}")
    private String HOME_QUERY_BI_URL;

    /**
     * 查询邮件信息列表
     *
     * @param query
     * @return
     */
    @Override
    public List<CusCenterEmailInfoVO> selectCusCenterEmailInfoList(CusCenterEmailInfoQuery query) {
        //查询时间调整
        if (query.getSenTime() != null) {
            String purchaseDateFrom = DateUtil.convertDateToString(query.getSenTime());
            String purchaseDateTo = DateUtil.convertDateToString(query.getSenTime());
            query.setSenTimeFrom(purchaseDateFrom + " 00:00:00");
            query.setSenTimeTo(purchaseDateTo + " 23:59:59");
        }
        List<CusCenterEmailInfoDTO> emailInfos = baseMapper.selectCusCenterEmailInfoList(query);
        return BeanCopyUtils.asmCopyList(emailInfos, CusCenterEmailInfoVO.class);
    }


    /**
     * @description: 查询邮箱列表病分页
     * @author: Moore
     * @date: 2024/8/7 14:44
     * @param
     * @param query
     * @param page
     * @return: java.util.List<com.bizark.op.api.vo.customer.CusCenterEmailInfoVO>
    **/
    @Override
    public List<CusCenterEmailInfoVO> selectCusCenterEmailInfoListByPage(CusCenterEmailInfoQuery query, Page<CusCenterEmailInfoDTO> page) {
        //查询时间调整
        if (query.getSenTime() != null) {
            String purchaseDateFrom = DateUtil.convertDateToString(query.getSenTime());
            String purchaseDateTo = DateUtil.convertDateToString(query.getSenTime());
            query.setSenTimeFrom(purchaseDateFrom + " 00:00:00");
            query.setSenTimeTo(purchaseDateTo + " 23:59:59");
        }
        List<CusCenterEmailInfoDTO> emailInfos = baseMapper.selectCusCenterEmailInfoList(query);
        // 分页
        if (page != null) {
            page.paging(emailInfos);
        }
        List<CusCenterEmailInfoVO> result = BeanCopyUtils.asmCopyList(emailInfos, CusCenterEmailInfoVO.class);
        return ConvertUtils.dictConvert(result);
    }


    /**
     * 获取邮件信息详细信息
     *
     * @param query 查询条件 Y查询历史，N当前
     * @return
     */
    @Override
    public CusCenterEmailInfoVO selectCusCenterEmailInfoItem(CusCenterEmailInfoQuery query) {

        CusCenterEmailInfoVO cusCenterEmailInfoVO = new CusCenterEmailInfoVO();
        if (AccountSaleChannelEnum.TIKTOK.getValue().equalsIgnoreCase(query.getChannel())) {
            List<ScStationLetterMessage> scStationLetterMessageList = tiktokMessageService.selectTikcetConversationContent(query.getContextId(), query.getTicketId(), query.getHisFlag());
            if (!"Y".equals(query.getHisFlag()) && !CollectionUtils.isEmpty(scStationLetterMessageList)) { //非历史只返回一条(取最早的一条)
                cusCenterEmailInfoVO.setTiktokMessages(Arrays.asList(scStationLetterMessageList.get(scStationLetterMessageList.size() - 1)));
            } else {
                cusCenterEmailInfoVO.setTiktokMessages(scStationLetterMessageList);
            }
        } else {
            if ("Y".equals(query.getHisFlag())) { //查询历史将主键查询置空、
                if ((StringUtils.isEmpty(query.getFromAddress()) || StringUtils.isEmpty(query.getToAddress()))) {
                    return null;
                }
                query.setInfoId(null);
            }
            //非历史 根据主键，历史根据from及to邮件名
            cusCenterEmailInfoVO.setOtherEmailMessage(baseMapper.selectByListItem(query));
        }
        return cusCenterEmailInfoVO;
    }


    /**
     * @description: 导出调用接口
     * @author: Moore
     * @date: 2024/5/21 16:16
     * @param
     * @param response
     * @param query
     * @param authUserEntity
     * @return: void
    **/
    @Override
    public void listExport(HttpServletResponse response, CusCenterEmailInfoQuery query, UserEntity authUserEntity) {
        TaskCenterRequest request = new TaskCenterRequest();
        request.setTaskCode("erp.sync_email_info.export");
        request.setOrgId(query.getContextId());
        List<Object> list = new ArrayList<>();
        list.add(JSON.toJSONString(query));
        request.setArgs(list);
        this.taskCenterService.startTask(request, authUserEntity);
        AssertUtil.isFail(FinanceErrorEnum.ASNYC_EXPORT_GENERATED);
    }

    /**
     * bi查询邮件信息
     *
     * @param query
     */
    @Override
    public JSONObject biQueryListPage(CusCenterEmailInfoQuery query) {
        PageInfoDomain pageInfoDomain = TableSupportInfo.buildPageRequest();
        Integer page = pageInfoDomain.getPage();
        Integer rows = pageInfoDomain.getRows();
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("contextId",query.getContextId());
        paramMap.put("emailTypeList",query.getEmailTypeList());
        paramMap.put("matterList",query.getMatterList());
        paramMap.put("channelList",query.getChannelList());
        paramMap.put("shopList",query.getShopList());
        paramMap.put("ticketStatusList",query.getTicketStatusList());
        paramMap.put("handlerBy",query.getHandlerBy());
        paramMap.put("senTimeFrom",query.getSenTimeFrom());
        paramMap.put("senTimeTo",query.getSenTimeTo());
        paramMap.put("senName",query.getSenName());
        paramMap.put("email",query.getEmail());
        paramMap.put("emailTitle",query.getEmailTitle());
        paramMap.put("orderNoList",query.getOrderNoList());
        paramMap.put("asinList",query.getAsinList());
        paramMap.put("goodsSkuList",query.getGoodsSkuList());
        paramMap.put("page",page);
        paramMap.put("rows",rows);
        //String queryUrl = "http://172.16.11.210:5560/api/v1/MailMessages";
        String queryUrl = HOME_QUERY_BI_URL + "/api/v1/MailMessages";
        HttpRequest request = HttpUtil.createPost(queryUrl);
        log.error("bi查询邮件信息地址：{},参数{},", queryUrl, JSON.toJSONString(paramMap));
        request.body(JSON.toJSONString(paramMap));
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            throw new RuntimeException("bi查询邮件信息地址接口异常");
        }
        log.info("bi查询邮件信息地址返回结果：{}", response.body());
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        return jsonObject;
    }

    /**
     * bi导出邮件信息
     *
     * @param query
     */
    @Override
    public void biExport(CusCenterEmailInfoQuery query) {
        Integer userId = UserUtils.getCurrentUserId(0); //获取当前用户
        String userName = UserUtils.getCurrentUserName("system");
        VideoExportTaskVO vo = new VideoExportTaskVO();
        vo.setExportId(userId + "-" + System.currentTimeMillis());
        query.setExportId(vo.getExportId());
        this.biCusCenterEmailInfoExport(query);
        Date date = new Date();
        vo.setTaskNo(vo.getExportId()); //任务号
        vo.setTaskCode("erp.bi.export.MarCusCenterEmailInfo.time");
        vo.setCreatedAt(date);
        vo.setCreatedBy(userId);
        vo.setCreatedName(userName);
        vo.setUpdatedAt(date);
        vo.setUpdatedBy(userId);
        vo.setUpdatedName(userName);
        vo.setStatus("processing");
        vo.setBody(JSON.toJSONString(query));
        vo.setOrgId(query.getContextId());
        vo.setProcessTime(date);
        vo.setTaskTitle("邮件信息导出");
        tabcutCreatorVideoMapper.expoertTaskInsert(vo);
    }

    void biCusCenterEmailInfoExport(CusCenterEmailInfoQuery query) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("contextId",query.getContextId());
        paramMap.put("emailTypeList",query.getEmailTypeList());
        paramMap.put("matterList",query.getMatterList());
        paramMap.put("channelList",query.getChannelList());
        paramMap.put("shopList",query.getShopList());
        paramMap.put("ticketStatusList",query.getTicketStatusList());
        paramMap.put("handlerBy",query.getHandlerBy());
        paramMap.put("senTimeFrom",query.getSenTimeFrom());
        paramMap.put("senTimeTo",query.getSenTimeTo());
        paramMap.put("senName",query.getSenName());
        paramMap.put("email",query.getEmail());
        paramMap.put("emailTitle",query.getEmailTitle());
        paramMap.put("orderNoList",query.getOrderNoList());
        paramMap.put("asinList",query.getAsinList());
        paramMap.put("goodsSkuList",query.getGoodsSkuList());
        paramMap.put("exportId",query.getExportId());
        //String queryUrl = "http://172.16.11.210:5560/api/v1/MailMessages";
        String queryUrl = HOME_QUERY_BI_URL + "/api/v1/MailMessages";
        HttpRequest request = HttpUtil.createPost(queryUrl);
        log.error("bi邮件信息导出地址：{},参数{},", queryUrl, JSON.toJSONString(paramMap));
        request.body(JSON.toJSONString(paramMap));
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            throw new RuntimeException("bi邮件信息导出接口异常");
        }
        log.info("bi邮件信息导出返回结果：{}", response.body());
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        if(jsonObject == null) {
            throw new RuntimeException("bi邮件信息导出接口异常");
        }
    }



}
