package com.bizark.op.service.service.mar.appeal;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bizark.boss.api.service.TaskCenterService;
import com.bizark.boss.api.vo.dashboard.TaskCenterRequest;
import com.bizark.op.api.cons.MQDefine;
import com.bizark.op.api.cons.ScTicketConstant;
import com.bizark.op.api.enm.mar.AppealApiStatusEnum;
import com.bizark.op.api.enm.mar.AppealExceptionEnum;
import com.bizark.op.api.enm.mar.AppealStatusEnum;
import com.bizark.op.api.enm.mar.AppealTypeEnum;
import com.bizark.op.api.enm.sale.AccountSaleChannelEnum;
import com.bizark.op.api.enm.ticket.TrackingStatusEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.mar.MarAdjustmentPriceManagement;
import com.bizark.op.api.entity.op.mar.appeal.MarOrderPackageAppeal;
import com.bizark.op.api.entity.op.mar.appeal.MarOrderPackageAppealImg;
import com.bizark.op.api.entity.op.mar.appeal.MarOrderPackageAppealLog;
import com.bizark.op.api.entity.op.mar.appeal.vo.MarOrderPackageAppealResponseVo;
import com.bizark.op.api.entity.op.order.SaleOrders;
import com.bizark.op.api.entity.op.order.SaleShipmentPackagesVO;
import com.bizark.op.api.entity.op.sale.message.TemuViolationMessage;
import com.bizark.op.api.entity.op.ticket.ScTicket;
import com.bizark.op.api.entity.op.ticket.ScTicketLog;
import com.bizark.op.api.entity.op.ticket.customerInfo.OrderLogisticInfo;
import com.bizark.op.api.entity.op.ticket.customerInfo.ScTicketCustomerOrderInfoVO;
import com.bizark.op.api.request.mar.MarAppealQuery;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.mar.appeal.MarAppealService;
import com.bizark.op.api.service.order.SaleOrdersService;
import com.bizark.op.api.service.sale.TemuViolationDetailService;
import com.bizark.op.api.service.ticket.IScTicketLogService;
import com.bizark.op.api.vo.mar.DeliveryItem;
import com.bizark.op.api.service.mar.appeal.MarOrderPackageAppealImgService;
import com.bizark.op.api.service.mar.appeal.MarOrderPackageAppealLogService;
import com.bizark.op.api.service.mar.appeal.MarOrderPackageAppealService;
import com.bizark.op.api.service.ticket.IScTicketService;

import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.service.mapper.mar.MarAdjustmentPriceManagementMapper;
import com.bizark.op.service.mapper.sale.ProductsMapper;
import com.bizark.op.service.mapper.sale.TemuViolationDetailMapper;
import com.bizark.op.service.mapper.ticket.SaleOrdersMapper;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import com.bizark.op.api.vo.mar.MarAppealListVo;
import com.bizark.op.common.util.AliyunOssClientUtil;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.common.util.sql.SqlUtil;
import com.bizark.op.service.mapper.mar.appeal.MarOrderPackageAppealMapper;
import com.bizark.op.service.mapper.ticket.customerInfo.ScTicketCustomerInfoMapper;
import com.bizark.op.service.util.ConvertUtils;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: Fountain
 * @Date: 2024/10/15
 */
@Service
@Slf4j
public class MarAppealServiceImpl implements MarAppealService {

    @Autowired
    @Lazy
    private MarOrderPackageAppealService marOrderPackageAppealService;

    @Autowired
    private MarOrderPackageAppealImgService marOrderPackageAppealImgService;

    @Autowired
    private TaskCenterService taskCenterService;


    @Autowired
    private MarOrderPackageAppealLogService marOrderPackageAppealLogService;

    @Autowired
    private IScTicketService scTicketService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private MarOrderPackageAppealMapper marOrderPackageAppealMapper;

    @Autowired
    private ScTicketCustomerInfoMapper scTicketCustomerInfoMapper;

    @Value("${task.center.file.path}")
    private String filePath;

    @Autowired
    private AccountService accountService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private MarAdjustmentPriceManagementMapper marAdjustmentPriceManagementMapper;

    @Autowired
    private IScTicketLogService scTicketLogService;

    @Autowired
    private TemuViolationDetailMapper temuViolationDetailMapper;

    @Autowired
    private TemuViolationDetailService temuViolationDetailService;

    @Autowired
    private ProductsMapper productsMapper;
    @Autowired
    private SaleOrdersMapper saleOrdersMapper;

    /**
     * Description: 异常重置
     *
     * @param
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/10/15
     */
    @Override
    public void exceptionReset(List<MarOrderPackageAppeal> marOrderPackageAppealListReq, Boolean ifTicketComeIn) {
        if (CollectionUtils.isEmpty(marOrderPackageAppealListReq)) {
            throw new CustomException("未获取到申诉数据");
        }

        if (CollectionUtils.isEmpty(marOrderPackageAppealListReq)) {
            throw new CustomException("未获取到申诉数据");
        }
        marOrderPackageAppealListReq = marOrderPackageAppealListReq.stream().filter(item -> item.getAppealType() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(marOrderPackageAppealListReq)) {
            throw new CustomException("申诉类型获取失败！");
        }


        //区分重置 申诉类型
        List<MarOrderPackageAppeal> packageAppealList = marOrderPackageAppealListReq.stream().filter(item ->
                AppealTypeEnum.DELIVERED_UNBALANCED.getValue().equals(item.getAppealType())
        ).collect(Collectors.toList());

        List<MarOrderPackageAppeal> violationAppealList = marOrderPackageAppealListReq.stream().filter(item ->
                AppealTypeEnum.DELAY_ARRIVAL.getValue().equals(item.getAppealType())
                        || AppealTypeEnum.DECEITFUL_DELIVERY.getValue().equals(item.getAppealType())
        ).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(packageAppealList) && CollectionUtils.isEmpty(violationAppealList)) {
            throw new CustomException("无符合类型申诉数据！");
        }




            if (CollectionUtil.isNotEmpty(packageAppealList)) {
                QueryWrapper<MarOrderPackageAppeal> queryWrapper = new QueryWrapper();
                queryWrapper.in("id", packageAppealList.stream().map(i -> i.getId()).distinct().collect(Collectors.toList()));
                packageAppealList = marOrderPackageAppealService.list(queryWrapper);
                packageAppealList = packageAppealList.stream().filter(i -> AppealExceptionEnum.ABNORMAL.getValue().equals(i.getExceptionFlag()) &&
                        (AppealStatusEnum.PROVEING.getValue().equals(i.getAppealStatus()) || AppealStatusEnum.SUBMITTING.getValue().equals(i.getAppealStatus()))).collect(Collectors.toList());

                if (CollectionUtil.isEmpty(packageAppealList)){
                    throw new CustomException("无符合类型包裹申诉数据！");
                }

                for (MarOrderPackageAppeal marOrderPackageAppeal : packageAppealList) {

                    if (AppealStatusEnum.PROVEING.getValue().equals(marOrderPackageAppeal.getAppealStatus())) {//获取证明中
                        MarOrderPackageAppealLog marOrderPackageAppealLog = new MarOrderPackageAppealLog();
                        marOrderPackageAppealLog.setAppealId(marOrderPackageAppeal.getId());
                        marOrderPackageAppealLog.setOrganizationId(marOrderPackageAppeal.getOrganizationId());
                        marOrderPackageAppealLog.setOperateContent("异常重置：提交获取证明中");
                        marOrderPackageAppealLog.settingDefaultCreate();
                        marOrderPackageAppealLogService.save(marOrderPackageAppealLog);
                        //发送获取证明
                        marOrderPackageAppealImgService.sendAppealImg(false, marOrderPackageAppeal.getTrackNo(), marOrderPackageAppeal.getCarrierCode(), marOrderPackageAppeal.getId());
                    }


                    if (AppealStatusEnum.SUBMITTING.getValue().equals(marOrderPackageAppeal.getAppealStatus())) {//提交申诉中
                        try {
                            //查询店铺
                            MarAdjustmentPriceManagement marAdjustmentPriceManagement = null;
                            Account account = accountService.getById(marOrderPackageAppeal.getShopId());
                            //查询公司名
                            List<MarAdjustmentPriceManagement> marAdjustmentPriceManagementList = marAdjustmentPriceManagementMapper.selectRegisterCompanyName(Arrays.asList(marOrderPackageAppeal.getShopId()));
                            if (CollectionUtil.isEmpty(marAdjustmentPriceManagementList)) {
                                log.info("包裹重置无公司名：{}", marOrderPackageAppeal.getId());
                                continue;
                            }
                            marAdjustmentPriceManagement = marAdjustmentPriceManagementList.get(0);

                            //查询申诉图片
                            QueryWrapper<MarOrderPackageAppealImg> marOrderPackageAppealImgQueryWrapper = new QueryWrapper();
                            marOrderPackageAppealImgQueryWrapper.in("appeal_id", marOrderPackageAppeal.getId());
                            List<MarOrderPackageAppealImg> marOrderPackageAppealImgList = marOrderPackageAppealImgService.list(marOrderPackageAppealImgQueryWrapper);

                            if (CollectionUtils.isEmpty(marOrderPackageAppealImgList)) {
                                continue;
                            }

                            JSONObject parmaJsonObject = new JSONObject();
                            parmaJsonObject.put("registerCompanyName", marAdjustmentPriceManagement.getRegisterCompanyName());
                            parmaJsonObject.put("id", marOrderPackageAppeal.getId());
                            parmaJsonObject.put("maillId", account.getSellerId());
                            parmaJsonObject.put("shopName", account.getStoreName());
                            parmaJsonObject.put("packageNo", marOrderPackageAppeal.getPackageNo());
                            parmaJsonObject.put("images", marOrderPackageAppealImgList.stream().map(i -> i.getImageUrl()).collect(Collectors.toList()));
                            parmaJsonObject.put("evidenceDesc", marOrderPackageAppeal.getEvidenceDesc());
                            parmaJsonObject.put("appealType", AppealTypeEnum.DELIVERED_UNBALANCED.getValue()); //包裹申诉
                            log.info("mq异常重置数据:{},店铺：{}", parmaJsonObject, JSONObject.toJSON(account));
                            if (new Integer(2).equals(account.getSaleChannel())) {
                                //本本店铺
                                rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_E_ERP, MQDefine.MAR_ORDER_PACKAGE_APPEAL_LOCAL_SHOP_SUBMIT_KEY, parmaJsonObject);
                            } else {
                                //跨境店铺
                                rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_E_ERP, MQDefine.MAR_ORDER_PACKAGE_APPEAL_SUBMIT_KEY, parmaJsonObject);
                            }

                            marOrderPackageAppeal.setExceptionFlag(AppealExceptionEnum.NORMAL.getValue());
                            marOrderPackageAppeal.settingDefaultUpdate();
                            marOrderPackageAppealService.updateById(marOrderPackageAppeal);

                            //todo 记录日志
                            MarOrderPackageAppealLog marOrderPackageAppealLog = new MarOrderPackageAppealLog();
                            marOrderPackageAppealLog.setAppealId(marOrderPackageAppeal.getId());
                            marOrderPackageAppealLog.setOrganizationId(marOrderPackageAppeal.getOrganizationId());
                            marOrderPackageAppealLog.setOperateContent("异常重置：提交申诉中");
                            marOrderPackageAppealLog.settingDefaultCreate();
                            marOrderPackageAppealLogService.save(marOrderPackageAppealLog);

                        } catch (Exception e) {
                            log.error("包裹异常重置，失败：{}", e);
                        }
                    }

                    if (marOrderPackageAppeal.getTicketId() != null) {
                        if (ifTicketComeIn) {
                            ScTicketLog ticketLog = new ScTicketLog();
                            ticketLog.setTicketId(marOrderPackageAppeal.getTicketId().longValue());
                            ticketLog.setOperateType("Update");
                            ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
                            ticketLog.setOperateTime(DateUtils.getNowDate());
                            ticketLog.setOperateContent("异常重置");
                            ticketLog.settingDefaultCreate(); //设置更新数据
                            scTicketLogService.insertScTicketLog(ticketLog);
                        }
                        ScTicket scTicket = scTicketService.selectScTicketById(marOrderPackageAppeal.getTicketId().longValue());
                        if (scTicket != null && "NEW".equals(scTicket.getTicketStatus())) {
                            scTicket.setTicketStatus(ScTicketConstant.TICKET_STATUS_PROCESSING);
                            scTicketService.updateScTicketStatus(scTicket);
                        }
                    }

                }
            }


        if (!CollectionUtils.isEmpty(violationAppealList)) {
            temuViolationDetailService.exceptionReset(violationAppealList.stream().map(MarOrderPackageAppeal::getId).distinct().collect(Collectors.toList()), null, ifTicketComeIn);
        }

    }

    /**
     * Description: 提交申诉
     * @param marOrderPackageAppealListReq
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/10/15
     */
    @Override
    public void subAppeal(List<MarOrderPackageAppeal> marOrderPackageAppealListReq, Boolean ifTicketComeIn, String evidenceDesc) {
        if (CollectionUtils.isEmpty(marOrderPackageAppealListReq)) {
            throw new CustomException("未获取到申诉数据");
        }
        marOrderPackageAppealListReq = marOrderPackageAppealListReq.stream().filter(item -> item.getAppealType() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(marOrderPackageAppealListReq)) {
            throw new CustomException("申诉类型获取失败！");
        }

        //包裹申诉 0  此处使用appealType 由前端传入
        List<MarOrderPackageAppeal> marOrderPackageAppealList = marOrderPackageAppealListReq.stream().filter(item ->
                AppealTypeEnum.DELIVERED_UNBALANCED.getValue().equals(item.getAppealType())
        ).collect(Collectors.toList());

        //虚假发货、延迟到货 1,4
        List<MarOrderPackageAppeal> violationAppealList = marOrderPackageAppealListReq.stream().filter(item ->
                AppealTypeEnum.DELAY_ARRIVAL.getValue().equals(item.getAppealType())
                        || AppealTypeEnum.DECEITFUL_DELIVERY.getValue().equals(item.getAppealType())
        ).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(marOrderPackageAppealList) && CollectionUtils.isEmpty(violationAppealList)) {
            throw new CustomException("无符合类型申诉数据！");
        }


        if (CollectionUtil.isNotEmpty(marOrderPackageAppealList)) {
            QueryWrapper<MarOrderPackageAppeal> queryWrapper = new QueryWrapper();
            queryWrapper.in("id", marOrderPackageAppealList.stream().map(i -> i.getId()).distinct().collect(Collectors.toList()));
            marOrderPackageAppealList = marOrderPackageAppealService.list(queryWrapper);
            if (ifTicketComeIn) { //工单更新举证说明
                marOrderPackageAppealList.stream().forEach(i -> i.setEvidenceDesc(evidenceDesc));
            }
            marOrderPackageAppealList = marOrderPackageAppealList.stream().filter(i -> (
                    AppealStatusEnum.TOBESUBMITTED.getValue().equals(i.getAppealStatus()) &&
                            StringUtils.isNotEmpty(i.getEvidenceDesc()))
                    || (AppealStatusEnum.PROVEING.getValue().equals(i.getAppealStatus())
                    && AppealExceptionEnum.ABNORMAL.getValue().equals(i.getExceptionFlag()) && StringUtils.isNotEmpty(i.getEvidenceDesc()))
            ).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(marOrderPackageAppealList)) {
                QueryWrapper<MarOrderPackageAppealImg> marOrderPackageAppealImgQueryWrapper = new QueryWrapper<>();
                marOrderPackageAppealImgQueryWrapper.in("appeal_id", marOrderPackageAppealList.stream().map(i -> i.getId()).collect(Collectors.toList()));
                List<MarOrderPackageAppealImg> marOrderPackageAppealImgList = marOrderPackageAppealImgService.list(marOrderPackageAppealImgQueryWrapper);
                if (CollectionUtils.isEmpty(marOrderPackageAppealImgList)) {
                    throw new RuntimeException("所选记录无申诉图片信息！");
                }

                for (MarOrderPackageAppeal marOrderPackageAppeal : marOrderPackageAppealList) {
                    try {
                        //查询店铺
                        Account account = accountService.getById(marOrderPackageAppeal.getShopId());

                        //查询公司名
                        List<MarAdjustmentPriceManagement> marAdjustmentPriceManagementList = marAdjustmentPriceManagementMapper.selectRegisterCompanyName(Arrays.asList(marOrderPackageAppeal.getShopId()));
                        if (CollectionUtil.isEmpty(marAdjustmentPriceManagementList)) {
                            log.info("包裹申诉无公司信息：{}", marOrderPackageAppeal.getId());
                            continue;
                        }
                        MarAdjustmentPriceManagement marAdjustmentPriceManagement = marAdjustmentPriceManagementList.get(0);

                        JSONObject parmaJsonObject = new JSONObject();
                        parmaJsonObject.put("registerCompanyName", marAdjustmentPriceManagement.getRegisterCompanyName());
                        parmaJsonObject.put("id", marOrderPackageAppeal.getId());
                        parmaJsonObject.put("maillId", account.getSellerId());
                        parmaJsonObject.put("shopName", account.getStoreName());
                        parmaJsonObject.put("packageNo", marOrderPackageAppeal.getPackageNo());
                        parmaJsonObject.put("appealType", AppealTypeEnum.DELIVERED_UNBALANCED.getValue()); //包裹申诉
                        parmaJsonObject.put("area_code", marAdjustmentPriceManagement.getAreaCode());
                        //获取图片
                        List<MarOrderPackageAppealImg> paramImgList = marOrderPackageAppealImgList.stream().filter(item -> item.getAppealId().equals(marOrderPackageAppeal.getId())).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(paramImgList)) {
                            log.info("提交包裹申诉无图片：{}", marOrderPackageAppeal.getId());
                            continue;
                        }
                        List<String> images = paramImgList.stream().map(MarOrderPackageAppealImg::getImageUrl).distinct().collect(Collectors.toList());
                        parmaJsonObject.put("images", images);
                        parmaJsonObject.put("evidenceDesc", marOrderPackageAppeal.getEvidenceDesc());
                        log.info("mq提交申诉数据:{},店铺：{}", parmaJsonObject, JSONObject.toJSONString(account));
                        if (new Integer(2).equals(account.getSaleChannel())) {
                            //本本店铺
                            rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_E_ERP, MQDefine.MAR_ORDER_PACKAGE_APPEAL_LOCAL_SHOP_SUBMIT_KEY, parmaJsonObject);
                        } else {
                            //跨境店铺
                            rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_E_ERP, MQDefine.MAR_ORDER_PACKAGE_APPEAL_SUBMIT_KEY, parmaJsonObject);
                        }
                        marOrderPackageAppeal.setAppealStatus(AppealStatusEnum.SUBMITTING.getValue());
                        marOrderPackageAppeal.settingDefaultUpdate();
                        marOrderPackageAppealService.updateById(marOrderPackageAppeal);

                        //日志
                        MarOrderPackageAppealLog marOrderPackageAppealLog = new MarOrderPackageAppealLog();
                        marOrderPackageAppealLog.setAppealId(marOrderPackageAppeal.getId());
                        marOrderPackageAppealLog.setOrganizationId(marOrderPackageAppeal.getOrganizationId());
                        marOrderPackageAppealLog.setOperateContent("提交申诉，状态变更为：提交申诉中");
                        marOrderPackageAppealLog.settingDefaultCreate();
                        marOrderPackageAppealLogService.save(marOrderPackageAppealLog);
                        if (marOrderPackageAppeal.getTicketId() != null) {
                            if (ifTicketComeIn) {
                                ScTicketLog ticketLog = new ScTicketLog();
                                ticketLog.setTicketId(marOrderPackageAppeal.getTicketId().longValue());
                                ticketLog.setOperateType("Update");
                                ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
                                ticketLog.setOperateTime(DateUtils.getNowDate());
                                ticketLog.setOperateContent("提交申诉");
                                ticketLog.settingDefaultCreate(); //设置更新数据
                                scTicketLogService.insertScTicketLog(ticketLog);
                            }
                            ScTicket scTicket = scTicketService.selectScTicketById(marOrderPackageAppeal.getTicketId().longValue());
                            if (scTicket != null && "NEW".equals(scTicket.getTicketStatus())) {
                                scTicket.setTicketStatus(ScTicketConstant.TICKET_STATUS_PROCESSING);
                                scTicketService.updateScTicketStatus(scTicket);
                            }
                        }
                    } catch (Exception e) {
                        log.error("mq提交包裹申诉数据异常，发送未成功：{}", e);
                    }
                }

            }
        }

        //提交申诉
        if (!CollectionUtils.isEmpty(violationAppealList)) {
            temuViolationDetailService.subAppeal(violationAppealList.stream().map(MarOrderPackageAppeal::getId).distinct().collect(Collectors.toList()), null, ifTicketComeIn, evidenceDesc);
        }

    }

    /**
     * Description: 取消申诉
     *
     * @param marOrderPackageAppealList
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/10/15
     */
    @Override
    public String cancelAppeal(List<MarOrderPackageAppeal> marOrderPackageAppealList,Boolean ifTicketComeIn) {
        int success = 0;//记录成功数并返回
        int fail = 0;//记录失败数并返回
        String errorTrackNoMsg = "";//记录失败的跟踪号

        //todo 筛选 取消申诉仅支持获取证明中（是否异常均可）、待提交申诉 以及 提交申诉中且异常 的操作
        if(CollectionUtil.isNotEmpty(marOrderPackageAppealList)) {
            //todo 根据id查询数据库防止页面长时间没刷新导致数据不一致
            QueryWrapper<MarOrderPackageAppeal> queryWrapper = new QueryWrapper();
            queryWrapper.in("id", marOrderPackageAppealList.stream().map(i -> i.getId()).collect(Collectors.toList()));
            marOrderPackageAppealList = marOrderPackageAppealService.list(queryWrapper);
            marOrderPackageAppealList = marOrderPackageAppealList.stream().filter(i -> AppealStatusEnum.PROVEING.getValue().equals(i.getAppealStatus()) ||
                            AppealStatusEnum.TOBESUBMITTED.getValue().equals(i.getAppealStatus())
                            ||  (AppealStatusEnum.SUBMITTING.getValue().equals(i.getAppealStatus()) && AppealExceptionEnum.ABNORMAL.getValue().equals(i.getExceptionFlag())))
                    .collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(marOrderPackageAppealList)) {
                for(MarOrderPackageAppeal marOrderPackageAppeal:marOrderPackageAppealList) {
                    try {
                        //todo 改状态
                        marOrderPackageAppeal.setAppealStatus(AppealStatusEnum.CANCLE.getValue());
                        marOrderPackageAppeal.setExceptionFlag(AppealExceptionEnum.NORMAL.getValue());
                        marOrderPackageAppeal.settingDefaultUpdate();
                        marOrderPackageAppealService.updateById(marOrderPackageAppeal);
                        //todo 记录日志
                        MarOrderPackageAppealLog marOrderPackageAppealLog = new MarOrderPackageAppealLog();
                        marOrderPackageAppealLog.setAppealId(marOrderPackageAppeal.getId());
                        marOrderPackageAppealLog.setOrganizationId(marOrderPackageAppeal.getOrganizationId());
                        marOrderPackageAppealLog.setOperateContent("提交取消申诉，状态变更为：取消申诉");
                        marOrderPackageAppealLog.settingDefaultCreate();
                        marOrderPackageAppealLogService.save(marOrderPackageAppealLog);
                        if (marOrderPackageAppeal.getTicketId() != null) {
                            if(ifTicketComeIn) {
                                //todo 记录工单日志
                                ScTicketLog ticketLog = new ScTicketLog();
                                ticketLog.setTicketId(marOrderPackageAppeal.getTicketId().longValue());
                                ticketLog.setOperateType("Update");
                                ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
                                ticketLog.setOperateTime(DateUtils.getNowDate());
                                ticketLog.setOperateContent("异常重置");
                                ticketLog.settingDefaultCreate(); //设置更新数据
                                scTicketLogService.insertScTicketLog(ticketLog);
                            }
                            //todo 调整工单状态 3.操作取消申诉：工单状态变更为  完成。
                            ScTicket scTicket = scTicketService.selectScTicketById(marOrderPackageAppeal.getTicketId().longValue());
                            if (scTicket != null) {
                                scTicket.setTicketStatus(ScTicketConstant.TICKET_STATUS_FINISH);
                                //todo 记录工单日志
                                scTicketService.updateScTicketStatus(scTicket);
                            }
                        }
                        success++;
                    } catch (Exception e) {
                        fail++;
                        errorTrackNoMsg = errorTrackNoMsg + marOrderPackageAppeal.getTrackNo() + ",";
                        log.error("取消申诉异常{}",e.getMessage());
                    }
                }
            } else {
                throw  new RuntimeException("所选记录的状态不可取消申诉");
            }
        } else {
            throw  new RuntimeException("所选记录的状态不可取消申诉");
        }
        return "成功数:" + success + ",失败数:" + fail + ",失败的跟踪号为:" + (StringUtils.isNotEmpty(errorTrackNoMsg) ?  errorTrackNoMsg.substring(0,errorTrackNoMsg.length() - 1) : "") ;
    }


    /**
     * @Description:列表查询
     * @Author: wly
     * @Date: 2024/10/15 14:46
     * @Params: [query]
     * @Return: java.util.List<com.bizark.op.api.vo.mar.MarAppealListVo>
     **/
    @Override
    public List<MarAppealListVo> queryAppealList(MarAppealQuery query) {
        query.resetSearchType();
        List<MarAppealListVo> marAppealListVos = marOrderPackageAppealMapper.selectMarAppealListVo(query);
        if (CollectionUtil.isNotEmpty(marAppealListVos)) {
            List<Long> orderIds = marAppealListVos.stream().map(MarAppealListVo::getOrderId).distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(orderIds)) {
                return marAppealListVos;
            }
            List<MarOrderPackageAppealLog> logList = marOrderPackageAppealLogService.list(new LambdaQueryWrapper<MarOrderPackageAppealLog>().in(MarOrderPackageAppealLog::getAppealId, marAppealListVos.stream().map(MarAppealListVo::getId).distinct().collect(Collectors.toList())).orderByDesc(MarOrderPackageAppealLog::getId));
            if (CollectionUtil.isNotEmpty(logList)) {
                for (MarAppealListVo vo : marAppealListVos) {
                    logList.stream().filter(i -> i.getAppealId().equals(vo.getId())).findFirst().ifPresent(i -> vo.setExceptionDesc(i.getOperateContent()));
                }
            }
        }
        return marAppealListVos;
    }

    /**
     * @Description:导出申诉列表
     * @Author: wly
     * @Date: 2024/10/15 14:51
     * @Params: [query]
     * @Return: void
     **/
    @Override
    public void originalExport(MarAppealQuery query, Integer contextId, UserEntity authUserEntity) {

        TaskCenterRequest request = new TaskCenterRequest();
        request.setOrgId(query.getContextId());
        request.setTaskCode("op.mar.appeal.list.export");
        List<Object> list = new ArrayList<>();
        list.add(JSON.toJSONString(query));
        list.add(query.getContextId());
        request.setArgs(list);
        this.taskCenterService.startTask(request, authUserEntity);
    }


    @Override
    public String asyncExport(String query, Integer contextId) {
        log.info("asyncMarAppealExport start ... ");
        int pageNo = 1;
        int pageSize = 5000;
        String path = filePath + "申诉信息" + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);

        MarAppealQuery request = JSON.parseObject(query, MarAppealQuery.class);
        String uploadPath = null;
        File file = null;
        try {
            file = File.createTempFile(path, ".xlsx");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        try (ExcelWriter writer = EasyExcel.write(file, MarAppealListVo.class).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "申诉信息").head(MarAppealListVo.class).build();
            while (true) {
                if (StringUtils.isNotEmpty(request.getSidx()) && StringUtils.isNotEmpty(request.getSord())) {
                    String orderBy = SqlUtil.escapeOrderBySql(StringUtils.toUnderScoreCase(request.getSidx()) + " " + request.getSord());
                    PageHelper.startPage(pageNo, pageSize, orderBy);
                } else {
                    PageHelper.startPage(pageNo, pageSize, "created_at desc");
                }

                List<MarAppealListVo> exportList = this.queryAppealList(request);
                if (CollUtil.isEmpty(exportList)) {
                    break;
                }

                ConvertUtils.dictConvert(exportList);

                log.info("申诉信息 query pageNo:{}, list.size = {}, ", pageNo, exportList.size());
                log.info("申诉信息 exportList.size = " + exportList.size());
                writer.write(exportList, writeSheet);
                pageNo++;
            }
            writer.finish();

            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "op/marappeal/");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e);
        }
        return uploadPath;
    }


    /**
     * @Description:申诉详情查询
     * @Author: wly
     * @Date: 2024/10/15 18:13
     * @Params: [appealId]
     * @Return: com.bizark.op.api.vo.mar.MarAppealListVo
     **/
    @Override
    public MarAppealListVo queryAppealDetailById(ScTicket scTicket) {
        if (scTicket == null || scTicket.getSourceId() == null || StringUtils.isEmpty(scTicket.getSourceDocument())) {
            return new MarAppealListVo();
        }
        Long appealId = scTicket.getSourceId();
        //包裹申诉
        if (scTicket.getSourceDocument().equals(ScTicketConstant.TICKET_TYPE_ORDER_PACKAGE_APPEAL_TABLE)) {
            MarAppealQuery marAppealQuery = new MarAppealQuery();
            marAppealQuery.setId(appealId);
            List<MarAppealListVo> marAppealListVos = marOrderPackageAppealMapper.selectMarAppealListVo(marAppealQuery);
            if (CollectionUtil.isEmpty(marAppealListVos)) {
                return new MarAppealListVo();
            }
            MarAppealListVo marAppealListVo = marAppealListVos.get(0);

            //异常日志
            if (new Integer(-1).equals(marAppealListVo.getExceptionFlag())) {
                MarOrderPackageAppealLog log = marOrderPackageAppealLogService.getOne(Wrappers.lambdaQuery(MarOrderPackageAppealLog.class).eq(MarOrderPackageAppealLog::getAppealId, appealId).orderByDesc(MarOrderPackageAppealLog::getId).last("limit 1"));
                marAppealListVo.setExceptionDesc(log != null ? log.getOperateContent() : null);
            }

            //图片信息
            List<MarOrderPackageAppealImg> imageList = marOrderPackageAppealImgService.list(new LambdaQueryWrapper<MarOrderPackageAppealImg>().eq(MarOrderPackageAppealImg::getAppealId, appealId));
            marAppealListVo.setAppealImgList(imageList);


            if (!StringUtils.isEmpty(marAppealListVo.getTrackingNo()) && marAppealListVo.getOrderId() != null) {
                List<SaleShipmentPackagesVO> saleShipmentPackagesVOS = saleOrdersMapper.selectOrderShipmentInfo(marAppealListVo.getOrderId());
                if (!CollectionUtils.isEmpty(saleShipmentPackagesVOS)) {
                    List<SaleShipmentPackagesVO> trackList = saleShipmentPackagesVOS.stream().filter(item -> item.getTrackNo().equalsIgnoreCase(marAppealListVo.getTrackingNo())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(trackList)) {
                        SaleShipmentPackagesVO saleShipmentPackagesVO = trackList.get(0);
                        DeliveryItem deliveryItem = new DeliveryItem();
                        deliveryItem.setNum(saleShipmentPackagesVO.getShipedQuantity());
                        deliveryItem.setSku(saleShipmentPackagesVO.getErpSku());
                        deliveryItem.setProductName(saleShipmentPackagesVO.getProductName());
                        marAppealListVo.setDeliveryItemList(Arrays.asList(deliveryItem));
                    }
                }

//            if (!StringUtils.isEmpty(marAppealListVo.getSku())) {
//                Products products = productsMapper.selectOne(new LambdaQueryWrapper<Products>().select(Products::getName).eq(Products::getErpsku, marAppealListVo.getSku()).eq(Products::getOrgId, marAppealListVo.getOrganizationId()));
//                DeliveryItem item = new DeliveryItem();
//                item.setNum(marAppealListVo.getShipedQuantity());
//                item.setSku(marAppealListVo.getSku());
//                item.setProductName(products != null ? products.getName() : null);
//                marAppealListVo.setAppealType(AppealTypeEnum.DELIVERED_UNBALANCED.getValue()); //默认妥投未回款
//                marAppealListVo.setDeliveryItemList(Arrays.asList(item));
//            }

            }
            return marAppealListVo;
        } else if (scTicket.getSourceDocument().equals(ScTicketConstant.TICKET_TYPE_VIOLATION_DETAIL_TABLE)) {
            //temu违规申诉
            return temuViolationDetailService.selectTicketSource(appealId);
        }

        return null;
    }

    /**
     * 保存申诉提交结果
     *
     * @param jsonObject
     */
    @Override
    public void saveAppealSubmitResult(JSONObject jsonObject) {
        String successFlag = jsonObject.getString("successFlag");
        String id = jsonObject.getString("id");
        String errorMsg = jsonObject.getString("errorMsg");
        //todo 跟新状态
        MarOrderPackageAppeal marOrderPackageAppeal = marOrderPackageAppealMapper.selectById(id);
        if("true".equals(successFlag)) {
                marOrderPackageAppeal.setAppealStatus(AppealStatusEnum.INREVIEW.getValue());
                marOrderPackageAppeal.setExceptionFlag(AppealExceptionEnum.NORMAL.getValue());
                marOrderPackageAppeal.settingDefaultUpdate();
                marOrderPackageAppealService.updateById(marOrderPackageAppeal);
                //todo 记录日志
                MarOrderPackageAppealLog marOrderPackageAppealLog = new MarOrderPackageAppealLog();
                marOrderPackageAppealLog.setAppealId(marOrderPackageAppeal.getId());
                marOrderPackageAppealLog.setOrganizationId(marOrderPackageAppeal.getOrganizationId());
                marOrderPackageAppealLog.setOperateContent("接收申诉提交结果：成功！修改状态为：申诉审核中");
                marOrderPackageAppealLog.settingDefaultCreate();
                marOrderPackageAppealLogService.save(marOrderPackageAppealLog);
                if (marOrderPackageAppeal.getTicketId() != null) {
                    //todo 调整工单状态  2.接收到状态：申诉提交成功修改工单状态为  完成。
                    ScTicket scTicket = scTicketService.selectScTicketById(marOrderPackageAppeal.getTicketId().longValue());
                    if (scTicket != null) {
                        scTicket.setTicketStatus(ScTicketConstant.TICKET_STATUS_FINISH);
                        //todo 记录工单日志
                        scTicketService.updateScTicketStatus(scTicket);
                    }
                }

        } else {
                marOrderPackageAppeal.setExceptionFlag(AppealExceptionEnum.ABNORMAL.getValue());
                marOrderPackageAppeal.settingDefaultUpdate();
                marOrderPackageAppealService.updateById(marOrderPackageAppeal);
                //todo 记录日志
                MarOrderPackageAppealLog marOrderPackageAppealLog = new MarOrderPackageAppealLog();
                marOrderPackageAppealLog.setAppealId(marOrderPackageAppeal.getId());
                marOrderPackageAppealLog.setOrganizationId(marOrderPackageAppeal.getOrganizationId());
                marOrderPackageAppealLog.setOperateContent("接收申诉提交结果：失败！，原因：" + errorMsg);
                marOrderPackageAppealLog.settingDefaultCreate();
                marOrderPackageAppealLogService.save(marOrderPackageAppealLog);
        }

    }

    /**
     * Description: 保存申诉结果
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/10/16
     */
    @Override
    @Transactional
    public void saveAppealResult(MarOrderPackageAppealResponseVo marOrderPackageAppealResponseVo) {
        if (StringUtils.isEmpty(marOrderPackageAppealResponseVo.getMaillid())) {
            log.info("temu包裹信息店铺标识为空：{}", JSONObject.toJSONString(marOrderPackageAppealResponseVo));
            return;
        }
        if(CollectionUtil.isEmpty(marOrderPackageAppealResponseVo.getParentOrderSnList())||StringUtils.isEmpty(marOrderPackageAppealResponseVo.getPackageSn())) {
            log.info("temu包裹信息缺失：{}", JSONObject.toJSONString(marOrderPackageAppealResponseVo));
            return;
        }
        String packageSn = marOrderPackageAppealResponseVo.getPackageSn();
        String parentOrderSn = marOrderPackageAppealResponseVo.getParentOrderSnList().get(0);;

        //判断店铺是否存在
//        Account accountInfo = accountService.lambdaQuery().select(Account::getId,Account::getOrgId)
//                .eq(Account::getSellerId, marOrderPackageAppealResponseVo.getMaillid())
//                .eq(Account::getType, AccountSaleChannelEnum.TEMU.getValue()).one();
        Account accountInfo = null;
        if (!StringUtils.isEmpty(parentOrderSn)) {
            SaleOrders saleorderInfo = saleOrdersMapper.selectOrderByOrderNoAndchannel(parentOrderSn, "temu");
            if (saleorderInfo == null) {
                return;
            }
            accountInfo = accountService.lambdaQuery()
                    .eq(Account::getId, saleorderInfo.getChannelId())
                    .one();
            log.info("包裹temu订单不存在：{}",JSONObject.toJSONString(marOrderPackageAppealResponseVo));
        }



        if (null == accountInfo) {
            log.info("temu包裹无系统店铺：{}", JSONObject.toJSONString(marOrderPackageAppealResponseVo));
            return;
        }

        RLock lock = redissonClient.getLock( packageSn + "_" + parentOrderSn);
        try {
        if (lock.tryLock(0, 1, TimeUnit.MINUTES)) {
                //查询
                QueryWrapper<MarOrderPackageAppeal> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("order_no", parentOrderSn);
                queryWrapper.eq("package_no", packageSn);
                List<MarOrderPackageAppeal> marOrderPackageAppealList = marOrderPackageAppealMapper.selectList(queryWrapper);
                if (CollectionUtil.isNotEmpty(marOrderPackageAppealList)) {
                    MarOrderPackageAppeal marOrderPackageAppeal = marOrderPackageAppealList.get(0);
                    try {
                        if (!StringUtils.isEmpty(marOrderPackageAppealResponseVo.getApplyTimeStr())) {
                            if (marOrderPackageAppealResponseVo.getApplyTimeStr().contains("CST")) {
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日 HH:mm z", Locale.CHINA);
                                sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
                                marOrderPackageAppeal.setApplyTime(sdf.parse(marOrderPackageAppealResponseVo.getApplyTimeStr()));
                            }else {
                                marOrderPackageAppeal.setApplyTime(DateUtil.parse(marOrderPackageAppealResponseVo.getApplyTimeStr(), "yyyy-MM-dd HH:mm:ss"));
                            }
                        }

//                        marOrderPackageAppeal.setApplyTime(StringUtils.isEmpty(marOrderPackageAppealResponseVo.getApplyTimeStr())?null: DateUtil.parse(marOrderPackageAppealResponseVo.getApplyTimeStr(),"yyyy/MM/dd HH:mm:ss"));
                        String endTime = StrUtil.nullToDefault(marOrderPackageAppealResponseVo.getApprovedTimeStr(), marOrderPackageAppealResponseVo.getRejectedTimeStr());
                        if (!StringUtils.isEmpty(endTime)) {
                            if (endTime.contains("CST")) {
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日 HH:mm z", Locale.CHINA);
                                sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
                                marOrderPackageAppeal.setEndTime(sdf.parse(endTime));
                            }else {
//                                marOrderPackageAppeal.setEndTime(DateUtil.parse(endTime,"yyyy/MM/dd HH:mm:ss"));
                                marOrderPackageAppeal.setEndTime(DateUtil.parse(endTime,"yyyy-MM-dd HH:mm:ss"));
                            }
                        }
                    } catch (Exception e) {
                      log.info("申诉时间转换异常：{}",e);
                    }
                    marOrderPackageAppealService.updateById(marOrderPackageAppeal);
                    //始终更新结束时间

                    if (marOrderPackageAppeal.getAppealStatus()!=null&&marOrderPackageAppeal.getAppealStatus().equals(AppealApiStatusEnum.getBusinessStatus(marOrderPackageAppealResponseVo.getAppealStatus()))){
                       log.info("接入状态与库中一致，不进行更新：{}",JSONObject.toJSONString(marOrderPackageAppealResponseVo));
                        return;
                    }
                        if (new Integer(1).equals(marOrderPackageAppealResponseVo.getAppealStatus())) { //1是申诉中
                            //todo 跟新状态
                            marOrderPackageAppeal.setAppealStatus(AppealStatusEnum.INREVIEW.getValue());
                            marOrderPackageAppeal.setExceptionFlag(AppealExceptionEnum.NORMAL.getValue());
                            marOrderPackageAppeal.settingDefaultUpdate();
                            marOrderPackageAppealService.updateById(marOrderPackageAppeal);
                            //todo 记录日志
                            MarOrderPackageAppealLog marOrderPackageAppealLog = new MarOrderPackageAppealLog();
                            marOrderPackageAppealLog.setAppealId(marOrderPackageAppeal.getId());
                            marOrderPackageAppealLog.setOrganizationId(marOrderPackageAppeal.getOrganizationId());
                            marOrderPackageAppealLog.setOperateContent("获取申诉结果：申诉中！");
                            marOrderPackageAppealLog.settingDefaultCreate();
                            marOrderPackageAppealLogService.save(marOrderPackageAppealLog);


                            if (marOrderPackageAppeal.getTicketId() != null) {
                                ScTicket ticket = new ScTicket();
                                ticket.setId(marOrderPackageAppeal.getTicketId());
                                ticket.setTicketStatus("CLOSED");
                                ticket.settingDefaultSystemUpdate();
                                scTicketService.updateScTicketStatus(ticket);
                            }
                        }

                        if (new Integer(2).equals(marOrderPackageAppealResponseVo.getAppealStatus())) { //2是申诉成功
                            //todo 跟新状态
                            marOrderPackageAppeal.setAppealStatus(AppealStatusEnum.SUCCESS.getValue());
                            marOrderPackageAppeal.setExceptionFlag(AppealExceptionEnum.NORMAL.getValue());
                            marOrderPackageAppeal.settingDefaultUpdate();
                            marOrderPackageAppealService.updateById(marOrderPackageAppeal);
                            //todo 记录日志
                            MarOrderPackageAppealLog marOrderPackageAppealLog = new MarOrderPackageAppealLog();
                            marOrderPackageAppealLog.setAppealId(marOrderPackageAppeal.getId());
                            marOrderPackageAppealLog.setOrganizationId(marOrderPackageAppeal.getOrganizationId());
                            marOrderPackageAppealLog.setOperateContent("获取申诉结果：成功！修改状态为：申诉成功。");
                            marOrderPackageAppealLog.settingDefaultCreate();
                            marOrderPackageAppealLogService.save(marOrderPackageAppealLog);


                            if (marOrderPackageAppeal.getTicketId() != null) {
                                ScTicket ticket = new ScTicket();
                                ticket.setId(marOrderPackageAppeal.getTicketId());
                                ticket.setTicketStatus("CLOSED");
                                ticket.settingDefaultSystemUpdate();
                                scTicketService.updateScTicketStatus(ticket);
                            }

                        }
                        if (new Integer(3).equals(marOrderPackageAppealResponseVo.getAppealStatus())) {// 3是申诉失败
                            //todo 跟新状态
                            marOrderPackageAppeal.setAppealStatus(AppealStatusEnum.FAILED.getValue());
                            marOrderPackageAppeal.setExceptionFlag(AppealExceptionEnum.NORMAL.getValue());
                            marOrderPackageAppeal.settingDefaultUpdate();
                            marOrderPackageAppealService.updateById(marOrderPackageAppeal);
                            //todo 记录日志
                            MarOrderPackageAppealLog marOrderPackageAppealLog = new MarOrderPackageAppealLog();
                            marOrderPackageAppealLog.setAppealId(marOrderPackageAppeal.getId());
                            marOrderPackageAppealLog.setOrganizationId(marOrderPackageAppeal.getOrganizationId());
                            marOrderPackageAppealLog.setOperateContent("获取申诉结果：失败！修改状态为：申诉失败。");
                            marOrderPackageAppealLog.settingDefaultCreate();
                            marOrderPackageAppealLogService.save(marOrderPackageAppealLog);

                            if (marOrderPackageAppeal.getTicketId() != null) {
                                ScTicket ticket = new ScTicket();
                                ticket.setId(marOrderPackageAppeal.getTicketId());
                                ticket.setTicketStatus("CLOSED");
                                ticket.settingDefaultSystemUpdate();
                                scTicketService.updateScTicketStatus(ticket);
                            }
                        }
                } else { //新增
                    marOrderPackageAppealService.saveMqTemurOrderPackageAppealInfo(marOrderPackageAppealResponseVo, accountInfo);
                }
            }
        } catch (Exception e) {
            log.error("获取temu包裹申诉结果失败：{}", e);
        }finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


}

