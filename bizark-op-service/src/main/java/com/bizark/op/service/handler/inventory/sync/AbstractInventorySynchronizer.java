package com.bizark.op.service.handler.inventory.sync;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.bizark.boss.api.entity.dashboard.stock.StockAccountAddressEntity;
import com.bizark.boss.api.service.stock.StockAccountAddressService;
import com.bizark.common.util.LocalDateTimeUtils;
import com.bizark.op.api.cons.RedisCons;
import com.bizark.op.api.enm.inventory.InventoryType;
import com.bizark.op.api.enm.log.LogEnums;
import com.bizark.op.api.entity.op.inventory.*;
import com.bizark.op.api.entity.op.inventory.enums.InventoryHisType;
import com.bizark.op.api.entity.op.log.ErpOperateLog;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.service.inventory.ProductChannelsInventoryService;
import com.bizark.op.api.service.log.ErpOperateLogService;
import com.bizark.op.api.service.sale.ProductChannelsSlaveService;
import com.bizark.op.common.core.domain.BaseEntity;
import com.bizark.op.common.util.BeanCopyUtils;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.common.util.UserUtils;
import com.bizark.op.service.mapper.sale.ProductChannelsMapper;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

public abstract class AbstractInventorySynchronizer implements ApplicationContextAware {

    Logger log = LoggerFactory.getLogger(AbstractInventorySynchronizer.class);

    protected ApplicationContext applicationContext;

    public abstract InventoryType getType();


    public abstract SelectInventoryBaseResponse getInventory(InventorySyncBean commonBean);

    public abstract SelectInventoryBaseResponse handleResponse(SelectInventoryBaseResponse response);


    public SyncInventoryResult sync(InventorySyncBean commonBean) {
        SelectInventoryBaseResponse response = getInventory(commonBean);
        if (Objects.isNull(response)) {
            response = new SelectInventoryBaseResponse();
            response.setStatus(SelectInventoryBaseResponse.Status.ERROR);
        }
        response.setCommonBean(commonBean);
        handleResponse(response);
        return SyncInventoryResult.buildResult(response);
    }


    public final SyncInventoryResult syncMultichannelTask(InventorySyncBean commonBean) {
        SelectInventoryBaseResponse response = buildInventoryByTask(commonBean);
        if (Objects.isNull(response)) {
            response = new SelectInventoryBaseResponse();
            response.setStatus(SelectInventoryBaseResponse.Status.ERROR);
        }
        response.setCommonBean(commonBean);
        handleResponse(response);
        return SyncInventoryResult.buildResult(response);
    }


    /**
     * 根据任务构建库存数据
     * @param account
     * @param task
     * @return
     */
    public SelectInventoryBaseResponse buildInventoryByTask(InventorySyncBean syncBean) {
        return new SelectInventoryBaseResponse();
    }

    @PostConstruct
    public void register(){
        InventorySynchronizerFactory.register(getType(), this);
    }


    public void buildErrorChannels(SelectInventoryBaseResponse baseResponse, List<ProductChannelsInventory> inventories) {
        if (CollectionUtil.isEmpty(inventories)) {
            baseResponse.appendError(baseResponse.getCommonBean().getChannels(), "未获取到库存信息");
        }
        List<Integer> channelIds = inventories.stream()
                .map(ProductChannelsInventory::getChannelId)
                .collect(Collectors.toList());
        List<ProductChannels> channels = baseResponse.getCommonBean().getChannels();

        List<ProductChannels> errorChannels = channels.stream()
                .filter(c -> !channelIds.contains(c.getId()))
                .collect(Collectors.toList());
        baseResponse.appendError(errorChannels, "未获取到库存信息");

    }
    protected void recordInventory(List<ProductChannels> channels, List<ProductChannelsInventory> inventories) {
        recordInventory(channels, inventories, false);
    }


    protected void recordInventory(List<ProductChannels> channels, List<ProductChannelsInventory> inventories, boolean updateZero) {

        if (CollectionUtil.isEmpty(inventories) || CollectionUtil.isEmpty(channels)) {
            log.error("SYNC_INVENTORY - 库存写入结束，数据为空! ");
            return;
        }

        // 数据去重，防止数据重复推送
        inventories = inventories.stream()
                .peek(inventory -> inventory.setWarehouseId(StrUtil.isBlank(inventory.getWarehouseId()) ? null : inventory.getWarehouseId()))
                .collect(Collectors.collectingAndThen(Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(v -> v.getChannelId() + " -" + v.getWarehouseId()))),
                        ArrayList::new));


        Date nowDate = DateUtils.getNowDate();
        ProductChannelsInventoryService inventoryService = applicationContext.getBean(ProductChannelsInventoryService.class);

        // 保存库存信息
        List<Integer> channelIds = inventories.stream().map(ProductChannelsInventory::getChannelId).distinct().collect(Collectors.toList());

        List<ProductChannelsInventory> inventoryList = inventoryService.lambdaQuery().in(ProductChannelsInventory::getChannelId, channelIds).list();


        Map<Integer, Integer> brforeInventoryMap = new HashMap<>();
        if (CollectionUtil.isEmpty(inventoryList)) {
            inventories.forEach(c -> this.creatAndUpdatedefault(c, nowDate));
            inventoryService.saveBatch(inventories);

        } else {
            handleInventoryUpdate(inventories, updateZero, inventoryList, brforeInventoryMap, inventoryService, nowDate);
        }
        // 处理库存数据变动
        handleProductChannelSlaves(channels);
        // 记录总库存修改日志
        recordTotalInventoryLogs(channelIds, brforeInventoryMap);
        // 修改映射总库存
//        this.modifyTotalInventory(inventories, channels, productChannelsMapper, operateLogService);

    }

    private void handleInventoryUpdate(List<ProductChannelsInventory> inventories, boolean updateZero, List<ProductChannelsInventory> inventoryList, Map<Integer, Integer> brforeInventoryMap, ProductChannelsInventoryService inventoryService, Date nowDate) {
        Map<String, ProductChannelsInventory> inventoryMap = inventoryList.stream()
                .peek(v -> v.setWarehouseId(StrUtil.isNotBlank(v.getWarehouseId()) ? v.getWarehouseId() : null))
                .collect(Collectors.toMap(k -> k.getChannelId() + k.getWarehouseId(), Function.identity(), (a, b) -> a));


        // 计算原始库存
        Map<Integer, List<ProductChannelsInventory>> channelIdMap = inventoryList.stream().collect(Collectors.groupingBy(ProductChannelsInventory::getChannelId));
        for (Map.Entry<Integer, List<ProductChannelsInventory>> entry : channelIdMap.entrySet()) {
            brforeInventoryMap.put(
                    entry.getKey(),
                    entry.getValue().stream().filter(c -> Objects.nonNull(c.getQuantity())).map((c -> Integer.parseInt(c.getQuantity()))).reduce(Integer::sum).orElse(null)
            );
        }

        // 获取删除库存信息
        if (updateZero) {
            List<String> currentChannelFlags = inventories.stream()
                    .map(c-> c.getChannelId() + c.getWarehouseId())
                    .collect(Collectors.toList());

            List<Long> zeroIds = inventoryList.stream()
                    .filter(f -> !currentChannelFlags.contains(f.getChannelId() + f.getWarehouseId()))
                    .map(ProductChannelsInventory::getId)
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(zeroIds)) {
                log.info("SYNC-INVENTORY 需要更新为0的库存ID:{}", zeroIds);
                inventoryService.lambdaUpdate()
                        .set(ProductChannelsInventory::getUpdatedAt, new Date())
                        .set(ProductChannelsInventory::getSyncLastTime, new Date())
                        .in(ProductChannelsInventory::getId, zeroIds)
                        .set(ProductChannelsInventory::getQuantity, "0")
                        .update();
            }
        }


        List<ProductChannelsInventory> insertData = new ArrayList<>();
        List<ProductChannelsInventory> updateData = new ArrayList<>();

        for (ProductChannelsInventory inventory : inventories) {

            this.updateDeafult(inventory, nowDate);

            String key = inventory.getChannelId() + inventory.getWarehouseId();

            ProductChannelsInventory productChannelsInventory = inventoryMap.get(key);

            if (Objects.nonNull(productChannelsInventory)) {
                // 原有库存
                inventory.setId(productChannelsInventory.getId());
                // 更新库存信息
                updateData.add(inventory);

                // 输出Walmart_dsv渠道更新
                if ("Walmart-DSV-US".equalsIgnoreCase(inventory.getAccountFlag())) {
                    log.info("库存更新 - {} - {}", inventory.getSellerSku(), JSON.toJSONString(inventory));
                }

                continue;
            }
            this.createDefault(inventory, nowDate);
            insertData.add(inventory);

        }

        if (CollectionUtil.isNotEmpty(insertData)) {
            inventoryService.saveBatch(insertData);
        }
        if (CollectionUtil.isNotEmpty(updateData)) {
            inventoryService.updateBatchById(updateData);
        }
    }


    protected void handleProductChannelSlaves(List<ProductChannels> channels) {
        try {
            RedissonClient redissonClient = applicationContext.getBean(RedissonClient.class);
            ProductChannelsSlaveService slaveService = applicationContext.getBean(ProductChannelsSlaveService.class);
            for (ProductChannels channel : channels) {

                RLock lock = redissonClient.getLock(RedisCons.SKU_MAP_LOCK + channel.getId());
                try {
                    lock.lock(10, TimeUnit.SECONDS);
                    slaveService.handleSlaves(channel);
                }finally {
                    lock.unlock();
                }
            }
        } catch (Exception e) {
            log.info("SKU映射从表库存处理失败 - {}", JSON.toJSONString(channels), e);
        }
    }

    protected void recordTotalInventoryLogs(List<Integer> channelIds, Map<Integer, Integer> brforeInventoryMap) {
        Integer userId = UserUtils.getCurrentUserId();
        String userName = UserUtils.getCurrentUserName();
        ProductChannelsInventoryService inventoryService = applicationContext.getBean(ProductChannelsInventoryService.class);
        ErpOperateLogService operateLogService = applicationContext.getBean(ErpOperateLogService.class);
        ThreadPoolTaskExecutor executor = applicationContext.getBean(ThreadPoolTaskExecutor.class);
        executor.execute(()->{
            Date date = new Date();
            LocalDateTime localDateTime = LocalDateTimeUtils.dateToLocalDateTime(date);
            List<ProductChannelsInventory> channelsInventories = inventoryService.selectTotalInventoryByChannelIds(channelIds);
            List<ErpOperateLog> logs = new ArrayList<>();
            for (ProductChannelsInventory inventory : channelsInventories) {
                BigDecimal beforeQuantity = brforeInventoryMap.containsKey(inventory.getChannelId())
                        ? new BigDecimal(brforeInventoryMap.get(inventory.getChannelId())) : BigDecimal.ZERO;
                BigDecimal afterQuantity = new BigDecimal(inventory.getQuantity());
                if (beforeQuantity.compareTo(afterQuantity) != 0) {
                    // 记录库存变动日志
                    ErpOperateLog operateLog = new ErpOperateLog();
                    operateLog.setOperateName("SKU映射");
                    operateLog.setOperateUserId(userId == null ? 0 : userId.longValue());
                    operateLog.setOperateUserName(userName == null ? "System" : userName);
                    operateLog.setLogType(1);
                    operateLog.setOperateAt(localDateTime);
                    operateLog.setBusinessId(inventory.getChannelId().longValue());
                    operateLog.setFieldDesc("平台库存");
                    operateLog.setOperateTable("dashboard.product_channels");
                    operateLog.setOperateOldValue(String.valueOf(beforeQuantity));
                    operateLog.setOperateNewValue(String.valueOf(afterQuantity));
                    operateLog.setOperateTarget("inventory");
                    operateLog.setOperateType(LogEnums.OperateTypeEnum.UPDATE.getValue());
                    operateLog.setCreatedBy(userId == null ? 0 : userId);
                    operateLog.setCreatedName(userName == null ? "System" : userName);
                    operateLog.setCreatedAt(date);
                    logs.add(operateLog);
                }
            }
            if (CollectionUtil.isNotEmpty(logs)) {
                operateLogService.saveBatch(logs);
            }
        });
    }




    /**
     * 总库存计算变更
     * @param inventories
     * @param channels
     * @param productChannelsMapper
     * @param operateLogService
     */
    public void modifyTotalInventory(List<ProductChannelsInventory> inventories, List<ProductChannels> channels, ProductChannelsMapper productChannelsMapper, ErpOperateLogService operateLogService) {

        Map<Integer, List<ProductChannelsInventory>> inventoryChannelMap = inventories.stream()
                .collect(Collectors.groupingBy(ProductChannelsInventory::getChannelId));


        Map<Integer, ProductChannels> channelsMap = channels.stream()
                .collect(Collectors.toMap(ProductChannels::getId, Function.identity(), (a, b) -> a));

        for (Map.Entry<Integer, List<ProductChannelsInventory>> entry : inventoryChannelMap.entrySet()) {
            if (!channelsMap.containsKey(entry.getKey())) {
                continue;
            }
            ProductChannels channel = channelsMap.get(entry.getKey());
            // 总库存
            Integer totalInventory = channel.getInventory();
            Integer updateTotalInventory = inventoryChannelMap.get(entry.getKey()).stream()
                    .map(ProductChannelsInventory::getQuantity)
                    .map(Integer::parseInt)
                    .reduce(Integer::sum)
                    .get();
            if (!totalInventory.equals(updateTotalInventory)) {
                // 更新总库存
//                productChannelsMapper.updateInventoryById(channel.getId(), updateTotalInventory);
                // 记录修改日志
                ProductChannels copyBean = BeanCopyUtils.copyBean(channel, ProductChannels.class);
                channel.setInventory(updateTotalInventory);
                operateLogService.logRecord(copyBean, channel, "SKU映射", true, false, "inventory");
            }
        }
    }

    protected static ProductChannelsInventoryHis buildInventoryHistory(ProductChannelsInventory inventory, Integer userId, String userName, Date nowDate) {
        ProductChannelsInventoryHis his = new ProductChannelsInventoryHis();
        his.setInventory(inventory.getQuantity());
        his.setCreatedBy(userId == null ? 0 : userId);
        his.setUpdatedBy(userId == null ? 0 : userId);
        his.setCreatedName(userName == null ? "System" : userName);
        his.setUpdatedName(userName == null ? "System" : userName);
        his.setCreatedAt(nowDate);
        his.setUpdatedAt(nowDate);
        his.setChannelId(inventory.getChannelId());
        his.setWarehouse(inventory.getOrgWarehouse());
        his.setWarehouseId(inventory.getWarehouseId());
        his.setType(InventoryHisType.SYNC.getCode());
        return his;
    }

    protected Map<String, String> getWarehouseMapping(String flag) {
        List<StockAccountAddressEntity> addressEntities = applicationContext.getBean(StockAccountAddressService.class).findByAccountFlag(flag);
        if (CollectionUtil.isEmpty(addressEntities)) {
            log.error("未获取到仓库映射关系！");
            return new HashMap<>();
        }

        return addressEntities.stream()
                .filter(d -> CollectionUtil.isNotEmpty(d.getItems()))
                .collect(Collectors.toMap(StockAccountAddressEntity::getAddressCode, v -> v.getItems().get(0).getOrgWarehouseCode(), (a, b) -> a));
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }


    public void updateDeafult(BaseEntity baseEntity,Date date) {
        baseEntity.setUpdatedAt(date);
        baseEntity.setUpdatedBy(0);
        baseEntity.setUpdatedName("System");
    }
    public void createDefault(BaseEntity baseEntity,Date date) {
        baseEntity.setCreatedAt(date);
        baseEntity.setCreatedBy(0);
        baseEntity.setCreatedName("System");
    }

    public void creatAndUpdatedefault(BaseEntity baseEntity,Date date) {
        updateDeafult(baseEntity, date);
        createDefault(baseEntity, date);
    }
}
