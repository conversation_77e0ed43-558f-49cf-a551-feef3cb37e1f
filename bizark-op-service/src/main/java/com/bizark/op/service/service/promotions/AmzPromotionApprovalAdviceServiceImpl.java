package com.bizark.op.service.service.promotions;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bizark.activiti.api.service.activiti.ActivitiProcessRecordsService;
import com.bizark.op.api.enm.promotions.*;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.promotions.*;
import com.bizark.op.api.request.msg.his.AmzPromotionsHisPromotionsReceiveMsg;
import com.bizark.op.api.request.msg.his.AmzPromotionsHisPromotionsSkuReceiveMsg;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.promotions.AmzPromotionPreService;
import com.bizark.op.api.service.promotions.IAmzPromotionApprovalAdviceService;
import com.bizark.op.common.util.*;
import com.bizark.op.common.util.spring.SpringUtils;
import com.bizark.op.service.mapper.promotions.*;
import com.bizark.op.service.mapper.sale.ProductChannelsMapper;
import com.bizark.op.service.util.third.qywx.QywxUtil;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.bizark.usercenter.api.parameter.user.UserDeptVO;
import com.bizark.usercenter.api.service.UcPostService;
import com.bizark.usercenter.api.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @remarks:copuon活动，promotions审批通知企业微信服务
 * @Author: Ailill
 * @Date: 2023/11/24 10:27
 */
@Slf4j
@Service
public class AmzPromotionApprovalAdviceServiceImpl implements IAmzPromotionApprovalAdviceService {

    @Autowired
    private AmzPromotionsSkuDetailMapper amzPromotionsSkuDetailMapper;

    @Autowired
    private ProductChannelsMapper productChannelsMapper;

    @Autowired
    private AmzPromotionsMapper amzPromotionsMapper;

    @Autowired
    private AmzPromotionsCouponsMapper amzPromotionsCouponsMapper;

    @Autowired
    private AmzPromotionsCouponMapper amzPromotionsCouponMapper;

    @Autowired
    private AmzPromotionsOperateLogMapper amzPromotionsOperateLogMapper;

    @Autowired
    private AmzPromotionsCouponOperateLogMapper amzPromotionsCouponOperateLogMapper;

    @Autowired
    private AmzPromotionCheckMapper amzPromotionCheckMapper;

    @Autowired
    private AccountService accountService;

    @Autowired
    private AmzPromotionPreService amzPromotionPreService;

    @Autowired
    private ActivitiProcessRecordsService activitiProcessRecordsService;

//    @Value("${promotion.advice.webhook}")
    private  String PROMOTION_MESSAGES_MASTER = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=bafc3555-31d5-4b47-848f-7059b1dbf35e";
    private static final String PROMOTION_MESSAGES = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a03f0ebd-b348-4dc3-b01d-1f5fec0f78b4";


    @Autowired
    private TemuSupplementaryReportPromotionMapper temuSupplementaryReportPromotionMapper;

    /**
     * promotion状态流转到待审批，企业微信消息通知
     *
     * @param promotionsId promotion主键Id
     * @param submitDate   提交日期
     * @param submitName   提交时间
     */
    @Async("threadPoolTaskExecutor")
    @Override
    public void promotionsApprovalAdviceWx(Long promotionsId, Date submitDate, String submitName,Integer submitId) {

        if (promotionsId == null) {
            log.error("promotion id为空");
            return;
        }
        try {
            Thread.sleep(30000L);
        } catch (InterruptedException e) {
            log.error("通知失败");
        }
        log.info("promotion id:{},企微通知当前时间:{}",promotionsId,DateUtils.getNowDate());
        AmzPromotions promotions = amzPromotionsMapper.selectById(promotionsId);
        if (promotions == null) {
            log.error("查无此promotion,id:{}", promotionsId);
            return;
        }
        boolean vcChannelCreate = promotions.getChannel().equals(1) && (promotions.getSubShopBack() == null || !promotions.getSubShopBack());

        boolean vcChannel = promotions.getChannel().equals(1) || promotions.getChannel().equals(2);
        String submitAt = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", submitDate);
        List<AmzPromotionsSkuDetail> list = amzPromotionsSkuDetailMapper.selectList(new LambdaQueryWrapper<AmzPromotionsSkuDetail>().eq(AmzPromotionsSkuDetail::getPromotionsId, promotionsId));
        if (CollectionUtil.isEmpty(list)) {
            log.error("该promotion下无sku,id:{}", promotionsId);
            return;
        }
        int size = list.size();
        String sku = list.stream().map(s -> StringUtil.isEmpty(s.getSellerSku()) ? "空" : s.getSellerSku()).collect(Collectors.joining(","));
        String asin = list.stream().map(s -> StringUtil.isEmpty(s.getAsin()) ? "空" : s.getAsin()).collect(Collectors.joining(","));
        List<String> asinList = list.stream().filter(s -> StringUtil.isNotEmpty(s.getAsin())).map(AmzPromotionsSkuDetail::getAsin).collect(Collectors.toList());
        String type = "Promotion";
        if (StringUtils.isEmpty(promotions.getInstanceId())) {
            log.error("promotion instanceId为空,id:{}", promotionsId);
            return;
        }
        qywxMsgSend(submitName, submitAt, size, sku, asin, asinList, type, promotions.getShopId(), vcChannelCreate,promotions.getInstanceId(),vcChannel ? "vc" : "temu",submitId);
    }

    @Async("threadPoolTaskExecutor")
    @Override
    public void temuSupplementaryReportPromotionsApprovalAdviceWx(Long promotionsId, Date submitDate, String submitName, Integer submitId) {

        try {
            Thread.sleep(5000L);
        } catch (InterruptedException e) {
            log.error("通知失败");
        }
        log.info("temu补报活动id--{}--企微通知当前时间--{}", promotionsId, DateUtils.getNowDate());

        TemuSupplementaryReportPromotion promotions = temuSupplementaryReportPromotionMapper.selectById(promotionsId);
        if (promotions == null) {
            for (int i = 0; i < 10; i++) {
                try {
                    Thread.sleep(2000L);
                    promotions = temuSupplementaryReportPromotionMapper.selectById(promotionsId);
                    if (promotions != null) {
                        break;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        if (promotions == null) {
            log.error("temu补报活动通知查无此promotion---id--{}", promotionsId);
            return;
        }

        String submitAt = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", submitDate);

        String sku = promotions.getSkuId();
        String asin = promotions.getSkuId();

        String type = "temu补报Promotion";
        if (StringUtils.isEmpty(promotions.getInstanceId())) {
            log.error("temu补报活动 instanceId为空--id:{}", promotionsId);
            return;
        }
        qywxMsgSend(submitName, submitAt, 1, sku, asin, Collections.singletonList(asin), type, promotions.getShopId(), false, promotions.getInstanceId(), "temu", submitId);
    }


    /**
     * coupon活动状态流转到待审批，企业微信消息通知
     *
     * @param couponsId  coupon活动id
     * @param submitDate
     * @param submitName
     */
    @Async("threadPoolTaskExecutor")
    @Override
    public void couponsApprovalAdviceWx(Long couponsId, Date submitDate, String submitName,Integer submitId) {

        if (couponsId == null) {
            log.error("coupon活动id为空");
            return;
        }
        try {
            Thread.sleep(30000L);
        } catch (InterruptedException e) {
            log.error("通知失败");
        }
        log.info("coupon活动id:{},企微通知当前时间:{}",couponsId,DateUtils.getNowDate());
        AmzPromotionsCoupons coupons = amzPromotionsCouponsMapper.selectById(couponsId);
        if (coupons == null) {
            log.error("查无此coupon活动，id:{}", couponsId);
            return;
        }
        boolean vcChannelCreate = coupons.getChannel().equals(1) && (coupons.getSubShopBack() == null || !coupons.getSubShopBack());
        boolean vcChannel = coupons.getChannel().equals(1) || coupons.getChannel().equals(2);
        if (!AmzPromotionApprovalEnum.APPROVING.getLabel().equals(coupons.getApprovalStatus())) {
            log.error("coupon活动审批状态不为待审批,id:{}", couponsId);
            return;
        }
        String submitAt = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", submitDate);
        AmzPromotionsCoupon coupon = new AmzPromotionsCoupon();
        coupon.setCouponId(couponsId);
        List<AmzPromotionsCoupon> list = amzPromotionsCouponMapper.selectAmzPromotionsCouponList(coupon);
        if (CollectionUtil.isEmpty(list)) {
            log.error("该coupon活动下无优惠券,id:{}", couponsId);
            return;
        }
        int size = list.size();
        String sku = list.stream().map(s -> StringUtil.isEmpty(s.getSellerSku()) ? "空" : s.getSellerSku()).collect(Collectors.joining(","));
        String asin = list.stream().map(s -> StringUtil.isEmpty(s.getAsin()) ? "空" : s.getAsin()).collect(Collectors.joining(","));
        List<String> asinList = list.stream().filter(s -> StringUtil.isNotEmpty(s.getAsin())).map(AmzPromotionsCoupon::getAsin).collect(Collectors.toList());
        String type = "Coupon";
        if (StringUtils.isEmpty(coupons.getInstanceId())) {
            log.error("coupon活动instanceId为空,id:{}", couponsId);
            return;
        }
        qywxMsgSend(submitName, submitAt, size, sku, asin, asinList, type, coupons.getShopId(), vcChannelCreate,coupons.getInstanceId(),vcChannel ? "vc" : "temu",submitId);
    }


    /**
     * coupon优惠券状态流转到待审批，企业微信消息通知
     *
     * @param couponId   coupon优惠券id
     * @param submitDate
     * @param submitName
     */
    @Override
    @Async("threadPoolTaskExecutor")
    public void couponApprovalAdviceWx(Long couponId, Date submitDate, String submitName,Integer submitId) {

        if (couponId == null) {
            log.error("coupon优惠券id为空");
            return;
        }
        try {
            Thread.sleep(15000L);
        } catch (InterruptedException e) {
            log.error("通知失败");
        }
        AmzPromotionsCoupon coupon = amzPromotionsCouponMapper.selectAmzPromotionsCouponById(couponId);
        if (coupon == null) {
            log.error("查无此coupon优惠券,id:{}", couponId);
            return;
        }
        AmzPromotionsCoupons amzPromotionsCoupons = amzPromotionsCouponsMapper.selectById(coupon.getCouponId());
        String submitAt = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", submitDate);

        String type = "Coupon";
        if (StringUtils.isEmpty(amzPromotionsCoupons.getInstanceId())) {
            log.error("coupon活动instanceId为空--id--{}--优惠券id--{}", amzPromotionsCoupons.getId(), couponId);
            return;
        }
//        boolean vcChannel = amzPromotionsCoupons.getChannel().equals(1) || amzPromotionsCoupons.getChannel().equals(2);
        qywxMsgSend(submitName, submitAt, 1, coupon.getSellerSku(), coupon.getAsin(), Arrays.asList(coupon.getAsin()), type, coupon.getShopId(), false,amzPromotionsCoupons.getInstanceId(),"vc",submitId);
    }

    public void sendMsgToGroupChat(String msg) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msgtype", "text");
        JSONObject jsonObjectContent = new JSONObject();
        jsonObjectContent.put("content", msg);
        jsonObject.put("text", jsonObjectContent);
        HttpUtils.doPostByForm(PROMOTION_MESSAGES_MASTER, jsonObject.toString());
    }

    /**
     * @param submitName      提交人
     * @param submitAt        提交时间
     * @param size            sku数量
     * @param sku             sku
     * @param asin            asin
     * @param asinList        asin集合
     * @param type            类型
     * @param vcChannelCreate
     */
    public void qywxMsgSend(String submitName, String submitAt, int size, String sku, String asin, List<String> asinList, String type, Long shopId, boolean vcChannelCreate,String instanceId, String source,Integer submitId) {
        /*List<AmzPromotionCheck> productChannels = amzPromotionCheckMapper.selectListByShopIdOrAsinOrSellerSku(shopId, StringUtil.isNotEmpty(asin) ? asin.split(",") : null, null);
        String brand = null;
        if (CollectionUtil.isNotEmpty(productChannels)) {
            String collect = productChannels.stream().filter(s -> StringUtil.isNotEmpty(s.getBrand())).map(AmzPromotionCheck::getBrand).distinct().collect(Collectors.joining(","));
            brand = StringUtil.isNotEmpty(collect) ? collect : "无品牌";
        } else {
            log.error("企业微信消息通知发送失败,sku无对应运营");
            return;
        }
        List<Integer> collect = productChannels.stream().filter(s -> s.getOperationUserId() != null).map(AmzPromotionCheck::getOperationUserId).distinct().collect(Collectors.toList());
        Integer operatorManagerIdByOperatorId = amzPromotionPreService.getOperatorManagerIdByOperatorId(collect.get(0));
//        UserDeptVO operationMangerById = SpringUtils.getBean(UcPostService.class).getOperationManagerById(collect.get(0));
        if (operatorManagerIdByOperatorId == null) {
            log.error("运营无对应上级,运营id为:{}", collect.get(0));
            return;
        }
        log.info("#运营用户ID#: {}，#运营上级ID#：{}",collect.get(0), operatorManagerIdByOperatorId);
        //UserEntity byName = SpringUtils.getBean(UserService.class).getByName(operationMangerById.getUserName());
        UserEntity byName = SpringUtils.getBean(UserService.class).getById(operatorManagerIdByOperatorId);
//        UserEntity byName = SpringUtils.getBean(UserService.class).getByName("Annie");
        if (StringUtil.isNotEmpty(sku)) {
            String[] split = sku.split(",");
            List<String> sellerSkuList = Arrays.asList(split).stream().filter(r -> !"空".equals(r)).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(sellerSkuList)) {
                List<AmzPromotionCheck> pList = amzPromotionCheckMapper.selectListByShopIdOrAsinOrSellerSku(shopId, StringUtil.isNotEmpty(asin) ? asin.split(",") : null, StringUtil.isNotEmpty(sku) ? sku.split(",") : null);
                if (CollectionUtil.isNotEmpty(pList)) {
                    String[] asinSplit = asin.split(",");
                    List<String> skuList = new ArrayList<>();
                    for (String asinSplit1 : asinSplit) {
                        List<String> asinErpSku = pList.stream().filter(r -> asinSplit1.equals(r.getAsin()) && StringUtil.isNotEmpty(r.getErpSku())).map(AmzPromotionCheck::getErpSku).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(asinErpSku)) {
                            skuList.add(asinErpSku.get(0));
                        }
                    }
                    sku = skuList.stream().collect(Collectors.joining(";"));
                } else {
                    sku = "空";
                }

            } else {
                sku = "空";
            }

        } else {
            sku = "空";
        }
        if (size > 10) {
            if (!"空".equals(sku)) {
                sku = Arrays.stream(sku.split(";")).limit(10).collect(Collectors.joining(";")) + "...";
            }
            asin = Arrays.stream(asin.split(",")).limit(10).collect(Collectors.joining(",")) + "...";
        }

        String context = String.format("提交人:%s 提交时间:%s 促销活动类型:%s 促销ASIN:%s SKU:%s 品牌:%s ", submitName, submitAt, type, asin, sku, brand);
        log.info("消息发送开始:{},准备发给:{},phone为:{}", context, byName.getName(), byName.getPhone());
        QywxUtil.wxMessageSend(context, byName.getPhone());
        if (vcChannelCreate) {
            QywxUtil.wxMessageSend(context, "17614565710");
        }*/


        List<AmzPromotionCheck> productChannels = amzPromotionCheckMapper.selectListByShopIdOrAsinOrSellerSku(shopId, StringUtil.isNotEmpty(asin) ? asin.split(",") : null, null);
        String brand = null;
        if (CollectionUtil.isNotEmpty(productChannels)) {
            String collect = productChannels.stream().filter(s -> StringUtil.isNotEmpty(s.getBrand())).map(AmzPromotionCheck::getBrand).distinct().collect(Collectors.joining(","));
            brand = StringUtil.isNotEmpty(collect) ? collect : "无品牌";
        }
        Set<Integer> operatorIdParent = amzPromotionPreService.getOperatorManagerIdByInstanceId(instanceId,source,submitId);
        if (CollectionUtil.isEmpty(operatorIdParent)) {
            log.error("运营无对应上级,实例id为:{}", instanceId);
            return;
        }
        log.info("#实例ID#: {}，#运营上级ID#：{}",instanceId, operatorIdParent);

        List<UserEntity> byName = SpringUtils.getBean(UserService.class).findByIds(new ArrayList<>(operatorIdParent));

        if (StringUtil.isNotEmpty(sku)) {
            String[] split = sku.split(",");
            List<String> sellerSkuList = Arrays.asList(split).stream().filter(r -> !"空".equals(r)).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(sellerSkuList)) {
                List<AmzPromotionCheck> pList = amzPromotionCheckMapper.selectListByShopIdOrAsinOrSellerSku(shopId, StringUtil.isNotEmpty(asin) ? asin.split(",") : null, StringUtil.isNotEmpty(sku) ? sku.split(",") : null);
                if (CollectionUtil.isNotEmpty(pList)) {
                    String[] asinSplit = asin.split(",");
                    List<String> skuList = new ArrayList<>();
                    for (String asinSplit1 : asinSplit) {
                        List<String> asinErpSku = pList.stream().filter(r -> asinSplit1.equals(r.getAsin()) && StringUtil.isNotEmpty(r.getErpSku())).map(AmzPromotionCheck::getErpSku).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(asinErpSku)) {
                            skuList.add(asinErpSku.get(0));
                        }
                    }
                    sku = skuList.stream().collect(Collectors.joining(";"));
                } else {
                    sku = "空";
                }

            } else {
                sku = "空";
            }

        } else {
            sku = "空";
        }
        if (size > 10) {
            if (!"空".equals(sku)) {
                sku = Arrays.stream(sku.split(";")).limit(10).collect(Collectors.joining(";")) + "...";
            }
            asin = Arrays.stream(asin.split(",")).limit(10).collect(Collectors.joining(",")) + "...";
        }

        String context = String.format("提交人:%s 提交时间:%s 促销活动类型:%s 促销ASIN:%s SKU:%s 品牌:%s ", submitName, submitAt, type, asin, sku, brand);
        log.info("消息发送开始:{},准备发给:{}", context, operatorIdParent);
        sendMsgToGroupChat(context + ":审批人:"+ byName.stream().map(UserEntity::getName).collect(Collectors.joining(",")));
        /*boolean flag = false;
        for (UserEntity userEntity : byName) {
            if ("17614565710".equals(userEntity.getPhone())) {
                flag = true;
            }
            QywxUtil.wxMessageSend(context, userEntity.getPhone());
        }
        if (vcChannelCreate && !flag) {
            QywxUtil.wxMessageSend(context, "17614565710");
        }*/
    }


    /**
     * 定时查询promotion取消异常、修改异常超过提交时间1小时、、提交中、修改中超过15分钟、取消中超过15分钟的通知到群
     */
    @Override
    public void promotionGroupChatAdvice() {

        Date nowDate = DateUtils.getNowDate();
        Integer[] promotionsState = new Integer[]{
                AmzPromotionsStateEnum.CANCEL_EXCEPTION.value(),
                AmzPromotionsStateEnum.MODIFY_EXCEPTION.value(),
                AmzPromotionsStateEnum.SUBMITTING.value(),
                AmzPromotionsStateEnum.MODIFYING.value(),
                AmzPromotionsStateEnum.CANCELING.value()};
        List<AmzPromotions> byPromotionsStateIn = amzPromotionsMapper.selectList(new LambdaQueryWrapper<AmzPromotions>().in(AmzPromotions::getPromotionsState, Arrays.asList(promotionsState)));
        if (CollectionUtil.isEmpty(byPromotionsStateIn)) {
            log.error("无取消异常，修改异常，提交中，修改中，取消中的promotion");
            return;
        }
        List<AmzPromotions> cancelException = byPromotionsStateIn.stream().filter(r -> AmzPromotionsStateEnum.CANCEL_EXCEPTION.value().equals(r.getPromotionsState())).collect(Collectors.toList());
        promotionSendMsg(nowDate, cancelException, "取消异常超过提交时间一小时", 60);

        List<AmzPromotions> modifyException = byPromotionsStateIn.stream().filter(r -> AmzPromotionsStateEnum.MODIFY_EXCEPTION.value().equals(r.getPromotionsState()) && AmzPromotionApprovalEnum.APPROVED.getLabel().equals(r.getApprovalStatus())).collect(Collectors.toList());
        promotionSendMsg(nowDate, modifyException, "修改异常超过提交时间一小时", 60);

        List<AmzPromotions> submitting = byPromotionsStateIn.stream().filter(r -> AmzPromotionsStateEnum.SUBMITTING.value().equals(r.getPromotionsState())).collect(Collectors.toList());
        promotionSendMsg(nowDate, submitting, "提交中超过提交时间15分钟", 15);

        List<AmzPromotions> modifying = byPromotionsStateIn.stream().filter(r -> AmzPromotionsStateEnum.MODIFYING.value().equals(r.getPromotionsState())).collect(Collectors.toList());
        promotionSendMsg(nowDate, modifying, "修改中超过提交时间15分钟", 15);

        List<AmzPromotions> canceling = byPromotionsStateIn.stream().filter(r -> AmzPromotionsStateEnum.CANCELING.value().equals(r.getPromotionsState())).collect(Collectors.toList());
        promotionSendMsg(nowDate, canceling, "取消中超过提交时间15分钟", 15);
    }

    /**
     * 定时查询coupon取消异常、修改异常、超过提交时间1小时、提交中、修改中超过15分钟、取消中超过15分钟的通知到群
     */
    @Override
    public void couponsGroupChatAdvice() {

        Date nowDate = DateUtils.getNowDate();
        Integer[] couponState = new Integer[]{
                AmzCouponStateEnum.CANCELING_EXCEPTION.value(),
                AmzCouponStateEnum.MODIFY_EXCEPTION.value(),
                AmzCouponStateEnum.SUBMITTING.value(),
                AmzCouponStateEnum.MODIFYING.value(),
                AmzCouponStateEnum.CANCELING.value()
        };

        List<AmzPromotionsCoupon> list = amzPromotionsCouponMapper.selectListByState(couponState);
        if (CollectionUtil.isEmpty(list)) {
            log.error("无取消异常，修改异常，提交中，修改中，取消中的coupon");
            return;
        }
        List<AmzPromotionsCoupon> cancelException = list.stream().filter(r -> AmzCouponStateEnum.CANCELING_EXCEPTION.value().equals(r.getCouponState())).collect(Collectors.toList());
        couponSendMsg(nowDate, cancelException, AmzCouponOperateTypeEnum.RPA_CANCEL_COUPON_OR_ACTIVE.value(), "取消异常超过提交时间一小时", 1, 60);
        List<AmzPromotionsCoupon> modifyException = list.stream().filter(r -> AmzCouponStateEnum.MODIFY_EXCEPTION.value().equals(r.getCouponState()) && AmzPromotionApprovalEnum.APPROVED.getLabel().equals(r.getApprovalStatus())).collect(Collectors.toList());
        couponSendMsg(nowDate, modifyException, AmzCouponOperateTypeEnum.RPA_MODIFY_COUPON.value(), "修改异常超过提交时间一小时", 2, 60);
        List<AmzPromotionsCoupon> submitting = list.stream().filter(r -> AmzCouponStateEnum.SUBMITTING.value().equals(r.getCouponState())).collect(Collectors.toList());
        couponSendMsg(nowDate, submitting, null, "提交中超过提交时间15分钟", 3, 15);
        List<AmzPromotionsCoupon> modifying = list.stream().filter(r -> AmzCouponStateEnum.MODIFYING.value().equals(r.getCouponState())).collect(Collectors.toList());
        couponSendMsg(nowDate, modifying, null, "修改中超过提交时间15分钟", 3, 15);
        List<AmzPromotionsCoupon> canceling = list.stream().filter(r -> AmzCouponStateEnum.CANCELING.value().equals(r.getCouponState())).collect(Collectors.toList());
        couponSendMsg(nowDate, canceling, null, "取消中超过提交时间15分钟", 3, 15);

    }


    private void promotionSendMsg(Date nowDate, List<AmzPromotions> amzPromotions, String msg, int time) {
        if (CollectionUtil.isNotEmpty(amzPromotions)) {
            List<Long> promotionIdList = amzPromotions.stream().map(AmzPromotions::getId).distinct().collect(Collectors.toList());
            List<AmzPromotionsSkuDetail> skuDetailList = amzPromotionsSkuDetailMapper.selectList(new LambdaQueryWrapper<AmzPromotionsSkuDetail>().in(AmzPromotionsSkuDetail::getPromotionsId, promotionIdList));
            for (AmzPromotions promotions : amzPromotions) {
                AmzPromotionsOperateLog pLog = new AmzPromotionsOperateLog();
                pLog.setPromotionId(promotions.getId());
                pLog.setOrganizationId(promotions.getOrganizationId());
                List<AmzPromotionsOperateLog> logList = amzPromotionsOperateLogMapper.selectAmzPromotionsOperateLogList(pLog);
                if (CollectionUtil.isNotEmpty(logList)) {
                    AmzPromotionsOperateLog log1 = logList.stream().sorted(Comparator.comparing(AmzPromotionsOperateLog::getCreatedAt).reversed()).findFirst().orElse(null);
                    if (log1 != null) {
                        if (DateUtil.getBetweenMinute(log1.getCreatedAt(), nowDate) > time) {
                            if (StringUtil.isNotEmpty(promotions.getPromotionsId())) {
                                sendMsgToGroupChat(String.format("promotion: %s %s", promotions.getPromotionsId(), msg));
                            } else {
                                String asinList = skuDetailList.stream().filter(r -> promotions.getId().equals(r.getPromotionsId())).map(AmzPromotionsSkuDetail::getAsin).collect(Collectors.joining(","));
                                String[] split = asinList.split(",");
                                if (split.length > 10) {
                                    asinList = Arrays.stream(split).limit(10).collect(Collectors.joining(",")) + "...";
                                }
                                String promotionId = asinList + "开始时间: " + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", promotions.getBeginTime());
                                if (promotions.getEndTime() != null) {
                                    promotionId = promotionId + " ~ 结束时间: " + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", promotions.getEndTime());
                                }
                                sendMsgToGroupChat(String.format("promotion: %s %s", promotionId, msg));
                            }
                        }
                    }
                }
            }
        }
    }


    private void couponSendMsg(Date nowDate, List<AmzPromotionsCoupon> amzPromotionsCoupon, Integer operateType, String msg, Integer type, int time) {

        if (CollectionUtil.isNotEmpty(amzPromotionsCoupon)) {

            List<Long> couponsIdList = amzPromotionsCoupon.stream().map(AmzPromotionsCoupon::getCouponId).distinct().collect(Collectors.toList());
            List<AmzPromotionsCoupons> couponsList = amzPromotionsCouponsMapper.selectAmzPromotionsCouponsByIds(couponsIdList.toArray(new Long[couponsIdList.size()]));
            for (AmzPromotionsCoupon coupon : amzPromotionsCoupon) {
                if (type == 1) {
                    AmzPromotionsCouponOperateLog log1 = new AmzPromotionsCouponOperateLog();
                    log1.setCouponsId(coupon.getCouponId());
                    log1.setOperateType(operateType);
                    log1.setResult("fail");
                    log1.setOrganizationId(coupon.getOrganizationId());
                    List<AmzPromotionsCouponOperateLog> logList = amzPromotionsCouponOperateLogMapper.selectAmzPromotionsCouponOperateLogList(log1);
                    if (CollectionUtil.isNotEmpty(logList)) {
                        couponLogMsgSend(nowDate, msg, time, couponsList, coupon, logList);
                    } else {
                        couponExceptionMsg(nowDate, msg, time, couponsList, coupon, AmzCouponOperateTypeEnum.RPA_CANCEL_COUPON.value());
                    }
                } else if (type == 2) {
                    couponExceptionMsg(nowDate, msg, time, couponsList, coupon, operateType);
                } else {
                    AmzPromotionsCouponOperateLog log1 = new AmzPromotionsCouponOperateLog();
                    log1.setCouponsId(coupon.getCouponId());
                    List<AmzPromotionsCouponOperateLog> logList = amzPromotionsCouponOperateLogMapper.selectAmzPromotionsCouponOperateLogList(log1);
                    couponLogMsgSend(nowDate, msg, time, couponsList, coupon, logList);
                }

            }
        }

    }

    private void couponExceptionMsg(Date nowDate, String msg, int time, List<AmzPromotionsCoupons> couponsList, AmzPromotionsCoupon coupon, Integer operateType) {
        AmzPromotionsCouponOperateLog log2 = new AmzPromotionsCouponOperateLog();
        log2.setCouponsId(coupon.getCouponId());
        log2.setCouponId(coupon.getId());
        log2.setOperateType(operateType);
        log2.setResult("fail");
        log2.setOrganizationId(coupon.getOrganizationId());
        List<AmzPromotionsCouponOperateLog> logList1 = amzPromotionsCouponOperateLogMapper.selectAmzPromotionsCouponOperateLogList(log2);
        if (CollectionUtil.isNotEmpty(logList1)) {
            couponLogMsgSend(nowDate, msg, time, couponsList, coupon, logList1);
        }
    }

    private void couponLogMsgSend(Date nowDate, String msg, int time, List<AmzPromotionsCoupons> couponsList, AmzPromotionsCoupon coupon, List<AmzPromotionsCouponOperateLog> logList1) {

        AmzPromotionsCouponOperateLog operateLog = logList1.stream().sorted(Comparator.comparing(AmzPromotionsCouponOperateLog::getCreatedAt).reversed()).findFirst().orElse(null);
        if (operateLog != null) {
            if (DateUtil.getBetweenMinute(operateLog.getCreatedAt(), nowDate) > time) {
                AmzPromotionsCoupons coupons = couponsList.stream().filter(r -> coupon.getCouponId().equals(r.getId())).findFirst().orElse(new AmzPromotionsCoupons());
                sendMsgToGroupChat(String.format("coupon活动: %s 优惠券名称: %s %s", coupons.getPromotionName(), coupon.getCouponName(), msg));
            }
        } else {
            log.error("coupon优惠券无相关日志:{}", coupon.getCouponName());
        }


    }

    /**
     * 定时更新 和 同步返回 的数据，如果状态是 approved but not featured 或者 approved but needs your attention，
     * 且当前不是这两种状态的，则给运营发送 提醒。
     * 提醒内容：Promotion ID：XXXXX，当前状态为：XXXX，异常内容：取张冉返回的 detail信息。
     *
     * @param promotionsMsg
     * @param amzPromotions
     * @param type          1 定时更新返回，2 同步返回
     */
    @Override
    @Async("threadPoolTaskExecutor")
    public void promotionExceptionListener(AmzPromotionsHisPromotionsReceiveMsg promotionsMsg, AmzPromotions amzPromotions, Integer type) {

        Integer returnState = promotionsMsg.getPromotionState();
        Integer nowState = amzPromotions.getPromotionsState();
        List<AmzPromotionsHisPromotionsSkuReceiveMsg> skuList = promotionsMsg.getSkuList();
        if (AmzPromotionsStateEnum.APPROVED_BUT_NOT_FEATURED.value().equals(returnState) || AmzPromotionsStateEnum.APPROVED_BUT_NEEDS_YOUR_ATTENTION.value().equals(returnState) || AmzPromotionsStateEnum.NEED_YOUR_ATTENTION.value().equals(returnState)) {
            if (!returnState.equals(nowState)) {
//                String context = String.format("Promotion ID:%s,当前状态:%s,异常内容:%s", amzPromotions.getPromotionsId(), AmzPromotionsStateEnum.labelOf(returnState), StringUtil.isEmpty(promotionsMsg.getDetail()) ? "空" : promotionsMsg.getDetail());
                List<String> asinList = skuList.stream().filter(r -> StringUtil.isNotEmpty(r.getAsin())).map(AmzPromotionsHisPromotionsSkuReceiveMsg::getAsin).distinct().collect(Collectors.toList());
                if (CollectionUtil.isEmpty(asinList)) {
                    log.error("Promotion ID返回的asin为空:{}", amzPromotions.getPromotionsId());
                    return;
                }
                List<AmzPromotionCheck> checks = amzPromotionCheckMapper.selectListByShopIdOrAsinOrSellerSku(amzPromotions.getShopId(), asinList.toArray(new String[asinList.size()]), null);
                if (CollectionUtil.isEmpty(checks)) {
                    log.error("Promotion ID对应的asin所属运营为空:{}", amzPromotions.getPromotionsId());
                    return;
                }
                List<Integer> operatorUserIdList = checks.stream().filter(t->t.getOperationUserId() != null).map(AmzPromotionCheck::getOperationUserId).distinct().collect(Collectors.toList());
                if (CollectionUtil.isEmpty(operatorUserIdList)) {
                    log.error("Promotion ID对应asin所属运营为空:{}", amzPromotions.getPromotionsId());
                    return;
                }
                Map<String, List<AmzPromotionCheck>> collect = checks.stream().collect(Collectors.groupingBy(AmzPromotionCheck::getAsin));
                String asinContext = String.join(",", collect.keySet());
                String skuContext = collect.values().stream().map(r -> r.stream().map(AmzPromotionCheck::getErpSku).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.joining(","))).filter(StringUtils::isNotEmpty).collect(Collectors.joining(";"));
                String context = String.format("Promotion ID:%s, 促销类型:%s, 创建时间:%s, Asin:%s, SKU:%s, 当前状态:%s,异常内容:%s", amzPromotions.getPromotionsId(), AmzPromotionsTypeEnum.labelOf(amzPromotions.getPromotionsType()), amzPromotions.getCreatedAt(), asinContext, StringUtils.isNotEmpty(skuContext) ? skuContext : "空", AmzPromotionsStateEnum.labelOf(returnState), StringUtil.isEmpty(promotionsMsg.getDetail()) ? "空" : promotionsMsg.getDetail());
                UserService userService = SpringUtils.getBean(UserService.class);
                for (Integer operatorUserId : operatorUserIdList) {
                    UserEntity userEntity = userService.getById(operatorUserId);
                    if (userEntity == null) {
                        log.error("Promotion ID对应asin所属运营为空:{},运营id为:{}", amzPromotions.getPromotionsId(), operatorUserId);
                        continue;
                    }
                    if (StringUtil.isEmpty(userEntity.getPhone())) {
                        log.error("Promotion ID对应asin所属运营的手机号为空:{},运营id为:{},运营名称为:{}", amzPromotions.getPromotionsId(), operatorUserId, userEntity.getName());
                        continue;
                    }
                    String phoneVerify = "^1[3-9]\\d{9}$";
                    if (!userEntity.getPhone().matches(phoneVerify)) {
                        log.error("Promotion ID对应asin所属运营的手机号校验不通过:{},运营id为:{},运营名称为:{},手机号为:{}", amzPromotions.getPromotionsId(), operatorUserId, userEntity.getName(), userEntity.getPhone());
                        continue;
                    }
                    log.info("promotion企业微信消息发送开始,准备发给:{},手机号为:{},消息内容为:{},来源:{}", userEntity.getName(), userEntity.getPhone(), context, type == 1 ? "RPA定时更新返回" : "同步返回");
                    QywxUtil.wxMessageSend(context, userEntity.getPhone());
                }
            }
        }
    }


    /**
     * promotion返回提交异常、提交失败、修改异常、取消异常 返回后通知到ASIN对应的运营
     *
     * @param id
     */
    @Override
    @Async("threadPoolTaskExecutor")
    public void promotionSubmitExceptionAdvice(Long id, String detail) {

        try {
            //回滚数据后通知
            Thread.sleep(10000L);
        } catch (InterruptedException e) {
            log.error("返回异常通知失败,id:{}", id);
            return;
        }
        AmzPromotions promotions = amzPromotionsMapper.selectById(id);
        String promotionsState = AmzPromotionsStateEnum.labelOf(promotions.getPromotionsState());
        String promotionsId = promotions.getPromotionsId();
        String beginDate = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", promotions.getBeginTime());
        String endDate = promotions.getEndTime() == null ? "空" : DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", promotions.getEndTime());
        List<AmzPromotionsSkuDetail> skuDetailList = amzPromotionsSkuDetailMapper.selectList(new LambdaQueryWrapper<AmzPromotionsSkuDetail>().eq(AmzPromotionsSkuDetail::getPromotionsId, id));
        List<String> asinList = skuDetailList.stream().filter(r -> StringUtil.isNotEmpty(r.getAsin())).map(AmzPromotionsSkuDetail::getAsin).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(asinList)) {
            log.error("promotion状态为:{},asin为空,id为:{}", promotionsState, id);
            return;
        }
        List<AmzPromotionCheck> checks = amzPromotionCheckMapper.selectListByShopIdOrAsinOrSellerSku(promotions.getShopId(), asinList.toArray(new String[asinList.size()]), null);
        if (CollectionUtil.isEmpty(checks)) {
            log.error("promotion状态为:{},asin所属运营为空,id为:{}", promotionsState, id);
            return;
        }
        List<Integer> operatorUserIdList = checks.stream().filter(t -> t.getOperationUserId() != null).map(AmzPromotionCheck::getOperationUserId).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(operatorUserIdList)) {
            log.error("promotion状态为:{},asin所属运营id为空,id为:{}", promotionsState, id);
            return;
        }
        Map<String, List<AmzPromotionCheck>> collect = checks.stream().collect(Collectors.groupingBy(AmzPromotionCheck::getAsin));
        String asinContext = String.join(",", collect.keySet());
        String skuContext = collect.values().stream().map(r -> r.stream().map(AmzPromotionCheck::getErpSku).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.joining(","))).filter(StringUtils::isNotEmpty).collect(Collectors.joining(";"));
        String context = String.format("促销时间:%s至%s, ASIN:%s, SKU:%s, 状态为:%s, 错误信息提示:%s", beginDate, endDate , asinContext, StringUtils.isNotEmpty(skuContext) ? skuContext : "空", promotionsState, StringUtil.isNotEmpty(detail) ? detail : "空。");
        if (StringUtil.isNotEmpty(promotionsId)) {
            context = "Promotion ID:" + promotionsId + "," + context;
        }
        if (AmzPromotionsStateEnum.CANCEL_EXCEPTION.value().equals(promotions.getPromotionsState()) ||
                AmzPromotionsStateEnum.MODIFY_EXCEPTION.value().equals(promotions.getPromotionsState())) {
            context = context + "如确认店铺后台无法操作，请操作同步来更新状态。";
        }

        UserService userService = SpringUtils.getBean(UserService.class);
        for (Integer operatorUserId : operatorUserIdList) {
            UserEntity userEntity = userService.getById(operatorUserId);
            if (userEntity == null) {
                log.error("异常状态:{},返回的asin所属运营为空,id为:{},运营id为:{}", promotionsState, id, operatorUserId);
                continue;
            }
            if (StringUtil.isEmpty(userEntity.getPhone())) {
                log.error("异常状态:{},返回的asin所属运营的手机号为空,id为:{},运营id为:{},运营名称为:{}", promotionsState, id, operatorUserId, userEntity.getName());
                continue;
            }
            String phoneVerify = "^1[3-9]\\d{9}$";
            if (!userEntity.getPhone().matches(phoneVerify)) {
                log.error("异常状态:{},返回的asin所属运营的手机号校验不通过,id为:{},运营id为:{},运营名称为:{},手机号为:{}", promotionsState, id, operatorUserId, userEntity.getName(), userEntity.getPhone());
                continue;
            }
            log.info("promotion返回异常状态:{},通知开始,准备发给:{},手机号为:{},消息内容为:{}", promotionsState, userEntity.getName(), userEntity.getPhone(), context);
                    QywxUtil.wxMessageSend(context, userEntity.getPhone());
        }
    }


    /**
     * coupon返回提交异常、修改异常、取消异常 返回后通知到ASIN对应的运营
     * @param couponsId
     * @param couponId
     * @param detail
     * @param type 1:提交coupon活动异常，2：修改coupon异常 3：取消coupon活动异常 4：取消coupon异常
     */
    @Override
    @Async("threadPoolTaskExecutor")
    public void couponSubmitExceptionAdvice(Long couponsId, Long couponId, String detail, Integer type) {

        try {
            //回滚数据后通知
            Thread.sleep(10000L);
        } catch (InterruptedException e) {
            log.error("返回异常通知失败,couponsId:{},couponId:{}", couponsId, couponId);
            return;
        }
        AmzPromotionsCoupons coupons = amzPromotionsCouponsMapper.selectAmzPromotionsCouponsById(couponsId);
        String couponActiveState = AmzCouponActiveStateEnum.labelOf(coupons.getActiveState());
        String promotionName = coupons.getPromotionName();
        AmzPromotionsCoupon coupon = null;
        String couponState = null;
        String couponName = null;
        if (couponId != null) {
            coupon = amzPromotionsCouponMapper.selectAmzPromotionsCouponById(couponId);
            couponState = AmzCouponStateEnum.labelOf(coupon.getCouponState());
            couponName = coupon.getCouponName();
        }

        AmzPromotionsCoupon tempCoupon = new AmzPromotionsCoupon();
        tempCoupon.setCouponId(couponsId);
        List<AmzPromotionsCoupon> couponList = amzPromotionsCouponMapper.selectAmzPromotionsCouponList(tempCoupon);

        String beginDate = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", coupons.getBeginDate());
        String endDate = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", coupons.getEndDate());

        List<String> asinList = couponList.stream().filter(r -> StringUtil.isNotEmpty(r.getAsin())).map(AmzPromotionsCoupon::getAsin).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(asinList)) {
            log.error("coupon优惠券列表为空，活动id为:{}", couponsId);
            return;
        }
        List<AmzPromotionCheck> checks = amzPromotionCheckMapper.selectListByShopIdOrAsinOrSellerSku(coupons.getShopId(), asinList.toArray(new String[asinList.size()]), null);
        if (CollectionUtil.isEmpty(checks)) {
            log.error("coupon活动id为:{},asin所属运营为空", couponsId);
            return;
        }
        List<Integer> operatorUserIdList = checks.stream().filter(t -> t.getOperationUserId() != null).map(AmzPromotionCheck::getOperationUserId).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(operatorUserIdList)) {
            log.error("coupon活动id为:{},asin所属运营id为空", couponsId);
            return;
        }
        Map<String, List<AmzPromotionCheck>> collect = checks.stream().collect(Collectors.groupingBy(AmzPromotionCheck::getAsin));
        String asinContext = String.join(",", collect.keySet());
        String skuContext = collect.values().stream().map(r -> r.stream().map(AmzPromotionCheck::getErpSku).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.joining(","))).filter(StringUtils::isNotEmpty).collect(Collectors.joining(";"));
        String context = String.format("促销时间:%s至%s, ASIN:%s, SKU:%s", beginDate, endDate , asinContext, StringUtils.isNotEmpty(skuContext) ? skuContext : "空");
        if (type == 1) {
            context = "coupon活动名称:" + promotionName + "," + context + "," + "coupon活动状态为:" + couponActiveState + "," + "错误信息提示:" + (StringUtil.isNotEmpty(detail) ? detail : "空") + ";";
        } else if (type == 2) {
            context = "coupon名称:" + couponName + "," + "coupon活动名称:" + promotionName +  "," + context + "," + "coupon状态为:" +couponState + "," + "错误信息提示:" + (StringUtil.isNotEmpty(detail) ? detail : "空") + ";";
        } else if (type == 3) {
            context = "取消活动:coupon活动名称:" + promotionName + "," + context + "," + "coupon活动状态为:" + couponActiveState + "," + "错误信息提示:" + (StringUtil.isNotEmpty(detail) ? detail : "空") + ";";
        } else if (type == 4) {
            context = "取消券:coupon名称:" + couponName + "," + "coupon活动名称:" + promotionName +  "," + context + "," + "coupons活动状态为:" + couponActiveState + "," + "coupon状态为:" +couponState + "," + "错误信息提示:" + (StringUtil.isNotEmpty(detail) ? detail : "空") + ";";
        }else {
            log.error("coupon返回异常类型错误");
            return;
        }

        UserService userService = SpringUtils.getBean(UserService.class);
        for (Integer operatorUserId : operatorUserIdList) {
            UserEntity userEntity = userService.getById(operatorUserId);
            if (userEntity == null) {
                log.error("coupon活动异常状态:{},返回的asin所属运营为空,活动id为:{},运营id为:{}", couponActiveState, couponsId, operatorUserId);
                continue;
            }
            if (StringUtil.isEmpty(userEntity.getPhone())) {
                log.error("coupon活动异常状态:{},返回的asin所属运营的手机号为空,活动id为:{},运营id为:{},运营名称为:{}", couponActiveState, couponsId, operatorUserId, userEntity.getName());
                continue;
            }
            String phoneVerify = "^1[3-9]\\d{9}$";
            if (!userEntity.getPhone().matches(phoneVerify)) {
                log.error("coupon活动异常状态:{},返回的asin所属运营的手机号校验不通过,活动id为:{},运营id为:{},运营名称为:{},手机号为:{}", couponActiveState, couponsId, operatorUserId, userEntity.getName(), userEntity.getPhone());
                continue;
            }
            log.info("coupon活动返回异常状态:{},通知开始,准备发给:{},手机号为:{},消息内容为:{}", couponActiveState, userEntity.getName(), userEntity.getPhone(), context);
                    QywxUtil.wxMessageSend(context, userEntity.getPhone());
        }
    }


    /**
     * @Description:temu促销创建驳回或创建成功后通知运营
     * @Author: wly
     * @Date: 2024/9/30 14:35
     * @Params: [promotionsList, type]
     * @Return: void
     **/
    @Override
    @Async("threadPoolTaskExecutor")
    public void temuPromotionApprovalAdviceWx(List<AmzPromotions> promotionsList, Integer type) {

        try {
            Thread.sleep(5000L);
        } catch (InterruptedException e) {
            log.error("temu促销创建驳回或创建成功后通知运营休眠失败,promotionsList:{}", promotionsList);
            return;
        }
        List<Long> shopIdList = promotionsList.stream().map(AmzPromotions::getShopId).distinct().collect(Collectors.toList());
        List<Account> accounts = accountService.listByIds(shopIdList);
        List<AmzPromotionsSkuDetail> promotionsSkuDetailList = amzPromotionsSkuDetailMapper.selectList(Wrappers.<AmzPromotionsSkuDetail>lambdaQuery().in(AmzPromotionsSkuDetail::getPromotionsId, promotionsList.stream().map(AmzPromotions::getId).collect(Collectors.toList())));

        if (CollectionUtil.isNotEmpty(promotionsList)) {
            List<Long> idList = promotionsList.stream().map(AmzPromotions::getId).collect(Collectors.toList());
            List<AmzPromotionCheck> amzPromotionChecks = amzPromotionCheckMapper.selectListByShopIdAndAsin(promotionsList.stream().map(AmzPromotions::getShopId).distinct().collect(Collectors.toList()), promotionsSkuDetailList.stream().filter(t -> idList.contains(t.getPromotionsId())).map(AmzPromotionsSkuDetail::getAsin).collect(Collectors.toList()));
            if (CollectionUtil.isNotEmpty(amzPromotionChecks)) {
                for (AmzPromotions p : promotionsList) {
                    List<String> pAsinList = promotionsSkuDetailList.stream().filter(t -> p.getId().equals(t.getPromotionsId())).map(AmzPromotionsSkuDetail::getAsin).distinct().collect(Collectors.toList());
                    amzPromotionChecks.stream().filter(q -> p.getShopId().equals(q.getShopId()) && pAsinList.contains(q.getAsin())).findFirst().ifPresent(q -> p.setCreatedBy(q.getOperationUserId()));
                }
            }
        }

        Map<Integer, List<AmzPromotions>> collect = promotionsList.stream().collect(Collectors.groupingBy(AmzPromotions::getCreatedBy));
        Map<Integer, List<StringBuilder>> advice = new HashMap<>();
        collect.forEach((operatorId, v) -> {
            List<StringBuilder> ad = new ArrayList<>();
            StringBuilder s = new StringBuilder();
            for (AmzPromotions amzPromotions : v) {
                accounts.stream().filter(q -> q.getId().equals(amzPromotions.getShopId().intValue())).findFirst().ifPresent(q -> s.append(String.format("店铺名称:[%s],", q.getTitle())));
                String parentAsin = promotionsSkuDetailList.stream().filter(t -> amzPromotions.getId().equals(t.getPromotionsId())).map(AmzPromotionsSkuDetail::getParentAsin).distinct().collect(Collectors.joining(","));
                s.append(String.format("活动名称:[%s],SKC:[%s];", amzPromotions.getPromotionsName(), parentAsin));

                s.append(getTemuAdvice(type, amzPromotions, promotionsSkuDetailList));
                ad.add(s);
            }

            advice.put(operatorId, ad);
        });
        UserService userService = SpringUtils.getBean(UserService.class);
        List<UserEntity> byIds = userService.findByIds(advice.keySet().stream().distinct().collect(Collectors.toList()));

        advice.forEach((operatorId, s) -> {
            for (StringBuilder builder : s) {
                QywxUtil.wxMessageSend(builder.toString(), byIds.stream().filter(t -> t.getId().equals(operatorId)).findFirst().get().getPhone());
            }
        });

    }

    private String getTemuAdvice(Integer type, AmzPromotions amzPromotions, List<AmzPromotionsSkuDetail> promotionsSkuDetailList) {
        if (type == 1) {
            return "创建成功";
        } else if (type == 2) {
            return "请求被驳回";
        } else if (type == 3) {
            return "创建失败";
        } else if (type == 4) {
            //修改库存成功
            return String.format("修改库存成功,修改内容:修改前库存:[" + amzPromotions.getLastSkcStock() + "],修改后库存:[" + amzPromotions.getSkcStock() + "]");
        } else if (type == 5) {
            //修改价格成功
            StringBuilder sb = new StringBuilder();
            sb.append("修改价格成功,修改内容:");
            List<AmzPromotionsSkuDetail> collect = promotionsSkuDetailList.stream().filter(q -> amzPromotions.getId().equals(q.getPromotionsId())).collect(Collectors.toList());
            for (AmzPromotionsSkuDetail detail : collect) {
                if (detail.getLastDiscountPrice() != null && detail.getDiscountPrice() != null && detail.getLastDiscountPrice().compareTo(detail.getDiscountPrice()) != 0) {
                    sb.append(String.format("商品ID:[%s],修改前价格:[" + detail.getLastDiscountPrice() + "],修改后价格:[" + detail.getDiscountPrice() + "];"));
                }
            }
            return sb.toString();
        } else if (type == 6) {
            //修改库存失败
            return String.format("修库存改失败,修改内容:修改前库存:[" + amzPromotions.getLastSkcStock() + "],修改后库存:[" + amzPromotions.getSkcStock() + "]");
        } else {
            //修改价格失败
            StringBuilder sb = new StringBuilder();
            sb.append("修改价格成功,修改内容:");
            boolean flag = false;
            List<AmzPromotionsSkuDetail> collect = promotionsSkuDetailList.stream().filter(q -> amzPromotions.getId().equals(q.getPromotionsId())).collect(Collectors.toList());
            for (AmzPromotionsSkuDetail detail : collect) {
                if (detail.getLastDiscountPrice() != null && detail.getDiscountPrice() != null && detail.getLastDiscountPrice().compareTo(detail.getDiscountPrice()) != 0) {
                    sb.append(String.format("商品ID:[%s],修改前价格:[" + detail.getDiscountPrice() + "],修改后价格:[" + detail.getLastDiscountPrice() + "];"));
                    flag = true;
                }
            }
            if (!flag) {
                return "修改价格失败,当前价格已刷新，与修改前一致";
            }
            return sb.toString();

        }
    }


    @Override
    @Async("threadPoolTaskExecutor")
    public void rpaCancelSuccessAndSyncPromotionAdvice(AmzPromotions amzPromotions, Integer syncReturnState) {

        List<AmzPromotionsSkuDetail> promotionsSkuDetailList = amzPromotionsSkuDetailMapper.selectList(new LambdaQueryWrapper<AmzPromotionsSkuDetail>().eq(AmzPromotionsSkuDetail::getPromotionsId, amzPromotions.getId()));
        List<AmzPromotionCheck> amzPromotionChecks = amzPromotionCheckMapper.selectListByShopIdAndAsin(Collections.singletonList(amzPromotions.getShopId()), promotionsSkuDetailList.stream().map(AmzPromotionsSkuDetail::getAsin).collect(Collectors.toList()));
        //取消成功的PromotionID：XXXXX重新抓取后台状态为：XXXXX，ASIN：XXXXX，促销时间：XXXX-YY-ZZ~XXXX-YY-ZZ
        List<Integer> operatorIdList = amzPromotionChecks.stream().filter(t -> t.getOperationUserId() != null).map(t -> t.getOperationUserId()).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(operatorIdList)) {
            log.error("rpa取消成功自动同步promotion通知运营失败,promotionsId:{},asin所属运营id为空", amzPromotions.getId());
            return;
        }
        String advice = String.format("取消成功的PromotionID:[%s]重新抓取后台状态为:[%s],Asin:[%s],促销时间:[%s]", amzPromotions.getPromotionsId(), AmzPromotionsStateEnum.labelOf(syncReturnState), promotionsSkuDetailList.stream().filter(t -> StringUtils.isNotEmpty(t.getAsin())).map(AmzPromotionsSkuDetail::getAsin).distinct().collect(Collectors.joining(",")), DateUtil.convertDateToString(amzPromotions.getBeginTime(), "yyyy-MM-dd") + "~" + DateUtil.convertDateToString(amzPromotions.getEndTime(), "yyyy-MM-dd"));
        List<UserEntity> byIds = SpringUtils.getBean(UserService.class).findByIds(operatorIdList);
        byIds.forEach(t -> {
            QywxUtil.wxMessageSend(advice, t.getPhone());
        });
    }
}
