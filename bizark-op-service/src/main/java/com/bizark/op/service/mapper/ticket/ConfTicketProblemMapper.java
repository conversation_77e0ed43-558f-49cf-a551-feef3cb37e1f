package com.bizark.op.service.mapper.ticket;

import com.bizark.op.api.entity.op.document.ConfTicketProblem;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 问题配置Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-05
 */
public interface ConfTicketProblemMapper {
    /**
     * 查询问题配置
     *
     * @param problemId 问题配置ID
     * @return 问题配置
     */
    ConfTicketProblem selectConfTicketProblemById(Long problemId);

    /**
     * 查询问题配置列表
     *
     * @param confTicketProblem 问题配置
     * @return 问题配置集合
     */
    List<ConfTicketProblem> selectConfTicketProblemList(ConfTicketProblem confTicketProblem);

    /**
     * 查询顶层问题分类
     *
     * @param confTicketProblem 问题配置
     * @return
     */
    List<ConfTicketProblem> selectTopTicketProblem(ConfTicketProblem confTicketProblem);

    /**
     * 新增问题配置
     *
     * @param confTicketProblem 问题配置
     * @return 结果
     */
    int insertConfTicketProblem(ConfTicketProblem confTicketProblem);

    /**
     * 修改问题配置
     *
     * @param confTicketProblem 问题配置
     * @return 结果
     */
    int updateConfTicketProblem(ConfTicketProblem confTicketProblem);

    /**
     * 删除问题配置
     *
     * @param problemId 问题配置ID
     * @return 结果
     */
    int deleteConfTicketProblemById(Long problemId);

    /**
     * 批量删除问题配置
     *
     * @param problemIds   需要删除的数据ID
     * @param activeStatus
     * @return 结果
     */
    int deleteConfTicketProblemByIds(@Param("problemIds") List<Integer> problemIds, @Param("activeStatus") Boolean activeStatus);

    List<ConfTicketProblem> selectConfTicketProblemListByIds(@Param("problemIds") Collection<Long> problemIds);

    List<ConfTicketProblem> selectOnlyQualityProblem(Integer orgId);
}
