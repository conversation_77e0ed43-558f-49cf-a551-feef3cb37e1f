package com.bizark.op.service.service.promotions;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.promotions.TemuPromotionLimit;
import com.bizark.op.api.entity.op.promotions.TemuPromotionLimitMessage;
import com.bizark.op.api.service.promotions.TemuPromotionLimitService;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.service.mapper.promotions.MarTemuPromotionMapper;
import com.bizark.op.service.mapper.promotions.TemuPromotionLimitMapper;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: Ailill
 * @Date: 2025-08-22 16:09
 */
@Service
@Slf4j
public class TemuPromotionLimitServiceImpl extends ServiceImpl<TemuPromotionLimitMapper, TemuPromotionLimit> implements TemuPromotionLimitService {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private MarTemuPromotionMapper marTemuPromotionMapper;

    @Override
    public void receivePromotionLimitMessage(String message) {
        if (StringUtils.isEmpty(message)) {
            return;
        }
        log.info("receivePromotionLimitMessage: {}", message);
        TemuPromotionLimitMessage temuPromotionLimitMessage = JSONObject.parseObject(message, TemuPromotionLimitMessage.class);

        if (temuPromotionLimitMessage == null) {
            return;
        }
        String mallId = temuPromotionLimitMessage.getMallId();

        if (StringUtils.isEmpty(mallId)) {
            return;
        }

        String existSkc = Optional.ofNullable(temuPromotionLimitMessage.getResult())
                .filter(r -> CollectionUtil.isNotEmpty(r.getDataList()))
                .map(TemuPromotionLimitMessage.Result::getDataList)
                .map(r -> r.stream().filter(t -> CollectionUtil.isNotEmpty(t.getSkcList())).findAny())
                .map(Optional::get)
                .map(r -> r.getSkcList().stream().filter(t -> StringUtils.isNotEmpty(t.getSkcId())).findAny())
                .map(r -> r.get().getSkcId())
                .orElse(null);

        if (StringUtils.isEmpty(existSkc)) {
            return;
        }
        Account account = marTemuPromotionMapper.selectAccountBySellerIdAndSkcId(mallId, existSkc);
        if (account == null) {
            log.error("receivePromotionLimitMessage--{}: account not found, sellerId: {}", message, mallId);
            return;
        }
        TemuPromotionLimitMessage.Result result = temuPromotionLimitMessage.getResult();
        if (result == null) {
            return;
        }
        List<TemuPromotionLimitMessage.DataList> dataList = result.getDataList();
        if (CollectionUtil.isEmpty(dataList)) {
            return;
        }
        List<TemuPromotionLimit> insertOrUpdateList = new ArrayList<>();
        for (TemuPromotionLimitMessage.DataList data : dataList) {
            List<TemuPromotionLimitMessage.SkcList> skcList = data.getSkcList();
            if (CollectionUtil.isEmpty(skcList)) {
                continue;
            }
            if (skcList.stream().allMatch(t -> StringUtils.isEmpty(t.getSkcId()) || CollectionUtil.isEmpty(t.getSkuList()))) {
                continue;
            }
            List<String> allSkcIdList = skcList.stream().filter(t -> StringUtils.isNotEmpty(t.getSkcId())).map(t -> t.getSkcId()).distinct().collect(Collectors.toList());
            List<String> allSkuIdList = skcList.stream().filter(t -> StringUtils.isNotEmpty(t.getSkcId())).flatMap(t -> t.getSkuList().stream().filter(s -> StringUtils.isNotEmpty(s.getSkuId())).map(TemuPromotionLimitMessage.SkuList::getSkuId)).distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(allSkuIdList)) {
                continue;
            }
            LambdaQueryWrapper<TemuPromotionLimit> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TemuPromotionLimit::getShopId, account.getId())
                    .in(TemuPromotionLimit::getSkc, allSkcIdList)
                    .in(TemuPromotionLimit::getSkuId, allSkuIdList);
            List<TemuPromotionLimit> existTemuPromotionLimit = this.list(queryWrapper);

            for (TemuPromotionLimitMessage.SkcList skcListObj : skcList) {
                String skcId = skcListObj.getSkcId();
                List<TemuPromotionLimitMessage.SkuList> skuList = skcListObj.getSkuList();
                if (StringUtils.isEmpty(skcId)) {
                    continue;
                }
                if (CollectionUtil.isEmpty(skuList)) {
                    continue;
                }

                for (TemuPromotionLimitMessage.SkuList skuListObj : skuList) {
                    String skuId = skuListObj.getSkuId();
                    List<TemuPromotionLimitMessage.SiteSupplierPriceList> siteSupplierPriceList = skuListObj.getSiteSupplierPriceList();
                    if (StringUtils.isEmpty(skuId)) {
                        continue;
                    }

                    RLock lock = redissonClient.getLock("receivePromotionLimitMessage" + ":" + skcId + ":" + skuId);
                    try {
                        if (lock.tryLock(0, 1, TimeUnit.MINUTES)) {

                            TemuPromotionLimit temuPromotionLimit = new TemuPromotionLimit();
                            temuPromotionLimit.setOrganizationId(account.getOrgId());
                            temuPromotionLimit.setShopId(account.getId());
                            temuPromotionLimit.setSkc(skcId);
                            temuPromotionLimit.setSkuId(skuId);
                            if (CollectionUtil.isNotEmpty(siteSupplierPriceList)) {
                                TemuPromotionLimitMessage.SiteSupplierPriceList priceList = siteSupplierPriceList.get(0);
                                if (priceList != null) {
                                    temuPromotionLimit.setTargetSupplyPrice(Optional.ofNullable(priceList.getTargetSupplyPrice()).map(t -> t.divide(new BigDecimal(100))).orElse(null));
                                    temuPromotionLimit.setSuggestActivitySupplierPrice(Optional.ofNullable(priceList.getSuggestActivitySupplierPrice()).map(t -> t.divide(new BigDecimal(100))).orElse(null));
                                    if (temuPromotionLimit.getTargetSupplyPrice() != null) {
                                        if (temuPromotionLimit.getSuggestActivitySupplierPrice() != null) {
                                            temuPromotionLimit.setLimitContent("日常申报价调整为：" + temuPromotionLimit.getTargetSupplyPrice() + "\n活动申报价调整为：" + temuPromotionLimit.getSuggestActivitySupplierPrice());
                                        } else {
                                            temuPromotionLimit.setLimitContent("日常申报价调整为：" + temuPromotionLimit.getTargetSupplyPrice());
                                        }
                                    } else {
                                        if (temuPromotionLimit.getSuggestActivitySupplierPrice() != null) {
                                            temuPromotionLimit.setLimitContent("活动申报价调整为：" + temuPromotionLimit.getSuggestActivitySupplierPrice());
                                        }
                                    }
                                }
                            }
                            temuPromotionLimit.settingDefaultSystemUpdate();

                            if (CollectionUtil.isNotEmpty(existTemuPromotionLimit)) {
                                existTemuPromotionLimit.stream().filter(exist -> exist.getSkc().equals(skcId) && exist.getSkuId().equals(skuId)).findFirst().ifPresent(exist -> temuPromotionLimit.setId(exist.getId()));
                            }
                            if (temuPromotionLimit.getId() == null) {
                                temuPromotionLimit.settingDefaultSystemCreate();
                            }
                            insertOrUpdateList.add(temuPromotionLimit);

                        }
                    } catch (Exception e) {
                        e.printStackTrace();

                    } finally {
                        if (lock.isHeldByCurrentThread()) {
                            lock.unlock();
                        }
                    }

                }
            }
        }

        if (CollectionUtil.isNotEmpty(insertOrUpdateList)) {
            this.saveOrUpdateBatch(insertOrUpdateList);
        }
    }
}
