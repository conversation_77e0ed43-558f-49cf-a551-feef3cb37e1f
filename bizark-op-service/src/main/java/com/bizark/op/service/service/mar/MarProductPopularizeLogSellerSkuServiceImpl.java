package com.bizark.op.service.service.mar;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.boss.api.service.TaskCenterService;
import com.bizark.boss.api.vo.dashboard.TaskCenterRequest;
import com.bizark.op.api.cons.StatConstant;
import com.bizark.op.api.enm.finance.FinanceErrorEnum;
import com.bizark.op.api.entity.op.mar.MarListingQuery;
import com.bizark.op.api.entity.op.mar.MarProductPopularizeLogSellerSku;
import com.bizark.op.api.entity.op.tk.expert.vo.VideoExportTaskVO;
import com.bizark.op.api.service.mar.MarProductPopularizeLogSellerSkuService;
import com.bizark.op.api.vo.mar.MarProductPopularizeLogAnalysisItemVo;
import com.bizark.op.api.vo.mar.MarProductPopularizeLogSellerSkuVo;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.*;
import com.bizark.op.service.mapper.mar.MarProductPopularizeLogSellerSkuMapper;
import com.bizark.op.service.mapper.tabcut.TabcutCreatorVideoMapper;
import com.bizark.op.service.util.ConvertUtils;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.bizark.op.api.vo.mar.AdAnalysisVo;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Slf4j
public class MarProductPopularizeLogSellerSkuServiceImpl extends ServiceImpl<MarProductPopularizeLogSellerSkuMapper, MarProductPopularizeLogSellerSku> implements MarProductPopularizeLogSellerSkuService {
    @Autowired
    private MarProductPopularizeLogSellerSkuMapper marProductPopularizeLogSellerSkuMapper;

    @Autowired
    private TaskCenterService taskCenterService;

    @Autowired
    private TabcutCreatorVideoMapper tabcutCreatorVideoMapper;

    @Value("${home_query_bi_url}")
    private  String QUERY_SALE_NUM_MASTER_URL;
    /**
     * @param
     * @param marListingQuery
     * @description:SellersSku维度列表
     * @author: Moore
     * @date: 2024/2/18 14:49
     * @return: java.util.List<com.bizark.op.api.entity.op.mar.MarProductPopularizeLogSellerSku>
     **/
    @Override
    public List<MarProductPopularizeLogSellerSkuVo> selectPopularizeLogBySellerSkuList(MarListingQuery marListingQuery) {
        List<MarProductPopularizeLogSellerSkuVo> marProductPopularizeLogSellerSkuVoList = marProductPopularizeLogSellerSkuMapper.selectPopularizeLogBySellerSkuList(marListingQuery);
        if(CollectionUtil.isNotEmpty(marProductPopularizeLogSellerSkuVoList)) {
            //查询环比数据
            List<String> sellerSkuList = marProductPopularizeLogSellerSkuVoList.stream().map(i -> i.getSellerSku()).collect(Collectors.toList());
            marListingQuery.setSellerSkuArray(sellerSkuList);
            List<MarProductPopularizeLogSellerSkuVo> qoqList = this.selectQoQByParam(marListingQuery);
            if(CollectionUtil.isNotEmpty(qoqList)) {
                marProductPopularizeLogSellerSkuVoList.stream().forEach(i -> {
                    qoqList.stream().forEach(j -> {
                        if(i.getSellerSku().equals(j.getSellerSku())) {
                            i.setPreSumQuantity(j.getPreSumQuantity());
                            i.setPreSumItemAmount(j.getPreSumItemAmount());
                            i.setPreSumOrderNum(j.getPreSumOrderNum());
                        }
                    }
                    );
                });
            }
            marProductPopularizeLogSellerSkuVoList = ConvertUtils.dictConvert(marProductPopularizeLogSellerSkuVoList);
            for(int i= 0; i < marProductPopularizeLogSellerSkuVoList.size(); i++) {
                MarProductPopularizeLogSellerSkuVo marProductPopularizeLogSellerSkuVo = marProductPopularizeLogSellerSkuVoList.get(i);
                //环比 环比增长率=(本期数-上期数)/上期数×100%
                BigDecimal sumQuantity = marProductPopularizeLogSellerSkuVo.getSumQuantity();
                BigDecimal preSumQuantity= marProductPopularizeLogSellerSkuVo.getPreSumQuantity();
                //销量环比
                if(preSumQuantity == null || preSumQuantity.compareTo(BigDecimal.ZERO) == 0) {
                    marProductPopularizeLogSellerSkuVo.setQuantityQoq(new BigDecimal(0));
                } else {
                    BigDecimal quantityQoq = sumQuantity.subtract(preSumQuantity).divide(preSumQuantity, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                    marProductPopularizeLogSellerSkuVo.setQuantityQoq(quantityQoq.setScale(2, BigDecimal.ROUND_HALF_UP));
                }

                BigDecimal sumItemAmount = marProductPopularizeLogSellerSkuVo.getSumItemAmount();
                BigDecimal preSumItemAmount= marProductPopularizeLogSellerSkuVo.getPreSumItemAmount();
                //销售额环比
                if(preSumItemAmount == null || preSumItemAmount.compareTo(BigDecimal.ZERO) == 0) {
                    marProductPopularizeLogSellerSkuVo.setItemAmountQoq(new BigDecimal(0));
                } else {
                    BigDecimal amountQoq = sumItemAmount.subtract(preSumItemAmount).divide(preSumItemAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                    marProductPopularizeLogSellerSkuVo.setItemAmountQoq(amountQoq.setScale(2, BigDecimal.ROUND_HALF_UP));
                }

                BigDecimal sumOrderNum = marProductPopularizeLogSellerSkuVo.getSumOrderNum();
                BigDecimal preSumOrderNum= marProductPopularizeLogSellerSkuVo.getPreSumOrderNum();
                //订单量环比
                if(preSumOrderNum == null || preSumOrderNum.compareTo(BigDecimal.ZERO) == 0) {
                    marProductPopularizeLogSellerSkuVo.setOrderNumQoq(new BigDecimal(0));
                } else {
                    BigDecimal orderNumQoq = sumOrderNum.subtract(preSumOrderNum).divide(preSumOrderNum, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                    marProductPopularizeLogSellerSkuVo.setOrderNumQoq(orderNumQoq.setScale(2, BigDecimal.ROUND_HALF_UP));
                }
//                //ACOS （广告花费/广告销售额）x100  ---保留两位小数
//                if(marProductPopularizeLogSellerSkuVo.getSumAdSaleAmount() == null || marProductPopularizeLogSellerSkuVo.getSumAdSaleAmount().compareTo(BigDecimal.ZERO) == 0) {
//                    marProductPopularizeLogSellerSkuVo.setAcos(new BigDecimal(0));
//                }else {
//                    BigDecimal acos = marProductPopularizeLogSellerSkuVo.getSumAdCost().divide(marProductPopularizeLogSellerSkuVo.getSumAdSaleAmount(), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
//                    marProductPopularizeLogSellerSkuVo.setAcos(acos.setScale(2, BigDecimal.ROUND_HALF_UP));
//                }
                //广告销售额占比 | 广告销售额/订单销售额）x100  ---保留两位小数
//                if(sumItemAmount == null || sumItemAmount.compareTo(BigDecimal.ZERO) == 0) {
//                    marProductPopularizeLogSellerSkuVo.setAdSaleAmountRatio(new BigDecimal(0));
//                }else {
//                    BigDecimal adSaleAmountRatio= marProductPopularizeLogSellerSkuVo.getSumAdSaleAmount().divide(sumItemAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
//                    marProductPopularizeLogSellerSkuVo.setAdSaleAmountRatio(adSaleAmountRatio.setScale(2, BigDecimal.ROUND_HALF_UP));
//                }
                //广告订单量占比 广告订单/订单量）x100    ---保留两位小数
//                if(sumOrderNum == null || sumOrderNum.compareTo(BigDecimal.ZERO) == 0) {
//                    marProductPopularizeLogSellerSkuVo.setAdOrderNumRatio(new BigDecimal(0));
//                }else {
//                    BigDecimal adOrderNumRatio= marProductPopularizeLogSellerSkuVo.getSumAdOrderNum().divide(sumOrderNum, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
//                    marProductPopularizeLogSellerSkuVo.setAdOrderNumRatio(adOrderNumRatio.setScale(2, BigDecimal.ROUND_HALF_UP));
//                }
                //点击率 点击量/曝光量）x100   ---保留两位小数
//                if(marProductPopularizeLogSellerSkuVo.getSumAdViews() == null || marProductPopularizeLogSellerSkuVo.getSumAdViews().compareTo(BigDecimal.ZERO) == 0) {
//                    marProductPopularizeLogSellerSkuVo.setAdClickRate(new BigDecimal(0));
//                }else {
//                    BigDecimal adClickRate= marProductPopularizeLogSellerSkuVo.getSumAdClick().divide(marProductPopularizeLogSellerSkuVo.getSumAdViews(), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
//                    marProductPopularizeLogSellerSkuVo.setAdClickRate(adClickRate.setScale(2, BigDecimal.ROUND_HALF_UP));
//                }
                //转化率 订单量/点击量）x100      ---保留两位小数
//                if(marProductPopularizeLogSellerSkuVo.getSumAdClick() == null || marProductPopularizeLogSellerSkuVo.getSumAdClick().compareTo(BigDecimal.ZERO) == 0) {
//                    marProductPopularizeLogSellerSkuVo.setAdConversion(new BigDecimal(0));
//                }else {
//                    BigDecimal adConversion = marProductPopularizeLogSellerSkuVo.getSumAdOrderNum().divide(marProductPopularizeLogSellerSkuVo.getSumAdClick(), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
//                    marProductPopularizeLogSellerSkuVo.setAdConversion(adConversion.setScale(2, BigDecimal.ROUND_HALF_UP));
//                }
            }
        }
        return marProductPopularizeLogSellerSkuVoList;
    }

    /**
     * Description: 综合分析sellersku 维度
     *
     * @param marListingQuery
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/2/20
     */
    @Override
    public List<MarProductPopularizeLogAnalysisItemVo> analysisSelectPopularizeLogBySellerSkuList(MarListingQuery marListingQuery) {
        List<MarProductPopularizeLogAnalysisItemVo> marProductPopularizeLogSellerSkuVoList = marProductPopularizeLogSellerSkuMapper.analysisSelectPopularizeLogBySellerSkuList(marListingQuery);
        if(CollectionUtil.isNotEmpty(marProductPopularizeLogSellerSkuVoList)) {
            for(int i= 0; i < marProductPopularizeLogSellerSkuVoList.size(); i++) {
                MarProductPopularizeLogAnalysisItemVo marProductPopularizeLogSellerSkuVo = marProductPopularizeLogSellerSkuVoList.get(i);
                //环比 环比增长率=(本期数-上期数)/上期数×100%
                BigDecimal sumItemAmount = marProductPopularizeLogSellerSkuVo.getItemAmount();
                BigDecimal sumOrderNum = marProductPopularizeLogSellerSkuVo.getOrderNum();

                //利润率（利润/收入） *100          ---保留两位小数
                if(marProductPopularizeLogSellerSkuVo.getIncomeFee() == null || marProductPopularizeLogSellerSkuVo.getIncomeFee().compareTo(BigDecimal.ZERO) == 0) {
                    marProductPopularizeLogSellerSkuVo.setProfitRate(new BigDecimal(0));
                } else {
                    BigDecimal profitRate = marProductPopularizeLogSellerSkuVo.getProfit().divide(marProductPopularizeLogSellerSkuVo.getIncomeFee(), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                    marProductPopularizeLogSellerSkuVo.setProfitRate(profitRate.setScale(2, BigDecimal.ROUND_HALF_UP));
                }

                //ACOS （广告花费/广告销售额）x100  ---保留两位小数
                if(marProductPopularizeLogSellerSkuVo.getAdSaleAmount() == null || marProductPopularizeLogSellerSkuVo.getAdSaleAmount().compareTo(BigDecimal.ZERO) == 0) {
                    marProductPopularizeLogSellerSkuVo.setAcos(new BigDecimal(0));
                }else {
                    BigDecimal acos = marProductPopularizeLogSellerSkuVo.getAdCost().divide(marProductPopularizeLogSellerSkuVo.getAdSaleAmount(), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                    marProductPopularizeLogSellerSkuVo.setAcos(acos.setScale(2, BigDecimal.ROUND_HALF_UP));
                }

                //TACOS （广告花费/订单销售额）x100  ---保留两位小数
                if(sumItemAmount == null || sumItemAmount.compareTo(BigDecimal.ZERO) == 0) {
                    marProductPopularizeLogSellerSkuVo.setTAcos(new BigDecimal(0));
                }else {
                    BigDecimal acos = marProductPopularizeLogSellerSkuVo.getAdCost().divide(sumItemAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                    marProductPopularizeLogSellerSkuVo.setTAcos(acos.setScale(2, BigDecimal.ROUND_HALF_UP));
                }

                //广告销售额占比 | 广告销售额/订单销售额）x100  ---保留两位小数
                if(sumItemAmount == null || sumItemAmount.compareTo(BigDecimal.ZERO) == 0) {
                    marProductPopularizeLogSellerSkuVo.setAdSaleAmountRatio(new BigDecimal(0));
                }else {
                    BigDecimal adSaleAmountRatio= marProductPopularizeLogSellerSkuVo.getAdSaleAmount().divide(sumItemAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                    marProductPopularizeLogSellerSkuVo.setAdSaleAmountRatio(adSaleAmountRatio.setScale(2, BigDecimal.ROUND_HALF_UP));
                }
                //广告订单量占比 广告订单/订单量）x100    ---保留两位小数
                if(sumOrderNum == null || sumOrderNum.compareTo(BigDecimal.ZERO) == 0) {
                    marProductPopularizeLogSellerSkuVo.setAdOrderNumRatio(new BigDecimal(0));
                }else {
                    BigDecimal adOrderNumRatio= marProductPopularizeLogSellerSkuVo.getAdOrderNum().divide(sumOrderNum, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                    marProductPopularizeLogSellerSkuVo.setAdOrderNumRatio(adOrderNumRatio.setScale(2, BigDecimal.ROUND_HALF_UP));
                }
                //点击率 点击量/曝光量）x100   ---保留两位小数
                if(marProductPopularizeLogSellerSkuVo.getAdViews() == null || marProductPopularizeLogSellerSkuVo.getAdViews().compareTo(BigDecimal.ZERO) == 0) {
                    marProductPopularizeLogSellerSkuVo.setAdClickRate(new BigDecimal(0));
                }else {
                    BigDecimal adClickRate= marProductPopularizeLogSellerSkuVo.getAdClick().divide(marProductPopularizeLogSellerSkuVo.getAdViews(), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                    marProductPopularizeLogSellerSkuVo.setAdClickRate(adClickRate.setScale(2, BigDecimal.ROUND_HALF_UP));
                }
                //转化率 订单量/点击量）x100      ---保留两位小数
                if(marProductPopularizeLogSellerSkuVo.getAdClick() == null || marProductPopularizeLogSellerSkuVo.getAdClick().compareTo(BigDecimal.ZERO) == 0) {
                    marProductPopularizeLogSellerSkuVo.setAdConversion(new BigDecimal(0));
                }else {
                    BigDecimal adConversion = marProductPopularizeLogSellerSkuVo.getAdOrderNum().divide(marProductPopularizeLogSellerSkuVo.getAdClick(), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                    marProductPopularizeLogSellerSkuVo.setAdConversion(adConversion.setScale(2, BigDecimal.ROUND_HALF_UP));
                }
            }
        }
        return marProductPopularizeLogSellerSkuVoList;
    }

    /**
     * Description: 广告分析sellersku 维度
     *
     * @param marListingQuery
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/2/20
     */
    @Override
    public List<AdAnalysisVo> adAnalysisSelectPopularizeLogBySellerSkuList(MarListingQuery marListingQuery) {
        List<AdAnalysisVo> adAnalysisVos = marProductPopularizeLogSellerSkuMapper.adAnalysisSelectPopularizeLogBySellerSkuList(marListingQuery);
        if(CollectionUtil.isNotEmpty(adAnalysisVos)) {
            for(int i= 0; i < adAnalysisVos.size(); i++) {
                AdAnalysisVo adAnalysisVo  = adAnalysisVos.get(i);
                BigDecimal sumItemAmount = adAnalysisVo.getItemAmount();
                BigDecimal sumOrderNum = adAnalysisVo.getOrderNum();
                //ACOS （广告花费/广告销售额）x100  ---保留两位小数
                if(adAnalysisVo.getAdSaleAmount() == null || adAnalysisVo.getAdSaleAmount().compareTo(BigDecimal.ZERO) == 0) {
                    adAnalysisVo.setAcos(new BigDecimal(0));
                }else {
                    BigDecimal acos = adAnalysisVo.getAdCost().divide(adAnalysisVo.getAdSaleAmount(), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                    adAnalysisVo.setAcos(acos.setScale(2, BigDecimal.ROUND_HALF_UP));
                }
                //广告订单量占比 广告订单/订单量）x100    ---保留两位小数
                if(sumOrderNum == null || sumOrderNum.compareTo(BigDecimal.ZERO) == 0) {
                    adAnalysisVo.setAdOrderNumRatio(new BigDecimal(0));
                }else {
                    BigDecimal adOrderNumRatio= adAnalysisVo.getAdOrderNum().divide(sumOrderNum, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                    adAnalysisVo.setAdOrderNumRatio(adOrderNumRatio.setScale(2, BigDecimal.ROUND_HALF_UP));
                }
                //点击率 点击量/曝光量）x100   ---保留两位小数
                if(adAnalysisVo.getAdViews() == null || adAnalysisVo.getAdViews().compareTo(BigDecimal.ZERO) == 0) {
                    adAnalysisVo.setAdClickRate(new BigDecimal(0));
                }else {
                    BigDecimal adClickRate= adAnalysisVo.getAdClick().divide(adAnalysisVo.getAdViews(), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                    adAnalysisVo.setAdClickRate(adClickRate.setScale(2, BigDecimal.ROUND_HALF_UP));
                }
                //转化率 订单量/点击量）x100      ---保留两位小数
                if(adAnalysisVo.getAdClick() == null || adAnalysisVo.getAdClick().compareTo(BigDecimal.ZERO) == 0) {
                    adAnalysisVo.setAdConversion(new BigDecimal(0));
                }else {
                    BigDecimal adConversion = adAnalysisVo.getAdOrderNum().divide(adAnalysisVo.getAdClick(), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                    adAnalysisVo.setAdConversion(adConversion.setScale(2, BigDecimal.ROUND_HALF_UP));
                }
            }
        }
        return adAnalysisVos;
    }

    /**
     * Description: 广告分析 sellersku 维度 折线图
     *
     * @param marListingQuery
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/2/21
     */
    @Override
    public JSONObject adAnalysisSelectPopularizeLogBySellerSkuListLineChart(MarListingQuery marListingQuery) {
        JSONObject result = new JSONObject();
        JSONObject data = new JSONObject();
        List<SpiltDateUtil.Range> rangeList = new ArrayList<>();
        List<AdAnalysisVo> adAnalysisVos = marProductPopularizeLogSellerSkuMapper.adAnalysisSelectPopularizeLogBySellerSkuList(marListingQuery);
        if(CollectionUtil.isEmpty(adAnalysisVos)) {
            return result;
        }
        if (StatConstant.STAT_DATA_TYPE_DAY.equals(marListingQuery.getDateType())) {
            rangeList = SpiltDateUtil.splitToDays(marListingQuery.getPurchaseDateFrom(), marListingQuery.getPurchaseDateTo());
        } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(marListingQuery.getDateType())) {
            rangeList = SpiltDateUtil.splitToWeeks(marListingQuery.getPurchaseDateFrom(), marListingQuery.getPurchaseDateTo());
        } else if (StatConstant.STAT_DATA_TYPE_MONTH.equals(marListingQuery.getDateType())) {
            rangeList = SpiltDateUtil.splitToMonths(marListingQuery.getPurchaseDateFrom(), marListingQuery.getPurchaseDateTo());
        } else if (StatConstant.STAT_DATA_TYPE_YEAR.equals(marListingQuery.getDateType())) {
            rangeList = SpiltDateUtil.splitToYears(marListingQuery.getPurchaseDateFrom(), marListingQuery.getPurchaseDateTo());
        }
        //x轴数据
        List<String> dateList = new ArrayList<>();
        for (SpiltDateUtil.Range range : rangeList) {
            String dateRange = "";
            if (StatConstant.STAT_DATA_TYPE_DAY.equals(marListingQuery.getDateType())) {
                dateRange = DateUtil.convertDateToString(range.getStart(), "MM-dd");
            } else if (StatConstant.STAT_DATA_TYPE_HOUR.equals(marListingQuery.getDateType())) {
                dateRange = DateUtil.convertDateToString(range.getStart(), "MM/dd HH");
            } else {
                dateRange = DateUtil.convertDateToString(range.getStart(), "MM-dd") + "~" + DateUtil.convertDateToString(range.getEnd(), "MM-dd");
            }
            dateList.add(dateRange);
        }
        result.put("x", dateList);

        //y轴各个指标数值
        List<BigDecimal> sumAdCostList = new ArrayList<>();
        List<BigDecimal> sumAdOrderNumList = new ArrayList<>();
        List<BigDecimal> sumAdSaleAmountList = new ArrayList<>();
        List<BigDecimal> sumAdCpcList = new ArrayList<>();
        List<BigDecimal> sumAdViewsList = new ArrayList<>();
        List<BigDecimal> sumAdClickList = new ArrayList<>();
        List<BigDecimal> acosList = new ArrayList<>();
        List<BigDecimal> adOrderNumRatioList = new ArrayList<>();
        List<BigDecimal> adClickRateList = new ArrayList<>();
        List<BigDecimal> adConversionList = new ArrayList<>();
        for (SpiltDateUtil.Range range : rangeList) {
            //广告花费
            BigDecimal sumAdCost = adAnalysisVos.stream().filter(marProductPopularizeShopAdVo ->
                            DateUtil.compareDate(marProductPopularizeShopAdVo.getPstChannelCreatedDate(), range.getStart()) >= 0 &&
                                    DateUtil.compareDate(marProductPopularizeShopAdVo.getPstChannelCreatedDate(), range.getEnd()) <= 0)
                    .map(i -> i.getAdCost() != null ? i.getAdCost() : new BigDecimal(0))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            sumAdCostList.add(sumAdCost);
            //广告订单量
            BigDecimal sumAdOrderNum = adAnalysisVos.stream().filter(marProductPopularizeShopAdVo ->
                            DateUtil.compareDate(marProductPopularizeShopAdVo.getPstChannelCreatedDate(), range.getStart()) >= 0 &&
                                    DateUtil.compareDate(marProductPopularizeShopAdVo.getPstChannelCreatedDate(), range.getEnd()) <= 0)
                    .map(i -> i.getAdOrderNum() != null ? i.getAdOrderNum() : new BigDecimal(0))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            sumAdOrderNumList.add(sumAdOrderNum);

            //广告销售额
            BigDecimal sumAdSaleAmount = adAnalysisVos.stream().filter(marProductPopularizeShopAdVo ->
                            DateUtil.compareDate(marProductPopularizeShopAdVo.getPstChannelCreatedDate(), range.getStart()) >= 0 &&
                                    DateUtil.compareDate(marProductPopularizeShopAdVo.getPstChannelCreatedDate(), range.getEnd()) <= 0)
                    .map(i -> i.getAdSaleAmount() != null ? i.getAdSaleAmount() : new BigDecimal(0))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            sumAdSaleAmountList.add(sumAdSaleAmount);

            //Acos （广告花费/广告销售额）x100  ---保留两位小数
            BigDecimal acos = null;
            if(sumAdSaleAmount == null || sumAdSaleAmount.compareTo(BigDecimal.ZERO) == 0) {
                acos = new BigDecimal(0);
            }else {
                acos = sumAdCost.divide(sumAdSaleAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
            acosList.add(acos);

            // 订单量
            BigDecimal sumOrderNum = adAnalysisVos.stream().filter(marProductPopularizeShopAdVo ->
                            DateUtil.compareDate(marProductPopularizeShopAdVo.getPstChannelCreatedDate(), range.getStart()) >= 0 &&
                                    DateUtil.compareDate(marProductPopularizeShopAdVo.getPstChannelCreatedDate(), range.getEnd()) <= 0)
                    .map(i -> i.getOrderNum() != null ? i.getOrderNum() : new BigDecimal(0))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            //广告销售单量占比 广告订单/订单量）x100    ---保留两位小数
            BigDecimal adOrderNumRatio = null;
            if(sumOrderNum == null || sumOrderNum.compareTo(BigDecimal.ZERO) == 0) {
                adOrderNumRatio = new BigDecimal("0");
            }else {
                adOrderNumRatio = sumAdOrderNum.divide(sumOrderNum, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
            adOrderNumRatioList.add(adOrderNumRatio);


            //CPC 广告花费/广告点击)
            BigDecimal sumAdCpc = adAnalysisVos.stream().filter(marProductPopularizeShopAdVo ->
                            DateUtil.compareDate(marProductPopularizeShopAdVo.getPstChannelCreatedDate(), range.getStart()) >= 0 &&
                                    DateUtil.compareDate(marProductPopularizeShopAdVo.getPstChannelCreatedDate(), range.getEnd()) <= 0)
                    .map(i -> i.getAdCpc() != null ? i.getAdCpc() : new BigDecimal(0))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            sumAdCpcList.add(sumAdCpc);

            //广告曝光量
            BigDecimal sumAdViews = adAnalysisVos.stream().filter(marProductPopularizeShopAdVo ->
                            DateUtil.compareDate(marProductPopularizeShopAdVo.getPstChannelCreatedDate(), range.getStart()) >= 0 &&
                                    DateUtil.compareDate(marProductPopularizeShopAdVo.getPstChannelCreatedDate(), range.getEnd()) <= 0)
                    .map(i -> i.getAdViews() != null ? i.getAdViews() : new BigDecimal(0))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            sumAdViewsList.add(sumAdViews);

            //广告点击量
            BigDecimal sumAdClick = adAnalysisVos.stream().filter(marProductPopularizeShopAdVo ->
                            DateUtil.compareDate(marProductPopularizeShopAdVo.getPstChannelCreatedDate(), range.getStart()) >= 0 &&
                                    DateUtil.compareDate(marProductPopularizeShopAdVo.getPstChannelCreatedDate(), range.getEnd()) <= 0)
                    .map(i -> i.getAdClick() != null ? i.getAdClick() : new BigDecimal(0))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            sumAdClickList.add(sumAdClick);

            //点击率,点击量/曝光量）x100   ---保留两位小数
            BigDecimal adClickRate = null;
            if(sumAdViews == null || sumAdViews.compareTo(BigDecimal.ZERO) == 0) {
                adClickRate = new BigDecimal(0);
            } else {
                adClickRate = sumAdClick.divide(sumAdViews, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
            }
            adClickRateList.add(adClickRate);

            //转换率，订单量/点击量）x100      ---保留两位小数
            BigDecimal adConversion = null;
            if(sumAdClick == null || sumAdClick.compareTo(BigDecimal.ZERO) == 0) {
                adConversion = new BigDecimal(0);
            } else {
                adConversion = sumAdOrderNum.divide(sumAdClick, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
            }
            adConversionList.add(adConversion);
        }
        data.put("sumAdCostList",sumAdCostList);
        data.put("sumAdOrderNumList",sumAdOrderNumList);
        data.put("sumAdSaleAmountList",sumAdSaleAmountList);
        data.put("sumAdCpcList",sumAdCpcList);
        data.put("sumAdViewsList",sumAdViewsList);
        data.put("sumAdClickList",sumAdClickList);
        data.put("acosList",acosList);
        data.put("adOrderNumRatioList",adOrderNumRatioList);
        data.put("adClickRateList",adClickRateList);
        data.put("adConversionList",adConversionList);
        result.put("data", data);
        return result;
    }

    /**
     * Description: 任务导出
     *
     * @param response
     * @param marListingQuery
     * @param authUserEntity
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/3/1
     */
    @Override
    public void listExport(HttpServletResponse response, MarListingQuery marListingQuery, UserEntity authUserEntity) {
        TaskCenterRequest request = new TaskCenterRequest();
        request.setTaskCode("erp.marProductPopularizeLogSellerSku.export");
        request.setOrgId(marListingQuery.getOrganizationId().intValue());
        List<Object> list = new ArrayList<>();
        list.add(JSON.toJSONString(marListingQuery));
        request.setArgs(list);
        this.taskCenterService.startTask(request, authUserEntity);
        AssertUtil.isFail(FinanceErrorEnum.ASNYC_EXPORT_GENERATED);
    }


    /**
     * Description: 根据条件查询环比数据
     *
     * @param marListingQuery
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/3/5
     */
    @Override
    public List<MarProductPopularizeLogSellerSkuVo> selectQoQByParam(MarListingQuery marListingQuery) {
        return marProductPopularizeLogSellerSkuMapper.selectQoQByParam(marListingQuery);
    }


    @Override
    public JSONObject queryPopularizeLogBySellerSku(MarListingQuery marListingQuery) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("organizationId", marListingQuery.getOrganizationId());
        paramMap.put("purchaseDateFrom", marListingQuery.getPurchaseDateFrom());
        paramMap.put("purchaseDateTo", marListingQuery.getPurchaseDateTo());
        paramMap.put("erpSku",marListingQuery.getErpSku());
        paramMap.put("erpSkuArray", marListingQuery.getErpSkuArray());
        paramMap.put("sellerSku", marListingQuery.getSellerSku());
        paramMap.put("sellerSkuArray", marListingQuery.getSellerSkuArray());
        paramMap.put("asin", marListingQuery.getAsin());
        paramMap.put("asinArray", marListingQuery.getAsinArray());
        paramMap.put("parentAsin", marListingQuery.getParentAsin());
        paramMap.put("parentAsinArray", marListingQuery.getParentAsinArray());
        paramMap.put("saleStatus", marListingQuery.getSaleStatus());
        paramMap.put("operationId", marListingQuery.getOperationId());
        paramMap.put("operationIdArray", marListingQuery.getOperationIdArray());
        paramMap.put("categoryIdArray", marListingQuery.getCategoryIdArray());
        paramMap.put("brandName", marListingQuery.getBrandName());
        paramMap.put("tag", marListingQuery.getTag());
        paramMap.put("saleChannel", marListingQuery.getSaleChannel());
        paramMap.put("saleChannelArray", marListingQuery.getSaleChannelArray());
        paramMap.put("accountId", marListingQuery.getShopId());
        paramMap.put("accountIdArray", marListingQuery.getShopIdArray());
        paramMap.put("countryCode", marListingQuery.getCountryCode());
        paramMap.put("countryCodeArray", marListingQuery.getCountryCodeArray());
        paramMap.put("page", marListingQuery.getPage());
        paramMap.put("size", marListingQuery.getSize());
        paramMap.put("sidx", marListingQuery.getSidx());
        paramMap.put("sord", marListingQuery.getSord());
        String url = QUERY_SALE_NUM_MASTER_URL + "/api/v1/SellerSkuOfflineData";
        HttpRequest request = HttpUtil.createPost(url);
        request.setReadTimeout(120*1000);
        request.body(JSON.toJSONString(paramMap));
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            throw new IllegalArgumentException("查询运营分析sellerSku维度数据异常");
        }
        return JSONObject.parseObject(response.body());
    }


    /**
     * Description: 调用外部接口导出
     *
     * @param response
     * @param marListingQuery
     * @param authUserEntity
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/8/30
     */
    @Override
    public void newlistExportSellerSku(HttpServletResponse response, MarListingQuery marListingQuery, UserEntity authUserEntity) {
        Integer userId = UserUtils.getCurrentUserId(0); //获取当前用户
        String userName = UserUtils.getCurrentUserName("system");
        VideoExportTaskVO vo = new VideoExportTaskVO();
        vo.setExportId(userId + "-" + System.currentTimeMillis());
        marListingQuery.setExportId(vo.getExportId()); //到处唯一ID
        this.sendBiExportSellerSku(marListingQuery); //请求BI导出
        Date date = new Date();
        vo.setTaskNo(vo.getExportId()); //任务号
        vo.setTaskCode("erp.bi.export.ListingSellerSku.time");
        vo.setCreatedAt(date);
        vo.setCreatedBy(userId);
        vo.setCreatedName(userName);
        vo.setUpdatedAt(date);
        vo.setUpdatedBy(userId);
        vo.setUpdatedName(userName);
        vo.setTaskCode(UUID.randomUUID().toString().replace("-", ""));
        vo.setStatus("processing");
        vo.setBody(JSONObject.toJSONString(marListingQuery));
        vo.setOrgId(marListingQuery.getOrganizationId().intValue());
        vo.setProcessTime(date);
        vo.setTaskTitle("综合分析SellerSku维度导出");
        tabcutCreatorVideoMapper.expoertTaskInsert(vo);
    }

    void sendBiExportSellerSku(MarListingQuery marListingQuery) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("organizationId", marListingQuery.getOrganizationId());
        paramMap.put("purchaseDateFrom", marListingQuery.getPurchaseDateFrom());
        paramMap.put("purchaseDateTo", marListingQuery.getPurchaseDateTo());
        paramMap.put("erpSku",marListingQuery.getErpSku());
        paramMap.put("erpSkuArray", marListingQuery.getErpSkuArray());
        paramMap.put("sellerSku", marListingQuery.getSellerSku());
        paramMap.put("sellerSkuArray", marListingQuery.getSellerSkuArray());
        paramMap.put("asin", marListingQuery.getAsin());
        paramMap.put("asinArray", marListingQuery.getAsinArray());
        paramMap.put("parentAsin", marListingQuery.getParentAsin());
        paramMap.put("parentAsinArray", marListingQuery.getParentAsinArray());
        paramMap.put("saleStatus", marListingQuery.getSaleStatus());
        paramMap.put("operationId", marListingQuery.getOperationId());
        paramMap.put("operationIdArray", marListingQuery.getOperationIdArray());
        paramMap.put("categoryIdArray", marListingQuery.getCategoryIdArray());
        paramMap.put("brandName", marListingQuery.getBrandName());
        paramMap.put("tag", marListingQuery.getTag());
        paramMap.put("saleChannel", marListingQuery.getSaleChannel());
        paramMap.put("saleChannelArray", marListingQuery.getSaleChannelArray());
        paramMap.put("accountId", marListingQuery.getShopId());
        paramMap.put("accountIdArray", marListingQuery.getShopIdArray());
        paramMap.put("countryCode", marListingQuery.getCountryCode());
        paramMap.put("countryCodeArray", marListingQuery.getCountryCodeArray());
        paramMap.put("page", marListingQuery.getPage());
        paramMap.put("size", marListingQuery.getSize());
        paramMap.put("sidx", marListingQuery.getSidx());
        paramMap.put("sord", marListingQuery.getSord());
        paramMap.put("exportId",marListingQuery.getExportId());
        String url = QUERY_SALE_NUM_MASTER_URL + "/api/v1/SellerSkuOfflineData";
        HttpRequest request = HttpUtil.createPost(url);
        request.setReadTimeout(120*1000);
        request.body(JSON.toJSONString(paramMap));
        HttpResponse response = request.execute();
        log.info("运营分析SellerSku数据导出查询参数：{}", JSON.toJSONString(paramMap));
        if (!response.isOk()) {
            log.error("运营分析SellerSku维度数据导出异常:{}",JSONObject.toJSONString(response));
            throw new CustomException("运营分析SellerSku维度数据导出异常");
        }
    }


}
