package com.bizark.op.service.service.ticket;

import cn.hutool.core.collection.CollectionUtil;
import com.bizark.op.api.entity.conf.CrmTreeSelect;
import com.bizark.op.api.entity.op.document.ConfTicketProblem;
import com.bizark.op.api.entity.op.sale.ErpCascaderSelect;
import com.bizark.op.api.service.ticket.IConfTicketProblemService;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.service.mapper.ticket.ConfTicketProblemMapper;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 问题配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-05
 */
@Service
public class ConfTicketProblemServiceImpl implements IConfTicketProblemService {
    @Autowired
    private ConfTicketProblemMapper confTicketProblemMapper;

    /**
     * 查询问题配置
     *
     * @param problemId 问题配置ID
     * @return 问题配置
     */
    @Override
    public ConfTicketProblem selectConfTicketProblemById(Long problemId) {
        return confTicketProblemMapper.selectConfTicketProblemById(problemId);
    }

    /**
     * 查询问题配置列表
     *
     * @param confTicketProblem 问题配置
     * @return 问题配置
     */
    @Override
    public List<ConfTicketProblem> selectConfTicketProblemList(ConfTicketProblem confTicketProblem) {
        return confTicketProblemMapper.selectConfTicketProblemList(confTicketProblem);
    }

    @Override
    public List<ConfTicketProblem> selectConfTicketProblemFirstList(Integer contextId) {
        PageHelper.startPage(1, 99);
        ConfTicketProblem confTicketProblem = new ConfTicketProblem();
        confTicketProblem.setLevel(1);
        confTicketProblem.setOrganizationId(contextId.longValue());
        return confTicketProblemMapper.selectConfTicketProblemList(confTicketProblem);
    }

    @Override
    public List<ErpCascaderSelect> buildTicketProblemCascaderSelect(Integer contextId) {
        List<ConfTicketProblem> ticketProblemList = this.selectConfTicketProblemFirstList(contextId);
        List<ErpCascaderSelect> cascaderSelectList = ticketProblemList.stream().map(ErpCascaderSelect::new).collect(Collectors.toList());
        cascaderSelectList.forEach(cascaderSelect -> {
            ConfTicketProblem record = new ConfTicketProblem();
            record.setParentId(cascaderSelect.getId());
            record.setOrganizationId(contextId.longValue());
            List<ConfTicketProblem> childrenTicketProblemList = this.selectConfTicketProblemList(record);
            List<ErpCascaderSelect> children = childrenTicketProblemList.stream().map(ErpCascaderSelect::new).collect(Collectors.toList());
            cascaderSelect.setChildren(children);
        });
        return cascaderSelectList;
    }

    @Override
    public List<ConfTicketProblem> buildTicketProblemTree(List<ConfTicketProblem> confTicketProblems) {
        List<ConfTicketProblem> returnList = new ArrayList<ConfTicketProblem>();
        List<Long> tempList = new ArrayList<Long>();
        for (ConfTicketProblem ticketProblem : confTicketProblems) {
            tempList.add(Long.valueOf(ticketProblem.getProblemId()));
        }
        for (Iterator<ConfTicketProblem> iterator = confTicketProblems.iterator(); iterator.hasNext(); ) {
            ConfTicketProblem ticketProblem = (ConfTicketProblem) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(ticketProblem.getParentId())) {
                recursionFn(confTicketProblems, ticketProblem);
                returnList.add(ticketProblem);
            }
        }
        if (returnList.isEmpty()) {
            returnList = confTicketProblems;
        }
        return returnList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<ConfTicketProblem> list, ConfTicketProblem t) {
        // 得到子节点列表
        List<ConfTicketProblem> childList = getChildList(list, t);
        t.setChildren(childList);
        for (ConfTicketProblem tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<ConfTicketProblem> getChildList(List<ConfTicketProblem> list, ConfTicketProblem t) {
        List<ConfTicketProblem> tlist = new ArrayList<ConfTicketProblem>();
        Iterator<ConfTicketProblem> it = list.iterator();
        while (it.hasNext()) {
            ConfTicketProblem n = (ConfTicketProblem) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getProblemId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<ConfTicketProblem> list, ConfTicketProblem t) {
        return getChildList(list, t).size() > 0 ? true : false;
    }

    @Override
    public List<CrmTreeSelect> buildTicketProblemTreeSelect(List<ConfTicketProblem> confTicketProblems) {
        List<ConfTicketProblem> ticketProblemTrees = buildTicketProblemTree(confTicketProblems);
        return ticketProblemTrees.stream().map(CrmTreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 新增问题配置
     *
     * @param confTicketProblem 问题配置
     * @return 结果
     */
    @Override
    public int insertConfTicketProblem(ConfTicketProblem confTicketProblem) {
       confTicketProblem.settingDefaultCreate();
       confTicketProblem.settingDefaultUpdate();
        if (0 == confTicketProblem.getParentId()) {
            confTicketProblem.setLevel(1);
        } else {
            confTicketProblem.setLevel(2);
        }
        return confTicketProblemMapper.insertConfTicketProblem(confTicketProblem);
    }

    /**
     * 修改问题配置
     *
     * @param confTicketProblem 问题配置
     * @return 结果
     */
    @Override
    public int updateConfTicketProblem(ConfTicketProblem confTicketProblem) {
        confTicketProblem.settingDefaultUpdate();
        return confTicketProblemMapper.updateConfTicketProblem(confTicketProblem);
    }

    /**
     * 批量删除问题配置
     *
     * @param problemId   需要删除的问题配置ID
     * @param activeStatus
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteConfTicketProblemByIds(Long problemId, Boolean activeStatus) {
        ConfTicketProblem confTicketProblem = this.selectConfTicketProblemById(problemId);
        if (confTicketProblem.getLevel().equals(1)) {
            ConfTicketProblem problem = new ConfTicketProblem();
            problem.setParentId(problemId.intValue());
            List<ConfTicketProblem> list = this.selectConfTicketProblemList(problem);
            List<Integer> collect = list.stream().map(ConfTicketProblem::getProblemId).collect(Collectors.toList());
            collect.add(problemId.intValue());
            return confTicketProblemMapper.deleteConfTicketProblemByIds(collect, activeStatus);
        } else {
            List<Integer> needUpdate = new ArrayList<>();
            if (activeStatus) {
                Integer parentId = confTicketProblem.getParentId();
                needUpdate.add(parentId);
                needUpdate.add(problemId.intValue());
                return confTicketProblemMapper.deleteConfTicketProblemByIds(needUpdate, activeStatus);
            } else {
                ConfTicketProblem confTicketProblem1 = new ConfTicketProblem();
                confTicketProblem1.setParentId(confTicketProblem.getParentId());
                List<ConfTicketProblem> other = this.selectConfTicketProblemList(confTicketProblem1);
                List<ConfTicketProblem> collect = other.stream().filter(t -> !t.getProblemId().equals(problemId.intValue())).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(collect)) {
                    needUpdate.add(problemId.intValue());
                    needUpdate.add(confTicketProblem.getParentId());
                    return confTicketProblemMapper.deleteConfTicketProblemByIds(needUpdate, activeStatus);
                } else {
                    needUpdate.add(problemId.intValue());
                    long count = collect.stream().filter(ConfTicketProblem::getActiveStatus).count();
                    if (count == 0) {
                        needUpdate.add(confTicketProblem.getParentId());
                    }
                    return confTicketProblemMapper.deleteConfTicketProblemByIds(needUpdate, activeStatus);
                }
            }
        }

    }

    /**
     * 删除问题配置信息
     *
     * @param problemId 问题配置ID
     * @return 结果
     */
    @Override
    public int deleteConfTicketProblemById(Long problemId) {
        return confTicketProblemMapper.deleteConfTicketProblemById(problemId);
    }
}
