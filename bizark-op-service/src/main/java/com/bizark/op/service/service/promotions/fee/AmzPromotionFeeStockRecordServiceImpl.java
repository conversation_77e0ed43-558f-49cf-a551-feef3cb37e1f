package com.bizark.op.service.service.promotions.fee;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.op.api.entity.op.order.SaleOrderItemPromotionPriceVO;
import com.bizark.op.api.entity.op.order.SaleOrderItems;
import com.bizark.op.api.entity.op.promotions.*;
import com.bizark.op.api.service.order.SaleOrderItemsService;
import com.bizark.op.api.service.promotions.IAmzPromotionsService;
import com.bizark.op.api.service.promotions.IAmzPromotionsSkuDetailService;
import com.bizark.op.api.service.promotions.fee.AmzPromotionFeeDateHisRecordService;
import com.bizark.op.api.service.promotions.fee.AmzPromotionFeeStockRecordService;
import com.bizark.op.common.config.WeChatBootConfigure;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.*;
import com.bizark.op.service.mapper.promotions.MarPromotionFeeStockLogMapper;
import com.bizark.op.service.mapper.promotions.fee.AmzPromotionFeeStockRecordMapper;
import com.bizark.op.service.mapper.ticket.SaleOrdersMapper;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * promotion促销费用活动skc库存消耗表
 *
 * @Author: Ailill
 * @Date: 2024/9/2 17:36
 */
@Service
@Slf4j
public class AmzPromotionFeeStockRecordServiceImpl extends ServiceImpl<AmzPromotionFeeStockRecordMapper, AmzPromotionFeeStockRecord> implements AmzPromotionFeeStockRecordService {

    @Autowired
    private IAmzPromotionsService amzPromotionsService;

    @Autowired
    private IAmzPromotionsSkuDetailService amzPromotionsSkuDetailService;

    @Autowired
    private AmzPromotionFeeDateHisRecordService amzPromotionFeeDateHisRecordService;

    @Autowired
    private SaleOrderItemsService saleOrderItemsService;

    @Autowired
    private AmzPromotionFeeStockRecordService amzPromotionFeeStockRecordService;

    @Autowired
    private SaleOrdersMapper saleOrdersMapper;

    @Autowired
    private MarPromotionFeeStockLogMapper marPromotionFeeStockLogMapper;

    @Autowired
    private WeChatBootConfigure weChatBootConfigure;

    @Override
    public void queryTemuprice(AmzPromotions amzPromotions) {
        AmzPromotions amzPromotionsReq = new AmzPromotions();
        String timeFlag = "2024-08-01 00:00:00";
        amzPromotionsReq.setEndTime(cn.hutool.core.date.DateUtil.parse(timeFlag));
        //分页执行任务信息
        int pageNo = 1;
        int pageSize = 100;
        log.info("计算temu促销费开始");
        try {
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<AmzPromotions> promotionsList = amzPromotionsService.sekectTemuPromotionList(amzPromotionsReq);
                if (CollectionUtil.isEmpty(promotionsList)) {
                    log.info("无对应temu促销活动");
                    break;
                }
                //查询活动下明细
                List<AmzPromotionsSkuDetail> promotionsSkuDetailList = amzPromotionsSkuDetailService.
                        list(Wrappers.lambdaQuery(AmzPromotionsSkuDetail.class).select(AmzPromotionsSkuDetail::getParentAsin,AmzPromotionsSkuDetail::getPromotionsId,
                                AmzPromotionsSkuDetail::getSellerSku,AmzPromotionsSkuDetail::getPrice,AmzPromotionsSkuDetail::getDiscountPrice,AmzPromotionsSkuDetail::getId
                                ).in(AmzPromotionsSkuDetail::getPromotionsId, promotionsList.stream().map(AmzPromotions::getId).collect(Collectors.toList())));
                if (CollectionUtil.isEmpty(promotionsSkuDetailList)) {
                    log.info("没有需要记录库存消耗的促销明细");
                    return;
                }
                //活动转为行信息
                Map<Long, AmzPromotions> promotionsMap = promotionsList.stream().collect(Collectors.toMap(AmzPromotions::getId, Function.identity(), (a, b) -> a));
                //SKC 分组
                Map<String, List<AmzPromotionsSkuDetail>> skcIdMapDetailList = promotionsSkuDetailList.stream().filter(t -> StringUtils.isNotEmpty(t.getParentAsin())).collect(Collectors.groupingBy(AmzPromotionsSkuDetail::getParentAsin));
                //skcId参与多场促销
                Map<String, List<AmzPromotions>> multiInfo = new HashMap<>();

                for (String skcId : skcIdMapDetailList.keySet()) {
                    List<AmzPromotionsSkuDetail> amzPromotionsSkuDetails = skcIdMapDetailList.get(skcId);
                    ArrayList<AmzPromotions> amzPromotionsArray = new ArrayList<>();
                    List<Long> promotionIds = amzPromotionsSkuDetails.stream().map(AmzPromotionsSkuDetail::getPromotionsId).distinct().collect(Collectors.toList());
                    for (Long promotionId : promotionIds) {
                        amzPromotionsArray.add(promotionsMap.get(promotionId));
                    }
                    multiInfo.put(skcId, amzPromotionsArray);
                }

                this.promotionIdsRateHis(multiInfo, promotionsSkuDetailList);
                pageNo++;
            }
        } catch (Exception e) {
            log.error("temu促销费计算异常：{}",e);
            throw new CustomException("temu促销费计算异常");
        }
        log.info("计算temu促销费结束----");
    }

    @Override
    public void queryTemupriceMuanl(AmzPromotions amzPromotions) {
        Date date = DateUtil.addDate(DateUtil.UtcToPacificDate(DateUtils.getNowDate()), -1);
        DateTime endDate = cn.hutool.core.date.DateUtil.endOfDay(date);
        if (amzPromotions.getEndTime() == null) {
            amzPromotions.setEndTime(endDate);
        }

        //分页执行任务信息
        List<AmzPromotions> promotionsList = amzPromotionsService.sekectTemuPromotionList(amzPromotions);
        if (CollectionUtil.isEmpty(promotionsList)) {
            log.info("无对应temu促销活动");
            return;
        }
        //查询明细
        List<AmzPromotionsSkuDetail> promotionsSkuDetailList = amzPromotionsSkuDetailService.
                list(Wrappers.lambdaQuery(AmzPromotionsSkuDetail.class).select(AmzPromotionsSkuDetail::getParentAsin, AmzPromotionsSkuDetail::getPromotionsId,
                        AmzPromotionsSkuDetail::getSellerSku, AmzPromotionsSkuDetail::getPrice, AmzPromotionsSkuDetail::getDiscountPrice, AmzPromotionsSkuDetail::getId
                ).in(AmzPromotionsSkuDetail::getPromotionsId, promotionsList.stream().map(AmzPromotions::getId).collect(Collectors.toList())));
        if (CollectionUtil.isEmpty(promotionsSkuDetailList)) {
            log.info("没有需要记录库存消耗的促销明细");
            return;
        }
        //活动转为行信息
        Map<Long, AmzPromotions> promotionsMap = promotionsList.stream().collect(Collectors.toMap(AmzPromotions::getId, Function.identity(), (a, b) -> a));
        //SKC 分组
        Map<String, List<AmzPromotionsSkuDetail>> skcIdMapDetailList = promotionsSkuDetailList.stream().filter(t -> StringUtils.isNotEmpty(t.getParentAsin())).collect(Collectors.groupingBy(AmzPromotionsSkuDetail::getParentAsin));
        //skcId参与多场促销
        Map<String, List<AmzPromotions>> multiInfo = new HashMap<>();

        for (String skcId : skcIdMapDetailList.keySet()) {
            List<AmzPromotionsSkuDetail> amzPromotionsSkuDetails = skcIdMapDetailList.get(skcId);
            ArrayList<AmzPromotions> amzPromotionsArray = new ArrayList<>();
            List<Long> promotionIds = amzPromotionsSkuDetails.stream().map(AmzPromotionsSkuDetail::getPromotionsId).distinct().collect(Collectors.toList());
            for (Long promotionId : promotionIds) {
                amzPromotionsArray.add(promotionsMap.get(promotionId));
            }
            multiInfo.put(skcId, amzPromotionsArray);
        }

        this.promotionIdsRateHis(multiInfo, promotionsSkuDetailList);


        log.info("计算temu促销费结束----");
    }

    /**
     * @description: 每天定时设置促销费
     * @author: Moore
     * @date: 2024/9/6 13:29
     * @param
     * @param amzPromotions
     * @return: void
    **/
    @Override
    public void queryTemupriceDayJob(AmzPromotions amzPromotions) {
        Date date = DateUtil.addDate(DateUtil.UtcToPacificDate(DateUtils.getNowDate()), -1);
        if (amzPromotions.getEndTime() == null) {
            amzPromotions.setEndTime(date);
        }
        log.info("促销计算参数：{}",JSONObject.toJSONString(amzPromotions));

        ExecutorService executor = Executors.newFixedThreadPool(5); // 控制并发线程数
        List<CompletableFuture<Void>> futuresList = new ArrayList<>();

        //以SKC分组
        int pageNo = 1;
        int pageSize = 100;
        try {
            log.info("temu每日促销开始计算-------------------：{}", cn.hutool.core.date.DateUtil.format(amzPromotions.getEndTime(),DatePattern.NORM_DATETIME_PATTERN));
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<AmzPromotionsTemuVO> promotionsList = amzPromotionsService.selectAmzPromotionsListBySkcGroup(amzPromotions);
                if (CollectionUtil.isEmpty(promotionsList)) {
                    log.info("无对应temu促销活动");
                    break;
                }
                //获取分组下所有活动ID
                List<String> promotionsIdList = new ArrayList<>();
                promotionsList.forEach(
                        item -> {
                            promotionsIdList.addAll(Arrays.asList(item.getPromotionsIds().split(",")));
                        }
                );

                //查询明细
                List<AmzPromotionsSkuDetail> detailList = amzPromotionsSkuDetailService.
                        list(Wrappers.lambdaQuery(AmzPromotionsSkuDetail.class).select(AmzPromotionsSkuDetail::getParentAsin, AmzPromotionsSkuDetail::getPromotionsId,
                                AmzPromotionsSkuDetail::getSellerSku, AmzPromotionsSkuDetail::getPrice, AmzPromotionsSkuDetail::getDiscountPrice, AmzPromotionsSkuDetail::getId).
                                in(AmzPromotionsSkuDetail::getPromotionsId, promotionsIdList));
                if (CollectionUtil.isEmpty(detailList)) {
                    log.info("没有需要记录库存消耗的促销明细");
                    continue;
                }

                // 活动
                Map<Long, AmzPromotions> promotionsMap = amzPromotionsService.lambdaQuery().in(AmzPromotions::getId, promotionsIdList).list().stream().collect(Collectors.toMap(AmzPromotions::getId, Function.identity(), (a, b) -> a));

                for (AmzPromotionsTemuVO amzPromotionsTemuVO : promotionsList) {
                    List<AmzPromotions> listFlag = new ArrayList<>();
                    for (String pid : amzPromotionsTemuVO.getPromotionsIds().split(",")) {
                        listFlag.add(promotionsMap.get(Long.valueOf(pid)));
                    }
                    amzPromotionsTemuVO.setAmzPromotionsList(listFlag);
                }

                // 避免闭包引用被修改
                List<AmzPromotionsTemuVO> taskPromotions = new ArrayList<>(promotionsList);
                List<AmzPromotionsSkuDetail> taskDetails = new ArrayList<>(detailList);
                int currentPage = pageNo;

                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        log.info("Page {} 开始处理", currentPage);
                        this.promotionTemuDayPriceV2(taskPromotions, taskDetails, amzPromotions.getEndTime());
                        log.info("Page {} 处理完成", currentPage);
                    } catch (Exception e) {
                        log.error("Page {} 处理异常：", currentPage, e);
                    }
                }, executor);
                futuresList.add(future);
                log.info("---temu每日促销费计算执行中---");
                pageNo++;
            }
            CompletableFuture.allOf(futuresList.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            log.error("每日temu促销费计算失败：{}", e);
            throw new CustomException("每日temu促销费执行失败！");
        }
        log.info("temu每日促销执行结束:{}",cn.hutool.core.date.DateUtil.format(amzPromotions.getEndTime(),DatePattern.NORM_DATETIME_PATTERN));
    }


    /**
     * @description: 手动计算指定促销ID促销费
     * @author: Moore
     * @date: 2024/10/25 9:38
     * @param
     * @param promotionsIds
     * @return: void
     **/
    @Override
    public void manuTemuPriceByPromotionId(Date beginTimeflag,List<String> promotionsIds) {
        if (CollectionUtils.isEmpty(promotionsIds)||beginTimeflag==null) {
            return;
        }

        //查询促销主表信息
        List<TemuPromotionsPriceVO> promotionsList = amzPromotionsService.selectAmzPromotionsListByPromotionsIds(promotionsIds);
        if (CollectionUtils.isEmpty(promotionsList)) {
            return;
        }

        //获取所有业务主键，即为历史表promotionId
        List<Long> promotionsIdsList = promotionsList.stream().map(TemuPromotionsPriceVO::getId).collect(Collectors.toList());
        //查询明细
        List<AmzPromotionsSkuDetail> detailList = amzPromotionsSkuDetailService.
                list(Wrappers.lambdaQuery(AmzPromotionsSkuDetail.class).select(AmzPromotionsSkuDetail::getParentAsin, AmzPromotionsSkuDetail::getPromotionsId,
                        AmzPromotionsSkuDetail::getSellerSku, AmzPromotionsSkuDetail::getPrice, AmzPromotionsSkuDetail::getDiscountPrice,AmzPromotionsSkuDetail::getDiscount, AmzPromotionsSkuDetail::getId).
                        in(AmzPromotionsSkuDetail::getPromotionsId, promotionsIdsList));
        if (CollectionUtil.isEmpty(detailList)) {
            log.info("无对应促销信息");
            return;
        }
         //始终10月1日开始算,获取历史表已计算。

        //明细根据促销主键Id分组
        Map<Long, List<AmzPromotionsSkuDetail>> iteMap = detailList.stream().collect(Collectors.groupingBy(AmzPromotionsSkuDetail::getPromotionsId));
        for (TemuPromotionsPriceVO amzPromotionsTemuVO : promotionsList) {
            amzPromotionsTemuVO.setSkuDetailList(iteMap.get(amzPromotionsTemuVO.getId()));
        }

        this.promotionTemuDayPriceV4(promotionsList, beginTimeflag);

    }

    /**
     * @description: 计算指定促销类型
     * @author: Moore
     * @date: 2024/11/14 10:31
     * @param
     * @param endTime
     * @param promotionsIds
     * @return: void
    **/
    @Override
    public void manuTemuPriceByAssignType(Date endTime ,List<String> promotionsIds,String remark) {

        //查询促销主表信息
        List<TemuPromotionsPriceVO> promotionsList = amzPromotionsService.selectSessionProtionList(promotionsIds);
        if (CollectionUtils.isEmpty(promotionsList)) {
            return;
        }

        promotionsList = promotionsList.stream().filter(item ->!new Integer(0).equals(item.getUnusedStock())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(promotionsList)) {
            return;
        }


        //获取所有业务主键，即为历史表promotionId
        List<Long> promotionsIdsList = promotionsList.stream().map(TemuPromotionsPriceVO::getId).collect(Collectors.toList());
        //查询明细
        List<AmzPromotionsSkuDetail> detailList = amzPromotionsSkuDetailService.
                list(Wrappers.lambdaQuery(AmzPromotionsSkuDetail.class).select(AmzPromotionsSkuDetail::getParentAsin, AmzPromotionsSkuDetail::getPromotionsId,
                        AmzPromotionsSkuDetail::getSellerSku, AmzPromotionsSkuDetail::getPrice, AmzPromotionsSkuDetail::getDiscountPrice,AmzPromotionsSkuDetail::getDiscount, AmzPromotionsSkuDetail::getId).
                        in(AmzPromotionsSkuDetail::getPromotionsId, promotionsIdsList));
        if (CollectionUtil.isEmpty(detailList)) {
            log.info("无对应促销信息");
            return;
        }
        //始终10月1日开始算,获取历史表已计算。

        //明细根据促销主键Id分组
        Map<Long, List<AmzPromotionsSkuDetail>> iteMap = detailList.stream().collect(Collectors.groupingBy(AmzPromotionsSkuDetail::getPromotionsId));
        for (TemuPromotionsPriceVO amzPromotionsTemuVO : promotionsList) {
            amzPromotionsTemuVO.setSkuDetailList(iteMap.get(amzPromotionsTemuVO.getId()));
        }

        //当前PST时间
        LocalDateTime localNow = LocalDateTime.now(TimeZone.getTimeZone("America/Los_Angeles").toZoneId());
        String nowPstTime = localNow.format(DateTimeFormatter.ISO_DATE);
        DateTime usTime = cn.hutool.core.date.DateUtil.parse(nowPstTime);
       this.promotionTemuDayPriceV5(promotionsList, usTime,endTime,remark);
    }


    /** 每日促销费通知
     *
     */
    @Override
    public void sendInformTemuPromotionFeeJob() {
        Date date = DateUtil.addDate(DateUtil.UtcToPacificDate(DateUtils.getNowDate()), -1);
        DateTime dateTime = cn.hutool.core.date.DateUtil.beginOfDay(date);
        DateTime endTime = cn.hutool.core.date.DateUtil.endOfDay(date);
        BigDecimal pFee = amzPromotionsService.selectOrderNowPromotionFee(dateTime, endTime);
        String msgFlag = "PST时间:" + cn.hutool.core.date.DateUtil.format(date, DatePattern.NORM_DATE_PATTERN) + "  计算后Temu订单促销费：" + pFee;
        WeComRobotUtil.sendTextMsgNew(msgFlag, "TEMU_PROMOTION", weChatBootConfigure);

    }


    /**
     * @param
  * @param amzPromotionsTemuVOS group SKC  skc对应活动、selelrSku
     * @description: SKC多活动，促销费计算(弃用。计算历史总合计)
     * @author: Moore
     * @date: 2024/9/3 10:51
     * @return: void
     **/
    public void promotionTemuDayPrice( List<AmzPromotionsTemuVO>  amzPromotionsTemuVOS,
                                       List<AmzPromotionsSkuDetail> promotionsSkuDetailList,
                                       Date nowDate) {

        //根据活动ID分组，获取所有
        Map<Long, List<AmzPromotionsSkuDetail>> nowPromtion = promotionsSkuDetailList.stream().collect(Collectors.groupingBy(AmzPromotionsSkuDetail::getPromotionsId));
        String nowtime = DateUtil.convertDateToString(nowDate, "yyyy-MM-dd");
        String pstqueryFrom = cn.hutool.core.date.DateUtil.format(nowDate, DatePattern.NORM_DATETIME_PATTERN);
        String pstqueryTo = cn.hutool.core.date.DateUtil.format(cn.hutool.core.date.DateUtil.endOfDay(nowDate), DatePattern.NORM_DATETIME_PATTERN);

        //所有SKC
        for (AmzPromotionsTemuVO amzPromotionsTemuVO : amzPromotionsTemuVOS) {
            String skc = amzPromotionsTemuVO.getSkc();
            List<AmzPromotions> amzPromotionsInfo = amzPromotionsTemuVO.getAmzPromotionsList();
            if (CollectionUtils.isEmpty(amzPromotionsInfo)) {
                continue;
            }
            Long shopId = amzPromotionsInfo.get(0).getShopId();
            //活动天顺序
            HashMap<Long, List<AmzPromotionFeeDateHisRecord>> timepromotionMap = amzPromotionFeeDateHisRecordService.selectMaxDateSkcPrmotionDay(amzPromotionsTemuVO, nowDate);

            if (timepromotionMap.size() == 0) {
                continue; //此活动无历史
            }

            //单活动
            if ( amzPromotionsTemuVO.getAmzPromotionsList().size()==1) {
                //
                Long pid = amzPromotionsTemuVO.getAmzPromotionsList().get(0).getId();
                List<AmzPromotionFeeDateHisRecord> amzPromotionFeeDateHisRecord = timepromotionMap.get(pid);
                if (CollectionUtils.isEmpty(amzPromotionFeeDateHisRecord)) {
                    continue; //无当天促销信息
                }

                //活动当天需消耗
                Integer skcStock = amzPromotionFeeDateHisRecord.get(0).getSkcStock()==null?0:amzPromotionFeeDateHisRecord.get(0).getSkcStock(); //提报库存
                Integer remainStock = amzPromotionFeeDateHisRecord.get(0).getRemainStock() == null ? 0 : amzPromotionFeeDateHisRecord.get(0).getRemainStock();//剩余库存
                if (skcStock - remainStock==0) {
                    continue;
                }


                //活动SellerSKu
                List<String> sellerSkuList = nowPromtion.get(pid).stream().map(AmzPromotionsSkuDetail::getSellerSku).collect(Collectors.toList());//SKU对应sellerSKU

                //sellerSku 对应促销费封装
                HashMap<String, BigDecimal> selllerSkuDiscountPrice = new HashMap<>();
                amzPromotionFeeDateHisRecord.forEach(
                        item -> {
                            //日常减活动
                            BigDecimal discountPrice = item.getDailyPrice().subtract(item.getActivityPrice()).divide(new BigDecimal(100));
                            selllerSkuDiscountPrice.put(item.getSellerSku(), discountPrice);
                        }

                );

                //查询活动 SKC 消耗
                List<AmzPromotionFeeStockRecord> promotionList = amzPromotionFeeStockRecordService.lambdaQuery()
                        .eq(AmzPromotionFeeStockRecord::getSkcId, skc)
                        .eq(AmzPromotionFeeStockRecord::getPromotionId, pid).list();

                //已消耗
                int usedStock = promotionList.stream().filter(item -> item.getUsedStock() != null).mapToInt(AmzPromotionFeeStockRecord::getUsedStock).sum(); //消耗总库存

                //当天存在消
                List<AmzPromotionFeeStockRecord> timePromotion = promotionList.stream().filter(item -> nowtime.equals(DateUtil.convertDateToString(item.getDate(), "yyyy-MM-dd"))).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(promotionList)&&!CollectionUtils.isEmpty(timePromotion)) {
                    continue;
                }



                HashMap<String, BigDecimal> selllerTotalFlag = new HashMap<>();
                BigDecimal initStockFlag = new BigDecimal(skcStock - remainStock - usedStock); //此活动当前天可消耗 （提报-剩余-已消耗）
                AtomicReference<BigDecimal> sumTotal = new AtomicReference<>(new BigDecimal("0")); //已计总Total

                //消耗为空,或当天消耗空，且可消耗大于0 且提报大于已消耗
                if (initStockFlag.compareTo(BigDecimal.ZERO) > 0 && skcStock > usedStock) { //提报大于已消耗
                    //获取SellerSKu订单
                    List<SaleOrderItemPromotionPriceVO> saleOrderItemPromotionPriceVOS = saleOrdersMapper.selectOrderItemBySellerSku(sellerSkuList, pstqueryFrom, pstqueryTo);
//
//                  if (!CollectionUtils.isEmpty(saleOrderItemPromotionPriceVOS)){
//                      log.error("获取到有订单SKC：{}",skc);
//                      return;
//                  }

                    saleOrderItemPromotionPriceVOS = saleOrderItemPromotionPriceVOS.stream().filter(item -> !StringUtils.isEmpty(item.getSellerSku())).collect(Collectors.toList());
                    for (SaleOrderItemPromotionPriceVO saleOrderPromotionPriceItem : saleOrderItemPromotionPriceVOS) {
                        if (saleOrderPromotionPriceItem.getQuantity() == null || saleOrderPromotionPriceItem.getQuantity() == 0) {
                            continue;
                        }

                        //单行总数量
                        BigDecimal orderQuantity = new BigDecimal(saleOrderPromotionPriceItem.getQuantity()) == null ? BigDecimal.ONE : new BigDecimal(saleOrderPromotionPriceItem.getQuantity()); //行销量

                        //本次计算后是否大于本次可消耗
                        boolean gtFlag = sumTotal.get().add(orderQuantity).compareTo(initStockFlag) > 0;
                        if (gtFlag) {
                            BigDecimal residueNum = initStockFlag.subtract(sumTotal.get()); //当天可消耗-已消耗 若等于0，结束循环
                            if (residueNum.compareTo(BigDecimal.ZERO) == 0) {
                                break;
                            }
                            //本次实际计算数量
                            orderQuantity = residueNum;
                        }

                        BigDecimal promotionPrice  = selllerSkuDiscountPrice.get(saleOrderPromotionPriceItem.getSellerSku());
                        saleOrderPromotionPriceItem.setPromotionDiscountPrice(promotionPrice.multiply(orderQuantity)); //销量 * 折扣
                        saleOrderPromotionPriceItem.setOriItemAmount(saleOrderPromotionPriceItem.getItemAmount()); //原始销售额
                        saleOrderPromotionPriceItem.setItemAmount(saleOrderPromotionPriceItem.getItemAmount().subtract(saleOrderPromotionPriceItem.getPromotionDiscountPrice())); //计算促销后销售额->(原始销售额 - 促销费)
                        usedStock += orderQuantity.intValue(); //标记已消耗
                        BigDecimal sellerSkuBig = selllerTotalFlag.get(saleOrderPromotionPriceItem.getSellerSku()) == null ? BigDecimal.ZERO : selllerTotalFlag.get(saleOrderPromotionPriceItem.getSellerSku());
                        selllerTotalFlag.put(saleOrderPromotionPriceItem.getSellerSku(), sellerSkuBig.add(orderQuantity));
                        sumTotal.set(sumTotal.get().add(orderQuantity));  //标记为了防止超出消耗范围
                    }


                    //记录已消耗库存信息表
                    for (String sellerSku : selllerTotalFlag.keySet()) {
                        AmzPromotionFeeStockRecord amzPromotionFeeStockRecord = new AmzPromotionFeeStockRecord();
                        amzPromotionFeeStockRecord.setShopId(shopId);
                        amzPromotionFeeStockRecord.setPromotionId(pid);
                        amzPromotionFeeStockRecord.setSkcId(skc);
                        amzPromotionFeeStockRecord.setDate(nowDate);
                        amzPromotionFeeStockRecord.setSellerSku(sellerSku);
                        amzPromotionFeeStockRecord.setUsedStock(selllerTotalFlag.get(sellerSku).intValue());
                        amzPromotionFeeStockRecord.settingDefaultSystemCreate();
                        amzPromotionFeeStockRecordService.save(amzPromotionFeeStockRecord);
                    }

                    //更新订单
                    List<SaleOrderItemPromotionPriceVO> saleOrderUpatePrice = saleOrderItemPromotionPriceVOS.stream().filter(item -> item.getPromotionDiscountPrice() != null).collect(Collectors.toList());
                    for (SaleOrderItemPromotionPriceVO saleOrderItemPromotionPriceVO : saleOrderUpatePrice) {
                        saleOrdersMapper.udpateOrderItemsPromotionDiscountPrice(saleOrderItemPromotionPriceVO.getPromotionDiscountPrice(),
                                saleOrderItemPromotionPriceVO.getItemAmount(), saleOrderItemPromotionPriceVO.getOriItemAmount(),
                                saleOrderItemPromotionPriceVO.getId(),"V1");
                    }
                }
            }


            //多活动
            if (amzPromotionsTemuVO.getAmzPromotionsList().size() > 1) {

                //获取活动历史对应天数据
                List<Long> promotionIds = timepromotionMap.keySet().stream().collect(Collectors.toList());
                List<AmzPromotionFeeDateHisRecord> infoAll = new ArrayList<>();
                List<List<AmzPromotionFeeDateHisRecord>> collectList = timepromotionMap.values().stream().collect(Collectors.toList());
                for (List<AmzPromotionFeeDateHisRecord> amzPromotionFeeDateHisRecords : collectList) {
                    infoAll.addAll(amzPromotionFeeDateHisRecords);
                }


                //查询所有活动已消耗表
                List<AmzPromotionFeeStockRecord> promotionList = amzPromotionFeeStockRecordService.lambdaQuery().in(AmzPromotionFeeStockRecord::getPromotionId, promotionIds).list();


                //每个活动已消耗封装
                Map<Long, Integer> groupUsedStockFlag = new HashMap<>();
                if (!CollectionUtils.isEmpty(promotionList)){ //剔除已计算天
                    infoAll.removeIf(
                            item -> (promotionList.stream().filter(pr -> pr.getPromotionId().equals(item.getPromotionId()) && DateUtil.convertDateToString(pr.getDate(), "yyyy-MM-dd").equals(nowtime)).findFirst().orElse(null) != null)
                    );
                    if (CollectionUtils.isEmpty(infoAll)) { //无可操作天数，结束本次时间循环
                        continue;
                    }

                    groupUsedStockFlag= promotionList.stream().filter(item->item.getUsedStock()!=null)
                            .collect(Collectors.groupingBy(
                                    AmzPromotionFeeStockRecord::getPromotionId,
                                    Collectors.reducing(0, AmzPromotionFeeStockRecord::getUsedStock, Integer::sum)));
                }

                //每个活动可消耗
                HashMap<Long, Integer> noOpStock = new HashMap<>();
                for (AmzPromotionFeeDateHisRecord amzPromotionFeeDateHisRecord : infoAll) {
                    Integer skcStock = amzPromotionFeeDateHisRecord.getSkcStock() == null ? 0 : amzPromotionFeeDateHisRecord.getSkcStock(); //提报
                    Integer remainStock = amzPromotionFeeDateHisRecord.getRemainStock() == null ? 0 : amzPromotionFeeDateHisRecord.getRemainStock(); //剩余
                    Integer usedStock = groupUsedStockFlag.get(amzPromotionFeeDateHisRecord.getPromotionId()) == null ? 0 : groupUsedStockFlag.get(amzPromotionFeeDateHisRecord.getPromotionId()); //已消耗
                    noOpStock.put(amzPromotionFeeDateHisRecord.getPromotionId(),skcStock - remainStock - usedStock);
                }

                //活动申报价升序
                infoAll = infoAll.stream().sorted(Comparator.comparing(AmzPromotionFeeDateHisRecord::getActivityPrice)).collect(Collectors.toList());

                //查订单
                List<SaleOrderItemPromotionPriceVO> orderItemsVo = saleOrdersMapper.selectOrderItemBySellerSku( infoAll.stream().map(AmzPromotionFeeDateHisRecord::getSellerSku).distinct().collect(Collectors.toList()), pstqueryFrom, pstqueryTo);
                orderItemsVo = orderItemsVo.stream().filter(item -> !StringUtils.isEmpty(item.getSellerSku())).collect(Collectors.toList());


                //活动,selelr->计算数量
                HashMap<Long, HashMap<String, Integer>> prSum = new HashMap<>();

                //各活动消耗
                HashMap<Long, Integer> prSumTotla = new HashMap<>();

                for (AmzPromotionFeeDateHisRecord itemHis : infoAll) {  //活动 - selelrsku
                    Long promotionId = itemHis.getPromotionId(); //活动ID
                    Integer initFlagStock = noOpStock.get(promotionId); //可消耗指整个活动下
                    if (initFlagStock==null||initFlagStock < 0) {
                        continue;
                    }
                    //sellerSKu折扣
                    BigDecimal promotionPrice = itemHis.getDailyPrice().subtract(itemHis.getActivityPrice()).divide(new BigDecimal(100));//（日常-活动）*100 = 折扣
                    String sellerSku = itemHis.getSellerSku(); //sellerSKu


                    List<SaleOrderItemPromotionPriceVO> sellerSkuOrderInfo = orderItemsVo.stream().filter(item -> item.getSellerSku().equals(sellerSku) && item.getPromotionDiscountPrice() == null).collect(Collectors.toList());
                    for (SaleOrderItemPromotionPriceVO orderItems : sellerSkuOrderInfo) {
                        Integer sumTotalFlag = prSumTotla.get(promotionId)==null?0:prSumTotla.get(promotionId);

                        if (orderItems.getQuantity() == null || orderItems.getQuantity() == 0) {
                            continue;
                        }

                        //单行总数量
                        BigDecimal orderQuantity = new BigDecimal(orderItems.getQuantity()); //行销量
                        //本次计算后是否大于本次可消耗
                        boolean gtFlag= (sumTotalFlag + orderQuantity.intValue()) > initFlagStock;
                        if (gtFlag) {
                            Integer residueNum = initFlagStock-sumTotalFlag; //当天可消耗-已消耗 若等于0，结束循环
                            if ((initFlagStock-sumTotalFlag)==0) { //可消耗-已消耗 结束
                                break;
                            }
                            //本次实际计算数量
                            orderQuantity =  new BigDecimal(residueNum);
                        }

                        orderItems.setPromotionDiscountPrice(promotionPrice.multiply(orderQuantity)); //销量 * 折扣
                        orderItems.setOriItemAmount(orderItems.getItemAmount()); //原始销售额
                        orderItems.setItemAmount(orderItems.getItemAmount().subtract(orderItems.getPromotionDiscountPrice())); //计算促销后销售额->(原始销售额 - 促销费)


                        //计算此活动已计算total
                        Integer sumTotalPr = prSumTotla.get(promotionId);
                        if (sumTotalPr == null) {
                            prSumTotla.put(promotionId, orderQuantity.intValue());
                        } else {
                            prSumTotla.put(promotionId, sumTotalPr + orderQuantity.intValue());
                        }

                        //汇总行数据
                        HashMap<String, Integer> stringIntegerHashMap = prSum.get(promotionId);
                        if (stringIntegerHashMap == null) {
                            HashMap<String, Integer> integerHashMap = new HashMap<>();
                            integerHashMap.put(sellerSku, orderQuantity.intValue());

                            prSum.put(promotionId, integerHashMap);
                        } else {
                            Integer sellerSkuStock = stringIntegerHashMap.get(sellerSku);
                            if (sellerSkuStock == null) {
                                stringIntegerHashMap.put(sellerSku,  orderQuantity.intValue());
                            } else {
                                stringIntegerHashMap.put(sellerSku, sellerSkuStock +  orderQuantity.intValue()); //累计库存
                            }
                        }

                    }

                }


                //多活动计算完毕，保存消耗日志
                if (prSum.size() > 0) {
                    for (Long pid : prSum.keySet()) {
                        HashMap<String, Integer> childList = prSum.get(pid);
                        for (String sellerSku : childList.keySet()) {
                            Integer stock = childList.get(sellerSku);
                            AmzPromotionFeeStockRecord amzPromotionFeeStockRecord = new AmzPromotionFeeStockRecord();
                            amzPromotionFeeStockRecord.setShopId(shopId);
                            amzPromotionFeeStockRecord.setSkcId(skc);
                            amzPromotionFeeStockRecord.setDate(nowDate);
                            amzPromotionFeeStockRecord.setPromotionId(pid);
                            amzPromotionFeeStockRecord.setSellerSku(sellerSku);
                            amzPromotionFeeStockRecord.setUsedStock(stock);
                            amzPromotionFeeStockRecord.settingDefaultSystemCreate();
                            amzPromotionFeeStockRecordService.save(amzPromotionFeeStockRecord);

                        }
                    }
                }
                //更新订单
                List<SaleOrderItemPromotionPriceVO> collect = orderItemsVo.stream().filter(item -> item.getPromotionDiscountPrice() != null).collect(Collectors.toList());
                for (SaleOrderItemPromotionPriceVO saleOrderItemPromotionPriceVO : collect) {
                    saleOrdersMapper.udpateOrderItemsPromotionDiscountPrice(saleOrderItemPromotionPriceVO.getPromotionDiscountPrice(),
                            saleOrderItemPromotionPriceVO.getItemAmount(), saleOrderItemPromotionPriceVO.getOriItemAmount(),
                            saleOrderItemPromotionPriceVO.getId(),"V1");
                }
            }


        }
    }





    /**
     * @param
     * @param amzPromotionsTemuVOS group SKC  skc对应活动、selelrSku
     * @description: SKC多活动，促销费计算
     * @author: Moore
     * @date: 2024/9/3 10:51
     * @return: void
     **/
    public void promotionTemuDayPriceV2( List<AmzPromotionsTemuVO>  amzPromotionsTemuVOS,
                                       List<AmzPromotionsSkuDetail> promotionsSkuDetailList,
                                       Date nowDate) {

        Date sumYesterdayTime = DateUtil.addDate(nowDate, -1);
        String sumYesterdayTimeStr = cn.hutool.core.date.DateUtil.format(sumYesterdayTime, DatePattern.NORM_DATE_PATTERN);


        //根据活动ID分组，获取所有
        Map<Long, List<AmzPromotionsSkuDetail>> nowPromtion = promotionsSkuDetailList.stream().collect(Collectors.groupingBy(AmzPromotionsSkuDetail::getPromotionsId));
        String nowtime = DateUtil.convertDateToString(nowDate, "yyyy-MM-dd");
        String pstqueryFrom = cn.hutool.core.date.DateUtil.format(nowDate, DatePattern.NORM_DATETIME_PATTERN);
        String pstqueryTo = cn.hutool.core.date.DateUtil.format(cn.hutool.core.date.DateUtil.endOfDay(nowDate), DatePattern.NORM_DATETIME_PATTERN);


        //所有SKC
        for (AmzPromotionsTemuVO amzPromotionsTemuVO : amzPromotionsTemuVOS) {
            String skc = amzPromotionsTemuVO.getSkc();
            List<AmzPromotions> amzPromotionsInfo = amzPromotionsTemuVO.getAmzPromotionsList();

            //skc无活动
            if (CollectionUtils.isEmpty(amzPromotionsInfo)) {
                continue;
            }

            Long shopId = amzPromotionsInfo.get(0).getShopId();
            //对应天活动数据
            Map<Long, List<AmzPromotionFeeDateHisRecord>> timepromotionMap = amzPromotionFeeDateHisRecordService.selectMaxDateSkcPrmotionDayNew(amzPromotionsTemuVO, nowDate);

            //昨日数据
            Map<Long, Integer> yesterDayStockMap = amzPromotionFeeDateHisRecordService.selectPormotionyesterDayStock(sumYesterdayTime, amzPromotionsInfo.stream().map(AmzPromotions::getId).distinct().collect(Collectors.toList()));
            if (timepromotionMap.size() == 0) {
                continue;
            }

            //单活动
            if ( amzPromotionsTemuVO.getAmzPromotionsList().size()==1) {
                Long pid = amzPromotionsTemuVO.getAmzPromotionsList().get(0).getId();
                List<AmzPromotionFeeDateHisRecord> amzPromotionFeeDateHisRecord = timepromotionMap.get(pid);

                //查询活动 SKC 消耗
                List<AmzPromotionFeeStockRecord> promotionList = amzPromotionFeeStockRecordService.lambdaQuery()
                        .eq(AmzPromotionFeeStockRecord::getPromotionId, pid).list();

                Integer lastUnusedstockt = 0;
                if (!CollectionUtils.isEmpty(promotionList)) {
                    List<AmzPromotionFeeStockRecord> timePromotion = promotionList.stream().filter(item -> nowtime.equals(DateUtil.convertDateToString(item.getDate(), "yyyy-MM-dd"))).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(timePromotion)) {
                        log.info("当天存在已计算促销费,未进行计算：{}",pid);
                        continue;
                    }
                    List<AmzPromotionFeeStockRecord> yesterDayPromotion = promotionList.stream().filter(item -> sumYesterdayTimeStr.equals(DateUtil.convertDateToString(item.getDate(), "yyyy-MM-dd"))).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(yesterDayPromotion)) {
                        lastUnusedstockt = yesterDayPromotion.stream().filter(item->item.getUsedStock()!=null).mapToInt(AmzPromotionFeeStockRecord::getUnusedStock).sum();
                    }
                }

                //活动可消耗 =（今日提报-剩余）-（昨日提报-剩余）+昨日剩余未消耗
                Integer nowSkcStock = amzPromotionFeeDateHisRecord.get(0).getSkcStock() == null ? 0 : amzPromotionFeeDateHisRecord.get(0).getSkcStock(); //提报库存
                Integer nowRemainStock = amzPromotionFeeDateHisRecord.get(0).getRemainStock() == null ? 0 : amzPromotionFeeDateHisRecord.get(0).getRemainStock();//剩余库存
                Integer yesterDayStock = yesterDayStockMap.get(pid) == null ? 0 : yesterDayStockMap.get(pid); //昨日提报-剩余
                if (nowSkcStock - nowRemainStock == 0) {
                    continue;
                }
                Integer nowStockFlag = nowSkcStock - nowRemainStock - yesterDayStock + lastUnusedstockt;
                if (nowStockFlag <= 0) {
                    log.info("当日可消耗，小于等于0，未进行计算：{},{}", nowStockFlag, pid);
                    continue;
                }

                //活动SellerSKu
                List<String> sellerSkuList = nowPromtion.get(pid).stream().map(AmzPromotionsSkuDetail::getSellerSku).collect(Collectors.toList());//SKU对应sellerSKU
                if (CollectionUtils.isEmpty(sellerSkuList)){
                    continue;
                }


                //sellerSku 对应促销费封装
                HashMap<String, BigDecimal> selllerSkuDiscountPrice = new HashMap<>();
                amzPromotionFeeDateHisRecord.forEach(
                        item -> {
                            BigDecimal discountPrice = item.getDailyPrice().subtract(item.getActivityPrice()).divide(new BigDecimal(100));
                            selllerSkuDiscountPrice.put(item.getSellerSku(), discountPrice);
                        }
                );




                HashMap<String, BigDecimal> selllerTotalFlagMap = new HashMap<>();
                BigDecimal initStockFlag = new BigDecimal(nowStockFlag); //今日可消耗
                AtomicReference<BigDecimal> sumTotal = new AtomicReference<>(new BigDecimal("0")); //已计总Total

                //可消耗小于等于0
                if (initStockFlag.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }

                //获取SellerSKu订单
                List<SaleOrderItemPromotionPriceVO> saleOrderItemPromotionPriceVOS = saleOrdersMapper.selectOrderItemBySellerSku(sellerSkuList, pstqueryFrom, pstqueryTo);
                saleOrderItemPromotionPriceVOS = saleOrderItemPromotionPriceVOS.stream().filter(item -> !StringUtils.isEmpty(item.getSellerSku())).collect(Collectors.toList());
                for (SaleOrderItemPromotionPriceVO saleOrderPromotionPriceItem : saleOrderItemPromotionPriceVOS) {
                    if (saleOrderPromotionPriceItem.getQuantity() == null || saleOrderPromotionPriceItem.getQuantity() == 0) {
                        continue;
                    }


                    //单行总数量
                    BigDecimal orderQuantity = new BigDecimal(saleOrderPromotionPriceItem.getQuantity()); //行销量

                    //对应促销费
                    BigDecimal promotionPrice = selllerSkuDiscountPrice.get(saleOrderPromotionPriceItem.getSellerSku());
                    if (promotionPrice == null) {
                        log.info("无促销费sellersku：{}", saleOrderPromotionPriceItem.getSellerSku());
                        continue;
                    }

                    //1. 折扣为负数 2.折扣大于订单金额不进行计算
                    if (promotionPrice.compareTo(BigDecimal.ZERO)<0||promotionPrice.compareTo(saleOrderPromotionPriceItem.getItemPrice())>0){
                        continue;
                    }

                    //本次计算后是否大于本次可消耗
                    boolean gtFlag = sumTotal.get().add(orderQuantity).compareTo(initStockFlag) > 0;
                    if (gtFlag) {
                        BigDecimal residueNum = initStockFlag.subtract(sumTotal.get()); //当天可消耗-已消耗 若等于0，结束循环
                        if (residueNum.compareTo(BigDecimal.ZERO) == 0) {
                            break;
                        }
                        //本次实际计算数量
                        orderQuantity = residueNum;
                    }

                    saleOrderPromotionPriceItem.setPromotionDiscountPrice(promotionPrice.multiply(orderQuantity)); //销量 * 折扣
                    saleOrderPromotionPriceItem.setOriItemAmount(saleOrderPromotionPriceItem.getItemAmount()); //原始销售额
                    saleOrderPromotionPriceItem.setItemAmount(saleOrderPromotionPriceItem.getItemAmount().subtract(saleOrderPromotionPriceItem.getPromotionDiscountPrice())); //计算促销后销售额->(原始销售额 - 促销费)


                    BigDecimal sellerSkuBig = selllerTotalFlagMap.get(saleOrderPromotionPriceItem.getSellerSku()) == null ? BigDecimal.ZERO : selllerTotalFlagMap.get(saleOrderPromotionPriceItem.getSellerSku());
                    selllerTotalFlagMap.put(saleOrderPromotionPriceItem.getSellerSku(), sellerSkuBig.add(orderQuantity));
                    sumTotal.set(sumTotal.get().add(orderQuantity));  //标记为了防止超出消耗范围


                    saleOrderPromotionPriceItem.setUsedQuantity(orderQuantity.intValue()); //日志使用  使用使用
                    saleOrderPromotionPriceItem.setPromotionId(pid); //日志使用  活动ID
                    saleOrderPromotionPriceItem.setDiscount(promotionPrice); //日志使用 折扣
                }

                //剩余未计算
                BigDecimal unUsedStock = initStockFlag.subtract(sumTotal.get());

                //记录已消耗库存信息表
                for (String sellerSku : selllerTotalFlagMap.keySet()) {
                    AmzPromotionFeeStockRecord amzPromotionFeeStockRecord = new AmzPromotionFeeStockRecord();
                    amzPromotionFeeStockRecord.setShopId(shopId);
                    amzPromotionFeeStockRecord.setPromotionId(pid);
                    amzPromotionFeeStockRecord.setSkcId(skc);
                    amzPromotionFeeStockRecord.setDate(nowDate);
                    amzPromotionFeeStockRecord.setSellerSku(sellerSku);
                    amzPromotionFeeStockRecord.setUsedStock(selllerTotalFlagMap.get(sellerSku).intValue());//已消耗
                    amzPromotionFeeStockRecord.settingDefaultSystemCreate();
                    amzPromotionFeeStockRecordService.save(amzPromotionFeeStockRecord);
                }
                if (unUsedStock.compareTo(BigDecimal.ZERO) > 0) {
                    AmzPromotionFeeStockRecord amzPromotionFeeStockRecord = new AmzPromotionFeeStockRecord();
                    amzPromotionFeeStockRecord.setShopId(shopId);
                    amzPromotionFeeStockRecord.setPromotionId(pid);
                    amzPromotionFeeStockRecord.setSkcId(skc);
                    amzPromotionFeeStockRecord.setDate(nowDate);
                    amzPromotionFeeStockRecord.setUsedStock(0);//此行已消耗，应为0
                    amzPromotionFeeStockRecord.setUnusedStock(unUsedStock.intValue());//未消耗
                    amzPromotionFeeStockRecord.setRemark("剩余未消耗");
                    amzPromotionFeeStockRecord.settingDefaultSystemCreate();
                    amzPromotionFeeStockRecordService.save(amzPromotionFeeStockRecord);
                }

                //更新订单
                List<SaleOrderItemPromotionPriceVO> saleOrderUpatePrice = saleOrderItemPromotionPriceVOS.stream().
                        filter(item -> item.getPromotionDiscountPrice() != null&&item.getPromotionDiscountPrice().compareTo(new BigDecimal("0"))>0).collect(Collectors.toList());
                for (SaleOrderItemPromotionPriceVO saleOrderItemPromotionPriceVO : saleOrderUpatePrice) {
                    saleOrdersMapper.udpateOrderItemsPromotionDiscountPrice(saleOrderItemPromotionPriceVO.getPromotionDiscountPrice(),
                            saleOrderItemPromotionPriceVO.getItemAmount(), saleOrderItemPromotionPriceVO.getOriItemAmount(),
                            saleOrderItemPromotionPriceVO.getId(),"V1");


                    MarPromotionFeeStockLog pOrderLog = new MarPromotionFeeStockLog();
                    pOrderLog.setUsedDate(nowDate);
                    pOrderLog.setShopId(shopId);
                    pOrderLog.setPromotionId(saleOrderItemPromotionPriceVO.getPromotionId());
                    pOrderLog.setSkcId(skc);
                    pOrderLog.setSellerSku(saleOrderItemPromotionPriceVO.getSellerSku());
                    pOrderLog.setOrderItemId(saleOrderItemPromotionPriceVO.getId());
                    pOrderLog.setUsedStock(saleOrderItemPromotionPriceVO.getUsedQuantity());
                    pOrderLog.setDiscount(saleOrderItemPromotionPriceVO.getDiscount());
                    pOrderLog.settingDefaultSystemCreate();
                    marPromotionFeeStockLogMapper.insert(pOrderLog);
                }

            }


            //多活动
            if (amzPromotionsTemuVO.getAmzPromotionsList().size() > 1) {

                //获取活动历史对应天数据
                List<Long> promotionIds = timepromotionMap.keySet().stream().collect(Collectors.toList());
                List<List<AmzPromotionFeeDateHisRecord>> collectList = timepromotionMap.values().stream().collect(Collectors.toList());

                List<AmzPromotionFeeDateHisRecord> infoAll = new ArrayList<>();
                //嵌套列表扁平化
                for (List<AmzPromotionFeeDateHisRecord> amzPromotionFeeDateHisRecords : collectList) {
                    infoAll.addAll(amzPromotionFeeDateHisRecords);
                }


                //查询所有活动已消耗表
                List<AmzPromotionFeeStockRecord> promotionList = amzPromotionFeeStockRecordService.lambdaQuery().in(AmzPromotionFeeStockRecord::getPromotionId, promotionIds).list();

                //可操作活动ID
                List<Long> newPromotionids = null;
                //每个活动昨日已消耗封装
                Map<Long, Integer> groupUnusedStockFlag = new HashMap<>();
                if (!CollectionUtils.isEmpty(promotionList)){ //剔除已计算天
                    infoAll.removeIf(
                            item -> (promotionList.stream().filter(pr -> pr.getPromotionId().equals(item.getPromotionId()) && DateUtil.convertDateToString(pr.getDate(), "yyyy-MM-dd").equals(nowtime)).findFirst().orElse(null) != null)
                    );
                    if (CollectionUtils.isEmpty(infoAll)) { //无可操作天数，结束本次时间循环
                        log.info("此天促销费已计算:{},",JSONObject.toJSONString(promotionList));
                        continue;
                    }

                    newPromotionids = infoAll.stream().map(AmzPromotionFeeDateHisRecord::getPromotionId).collect(Collectors.toList());

                    //获取每个活动昨日 未消耗
                    for (Long promotionId : newPromotionids) {
                        List<AmzPromotionFeeStockRecord> yesterDayPromotion = promotionList.stream().filter(pr -> pr.getPromotionId().equals(promotionId) && DateUtil.convertDateToString(pr.getDate(), "yyyy-MM-dd").equals(sumYesterdayTimeStr)).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(yesterDayPromotion)) {
                            groupUnusedStockFlag.put(promotionId, yesterDayPromotion.stream().filter(item->item.getUnusedStock()!=null).mapToInt(AmzPromotionFeeStockRecord::getUnusedStock).sum());
                        }
                    }
                }

                //每个活动可消耗
                HashMap<Long, Integer> noOpStock = new HashMap<>();
                Map<Long, List<AmzPromotionFeeDateHisRecord>> mapInfoAll = infoAll.stream().collect(Collectors.groupingBy(AmzPromotionFeeDateHisRecord::getPromotionId));
                for (Long pid : mapInfoAll.keySet()) {
                    List<AmzPromotionFeeDateHisRecord> amzPromotionFeeDateHisRecords = mapInfoAll.get(pid);
                    AmzPromotionFeeDateHisRecord itemPromotionInfo = amzPromotionFeeDateHisRecords.get(0);
                    Integer skcStock = itemPromotionInfo.getSkcStock() == null ? 0 : itemPromotionInfo.getSkcStock(); //提报
                    Integer remainStock = itemPromotionInfo.getRemainStock() == null ? 0 : itemPromotionInfo.getRemainStock(); //剩余
                    Integer yesterDay = yesterDayStockMap.get(pid) == null ? 0 : yesterDayStockMap.get(pid);//昨日提报-昨日剩余
                    Integer unUsedStock = groupUnusedStockFlag.get(pid) == null ? 0 : groupUnusedStockFlag.get(pid); //昨日未消耗
                    noOpStock.put(pid, skcStock - remainStock - yesterDay + unUsedStock); //（提报-剩余）-（昨日提报-昨日剩余） +昨日未消耗
                }



                //活动申报价升序
                infoAll = infoAll.stream().sorted(Comparator.comparing(AmzPromotionFeeDateHisRecord::getActivityPrice)).collect(Collectors.toList());

                //查订单
                List<SaleOrderItemPromotionPriceVO> orderItemsVo = saleOrdersMapper.selectOrderItemBySellerSku( infoAll.stream().map(AmzPromotionFeeDateHisRecord::getSellerSku).distinct().collect(Collectors.toList()), pstqueryFrom, pstqueryTo);
                orderItemsVo = orderItemsVo.stream().filter(item -> !StringUtils.isEmpty(item.getSellerSku())).collect(Collectors.toList());


                //活动,selelr->计算数量
                HashMap<Long, HashMap<String, Integer>> prSum = new HashMap<>();

                //各活动消耗
                HashMap<Long, Integer> prSumTotla = new HashMap<>();

                for (AmzPromotionFeeDateHisRecord itemHis : infoAll) {  //活动 - selelrsku
                    Long promotionId = itemHis.getPromotionId(); //活动ID
                    Integer initFlagStock = noOpStock.get(promotionId); //可消耗指整个活动下
                    if (initFlagStock==null||initFlagStock <= 0) {
                        continue;
                    }
                    //sellerSKu折扣
                    BigDecimal promotionPrice = itemHis.getDailyPrice().subtract(itemHis.getActivityPrice()).divide(new BigDecimal(100));//（日常-活动）*100 = 折扣
                    String sellerSku = itemHis.getSellerSku(); //sellerSKu
                    List<SaleOrderItemPromotionPriceVO> sellerSkuOrderInfo = orderItemsVo.stream().filter(item -> item.getSellerSku().equals(sellerSku) && item.getPromotionDiscountPrice() == null).collect(Collectors.toList());
                    for (SaleOrderItemPromotionPriceVO orderItems : sellerSkuOrderInfo) {
                        Integer sumTotalFlag = prSumTotla.get(promotionId)==null?0:prSumTotla.get(promotionId);
                        if (orderItems.getQuantity() == null || orderItems.getQuantity() == 0) {
                            log.info("促销销售数量异常：{}",JSONObject.toJSONString(orderItems));
                            continue;
                        }

                        BigDecimal orderQuantity = new BigDecimal(orderItems.getQuantity()); //行销量


                        if (promotionPrice.compareTo(BigDecimal.ZERO)<0||promotionPrice.compareTo(orderItems.getItemPrice())>0){
                            continue;
                        }

                        //本次计算后是否大于本次可消耗
                        boolean gtFlag= (sumTotalFlag + orderQuantity.intValue()) > initFlagStock;
                        if (gtFlag) {
                            Integer residueNum = initFlagStock-sumTotalFlag; //当天可消耗-已消耗 若等于0，结束循环
                            if ((initFlagStock-sumTotalFlag)==0) { //可消耗-已消耗 结束
                                break;
                            }
                            //本次实际计算数量
                            orderQuantity =  new BigDecimal(residueNum);
                        }

                        //原始金额若无值才设置
                        if (orderItems.getOriItemAmount() == null || orderItems.getOriItemAmount().compareTo(new BigDecimal("0")) == 0) {
                            orderItems.setOriItemAmount(orderItems.getItemAmount()); //原始销售额
                        }else{
                            log.info("促销费计算未设置原始金额：{}", JSONObject.toJSONString(orderItems));
                        }
                        orderItems.setPromotionDiscountPrice(promotionPrice.multiply(orderQuantity)); //销量 * 折扣
                        orderItems.setItemAmount(orderItems.getItemAmount().subtract(orderItems.getPromotionDiscountPrice())); //计算促销后销售额->(原始销售额 - 促销费)


                        orderItems.setUsedQuantity(orderQuantity.intValue()); //日志使用
                        orderItems.setPromotionId(promotionId); //日志使用
                        orderItems.setDiscount(promotionPrice); //日志使用


                        //计算此活动已计算total
                        Integer sumTotalPr = prSumTotla.get(promotionId);
                        if (sumTotalPr == null) {
                            prSumTotla.put(promotionId, orderQuantity.intValue());
                        } else {
                            prSumTotla.put(promotionId, sumTotalPr + orderQuantity.intValue());
                        }

                        //汇总行数据
                        HashMap<String, Integer> stringIntegerHashMap = prSum.get(promotionId);
                        if (stringIntegerHashMap == null) {
                            HashMap<String, Integer> integerHashMap = new HashMap<>();
                            integerHashMap.put(sellerSku, orderQuantity.intValue());

                            prSum.put(promotionId, integerHashMap);
                        } else {
                            Integer sellerSkuStock = stringIntegerHashMap.get(sellerSku);
                            if (sellerSkuStock == null) {
                                stringIntegerHashMap.put(sellerSku,  orderQuantity.intValue());
                            } else {
                                stringIntegerHashMap.put(sellerSku, sellerSkuStock +  orderQuantity.intValue()); //累计库存
                            }
                        }
                    }
                }
                //看各个活动是否有未消耗，进行记录
                for (Long pid : noOpStock.keySet()) { //循环可消耗
                    Integer usableStock = noOpStock.get(pid); //需消耗
                    if (usableStock == 0) {
                        continue;
                    }
                    Integer usedStocket = prSumTotla.get(pid)==null?0:prSumTotla.get(pid); //本次已消耗
                    int  unUsedStockt= usableStock - usedStocket; //需消耗-已消耗=未消耗
                    if (usedStocket == 0 || unUsedStockt > 0) {  //记录未消耗日志  已消耗等于0，或者未消耗大于0
                        AmzPromotionFeeStockRecord amzPromotionFeeStockRecord = new AmzPromotionFeeStockRecord();
                        amzPromotionFeeStockRecord.setShopId(shopId);
                        amzPromotionFeeStockRecord.setSkcId(skc);
                        amzPromotionFeeStockRecord.setDate(nowDate);
                        amzPromotionFeeStockRecord.setPromotionId(pid);
                        amzPromotionFeeStockRecord.setUsedStock(0);//已消耗
                        amzPromotionFeeStockRecord.setUnusedStock(unUsedStockt); //未消耗
                        amzPromotionFeeStockRecord.setRemark("剩余未消耗");
                        amzPromotionFeeStockRecord.settingDefaultSystemCreate();
                        amzPromotionFeeStockRecordService.save(amzPromotionFeeStockRecord);
                    }
                }

                //多活动计算完毕，保存消耗日志
                if (prSum.size() > 0) {
                    for (Long pid : prSum.keySet()) {
                        HashMap<String, Integer> childList = prSum.get(pid);
                        for (String sellerSku : childList.keySet()) {
                            Integer stock = childList.get(sellerSku);
                            AmzPromotionFeeStockRecord amzPromotionFeeStockRecord = new AmzPromotionFeeStockRecord();
                            amzPromotionFeeStockRecord.setShopId(shopId);
                            amzPromotionFeeStockRecord.setSkcId(skc);
                            amzPromotionFeeStockRecord.setDate(nowDate);
                            amzPromotionFeeStockRecord.setPromotionId(pid);
                            amzPromotionFeeStockRecord.setSellerSku(sellerSku);
                            amzPromotionFeeStockRecord.setUsedStock(stock);
                            amzPromotionFeeStockRecord.settingDefaultSystemCreate();
                            amzPromotionFeeStockRecordService.save(amzPromotionFeeStockRecord);

                        }
                    }
                }
                //更新订单
                List<SaleOrderItemPromotionPriceVO> collect = orderItemsVo.stream().
                        filter(item -> item.getPromotionDiscountPrice() != null&&item.getPromotionDiscountPrice().compareTo(new BigDecimal("0"))>0).collect(Collectors.toList());
                for (SaleOrderItemPromotionPriceVO saleOrderItemPromotionPriceVO : collect) {
                    saleOrdersMapper.udpateOrderItemsPromotionDiscountPrice(saleOrderItemPromotionPriceVO.getPromotionDiscountPrice(),
                            saleOrderItemPromotionPriceVO.getItemAmount(), saleOrderItemPromotionPriceVO.getOriItemAmount(),
                            saleOrderItemPromotionPriceVO.getId(),"V1");


                    MarPromotionFeeStockLog pOrderLog = new MarPromotionFeeStockLog();
                    pOrderLog.setUsedDate(nowDate);
                    pOrderLog.setShopId(shopId);
                    pOrderLog.setPromotionId(saleOrderItemPromotionPriceVO.getPromotionId());
                    pOrderLog.setSkcId(skc);
                    pOrderLog.setSellerSku(saleOrderItemPromotionPriceVO.getSellerSku());
                    pOrderLog.setOrderItemId(saleOrderItemPromotionPriceVO.getId());
                    pOrderLog.setUsedStock(saleOrderItemPromotionPriceVO.getUsedQuantity());
                    pOrderLog.setDiscount(saleOrderItemPromotionPriceVO.getDiscount());
                    pOrderLog.setOriItemAmount(saleOrderItemPromotionPriceVO.getOriItemAmount()); //设置原始值
                    pOrderLog.settingDefaultSystemCreate();
                    marPromotionFeeStockLogMapper.insert(pOrderLog);
                }
            }


        }
    }



    /**
     * @param
     * @description: 指定日期后进行回算（即回算某日前对应数据）
     * @author: Moore
     * @date: 2024/10/25 10:28
     * @return: void
     **/
    public void promotionTemuDayPriceV3(List<TemuPromotionsPriceVO> promotionsList, Date beginTimeFlag) {
        for (TemuPromotionsPriceVO temuPromotionsPriceVO : promotionsList) {
            Long pid = temuPromotionsPriceVO.getId();
            Long shopId = temuPromotionsPriceVO.getShopId();
            List<AmzPromotionsSkuDetail> skuDetailList = temuPromotionsPriceVO.getSkuDetailList();
            if (CollectionUtils.isEmpty(skuDetailList)) {
                continue;
            }
            String skc = skuDetailList.get(0).getParentAsin();


            //传入的值，不能大于库中的结束时间
            if (cn.hutool.core.date.DateUtil.compare(beginTimeFlag, temuPromotionsPriceVO.getEndTime()) > 0) {
                log.info("传入时间大于结束时间：{}", JSONObject.toJSONString(temuPromotionsPriceVO));
                continue;
            }

            //计算实际开始时间
            Date sumTime = null;
            int compareResult = cn.hutool.core.date.DateUtil.compare(beginTimeFlag, temuPromotionsPriceVO.getBeginTime());
            if (compareResult < 0) { //传入小于库 ，使用库中
                sumTime = temuPromotionsPriceVO.getBeginTime();
            } else if (compareResult > 0) { //传入大于库中，使用
                sumTime = beginTimeFlag;
            } else {
                sumTime = beginTimeFlag;
            }

            //获取该skc计算的第一条数据是否存在未结算
            AmzPromotionFeeStockRecord hisStockSumInfo = this.baseMapper.selectPromotionHisMinDate(temuPromotionsPriceVO.getId());
            if (hisStockSumInfo == null || hisStockSumInfo.getUnusedStock().equals(0)) { //空代表 ：无对应历史记录，或历史记录消耗库存为0     /无剩余可消耗
                continue;
            }

            //开始时间需小于库中已计算时间    （开始时间大于计算时间或等于计算时间不进行计算）
            if (cn.hutool.core.date.DateUtil.compare(sumTime, hisStockSumInfo.getDate()) > 0 || cn.hutool.core.date.DateUtil.compare(cn.hutool.core.date.DateUtil.beginOfDay(sumTime), cn.hutool.core.date.DateUtil.beginOfDay(hisStockSumInfo.getDate())) == 0) {
                continue;
            }

            //sellerSku 对应促销费封装
            HashMap<String, BigDecimal> selllerSkuDiscountPrice = new HashMap<>();
            skuDetailList.forEach(
                    item -> {
                        selllerSkuDiscountPrice.put(item.getSellerSku(), item.getDiscount()); //已计算完成促销费
                    }
            );

            String queryFrom = cn.hutool.core.date.DateUtil.format(sumTime, DatePattern.NORM_DATETIME_PATTERN);
            String queryTo = cn.hutool.core.date.DateUtil.format(cn.hutool.core.date.DateUtil.endOfDay(cn.hutool.core.date.DateUtil.offsetDay(hisStockSumInfo.getDate(), -1)), DatePattern.NORM_DATETIME_PATTERN);

            log.info("实际计算时间：{},促销数据：{}", queryFrom + queryTo, JSONObject.toJSONString(temuPromotionsPriceVO));
            HashMap<String, BigDecimal> selllerTotalFlagMap = new HashMap<>();
            BigDecimal initStockFlag = new BigDecimal(hisStockSumInfo.getUnusedStock()); //今日可消耗
            AtomicReference<BigDecimal> sumTotal = new AtomicReference<>(new BigDecimal("0")); //已计总Total


            //获取SellerSKu订单
            List<SaleOrderItemPromotionPriceVO> saleOrderItemPromotionPriceVOS = saleOrdersMapper.selectOrderItemBySellerSku(skuDetailList.stream().map(AmzPromotionsSkuDetail::getSellerSku).collect(Collectors.toList()), queryFrom, queryTo);
            saleOrderItemPromotionPriceVOS = saleOrderItemPromotionPriceVOS.stream().filter(item -> !StringUtils.isEmpty(item.getSellerSku())).collect(Collectors.toList());
            for (SaleOrderItemPromotionPriceVO saleOrderPromotionPriceItem : saleOrderItemPromotionPriceVOS) {

                if (saleOrderPromotionPriceItem.getQuantity() == null || saleOrderPromotionPriceItem.getQuantity() == 0) {
                    continue;
                }

                //单行总数量
                BigDecimal orderQuantity = new BigDecimal(saleOrderPromotionPriceItem.getQuantity()); //行销量

                //对应单促销费
                BigDecimal promotionPrice = selllerSkuDiscountPrice.get(saleOrderPromotionPriceItem.getSellerSku());

                //1. 折扣为负数 2.折扣大于订单金额不进行计算
                if (promotionPrice.compareTo(BigDecimal.ZERO) < 0 || promotionPrice.compareTo(saleOrderPromotionPriceItem.getItemPrice()) > 0) {
                    continue;
                }

                //本次计算后是否大于本次可消耗
                boolean gtFlag = sumTotal.get().add(orderQuantity).compareTo(initStockFlag) > 0;
                if (gtFlag) {
                    BigDecimal residueNum = initStockFlag.subtract(sumTotal.get()); //当天可消耗-已消耗 若等于0，结束循环
                    if (residueNum.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                    //本次实际计算数量
                    orderQuantity = residueNum;
                }
                saleOrderPromotionPriceItem.setPromotionDiscountPrice(promotionPrice.multiply(orderQuantity)); //销量 * 折扣
                saleOrderPromotionPriceItem.setOriItemAmount(saleOrderPromotionPriceItem.getItemAmount()); //原始销售额
                saleOrderPromotionPriceItem.setItemAmount(saleOrderPromotionPriceItem.getItemAmount().subtract(saleOrderPromotionPriceItem.getPromotionDiscountPrice())); //计算促销后销售额->(原始销售额 - 促销费)
                BigDecimal sellerSkuBig = selllerTotalFlagMap.get(saleOrderPromotionPriceItem.getSellerSku()) == null ? BigDecimal.ZERO : selllerTotalFlagMap.get(saleOrderPromotionPriceItem.getSellerSku());
                selllerTotalFlagMap.put(saleOrderPromotionPriceItem.getSellerSku(), sellerSkuBig.add(orderQuantity));
                sumTotal.set(sumTotal.get().add(orderQuantity));  //标记为了防止超出消耗范围
                saleOrderPromotionPriceItem.setUsedQuantity(orderQuantity.intValue()); //日志使用  使用使用
                saleOrderPromotionPriceItem.setPromotionId(pid); //日志使用  活动ID
                saleOrderPromotionPriceItem.setDiscount(promotionPrice); //日志使用 折扣
            }


            //更新订单
            List<SaleOrderItemPromotionPriceVO> saleOrderUpatePrice = saleOrderItemPromotionPriceVOS.stream().filter(item -> item.getPromotionDiscountPrice() != null).collect(Collectors.toList());
            for (SaleOrderItemPromotionPriceVO saleOrderItemPromotionPriceVO : saleOrderUpatePrice) {
                saleOrdersMapper.udpateOrderItemsPromotionDiscountPrice(saleOrderItemPromotionPriceVO.getPromotionDiscountPrice(),
                        saleOrderItemPromotionPriceVO.getItemAmount(), saleOrderItemPromotionPriceVO.getOriItemAmount(),
                        saleOrderItemPromotionPriceVO.getId(), "V1_manual");

                //保存计算日志
                MarPromotionFeeStockLog pOrderLog = new MarPromotionFeeStockLog();
//                    pOrderLog.setUsedDate(nowDate);
                pOrderLog.setShopId(shopId);
                pOrderLog.setPromotionId(saleOrderItemPromotionPriceVO.getPromotionId());
                pOrderLog.setSkcId(skc);
                pOrderLog.setSellerSku(saleOrderItemPromotionPriceVO.getSellerSku());
                pOrderLog.setOrderItemId(saleOrderItemPromotionPriceVO.getId());
                pOrderLog.setUsedStock(saleOrderItemPromotionPriceVO.getUsedQuantity());
                pOrderLog.setDiscount(saleOrderItemPromotionPriceVO.getDiscount());
                pOrderLog.setRemark("手动回算");
                pOrderLog.settingDefaultSystemCreate();
                marPromotionFeeStockLogMapper.insert(pOrderLog);
            }
        }
    }





    /**
     * @param
     * @description: 指定日期回算，并指定回算未消耗天数
     * @author: Moore
     * @date: 2024/10/25 10:28
     * @return: void
     **/
    public void promotionTemuDayPriceV4(List<TemuPromotionsPriceVO> promotionsList, Date beginTimeFlag) {
        for (TemuPromotionsPriceVO temuPromotionsPriceVO : promotionsList) {
            Long pid = temuPromotionsPriceVO.getId();
            Long shopId = temuPromotionsPriceVO.getShopId();
            List<AmzPromotionsSkuDetail> skuDetailList = temuPromotionsPriceVO.getSkuDetailList();
            if (CollectionUtils.isEmpty(skuDetailList)) {
                continue;
            }
            String skc = skuDetailList.get(0).getParentAsin();


            //传入的值，不能大于库中的结束时间
            if (cn.hutool.core.date.DateUtil.compare(beginTimeFlag, temuPromotionsPriceVO.getEndTime()) > 0) {
                log.info("传入时间大于结束时间：{}", JSONObject.toJSONString(temuPromotionsPriceVO));
                continue;
            }

            //计算实际开始时间
            Date sumTime = null;
            int compareResult = cn.hutool.core.date.DateUtil.compare(beginTimeFlag, temuPromotionsPriceVO.getBeginTime());
            if (compareResult < 0) { //传入小于库中 开始时间
               continue;  //不进行计算
            } else if (compareResult > 0) { //传入大于库中，使用
                sumTime = beginTimeFlag;
            } else {
                sumTime = beginTimeFlag;
            }

            //获取该计算日 后一日剩余销量
            DateTime   tomorrowDay= cn.hutool.core.date.DateUtil.offsetDay(sumTime, 1);
            AmzPromotionFeeStockRecord hisStockSumInfo = this.baseMapper.selectPromotionHisDate(temuPromotionsPriceVO.getId(), cn.hutool.core.date.DateUtil.beginOfDay(tomorrowDay), cn.hutool.core.date.DateUtil.endOfDay(tomorrowDay));
            if (hisStockSumInfo == null || hisStockSumInfo.getUnusedStock().equals(0)) { //空代表 ：无对应历史记录，或历史记录消耗库存为0     /无剩余可消耗
                continue;
            }

            //开始时间需小于库中已计算时间    （开始时间大于计算时间或等于计算时间不进行计算）
            if (cn.hutool.core.date.DateUtil.compare(sumTime, hisStockSumInfo.getDate()) > 0 || cn.hutool.core.date.DateUtil.compare(cn.hutool.core.date.DateUtil.beginOfDay(sumTime), cn.hutool.core.date.DateUtil.beginOfDay(hisStockSumInfo.getDate())) == 0) {
                continue;
            }

            //判断此促销是否当天已经计算
            LambdaQueryWrapper<AmzPromotionFeeStockRecord> queryWrapper = new LambdaQueryWrapper<AmzPromotionFeeStockRecord>();
            queryWrapper.eq(AmzPromotionFeeStockRecord::getPromotionId, temuPromotionsPriceVO.getId());
            queryWrapper.ge(AmzPromotionFeeStockRecord::getDate, cn.hutool.core.date.DateUtil.beginOfDay(sumTime));
            queryWrapper.le(AmzPromotionFeeStockRecord::getDate, cn.hutool.core.date.DateUtil.endOfDay(sumTime));
            List<AmzPromotionFeeStockRecord> promotionFeeStockRecords = this.baseMapper.selectList(queryWrapper);
            if (!CollectionUtils.isEmpty(promotionFeeStockRecords)) {
                log.info("当天temu促销费已计算：{}", JSONObject.toJSONString(promotionFeeStockRecords));
                continue;
            }


            //sellerSku 对应促销费封装
            HashMap<String, BigDecimal> selllerSkuDiscountPrice = new HashMap<>();
            skuDetailList.forEach(
                    item -> {
                        selllerSkuDiscountPrice.put(item.getSellerSku(), item.getDiscount()); //已计算完成促销费
                    }
            );

            //计算的天数
            String queryFrom = cn.hutool.core.date.DateUtil.format(sumTime, DatePattern.NORM_DATETIME_PATTERN);
            String queryTo = cn.hutool.core.date.DateUtil.format(cn.hutool.core.date.DateUtil.endOfDay(sumTime), DatePattern.NORM_DATETIME_PATTERN);

            log.info("实际计算时间：{},促销数据：{}", queryFrom + queryTo, JSONObject.toJSONString(temuPromotionsPriceVO));
            HashMap<String, BigDecimal> selllerTotalFlagMap = new HashMap<>();
            BigDecimal initStockFlag = new BigDecimal(hisStockSumInfo.getUnusedStock()); //今日可消耗
            AtomicReference<BigDecimal> sumTotal = new AtomicReference<>(new BigDecimal("0")); //已计总Total

            //活动,selelr->计算数量
            HashMap<Long, HashMap<String, Integer>> prSum = new HashMap<>();

            //获取SellerSKu订单
            List<SaleOrderItemPromotionPriceVO> saleOrderItemPromotionPriceVOS = saleOrdersMapper.selectOrderItemBySellerSku(skuDetailList.stream().map(AmzPromotionsSkuDetail::getSellerSku).collect(Collectors.toList()), queryFrom, queryTo);
            saleOrderItemPromotionPriceVOS = saleOrderItemPromotionPriceVOS.stream().filter(item -> !StringUtils.isEmpty(item.getSellerSku())).collect(Collectors.toList());
            for (SaleOrderItemPromotionPriceVO saleOrderPromotionPriceItem : saleOrderItemPromotionPriceVOS) {


                if (saleOrderPromotionPriceItem.getQuantity() == null || saleOrderPromotionPriceItem.getQuantity() == 0) {
                    continue;
                }

                //单行总数量
                BigDecimal orderQuantity = new BigDecimal(saleOrderPromotionPriceItem.getQuantity()); //行销量

                //对应单促销费
                BigDecimal promotionPrice = selllerSkuDiscountPrice.get(saleOrderPromotionPriceItem.getSellerSku());

                //1. 折扣为负数 2.折扣大于订单金额不进行计算
                if (promotionPrice.compareTo(BigDecimal.ZERO) < 0 || promotionPrice.compareTo(saleOrderPromotionPriceItem.getItemPrice()) > 0) {
                    continue;
                }

                //本次计算后是否大于本次可消耗
                boolean gtFlag = sumTotal.get().add(orderQuantity).compareTo(initStockFlag) > 0;
                if (gtFlag) {
                    BigDecimal residueNum = initStockFlag.subtract(sumTotal.get()); //当天可消耗-已消耗 若等于0，结束循环
                    if (residueNum.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                    //本次实际计算数量
                    orderQuantity = residueNum;
                }
                saleOrderPromotionPriceItem.setPromotionDiscountPrice(promotionPrice.multiply(orderQuantity)); //销量 * 折扣
                saleOrderPromotionPriceItem.setOriItemAmount(saleOrderPromotionPriceItem.getItemAmount()); //原始销售额
                saleOrderPromotionPriceItem.setItemAmount(saleOrderPromotionPriceItem.getItemAmount().subtract(saleOrderPromotionPriceItem.getPromotionDiscountPrice())); //计算促销后销售额->(原始销售额 - 促销费)
                BigDecimal sellerSkuBig = selllerTotalFlagMap.get(saleOrderPromotionPriceItem.getSellerSku()) == null ? BigDecimal.ZERO : selllerTotalFlagMap.get(saleOrderPromotionPriceItem.getSellerSku());
                selllerTotalFlagMap.put(saleOrderPromotionPriceItem.getSellerSku(), sellerSkuBig.add(orderQuantity));
                sumTotal.set(sumTotal.get().add(orderQuantity));  //标记为了防止超出消耗范围
                saleOrderPromotionPriceItem.setUsedQuantity(orderQuantity.intValue()); //日志使用  使用使用
                saleOrderPromotionPriceItem.setPromotionId(pid); //日志使用  活动ID
                saleOrderPromotionPriceItem.setDiscount(promotionPrice); //日志使用 折扣


                //汇总行数据
                HashMap<String, Integer> stringIntegerHashMap = prSum.get(pid);
                if (stringIntegerHashMap == null) {
                    HashMap<String, Integer> integerHashMap = new HashMap<>();
                    integerHashMap.put(saleOrderPromotionPriceItem.getSellerSku(), orderQuantity.intValue());

                    prSum.put(pid, integerHashMap);
                } else {
                    Integer sellerSkuStock = stringIntegerHashMap.get(saleOrderPromotionPriceItem.getSellerSku());
                    if (sellerSkuStock == null) {
                        stringIntegerHashMap.put(saleOrderPromotionPriceItem.getSellerSku(),  orderQuantity.intValue());
                    } else {
                        stringIntegerHashMap.put(saleOrderPromotionPriceItem.getSellerSku(), sellerSkuStock +  orderQuantity.intValue()); //累计库存
                    }
                }

            }




            //多活动计算完毕，保存消耗日志
            if (prSum.size() > 0) {

                Integer flag = 0;
                for (Long pidFlag : prSum.keySet()) {
                    HashMap<String, Integer> childList = prSum.get(pidFlag);
                    for (String sellerSku : childList.keySet()) {
                        Integer stock = childList.get(sellerSku);
                        AmzPromotionFeeStockRecord amzPromotionFeeStockRecord = new AmzPromotionFeeStockRecord();
                        amzPromotionFeeStockRecord.setShopId(shopId);
                        amzPromotionFeeStockRecord.setSkcId(skc);
                        amzPromotionFeeStockRecord.setDate(cn.hutool.core.date.DateUtil.beginOfDay(sumTime));
                        amzPromotionFeeStockRecord.setPromotionId(pid);
                        amzPromotionFeeStockRecord.setSellerSku(sellerSku);
                        amzPromotionFeeStockRecord.setUsedStock(stock);
                        amzPromotionFeeStockRecord.setRemark("手动计算单日");
                        amzPromotionFeeStockRecord.settingDefaultSystemCreate();
                        amzPromotionFeeStockRecordService.save(amzPromotionFeeStockRecord);
                        flag += stock;
                    }
                }
                //减去已消耗剩余库存
                if (flag > 0&&hisStockSumInfo.getId() !=null) {
                    AmzPromotionFeeStockRecord amzPromotionFeeStockRecord = new AmzPromotionFeeStockRecord();
                    amzPromotionFeeStockRecord.setId(hisStockSumInfo.getId());
                    amzPromotionFeeStockRecord.setUnusedStock(hisStockSumInfo.getUnusedStock() - flag);
                    amzPromotionFeeStockRecord.setRemark("剩余未消耗,回算冲销" + flag);
                    amzPromotionFeeStockRecord.settingDefaultUpdate();
                    this.baseMapper.updateById(amzPromotionFeeStockRecord);
                }
            }


            //更新订单
            List<SaleOrderItemPromotionPriceVO> saleOrderUpatePrice = saleOrderItemPromotionPriceVOS.stream().filter(item -> item.getPromotionDiscountPrice() != null).collect(Collectors.toList());
            for (SaleOrderItemPromotionPriceVO saleOrderItemPromotionPriceVO : saleOrderUpatePrice) {
                saleOrdersMapper.udpateOrderItemsPromotionDiscountPrice(saleOrderItemPromotionPriceVO.getPromotionDiscountPrice(),
                        saleOrderItemPromotionPriceVO.getItemAmount(), saleOrderItemPromotionPriceVO.getOriItemAmount(),
                        saleOrderItemPromotionPriceVO.getId(), "V1_manual");

                //保存计算日志
                MarPromotionFeeStockLog pOrderLog = new MarPromotionFeeStockLog();
//                    pOrderLog.setUsedDate(nowDate);
                pOrderLog.setShopId(shopId);
                pOrderLog.setPromotionId(saleOrderItemPromotionPriceVO.getPromotionId());
                pOrderLog.setSkcId(skc);
                pOrderLog.setSellerSku(saleOrderItemPromotionPriceVO.getSellerSku());
                pOrderLog.setOrderItemId(saleOrderItemPromotionPriceVO.getId());
                pOrderLog.setUsedStock(saleOrderItemPromotionPriceVO.getUsedQuantity());
                pOrderLog.setDiscount(saleOrderItemPromotionPriceVO.getDiscount());
                pOrderLog.setRemark("手动回算-单日");
                pOrderLog.settingDefaultSystemCreate();
                marPromotionFeeStockLogMapper.insert(pOrderLog);
            }
        }
    }



    /**
     * @param
     * @description: 回算指定新促销类型，11月份数据
     * @author: Moore
     * @date: 2024/10/25 10:28
     * @return: void
     **/
    public void promotionTemuDayPriceV5(List<TemuPromotionsPriceVO> promotionsList, DateTime nowPstTime,Date sumEndTime,String remarkFlag) {
        for (TemuPromotionsPriceVO temuPromotionsPriceVO : promotionsList) {
            Long pid = temuPromotionsPriceVO.getId();
            Long shopId = temuPromotionsPriceVO.getShopId();
            List<AmzPromotionsSkuDetail> skuDetailList = temuPromotionsPriceVO.getSkuDetailList();
            if (CollectionUtils.isEmpty(skuDetailList)) {
                continue;
            }
            Integer unusedStock = temuPromotionsPriceVO.getUnusedStock();
            if (unusedStock.equals(0)) {
                log.info("此促销可计算库存为0：{}", pid);
                continue;
            }

            String skc = skuDetailList.get(0).getParentAsin(); //SKC
            Date beginTime = temuPromotionsPriceVO.getBeginTime(); //当前促销活动开始

            //开始时间大于当前pst 不进行计算 (活动未开始)
            if (cn.hutool.core.date.DateUtil.compare(beginTime, nowPstTime) > 0) {
                log.info("开始时间大于当前Pst时间：{}",JSONObject.toJSONString(temuPromotionsPriceVO));
                continue;
            }

            //开始时间小于11月1号。那真实计算开始时间为11月1
            Date nosPstDate = cn.hutool.core.date.DateUtil.parse("2024-11-01 00:00:00", "yyyy-MM-dd HH:mm:ss");


            int compareResult = cn.hutool.core.date.DateUtil.compare(beginTime,nosPstDate);

            //计算实际开始时间
            Date sumTime = null;
            if (compareResult < 0) { //开始时间小于11月 01
                sumTime = nosPstDate;
            } else if (compareResult > 0) { //开始大于11月01
                sumTime = beginTime;
            } else {
                sumTime = beginTime;
            }


            //sellerSku 对应促销费封装
            HashMap<String, BigDecimal> selllerSkuDiscountPrice = new HashMap<>();
            skuDetailList.forEach(
                    item -> {
                        selllerSkuDiscountPrice.put(item.getSellerSku(), item.getDiscount()); //已计算完成促销费
                    }
            );

            String queryFrom = cn.hutool.core.date.DateUtil.format(sumTime, DatePattern.NORM_DATETIME_PATTERN);
            String queryTo = cn.hutool.core.date.DateUtil.format(sumEndTime != null ? sumEndTime : temuPromotionsPriceVO.getEndTime(), DatePattern.NORM_DATETIME_PATTERN);

            log.info("实际计算时间：{},促销数据：{}", queryFrom + queryTo, JSONObject.toJSONString(temuPromotionsPriceVO));
            HashMap<String, BigDecimal> selllerTotalFlagMap = new HashMap<>();
            BigDecimal initStockFlag = new BigDecimal(unusedStock); //总可消耗
            AtomicReference<BigDecimal> sumTotal = new AtomicReference<>(new BigDecimal("0")); //已计总Total

            if(CollectionUtils.isEmpty(skuDetailList.stream().map(AmzPromotionsSkuDetail::getSellerSku).collect(Collectors.toList()))){
                log.info("无可用sellerSku：{}", JSONObject.toJSONString(temuPromotionsPriceVO));
            }
            //获取SellerSKu订单
            List<SaleOrderItemPromotionPriceVO> saleOrderItemPromotionPriceVOS = saleOrdersMapper.selectOrderItemBySellerSku(skuDetailList.stream().map(AmzPromotionsSkuDetail::getSellerSku).collect(Collectors.toList()), queryFrom, queryTo);
            saleOrderItemPromotionPriceVOS = saleOrderItemPromotionPriceVOS.stream().filter(item -> !StringUtils.isEmpty(item.getSellerSku())).collect(Collectors.toList());
            for (SaleOrderItemPromotionPriceVO saleOrderPromotionPriceItem : saleOrderItemPromotionPriceVOS) {

                if (saleOrderPromotionPriceItem.getQuantity() == null || saleOrderPromotionPriceItem.getQuantity() == 0) {
                    continue;
                }

                //单行总数量
                BigDecimal orderQuantity = new BigDecimal(saleOrderPromotionPriceItem.getQuantity()); //行销量

                //对应单促销费
                BigDecimal promotionPrice = selllerSkuDiscountPrice.get(saleOrderPromotionPriceItem.getSellerSku());

                //1. 折扣为负数 2.折扣大于订单金额不进行计算
                if (promotionPrice.compareTo(BigDecimal.ZERO) < 0 || promotionPrice.compareTo(saleOrderPromotionPriceItem.getItemPrice()) > 0) {
                    continue;
                }

                //本次计算后是否大于本次可消耗
                boolean gtFlag = sumTotal.get().add(orderQuantity).compareTo(initStockFlag) > 0;
                if (gtFlag) {
                    BigDecimal residueNum = initStockFlag.subtract(sumTotal.get()); //当天可消耗-已消耗 若等于0，结束循环
                    if (residueNum.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                    //本次实际计算数量
                    orderQuantity = residueNum;
                }
                saleOrderPromotionPriceItem.setPromotionDiscountPrice(promotionPrice.multiply(orderQuantity)); //销量 * 折扣
                saleOrderPromotionPriceItem.setOriItemAmount(saleOrderPromotionPriceItem.getItemAmount()); //原始销售额
                saleOrderPromotionPriceItem.setItemAmount(saleOrderPromotionPriceItem.getItemAmount().subtract(saleOrderPromotionPriceItem.getPromotionDiscountPrice())); //计算促销后销售额->(原始销售额 - 促销费)
                BigDecimal sellerSkuBig = selllerTotalFlagMap.get(saleOrderPromotionPriceItem.getSellerSku()) == null ? BigDecimal.ZERO : selllerTotalFlagMap.get(saleOrderPromotionPriceItem.getSellerSku());
                selllerTotalFlagMap.put(saleOrderPromotionPriceItem.getSellerSku(), sellerSkuBig.add(orderQuantity));
                sumTotal.set(sumTotal.get().add(orderQuantity));  //标记为了防止超出消耗范围
                saleOrderPromotionPriceItem.setUsedQuantity(orderQuantity.intValue()); //日志使用  使用使用
                saleOrderPromotionPriceItem.setPromotionId(pid); //日志使用  活动ID
                saleOrderPromotionPriceItem.setDiscount(promotionPrice); //日志使用 折扣
            }


            //更新订单
            List<SaleOrderItemPromotionPriceVO> saleOrderUpatePrice = saleOrderItemPromotionPriceVOS.stream().filter(item -> item.getPromotionDiscountPrice() != null).collect(Collectors.toList());
            for (SaleOrderItemPromotionPriceVO saleOrderItemPromotionPriceVO : saleOrderUpatePrice) {
                saleOrdersMapper.udpateOrderItemsPromotionDiscountPrice(saleOrderItemPromotionPriceVO.getPromotionDiscountPrice(),
                        saleOrderItemPromotionPriceVO.getItemAmount(), saleOrderItemPromotionPriceVO.getOriItemAmount(),
                        saleOrderItemPromotionPriceVO.getId(), StringUtils.isEmpty(remarkFlag) ? "V2_session" : remarkFlag);

                //保存计算日志
                MarPromotionFeeStockLog pOrderLog = new MarPromotionFeeStockLog();
//                    pOrderLog.setUsedDate(nowDate);
                pOrderLog.setShopId(shopId);
                pOrderLog.setPromotionId(saleOrderItemPromotionPriceVO.getPromotionId());
                pOrderLog.setSkcId(skc);
                pOrderLog.setSellerSku(saleOrderItemPromotionPriceVO.getSellerSku());
                pOrderLog.setOrderItemId(saleOrderItemPromotionPriceVO.getId());
                pOrderLog.setUsedStock(saleOrderItemPromotionPriceVO.getUsedQuantity());
                pOrderLog.setDiscount(saleOrderItemPromotionPriceVO.getDiscount());
                pOrderLog.setRemark(StringUtils.isEmpty(remarkFlag) ? "手动回算-Session" : remarkFlag);
                pOrderLog.settingDefaultSystemCreate();
                marPromotionFeeStockLogMapper.insert(pOrderLog);
            }
        }
    }



    /**
     * @param
     * @param skcIdMapPromotionIdList 所有SKC  多活动数据  key-SKC， value-活动ID
     * @description: SKC多活动，促销费计算
     * @author: Moore
     * @date: 2024/9/3 10:51
     * @return: void
     **/
    public void promotionIdsRate(Map<String, List<AmzPromotions>> skcIdMapPromotionIdList,
                                 List<AmzPromotionsSkuDetail> promotionsSkuDetailList,
                                 Date nowDate) {

        //根据活动ID分组，获取所有
        Map<Long, List<AmzPromotionsSkuDetail>> nowPromtion = promotionsSkuDetailList.stream().collect(Collectors.groupingBy(AmzPromotionsSkuDetail::getPromotionsId));

        //对 SKC 下
        for (String skc : skcIdMapPromotionIdList.keySet()) {
            //获取所有活动
            List<AmzPromotions> amzPromotionsInfo = skcIdMapPromotionIdList.get(skc);

            //获取活动最大开始结束时间
            List<AmzPromotions> ascTimePromotion = amzPromotionsInfo.stream().sorted(Comparator.comparing(AmzPromotions::getBeginTime)).collect(Collectors.toList());
            ;
//            List<SpiltDateUtil.Range> rangeList = SpiltDateUtil.splitToDays(DateUtil.convertDateToString(ascTimePromotion.get(0).getBeginTime(), "yyyy-MM-dd"), DateUtil.convertDateToString(descTimePromotion.get(0).getEndTime(), "yyyy-MM-dd"));
            List<SpiltDateUtil.Range> rangeList = null;

            //时间-活动-活动下对应时间数据（对应活动下所有，当天）
            HashMap<String, HashMap<Long, List<AmzPromotionFeeDateHisRecord>>> timeMap = amzPromotionFeeDateHisRecordService.selectMaxDateSkcPrmotionInfo(skc, amzPromotionsInfo, nowPromtion, amzPromotionsInfo);

            //对该SKC下 所有活动天数进行计算
            for (SpiltDateUtil.Range range : rangeList) {  //升序
                String nowtime = DateUtil.convertDateToString(range.getStart(), "yyyy-MM-dd");
                String pstqueryFrom = cn.hutool.core.date.DateUtil.format(range.getStart(), DatePattern.NORM_DATETIME_PATTERN);
                String pstqueryTo = cn.hutool.core.date.DateUtil.format(cn.hutool.core.date.DateUtil.endOfDay(range.getStart()), DatePattern.NORM_DATETIME_PATTERN);


                HashMap<Long, List<AmzPromotionFeeDateHisRecord>> longListHashMap = timeMap.get(nowtime);
                if (longListHashMap.size() == 0) {
                    continue;   //无历史信息 不计算
                }


                //单天 仅有一个
                if (longListHashMap.size() == 1) {
                    List<AmzPromotionFeeDateHisRecord> amzPromotionFeeDateHisRecord = longListHashMap.values().stream().collect(Collectors.toList()).get(0); //活动信息
                    Long pid = longListHashMap.keySet().stream().collect(Collectors.toList()).get(0); //活动ID

                    //当前活动
                    Integer skcStock = amzPromotionFeeDateHisRecord.get(0).getSkcStock(); //提报库存
                    Integer remainStock = amzPromotionFeeDateHisRecord.get(0).getRemainStock();//剩余库存


                    List<String> sellerSkuList = nowPromtion.get(pid).stream().map(AmzPromotionsSkuDetail::getSellerSku).collect(Collectors.toList());//SKU对应sellerSKU

                    //sellerSku 对应促销费封装
                    HashMap<String, BigDecimal> selllerSkuDiscountPrice = new HashMap<>();
                    amzPromotionFeeDateHisRecord.forEach(
                            item -> {
                                BigDecimal discountPrice = item.getDailyPrice().subtract(item.getActivityPrice()).divide(new BigDecimal(100));
                                selllerSkuDiscountPrice.put(item.getSellerSku(), discountPrice);
                            }

                    );

                    //查询活动 SKC 消耗
                    List<AmzPromotionFeeStockRecord> promotionList = amzPromotionFeeStockRecordService.lambdaQuery()
                            .eq(AmzPromotionFeeStockRecord::getSkcId, skc)
                            .eq(AmzPromotionFeeStockRecord::getPromotionId, pid).list();

                    //已消耗
                    int usedStock = promotionList.stream().filter(item -> item.getUsedStock() != null).mapToInt(AmzPromotionFeeStockRecord::getUsedStock).sum(); //消耗总库存

                    //当天是否存在消耗
                    List<AmzPromotionFeeStockRecord> timePromotion = promotionList.stream().filter(item -> nowtime.equals(DateUtil.convertDateToString(item.getDate(), "yyyy-MM-dd"))).collect(Collectors.toList());


                    //存储各SellerSKuTotal
                    HashMap<String, BigDecimal> selllerSkuTotal = new HashMap<>();

                    BigDecimal initStockFlag = new BigDecimal(skcStock - remainStock - usedStock); //此活动当前天可消耗 （提报-剩余-已消耗）
                    AtomicReference<BigDecimal> sumTotal = new AtomicReference<>(new BigDecimal("0")); //已计算Total
                    Long currentId = null; //当前ID

                    //消耗为空,或当天消耗空，且可消耗大于0 且提报大于已消耗
                    if (CollectionUtils.isEmpty(promotionList) || (CollectionUtils.isEmpty(timePromotion) && initStockFlag.compareTo(BigDecimal.ZERO) > 0 && skcStock > usedStock)) { //提报大于已消耗
                        //获取SellerSKu订单
                        List<SaleOrderItemPromotionPriceVO> saleOrderItemPromotionPriceVOS = saleOrdersMapper.selectOrderItemBySellerSku(sellerSkuList, pstqueryFrom, pstqueryTo);
                        //TODO 确认SellerSku是否为null
                        for (SaleOrderItemPromotionPriceVO saleOrderPromotionPriceItem : saleOrderItemPromotionPriceVOS) {
                            //总计算total等于 可消耗，结束计算
                            if (sumTotal.get().compareTo(initStockFlag) == 0) {
                                break; //结束计算
                            }
                            //总计算total 大于已消耗，进行回退
                            if (sumTotal.get().compareTo(initStockFlag) > 0) {
                                BigDecimal subtract = sumTotal.get().subtract(initStockFlag); //多出数量
                                //获取行
                                SaleOrderItemPromotionPriceVO rollBackPrice = saleOrderItemPromotionPriceVOS.stream().filter(item -> item.getId().equals(saleOrderItemPromotionPriceVOS)).findFirst().orElse(null);
                                String sellerSku = rollBackPrice.getSellerSku();
                                BigDecimal promotionPrice = selllerSkuDiscountPrice.get(rollBackPrice.getSellerSku()); //回滚金额
                                rollBackPrice.setPromotionDiscountPrice((promotionPrice.multiply(subtract)));

                                //重置sellerSKu行 计数
                                BigDecimal bigDecimal = selllerSkuTotal.get(sellerSku);
                                selllerSkuTotal.put(sellerSku, bigDecimal.subtract(subtract));  //减去对应sellerSKu 多出数量
                                break; //结束循环
                            }

                            BigDecimal quantity = new BigDecimal(saleOrderPromotionPriceItem.getQuantity()); //行销量
                            BigDecimal promotionPrice = selllerSkuDiscountPrice.get(saleOrderPromotionPriceItem.getSellerSku()); //sellerSku
                            saleOrderPromotionPriceItem.setPromotionDiscountPrice(promotionPrice.multiply(quantity)); //销量 * 折扣

                            currentId = saleOrderPromotionPriceItem.getId(); //当前循环计算的ID
                            BigDecimal sellerSkuBig = selllerSkuTotal.get(saleOrderPromotionPriceItem.getSellerSku()) == null ? BigDecimal.ZERO : selllerSkuTotal.get(saleOrderPromotionPriceItem.getSellerSku());
                            selllerSkuTotal.put(saleOrderPromotionPriceItem.getSellerSku(), sellerSkuBig.add(quantity));
                            sumTotal.set(sumTotal.get().add(quantity));
                        }

                        //记录已消耗库存信息表
                        for (String sellerSku : selllerSkuTotal.keySet()) {
                            AmzPromotionFeeStockRecord amzPromotionFeeStockRecord = new AmzPromotionFeeStockRecord();
                            amzPromotionFeeStockRecord.setPromotionId(pid);
                            amzPromotionFeeStockRecord.setSellerSku(sellerSku);
                            amzPromotionFeeStockRecord.setUsedStock(selllerSkuTotal.get(sellerSku).intValue());
                            amzPromotionFeeStockRecord.settingDefaultSystemCreate();
                            amzPromotionFeeStockRecordService.save(amzPromotionFeeStockRecord);
                        }


                        //更新订单
                        List<SaleOrderItemPromotionPriceVO> saleOrderUpatePrice = saleOrderItemPromotionPriceVOS.stream().filter(item -> item.getPromotionDiscountPrice() != null).collect(Collectors.toList());
                        for (SaleOrderItemPromotionPriceVO saleOrderItemPromotionPriceVO : saleOrderUpatePrice) {
//                            saleOrdersMapper.udpateOrderItemsPromotionDiscountPrice(saleOrderItemPromotionPriceVO.getPromotionDiscountPrice(), saleOrderItemPromotionPriceVO.getId());
                        }
                    }
                }

                if (longListHashMap.size() > 1) {

                    //实际SKC 对应的活动
                    List<Long> pIds = amzPromotionsInfo.stream().map(AmzPromotions::getId).distinct().collect(Collectors.toList());


                    //多活动所有可计算活动
                    List<Long> promotionIds = longListHashMap.keySet().stream().collect(Collectors.toList());

                    //可计算活动明细行  活动 -sellersku
                    List<AmzPromotionFeeDateHisRecord> infoAll = new ArrayList<>();
                    longListHashMap.values().stream().collect(Collectors.toList()).stream().forEach(item -> infoAll.addAll(item));


                    //查询所有活动已消耗
                    List<AmzPromotionFeeStockRecord> promotionList = amzPromotionFeeStockRecordService.lambdaQuery()
                            .in(AmzPromotionFeeStockRecord::getPromotionId, pIds).list();


                    Map<Long, IntSummaryStatistics> groupUsedStockFlag = new HashMap<>();
                    //获取每个活动的总已消耗
                    if (!CollectionUtils.isEmpty(promotionList)) {
                        // 已消耗分组
                        groupUsedStockFlag = promotionList.stream().collect(Collectors.groupingBy(AmzPromotionFeeStockRecord::getPromotionId, Collectors.summarizingInt(AmzPromotionFeeStockRecord::getUsedStock)));

                        //当前循环天，已经计算过的活动行剔除
                        infoAll.removeIf(
                                item -> (promotionList.stream().filter(spent -> spent.getPromotionId().equals(item.getPromotionId()) && DateUtil.convertDateToString(spent.getDate(), "yyyy-MM-dd").equals(nowtime)).findFirst().orElse(null) != null)
                        );

                    }
                    Map<Long, IntSummaryStatistics> groupUsedStock = groupUsedStockFlag;


                    //计算每个活动当前循环天 可消耗库存
                    HashMap<Long, Integer> noOpStock = new HashMap<>();
                    promotionIds.forEach(pid -> {
                                //获取促销活动的其中一个单行
                                AmzPromotionFeeDateHisRecord amzPromotionFeeDateHisRecord = infoAll.stream().filter(Pitem -> Pitem.getPromotionId().equals(Pitem)).findFirst().orElse(null);
                                Integer flag = 0;
                                if (groupUsedStock.get(pid) != null) {
                                    flag = (int) groupUsedStock.get(pid).getSum();
                                }
                                //计算可操作库存 （提报-剩余-此活动已消耗）
                                noOpStock.put(pid, amzPromotionFeeDateHisRecord.getSkcStock() - amzPromotionFeeDateHisRecord.getRemainStock() - flag);
                            }
                    );


                    if (CollectionUtils.isEmpty(infoAll)) { //无可操作天数，结束本次时间循环
                        continue;
                    }

                    //活动申报价升序
                    List<AmzPromotionFeeDateHisRecord> ascActivityPrice = infoAll.stream().sorted(Comparator.comparing(AmzPromotionFeeDateHisRecord::getActivityPrice))
                            .collect(Collectors.toList());


                    List<String> sellerSkuLists = infoAll.stream().map(AmzPromotionFeeDateHisRecord::getSellerSku).distinct().collect(Collectors.toList());//所有要计算SellerSKu


                    //查询对应所有订单信息
                    List<SaleOrderItemPromotionPriceVO> orderItemsVo = saleOrdersMapper.selectOrderItemBySellerSku(sellerSkuLists, pstqueryFrom, pstqueryTo);

                    Long currentIdItem = null;
                    Long currentIdItemnew = currentIdItem;

                    //活动ID，selelr，消耗数量
                    HashMap<Long, HashMap<String, Integer>> prSum = new HashMap<>();

                    //活动本次总消耗
                    HashMap<Long, Integer> prSumTotla = new HashMap<>();
                    //操作对应活动 seller sku 行
                    for (AmzPromotionFeeDateHisRecord amzPromotionFeeDateHisRecord : ascActivityPrice) {  //活动 - selelrsku
                        Long promotionId = amzPromotionFeeDateHisRecord.getPromotionId(); //活动ID
                        Integer initFlagStock = noOpStock.get(promotionId); //可消耗
                        if (initFlagStock < 0) {
                            //无可消耗，此selelrSKu不计算
                            continue;
                        }
                        BigDecimal promotionPrice = amzPromotionFeeDateHisRecord.getDailyPrice().subtract(amzPromotionFeeDateHisRecord.getActivityPrice()).divide(new BigDecimal(100));//（日常-活动）*100 = 折扣


                        String sellerSku = amzPromotionFeeDateHisRecord.getSellerSku(); //sellerSKu
                        Integer usedStock = prSumTotla.get(promotionId); //已消耗


                        List<SaleOrderItemPromotionPriceVO> sellerSkuOrderInfo = orderItemsVo.stream().filter(item -> item.getSellerSku().equals(sellerSku) && item.getPromotionDiscountPrice() == null).collect(Collectors.toList());
                        for (SaleOrderItemPromotionPriceVO orderItems : sellerSkuOrderInfo) {
                            //活动可消耗 =已消耗结束
                            if (initFlagStock == usedStock) {
                                break;
                            }

                            if (usedStock >= initFlagStock) { //已消耗大于可消耗 ，回滚
                                int maxTotal = usedStock - initFlagStock;//多出数量

                                //获取行
                                SaleOrderItemPromotionPriceVO rollBackPrice = sellerSkuOrderInfo.stream().filter(item -> item.getId().equals(currentIdItemnew)).findFirst().orElse(null);
                                String sellerSkuRoll = rollBackPrice.getSellerSku();
                                rollBackPrice.setPromotionDiscountPrice((promotionPrice.multiply(new BigDecimal(maxTotal))));      //重置sellerSKu行 促销费

                                //回滚总计算行信息
                                HashMap<String, Integer> integerHashMap = prSum.get(promotionId);
                                Integer integer = integerHashMap.get(sellerSkuRoll);
                                integerHashMap.put(sellerSkuRoll, integer - maxTotal);
                                //TODO 是否需要重新put
                                break; //结束循环
                            }


                            BigDecimal quantity = new BigDecimal(orderItems.getQuantity()); //行销量
                            orderItems.setPromotionDiscountPrice(promotionPrice.multiply(quantity)); //销量 * 折扣

                            //计算此活动已计算total
                            Integer sumTotalPr = prSumTotla.get(promotionId);
                            if (sumTotalPr == null) {
                                prSumTotla.put(promotionId, orderItems.getQuantity());
                            } else {
                                prSumTotla.put(promotionId, sumTotalPr + orderItems.getQuantity());
                            }


                            HashMap<String, Integer> stringIntegerHashMap = prSum.get(promotionId);
                            if (stringIntegerHashMap == null) {
                                HashMap<String, Integer> integerHashMap = new HashMap<>();
                                integerHashMap.put(sellerSku, orderItems.getQuantity());
                                prSum.put(promotionId, integerHashMap);
                            } else {
                                Integer sellerSkuStock = stringIntegerHashMap.get(sellerSku);
                                if (sellerSkuStock == null) {
                                    stringIntegerHashMap.put(sellerSku, orderItems.getQuantity());
                                } else {
                                    stringIntegerHashMap.put(sellerSku, sellerSkuStock + orderItems.getQuantity()); //累计库存
                                }
                            }
                        }
                    }


                    //多活动计算完毕，保存消耗日志
                    if (prSum.size() > 0) {
                        for (Long pid : prSum.keySet()) {
                            HashMap<String, Integer> childList = prSum.get(pid);
                            for (String sellerSku : childList.keySet()) {
                                Integer stock = childList.get(childList);
                                AmzPromotionFeeStockRecord amzPromotionFeeStockRecord = new AmzPromotionFeeStockRecord();
                                amzPromotionFeeStockRecord.setPromotionId(pid);
                                amzPromotionFeeStockRecord.setSellerSku(sellerSku);
                                amzPromotionFeeStockRecord.setUsedStock(stock);
                                amzPromotionFeeStockRecord.settingDefaultSystemCreate();
                                amzPromotionFeeStockRecordService.save(amzPromotionFeeStockRecord);
                            }
                        }
                    }
                    //更新订单
                    List<SaleOrderItemPromotionPriceVO> collect = orderItemsVo.stream().filter(item -> item.getPromotionDiscountPrice() != null).collect(Collectors.toList());
                    for (SaleOrderItemPromotionPriceVO saleOrderItemPromotionPriceVO : collect) {
//                        saleOrdersMapper.udpateOrderItemsPromotionDiscountPrice(saleOrderItemPromotionPriceVO.getPromotionDiscountPrice(), saleOrderItemPromotionPriceVO.getId());
                    }
                }

            }
        }
    }




    /**
     * @param
     * @param skcIdMapPromotionIdList   所有SKC  多活动数据  key-SKC， value-活动ID
     * @description: 刷新历史Temu 8月数据
     * @author: Moore
     * @date: 2024/9/3 10:51
     * @return: void
     **/
    public void promotionIdsRateHis(Map<String, List<AmzPromotions>> skcIdMapPromotionIdList,
                                    List<AmzPromotionsSkuDetail> promotionsSkuDetailList) {

        Map<Long, List<AmzPromotionsSkuDetail>> nowPromtion = promotionsSkuDetailList.stream().collect(Collectors.groupingBy(AmzPromotionsSkuDetail::getPromotionsId));

        //对 SKC 下
        for (String skc : skcIdMapPromotionIdList.keySet()) {
            //获取所有活动
            List<AmzPromotions> amzPromotionsInfo = skcIdMapPromotionIdList.get(skc);
            if (CollectionUtils.isEmpty(amzPromotionsInfo)) {
                continue;
            }

            String beginTimePromotionFlag;
            String endTimePromotionfalag;
            //计算活动实际已消耗
            if (amzPromotionsInfo.size() > 1) {  //多活动
                AmzPromotions amzPromotions = amzPromotionsInfo.get(amzPromotionsInfo.size() - 1);
                beginTimePromotionFlag = cn.hutool.core.date.DateUtil.format(amzPromotionsInfo.get(0).getBeginTime(), DatePattern.NORM_DATETIME_PATTERN);
                endTimePromotionfalag = cn.hutool.core.date.DateUtil.format(amzPromotions.getEndTime(), DatePattern.NORM_DATETIME_PATTERN);
            }else{
                beginTimePromotionFlag = cn.hutool.core.date.DateUtil.format(amzPromotionsInfo.get(0).getBeginTime(), DatePattern.NORM_DATETIME_PATTERN);
                endTimePromotionfalag = cn.hutool.core.date.DateUtil.format(amzPromotionsInfo.get(0).getEndTime(), DatePattern.NORM_DATETIME_PATTERN);
            }


            //活动下所有SellerSKu
            List<String> sellerSkuListAll = new ArrayList<>();
            for (AmzPromotions amzPromotions : amzPromotionsInfo) {
                if (!CollectionUtils.isEmpty(nowPromtion.get(amzPromotions.getId()))) {
                    List<String> sellerSkuItem = nowPromtion.get(amzPromotions.getId()).stream().map(AmzPromotionsSkuDetail::getSellerSku).collect(Collectors.toList());
                    sellerSkuListAll.addAll(sellerSkuItem);
                }
            }

            //当前SKC 下午无SelelrSKU
            if (CollectionUtils.isEmpty(sellerSkuListAll)) {
                continue;
            }

            //订单已消耗
            List<SaleOrderItemPromotionPriceVO> saleOrderItemPromotionPriceGroup = saleOrdersMapper.selectOrderItemByPromotionPrice(sellerSkuListAll, beginTimePromotionFlag, endTimePromotionfalag);
            //当前SKC所有总已消耗
            Integer usedStock = saleOrderItemPromotionPriceGroup.stream().filter(item -> item.getQuantity() != null).mapToInt(SaleOrderItemPromotionPriceVO::getQuantity).sum();


            //skc 所有活动下总可消耗库存（提报-剩余）
            int skcStock = amzPromotionsInfo.stream().mapToInt(AmzPromotions::getSkcStock).sum(); //提报
            int remainStock = amzPromotionsInfo.stream().filter(item->item.getRemainStock()!=null).mapToInt(AmzPromotions::getRemainStock).sum(); //剩余
            int promotionStock = skcStock - remainStock; //总需消耗库存

            log.info("temu活动总需消耗库存：{},数量:{}", JSONObject.toJSONString(amzPromotionsInfo), promotionStock);
            //获取活动下当前
            List<AmzPromotionFeeStockRecord> promotionList = amzPromotionFeeStockRecordService.lambdaQuery()
                    .in(AmzPromotionFeeStockRecord::getPromotionId, amzPromotionsInfo.stream().map(AmzPromotions::getId).distinct().collect(Collectors.toList())).list();


            //有已消耗，无消耗记录记录日志
            if (CollectionUtils.isEmpty(promotionList)&&usedStock>0) {
                AmzPromotions amzPromotions = null;
                if (amzPromotionsInfo.size() > 1) {
                    //取提报最大的行
                    amzPromotions = amzPromotionsInfo.stream().max(Comparator.comparing(AmzPromotions::getSkcStock)).get();
                } else {
                    amzPromotions = amzPromotionsInfo.get(0);
                }
                AmzPromotionFeeStockRecord amzPromotionFeeStockRecord = new AmzPromotionFeeStockRecord();
                amzPromotionFeeStockRecord.setShopId(amzPromotions.getShopId());
                amzPromotionFeeStockRecord.setPromotionId(amzPromotions.getId());
                amzPromotionFeeStockRecord.setSkcId(skc);
                amzPromotionFeeStockRecord.setDate(new Date());
                amzPromotionFeeStockRecord.setUsedStock(usedStock);  //消耗
                amzPromotionFeeStockRecord.setRemark("Order" + amzPromotionsInfo.size());
                amzPromotionFeeStockRecord.settingDefaultSystemCreate();
                amzPromotionFeeStockRecordService.save(amzPromotionFeeStockRecord);
            }

            for (AmzPromotions amzPromotions : amzPromotionsInfo) {
                Integer skcStockRow = amzPromotions.getSkcStock(); //提报
                Integer skcStock1 = amzPromotions.getRemainStock()==null?0:amzPromotions.getRemainStock();//剩余
                if (skcStockRow - skcStock1==0) { //提报-剩余=0代表无需消耗
                    continue;
                }

                //活动ID
                Long promotionsId = amzPromotions.getId();
                Integer remainStockFlag= amzPromotions.getRemainStock();
                //判断明细是否存在
                List<AmzPromotionsSkuDetail> amzPromotionsSkuDetails = nowPromtion.get(promotionsId);
                if (CollectionUtils.isEmpty(amzPromotionsSkuDetails)) {
                    continue;
                }
                //当前活动时间
                List<SpiltDateUtil.Range> rangeList = SpiltDateUtil.splitToDays(DateUtil.convertDateToString(amzPromotions.getBeginTime(), "yyyy-MM-dd"), DateUtil.convertDateToString(amzPromotions.getEndTime(), "yyyy-MM-dd"));
                //活动下所有SKU
                List<String> sellerSkuList = amzPromotionsSkuDetails.stream().map(AmzPromotionsSkuDetail::getSellerSku).distinct().collect(Collectors.toList());
                //sellerSku 对应活动申报价
                HashMap<String, BigDecimal> selllerSkuDiscountPrice = new HashMap<>();
                //剩余为null
                amzPromotionsSkuDetails.forEach(item->{
                    if (remainStockFlag==null){  //取订单数据
                        selllerSkuDiscountPrice.put(item.getSellerSku(), item.getDiscountPrice()); //活动价
                    }else{ //取促销
                        selllerSkuDiscountPrice.put(item.getSellerSku(), item.getPrice().subtract(item.getDiscountPrice())); //折扣
                    }
                });
                //对活动下每天进行计算
                for (SpiltDateUtil.Range range : rangeList) {
                    String nowtime = DateUtil.convertDateToString(range.getStart(), "yyyy-MM-dd");
                    String pstqueryFrom = cn.hutool.core.date.DateUtil.format(range.getStart(), DatePattern.NORM_DATETIME_PATTERN);
                    String pstqueryTo = cn.hutool.core.date.DateUtil.format(cn.hutool.core.date.DateUtil.endOfDay(range.getStart()), DatePattern.NORM_DATETIME_PATTERN);

                    //存储各SellerSKuTotal
                    HashMap<String, BigDecimal> selllerSkuTotal = new HashMap<>();
                    BigDecimal initStockFlag = new BigDecimal(promotionStock - usedStock); //剩余可消耗
                    AtomicReference<BigDecimal> sumTotal = new AtomicReference<>(new BigDecimal("0")); //已计算Total

                    //可消耗大于0，  //提报大于已消耗
                    if (initStockFlag.compareTo(BigDecimal.ZERO) > 0 && amzPromotions.getSkcStock() > usedStock) {
                        //获取SellerSKu订单
                        List<SaleOrderItemPromotionPriceVO> saleOrderItemPromotionPriceVOS = saleOrdersMapper.selectOrderItemBySellerSku(sellerSkuList, pstqueryFrom, pstqueryTo);
                        saleOrderItemPromotionPriceVOS = saleOrderItemPromotionPriceVOS.stream().filter(item -> !StringUtils.isEmpty(item.getSellerSku())).collect(Collectors.toList());

                        for (SaleOrderItemPromotionPriceVO saleOrderPromotionPriceItem : saleOrderItemPromotionPriceVOS) {
                            if (saleOrderPromotionPriceItem.getQuantity() == null || saleOrderPromotionPriceItem.getQuantity() == 0) {
                                continue;
                            }

                            //单行总数量
                            BigDecimal orderQuantity = new BigDecimal(saleOrderPromotionPriceItem.getQuantity()) == null ? BigDecimal.ONE : new BigDecimal(saleOrderPromotionPriceItem.getQuantity()); //行销量

                            //本次计算后是否大于本次可消耗
                            boolean gtFlag = sumTotal.get().add(orderQuantity).compareTo(initStockFlag) > 0;
                            if (gtFlag) {
                                BigDecimal residueNum = initStockFlag.subtract(sumTotal.get()); //当天可消耗-已消耗 若等于0，结束循环
                                if (residueNum.compareTo(BigDecimal.ZERO) == 0) {
                                    break;
                                }
                                //本次实际计算数量
                                orderQuantity = residueNum;
                            }

                            BigDecimal promotionPrice = null;
                            if (remainStockFlag == null) {
                                BigDecimal applyPrice = selllerSkuDiscountPrice.get(saleOrderPromotionPriceItem.getSellerSku()); //活动申报价
                                 promotionPrice = saleOrderPromotionPriceItem.getItemPrice().subtract(applyPrice);//订单行单价-活动申报价=折扣
                            }else{
                                promotionPrice = selllerSkuDiscountPrice.get(saleOrderPromotionPriceItem.getSellerSku());
                            }
                            saleOrderPromotionPriceItem.setPromotionDiscountPrice(promotionPrice.multiply(orderQuantity)); //销量 * 折扣
                            saleOrderPromotionPriceItem.setOriItemAmount(saleOrderPromotionPriceItem.getItemAmount()); //原始销售额
                            saleOrderPromotionPriceItem.setItemAmount(saleOrderPromotionPriceItem.getItemAmount().subtract(saleOrderPromotionPriceItem.getPromotionDiscountPrice())); //计算促销后销售额->(原始销售额 - 促销费)
                            usedStock += orderQuantity.intValue(); //记录当前活动总已消耗
                            BigDecimal sellerSkuBig = selllerSkuTotal.get(saleOrderPromotionPriceItem.getSellerSku()) == null ? BigDecimal.ZERO : selllerSkuTotal.get(saleOrderPromotionPriceItem.getSellerSku());
                            selllerSkuTotal.put(saleOrderPromotionPriceItem.getSellerSku(), sellerSkuBig.add(orderQuantity));
                            sumTotal.set(sumTotal.get().add(orderQuantity));
                        }


                        //记录已消耗库存信息表
                        for (String sellerSku : selllerSkuTotal.keySet()) {
                            AmzPromotionFeeStockRecord amzPromotionFeeStockRecord = new AmzPromotionFeeStockRecord();
                            amzPromotionFeeStockRecord.setShopId(amzPromotions.getShopId());
                            amzPromotionFeeStockRecord.setPromotionId(promotionsId);
                            amzPromotionFeeStockRecord.setSkcId(skc);
                            amzPromotionFeeStockRecord.setDate(range.getStart());
                            amzPromotionFeeStockRecord.setSellerSku(sellerSku);
                            amzPromotionFeeStockRecord.setUsedStock(selllerSkuTotal.get(sellerSku).intValue());
                            amzPromotionFeeStockRecord.settingDefaultSystemCreate();
                            amzPromotionFeeStockRecordService.save(amzPromotionFeeStockRecord);
                        }

                        //更新订单
                        List<SaleOrderItemPromotionPriceVO> saleOrderUpatePrice = saleOrderItemPromotionPriceVOS.stream().filter(item -> item.getPromotionDiscountPrice() != null).collect(Collectors.toList());
                        for (SaleOrderItemPromotionPriceVO saleOrderItemPromotionPriceVO : saleOrderUpatePrice) {
                            saleOrdersMapper.udpateOrderItemsPromotionDiscountPrice(saleOrderItemPromotionPriceVO.getPromotionDiscountPrice(),
                                    saleOrderItemPromotionPriceVO.getItemAmount(), saleOrderItemPromotionPriceVO.getOriItemAmount(),
                                    saleOrderItemPromotionPriceVO.getId(),"V1");
                        }
                    }
                }

            }
        }
    }




}

