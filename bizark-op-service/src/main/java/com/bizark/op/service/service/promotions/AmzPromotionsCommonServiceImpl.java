package com.bizark.op.service.service.promotions;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bizark.common.util.BeanUtils;
import com.bizark.op.api.cons.AmzPromotionsCouponConstants;
import com.bizark.op.api.enm.promotions.*;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.promotions.*;
import com.bizark.op.api.exception.ErpCommonException;
import com.bizark.op.api.request.msg.his.*;
import com.bizark.op.api.request.msg.receive.*;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.promotions.*;
import com.bizark.op.api.vo.promotions.AmzPromotionsCouponDetailFromDb;
import com.bizark.op.common.core.redis.RedisCache;
import com.bizark.op.common.util.DateUtil;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.common.util.spring.SpringUtils;
import com.bizark.op.service.mapper.promotions.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @remarks:promotions和coupon公共服务
 * @Author: Ailill
 * @Date: 2023/7/17 14:58
 */
@Service
@Slf4j
public class AmzPromotionsCommonServiceImpl implements IAmzPromotionsCommonService {

    @Autowired
    private AmzPromotionsMapper amzPromotionsMapper;

    @Autowired
    private IAmzPromotionsCouponsService iAmzPromotionsCouponsService;

    @Autowired
    private IAmzPromotionsCouponService iAmzPromotionsCouponService;

    @Autowired
    private IAmzPromotionsCouponOperateLogService iAmzPromotionsCouponOperateLogService;

    @Autowired
    private AmzPromotionsCouponMapper amzPromotionsCouponMapper;

    @Autowired
    private IAmzPromotionsService iAmzPromotionsService;

    @Autowired
    private IAmzPromotionsSkuDetailService iAmzPromotionsSkuDetailService;

    @Autowired
    private IAmzPromotionsOperateLogService iAmzPromotionsOperateLogService;

    @Autowired
    private AmzPromotionsCouponsMapper amzPromotionsCouponsMapper;

    @Autowired
    private AmzPromotionsDailyDetailMapper amzPromotionsDailyDetailMapper;

    @Autowired
    private IAmzPromotionsApprovalService iAmzPromotionsApprovalService;

    @Autowired
    private AmzPromotionsHisMapper amzPromotionsHisMapper;

    @Autowired
    private AmzPromotionsCouponsHisMapper amzPromotionsCouponsHisMapper;

    @Autowired
    private IAmzPromotionAndCouponService iAmzPromotionAndCouponService;

    @Autowired
    private IAmzPromotionApprovalAdviceService iAmzPromotionApprovalAdviceService;


    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private IAmzPromotionsHisService iAmzPromotionsHisService;

    @Autowired
    private AmzPromotionsDetailExtraPriceService amzPromotionsDetailExtraPriceService;

    @Autowired
    private AccountService accountService;


    /**
     * coupon消息接收处理
     *
     * @param msg
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String couponMsgResolve(AmzPromotionsCouponsReceiveMsg msg) {

        if (msg == null) {
            return "coupon消息为空";
        }
        Integer channel = msg.getChannel();
        if (channel == null) {
            return "coupon渠道为空";
        }
        if (channel == AmzPromotionsChannelEnum.VC.value()) {
            return vcMsgResolve(msg);
        } else {
            return scMsgResolve(msg);
        }
    }


    /**
     * vc渠道 coupon 消息处理
     *
     * @param msg
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String vcMsgResolve(AmzPromotionsCouponsReceiveMsg msg) {

        Integer rpaType = msg.getRpaType();
        Long campaignId = msg.getCampaignId();
        if (campaignId == null) {
            return "coupon活动id为空";
        }
        AmzPromotionsCoupons amzPromotionsCoupons = iAmzPromotionsCouponsService.getById(campaignId);
        if (amzPromotionsCoupons == null) {
            return "coupon无此活动";
        }
        if (rpaType == null) {
            return "couponRPA类型为空";
        }
        if (rpaType != 1 && rpaType != 2 && rpaType != 3 && rpaType != 4) {
            return "couponRPA类型错误";
        }
        List<AmzPromotionsCouponReMsg> couponList = msg.getCouponList();
        if (CollectionUtil.isEmpty(couponList)) {
            return "coupon优惠券为空";
        }
        String result = msg.getResult();
        if (StringUtil.isEmpty(result)) {
            return "coupon_result为空";
        }
        return vcCouponStateUpdate(msg, couponList, amzPromotionsCoupons);

    }

    /**
     * vc渠道 coupon 消息处理
     *
     * @param msg
     * @param couponList
     * @param amzPromotionsCoupons
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String vcCouponStateUpdate(AmzPromotionsCouponsReceiveMsg msg, List<AmzPromotionsCouponReMsg> couponList, AmzPromotionsCoupons amzPromotionsCoupons) {

        String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", DateUtils.getNowDate());
        Date date = DateUtils.parseDate(dateStr, "yyyy-MM-dd HH:mm:ss");
        AmzPromotionsCoupons promotionsCoupons = new AmzPromotionsCoupons();
        promotionsCoupons.setId(msg.getCampaignId());
        promotionsCoupons.setUpdatedAt(date);
        promotionsCoupons.setUpdatedName("System");

        Integer rpaType = msg.getRpaType();
        String result = msg.getResult();
        Integer campaignState = msg.getCampaignState();
        StringBuilder detail = new StringBuilder();
        Integer operateType = null;

        //创建或者提交异常的修改或者上传表格修改
        if (rpaType == 1) {
            operateType = AmzCouponOperateTypeEnum.RPA_SUBMIT_COUPON_ACTIVE.value();
            Date campaignDate = null;
            if (StringUtil.isNotEmpty(msg.getCampaignCreated())) {
                try {
                    campaignDate = DateUtils.parseDate(msg.getCampaignCreated(), "yyyy-MM-dd HH:mm:ss");
                } catch (Exception e) {
                    campaignDate = DateUtil.convertStringToDate(DateUtil.getDateToStr(msg.getCampaignCreated()),"yyyy-MM-dd HH:mm:ss");
                }
            }
            if ("success".equalsIgnoreCase(result)) {

                if (StringUtil.isNotEmpty(msg.getCouponsBackId())) {
                    promotionsCoupons.setCampaignId(msg.getCouponsBackId());
                    if (couponList.stream().anyMatch(t -> StringUtils.isEmpty(t.getCouponBackId()))) {
                        threadPoolTaskExecutor.execute(()->{
                            try {
                                TimeUnit.MINUTES.sleep(2);
                                iAmzPromotionAndCouponService.syncCouponsList(iAmzPromotionsCouponsService.list(new LambdaQueryWrapper<AmzPromotionsCoupons>().in(AmzPromotionsCoupons::getId, Arrays.asList(msg.getCampaignId()))), "system");
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                        });
                    }
                }
                promotionsCoupons.setSubShopBack(true);
                if (AmzCouponActiveStateEnum.NEEDS_YOUR_ATTENTION.value() == campaignState) {
                    String errorExcel = msg.getErrorExcel();
                    detail.append("活动状态为Needs your attention。重新上传时，提供了错误信息表格以供下载。共尝试1次。");
                    promotionsCoupons.setActiveState(campaignState);
                    promotionsCoupons.setErrorExcel(errorExcel);
                    iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(promotionsCoupons);
                    iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), null, null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());

                    for (AmzPromotionsCouponReMsg couponReMsg : couponList) {
                        AmzPromotionsCoupon coupon = new AmzPromotionsCoupon();
                        coupon.setId(couponReMsg.getCouponId());
                        coupon.setUpdatedName("System");
                        coupon.setUpdatedAt(date);
                        coupon.setCouponState(AmzCouponStateEnum.NEEDS_YOUR_ATTENTION.value());
                        try {
                            if (StringUtil.isNotEmpty(couponReMsg.getCouponBeginDate())) {
                                log.info("couponId:{},返回开始日期:{}",couponReMsg.getCouponId(),couponReMsg.getCouponBeginDate());
                                coupon.setBeginDate(DateUtils.parseDate(couponReMsg.getCouponBeginDate(),"yyyy-MM-dd HH:mm:ss"));
                            }
                            if (StringUtil.isNotEmpty(couponReMsg.getCouponEndDate())) {
                                log.info("couponId:{},返回结束日期:{}",couponReMsg.getCouponId(),couponReMsg.getCouponEndDate());
                                coupon.setEndDate(dateAddHms(DateUtils.parseDate(couponReMsg.getCouponEndDate(),"yyyy-MM-dd HH:mm:ss")));
                            }
                        } catch (Exception e) {
                            log.error("后台返回coupon开始结束时间错误,{}",e.getMessage());
                            coupon.setBeginDate(null);
                            coupon.setEndDate(null);
                        }
                        if (campaignDate != null) {
                            coupon.setCreatedAt(campaignDate);
                        }
                        if (StringUtil.isNotEmpty(couponReMsg.getCouponBackId())) {
                            coupon.setBackId(couponReMsg.getCouponBackId());
                        }
                        iAmzPromotionsCouponService.updateAmzPromotionsCoupon(coupon);
                        iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), couponReMsg.getCouponId(), null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());
                    }
                    //更新活动开始结束时间
                    updateCouponBeginDateAndEndDate(msg.getCampaignId());
                    //清除缓存
                    deleteCouponsCacheKey(msg.getCampaignId());
                } else if (AmzCouponActiveStateEnum.ACTIVE.value() == campaignState) {
                    detail.append("活动状态为Active。共尝试1次。");
                    promotionsCoupons.setActiveState(campaignState);
                    iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(promotionsCoupons);
                    iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), null, null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());
                    for (AmzPromotionsCouponReMsg couponReMsg : couponList) {
                        AmzPromotionsCoupon coupon = new AmzPromotionsCoupon();
                        coupon.setId(couponReMsg.getCouponId());
                        coupon.setUpdatedName("System");
                        coupon.setUpdatedAt(date);
                        coupon.setCouponState(AmzCouponStateEnum.ACTIVE_VC.value());
                        try {
                            if (StringUtil.isNotEmpty(couponReMsg.getCouponBeginDate())) {
                                log.info("couponId:{},返回开始日期:{}",couponReMsg.getCouponId(),couponReMsg.getCouponBeginDate());
                                coupon.setBeginDate(DateUtils.parseDate(couponReMsg.getCouponBeginDate(),"yyyy-MM-dd HH:mm:ss"));
                            }
                            if (StringUtil.isNotEmpty(couponReMsg.getCouponEndDate())) {
                                log.info("couponId:{},返回结束日期:{}",couponReMsg.getCouponId(),couponReMsg.getCouponEndDate());
                                coupon.setEndDate(dateAddHms(DateUtils.parseDate(couponReMsg.getCouponEndDate(),"yyyy-MM-dd HH:mm:ss")));
                            }
                        } catch (Exception e) {
                            log.error("后台返回coupon开始结束时间错误,{}",e.getMessage());
                            coupon.setBeginDate(null);
                            coupon.setEndDate(null);
                        }
                        if (campaignDate != null) {
                            coupon.setCreatedAt(campaignDate);
                        }
                        if (StringUtil.isNotEmpty(couponReMsg.getCouponBackId())) {
                            coupon.setBackId(couponReMsg.getCouponBackId());
                        }
                        iAmzPromotionsCouponService.updateAmzPromotionsCoupon(coupon);
                        iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), couponReMsg.getCouponId(), null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());
                    }
                    updateCouponBeginDateAndEndDate(msg.getCampaignId());
                    //清除缓存
                    deleteCouponsCacheKey(msg.getCampaignId());
                } else {
                    operateType = AmzCouponOperateTypeEnum.RPA_SUBMIT_COUPON_ACTIVE.value();
                    detail.append("截至操作时间10分钟后未成功，共尝试1次。返回活动状态不是ACTIVE或者ATTENTION。");
                    promotionsCoupons.setActiveState(AmzCouponActiveStateEnum.SUBMIT_EXCEPTION.value());
                    if (StringUtil.isNotEmpty(msg.getDetail())) {
                        detail.append("错误信息如下:").append(msg.getDetail());
                    }
                    iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(promotionsCoupons);
                    iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), null, null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());
                    for (AmzPromotionsCouponReMsg couponReMsg : couponList) {
                        AmzPromotionsCoupon coupon = new AmzPromotionsCoupon();
                        coupon.setId(couponReMsg.getCouponId());
                        coupon.setUpdatedName("System");
                        coupon.setUpdatedAt(date);
                        coupon.setCouponState(AmzCouponStateEnum.SUBMIT_EXCEPTION.value());
                        coupon.setError(couponReMsg.getError());
                        iAmzPromotionsCouponService.updateAmzPromotionsCoupon(coupon);
                        iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), couponReMsg.getCouponId(), null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());
                    }
                    //清除缓存
                    deleteCouponsCacheKey(msg.getCampaignId());
                }
            } else {
                operateType = AmzCouponOperateTypeEnum.RPA_SUBMIT_COUPON_ACTIVE.value();
                detail.append("截至操作时间10分钟后未成功，共尝试1次。");
                if (StringUtil.isNotEmpty(msg.getDetail())) {
                    detail.append("错误信息如下:").append(msg.getDetail());
                }
                promotionsCoupons.setActiveState(AmzCouponActiveStateEnum.SUBMIT_EXCEPTION.value());
//                promotionsCoupons.setApprovalStatus(AmzPromotionApprovalEnum.APPROVING.getLabel());
                iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(promotionsCoupons);
                iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), null, null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());
                for (AmzPromotionsCouponReMsg couponReMsg : couponList) {
                    AmzPromotionsCoupon coupon = new AmzPromotionsCoupon();
                    coupon.setId(couponReMsg.getCouponId());
                    coupon.setUpdatedName("System");
                    coupon.setUpdatedAt(date);
                    coupon.setCouponState(AmzCouponStateEnum.SUBMIT_EXCEPTION.value());
                    coupon.setError(couponReMsg.getError());
//                    coupon.setApprovalStatus(AmzPromotionApprovalEnum.APPROVING.getLabel());
                    iAmzPromotionsCouponService.updateAmzPromotionsCoupon(coupon);
                    iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), couponReMsg.getCouponId(), null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());
                }
                AmzPromotionsCoupons fromDb = amzPromotionsCouponsMapper.selectAmzPromotionsCouponsById(msg.getCampaignId());
                //创建失败，如果店铺后台不存在，不回滚数据
                if (fromDb.getSubShopBack() == null || !fromDb.getSubShopBack()) {
                    log.info("coupon活动创建失败，清除历史数据");
                    //清除缓存
                    deleteCouponsCacheKey(msg.getCampaignId());
                }else {
                    log.info("开始回滚数据，活动id为:" + msg.getCampaignId());
                    //回滚数据
                    rollBack(msg.getCampaignId(), null);
                }

                try {
                    iAmzPromotionApprovalAdviceService.couponSubmitExceptionAdvice(msg.getCampaignId(),null,msg.getDetail(),1);
                } catch (Exception e) {
                    log.error("coupon活动提交返回提交异常通知运营失败,活动id为:{},错误信息为:{}",msg.getCampaignId(), e.getMessage());
                }
            }

            //修改
        } else if (rpaType == 2) {
            if ("success".equalsIgnoreCase(result)) {
                operateType = AmzCouponOperateTypeEnum.RPA_MODIFY_COUPON.value();
                detail.append("状态更新为Active。共尝试1次。");
                AmzPromotionsCouponReMsg couponReMsg = couponList.get(0);
                AmzPromotionsCoupon couponFromDb = amzPromotionsCouponMapper.selectAmzPromotionsCouponById(couponReMsg.getCouponId());
                if (couponFromDb != null && AmzCouponStateEnum.COMPLETE_VC.value() != couponFromDb.getCouponState() &&
                        AmzCouponStateEnum.CANCELED.value() != couponFromDb.getCouponState() &&
                        AmzCouponStateEnum.EXPIRED.value() != couponFromDb.getCouponState()) {
                    AmzPromotionsCoupon coupon = new AmzPromotionsCoupon();
                    coupon.setId(couponReMsg.getCouponId());
                    coupon.setUpdatedName("System");
                    coupon.setUpdatedAt(date);
                    coupon.setCouponState(AmzCouponStateEnum.ACTIVE_VC.value());
                    iAmzPromotionsCouponService.updateAmzPromotionsCoupon(coupon);
                    iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), couponReMsg.getCouponId(), null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());
                    //判断活动下的所有优惠券 是否含有 修改中 修改异常 取消中 取消异常，如果含的话活动状态就需更新为Active（含异常），不含就直接更新为Active。
                    updateCampaignStateWhenStateIsActive(msg, promotionsCoupons, date, operateType, detail, amzPromotionsCoupons);

                }
                //清除缓存
                deleteCouponCacheKey(couponReMsg.getCouponId(), msg.getCampaignId());
            } else {
                operateType = AmzCouponOperateTypeEnum.RPA_MODIFY_COUPON.value();
                detail.append("截至操作时间10分钟后未成功，返回异常，共尝试1次。");
                AmzPromotionsCouponReMsg couponReMsg = couponList.get(0);
                AmzPromotionsCoupon coupon = new AmzPromotionsCoupon();
                coupon.setId(couponReMsg.getCouponId());
                coupon.setUpdatedName("System");
                coupon.setUpdatedAt(date);
                coupon.setCouponState(AmzCouponStateEnum.MODIFY_EXCEPTION.value());
//                coupon.setApprovalStatus(AmzPromotionApprovalEnum.APPROVING.getLabel());
                iAmzPromotionsCouponService.updateAmzPromotionsCoupon(coupon);
                if (StringUtil.isNotEmpty(msg.getDetail())) {
                    detail.append("错误信息如下:").append(msg.getDetail()).append(";");
                }
                iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), couponReMsg.getCouponId(), null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());
                AmzPromotionsCoupons coupons = iAmzPromotionsCouponsService.selectAmzPromotionsCouponsById(msg.getCampaignId());
                if (coupons != null && coupons.getActiveState() == AmzCouponActiveStateEnum.ACTIVE.value()) {
                    promotionsCoupons.setActiveState(AmzCouponActiveStateEnum.ACTIVE_HAS_EXCEPTION.value());
                    iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(promotionsCoupons);
                    iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), null, null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());
                }
                //更新活动的更新时间为优惠券的操作时间
                if (coupons != null && coupons.getActiveState() != AmzCouponActiveStateEnum.ACTIVE.value()) {
                    iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(promotionsCoupons);
                }
/*                AmzPromotionsCoupons amzPromotionsCoupons1 = new AmzPromotionsCoupons();
                amzPromotionsCoupons1.setId(msg.getCampaignId());
//                amzPromotionsCoupons1.setApprovalStatus(AmzPromotionApprovalEnum.APPROVING.getLabel());
                iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(amzPromotionsCoupons1);*/
                log.info("开始回滚数据，活动id为:" + msg.getCampaignId());
                //回滚数据
                rollBack(msg.getCampaignId(), couponReMsg.getCouponId());

                try {
                    iAmzPromotionApprovalAdviceService.couponSubmitExceptionAdvice(msg.getCampaignId(),couponReMsg.getCouponId(),msg.getDetail(),2);
                } catch (Exception e) {
                    log.error("coupon活动修改返回修改异常通知运营失败,活动id为:{},错误信息为:{}",msg.getCampaignId(), e.getMessage());
                }
            }
            //update
        } else if (rpaType == 3) {
            if ("success".equalsIgnoreCase(result)) {

            } else {

            }
            //取消
        } else if (rpaType == 4) {
            if ("success".equalsIgnoreCase(result)) {
                //取消活动成功
                if (msg.getCancelCampaign()) {
                    operateType = AmzCouponOperateTypeEnum.RPA_CANCEL_COUPON_OR_ACTIVE.value();
                    detail.append("Coupon活动(").append(msg.getCampaignName()).append(")状态更新为Canceled。共尝试1次。");
                    promotionsCoupons.setActiveState(AmzCouponActiveStateEnum.CANCELED.value());
                    iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(promotionsCoupons);
                    iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), null, null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());
                    AmzPromotionsCoupon c = new AmzPromotionsCoupon();
                    c.setCouponId(msg.getCampaignId());
                    List<AmzPromotionsCoupon> couponList1 = iAmzPromotionsCouponService.selectAmzPromotionsCouponList(c);
                    if (CollectionUtil.isNotEmpty(couponList1)) {
                        List<Long> collect = couponList1.stream()
                                .filter(item -> AmzCouponStateEnum.CANCELED.value() != item.getCouponState() && AmzCouponStateEnum.COMPLETE_VC.value() != item.getCouponState())
                                .map(AmzPromotionsCoupon::getId).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(collect)) {
                            //更新已完结coupon的完结时间
                            for (Long aLong : collect) {
                                AmzPromotionsCoupon completeDateCoupon = new AmzPromotionsCoupon();
                                completeDateCoupon.setId(aLong);
                                AmzPromotionsCoupon fromDb = amzPromotionsCouponMapper.selectAmzPromotionsCouponById(aLong);
                                if (fromDb != null && fromDb.getCompleteDate() == null) {
                                    Date tempCompleteDate = DateUtil.UtcToPacificDate(date);
                                    //如果券的结束时间小于等于当前pst时间，则完结时间为结束时间，否则为当前pst时间
                                    if (DateUtil.compareDate(fromDb.getEndDate(), tempCompleteDate) <= 0) {
                                        completeDateCoupon.setCompleteDate(fromDb.getEndDate());
                                    } else {
                                        completeDateCoupon.setCompleteDate(tempCompleteDate);
                                    }

                                    amzPromotionsCouponMapper.updateAmzPromotionsCoupon(completeDateCoupon);
                                }
                            }
                            //更新该优惠券所属活动的其他优惠券的状态
                            amzPromotionsCouponMapper.updateAmzPromotionsCouponByIds(collect.toArray(new Long[collect.size()]), AmzCouponStateEnum.CANCELED.value(), null, "System", date);
                        }

                    }
                    //取消优惠券成功
                } else {
                    operateType = AmzCouponOperateTypeEnum.RPA_CANCEL_COUPON.value();
                    AmzPromotionsCouponReMsg couponReMsg = couponList.get(0);
                    detail.append("Coupon(").append(couponReMsg.getCouponName()).append(")状态更新为Canceled。共尝试1次。");
                    AmzPromotionsCoupon coupon = new AmzPromotionsCoupon();
                    coupon.setId(couponReMsg.getCouponId());
                    coupon.setUpdatedName("System");
                    coupon.setUpdatedAt(date);
                    //更新该优惠券状态为Canceled
                    coupon.setCouponState(AmzCouponStateEnum.CANCELED.value());
                    //更新已完结coupon的完结时间
                    AmzPromotionsCoupon fromDb = amzPromotionsCouponMapper.selectAmzPromotionsCouponById(couponReMsg.getCouponId());
                    if (fromDb != null && fromDb.getCompleteDate() == null) {
                        Date tempCompleteDate = DateUtil.UtcToPacificDate(date);
                        //如果结束时间小于等于当前pst时间，则设置完结时间为结束时间，否则设置为当前pst时间
                        if (DateUtil.compareDate(fromDb.getEndDate(), tempCompleteDate) <= 0) {
                            coupon.setCompleteDate(fromDb.getEndDate());
                        } else {
                            coupon.setCompleteDate(tempCompleteDate);
                        }
                    }
                    iAmzPromotionsCouponService.updateAmzPromotionsCoupon(coupon);
                    iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), couponReMsg.getCouponId(), null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());
                    AmzPromotionsCoupons couponsById = iAmzPromotionsCouponsService.selectAmzPromotionsCouponsById(msg.getCampaignId());
                    //更新活动状态
                    AmzPromotionsCoupon c = new AmzPromotionsCoupon();
                    c.setCouponId(msg.getCampaignId());
                    List<AmzPromotionsCoupon> couponList1 = iAmzPromotionsCouponService.selectAmzPromotionsCouponList(c);
                    long count = couponList1.stream().filter(s -> s.getCouponState() == AmzCouponStateEnum.CANCELED.value()).count();
                    int pcFlag = 0;
                    boolean hasActive = false;
                    if (CollectionUtil.isNotEmpty(couponList1)) {
                        for (AmzPromotionsCoupon pc : couponList1) {
                            if (AmzCouponStateEnum.MODIFYING.value() == pc.getCouponState() || AmzCouponStateEnum.MODIFY_EXCEPTION.value() == pc.getCouponState() || AmzCouponStateEnum.CANCELING.value() == pc.getCouponState() || AmzCouponStateEnum.CANCELING_EXCEPTION.value() == pc.getCouponState()) {
                                pcFlag = 1;
                                break;
                            }
                            if (AmzCouponStateEnum.ACTIVE_VC.value() == pc.getCouponState()) {
                                hasActive = true;
                            }
                        }
                    }
                    if (new Integer(couponList1.size()).longValue() == count) {
                        promotionsCoupons.setActiveState(AmzCouponActiveStateEnum.CANCELED.value());
                    } else if (pcFlag == 1) {
                        promotionsCoupons.setActiveState(AmzCouponActiveStateEnum.ACTIVE_HAS_EXCEPTION.value());
                    } else if (pcFlag == 0 && hasActive) {
                        promotionsCoupons.setActiveState(AmzCouponActiveStateEnum.ACTIVE.value());
                    } else {
                    }
                    iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(promotionsCoupons);
                    iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), null, null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());
                }
            } else {
                detail.append("截至操作时间10分钟后未成功，返回异常，共尝试1次。");
                //取消活动异常，活动状态改为取消异常，该活动下的非Canceled状态,非Complete的优惠券，全部为取消异常，已经是Canceled,Complete状态的不变。
                if (msg.getCancelCampaign()) {
                    operateType = AmzCouponOperateTypeEnum.RPA_CANCEL_COUPON_OR_ACTIVE.value();
                    promotionsCoupons.setActiveState(AmzCouponActiveStateEnum.CANCELING_EXCEPTION.value());
                    iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(promotionsCoupons);
                    if (StringUtil.isNotEmpty(msg.getDetail())) {
                        detail.append("错误信息如下:").append(msg.getDetail()).append(";");
                    }
                    iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), null, null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());
                    AmzPromotionsCoupon c = new AmzPromotionsCoupon();
                    c.setCouponId(msg.getCampaignId());
                    List<AmzPromotionsCoupon> couponList1 = iAmzPromotionsCouponService.selectAmzPromotionsCouponList(c);
                    if (CollectionUtil.isNotEmpty(couponList1)) {
                        List<Long> collect = couponList1.stream()
                                .filter(item -> AmzCouponStateEnum.CANCELED.value() != item.getCouponState() && AmzCouponStateEnum.COMPLETE_VC.value() != item.getCouponState())
                                .map(AmzPromotionsCoupon::getId)
                                .collect(Collectors.toList());
                        //更新该优惠券所属活动的其他优惠券的状态
                        if (CollectionUtil.isNotEmpty(collect)) {
                            amzPromotionsCouponMapper.updateAmzPromotionsCouponByIds(collect.toArray(new Long[collect.size()]), AmzCouponStateEnum.CANCELING_EXCEPTION.value(), null, "System", date);
                            for (Long needUpdateCouponId : collect) {
                                iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), needUpdateCouponId, null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());
                            }
                        }
                    }
                    try {
                        iAmzPromotionApprovalAdviceService.couponSubmitExceptionAdvice(msg.getCampaignId(),null,msg.getDetail(),3);
                    } catch (Exception e) {
                        log.error("coupon活动取消返回取消活动异常通知运营失败,活动id为:{},错误信息为:{}",msg.getCampaignId(), e.getMessage());
                    }
                    //取消优惠券异常
                } else {
                    operateType = AmzCouponOperateTypeEnum.RPA_CANCEL_COUPON.value();
                    detail.append("截至操作时间10分钟后未成功，返回异常，共尝试1次。");
                    AmzPromotionsCouponReMsg couponReMsg = couponList.get(0);
                    AmzPromotionsCoupon coupon = new AmzPromotionsCoupon();
                    coupon.setId(couponReMsg.getCouponId());
                    coupon.setUpdatedName("System");
                    coupon.setUpdatedAt(date);
                    coupon.setCouponState(AmzCouponStateEnum.CANCELING_EXCEPTION.value());
                    iAmzPromotionsCouponService.updateAmzPromotionsCoupon(coupon);
                    if (StringUtil.isNotEmpty(msg.getDetail())) {
                        detail.append("错误信息如下:").append(msg.getDetail()).append(";");
                    }
                    iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), couponReMsg.getCouponId(), null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());
                    AmzPromotionsCoupons c = iAmzPromotionsCouponsService.selectAmzPromotionsCouponsById(msg.getCampaignId());
                    if (AmzCouponActiveStateEnum.ACTIVE.value() == c.getActiveState()) {
                        AmzPromotionsCoupon tempAmzPromotionCoupon = new AmzPromotionsCoupon();
                        tempAmzPromotionCoupon.setCouponId(msg.getCampaignId());
                        List<AmzPromotionsCoupon> tempAmzPromotionCouponListFromDb = iAmzPromotionsCouponService.selectAmzPromotionsCouponList(tempAmzPromotionCoupon);
                        promotionsCoupons.setActiveState(tempAmzPromotionCouponListFromDb.size() > 1 ? AmzCouponActiveStateEnum.ACTIVE_HAS_EXCEPTION.value() : AmzCouponActiveStateEnum.CANCELING_EXCEPTION.value());
                        iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(promotionsCoupons);
                        iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), null, null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());
                    } else {
                        iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(promotionsCoupons);
                    }
                    try {
                        iAmzPromotionApprovalAdviceService.couponSubmitExceptionAdvice(msg.getCampaignId(),couponReMsg.getCouponId(),msg.getDetail(),4);
                    } catch (Exception e) {
                        log.error("coupon优惠券取消返回取消优惠券异常通知运营失败,活动id为:{},错误信息为:{}",msg.getCampaignId(), e.getMessage());
                    }
                }
            }
        } else {

        }
        return "success";
    }

    /**
     * vc渠道下创建活动状态返回Active判断活动下的所有优惠券 是否含有 修改中 修改异常 取消中 取消异常，如果含的话活动状态就需更新为Active（含异常），不含就直接更新为Active。
     *
     * @param msg                  活动消息体
     * @param promotionsCoupons    待更新的活动
     * @param date                 操作日期
     * @param operateType          操作类型
     * @param detail               详情
     * @param amzPromotionsCoupons 库中的活动
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateCampaignStateWhenStateIsActive(AmzPromotionsCouponsReceiveMsg msg, AmzPromotionsCoupons promotionsCoupons, Date date, Integer operateType, StringBuilder detail, AmzPromotionsCoupons amzPromotionsCoupons) {

        //查询活动下所有优惠券
        AmzPromotionsCoupon coupon = new AmzPromotionsCoupon();
        coupon.setCouponId(msg.getCampaignId());
        List<AmzPromotionsCoupon> couponList = amzPromotionsCouponMapper.selectAmzPromotionsCouponList(coupon);
        if (CollectionUtil.isEmpty(couponList)) {
            return;
        }
        List<Integer> couponStateList = new ArrayList<>();
        //修改中 修改异常 取消中 取消异常
        couponStateList.add(AmzCouponStateEnum.MODIFYING.value());
        couponStateList.add(AmzCouponStateEnum.MODIFY_EXCEPTION.value());
        couponStateList.add(AmzCouponStateEnum.CANCELING.value());
        couponStateList.add(AmzCouponStateEnum.CANCELING_EXCEPTION.value());
        Boolean hasException = false;
        for (AmzPromotionsCoupon c : couponList) {
            if (couponStateList.contains(c.getCouponState())) {
                hasException = true;
                break;
            }
        }
        promotionsCoupons.setActiveState(hasException ? AmzCouponActiveStateEnum.ACTIVE_HAS_EXCEPTION.value() : AmzCouponActiveStateEnum.ACTIVE.value());
        iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(promotionsCoupons);
        iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), null, null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());
    }

    /**
     * vc渠道下创建活动返回的优惠券状态与库中状态对比并更新
     *
     * @param msg                  活动消息体
     * @param couponReMsg          优惠券消息体
     * @param couponListFromDb     返回的优惠券在库中对应的优惠券集合
     * @param coupon               待更新的优惠券
     * @param amzPromotionsCoupons 库中的活动
     * @param date                 日期
     * @param operateType          操作类型
     * @param detail               详情
     */
    private void couponCompareWithCouponFromDb(AmzPromotionsCouponsReceiveMsg msg, AmzPromotionsCouponReMsg couponReMsg, List<AmzPromotionsCoupon> couponListFromDb, AmzPromotionsCoupon coupon, AmzPromotionsCoupons amzPromotionsCoupons, Date date, Integer operateType, StringBuilder detail) {

        List<AmzPromotionsCoupon> collect = couponListFromDb.stream().filter(s -> s.getId().compareTo(couponReMsg.getCouponId()) == 0).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(collect)) {
            return;
        }
        //如果优惠券返回 complete canceled expired，直接用返回的状态更新（如果相等不用更新）
        List<Integer> couponStateList1 = new ArrayList<>();
        couponStateList1.add(AmzCouponStateEnum.COMPLETE_VC.value());
        couponStateList1.add(AmzCouponStateEnum.CANCELED.value());
        couponStateList1.add(AmzCouponStateEnum.EXPIRED.value());

        //如果优惠券返回Active，且优惠券在数据库是Active、修改中、修改异常、取消中、取消异常，就不更新优惠券状态。
        List<Integer> couponStateList2 = new ArrayList<>();
        couponStateList2.add(AmzCouponStateEnum.ACTIVE_VC.value());
        couponStateList2.add(AmzCouponStateEnum.MODIFYING.value());
        couponStateList2.add(AmzCouponStateEnum.MODIFY_EXCEPTION.value());
        couponStateList2.add(AmzCouponStateEnum.CANCELING.value());
        couponStateList2.add(AmzCouponStateEnum.CANCELING_EXCEPTION.value());

        List<Integer> couponStateList3 = new ArrayList<>();
        //如果优惠券返回Active，且优惠券在数据库是Needs your attention、canceled（Budget exhausted），直接更新优惠券状态为Active。
        couponStateList3.add(AmzCouponStateEnum.NEEDS_YOUR_ATTENTION.value());
        couponStateList3.add(AmzCouponStateEnum.CANCELED_VC.value());

        //如果优惠券返回 complete canceled expired，直接用返回的状态更新（如果相等不用更新）
        if (couponStateList1.contains(couponReMsg.getCouponState())) {
            if (couponReMsg.getCouponState() != collect.get(0).getCouponState()) {
                coupon.setCouponState(couponReMsg.getCouponState() != null ? couponReMsg.getCouponState() : AmzCouponStateEnum.ACTIVE_VC.value());
                iAmzPromotionsCouponService.updateAmzPromotionsCoupon(coupon);
                iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), couponReMsg.getCouponId(), null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());
            }

        } else if (AmzCouponStateEnum.ACTIVE_VC.value() == couponReMsg.getCouponState()) {
            //如果优惠券返回Active，且优惠券在数据库是Needs your attention、canceled（Budget exhausted），直接更新优惠券状态为Active。
            if (couponStateList3.contains(collect.get(0).getCouponState())) {
                coupon.setCouponState(AmzCouponStateEnum.ACTIVE_VC.value());
                iAmzPromotionsCouponService.updateAmzPromotionsCoupon(coupon);
                iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), couponReMsg.getCouponId(), null, date, "System", operateType, msg.getResult(), detail.toString(), amzPromotionsCoupons.getOrganizationId());
            }
        } else {

        }
    }

    /**
     * sc渠道 coupon 消息处理
     *
     * @param msg
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String scMsgResolve(AmzPromotionsCouponsReceiveMsg msg) {

        Integer rpaType = msg.getRpaType();
        if (rpaType == null) {
            return "RPA类型为空";
        }
        if (rpaType != 1 && rpaType != 2 && rpaType != 3 && rpaType != 4) {
            return "RPA类型错误";
        }
        List<AmzPromotionsCouponReMsg> couponList = msg.getCouponList();
        if (CollectionUtil.isEmpty(couponList)) {
            return "优惠券为空";
        }
        String result = msg.getResult();
        if (StringUtil.isEmpty(result)) {
            return "result为空";
        }

        String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", DateUtils.getNowDate());
        Date date = DateUtils.parseDate(dateStr, "yyyy-MM-dd HH:mm:ss");

        List<Long> collect = couponList.stream().filter(s -> s.getCouponId() != null).map(s -> s.getCouponId()).collect(Collectors.toList());
        List<AmzPromotionsCoupon> couponList1 = amzPromotionsCouponMapper.selectCouponListByCouponIds(collect.toArray(new Long[collect.size()]));
        if (CollectionUtil.isEmpty(couponList1)) {
            return "数据库查无此优惠券";
        }
        for (AmzPromotionsCouponReMsg reMsg : couponList) {
            List<AmzPromotionsCoupon> collect1 = couponList1.stream().filter(s -> s.getId().compareTo(reMsg.getCouponId()) == 0).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(collect1)) {
                scCouponStateUpdate(msg, reMsg, collect1.get(0).getOrganizationId(), date);
            }
        }
        return "success";
    }

    public AccountWithCountry selectAccountWithCountry(Long shopId) {

        return amzPromotionsMapper.selectAccountWithCountry(shopId);
    }

    /**
     * sc渠道 coupon 消息处理
     *
     * @param msg
     * @param amzPromotionsCouponReMsg
     * @param contextId
     * @param date
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String scCouponStateUpdate(AmzPromotionsCouponsReceiveMsg msg, AmzPromotionsCouponReMsg amzPromotionsCouponReMsg, Integer contextId, Date date) {

        Integer rpaType = msg.getRpaType();
        String result = msg.getResult();
        Integer couponState = amzPromotionsCouponReMsg.getCouponState();
        StringBuilder detail = new StringBuilder();
        Integer operateType = null;
        AmzPromotionsCoupon coupon = new AmzPromotionsCoupon();
        coupon.setId(amzPromotionsCouponReMsg.getCouponId());
        coupon.setUpdatedAt(date);
        coupon.setUpdatedName("System");
        AmzPromotionsCoupons promotionsCoupons = new AmzPromotionsCoupons();
        promotionsCoupons.setId(msg.getCampaignId());
        promotionsCoupons.setUpdatedAt(date);
        promotionsCoupons.setUpdatedName("System");
        //返回的是创建SC coupon消息
        if (rpaType == 1) {
            if ("success".equalsIgnoreCase(result)) {
                promotionsCoupons.setSubShopBack(true);
                if (AmzCouponStateEnum.SUBMITTED_SC.value() == couponState) {
                    coupon.setCouponState(AmzCouponStateEnum.SUBMITTED_SC.value());
                    iAmzPromotionsCouponService.updateAmzPromotionsCoupon(coupon);
                    iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(promotionsCoupons);
                    detail.append("优惠券状态为Submitted。共尝试1次。");
                    operateType = AmzCouponOperateTypeEnum.RPA_SUBMIT_COUPON.value();
                    iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), amzPromotionsCouponReMsg.getCouponId(), null, date, "System", operateType, msg.getResult(), detail.toString(), contextId);

                } else if (AmzCouponStateEnum.NEEDS_YOUR_ATTENTION.value() == couponState) {
                    coupon.setCouponState(AmzCouponStateEnum.NEEDS_YOUR_ATTENTION.value());
                    iAmzPromotionsCouponService.updateAmzPromotionsCoupon(coupon);
                    iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(promotionsCoupons);
                    detail.append("优惠券状态为Needs Action。错误信息为：");
                    detail.append(amzPromotionsCouponReMsg.getError());
                    operateType = AmzCouponOperateTypeEnum.RPA_SUBMIT_COUPON.value();
                    iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), amzPromotionsCouponReMsg.getCouponId(), null, date, "System", operateType, msg.getResult(), detail.toString(), contextId);

                } else {
                    return "SC创建优惠券返回状态异常";
                }
                log.info("sc coupon开始清除历史数据,id为:{}",msg.getCampaignId());
                deleteCouponsCacheKey(msg.getCampaignId());
            } else {
                coupon.setCouponState(AmzCouponStateEnum.SUBMIT_EXCEPTION.value());
                coupon.setError(amzPromotionsCouponReMsg.getError());
//                coupon.setApprovalStatus(AmzPromotionApprovalEnum.APPROVING.getLabel());
//                promotionsCoupons.setApprovalStatus(AmzPromotionApprovalEnum.APPROVING.getLabel());
                iAmzPromotionsCouponService.updateAmzPromotionsCoupon(coupon);
                iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(promotionsCoupons);
                detail.append("截至操作时间10分钟后未成功，返回异常，共尝试1次。");
                if (StringUtil.isNotEmpty(msg.getDetail())) {
                    detail.append("错误信息如下:").append(msg.getDetail()).append(";");
                }
                operateType = AmzCouponOperateTypeEnum.RPA_SUBMIT_COUPON.value();
                iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), amzPromotionsCouponReMsg.getCouponId(), null, date, "System", operateType, msg.getResult(), detail.toString(), contextId);

                AmzPromotionsCoupons fromDb = amzPromotionsCouponsMapper.selectAmzPromotionsCouponsById(msg.getCampaignId());
                if (fromDb.getSubShopBack() == null || !fromDb.getSubShopBack()) {
                    log.info("创建sc coupon失败，开始清除历史数据");
                    deleteCouponsCacheKey(msg.getCampaignId());
                }else {
                    log.info("开始回滚数据，活动id为:" + msg.getCampaignId());
                    //回滚数据
                    rollBack(msg.getCampaignId(), null);
                }
                try {
                    iAmzPromotionApprovalAdviceService.couponSubmitExceptionAdvice(msg.getCampaignId(),null,msg.getDetail(),1);
                } catch (Exception e) {
                    log.error("coupon活动sc提交返回异常通知运营失败,活动id为:{},错误信息为:{}",msg.getCampaignId(), e.getMessage());
                }

            }
            //修改
        } else if (rpaType == 2) {
            if ("success".equalsIgnoreCase(result)) {
                AmzPromotionsCouponDetailFromDb couponFromDb = amzPromotionsCouponMapper.selectAmzPromotionsCouponDetailById(amzPromotionsCouponReMsg.getCouponId());
                //如果优惠券在数据库里不是complete canceled expired Failed 其中一种，就更新优惠券状态为Running
                if (couponFromDb != null && AmzCouponStateEnum.COMPLETE_VC.value() != couponFromDb.getCouponState()
                        && AmzCouponStateEnum.CANCELED.value() != couponFromDb.getCouponState() &&
                        AmzCouponStateEnum.EXPIRED.value() != couponFromDb.getCouponState() &&
                        AmzCouponStateEnum.FAILED_SC.value() != couponFromDb.getCouponState()) {
                    coupon.setCouponState(AmzCouponStateEnum.RUNNING_SC.value());
                    iAmzPromotionsCouponService.updateAmzPromotionsCoupon(coupon);
                    iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(promotionsCoupons);
                    detail.append("共尝试1次。");
                    operateType = AmzCouponOperateTypeEnum.RPA_SUBMIT_COUPON.value();
                    iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), amzPromotionsCouponReMsg.getCouponId(), null, date, "System", operateType, msg.getResult(), detail.toString(), contextId);
                }
                log.info("修改sc coupon成功，开始清除历史数据");
                deleteCouponCacheKey(amzPromotionsCouponReMsg.getCouponId(),msg.getCampaignId());
            } else {
                coupon.setCouponState(AmzCouponStateEnum.MODIFY_EXCEPTION.value());
//                coupon.setApprovalStatus(AmzPromotionApprovalEnum.APPROVING.getLabel());
//                promotionsCoupons.setApprovalStatus(AmzPromotionApprovalEnum.APPROVING.getLabel());
                iAmzPromotionsCouponService.updateAmzPromotionsCoupon(coupon);
                iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(promotionsCoupons);
                detail.append("截至操作时间10分钟后未成功，返回异常，共尝试1次。");
                if (StringUtil.isNotEmpty(msg.getDetail())) {
                    detail.append("错误信息如下:").append(msg.getDetail()).append(";");
                }
                operateType = AmzCouponOperateTypeEnum.RPA_MODIFY_COUPON.value();
                iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), amzPromotionsCouponReMsg.getCouponId(), null, date, "System", operateType, msg.getResult(), detail.toString(), contextId);

                log.info("开始回滚数据，活动id为:" + msg.getCampaignId());
                //回滚数据
                rollBack(msg.getCampaignId(), amzPromotionsCouponReMsg.getCouponId());

                try {
                    iAmzPromotionApprovalAdviceService.couponSubmitExceptionAdvice(msg.getCampaignId(),amzPromotionsCouponReMsg.getCouponId(),msg.getDetail(),2);
                } catch (Exception e) {
                    log.error("coupon优惠券修改返回修改异常通知运营失败,活动id为:{},错误信息为:{}",msg.getCampaignId(), e.getMessage());
                }
            }
            //更新
        } else if (rpaType == 3) {

            //取消
        } else if (rpaType == 4) {
            if ("success".equalsIgnoreCase(result)) {
                coupon.setCouponState(AmzCouponStateEnum.CANCELED.value());
                //更新已完结coupon的完结时间
                coupon.setCompleteDate(DateUtil.UtcToPacificDate(date));
                iAmzPromotionsCouponService.updateAmzPromotionsCoupon(coupon);
                iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(promotionsCoupons);
                detail.append("优惠券状态为Canceled。共尝试1次。");
                operateType = AmzCouponOperateTypeEnum.RPA_CANCEL_COUPON.value();
                iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), amzPromotionsCouponReMsg.getCouponId(), null, date, "System", operateType, msg.getResult(), detail.toString(), contextId);

            } else {
                coupon.setCouponState(AmzCouponStateEnum.CANCELING_EXCEPTION.value());
                iAmzPromotionsCouponService.updateAmzPromotionsCoupon(coupon);
                iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(promotionsCoupons);
                detail.append("截至操作时间10分钟后未成功，返回异常，共尝试1次。");
                if (StringUtil.isNotEmpty(msg.getDetail())) {
                    detail.append("错误信息如下:").append(msg.getDetail()).append(";");
                }
                operateType = AmzCouponOperateTypeEnum.RPA_CANCEL_COUPON.value();
                iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(msg.getCampaignId(), amzPromotionsCouponReMsg.getCouponId(), null, date, "System", operateType, msg.getResult(), detail.toString(), contextId);

                try {
                    iAmzPromotionApprovalAdviceService.couponSubmitExceptionAdvice(msg.getCampaignId(),amzPromotionsCouponReMsg.getCouponId(),msg.getDetail(),4);
                } catch (Exception e) {
                    log.error("coupon优惠券sc取消返回取消优惠券异常通知运营失败,活动id为:{},错误信息为:{}",msg.getCampaignId(), e.getMessage());
                }
            }
        } else {
        }
        return "success";
    }


    /**
     * 历史coupon消息处理
     *
     * @param message
     * @return
     */
    @Transactional
    public String couponHisMsgResolve(AmzPromotionsHisCouponsListReceiveMsg message) {

        List<AmzPromotionsHisCouponsReceiveMsg> campaignList = message.getCampaignList();
        StringBuilder result = new StringBuilder();
        if (CollectionUtil.isNotEmpty(campaignList)) {
            threadPoolTaskExecutor.execute(()->updateHisCouponDetail(campaignList));
            for (AmzPromotionsHisCouponsReceiveMsg msg : campaignList) {
                String s = couponHisMsgResolve(msg);
                if (!"success".equalsIgnoreCase(s)) {
                    result.append(s);
                }
            }
        }
        return StringUtil.isEmpty(result.toString()) ? "success" : result.toString();
    }


    private void updateHisCouponDetail(List<AmzPromotionsHisCouponsReceiveMsg> campaignList) {
        Date nowDate = DateUtils.getNowDate();
        for (AmzPromotionsHisCouponsReceiveMsg msg : campaignList) {
            try {
                if (CollectionUtil.isNotEmpty(msg.getCouponList())) {
                    if (StringUtils.isEmpty(msg.getDetail()) || "[]".equals(msg.getDetail())) {
                        iAmzPromotionsCouponService.lambdaUpdate().in(AmzPromotionsCoupon::getId, msg.getCouponList().stream().map(q -> q.getCouponId()).distinct().collect(Collectors.toList()))
                                .set(AmzPromotionsCoupon::getError, null).set(AmzPromotionsCoupon::getUpdatedAt,nowDate).update();
                    } else {
                        iAmzPromotionsCouponService.lambdaUpdate().in(AmzPromotionsCoupon::getId, msg.getCouponList().stream().map(q -> q.getCouponId()).distinct().collect(Collectors.toList()))
                                .set(AmzPromotionsCoupon::getError, msg.getDetail()).set(AmzPromotionsCoupon::getUpdatedAt,nowDate).update();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("定时更新coupon_detail失败,错误信息为:{}---msg--{}", e.getMessage(),msg);
            }
        }
    }
    /**
     * 历史coupon消息处理
     *
     * @param msg
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String couponHisMsgResolve(AmzPromotionsHisCouponsReceiveMsg msg) {

        Integer channel = msg.getChannel();
        if (msg.getCampaignId() == null || channel == null || CollectionUtil.isEmpty(msg.getCouponList())) {
            return "his_coupon活动id为空或者渠道为空或者优惠券列表为空";
        }
        if (channel == 1) {
            return vcCouponHisMsgResolve(msg);
        } else {
            return scCouponHisMsgResolve(msg);
        }
    }

    /**
     * vc渠道下历史coupon消息处理
     *
     * @param msg
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String vcCouponHisMsgResolve(AmzPromotionsHisCouponsReceiveMsg msg) {

        Integer campaignState = msg.getCampaignState();
        Long campaignId = msg.getCampaignId();
        //查询库中原有的活动和原有的优惠券
        AmzPromotionsCoupons byId = iAmzPromotionsCouponsService.selectAmzPromotionsCouponsById(campaignId);
        AmzPromotionsCoupon tempCoupon = new AmzPromotionsCoupon();
        tempCoupon.setCouponId(campaignId);
        List<AmzPromotionsCoupon> couponListByIds = amzPromotionsCouponMapper.selectAmzPromotionsCouponList(tempCoupon);
        if (byId == null) {
            return "his_vc_coupon查无此活动";
        }
        if (CollectionUtil.isEmpty(couponListByIds)) {
            return "his_vc_coupon活动查无此优惠券列表";
        }
        List<AmzPromotionsHisCouponReceiveMsg> couponList = msg.getCouponList();
        long couponIdNum = couponList.stream().filter(s -> s.getCouponId() != null).count();
        if (couponIdNum != couponList.size()) {
            return "his_vc_coupon返回的优惠券列表某些优惠券id为空";
        }
        List<Long> couponIds = couponList.stream().map(s -> s.getCouponId()).collect(Collectors.toList());
        List<AmzPromotionsCoupon> couponListByIdList = amzPromotionsCouponMapper.selectCouponListByCouponIds(couponIds.toArray(new Long[couponIds.size()]));
        if (CollectionUtil.isEmpty(couponListByIdList) || couponListByIdList.size() != couponIds.size()) {
            return "his_vc_coupon返回优惠券列表有空值";
        }
        List<Integer> campaignStateList = new ArrayList<>();
        //complete、canceled、expired、needs your attention
        campaignStateList.add(AmzCouponActiveStateEnum.COMPLETE.value());
        campaignStateList.add(AmzCouponActiveStateEnum.CANCELED.value());
        campaignStateList.add(AmzCouponActiveStateEnum.EXPIRED.value());
        campaignStateList.add(AmzCouponActiveStateEnum.NEEDS_YOUR_ATTENTION.value());

        List<Integer> couponStateList = new ArrayList<>();
        //修改中 修改异常 取消中 取消异常
        couponStateList.add(AmzCouponStateEnum.MODIFYING.value());
        couponStateList.add(AmzCouponStateEnum.MODIFY_EXCEPTION.value());
        couponStateList.add(AmzCouponStateEnum.CANCELING.value());
        couponStateList.add(AmzCouponStateEnum.CANCELING_EXCEPTION.value());
        String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", DateUtils.getNowDate());
        Date operateDate = DateUtils.parseDate(dateStr, "yyyy-MM-dd HH:mm:ss");
        //更新couponPage无条件
        updateCouponPage(couponList, operateDate,msg);
        if (couponListByIds.get(0).getCouponState() == null) {
            //库里活动状态为空，说明活动来自于同步
            updateHisCampaignAndLog(operateDate, msg, msg.getCampaignState(), byId);
            updateHisCouponAndLogFromSync(couponList, couponListByIds, operateDate);
        } else if (campaignStateList.contains(campaignState)) {
            //更新活动状态写入日志
            updateHisCampaignAndLog(operateDate, msg, msg.getCampaignState(), byId);
            //更新优惠券状态写入日志
            updateHisActiveCouponAndLog(couponList, couponListByIds, operateDate);
            //返回的活动状态为active
        } else {
            //更新优惠券状态写入日志
            updateHisActiveCouponAndLog(couponList, couponListByIds, operateDate);
            List<AmzPromotionsCoupon> couponListByIdsAfterUpdateCoupon = amzPromotionsCouponMapper.selectAmzPromotionsCouponList(tempCoupon);
            //判断活动下的优惠券的状态是否包含修改中 修改异常 取消中 取消异常,包含则更新活动状态为active含异常,否则更新活动状态为active
            if (includeState(couponListByIdsAfterUpdateCoupon, couponStateList)) {
                //更新活动状态并写入日志
                updateHisCampaignAndLog(operateDate, msg, AmzCouponActiveStateEnum.ACTIVE_HAS_EXCEPTION.value(), byId);
            } else {
                //更新活动状态并写入日志
                updateHisCampaignAndLog(operateDate, msg, AmzCouponActiveStateEnum.ACTIVE.value(), byId);
            }
        }
        return "success";
    }

    /**
     * 更新来着于同步的coupon
     *
     * @param couponList      回传的coupon列表
     * @param couponListByIds 库里原有的coupon列表
     * @param operateDate     操作时间
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateHisCouponAndLogFromSync(List<AmzPromotionsHisCouponReceiveMsg> couponList, List<AmzPromotionsCoupon> couponListByIds, Date operateDate) {

        for (AmzPromotionsHisCouponReceiveMsg cMsg : couponList) {
            couponListByIds.stream().filter(s -> cMsg.getCouponId().compareTo(s.getId()) == 0).forEach(s -> {
                log.info("his_coupon开始更新:{}", cMsg.getCouponId());
                if (cMsg.getCouponState() == AmzCouponStateEnum.COMPLETE_VC.value() || cMsg.getCouponState() == AmzCouponStateEnum.CANCELED.value() ||
                        cMsg.getCouponState() == AmzCouponStateEnum.EXPIRED.value()) {
                    updateHisCouponAndLog(operateDate, cMsg, cMsg.getCouponState(), s, true);
                } else {
                    updateHisCouponAndLog(operateDate, cMsg, cMsg.getCouponState(), s, false);
                }
                log.info("his_coupon更新完成:{}", cMsg.getCouponId());
            });
        }
    }

    /**
     * 更新couponPage
     *
     * @param couponList
     * @param operateDate
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateCouponPage(List<AmzPromotionsHisCouponReceiveMsg> couponList, Date operateDate,AmzPromotionsHisCouponsReceiveMsg primaryMsg) {
        for (AmzPromotionsHisCouponReceiveMsg msg : couponList) {
            AmzPromotionsCoupon amzPromotionsCoupon = new AmzPromotionsCoupon();
            amzPromotionsCoupon.setId(msg.getCouponId());
            amzPromotionsCoupon.setUpdatedAt(operateDate);
            amzPromotionsCoupon.setUpdatedName("System");
            amzPromotionsCoupon.setCouponPage(msg.getCouponPage());
            amzPromotionsCoupon.setError(primaryMsg.getDetail());
            log.info("定时更新返回coupon:{}的couponPage为:{}",msg.getCouponId(),msg.getCouponPage());
            if (StringUtils.isNotEmpty(msg.getCouponPage()) && msg.getCouponPage().contains("/")) {
                String couponPageId = msg.getCouponPage().substring(msg.getCouponPage().lastIndexOf("/")+1);
                log.info("定时更新返回coupon:{}的couponPageId为:{}",msg.getCouponId(),couponPageId);
                amzPromotionsCoupon.setCouponPageId(couponPageId);
            }
            iAmzPromotionsCouponService.updateAmzPromotionsCoupon(amzPromotionsCoupon);
            if (StringUtils.isEmpty(primaryMsg.getDetail()) || "[]".equals(primaryMsg.getDetail())) {
                iAmzPromotionsCouponService.lambdaUpdate().eq(AmzPromotionsCoupon::getId, amzPromotionsCoupon.getId())
                        .set(AmzPromotionsCoupon::getError,null).update();
            }
        }
    }

    /**
     * 返回活动状态为Active时，更新优惠券状态
     *
     * @param couponList      返回的优惠券列表
     * @param couponListByIds 查询的优惠券列表
     * @param operateDate     操作时间
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateHisActiveCouponAndLog(List<AmzPromotionsHisCouponReceiveMsg> couponList, List<AmzPromotionsCoupon> couponListByIds, Date operateDate) {
        for (AmzPromotionsHisCouponReceiveMsg cMsg : couponList) {
            //如果优惠券返回 complete canceled expired，就直接用返回的状态更新（如果相等不用更新）
            if (cMsg.getCouponState() == AmzCouponStateEnum.COMPLETE_VC.value() || cMsg.getCouponState() == AmzCouponStateEnum.CANCELED.value() ||
                    cMsg.getCouponState() == AmzCouponStateEnum.EXPIRED.value()) {
                couponListByIds.stream().filter(s -> cMsg.getCouponId().compareTo(s.getId()) == 0).forEach(s -> {
                    if (cMsg.getCouponState() != s.getCouponState()) {
                        log.info("his_coupon开始更新:{}", cMsg.getCouponId());
                        updateHisCouponAndLog(operateDate, cMsg, cMsg.getCouponState(), s, true);
                        log.info("his_coupon更新完成:{}", cMsg.getCouponId());
                    }
                });
                //如果优惠券返回Active，且优惠券在数据库是Needs your attention、canceled（Budget exhausted），直接更新优惠券状态为Active.
            } else if (cMsg.getCouponState() == AmzCouponStateEnum.ACTIVE_VC.value()) {
                List<AmzPromotionsCoupon> collect = couponListByIds.stream().filter(s -> cMsg.getCouponId().compareTo(s.getId()) == 0).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(collect)) {
                    AmzPromotionsCoupon c1 = collect.get(0);
                    if (c1.getCouponState() == AmzCouponStateEnum.NEEDS_YOUR_ATTENTION.value() ||
                            c1.getCouponState() == AmzCouponStateEnum.CANCELED_VC.value()) {
                        log.info("his_coupon开始更新:{}", cMsg.getCouponId());
                        updateHisCouponAndLog(operateDate, cMsg, cMsg.getCouponState(), c1, false);
                        log.info("his_coupon更新完成:{}", cMsg.getCouponId());
                    }
                }
            } else {
                log.info("his_coupon无需更新:{}", cMsg.getCouponId());
            }
        }
    }


    /**
     * 判断优惠券列表是否包含修改中 修改异常 取消中 取消异常
     *
     * @param couponList
     * @param couponStateList
     * @return
     */
    private Boolean includeState(List<AmzPromotionsCoupon> couponList, List<Integer> couponStateList) {
        for (AmzPromotionsCoupon coupon : couponList) {
            if (couponStateList.contains(coupon.getCouponState())) {
                return true;
            }
        }
        return false;
    }


    /**
     * 更新活动状态写入日志
     *
     * @param operateDate  操作日期
     * @param msg          回传的活动
     * @param updatedState 要更新的活动状态
     * @param byId         数据库查询出的活动
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateHisCampaignAndLog(Date operateDate, AmzPromotionsHisCouponsReceiveMsg msg, Integer updatedState, AmzPromotionsCoupons byId) {
        if (byId.getActiveState() != null && msg.getCampaignState() == byId.getActiveState()) {
            log.info("his_coupon活动状态未发生改变无需更新:{}", msg.getCampaignId());
            return;
        }
        log.info("his_coupon活动及日志开始更新:{}", msg.getCampaignId());
        Integer operateType = AmzCouponOperateTypeEnum.RPA_UPDATE_COUPON.value();
        String operateName = "System";
        StringBuilder detail = new StringBuilder();
        AmzPromotionsCoupons coupons = new AmzPromotionsCoupons();
        coupons.setId(msg.getCampaignId());
        coupons.setUpdatedAt(operateDate);
        coupons.setUpdatedName(operateName);
        coupons.setActiveState(updatedState);
        coupons.setCampaignId(msg.getCouponsBackId());
        detail.append("活动状态由").append(byId.getActiveState() != null ? AmzCouponActiveStateEnum.labelOf(byId.getActiveState()) : "空").append("更新为").append(AmzCouponActiveStateEnum.labelOf(coupons.getActiveState()));
        //更新活动状态写入日志
        iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(coupons);
        iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(coupons.getId(), null, null, operateDate, operateName, operateType, null, detail.toString(), byId.getOrganizationId());
        log.info("his_coupon活动及日志更新完成:{}", msg.getCampaignId());
    }


    /**
     * 更新优惠券状态写入日志
     *
     * @param operateDate 操作时间
     * @param msg         回传的coupon
     * @param couponState 要更新的状态
     * @param byId        查询的coupon
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateHisCouponAndLog(Date operateDate, AmzPromotionsHisCouponReceiveMsg msg, Integer couponState, AmzPromotionsCoupon byId, boolean needUpdateCompleteDate) {
        Integer operateType = AmzCouponOperateTypeEnum.RPA_UPDATE_COUPON.value();
        String operateName = "System";
        StringBuilder couponDetail = new StringBuilder();
        AmzPromotionsCoupon amzPromotionsCoupon = new AmzPromotionsCoupon();
        amzPromotionsCoupon.setId(msg.getCouponId());
        amzPromotionsCoupon.setUpdatedAt(operateDate);
        amzPromotionsCoupon.setUpdatedName(operateName);
        amzPromotionsCoupon.setCouponState(couponState);
        amzPromotionsCoupon.setBackId(msg.getCouponBackId());
        if (needUpdateCompleteDate) {
            if (byId.getCompleteDate() == null) {
                Date tempCompleteDate = DateUtil.UtcToPacificDate(operateDate);
                //如果券结束时间小于等于当前pst时间，则更新完结时间为结束时间，否则更新为当前pst时间
                if (DateUtil.compareDate(byId.getEndDate(), tempCompleteDate) <= 0) {
                    amzPromotionsCoupon.setCompleteDate(byId.getEndDate());
                } else {
                    amzPromotionsCoupon.setCompleteDate(tempCompleteDate);
                }
            }
        }
//        amzPromotionsCoupon.setCouponPage(msg.getCouponPage());
        discountConvert(amzPromotionsCoupon, msg);
        iAmzPromotionsCouponService.updateAmzPromotionsCoupon(amzPromotionsCoupon);
        AmzPromotionsCoupons promotionsCoupons = new AmzPromotionsCoupons();
        promotionsCoupons.setId(byId.getCouponId());
        promotionsCoupons.setUpdatedAt(operateDate);
        promotionsCoupons.setUpdatedName(operateName);
        iAmzPromotionsCouponsService.updateAmzPromotionsCoupons(promotionsCoupons);
        iAmzPromotionsCouponService.updateExpiringSoon(msg.getCouponId());
        couponDetail.append("优惠券状态由").append(byId.getCouponState() != null ? AmzCouponStateEnum.labelOf(byId.getCouponState()) : "空").append("更新为").append(AmzCouponStateEnum.labelOf(couponState));
        iAmzPromotionsCouponOperateLogService.insertCouponOperateLog(byId.getCouponId(), byId.getId(), null, operateDate, operateName, operateType, null, couponDetail.toString(), byId.getOrganizationId());
    }

    private void discountConvert(AmzPromotionsCoupon amzPromotionsCoupon, AmzPromotionsHisCouponReceiveMsg msg) {
        String discount = msg.getDiscount();
        if (StringUtil.isEmpty(discount)) {
            return;
        }
        if (discount.contains("%")) {
            amzPromotionsCoupon.setDiscountFlag("%");
            amzPromotionsCoupon.setDiscount(discount.trim().replaceAll("%", ""));
        } else {
            amzPromotionsCoupon.setDiscountFlag("$");
            amzPromotionsCoupon.setDiscount(discount.trim().replaceAll("\\$", ""));
        }
    }

    /**
     * sc渠道下历史coupon消息处理
     *
     * @param msg
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String scCouponHisMsgResolve(AmzPromotionsHisCouponsReceiveMsg msg) {

        Long campaignId = msg.getCampaignId();
        String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", DateUtils.getNowDate());
        Date operateDate = DateUtils.parseDate(dateStr, "yyyy-MM-dd HH:mm:ss");
        //查询库中原有的活动
        AmzPromotionsCoupons byId = iAmzPromotionsCouponsService.selectAmzPromotionsCouponsById(campaignId);
        if (byId == null) {
            return "his_sc_coupon查无此优惠券对应活动";
        }
        List<AmzPromotionsHisCouponReceiveMsg> couponList = msg.getCouponList();
        AmzPromotionsHisCouponReceiveMsg receiveMsg = couponList.get(0);
        if (receiveMsg.getCouponId() == null || receiveMsg.getCouponState() == null) {
            return "his_sc_coupon返回优惠券有误,id或状态为空";
        }
        AmzPromotionsCoupon coupon = iAmzPromotionsCouponService.selectAmzPromotionsCouponById(receiveMsg.getCouponId());
        if (coupon == null) {
            return "his_sc_coupon查无此优惠券";
        }
        updateCouponPage(couponList, operateDate,msg);
        //如果优惠券返回 complete canceled expired Failed ，就直接用返回的状态更新（如果相等不用更新）
        if (receiveMsg.getCouponState() == AmzCouponStateEnum.COMPLETE_VC.value() ||
                receiveMsg.getCouponState() == AmzCouponStateEnum.CANCELED.value() ||
                receiveMsg.getCouponState() == AmzCouponStateEnum.FAILED_SC.value() ||
                receiveMsg.getCouponState() == AmzCouponStateEnum.EXPIRED.value()) {
            if (coupon.getCouponState() != receiveMsg.getCouponState()) {
                log.info("his_sc_coupon开始更新:{}", receiveMsg.getCouponId());
                updateHisCouponAndLog(operateDate, receiveMsg, receiveMsg.getCouponState(), coupon, true);
                log.info("his_sc_coupon更新完成:{}", receiveMsg.getCouponId());
            }
        } else if (receiveMsg.getCouponState() == AmzCouponStateEnum.NEEDS_ACTION_SC.value() ||
                receiveMsg.getCouponState() == AmzCouponStateEnum.SUBMITTED_SC.value() ||
                receiveMsg.getCouponState() == AmzCouponStateEnum.RUNNING_SC.value()) {
            if (coupon.getCouponState() != AmzCouponStateEnum.MODIFYING.value() &&
                    coupon.getCouponState() != AmzCouponStateEnum.MODIFY_EXCEPTION.value() &&
                    coupon.getCouponState() != AmzCouponStateEnum.CANCELING.value() &&
                    coupon.getCouponState() != AmzCouponStateEnum.CANCELING_EXCEPTION.value()) {
                log.info("his_sc_coupon开始更新:{}", receiveMsg.getCouponId());
                updateHisCouponAndLog(operateDate, receiveMsg, receiveMsg.getCouponState(), coupon, false);
                log.info("his_sc_coupon更新完成:{}", receiveMsg.getCouponId());
            } else {
                log.info("his_sc_coupon无需更新:{}", receiveMsg.getCouponId());
            }
        } else {
            log.info("his_sc_coupon无需更新:{}", receiveMsg.getCouponId());
        }
        return "success";
    }

    /**
     * promotion消息处理
     *
     * @param msg
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String promotionsMsgResolve(AmzPromotionsPromotionsReceiveMsg msg) {

        Integer rpaType = msg.getRpaType();
        if (rpaType == null) {
            return "promotion_RPA类型为空";
        }
        //创建
        if (rpaType == 1) {
            return promotionsMsgCreateResolve(msg);
            //修改
        } else if (rpaType == 2) {
            return promotionsMsgModifyResolve(msg);
            //更新
        } else if (rpaType == 3) {
            return promotionsMsgUpdateResolve(msg);
            //取消
        } else if (rpaType == 4) {
            return promotionsMsgCancelResolve(msg);
        } else {
            return "promotion_RPA类型错误";
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String promotionsMsgCancelResolve(AmzPromotionsPromotionsReceiveMsg msg) {
        if (msg.getId() == null) {
            return "取消promotion_id为空";
        }
        AmzPromotions byId = amzPromotionsMapper.selectById(msg.getId());
        if (byId == null) {
            return "取消promotion查无此promotion";
        }
        String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", DateUtils.getNowDate());
        Date operateDate = DateUtils.parseDate(dateStr, "yyyy-MM-dd HH:mm:ss");
        Integer operateType = null;
        String result = msg.getResult();
        StringBuilder detail = new StringBuilder();
        AmzPromotions amzPromotions = new AmzPromotions();
        if ("success".equalsIgnoreCase(result)) {
            operateType = AmzPromotionsOperateTypeEnum.RPA_CANCEL_PROMOTION.value();
            AmzPromotionsPromotionsReceiveMsg msgCopy = new AmzPromotionsPromotionsReceiveMsg();
            BeanUtils.copyProperties(msg, msgCopy);
            msgCopy.setPromotionState(AmzPromotionsStateEnum.CANCELED.value());
            detail.append("promotion(").append(byId.getPromotionsId()).append(")状态更新为Canceled。共尝试1次。");
            log.info("rpa取消promotion开始更新");
            //更新已完结promotion的完结时间
            if (byId.getCompleteDate() == null) {
                //如果结束时间小于等于于当前pst时间，完结时间设置为结束时间，否则设置为当前pst时间
                Date tempCompleteDate = DateUtil.UtcToPacificDate(operateDate);
                if (DateUtil.compareDate(byId.getEndTime(), tempCompleteDate) <= 0) {
                    amzPromotions.setCompleteDate(byId.getEndTime());
                } else {
                    amzPromotions.setCompleteDate(tempCompleteDate);
                }
            }
            boolean needToCanceled = false;
            /*AmzPromotionsOperateLog proLog = new AmzPromotionsOperateLog();
            proLog.setPromotionId(msg.getId());
            List<AmzPromotionsOperateLog> logList = iAmzPromotionsOperateLogService.selectAmzPromotionsOperateLogList(proLog);
            if (CollectionUtil.isNotEmpty(logList)) {
                AmzPromotionsOperateLog cancelLog = logList.stream().sorted(Comparator.comparing(AmzPromotionsOperateLog::getCreatedAt).reversed()).findFirst().orElse(null);
                if (cancelLog != null && AmzPromotionsOperateTypeEnum.CANCEL_PROMOTION.value().equals(cancelLog.getOperateType()) && StringUtil.isNotEmpty(cancelLog.getDetail()) && cancelLog.getDetail().contains("状态由Needs your attention更新为取消中")) {
                    needToCanceled = true;
                }
            }*/
            //取消失败，状态由Needs your attention变成取消中，取消中变取消异常，最后变Canceled，也记录needToCanceled
            List<AmzPromotionsOperateLog> logList = iAmzPromotionsOperateLogService.list(Wrappers.lambdaQuery(AmzPromotionsOperateLog.class).eq(AmzPromotionsOperateLog::getPromotionId, msg.getId()).orderByDesc(AmzPromotionsOperateLog::getCreatedAt));
            if (CollectionUtil.isNotEmpty(logList)) {
                for (AmzPromotionsOperateLog needLog : logList) {
                    if (AmzPromotionsOperateTypeEnum.CANCEL_PROMOTION.value().equals(needLog.getOperateType())) {
                        if (StringUtil.isNotEmpty(needLog.getDetail()) && needLog.getDetail().contains("状态由Needs your attention更新为取消中")) {
                            needToCanceled = true;
                            break;
                        }
                    } else if (AmzPromotionsOperateTypeEnum.RPA_CANCEL_PROMOTION.value().equals(needLog.getOperateType())) {
                    }else {
                        break;
                    }
                }
            }
            if (AmzPromotionsStateEnum.NEED_YOUR_ATTENTION.value().equals(byId.getPromotionsState()) || needToCanceled) {
                amzPromotions.setNeedToCanceled(true);
            } else {
                amzPromotions.setNeedToCanceled(false);
            }
            updatePromotionsAndLog(msgCopy, operateDate, operateType, result, detail, amzPromotions, byId);
            iAmzPromotionAndCouponService.rpaCancelPromotionAutoSync(msg.getId());
        } else {
            operateType = AmzPromotionsOperateTypeEnum.RPA_CANCEL_PROMOTION.value();
            detail.append("截至操作时间10分钟后未成功，返回异常，共尝试1次。");
            if (StringUtil.isNotEmpty(msg.getDetail())) {
                detail.append("具体错误信息:").append(msg.getDetail());
            }
            log.info("rpa取消promotion异常开始更新");
            updatePromotionsAndLog(operateDate, operateType, result, detail, amzPromotions, AmzPromotionsStateEnum.CANCEL_EXCEPTION.value(), byId);

            //取消异常通知到对应运营
            try {
                iAmzPromotionApprovalAdviceService.promotionSubmitExceptionAdvice(msg.getId(), msg.getDetail());
            } catch (Exception e) {
                log.error("取消异常通知失败,id为:{},错误信息为:{},error为:{}", msg.getId(), msg.getDetail(), e.getMessage());
            }
        }
        iAmzPromotionsHisService.deletePromtionStateCache(msg.getId());
        return "success";
    }

    @Transactional(rollbackFor = Exception.class)
    public String promotionsMsgUpdateResolve(AmzPromotionsPromotionsReceiveMsg msg) {
        return "success";
    }

    @Transactional(rollbackFor = Exception.class)
    public String promotionsMsgModifyResolve(AmzPromotionsPromotionsReceiveMsg msg) {
        if (msg.getId() == null) {
            return "修改promotion_id为空";
        }
        if ("success".equalsIgnoreCase(msg.getResult()) && msg.getPromotionState() == null) {
            return "修改promotion成功返回状态为空";
        }
        AmzPromotions byId = amzPromotionsMapper.selectById(msg.getId());
        if (byId == null) {
            return "修改查无此promotion";
        }
        String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", DateUtils.getNowDate());
        Date operateDate = DateUtils.parseDate(dateStr, "yyyy-MM-dd HH:mm:ss");
        Integer operateType = null;
        String result = msg.getResult();
        StringBuilder detail = new StringBuilder();
        AmzPromotions amzPromotions = new AmzPromotions();
        if ("success".equalsIgnoreCase(result)) {
            operateType = AmzPromotionsOperateTypeEnum.RPA_MODIFY_PROMOTION.value();
            detail.append("promotion(").append(byId.getPromotionsId()).append(")状态更新为").append(AmzPromotionsStateEnum.labelOf(msg.getPromotionState())).append(",共尝试1次。");
            AmzPromotionsPromotionsReceiveMsg msgCopy = new AmzPromotionsPromotionsReceiveMsg();
            BeanUtils.copyProperties(msg, msgCopy);
            msgCopy.setPromotionState(msg.getPromotionState());
            log.info("rpa修改promotion开始更新");
            updatePromotionsNoLog(msgCopy, operateDate, operateType, result, detail, amzPromotions, byId);
            List<AmzPromotionsHisPromotionsSkuReceiveMsg> skuList = msg.getSkuList();
            updatePromotionCreate(skuList, msg.getId(), detail, operateDate, 2);
            iAmzPromotionsService.insertPromotionLog(byId.getId(), null, operateDate, "System", operateType, result, detail.toString(), byId.getOrganizationId());
            //清除缓存
            deletePromotionCacheKey(msg.getId());
            try {
                if (AmzPromotionsStateEnum.APPROVED_BUT_NEEDS_YOUR_ATTENTION.value().equals(msg.getPromotionState()) || AmzPromotionsStateEnum.APPROVED_BUT_NOT_FEATURED.value().equals(msg.getPromotionState())) {
                    AmzPromotions needSync = amzPromotionsMapper.selectById(msg.getId());
                    List<AmzPromotions> needSyncPromotions = new ArrayList<>();
                    needSyncPromotions.add(needSync);
                    iAmzPromotionAndCouponService.syncPromotionsInfoUpdateReturnSomeState(needSyncPromotions, "System","promotion修改返回"+ AmzPromotionsStateEnum.labelOf(msg.getPromotionState()) + "自动同步");
                }
            } catch (Exception e) {
                log.error("promotion修改返回"+ AmzPromotionsStateEnum.labelOf(msg.getPromotionState()) + "自动同步失败,{}",e.getMessage());
            }


        } else {
            operateType = AmzPromotionsOperateTypeEnum.RPA_MODIFY_PROMOTION.value();
            detail.append("截至操作时间10分钟后未成功，返回异常，共尝试1次。");
            if (StringUtil.isNotEmpty(msg.getDetail())) {
                detail.append("具体错误信息:").append(msg.getDetail());
            }
            log.info("rpa修改promotion异常开始更新");
            updatePromotionsAndLog(operateDate, operateType, result, detail, amzPromotions, AmzPromotionsStateEnum.MODIFY_EXCEPTION.value(), byId);

            log.info("开始回滚数据，promotion主键为:" + msg.getId());
            promotionRollBack(msg.getId());

            //修改异常通知到对应运营
            try {
                iAmzPromotionApprovalAdviceService.promotionSubmitExceptionAdvice(msg.getId(), msg.getDetail());
            } catch (Exception e) {
                log.error("修改异常通知失败,id为:{},错误信息为:{},error为:{}", msg.getId(), msg.getDetail(), e.getMessage());
            }
        }

        iAmzPromotionsHisService.deletePromtionStateCache(msg.getId());
        return "success";
    }


    @Transactional(rollbackFor = Exception.class)
    public String promotionsMsgCreateResolve(AmzPromotionsPromotionsReceiveMsg msg) {
        if (msg.getId() == null) {
            return "创建promotion_id为空";
        }
        AmzPromotions byId = amzPromotionsMapper.selectById(msg.getId());
        if (byId == null) {
            return "创建查无此promotion";
        }
        String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", DateUtils.getNowDate());
        Date operateDate = DateUtils.parseDate(dateStr, "yyyy-MM-dd HH:mm:ss");
        Integer operateType = null;
        String result = msg.getResult();
        Integer promotionState = msg.getPromotionState();
        String createdOn = msg.getCreatedOn();
        Date createdOnDate = null;
        StringBuilder detail = new StringBuilder();
        AmzPromotions amzPromotions = new AmzPromotions();

        if ("success".equalsIgnoreCase(result) && StringUtil.isNotEmpty(msg.getPromotionsId())) {

            try {
                if (StringUtil.isEmpty(createdOn)) {
                    createdOnDate = null;
                } else {
                    AccountWithCountry accountWithCountry = amzPromotionsMapper.selectAccountWithCountry(byId.getShopId());
                    if ("DE".equalsIgnoreCase(accountWithCountry.getCountryCode())) {
                        createdOnDate = DateUtils.parseDate(DateUtil.getDateToStr(createdOn), "yyyy-MM-dd HH:mm:ss");
                    } else {
                        createdOnDate = DateUtils.parseDate(
                                DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",
                                        DateUtils.parseDate(DateUtil.getDateStr(createdOn), "yyyy-MM-dd HH:mm:ss")),
                                "yyyy-MM-dd HH:mm:ss");
                    }
                }
            } catch (Exception e) {
                createdOnDate = null;
                e.printStackTrace();
                log.error("创建promotion--{}---返回创建时间异常--{}", msg, e.getMessage());
            }

            operateType = AmzPromotionsOperateTypeEnum.RPA_SUBMIT_PROMOTION.value();
            amzPromotions.setSubShopBack(true);
            amzPromotions.setPromotionsId(msg.getPromotionsId());
            amzPromotions.setMarketplaceId(msg.getMarketplaceId());
            amzPromotions.setPromotionsName(msg.getPromotionsName());
            amzPromotions.setCreatedAt(createdOnDate);
            amzPromotions.setFundingAgreement(stringToLong(msg.getFundingAgreement()));
            amzPromotions.setMerchandisingFee(stringToBigDecimal(msg.getMerchandisingFee()));
            if (AmzPromotionsStateEnum.APPROVED.value().equals(promotionState) || AmzPromotionsStateEnum.PENDING_APPROVAL.value().equals(promotionState)) {
                detail.append("活动状态为Approved。共尝试1次。");
                /*if (StringUtil.isNotEmpty(msg.getDetail())) {
                    detail.append("具体错误信息:").append(msg.getDetail());
                }*/
                log.info("rpa创建promotion开始更新1");
//                updatePromotionsAndLog(operateDate, operateType, result, detail, amzPromotions, AmzPromotionsStateEnum.APPROVED.value(), byId);
                updatePromotionsNoLog(operateDate, amzPromotions, AmzPromotionsStateEnum.APPROVED.value(), byId);

            } else if (AmzPromotionsStateEnum.NEED_YOUR_ATTENTION.value().equals(promotionState)) {
                detail.append("活动状态为Needs your attention。共尝试1次。");
                /*if (StringUtil.isNotEmpty(msg.getDetail())) {
                    detail.append("具体错误信息:").append(msg.getDetail());
                }*/
                log.info("rpa创建promotion开始更新2");
//                updatePromotionsAndLog(operateDate, operateType, result, detail, amzPromotions, AmzPromotionsStateEnum.NEED_YOUR_ATTENTION.value(), byId);
                updatePromotionsNoLog(operateDate, amzPromotions, AmzPromotionsStateEnum.NEED_YOUR_ATTENTION.value(), byId);

            } else if (AmzPromotionsStateEnum.PROCESSING.value().equals(promotionState)) {
                detail.append("活动状态为Processing。共尝试1次。");
               /* if (StringUtil.isNotEmpty(msg.getDetail())) {
                    detail.append("具体错误信息:").append(msg.getDetail());
                }*/
                log.info("rpa创建promotion开始更新3");
//                updatePromotionsAndLog(operateDate, operateType, result, detail, amzPromotions, AmzPromotionsStateEnum.PROCESSING.value(), byId);
                updatePromotionsNoLog(operateDate, amzPromotions, AmzPromotionsStateEnum.PROCESSING.value(), byId);
            } else {
                throw new ErpCommonException("创建promotion状态返回异常");
            }
            List<AmzPromotionsHisPromotionsSkuReceiveMsg> skuList = msg.getSkuList();
            updatePromotionCreate(skuList, msg.getId(), detail, operateDate, 1);
            if (StringUtil.isNotEmpty(msg.getDetail())) {
                detail.append("具体错误信息:").append(msg.getDetail());
            }
            iAmzPromotionsService.insertPromotionLog(byId.getId(), null, operateDate, "System", operateType, result, detail.toString(), byId.getOrganizationId());

            //清除缓存
            deletePromotionCacheKey(msg.getId());
            try {
                if (promotionState != null && !AmzPromotionsStateEnum.NEED_YOUR_ATTENTION.value().equals(promotionState)) {
                    AmzPromotions needSync = amzPromotionsMapper.selectById(msg.getId());
                    List<AmzPromotions> needSyncPromotions = new ArrayList<>();
                    needSyncPromotions.add(needSync);
                    iAmzPromotionAndCouponService.syncPromotionsNoConditionSleep(needSyncPromotions, "System","promotion创建非Needs your attention状态自动同步");
                }
            } catch (Exception e) {
                log.error("promotion创建非Needs your attention状态自动同步失败,{}",e.getMessage());
            }


        } else {
            operateType = AmzPromotionsOperateTypeEnum.RPA_SUBMIT_PROMOTION.value();
            detail.append("截至操作时间10分钟后未成功，返回异常，共尝试1次。");
            if (StringUtil.isNotEmpty(msg.getDetail())) {
                detail.append("具体错误信息:").append(msg.getDetail());
            }
            log.info("rpa创建promotion返回异常开始更新");
            if ("success".equals(result) && StringUtil.isEmpty(msg.getPromotionsId())) {
                result = "fail";
            }
            if (StringUtil.isNotEmpty(msg.getDetail()) && msg.getDetail().toLowerCase().contains("this promotion needs your attention")) {
                updatePromotionsAndLog(operateDate, operateType, result, detail, amzPromotions, AmzPromotionsStateEnum.SUBMIT_FAIL.value(), byId);
            }else {
                updatePromotionsAndLog(operateDate, operateType, result, detail, amzPromotions, AmzPromotionsStateEnum.SUBMIT_EXCEPTION.value(), byId);
            }

            //店铺后台不存在不回滚数据，只清除历史数据
            if (byId.getSubShopBack() == null || !byId.getSubShopBack()) {
                log.info("创建失败返回异常，清除历史数据，不回滚数据");
                //清除缓存
                deletePromotionCacheKey(msg.getId());
            }else {
                log.info("开始回滚数据，promotion主键为:" + msg.getId());
                promotionRollBack(msg.getId());
            }
            //提交异常或提交失败通知到对应运营
            try {
                iAmzPromotionApprovalAdviceService.promotionSubmitExceptionAdvice(msg.getId(), msg.getDetail());
            } catch (Exception e) {
                log.error("提交异常或提交失败通知失败,id为:{},错误信息为:{},error为:{}", msg.getId(), msg.getDetail(), e.getMessage());
            }
        }

        return "success";
    }


    /**
     * promotion创建完成后更新售价折扣价等信息
     *
     * @param skuList
     * @param promotionId
     * @param detail
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePromotionCreate(List<AmzPromotionsHisPromotionsSkuReceiveMsg> skuList, Long promotionId, StringBuilder detail, Date date, Integer createOrUpdate) {
        if (CollectionUtil.isNotEmpty(skuList)) {
            for (AmzPromotionsHisPromotionsSkuReceiveMsg msg : skuList) {
                String asinID = msg.getAsin();
                List<AmzPromotionsSkuDetail> detailList = iAmzPromotionsSkuDetailService.list(new LambdaQueryWrapper<AmzPromotionsSkuDetail>()
                        .eq(AmzPromotionsSkuDetail::getPromotionsId, promotionId)
                        .eq(AmzPromotionsSkuDetail::getAsin, asinID));
                if (CollectionUtil.isNotEmpty(detailList)) {
                    for (AmzPromotionsSkuDetail skuDetail1 : detailList) {
                        addHisPromotionSkuUpdateLog(skuDetail1.getAsin(), skuDetail1.getPrice(), msg.getPrice(), "售价", detail);
                        addHisPromotionSkuUpdateLog(skuDetail1.getAsin(), skuDetail1.getFrontPrice(), msg.getFrontPrice(), "前台价格", detail);
                        addHisPromotionSkuUpdateLog(skuDetail1.getAsin(), skuDetail1.getDiscountPrice(), msg.getLikelyPromotionPrice(), "折后价", detail);
                        if (createOrUpdate == 1) {
                            //记录后台第一次创建成功后的前台价格，折后价
                            addHisPromotionSkuUpdateLog(skuDetail1.getAsin(), skuDetail1.getOriginalFrontPrice(), msg.getFrontPrice(), "初始前台价格", detail);
                            addHisPromotionSkuUpdateLog(skuDetail1.getAsin(), skuDetail1.getOriginalDiscountPrice(), msg.getLikelyPromotionPrice(), "初始折后价", detail);
                            skuDetail1.setOriginalFrontPrice(stringToBigDecimal(msg.getFrontPrice()));
                            skuDetail1.setOriginalDiscountPrice(stringToBigDecimal(msg.getLikelyPromotionPrice()));
                        }
                        skuDetail1.setUpdatedAt(date);
                        skuDetail1.setUpdatedName("System");
                        skuDetail1.setPrice(stringToBigDecimal(msg.getPrice()));
                        skuDetail1.setFrontPrice(stringToBigDecimal(msg.getFrontPrice()));
                        skuDetail1.setDiscountPrice(stringToBigDecimal(msg.getLikelyPromotionPrice()));
                        addPerFundUnit(detail,skuDetail1,msg);
                        log.info("更新promotion的sku开始:{}", skuDetail1);
                        Boolean hasDiscountPriceWarn = skuDetail1.getDiscountPriceWarn();
                        if (skuDetail1.getDiscountPrice() != null && skuDetail1.getExpectedDiscountPrice() != null && skuDetail1.getDiscountPrice().compareTo(skuDetail1.getExpectedDiscountPrice()) != 0) {
                            skuDetail1.setDiscountPriceWarn(true);
                            if (hasDiscountPriceWarn != null && !hasDiscountPriceWarn) {
                                detail.append("折后价预警由否更新为是;");
                            }
                        }else{
                            skuDetail1.setDiscountPriceWarn(false);
                            if (hasDiscountPriceWarn != null && hasDiscountPriceWarn) {
                                detail.append("折后价预警由是更新为否;");
                            }
                        }
                        iAmzPromotionsSkuDetailService.updateById(skuDetail1);
                        updateSkuDailyDetail(skuDetail1);
                    }
                }
            }
        } else {
            log.info("promotion创建完成后sku列表为空:{}", promotionId);
        }
    }


    /**
     * 更新promotions
     *
     * @param operateDate         操作时间
     * @param msg                 回传的promotions
     * @param operateType         操作类型
     * @param result              结果
     * @param detail              详情
     * @param amzPromotions       待更新的promotions
     * @param byId                数据库中原有的promotions
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePromotionsAndLog(AmzPromotionsPromotionsReceiveMsg msg, Date operateDate, Integer operateType, String result, StringBuilder detail, AmzPromotions amzPromotions, AmzPromotions byId) {

        Integer promotionState = msg.getPromotionState();
        updatePromotionsAndLog(operateDate, operateType, result, detail, amzPromotions, promotionState, byId);

    }

    @Transactional(rollbackFor = Exception.class)
    public void updatePromotionsNoLog(AmzPromotionsPromotionsReceiveMsg msg, Date operateDate, Integer operateType, String result, StringBuilder detail, AmzPromotions amzPromotions, AmzPromotions byId) {

        Integer promotionState = msg.getPromotionState();
        String operateName = "System";
        amzPromotions.setId(byId.getId());
        amzPromotions.setUpdatedAt(operateDate);
        amzPromotions.setUpdatedName(operateName);
        amzPromotions.setPromotionsState(promotionState);
        amzPromotionsMapper.updateById(amzPromotions);


    }

    /**
     * 更新promotions
     *
     * @param operateDate     操作时间
     * @param operateType     操作类型
     * @param result          结果
     * @param detail          详情
     * @param amzPromotions   待更新的promotions
     * @param promotionsState 促销状态
     * @param byId            数据库中原有的promotions
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePromotionsAndLog(Date operateDate, Integer operateType, String result, StringBuilder detail, AmzPromotions amzPromotions, Integer promotionsState, AmzPromotions byId) {
        String operateName = "System";
        amzPromotions.setId(byId.getId());
        amzPromotions.setUpdatedAt(operateDate);
        amzPromotions.setUpdatedName(operateName);
        amzPromotions.setPromotionsState(promotionsState);
        amzPromotionsMapper.updateById(amzPromotions);
        iAmzPromotionsService.insertPromotionLog(byId.getId(), null, operateDate, operateName, operateType, result, detail.toString(), byId.getOrganizationId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void updatePromotionsNoLog(Date operateDate, AmzPromotions amzPromotions, Integer promotionsState, AmzPromotions byId) {
        String operateName = "System";
        amzPromotions.setId(byId.getId());
        amzPromotions.setUpdatedAt(operateDate);
        amzPromotions.setUpdatedName(operateName);
        amzPromotions.setPromotionsState(promotionsState);
        amzPromotionsMapper.updateById(amzPromotions);

    }

    /**
     * 历史promotions消息处理
     *
     * @param promotionsMsg
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String promotionsHisMsgResolve(AmzPromotionsHisPromotionsListReceiveMsg promotionsMsg) {

        List<Integer> promotionsStateList = new ArrayList<>();
        promotionsStateList.add(AmzPromotionsStateEnum.MODIFYING.value());
        promotionsStateList.add(AmzPromotionsStateEnum.MODIFY_EXCEPTION.value());
        promotionsStateList.add(AmzPromotionsStateEnum.CANCELING.value());
        promotionsStateList.add(AmzPromotionsStateEnum.CANCEL_EXCEPTION.value());
        List<AmzPromotionsHisPromotionsReceiveMsg> promotionList = promotionsMsg.getPromotionList();
        if (CollectionUtil.isNotEmpty(promotionList)) {
            for (AmzPromotionsHisPromotionsReceiveMsg receiveMsg : promotionList) {
                promotionsHisMsgResolve(receiveMsg, promotionsStateList);
            }
            return "success";
        } else {
            return "promotion列表为空";
        }
    }

    /**
     * promotion历史消息处理
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String promotionsHisMsgResolve(AmzPromotionsHisPromotionsReceiveMsg promotionsMsg, List<Integer> promotionsStateList) {
        String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", DateUtils.getNowDate());
        Date operateDate = DateUtils.parseDate(dateStr, "yyyy-MM-dd HH:mm:ss");
        Integer operateType = AmzPromotionsOperateTypeEnum.RPA_UPDATE_PROMOTION.value();
        StringBuilder detail = new StringBuilder();
        String promotionId = promotionsMsg.getPromotionID();
        if (StringUtil.isEmpty(promotionId)) {
            throw new ErpCommonException("his_promotionId为空");
        }
        AmzPromotions byId = iAmzPromotionsService.getOne(new LambdaQueryWrapper<AmzPromotions>().eq(AmzPromotions::getPromotionsId, promotionId).last("limit 1"));
        if (byId == null) {
            throw new ErpCommonException("his无此promotion");
        }else {
            try {
                iAmzPromotionApprovalAdviceService.promotionExceptionListener(promotionsMsg, byId, 1);
            } catch (Exception e) {
                log.error("RPA定时更新返回消息通知失败:{}", e.getMessage());
            }
        }
        AmzPromotions amzPromotions = new AmzPromotions();
        if (StringUtil.isNotEmpty(promotionsMsg.getBeginTime())) {
            try {
                amzPromotions.setBeginTime(DateUtils.parseDate(DateUtil.getDateStr(promotionsMsg.getBeginTime()), "yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                amzPromotions.setBeginTime(DateUtils.parseDate(promotionsMsg.getBeginTime(), "yyyy-MM-dd HH:mm:ss"));
            }
        }
        if (StringUtil.isNotEmpty(promotionsMsg.getEndTime())) {
            try {
                amzPromotions.setEndTime(DateUtils.parseDate(DateUtil.getDateStr(promotionsMsg.getEndTime()), "yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                amzPromotions.setEndTime(DateUtils.parseDate(promotionsMsg.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
            }
        }
        amzPromotions.setId(byId.getId());
        if (StringUtils.isNotEmpty(promotionsMsg.getMarketplaceId())) {
            amzPromotions.setMarketplaceId(promotionsMsg.getMarketplaceId());
        }

        amzPromotions.setPromotionsState(promotionsMsg.getPromotionState());
        amzPromotions.setGlanceViews(stringToLong(promotionsMsg.getGlanceViews()));
        amzPromotions.setUnitsSold(stringToLong(promotionsMsg.getUnitsSold()));
        amzPromotions.setBudget(stringToBigDecimal(promotionsMsg.getAmountSpent()));
        amzPromotions.setRevenue(stringToBigDecimal(promotionsMsg.getRevenue()));
        amzPromotions.setFundingAgreement(stringToLong(promotionsMsg.getFundingAgreement()));
        if (byId.getPromotionsState() == null) {
            detail.append("promotion(").append(promotionsMsg.getPromotionID()).append(")状态由")
                    .append("空").
                    append("更新为").append(AmzPromotionsStateEnum.labelOf(promotionsMsg.getPromotionState())).append("。");
            //promotion返回状态为canceled或者Approved and ended时，更新完结时间
            if (AmzPromotionsStateEnum.CANCELED.value().equals(promotionsMsg.getPromotionState()) || AmzPromotionsStateEnum.APPROVED_AND_ENDED.value().equals(promotionsMsg.getPromotionState())) {
                if (byId.getCompleteDate() == null) {
                    //如果结束时间小于等于当前pst时间，则设置完结时间为结束时间，否则设置当前pst时间
                    Date tempCompleteDate = DateUtil.UtcToPacificDate(operateDate);
                    Date actureEndTime = amzPromotions.getEndTime() == null ? byId.getEndTime() : amzPromotions.getEndTime();
                    if (DateUtil.compareDate(actureEndTime, tempCompleteDate) <= 0) {
                        amzPromotions.setCompleteDate(actureEndTime);
                    } else {
                        amzPromotions.setCompleteDate(tempCompleteDate);
                    }
                }
            }
        }
        if (byId.getPromotionsState() != null && byId.getPromotionsState().compareTo(promotionsMsg.getPromotionState()) != 0) {
            if (AmzPromotionsStateEnum.CANCELED.value().equals(byId.getPromotionsState()) || AmzPromotionsStateEnum.APPROVED_AND_ENDED.value().equals(byId.getPromotionsState())) {
                detail.append("promotion(").append(promotionsMsg.getPromotionID()).append("状态已经处于完结状态无需更新");
            }else {
                detail.append("promotion(").append(promotionsMsg.getPromotionID()).append(")状态由")
                        .append(AmzPromotionsStateEnum.labelOf(byId.getPromotionsState())).
                        append("更新为").append(AmzPromotionsStateEnum.labelOf(promotionsMsg.getPromotionState())).append("。");
            }
            //promotion返回状态为canceled或者Approved and ended时，更新完结时间
            if (AmzPromotionsStateEnum.CANCELED.value().equals(promotionsMsg.getPromotionState()) || AmzPromotionsStateEnum.APPROVED_AND_ENDED.value().equals(promotionsMsg.getPromotionState())) {
                if (byId.getCompleteDate() == null) {
                    //如果结束时间小于等于当前pst时间，则设置完结时间为结束时间，否则设置当前pst时间
                    Date tempCompleteDate = DateUtil.UtcToPacificDate(operateDate);
                    Date actureEndTime = amzPromotions.getEndTime() == null ? byId.getEndTime() : amzPromotions.getEndTime();
                    if (DateUtil.compareDate(actureEndTime, tempCompleteDate) <= 0) {
                        amzPromotions.setCompleteDate(actureEndTime);
                    } else {
                        amzPromotions.setCompleteDate(tempCompleteDate);
                    }
                }
            }
        }
        if (AmzPromotionsStateEnum.NEED_YOUR_ATTENTION.value().equals(byId.getPromotionsState()) && AmzPromotionsStateEnum.CANCELED.value().equals(promotionsMsg.getPromotionState())) {
            amzPromotions.setNeedToCanceled(true);
        }else {
            amzPromotions.setNeedToCanceled(false);
        }
        if (StringUtil.isNotEmpty(promotionsMsg.getDetail())) {
            detail.append("错误信息如下:").append(promotionsMsg.getDetail()).append(";");
        }
        addHisPromotionLongTypeUpdateLog(promotionsMsg.getPromotionID(), byId.getFundingAgreement(), stringToLong(promotionsMsg.getFundingAgreement()), "fundingAgreement", detail);
        List<AmzPromotionsHisPromotionsSkuReceiveMsg> skuList = promotionsMsg.getSkuList();
        if (CollectionUtil.isNotEmpty(skuList)) {
            for (AmzPromotionsHisPromotionsSkuReceiveMsg msg : skuList) {
                String asinID = msg.getAsin();

                List<AmzPromotionsSkuDetail> detailList = iAmzPromotionsSkuDetailService.list(new LambdaQueryWrapper<AmzPromotionsSkuDetail>().eq(AmzPromotionsSkuDetail::getPromotionsId, amzPromotions.getId()).eq(AmzPromotionsSkuDetail::getAsin, asinID));
                if (CollectionUtil.isNotEmpty(detailList)) {
                    for (AmzPromotionsSkuDetail skuDetail1 : detailList) {
                        addHisPromotionSkuUpdateLog(skuDetail1.getAsin(), skuDetail1.getPrice(), msg.getPrice(), "售价", detail);
                        addHisPromotionSkuUpdateLog(skuDetail1.getAsin(), skuDetail1.getFrontPrice(), msg.getFrontPrice(), "前台价格", detail);
                        addHisPromotionSkuUpdateLog(skuDetail1.getAsin(), skuDetail1.getDiscountPrice(), msg.getLikelyPromotionPrice(), "折后价", detail);

                        skuDetail1.setPrice(stringToBigDecimal(msg.getPrice()));
                        skuDetail1.setFrontPrice(stringToBigDecimal(msg.getFrontPrice()));
                        skuDetail1.setDiscountPrice(stringToBigDecimal(msg.getLikelyPromotionPrice()));
                        addPerFundUnit(detail,skuDetail1,msg);
                        Boolean hasDiscountPriceWarn = skuDetail1.getDiscountPriceWarn();
                        if (skuDetail1.getDiscountPrice() != null && skuDetail1.getExpectedDiscountPrice() != null && skuDetail1.getDiscountPrice().compareTo(skuDetail1.getExpectedDiscountPrice()) != 0) {
                            skuDetail1.setDiscountPriceWarn(true);
                            if (hasDiscountPriceWarn != null && !hasDiscountPriceWarn) {
                                detail.append("折后价预警由否更新为是;");
                            }

                        }else{
                            skuDetail1.setDiscountPriceWarn(false);
                            if (hasDiscountPriceWarn != null && hasDiscountPriceWarn) {
                                detail.append("折后价预警由是更新为否;");
                            }
                        }
                        //todo 判断数量是否跟新
                        if (!Objects.equals(msg.getNum(),skuDetail1.getNum())) {
                            detail.append("数量有跟新;");
                            skuDetail1.setNum(msg.getNum());
                        }
                        //todo 判断销量是否跟新
                        if(!Objects.equals(msg.getUnitsSold(),skuDetail1.getUnitsSold())) {
                            detail.append("销量有跟新;");
                            skuDetail1.setUnitsSold(msg.getUnitsSold());
                        }
                        log.info("his更新promotion的sku开始:{}", skuDetail1);
                        if (StringUtil.isNotEmpty(detail.toString())) {
                            iAmzPromotionsSkuDetailService.updateById(skuDetail1);
                        }
                        updateSkuDailyDetail(skuDetail1);
                    }
                }
            }
        } else {
            throw new ErpCommonException("hispromotion_sku列表为空");
        }



        if (StringUtil.isNotEmpty(detail.toString())) {
            updateHisPromotionsAndLog(promotionsStateList, promotionsMsg, operateDate, operateType, null, detail, amzPromotions, byId);
        }
        return "success";
    }

    @Transactional(rollbackFor = Exception.class)
    public int updateSkuDailyDetail(AmzPromotionsSkuDetail skuDetail) {
        Date nowDate = DateUtils.getNowDate();
        List<AmzPromotionsDailyDetail> nowDayDailyDetails = amzPromotionsDailyDetailMapper.selectAmzPromotionsDailyDetailList(skuDetail.getPromotionsId(), DateUtil.UtcToPacificDate(nowDate), DateUtil.UtcToPacificDate(nowDate), new String[]{skuDetail.getAsin()});
        AmzPromotionsDailyDetail detail = new AmzPromotionsDailyDetail();
        detail.setPrice(skuDetail.getPrice());
        detail.setFrontPrice(skuDetail.getFrontPrice());
        detail.setDiscountPrice(skuDetail.getDiscountPrice());
//        detail.setPerFunding(skuDetail.getPerFunding());
        if (CollectionUtil.isEmpty(nowDayDailyDetails)) {
            detail.setDate(DateUtil.UtcToPacificDate(nowDate));
            detail.setAsin(skuDetail.getAsin());
            detail.setSku(skuDetail.getSellerSku());
            detail.setTitle(skuDetail.getTitle());
            detail.setRevenue(skuDetail.getRevenue());
            detail.setSpent(skuDetail.getAmountSpent());
            detail.setUnitsSold(skuDetail.getUnitsSold() != null ? Long.valueOf(skuDetail.getUnitsSold()) : null);
            detail.setOrganizationId(skuDetail.getOrganizationId());
            detail.setFlag("$");
            detail.setCreatedAt(nowDate);
            detail.setCreatedName("System");
            detail.setUpdatedAt(nowDate);
            detail.setUpdatedName("System");
            detail.setPromotionId(skuDetail.getPromotionsId());
            detail.setSkuDetailId(skuDetail.getId());
            log.info("his新增promotion每日明细开始:{}", detail);
            return amzPromotionsDailyDetailMapper.insertAmzPromotionsDailyDetail(detail);
        } else {
            detail.setId(nowDayDailyDetails.get(0).getId());
            log.info("his更新promotion每日明细开始:{}", detail);
            return amzPromotionsDailyDetailMapper.updateAmzPromotionsDailyDetail(detail);
        }
    }

    private void addHisPromotionSkuUpdateLog(String asin, BigDecimal originalData, String updateData, String column, StringBuilder detail) {
        if (originalData != null && StringUtil.isNotEmpty(updateData)) {
            String s = updateData.replaceAll("[^\\d.]", "").replaceAll(",", "");
            if (originalData.compareTo(new BigDecimal(s)) != 0) {
                detail.append(asin).append(":").append(column).append("由").append(originalData).append("更新为").append(s).append(";");
            }
        } else if (originalData == null && StringUtil.isNotEmpty(updateData)) {
            String s = updateData.replaceAll("[^\\d.]", "").replaceAll(",", "");
            detail.append(asin).append(":").append(column).append("由").append("空").append("更新为").append(s).append(";");
        } else if (originalData != null && StringUtil.isEmpty(updateData)) {
            detail.append(asin).append(":").append(column).append("由").append(originalData).append("更新为").append("空").append(";");
        } else {
        }
    }

    /**
     * @param promotionName promotion活动名
     * @param originalData  库中原始数据
     * @param updateData    更新后数据
     * @param column        更新字段
     * @param detail
     */
    private void addHisPromotionLongTypeUpdateLog(String promotionName, Long originalData, Long updateData, String column, StringBuilder detail) {

        if (originalData != null && updateData != null) {
            if (originalData.compareTo(updateData) != 0) {
                detail.append(promotionName).append(":").append(column).append("由").append(originalData).append("更新为").append(updateData).append(";");
            }
        } else if (originalData != null) {
            detail.append(promotionName).append(":").append(column).append("由").append(originalData).append("更新为").append("空").append(";");
        } else if (updateData != null) {
            detail.append(promotionName).append(":").append(column).append("由").append("空").append("更新为").append(updateData).append(";");
        } else {

        }
    }

    /**
     * 更新历史promotions
     *
     * @param promotionsStateList
     * @param msg
     * @param operateDate
     * @param operateType
     * @param result
     * @param detail
     * @param amzPromotions
     * @param byId
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateHisPromotionsAndLog(List<Integer> promotionsStateList, AmzPromotionsHisPromotionsReceiveMsg msg, Date operateDate, Integer operateType, String result, StringBuilder detail, AmzPromotions amzPromotions, AmzPromotions byId) {

        Integer promotionState = 0;
        if (AmzPromotionsStateEnum.CANCELED.value().equals(byId.getPromotionsState()) || AmzPromotionsStateEnum.APPROVED_AND_ENDED.value().equals(byId.getPromotionsState())) {
            promotionState = byId.getPromotionsState();
        }else {
            promotionState = msg.getPromotionState();
        }
        if (AmzPromotionsStateEnum.CANCELED.value() == promotionState) {
            log.info("his_promotion开始更新");
//            detail.append("promotion(").append(msg.getPromotionName()).append(")状态更新为Canceled。共尝试1次。");
            updatePromotionsAndLog(operateDate, operateType, result, detail, amzPromotions, promotionState, byId);
        } else {
            if (!promotionsStateList.contains(byId.getPromotionsState())) {
                log.info("his_promotion开始更新");
                updatePromotionsAndLog(operateDate, operateType, result, detail, amzPromotions, promotionState, byId);
            }
        }
    }


    public BigDecimal stringToBigDecimal(String number) {
        BigDecimal t = null;
        if (StringUtil.isNotEmpty(number)) {
            String s = number.replaceAll(",", "").replaceAll("[^\\d.]","").trim();
            t = StringUtil.isEmpty(s) ? null : new BigDecimal(s);
        }
        return t;
    }

    public Long stringToLong(String number) {
        Long t = null;
        if (StringUtil.isNotEmpty(number)) {
            String s = number.replaceAll(",", "").replaceAll("[^\\d.]","").trim();
            if (StringUtil.isNotEmpty(s)) {
                if (s.contains(".")) {
                    String substring = s.substring(0, s.indexOf("."));
                    return Long.valueOf(substring);
                } else {
                    return Long.valueOf(s);
                }
            }
        }
        return t;
    }


    /**
     * 回滚数据
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void rollBack(Long id, Long couponId) {
        AmzPromotionsApproval amzPromotionsApproval = new AmzPromotionsApproval();
        amzPromotionsApproval.setIds(new Long[]{id});
        amzPromotionsApproval.setApprovalStatus(AmzPromotionApprovalEnum.RETURN.getLabel());
        amzPromotionsApproval.setOperationUserId(-1);
        amzPromotionsApproval.setOperationUserName("System");
        amzPromotionsApproval.setDate(DateUtils.getNowDate());
        amzPromotionsApproval.setViewOrRpa(false);
        String activeApprovalTypeKey = AmzPromotionsCouponConstants.COUPONS_CREATE_REDIS_KEY + ":" + id;
        String couponApprovalTypeKey = AmzPromotionsCouponConstants.COUPON_MODIFY_REDIS_KEY + ":" + couponId;
        //提交异常直接提交不回滚
        AmzPromotionsCouponsHis activeApprovalTypeObject = amzPromotionsCouponsHisMapper.selectOne(Wrappers.<AmzPromotionsCouponsHis>lambdaQuery().eq(AmzPromotionsCouponsHis::getActiveApprovalTypeKey, activeApprovalTypeKey));
        AmzPromotionsCouponsHis couponApprovalTypeKeyObject = amzPromotionsCouponsHisMapper.selectOne(Wrappers.<AmzPromotionsCouponsHis>lambdaQuery().eq(AmzPromotionsCouponsHis::getActiveApprovalTypeKey, couponApprovalTypeKey));
        if (activeApprovalTypeObject != null || couponApprovalTypeKeyObject != null) {
            iAmzPromotionsApprovalService.couponApproval(amzPromotionsApproval);
        }
    }


    /**
     * 回滚数据
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void promotionRollBack(Long id) {
        AmzPromotionsApproval amzPromotionsApproval = new AmzPromotionsApproval();
        amzPromotionsApproval.setIds(new Long[]{id});
        amzPromotionsApproval.setApprovalStatus(AmzPromotionApprovalEnum.RETURN.getLabel());
        amzPromotionsApproval.setOperationUserId(-1);
        amzPromotionsApproval.setOperationUserName("System");
        amzPromotionsApproval.setDate(DateUtils.getNowDate());
        amzPromotionsApproval.setViewOrRpa(false);
        String promotionApprovalTypeKey = AmzPromotionsCouponConstants.PROMOTION_CREATE_OR_UPDATE_APPROVAL_TYPE_KEY + ":" + id;
        //提交异常直接提交不回滚
        AmzPromotionsHis promotionApprovalTypeObject = amzPromotionsHisMapper.selectOne(Wrappers.<AmzPromotionsHis>lambdaQuery().eq(AmzPromotionsHis::getPromotionApprovalTypeKey, promotionApprovalTypeKey));
        if (promotionApprovalTypeObject != null) {
            iAmzPromotionsApprovalService.promotionApproval(amzPromotionsApproval);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteCouponsCacheKey(Long couponsId) {
        String activeApprovalTypeKey = AmzPromotionsCouponConstants.COUPONS_CREATE_REDIS_KEY + ":" + couponsId;
        String activeObjectKey = AmzPromotionsCouponConstants.COUPONS_SAVE_REDIS_KEY + ":" + couponsId;
        String couponListKey = AmzPromotionsCouponConstants.COUPON_EXCEPTION_SAVE_REDIS_KEY + ":" + couponsId;
        AmzPromotionsCouponsHis activeApprovalTypeObject = amzPromotionsCouponsHisMapper.selectOne(Wrappers.<AmzPromotionsCouponsHis>lambdaQuery().eq(AmzPromotionsCouponsHis::getActiveApprovalTypeKey, activeApprovalTypeKey));
        AmzPromotionsCouponsHis activeObject = amzPromotionsCouponsHisMapper.selectOne(Wrappers.<AmzPromotionsCouponsHis>lambdaQuery().eq(AmzPromotionsCouponsHis::getActiveObjectKey, activeObjectKey));
        List<AmzPromotionsCouponsHis> couponsHisList = amzPromotionsCouponsHisMapper.selectList(Wrappers.<AmzPromotionsCouponsHis>lambdaQuery().eq(AmzPromotionsCouponsHis::getCouponListKey, couponListKey));
        List<Long> deleteIdList = new ArrayList<>();
        if (activeApprovalTypeObject != null) {
            deleteIdList.add(activeApprovalTypeObject.getId());
        }
        if (activeObject != null) {
            deleteIdList.add(activeObject.getId());
        }
        if (CollectionUtil.isNotEmpty(couponsHisList)) {
            List<Long> collect = couponsHisList.stream().map(AmzPromotionsCouponsHis::getId).collect(Collectors.toList());
            deleteIdList.addAll(collect);
        }
        if (CollectionUtil.isNotEmpty(deleteIdList)) {
            amzPromotionsCouponsHisMapper.deleteBatchIds(deleteIdList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteCouponCacheKey(Long couponId, Long couponsId) {
        String activeApprovalTypeKey = AmzPromotionsCouponConstants.COUPON_MODIFY_REDIS_KEY + ":" + couponId;
        String activeObjectKey = AmzPromotionsCouponConstants.COUPONS_SAVE_REDIS_KEY + ":" + couponsId;
        String couponListKey = AmzPromotionsCouponConstants.COUPON_SAVE_REDIS_KEY + ":" + couponId;
        AmzPromotionsCouponsHis activeApprovalTypeObject = amzPromotionsCouponsHisMapper.selectOne(Wrappers.<AmzPromotionsCouponsHis>lambdaQuery().eq(AmzPromotionsCouponsHis::getActiveApprovalTypeKey, activeApprovalTypeKey));
        AmzPromotionsCouponsHis activeObject = amzPromotionsCouponsHisMapper.selectOne(Wrappers.<AmzPromotionsCouponsHis>lambdaQuery().eq(AmzPromotionsCouponsHis::getActiveObjectKey, activeObjectKey));
        AmzPromotionsCouponsHis couponListKeyObject = amzPromotionsCouponsHisMapper.selectOne(Wrappers.<AmzPromotionsCouponsHis>lambdaQuery().eq(AmzPromotionsCouponsHis::getCouponListKey, couponListKey));
        List<Long> deleteIdList = new ArrayList<>();
        if (activeApprovalTypeObject != null) {
            deleteIdList.add(activeApprovalTypeObject.getId());
        }
        if (activeObject != null) {
            deleteIdList.add(activeObject.getId());
        }
        if (couponListKeyObject != null) {
            deleteIdList.add(couponListKeyObject.getId());
        }
        if (CollectionUtil.isNotEmpty(deleteIdList)) {
            amzPromotionsCouponsHisMapper.deleteBatchIds(deleteIdList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deletePromotionCacheKey(Long id) {

        String promotionApprovalTypeKey = AmzPromotionsCouponConstants.PROMOTION_CREATE_OR_UPDATE_APPROVAL_TYPE_KEY + ":" + id;
        String promotionObjectKey = AmzPromotionsCouponConstants.PROMOTION_CREATE_OR_UPDATE_OBJECT_KEY + ":" + id;
        String skuListKey = AmzPromotionsCouponConstants.PROMOTION_CREATE_OR_UPDATE_SKU_KEY + ":" + id;
        List<AmzPromotionsHis> promotionApprovalTypeObject = amzPromotionsHisMapper.selectList(Wrappers.<AmzPromotionsHis>lambdaQuery().eq(AmzPromotionsHis::getPromotionApprovalTypeKey, promotionApprovalTypeKey));
        List<AmzPromotionsHis> promotionObject = amzPromotionsHisMapper.selectList(Wrappers.<AmzPromotionsHis>lambdaQuery().eq(AmzPromotionsHis::getPromotionsObjectKey, promotionObjectKey));
        List<AmzPromotionsHis> amzPromotionsHis2 = amzPromotionsHisMapper.selectList(Wrappers.<AmzPromotionsHis>lambdaQuery().eq(AmzPromotionsHis::getSkuListKey, skuListKey));
        List<Long> deleteIdList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(promotionApprovalTypeObject)) {
            deleteIdList.addAll(promotionApprovalTypeObject.stream().map(AmzPromotionsHis::getId).distinct().collect(Collectors.toList()));
        }
        if (CollectionUtil.isNotEmpty(promotionObject)) {
            deleteIdList.addAll(promotionObject.stream().map(AmzPromotionsHis::getId).distinct().collect(Collectors.toList()));
        }
        if (CollectionUtil.isNotEmpty(amzPromotionsHis2)) {
            List<Long> collect = amzPromotionsHis2.stream().map(AmzPromotionsHis::getId).collect(Collectors.toList());
            deleteIdList.addAll(collect);
        }
        if (CollectionUtil.isNotEmpty(deleteIdList)) {
            amzPromotionsHisMapper.deleteBatchIds(deleteIdList);
        }
    }

    private void addPerFundUnit(StringBuilder detail, AmzPromotionsSkuDetail  skuDetail1, AmzPromotionsHisPromotionsSkuReceiveMsg msg) {

        if (StringUtil.isEmpty(msg.getPerType()) || StringUtil.isEmpty(msg.getPerAmount())) {
            log.error("每件商品返回折扣为空,asin为:{}", skuDetail1.getAsin());
        } else {
            msg.setPerType(resetCurrency(msg.getPerType()));
            String perFunding = skuDetail1.getPerFunding();
            String newPerFunding = "%".equals(msg.getPerType()) ? msg.getPerAmount() + msg.getPerType() : msg.getPerType() + msg.getPerAmount() ;
            if (StringUtil.isEmpty(perFunding)) {
                detail.append(skuDetail1.getAsin()).append(":").append("每件商品折扣").append("由")
                        .append("空更新为").append(newPerFunding).append(";");
                skuDetail1.setPerFunding(newPerFunding);
            } else {
                if ((!"%".equals(msg.getPerType()) && perFunding.contains("%")) || ("%".equals(msg.getPerType()) && !perFunding.contains("%"))) {
                    detail.append(skuDetail1.getAsin()).append(":").append("每件商品折扣").append("由")
                            .append(perFunding)
                            .append("更新为").append(newPerFunding).append(";");
                    skuDetail1.setPerFunding(newPerFunding);
                } else {
                    BigDecimal newValue = new BigDecimal(msg.getPerAmount());
                    BigDecimal old = BigDecimal.ZERO;
                    if (!"%".equals(msg.getPerType())) {
                         old = new BigDecimal(perFunding.replaceAll("[^\\d.]",""));
                    } else {
                         old = new BigDecimal(perFunding.replace("%", ""));
                    }
                    if (newValue.compareTo(old) != 0) {
                        detail.append(skuDetail1.getAsin()).append(":").append("每件商品折扣").append("由")
                                .append(perFunding)
                                .append("更新为").append(newPerFunding).append(";");
                        skuDetail1.setPerFunding(newPerFunding);
                    }
                }
            }
        }

    }

    private String resetCurrency(String perType) {
        if (StringUtils.isEmpty(perType)) {
            return null;
        }
        return MarPromotionCountryCurrencyEnum.getCurrencyFlagByCurrency(perType.trim());
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateCouponBeginDateAndEndDate(Long couponsId) {
        AmzPromotionsCoupons coupons1 = amzPromotionsCouponsMapper.selectAmzPromotionsCouponsById(couponsId);
        AmzPromotionsCoupon coupon = new AmzPromotionsCoupon();
        coupon.setCouponId(couponsId);
        List<AmzPromotionsCoupon> list = amzPromotionsCouponMapper.selectAmzPromotionsCouponList(coupon);
        Date maxDate = Collections.max(list.stream().map(AmzPromotionsCoupon::getEndDate).collect(Collectors.toList()));
        Date minDate = Collections.min(list.stream().map(AmzPromotionsCoupon::getBeginDate).collect(Collectors.toList()));
        AmzPromotionsCoupons coupons = new AmzPromotionsCoupons();
        coupons.setId(couponsId);
        coupons.setBeginDate(minDate);
        coupons.setEndDate(maxDate);
        if (DateUtil.compareDate(coupons1.getBeginDate(), coupons.getBeginDate()) != 0 || DateUtil.compareDate(coupons1.getEndDate(), coupons.getEndDate()) != 0) {
            amzPromotionsCouponsMapper.updateAmzPromotionsCoupons(coupons);
        } else {
            log.info("活动:{}开始结束时间未发生变化无需更新",couponsId);
        }

    }


    private Date dateAddHms(Date original) {
        if (original == null) {
            return null;
        }
        return DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-MM-dd", original) + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
    }


    @Override
    public void processPromotionReferencePrice(JSONObject jsonObject) {

        log.info("促销获取参考价格--{}",jsonObject);
        String webStoreName = jsonObject.getString("webStoreName");

        String countryCode = jsonObject.getString("countryCode");
        String promotionId = null;
        if (jsonObject.containsKey("promotionId")) {
            promotionId = jsonObject.getString("promotionId");
        }
        Integer createOrUpdate = StringUtils.isNotEmpty(promotionId) ? 2 : 1;
        Integer type = jsonObject.getInteger("type");
        String asin = jsonObject.getString("asin");
        BigDecimal referencePrice = jsonObject.getBigDecimal("referencePrice");
        BigDecimal maxDiscountPrice = jsonObject.getBigDecimal("maxDiscountPrice");
        BigDecimal mixPerUintFunding = jsonObject.getBigDecimal("mixPerUintFunding");
        BigDecimal cppu = jsonObject.getBigDecimal("cppu");
        Account account = accountService.getOne(Wrappers.<Account>lambdaQuery().eq(Account::getStoreName, webStoreName).eq(Account::getCountryCode,countryCode).eq(Account::getActive, "Y").last("limit 1"));
        if (account == null) {
            log.error("促销获取参考价格失败店铺不存在[{}]",jsonObject);
            return;
        }
        AmzPromotionsDetailExtraPrice amzPromotionsDetailExtraPrice = new AmzPromotionsDetailExtraPrice();
        amzPromotionsDetailExtraPrice.setShopId(account.getId().longValue());
        amzPromotionsDetailExtraPrice.setPromotionsType(type);
        amzPromotionsDetailExtraPrice.setAsin(asin);
        amzPromotionsDetailExtraPrice.setReferencePrice(referencePrice);
        amzPromotionsDetailExtraPrice.setMaxDiscountPrice(maxDiscountPrice);
        amzPromotionsDetailExtraPrice.setMixPerUintFunding(mixPerUintFunding);
        amzPromotionsDetailExtraPrice.setCppu(cppu);
        amzPromotionsDetailExtraPrice.setType(createOrUpdate);
        amzPromotionsDetailExtraPrice.settingDefaultSystemUpdate();

        LambdaQueryWrapper<AmzPromotionsDetailExtraPrice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AmzPromotionsDetailExtraPrice::getShopId, amzPromotionsDetailExtraPrice.getShopId());
        wrapper.eq(AmzPromotionsDetailExtraPrice::getPromotionsType, amzPromotionsDetailExtraPrice.getPromotionsType());
        wrapper.eq(AmzPromotionsDetailExtraPrice::getAsin, amzPromotionsDetailExtraPrice.getAsin());
        wrapper.eq(AmzPromotionsDetailExtraPrice::getType, amzPromotionsDetailExtraPrice.getType());
        wrapper.orderByDesc(AmzPromotionsDetailExtraPrice::getUpdatedAt);
        wrapper.last("limit 1");
        AmzPromotionsDetailExtraPrice one = amzPromotionsDetailExtraPriceService.getOne(wrapper);
        if (one == null) {
            amzPromotionsDetailExtraPrice.settingDefaultSystemCreate();
        }else {
            amzPromotionsDetailExtraPrice.setId(one.getId());
        }
        amzPromotionsDetailExtraPriceService.saveOrUpdate(amzPromotionsDetailExtraPrice);

    }
}
