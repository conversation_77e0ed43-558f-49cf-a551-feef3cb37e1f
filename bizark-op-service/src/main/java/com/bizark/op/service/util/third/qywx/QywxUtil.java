package com.bizark.op.service.util.third.qywx;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bizark.op.api.cons.RedisCons;
import com.bizark.op.api.entity.op.user.WxNotifyUser;
import com.bizark.op.api.service.user.WxNotifyUserService;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.common.util.spring.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 企业微信工具类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-08-14 10:11:00
 */
@Slf4j
public class QywxUtil {

    @Autowired
    private QywxProperties qywxProperties;

    private static final String GET_TOKEN = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=%s&corpsecret=%s";

    private static final String GET_USERID_BY_EMAIL = "https://qyapi.weixin.qq.com/cgi-bin/user/get_userid_by_email?access_token=%s";

    private static final String SEND = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=%s";

    private static final String SEND_BY_MOBILE = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserid?access_token=%s";

    public static String getToken() {
        String getTokenUrl = String.format(GET_TOKEN, "ww4b6916d02035aabb", "RZHNTP-krS0A8o8u762Olrga2dzQe_8TdNLAVjj0jV4");
        String response = HttpUtil.get(getTokenUrl);
        JSONObject jsonObject = JSON.parseObject(response);
        String result = "";
        if (Integer.parseInt(jsonObject.get("errcode").toString()) == 0 && "ok".equals(jsonObject.get("errmsg"))) {
            result = String.valueOf(jsonObject.get("access_token"));
        } else {
            log.error("微信TOKEN获取失败：{}", response);
        }
        return result;
    }

    public static String getUserIdByEmail(String email, Integer emailType) {
        String token = getToken();
        String getUserIdByEmailUrl = String.format(GET_USERID_BY_EMAIL, token);

        GetUserIdByEmailRequest getUserIdByEmailRequest = GetUserIdByEmailRequest.builder()
                .email(email)
                .emailType(emailType)
                .build();
        String response = HttpUtil.post(getUserIdByEmailUrl, JSON.toJSONString(getUserIdByEmailRequest), -1);
        JSONObject jsonObject = JSON.parseObject(response);
        String result = "";
        if (Integer.parseInt(jsonObject.get("errcode").toString()) == 0 && "ok".equals(jsonObject.get("errmsg"))) {
            result = String.valueOf(jsonObject.get("userid"));
        } else {
            log.error(response);
        }
        return result;
    }

    public static void send(SendMessageRequest request) {
        String token = getToken();
        String sendUrl = String.format(SEND, token);

        log.error("#微信发送个人消息内容，{}, token: {}", JSON.toJSONString(request),token);

        String response = HttpUtil.post(sendUrl, JSON.toJSONString(request), -1);
        JSONObject jsonObject = JSON.parseObject(response);
        String result = "";
        if (Integer.parseInt(jsonObject.get("errcode").toString()) == 0 && "ok".equals(jsonObject.get("errmsg"))) {
            log.info("微信发送个人{}消息返回内容 {}，token: {}",request.getTouser(), response,token);
        } else {
            log.error("#微信发送个人{}消息返回内容error {}，token: {}",request.getTouser(),response,token);
        }
    }

    public static String getUserIdByPhoneInterface(String phone) {
        String token = getToken();
        String getUserIdByEmailUrl = String.format(SEND_BY_MOBILE, token);

        GetUserIdByEmailRequest getUserIdByEmailRequest = GetUserIdByEmailRequest.builder()
                .mobile(phone)
                .build();
        String response = HttpUtil.post(getUserIdByEmailUrl, JSON.toJSONString(getUserIdByEmailRequest), -1);

        log.info("微信发送个人消息，手机号：{},Respinose：{}", phone, response);
        JSONObject jsonObject = JSON.parseObject(response);
        String result = "";
        if (Integer.parseInt(jsonObject.get("errcode").toString()) == 0 && "ok".equals(jsonObject.get("errmsg"))) {
            result = String.valueOf(jsonObject.get("userid"));
        } else {
            log.error(response);
        }
        return result;
    }

    public static String getUserIdByPhone(String phone) {
        // 查询用户
        WxNotifyUser notifyUser = getWxNotifyUser(phone);

        if (Objects.nonNull(notifyUser)) {
            String wxUserId = notifyUser.getWxUserId();
            log.info("微信通知用户：{}", wxUserId);
            return wxUserId;
        }
        RedissonClient redissonClient = SpringUtils.getBean(RedissonClient.class);

        RLock lock = redissonClient.getLock(RedisCons.WX_NOTIFY_USER_LOCK + phone);
        try {
            lock.lock(10, TimeUnit.SECONDS);

            notifyUser = getWxNotifyUser(phone);
            if (Objects.nonNull(notifyUser)) {
                return notifyUser.getWxUserId();
            }
            String result = getUserIdByPhoneInterface(phone);
            // 写入
            saveNotifyUSer(phone, result);

            return result;
        }finally {
            lock.unlock();
        }
    }


    private static WxNotifyUser getWxNotifyUser(String phone) {
        WxNotifyUserService notifyUserService = SpringUtils.getBean(WxNotifyUserService.class);
        WxNotifyUser notifyUser = notifyUserService.lambdaQuery()
                .eq(WxNotifyUser::getPhone, phone)
                .last("limit 1")
                .one();
        return notifyUser;
    }

    private static void saveNotifyUSer(String phone,String wxUserId) {
        try {
            WxNotifyUserService notifyUserService = SpringUtils.getBean(WxNotifyUserService.class);
            WxNotifyUser notifyUser = new WxNotifyUser();
            notifyUser.setPhone(phone);
            notifyUser.setWxUserId(StrUtil.isBlank(wxUserId) || Objects.equals(wxUserId, StrUtil.NULL) ? StrUtil.EMPTY : wxUserId);
            notifyUserService.save(notifyUser);
        } catch (Exception e) {
            log.error("微信联系人保存失败 - {} {}", phone, wxUserId, e);
        }

    }

    public static void wxMessageSend(String content, String phone) {
        if (StringUtils.isEmpty(phone)) {
            return;
        }
        try {
            String userIdByPhone = QywxUtil.getUserIdByPhone(phone);

            if (StrUtil.isBlank(userIdByPhone) || Objects.equals(StrUtil.NULL, userIdByPhone)) {
                log.error("企业微信通知停止,用户ID不存在：{}", phone);
                return;
            }

            TextRequest text = TextRequest.builder()
                    .content(content)
                    .build();
            SendMessageRequest sendMessageRequest = SendMessageRequest.builder()
                    .touser(userIdByPhone)
                    .toparty("")
                    .totag("")
                    .msgtype("text")
                    .agentid(1000027)
                    .text(text)
                    .safe(0)
                    .enable_id_trans(0)
                    .enable_duplicate_check(0)
                    .duplicate_check_interval(1800)
                    .build();
            QywxUtil.send(sendMessageRequest);
        } catch (Exception e) {
            log.error("发送微信消息通知失败:{}", e.getMessage());
        }
    }


    /**
     *
     * @param content 消息内容
     * @param email 邮箱
     * @param emailType 邮箱类型 例如：1
     */
    public static void wxMessageSendByEmail(String content, String email, Integer emailType) {
        if (StringUtils.isEmpty(email) || emailType == null) {
            return;
        }
        try {
            String userIdByEmail = QywxUtil.getUserIdByEmail(email, emailType);
            TextRequest text = TextRequest.builder()
                    .content(content)
                    .build();
            SendMessageRequest sendMessageRequest = SendMessageRequest.builder()
                    .touser(userIdByEmail)
                    .toparty("")
                    .totag("")
                    .msgtype("text")
                    .agentid(1000027)
                    .text(text)
                    .safe(0)
                    .enable_id_trans(0)
                    .enable_duplicate_check(0)
                    .duplicate_check_interval(1800)
                    .build();
            QywxUtil.send(sendMessageRequest);
        } catch (Exception e) {
            log.error("发送微信消息通知失败:{}", e.getMessage());
        }
    }
}
