package com.bizark.op.service.handler.inventory.sync;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.bizark.op.api.enm.inventory.InventoryType;
import com.bizark.op.api.enm.sale.SaleChannelMappingEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.inventory.InventorySyncBean;
import com.bizark.op.api.entity.op.inventory.ProductChannelsInventory;
import com.bizark.op.api.entity.op.inventory.SelectInventoryBaseResponse;
import com.bizark.op.api.entity.op.inventory.SyncInventoryResult;
import com.bizark.op.api.entity.op.inventory.conf.vo.SellerSkuInventoryTaskMessage;
import com.bizark.op.api.entity.op.inventory.response.query.WalmartDSVInventorySelectResponse;
import com.bizark.op.api.entity.op.inventory.response.query.WalmartInventorySelectResponse;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.entity.op.task.PublishTask;
import com.bizark.op.api.service.inventory.ProductChannelsInventoryService;
import com.bizark.op.common.util.UserUtils;
import com.bizark.op.service.api.InventorySelectApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.bizark.op.api.enm.sale.SaleChannelMappingEnum.WALMART_DSV;

@Component
@Slf4j
public class WalmartDsvInventorySynchronizer extends AbstractInventorySynchronizer{

    @Autowired
    private InventorySelectApi inventorySelectApi;


    @Override
    public InventoryType getType() {
        return InventoryType.WALMART_DSV;
    }

    /**
     * 重写执行逻辑
     * @param commonBean
     */
    @Override
    public SyncInventoryResult sync(InventorySyncBean commonBean) {
        SyncInventoryResult result = new SyncInventoryResult();

        List<ProductChannels> channels = commonBean.getChannels();
        for (ProductChannels channel : channels) {
            InventorySyncBean bean = new InventorySyncBean();
            bean.setAccount(commonBean.getAccount());
            bean.setChannels(Collections.singletonList(channel));
            result.appendResult(super.sync(bean));
        }
        return result;
    }

    @Override
    public SelectInventoryBaseResponse getInventory(InventorySyncBean commonBean) {
        ProductChannels channels = commonBean.getChannels().get(0);
        Account account = commonBean.getAccount();
        WalmartDSVInventorySelectResponse response = inventorySelectApi.selectWalmartDSVItemInventory(account, channels.getSellerSku());
        if (Objects.isNull(response)) {
            // 失败了重试一次
            response = inventorySelectApi.selectWalmartDSVItemInventory(account, channels.getSellerSku());
        }

        return response;
    }

    @Override
    public SelectInventoryBaseResponse buildInventoryByTask(InventorySyncBean syncBean) {
        WalmartDSVInventorySelectResponse walmartDsvResponse = new WalmartDSVInventorySelectResponse();

        List<SellerSkuInventoryTaskMessage> taskMessages = syncBean.getInventoryTaskMessages();
        List<WalmartDSVInventorySelectResponse.Node> nodes = new ArrayList<>();
        for (SellerSkuInventoryTaskMessage taskMessage : taskMessages) {
            PublishTask task = taskMessage.getTask();
            WalmartDSVInventorySelectResponse.Node node = new WalmartDSVInventorySelectResponse.Node();
            node.setShipNode(task.getWarehouseId());
            node.setSellerSku(task.getSellerSku());
            WalmartDSVInventorySelectResponse.QuantityInfo quantityInfo = new WalmartDSVInventorySelectResponse.QuantityInfo();
            quantityInfo.setAmount(new BigDecimal(task.getTaskTarget()));
            quantityInfo.setUnit("EACH");
            node.setAvailToSellQty(quantityInfo);
            nodes.add(node);
            log.info("WalamrtDsv多渠道库存推送 - {} - SKU:{} - 仓库:{} - 数量:{}", task.getAccountFlag(), task.getSellerSku(), task.getWarehouseId(), task.getTaskTarget());
        }
        walmartDsvResponse.setNodes(nodes);
        walmartDsvResponse.setUpdateType(2);
        walmartDsvResponse.setStatus(SelectInventoryBaseResponse.Status.SUCCESS);
        return walmartDsvResponse;
    }

    @Override
    public SelectInventoryBaseResponse handleResponse(SelectInventoryBaseResponse response) {
        if (response.getStatus() == SelectInventoryBaseResponse.Status.ERROR) {
            log.error("Walmart_DSV库存同步失败，请求库存数据失败！");
            return response.convertError("请求库存数据失败");
        }

        WalmartDSVInventorySelectResponse walmartDsvResponse = (WalmartDSVInventorySelectResponse) response;
        if (CollectionUtil.isEmpty(walmartDsvResponse.getNodes())) {
            log.error("Walmart_DSV库存同步失败，未获取到库存详细信息！");
            return response.convertError("未获取到库存详细信息");
        }

        ProductChannels channels = response.getCommonBean().getChannels().get(0);
        Account account = response.getCommonBean().getAccount();
        List<WalmartDSVInventorySelectResponse.Node> nodes = walmartDsvResponse.getNodes();
        Date date = new Date();
        List<ProductChannelsInventory> inventories = nodes.stream()
                .map(n -> {
                    ProductChannelsInventory inventory = new ProductChannelsInventory();
                    inventory.setChannelId(channels.getId());
                    inventory.setQuantity(String.valueOf(n.getAvailToSellQty().getAmount()));
                    inventory.setUnit(n.getAvailToSellQty().getUnit());
                    inventory.setOrgId(channels.getOrgId());
                    inventory.setWarehouseId(n.getShipNode());
                    inventory.setAccountFlag(channels.getAccountId());
                    inventory.setSellerSku(StrUtil.isNotBlank(n.getSellerSku()) ? n.getSellerSku() : channels.getSellerSku());
                    inventory.setAccountId(account.getId());
                    if (Objects.equals(1, response.getUpdateType())) {
                        inventory.setSyncLastTime(date);
                    }else{
                        inventory.setUpdateLastTime(date);
                    }
                    log.info("WalamrtDsv多渠道库存推送库存构建 - {} - SKU:{} - 仓库:{} - 数量:{}", inventory.getAccountFlag(), inventory.getSellerSku(), inventory.getWarehouseId(), inventory.getQuantity());
                    return inventory;
                }).collect(Collectors.toList());

        // 保存库存信息
        try {
            recordInventory(response.getCommonBean().getChannels(), inventories);
        } catch (Exception e) {
            log.info("WalmartDsv库存记录异常 - ", e);
        }
        buildErrorChannels(response,inventories);
        return response;
    }
}
