package com.bizark.op.service.service.tiktok.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.boss.api.service.TaskCenterService;
import com.bizark.boss.api.vo.dashboard.TaskCenterRequest;
import com.bizark.common.exception.CommonException;
import com.bizark.op.api.cons.MQDefine;
import com.bizark.op.api.cons.ScTicketConstant;
import com.bizark.op.api.enm.TikTokApiEnums;
import com.bizark.op.api.enm.mar.BillSourceEnum;
import com.bizark.op.api.enm.refund.*;
import com.bizark.op.api.enm.sale.AccountSaleChannelEnum;
import com.bizark.op.api.enm.tiktok.TiktokReturnRefundApiType;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.order.SaleOrderItemVO;
import com.bizark.op.api.entity.op.order.SaleOrders;
import com.bizark.op.api.entity.op.refund.OrderRefundRequestInfo;
import com.bizark.op.api.entity.op.refund.OrderRefundRequestItem;
import com.bizark.op.api.entity.op.returns.temu.MarTemuChildrenReturnInfo;
import com.bizark.op.api.entity.op.returns.temu.MarTemuParentReturnInfo;
import com.bizark.op.api.entity.op.returns.tiktok.TikTokRefundAmount;
import com.bizark.op.api.entity.op.returns.tiktok.TiktokReturnRefund;
import com.bizark.op.api.entity.op.returns.tiktok.TiktokReturnRefundItem;
import com.bizark.op.api.entity.op.ticket.ScTicket;
import com.bizark.op.api.entity.op.ticket.VO.SaleOrderVoTicket;
import com.bizark.op.api.entity.op.titok.response.ReturnStatusChangeWebHooks;
import com.bizark.op.api.entity.op.titok.response.ReverseOrderResponseMsg;
import com.bizark.op.api.entity.op.titok.webhook.TikTokBusinessData;
import com.bizark.op.api.form.customer.CusCenterOrderRefundQuery;
import com.bizark.op.api.parameter.util.TikTokUtil;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.order.SaleOrdersService;
import com.bizark.op.api.service.refund.IOrderRefundRequestInfoService;
import com.bizark.op.api.service.refund.IOrderRefundRequestItemService;
import com.bizark.op.api.service.ticket.IScTicketService;
import com.bizark.op.api.vo.customer.CusCenterOrderRefundDTO;
import com.bizark.op.api.vo.customer.CusCenterOrderRefundVO;
import com.bizark.op.api.vo.refund.OrderRefundInfoVO;
import com.bizark.op.common.function.page.Page;
import com.bizark.op.common.util.*;
import com.bizark.op.service.manager.refund.AccountSaleMsgManger;
import com.bizark.op.service.mapper.order.SaleOrderItemsMapper;
import com.bizark.op.service.mapper.refund.OrderRefundItemMapper;
import com.bizark.op.service.mapper.refund.OrderRefundMapper;
import com.bizark.op.service.mapper.refund.OrderRefundRequestInfoItemMapper;
import com.bizark.op.service.mapper.refund.OrderRefundRequestInfoMapper;
import com.bizark.op.service.mapper.ticket.SaleOrdersMapper;
import com.bizark.op.service.util.ConvertUtils;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.lang.ref.WeakReference;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: Ailill
 * @Date: 2024/4/2 16:14
 */
@Service
@Slf4j
public class OrderRefundRequestInfoServiceImpl extends ServiceImpl<OrderRefundRequestInfoMapper, OrderRefundRequestInfo> implements IOrderRefundRequestInfoService {

    @Resource
    private OrderRefundRequestInfoMapper orderRefundRequestInfoMapper;

    @Resource
    private OrderRefundRequestInfoItemMapper orderRefundRequestInfoItemMapper;

    @Resource
    private AccountService accountService;

    @Resource
    private AccountSaleMsgManger accountSaleMsgManger;

    @Resource
    private IScTicketService scTicketService;


    @Resource
    private SaleOrdersService saleOrdersService;

    @Autowired
    private TikTokUtil tikTokUtil;


    @Autowired
    private TaskCenterService taskCenterService;


    @Autowired
    private IOrderRefundRequestItemService orderRefundRequestItemService;

    @Autowired
    private SaleOrderItemsMapper saleOrderItemsMapper;

    @Autowired
    private RabbitTemplate rabbitTemplate;


    @Override
    public OrderRefundRequestInfo getOrderRefunds(TiktokReturnRefund i, TikTokBusinessData webhookData, String channel) {
        Account account = accountService.getAccountByShopId(webhookData.getShopId());
        List<SaleOrders> list = accountSaleMsgManger.getNewOrderId(webhookData.getShopId(),i.getOrderId());
        if(CollUtil.isEmpty(list)){
            throw new CommonException("订单不存在");
        }
        SaleOrders one = list.get(0);

        WeakReference<OrderRefundRequestInfo> orderRefundWeakReference = new WeakReference<>(new OrderRefundRequestInfo());
        OrderRefundRequestInfo orderRefundRequest = orderRefundWeakReference.get();
        if (ObjectUtil.isNotNull(orderRefundRequest)) {
            // tiktok数据填充
            BeanUtil.copyProperties(i, orderRefundRequest);
            if (StringUtils.isNotEmpty(i.getReturnType()) && StringUtils.isNotEmpty(i.getReturnStatus())) {
                if ("RETURN_AND_REFUND".equals(i.getReturnType())) {
                    OrderRefundStatusEnum andDesc = OrderRefundStatusEnum.getAndDesc(orderRefundRequest.getReturnStatus());
                    orderRefundRequest.setReturnStatus(andDesc != null ? andDesc.getCode() : null);
                }
            }
            orderRefundRequest.setPlatformRefundStatus(TKRefundStatusEnum.getDesc(orderRefundRequest.getReturnStatus()) != null ? TKRefundStatusEnum.getDesc(orderRefundRequest.getReturnStatus()).getDesc():null);//平台的退款状态。
            orderRefundRequest.setCreateTime(i.getTkCreateTime());
            orderRefundRequest.setUpdateTime(i.getTkUpdateTime());
            orderRefundRequest.setReverseRequestTime(DateUtil.BjToPstByTimestamp(orderRefundRequest.getCreateTime()));
            // 主订单表填充
            BeanUtil.copyProperties(one, orderRefundRequest);

            orderRefundRequest.setOrderId(i.getOrderId());
            orderRefundRequest.setOrderPrimaryId(one.getId());
//            orderRefundRequest.setChannelOrderId(i.getOrderId());

            orderRefundRequest.setShopId(account.getId().longValue());
            orderRefundRequest.setRefundType(OrderRefundEnum.INTERNAL.getCodeType());
            orderRefundRequest.setOrganizationId(one.getOrgId());
            orderRefundRequest.setChannel(channel);
            orderRefundRequest.setRefundOrderStatus(OrderRefundEnum.PENDING.getCodeType());
            orderRefundRequest.setRefundStatus(RefundStateEnum.REFUNDING.getCode());
            // TODO price 相关
            /*orderRefundRequest.setItemPriceAdj(i.refundTotal);
            orderRefundRequest.setItemPriceAdjRefunded(BigDecimal.ZERO);
            orderRefundRequest.setItemTaxAdjRefunded(BigDecimal.ZERO);
            orderRefundRequest.setReturnOfferRefunded(BigDecimal.ZERO);
            orderRefundRequest.setRestockingFeesRefunded(BigDecimal.ZERO);
            orderRefundRequest.setIsSaved(Boolean.FALSE);
            orderRefundRequest.setTreatmentState(ScTicketConstant.TICKET_STATUS_NEW);
            orderRefundRequest.setReturnText(i.getReturnReason());

            orderRefundRequest.setReverseRequestDate(DateUtil.BjToPstByTimestamp(orderRefundRequest.getReverseRequestTime()));*/

        }
        return orderRefundRequest;
    }

    @Override
    public ScTicket selectFromScTicketForTikTokTypeRefund(JSONObject json) {
        TikTokBusinessData webHookData = JSON.toJavaObject(json, TikTokBusinessData.class);
        // businessType
        String businessType = "tiktok:"+"[type="+webHookData.getType()+",reverseType="+webHookData.getData().getReverseType()+"]";
        ScTicket scTicket = orderRefundRequestInfoMapper.selectFromScTicketForTemporary(webHookData.getData().getReverseOrderId(), businessType);
        return scTicket;
    }


    @Override
    public ScTicket selectByReverseAndTypeFromTicket(String reverseOrderId, String businessType){

        return orderRefundRequestInfoMapper.selectFromScTicketForTemporary(reverseOrderId,businessType);
    }

    @Override
    public void deleteByTicketNumberForUpdate(String ticketNumber) {
        orderRefundRequestInfoMapper.deleteByTicketNumberForUpdate(ticketNumber);
    }


    /**
     * @description: 保存Temu退款请求信息，并生成工单
     * @author: Moore
     * @date: 2024/8/19 15:17
     * @param
     * @param marTemuParentReturnInfo
     * @return: void
     **/
    @Override
    public void saveTemuRefundRequestInfo(MarTemuParentReturnInfo marTemuParentReturnInfo) {
        if (null == marTemuParentReturnInfo||StringUtils.isEmpty(marTemuParentReturnInfo.getAccountFlag())) {
            return;
        }

        //判读售后单是否存在
        LambdaQueryWrapper<OrderRefundRequestInfo> queryParm = new LambdaQueryWrapper<>();
        queryParm.select(OrderRefundRequestInfo::getId, OrderRefundRequestInfo::getReturnStatus); //查询字段
        queryParm.eq(OrderRefundRequestInfo::getReturnId, marTemuParentReturnInfo.getParentAfterSalesSn()); //售后父单号，唯一
        queryParm.eq(OrderRefundRequestInfo::getChannel, AccountSaleChannelEnum.TEMU.getValue());//渠道
        List<OrderRefundRequestInfo> orderRefundRequestInfos = orderRefundRequestInfoMapper.selectList(queryParm);
        if (!CollectionUtils.isEmpty(orderRefundRequestInfos)) {
            OrderRefundRequestInfo orderRefundRequestInfo = orderRefundRequestInfos.get(0);  //原则只会出现唯一的一条
            if (marTemuParentReturnInfo.getParentAfterSalesStatus() != null && !(marTemuParentReturnInfo.getParentAfterSalesStatus().toString()).equals(orderRefundRequestInfo.getReturnStatus())) {
                //更新状态
                orderRefundRequestInfo.setPlatformRefundStatus(PlatformRefundStatusEnum.getDesc(marTemuParentReturnInfo.getParentAfterSalesStatus().toString()) != null ? PlatformRefundStatusEnum.getDesc(marTemuParentReturnInfo.getParentAfterSalesStatus().toString()).getDesc() : null);//平台的退款状态。}
                orderRefundRequestInfo.setReturnStatus(marTemuParentReturnInfo.getParentAfterSalesStatus().toString());
                orderRefundRequestInfo.settingDefaultSystemUpdate();
                this.updateById(orderRefundRequestInfo);
            }
            return;
        }

        //订单号
        String orderNo = marTemuParentReturnInfo.getParentOrderSn();
        //父售后ID（唯一售后ID）
        String returnId = marTemuParentReturnInfo.getParentAfterSalesSn();


        //获取订单信息
        //2.获取业务订单数据
        SaleOrders saleOrders = new SaleOrders();
        saleOrders.setOrderNo(orderNo);//平台订单号
        saleOrders.setChannel(com.bizark.boss.api.enm.AccountSaleChannelEnum.TEMU.getName()); //渠道
        saleOrders.setChannelFlag(marTemuParentReturnInfo.getAccountFlag()); //店铺标识
        List<SaleOrderVoTicket> saleOrdersLists = saleOrdersService.selectOrderItems(saleOrders);//查询订单明细信息
        if (CollectionUtils.isEmpty(saleOrdersLists)) {
            log.info("temu退款未获取到订单信息：{}",JSONObject.toJSONString(marTemuParentReturnInfo));
            return;
        }

        SaleOrderVoTicket saleOrderVoTicket = saleOrdersLists.get(0);
        Long orderId = saleOrderVoTicket.getId();
        Integer orgId = saleOrderVoTicket.getOrgId();
        Integer shopId = saleOrderVoTicket.getChannelId();
        String buyName = saleOrderVoTicket.getBuyName();
        Date pstChannelCreated = saleOrderVoTicket.getPstChannelCreated(); //订单时间
        String shippingMethodCode = saleOrderVoTicket.getShippingMethodCode(); //发货方式
        Date shipmentDate = saleOrderVoTicket.getShipmentDate();//发货时间

        //店铺信息
        Account account = accountService.selectByAccountId(shopId);

        //行ID转MAP
        Map<String, SaleOrderVoTicket> saleOrderMap = saleOrdersLists.stream().collect(Collectors.toMap(SaleOrderVoTicket::getChannelOrderItemId, each -> each, (value1, value2) -> value1));

        //保存请求主表
        OrderRefundRequestInfo orderRefundRequestInfo = new OrderRefundRequestInfo();
        orderRefundRequestInfo.setOrganizationId(orgId!=null?orgId.longValue():null); //组织ID
        orderRefundRequestInfo.setCreateTime(marTemuParentReturnInfo.getCreateAt()); //创建时间，时间戳
        orderRefundRequestInfo.setOrderId(orderNo);//平台订单号
        orderRefundRequestInfo.setOrderPrimaryId(saleOrderVoTicket.getId());//主键
        orderRefundRequestInfo.setReturnId(returnId); //唯一ID（父售后单号）
        orderRefundRequestInfo.setBuyName(buyName);//订单用户姓名
        orderRefundRequestInfo.settingDefaultSystemCreate();
        orderRefundRequestInfo.setChannel(AccountSaleChannelEnum.TEMU.getValue());
        orderRefundRequestInfo.setReverseRequestTime(DateUtil.BjToPstByTimestamp(marTemuParentReturnInfo.getCreateAt()));
        orderRefundRequestInfo.setShopId(shopId != null ? shopId.longValue():null);
        orderRefundRequestInfo.setRefundStatus(RefundStateEnum.NOTAPPLIED.getCode());//未申请。
        orderRefundRequestInfo.setPlatformRefundStatus(PlatformRefundStatusEnum.getDesc(marTemuParentReturnInfo.getParentAfterSalesStatus().toString()) != null ? PlatformRefundStatusEnum.getDesc(marTemuParentReturnInfo.getParentAfterSalesStatus().toString()).getDesc():null);//平台的退款状态。
        orderRefundRequestInfo.setReturnStatus(marTemuParentReturnInfo.getParentAfterSalesStatus().toString());

        orderRefundRequestInfo.setPstChannelCreated(pstChannelCreated); //订单时间
        orderRefundRequestInfo.setShipmentDate(shipmentDate); //发货时间
        orderRefundRequestInfo.setShippingMethodCode(shippingMethodCode); //发货方式

        if (!StringUtils.isEmpty(shippingMethodCode)) {
            if ("UPSG_temu".equalsIgnoreCase(shippingMethodCode)
                    ||"FEDXG_temu".equalsIgnoreCase(shippingMethodCode)
                    ||"Temu".equalsIgnoreCase(shippingMethodCode)
            ||"WLC-FEDEX".equalsIgnoreCase(shippingMethodCode)
            ){
                orderRefundRequestInfo.setBillSource(BillSourceEnum.PLATFORM.getValue()); //平台面单
            }else{
                orderRefundRequestInfo.setBillSource(BillSourceEnum.SHOP.getValue()); //商家面单
            }
        }
        this.save(orderRefundRequestInfo);

        //明细
        List<MarTemuChildrenReturnInfo> sonAfterSaleOrders = marTemuParentReturnInfo.getSonAfterSaleOrders();
        for (MarTemuChildrenReturnInfo sonAfterSaleOrder : sonAfterSaleOrders) {
            String afterSalesSn = sonAfterSaleOrder.getAfterSalesSn();//子售后单ID
            String sellerSKu = sonAfterSaleOrder.getProductSkuId();//产品ID
            String channelOrderItemId = afterSalesSn.substring(0, afterSalesSn.lastIndexOf("-"));//行订单号
            Long applyNumber = sonAfterSaleOrder.getApplyAfterSalesGoodsNumber() != null ? sonAfterSaleOrder.getApplyAfterSalesGoodsNumber().longValue() : null;//申请数量


            //获取子售后单行信息
            SaleOrderVoTicket saleOrderItemInfo = saleOrderMap.get(channelOrderItemId) == null ? new SaleOrderVoTicket() :  saleOrderMap.get(channelOrderItemId) ;

            //明细行信息
            OrderRefundRequestItem orderRefundRequestItem = new OrderRefundRequestItem();
            orderRefundRequestItem.setOrganizationId(orgId!=null?orgId.longValue():null); //组织ID
            orderRefundRequestItem.setOrderId(orderNo);
            orderRefundRequestItem.setUrl(saleOrderItemInfo.getImageUrl()); //图片
            orderRefundRequestItem.setSellerSku(sellerSKu); //SellerSku
//            orderRefundRequestItem.setProductName(saleOrderItemInfo.getChannelTitle());//产品名称（暂时来看存储无意义）
            orderRefundRequestItem.setErpSku(saleOrderItemInfo.getErpsku()); //erpSKu
//            orderRefundRequestItem.setReturnNum(applyNumber);//退款数量
            orderRefundRequestItem.setReturnId(returnId); //唯一ID （父售后单号）
            orderRefundRequestItem.setReturnLineItemId(afterSalesSn); //子售后单
            orderRefundRequestItem.setOrderLineItemId(channelOrderItemId); //行ID
            orderRefundRequestItem.setAccountId(shopId);
            orderRefundRequestItem.setAccountFlag(saleOrderVoTicket.getAccountId());
            orderRefundRequestItem.setCurrency(saleOrderVoTicket.getCurrencyCode()); //币种

            BigDecimal amount = saleOrderItemInfo.getItemAmount(); //单行销售额
            Integer quantity = saleOrderItemInfo.getQuantity()==null?0:saleOrderItemInfo.getQuantity(); //销量
            BigDecimal quantityBig = new BigDecimal(quantity);
            orderRefundRequestItem.setUnitPrice(quantity == 0 ? amount : amount.divide(quantityBig)); //订单行单价
            try {
                //使用申请数量* 订单行单价（原则API，temu未返回，只返回数量）
                if (quantity != 0 && applyNumber != null) {
                    orderRefundRequestItem.setRefundTotal(amount.divide(quantityBig).multiply(new BigDecimal(applyNumber.intValue())));
                }
            } catch (Exception e) {
                log.info("计算tmeu退货单价异常：{}", e);
            }


            orderRefundRequestItem.setReturnTotalAmount(saleOrderItemInfo.getItemAmount()); //订单行总金额
            orderRefundRequestItem.settingDefaultSystemCreate(); //创建时间
            orderRefundRequestItem.setRefundQuantity(applyNumber);//退货数量 ,接口申请数量


            //包裹信息
            if (saleOrderItemInfo.getItemId() != null) {
                List<SaleOrderItemVO> saleOrderItemVOS = saleOrderItemsMapper.selectOrderShipmentInfo(orderId);
                String tranckNos = saleOrderItemVOS.stream().filter(item -> item.getHeadItemId().equals(saleOrderItemInfo.getItemId()) && !StringUtils.isEmpty(item.getTrackNo())).map(SaleOrderItemVO::getTrackNo).distinct().collect(Collectors.joining(","));
                if (!StringUtils.isEmpty(tranckNos)) {
                    orderRefundRequestItem.setShipmentTrackNo(tranckNos);
                }
            }
            orderRefundRequestInfoItemMapper.insert(orderRefundRequestItem);
        }


        //创建工单
        ScTicket scTicketCrete = new ScTicket();
        scTicketCrete.setOrderId(orderId);
        scTicketCrete.setTicketType(ScTicketConstant.TICKET_TYPE_REFUND_REQUEST);  //工单类型 站外
        scTicketCrete.setTicketName("客户申请订单号:" + marTemuParentReturnInfo.getParentOrderSn() + "退款"); //工单名称
        scTicketCrete.setRemark("店铺名称:" + StrUtil.nullToDefault(account.getTitle(), "") + " 订单号:" + marTemuParentReturnInfo.getParentOrderSn()); //描述
        scTicketCrete.setTicketSource(AccountSaleChannelEnum.TEMU.getValue());// 工单来源(订单API)
        scTicketCrete.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH);
        scTicketCrete.setReverseOrderId(marTemuParentReturnInfo.getParentAfterSalesSn());
        scTicketCrete.setAmazonOrderId(orderNo);
        scTicketCrete.setSourceId(orderRefundRequestInfo.getId());
        scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(orderId.toString(), "l", 5, "0") + RandomUtil.randomNumbers(4));
        try {
            scTicketCrete.setBusinessApplyTime(MathTimeUtils.longToDate(marTemuParentReturnInfo.getCreateAt()));
        } catch (Exception e) {
            throw new CommonException("时间异常");
        }
        scTicketCrete.setSourceDocument("order_refund_request_info"); //来源单据 退款请求表
        scTicketCrete.setShopId(Long.valueOf(account.getId()));  //店铺
        scTicketCrete.setOrganizationId(Long.valueOf(account.getOrgId()));//组织ID
        scTicketService.insertScTicket(scTicketCrete); //创建工单

        //自动关闭逻辑

        //更新退款请求表工单ID
        OrderRefundRequestInfo orderRefundRequestInfoUpdate = new OrderRefundRequestInfo();
        orderRefundRequestInfoUpdate.setId(orderRefundRequestInfo.getId());
        orderRefundRequestInfoUpdate.setTicketId(scTicketCrete.getId());
        orderRefundRequestInfoUpdate.settingDefaultSystemUpdate();
        this.updateById(orderRefundRequestInfoUpdate);

    }

    /**
     * @description:TK 退款请求V2接口
     * @author: Moore
     * @date: 2024/8/21 14:26
     * @param
     * @param tiktokData
     * @return: void
     **/
    @Override
   @Async("threadPoolTaskExecutor")
    public void saveTkOrderReturnInfoV2(JSONObject tiktokData) {
        if (null == tiktokData || null == tiktokData.get("type") || !tiktokData.get("type").equals(12)) {
            return;
        }
        ReturnStatusChangeWebHooks returnStatusChangeWebHooks = JSON.toJavaObject(tiktokData, ReturnStatusChangeWebHooks.class);
        if (returnStatusChangeWebHooks.getData() == null || !"REFUND".equals(returnStatusChangeWebHooks.getData().getReturnType())) {
            return;
        }

        String returnId = returnStatusChangeWebHooks.getData().getReturnId();
        String orderId = returnStatusChangeWebHooks.getData().getOrderId();
        String returnStatus = returnStatusChangeWebHooks.getData().getReturnStatus(); //对于退款此字段是退款状态，

        log.info("接收到TK退款请求：{}", tiktokData);
        Account accountInfo = this.getShopId(returnStatusChangeWebHooks.getShopId());
        if (accountInfo == null) {
            log.error("tk退款请求无店铺信息：{}", tiktokData);
            return;
        }
        Integer orgId = accountInfo.getOrgId();
        Integer shopId = accountInfo.getId();

        List<OrderRefundRequestInfo> tkRefundList = this.lambdaQuery().eq(OrderRefundRequestInfo::getReturnId, returnId).list();
        if (!CollectionUtils.isEmpty(tkRefundList)) {
            OrderRefundRequestInfo orderRefundRequestInfo = tkRefundList.get(0);
            if (!StringUtils.isEmpty(returnStatus)&&!returnStatus.equalsIgnoreCase(orderRefundRequestInfo.getReturnStatus())) {
                log.info("更新TK退款状态：{}", tiktokData);
                orderRefundRequestInfo.setPlatformRefundStatus(TKRefundStatusEnum.getDesc(returnStatus) != null ? TKRefundStatusEnum.getDesc(returnStatus).getDesc():null);//平台的退款状态。
                orderRefundRequestInfo.settingDefaultSystemUpdate();
                this.updateById(orderRefundRequestInfo);
            }
            log.info("已存在TK退款单：{}", tiktokData);
            return;
        }

        //获取取消单信息
        ReverseOrderResponseMsg reverseOrderResponseMsg = this.sendRequestTkRefundInfo(accountInfo.getId().longValue(), returnStatusChangeWebHooks.getData().getReturnId());
        if (reverseOrderResponseMsg == null || reverseOrderResponseMsg.getData() == null || CollectionUtils.isEmpty(reverseOrderResponseMsg.getData().getReturnOrdersList())) {
             //调用失败后5s后重试
            try {
                Thread.sleep(5000L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            reverseOrderResponseMsg = this.sendRequestTkRefundInfo(accountInfo.getId().longValue(), returnStatusChangeWebHooks.getData().getReturnId());
        }


        if (reverseOrderResponseMsg == null || reverseOrderResponseMsg.getData() == null || CollectionUtils.isEmpty(reverseOrderResponseMsg.getData().getReturnOrdersList())) {
            log.error("获取到TK退款明细信息为空：{}", tiktokData);
            //仅保存基础退款信息，后续定时补偿
            OrderRefundRequestInfo orderRefundRequestInfo = new OrderRefundRequestInfo();
            orderRefundRequestInfo.setOrganizationId(orgId != null ? orgId.longValue() : null);
            orderRefundRequestInfo.setReturnId(returnId);
            orderRefundRequestInfo.settingDefaultSystemCreate();
            this.save(orderRefundRequestInfo);
            return;
        }

        log.info("逆向单获取到退货相关数据：{}", JSONObject.toJSONString(reverseOrderResponseMsg));
        //2.获取业务订单数据
        SaleOrders saleOrders = new SaleOrders();
        saleOrders.setOrderNo(orderId);//平台订单号
        saleOrders.setChannel(com.bizark.boss.api.enm.AccountSaleChannelEnum.TIKTOK.getName()); //渠道
        saleOrders.setOrgId(orgId.longValue()); //组织ID
        saleOrders.setChannelId(shopId.longValue());//店铺ID
        List<SaleOrderVoTicket> saleOrdersLists = saleOrdersService.selectOrderItems(saleOrders);//查询订单明细信息
        if (CollectionUtils.isEmpty(saleOrdersLists)) {
            return;
        }

        //存储对应退款请求信息
        List<TiktokReturnRefund> returnOrdersList = reverseOrderResponseMsg.getData().getReturnOrdersList();
        for (TiktokReturnRefund tiktokReturnRefund :returnOrdersList) {
            if (TiktokReturnRefundApiType.REFUND.getType().equalsIgnoreCase(tiktokReturnRefund.getReturnType())) {
                this.saveTkRefudnRequestInfo(tiktokReturnRefund, accountInfo, saleOrdersLists);
            }
//            else if (TiktokReturnRefundApiType.RETURN_AND_REFUND.getType().equalsIgnoreCase(tiktokReturnRefund.getReturnType())) {
//                tiktokReturnRefund.setChannelId(accountInfo.getFlag());//设置flag,用于JSON使用
//                rabbitTemplate.convertAndSend(MQDefine.TIKTOK_RETURN,  (JSONObject) JSONObject.toJSON(Arrays.asList(tiktokReturnRefund)));
//            }else if (TiktokReturnRefundApiType.REPLACEMENT.getType().equalsIgnoreCase(tiktokReturnRefund.getReturnType())){
//                //换货
//            }
        }
    }


    /*
     * @description: 手动拉取退货或退款信息
     * @author: Moore
     * @date: 2025/3/5 17:31
     * @param
     * @param shopId
     * @param orderNO
     * @return: void
    **/
    public void manualRequestTkReturnRefundInfo(Long shopId, String orderNo) {

        ReverseOrderResponseMsg reverseOrderResponseMsg = null;
        try {
            if (shopId == null || StringUtils.isEmpty(orderNo)) {
                return ;
            }
            JSONObject req = new JSONObject();
            req.put("order_ids", Arrays.asList(orderNo));
            log.info("查看Tk退款请求信息：{}", req);
            reverseOrderResponseMsg = tikTokUtil.postTikTokShopV2(req.toJSONString(), TikTokApiEnums.GET_REVERSE_ORDER_LIST_V2.getUrl(), TikTokApiEnums.GET_REVERSE_ORDER_LIST_V2_NEW.getPath(), ReverseOrderResponseMsg.class, Long.valueOf(shopId), null);
            if (reverseOrderResponseMsg == null || reverseOrderResponseMsg.getData() == null || CollectionUtils.isEmpty(reverseOrderResponseMsg.getData().getReturnOrdersList())) {
                Thread.sleep(5000L);
                reverseOrderResponseMsg = tikTokUtil.postTikTokShopV2(req.toJSONString(), TikTokApiEnums.GET_REVERSE_ORDER_LIST_V2.getUrl(), TikTokApiEnums.GET_REVERSE_ORDER_LIST_V2_NEW.getPath(), ReverseOrderResponseMsg.class, Long.valueOf(shopId), null);
            }
        } catch (Exception e) {
            log.error("调用TK退款信息异常：{}", e);
        }

        if (reverseOrderResponseMsg == null || reverseOrderResponseMsg.getData() == null || CollectionUtils.isEmpty(reverseOrderResponseMsg.getData().getReturnOrdersList())) {
            return;
        }

        Account account = accountService.selectByAccountId(shopId.intValue());
        if (account == null) {
            return;
        }
        List<TiktokReturnRefund> returnOrdersList = reverseOrderResponseMsg.getData().getReturnOrdersList();
        for (TiktokReturnRefund tiktokReturnRefund :returnOrdersList) {
            if (TiktokReturnRefundApiType.REFUND.getType().equalsIgnoreCase(tiktokReturnRefund.getReturnType())) {

                SaleOrders saleOrders = new SaleOrders();
                saleOrders.setOrderNo(orderNo);//平台订单号
                saleOrders.setChannel(com.bizark.boss.api.enm.AccountSaleChannelEnum.TIKTOK.getName()); //渠道
                saleOrders.setOrgId(account.getOrgId().longValue()); //组织ID
                saleOrders.setChannelId(shopId.longValue());//店铺ID
                List<SaleOrderVoTicket> saleOrdersLists = saleOrdersService.selectOrderItems(saleOrders);//查询订单明细信息
                if (CollectionUtils.isEmpty(saleOrdersLists)) {
                    return;
                }

                this.saveTkRefudnRequestInfo(tiktokReturnRefund, account, saleOrdersLists);
            }else {  //退货或者退款信息 //TODO 若换货拆分需调整逻辑
                tiktokReturnRefund.setChannelId(account.getFlag());//设置flag,用于JSON使用

                //外层总退款金额
                TikTokRefundAmount tikTokRefundAmountMain = tiktokReturnRefund.getTikTokRefundAmount();
                if (tikTokRefundAmountMain != null) {
                    tiktokReturnRefund.setCurrency(tikTokRefundAmountMain.getCurrency());
                    tiktokReturnRefund.setRefundShippingFee(tikTokRefundAmountMain.getRefundShippingFee());
                    tiktokReturnRefund.setRefundSubtotal(tikTokRefundAmountMain.getRefundSubtotal());
                    tiktokReturnRefund.setRefundTax(tikTokRefundAmountMain.getRefundTax());
                    tiktokReturnRefund.setRefundTotal(tikTokRefundAmountMain.getRefundTotal());
                    tiktokReturnRefund.setRefundSubtotal(tikTokRefundAmountMain.getRefundSubtotal());
                }

                tiktokReturnRefund.setCreateTime(new Date(tiktokReturnRefund.getTkCreateTime() * 1000));
                tiktokReturnRefund.setUpdateTime(new Date(tiktokReturnRefund.getTkUpdateTime() * 1000));

                //行明细退款金额
                List<TiktokReturnRefundItem> returnRefundItemList = tiktokReturnRefund.getReturnRefundItemList();
                for (TiktokReturnRefundItem tiktokReturnRefundItem : returnRefundItemList) {
                    TikTokRefundAmount tikTokRefundAmount = tiktokReturnRefundItem.getTikTokRefundAmount();
                    tiktokReturnRefundItem.setChannelId(account.getFlag());
                    if (tikTokRefundAmount!=null){
                        tiktokReturnRefundItem.setCurrency(tikTokRefundAmount.getCurrency());
                        tiktokReturnRefundItem.setOrderId(tiktokReturnRefund.getOrderId());
                        tiktokReturnRefundItem.setRefundShippingFee(tikTokRefundAmount.getRefundShippingFee());
                        tiktokReturnRefundItem.setRefundTax(tikTokRefundAmount.getRefundTax());
                        tiktokReturnRefundItem.setRefundTotal(tikTokRefundAmount.getRefundTotal());
                        tiktokReturnRefundItem.setReturnId(tiktokReturnRefund.getReturnId());
                        tiktokReturnRefund.setRefundSubtotal(tikTokRefundAmount.getRefundSubtotal());
                    }
                }
                List<TiktokReturnRefund> tiktokReturnRefunds = Arrays.asList(tiktokReturnRefund);
                rabbitTemplate.convertAndSend(MQDefine.TIKTOK_RETURN,   JSONArray.parseArray(JSONObject.toJSONString(tiktokReturnRefunds)));
            }
        }

    }


    /**
     * 定时刷新无明细行Tk数据
     *
     * @param returnId
     */
    @Override
    public void compensationTkRefundNoItems(String returnId) {

        int pageNo = 1;
        int pageSize = 200;
        while (true) {
            try {
                TimeUnit.SECONDS.sleep(5);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            PageHelper.startPage(pageNo, pageSize);
            List<OrderRefundRequestInfo> orderRefundRequestInfos = this.baseMapper.selectTkNoItemsList(returnId);
            if (CollectionUtils.isEmpty(orderRefundRequestInfos)) {
                return;
            }

            ReverseOrderResponseMsg reverseOrderResponseMsg = null;
            for (OrderRefundRequestInfo orderRefundRequestInfo : orderRefundRequestInfos) {
                try {
                    if (orderRefundRequestInfo.getShopId() == null || StringUtils.isEmpty(orderRefundRequestInfo.getReturnId())
                            || orderRefundRequestInfo.getOrderPrimaryId() == null) {
                        continue;
                    }
                    JSONObject req = new JSONObject();
                    req.put("return_ids", Arrays.asList(orderRefundRequestInfo.getReturnId()));
                    log.info("查看Tk退款请求信息：{}", req);
                    reverseOrderResponseMsg = tikTokUtil.postTikTokShopV2(req.toJSONString(), TikTokApiEnums.GET_REVERSE_ORDER_LIST_V2.getUrl(), TikTokApiEnums.GET_REVERSE_ORDER_LIST_V2_NEW.getPath(), ReverseOrderResponseMsg.class, Long.valueOf(orderRefundRequestInfo.getShopId()), null);
                    if (reverseOrderResponseMsg == null || reverseOrderResponseMsg.getData() == null || CollectionUtils.isEmpty(reverseOrderResponseMsg.getData().getReturnOrdersList())) {
                        Thread.sleep(5000L);
                        reverseOrderResponseMsg = tikTokUtil.postTikTokShopV2(req.toJSONString(), TikTokApiEnums.GET_REVERSE_ORDER_LIST_V2.getUrl(), TikTokApiEnums.GET_REVERSE_ORDER_LIST_V2_NEW.getPath(), ReverseOrderResponseMsg.class, Long.valueOf(orderRefundRequestInfo.getShopId()), null);
                    }
                } catch (Exception e) {
                    log.error("调用TK退款信息异常：{}", e);
                }

                if (reverseOrderResponseMsg == null || reverseOrderResponseMsg.getData() == null || CollectionUtils.isEmpty(reverseOrderResponseMsg.getData().getReturnOrdersList())) {
                    continue;
                }


                log.info("逆向单获取到退货相关数据：{}", JSONObject.toJSONString(reverseOrderResponseMsg));
                //2.获取业务订单数据
                SaleOrders saleOrders = new SaleOrders();
                saleOrders.setId(orderRefundRequestInfo.getOrderPrimaryId());//平台订单号
                List<SaleOrderVoTicket> saleOrdersLists = saleOrdersService.selectOrderItems(saleOrders);//查询订单明细信息
                if (CollectionUtils.isEmpty(saleOrdersLists)) {
                    continue;
                }

                //存储对应退款请求信息
                List<TiktokReturnRefund> returnOrdersList = reverseOrderResponseMsg.getData().getReturnOrdersList();
                for (TiktokReturnRefund tiktokReturnRefund : returnOrdersList) {
                    if (TiktokReturnRefundApiType.REFUND.getType().equalsIgnoreCase(tiktokReturnRefund.getReturnType())) {
                      this.saveTkRefudnRequestCompensationInfo(tiktokReturnRefund, orderRefundRequestInfo, saleOrdersLists);
                    }
                }

            }

            pageNo++;
        }
    }


    /**
     * @description: 保存退款请求信息
     * @author: Moore
     * @date: 2024/8/21 15:44
     * @param
     * @return: void
     **/
    public void saveTkRefudnRequestInfo(TiktokReturnRefund tiktokReturnRefund, Account account, List<SaleOrderVoTicket> saleOrderVoTickets) {
        SaleOrderVoTicket orderInfo = new SaleOrderVoTicket();
        if (!CollectionUtils.isEmpty(saleOrderVoTickets)) {
            orderInfo = saleOrderVoTickets.get(0);
        }
        Map<String, SaleOrderVoTicket> saleOrderMap = saleOrderVoTickets.stream().collect(Collectors.toMap(SaleOrderVoTicket::getChannelOrderItemId, each -> each, (value1, value2) -> value1));
        //保存主表信息
        OrderRefundRequestInfo orderRefundRequest = new OrderRefundRequestInfo();
        BeanUtil.copyProperties(tiktokReturnRefund, orderRefundRequest);
        if (!StringUtils.isEmpty(orderRefundRequest.getReturnStatus())){
            orderRefundRequest.setPlatformRefundStatus(TKRefundStatusEnum.getDesc(orderRefundRequest.getReturnStatus()) != null ? TKRefundStatusEnum.getDesc(orderRefundRequest.getReturnStatus()).getDesc():null);//平台的退款状态。
        }
        orderRefundRequest.setCreateTime(tiktokReturnRefund.getTkCreateTime()); //平台创建时间
        orderRefundRequest.setUpdateTime(tiktokReturnRefund.getTkUpdateTime());//平台更新时间
        orderRefundRequest.setReverseRequestTime(DateUtil.BjToPstByTimestamp(orderRefundRequest.getCreateTime())); //业务请求真实PSt时间
        orderRefundRequest.setOrderId(orderInfo.getOrderNo()); //订单号
        orderRefundRequest.setOrderPrimaryId(orderInfo.getId());//订单主键
        orderRefundRequest.setShopId(account.getId().longValue());
//        orderRefundRequest.setRefundType(OrderRefundEnum.INTERNAL.getCodeType()); //默认站内退款
        orderRefundRequest.setOrganizationId(account.getOrgId().longValue()); //组织ID
        orderRefundRequest.setChannel(AccountSaleChannelEnum.TIKTOK.getValue());
        orderRefundRequest.setRefundOrderStatus(OrderRefundEnum.PENDING.getCodeType()); //退款订单类型 待处理
        orderRefundRequest.setRefundStatus(RefundStateEnum.NOTAPPLIED.getCode()); //退款状态 未申请
        orderRefundRequest.setBuyName(orderInfo.getBuyName());//买家名称 （用于退款请求页面展示使用）

        orderRefundRequest.setPstChannelCreated(orderInfo.getPstChannelCreated()); //订单时间
        orderRefundRequest.setShippingMethodCode(orderInfo.getShippingMethodCode()); //发货方式
        orderRefundRequest.setShipmentDate(orderInfo.getShipmentDate());//发货时间
        if ("TIKTOK".equalsIgnoreCase(orderInfo.getShippingMethodCode())){ //面单类型
            orderRefundRequest.setBillSource(BillSourceEnum.PLATFORM.getValue());
        }else {
            orderRefundRequest.setBillSource(BillSourceEnum.SHOP.getValue());
        }

        orderRefundRequest.settingDefaultSystemCreate();
        this.save(orderRefundRequest); //存储退款主信息


        //创建工单
        ScTicket scTicketCrete = new ScTicket();
        scTicketCrete.setOrderId(orderInfo.getId());
        scTicketCrete.setTicketType(ScTicketConstant.TICKET_TYPE_REFUND_REQUEST);  //工单类型 站外
        scTicketCrete.setTicketName("客户申请订单号:" + tiktokReturnRefund.getOrderId() + "退款"); //工单名称
        scTicketCrete.setRemark("店铺名称:" + StrUtil.nullToDefault(account.getTitle(), "") + " 订单号:" + tiktokReturnRefund.getOrderId() ); //描述

        scTicketCrete.setTicketSource("tiktok");// 工单来源(订单API)
        scTicketCrete.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH);
        scTicketCrete.setReverseOrderId(orderRefundRequest.getReturnId());
        scTicketCrete.setAmazonOrderId(orderRefundRequest.getOrderId());
        scTicketCrete.setSourceId(orderRefundRequest.getId());
        scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(orderRefundRequest.getOrderId().toString(), "l", 5, "0") + RandomUtil.randomNumbers(4));
        scTicketCrete.setBusinessApplyTime(orderRefundRequest.getReverseRequestTime());
        //临时表
        scTicketCrete.setSourceDocument("order_refund_request_info"); //来源单据 退款表
        scTicketCrete.setShopId(Long.valueOf(account.getId()));  //店铺
        scTicketCrete.setOrganizationId(Long.valueOf(account.getOrgId()));//组织ID
        scTicketCrete.setReturnReason(orderRefundRequest.getReturnReasonText());
        scTicketService.insertScTicket(scTicketCrete); //创建工单


        //如果退款请求为已取消,已完成则关闭工单
        if(OrderRefundStatusEnum.RETURN_OR_REFUND_REQUEST_CANCEL.getCode().equals(orderRefundRequest.getReturnStatus()) ||
                OrderRefundStatusEnum.RETURN_AND_REFUND_REQUEST_CANCEL.getCode().equals(orderRefundRequest.getReturnStatus()) ||
                OrderRefundStatusEnum.RETURN_OR_REFUND_REQUEST_COMPLETE.getCode().equals(orderRefundRequest.getReturnStatus()) ||
                OrderRefundStatusEnum.RETURN_AND_REFUND_REQUEST_COMPLETE.getCode().equals(orderRefundRequest.getReturnStatus())||
                OrderRefundStatusEnum.RETURN_OR_REFUND_REQUEST_SUCCESS.getCode().equals(orderRefundRequest.getReturnStatus()) ||
                OrderRefundStatusEnum.RETURN_AND_REFUND_REQUEST_SUCCESS.getCode().equals(orderRefundRequest.getReturnStatus())){
            ScTicket ticket = new ScTicket();
            ticket.setId(scTicketCrete.getId());
            ticket.setTicketStatus("CLOSED");
            ticket.settingDefaultSystemUpdate();
            scTicketService.updateScTicketStatus(ticket);
        }







        //更新取消主表工单ID
        OrderRefundRequestInfo updateRequestInfo = new OrderRefundRequestInfo();
        updateRequestInfo.setId(orderRefundRequest.getId());
        updateRequestInfo.setTicketId(scTicketCrete.getId());
        this.updateById(updateRequestInfo);


        //保存明细信息
        List<TiktokReturnRefundItem> returnRefundItemList = tiktokReturnRefund.getReturnRefundItemList();
        for (TiktokReturnRefundItem tiktokReturnRefundItem : returnRefundItemList) {
            SaleOrderVoTicket saleOrderVoTicket = saleOrderMap.get(tiktokReturnRefundItem.getOrderLineItemId()); //获取对应订单行信息

            //行明细信息
            OrderRefundRequestItem recordDetail = new OrderRefundRequestItem();
            recordDetail.setOrganizationId(account.getOrgId().longValue()); //组织ID
            recordDetail.setReturnId(orderRefundRequest.getReturnId()); //主退货ID
            recordDetail.setOrderLineItemId(tiktokReturnRefundItem.getOrderLineItemId()); //订单明细行ID
            recordDetail.setProductName(tiktokReturnRefundItem.getProductName()); //产品名 APi获取
            recordDetail.setReturnLineItemId(tiktokReturnRefundItem.getReturnLineItemId()); //退货行唯一ID
            recordDetail.setSellerSku(tiktokReturnRefundItem.getSellerSku()); //SellerSKu
//            recordDetail.setUrl(tiktokReturnRefundItem.getProductImage() != null ? tiktokReturnRefundItem.getProductImage().getUrl() : null);  //图片信息
            recordDetail.setSkuId(tiktokReturnRefundItem.getSkuId()); //SkuID
            recordDetail.setSkuName(tiktokReturnRefundItem.getSkuName()); //skuName
            recordDetail.setCurrency(tiktokReturnRefundItem.getTikTokRefundAmount() != null ? tiktokReturnRefundItem.getTikTokRefundAmount().getCurrency() : null);//币种
            recordDetail.setOrderId(orderInfo.getOrderNo()); //订单号
            recordDetail.setOrderPrimaryId(orderInfo.getId());//主键ID
            //tiktok退款明细信息
            TikTokRefundAmount tikTokRefundAmount = tiktokReturnRefundItem.getTikTokRefundAmount();
            if (tikTokRefundAmount != null) {
                recordDetail.setCurrency(tikTokRefundAmount.getCurrency()); //币种
                recordDetail.setRefundShippingFee(tikTokRefundAmount.getRefundShippingFee()); //退款运费
                recordDetail.setRefundSubtotal(tikTokRefundAmount.getRefundSubtotal());//退款总额
                recordDetail.setRefundTax(tikTokRefundAmount.getRefundTax());//退款税费
                recordDetail.setRefundTotal(tikTokRefundAmount.getRefundTotal()); ////退款总额 （实际页面展示此退款金额，接口金额）
                recordDetail.setRetailDeliveryFee(tikTokRefundAmount.getRetailDeliveryFee());
            }
            recordDetail.setAccountFlag(account.getFlag());//店铺Flag
            recordDetail.setAccountId(account.getId()); //店铺ID
            recordDetail.setRefundType(OrderRefundEnum.INTERNAL.getCodeType()); //退款类型

            if (saleOrderVoTicket != null) {
                BigDecimal amount = saleOrderVoTicket.getItemAmount();
                Integer quantity = saleOrderVoTicket.getQuantity();
                BigDecimal quantityBig = new BigDecimal(quantity);
                recordDetail.setReturnTotalAmount(saleOrderVoTicket.getItemAmount()); //订单行总价
                recordDetail.setUnitPrice(quantity == 0 ? amount : amount.divide(quantityBig)); //订单行单价
                recordDetail.setErpSku(saleOrderVoTicket.getErpsku()); //ERPSKU
                recordDetail.setRefundQuantity(quantity != null ? quantity.longValue() : null);//退款数量（接口未返回数量，采用订单行数量）

                //包裹信息
                if (saleOrderVoTicket.getItemId() != null) {
                    List<SaleOrderItemVO> saleOrderItemVOS = saleOrderItemsMapper.selectOrderShipmentInfo(orderInfo.getId());
                    String tranckNos = saleOrderItemVOS.stream().filter(item -> item.getHeadItemId().equals(saleOrderVoTicket.getItemId()) && !StringUtils.isEmpty(item.getTrackNo())).map(SaleOrderItemVO::getTrackNo).distinct().collect(Collectors.joining(","));
                    if (!StringUtils.isEmpty(tranckNos)) {
                        recordDetail.setShipmentTrackNo(tranckNos);
                    }
                }


            }
            //保存明细信息
            orderRefundRequestInfoItemMapper.insert(recordDetail);
        }


    }


    /**
     * @description: 调用退款请求信息
     * @author: Moore
     * @date: 2024/9/13 9:53
     * @param
     * @param shopId
     * @param refrundId
     * @return: com.bizark.op.api.entity.op.titok.response.ReverseOrderResponseMsg
    **/
    public ReverseOrderResponseMsg sendRequestTkRefundInfo(Long shopId, String refrundId) {
        try {
            if (shopId == null || StringUtils.isEmpty(refrundId)) {
                return null;
            }

            JSONObject req = new JSONObject();
            req.put("return_ids", Arrays.asList(refrundId));
            log.info("查看Tk退款请求信息：{}", req);
            ReverseOrderResponseMsg tikTokShopReturn = tikTokUtil.postTikTokShopV2(req.toJSONString(), TikTokApiEnums.GET_REVERSE_ORDER_LIST_V2.getUrl(), TikTokApiEnums.GET_REVERSE_ORDER_LIST_V2_NEW.getPath(), ReverseOrderResponseMsg.class, Long.valueOf(shopId), null);
            if (tikTokShopReturn == null || tikTokShopReturn.getData() == null || CollectionUtils.isEmpty(tikTokShopReturn.getData().getReturnOrdersList())) {
                Thread.sleep(5000L);
                tikTokShopReturn = tikTokUtil.postTikTokShopV2(req.toJSONString(), TikTokApiEnums.GET_REVERSE_ORDER_LIST_V2.getUrl(), TikTokApiEnums.GET_REVERSE_ORDER_LIST_V2_NEW.getPath(), ReverseOrderResponseMsg.class, Long.valueOf(shopId), null);
            }
            return tikTokShopReturn;
        } catch (Exception e) {
            log.error("调用TK退款信息异常：{}", e);
        }
        return null;
    }

    /**
     * @param
     * @param shopId
     * @description:根据平台店铺ID获取店铺主键ID
     * @author: Moore
     * @date: 2023/10/24 17:57
     * @return: java.lang.Integer
     **/
    public Account getShopId(String shopId) {
        if (StringUtil.isEmpty(shopId)) {
            return null;
        }
        Account account = new Account();
        account.setType("tiktok");
        List<Account> accounts = accountService.selectAccountListRefactor(account);
        for (Account accountItem : accounts) {
            JSONObject accountItemJson = JSONObject.parseObject(accountItem.getConnectStr());
            if (null != accountItemJson) {
                if (shopId.equals(accountItemJson.getString("shopId")))
                    return accountItem;
            }
        }
        return null;
    }


    /**
     * Description: 查询退款请求信息
     *
     * @param query
     * @param page
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/8/22
     */
    @Override
    public List<CusCenterOrderRefundVO> selectList(CusCenterOrderRefundQuery query, Page<CusCenterOrderRefundDTO> page) {
        List<CusCenterOrderRefundDTO> result =orderRefundRequestInfoMapper.selectRefundRequestList(query);
        if (page != null) {
            page.paging(result);
        }
        return  BeanCopyUtils.asmCopyList(result, CusCenterOrderRefundVO.class);
    }

    /**
     * Description: 导出退款请求信息
     *
     * @param query
     * @param response
     * @param authUserEntity
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/8/26
     */
    @Override
    public void originalExportOrderRequestRefund(CusCenterOrderRefundQuery query, HttpServletResponse response, UserEntity authUserEntity) {
        TaskCenterRequest request = new TaskCenterRequest();
        request.setOrgId(query.getContextId());
        request.setTaskCode("erp.cus_center_order_request_refund.export");
        List<Object> list = new ArrayList<>();
        list.add(JSON.toJSONString(query));
        request.setArgs(list);
        taskCenterService.startTask(request, authUserEntity);
    }

    /**
     * Description: 查询退款请求信息
     *
     * @param query
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/8/22
     */
    @Override
    public List<CusCenterOrderRefundVO> selectExportList(CusCenterOrderRefundQuery query) {
        List<CusCenterOrderRefundDTO> result =orderRefundRequestInfoMapper.selectRefundRequestList(query);
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        List<CusCenterOrderRefundVO> cusCenterOrderRefundVOS = BeanCopyUtils.asmCopyList(result, CusCenterOrderRefundVO.class);
        return  cusCenterOrderRefundVOS;
    }


    /**
     * Description:
     *
     * @param orderRefundId
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/8/29
     */
    @Override
    public OrderRefundInfoVO getRequestOrderRefundDetailInfo(String orderRefundId) {
        OrderRefundInfoVO orderRefundInfoVO = new OrderRefundInfoVO();
        QueryWrapper<OrderRefundRequestItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("return_id",orderRefundId);
        List<OrderRefundRequestItem> orderRefundRequestItemList = orderRefundRequestItemService.list(queryWrapper);
        if(CollectionUtil.isNotEmpty(orderRefundRequestItemList)) {
            String[] refundSellerSku = orderRefundRequestItemList.stream().map(s -> s.getSellerSku() + "|" + s.getOrderLineItemId()).distinct().toArray(String[]::new);
            orderRefundInfoVO.setOrderRefundSellerSku(refundSellerSku);
        }
        return  orderRefundInfoVO;
    }

    /**
     * 补充TIKTOk无明细行信息
     *
     * @param tiktokReturnRefund
     * @param orderRefundRequestInfo
     * @param
     */
    private void saveTkRefudnRequestCompensationInfo(TiktokReturnRefund tiktokReturnRefund, OrderRefundRequestInfo orderRefundRequestInfo, List<SaleOrderVoTicket> saleOrderVoTickets) {

        Map<String, SaleOrderVoTicket> saleOrderMap = saleOrderVoTickets.stream().collect(Collectors.toMap(SaleOrderVoTicket::getChannelOrderItemId, each -> each, (value1, value2) -> value1));

        //保存明细信息
        List<TiktokReturnRefundItem> returnRefundItemList = tiktokReturnRefund.getReturnRefundItemList();
        for (TiktokReturnRefundItem tiktokReturnRefundItem : returnRefundItemList) {
            SaleOrderVoTicket saleOrderVoTicket = saleOrderMap.get(tiktokReturnRefundItem.getOrderLineItemId()); //获取对应订单行信息

            //行明细信息
            OrderRefundRequestItem recordDetail = new OrderRefundRequestItem();
            recordDetail.setOrganizationId(orderRefundRequestInfo.getOrganizationId()); //组织ID
            recordDetail.setReturnId(orderRefundRequestInfo.getReturnId()); //主退货ID
            recordDetail.setOrderLineItemId(tiktokReturnRefundItem.getOrderLineItemId()); //订单明细行ID
            recordDetail.setProductName(tiktokReturnRefundItem.getProductName()); //产品名 APi获取
            recordDetail.setReturnLineItemId(tiktokReturnRefundItem.getReturnLineItemId()); //退货行唯一ID
            recordDetail.setSellerSku(tiktokReturnRefundItem.getSellerSku()); //SellerSKu
            recordDetail.setSkuId(tiktokReturnRefundItem.getSkuId()); //SkuID
            recordDetail.setSkuName(tiktokReturnRefundItem.getSkuName()); //skuName
            recordDetail.setCurrency(tiktokReturnRefundItem.getTikTokRefundAmount() != null ? tiktokReturnRefundItem.getTikTokRefundAmount().getCurrency() : null);//币种
            recordDetail.setOrderId(orderRefundRequestInfo.getOrderId()); //订单号
            recordDetail.setOrderPrimaryId(orderRefundRequestInfo.getOrderPrimaryId());//主键ID
            //tiktok退款明细信息
            TikTokRefundAmount tikTokRefundAmount = tiktokReturnRefundItem.getTikTokRefundAmount();
            if (tikTokRefundAmount != null) {
                recordDetail.setCurrency(tikTokRefundAmount.getCurrency()); //币种
                recordDetail.setRefundShippingFee(tikTokRefundAmount.getRefundShippingFee()); //退款运费
                recordDetail.setRefundSubtotal(tikTokRefundAmount.getRefundSubtotal());//退款总额
                recordDetail.setRefundTax(tikTokRefundAmount.getRefundTax());//退款税费
                recordDetail.setRefundTotal(tikTokRefundAmount.getRefundTotal()); ////退款总额 （实际页面展示此退款金额，接口金额）
                recordDetail.setRetailDeliveryFee(tikTokRefundAmount.getRetailDeliveryFee());
            }
            recordDetail.setAccountId(orderRefundRequestInfo.getShopId().intValue()); //店铺ID
            Account accountInfo = accountService.getById(orderRefundRequestInfo.getShopId());
            if (accountInfo != null) {
                recordDetail.setAccountFlag(accountInfo.getFlag());//店铺Flag
            }
            recordDetail.setRefundType(OrderRefundEnum.INTERNAL.getCodeType()); //退款类型
            if (saleOrderVoTicket != null) {
                BigDecimal amount = saleOrderVoTicket.getItemAmount();
                Integer quantity = saleOrderVoTicket.getQuantity();
                BigDecimal quantityBig = new BigDecimal(quantity);
                recordDetail.setReturnTotalAmount(saleOrderVoTicket.getItemAmount()); //订单行总价
                recordDetail.setUnitPrice(quantity == 0 ? amount : amount.divide(quantityBig)); //订单行单价
                recordDetail.setErpSku(saleOrderVoTicket.getErpsku()); //ERPSKU
                recordDetail.setRefundQuantity(quantity != null ? quantity.longValue() : null);//退款数量（接口未返回数量，采用订单行数量）

                //包裹信息
                if (saleOrderVoTicket.getItemId() != null) {
                    List<SaleOrderItemVO> saleOrderItemVOS = saleOrderItemsMapper.selectOrderShipmentInfo(orderRefundRequestInfo.getOrderPrimaryId());
                    String tranckNos = saleOrderItemVOS.stream().filter(item -> item.getHeadItemId().equals(saleOrderVoTicket.getItemId()) && !StringUtils.isEmpty(item.getTrackNo())).map(SaleOrderItemVO::getTrackNo).distinct().collect(Collectors.joining(","));
                    if (!StringUtils.isEmpty(tranckNos)) {
                        recordDetail.setShipmentTrackNo(tranckNos);
                    }
                }
            }
            //保存明细信息
            orderRefundRequestInfoItemMapper.insert(recordDetail);
        }


    }
}
