package com.bizark.op.service.service.ticket;

import com.bizark.op.api.cons.ScTicketConstant;
import com.bizark.op.api.entity.op.ticket.*;
import com.bizark.op.api.service.ticket.IScReissueAccessoriesService;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.service.mapper.ticket.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 补发配件Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-16
 */
@Service
public class ScReissueAccessoriesServiceImpl implements IScReissueAccessoriesService {
    @Autowired
    private ScReissueAccessoriesMapper scReissueAccessoriesMapper;

    @Autowired
    private ScOrderReissueMapper scOrderReissueMapper;


    @Autowired
    private ScTicketMapper scTicketMapper;

    @Autowired
    private ScCustomerMapper scCustomerMapper;

    @Autowired
    private ScManualOrderMapper scManualOrderMapper;

    /**
     * 查询补发配件
     *
     * @param id 补发配件ID
     * @return 补发配件
     */
    @Override
    public ScReissueAccessories selectScReissueAccessoriesById(Long id) {
        return scReissueAccessoriesMapper.selectScReissueAccessoriesById(id);
    }

    /**
     * 查询补发配件列表
     *
     * @param scReissueAccessories 补发配件
     * @return 补发配件
     */
    @Override
    public List<ScReissueAccessories> selectScReissueAccessoriesList(ScReissueAccessories scReissueAccessories) {
        return scReissueAccessoriesMapper.selectScReissueAccessoriesList(scReissueAccessories);
    }

    @Override
    public List<ScReissueAccessories> selectScReissueAccessoriesByReissueId(Long reissueId) {
        return scReissueAccessoriesMapper.selectScReissueAccessoriesByReissueId(reissueId);
    }

    /**
     * 新增补发配件
     *
     * @param scReissueAccessories 补发配件
     * @return 结果
     */
    @Override
    public int insertScReissueAccessories(ScReissueAccessories scReissueAccessories) {
        scReissueAccessories.setSendStatus("1");
//        scReissueAccessories.setOrganizationId(SecurityUtils.getOrganizationId());
//        scReissueAccessories.setCreateBy(SecurityUtils.getUsername());
        scReissueAccessories.setCreatedAt(DateUtils.getNowDate());
        return scReissueAccessoriesMapper.insertScReissueAccessories(scReissueAccessories);
    }

    /**
     * 修改补发配件
     *
     * @param scReissueAccessories 补发配件
     * @return 结果
     */
    @Override
    public int updateScReissueAccessories(ScReissueAccessories scReissueAccessories) {
//        scReissueAccessories.setUpdateBy(SecurityUtils.getUsername());
        scReissueAccessories.setUpdatedAt(DateUtils.getNowDate());
        return scReissueAccessoriesMapper.updateScReissueAccessories(scReissueAccessories);
    }

    @Override
    public void saveScReissueAccessories(List<ScReissueAccessories> scReissueAccessoriesList) {
        for (ScReissueAccessories scReissueAccessories : scReissueAccessoriesList) {
            if (null == scReissueAccessories.getId()) {
                scReissueAccessories.settingDefaultCreate();
                this.insertScReissueAccessories(scReissueAccessories);
            } else {
                scReissueAccessories.settingDefaultUpdate();
                this.updateScReissueAccessories(scReissueAccessories);
            }
        }
    }



    /**
     * @description: 补发配件去重保存
     * @author: Moore
     * @date: 2023/10/25 14:31
     * @param
     * @param reissueAccessoriesList
     * @param id  补发主表id
     * @return: void
    **/
    @Override
    public void saveScReissueAccessoriesDistinct(List<ScReissueAccessories> reissueAccessoriesList, Long id) {
        if (null == id) {
            for (ScReissueAccessories scReissueAccessories : reissueAccessoriesList) {
                if (null == scReissueAccessories.getId()) {
                    scReissueAccessories.settingDefaultCreate();
                    this.insertScReissueAccessories(scReissueAccessories);
                } else {
                    scReissueAccessories.settingDefaultUpdate();
                    this.updateScReissueAccessories(scReissueAccessories);
                }
            }
        } else {
            for (ScReissueAccessories scReissueAccessories : reissueAccessoriesList) {
                if (null == scReissueAccessories.getId()) {
                    scReissueAccessories.settingDefaultCreate();
                    this.insertScReissueAccessories(scReissueAccessories);
                } else {
                    scReissueAccessories.settingDefaultUpdate();
                    this.updateScReissueAccessories(scReissueAccessories);
                }
            }
        }
    }

    @Override
    public int updateReissueAccessoriesSendStatus(ScReissueAccessories scReissueAccessories) {
        return scReissueAccessoriesMapper.updateReissueAccessoriesSendStatus(scReissueAccessories);
    }

    @Override
    public int updateAccessoriesTrackingNo(ScReissueAccessories scReissueAccessories) {
        return scReissueAccessoriesMapper.updateAccessoriesTrackingNo(scReissueAccessories);
    }

    /**
     * 批量删除补发配件
     *
     * @param ids 需要删除的补发配件ID
     * @return 结果
     */
    @Override
    public int deleteScReissueAccessoriesByIds(Long[] ids) {
        for (Long id : ids) {
            deleteScReissueAccessoriesCheck(id);
        }
        return scReissueAccessoriesMapper.deleteScReissueAccessoriesByIds(ids);
    }

    private void deleteScReissueAccessoriesCheck(Long id) {
        ScReissueAccessories scReissueAccessories = this.selectScReissueAccessoriesById(id);
        if (null != scReissueAccessories) {
            ScOrderReissue scOrderReissue = scOrderReissueMapper.selectScOrderReissueById(scReissueAccessories.getReissueId());
            if (!"NEW".equals(scOrderReissue.getStatus()) && !"SUBMIT".equals(scOrderReissue.getStatus()) && !"RETURN".equals(scOrderReissue.getStatus())) {
                throw new CustomException("已处理的补发不可删除配件明细");
            }
        }
    }

    /**
     * 删除补发配件信息
     *
     * @param id 补发配件ID
     * @return 结果
     */
    @Override
    public int deleteScReissueAccessoriesById(Long id) {
        deleteScReissueAccessoriesCheck(id);
        return scReissueAccessoriesMapper.deleteScReissueAccessoriesById(id);
    }


//    @Override
//    public ScOrderReissue selectAccessoriesReissue(Long ticketId, Long contextId) {
//        ScOrderReissue scOrderReissue = new ScOrderReissue();
//        List<ScOrderReissue> scOrderReissueList = new ArrayList<>();
//
//        ScOrderReissue orderReissue = new ScOrderReissue();
//        orderReissue.setReissueType("ACCESSORIES");
//        orderReissue.setTicketId(ticketId);
//        //补发配件详情查询
//        scOrderReissueList = scOrderReissueMapper.selectGoodsReissue(orderReissue);
//        if (!CollectionUtils.isEmpty(scOrderReissueList)) {
//            scOrderReissue = selectScOrderReissue(scOrderReissueList, scOrderReissue, contextId);
//        }
//        //补发配件弹窗查询
//        else {
//            scOrderReissueList = scOrderReissueMapper.selectReissueGoods(ticketId);
//            scOrderReissue = selectScOrderReissue(scOrderReissueList, scOrderReissue, contextId);
//        }
//        return scOrderReissue;
//    }

    @Override
    public List<ScReissueAccessories> selectAccessoriesDetailsList(Long contextId,Long[] productIds,String ccessoriesName) {
        if (productIds != null && productIds.length != 0) {
            Arrays.stream(productIds).distinct().collect(Collectors.toList());
            return  scOrderReissueMapper.selectAccessoriesDetailsList(contextId,Arrays.stream(productIds).distinct().collect(Collectors.toList()), ccessoriesName);
        }
        return new ArrayList<>();
    }


    /**
     * @description: 获取客户信息,根据不同工单订单取不同客户信息
     * @author: Moore
     * @date: 2024/1/30 15:33
     * @param
     * @param ticketId
     * @return: com.bizark.op.api.entity.op.ticket.ScCustomer
     **/
    @Override
    public ScCustomer selectReissceCystomer(Long ticketId) {
        ScTicket scTicket = scTicketMapper.selectScTicketById(ticketId);
        if (null == scTicket) {
            throw new CustomException("获取工单信息失败!");
        }
        if (scTicket.getMatchOrderType() == ScTicketConstant.MATCH_TYPE_MANUAL) { //手动单取手工单表
            ScManualOrder scManualOrder = scManualOrderMapper.selectScManualOrderById(scTicket.getOrderId()); //
            if (scManualOrder == null) {
                return new ScCustomer();
            }
            ScCustomer scCustomer = new ScCustomer();
            scCustomer.setCustomerName(scManualOrder.getBuyerName()); //客户名称
            scCustomer.setPhone(scManualOrder.getTelphone()); //电话
            scCustomer.setCountry(scManualOrder.getCountry()); //国家CODE
            scCustomer.setCountryCode(scManualOrder.getCountryCode());//国家CODE
            scCustomer.setStateOrRegion(scManualOrder.getStateOrRegion()); // 州或者地区
            scCustomer.setCity(scManualOrder.getCity()); // 城市
            scCustomer.setAddress(scManualOrder.getAddressLine1());// 地址1
            scCustomer.setAddress2(scManualOrder.getAddressLine2());// 地址2 非系统订单无地址3
            scCustomer.setPostalCode(scManualOrder.getPostalCode()); // 邮编
            scCustomer.setAmzCustomerId(scManualOrder.getCustomerOuterId());// 对应 customer_outer_id
            return scCustomer;
        } else {  //取客戶表
            if (null == scTicket.getCustomerId()) {
                return new ScCustomer();
            }
            ScCustomer scCustomer = scCustomerMapper.selectScCustomerById(scTicket.getCustomerId());
            if (null == scCustomer) {
                return new ScCustomer();
            }
            scCustomer.setCustomerName(scCustomer.getRecipientName());  //前端取用户字段（将实际发货人名称,设置到客户名中，用于补发时回显使用）
            scCustomer.setAddress(scCustomer.getAddressLine1()); //补发回显地址使用
            scCustomer.setAddress2(scCustomer.getAddressLine2()); //地址2
            scCustomer.setAddress3(scCustomer.getAddress3()); //地址3
            return scCustomer;
        }
    }

//    /**
//     * 补发的弹窗和详情查询
//     */
//    public ScOrderReissue selectScOrderReissue(List<ScOrderReissue> scOrderReissueList, ScOrderReissue scOrderReissue, Long contextId) {
//        if (!CollectionUtils.isEmpty(scOrderReissueList)) {
//            ScOrderReissue scOrderReissueDB = scOrderReissueList.get(0);
//            for (ScOrderReissue reissue : scOrderReissueList) {
//                if (StringUtils.isNotEmpty(reissue.getErpSku())) {
//                    String erpSku = reissue.getErpSku();
//                    if (erpSku.contains(",")) {
//                        String[] erpSkuArray = erpSku.split(",");
//                        for (String sku : erpSkuArray) {
//                            List<ScReissueAccessories> accessoriesList = scOrderReissueMapper.selectAccessoriesDetails(sku, contextId);
//                            scOrderReissue.setAccessoriesList(accessoriesList);
//                        }
//                    } else {
//                        List<ScReissueAccessories> accessoriesList = scOrderReissueMapper.selectAccessoriesDetails(erpSku, contextId);
//                        scOrderReissue.setAccessoriesList(accessoriesList);
//                    }
//                    scOrderReissue.setReissueNumber(scOrderReissueDB.getReissueNumber());
//                    scOrderReissue.setCustomerName(scOrderReissueDB.getCustomerName());
//                    scOrderReissue.setPhone(scOrderReissueDB.getPhone());
//                    scOrderReissue.setCity(scOrderReissueDB.getCity());
//                    scOrderReissue.setStateOrRegion(scOrderReissueDB.getStateOrRegion());
//                    scOrderReissue.setCountryCode(scOrderReissueDB.getCountryCode());
//                    scOrderReissue.setPostalCode(scOrderReissueDB.getPostalCode());
//                    scOrderReissue.setAddress(scOrderReissueDB.getAddress());
//                    scOrderReissue.setRemark(scOrderReissueDB.getRemark());
//                }
//            }
//        }
//        return scOrderReissue;
//    }
}
