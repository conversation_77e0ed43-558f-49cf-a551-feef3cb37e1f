package com.bizark.op.service.mapper.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bizark.op.api.entity.op.sale.ScGoodsCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ScGoodsCategoryMapper extends BaseMapper<ScGoodsCategory> {
    List<ScGoodsCategory> selectScGoodsCategoryAccessories();

    List<ScGoodsCategory> selectSCGoodsCategoryList(ScGoodsCategory category);

    /**
     * 删除
     * @param ids
     * @return
     */
    int removeByIds(@Param("ids") List<Integer> ids);

    List<ScGoodsCategory> selectLevelCategoryName();
}
