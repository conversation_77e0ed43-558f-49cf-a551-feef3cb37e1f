package com.bizark.op.service.service.ticket;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.bizark.boss.api.entity.dashboard.ProductLineEntity;
import com.bizark.boss.api.service.ProductLineService;
import com.bizark.boss.api.service.TaskCenterService;
import com.bizark.boss.api.vo.dashboard.TaskCenterRequest;
import com.bizark.common.contract.AuthUserDetails;
import com.bizark.common.entity.BizBaseNativeEntity;
import com.bizark.common.exception.CommonException;
import com.bizark.common.util.EmptyUtil;
import com.bizark.common.util.JacksonUtils;
import com.bizark.framework.security.AuthContextHolder;
import com.bizark.op.api.cons.ScTicketConstant;
import com.bizark.op.api.enm.finance.FinanceErrorEnum;
import com.bizark.op.api.enm.mar.AppealTypeEnum;
import com.bizark.op.api.enm.refund.OrderRefundEnum;
import com.bizark.op.api.enm.refund.OrderRefundStateEnum;
import com.bizark.op.api.enm.refund.OrderRefundStatusEnum;
import com.bizark.op.api.enm.sale.AccountSaleChannelEnum;
import com.bizark.op.api.enm.ticket.ScTicketTypeEnum;
import com.bizark.op.api.enm.tiktok.OrderCancelStatusEnum;
import com.bizark.op.api.enm.tiktok.OrderReturnStatusEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.document.ConfTicketProblem;
import com.bizark.op.api.entity.op.mar.calim.wfs.MarWfsClaimCase;
import com.bizark.op.api.entity.op.mar.calim.wfs.vo.MarNotReadMessageVO;
import com.bizark.op.api.entity.op.order.SaleOrderCancel;
import com.bizark.op.api.entity.op.order.SaleOrderItems;
import com.bizark.op.api.entity.op.order.SaleOrders;
import com.bizark.op.api.entity.op.refund.OrderRefund;
import com.bizark.op.api.entity.op.refund.OrderRefundRequest;
import com.bizark.op.api.entity.op.refund.OrderRefundRequestInfo;
import com.bizark.op.api.entity.op.returns.DO.MarReturnManuaItemDTO;
import com.bizark.op.api.entity.op.returns.entity.*;
import com.bizark.op.api.entity.op.sale.*;
import com.bizark.op.api.entity.op.tabcut.vo.TabcutVideoTagRealtionDTO;
import com.bizark.op.api.entity.op.ticket.*;
import com.bizark.op.api.entity.op.ticket.VO.*;
import com.bizark.op.api.entity.op.ticket.order.AmzVcPoOrderV;
import com.bizark.op.api.exception.ErpCommonException;
import com.bizark.op.api.service.customer.ICusCenterOffsiteService;
import com.bizark.op.api.service.mar.claim.MarWfsClaimCaseMessageService;
import com.bizark.op.api.service.mar.claim.MarWfsClaimCaseService;
import com.bizark.op.api.service.order.SaleOrderCancelService;
import com.bizark.op.api.service.order.SaleOrderItemsService;
import com.bizark.op.api.service.refund.IOrderRefundRequestInfoService;
import com.bizark.op.api.service.refund.IOrderRefundRequestService;
import com.bizark.op.api.service.refund.IOrderRefundService;
import com.bizark.op.api.service.sale.ProductChannelsService;
import com.bizark.op.api.service.sale.ProductsService;
import com.bizark.op.api.service.ticket.*;
import com.bizark.op.api.service.ticket.email.IScEmailSendService;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.*;
import com.bizark.op.common.util.spring.SpringUtils;
import com.bizark.op.service.mapper.customer.CusCenterOffsiteMapper;
import com.bizark.op.service.mapper.returns.OrderRefundRequestInfoConfigurationMapper;
import com.bizark.op.service.mapper.returns.ReturnInfoConfigurationMapper;
import com.bizark.op.service.mapper.returns.ReturnInfoMapper;
import com.bizark.op.service.mapper.returns.ScTicketRefundInfoMapper;
import com.bizark.op.service.mapper.sale.AmzOrderVMapper;
import com.bizark.op.service.mapper.sale.ProductsMapper;
import com.bizark.op.service.mapper.ticket.*;
import com.bizark.op.service.service.account.AccountServiceImpl;
import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.op.service.util.GraphEmailApi;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.bizark.usercenter.api.parameter.attachment.AttachItemRow;
import com.bizark.usercenter.api.parameter.attachment.ContextAttachMeta;
import com.bizark.usercenter.api.service.UserService;
import com.bizark.usercenter.api.service.attachment.UcAttachmentService;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bizark.op.service.util.EmailApi.readAndWriteEmail;
import static com.bizark.op.service.util.GraphEmailApi.readEmailByInternetMessageId;

/**
 * 工单管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-12
 */
@Service
@Slf4j
public class ScTicketServiceImpl implements IScTicketService {
    private static Logger logger = LoggerFactory.getLogger(ScTicketServiceImpl.class);

    @Resource
    @Lazy
    private IOrderRefundService orderRefundService;

    @Resource
    private IOrderRefundRequestService orderRefundRequestService;

    @Autowired
    private IScOrderReissueService scOrderReissueService;

    @Autowired
    @Lazy
    private ICusCenterOffsiteService cusCenterOffsiteService;


    @Autowired
    private ScTicketMapper scTicketMapper;

    @Autowired
    private SaleOrderItemsService orderItemsService;

    @Autowired
    private ProductsService productsService;

    /*店铺信息*/
    @Autowired
    private AccountServiceImpl accountService;

    @Autowired
    private IScTicketLogService scTicketLogService;

    @Autowired
    private UcAttachmentService ucAttachmentService;


    @Autowired
    private ScManualOrderMapper manualOrderMapper;




    @Autowired
    private IScStationLetterService scStationLetterService;

    @Autowired
    @Lazy
    private IConfTicketAssignService confTicketAssignService;

    @Autowired
    private IConfTicketFilterService confTicketFilterService;


    @Autowired
    private IScTicketHandleService scTicketHandleService;

    @Autowired
    private SaleOrdersMapper saleOrdersMapper;


    @Autowired
    private ScTicketPriorityMapper scTicketPriorityMapper;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;


    @Autowired
    private ProductChannelsService productChannelsService;

    @Autowired
    private ProductLineService productLineService;

    /**
     * 客戶信息
     */
    @Autowired
    @Lazy
    private IScCustomerService scCustomerService;


    /**
     * 取消订单信息
     */
    @Autowired
    @Lazy
    private SaleOrderCancelService saleOrderCancelService;


    /**
     * 退货信息
     */
    @Autowired
   private ReturnInfoMapper returnInfoMapper;

    @Autowired
    private ProductsMapper productsMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private AmzOrderVMapper amzOrderVMapper;
    @Autowired
    private CusCenterOffsiteMapper cusCenterOffsiteMapper;

    @Autowired
    private ScManualOrderMapper scManualOrderMapper;




    @Autowired
    private IAmzVcPoOrderTimeRecordService amzVcPoOrderTimeRecordService;

    @Autowired
    private AmzVcPoOrderVMapper amzVcPoOrderVMapper;

    @Autowired
    private ISyncEmailInfoService isyncEmailInfoService;

    @Autowired
    private ScTicketRefundInfoMapper scTicketRefundInfoMapper;

    @Autowired
    private IOrderRefundRequestInfoService orderRefundRequestInfoService;

    @Autowired
    private IScStationLetterService iScStationLetterService;

    @Autowired
    private IScEmailSendService iScEmailSendService;

    @Autowired
    private SyncEmailInfoMapper syncEmailInfoMapper;


    @Autowired
    private TaskCenterService taskCenterService;

    @Autowired
    private IMerchantEmailService merchantEmailService;

    @Autowired
    private IScStationLetterMessageService iScStationLetterMessageService;

    @Autowired
    private ScTicketHandleMapper scTicketHandleMapper;

    @Autowired
    private ConfTicketProblemMapper confTicketProblemMapper;

    @Autowired
    private OrderRefundRequestInfoConfigurationMapper orderRefundRequestInfoConfigurationMapper;

    @Autowired
    private ScOrderReissueMapper scOrderReissueMapper;

    @Autowired
    private ScTicketTagRelationService scTicketTagRelationService;

    @Autowired
    private MarWfsClaimCaseService marWfsClaimCaseService;

    /**
     * 查询工单管理
     * /     *
     *
     * @param ticketId 工单管理ID
     * @return 工单管理
     */
    @Override
    public ScTicket selectScTicketById(Long ticketId) {
        ScTicket scTicket = scTicketMapper.selectScTicketById(ticketId);
        return scTicket;
    }


    /**
     * 查询工单管理列表
     *
     * @param scTicket 工单管理
     * @return 工单管理
     */
    @Override
    public List<ScTicket> selectScTicketList(ScTicket scTicket) {


        //对NRPR工单单独处理 -渠道有值即为查询所有店铺
        if (CollectionUtils.isNotEmpty(scTicket.getSaleChannelList())) {

            //判断是否包含review相关工单
            if(CollectionUtils.isNotEmpty(scTicket.getTicketTypeArray())&&scTicket.getTicketTypeArray().stream().anyMatch(item -> item.equals(ScTicketTypeEnum.NR.getValue()) || item.equals(ScTicketTypeEnum.PR.getValue()))){
                //判断是否需要关联查询
                //VC-DF，VC-USPO，VC-DI,amazon
                List<String> amzSaleChannel = Arrays.asList("amazonvendords", "VC-DI", "amazonvendor","amazon");


                //包含amz相关渠道，且渠道非全包含
                if(scTicket.getSaleChannelList().stream().anyMatch(item->amzSaleChannel.contains(item))&&! new HashSet<>(amzSaleChannel).equals(new HashSet<>(scTicket.getSaleChannelList()))){


                    //交集channel 含（amz）
                    List<String> containReviewAmzChannel = scTicket.getSaleChannelList().stream().filter(item -> amzSaleChannel.contains(item)).collect(Collectors.toList());
                    //差集channel
                    List<String> noContaimReviewAmzChannel = scTicket.getSaleChannelList().stream().filter(item -> !amzSaleChannel.contains(item)).collect(Collectors.toList());


                    scTicket.setAmzChannelList(containReviewAmzChannel);
                    scTicket.setNoAmzChannelList(noContaimReviewAmzChannel);

                    List<String> nrprList = scTicket.getTicketTypeArray().stream().filter(item -> item.equals(ScTicketTypeEnum.NR.getValue()) || item.equals(ScTicketTypeEnum.PR.getValue())).collect(Collectors.toList());
                    scTicket.getTicketTypeArray().removeAll(nrprList); //删除工单类型 in中review类型工单
                    scTicket.setReviewTicketTypeList(nrprList); //对review工单进行单独or查询
                }
            }

//            //判断渠道 是否均为amz相关渠道
//            if (scTicket.getSaleChannelList().stream().allMatch(s -> amzSaleChannel.contains(s))){}
//
//            if (CollectionUtils.isEmpty(scTicket.getSaleChannelList())){
//                scTicket.setSaleChannelList(null); //无工单类型不直接查询渠道
//            }else  if (scTicket.getTicketTypeArray().stream().allMatch(item -> item.equals(ScTicketTypeEnum.NR.getValue()) || item.equals(ScTicketTypeEnum.PR.getValue()))) {
//                scTicket.setShopIds(null); //置空店铺使用 ticketSource直接查询
//            } else if (CollectionUtils.isNotEmpty(scTicket.getTicketTypeArray().stream().filter(item -> item.equals(ScTicketTypeEnum.NR.getValue()) || item.equals(ScTicketTypeEnum.PR.getValue())).collect(Collectors.toList()))) { //部分包含
//                List<String> nrprList = scTicket.getTicketTypeArray().stream().filter(item -> item.equals(ScTicketTypeEnum.NR.getValue()) || item.equals(ScTicketTypeEnum.PR.getValue())).collect(Collectors.toList());
//                scTicket.getTicketTypeArray().removeAll(nrprList); //删除in中review类型工单
//                scTicket.setReviewTicketTypeList(nrprList); //进行单独or查询
//            } else {
//                scTicket.setSaleChannelList(null); //其余工单类型不进行渠道查询
//            }
        }
        if(CollectionUtil.isNotEmpty(scTicket.getTagIdList())) {
            if(scTicket.getTagIdList().stream().anyMatch(i -> new Long(-1).equals(i))) {
                scTicket.setTagIdFlag(1);
            }
        }
        List<ScTicket> scTicketList = scTicketMapper.selectScTicketList(scTicket);

        List<ScTicket> wfsClaimList = scTicketList.stream().filter(item -> ScTicketConstant.TICKET_TYPE_WFS_CLAIM.equals(item.getTicketType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(wfsClaimList)) {
            List<String> ticketNumber = wfsClaimList.stream().map(ScTicket::getTicketNumber).collect(Collectors.toList());
            List<MarWfsClaimCase> marWfsClaimCases = marWfsClaimCaseService.lambdaQuery()
                    .select(MarWfsClaimCase::getId,MarWfsClaimCase::getTicketNumber).in(MarWfsClaimCase::getTicketNumber, ticketNumber).list();
            if (CollectionUtils.isNotEmpty(marWfsClaimCases)) {
                List<Long> list = marWfsClaimCases.stream().map(MarWfsClaimCase::getId).collect(Collectors.toList());
               List<MarNotReadMessageVO> messageVOS = SpringUtils.getBean(MarWfsClaimCaseMessageService.class).queryNotReadCount(list);
               if (CollectionUtils.isNotEmpty(messageVOS)) {
                   Map<String, Long> caseMap = marWfsClaimCases.stream().collect(Collectors.toMap(MarWfsClaimCase::getTicketNumber, MarWfsClaimCase::getId, (key1, key2) -> key1));
                   Map<Long, Integer> countMap = messageVOS.stream().collect(Collectors.toMap(MarNotReadMessageVO::getCaseSysId, MarNotReadMessageVO::getCount, (key1, key2) -> key1));
                   scTicketList.forEach(item -> {
                       if (caseMap.containsKey(item.getTicketNumber())) {
                           Long caseId = caseMap.get(item.getTicketNumber());
                           Integer readNum = countMap.containsKey(caseId) ? countMap.get(caseId) : 0;
                           item.setNoReplyQty(readNum);
                       }
                   });
               }
            }
        }
        CompletableFuture<Void> f1 = CompletableFuture.runAsync(() -> {
            setParentTicketInfo(scTicketList);
        }, taskExecutor);
        //查询子工单
        CompletableFuture<Void> f2 = CompletableFuture.runAsync(() -> {
            setchildTicketInfo(scTicketList);
        }, taskExecutor);
        //设置标签值
        CompletableFuture<Void> f3 = CompletableFuture.runAsync(() -> {
            setTicketLabel(scTicketList);
        }, taskExecutor);

        try {
            CompletableFuture.allOf(f1, f2, f3).get(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.info("查新父子工单信息异常：{}", e);
        }
        return scTicketList;
    }



    /**
     * @description: 设置工单父工单信息
     * @author: Moore
     * @date: 2025/3/5 10:12
     * @param
     * @param scTickets
     * @return: void
    **/
    public void setParentTicketInfo(List<ScTicket> scTickets) {
        List<Long> ticketIds = scTickets.stream().filter(item -> item.getParentTicketId() != null).map(ScTicket::getParentTicketId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(ticketIds)) {
            List<ScTicket> parentScticket = scTicketMapper.selectScTicketByTicketIds(ticketIds);
            if (!CollectionUtils.isEmpty(parentScticket)) {
                Map<Long, ScTicket> ticketMap = parentScticket.stream().collect(Collectors.toMap(ScTicket::getId, Function.identity(), (a, b) -> a));
                scTickets.forEach(item -> item.setParentScTicket(ticketMap.get(item.getParentTicketId())));
            }
        }
    }


    /**
     * 设置工单子工单信息
     *
     * @param scTickets
     */
    public void setchildTicketInfo(List<ScTicket> scTickets) {
        List<Long> ticketIds = scTickets.stream().map(ScTicket::getId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(ticketIds)) {
            //获取所有子
            List<ScTicket> childList = scTicketMapper.selectScTicketByParentTicketIds(ticketIds);
            if (!CollectionUtils.isEmpty(childList)) {
                Map<Long, List<ScTicket>> childMap = childList.stream().collect(Collectors.groupingBy(ScTicket::getParentTicketId));
                scTickets.forEach(item -> item.setChildTicketList(childMap.get(item.getId())));
            }
        }
    }

    /**
     * 设置工单标签信息
     *
     * @param scTicketList
     */
    public void setTicketLabel(List<ScTicket> scTicketList) {
        if(CollectionUtils.isNotEmpty(scTicketList)) {
            List<Long> ticketIdList = scTicketList.stream().map(i -> i.getId()).collect(Collectors.toList());
            List<ScTicketTagRelationVo> scTicketTagRelationVos = scTicketTagRelationService.queryScTicketTagRelationAndTag(ticketIdList);
            if (CollectionUtils.isNotEmpty(scTicketTagRelationVos)) {
                // 按ticketId分组（分组结果：Map<Long, List<ScTicketTagRelationVo>>）
                Map<Long, List<ScTicketTagRelationVo>> groupedByTicketId = scTicketTagRelationVos.stream()
                        .collect(Collectors.groupingBy(ScTicketTagRelationVo::getTicketId));
                scTicketList.stream().forEach(i -> i.setScTicketTagRelationVoList(groupedByTicketId.get(i.getId())));
            }
        }
    }




    @Override
    public List<ScTicket> selectScTicketListNoParent(ScTicket scTicket) {
//        return setTicketAttr(scTicketMapper.selectScTicketList(scTicket));
        return scTicketMapper.selectScTicketList(scTicket);
    }


    @Override
    public List<ScTicket> getChildTicket(Long ticketId, Integer contextId) {
        return this.selectScTicketByParentId(ticketId, contextId);
    }

    private void buildOtherData(List<SaleOrdersVO> saleOrders) {
        CompletableFuture<Void> f1 = CompletableFuture.runAsync(() -> {
            buildLineName(saleOrders);
        }, taskExecutor);

        CompletableFuture<Void> f2 = CompletableFuture.runAsync(() -> {
            buildCustomerName(saleOrders);
        }, taskExecutor);

        CompletableFuture<Void> f3 = CompletableFuture.runAsync(() -> {
            List<String> orderNos = saleOrders.stream().map(SaleOrders::getOrderNo).distinct().collect(Collectors.toList());
            Long contextId = saleOrders.get(0).getOrgId();
            List<SaleOrderItems> items = orderItemsService.lambdaQuery()
                    .eq(SaleOrderItems::getOrgId, contextId)
                    .in(SaleOrderItems::getOrderNo, orderNos)
                    .list();
            if (CollectionUtil.isEmpty(items)) {
                return;
            }
            // 设置运营
            buildOpUserName(saleOrders, contextId, items);
            // 设置sku/分类信息
            List<String> erpSkus = items.stream().map(SaleOrderItems::getErpSku).distinct().collect(Collectors.toList());
            List<Products> products = productsService.lambdaQuery().in(Products::getErpsku, erpSkus).list();
            if (CollectionUtil.isNotEmpty(products)) {
                products.forEach(
                        p -> p.setCategoryName(productsService.buildProductsCategoryName(p))
                );
                Map<String, Products> productsMap = products.stream().collect(Collectors.toMap(Products::getErpsku, p -> p, (a, b) -> a));
                Map<String, SaleOrderItems> itemMap = items.stream()
                        .collect(Collectors.toMap(o -> o.getOrderNo() + o.getChannelId(), o -> o, (a, b) -> a));
                for (SaleOrdersVO saleOrder : saleOrders) {
                    String key = saleOrder.getOrderNo() + saleOrder.getChannelId();
                    if (itemMap.containsKey(key)) {
                        saleOrder.setGoodsSku(itemMap.get(key).getErpSku());
                        SaleOrderItems orderItems = itemMap.get(key);
                        BigDecimal itemPrice = orderItems.getItemAmount() != null && orderItems.getQuantity() != null && !orderItems.getQuantity().equals(0)
                                ? orderItems.getItemAmount().divide(new BigDecimal(orderItems.getQuantity())) : BigDecimal.ZERO;
                        saleOrder.setItemPrice(itemPrice);
                        saleOrder.setAsin(orderItems.getAsin());
                        saleOrder.setAmount(orderItems.getItemAmount());
                    }
                    if (itemMap.containsKey(key) && productsMap.containsKey(itemMap.get(key).getErpSku())) {
                        saleOrder.setGoodsSku(itemMap.get(key).getErpSku());
                        saleOrder.setGoodsName(productsMap.get(itemMap.get(key).getErpSku()).getName());
                        saleOrder.setCategoryName(productsMap.get(itemMap.get(key).getErpSku()).getCategoryName());
                        saleOrder.setImgUrl(productsMap.get(itemMap.get(key).getErpSku()).parseImg());
                    }
                }
            }

        });
        try {
            CompletableFuture.allOf(f1, f2, f3).get(60, TimeUnit.SECONDS);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    private void buildOpUserName(List<SaleOrdersVO> saleOrders, Long contextId, List<SaleOrderItems> items) {
        Map<String, SaleOrdersVO> orderMap = saleOrders.stream().collect(Collectors.toMap(SaleOrders::getOrderNo, o -> o, (a, b) -> a));
        List<String> sellerSkus = items.stream().map(SaleOrderItems::getSellerSku).distinct().collect(Collectors.toList());
        List<ProductChannels> channels = productChannelsService.lambdaQuery()
                .in(ProductChannels::getSellerSku, sellerSkus)
                .eq(ProductChannels::getOrgId, contextId)
                .list();
        if (CollectionUtil.isNotEmpty(channels)) {
            List<String> flags = channels.stream()
                    .map(ProductChannels::getAccountId)
                    .distinct()
                    .collect(Collectors.toList());

            List<Account> accounts = accountService.lambdaQuery()
                    .eq(Account::getFlag, flags)
                    .list();
            if (CollectionUtil.isNotEmpty(accounts)) {
                Map<String, Account> accountMap = accounts.stream().collect(Collectors.toMap(Account::getFlag, o -> o, (a, b) -> a));
                for (ProductChannels channel : channels) {
                    if (accounts.contains(channel.getAccountId())) {
                        channel.setShopName(accountMap.get(channel.getAccountId()).getTitle());
                    }
                }

            }

            Map<String, ProductChannels> channelMap = channels.stream()
                    .collect(Collectors.toMap(ProductChannels::getSellerSku, o -> o, (a, b) -> a));
            for (SaleOrderItems item : items) {
                if (channelMap.containsKey(item.getSellerSku()) && orderMap.containsKey(item.getOrderNo())) {
                    orderMap.get(item.getOrderNo()).setOperationName(channelMap.get(item.getSellerSku()).getOperationUserName());
                    orderMap.get(item.getOrderNo()).setBrand(channelMap.get(item.getSellerSku()).getBrand());
                    orderMap.get(item.getOrderNo()).setShopName(channelMap.get(item.getSellerSku()).getShopName());
                    orderMap.get(item.getOrderNo()).setCountry(channelMap.get(item.getSellerSku()).getCountry());
                }
            }


        }
    }

    private void buildCustomerName(List<SaleOrdersVO> saleOrders) {
        List<Integer> customerIds = saleOrders.stream()
                .filter(o -> o.getCustomerId() != null)
                .map(o -> o.getCustomerId())
                .distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(customerIds)) {
            return;
        }
        List<ScCustomer> customers = scCustomerService.selectScCustomerByIds(customerIds);
        if (CollectionUtil.isEmpty(customers)) {
            return;
        }
        Map<Long, String> map = customers.stream().collect(Collectors.toMap(ScCustomer::getCustomerId, ScCustomer::getCustomerName));
        for (SaleOrdersVO order : saleOrders) {
            if (map.containsKey(order.getCustomerId())) {
                order.setCustomerName(map.get(order.getChannelId()));
            }
        }
    }

    private void buildLineName(List<SaleOrdersVO> saleOrders) {
        List<Integer> lineIds = saleOrders.stream()
                .filter(o -> o.getLineId() != null)
                .map(SaleOrders::getLineId).distinct()
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(lineIds)) {
            return;
        }
        List<ProductLineEntity> lines = productLineService.selectProductLineByIds(ArrayUtil.toArray(lineIds, Integer.class));
        if (CollectionUtil.isEmpty(lines)) {
            return;
        }
        Map<Integer, String> lineMap = lines.stream()
                .collect(Collectors.toMap(BizBaseNativeEntity::getId, ProductLineEntity::getName));
        for (SaleOrdersVO order : saleOrders) {
            if (lineMap.containsKey(order.getLineId())) {
                order.setLineName(lineMap.get(order.getLineId()));
            }
        }

    }


    /**
     * @description: 查询父工单信息
     * @author: Moore
     * @date: 2024/6/28 20:07
     * @param
     * @param ticketId
     * @return: com.bizark.op.api.entity.op.ticket.ScTicket
    **/
    public ScTicket selectScTicketByTicketIdInfo(Long ticketId) {
        return scTicketMapper.selectScTicketByTicketId(ticketId);
    }


    @Override
    public ScTicket selectScTicketByTicketId(Long ticketId) {
        ScTicket scTicket = scTicketMapper.selectScTicketByTicketId(ticketId);
        if (scTicket == null) {
            throw new CustomException("工单信息获取失败!");
        }

        CompletableFuture<Void> f1 = CompletableFuture.runAsync(() -> {
            setParentTicketInfo(Arrays.asList(scTicket));
        }, taskExecutor);
        //查询子工单
        CompletableFuture<Void> f2 = CompletableFuture.runAsync(() -> {
            setchildTicketInfo(Arrays.asList(scTicket));
        }, taskExecutor);



        String businessTicketStatusStr;
        BaseTicketOrderVO amzOrderV = new BaseTicketOrderVO();

        if (null != scTicket) {
            if (ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG.equals(scTicket.getTicketType())) { //站内信
                if(ScTicketConstant.MATCH_TYPE_MANUAL.equals(scTicket.getMatchOrderType())) {//手动
                    ScManualOrder scManualOrder = scManualOrderMapper.selectScManualOrderById(scTicket.getOrderId());//手工订单
                    //查询客户信息
                    if (scManualOrder != null) {
                        amzOrderV.setOrderNo(scManualOrder.getOrderNumber()); //订单号
                        amzOrderV.setBuyName(scManualOrder.getBuyerName()); //买家姓名（使用收件人姓名）
                        amzOrderV.setEncryptEmail(scManualOrder.getEmail());//加密邮箱
                        amzOrderV.setBuyEmail(scManualOrder.getEmail());//邮箱
                        amzOrderV.setSellerSku(scManualOrder.getSellerSku()); //SellerSku
                        amzOrderV.setQuantity(scManualOrder.getQuantity()); //销量/数量
                        amzOrderV.setItemAmount(scManualOrder.getAmount());//金额
                        if(scManualOrder.getShopId() != null) {
                            QueryWrapper<Account> queryWrapper = new QueryWrapper<>();
                            queryWrapper.eq("id",scManualOrder.getShopId());
                            queryWrapper.eq("org_id",scTicket.getOrganizationId());
                            List<Account> accountList = accountService.list(queryWrapper);
                            if(CollectionUtil.isNotEmpty(accountList)) {
                                amzOrderV.setShopName(accountList.get(0).getTitle());//店铺名
                            }
                        };
                        if(StringUtil.isNotEmpty(scManualOrder.getGoodsSku())) {
                            this.ticketOrderProductPack(Arrays.asList(scManualOrder.getGoodsSku().split(",")), scTicket.getOrganizationId().intValue(), amzOrderV,null);
                        }
                        amzOrderV.setCountry(scManualOrder.getCountry()); //国家
                        amzOrderV.setErpsku(scManualOrder.getGoodsSku()); //款号
                        amzOrderV.setAsin(scManualOrder.getAsin()); //ASIN
                        amzOrderV.setPromoteOrders("N");
                        scTicket.setCustomerName(scManualOrder.getBuyerName());//客户名称
                    }
                } else {
                    //异步保存客户及工单关联客户（用不补发，原则上tk会在匹配订单时保存客户信息，此处为其他站内信设计）
                    if (null == scTicket.getCustomerId()&&scTicket.getOrderId()!=null) {
                        List<ScManualOrderVO> saleOrderVosOne = saleOrdersMapper.selectSaleOrderAndCustomerByOrderId(scTicket.getOrderId());
                        if (!CollectionUtils.isEmpty(saleOrderVosOne))
                        scCustomerService.saveCustomerInfoAndJoinTicket(saleOrderVosOne.get(0), scTicket);
                    }
                    this.amzSitemsgTicketPack(scTicket, amzOrderV);
                }
            } else if ("VC_PO_DI".equals(scTicket.getTicketType()) || "VC_PO_USPO".equals(scTicket.getTicketType())) {  //PO工单合并多条，取其中一条
                AmzVcPoOrderTimeRecord recordTime = amzVcPoOrderTimeRecordService.getById(scTicket.getSourceId());
                if (recordTime != null) {
                    amzOrderV.setChannel("VC_PO_DI".equals(scTicket.getTicketType())?"vc-di":"vc-uspo");
                    amzOrderV.setChannelCreated(recordTime.getOrderDate());//订单时间
                    amzOrderV.setShopName(recordTime.getShopName());//店铺名
                    if (recordTime.getShopId() != null) {
                        Account shop = accountService.lambdaQuery().eq(Account::getId, recordTime.getShopId()).one();
                        amzOrderV.setCountry(shop.getCountryCode());
                    }
                }
            } else if (ScTicketConstant.TICKET_TYPE_REISSUE_GOODS.equals(scTicket.getTicketType()) ||
                    ScTicketConstant.TICKET_TYPE_REISSUE_ACCESSORIES.equals(scTicket.getTicketType())) {
                if (!Objects.isNull(scTicket.getParentTicketId())) {
                    ScTicket scTicketByParentId = scTicketMapper.selectScTicketByTicketId(scTicket.getParentTicketId());
                    if (!Objects.isNull(scTicketByParentId)) {
                        scTicket.setParentTicketNumber(scTicketByParentId.getTicketNumber());
                    }
                }
                if(ScTicketConstant.MATCH_TYPE_MANUAL.equals(scTicket.getMatchOrderType())) {//手动
                    ScManualOrder scManualOrder = scManualOrderMapper.selectScManualOrderById(scTicket.getOrderId());//手工订单
                    //查询客户信息
                    if (scManualOrder != null) {
                        amzOrderV.setOrderNo(scManualOrder.getOrderNumber()); //订单号
                        amzOrderV.setBuyName(scManualOrder.getBuyerName()); //买家姓名（使用收件人姓名）
                        amzOrderV.setEncryptEmail(scManualOrder.getEmail());//加密邮箱
                        amzOrderV.setBuyEmail(scManualOrder.getEmail());//邮箱
                        amzOrderV.setSellerSku(scManualOrder.getSellerSku()); //SellerSku
                        amzOrderV.setQuantity(scManualOrder.getQuantity()); //销量/数量
                        amzOrderV.setItemAmount(scManualOrder.getAmount());//金额
                        if(scManualOrder.getShopId() != null) {
                            QueryWrapper<Account> queryWrapper = new QueryWrapper<>();
                            queryWrapper.eq("id",scManualOrder.getShopId());
                            queryWrapper.eq("org_id",scTicket.getOrganizationId());
                            List<Account> accountList = accountService.list(queryWrapper);
                            if(CollectionUtil.isNotEmpty(accountList)) {
                                amzOrderV.setShopName(accountList.get(0).getTitle());//店铺名
                            }
                        }
                        if(StringUtil.isNotEmpty(scManualOrder.getGoodsSku())) {
                            this.ticketOrderProductPack(Arrays.asList(scManualOrder.getGoodsSku().split(",")), scTicket.getOrganizationId().intValue(), amzOrderV,null);
                        }
                        //amzOrderV.setChannel();//销售渠道
                        amzOrderV.setCountry(scManualOrder.getCountry()); //国家
//                        amzOrderV.setOperationUserName(); //运营
                        amzOrderV.setErpsku(scManualOrder.getGoodsSku()); //款号
                        amzOrderV.setAsin(scManualOrder.getAsin()); //ASIN
//                        amzOrderV.setBelongProject();//项目
                        amzOrderV.setPromoteOrders("N");
                        scTicket.setCustomerName(scManualOrder.getBuyerName());//客户名称
                    }

                }else  if(null != scTicket.getAmazonOrderId()) {
                        //查询订单信息
                        List<SaleOrderVoTicket> saleOrderVoTicketList = saleOrdersMapper.selectOrderByOrderId(scTicket.getOrderId());
                        //查询客户信息
                        ScCustomer scCustomer = scCustomerService.selectScCustomerById(scTicket.getCustomerId());
                        if (!CollectionUtils.isEmpty(saleOrderVoTicketList)) {
                            SaleOrderVoTicket saleOrderVoTicket = saleOrderVoTicketList.get(0);
                            amzOrderV.setOrderNo(saleOrderVoTicket.getOrderNo()); //订单号
                            amzOrderV.setSellerOrderNumber(saleOrderVoTicket.getPoNumber());
                            amzOrderV.setBuyName(scCustomer != null ? scCustomer.getRecipientName() : null); //买家姓名（使用收件人姓名）
                            amzOrderV.setBuyEmail(scCustomer != null ? scCustomer.getEmail() : null);//邮箱
                            amzOrderV.setChannelCreated(saleOrderVoTicket.getPstChannelCreated()); //下单时间
                            amzOrderV.setLatestShipDate(saleOrderVoTicket.getLatestShipDate()); //发货时限
                            amzOrderV.setShipmentDate(saleOrderVoTicket.getShipmentDate()); //发货时间
                            amzOrderV.setSellerSku(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getSellerSku).distinct().collect(Collectors.joining(","))); //SellerSku
                            amzOrderV.setQuantity(saleOrderVoTicketList.stream().mapToInt(SaleOrderVoTicket::getQuantity).sum()); //销量/数量
                            amzOrderV.setItemAmount(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getItemAmount).reduce(BigDecimal.ZERO, BigDecimal::add));//金额
                            amzOrderV.setShopName(saleOrderVoTicket.getTitle());//店铺名
                            amzOrderV.setChannel(saleOrderVoTicket.getChannel());//销售渠道
                            amzOrderV.setCountry(scCustomer != null ? scCustomer.getCountry() : null); //国家
                            amzOrderV.setOperationUserName(saleOrderVoTicket.getOperationUserName()); //运营
                            amzOrderV.setErpsku(StringUtils.isNotEmpty(scTicket.getGoodsSku())?scTicket.getGoodsSku():saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getErpsku).distinct().collect(Collectors.joining(","))); //款号
                            amzOrderV.setAsin(StringUtils.isNotEmpty(scTicket.getAsin())?scTicket.getAsin():saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getAsin).distinct().collect(Collectors.joining(","))); //ASIN

                            //商品信息封装
                            String erpskus = saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getErpsku).collect(Collectors.joining(","));
                            this.ticketOrderProductPack(Arrays.asList(erpskus.split(",")), scTicket.getOrganizationId().intValue(), amzOrderV,scTicket.getGoodsSku());
                            amzOrderV.setBelongProject(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getBelongProject).distinct().collect(Collectors.joining(",")));
                        }

                }
            } else if (ScTicketConstant.TICKET_TYPE_ORDER_CANCEL.equals(scTicket.getTicketType())) {  //订单取消
                Long orderId = scTicket.getOrderId(); //取消订单会存在接收时系统无订单情况

                //工单表设置订单ID
                if (scTicket.getOrderId() == null && !StringUtils.isEmpty(scTicket.getAmazonOrderId())&&AccountSaleChannelEnum.TIKTOK.getValue().equals(scTicket.getTicketSource())) {
                    SaleOrders saleOrders = new SaleOrders();
                    saleOrders.setOrgId(scTicket.getOrganizationId());
                    saleOrders.setChannelId(scTicket.getShopId());
                    saleOrders.setOrderNo(scTicket.getAmazonOrderId());
                    saleOrders.setChannel(AccountSaleChannelEnum.TIKTOK.getValue());
                    List<SaleOrders> saleOrdersList = saleOrdersMapper.selectSaleOrderList(saleOrders);
                    if (!CollectionUtils.isEmpty(saleOrdersList)) {
                        orderId = saleOrdersList.get(0).getId();
                    }
                }
                //根据ID获取客户信息
                List<ScManualOrderVO> saleOrderVosOne = null;
                if (orderId !=null) {
                    saleOrderVosOne = saleOrdersMapper.selectSaleOrderAndCustomerByOrderId(orderId);
                }


                //查询客户信息
                if (!CollectionUtils.isEmpty(saleOrderVosOne)) {
                    ScManualOrderVO scManualOrderVO = saleOrderVosOne.get(0);
                    amzOrderV.setReverseOrderId(scTicket.getReverseOrderId()); //逆向订单号
                    amzOrderV.setOrderNo(scTicket.getAmazonOrderId());//订单号
                    amzOrderV.setSellerOrderNumber(scManualOrderVO.getPoNumber());//买家订单号
                    amzOrderV.setBuyName(scManualOrderVO.getCustomerName()); //买家姓名
                    amzOrderV.setBuyEmail(scManualOrderVO.getEmail());//买家邮箱
                    amzOrderV.setChannelCreated(scManualOrderVO.getPstChannelCreated()); //下单时间
                    amzOrderV.setLatestShipDate(scManualOrderVO.getLatestShipDate()); //发货时限
                    amzOrderV.setShipmentDate(scManualOrderVO.getShipmentDate());//发货时间
                    amzOrderV.setPromoteOrders("N");    //推广订单
                    amzOrderV.setShopName(scManualOrderVO.getTitle()); //店铺名
                    amzOrderV.setChannel(scManualOrderVO.getChannel()); //TikTok渠道
                    amzOrderV.setCountry(scManualOrderVO.getCountryCode()); //国家
                    amzOrderV.setProductIds(saleOrderVosOne.stream().filter(item -> item.getProductId() != null).map(ScManualOrderVO::getProductId).distinct().collect(Collectors.toList()));// 商品ID集合
                    amzOrderV.setErpsku(saleOrderVosOne.stream().map(ScManualOrderVO::getErpSku).distinct().collect(Collectors.joining(","))); //款号
                    amzOrderV.setImageUrls(saleOrderVosOne.stream().filter(item -> item.getImageUrl() != null).map(ScManualOrderVO::getImageUrl).distinct().collect(Collectors.toList()));//图片集合
                  //保存该工单客户信息
                    if (null == scTicket.getCustomerId()) {  //异步保存客户及工单关联客户
                        scCustomerService.saveCustomerInfoAndJoinTicket(scManualOrderVO, scTicket);
                    }
                    if (null == scTicket.getOrderId() && orderId != null) { //工单订单号为空，更新取消表计工单表订单ID
                        ScTicket scTicketUpate = new ScTicket();
                        scTicketUpate.setId(scTicket.getId());
                        scTicketUpate.setOrderId(orderId);
                        scTicketMapper.updateScTicket(scTicketUpate);

                        if (scTicket.getSourceId() != null) {
                            SaleOrderCancel saleOrderCanceUpate = new SaleOrderCancel();
                            saleOrderCanceUpate.setId(scTicket.getSourceId());
                            saleOrderCanceUpate.setOrderId(orderId);
                            saleOrderCancelService.updateById(saleOrderCanceUpate);
                        }
                    }


                    scTicket.setOuterId(scManualOrderVO.getCustomerOuterId()); //会话用户outerId   展示拼接使用
                    scTicket.setCustomerName(scManualOrderVO.getCustomerName());//客户名称
                }

                //查询订单获取 取消状态
                SaleOrderCancel saleOrderCancel = saleOrderCancelService.getById(scTicket.getSourceId());
                if (null != saleOrderCancel) {
                    amzOrderV.setBusinessTicketStatusStr(OrderCancelStatusEnum.getDesc(saleOrderCancel.getCancelStatus())); //取消状态
                }
            } else if (ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(scTicket.getTicketType()) ||
                    ScTicketConstant.TICKET_TYPE_OUTSIDE_REFUND.equals(scTicket.getTicketType())) {
                Long customerId = scTicket.getCustomerId();
                if (null != scTicket.getAmazonOrderId()) {
                    //查询订单信息
                    List<SaleOrderVoTicket> saleOrderVoTicketList = saleOrdersMapper.selectOrderByOrderId(scTicket.getOrderId());
                    //查询客户信息
                    if (!CollectionUtils.isEmpty(saleOrderVoTicketList)) {
                        SaleOrderVoTicket saleOrderVoTicket = saleOrderVoTicketList.get(0);
                        amzOrderV.setOrderNo(saleOrderVoTicket.getOrderNo()); //订单号
                        amzOrderV.setSellerOrderNumber(saleOrderVoTicket.getPoNumber()); //买家订单号
                        amzOrderV.setBuyName(saleOrderVoTicket.getBuyName()); //买家姓名（使用收件人姓名）
                        amzOrderV.setEncryptEmail(null);//加密邮箱
                        amzOrderV.setBuyEmail(saleOrderVoTicket.getBuyEmail());//邮箱
                        amzOrderV.setSellerSku(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getSellerSku).distinct().collect(Collectors.joining(","))); //SellerSku
                        amzOrderV.setQuantity(saleOrderVoTicketList.stream().mapToInt(SaleOrderVoTicket::getQuantity).sum()); //销量/数量
                        amzOrderV.setItemAmount(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getItemAmount).reduce(BigDecimal.ZERO, BigDecimal::add));//金额
                        amzOrderV.setShopName(saleOrderVoTicket.getTitle());//店铺名
                        amzOrderV.setChannel(saleOrderVoTicket.getChannel());//销售渠道
                        amzOrderV.setCountry(saleOrderVoTicket.getCountryCode()); //国家
                        amzOrderV.setOperationUserName(saleOrderVoTicket.getOperationUserName()); //运营
                        amzOrderV.setErpsku(StringUtils.isNotEmpty(scTicket.getGoodsSku())?scTicket.getGoodsSku():saleOrderVoTicketList.stream().filter(t->StringUtils.isNotEmpty(t.getErpsku())).map(SaleOrderVoTicket::getErpsku).distinct().collect(Collectors.joining(","))); //款号
                        amzOrderV.setAsin(StringUtils.isNotEmpty(scTicket.getAsin())?scTicket.getAsin():saleOrderVoTicketList.stream().filter(t->StringUtils.isNotEmpty(t.getAsin())).map(SaleOrderVoTicket::getAsin).distinct().collect(Collectors.joining(","))); //ASIN
                        amzOrderV.setBelongProject(saleOrderVoTicket.getBelongProject());//项目
                        amzOrderV.setPromoteOrders("N");
                        amzOrderV.setGoodsName(saleOrderVoTicketList.stream().filter(t->StringUtils.isNotEmpty(t.getGoodsName())).map(SaleOrderVoTicket::getGoodsName).distinct().collect(Collectors.joining(",")));//商品名称
                        //图片相关信息封装
                        String erpskus = saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getErpsku).collect(Collectors.joining(","));
                        this.ticketOrderProductPack(Arrays.asList(erpskus.split(",")), scTicket.getOrganizationId().intValue(), amzOrderV,scTicket.getGoodsSku());


                        //异步保存及工单关联客户
                        if (null == customerId) {
                            scCustomerService.saveCustomerAndJoinTicket(saleOrderVoTicket, scTicket);
                        }
                        scTicket.setCustomerName(amzOrderV.getBuyName());//客户名称
                        scTicket.setCustomerOuterId(saleOrderVoTicket.getCustomerOuterId()); //平台客户ID，用于侧边栏获取用户订单使用
                    }
                    businessTicketStatusStr = getTicketBusinessStatus(ticketId, String.valueOf(scTicket.getOrderId()));
                    amzOrderV.setBusinessTicketStatusStr(businessTicketStatusStr);
                }
            } else if (ScTicketConstant.TICKET_TYPE_REFUND_REQUEST.equals(scTicket.getTicketType())) { //退款请求
                Long customerId = scTicket.getCustomerId();
                if (null != scTicket.getAmazonOrderId()) {
                    //查询订单信息
                    List<SaleOrderVoTicket> saleOrderVoTicketList = saleOrdersMapper.selectOrderByOrderId(scTicket.getOrderId());
                    if (!CollectionUtils.isEmpty(saleOrderVoTicketList)) {
                        SaleOrderVoTicket saleOrderVoTicket = saleOrderVoTicketList.get(0);
                        amzOrderV.setOrderNo(saleOrderVoTicket.getOrderNo()); //订单号
                        amzOrderV.setSellerOrderNumber(saleOrderVoTicket.getPoNumber()); //买家订单号
                        amzOrderV.setReverseOrderId(scTicket.getReverseOrderId()); //逆向订单号
                        amzOrderV.setBuyName(saleOrderVoTicket.getCustomerName()); //买家姓名（使用收件人姓名）
                        amzOrderV.setBuyEmail(saleOrderVoTicket.getBuyEmail());//买家邮箱
                        amzOrderV.setEncryptEmail(saleOrderVoTicket.getBuyEmail());//加密邮箱
                        amzOrderV.setChannelCreated(saleOrderVoTicket.getPstChannelCreated()); //下单时间
                        amzOrderV.setLatestShipDate(saleOrderVoTicket.getLatestShipDate()); //发货时限
                        amzOrderV.setShipmentDate(saleOrderVoTicket.getShipmentDate()); //发货时间
                        amzOrderV.setPromoteOrders("N");
                        amzOrderV.setShopName(saleOrderVoTicket.getTitle());//店铺名
                        amzOrderV.setChannel(saleOrderVoTicket.getChannel());//销售渠道
                        amzOrderV.setCountry(saleOrderVoTicket.getCountryCode()); //国家CODE
                        amzOrderV.setErpsku(StringUtils.isNotEmpty(scTicket.getGoodsSku())?scTicket.getGoodsSku():saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getErpsku).distinct().collect(Collectors.joining(","))); //款号
                        scTicket.setOuterId(saleOrderVoTicket.getCustomerOuterId()); //会话用户outerId   展示拼接使用
                        scTicket.setCustomerName(saleOrderVoTicket.getCustomerName());//客户名称
                        scTicket.setCustomerOuterId(saleOrderVoTicket.getCustomerOuterId()); //平台客户ID，用于侧边栏获取用户订单使用


                        //图片相关信息封装
                        String erpskus = saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getErpsku).collect(Collectors.joining(","));
                        this.ticketOrderProductPack(Arrays.asList(erpskus.split(",")), scTicket.getOrganizationId().intValue(), amzOrderV,scTicket.getGoodsSku());


                        //异步保存及工单关联客户
                        if (null == customerId) {
                            scCustomerService.saveCustomerAndJoinTicket(saleOrderVoTicket, scTicket);
                        }
                    }
                    businessTicketStatusStr = getTicketBusinessStatus(ticketId, String.valueOf(scTicket.getOrderId()));
                    amzOrderV.setBusinessTicketStatusStr(businessTicketStatusStr);
                }
            } else if (ScTicketConstant.TICKET_TYPE_RETURN.equals(scTicket.getTicketType()) || ScTicketConstant.TICKET_TYPE_REPLACEMENT.equals(scTicket.getTicketType())) { //退货
                Long customerId = scTicket.getCustomerId();



                if(ScTicketConstant.MATCH_TYPE_MANUAL.equals(scTicket.getMatchOrderType())) {//手动
                    ScManualOrder scManualOrder = scManualOrderMapper.selectScManualOrderById(scTicket.getOrderId());//手工订单
                    //查询客户信息
                    if (scManualOrder != null) {
                        amzOrderV.setOrderNo(scManualOrder.getOrderNumber()); //订单号
                        amzOrderV.setBuyName(scManualOrder.getBuyerName()); //买家姓名（使用收件人姓名）
                        amzOrderV.setEncryptEmail(scManualOrder.getEmail());//加密邮箱
                        amzOrderV.setBuyEmail(scManualOrder.getEmail());//邮箱
                        amzOrderV.setSellerSku(scManualOrder.getSellerSku()); //SellerSku
                        amzOrderV.setQuantity(scManualOrder.getQuantity()); //销量/数量
                        amzOrderV.setItemAmount(scManualOrder.getAmount());//金额
                        if(scManualOrder.getShopId() != null) {
                            QueryWrapper<Account> queryWrapper = new QueryWrapper<>();
                            queryWrapper.eq("id",scManualOrder.getShopId());
                            queryWrapper.eq("org_id",scTicket.getOrganizationId());
                            List<Account> accountList = accountService.list(queryWrapper);
                            if(CollectionUtil.isNotEmpty(accountList)) {
                                amzOrderV.setShopName(accountList.get(0).getTitle());//店铺名
                            }
                        }
                        if(StringUtil.isNotEmpty(scManualOrder.getGoodsSku())) {
                            this.ticketOrderProductPack(Arrays.asList(scManualOrder.getGoodsSku().split(",")), scTicket.getOrganizationId().intValue(), amzOrderV,null);
                        }
                        //amzOrderV.setChannel();//销售渠道
                        amzOrderV.setCountry(scManualOrder.getCountry()); //国家
//                        amzOrderV.setOperationUserName(); //运营
                        amzOrderV.setErpsku(scManualOrder.getGoodsSku()); //款号
                        amzOrderV.setAsin(scManualOrder.getAsin()); //ASIN
//                        amzOrderV.setBelongProject();//项目
                        amzOrderV.setPromoteOrders("N");
                        scTicket.setCustomerName(scManualOrder.getBuyerName());//客户名称
                    }

                }else {
                    List<ScManualOrderVO> saleOrderVosOne = saleOrdersMapper.selectSaleOrderAndCustomerByOrderId(scTicket.getOrderId());
                    //查询客户信息
                    if (!CollectionUtils.isEmpty(saleOrderVosOne)) {
                        ScManualOrderVO scManualOrderVO = saleOrderVosOne.get(0);
                        amzOrderV.setReverseOrderId(scTicket.getReverseOrderId()); //逆向订单号
                        amzOrderV.setOrderNo(scTicket.getAmazonOrderId());//订单号
                        amzOrderV.setBuyName(scManualOrderVO.getCustomerName()); //买家姓名
                        amzOrderV.setBuyEmail(scManualOrderVO.getEmail());//买家邮箱
                        if(ScTicketConstant.TICKET_SOURCE_WALMART.equals(scTicket.getTicketSource())) { // walmart的退货工单walmart的联系客户发送对象是加密邮箱，退货数据中有存储
                            WalmartReturnInfoEntity walmartReturnInfoEntity = returnInfoMapper.getWalmartReturnInfoByReturnOrderId(scTicket.getOrganizationId(),scTicket.getReverseOrderId());
                            amzOrderV.setEncryptEmail(walmartReturnInfoEntity != null ? walmartReturnInfoEntity.getCustomerEmailId() : null);//加密邮箱
                        } else {
                            amzOrderV.setEncryptEmail(scManualOrderVO.getEmail());//加密邮箱
                        }
                        amzOrderV.setChannelCreated(scManualOrderVO.getPstChannelCreated()); //下单时间
                        amzOrderV.setLatestShipDate(scManualOrderVO.getLatestShipDate()); //发货时限
                        amzOrderV.setShipmentDate(scManualOrderVO.getShipmentDate());//发货时间
                        amzOrderV.setPromoteOrders("N");    //推广订单
                        amzOrderV.setShopName(scManualOrderVO.getTitle()); //店铺名
                        amzOrderV.setChannel(saleOrderVosOne.get(0).getChannel());
                        amzOrderV.setCountry(scManualOrderVO.getCountryCode()); //国家
                        //若工单有，取工单SKU
                        amzOrderV.setErpsku(StringUtils.isNotEmpty(scTicket.getGoodsSku())?scTicket.getGoodsSku():saleOrderVosOne.stream().map(ScManualOrderVO::getErpSku).distinct().collect(Collectors.joining(","))); //款号

                        //图片相关信息封装
                        String erpskus = saleOrderVosOne.stream().map(ScManualOrderVO::getErpSku).collect(Collectors.joining(","));
                        this.ticketOrderProductPack(Arrays.asList(erpskus.split(",")), scTicket.getOrganizationId().intValue(), amzOrderV,scTicket.getGoodsSku());

                        if (null == customerId) {  //异步保存客户及工单关联客户（用于关联客户发起会话）
                            scCustomerService.saveCustomerInfoAndJoinTicket(scManualOrderVO, scTicket);
                        }
                        scTicket.setOuterId(scManualOrderVO.getCustomerOuterId()); //平台用户ID  展示拼接使用
                        scTicket.setCustomerOuterId(scManualOrderVO.getCustomerOuterId()); //平台客户ID，用于侧边栏获取用户订单使用
                        scTicket.setCustomerName(scManualOrderVO.getCustomerName());//客户名称
                        amzOrderV.setBusinessTicketStatusStr(getTicketBusinessStatus(ticketId));

                    }
                }
            } else if (ScTicketConstant.TICKET_TYPE_PF.equals(scTicket.getTicketType())) {//PF

                Long customerId = scTicket.getCustomerId();
                if (null != scTicket.getAmazonOrderId()) {
                    //查询订单信息
                    List<SaleOrderVoTicket> saleOrderVoTicketList = saleOrdersMapper.selectOrderByOrderId(scTicket.getOrderId());
                    //查询客户信息
                    if (!CollectionUtils.isEmpty(saleOrderVoTicketList)) {
                        SaleOrderVoTicket saleOrderVoTicket = saleOrderVoTicketList.get(0);
                        amzOrderV.setOrderNo(saleOrderVoTicket.getOrderNo()); //订单号
                        amzOrderV.setSellerOrderNumber(saleOrderVoTicket.getPoNumber()); //买家订单号
                        amzOrderV.setBuyName(saleOrderVoTicket.getBuyName()); //买家姓名（使用收件人姓名）
                        amzOrderV.setEncryptEmail(saleOrderVoTicket.getBuyEmail());//加密邮箱
                        amzOrderV.setBuyEmail(saleOrderVoTicket.getBuyEmail());//邮箱
                        amzOrderV.setSellerSku(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getSellerSku).distinct().collect(Collectors.joining(","))); //SellerSku
                        amzOrderV.setQuantity(saleOrderVoTicketList.stream().mapToInt(SaleOrderVoTicket::getQuantity).sum()); //销量/数量
                        amzOrderV.setItemAmount(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getItemAmount).reduce(BigDecimal.ZERO, BigDecimal::add));//金额
                        amzOrderV.setShopName(saleOrderVoTicket.getTitle());//店铺名
                        amzOrderV.setChannel(saleOrderVoTicket.getChannel());//销售渠道
                        amzOrderV.setCountry(saleOrderVoTicket.getCountryCode()); //国家
                        amzOrderV.setOperationUserName(saleOrderVoTicket.getOperationUserName()); //运营

                        amzOrderV.setErpsku(StringUtils.isNotEmpty(scTicket.getGoodsSku())?scTicket.getGoodsSku():saleOrderVoTicketList.stream().filter(t->StringUtils.isNotEmpty(t.getErpsku())).map(SaleOrderVoTicket::getErpsku).distinct().collect(Collectors.joining(","))); //款号
                        amzOrderV.setAsin(StringUtils.isNotEmpty(scTicket.getAsin())?scTicket.getAsin():saleOrderVoTicketList.stream().filter(t->StringUtils.isNotEmpty(t.getAsin())).map(SaleOrderVoTicket::getAsin).distinct().collect(Collectors.joining(","))); //ASIN
                        amzOrderV.setBelongProject(saleOrderVoTicket.getBelongProject());//项目
                        amzOrderV.setGoodsName(saleOrderVoTicketList.stream().filter(t->StringUtils.isNotEmpty(t.getGoodsName())).map(SaleOrderVoTicket::getGoodsName).distinct().collect(Collectors.joining(",")));//商品名称
                        amzOrderV.setChannelCreated(saleOrderVoTicket.getPstChannelCreated()); //下单时间
                        amzOrderV.setLatestShipDate(saleOrderVoTicket.getLatestShipDate()); //发货时限
                        amzOrderV.setShipmentDate(saleOrderVoTicket.getShipmentDate()); //发货时间
                        amzOrderV.setExpectedReceipt(null);//收货预计
                        //图片相关信息封装
                        String erpskus = saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getErpsku).collect(Collectors.joining(","));
                        this.ticketOrderProductPack(Arrays.asList(erpskus.split(",")), scTicket.getOrganizationId().intValue(), amzOrderV,scTicket.getGoodsSku());

                        amzOrderV.setPromoteOrders("N");
                        //异步保存及工单关联客户
                        if (null == customerId) {
                            scCustomerService.saveCustomerAndJoinTicket(saleOrderVoTicket, scTicket);
                        }
                        scTicket.setCustomerName(amzOrderV.getBuyName());//客户名称
                    }

                }
            }else if (ScTicketConstant.TICKET_TYPE_OFFSITE_LETTER.equals(scTicket.getTicketType())) {//其他站外信

                Long customerId = scTicket.getCustomerId();
                if(ScTicketConstant.MATCH_TYPE_MANUAL.equals(scTicket.getMatchOrderType())) {//手动
                    ScManualOrder scManualOrder = scManualOrderMapper.selectScManualOrderById(scTicket.getOrderId());//手工订单
                    //查询客户信息
                    if (scManualOrder != null) {
                        amzOrderV.setOrderNo(scManualOrder.getOrderNumber()); //订单号
                        amzOrderV.setBuyName(scManualOrder.getBuyerName()); //买家姓名（使用收件人姓名）
                        amzOrderV.setEncryptEmail(scManualOrder.getEmail());//加密邮箱
                        amzOrderV.setBuyEmail(scManualOrder.getEmail());//邮箱
                        amzOrderV.setSellerSku(scManualOrder.getSellerSku()); //SellerSku
                        amzOrderV.setQuantity(scManualOrder.getQuantity()); //销量/数量
                        amzOrderV.setItemAmount(scManualOrder.getAmount());//金额
                        if(scManualOrder.getShopId() != null) {
                            QueryWrapper<Account> queryWrapper = new QueryWrapper<>();
                            queryWrapper.eq("id",scManualOrder.getShopId());
                            queryWrapper.eq("org_id",scTicket.getOrganizationId());
                            List<Account> accountList = accountService.list(queryWrapper);
                            if(CollectionUtil.isNotEmpty(accountList)) {
                                amzOrderV.setShopName(accountList.get(0).getTitle());//店铺名
                            }
                        }
                        if(StringUtil.isNotEmpty(scManualOrder.getGoodsSku())) {
                            this.ticketOrderProductPack(Arrays.asList(scManualOrder.getGoodsSku().split(",")), scTicket.getOrganizationId().intValue(), amzOrderV,null);
                        }
                        //amzOrderV.setChannel();//销售渠道
                        amzOrderV.setCountry(scManualOrder.getCountry()); //国家
//                        amzOrderV.setOperationUserName(); //运营
                        amzOrderV.setErpsku(scManualOrder.getGoodsSku()); //款号
                        amzOrderV.setAsin(scManualOrder.getAsin()); //ASIN
//                        amzOrderV.setBelongProject();//项目
                        amzOrderV.setPromoteOrders("N");
                        scTicket.setCustomerName(scManualOrder.getBuyerName());//客户名称
                    }

                }else {
                    if (null != scTicket.getAmazonOrderId()) {
                        //查询订单信息
                        List<SaleOrderVoTicket> saleOrderVoTicketList = saleOrdersMapper.selectOrderByOrderId(scTicket.getOrderId());
                        //查询客户信息
                        if (!CollectionUtils.isEmpty(saleOrderVoTicketList)) {
                            SaleOrderVoTicket saleOrderVoTicket = saleOrderVoTicketList.get(0);
                            amzOrderV.setOrderNo(saleOrderVoTicket.getOrderNo()); //订单号
                            amzOrderV.setSellerOrderNumber(saleOrderVoTicket.getPoNumber()); //买家订单号
                            amzOrderV.setBuyName(saleOrderVoTicket.getBuyName()); //买家姓名（使用收件人姓名）
                            amzOrderV.setEncryptEmail(saleOrderVoTicket.getBuyEmail());//加密邮箱
                            amzOrderV.setBuyEmail(saleOrderVoTicket.getBuyEmail());//邮箱
                            amzOrderV.setSellerSku(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getSellerSku).distinct().collect(Collectors.joining(","))); //SellerSku
                            amzOrderV.setQuantity(saleOrderVoTicketList.stream().mapToInt(SaleOrderVoTicket::getQuantity).sum()); //销量/数量
                            amzOrderV.setItemAmount(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getItemAmount).reduce(BigDecimal.ZERO, BigDecimal::add));//金额
                            amzOrderV.setShopName(saleOrderVoTicket.getTitle());//店铺名
                            amzOrderV.setChannel(saleOrderVoTicket.getChannel());//销售渠道
                            amzOrderV.setCountry(saleOrderVoTicket.getCountryCode()); //国家
                            amzOrderV.setOperationUserName(saleOrderVoTicket.getOperationUserName()); //运营
                            amzOrderV.setChannelCreated(saleOrderVoTicket.getPstChannelCreated()); //下单时间
                            amzOrderV.setLatestShipDate(saleOrderVoTicket.getLatestShipDate()); //发货时限
                            amzOrderV.setShipmentDate(saleOrderVoTicket.getShipmentDate()); //发货时间
                            amzOrderV.setExpectedReceipt(null);//收货预计
                            amzOrderV.setErpsku(StringUtils.isNotEmpty(scTicket.getGoodsSku()) ? scTicket.getGoodsSku() : saleOrderVoTicketList.stream().filter(t -> StringUtils.isNotEmpty(t.getErpsku())).map(SaleOrderVoTicket::getErpsku).distinct().collect(Collectors.joining(","))); //款号
                            amzOrderV.setAsin(StringUtils.isNotEmpty(scTicket.getAsin()) ? scTicket.getAsin() : saleOrderVoTicketList.stream().filter(t -> StringUtils.isNotEmpty(t.getAsin())).map(SaleOrderVoTicket::getAsin).distinct().collect(Collectors.joining(","))); //ASIN
                            amzOrderV.setBelongProject(saleOrderVoTicket.getBelongProject());//项目

                            List<SaleOrderVoTicket> orderEstimateShipCost = saleOrdersMapper.selectOrderEstimateShipCost(scTicket.getOrderId());
                            if(CollectionUtils.isNotEmpty(orderEstimateShipCost)) {
                                BigDecimal total = orderEstimateShipCost.stream()
                                        .map(obj -> obj.getEstimateShipCost() != null ? obj.getEstimateShipCost() : BigDecimal.ZERO)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                amzOrderV.setEstimateShipCost(total);//预估物流费
                            }
                            //图片相关信息封装
                            String erpskus = saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getErpsku).collect(Collectors.joining(","));
                            this.ticketOrderProductPack(Arrays.asList(erpskus.split(",")), scTicket.getOrganizationId().intValue(), amzOrderV, scTicket.getGoodsSku());

                            amzOrderV.setPromoteOrders("N");
                            //异步保存及工单关联客户
                            if (null == customerId) {
                                scCustomerService.saveCustomerAndJoinTicket(saleOrderVoTicket, scTicket);
                            }
                            scTicket.setCustomerName(amzOrderV.getBuyName());//客户名称
                        }

                    } else {
                        amzOrderV.setErpsku(scTicket.getGoodsSku());
                    }
                }

            } else if (ScTicketConstant.TICKET_TYPE_PR.equals(scTicket.getTicketType()) || ScTicketConstant.TICKET_TYPE_NR.equals(scTicket.getTicketType())) {//PR,NR工单
                Long customerId = scTicket.getCustomerId();
                if(ScTicketConstant.MATCH_TYPE_MANUAL.equals(scTicket.getMatchOrderType())) {//手动
                    ScManualOrder scManualOrder = scManualOrderMapper.selectScManualOrderById(scTicket.getOrderId());//手工订单
                    //查询客户信息
                    if (scManualOrder != null) {
                        amzOrderV.setOrderNo(scManualOrder.getOrderNumber()); //订单号
                        amzOrderV.setBuyName(scManualOrder.getBuyerName()); //买家姓名（使用收件人姓名）
                        amzOrderV.setEncryptEmail(scManualOrder.getEmail());//加密邮箱
                        amzOrderV.setBuyEmail(scManualOrder.getEmail());//邮箱
                        amzOrderV.setSellerSku(scManualOrder.getSellerSku()); //SellerSku
                        amzOrderV.setQuantity(scManualOrder.getQuantity()); //销量/数量
                        amzOrderV.setItemAmount(scManualOrder.getAmount());//金额
                        if(scManualOrder.getShopId() != null) {
                            QueryWrapper<Account> queryWrapper = new QueryWrapper<>();
                            queryWrapper.eq("id",scManualOrder.getShopId());
                            queryWrapper.eq("org_id",scTicket.getOrganizationId());
                            List<Account> accountList = accountService.list(queryWrapper);
                            if(CollectionUtil.isNotEmpty(accountList)) {
                                amzOrderV.setShopName(accountList.get(0).getTitle());//店铺名
                            }
                        };
                        if(StringUtil.isNotEmpty(scManualOrder.getGoodsSku())) {
                            this.ticketOrderProductPack(Arrays.asList(scManualOrder.getGoodsSku().split(",")), scTicket.getOrganizationId().intValue(), amzOrderV,null);
                        }
                        //amzOrderV.setChannel();//销售渠道
                        amzOrderV.setCountry(scManualOrder.getCountry()); //国家
//                        amzOrderV.setOperationUserName(); //运营
                        amzOrderV.setErpsku(scManualOrder.getGoodsSku()); //款号
                        amzOrderV.setAsin(scManualOrder.getAsin()); //ASIN
//                        amzOrderV.setBelongProject();//项目
                        amzOrderV.setPromoteOrders("N");
                        scTicket.setCustomerName(scManualOrder.getBuyerName());//客户名称
                    }

                } else {//自动匹配
                    if (null != scTicket.getAmazonOrderId() && ScTicketConstant.MATCH_TYPE_AUTO.equals(scTicket.getMatchOrderType())) {//自动
                        //查询订单信息
                        List<SaleOrderVoTicket> saleOrderVoTicketList = saleOrdersMapper.selectOrderByOrderId(scTicket.getOrderId());
                        //查询客户信息
                        if (!CollectionUtils.isEmpty(saleOrderVoTicketList)) {
                            SaleOrderVoTicket saleOrderVoTicket = saleOrderVoTicketList.get(0);
                            amzOrderV.setOrderNo(saleOrderVoTicket.getOrderNo()); //订单号
                            amzOrderV.setSellerOrderNumber(saleOrderVoTicket.getPoNumber()); //买家订单号
                            amzOrderV.setBuyName(saleOrderVoTicket.getBuyName()); //买家姓名（使用收件人姓名）
                            amzOrderV.setEncryptEmail(saleOrderVoTicket.getBuyEmail());//加密邮箱
                            amzOrderV.setBuyEmail(saleOrderVoTicket.getBuyEmail());//邮箱
                            amzOrderV.setChannelCreated(saleOrderVoTicket.getPstChannelCreated()); //下单时间
                            amzOrderV.setLatestShipDate(saleOrderVoTicket.getLatestShipDate()); //发货时限
                            amzOrderV.setShipmentDate(saleOrderVoTicket.getShipmentDate()); //发货时间
                            amzOrderV.setExpectedReceipt(null);//收货预计
                            amzOrderV.setSellerSku(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getSellerSku).distinct().collect(Collectors.joining(","))); //SellerSku
                            amzOrderV.setQuantity(saleOrderVoTicketList.stream().mapToInt(SaleOrderVoTicket::getQuantity).sum()); //销量/数量
                            amzOrderV.setItemAmount(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getItemAmount).reduce(BigDecimal.ZERO, BigDecimal::add));//金额
                            amzOrderV.setShopName(saleOrderVoTicket.getTitle());//店铺名
                            amzOrderV.setChannel(saleOrderVoTicket.getChannel());//销售渠道
                            amzOrderV.setCountry(saleOrderVoTicket.getCountryCode()); //国家
                            amzOrderV.setOperationUserName(saleOrderVoTicket.getOperationUserName()); //运营
                            amzOrderV.setErpsku(StringUtils.isNotEmpty(scTicket.getGoodsSku())?scTicket.getGoodsSku():saleOrderVoTicketList.stream().filter(t -> StringUtils.isNotEmpty(t.getErpsku())).map(SaleOrderVoTicket::getErpsku).distinct().collect(Collectors.joining(","))); //款号
                            amzOrderV.setAsin(StringUtils.isNotEmpty(scTicket.getAsin())?scTicket.getAsin():saleOrderVoTicketList.stream().filter(t -> StringUtils.isNotEmpty(t.getAsin())).map(SaleOrderVoTicket::getAsin).distinct().collect(Collectors.joining(","))); //ASIN
                            amzOrderV.setBelongProject(saleOrderVoTicket.getBelongProject());//项目
                            amzOrderV.setPromoteOrders("N");

                            //图片相关信息封装
                            if (!StringUtils.isEmpty(amzOrderV.getErpsku())) {
                                this.ticketOrderProductPack(Arrays.asList(amzOrderV.getErpsku().split(",")), scTicket.getOrganizationId().intValue(), amzOrderV,scTicket.getGoodsSku());
                            }
                            //异步保存及工单关联客户
                            if (null == customerId) {
                                scCustomerService.saveCustomerAndJoinTicket(saleOrderVoTicket, scTicket);
                            }
                            scTicket.setCustomerName(amzOrderV.getBuyName());//客户名称
                        }
                    }else if(!StringUtils.isEmpty(scTicket.getGoodsSku())){ //未匹配到订单，展示review对应asin及sku信息
                        amzOrderV.setAsin(scTicket.getAsin()); //ASIN
                        amzOrderV.setErpsku(scTicket.getGoodsSku()); //款号
                        this.ticketOrderProductPack(Arrays.asList(scTicket.getGoodsSku().split(",")), scTicket.getOrganizationId().intValue(), amzOrderV,null);
                    }
                }
            } else if (ScTicketConstant.TICKET_TYPE_AMZ_QA.equals(scTicket.getTicketType()) || ScTicketConstant.TICKET_TYPE_AMZ_ATOZ.equals(scTicket.getTicketType())  || ScTicketConstant.TICKET_TYPE_AMZ_EMAIL.equals(scTicket.getTicketType()) || ScTicketConstant.TICKET_TYPE_INBOX.equals(scTicket.getTicketType()) || ScTicketConstant.TICKET_TYPE_EMAIL.equals(scTicket.getTicketType())
            || ScTicketConstant.TICKET_TYPE_CHARGE_BACK.equals(scTicket.getTicketType())) {

                //查询发件人及收件人邮箱
                if (scTicket.getSourceId() != null) {
                    SyncEmailInfo syncEmailInfo = syncEmailInfoMapper.selectEmailInfoById(scTicket.getSourceId());
                    if (syncEmailInfo != null) {
                          //1.买家发给卖家
                        scTicket.setFromEmailAddress("1".equals(syncEmailInfo.getType()) ? syncEmailInfo.getToAddress() : syncEmailInfo.getFromAddress()); //发件箱
                        scTicket.setToEmailAddress("1".equals(syncEmailInfo.getType()) ? syncEmailInfo.getFromAddress() : syncEmailInfo.getToAddress());//收件箱
                    }
                }

                Long customerId = scTicket.getCustomerId();
                if(ScTicketConstant.MATCH_TYPE_MANUAL.equals(scTicket.getMatchOrderType())) {//手动
                    ScManualOrder scManualOrder = scManualOrderMapper.selectScManualOrderById(scTicket.getOrderId());//手工订单
                    //查询客户信息
                    if (scManualOrder != null) {
                        amzOrderV.setOrderNo(scManualOrder.getOrderNumber()); //订单号
                        amzOrderV.setBuyName(scManualOrder.getBuyerName()); //买家姓名（使用收件人姓名）
                        amzOrderV.setEncryptEmail(scManualOrder.getEmail());//加密邮箱
                        amzOrderV.setBuyEmail(scManualOrder.getEmail());//邮箱
                        amzOrderV.setSellerSku(scManualOrder.getSellerSku()); //SellerSku
                        amzOrderV.setQuantity(scManualOrder.getQuantity()); //销量/数量
                        amzOrderV.setItemAmount(scManualOrder.getAmount());//金额
                        if(scManualOrder.getShopId() != null) {
                            QueryWrapper<Account> queryWrapper = new QueryWrapper<>();
                            queryWrapper.eq("id",scManualOrder.getShopId());
                            queryWrapper.eq("org_id",scTicket.getOrganizationId());
                            List<Account> accountList = accountService.list(queryWrapper);
                            if(CollectionUtil.isNotEmpty(accountList)) {
                                amzOrderV.setShopName(accountList.get(0).getTitle());//店铺名
                            }
                        }
                        if(StringUtil.isNotEmpty(scManualOrder.getGoodsSku())) {
                            this.ticketOrderProductPack(Arrays.asList(scManualOrder.getGoodsSku().split(",")), scTicket.getOrganizationId().intValue(), amzOrderV,null);
                        }
                        //amzOrderV.setChannel();//销售渠道
                        amzOrderV.setCountry(scManualOrder.getCountry()); //国家
//                        amzOrderV.setOperationUserName(); //运营
                        amzOrderV.setErpsku(scManualOrder.getGoodsSku()); //款号
                        amzOrderV.setAsin(scManualOrder.getAsin()); //ASIN
//                        amzOrderV.setBelongProject();//项目
                        amzOrderV.setPromoteOrders("N");
                        scTicket.setCustomerName(scManualOrder.getBuyerName());//客户名称
                    }

                } else {//自动匹配
                    if (null != scTicket.getAmazonOrderId() && ScTicketConstant.MATCH_TYPE_AUTO.equals(scTicket.getMatchOrderType())) {//自动
                        //查询订单信息
                        List<SaleOrderVoTicket> saleOrderVoTicketList = saleOrdersMapper.selectOrderByOrderId(scTicket.getOrderId());
                        //查询客户信息
                        if (!CollectionUtils.isEmpty(saleOrderVoTicketList)) {
                            SaleOrderVoTicket saleOrderVoTicket = saleOrderVoTicketList.get(0);
                            amzOrderV.setOrderNo(saleOrderVoTicket.getOrderNo()); //订单号
                            amzOrderV.setSellerOrderNumber(saleOrderVoTicket.getPoNumber()); //买家订单号
                            amzOrderV.setBuyName(saleOrderVoTicket.getBuyName()); //买家姓名（使用收件人姓名）
                            amzOrderV.setEncryptEmail(saleOrderVoTicket.getBuyEmail());//加密邮箱
                            amzOrderV.setBuyEmail(saleOrderVoTicket.getBuyEmail());//邮箱
                            amzOrderV.setChannelCreated(saleOrderVoTicket.getPstChannelCreated()); //下单时间
                            amzOrderV.setLatestShipDate(saleOrderVoTicket.getLatestShipDate()); //发货时限
                            amzOrderV.setShipmentDate(saleOrderVoTicket.getShipmentDate()); //发货时间
                            amzOrderV.setExpectedReceipt(null);//收货预计
                            amzOrderV.setSellerSku(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getSellerSku).distinct().collect(Collectors.joining(","))); //SellerSku
                            amzOrderV.setQuantity(saleOrderVoTicketList.stream().mapToInt(SaleOrderVoTicket::getQuantity).sum()); //销量/数量
                            amzOrderV.setItemAmount(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getItemAmount).reduce(BigDecimal.ZERO, BigDecimal::add));//金额
                            amzOrderV.setShopName(saleOrderVoTicket.getTitle());//店铺名
                            amzOrderV.setChannel(saleOrderVoTicket.getChannel());//销售渠道
                            amzOrderV.setCountry(saleOrderVoTicket.getCountryCode()); //国家
                            amzOrderV.setOperationUserName(saleOrderVoTicket.getOperationUserName()); //运营
                            amzOrderV.setErpsku(StringUtils.isNotEmpty(scTicket.getGoodsSku())?scTicket.getGoodsSku():saleOrderVoTicketList.stream().filter(t -> StringUtils.isNotEmpty(t.getErpsku())).map(SaleOrderVoTicket::getErpsku).distinct().collect(Collectors.joining(","))); //款号
                            amzOrderV.setAsin(StringUtils.isNotEmpty(scTicket.getAsin())?scTicket.getAsin():saleOrderVoTicketList.stream().filter(t -> StringUtils.isNotEmpty(t.getAsin())).map(SaleOrderVoTicket::getAsin).distinct().collect(Collectors.joining(","))); //ASIN
                            amzOrderV.setBelongProject(saleOrderVoTicket.getBelongProject());//项目
                            //图片相关信息封装
                            String erpskus = saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getErpsku).collect(Collectors.joining(","));
                            this.ticketOrderProductPack(Arrays.asList(erpskus.split(",")), scTicket.getOrganizationId().intValue(), amzOrderV,scTicket.getGoodsSku());
                            amzOrderV.setPromoteOrders("N");
                            if(ScTicketConstant.TICKET_TYPE_EMAIL.equals(scTicket.getTicketType())) {
                                List<SaleOrderVoTicket> orderEstimateShipCost = saleOrdersMapper.selectOrderEstimateShipCost(scTicket.getOrderId());
                                if(CollectionUtils.isNotEmpty(orderEstimateShipCost)) {
                                    BigDecimal total = orderEstimateShipCost.stream()
                                            .map(obj -> obj.getEstimateShipCost() != null ? obj.getEstimateShipCost() : BigDecimal.ZERO)
                                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                                    amzOrderV.setEstimateShipCost(total);//预估物流费
                                }
                            }
                            //异步保存及工单关联客户
                            if (null == customerId) {
                                scCustomerService.saveCustomerAndJoinTicket(saleOrderVoTicket, scTicket);
                            }
                            scTicket.setCustomerName(amzOrderV.getBuyName());//客户名称
                        }
                    }
                }
            } else if (ScTicketConstant.TICKET_TYPE_NF.equals(scTicket.getTicketType())) {//NF
                Long customerId = scTicket.getCustomerId();
                //查询订单信息
                List<SaleOrderVoTicket> saleOrderVoTicketList = saleOrdersMapper.selectOrderByOrderId(scTicket.getOrderId());
                //查询客户信息
                if (!CollectionUtils.isEmpty(saleOrderVoTicketList)) {
                    SaleOrderVoTicket saleOrderVoTicket = saleOrderVoTicketList.get(0);
                    amzOrderV.setOrderNo(saleOrderVoTicket.getOrderNo()); //订单号
                    amzOrderV.setSellerOrderNumber(saleOrderVoTicket.getPoNumber()); //买家订单号
                    amzOrderV.setBuyName(saleOrderVoTicket.getBuyName()); //买家姓名（使用收件人姓名）
                    amzOrderV.setEncryptEmail(saleOrderVoTicket.getBuyEmail());//加密邮箱
                    amzOrderV.setBuyEmail(saleOrderVoTicket.getBuyEmail());//邮箱
                    amzOrderV.setSellerSku(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getSellerSku).distinct().collect(Collectors.joining(","))); //SellerSku
                    amzOrderV.setQuantity(saleOrderVoTicketList.stream().mapToInt(SaleOrderVoTicket::getQuantity).sum()); //销量/数量
                    amzOrderV.setItemAmount(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getItemAmount).reduce(BigDecimal.ZERO, BigDecimal::add));//金额
                    amzOrderV.setShopName(saleOrderVoTicket.getTitle());//店铺名
                    amzOrderV.setChannel(saleOrderVoTicket.getChannel());//销售渠道
                    amzOrderV.setCountry(saleOrderVoTicket.getCountryCode()); //国家
                    amzOrderV.setOperationUserName(saleOrderVoTicket.getOperationUserName()); //运营
                    amzOrderV.setErpsku(StringUtils.isNotEmpty(scTicket.getGoodsSku())?scTicket.getGoodsSku():saleOrderVoTicketList.stream().filter(t -> StringUtils.isNotEmpty(t.getErpsku())).map(SaleOrderVoTicket::getErpsku).distinct().collect(Collectors.joining(","))); //款号
                    amzOrderV.setAsin(StringUtils.isNotEmpty(scTicket.getAsin())?scTicket.getAsin():saleOrderVoTicketList.stream().filter(t -> StringUtils.isNotEmpty(t.getAsin())).map(SaleOrderVoTicket::getAsin).distinct().collect(Collectors.joining(","))); //ASIN
                    amzOrderV.setBelongProject(saleOrderVoTicket.getBelongProject());//项目
                    //图片相关信息封装
                    String erpskus = saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getErpsku).collect(Collectors.joining(","));
                    this.ticketOrderProductPack(Arrays.asList(erpskus.split(",")), scTicket.getOrganizationId().intValue(), amzOrderV,scTicket.getGoodsSku());
                    amzOrderV.setPromoteOrders("N");
                    //异步保存及工单关联客户
                    if (null == customerId) {
                        scCustomerService.saveCustomerAndJoinTicket(saleOrderVoTicket, scTicket);
                    }
                    scTicket.setCustomerName(amzOrderV.getBuyName());//客户名称
                }
            } else if (ScTicketConstant.TICKET_TYPE_REISSUE_SHIPMENT.equals(scTicket.getTicketType())) {//发货

                ScTicket scTicketByParentId = null;
                if (!Objects.isNull(scTicket.getParentTicketId())) {
                    scTicketByParentId = scTicketMapper.selectScTicketByTicketId(scTicket.getParentTicketId());
                    if (!Objects.isNull(scTicketByParentId)) {
                        scTicket.setParentTicketNumber(scTicketByParentId.getTicketNumber());
                    }
                }
                Long customerId = scTicket.getCustomerId();
                if(scTicketByParentId != null && ScTicketConstant.MATCH_TYPE_MANUAL.equals(scTicketByParentId.getMatchOrderType())) {//手动
                    ScManualOrder scManualOrder = scManualOrderMapper.selectScManualOrderById(scTicket.getOrderId());//手工订单
                    //查询客户信息
                    if (scManualOrder != null) {
                        amzOrderV.setOrderNo(scManualOrder.getOrderNumber()); //订单号
                        amzOrderV.setBuyName(scManualOrder.getBuyerName()); //买家姓名（使用收件人姓名）
                        amzOrderV.setEncryptEmail(scManualOrder.getEmail());//加密邮箱
                        amzOrderV.setBuyEmail(scManualOrder.getEmail());//邮箱
                        amzOrderV.setSellerSku(scManualOrder.getSellerSku()); //SellerSku
                        amzOrderV.setQuantity(scManualOrder.getQuantity()); //销量/数量
                        amzOrderV.setItemAmount(scManualOrder.getAmount());//金额
                        if(scManualOrder.getShopId() != null) {
                            QueryWrapper<Account> queryWrapper = new QueryWrapper<>();
                            queryWrapper.eq("id",scManualOrder.getShopId());
                            queryWrapper.eq("org_id",scTicket.getOrganizationId());
                            List<Account> accountList = accountService.list(queryWrapper);
                            if(CollectionUtil.isNotEmpty(accountList)) {
                                amzOrderV.setShopName(accountList.get(0).getTitle());//店铺名
                            }
                        }
                        if(StringUtil.isNotEmpty(scManualOrder.getGoodsSku())) {
                            this.ticketOrderProductPack(Arrays.asList(scManualOrder.getGoodsSku().split(",")), scTicket.getOrganizationId().intValue(), amzOrderV,null);
                        }
                        //amzOrderV.setChannel();//销售渠道
                        amzOrderV.setCountry(scManualOrder.getCountry()); //国家
//                        amzOrderV.setOperationUserName(); //运营
                        amzOrderV.setErpsku(scManualOrder.getGoodsSku()); //款号
                        amzOrderV.setAsin(scManualOrder.getAsin()); //ASIN
//                        amzOrderV.setBelongProject();//项目
                        amzOrderV.setPromoteOrders("N");
                        scTicket.setCustomerName(scManualOrder.getBuyerName());//客户名称
                    }

                }else {
                    if (null != scTicket.getAmazonOrderId()) {
                        //查询订单信息
                        List<SaleOrderVoTicket> saleOrderVoTicketList = saleOrdersMapper.selectOrderByOrderId(scTicket.getOrderId());
                        //查询客户信息
                        if (!CollectionUtils.isEmpty(saleOrderVoTicketList)) {
                            SaleOrderVoTicket saleOrderVoTicket = saleOrderVoTicketList.get(0);
                            amzOrderV.setOrderNo(saleOrderVoTicket.getOrderNo()); //订单号
                            amzOrderV.setSellerOrderNumber(saleOrderVoTicket.getPoNumber()); //买家订单号
                            amzOrderV.setBuyName(saleOrderVoTicket.getBuyName()); //买家姓名（使用收件人姓名）
                            amzOrderV.setEncryptEmail(saleOrderVoTicket.getBuyEmail());//加密邮箱
                            amzOrderV.setBuyEmail(saleOrderVoTicket.getBuyEmail());//邮箱
                            amzOrderV.setSellerSku(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getSellerSku).distinct().collect(Collectors.joining(","))); //SellerSku
                            amzOrderV.setQuantity(saleOrderVoTicketList.stream().mapToInt(SaleOrderVoTicket::getQuantity).sum()); //销量/数量
                            amzOrderV.setItemAmount(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getItemAmount).reduce(BigDecimal.ZERO, BigDecimal::add));//金额
                            amzOrderV.setShopName(saleOrderVoTicket.getTitle());//店铺名
                            amzOrderV.setChannel(saleOrderVoTicket.getChannel());//销售渠道
                            amzOrderV.setCountry(saleOrderVoTicket.getCountryCode()); //国家
                            amzOrderV.setOperationUserName(saleOrderVoTicket.getOperationUserName()); //运营
                            amzOrderV.setGoodsName(saleOrderVoTicketList.stream().filter(t->StringUtils.isNotEmpty(t.getGoodsName())).map(SaleOrderVoTicket::getGoodsName).distinct().collect(Collectors.joining(",")));//商品名称
                            amzOrderV.setChannelCreated(saleOrderVoTicket.getPstChannelCreated()); //下单时间
                            amzOrderV.setLatestShipDate(saleOrderVoTicket.getLatestShipDate()); //发货时限
                            amzOrderV.setShipmentDate(saleOrderVoTicket.getShipmentDate()); //发货时间
                            amzOrderV.setExpectedReceipt(null);//收货预计
                            amzOrderV.setErpsku(StringUtils.isNotEmpty(scTicket.getGoodsSku())?scTicket.getGoodsSku():saleOrderVoTicketList.stream().filter(t->StringUtils.isNotEmpty(t.getErpsku())).map(SaleOrderVoTicket::getErpsku).distinct().collect(Collectors.joining(","))); //款号
                            amzOrderV.setAsin(StringUtils.isNotEmpty(scTicket.getAsin())?scTicket.getAsin():saleOrderVoTicketList.stream().filter(t->StringUtils.isNotEmpty(t.getAsin())).map(SaleOrderVoTicket::getAsin).distinct().collect(Collectors.joining(","))); //ASIN
                            amzOrderV.setBelongProject(saleOrderVoTicket.getBelongProject());//项目

                            //图片相关信息封装
                            String erpskus = saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getErpsku).collect(Collectors.joining(","));
                            this.ticketOrderProductPack(Arrays.asList(erpskus.split(",")), scTicket.getOrganizationId().intValue(), amzOrderV,scTicket.getGoodsSku());

                            amzOrderV.setPromoteOrders("N");
                            //异步保存及工单关联客户
                            if (null == customerId) {
                                scCustomerService.saveCustomerAndJoinTicket(saleOrderVoTicket, scTicket);
                            }
                            scTicket.setCustomerName(amzOrderV.getBuyName());//客户名称
                        }

                    }
                }

            } else if (ScTicketConstant.TICKET_TYPE_ORDER_CREATE_INVOICE.equals(scTicket.getTicketType())) {
                if (scTicket.getOrderId() != null) {
                    AmzVcPoOrderV amzVcPoOrderV = amzVcPoOrderVMapper.selectSaleOrderByOrderId(scTicket.getOrderId());
                    if (amzVcPoOrderV != null) {
                        amzOrderV.setOrderNo(amzVcPoOrderV.getOrderNo());
                        amzOrderV.setChannelCreated(amzVcPoOrderV.getPstChannelCreated());
                        amzOrderV.setBusinessTicketStatusStr(amzVcPoOrderV.getPurchaseOrderState());
                        amzOrderV.setChannel(amzVcPoOrderV.getSaleChannel());
                        amzOrderV.setShopName(amzVcPoOrderV.getShopName());
                        amzOrderV.setSellingPartyId(amzVcPoOrderV.getSellingPartyId());
                        amzOrderV.setShipWindow(amzVcPoOrderV.getShipWindow());
                    }
                }

            } else if (ScTicketConstant.TICKET_TYPE_ORDER_PACKAGE_APPEAL.equals(scTicket.getTicketType())) {//申诉处理

                if (scTicket.getOrderId() != null) {
                    //查询订单信息
                    List<SaleOrderVoTicket> saleOrderVoTicketList = saleOrdersMapper.selectOrderByOrderId(scTicket.getOrderId());
                    if (!CollectionUtils.isEmpty(saleOrderVoTicketList)) {
                        SaleOrderVoTicket saleOrderVoTicket = saleOrderVoTicketList.get(0);
                        amzOrderV.setOrderNo(saleOrderVoTicket.getOrderNo()); //订单号
                        amzOrderV.setSellerOrderNumber(saleOrderVoTicket.getPoNumber()); //买家订单号
                        amzOrderV.setReverseOrderId(scTicket.getReverseOrderId()); //逆向订单号
                        amzOrderV.setBuyName(saleOrderVoTicket.getCustomerName()); //买家姓名（使用收件人姓名）
                        amzOrderV.setBuyEmail(saleOrderVoTicket.getBuyEmail());//买家邮箱
                        amzOrderV.setEncryptEmail(saleOrderVoTicket.getBuyEmail());//加密邮箱
                        amzOrderV.setChannelCreated(saleOrderVoTicket.getPstChannelCreated()); //下单时间
                        amzOrderV.setLatestShipDate(saleOrderVoTicket.getLatestShipDate()); //发货时限
                        amzOrderV.setShipmentDate(saleOrderVoTicket.getShipmentDate()); //发货时间
                        amzOrderV.setExpectedReceipt(null);//收货预计
                        amzOrderV.setPromoteOrders("N");
                        amzOrderV.setShopName(saleOrderVoTicket.getTitle());//店铺名
                        amzOrderV.setChannel(saleOrderVoTicket.getChannel());//销售渠道
                        amzOrderV.setCountry(saleOrderVoTicket.getCountryCode()); //国家CODE
                        amzOrderV.setErpsku(StringUtils.isNotEmpty(scTicket.getGoodsSku()) ? scTicket.getGoodsSku() : saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getErpsku).distinct().collect(Collectors.joining(","))); //款号

                    }
                }


            } else if(ScTicketConstant.TICKET_TYPE_CLAIM.equals(scTicket.getTicketType())) {
                //查询订单信息
                List<SaleOrderVoTicket> saleOrderVoTicketList = saleOrdersMapper.selectOrderByOrderId(scTicket.getOrderId());
                if (!CollectionUtils.isEmpty(saleOrderVoTicketList)) {
                    SaleOrderVoTicket saleOrderVoTicket = saleOrderVoTicketList.get(0);
                    amzOrderV.setOrderNo(saleOrderVoTicket.getOrderNo()); //订单号
                    amzOrderV.setBuyName(saleOrderVoTicket.getCustomerName()); //买家姓名（使用收件人姓名）
                    amzOrderV.setBuyEmail(saleOrderVoTicket.getBuyEmail());//买家邮箱
                    amzOrderV.setEncryptEmail(saleOrderVoTicket.getBuyEmail());//加密邮箱
                    amzOrderV.setChannelCreated(saleOrderVoTicket.getPstChannelCreated()); //下单时间
                    amzOrderV.setLatestShipDate(saleOrderVoTicket.getLatestShipDate()); //发货时限
                    amzOrderV.setShipmentDate(saleOrderVoTicket.getShipmentDate()); //发货时间
                    amzOrderV.setExpectedReceipt(null);//收货预计
                    amzOrderV.setPromoteOrders("N");
                    amzOrderV.setShopName(saleOrderVoTicket.getTitle());//店铺名
                    amzOrderV.setChannel(saleOrderVoTicket.getChannel());//销售渠道
                    amzOrderV.setCountry(saleOrderVoTicket.getCountryCode()); //国家CODE
                }
            }else if (ScTicketConstant.TICKET_TYPE_REISSUE_SPLIT.equals(scTicket.getTicketType())) {
                if (!Objects.isNull(scTicket.getParentTicketId())) {
                    ScTicket scTicketByParentId = scTicketMapper.selectScTicketByTicketId(scTicket.getParentTicketId());
                    if (!Objects.isNull(scTicketByParentId)) {
                        scTicket.setParentTicketNumber(scTicketByParentId.getTicketNumber());
                    }
                }
                if (null != scTicket.getAmazonOrderId()) {
                    //查询订单信息
                    List<SaleOrderVoTicket> saleOrderVoTicketList = saleOrdersMapper.selectOrderByOrderId(scTicket.getOrderId());
                    //查询客户信息
                    ScCustomer scCustomer = scCustomerService.selectScCustomerById(scTicket.getCustomerId());
                    if (!CollectionUtils.isEmpty(saleOrderVoTicketList)) {
                        SaleOrderVoTicket saleOrderVoTicket = saleOrderVoTicketList.get(0);
                        amzOrderV.setOrderNo(saleOrderVoTicket.getOrderNo()); //订单号
                        amzOrderV.setSellerOrderNumber(saleOrderVoTicket.getPoNumber());
                        amzOrderV.setBuyName(scCustomer != null ? scCustomer.getRecipientName() : null); //买家姓名（使用收件人姓名）
                        amzOrderV.setBuyEmail(scCustomer != null ? scCustomer.getEmail() : null);//邮箱
                        amzOrderV.setChannelCreated(saleOrderVoTicket.getPstChannelCreated()); //下单时间
                        amzOrderV.setLatestShipDate(saleOrderVoTicket.getLatestShipDate()); //发货时限
                        amzOrderV.setShipmentDate(saleOrderVoTicket.getShipmentDate()); //发货时间
                        amzOrderV.setSellerSku(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getSellerSku).distinct().collect(Collectors.joining(","))); //SellerSku
                        amzOrderV.setQuantity(saleOrderVoTicketList.stream().mapToInt(SaleOrderVoTicket::getQuantity).sum()); //销量/数量
                        amzOrderV.setItemAmount(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getItemAmount).reduce(BigDecimal.ZERO, BigDecimal::add));//金额
                        amzOrderV.setShopName(saleOrderVoTicket.getTitle());//店铺名
                        amzOrderV.setChannel(saleOrderVoTicket.getChannel());//销售渠道
                        amzOrderV.setCountry(scCustomer != null ? scCustomer.getCountry() : null); //国家
                        amzOrderV.setOperationUserName(saleOrderVoTicket.getOperationUserName()); //运营
                        amzOrderV.setErpsku(StringUtils.isNotEmpty(scTicket.getGoodsSku())?scTicket.getGoodsSku():saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getErpsku).distinct().collect(Collectors.joining(","))); //款号
                        amzOrderV.setAsin(StringUtils.isNotEmpty(scTicket.getAsin())?scTicket.getAsin():saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getAsin).distinct().collect(Collectors.joining(","))); //ASIN

                        //商品信息封装
                        String erpskus = saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getErpsku).collect(Collectors.joining(","));
                        this.ticketOrderProductPack(Arrays.asList(erpskus.split(",")), scTicket.getOrganizationId().intValue(), amzOrderV,scTicket.getGoodsSku());
                        amzOrderV.setBelongProject(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getBelongProject).distinct().collect(Collectors.joining(",")));
                    }
                }
            } else if (ScTicketConstant.TICKET_TYPE_WFS_CLAIM.equals(scTicket.getTicketType())) {
                    //wfs索赔
                MarWfsClaimCase wfsClaimCase = marWfsClaimCaseService.lambdaQuery().eq(MarWfsClaimCase::getTicketId, scTicket.getId()).one();
                if (null != wfsClaimCase) {
                    Account account = accountService.getById(wfsClaimCase.getShopId());
                    if(null != wfsClaimCase.getOperationId()) {
                        try {
                            UserEntity user = userService.getById(wfsClaimCase.getOperationId());
                            amzOrderV.setOperationUserName(user.getName());//运营
                        } catch (Exception e) {
                            log.error("当前查询运营数据异常,当前入参数为:{}", wfsClaimCase.getOperationId());
                        }
                    }
                    if (null != account) {
                        amzOrderV.setShopName(account.getTitle());//店铺
                        amzOrderV.setChannel(account.getType()); //渠道
                        amzOrderV.setCountry(account.getCountryCode());//国家
                        amzOrderV.setShopId(account.getId());

                        if (StringUtils.isNotEmpty(wfsClaimCase.getSellerSku())) {
                            ProductChannels productChannels = productChannelsService.lambdaQuery()
                                    .eq(ProductChannels::getSellerSku, wfsClaimCase.getSellerSku())
                                    .eq(ProductChannels::getAccountId, account.getFlag()).one();
                            amzOrderV.setGoodsName(productChannels.getTitle());//商品名称
                            amzOrderV.setAsin(productChannels.getAsin());//asin
                            amzOrderV.setImageUrls(Collections.singletonList(productChannels.getImageUrl()));//图片
                        }
                    }
                    amzOrderV.setBusinessTicketStatus(wfsClaimCase.getClaimStatus());
                    amzOrderV.setCaseSysId(wfsClaimCase.getId());//case系统Id
                    amzOrderV.setCaseId(wfsClaimCase.getCaseId());//caseId
                    amzOrderV.setCaseType(wfsClaimCase.getCaseType());//case类型
                    amzOrderV.setCaseStatus(wfsClaimCase.getCaseStatus());//case状态
                    amzOrderV.setBusinessTicketStatus(wfsClaimCase.getClaimStatus()); //索赔状态
                    amzOrderV.setSellerSku(wfsClaimCase.getSellerSku());//sellerSku
                    amzOrderV.setActualFee(wfsClaimCase.getNetPayable()); //实际金额
                    amzOrderV.setPredictFee(wfsClaimCase.getExpectedFee()); //预测金额
                    amzOrderV.setOriginalFee(wfsClaimCase.getOriginalFee()); //原始金额
                    amzOrderV.setDifferFee(wfsClaimCase.getClaimFee());//差额
                    amzOrderV.setDiscountFee(wfsClaimCase.getDiscountFee());//折扣金额
                }
            } else if (StringUtil.isNotEmpty(scTicket.getAmazonOrderId())) { //其余工单订单信息
                LambdaQueryWrapper<SaleOrders> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SaleOrders::getOrderNo, scTicket.getAmazonOrderId());
            }
            scTicket.setOrder(amzOrderV);
            //设置仓库
            SaleOrders saleOrders = saleOrdersMapper.selectById(scTicket.getOrderId());
            if(saleOrders != null) {
                if(amzOrderV != null) {
                    amzOrderV.setOrgWarehouseCode(saleOrders.getOrgWarehouseCode());
                }
            }
        }

        try {
            CompletableFuture.allOf(f1, f2).get(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.info("查新父子工单信息异常：{}", e);
        }

        return scTicket;
    }


    /*根据来源ID及 来源document查询工单信息*/
    @Override
    public ScTicket selectScTicketBySource(Long sourceId, String sourceDocument) {
        return scTicketMapper.selectScTicketBySource(sourceId, sourceDocument);
    }


    /**
     * @param
     * @param sourceId
     * @param sourceDocument
     * @description:根据来源ID及来源Document查询工单
     * @author: Moore
     * @date: 2023/10/18 1:16
     * @return: 工单行信息，当前仅TIKTOK站内信会存在此多条情况
     **/
    @Override
    public List<ScTicket> selectScTicketBySourceList(Long sourceId, String sourceDocument) {
        return scTicketMapper.selectScTicketBySourceList(sourceId, sourceDocument);
    }

    @Override
    public List<ScTicket> selectScTicketBySourceList(List<Long> sourceIdList, String sourceDocument) {
        return scTicketMapper.selectScTicketBySourceListV2(sourceIdList, sourceDocument);
    }


    /**
     * 定时任务自动生成工单及创建子工单调用
     *
     * @param scTicket 工单管理
     * @return 结果
     */
    @Transactional
    @Override
    public int insertScTicket(ScTicket scTicket) {
        //若子工单，判断母工单是否保存工单处理信息
        if (null != scTicket.getParentTicketId() &&
                !ScTicketConstant.TICKET_TYPE_ORDER_SHIPMENT.equals(scTicket.getTicketType())
                && !ScTicketConstant.TICKET_TYPE_VC_USPO_CREATE_SHIPMENT.equals(scTicket.getTicketType())
                && !ScTicketConstant.TICKET_TYPE_ASN.equals(scTicket.getTicketType())
                && !ScTicketConstant.TICKET_TYPE_ORDER_CREATE_INVOICE.equals(scTicket.getTicketType())
        ) {
            ScTicketHandle ticketHandle = scTicketHandleService.selectScTicketHandleByTicketId(scTicket.getParentTicketId());
            if (null == ticketHandle || StringUtil.isEmpty(ticketHandle.getMatter())) {
                throw new CustomException("提交子工单前请先保存母工单处理信息");
            }
        }
        scTicket.setTicketStatus(ScTicketConstant.TICKET_STATUS_NEW);//待处理
        if (scTicket.getCreatedBy() == null) {
            scTicket.settingDefaultCreate(); //设置默认创建人
        }
        if (scTicket.getUpdatedBy() == null || StringUtils.isEmpty(scTicket.getUpdatedName())) {
            scTicket.settingDefaultUpdate(); //设置默认跟新人
        }

        //创建子工单时，记录日志
        if (null != scTicket.getParentTicketId()&& !ScTicketConstant.TICKET_TYPE_REISSUE_ACCESSORIES.equals(scTicket.getTicketType())
                && !ScTicketConstant.TICKET_TYPE_REISSUE_GOODS.equals(scTicket.getTicketType())
                && !ScTicketConstant.TICKET_TYPE_ORDER_SHIPMENT.equals(scTicket.getTicketType())
                && !ScTicketConstant.TICKET_TYPE_REISSUE_SPLIT.equals(scTicket.getTicketType())
                &&!StringUtils.isEmpty(scTicket.getTicketType())) {
            //设置工单日志
            ScTicketLog ticketLog = new ScTicketLog();
            if (scTicket.getCreatedBy() == null) {
                ticketLog.settingDefaultCreate(); //设置默认创建人
                ticketLog.setOperatorBy(ticketLog.getCreatedName());
            }else{
                ticketLog.setOperatorBy(scTicket.getCreatedName()); //操作人
                ticketLog.setCreatedBy(scTicket.getCreatedBy());
                ticketLog.setCreatedAt(scTicket.getCreatedAt());
                ticketLog.setCreatedName(scTicket.getCreatedName());
            }
            ticketLog.setTicketId(scTicket.getParentTicketId()); //记录父工单日志
            ticketLog.setOperateType("Create");
            ticketLog.setOperateTime(DateUtils.getNowDate());//操作时间
            ticketLog.setOperateContent("提交"+ ScTicketTypeEnum.getName(scTicket.getTicketType())+"申请,生成子工单："+scTicket.getTicketNumber());
            scTicketLogService.insertScTicketLog(ticketLog);
        }


        //设置组织ID
        if (null == scTicket.getOrganizationId()) {
            if (null != scTicket.getShopId()) {
                Account shop = accountService.lambdaQuery().eq(Account::getId, scTicket.getShopId()).one();
                if (null != shop) {
                    scTicket.setOrganizationId(Long.valueOf(shop.getOrgId())); //组织ID (根据)
                }
            }
        }


        //分配工单处理人(临时)
        /*if("REISSUE_ACCESSORIES".equals(scTicket.getTicketType())||"REISSUE_GOODS".equals(scTicket.getTicketType())||"REISSUE_SHIPMENT".equals(scTicket.getTicketType())){ //暂时不按照店铺分配
            //根据配置设置工单处理人(临时)
            ConfTicketAssign confTicketAssign = confTicketAssignService.selectConfTicketAssignByTicketType(1000049L, scTicket.getTicketType());
            if (null != confTicketAssign) {
                scTicket.setHandlerBy(confTicketAssign.getHandleBy()); //处理人Name
                scTicket.setUserId(confTicketAssign.getHandleById());//用户ID
            }
        }else { //其余按照店铺分配
            ConfTicketAssign confTicketAssign = confTicketAssignService.selectConfTicketAssignByshopIdAndTicketType(scTicket.getShopId(), scTicket.getTicketType());
            if (null != confTicketAssign) {
                scTicket.setHandlerBy(confTicketAssign.getHandleBy()); //处理人Name
                scTicket.setUserId(confTicketAssign.getHandleById());//用户ID
            }
        }*/



        //工单优先级设置(及是否开启工单) TODO
    /*    if (!this.scTicketPriorityAndOpenStatusSet(scTicket.getOrganizationId(), scTicket)) {
            return 0;
        }*/

        //处理人设置 TODO

/*       Long handelById = confTicketAssignService.getTicketHandleNewVersion(scTicket);
       if (null != handelById) {
          SysUser sysUser = userMapper.selectUserById(handelById);
           if (null !=sysUser){
                scTicket.setHandlerBy(sysUser.getUserName());
                scTicket.setDeptId(sysUser.getDeptId());
                scTicket.setUserId(sysUser.getUserId());
            }
        }*/


        //设置SKu TODO
//        if (StringUtil.isNotEmpty(scTicket.getAmazonOrderId())) {
//            AmzOrderV amzOrderV = amzOrderVMapper.selectAmzOrderByAmazonOrderId(scTicket.getOrganizationId(), scTicket.getAmazonOrderId());
//            scTicket.setGoodsSku(null != amzOrderV ? amzOrderV.getGoodsSku() : null);
//        }


        //工单优先级设置，处理人设置，设置SKu
        //工单优先级设置(及是否开启工单)
        try {

            String priority = scTicket.getPriority();
            boolean priorityFlag = StringUtils.isNotEmpty(priority);
            if (!this.scTicketPriorityAndOpenStatusSet(scTicket.getOrganizationId(), scTicket)) {
                return 0;
            }
            if (priorityFlag) {
                scTicket.setPriority(priority);
            }
            //处理人设置
            Long handelById = scTicket.getUserId() != null ? scTicket.getUserId() : confTicketAssignService.getTicketHandleNewVersion(scTicket);
            if (null != handelById) {
                UserEntity sysUser = userService.getById(handelById.intValue());
                if (null != sysUser) {
                    scTicket.setHandlerBy(sysUser.getName());
                    scTicket.setDeptId(StringUtils.isNotEmpty(sysUser.getDeptId()) ? Long.valueOf(sysUser.getDeptId()) : null);
                    scTicket.setUserId(sysUser.getId().longValue());
                }
            }

            //设置SKu
//            if (scTicket.getOrderId() != null && scTicket.getOrganizationId() != null) {
//                scTicket.setGoodsSku(getGoodsSkuByOrgIdAndOrderId(scTicket.getOrderId(), scTicket.getOrganizationId().intValue()));
//            }
        } catch (Exception e) {
            log.error("工单优先级设置，处理人设置，设置Sku错误,{}",e.getMessage());
        }
        //默认操作状态
        if (StringUtil.isEmpty(scTicket.getOperateStatus())) {
            scTicket.setOperateStatus(ScTicketConstant.TICKET_OPERATE_PROCESS);
        }
        scTicket.setPstCreatedAt(DateUtil.UtcToPacificDate(scTicket.getCreatedAt()));
        Integer count = scTicketMapper.insertScTicket(scTicket);

        //子工单保存父工单处理信息，便于查询统计
        if (null != scTicket.getParentTicketId()) {
            scTicketHandleService.saveSubTicketHandle(scTicket.getId());
        }

        //设置工单日志
        ScTicketLog ticketLog = new ScTicketLog();
        if (scTicket.getCreatedBy() == null) {
            ticketLog.settingDefaultCreate(); //设置默认创建人
            ticketLog.setOperatorBy(ticketLog.getCreatedName());
        }else{
            if (!"NR".equals(scTicket.getTicketType())) {
                ticketLog.setOperatorBy(scTicket.getCreatedName()); //操作人
                ticketLog.setCreatedBy(scTicket.getCreatedBy());
                ticketLog.setCreatedAt(scTicket.getCreatedAt());
                ticketLog.setCreatedName(scTicket.getCreatedName());
            } else {
                ticketLog.settingDefaultCreate(); //设置默认创建人
                ticketLog.setOperatorBy(ticketLog.getCreatedName());
            }
        }
        ticketLog.setTicketId(scTicket.getId());
        ticketLog.setOperateType("Create");
        ticketLog.setOperateTime(DateUtils.getNowDate());//操作时间
        ticketLog.setOperateContent("生成工单");
        scTicketLogService.insertScTicketLog(ticketLog);
        //todo 生成工单处理表
        try {
            ScTicketHandle scTicketHandle = new ScTicketHandle();
            scTicketHandle.setTicketId(scTicket.getId());
            scTicketHandle.setOrganizationId(scTicket.getOrganizationId());
            if ("NR".equals(scTicket.getTicketType())) { //NR默认赋值为差评
                scTicketHandle.setMatter("NR");
                scTicketHandleMapper.insertScTicketHandle(scTicketHandle);
            } else if ("ORDER_PACKAGE_APPEAL".equals(scTicket.getTicketType())) { //申诉处理
                scTicketHandle.setMatter("60");
                scTicketHandleMapper.insertScTicketHandle(scTicketHandle);
            } else if(ScTicketConstant.TICKET_TYPE_REFUND_REQUEST.equals(scTicket.getTicketType())) { //退款请求
                if (StringUtils.isEmpty(scTicket.getReturnReason())) {
                    log.error("工单id:{}对应的退款信息退款原因为空", scTicket.getId());
                } else {
                    List<OrderRefundRequestInfoConfiguration> orderRefundRequestInfoConfigurationList = orderRefundRequestInfoConfigurationMapper.getRefundRequestInfoConfigurationPage(scTicket.getOrganizationId().intValue(), scTicket.getReturnReason());
                    if (CollectionUtil.isNotEmpty(orderRefundRequestInfoConfigurationList)) {
                        OrderRefundRequestInfoConfiguration orderRefundRequestInfoConfiguration = orderRefundRequestInfoConfigurationList.get(0);
                        if (StringUtils.isNotEmpty(orderRefundRequestInfoConfiguration.getReturnReasonCategory())) {
                            ConfTicketProblem confTicketProblem = confTicketProblemMapper.selectConfTicketProblemById(Long.valueOf(orderRefundRequestInfoConfiguration.getReturnReasonCategory()));
                            if (confTicketProblem != null) {
                                scTicketHandle.setProblemType1(confTicketProblem.getParentId().toString());
                                scTicketHandle.setProblemType2(confTicketProblem.getProblemId().toString());
                            }
                        }
                    }
                }
                scTicketHandle.setMatter("80");//退款请求
                scTicketHandleMapper.insertScTicketHandle(scTicketHandle);
            }
        } catch (Exception e) {
            log.error("创建工单创建工单handler失败{}", e.getMessage());
        }
        if (ScTicketConstant.TICKET_TYPE_ORDER_CANCEL.equals(scTicket.getTicketType())) {

            saleOrderCancelService.handleParentSku(scTicket);
        }
        return count;
    }


    //
//    /**
//     * 修改工单管理
//     *
//     * @param scTicket 工单管理
//     * @return 结果
//     */
//    @Transactional
//    @Override
//    public int updateScTicket(ScTicket scTicket) {
//        scTicket.setUpdateTime(DateUtils.getNowDate());
//        return scTicketMapper.updateScTicket(scTicket);
//    }
//
    @Transactional
    @Override
    public int updateScTicketHandler(ScTicket scTicket) {

        int count = scTicketMapper.updateScTicketHandler(scTicket);

        //添加日志
        ScTicketLog ticketLog = new ScTicketLog();
        ticketLog.setTicketId(scTicket.getId());
        ticketLog.setOperateType("Assign");
        ticketLog.setOperateTime(new Date());
        ticketLog.setOperateContent("分配工单给:" + scTicket.getHandlerBy());
        try {
            if (StringUtil.isNotEmpty(scTicket.getHandlerBy())) {
                ticketLog.setOperatorBy(scTicket.getHandlerBy());
            }
            scTicketLogService.insertScTicketLog(ticketLog);
        } catch (CustomException e) {
            logger.error("修改工单处理人时获取用户账户异常", e);
        }
        return count;
    }

    @Override
    @Transactional
    public void updateScTicketHandlerBy(ConfTicketAssign confTicketAssign, ScTicket scTicket) {
        scTicket.setHandlerBy(confTicketAssign.getHandleBy());
        scTicket.setUserId(confTicketAssign.getHandleById());
////        scTicket.setDeptId(); //  TODO 未设置部门信息
//        List<Long> deptIdList = scTicketMapper.selectDeptIdByUserId(scTicket.getUserId());
//        Long deptId = deptIdList.get(0);
//        if (!Objects.isNull(deptId)) {
//            scTicket.setDeptId(Long.valueOf(deptId));
//        }
        scTicketMapper.updateScTicketHandler(scTicket);

        //添加日志
        ScTicketLog ticketLog = new ScTicketLog();
        ticketLog.settingDefaultCreate();
        ticketLog.setTicketId(scTicket.getId());
        ticketLog.setOperateType("Assign");
        ticketLog.setOperateTime(DateUtils.getNowDate());
        ticketLog.setOperateContent("分配工单给:" + confTicketAssign.getHandleBy());
        ticketLog.setOperatorBy(ticketLog.getCreatedName());
        try {
            scTicketLogService.insertScTicketLog(ticketLog);
        } catch (CustomException e) {
            logger.error("修改工单处理人时获取用户账户异常", e);
        }
    }

    @Transactional
    @Override
    public int updateReviewTicketOrder(ScOrderMatchingTicket scOrderMatchingTicket) {
        Long sourceId = scOrderMatchingTicket.getSourceId();
        String amazonOrderId = scOrderMatchingTicket.getAmazonOrderId();
        Long shopId = scOrderMatchingTicket.getShopId();
        String matchType = scOrderMatchingTicket.getMatchType();
        String matchDocument = scOrderMatchingTicket.getMatchDocument();
        Long orderId = scOrderMatchingTicket.getOrderId();
        String asin = scOrderMatchingTicket.getAsin();
        String parentSku = scOrderMatchingTicket.getParentSku();
        String sku = scOrderMatchingTicket.getSku();
        int count = 0;
        //工单类型
        ScTicket scTicket = null;
        if ("REVIEW".equals(matchDocument)) {
            //Review工单
            scTicket = scTicketMapper.selectScTicketBySource(sourceId, ScTicketConstant.XSM_REVIEW_COMMON);
        } else if("FEEDBACK".equals(matchDocument)){
            //feedback工单
            scTicket = scTicketMapper.selectScTicketBySource(sourceId, ScTicketConstant.TICKET_SOURCE_DOCUMENT_FEEDBACK);
        } else if("AMZ_QA".equals(matchDocument)) {
            //Q&A工单
            scTicket = scTicketMapper.selectScTicketById(sourceId);
        } else if ("OFFSITE_LETTER".equals(matchDocument)) {
            //其他站外信
            scTicket = scTicketMapper.selectScTicketBySource(sourceId, ScTicketConstant.TICKET_SOURCE_DOCUMENT_OFFSITE_LETTER);
        }
        if (null != scTicket) {
            //设置亚马逊订单ID
            scTicket.setAmazonOrderId(amazonOrderId);
            scTicket.setOrderId(orderId);

            if ("AUTO".equals(matchType)) {
                AmzOrderV amzOrderV = amzOrderVMapper.selectAmzOrderByAmazonOrderId(orderId);
                scTicket.setShopId(amzOrderV.getShopId());
                if(amzOrderV.getCustomerId() != null) {
                    scTicket.setCustomerId(amzOrderV.getCustomerId());
                }else {// 维护
                    //查询订单信息
                    List<SaleOrderVoTicket> saleOrderVoTicketList = saleOrdersMapper.selectOrderByOrderId(orderId);
                    SaleOrderVoTicket saleOrderVoTicket = saleOrderVoTicketList.get(0);
                    scCustomerService.saveCustomerAndJoinTicket(saleOrderVoTicket, scTicket);
                }
                scTicket.setGoodsSku(amzOrderV.getGoodsSku());
                scTicket.setOrderId(orderId);
                scTicket.setMatchOrderType(ScTicketConstant.MATCH_TYPE_AUTO);//系统订单
                scTicket.setAsin(asin);
                scTicket.setSellerSku(amzOrderV.getSellerSku());
            } else {
                ScManualOrder manualOrder = manualOrderMapper.selectScManualOrderByNum(amazonOrderId);
                scTicket.setShopId(shopId);
                if (null != manualOrder) {
                    scTicket.setCustomerId(manualOrder.getCustomerId());
                    scTicket.setGoodsSku(manualOrder.getGoodsSku());
                    scTicket.setSellerSku(manualOrder.getSellerSku());
                }
                scTicket.setMatchOrderType(ScTicketConstant.MATCH_TYPE_MANUAL);//手动订单
                scTicket.setAsin(asin);
            }

/*            if (StringUtil.isEmpty(scTicket.getHandlerBy())) {
                String handlerBy = confTicketAssignService.getTicketHandle(scTicket, "PROCESS");
                scTicket.setHandlerBy(handlerBy);
                if (StringUtil.isNotEmpty(handlerBy)) {
//                    SysUser user = userMapper.selectUserByUserName(handlerBy);
//                    scTicket.setUserId(null != user ? user.getUserId() : null);
//                    scTicket.setDeptId(null != user ? user.getDeptId() : null);
                }
            }*/
            //新版处理人
            if (StringUtil.isEmpty(scTicket.getHandlerBy())) {
                Long handelById = confTicketAssignService.getTicketHandleNewVersion(scTicket);
                if (null != handelById) {
                    UserEntity sysUser = userService.getById(handelById.intValue());
                    if (null !=sysUser){
                        scTicket.setHandlerBy(sysUser.getName());
                        scTicket.setDeptId(StringUtils.isNotEmpty(sysUser.getDeptId()) ? Long.valueOf(sysUser.getDeptId()) : null);
                        scTicket.setUserId(sysUser.getId().longValue());
                    }
                }
            }

            if ("AUTO".equals(matchType)) {
                scTicket.setParentSku(parentSku);
                scTicket.setGoodsSku(sku);
            }

            count = scTicketMapper.updateScTicketOrder(scTicket);

            //添加日志
            ScTicketLog ticketLog = new ScTicketLog();
            ticketLog.setTicketId(scTicket.getId());
            ticketLog.setOperateType("Match");
            ticketLog.setOperateTime(new Date());
            ticketLog.setOperateContent("匹配订单");
            scTicketLogService.insertScTicketLog(ticketLog);
        }
        return count;
    }

    @Transactional
    @Override
    public int updateScTicketStatus(ScTicket scTicket) {
        //添加日志
        if (!ScTicketConstant.TICKET_STATUS_CLOSED.equals(scTicket.getTicketStatus())) {
            ScTicketLog ticketLog = new ScTicketLog();
            ticketLog.setTicketId(scTicket.getId());
            ticketLog.setOperateType("Update");
            ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
            ticketLog.setOperateTime(DateUtils.getNowDate());
            ticketLog.setOperateContent("修改工单状态为:" + scTicket.getTicketStatus());
            if (scTicket.getUpdatedBy() == null) {
                ticketLog.settingDefaultCreate(); //设置更新数据
            }else{
                ticketLog.setCreatedName(scTicket.getUpdatedName());
                ticketLog.setCreatedBy(scTicket.getUpdatedBy());
            }
            scTicketLogService.insertScTicketLog(ticketLog);
        }
        scTicket.setHandleTime(DateUtils.getNowDate()); //处理时间
        if (scTicket.getUpdatedBy() == null) {
            scTicket.settingDefaultUpdate(); //设置更新数据
        }
        return scTicketMapper.updateScTicketStatus(scTicket);
    }


    /*保存工单附件信息*/
    @Override
    public int saveTicketFilet(ScTicket scTicket, Integer contextId, UserEntity authUserEntity) {
        Long ticketId = scTicket.getId();
        if (ticketId == null) {
            throw new CustomException("工单ID获取失败");
        }
        Integer updateFlag = 0;
        if (EmptyUtil.isNotEmpty(scTicket.getMeta())) {
            //存储 meta
            ScTicket scTicketUpdate = new ScTicket();
            scTicketUpdate.settingDefaultValue();
            scTicketUpdate.setId(ticketId);
            scTicketUpdate.setMeta(scTicket.getMeta());
            updateFlag = scTicketMapper.updateScTicket(scTicketUpdate); //更新Meta
            ContextAttachMeta metaData = JacksonUtils.jsonToBean(scTicket.getMeta(), ContextAttachMeta.class);
            List<Long> attachIds = new ArrayList<>();
            if (metaData != null) {
                List<AttachItemRow> items = metaData.getItems();

                items.stream().forEach(l -> {
                    attachIds.add(l.getAttachId().longValue());
                });
                if (EmptyUtil.isNotEmpty(attachIds)) {
                    ucAttachmentService.batchUpdateSrcLabelByItemIds(authUserEntity, attachIds, scTicket.getId().toString());
                }
            }
        }
        return updateFlag;
    }


    @Override
    public int updateScTicketSource(ScTicket scTicket) {
        return scTicketMapper.updateScTicketSource(scTicket);
    }

    @Override
    public int updateScTicketOperateStatus(ScTicket scTicket) {
        return scTicketMapper.updateScTicketOperateStatus(scTicket);
    }

    @Override
    public int insertBatch(List<ScTicket> scTickets) {
        return scTicketMapper.insertBatch(scTickets);
    }


    /**
     * 工单创建
     */
    @Override
    public void insertScTickets(List<? extends SyncEmailInfo> emailInfos, List<ScTicket> scTickets) {
        // TODO 插入退货工单
        Map<Long, List<ScTicket>> ticketGroup = scTickets.stream()
                .filter(item -> !StringUtils.isEmpty(item.getParentTicketId() + ""))
                .collect(Collectors.groupingBy(ScTicket::getParentTicketId));
        List<ScTicket> matterScticket = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(ticketGroup)) {
            List<ScTicketHandle> ticketHandles = scTicketHandleService.selectScTicketHandleByTicketIds(ticketGroup.keySet());
            if (CollectionUtil.isNotEmpty(ticketHandles)) {
                List<ScTicketHandle> matterFilterList = ticketHandles.stream().filter(item -> !StringUtils.isEmpty(item.getMatter())).collect(Collectors.toList());
                for (ScTicketHandle handle : matterFilterList) {
                    if (ticketGroup.containsKey(handle.getTicketId())) {
                        matterScticket.addAll(ticketGroup.get(handle.getTicketId()));
                    } else {
                        // TODO 未找到母工单信息
//                        if (CollectionUtil.isEmpty(matterFilterList) || matterFilterList.size() < ticketHandles.size()) {
//                            throw new CustomException("提交子工单前请先保存母工单处理信息");
//                        }
                    }
                }
                // 根据店铺信息分组
                Map<Long, List<ScTicket>> ticketShopGroup = matterScticket.stream()
                        .filter(item -> !StringUtils.isEmpty(item.getShopId() + ""))
                        .collect(Collectors.groupingBy(ScTicket::getShopId));
                // 设置组织信息
                if (CollectionUtil.isNotEmpty(ticketShopGroup)) {
                    Set<Long> shopIds = ticketShopGroup.keySet();
                    List<Account> accounts = accountService.listByIds(shopIds);
                    for (Account shop : accounts) {
                        List<ScTicket> tickets = ticketGroup.get(shop.getId());
                        tickets.forEach(item -> {
                            item.setOrganizationId(shop.getOrgId().longValue());
                            // TODO 店铺不存在部门信息
//                            item.setDeptId(shop.getDeptId());
                        });
                    }
                }
            }
        }

        //设置工单状态
        scTickets.forEach(item -> {
            item.setTicketStatus(ScTicketConstant.TICKET_STATUS_NEW);
        });

        // 设置处理人
//         confTicketAssignService.getTicketHandlers(scTickets, "PROCESS");

        //新版设置处理人
        confTicketAssignService.getTicketHandlersNewVersion(scTickets);
    }

    //
    @Override
    public List<ScTicket> selectScTicketBySources(Collection<Long> sourceIds, String source) {
        return scTicketMapper.selectScTicketBySources(sourceIds, source);
    }


    /**
     * 工单优先级配置（并判断开启状态）
     * true  可以生成工单
     *
     * @param organizationId 组织ID
     * @param scTicket       工单信息
     */
    @Override
    public Boolean scTicketPriorityAndOpenStatusSet(Long organizationId, ScTicket scTicket) {
        try {
            if (null != organizationId && !StringUtils.isEmpty(scTicket.getTicketType())) {
                //根据组织ID和工单类型获取 对应优先级
                List<ScTicketPriority> scTicketPriorities = scTicketPriorityMapper.selectScTicketPriority(organizationId, scTicket.getTicketType());
                //亚马逊邮件、站外信  不判断开启值判断发件邮箱
                if (ScTicketConstant.TICKET_TYPE_AMZ_EMAIL.equals(scTicket.getTicketType()) || ScTicketConstant.TICKET_TYPE_EMAIL.equals(scTicket.getTicketType())) {
                    if (!CollectionUtils.isEmpty(scTicketPriorities) && !StringUtils.isEmpty(scTicketPriorities.get(0).getPriority())) {
                        scTicket.setPriority(scTicketPriorities.get(0).getPriority()); //优先级设置
                    }
                    return this.ticketFilterEmail(scTicket); //过滤邮箱
                } else if (ScTicketConstant.TICKET_TYPE_INBOX.equals(scTicket.getTicketType())) { //其他邮件 判断发件邮箱及开启状态
                    if (!CollectionUtils.isEmpty(scTicketPriorities)) { //判断是否开启配置
                        if (!StringUtils.isEmpty(scTicketPriorities.get(0).getPriority())) {
                            scTicket.setPriority(scTicketPriorities.get(0).getPriority()); //优先级设置
                        }
                        if (StringUtils.isEmpty(scTicketPriorities.get(0).getOpenStatus())) { //开启为空 视为开启
                            return this.ticketFilterEmail(scTicket);
                        }
                        if ("Y".equals(scTicketPriorities.get(0).getOpenStatus())) { // 判断开启状态
                            return this.ticketFilterEmail(scTicket); //开启后过滤
                        } else {
                            return false;
                        }
                    } else { //开启配置无数据，直接进行邮件过滤
                        return this.ticketFilterEmail(scTicket);
                    }
                } else if (ScTicketConstant.TICKET_TYPE_PR.equals(scTicket.getTicketType()) || ScTicketConstant.TICKET_TYPE_PF.equals(scTicket.getTicketType())) { //PR //PF 判断开启
                    if (!CollectionUtils.isEmpty(scTicketPriorities)) {
                        if (!StringUtils.isEmpty(scTicketPriorities.get(0).getPriority())) {
                            scTicket.setPriority(scTicketPriorities.get(0).getPriority()); //优先级设置
                        }
                        if (StringUtils.isEmpty(scTicketPriorities.get(0).getOpenStatus())) { //未设置值开启值
                            return true;
                        }
                        return "Y".equals(scTicketPriorities.get(0).getOpenStatus()); //工单开启状态（Y开启）
                    }
                } else {
                    if (!CollectionUtils.isEmpty(scTicketPriorities) && !StringUtils.isEmpty(scTicketPriorities.get(0).getPriority())) {
                        scTicket.setPriority(scTicketPriorities.get(0).getPriority()); //优先级设置
                    }
                    return true;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("获取优先级及过滤工单失败：{}", e);
            return true;
        }
    }


    /**
     * 工单邮件过滤操作（仅针对 亚马逊邮件，其他邮件，站外信、）
     * true 生成工单
     *
     * @param ticket 工单信息
     */
    public Boolean ticketFilterEmail(ScTicket ticket) {
        //查询过滤配置表
        Boolean ticketOpenFlag = true;

        //查询开启状态工单
        ConfTicketFilter confTicketFilter = new ConfTicketFilter();
        confTicketFilter.setOrganizationId(ticket.getOrganizationId());
        confTicketFilter.setFilterStatus("Y");
        List<ConfTicketFilter> confTicketFilters = confTicketFilterService.selectConfTicketFilterList(confTicketFilter);//获取启用状态过滤邮件
        if (CollectionUtils.isEmpty(confTicketFilters)) { //过滤条件为空,生成工单
            return ticketOpenFlag;
        }
        //无邮箱
        SyncEmailInfo emailInfo = isyncEmailInfoService.selectEmailInfoById(ticket.getSourceId());
        if (emailInfo == null) { //工单对应邮件信息查询
            return ticketOpenFlag;
        }

        for (ConfTicketFilter ticketFilter : confTicketFilters) {
            if (this.ticketFilterEmailRule(emailInfo, ticketFilter)) { //匹配到对应邮箱规则，不生成工单
                return false;
            }
        }
        return ticketOpenFlag;
    }

    /**
     * 工单邮件过滤规则操作
     * false 生成工单
     *
     * @param
     */
    public Boolean ticketFilterEmailRule(SyncEmailInfo emailInfo, ConfTicketFilter ticketFilter) {
        String filterAddress = ticketFilter.getFilterAddress(); //邮件地址
        String filterContent = ticketFilter.getFilterContent(); //过滤内容(标题)
        //实际发件标题
        String emailTitle = emailInfo.getEmailTitle();
        Boolean ruleEmailFlag = false;
        //规则1判断
        if (!StringUtils.isEmpty(filterAddress)) {
            ruleEmailFlag = filterAddress.equalsIgnoreCase(emailInfo.getFromAddress());

            //规则2判断
            if (ruleEmailFlag && !StringUtils.isEmpty(filterContent)) {
                String[] split = filterContent.split(",");
                boolean flag = true;

                for (String title : split) {
                    //标题不为空，并且存在 不包含匹配项
                    if (StringUtils.isEmpty(emailTitle)) {
                        return false;
                    } else {
                        if (!emailTitle.contains(title)) {
                            flag = false;
                        }
                    }
                }
                return flag;
            }
        } else {
            //判断规则2
            if (!StringUtils.isEmpty(filterContent)) {
                String[] split = filterContent.split(",");
                boolean flag = true;
                for (String title : split) {
                    if (StringUtils.isEmpty(emailTitle)) {
                        return false;
                    } else {
                        if (flag && !emailTitle.contains(title)) {
                            flag = false; //不完全匹配
                        }
                    }
                }
                return flag;
            } else {
                return ruleEmailFlag;
            }
        }
        return ruleEmailFlag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDistributeScTicketByIds(Long handlerId, Long[] ticketIds) {
        int count = 0;
        List<Long> deptIdList = scTicketMapper.selectDeptIdByUserId(handlerId);
        String name = scTicketMapper.selectUserName(handlerId.intValue());
        for (Long ticketId : ticketIds) {
            ScTicket scTicket = new ScTicket();
            scTicket.setId(ticketId);
            scTicket.setUserId(handlerId);
            scTicket.setDeptId(CollectionUtil.isNotEmpty(deptIdList) ? deptIdList.get(0) : null);
            scTicket.setHandlerBy(name);
            updateScTicketHandler(scTicket);
            count++;
        }
        return count;
    }


    @Override
    public String updateAStatusndSure(Long[] ticketIds) {
        String Status = "";
        if (ObjectUtil.isNotEmpty(ticketIds)) {
            List<ScTicket> list = scTicketMapper.selectScTicketListByIds(ticketIds);
            if (CollectionUtil.isEmpty(list)) {
                throw new ErpCommonException("查无此工单");
            }
            List<Long> idList = list.stream().filter(r -> ScTicketConstant.TICKET_STATUS_PROCESSING.equals(r.getTicketStatus())).map(ScTicket::getId).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(idList)) {
                throw new ErpCommonException("无处理中状态的工单");
            }
            ticketIds = idList.toArray(new Long[0]);
            for (Long ticketId : ticketIds) {
                ScTicketLog ticketLog = new ScTicketLog();
                ticketLog.settingDefaultCreate();
                ticketLog.setTicketId(ticketId);
                ticketLog.setOperateType("Finished");
                ticketLog.setOperatorBy(ticketLog.getCreatedName());
                ticketLog.setOperateTime(new Date());
                ticketLog.setOperateContent("完成工单:");
                scTicketLogService.insertScTicketLog(ticketLog);

                ScTicket scTicket = scTicketMapper.selectScTicketByTicketId(ticketId);
                if (!Objects.isNull(scTicket)) {
                    ScTicket ticket = new ScTicket();
                    ticket.setId(scTicket.getId());
                    ticket.setTicketStatus(ScTicketConstant.TICKET_STATUS_FINISH);
                    ticket.settingDefaultUpdate();
                    if (scTicketMapper.updateScTicketStatus(ticket) > 0) {
                        Status = ticket.getTicketStatus();
                    }
                }
            }
        }
        return Status;
    }

    @Override
    public String updateStatusAndShutdown(Long[] ticketIds) {
        String Status = "";
        if (ObjectUtil.isNotEmpty(ticketIds)) {
            List<ScTicket> list = scTicketMapper.selectScTicketListByIds(ticketIds);
            if (CollectionUtil.isEmpty(list)) {
                throw new ErpCommonException("查无此工单");
            }
            List<Long> idList = list.stream().filter(r -> ScTicketConstant.TICKET_STATUS_NEW.equals(r.getTicketStatus())||ScTicketConstant.TICKET_STATUS_PROCESSING.equals(r.getTicketStatus())).map(ScTicket::getId).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(idList)) {
                throw new ErpCommonException("无待处理或处理中状态的工单");
            }
            ticketIds = idList.toArray(new Long[0]);
            for (Long ticketId : ticketIds) {
                ScTicketLog ticketLog = new ScTicketLog();
                ticketLog.settingDefaultCreate();
                ticketLog.setTicketId(ticketId);
                ticketLog.setOperateType("Closed");
                ticketLog.setOperatorBy(ticketLog.getCreatedName());
                ticketLog.setOperateTime(new Date());
                ticketLog.setOperateContent("关闭工单:");
                scTicketLogService.insertScTicketLog(ticketLog);


                ScTicket scTicket = scTicketMapper.selectScTicketByTicketId(ticketId);
                if (!Objects.isNull(scTicket)) {
                    ScTicket ticket = new ScTicket();
                    ticket.setId(scTicket.getId());
                    ticket.setTicketStatus(ScTicketConstant.TICKET_STATUS_CLOSED);
                    ticket.settingDefaultUpdate();
                    if (scTicketMapper.updateScTicketStatus(ticket) > 0) {
                        Status = ticket.getTicketStatus();
                    }
                }
            }
        }
        return Status;
    }


    /**
     * @param
     * @param convShortId
     * @param ticketSourceDocumentStation
     * @description: 根据系统会话ID 获取工单信息
     * @author: Moore
     * @date: 2023/10/18 11:21
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.ScTicket>
     **/
    @Override
    public List<ScTicket> selectScTicketBySourceAndConvShortList(String convShortId, String ticketSourceDocumentStation) {
        return scTicketMapper.selectScTicketBySourceAndConvShortList(convShortId, ticketSourceDocumentStation);
    }


    /**
     * @param
     * @param scTicket
     * @description: 根据ID更新工单信息
     * @author: Moore
     * @date: 2023/10/23 9:55
     * @return: void
     **/
    @Override
    @Transactional
    public void updateScTickeByTicketId(ScTicket scTicket) {
        scTicketMapper.updateScTicket(scTicket);
    }


    /**
     * @param
     * @param ticket
     * @description:更新工单业务类型
     * @author: Moore
     * @date: 2025/5/7 17:39
     * @return: void
     **/
    @Override
    @Transactional
    public void updateScTicketBusinessType(ScTicket ticket) {
        scTicketMapper.updateScTicketBusinessType(ticket);
    }

    @Override
    public ScTicket createdAndInsertScTicket(String channelOrderNo, Long sourceId, String sourceOfWork, String sourceFrom, Long ticketId, String refundType, OrderRefundRequest orderRefundRequest, String flow, String shopId, Long orderId, String sourceChannel, String ticketNumber, Date createdAt, String businessType) {
//        Integer customerId = saleOrdersMapper.getCustomerIdByOrderId(orderId);

        OrderRefundRequest refundRequest = new OrderRefundRequest();
        Account account = new Account();
        Long busSourceId = 0L;
        if (OrderRefundEnum.INTERNAL.getCode().equals(refundType)) {
            // 这里逻辑可以优化,写的太急了有不必要的查询
            refundRequest = orderRefundRequestService.getById(sourceId);
            OrderRefund orderRefund = orderRefundService.getOne(new LambdaQueryWrapper<OrderRefund>().eq(OrderRefund::getReverseOrderId, refundRequest.getReverseOrderId()));
            if (ObjectUtil.isNull(orderRefund)) {
                busSourceId = refundRequest.getId();
            } else {
                busSourceId = orderRefund.getId();
            }


            if (ObjectUtil.isNull(refundRequest)) {
                account = accountService.getById(orderRefundRequest.getShopId());
            } else {
                account = accountService.getById(refundRequest.getShopId());
            }
        } else if (OrderRefundEnum.EXTERNAL.getCode().equals(refundType)) {
            if (StringUtils.isNotEmpty(shopId)) {
                account = accountService.getById(shopId);
            }
        } else if (OrderRefundEnum.SALES_RETURN_TO_INTERNAL.getCode().equals(refundType)) {
            if (StringUtils.isNotEmpty(shopId)) {
                account = accountService.getById(shopId);
            }
        }
        if ("webhookFirst".equals(flow)) {
            account = accountService.getAccountByShopId(shopId);
        }
        String customerName = saleOrdersMapper.getCustomerNameByOrderId(String.valueOf(orderId));
        ScTicket scTicketCrete = new ScTicket();
        scTicketCrete.setOrderId(orderId);
        scTicketCrete.setTicketType(sourceOfWork);  //工单类型 站外
        scTicketCrete.setTicketName("客户申请订单号:" + channelOrderNo + "退款"); //工单名称
        scTicketCrete.setCustomerName(customerName);
        scTicketCrete.setRemark("店铺名称:" + account.getTitle() + " 订单号:" + channelOrderNo + " 买家名称:" + customerName); //描述

        scTicketCrete.setTicketSource(sourceChannel);// 工单来源(订单API)
        scTicketCrete.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH);
        if (OrderRefundEnum.INTERNAL.getCode().equals(refundType)) {
            if (ObjectUtil.isNull(orderRefundRequest)) {
                scTicketCrete.setReverseOrderId(String.valueOf(refundRequest.getReverseOrderId()));//优先级（高）
            } else {
                scTicketCrete.setReverseOrderId(String.valueOf(orderRefundRequest.getReverseOrderId()));//优先级（高）
            }
        }

        scTicketCrete.setAmazonOrderId(channelOrderNo);
        // 站内,子工单的 sourceid 要用 refundid
        if (OrderRefundEnum.INTERNAL.getCode().equals(refundType)) {
            if ("webhookFirst".equals(flow)) {
                scTicketCrete.setSourceId(busSourceId);
                scTicketCrete.setReverseOrderId(String.valueOf(orderRefundRequest.getReverseOrderId()));
                scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(busSourceId.toString(), "l", 5, "0")+ RandomUtil.randomNumbers(4));
            } else if ("tiktok".equals(flow)) {
                scTicketCrete.setReverseOrderId(String.valueOf(refundRequest.getReverseOrderId()));
                scTicketCrete.setOperateStatus(ScTicketConstant.TICKET_OPERATE_APPROVE);
            }

        }

        if (ObjectUtil.isNotNull(ticketId) && OrderRefundEnum.INTERNAL.getCode().equals(refundType)) {
            ScTicket scTicket = scTicketMapper.selectScTicketByTicketId(ticketId);
            if (ObjectUtil.isNotNull(scTicket) && !"0".equals(String.valueOf(busSourceId))) {
                scTicketCrete.setSourceId(busSourceId);
                scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(busSourceId.toString(), "l", 5, "0")+ RandomUtil.randomNumbers(4));
            } else {
                scTicketCrete.setSourceId(sourceId);
                scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(sourceId.toString(), "l", 5, "0")+ RandomUtil.randomNumbers(4));
            }
        } else if (ObjectUtil.isNotNull(ticketId) && OrderRefundEnum.EXTERNAL.getCode().equals(refundType)) {
            scTicketCrete.setSourceId(sourceId);//来源ID 退款表主键
            scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(sourceId.toString(), "l", 5, "0")+ RandomUtil.randomNumbers(4));
        } else if (ObjectUtil.isNotNull(ticketId) && OrderRefundEnum.SALES_RETURN_TO_INTERNAL.getCode().equals(refundType)) {
            scTicketCrete.setSourceId(sourceId);//来源ID 退款表主键
            scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(sourceId.toString(), "l", 5, "0")+ RandomUtil.randomNumbers(4));
        }


        try {
            if (OrderRefundEnum.INTERNAL.getCode().equals(refundType)) {
                scTicketCrete.setBusinessApplyTime(MathTimeUtils.longToDate(refundRequest.getReverseRequestTime()));
            } else {
                scTicketCrete.setBusinessApplyTime(MathTimeUtils.getNow());
            }
        } catch (Exception e) {
            throw new CommonException("时间异常");
        }
        if (OrderRefundEnum.EXTERNAL.getCode().equals(refundType) || OrderRefundEnum.SALES_RETURN_TO_INTERNAL.getCode().equals(refundType)) {
            scTicketCrete.setOperateStatus(ScTicketConstant.TICKET_OPERATE_APPROVE);
        }
        scTicketCrete.setParentTicketId(ticketId);
        scTicketCrete.setSourceDocument(sourceFrom); //来源单据 退款表
        scTicketCrete.setShopId(Long.valueOf(account.getId()));  //店铺
        scTicketCrete.setOrganizationId(Long.valueOf(account.getOrgId()));//组织ID

//        临时单的填充 补完
        if(ObjectUtil.isNotNull(ticketNumber)){
            scTicketCrete.setTicketNumber(ticketNumber);
        }
        if(ObjectUtil.isNotNull(createdAt)){
            scTicketCrete.setCreatedAt(createdAt);
        }
        if(ObjectUtil.isNotEmpty(businessType)){
            scTicketCrete.setBusinessType(businessType);
        }
        return scTicketCrete;
    }


    /**
     * @param
     * @param ticketId
     * @description: 基础查询生成拦截单
     * @author: Moore
     * @date: 2023/10/27 10:23
     * @return: com.bizark.op.api.entity.op.ticket.ScTicket
     **/
    @Override
    public ScTicket selectScTicketByBaseTicketId(Long ticketId) {
        return scTicketMapper.selectScTicketById(ticketId);
    }

    @Override
    public Integer countList(String orderId, String ticketStatusFinish) {
        return scTicketMapper.countList(orderId, ticketStatusFinish);
    }

    @Override
    public Integer countByStatusAndOrderId(String orderId, String channel) {
        //写一个冒泡排序
        return scTicketMapper.countByStatusAndOrderId(orderId, channel);
    }

    @Override
    public List<ScTicket> selectScTicketByParentId(Long parentTicketId, Integer contextId) {
        List<ScTicket> scTicketList = scTicketMapper.selectScTicketByParentId(parentTicketId);
        return scTicketList;
    }

    @Override
    public ScTicket selectScTicketByParentId(String orderId, String ticketStatusFinish, Long ticketId, String
            ticketType) {

        return scTicketMapper.existsScTicketByParentId(orderId, ticketStatusFinish, ticketId, ticketType);
    }

    @Override
    public String getTicketBusinessStatus(Long ticketId, String orderId) {
        ScTicket ticket = getTicketByTicketId(ticketId);
        if (ScTicketConstant.TICKET_SOURCE_REFUND_REQUEST_INFO.equals(ticket.getSourceDocument())) {
            String returnStatus = orderRefundRequestInfoService.getById(ticket.getSourceId()).getReturnStatus();
            return StringUtil.isNotEmpty(returnStatus) ? (OrderRefundStatusEnum.getDesc(returnStatus) != null ? OrderRefundStatusEnum.getDesc(returnStatus).getDesc() : null) : null ;
        } else if (ScTicketConstant.TICKET_TYPE_INSIDE_REFUND.equals(ticket.getTicketType()) || ScTicketConstant.TICKET_TYPE_OUTSIDE_REFUND.equals(ticket.getTicketType())) {
            OrderRefund orderRefund = orderRefundService.getById(ticket.getSourceId());
            return orderRefund != null ? OrderRefundStateEnum.getDesc(orderRefund.getRefundStatus()).getDesc() : null;
        }
        OrderRefund refund = orderRefundService.getById(ticket.getSourceId());
        OrderRefund refundFinish = new OrderRefund();
        OrderRefundRequest request = orderRefundRequestService.getById(ticket.getSourceId());
        if (ObjectUtil.isNotNull(request) && ObjectUtil.isNotNull(request.getOrderRefundId())) {
            refundFinish = orderRefundService.getById(request.getOrderRefundId());
        }
//        OrderRefundRequest refundRequest = orderRefundRequestService.getOne(new LambdaQueryWrapper<OrderRefundRequest>().eq(OrderRefundRequest::getOrderId, orderId).eq(OrderRefundRequest::getTicketId, ticketId));

        List<OrderRefundRequest> refundRequest = orderRefundRequestService.list(new LambdaQueryWrapper<OrderRefundRequest>().
                eq(OrderRefundRequest::getOrderId, orderId).
                and(Wrapper -> Wrapper.ne(OrderRefundRequest::getTreatmentState, OrderRefundEnum.FINISHED.getCode()).
                        or().
                        ne(OrderRefundRequest::getTreatmentState, OrderRefundEnum.CLOSE.getCode()))
        );
        String status = "";
        // notes: 场景 1. 退款申请工单页面 2.站内退款工单页面 3.站外退款工单页面 4.退货后续补充...
        // 父工单的request对应的orderRefund
        if (ObjectUtil.isNotNull(refund)) {
            // 站外
            if (OrderRefundEnum.EXTERNAL.getCode().equals(refund.getRefundType())) {
                if (OrderRefundEnum.TO_BE_REFUNDED.getCode().equals(refund.getTreatmentState())) {
                    status = "退款中";
                }
                if (OrderRefundEnum.FINISHED.getCode().equals(refund.getTreatmentState())) {
                    status = "已退款";
                }
            }
            // 站内
            if (OrderRefundEnum.INTERNAL.getCode().equals(refund.getRefundType())) {
                if (OrderRefundEnum.TO_BE_REFUNDED.getCode().equals(refund.getTreatmentState()) ||
                        OrderRefundEnum.SUBMITTED.getCode().equals(refund.getTreatmentState())) {
                    status = "退款中";
                }
                if (OrderRefundEnum.FINISHED.getCode().equals(refund.getTreatmentState()) || ScTicketConstant.TICKET_STATUS_REFUNDED.equals(ticket.getTicketStatus())) {
                    status = "已退款";
                }
            }
        }

        if (ObjectUtil.isNotNull(refundFinish)) {
            // 站外
            if (OrderRefundEnum.EXTERNAL.getCode().equals(refundFinish.getRefundType())) {
                if (OrderRefundEnum.TO_BE_REFUNDED.getCode().equals(refundFinish.getTreatmentState())) {
                    status = "退款中";
                }
                if (OrderRefundEnum.FINISHED.getCode().equals(refundFinish.getTreatmentState())) {
                    status = "已退款";
                }
            }
            // 站内
            if (OrderRefundEnum.INTERNAL.getCode().equals(refundFinish.getRefundType())) {
                if (OrderRefundEnum.TO_BE_REFUNDED.getCode().equals(refundFinish.getTreatmentState())) {
                    status = "退款中";
                }
                if (OrderRefundEnum.FINISHED.getCode().equals(refundFinish.getTreatmentState()) || ScTicketConstant.TICKET_STATUS_REFUNDED.equals(ticket.getTicketStatus())) {
                    status = "已退款";
                }
            }
        }


        OrderRefundRequest req = new OrderRefundRequest();
        if (ObjectUtil.isNotNull(ticket.getReverseOrderId())) {
            String reverseOrderId = ticket.getReverseOrderId();
            req = orderRefundRequestService.getOne(new LambdaQueryWrapper<OrderRefundRequest>().eq(OrderRefundRequest::getReverseOrderId, reverseOrderId).eq(OrderRefundRequest::getTreatmentState, OrderRefundEnum.FINISHED.getCode()));
        }


        // 买家已申请退款 :1.不存在orederRefund 2.存在 orederId ticketId 并且此request状态是站内
        if (CollectionUtil.isNotEmpty(refundRequest) && ObjectUtil.isNotNull(refundRequest.get(0))) {
            if (OrderRefundEnum.INTERNAL.getCode().equals(refundRequest.get(0).getRefundType()) &&
                    !OrderRefundEnum.FINISHED.getCode().equals(ticket.getTicketStatus()) &&
                    !OrderRefundEnum.CLOSE.getCode().equals(ticket.getTicketStatus()) &&
                    !ScTicketConstant.TICKET_STATUS_REFUNDED.equals(ticket.getTicketStatus()) &&
                    ObjectUtil.isNull(req) &&
                    ScTicketConstant.TICKET_TYPE_REFUND_REQUEST.equals(ticket.getTicketType())) {
                status = "买家已申请退款";
            }
        }
        return status;
    }

    private ScTicket getTicketByTicketId(Long ticketId) {
        return scTicketMapper.getTicketByTicketId(ticketId);
    }

    /**
     * 根据补发订单来查询子工单状态
     *
     * @param id
     * @param sourceDocument
     * @return
     */
    @Override
    public ScTicket selectScTicketByTicketSource(Long id, String sourceDocument) {
        return scTicketMapper.selectScTicketByTicketSource(id, sourceDocument);
    }
//    工单状态,操作状态,工单日志类型,操做内容
    @Override
    public ScTicket getScTicketForReturn(Long ticketId, String refundType, String ticketStatus, String operateStatus, String refundStatus, String channel, String backReason) {

        ScTicket scTicket = selectScTicketById(ticketId);
//        ScTicket parentScTicket = selectScTicketById(scTicket.getParentTicketId());

        scTicket.setHandlerBy(scTicket.getCreatedName());
        scTicket.setUserId(scTicket.getCreatedBy().longValue());
        scTicket.setTicketStatus(ticketStatus);
        scTicket.setOperateStatus(operateStatus);

        //记录工单日志
        ScTicketLog ticketLog = new ScTicketLog();
        ticketLog.setTicketId(scTicket.getId());
        ticketLog.setOperateType(OrderRefundEnum.RETURN.getCode());
        ticketLog.setOperateTime(new Date());
        if (StringUtils.isEmpty(backReason)) {
            ticketLog.setOperateContent(refundStatus + channel + "申请工单");
        } else {
            ticketLog.setOperateContent(refundStatus + channel + "申请工单,退回原因:" + backReason);
        }

        scTicketLogService.insertScTicketLog(ticketLog);

        return scTicket;
    }

    @Override
    public Boolean updateScTicket(Long ticketId, String ticketStatus, String operateStatus, String businessTicketStatus) {
        ScTicket scTicket = selectScTicketById(ticketId);
        if (ObjectUtil.isNotNull(ticketStatus)) {
            scTicket.setTicketStatus(ticketStatus);
        }
        if (ObjectUtil.isNotNull(operateStatus)) {
            scTicket.setOperateStatus(operateStatus);
        }
        if (ObjectUtil.isNotNull(businessTicketStatus)) {
            scTicket.setBusinessTicketStatus(businessTicketStatus);
        }
        updateScTickeByTicketId(scTicket);
        return Boolean.TRUE;
    }

    @Override
    public ScTicket selectScticketByOrderNo(String orderNo, String reverseOrderId) {
        ScTicket scTicket =scTicketMapper.getOne(orderNo,reverseOrderId);
        return null;
    }

    @Override
    public void deleteByOrderNoAndReverseOrderId(String orderNo, String reverseOrderId) {
        scTicketMapper.deleteByOrderNoAndReverseOrderId(orderNo,reverseOrderId);
    }

    /**
     * @param
     * @param reverseOrderId 逆向订单号
     * @description: 根据逆向订单号查询工单
     * @author: Moore
     * @date: 2023/11/8 19:35
     * @return: com.bizark.op.api.entity.op.ticket.ScTicket
     **/
    @Override
    public ScTicket selectScTicketByReverseOrderId(String reverseOrderId) {
        return scTicketMapper.selectScTicketByReverseOrderId(reverseOrderId);
    }


    /**
     * @description: 设置工单状态为处理中
     * @author: Moore
     * @date: 2023/11/10 9:26
     * @param
     * @param ticketId
     * @return: void
    **/
    @Override
    public void setTikceStatusProcessing(Long ticketId) {
            ScTicket scTicketUpate = new ScTicket();
            scTicketUpate.setId(ticketId); //工单ID
            scTicketUpate.setTicketStatus(ScTicketConstant.TICKET_STATUS_PROCESSING); //处理中
            this.updateScTicketStatus(scTicketUpate);
    }


    /**
     * @param
     * @param reverseOrderId
     * @param ticketType
     * @description: 根据
     * @author: Moore
     * @date: 2023/12/4 9:43
     * @return: com.bizark.op.api.entity.op.ticket.ScTicket
     **/
    @Override
    public ScTicket selectScTicketByReverseOrderIdAndTicketType(String reverseOrderId, String ticketType) {
        return scTicketMapper.selectScTicketByReverseOrderIdAndTicketType(reverseOrderId, ticketType);
    }


    /**
     * 根据工单类型更新工单优先级
     *
     * @param scTicketPriority 状态信息
     */
    @Override
    @Transactional
    public void updateScTicketPriorityBatch(List<ScTicketPriority> scTicketPriority, Long contextId) {
        for (ScTicketPriority ticketPriority : scTicketPriority) {
            //更新工单优先级/组织ID、 工单类型、优先级
            scTicketMapper.updateScTicketPriorityStatus(contextId, ticketPriority.getDictValue(), ticketPriority.getPriority());
        }
    }


    /**
     * 根据工单类型更新工单状态 只关闭对应组织工单类型
     *
     * @param scTickets 状态信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateScTicketStatusList(List<ScTicket> scTickets, Long contextId) {
        for (ScTicket scTicket : scTickets) {
            //更新工单状态
            scTicketMapper.updateScTicketStatusByType(contextId, scTicket.getTicketType(), scTicket.getTicketStatus());
        }
    }


    /**
     * 根据订单号和组织id获取goodsSku
     * @param orderId
     * @param orgId
     * @return
     */
    @Override
    public String getGoodsSkuByOrgIdAndOrderId(Long orderId, Integer orgId) {
        if (orgId == null || orderId == null) {
            return null;
        }
        List<SaleOrderItems> list = orderItemsService.selectByHeadIdAndOrgId(orderId.intValue(), orgId);
        if (CollectionUtil.isNotEmpty(list)) {
            List<String> collect = list.stream().filter(r -> StringUtils.isNotEmpty(r.getErpSku())).map(SaleOrderItems::getErpSku).distinct().collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)) {
                return String.join(",", collect);
            }
        }
        return null;
    }


    /**
     * @param
     * @param scTicket
     * @description: 站内信工单数据封装
     * @author: Moore
     * @date: 2024/1/2 19:17
     * @return: void
     **/
    public void amzSitemsgTicketPack(ScTicket scTicket, BaseTicketOrderVO amzOrderV) {

        //TIKTOK会话，设置会话相关信息 设置会话基础信息
        if (null != scTicket.getSourceId() && "tiktok".equals(scTicket.getTicketSource())) {
            ScStationLetter scStationLetter = scStationLetterService.selectBaseScStationLetterById(scTicket.getSourceId());
            if (null != scStationLetter) {
                scTicket.setConvShortId(scStationLetter.getConvShortId()); //会话ID    发送会话使用
                scTicket.setOuterId(scStationLetter.getOuterId()); //会话用户outerId   展示拼接使用
                scTicket.setCustomerName(scStationLetter.getCustomerName());//客户名称  展示拼接使用

                scTicket.setCustomerOuterId(scStationLetter.getOuterId()); //通用侧边栏查询订单等信息使用
            }
        }
        //tiktik 站内 有订单号，即代表已匹配(接收时不设置订单号，有订单号即代表匹配成功)
        if (("tiktok".equals(scTicket.getTicketSource()) && null != scTicket.getAmazonOrderId()) ||
                ("amazon".equals(scTicket.getTicketSource()) && null != scTicket.getAmazonOrderId() && !StringUtils.isEmpty(scTicket.getGoodsSku()))  //amz 有订单号且goodssku存在即代表已匹配
           || ("walmart".equals(scTicket.getTicketSource()) && null != scTicket.getAmazonOrderId() && !StringUtils.isEmpty(scTicket.getGoodsSku()))
        ) {   //查询订单信息
            List<SaleOrderVoTicket> saleOrderVoTicketList = saleOrdersMapper.selectOrderByOrderId(scTicket.getOrderId());
            if (!CollectionUtils.isEmpty(saleOrderVoTicketList)) {
                SaleOrderVoTicket saleOrderVoTicket = saleOrderVoTicketList.get(0);
                amzOrderV.setOrderNo(saleOrderVoTicket.getOrderNo()); //订单号
                if("walmart".equals(scTicket.getTicketSource())) {
                    ScStationLetter scStationLetter = scStationLetterService.selectBaseScStationLetterById(scTicket.getSourceId());
                    if(scStationLetter != null) {
                        List<ScStationLetterMessage> scStationLetterMessageList = iScStationLetterMessageService.selectScStationLetterMessages(scStationLetter.getStationLetterId());
                        if(CollectionUtil.isNotEmpty(scStationLetterMessageList)) {
                            scStationLetterMessageList = scStationLetterMessageList.stream().filter(i -> "1".equals(i.getType())).collect(Collectors.toList());
                            if(CollectionUtil.isNotEmpty(scStationLetterMessageList)) {
                                Long infoId = scStationLetterMessageList.get(0).getEmailInfoId();
                                //todo 查询邮件 取from
                                SyncEmailInfo  syncEmailInfo = syncEmailInfoMapper.selectEmailInfoById(infoId);
                                if(syncEmailInfo != null) {
                                    amzOrderV.setEncryptEmail(syncEmailInfo.getFromAddress()); //加密邮箱
                                }
                            }
                        }
                    }

                } else {
                    amzOrderV.setEncryptEmail(saleOrderVoTicket.getBuyEmail()); //加密邮箱
                }
                amzOrderV.setBuyEmail(saleOrderVoTicket.getBuyEmail());//邮箱
                amzOrderV.setBuyName(saleOrderVoTicket.getBuyName()); //买家姓名（使用收件人姓名）
                amzOrderV.setChannelCreated(saleOrderVoTicket.getPstChannelCreated()); //下单时间
                amzOrderV.setLatestShipDate(saleOrderVoTicket.getLatestShipDate()); //发货时限
                amzOrderV.setShipmentDate(saleOrderVoTicket.getShipmentDate()); //发货时间
                amzOrderV.setShopName(saleOrderVoTicket.getTitle());//店铺名
                amzOrderV.setChannel(saleOrderVoTicket.getChannel());//销售渠道
                amzOrderV.setCountry(saleOrderVoTicket.getCountryCode()); //国家
                amzOrderV.setOperationUserName(saleOrderVoTicket.getOperationUserName()); //运营
                amzOrderV.setSellerOrderNumber(saleOrderVoTicket.getPoNumber()); //客户单号
                amzOrderV.setSellerSku(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getSellerSku).distinct().collect(Collectors.joining(","))); //SellerSku
                amzOrderV.setQuantity(saleOrderVoTicketList.stream().mapToInt(SaleOrderVoTicket::getQuantity).sum()); //销量/数量
                amzOrderV.setItemAmount(saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getItemAmount).reduce(BigDecimal.ZERO, BigDecimal::add));//金额
                if (StringUtils.isEmpty(scTicket.getGoodsSku())) {
                    amzOrderV.setErpsku(StrUtil.nullToDefault(saleOrderVoTicketList.stream().filter(item -> !StringUtils.isEmpty(item.getErpsku())).map(SaleOrderVoTicket::getErpsku).distinct().collect(Collectors.joining(",")), null)); //款号}
                } else {
                    amzOrderV.setErpsku(scTicket.getGoodsSku()); //款号}
                }
                amzOrderV.setAsin(StrUtil.nullToDefault(saleOrderVoTicketList.stream().filter(item -> !StringUtils.isEmpty(item.getAsin())).map(SaleOrderVoTicket::getAsin).distinct().collect(Collectors.joining(",")), null)); //ASIN
                //商品信息封装
                String erpskus = saleOrderVoTicketList.stream().map(SaleOrderVoTicket::getErpsku).collect(Collectors.joining(","));
                if (!StringUtils.isEmpty(erpskus)) {
                    this.ticketOrderProductPack(Arrays.asList(erpskus.split(",")), scTicket.getOrganizationId().intValue(), amzOrderV, scTicket.getGoodsSku());

                }
            }
        } else{
            //站内信匹配订单实体类
            ArrayList<TicketMatchOrderVO> resMatchOrderList = new ArrayList<>();

            if (AccountSaleChannelEnum.TIKTOK.getValue().equals(scTicket.getTicketSource())){
                //查询匹配订单信息
                if (scTicket.getOuterId() != null) {
                    List<TicketMatchOrderVO> saleOrderVoTicketList = saleOrdersMapper.selectOrderAndProductByCustomerOuterId(scTicket.getOuterId());
                    //每行为一个订单号项
                    for (TicketMatchOrderVO ticketMatchOrderVO : saleOrderVoTicketList) {
                        TicketMatchOrderVO ticketMatchOrderVOItem = new TicketMatchOrderVO();
                        ticketMatchOrderVOItem.setAsin(ticketMatchOrderVO.getAsin());
                        ticketMatchOrderVOItem.setSellerSku(ticketMatchOrderVO.getSellerSku());
                        ticketMatchOrderVOItem.setImageUrl(ticketMatchOrderVO.getImageUrl());
                        ticketMatchOrderVOItem.setErpSku(ticketMatchOrderVO.getErpSku());
                        ticketMatchOrderVOItem.setQuantity(ticketMatchOrderVO.getQuantity());
                        ticketMatchOrderVOItem.setItemAmount(ticketMatchOrderVO.getItemAmount());
                        ticketMatchOrderVO.setOrderMatchInfo(Arrays.asList(ticketMatchOrderVOItem));
                    }
                    resMatchOrderList.addAll(saleOrderVoTicketList);
                }
            }else  if (AccountSaleChannelEnum.SC.getValue().equals(scTicket.getTicketSource())&&scTicket.getSourceId() != null) { //亚马逊站内信 ，需匹配订单
                if (null != scTicket.getAmazonOrderId()){ //有订单，但未匹配sku，则为多行
                        //有订单，但是有多行，需用户手动选择具体订单列
                        List<TicketMatchOrderVO> saleOrderVoEmail = saleOrdersMapper.selectOrderItemByOrderId(scTicket.getOrderId());
                        for (TicketMatchOrderVO ticketMatchOrderVO : saleOrderVoEmail) {
                            TicketMatchOrderVO ticketMatchOrderVOItem = new TicketMatchOrderVO();
                            ticketMatchOrderVOItem.setAsin(ticketMatchOrderVO.getAsin());
                            ticketMatchOrderVOItem.setSellerSku(ticketMatchOrderVO.getSellerSku());
                            ticketMatchOrderVOItem.setImageUrl(ticketMatchOrderVO.getImageUrl());
                            ticketMatchOrderVOItem.setErpSku(ticketMatchOrderVO.getErpSku());
                            ticketMatchOrderVOItem.setQuantity(ticketMatchOrderVO.getQuantity());
                            ticketMatchOrderVOItem.setItemAmount(ticketMatchOrderVO.getItemAmount());
                            ticketMatchOrderVO.setOrderMatchInfo(Arrays.asList(ticketMatchOrderVOItem));
                        }
                        resMatchOrderList.addAll(saleOrderVoEmail);
                        //将工单对应订单号置空用于前端显示匹配列信息
                        scTicket.setAmazonOrderId(null);
                }else{

                    ScStationLetter scStationLetter = scStationLetterService.selectBaseScStationLetterById(scTicket.getSourceId());
                    if (scStationLetter!=null&&!StringUtils.isEmpty(scStationLetter.getCustomerEmail())){
                        //根据邮箱获取最近15笔订单
                        List<TicketMatchOrderVO> saleOrderVoEmail = saleOrdersMapper.selectOrderAndProductByEmail(scStationLetter.getCustomerEmail());
                        //每行为一个订单号项
                        for (TicketMatchOrderVO ticketMatchOrderVO : saleOrderVoEmail) {
                            TicketMatchOrderVO ticketMatchOrderVOItem = new TicketMatchOrderVO();
                            ticketMatchOrderVOItem.setAsin(ticketMatchOrderVO.getAsin());
                            ticketMatchOrderVOItem.setSellerSku(ticketMatchOrderVO.getSellerSku());
                            ticketMatchOrderVOItem.setImageUrl(ticketMatchOrderVO.getImageUrl());
                            ticketMatchOrderVOItem.setErpSku(ticketMatchOrderVO.getErpSku());
                            ticketMatchOrderVOItem.setQuantity(ticketMatchOrderVO.getQuantity());
                            ticketMatchOrderVOItem.setItemAmount(ticketMatchOrderVO.getItemAmount());
                            ticketMatchOrderVO.setOrderMatchInfo(Arrays.asList(ticketMatchOrderVOItem));
                        }
                        resMatchOrderList.addAll(saleOrderVoEmail);
                    }
                }
            } else  if (AccountSaleChannelEnum.WALMART.getValue().equals(scTicket.getTicketSource())&&scTicket.getSourceId() != null) { //沃尔玛站内信 ，需匹配订单
                ScStationLetter scStationLetter = scStationLetterService.selectBaseScStationLetterById(scTicket.getSourceId());
                if(scStationLetter != null) {
                    List<ScStationLetterMessage> scStationLetterMessageList = iScStationLetterMessageService.selectScStationLetterMessages(scStationLetter.getStationLetterId());
                    if(CollectionUtil.isNotEmpty(scStationLetterMessageList)) {
                        scStationLetterMessageList = scStationLetterMessageList.stream().filter(i -> "1".equals(i.getType())).collect(Collectors.toList());
                        if(CollectionUtil.isNotEmpty(scStationLetterMessageList)) {
                            Long infoId = scStationLetterMessageList.get(0).getEmailInfoId();
                            //todo 查询邮件 取from
                            SyncEmailInfo  syncEmailInfo = syncEmailInfoMapper.selectEmailInfoById(infoId);
                            if(syncEmailInfo != null) {
                                amzOrderV.setEncryptEmail(syncEmailInfo.getFromAddress()); //加密邮箱
                            }
                        }
                    }
                }
                if (null != scTicket.getAmazonOrderId()){ //有订单，但未匹配sku，则为多行
                    //有订单，但是有多行，需用户手动选择具体订单列
                    List<TicketMatchOrderVO> saleOrderVoEmail = saleOrdersMapper.selectOrderItemByOrderId(scTicket.getOrderId());
                    for (TicketMatchOrderVO ticketMatchOrderVO : saleOrderVoEmail) {
                        TicketMatchOrderVO ticketMatchOrderVOItem = new TicketMatchOrderVO();
                        ticketMatchOrderVOItem.setAsin(ticketMatchOrderVO.getAsin());
                        ticketMatchOrderVOItem.setSellerSku(ticketMatchOrderVO.getSellerSku());
                        ticketMatchOrderVOItem.setImageUrl(ticketMatchOrderVO.getImageUrl());
                        ticketMatchOrderVOItem.setErpSku(ticketMatchOrderVO.getErpSku());
                        ticketMatchOrderVOItem.setQuantity(ticketMatchOrderVO.getQuantity());
                        ticketMatchOrderVOItem.setItemAmount(ticketMatchOrderVO.getItemAmount());
                        ticketMatchOrderVO.setOrderMatchInfo(Arrays.asList(ticketMatchOrderVOItem));
                    }
                    resMatchOrderList.addAll(saleOrderVoEmail);
                    //将工单对应订单号置空用于前端显示匹配列信息
                    scTicket.setAmazonOrderId(null);
                }
            }
            scTicket.setMatchingOrderInfoList(resMatchOrderList); //设置匹配订单
        }
    }

    /**
     * @param erpskus           款号
     * @param orgId             组织ID
     * @param baseTicketOrderVO 工单订单基础信息
     * @param ticketGoodsSku    工单行SKU
     * @description: 查询订单商品信息
     * @author: Moore
     * @date: 2024/1/3 16:35
     * @return: void
     **/
    public void ticketOrderProductPack(List<String> erpskus, Integer orgId, BaseTicketOrderVO baseTicketOrderVO,String ticketGoodsSku) {
        LambdaQueryWrapper<Products> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Products::getPicUrls, Products::getId,Products::getName);
        if (StringUtils.isEmpty(ticketGoodsSku)){
            queryWrapper.in(Products::getErpsku, erpskus);
        }else{
            queryWrapper.in(Products::getErpsku, Arrays.asList(ticketGoodsSku.split(","))); //工单行SKU
        }
        queryWrapper.eq(Products::getOrgId, orgId);
        List<Products> products = productsMapper.selectList(queryWrapper);
        baseTicketOrderVO.setImageUrls(products.stream().filter(item -> !StringUtils.isEmpty(item.parseImg())).map(Products::parseImg).distinct().collect(Collectors.toList()));
        baseTicketOrderVO.setGoodsName(products.stream().filter(item -> !StringUtils.isEmpty(item.getName())).map(Products::getName).distinct().collect(Collectors.joining(",")));
        baseTicketOrderVO.setProductIds(products.stream().map(Products::getId).distinct().collect(Collectors.toList()));
    }


    /**
     * 手动创建工单(其他站外信类型)
     * @param scTicket
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ScTicket manualSaveScTicket(ScTicket scTicket) {

        String ticketNumber = DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + RandomUtil.randomNumbers(9);
        ScTicket byId = scTicketMapper.selectScTicketByTicketNumber(ticketNumber);
        if (byId != null) {
            throw new CustomException("工单已存在，请勿重复创建");
        }
        scTicket.setTicketNumber(ticketNumber);
        if (scTicket.getUserId()!=null){
            UserEntity sysUser = userService.getById(scTicket.getUserId().intValue());
            scTicket.setHandlerBy(sysUser.getName());
            scTicket.setDeptId(StringUtils.isNotEmpty(sysUser.getDeptId()) ? Long.valueOf(sysUser.getDeptId()) : null);
        }else {
            //页面操作使用当前登录用户
            try {
                AuthUserDetails authUserDetails = AuthContextHolder.getAuthUserDetails();
                UserEntity sysUser = userService.getById(authUserDetails.getId());
                scTicket.setHandlerBy(sysUser.getName());
                scTicket.setUserId(authUserDetails.getId().longValue());
                scTicket.setDeptId(StringUtils.isNotEmpty(sysUser.getDeptId()) ? Long.valueOf(sysUser.getDeptId()) : null);
            } catch (Exception e) {
                throw new CustomException("获取当前用户失败");
            }
        }

        if (scTicket.getOrderId() != null) {
            scTicket.setMatchOrderType(ScTicketConstant.MATCH_TYPE_AUTO);
        }
        int count = this.insertScTicket(scTicket);
        /*//有匹配订单，保存客户信息
        //查询订单信息
        Long customerId = scTicket.getCustomerId();
        if (scTicket.getOrderId() != null) {
            List<SaleOrderVoTicket> saleOrderVoTicketList = saleOrdersMapper.selectOrderByOrderId(scTicket.getOrderId());
            if (!CollectionUtils.isEmpty(saleOrderVoTicketList)) {
                SaleOrderVoTicket saleOrderVoTicket = saleOrderVoTicketList.get(0);
                if (null == customerId) {
                    scCustomerService.saveCustomerAndJoinTicket(saleOrderVoTicket, scTicket);
                }
            }
        }*/
        //插入其他站内信
        ScOffsiteLetter scOffsiteLetter = new ScOffsiteLetter();
        scOffsiteLetter.setOrganizationId(scTicket.getOrganizationId().intValue());
        scOffsiteLetter.setTicketId(scTicket.getId());
        scOffsiteLetter.setAmazonOrderId(scTicket.getAmazonOrderId());
        scOffsiteLetter.setOrderId(scTicket.getOrderId());
        scOffsiteLetter.setShopId(scTicket.getShopId());
        scOffsiteLetter.settingDefaultCreate();
        scOffsiteLetter.settingDefaultUpdate();
        cusCenterOffsiteMapper.insert(scOffsiteLetter);
        scTicket.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_OFFSITE_LETTER);
        scTicket.setSourceId(scOffsiteLetter.getId());
        this.updateScTicketSource(scTicket);
        return scTicket;
    }

    @Override
    public ScTicket manualSaveScTicketForExpert(ScTicket scTicket) {
        manualSaveScTicket(scTicket);
        return scTicket;
    }


    /**
     * 查询手动创建工单对应订单
     * @param orderNo
     * @param orgId
     * @return
     */
    @Override
    public List<ScTicketOrderAndOrderItem> selectManualOrder(String orderNo, Long orgId) {
        return scTicketMapper.selectManualOrder(orderNo, orgId);
    }

    @Override
    public ApiResponseResult getTicketSourceDocument(Long ticketId) {
        ScTicket ticket = selectScTicketById(ticketId);
        if (ScTicketConstant.TICKET_SOURCE_DOCUMENT_REISSUE.equals(ticket.getSourceDocument())) {  //补发产品及配件
            return ApiResponseResult.buildSuccessResult(scOrderReissueService.selectScOrderReissueById(ticket.getSourceId()));
        } else if (ScTicketConstant.TICKET_SOURCE_DOCUMENT_ORDER_CANNEL.equals(ticket.getSourceDocument())) { //订单取消
            return ApiResponseResult.buildSuccessResult(saleOrderCancelService.selectSaleOrderCancelById(ticket.getSourceId()));
        }else if (ScTicketConstant.TICKET_SOURCE_DOCUMENT_OFFSITE_LETTER.equals(ticket.getSourceDocument())) {//其他站外信
            return ApiResponseResult.buildSuccessResult(cusCenterOffsiteService.selectCusCenterOffsiteById(ticket.getSourceId()));
        }
        return ApiResponseResult.buildSuccessResult();
    }



    @Override
    public List<ScTicket> selectTicketByCustomerIdAndTicketType(Long customerId,String ticketType) {
        return scTicketMapper.selectTicketByCustomerIdAndTicketType(customerId, ticketType);
    }

    @Override
    public ScTicket selectScTicketBySourceType(Long sourceId,  String ticketType) {
        return scTicketMapper.selectScTicketBySourceType(sourceId, ticketType);
    }

    private String getTicketBusinessStatus(Long ticketId) {
        List<ScTicketRefundInfo> scTicketRefundInfos = scTicketRefundInfoMapper.selectList(Wrappers.lambdaQuery(ScTicketRefundInfo.class).eq(ScTicketRefundInfo::getTicketId, ticketId));
        if (CollectionUtil.isNotEmpty(scTicketRefundInfos)) {
            List<ReturnInfoEntity> returnInfoList = returnInfoMapper.selectReturnInfoListByIds(scTicketRefundInfos.stream().map(ScTicketRefundInfo::getReturnInfoId).collect(Collectors.toList()));
            return CollectionUtil.isEmpty(returnInfoList) ? null : OrderReturnStatusEnum.getDesc(returnInfoList.get(0).getReturnStatusCommon()); //退货状态
        } else {
            return null;
        }
    }


    /**
     * Description: 校验联系客户
     *
     * @param contextId
     * @param ticketId
     * @param ticketType
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/3/18
     */
    @Override
    public ScTicket checkContactCustomers(Integer contextId, Long ticketId, String ticketType,Long shopId,String buyEmail) {
        if (ScTicketConstant.TICKET_TYPE_OFFSITE_LETTER.equals(ticketType)) {//其他站外信
            return cusCenterOffsiteService.scOffsiteLetterCheckContactCustomers(contextId, ticketId, ticketType);
        } else if (ScTicketConstant.TICKET_TYPE_EMAIL.equals(ticketType)) {//站外信
            return iScEmailSendService.checkReplyEmail(contextId, ticketId, ticketType);
        } else if (ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG.equals(ticketType)) {//站内信
            return iScStationLetterService.checkReplyStationLetter(contextId, ticketId, ticketType,shopId,buyEmail);
        }
        throw new RuntimeException("校验工单类型错误");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ScTicket createdAndInsertScTicketNew(String channelOrderNo, Long sourceId, String sourceOfWork, String sourceFrom, Long ticketId, String refundType, OrderRefundRequestInfo orderRefundRequest, String flow, String shopId, Long orderId, String sourceChannel, String ticketNumber, Date createdAt, String businessType) {
        OrderRefundRequestInfo refundRequest = new OrderRefundRequestInfo();
        Account account = new Account();
        Long busSourceId = 0L;
        if (OrderRefundEnum.INTERNAL.getCode().equals(refundType)) {
            // 这里逻辑可以优化,写的太急了有不必要的查询
            refundRequest = orderRefundRequestInfoService.getById(sourceId);
            OrderRefund orderRefund = orderRefundService.getOne(new LambdaQueryWrapper<OrderRefund>().eq(OrderRefund::getReverseOrderId, refundRequest.getReturnId()));
            if (ObjectUtil.isNull(orderRefund)) {
                busSourceId = refundRequest.getId();
            } else {
                busSourceId = orderRefund.getId();
            }


            if (ObjectUtil.isNull(refundRequest)) {
                account = accountService.getById(orderRefundRequest.getShopId());
            } else {
                account = accountService.getById(refundRequest.getShopId());
            }
        } else if (OrderRefundEnum.EXTERNAL.getCode().equals(refundType)) {
            if (StringUtils.isNotEmpty(shopId)) {
                account = accountService.getById(shopId);
            }
        } else if (OrderRefundEnum.SALES_RETURN_TO_INTERNAL.getCode().equals(refundType)) {
            if (StringUtils.isNotEmpty(shopId)) {
                account = accountService.getById(shopId);
            }
        }
        if ("webhookFirst".equals(flow)) {
            account = accountService.getAccountByShopId(shopId);
        }
        String customerName = saleOrdersMapper.getCustomerNameByOrderId(String.valueOf(orderId));
        ScTicket scTicketCrete = new ScTicket();
        scTicketCrete.setOrderId(orderId);
        scTicketCrete.setTicketType(sourceOfWork);  //工单类型 站外
        scTicketCrete.setTicketName("客户申请订单号:" + channelOrderNo + "退款"); //工单名称
        scTicketCrete.setCustomerName(customerName);
        scTicketCrete.setRemark("店铺名称:" + account.getTitle() + " 订单号:" + channelOrderNo + " 买家名称:" + customerName); //描述

        scTicketCrete.setTicketSource(sourceChannel);// 工单来源(订单API)
        scTicketCrete.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH);
        if (OrderRefundEnum.INTERNAL.getCode().equals(refundType)) {
            if (ObjectUtil.isNull(orderRefundRequest)) {
                scTicketCrete.setReverseOrderId(String.valueOf(refundRequest.getReturnId()));//优先级（高）
            } else {
                scTicketCrete.setReverseOrderId(String.valueOf(orderRefundRequest.getReturnId()));//优先级（高）
            }
        }

        scTicketCrete.setAmazonOrderId(channelOrderNo);
        // 站内,子工单的 sourceid 要用 refundid
        if (OrderRefundEnum.INTERNAL.getCode().equals(refundType)) {
            if ("webhookFirst".equals(flow)) {
                scTicketCrete.setSourceId(busSourceId);
                scTicketCrete.setReverseOrderId(String.valueOf(orderRefundRequest.getReturnId()));
                scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(busSourceId.toString(), "l", 5, "0")+ RandomUtil.randomNumbers(4));
            } else if ("tiktok".equals(flow)) {
                scTicketCrete.setReverseOrderId(String.valueOf(refundRequest.getReturnId()));
                scTicketCrete.setOperateStatus(ScTicketConstant.TICKET_OPERATE_APPROVE);
            }

        }

        if (ObjectUtil.isNotNull(ticketId) && OrderRefundEnum.INTERNAL.getCode().equals(refundType)) {
            ScTicket scTicket = scTicketMapper.selectScTicketByTicketId(ticketId);
            if (ObjectUtil.isNotNull(scTicket) && !"0".equals(String.valueOf(busSourceId))) {
                scTicketCrete.setSourceId(busSourceId);
                scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(busSourceId.toString(), "l", 5, "0")+ RandomUtil.randomNumbers(4));
            } else {
                scTicketCrete.setSourceId(sourceId);
                scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(sourceId.toString(), "l", 5, "0")+ RandomUtil.randomNumbers(4));
            }
        } else if (ObjectUtil.isNotNull(ticketId) && OrderRefundEnum.EXTERNAL.getCode().equals(refundType)) {
            scTicketCrete.setSourceId(sourceId);//来源ID 退款表主键
            scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(sourceId.toString(), "l", 5, "0")+ RandomUtil.randomNumbers(4));
        } else if (ObjectUtil.isNotNull(ticketId) && OrderRefundEnum.SALES_RETURN_TO_INTERNAL.getCode().equals(refundType)) {
            scTicketCrete.setSourceId(sourceId);//来源ID 退款表主键
            scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(sourceId.toString(), "l", 5, "0")+ RandomUtil.randomNumbers(4));
        }


        try {
            if (OrderRefundEnum.INTERNAL.getCode().equals(refundType)) {
                scTicketCrete.setBusinessApplyTime(MathTimeUtils.longToDate(refundRequest.getCreateTime()));
            } else {
                scTicketCrete.setBusinessApplyTime(MathTimeUtils.getNow());
            }
        } catch (Exception e) {
            throw new CommonException("时间异常");
        }
        if (OrderRefundEnum.EXTERNAL.getCode().equals(refundType) || OrderRefundEnum.SALES_RETURN_TO_INTERNAL.getCode().equals(refundType)) {
            scTicketCrete.setOperateStatus(ScTicketConstant.TICKET_OPERATE_APPROVE);
        }
        scTicketCrete.setParentTicketId(ticketId);
        scTicketCrete.setSourceDocument(sourceFrom); //来源单据 退款表
        scTicketCrete.setShopId(Long.valueOf(account.getId()));  //店铺
        scTicketCrete.setOrganizationId(Long.valueOf(account.getOrgId()));//组织ID

//        临时单的填充 补完
        if(ObjectUtil.isNotNull(ticketNumber)){
            scTicketCrete.setTicketNumber(ticketNumber);
        }
        if(ObjectUtil.isNotNull(createdAt)){
            scTicketCrete.setCreatedAt(createdAt);
        }
        if(ObjectUtil.isNotEmpty(businessType)){
            scTicketCrete.setBusinessType(businessType);
        }
        return scTicketCrete;
    }


    @Override
    public void originalExportScTicket(ScTicket query, UserEntity authUserEntity) {
        TaskCenterRequest request = new TaskCenterRequest();
        request.setOrgId(query.getOrganizationId().intValue());
        request.setTaskCode("erp.sc_ticket.export");
        List<Object> list = new ArrayList<>();
        list.add(JSON.toJSONString(query));
        list.add(query.getOrganizationId().intValue());
        request.setArgs(list);
        this.taskCenterService.startTask(request, authUserEntity);
//        AssertUtil.isFail(FinanceErrorEnum.ASNYC_EXPORT_GENERATED);
    }

    private List<ScTicket> setTicketAttr(List<ScTicket> scTickets) {
        if (CollectionUtil.isEmpty(scTickets)) {
            return new ArrayList<>();
        }
        try {
            List<Long> collect = scTickets.stream().map(ScTicket::getShopId).filter(Objects::nonNull).collect(Collectors.toList());
            List<Account> accounts = accountService.getBaseMapper().selectBatchIds(collect);
            if (CollectionUtil.isNotEmpty(collect)) {
                scTickets.parallelStream().filter(t->t.getShopId() != null).forEach(s->{
                    List<Account> accounts1 = accounts.stream().filter(q -> q.getId().equals(s.getShopId().intValue())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(accounts1)) {
                        s.setShopName(accounts1.get(0).getTitle());
                    }
                });
            }
            return scTickets;
        } catch (Exception e) {
            log.error("设置店铺失败,{}",e.getMessage());
            return scTickets;
        }
    }



    /**
     * Description: temu退货工单自动关闭
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/11/28
     */
    @Override
    @Transactional
    public void temuReturnTicketAutoClosed() {
        int pageNo = 1;
        int pageSize = 2000;
        //todo 查询退货工单
        while(true) {
            PageHelper.startPage(pageNo, pageSize);
            List<ScTicket> scTicketList = scTicketMapper.temuReturnTicketAutoClosed(ScTicketConstant.TICKET_TYPE_RETURN);
            if (CollUtil.isEmpty(scTicketList)) {
                break;
            }
            for (ScTicket scTicket : scTicketList) {
                //todo 关闭工单
                Long ticketId = scTicket.getId();
                log.error("关闭工单,ticketId:{}", ticketId);
                ScTicketLog ticketLog = new ScTicketLog();
                ticketLog.settingDefaultSystemCreate();
                ticketLog.setTicketId(ticketId);
                ticketLog.setOperateType("Closed");
                ticketLog.setOperatorBy(ticketLog.getCreatedName());
                ticketLog.setOperateTime(new Date());
                ticketLog.setOperateContent("关闭工单:");
                scTicketLogService.insertScTicketLog(ticketLog);
                ScTicket ticket = new ScTicket();
                ticket.setId(ticketId);
                ticket.setTicketStatus("CLOSED");
                ticket.settingDefaultSystemUpdate();
                scTicketMapper.updateScTicketStatus(ticket);
            }
        }
    }

    /**
     * Description: temu退款工单自动关闭
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/11/28
     */
    @Override
    @Transactional
    public void temuRefundTicketAutoClosed() {
        int pageNo = 1;
        int pageSize = 2000;
        //todo 查询退款工单
        while(true) {
            PageHelper.startPage(pageNo, pageSize);
            List<ScTicket> scTicketList = scTicketMapper.temuReturnTicketAutoClosed(ScTicketConstant.TICKET_TYPE_REFUND_REQUEST);
            if (CollUtil.isEmpty(scTicketList)) {
                break;
            }
            for (ScTicket scTicket : scTicketList) {
                //todo 关闭工单
                Long ticketId = scTicket.getId();
                log.error("关闭工单,ticketId:{}", ticketId);
                ScTicketLog ticketLog = new ScTicketLog();
                ticketLog.settingDefaultSystemCreate();
                ticketLog.setTicketId(ticketId);
                ticketLog.setOperateType("Closed");
                ticketLog.setOperatorBy(ticketLog.getCreatedName());
                ticketLog.setOperateTime(new Date());
                ticketLog.setOperateContent("关闭工单:");
                scTicketLogService.insertScTicketLog(ticketLog);
                ScTicket ticket = new ScTicket();
                ticket.setId(ticketId);
                ticket.setTicketStatus("CLOSED");
                ticket.settingDefaultSystemUpdate();
                scTicketMapper.updateScTicketStatus(ticket);
            }
        }
    }



    /**
     * @description:创建temu申诉工单
     * @author: Moore
     * @date: 2024/11/29 14:51
     * @param
     * @param violation
     * @param violationDetails
     * @return: void
    **/
    @Override
    public void createTemuAppealTicket(TemuViolation violation, List<TemuViolationDetail> violationDetails) {

        Integer violationType = violation.getViolationType();
        //violationType.equals(1)
        if ((violationType.equals(1)|| violationType.equals(4)) && new Integer(0).equals(violation.getAppealStatus())) {

            try {
                for (TemuViolationDetail violationDetail : violationDetails) {
                    //创建工单
                        ScTicket scTicketCrete = new ScTicket();
                        scTicketCrete.setOrderId(violationDetail.getOrderId());
                        scTicketCrete.setTicketType(ScTicketConstant.TICKET_TYPE_ORDER_PACKAGE_APPEAL);  //工单类型
                        scTicketCrete.setTicketName("订单号:" + violationDetail.getOrderNo() + " " + AppealTypeEnum.getName(violationType)); //工单名称
                        scTicketCrete.setRemark("店铺名称:" + StrUtil.nullToDefault(violation.getAccountTitle(), "") + " 订单号:" + violationDetail.getOrderNo()); //描述
                        scTicketCrete.setTicketSource(AccountSaleChannelEnum.TEMU.getValue());// 工单来源(订单API)
                        scTicketCrete.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH); //优先级
                        scTicketCrete.setAmazonOrderId(violationDetail.getOrderNo());
                        scTicketCrete.setSourceId(violationDetail.getId());
                        scTicketCrete.setSourceDocument(ScTicketConstant.TICKET_TYPE_VIOLATION_DETAIL_TABLE);
    //                scTicketCrete.setSellerSku(appealInfo.getSellerSku());
                        scTicketCrete.setGoodsSku(violationDetail.getErpSku());
                        scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(violationDetail.getOrderId().toString(), "l", 5, "0") + RandomUtil.randomNumbers(4));
                        scTicketCrete.setShopId(violation.getAccountId().longValue());  //店铺
                        scTicketCrete.setOrganizationId(violation.getOrgId().longValue());//组织ID
                        scTicketCrete.setBusinessApplyTime(violationDetail.getEarliestAppealEndTime()); //对于此类型为 申诉结束时间
                        this.insertScTicket(scTicketCrete); //创建工单
                      violationDetail.setTicketId(scTicketCrete.getId());
                }
            } catch (Exception e) {
                log.error("创建temu违规申诉工单失败：{}", e);
            }

        }

    }

    /**
     * Description: 无需回复
     *
     * @param ticketId
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2025/1/17
     */
    @Override
    @Transactional
    public void noReply(Long ticketId) {
        ScTicket scTicket = scTicketMapper.selectScTicketById(ticketId);
        if(scTicket == null) {
            throw new RuntimeException("查询不到对应的工单");
        }
        log.error("关闭工单,ticketId:{}", ticketId);
        ScTicketLog ticketLog = new ScTicketLog();
        ticketLog.settingDefaultSystemCreate();
        ticketLog.setTicketId(ticketId);
        ticketLog.setOperateType("Closed");
        ticketLog.setOperatorBy(ticketLog.getCreatedName());
        ticketLog.setOperateTime(new Date());
        ticketLog.setOperateContent("无需回复，关闭工单");
        scTicketLogService.insertScTicketLog(ticketLog);
        //todo 一个是邮件未回复数为0，一个是回复状态为已回复
        ScTicket ticket = new ScTicket();
        ticket.setId(ticketId);
        ticket.setTicketStatus("CLOSED");
        ticket.setNoReplyQty(new Integer(0));
        ticket.setTicketReplyStatus(new Integer(1));
        ticket.settingDefaultSystemUpdate();
        scTicketMapper.updateScTicketStatus(ticket);
        //todo 修改同一个对话工单下面的所有邮件的已读标识为已读。
        QueryWrapper<SyncEmailInfo> queryWrapper = new QueryWrapper();
        queryWrapper.eq("source_id",scTicket.getId());
        queryWrapper.eq("source_document","sc_ticket");
        List<SyncEmailInfo> syncEmailInfoList = isyncEmailInfoService.list(queryWrapper);
        if(CollectionUtils.isNotEmpty(syncEmailInfoList)) {
            syncEmailInfoList.parallelStream().forEach(syncEmailInfo -> {
                MerchantEmail merchantEmail = merchantEmailService.selectMerchantEmailById(syncEmailInfo.getMerchantEmailId());
                if (merchantEmail != null && StringUtils.isNotEmpty(syncEmailInfo.getEmailId())) {
                    //todo 异步执行跟新已回复标记。
                    if (merchantEmail.getEmail().contains("@outlook.com")) {
                        String token = merchantEmailService.getoutLookAccessToken(merchantEmail);
                        String id = GraphEmailApi.readEmailbyMessageId(syncEmailInfo.getEmailId(), token);
                        if (StringUtil.isNotEmpty(id)) {
                           readEmailByInternetMessageId(token, id);
                        }
                    } else {
                        readAndWriteEmail(merchantEmail.getEmail(), merchantEmail.getPassword(), syncEmailInfo.getEmailId());
                    }
                }
            });
        }
    }

    @Override
    public void deleteScTicket(Long ticketId) {
        scTicketMapper.deleteTicketById(ticketId);
    }

    @Override
    public String checkUpdateMatchOrder(Long ticketId) {
        //todo 判断是不是有子工单
        List<ScTicket> scTicketList = scTicketMapper.selectScTicketByParentId(ticketId);
        if(CollectionUtil.isNotEmpty(scTicketList)) { //有子工单
            return "存在子工单，不允许修改";
        }
        return "";
    }


    @Override
    public List<MarReturnManuaItemDTO>  selectReturnManualInfoSkuInfo(Integer orderType, Long orderId, Long orgId)  {
        ArrayList<MarReturnManuaItemDTO> res = new ArrayList<>();
        if (ScTicketConstant.MATCH_TYPE_MANUAL.equals(orderType)) {//手动
            ScManualOrder scManualOrder = scManualOrderMapper.selectScManualOrderById(orderId);//手工订单
            if (scManualOrder != null && !StringUtils.isEmpty(scManualOrder.getGoodsSku())) {
                LambdaQueryWrapper<Products> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.select(Products::getPicUrls, Products::getId, Products::getName,Products::getErpsku);
                queryWrapper.in(Products::getErpsku, Arrays.asList(scManualOrder.getGoodsSku().split(",")));
                queryWrapper.eq(Products::getOrgId, orgId);
                List<Products> products = productsMapper.selectList(queryWrapper);
                if (!CollectionUtils.isEmpty(products)) {
                    for (Products product : products) {
                        MarReturnManuaItemDTO marReturnManuaItemDTO = new MarReturnManuaItemDTO();
                        marReturnManuaItemDTO.setErpSku(product.getErpsku());
                        marReturnManuaItemDTO.setImageUrl(product.parseImg());
                        marReturnManuaItemDTO.setProductName(product.getName());
                        res.add(marReturnManuaItemDTO);
                    }
                } else {
                    MarReturnManuaItemDTO marReturnManuaItemDTO = new MarReturnManuaItemDTO();
                    marReturnManuaItemDTO.setErpSku(scManualOrder.getGoodsSku());
                    res.add(marReturnManuaItemDTO);
                }
            }
        } else {
            //查询订单信息
            List<SaleOrderVoTicket> saleOrderVoTicketList = saleOrdersMapper.selectOrderByOrderId(orderId);
            for (SaleOrderVoTicket saleOrderVoTicket : saleOrderVoTicketList) {
                MarReturnManuaItemDTO marReturnManuaItemDTO = new MarReturnManuaItemDTO();
                marReturnManuaItemDTO.setImageUrl(saleOrderVoTicket.getImageUrl());
                marReturnManuaItemDTO.setErpSku(saleOrderVoTicket.getErpsku());
                marReturnManuaItemDTO.setSellerSku(saleOrderVoTicket.getSellerSku());
                marReturnManuaItemDTO.setProductName(saleOrderVoTicket.getGoodsName());
                marReturnManuaItemDTO.setSaleNum(saleOrderVoTicket.getQuantity());
                res.add(marReturnManuaItemDTO);
            }
        }
        return res;
    }


    /**
     * @param
     * @param ticketType
     * @description: 校验订单号相关工单是否已存在
     * @author: Moore
     * @date: 2025/7/7 15:27
     * @return: java.lang.Boolean  TRUE 弹窗
     **/
    @Override
    public JSONObject checkTicketOrder(Long contextId, String amazonOrderId, Long orderId, String ticketType, Long ticketId) {

        JSONObject res = new JSONObject();
        res.put("types", Collections.emptyList());

        if (StringUtil.isEmpty(amazonOrderId) || ticketType == null||contextId==null) {
            return res;
        }

        //退货、补发配件 根据订单号判断是有正在进行中的工单
        if (ScTicketTypeEnum.RETURN.getValue().equals(ticketType) || ScTicketTypeEnum.REISSUE_ACCESSORIES.getValue().equals(ticketType)) {
            ScTicket scTicket = new ScTicket();
            scTicket.setOrganizationId(contextId);
            scTicket.setAmazonOrderId(amazonOrderId);
            scTicket.setOrderId(orderId);
            scTicket.setTicketType(ticketType);
            List<ScTicket> scTickets = scTicketMapper.selectScTicketList(scTicket);
            if (scTickets.stream().anyMatch(item -> "NEW".equals(item.getTicketStatus())||"PROCESSING".equals(item.getTicketStatus()) )){
                res.put("types", Arrays.asList(1));
            }
        } else if (ScTicketTypeEnum.OUTSIDE_REFUND.getValue().equals(ticketType)||ScTicketTypeEnum.INSIDE_REFUND.getValue().equals(ticketType)||ScTicketTypeEnum.REISSUE_GOODS.getValue().equals(ticketType)) {
            ScTicket scTicket = new ScTicket();
            scTicket.setOrganizationId(contextId);
            scTicket.setAmazonOrderId(amazonOrderId);
            scTicket.setOrderId(orderId);
            scTicket.setTicketType(ticketType);
            List<ScTicket> scTickets = scTicketMapper.selectScTicketList(scTicket);
            boolean ruleOne = scTickets.stream().anyMatch(item -> item.getTicketStatus().equals("NEW") || item.getTicketStatus().equals("PROCESSING"));
            boolean ruleTwo =CollectionUtils.isNotEmpty(returnInfoMapper.selectReturnPlanByOrder(amazonOrderId, orderId));
            if (ruleOne || ruleTwo) {
                List<Integer> list = new ArrayList<>();
                if (ruleOne) list.add(1);
                if (ruleTwo) list.add(2);
                res.put("types", list);
            }
        }else  if (ScTicketTypeEnum.REISSUE_SHIPMENT.getValue().equals(ticketType)){
            ScOrderReissue reissue = new ScOrderReissue();
            reissue.setTicketId(ticketId);
            reissue.setReissueType("SHIPMENT");
            List<ScOrderReissue> orderReissueList = scOrderReissueMapper.selectScOrderReissueByTicketList(reissue);
            //工单状态为 待处理或处理中
            List<ScOrderReissue> filteScorerReissue = orderReissueList.stream().filter(item ->
                    "NEW".equals(item.getTicketStatus()) || "PROCESSING".equals(item.getTicketStatus())
            ).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filteScorerReissue)) {
                res.put("types", Arrays.asList(1));
            }
        }
        return res;
    }

    /**
     * 工单增加标签
     *
     * @param tagRelation
     * @param contextId
     * @param opType
     */
    @Transactional
    @Override
    public void addTag(TabcutVideoTagRealtionDTO tagRelation, Integer contextId, String opType) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(tagRelation.getIds())) {
            throw new CommonException("未选择工单数据");
        }
        for (Long id : tagRelation.getIds()) {
            ScTicket scTicket = scTicketMapper.selectScTicketById(id);
            if (scTicket == null) {
                continue;
            }
            if ("remove".equals(opType) && CollectionUtils.isNotEmpty(tagRelation.getTagIds())) { //移除,传入的非空
                QueryWrapper<ScTicketTagRelation> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("ticket_id", scTicket.getId());
                queryWrapper.in("tag_id", tagRelation.getTagIds());
                scTicketTagRelationService.remove(queryWrapper);
            } else if ("add".equals(opType) && CollectionUtils.isNotEmpty(tagRelation.getTagIds())) { //追加
                QueryWrapper<ScTicketTagRelation> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("ticket_id", scTicket.getId());
                queryWrapper.in("tag_id", tagRelation.getTagIds());
                List<ScTicketTagRelation> scTicketTagRelationList = scTicketTagRelationService.list(queryWrapper);
                if(CollectionUtils.isNotEmpty(scTicketTagRelationList)) { //筛选出已经添加了的
                    Set<Long> tagIdSet = scTicketTagRelationList.stream().map(i -> i.getTagId()).collect(Collectors.toSet());
                    scTicketTagRelationService.saveBatch(tagRelation.getTagIds().stream().filter(i -> !tagIdSet.contains(i)).map(i -> {
                        ScTicketTagRelation scTicketTagRelation = new ScTicketTagRelation();
                        scTicketTagRelation.setTicketId(scTicket.getId());
                        scTicketTagRelation.setTagId(i);
                        scTicketTagRelation.settingDefaultCreate();
                        return scTicketTagRelation;
                    }).collect(Collectors.toList()));
                } else {
                    scTicketTagRelationService.saveBatch(tagRelation.getTagIds().stream().map(i -> {
                        ScTicketTagRelation scTicketTagRelation = new ScTicketTagRelation();
                        scTicketTagRelation.setTicketId(scTicket.getId());
                        scTicketTagRelation.setTagId(i);
                        scTicketTagRelation.settingDefaultCreate();
                        return scTicketTagRelation;
                    }).collect(Collectors.toList()));
                }
            } else if ("update".equals(opType)) { // 跟新 的情况
                //todo 先删除之前的数据
                QueryWrapper<ScTicketTagRelation> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("ticket_id", scTicket.getId());
                scTicketTagRelationService.remove(queryWrapper);
                if(CollectionUtil.isNotEmpty(tagRelation.getTagIds())) {
                    scTicketTagRelationService.saveBatch(tagRelation.getTagIds().stream().map(i -> {
                        ScTicketTagRelation scTicketTagRelation = new ScTicketTagRelation();
                        scTicketTagRelation.setTicketId(scTicket.getId());
                        scTicketTagRelation.setTagId(i);
                        scTicketTagRelation.settingDefaultCreate();
                        return scTicketTagRelation;
                    }).collect(Collectors.toList()));
                }
            }
        }
    }


    @Override
    public List<ScTicket> selectRemarkByIds(List<Long> ticketIdList) {
        return scTicketMapper.selectRemarkByIds(ticketIdList);
    }

    @Override
    public void updateScTicketRemark(Long id, String remark, Long contextId) {
        ScTicketHandle scTicketHandle = scTicketHandleMapper.findScTicketHandleByTicketId(id);
        if (Objects.isNull(scTicketHandle)) {
            ScTicketHandle newHandle = new ScTicketHandle();
            newHandle.setTicketId( id);
            newHandle.setOrganizationId(contextId);
            newHandle.setRemark(remark);
            newHandle.settingCreated();
            newHandle.settingUpdated();
            scTicketHandleService.saveHandle(newHandle);
            String message = String.format("工单备注由%s -> %s", null,  remark);
            buildTicketLog(id, message, "Update");
            return;
        }
        String message = String.format("工单备注由%s -> %s", scTicketHandle.getRemark(),  remark);
        buildTicketLog(id, message, "Update");
        scTicketMapper.updateScTicketRemark(id, remark);
    }

   private void buildTicketLog(Long id, String message, String operateType) {
       ScTicketLog scTicketLog = new ScTicketLog();
       scTicketLog.settingDefaultCreate();
       scTicketLog.setTicketId(id);
       scTicketLog.setOperateType(operateType);
       scTicketLog.setOperateTime(new Date());
       scTicketLog.setOperatorBy(UserUtils.getCurrentUserName());
       scTicketLog.setOperateContent(message);
       scTicketLogService.insertScTicketLog(scTicketLog);
    }
}


