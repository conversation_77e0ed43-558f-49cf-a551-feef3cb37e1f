package com.bizark.op.service.excel.handler;

import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Comment;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;

import java.util.Map;

/**
 * 插入批注和下拉框
 */
@Slf4j
public class CommentWriteHandler extends SelectorCellWriteHandler implements RowWriteHandler {

    private Map<String, String> commentMap;


    public CommentWriteHandler(Map<Integer, String[]> map, Map<String, String> commentMap) {
        super(map);
        this.commentMap = commentMap;
    }

    @Override
    public void afterRowDispose(RowWriteHandlerContext context) {
        if (BooleanUtils.isTrue(context.getHead())) {
            Sheet sheet = context.getWriteSheetHolder().getSheet();
            Drawing<?> drawingPatriarch = sheet.createDrawingPatriarch();

            for (Map.Entry<String, String> entry : commentMap.entrySet()) {
                String col =  entry.getKey();
                String value = entry.getValue();
                Comment comment =
                        drawingPatriarch.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) Short.valueOf(col), 0, Short.valueOf(col), 1));
                // 输入批注信息
                comment.setString(new XSSFRichTextString(value));
                // 将批注添加到单元格对象中
                sheet.getRow(0).getCell(1).setCellComment(comment);
            }

        }
    }

}
