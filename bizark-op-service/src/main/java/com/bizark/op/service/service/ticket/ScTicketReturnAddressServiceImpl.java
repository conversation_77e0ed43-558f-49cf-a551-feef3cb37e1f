package com.bizark.op.service.service.ticket;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.boss.api.parameter.aftersale.OrderServiceItem;
import com.bizark.boss.api.parameter.aftersale.OrderServiceRequest;
import com.bizark.boss.api.parameter.aftersale.OrderServiceReturn;
import com.bizark.boss.api.service.aftersale.OrderSerService;
import com.bizark.op.api.cons.ScTicketConstant;
import com.bizark.op.api.enm.ticket.TiktokRefundCarrierCodeEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.returns.VO.ReturnItemInfoVO;
import com.bizark.op.api.entity.op.returns.VO.ReturnRefundExpressSheetVO;
import com.bizark.op.api.entity.op.returns.VO.TiktokReturnRefundPlanVO;
import com.bizark.op.api.entity.op.returns.entity.MarManualReturn;
import com.bizark.op.api.entity.op.returns.entity.ReturnInfoEntity;
import com.bizark.op.api.entity.op.returns.entity.ScTicketRefundInfo;
import com.bizark.op.api.entity.op.returns.temu.MarTemuReverseLogisticInfo;
import com.bizark.op.api.entity.op.ticket.ScTicket;
import com.bizark.op.api.entity.op.ticket.ScTicketLog;
import com.bizark.op.api.entity.op.ticket.ScTicketReturnAddress;
import com.bizark.op.api.exception.ErpCommonException;
import com.bizark.op.api.service.amazon.AmazonReturnService;
import com.bizark.op.api.service.returns.MarManualReturnService;
import com.bizark.op.api.service.ticket.IScTicketLogService;
import com.bizark.op.api.service.ticket.IScTicketReturnAddressService;
import com.bizark.op.api.service.ticket.IScTicketService;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.AssertUtil;
import com.bizark.op.common.util.BeanCopyUtils;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.service.mapper.account.AccountMapper;
import com.bizark.op.service.mapper.returns.ReturnInfoMapper;
import com.bizark.op.service.mapper.returns.ScTicketRefundInfoMapper;
import com.bizark.op.service.mapper.temu.MarTemuReverseLogisticInfoMapper;
import com.bizark.op.service.mapper.ticket.ScTicketMapper;
import com.bizark.op.service.mapper.ticket.ScTicketReturnAddressMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工单退货地址服务
 *
 * @Author: Ailill
 * @Date: 2024/3/19 10:11
 */
@Service
@Slf4j
public class ScTicketReturnAddressServiceImpl extends ServiceImpl<ScTicketReturnAddressMapper, ScTicketReturnAddress> implements IScTicketReturnAddressService {

    @Autowired
    private OrderSerService orderSerService;

    @Autowired
    private IScTicketService scTicketService;

    @Autowired
    private ScTicketRefundInfoMapper scTicketRefundInfoMapper;

    @Autowired
    private ReturnInfoMapper returnInfoMapper;

    @Autowired
    private AccountMapper accountMapper;

    @Autowired
    private IScTicketLogService scTicketLogService;

    @Autowired
    private ScTicketMapper scTicketMapper;

    @Autowired
    private AmazonReturnService amazonReturnService;

    @Autowired
    private MarTemuReverseLogisticInfoMapper marTemuReverseLogisticInfoMapper;


    @Autowired
    private MarManualReturnService marManualReturnService;



    /**
     * @description: 购买面单回显
     * @author: Moore
     * @date: 2025/7/2 17:18
     * @param
     * @param ticketId
     * @return: com.bizark.op.api.entity.op.returns.VO.TiktokReturnRefundPlanVO
    **/
    @Override
    public ReturnRefundExpressSheetVO getTicketReturnAddressInfo(Long ticketId) {
        ScTicket scTicketById = scTicketService.selectScTicketById(ticketId);
        if (scTicketById == null) {
            throw new ErpCommonException("工单信息为空");
        }

        //根据工单id在退货中间表查询退货信息id
        List<ReturnInfoEntity> returnInfoList = getReturnInfoEntities(ticketId);

        AssertUtil.isFalse(CollectionUtil.isEmpty(returnInfoList), scTicketById.getTicketSource() + "退货信息为空,工单id为:" + ticketId);


        ReturnRefundExpressSheetVO vo = new ReturnRefundExpressSheetVO();
        ScTicketReturnAddress one = this.getOne(Wrappers.lambdaQuery(ScTicketReturnAddress.class).eq(ScTicketReturnAddress::getTicketId, ticketId));
        if (one != null) {
            vo.setOrgWarehouseId(one.getOrgWarehouseId()); //退货仓ID
            vo.setOrgWarehouseCode(one.getOrgWarehouseCode()); //退货仓CODE
            vo.setReturnProviderName(one.getReturnProviderName()); //退货物流商
            vo.setConsignor(one.getConsignor()); //发件人 名称
            vo.setProvince(one.getProvince());//州/省份
            vo.setCity(one.getCity()); //城市
            vo.setCountry(one.getCountry()); //国家
            vo.setAddressOne(one.getAddressOne());//地址1
            vo.setAddressTwo(one.getAddressTwo());//地址2
            vo.setPhone(one.getPhone()); //手机号
            vo.setZipCode(one.getZipCode());//邮编
        }else  if (ScTicketConstant.TICKET_SOURCE_DOCUMENT_MANUAL_RETURN.equals(scTicketById.getSourceDocument())){
            List<Account> accountList = null;
            if (scTicketById.getMatchOrderType().equals(1)) {
                AssertUtil.isFalse(returnInfoList.get(0).getShopId() == null, scTicketById.getTicketSource() + "退货工单店铺id为空,工单id为:" + ticketId);
                accountList = accountMapper.selectAccountRefundInfoNew(returnInfoList.get(0).getShopId().longValue(),"Y");
                if (CollectionUtils.isEmpty(accountList)) {
                    throw new ErpCommonException("店铺未维护退货仓库，请先维护后再创建退货计划");
                }
            }
            MarManualReturn manualReturn = marManualReturnService.getById(scTicketById.getSourceId());
            if (CollectionUtils.isNotEmpty(accountList)) {
                vo.setOrgWarehouseId(accountList.get(0).getId()); //退货仓ID
                vo.setOrgWarehouseCode(accountList.get(0).getAreaCode()); //退货仓CODE
            }

//            vo.setReturnProviderName(); //退货物流商(承运商)，最终归属于行信息
            vo.setConsignor(manualReturn.getCustomerName()); //发件人 名称
            vo.setProvince(manualReturn.getStateOrRegion());//州/省份
            vo.setCity(manualReturn.getCity()); //城市
            vo.setCountry(manualReturn.getCountryCode()); //国家
            vo.setAddressOne(manualReturn.getAddress());//地址1
            vo.setAddressTwo(manualReturn.getAddress2());//地址2
            vo.setPhone(manualReturn.getPhone()); //手机号
            vo.setZipCode(manualReturn.getPostalCode());//邮编
        }else  {
              //API 生成退货工单，查询订单获取
            AssertUtil.isFalse(returnInfoList.get(0).getShopId() == null, scTicketById.getTicketSource() + "退货工单店铺id为空,工单id为:" + ticketId);
            List<Account> accountList = accountMapper.selectAccountRefundInfoNew(returnInfoList.get(0).getShopId().longValue(),"Y");
            if (CollectionUtils.isEmpty(accountList)) {
                throw new ErpCommonException("店铺未维护退货仓库，请先维护后再创建退货计划");
            }
            TiktokReturnRefundPlanVO returnRefundPlanVO = returnInfoMapper.selectTiktokReturnPlanById(returnInfoList.get(0).getId().longValue());
            vo.setOrgWarehouseId(accountList.get(0).getId()); //退货仓ID
            vo.setOrgWarehouseCode(accountList.get(0).getAreaCode()); //退货仓CODE
            vo.setReturnProviderName(returnRefundPlanVO.getReturnProviderName()); //退货物流商(承运商)，最终归属于行信息
            vo.setConsignor(returnRefundPlanVO.getConsignor()); //发件人 名称
            vo.setProvince(returnRefundPlanVO.getProvince());//州/省份
            vo.setCity(returnRefundPlanVO.getCity()); //城市
            vo.setCountry(returnRefundPlanVO.getCountry()); //国家
            vo.setAddressOne(returnRefundPlanVO.getAddressOne());//地址1
            vo.setAddressTwo(returnRefundPlanVO.getAddressTwo());//地址2
            vo.setPhone(returnRefundPlanVO.getPhone()); //手机号
            vo.setZipCode(returnRefundPlanVO.getZipCode());//邮编
        }

        //设置退货明细信息
        List<ReturnRefundExpressSheetVO.TiktokReturnRefundVoItem> itemList = returnInfoList.stream().map(item -> {
            ReturnRefundExpressSheetVO.TiktokReturnRefundVoItem tiktokReturnRefundVoItem = new ReturnRefundExpressSheetVO.TiktokReturnRefundVoItem();
            tiktokReturnRefundVoItem.setSku(item.getSku());
            return tiktokReturnRefundVoItem;
        }).collect(Collectors.toList());
        vo.setProductItemRequests(itemList);
        return vo;
    }

    /**
     * 查询店铺下所有退货仓库
     *
     * @param shopId
     * @return
     */
    @Override
    public List<Account> selectReturnWarehouse(Long shopId) {
        return accountMapper.selectAccountRefundInfoNew(shopId,"N");
    }

    public List<ReturnInfoEntity> getReturnInfoEntities(Long ticketId) {
        List<ScTicketRefundInfo> scTicketRefundInfos = scTicketRefundInfoMapper.selectList(Wrappers.lambdaQuery(ScTicketRefundInfo.class).eq(ScTicketRefundInfo::getTicketId, ticketId));
        if (CollectionUtil.isEmpty(scTicketRefundInfos)) {
            throw new CustomException("退货中间信息为空,工单id为:" + ticketId);
        }
        List<Long> returnInfoIds = scTicketRefundInfos.stream().map(ScTicketRefundInfo::getReturnInfoId).collect(Collectors.toList());
        List<ReturnInfoEntity> returnInfoList = returnInfoMapper.selectReturnInfoListByIds(returnInfoIds);
        return returnInfoList;
    }

    /**
     * 保存工单退货地址信息
     *
     * @param contextId
     * @param ticketId
     * @param tiktokReturnRefundPlanVO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveScTicketReturnAddress(Integer contextId, Long ticketId, ReturnRefundExpressSheetVO tiktokReturnRefundPlanVO) {

        log.info("保存或更新面单地址:{},工单id:{},当前时间:{}", JSON.toJSONString(tiktokReturnRefundPlanVO),ticketId, DateUtils.getNowDate());
        ScTicketReturnAddress scTicketReturnAddress = BeanCopyUtils.copyBean(tiktokReturnRefundPlanVO, ScTicketReturnAddress.class);
        scTicketReturnAddress.setOrganizationId(contextId);
        scTicketReturnAddress.setTicketId(ticketId);
        ScTicketReturnAddress one = this.getOne(Wrappers.lambdaQuery(ScTicketReturnAddress.class).eq(ScTicketReturnAddress::getTicketId, ticketId));
        if (one == null) {
            scTicketReturnAddress.settingDefaultCreate();
            return this.save(scTicketReturnAddress);
        } else {
            scTicketReturnAddress.setId(one.getId());
            scTicketReturnAddress.settingDefaultSystemUpdate();
            return this.updateById(scTicketReturnAddress);
        }

    }

    /**
     * 购买面单
     *
     * @param contextId
     * @param ticketId
     * @param tiktokReturnRefundPlanVO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitScTicketReturnAddress(Integer contextId, Long ticketId, ReturnRefundExpressSheetVO tiktokReturnRefundPlanVO) {

        if (StringUtils.isEmpty(tiktokReturnRefundPlanVO.getReturnProviderName())) {
            throw new ErpCommonException("退货发货方式不能为空");
        }
        log.info("提交面单地址:{},工单id:{},当前时间:{}", JSON.toJSONString(tiktokReturnRefundPlanVO),ticketId, DateUtils.getNowDate());
        this.saveScTicketReturnAddress(contextId, ticketId, tiktokReturnRefundPlanVO);
        OrderServiceRequest orderServiceRequest = new OrderServiceRequest();
        orderServiceRequest.setContextId(contextId);
        OrderServiceReturn orderServiceReturn = new OrderServiceReturn();
        orderServiceReturn.setOrgWarehouseId(tiktokReturnRefundPlanVO.getOrgWarehouseId()); //退货仓ID
        orderServiceReturn.setOrgWarehouseCode(tiktokReturnRefundPlanVO.getOrgWarehouseCode()); //退货仓
        orderServiceReturn.setName(tiktokReturnRefundPlanVO.getConsignor()); //发件人
        orderServiceReturn.setPhone(tiktokReturnRefundPlanVO.getPhone());//
        orderServiceReturn.setAddress1(tiktokReturnRefundPlanVO.getAddressOne());
        orderServiceReturn.setAddress2(tiktokReturnRefundPlanVO.getAddressTwo());
        orderServiceReturn.setCountry(tiktokReturnRefundPlanVO.getCountry()); //国家
        orderServiceReturn.setState(tiktokReturnRefundPlanVO.getProvince()); //州 地区
        orderServiceReturn.setCity(tiktokReturnRefundPlanVO.getCity()); //城市
        orderServiceReturn.setPostal(tiktokReturnRefundPlanVO.getZipCode()); //邮编

        List<OrderServiceItem> items = new ArrayList<>();
        for (ReturnRefundExpressSheetVO.TiktokReturnRefundVoItem productItemRequest : tiktokReturnRefundPlanVO.getProductItemRequests()) {
            if (StringUtils.isEmpty(productItemRequest.getSku())){
               continue;
            }
            OrderServiceItem orderServiceItem = new OrderServiceItem();
            orderServiceItem.setErpSku(productItemRequest.getSku());
            //如果明细的承运商未传或用户在提交前更改了表单的承运商且前端未同步用户更改后的承运商到明细承运商
            orderServiceItem.setReturnMode(tiktokReturnRefundPlanVO.getReturnProviderName());
            items.add(orderServiceItem);
        }

        orderServiceReturn.setOrderServiceItemList(items);

        orderServiceRequest.setOrderServiceReturn(orderServiceReturn);
        List<Map<String, String>> maps = new ArrayList<>();
        try {
            maps = orderSerService.buyLabelThirdParty(orderServiceRequest);
            log.info("购买面单返回结果:{}",maps);
        } catch (Exception e) {
            if (StringUtils.isNotEmpty(e.getMessage())) {
                log.error("购买面单出错:{}", e.getMessage());
                if (!e.getMessage().contains("Exception")) {
                    throw new ErpCommonException(String.format("购买面单出错,%s",e.getMessage()));
                }
                String message = e.getMessage().replaceFirst("com.*?Exception:", "");
                if (StringUtils.isEmpty(message)) {
                    throw new ErpCommonException(String.format("购买面单出错,%s",e.getMessage()));
                } else {
                    if (message.contains("Exception:")) {
                        message = message.replaceFirst("com.*?Exception:", "");
                    }
                    throw new ErpCommonException(String.format("购买面单出错,%s", message));
                }
            } else {
                throw new ErpCommonException("购买面单出错");
            }

        }
        for (Map<String, String> map : maps) {
            if (map.containsKey("trackNo")) {
                String trackNo = map.get("trackNo");
                String url = map.get("url");
                if (StringUtils.isNotEmpty(trackNo)) {
                    //保存跟踪号到退货信息表
                    List<ReturnInfoEntity> returnInfoList = getReturnInfoEntities(ticketId);
                    if (CollectionUtil.isEmpty(returnInfoList)) {
                        throw new ErpCommonException("退货信息为空");
                    }
                    List<Integer> idList = returnInfoList.stream().map(ReturnInfoEntity::getId).collect(Collectors.toList());
                    if (CollectionUtil.isEmpty(idList)) {
                        throw new ErpCommonException("查无退货信息");
                    }
                    returnInfoMapper.updateReturnTrackNo(idList, trackNo,tiktokReturnRefundPlanVO.getReturnProviderName());
                    this.update(Wrappers.lambdaUpdate(ScTicketReturnAddress.class).eq(ScTicketReturnAddress::getTicketId,ticketId).set(ScTicketReturnAddress::getUrl,url));
                    ScTicket scTicketByTicketId = scTicketService.selectScTicketByTicketId(ticketId);
                    ScTicket scTicket = new ScTicket();
                    scTicket.settingDefaultUpdate();
                    if ("NEW".equals(scTicketByTicketId.getTicketStatus())) {
                        scTicket.setId(ticketId);
                        scTicket.setTicketStatus(ScTicketConstant.TICKET_STATUS_PROCESSING); //设置为处理中
                        scTicketMapper.updateScTicketStatus(scTicket);

                    }
                    ScTicketLog ticketLog = new ScTicketLog();
                    ticketLog.setTicketId(ticketId);
                    ticketLog.setOperateType("HANDLE");
                    ticketLog.setOperateTime(new Date());
                    ticketLog.setOperateContent("操作人" + scTicket.getUpdatedName() + "购买了面单");
                    scTicketLogService.insertScTicketLog(ticketLog);


                    if ("temu".equals(returnInfoList.get(0).getChannel())) {
                        for (ReturnInfoEntity returnInfoEntity : returnInfoList) {
                            MarTemuReverseLogisticInfo marTemuReverseLogisticInfo = new MarTemuReverseLogisticInfo();
                            marTemuReverseLogisticInfo.setParentAfterSalesSn(returnInfoEntity.getReturnId());  //退货ID
                            marTemuReverseLogisticInfo.setAfterSaleSn(returnInfoEntity.getRma()); //退货子
                            marTemuReverseLogisticInfo.setCarrierName(tiktokReturnRefundPlanVO.getReturnProviderName()); //承运商
                            marTemuReverseLogisticInfo.setTrackingNumber(trackNo);
                            marTemuReverseLogisticInfo.setIsSync(0); //不同步
                            marTemuReverseLogisticInfo.setRemark("购买面单");
                            marTemuReverseLogisticInfo.settingDefaultCreate();
                            marTemuReverseLogisticInfoMapper.insert(marTemuReverseLogisticInfo);
                        }
                    }


                    //创建退货计划,手动创建退货信息，不自动创建退货计划
                    if (!ScTicketConstant.TICKET_SOURCE_DOCUMENT_MANUAL_RETURN.equals(scTicketByTicketId)){
                        amazonReturnService.autoReturnRefundPlan(scTicketByTicketId,returnInfoList);

                    }
                    break;
                }
            }
        }

    }

}
