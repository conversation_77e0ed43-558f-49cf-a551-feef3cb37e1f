package com.bizark.op.service;

import com.bizark.common.constant.PropertiesConfig;
import com.bizark.framework.service.startup.server.AbstractServletWebServerFactory;
import com.bizark.framework.service.startup.server.WebServer;
import com.bizark.framework.service.startup.server.webconfig.*;
import com.google.common.collect.ImmutableList;
import org.apache.dubbo.remoting.http.servlet.DispatcherServlet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2019/4/12
 */
public class ServiceBootstrap {

    private static final Logger LOG = LoggerFactory.getLogger(ServiceBootstrap.class);

    private static final int DEFAULT_PORT = 8080;
    private static String DEFAULT_ENTITYMANAGERBEANNAME = "";
//    private static String DEFAULT_ENTITYMANAGERBEANNAME = "entityManagerFactoryDefault";

    private static String DEFAULT_CONTEXTPATH = "";

    private static String DEFAULT_WEB_SERVER_FACTORY_CLASS_NAME = "com.bizark.framework.service.startup.embed.tomcat.TomcatServletWebServerFactory";

    private static AbstractServletWebServerFactory serverFactory;

    private static WebServer webServer;

    public static final String ENVIRONMENT;

    private static PropertiesConfig config;
    static {
        config = new PropertiesConfig("properties/setting");
        ENVIRONMENT = config.getPropByKey("enviorment");
    }

    public static void main(String[] args) {

        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            LOG.info("即将停止程序................................");
            try {
                if (webServer != null) {
                    webServer.stop();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }));


        long startTime = System.currentTimeMillis();
        LOG.info("\n------------------------------------------------------------------- 程序准备启动 -------------------------------------------------------------------");


//        int port = Integer.parseInt(System.getProperty("server.port", String.valueOf(DEFAULT_PORT)));
        int port = Integer.parseInt(System.getProperty("server.port", config.getPropByKey("dubbo_service_rpc.http.port",String.valueOf(DEFAULT_PORT))));

        String contextPath = System.getProperty("server.context-path", DEFAULT_CONTEXTPATH);
        String webServerFactoryClassName = System.getProperty("server.web-server-factory", DEFAULT_WEB_SERVER_FACTORY_CLASS_NAME);
        String entityManagerFactoryBeanName = System.getProperty("server.entity-manager-name", config.getPropByKey("entity.manager.factory.bean.name.default",DEFAULT_ENTITYMANAGERBEANNAME));

        Class<?> clazz;
        try {
            clazz = Class.forName(webServerFactoryClassName);
            serverFactory = (AbstractServletWebServerFactory) clazz.newInstance();
        } catch (Exception e) {
            throw new IllegalArgumentException(webServerFactoryClassName, e);
        }

        serverFactory.setPort(port);
        serverFactory.setContextPath(contextPath);
        serverFactory.setWebConfiguration(createWebConfig(entityManagerFactoryBeanName));

        webServer = serverFactory.getWebServer();
        webServer.start();

        long runTime = (System.currentTimeMillis() - startTime) / 1000;
        LOG.info("\n" + getPattern());
        LOG.info("\n------------------------------------------------------------------- 程序启动完成...启动用时:[{}]s -------------------------------------------------------------------", runTime);
    }

    private static WebConfiguration createWebConfig(String entityManagerFactoryBeanName) {
        ContextParam contextParam = new ContextParam("contextConfigLocation", "classpath:config/spring.xml");
        Listener listener = new Listener(org.springframework.web.context.ContextLoaderListener.class);
        Servlet servlet = new Servlet("dubbo", DispatcherServlet.class, 1);
        ServletMapping servletMapping = new ServletMapping("dubbo", "/*");


        WebConfiguration webConfiguration = new WebConfiguration();
        webConfiguration.setContextParams(ImmutableList.of(contextParam));
        webConfiguration.setListeners(ImmutableList.of(listener));
        webConfiguration.setServlets(ImmutableList.of(servlet));
        webConfiguration.setServletMappings(ImmutableList.of(servletMapping));

//        if(entityManagerFactoryBeanName!=null && entityManagerFactoryBeanName.length()>0){
//            Filter.InitParam initParam = new Filter.InitParam("entityManagerFactoryBeanName",entityManagerFactoryBeanName);
//            Filter filter = new Filter("openEntityManagerInViewFilter"
//                    ,org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter.class
//                    ,ImmutableList.of(initParam));
//            FilterMapping filterMapping = new FilterMapping("openEntityManagerInViewFilter", "/*");
//            webConfiguration.setFilters(ImmutableList.of(filter));
//            webConfiguration.setFilterMappings(ImmutableList.of(filterMapping));
//        }

        return webConfiguration;
    }

    private static String getPattern() {

        return  "\n" +
                "////////////////////////////////////////////////////////////////////\n" +
                "//                          _ooOoo_                               //\n" +
                "//                         o8888888o                              //\n" +
                "//                         88\" . \"88                              //\n" +
                "//                         (| ^_^ |)                              //\n" +
                "//                         O\\  =  /O                              //\n" +
                "//                      ____/`---'\\____                           //\n" +
                "//                    .'  \\\\|     |//  `.                         //\n" +
                "//                   /  \\\\|||  :  |||//  \\                        //\n" +
                "//                  /  _||||| -:- |||||-  \\                       //\n" +
                "//                  |   | \\\\\\  -  /// |   |                       //\n" +
                "//                  | \\_|  ''\\---/''  |   |                       //\n" +
                "//                  \\  .-\\__  `-`  ___/-. /                       //\n" +
                "//                ___`. .'  /--.--\\  `. . ___                     //\n" +
                "//              .\"\" '<  `.___\\_<|>_/___.'  >'\"\".                  //\n" +
                "//            | | :  `- \\`.;`\\ _ /`;.`/ - ` : | |                 //\n" +
                "//            \\  \\ `-.   \\_ __\\ /__ _/   .-` /  /                 //\n" +
                "//      ========`-.____`-.___\\_____/___.-`____.-'========         //\n" +
                "//                           `=---='                              //\n" +
                "//      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^        //\n" +
                "//             佛祖保佑       永不宕机      永无BUG               //\n" +
                "////////////////////////////////////////////////////////////////////" +
                "\n" +
                "\n";
    }

}
