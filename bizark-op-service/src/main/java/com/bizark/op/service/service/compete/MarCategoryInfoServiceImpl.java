package com.bizark.op.service.service.compete;

import java.util.*;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;


import com.bizark.op.api.entity.op.compete.*;
import com.bizark.op.api.entity.op.tk.expert.vo.VideoExportTaskVO;
import com.bizark.op.api.service.compete.IMarCategoryInfoService;
import com.bizark.op.common.core.page.PageInfoDomain;
import com.bizark.op.common.core.page.TableSupportInfo;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.common.util.UserUtils;
import com.bizark.op.service.mapper.compete.*;
import com.bizark.op.service.mapper.tabcut.TabcutCreatorVideoMapper;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 营销类目监控信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-10-28
 */
@Service
@Slf4j
public class MarCategoryInfoServiceImpl implements IMarCategoryInfoService {
    @Autowired
    private MarCategoryInfoMapper marCategoryInfoMapper;

    @Autowired
    private MarCategoryMapper marCategoryMapper;

    @Autowired
    private MarCategoryInfoHisMapper marCategoryInfoHisMapper;

    @Autowired
    private MarCompeteAsinMapper marCompeteAsinMapper;

    @Autowired
    private MarCompeteInfoMapper marCompeteInfoMapper;

    @Autowired
    private MarCompeteImgMapper marCompeteImgMapper;
    private String message;

    @Autowired
    private TabcutCreatorVideoMapper tabcutCreatorVideoMapper;

    @Value("${home_query_bi_url}")
    private String HOME_QUERY_BI_URL;

    /**
     * 查询营销类目监控信息
     *
     * @param id 营销类目监控信息ID
     * @return 营销类目监控信息
     */
    @Override
    public MarCategoryInfo selectMarCategoryInfoById(Long id) {
        return marCategoryInfoMapper.selectMarCategoryInfoById(id);
    }

    /**
     * 查询营销类目监控信息列表
     *
     * @param marCategoryInfo 营销类目监控信息
     * @return 营销类目监控信息
     */
    @Override
    public List<MarCategoryInfo> selectMarCategoryInfoList(MarCategoryInfo marCategoryInfo) {
        return marCategoryInfoMapper.selectMarCategoryInfoList(marCategoryInfo);

    }

    /**
     * 查询营销类目监控信息列表
     *
     * @param marCategoryInfo 营销类目监控信息
     * @return 营销类目监控信息
     */
    public List<MarCategoryInfoHis> selectMarCategoryInfoHisList(MarCategoryInfo marCategoryInfo) {
        MarCategoryInfoHis marCategoryInfoHis = new MarCategoryInfoHis();
        if (!StringUtils.isEmpty(marCategoryInfo.getDateMinute())) {
            marCategoryInfoHis.setSutcTime(null); //
            marCategoryInfoHis.setDateMinute(marCategoryInfo.getSutcTime() + " " + marCategoryInfo.getDateMinute());
        } else {
            marCategoryInfoHis.setSutcTime(marCategoryInfo.getSutcTime());
        }
        marCategoryInfoHis.setAsin(marCategoryInfo.getAsin());
        marCategoryInfoHis.setAsinTitle(marCategoryInfo.getAsinTitle());
        marCategoryInfoHis.setCategoryName(marCategoryInfo.getCategoryName());
        return marCategoryInfoHisMapper.selectMarCategoryInfoHisList(marCategoryInfoHis);

    }

    /**
     * 获取所有监控类目
     *
     * @param
     * @return 监控类目集合
     */
    @Override
    public List<MarCategory> selectMarCategoryByName() {
        return marCategoryMapper.selectMarCategoryList(new MarCategory());
    }

    /**
     * 新增营销类目监控信息
     *
     * @param marCategoryInfo 营销类目监控信息
     * @return 结果
     */
    @Override
    public int insertMarCategoryInfo(MarCategoryInfo marCategoryInfo) {
        marCategoryInfo.setCreatedAt(DateUtils.getNowDate());
        return marCategoryInfoMapper.insertMarCategoryInfo(marCategoryInfo);
    }

    /**
     * 新增监控竞品ASIN
     *
     * @param asins Asin数据集
     * @return 结果
     */
    @Override
    public String saveMonitoringAsins(String[] asins, UserEntity userEntity) {
        if (asins == null || asins.length == 0) {
            throw new CustomException("添加竞品不能空");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        //新增至
        for (String asin : asins) {
            //类目表
            MarCategoryInfo marCategoryInfo = marCategoryInfoMapper.selectMarCategoryByAsin(asin);


            //竞品表
            MarCompeteInfo marCompeteInfo = marCompeteInfoMapper.selectMarCompeteInfoByAsin(asin);
            if (marCompeteInfo != null) {
                failureNum++;
                failureMsg.append(" <br/> " + " ASIN：" + asin + " 已在竞品监控中，添加失败!");
                continue;
            }
            marCompeteInfo = new MarCompeteInfo();
            BeanUtils.copyProperties(marCategoryInfo, marCompeteInfo);
            marCompeteInfo.setId(null);
            marCompeteInfo.setMainCategory(null);
            marCompeteInfo.setMainCategoryRank(null);
            marCompeteInfo.setAsin(asin);
            marCompeteInfo.setCreatedName(userEntity.getName()); //添加人
            marCompeteInfo.setOrganizationId(marCategoryInfo.getOrganizationId());
            marCompeteInfoMapper.insertMarCompeteInfo(marCompeteInfo); //添加至竞品监控表
            //新增图片至图片表.
            if (!StringUtils.isEmpty(marCategoryInfo.getImgUrl())) {
                MarCompeteImg marCompeteImg = new MarCompeteImg();
                marCompeteImg.setAsin(asin);
                marCompeteImg.setImgSrc(marCategoryInfo.getImgUrl());
                marCompeteImg.setCompeteId(marCategoryInfo.getId());
                marCompeteImg.setCreatedName(userEntity.getName());
                marCompeteImg.setOrganizationId(marCategoryInfo.getOrganizationId());
                marCompeteImgMapper.insertMarCompeteImg(marCompeteImg);
            }
            successNum++;
        }

        //新增至 竞品监控表
        for (String asin : asins) {
            MarCompeteAsin marCompeteAsin = marCompeteAsinMapper.selectMarCompeteAsinByAsin(asin);
            if (null == marCompeteAsin) {
                MarCompeteAsin marCompeteAsinReq = new MarCompeteAsin();
                marCompeteAsinReq.setAsin(asin);
                marCompeteAsinReq.setCreatedName(userEntity.getName()); //添加人
                marCompeteAsinReq.setCreatedBy(userEntity.getId());//添加ID
                marCompeteAsinReq.setOrganizationId(userEntity.getOrgId() != null ? Long.valueOf(userEntity.getOrgId()): null);
                marCompeteAsinMapper.insertMarCompeteAsin(marCompeteAsinReq);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "共 " + successNum + " 条添加成功， " + failureNum + " 条添加失败，错误如下：");
            return failureMsg.toString();
        } else {
            successMsg.insert(0, "添加成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    @Override
    public int saveMonitoringAsin(String asin,UserEntity userEntity) {
        if (StringUtils.isEmpty(asin)) {
            throw new CustomException("获取竞品ASIN失败");
        }

        //判断竞品监控信息是否已经存在
        MarCompeteInfo marCompeteInfo = marCompeteInfoMapper.selectMarCompeteInfoByAsin(asin);
        if (null != marCompeteInfo) {
            throw new CustomException("ASIN: " + asin + " 已在监控中，不可重复添加！");
        }
        int flag = 0;

        //查询分类信息
        MarCategoryInfo marCategoryInfo = marCategoryInfoMapper.selectMarCategoryByAsin(asin);

        //新增至竞品监控信息表
        marCompeteInfo = new MarCompeteInfo();
        BeanUtils.copyProperties(marCategoryInfo, marCompeteInfo); //对象copy
        marCompeteInfo.setId(null);
        marCompeteInfo.setMainCategory(null);
        marCompeteInfo.setMainCategoryRank(null);
        marCompeteInfo.setId(null);
        marCompeteInfo.setAsin(asin);
        marCompeteInfo.setCreatedName(userEntity.getName()); //添加人
        flag = marCompeteInfoMapper.insertMarCompeteInfo(marCompeteInfo); //添加至竞品监控表
        //新增图片至图片表.
        if (!StringUtils.isEmpty(marCategoryInfo.getImgUrl())) {
            MarCompeteImg marCompeteImg = new MarCompeteImg();
            marCompeteImg.setAsin(asin);
            marCompeteImg.setImgSrc(marCategoryInfo.getImgUrl());
            marCompeteImg.setCompeteId(marCategoryInfo.getId());
            marCompeteImg.setCreatedName(userEntity.getName());
            marCompeteImgMapper.insertMarCompeteImg(marCompeteImg);
        }
        //添加至竞品监控ASIN
        MarCompeteAsin marCompeteAsin = marCompeteAsinMapper.selectMarCompeteAsinByAsin(asin);

        if (marCompeteAsin == null) {
            MarCompeteAsin marCompeteAsinReq = new MarCompeteAsin();
            marCompeteAsinReq.setAsin(asin);
            marCompeteAsinReq.setCreatedName(userEntity.getName()); //添加人
            marCompeteAsinReq.setCreatedBy(userEntity.getId());//添加ID
            marCompeteAsinMapper.insertMarCompeteAsin(marCompeteAsinReq);

        }
        return flag;
    }


    /**
     * 修改营销类目监控信息
     *
     * @param marCategoryInfo 营销类目监控信息
     * @return 结果
     */
    @Override
    public int updateMarCategoryInfo(MarCategoryInfo marCategoryInfo) {
        marCategoryInfo.setUpdatedAt(DateUtils.getNowDate());
        return marCategoryInfoMapper.updateMarCategoryInfo(marCategoryInfo);
    }

    /**
     * 批量删除营销类目监控信息
     *
     * @param ids 需要删除的营销类目监控信息ID
     * @return 结果
     */
    @Override
    public int deleteMarCategoryInfoByIds(Long[] ids) {
        return marCategoryInfoMapper.deleteMarCategoryInfoByIds(ids);
    }

    /**
     * 删除营销类目监控信息信息
     *
     * @param id 营销类目监控信息ID
     * @return 结果
     */
    @Override
    public int deleteMarCategoryInfoById(Long id) {
        return marCategoryInfoMapper.deleteMarCategoryInfoById(id);
    }


    /**
     * 保存
     *
     * @return 保存类目监控Mq信息
     */
    @Override
    @Transactional
    public void saveMqCategoryAsin(String message) {
        this.message = message;
        if (StringUtils.isEmpty(message)) {
            return;
        }
        //转换
        MarCategoryInfo marCategoryReqInfo = JSONObject.parseObject(message, MarCategoryInfo.class);
        //asin/排名/分类URl(确定具体分类名称)
        if (null == marCategoryReqInfo || StringUtils.isEmpty(marCategoryReqInfo.getAsin()) || StringUtils.isEmpty(marCategoryReqInfo.getAsinRank()) || StringUtils.isEmpty(marCategoryReqInfo.getCategoryUrl())) {
            return;
        }
        //根据URL获取实际类目名
        MarCategory marCategory = marCategoryMapper.selectMarCategoryByUrl(marCategoryReqInfo.getCategoryUrl().trim());
        if (null == marCategory) {
            return;
        }


        //判断是否存在
        MarCategoryInfo marCategoryInfoReq = new MarCategoryInfo();
        marCategoryInfoReq.setCategoryName(marCategory.getCategoryName()); //大类目下，排名
        marCategoryInfoReq.setAsinRank(marCategoryReqInfo.getAsinRank().trim());//排名
        List<MarCategoryInfo> marCategoryInfos = marCategoryInfoMapper.selectMarCategoryInfoList(marCategoryInfoReq);

        Long organizationId = marCategory.getOrganizationId(); //组织ID
        marCategoryReqInfo.setImgUrl(CollectionUtils.isEmpty(marCategoryReqInfo.getImages()) ? null : marCategoryReqInfo.getImages().get(0));  //图片取第一张
        marCategoryReqInfo.setCategoryName(marCategory.getCategoryName()); //实际目录名
        marCategoryReqInfo.setOrganizationId(organizationId);
        if (CollectionUtils.isEmpty(marCategoryInfos)) {
            marCategoryInfoMapper.insertMarCategoryInfo(marCategoryReqInfo);
        } else {
            //更新类目
            Long categoryId = marCategoryInfos.get(0).getId();
            MarCategoryInfo marCategoryInfo = this.entityNullTran(marCategoryReqInfo);
            marCategoryInfo.setId(categoryId);
            marCategoryInfo.setUpdatedAt(DateUtils.getNowDate());
            marCategoryInfoMapper.updateMarCategoryInfo(marCategoryInfo);
        }
        this.saveCategoryHisInfoBatch(marCategoryReqInfo, organizationId);
    }


    /**
     * Listing 分类监控时间查询
     *
     * @param dateStr 时间格式字符串
     * @return 结果
     */
    @Override
    public List<CategoryDateListEntity> selectCategoryDateList(String dateStr) {
        List<CategoryDateListEntity> categoryDateListEntities = marCategoryInfoMapper.selectCategoryDateList(dateStr);
        return categoryDateListEntities;
    }

    /**
     * Description: 营销类目监控趋势图
     *
     * @param competeRequest
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2023/12/22
     */
    @Override
    public List<CompeteSectionRes> selectCompeteTendency(CompeteRequest competeRequest) {
        if (!StringUtils.isEmpty(competeRequest.getDateFrom()) || !StringUtils.isEmpty(competeRequest.getDateTo())) {
            competeRequest.setDateType(null);
        }
        //响应结果
        List<CompeteSectionRes> competeSectionRes = new ArrayList<>();
        //todo 这里的sql 后面参照那边的
        List<MarCategoryInfo> marCategoryInfos = marCategoryInfoMapper.selectMarCategoryInfoTendencyList(competeRequest);
        CompeteSectionRes competeSection = null;
        for (MarCategoryInfo marCategoryInfo : marCategoryInfos) {
            competeSection = new CompeteSectionRes();
            if (null != marCategoryInfo.getCreatedAt()) {
                competeSection.setCreateTime(DateUtil.format(marCategoryInfo.getCreatedAt(), "yyyy-MM-dd"));
            }
            competeSection.setPrice(marCategoryInfo.getListPrice());
            competeSection.setStarRating(marCategoryInfo.getStarRating());
            competeSection.setStarReview(marCategoryInfo.getStarReviews());
            competeSection.setMainRank(StrUtil.nullToEmpty(marCategoryInfo.getMainCategoryRank()));
            competeSection.setMainCategoryStr(StrUtil.nullToEmpty(marCategoryInfo.getMainCategory()));
            competeSection.setSubCategoryStr(marCategoryInfo.getMinCategory()); //设置小类名称
            competeSection.setSubRank(marCategoryInfo.getMinCategoryRank());  //设置小类排名
            competeSectionRes.add(competeSection);
        }
        return competeSectionRes;
    }


    /**
     * @description:
     * @author: Moore
     * @date: 2024/8/15 19:04
     * @param：获取最新抓取时间
     * @return: java.lang.String
    **/
    @Override
    public String selectMaxCaptureTime() {
        return marCategoryInfoMapper.selectMaxCaptureTime();
    }


    /**
     * Listing 保存分类竞品历史信息
     *
     * @param marCategoryReqInfo 分类下排名ASIN信息
     * @return 结果
     */
    @Transactional
    public void saveCategoryHisInfoBatch(MarCategoryInfo marCategoryReqInfo, Long organizationId) {
        //直接存储，不做重复交验
        marCategoryReqInfo.setOrganizationId(organizationId);//设置组织ID
        marCategoryInfoHisMapper.insertMarCategoryInfoHis(marCategoryReqInfo);
    }


    /**
     * Listing 保存分类竞品历史信息（弃用）
     *
     * @param marCategoryReqInfo 分类下排名ASIN信息
     * @return 结果
     */
    private void saveCategoryHisInfo(MarCategoryInfo marCategoryReqInfo) {

        //查询当天是否已经更新

        MarCategoryInfoHis marCategoryInfo = new MarCategoryInfoHis();
        marCategoryInfo.setSyncTime(cn.hutool.core.date.DateUtil.today());
        marCategoryInfo.setCategoryName(marCategoryReqInfo.getCategoryName()); //分类名
        marCategoryInfo.setAsinRank(marCategoryReqInfo.getAsinRank());//排名
        List<MarCategoryInfoHis> marCategoryInfoHis = marCategoryInfoHisMapper.selectMarCategoryInfoHisList(marCategoryInfo);

        //当天新增
        if (CollectionUtils.isEmpty(marCategoryInfoHis)) {
            marCategoryInfoHisMapper.insertMarCategoryInfoHis(marCategoryReqInfo);
        } else {
            Long hisId = marCategoryInfoHis.get(0).getId();
            MarCategoryInfo marCategoryInfoTran = this.entityNullTran(marCategoryReqInfo);
            marCategoryInfoTran.setId(hisId);
            marCategoryInfoTran.setUpdatedAt(DateUtils.getNowDate());
            marCategoryInfoHisMapper.updateMarCategoryInfoHis(marCategoryInfoTran);
        }
    }


    /**
     * Listing 实体类Nul值转换空串操作
     *
     * @param marCategoryInfo 分类下排名ASIN信息
     * @return 结果
     */
    private MarCategoryInfo entityNullTran(MarCategoryInfo marCategoryInfo) {
        marCategoryInfo.setAsinTitle(StrUtil.nullToEmpty(marCategoryInfo.getAsinTitle()));//标题
        marCategoryInfo.setMainCategory(StrUtil.nullToEmpty(marCategoryInfo.getMainCategory())); //大类名称
        marCategoryInfo.setMainCategoryRank(StrUtil.nullToEmpty(marCategoryInfo.getMainCategoryRank())); //大类名称
        marCategoryInfo.setStarRating(StrUtil.nullToEmpty(marCategoryInfo.getStarRating())); //总星级
        marCategoryInfo.setStarReviews(StrUtil.nullToEmpty(marCategoryInfo.getStarReviews()));//总reviews数
        marCategoryInfo.setBrand(marCategoryInfo.getBrand());//品牌
        marCategoryInfo.setColor(marCategoryInfo.getColor());//颜色
        marCategoryInfo.setSpec(marCategoryInfo.getSpec());//规格
        marCategoryInfo.setListPrice(StrUtil.nullToEmpty(marCategoryInfo.getListPrice())); //售价
        marCategoryInfo.setCoupon(StrUtil.nullToEmpty(marCategoryInfo.getCoupon()));//优惠券
        marCategoryInfo.setDiscount(StrUtil.nullToEmpty(marCategoryInfo.getDiscount()));//折扣
        marCategoryInfo.setShipsFrom(StrUtil.nullToEmpty(marCategoryInfo.getShipsFrom()));//发货方
        marCategoryInfo.setSoldBy(StrUtil.nullToEmpty(marCategoryInfo.getSoldBy()));//销售方
        marCategoryInfo.setDeliveryTime(StrUtil.nullToEmpty(marCategoryInfo.getDeliveryTime()));//送达时效
        marCategoryInfo.setSalesStatus(StrUtil.nullToEmpty(marCategoryInfo.getSalesStatus()));//发货时效
        marCategoryInfo.setFreight(StrUtil.nullToEmpty(marCategoryInfo.getFreight()));//运费
        marCategoryInfo.setProductDimensions(StrUtil.nullToEmpty(marCategoryInfo.getProductDimensions()));//产品尺寸
        marCategoryInfo.setItemWeight(StrUtil.nullToEmpty(marCategoryInfo.getItemWeight()));//重量
        marCategoryInfo.setStyle(StrUtil.nullToEmpty(marCategoryInfo.getStyle()));//风格
        marCategoryInfo.setFirstDate(StrUtil.nullToEmpty(marCategoryInfo.getFirstDate()));//上线时间
        return marCategoryInfo;
    }


    /**
     * 查询营销类目监控信息列表
     *
     * @param marCategoryInfo 营销类目监控信息
     * @return 营销类目监控信息集合
     */
    @Override
    public JSONObject selectMarCategoryInfoBiList(MarCategoryInfo marCategoryInfo) {
        PageInfoDomain pageInfoDomain = TableSupportInfo.buildPageRequest();
        Integer page = pageInfoDomain.getPage();
        Integer rows = pageInfoDomain.getRows();
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("asinList",marCategoryInfo.getAsinList());
        paramMap.put("asinTitle",marCategoryInfo.getAsinTitle());
        paramMap.put("brand",marCategoryInfo.getBrand());
        paramMap.put("spiderCategory",marCategoryInfo.getSpiderCategory());
        paramMap.put("contextId",marCategoryInfo.getOrganizationId());
        paramMap.put("page",page);
        paramMap.put("rows",rows);

        //String queryUrl = "http://*************:5555/api/v1/bestSellerList";
        String queryUrl = HOME_QUERY_BI_URL + "/api/v1/bestSellerList";
        HttpRequest request = HttpUtil.createPost(queryUrl);
        log.error("bi查询bestSeller信息地址：{},参数{},", queryUrl, JSON.toJSONString(paramMap));
        request.body(JSON.toJSONString(paramMap));
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            throw new RuntimeException("bi查询bestSeller信息接口异常");
        }
        log.info("bi查询bestSeller信息返回结果：{}", response.body());
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        if(jsonObject != null) {
            JSONObject data = jsonObject.getJSONObject("data");
            if(data != null) {
                JSONArray content = data.getJSONArray("content");
                if(content != null && content.size() > 0) {
                    for(Object o:content) {
                        JSONObject con = (JSONObject)o;
                        if(StringUtil.isNotEmpty(con.getString("ydayMainCategoryRank"))) {
                            Integer yDay = Integer.valueOf(con.getString("ydayMainCategoryRank"));
                            Integer tDay = Integer.valueOf(con.getString("mainCategoryRank"));
                            con.put("mainCompareToday",tDay - yDay);
                        }else {
                            con.put("mainCompareToday",new Integer(0));
                        }
                        if(StringUtil.isNotEmpty(con.getString("ydayMinCategoryRank"))) {
                            Integer yDay = Integer.valueOf(con.getString("ydayMinCategoryRank"));
                            Integer tDay = Integer.valueOf(con.getString("minCategoryRank"));
                            con.put("minCompareToday",tDay - yDay);
                        }else {
                            con.put("minCompareToday",new Integer(0));
                        }
                    }
                }
            }
            return jsonObject;
        } else {
            throw new RuntimeException("bi查询bestSeller信息接口异常");
        }
    }

    /**
     * @param marCategoryInfo return
     * @desc
     * <AUTHOR>
     * @date 2025/5/12 18:24
     * param
     */
    @Override
    public void exportBiList(MarCategoryInfo marCategoryInfo) {
        Integer userId = UserUtils.getCurrentUserId(0); //获取当前用户
        String userName = UserUtils.getCurrentUserName("system");
        VideoExportTaskVO vo = new VideoExportTaskVO();
        vo.setExportId(userId + "-" + System.currentTimeMillis());
        marCategoryInfo.setExportId(vo.getExportId());
        this.biMarCategoryInfoExport(marCategoryInfo);
        Date date = new Date();
        vo.setTaskNo(vo.getExportId()); //任务号
        vo.setTaskCode("erp.bi.export.MarCategoryInfo.time");
        vo.setCreatedAt(date);
        vo.setCreatedBy(userId);
        vo.setCreatedName(userName);
        vo.setUpdatedAt(date);
        vo.setUpdatedBy(userId);
        vo.setUpdatedName(userName);
        vo.setStatus("processing");
        vo.setBody(JSON.toJSONString(marCategoryInfo));
        vo.setOrgId(marCategoryInfo.getOrganizationId().intValue());
        vo.setProcessTime(date);
        vo.setTaskTitle("bestSeller信息导出");
        tabcutCreatorVideoMapper.expoertTaskInsert(vo);
    }


    void biMarCategoryInfoExport(MarCategoryInfo marCategoryInfo) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("asinList",marCategoryInfo.getAsinList());
        paramMap.put("asinTitle",marCategoryInfo.getAsinTitle());
        paramMap.put("brand",marCategoryInfo.getBrand());
        paramMap.put("spiderCategory",marCategoryInfo.getSpiderCategory());
        paramMap.put("contextId",marCategoryInfo.getOrganizationId());
        paramMap.put("exportId",marCategoryInfo.getExportId());
        //String queryUrl = "http://*************:5555/api/v1/bestSellerExport";
        String queryUrl = HOME_QUERY_BI_URL + "/api/v1/bestSellerExport";
        HttpRequest request = HttpUtil.createPost(queryUrl);
        log.error("bi查询bestSeller信息导出地址：{},参数{},", queryUrl,JSON.toJSONString(paramMap));
        request.body(JSON.toJSONString(paramMap));
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            throw new RuntimeException("bi查询bestSeller信息导出接口异常");
        }
        log.info("bi查询bestSeller信息导出返回结果：{}", response.body());
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        if(jsonObject == null) {
            throw new RuntimeException("bi查询bestSeller信息导出接口异常");
        }
    }

    /**
     * Description: 营销类目监控趋势图
     *
     * @param competeRequest
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2023/12/22
     */
    @Override
    public JSONObject selectBiCompeteTendency(CompeteRequest competeRequest,Long contextId) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("contextId",contextId);
        paramMap.put("asin",competeRequest.getAsin());
        paramMap.put("dateFrom",competeRequest.getDateFrom());
        paramMap.put("dateTo",competeRequest.getDateTo());
        //String queryUrl = "http://*************:5555/api/v1/bestSellerTendency";
        String queryUrl = HOME_QUERY_BI_URL + "/api/v1/bestSellerTendency";
        HttpRequest request = HttpUtil.createPost(queryUrl);
        log.error("bi bestSeller趋势图地址：{},参数{},", queryUrl,JSON.toJSONString(paramMap));
        request.body(JSON.toJSONString(paramMap));
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            throw new RuntimeException("bi bestSeller趋势图接口异常");
        }
        log.info("bi bestSeller趋势图返回结果：{}", response.body());
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        if(jsonObject != null) {
            return jsonObject;
        } else {
            throw new RuntimeException("bi bestSeller趋势图接口异常");
        }
    }


}
