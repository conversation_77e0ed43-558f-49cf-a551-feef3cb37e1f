package com.bizark.op.service.service.log;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.common.contract.AuthUserDetails;
import com.bizark.op.api.annotation.LogConvert;
import com.bizark.op.api.enm.log.LogEnums;
import com.bizark.op.api.entity.op.log.ErpLogModify;
import com.bizark.op.api.entity.op.log.ErpOperateLog;
import com.bizark.op.api.entity.op.log.ErpOperateLogDTO;
import com.bizark.op.api.entity.op.tk.expert.dto.TKExpertLogDTO;
import com.bizark.op.api.service.sys.ISysDictTypeService;
import com.bizark.op.common.core.domain.sys.SysDictData;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.common.util.UserUtils;
import com.bizark.op.common.util.spring.SpringUtils;
import com.bizark.op.service.mapper.log.ErpOperateLogMapper;
import com.bizark.framework.security.AuthContextHolder;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Description;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【ad_operate_log(广告操作日志表)】的数据库操作Service
 * @createDate 2023-02-28 18:01:35
 */
@Slf4j
@Service
public class ErpOperateLogServiceImpl extends ServiceImpl<ErpOperateLogMapper, ErpOperateLog> implements com.bizark.op.api.service.log.ErpOperateLogService {

    public static final String ID = "id";


    @Autowired
    private ErpOperateLogMapper operateLogMapper;

    @Autowired
    private ThreadPoolTaskExecutor executor;

    @Override
    public List<ErpOperateLog> selectOpLogList(ErpOperateLogDTO operateLog) {
        return operateLogMapper.selectLogList(operateLog);
    }


    @Override
    public <T> void logRecord(Integer userId, String userName, T before, T after, String opName, boolean isInclude, boolean checkNull, String... fields) {
        Class<?> clazz = after.getClass();
        List<String> excludesFields = new ArrayList<>();
        if (fields != null && fields.length > 0) {
            for (String field : fields) {
                // 如果是*则添加全部字段到列表
                if (field.equals("*")) {
                    List<String> fieldNames = Arrays.stream(clazz.getDeclaredFields())
                            .filter(
                                    c-> (!c.isAnnotationPresent(TableField.class)) ||
                                            (c.isAnnotationPresent(TableField.class) && c.getAnnotation(TableField.class).exist())
                            )
                            .map(Field::getName)
                            .collect(Collectors.toList());
                    excludesFields.addAll(fieldNames);
                    break;
                }
                excludesFields.addAll(Arrays.asList(fields));
            }
        }
        try {
            List<Field> logFields = getLogFields(before, after, excludesFields, isInclude, checkNull);
            if (CollectionUtil.isEmpty(logFields)) {
                return;
            }
            invokeLogRecord(before, after, opName, logFields, userId, userName);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @SneakyThrows
    public <T> void logRecord(T before, T after, String opName, boolean isInclude,boolean checkNull, String... fields) {
        // 默认排除字段
        Class<?> clazz = after.getClass();
        List<String> excludesFields = new ArrayList<>();
        if (fields != null && fields.length > 0) {
            for (String field : fields) {
                // 如果是*则添加全部字段到列表
                if (field.equals("*")) {
                    List<String> fieldNames = Arrays.stream(clazz.getDeclaredFields())
                            .filter(
                                    c-> (!c.isAnnotationPresent(TableField.class)) ||
                                            (c.isAnnotationPresent(TableField.class) && c.getAnnotation(TableField.class).exist())
                                    )
                            .map(Field::getName)
                            .collect(Collectors.toList());
                    excludesFields.addAll(fieldNames);
                    break;
                }
                excludesFields.addAll(Arrays.asList(fields));
            }
        }
        String userName = "System";
        Integer userId = 0;
        try {
            AuthUserDetails authUserDetails = AuthContextHolder.getAuthUserDetails();
            userName = authUserDetails.getName();
            userId = authUserDetails.getId();
        } catch (Exception e) {
            log.error("获取当前登录用户失败！");
        }
        String finalUserName = userName;
        Integer finalUserId = userId;
        executor.execute(() -> {
            try {
                List<Field> logFields = getLogFields(before, after, excludesFields, isInclude, checkNull);
                if (CollectionUtil.isEmpty(logFields)) {
                    return;
                }
                invokeLogRecord(before, after, opName, logFields, finalUserId, finalUserName);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

    @Override
    @SneakyThrows
    public <T> void logRecord(Supplier<List<T>> before, List<T> after, String unique, String opName, boolean isInclude,boolean checkNull, String... fields) {
        // 默认排除字段
        List<String> excludesFields = new ArrayList<>();
        if (fields != null && fields.length > 0) {
            excludesFields.addAll(Arrays.asList(fields));
        }
        String userName = "System";
        Integer userId = 0;
        try {
            AuthUserDetails authUserDetails = AuthContextHolder.getAuthUserDetails();
            userName = authUserDetails.getName();
            userId = authUserDetails.getId();
        } catch (Exception e) {
            log.error("获取当前登录用户失败！");
        }
        String finalUserName = userName;
        Integer finalUserId = userId;
        executor.execute(() -> {
            List<T> beforeData = before.get();
            T t = after.get(0);
            Class<?> clazz = t.getClass();
            try {
                Field field = clazz.getDeclaredField(unique);
                field.setAccessible(true);
                Map<String, T> dataMap = beforeData.stream()
                        .collect(Collectors.toMap(k -> {
                            try {
                                return String.valueOf(field.get(k));
                            } catch (IllegalAccessException e) {
                                throw new RuntimeException(e);
                            }
                        }, Function.identity(), (a, b) -> a));

                for (T afterObj : after) {
                    if (!dataMap.containsKey(String.valueOf(field.get(afterObj)))) {
                        log.error("日志记录失败，对象对比失败 ： {}", JSON.toJSONString(afterObj));
                        continue;
                    }
                    T beforeObj = dataMap.get(String.valueOf(field.get(afterObj)));
                    List<Field> logFields = getLogFields(beforeObj, afterObj, excludesFields, isInclude, checkNull);
                    if (CollectionUtil.isEmpty(logFields)) {
                        return;
                    }
                    invokeLogRecord(beforeObj, afterObj, opName, logFields, finalUserId, finalUserName);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        });
    }
    public <T> void logRecord(List<T> before, List<T> after, String unique, String opName, boolean isInclude,boolean checkNull, String... fields) {
        // 默认排除字段
        List<String> excludesFields = new ArrayList<>();
        if (fields != null && fields.length > 0) {
            excludesFields.addAll(Arrays.asList(fields));
        }
        String userName = "System";
        Integer userId = 0;
        try {
            AuthUserDetails authUserDetails = AuthContextHolder.getAuthUserDetails();
            userName = authUserDetails.getName();
            userId = authUserDetails.getId();
        } catch (Exception e) {
            log.error("获取当前登录用户失败！");
        }
        String finalUserName = userName;
        Integer finalUserId = userId;
        executor.execute(() -> {
            T t = after.get(0);
            Class<?> clazz = t.getClass();
            try {
                Field field = clazz.getDeclaredField(unique);
                field.setAccessible(true);
                Map<String, T> dataMap = before.stream()
                        .collect(Collectors.toMap(k -> {
                            try {
                                return String.valueOf(field.get(k));
                            } catch (IllegalAccessException e) {
                                throw new RuntimeException(e);
                            }
                        }, Function.identity(), (a, b) -> a));

                for (T afterObj : after) {
                    if (!dataMap.containsKey(String.valueOf(field.get(afterObj)))) {
                        log.error("日志记录失败，对象对比失败 ： {}", JSON.toJSONString(afterObj));
                        continue;
                    }
                    Object beforeObj = dataMap.get(String.valueOf(field.get(afterObj)));
                    List<Field> logFields = getLogFields(beforeObj, afterObj, excludesFields, isInclude, checkNull);
                    if (CollectionUtil.isEmpty(logFields)) {
                        return;
                    }
                    invokeLogRecord(beforeObj, afterObj, opName, logFields, finalUserId, finalUserName);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        });
    }


    private <T> void invokeLogRecord(T before, T after, String opName, List<Field> logFields, Integer finalUserId, String finalUserName) throws NoSuchFieldException, IllegalAccessException {
        List<ErpOperateLog> logs = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        Date nowDate = DateUtils.getNowDate();
        Class<?> type = after.getClass();
        String table = type.isAnnotationPresent(TableName.class) ?
                type.getAnnotation(TableName.class).value()
                : StrUtil.toUnderlineCase(type.getSimpleName());
        Field idField = type.getDeclaredField(ID);
        idField.setAccessible(true);
        Object id = idField.get(before);
        Map<String, List<SysDictData>> dictMap = selectDict(logFields);
        for (Field logField : logFields) {
            ErpOperateLog opLog = new ErpOperateLog();
            opLog.setOperateTable(table);
            opLog.setOperateAt(now);
            opLog.setOperateName(opName);
            opLog.setLogType(-100);
            opLog.setBusinessId(id != null ? Long.parseLong(String.valueOf(id)) : -1);
            opLog.setOperateUserId(finalUserId.longValue());
            opLog.setOperateUserName(finalUserName);
            Schema schema = logField.getAnnotation(Schema.class);
            if (schema != null) {
                opLog.setFieldDesc(schema.description());
            } else {
                opLog.setFieldDesc(logField.getName());
            }
            opLog.setOperateTarget(logField.getName());
            opLog.setOperateType(1);
            settingOperateValue(logField, dictMap, logField.get(before), logField.get(after), opLog);
            opLog.setCreatedAt(nowDate);
            opLog.setCreatedName(finalUserName);
            opLog.setCreatedBy(finalUserId);
            logs.add(opLog);
        }
        if (CollectionUtil.isNotEmpty(logs)) {
            log.error("插入日志：{}", JSON.toJSONString(logs));
            this.saveBatch(logs);
        }
    }


    public void settingOperateValue(Field logField, Map<String, List<SysDictData>> dictMap, Object beforeData, Object afterData, ErpOperateLog opLog) {
        Class<?> type = logField.getType();
        if (type == Date.class) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if (Objects.nonNull(beforeData)) {
                opLog.setOperateOldValue(dateFormat.format(beforeData));
            }
            if (Objects.nonNull(afterData)) {
                opLog.setOperateOldValue(dateFormat.format(afterData));
            }
            return;
        }
        LogConvert logConvert = logField.getAnnotation(LogConvert.class);
        if (Objects.nonNull(logConvert) && dictMap.containsKey(logConvert.dict())) {
            if (StrUtil.isNotBlank(logConvert.dict()) && dictMap.containsKey(logConvert.dict())) {
                String dict = logConvert.dict();
                List<SysDictData> dictData = dictMap.get(dict);
                for (SysDictData data : dictData) {
                    if (data.getDictValue().equals(String.valueOf(beforeData))) {
                        opLog.setOperateOldValue(data.getDictLabel());
                    }
                    if (data.getDictValue().equals(String.valueOf(afterData))) {
                        opLog.setOperateNewValue(data.getDictLabel());
                    }
                }
            } else {
                String[] value = logConvert.value();
                for (String v : value) {
                    String[] split = v.split(":");
                    if (String.valueOf(beforeData).equals(split[0])) {
                        opLog.setOperateOldValue(Objects.equals(split[1], "null") ? null : split[1]);
                    }
                    if (String.valueOf(afterData).equals(split[0])) {
                        opLog.setOperateNewValue(Objects.equals(split[1], "null") ? null : split[1]);
                    }
                }
            }
        } else {
            opLog.setOperateOldValue(Objects.equals("null", String.valueOf(beforeData)) ? null : String.valueOf(beforeData));
            opLog.setOperateNewValue(Objects.equals("null", String.valueOf(afterData)) ? null : String.valueOf(afterData));
        }
    }


    public Map<String, List<SysDictData>> selectDict(List<Field> fields) {
        Map<String, List<SysDictData>> map = new HashMap<>();
        List<String> dicts = fields.stream()
                .filter(c -> c.isAnnotationPresent(LogConvert.class))
                .map(f -> f.getAnnotation(LogConvert.class).dict())
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(dicts)) {
            ISysDictTypeService dictTypeService = SpringUtils.getBean(ISysDictTypeService.class);
            map = dictTypeService.selectDictDataByTypes(dicts);
        }
        return map;
    }


    public <T> List<Field> getLogFields(T before, T after, List<String> fields, boolean isIncludes,boolean checkNull) {
        List<Field> logFields = new ArrayList<>();
        // 默认排除字段
        List<String> baseExcludes = new ArrayList<>(Arrays.asList(
                "id","createdAt", "createdBy", "createdName", "updatedBy", "serialVersionUID", "updatedAt", "updatedName", "disabledBy", "disabledAt", "disabledName"
        ));
        List<String> customField = new ArrayList<>(fields);
        try {
            Class<?> type = after.getClass();
            Field[] declaredFields = type.getDeclaredFields();
            for (Field field : declaredFields) {
                field.setAccessible(true);
                if (baseExcludes.contains(field.getName())) {
                    continue;
                }
                if (!isIncludes && customField.contains(field.getName())) {
                    continue;
                }
                if (isIncludes && !customField.contains(field.getName())) {
                    continue;
                }
                if (field.isAnnotationPresent(TableField.class) && !field.getAnnotation(TableField.class).exist()) {
                    continue;
                }
                Object beforeData = field.get(before);
                Object afterData = field.get(after);
                // 跳过null值检查
                if ((afterData == null || (afterData instanceof String && StrUtil.isBlank((String) afterData))) && !checkNull) {
                    continue;
                }
                if (beforeData == null && afterData == null) {
                    continue;
                }
                if (field.getType() == Date.class) {
                    if ((beforeData == null && afterData != null) || (beforeData != null && afterData == null)) {
                        logFields.add(field);
                    }
                    if (DateUtil.compare((Date) beforeData, (Date) afterData) != 0) {
                        logFields.add(field);
                    }
                    continue;
                }
                if (field.getType() == Integer.class || field.getType() == Long.class || field.getType() == Double.class || field.getType() == Float.class) {
                    if ((beforeData == null && afterData != null) || (beforeData != null && afterData == null)) {
                        logFields.add(field);
                        continue;
                    }
                    if (!Objects.equals(beforeData, afterData)) {
                        logFields.add(field);
                    }
                    continue;
                }
                if (field.getType() == BigDecimal.class) {
                    if ((beforeData == null && afterData != null) || (beforeData != null && afterData == null)) {
                        logFields.add(field);
                        continue;
                    }
                    if (((BigDecimal) afterData).compareTo((BigDecimal) beforeData) != 0) {
                        logFields.add(field);
                    }
                    continue;
                }

                if (String.valueOf(afterData).equals(String.valueOf(beforeData))) {
                    continue;
                }
                logFields.add(field);
            }
        } catch (IllegalAccessException e) {
            return logFields;
        }
        return logFields;
    }

    @Override
    public List<ErpOperateLog> selectExpertLog(TKExpertLogDTO dto) {
        return operateLogMapper.selectExpertLog(dto);
    }

    @Override
    @SneakyThrows
    public <T> List<ErpLogModify> checkFieldsUpdate(List<String> fields, T before, T after) {
        Class<?> clazz = before.getClass();
        List<ErpLogModify> modifies = new ArrayList<>();
        for (String field : fields) {
            try {
                Field declaredField = clazz.getDeclaredField(field);
                declaredField.setAccessible(true);
                Object beforeValue = declaredField.get(before);
                Object afterValue = declaredField.get(after);
                if (beforeValue == null && afterValue == null) {
                    continue;
                }
                if (beforeValue == null || afterValue == null) {
                    modifies.add(new ErpLogModify(field, beforeValue, afterValue));
                    continue;
                }
                if (declaredField.getType() == BigDecimal.class) {
                    if (((BigDecimal) afterValue).compareTo((BigDecimal) beforeValue) != 0) {
                        modifies.add(new ErpLogModify(field, beforeValue, afterValue));
                    }
                    continue;
                }


                if (declaredField.getType() == Date.class) {
                    if (DateUtil.compare((Date) beforeValue, (Date) afterValue) != 0) {
                        modifies.add(new ErpLogModify(field, beforeValue, afterValue));
                    }
                    continue;
                }


                if (declaredField.getType() == Integer.class || declaredField.getType() == Long.class || declaredField.getType() == Double.class || declaredField.getType() == Float.class) {
                    if (!Objects.equals(beforeValue, afterValue)) {
                        modifies.add(new ErpLogModify(field, beforeValue, afterValue));
                    }
                    continue;
                }

                if (declaredField.getType() == String.class) {
                    if (!String.valueOf(afterValue).equals(String.valueOf(beforeValue))) {
                        modifies.add(new ErpLogModify(field, beforeValue, afterValue));
                    }
                    continue;
                }
            } catch (Exception e) {
                log.error("日志检查异常：{} - {}", field, e.getMessage(), e);
            }

        }
        return modifies;
    }

    @Override
    @SneakyThrows
    public <T> List<ErpLogModify> checkFieldsUpdateToSchema(List<String> fields, T before, T after) {
        Class<?> clazz = before.getClass();
        List<ErpLogModify> modifies = new ArrayList<>();
        for (String field : fields) {
            try {
                Field declaredField = clazz.getDeclaredField(field);
                String operateName = null;
                if (declaredField.isAnnotationPresent(Schema.class)) {
                    Schema annotation = declaredField.getAnnotation(Schema.class);
                    operateName = annotation.name();
                }
                declaredField.setAccessible(true);
                Object beforeValue = declaredField.get(before);
                Object afterValue = declaredField.get(after);
                if (beforeValue == null && afterValue == null) {
                    continue;
                }
                if (beforeValue == null || afterValue == null) {
                    modifies.add(new ErpLogModify(field, operateName, beforeValue, afterValue));
                    continue;
                }
                if (declaredField.getType() == BigDecimal.class) {
                    if (((BigDecimal) afterValue).compareTo((BigDecimal) beforeValue) != 0) {
                        modifies.add(new ErpLogModify(field, operateName, beforeValue, afterValue));
                    }
                    continue;
                }


                if (declaredField.getType() == Date.class) {
                    if (DateUtil.compare((Date) beforeValue, (Date) afterValue) != 0) {
                        modifies.add(new ErpLogModify(field, operateName, beforeValue, afterValue));
                    }
                    continue;
                }


                if (declaredField.getType() == Integer.class || declaredField.getType() == Long.class || declaredField.getType() == Double.class || declaredField.getType() == Float.class) {
                    if (!Objects.equals(beforeValue, afterValue)) {
                        modifies.add(new ErpLogModify(field, operateName, beforeValue, afterValue));
                    }
                    continue;
                }

                if (declaredField.getType() == String.class) {
                    if (!String.valueOf(afterValue).equals(String.valueOf(beforeValue))) {
                        modifies.add(new ErpLogModify(field, operateName, beforeValue, afterValue));
                    }
                    continue;
                }
            } catch (Exception e) {
                log.error("日志检查异常：{} - {}", field, e.getMessage(), e);
            }

        }
        return modifies;
    }

    @Override
    public List<ErpOperateLog> selectLogFixedRows(ErpOperateLogDTO operateLog) {
        return operateLogMapper.selectLogFixedRows(operateLog);
    }


    @Override
    public <T> void recordInsert(T t, String uniqueField) {
        Integer userId = UserUtils.getCurrentUserId(0);
        String userName = UserUtils.getCurrentUserName("System");
        executor.execute(() -> recordInsert(t, userId, userName, null, uniqueField));
    }


    @Override
    public <T> void recordInsertAsync(T t) {
        Integer userId = UserUtils.getCurrentUserId(0);
        String userName = UserUtils.getCurrentUserName("System");
        executor.execute(() -> recordInsert(t, userId, userName, null, null));
    }

    @Override
    public <T> void recordInsert(T t) {
        Integer userId = UserUtils.getCurrentUserId(0);
        String userName = UserUtils.getCurrentUserName("System");
        recordInsert(t, userId, userName, null, null);
    }

    @Override
    public <T> void recordInsert(List<T> t, String uniqueField) {
        recordInsert(t, null, uniqueField);
    }

    @Override
    public <T> void recordInsert(List<T> t) {
        recordInsert(t, null, null);
    }

    @Override
    public <T> void recordInsert(List<T> t, String tableName, String uniqueField) {
        Integer userId = UserUtils.getCurrentUserId(0);
        String userName = UserUtils.getCurrentUserName("System");
        executor.execute(() -> t.forEach(t1 -> recordInsert(t1, userId, userName, tableName, uniqueField)));
    }



    @Override
    public <T> void recordInsert(T t, String tableName, String idField) {
        Integer userId = UserUtils.getCurrentUserId(0);
        String userName = UserUtils.getCurrentUserName("System");
        executor.execute(() -> recordInsert(t, userId, userName, tableName, idField));
    }

    @Override
    public <T> void recordInsert(T t, Integer userId, String userName, String tableName, String idField) {
        Class<?> clazz = t.getClass();
        String filedName = StrUtil.isBlank(idField) ? ID : idField;
        // 获取唯一标识
        try {
            Field field = clazz.getDeclaredField(filedName);
            field.setAccessible(true);
            Object id = field.get(t);
            String table = StrUtil.isNotBlank(tableName) ? tableName
                    : clazz.isAnnotationPresent(TableName.class) ? clazz.getAnnotation(TableName.class).value()
                    : clazz.getSimpleName();
            log.info("record insert log : {}", table);
            String operateName = clazz.isAnnotationPresent(Description.class) ?
                    clazz.getAnnotation(Description.class).value() : table;
            ErpOperateLog operateLog = new ErpOperateLog();
            Date nowDate = DateUtils.getNowDate();
            operateLog.setOperateAt(LocalDateTime.now());
            operateLog.setOperateName(operateName);
            operateLog.setLogType(2);
            operateLog.setOperateUserId(userId.longValue());
            operateLog.setOperateUserName(userName);
            operateLog.setOperateType(LogEnums.OperateTypeEnum.INSERT.getValue());
            operateLog.setCreatedAt(nowDate);
            operateLog.setCreatedName(userName);
            operateLog.setCreatedBy(userId);
            operateLog.setUpdatedName(userName);
            operateLog.setUpdatedBy(userId);
            operateLog.setUpdatedAt(nowDate);
            operateLog.setOperateTable(table);
            operateLog.setBusinessId(Long.parseLong(String.valueOf(id)));
            this.save(operateLog);
        } catch (Exception e) {

        }

    }
    @SneakyThrows
    private <T> Field compareField(Field field, T before, T after) {
        field.setAccessible(true);
        Object beforeData = field.get(before);
        Object afterData = field.get(after);

        if (beforeData == null && afterData == null) {
            return null;
        }

        if (field.getType() == Date.class) {
            if (beforeData == null || afterData == null) {
                return field;
            }
            if (DateUtil.compare((Date) beforeData, (Date) afterData) != 0) {
                return field;
            }
            return null;
        }
        if (field.getType() == Integer.class || field.getType() == Long.class || field.getType() == Double.class || field.getType() == Float.class) {
            if (beforeData == null || afterData == null) {
                return field;
            }
            if (!Objects.equals(beforeData, afterData)) {
                return field;
            }
            return null;
        }

        if (field.getType() == BigDecimal.class) {
            if (beforeData == null || afterData == null) {
                return field;
            }
            if (((BigDecimal) afterData).compareTo((BigDecimal) beforeData) != 0) {
                return field;
            }
            return null;
        }

        if (String.valueOf(afterData).equals(String.valueOf(beforeData))) {
            return null;
        }
        return field;
    }



    @Override
    @SneakyThrows
    public <T> boolean compareBean(T one, T other, String... fields) {
        if (fields == null || fields.length == 0) {
            return getLogFields(one, other, null, false, false).isEmpty();
        }

        Class<?> clazz = one.getClass();
        for (String field : fields) {
            Field declaredField = clazz.getDeclaredField(field);
            if (compareField(declaredField, one, other) != null) {
                return false;
            }
        }
        return true;
    }

}

