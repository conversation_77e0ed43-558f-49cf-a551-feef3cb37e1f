package com.bizark.op.service.service.amazon.fba;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.op.api.entity.op.amazon.fba.MarStaShipmentSkus;
import com.bizark.op.api.entity.op.amazon.fba.VO.MarFnSkuPrintVO;
import com.bizark.op.api.service.amazon.fba.MarStaShipmentSkusService;
import com.bizark.op.service.mapper.amazon.fba.MarStaShipmentSkusMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*
*/
@Service
public class MarStaShipmentSkusServiceImpl extends ServiceImpl<MarStaShipmentSkusMapper, MarStaShipmentSkus>
implements MarStaShipmentSkusService {


    /** 查询FNSKU
     *
     * @param ids
     * @return
     */
    @Override
    public List<MarFnSkuPrintVO.SellerSkuBean> selectPrintFnsku(List<Long> ids) {
        List<MarFnSkuPrintVO.SellerSkuBean> marList = this.baseMapper.selectPrintFnsku(ids);
        return marList;
    }
}
