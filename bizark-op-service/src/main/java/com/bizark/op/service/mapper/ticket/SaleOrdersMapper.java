package com.bizark.op.service.mapper.ticket;

import com.bizark.boss.api.entity.dashboard.order.SaleOrderDetailEntity;
import com.bizark.boss.api.entity.dashboard.order.SaleOrderLogEntity;
import com.bizark.op.api.entity.op.mar.MarProductPopularizeLog;
import com.bizark.op.api.entity.op.order.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bizark.op.api.entity.op.order.vo.DeliverOrderVo;
import com.bizark.op.api.entity.op.sale.AmzOrderV;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.entity.op.stat.StatOrderSalesQuery;
import com.bizark.op.api.entity.op.ticket.ScTicket;
import com.bizark.op.api.entity.op.ticket.TicketMsgCardInfo;
import com.bizark.op.api.entity.op.ticket.TicketMsgCardOrderInfo;
import com.bizark.op.api.entity.op.ticket.VO.SaleOrderVoTicket;
import com.bizark.op.api.entity.op.ticket.VO.SaleOrdersVO;
import com.bizark.op.api.entity.op.ticket.VO.ScManualOrderVO;
import com.bizark.op.api.entity.op.ticket.VO.TicketMatchOrderVO;
import com.bizark.op.api.entity.op.tk.expert.dto.CreatorRelationSampleDTO;
import com.bizark.op.api.entity.op.tk.expert.vo.CreatorRelationSampleVO;
import com.bizark.op.api.vo.refund.OrderRefundReqVO;
import org.apache.ibatis.annotations.Param;
import org.redisson.misc.Hash;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @Entity generator.domain.SaleOrders
 */
public interface SaleOrdersMapper extends BaseMapper<SaleOrders> {


    /**
     * @param
     * @param orderId
     * @description:根据订单ID获取订单需要信息
     * @author: Moore
     * @date: 2023/11/27 11:16
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.VO.SaleOrderVoTicket>
     **/
    List<SaleOrderVoTicket> selectOrderByOrderId(Long orderId);


    /**
     * @param
     * @param orderId
     * @description:根据订单ID预估物流费
     * @author: Moore
     * @date: 2023/11/27 11:16
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.VO.SaleOrderVoTicket>
     **/
    List<SaleOrderVoTicket> selectOrderEstimateShipCost(Long orderId);


    /**
     * @param
     * @param orderNo 订单号
     * @param customerId 客户ID
     * @param customerId 渠道
     * @description: 获取客户下所有TIktok订单
     * @author: Moore
     * @date: 2023/10/22 22:45
     * @return: java.util.List<com.bizark.op.api.entity.op.order.SaleOrders>
     **/
    List<SaleOrders> selectOrderNoByOuterId(@Param("orgId") Integer orgId, @Param("orderNo") String orderNo,@Param("customerId") String  customerId,@Param("channel") String channel);


    /**
     * @description:根据订单号，关联查询客户信息及对应商品，销量，销售额信息
     * @author: Moore
     * @date: 2023/10/23 0:24
     * @param
     * @param orderNo
     * @return: com.bizark.op.api.entity.op.ticket.ScManualOrder
     **/
    List<ScManualOrderVO> selectSaleOrderAndCustomerByOrderNo(@Param("orderNo") String orderNo,@Param("orgId") Integer orgId,@Param("channel") String channel);


    /**
     * @description: 根据订单主键查询客户及订单信息
     * @author: Moore
     * @date: 2023/11/27 10:32
     * @param
     * @param orderId
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.VO.ScManualOrderVO>
     **/
    List<ScManualOrderVO> selectSaleOrderAndCustomerByOrderId(Long orderId);


    String getCustomerNameByOrderId(@Param("orderId") String orderId);

    Integer getCustomerIdByOrderId(@Param("orderId") String orderId);

    OrderRefundReqVO getBaseMsgByOrderId(@Param("orderId")String orderId);

    Long getAccountIdByOrderId(@Param("orderId")String orderId);

    ScTicket getBaseMsg();

    /**
     * 通过订单号查询订单
     *
     * @param orgId
     * @param orderNo
     * @return
     */
    SaleOrders selectScOrderByAmazonOrderId(@Param("orgId") Long orgId, @Param("orderNo") String orderNo);




    /**
     * @description: 查询订单信息
     * @author: Moore
     * @date: 2023/11/29 17:10
     * @param
     * @param saleOrders
     * @return: java.util.List<com.bizark.op.api.entity.op.order.SaleOrders>
    **/
    List<SaleOrders> selectSaleOrderList(SaleOrders saleOrders);

    /**
     * @description: 查询订单信息
     * @author: Moore
     * @date: 2023/11/29 17:10
     * @param
     * @param saleOrdersMatch
     * @return: java.util.List<com.bizark.op.api.entity.op.order.SaleOrders>
     **/
    List<SaleOrdersMatch> selectSaleOrderMatchList(SaleOrdersMatch saleOrdersMatch);


    /**
     * @description: 根据订单查询仅客户信息
     * @author: Moore
     * @date: 2023/10/30 9:16
     * @param
     * @param amazonOrderId
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.VO.ScManualOrderVO>
     **/
    List<ScManualOrderVO> selectSaleOrderAndCustomerInfoByOrderNo(String amazonOrderId);

    ProductChannels getProductChannelBySkuId(@Param("sellerSku") String sellerSku, @Param("accountFlag") String accountFlag);
    String getErpSkuChannelBySkuId(@Param("sellerSku") String sellerSku, @Param("accountFlag") String accountFlag);

    BigDecimal getTotalAmount(@Param("orderId")String orderId, @Param("sellerSku")String sellerSku);


    /**
     * @description: 根据订单号查询客户及订单信息
     * @author: Moore
     * @date: 2023/11/9 0:08
     * @param
     * @param orderNo 平台订单号
     * @return: com.bizark.op.api.entity.op.ticket.VO.SaleOrdersVO
     **/
    SaleOrdersVO selectSaleOrderByCustomer(String orderNo);

    List<Long> selectByCreatedAtRange(@Param("startDate") String startDate, @Param("endDate")String endDate, @Param("orgId")Long orgId);

    SaleOrders selectSaleOrderByOrderNo(String orderNo);

    /**
     * @description: 根据订单号查询工单主表
     * @author: Moore
     * @date: 2023/12/7 17:29
     * @param
     * @param orderId
     * @return: com.bizark.op.api.entity.op.order.SaleOrders
     **/
    SaleOrders selectOrderById(@Param("orderId") Long orderId);

    /**
     * @description: 根据订单号查询工单主表
     * @author: Moore
     * @date: 2023/12/7 17:29
     * @param
     * @param
     * @return: com.bizark.op.api.entity.op.order.SaleOrders
     **/
    SaleOrders selectOrderByOrderNoAndchannel(@Param("orderNo") String orderNo,@Param("channel") String channel);

    SaleOrders selectOrderByIdAndNonDisable(@Param("orderId") Long orderId);

    List<SaleOrders> selectOrderByIdsAndNonDisable(@Param("orderIdList") List<Long> orderIdList);

    /**
     * @description: 根据用户ID获取订单信息，会话工单匹配订单使用
     * @author: Moore
     * @date: 2024/1/3 10:53
     * @param
     * @param customerOuterId
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.VO.SaleOrderVoTicket>
    **/
    List<TicketMatchOrderVO> selectOrderAndProductByCustomerOuterId(@Param("customerOuterId") String  customerOuterId);


    /**
     * Description: 通过orderId 查询SaleOrderDetail
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/1/12
     */
    String getBuyNameByOrderId(@Param("orderId") Long  orderId);

    /**
     * @description: 根据邮件获取订单信息，站内信Email会话工单匹配订单使用
     * @author: Moore
     * @date: 2024/5/27 10:12
     * @param
     * @param email
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.VO.TicketMatchOrderVO>
     **/
    List<TicketMatchOrderVO> selectOrderAndProductByEmail(@Param("email") String email);


    /**
     * @description: 订单主键获取明细信息
     * @author: Moore
     * @date: 2024/5/27 10:12
     * @param
     * @param orderId 订单主键
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.VO.TicketMatchOrderVO>
     **/
    List<TicketMatchOrderVO> selectOrderItemByOrderId(@Param("orderId") Long orderId);


    /**
     * @description: ASIN 推广日志查询
     * @author: Moore
     * @date: 2024/1/17 16:25
     * @param
     * @param statOrderSalesQuery
     * @return: java.util.List<com.bizark.op.api.entity.op.order.SaleOrders>
     **/
    List<MarProductPopularizeLog> selectPopularizeLog(StatOrderSalesQuery statOrderSalesQuery);



    /**
     * @description: ASIN推广日志复制功能
     * @author: Moore
     * @date: 2024/1/17 17:39
     * @param
     * @param  statOrderSalesQuery 查询参数
     * @return: java.util.List<com.bizark.op.api.entity.op.mar.MarProductPopularizeLog>
     **/
    List<AmzOrderV> selectPopularizeLogItem(StatOrderSalesQuery statOrderSalesQuery);

    List<CreatorRelationSampleVO> selectCreatorSampleOrder(CreatorRelationSampleDTO dto);
    List<CreatorRelationSampleVO> selectCreatorSampleOrderByCreatorIds(@Param("creatorCids")List<String> creatorCids);

    CreatorRelationSampleVO selectCommissonByCreatorCid(@Param("creatorCid") String creatorCid);

    SaleOrders selectSampleOrderByCreatorCid(@Param("creatorCid")String creatorCid);

    int updatePriceById(@Param("id") Integer id,@Param("price") BigDecimal price);

    String getMaxUpdateDt();


    List<Long> getByUpdateDtRange(@Param("orgId") Integer orgId, @Param("startDt") String startDt, @Param("endDt")String endDt);

    int updateDiscountPrice(@Param("asin") String asin, @Param("accountFlag") String accountFlag,@Param("listingPrice") BigDecimal listingPrice);

    List<SaleOrders> selectOrderByAccountFlagAndOrderNos(@Param("accountFlag") String accountFlag, @Param("orderNos") List<String> orderNos);

    List<Long> selectByOrderNos(@Param("orderNos") List<String> orderNos);

    String selectMaxCreateDate(@Param("orgId") Integer orgId);

    List<SaleOrders> selectOrderByCreateAt(@Param("orgId")Integer orgId,@Param("createAt") String createAt);


    SaleOrderDetailEntity selectOrderDetailById(@Param("orderId") Long orderId);

    SaleOrders  selectSaleOrderByOrderNoAndShopId(@Param("orderNo") String orderNo, @Param("shopId") long shopId);
    List<SaleOrders> selectSaleOrderByOrderNoAndStoreName(@Param("orderNo") String orderNo, @Param("storeName") String storeName);
    List<SaleOrders> selectSaleOrderByOrderNoAndSellerId(@Param("orderNo") String orderNo, @Param("sellerId") String sellerId);
    List<SaleOrders> selectSaleOrderByOrderNoAndFlag(@Param("orderNo") String orderNo, @Param("flag") String flag);

    List<Long> selectOrderByUst(@Param("orgId")Integer orgId,@Param("createAt") String createAt,@Param("end") String end);

    List<SaleOrders> selectOrderByPstNotShippingSubsidy(@Param("orgId")Integer orgId,@Param("channel")String channel,@Param("createAt") String createAt,@Param("end") String end);

    List<SaleOrders> selectOrderByPstShippingSubsidyAll(@Param("orgId")Integer orgId,@Param("channel")String channel,@Param("createAt") String createAt,@Param("end") String end);


    List<SaleOrders> selectOrderSkuByAccountFlagAndOrderNos(@Param("accountFlag") String flag, @Param("orderNos") List<String> orderNos);


    /**
     * 查询订单及订单明细信息
     *
     * @param saleOrders
     * @return
     */
    List<SaleOrderVoTicket> selectOrderItems(SaleOrders saleOrders);

    /**
     * @param
     * @param shopId
     * @param orderNos
     * @description: 根据店铺ID及订单号查询订单明细信息
     * @author: Moore
     * @date: 2024/7/31 16:59
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.VO.SaleOrderVoTicket>
     **/
    List<SaleOrderVoTicket> selectOrderItemsByAccounId(@Param("shopId") Long shopId, @Param("orderNos") List<String> orderNos);


    List<Long> selectOrderDetailByUpdateAndWalmart(@Param("orgId") Integer orgId, @Param("startDt") String startDt, @Param("endDt")String endDt);


    /**
     * @param
     * @param sellerSkus
     * @param purchaseDateFrom
     * @param purchaseDateTo
     * @description: 根据SelelrSku查询订单明细信息,只查询促销费为0的订单行
     * @author: Moore
     * @date: 2024/9/3 10:38
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.VO.SaleOrderVoTicket>
     **/
    List<SaleOrderItemPromotionPriceVO> selectOrderItemBySellerSku(@Param("sellerSkus") List<String> sellerSkus, @Param("purchaseDateFrom") String purchaseDateFrom, @Param("purchaseDateTo") String purchaseDateTo);




    /**
     * @param
     * @param sellerSkus
     * @param purchaseDateFrom
     * @param purchaseDateTo
     * @description: 根据SelelrSku查询订单明细信息,只查询促销费为0的订单行
     * @author: Moore
     * @date: 2024/9/3 10:38
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.VO.SaleOrderVoTicket>
     **/
    List<SaleOrderItemPromotionPriceVO> selectOrderItemBySellerSkuCoupon(@Param("sellerSkus") List<String> sellerSkus, @Param("purchaseDateFrom") String purchaseDateFrom, @Param("purchaseDateTo") String purchaseDateTo);

    /**
     * @param
     * @param promotionPrice  促销费
     * @param promotionPriceItemAmount itemamount-促销费
     * @param oriItemAmount 原始销售额
     * @param itemId  行ID
     * @description: 更新促销费费信息
     * @author: Moore
     * @date: 2024/9/3 10:28
     * @return: void
     **/
    void udpateOrderItemsPromotionDiscountPrice(@Param("promotionPrice") BigDecimal promotionPrice,
                                                @Param("promotionPriceItemAmount") BigDecimal promotionPriceItemAmount,
                                                @Param("oriItemAmount") BigDecimal oriItemAmount,
                                                @Param("id") Long itemId,@Param("flagRemark") String flagRemark);


    /**
     * 更新亚马逊VINE费用
     *
     * @param vinePrie vine金额信息
     * @param itemId 明细行ID
     */
    void udpateOrderItemsVineFee(@Param("vinePrice") BigDecimal vinePrie,
                                                @Param("id") Long itemId);



    /**
     * 更新亚马逊VINE费用
     *
     * @param vinePrie vine金额信息
     * @param itemId 明细行ID
     */
    void udpateOrderItemsVineFeeReduce(@Param("vinePrice") BigDecimal vinePrie,
                                 @Param("id") Long itemId);

    /**
     * @description:
     * @author: Moore
     * @date: 2024/12/18 10:18
     * @param
     * @param itemAmount 订单金额
     * @param oriItemAmount 原始订单金额
     * @param itemId 订单行ID
     * @param promotionDiscountPrice 折扣金额
     * @return: void
    **/
    void udpateOrderItemsPromotionCouponPrice(@Param("itemAmount") BigDecimal itemAmount,
                                              @Param("oriItemAmount") BigDecimal oriItemAmount,
                                              @Param("promotionDiscountPrice") BigDecimal promotionDiscountPrice,
                                              @Param("id") Long itemId);



    /**
     * @param
     * @param sellerSkus
     * @param purchaseDateFrom
     * @param purchaseDateTo
     * @description: 根据SelelrSku查询订单明细信息
     * @author: Moore
     * @date: 2024/9/3 10:38
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.VO.SaleOrderVoTicket>
     **/
    List<SaleOrderItemPromotionPriceVO>  selectOrderItemByPromotionPrice(@Param("sellerSkus") List<String> sellerSkus, @Param("purchaseDateFrom") String purchaseDateFrom, @Param("purchaseDateTo") String purchaseDateTo);

    List<SaleOrders> selectOrderByOrderNos(@Param("orderNos") List<String> orderNos,@Param("shopId") Integer shopId);
    List<DeliverOrderVo> selectOrderByPackageSn(@Param("packageSns") List<String> packageSns,@Param("orgId") Integer orgId);
    /**
     * @description:订单包裹信息
     * @author: Moore
     * @date: 2024/10/17 16:36
     * @param
     * @param orderId
     * @param trackNo
     * @return: java.util.List<com.bizark.op.api.entity.op.order.SaleShipmentPackagesVO>
    **/
    List<SaleShipmentPackagesVO> selectOrderShipmentPackagesInfo(@Param("headId") Long orderId,@Param("trackNo") String trackNo);


    /**
     * @description: 订单发货信息
     * @author: Moore
     * @date: 2024/12/6 10:30
     * @param
     * @param orderId
     * @return: java.util.List<com.bizark.op.api.entity.op.order.SaleShipmentPackagesVO>
     **/
    List<SaleShipmentPackagesVO> selectOrderShipmentInfo(@Param("headId") Long orderId);


    /**
     * @description: 订单发货信息
     * @author: Moore
     * @date: 2024/12/6 10:30
     * @param orderNos 订单号
     * @param orgId 组织ID
     * @param headId 订单表主键
     *
     * @return: java.util.List<com.bizark.op.api.entity.op.order.SaleShipmentPackagesVO>
     **/
    List<TicketMsgCardInfo.TrackInfo> selectOrderCardInfoShipmentInfo(@Param("orgId") Long orgId, @Param("orderNos") List<String> orderNos, @Param("headId") Long headId);


    List<TicketMsgCardInfo.SkuInfo> selectOrderCardInfo(@Param("orgId") Long orgId, @Param("orderNos") List<String> orderNos, @Param("shopId") Long shopId,@Param("orderId") Long orderId);


    /**
     * 根据 tiktok会话用户ID 获取对应订单信息
     *
     * @param outerId
     * @return
     */
    List<TicketMsgCardOrderInfo> selectTikcetConversationOrderList(@Param("outerId") String outerId, @Param("shopId") Long shopId,@Param("channelStatus")List<Long> channelStatus, @Param("orderNo")String orderNo);


    /** 查询客户ID
     *
     * @param contextId
     * @param channelId
     * @param orderNo
     */
    SaleOrderVoTicket selectScOrderCustomerOuterId(@Param("orgId") Integer orgId,@Param("channelId")  Integer channelId,@Param("orderNo") String orderNo);


    void updateOrderStatus(@Param("id") Long id, @Param("channelStatus") Integer channelStatus, @Param("orderStatus") Integer orderStatus, @Param("shipmentDate") LocalDateTime shipmentDate);

    void insertSaleOrderLog(SaleOrderLogEntity saleOrderLogEntity);



    /**
     * @param
     * @param asins
     * @param purchaseDateFrom
     * @param purchaseDateTo
     * @description: 根据asin查询订单明细信息,只查询未计算vin费的行
     * @author: Moore
     * @date: 2024/9/3 10:38
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.VO.SaleOrderVoTicket>
     **/
    List<SaleOrderItemVinePriceVO> selectOrderItemByAsinAmzVine(@Param("asins") List<String> asins,
                                                                @Param("shopId") Long shopId,
                                                                @Param("purchaseDateFrom") String purchaseDateFrom, @Param("purchaseDateTo") String purchaseDateTo);
}




