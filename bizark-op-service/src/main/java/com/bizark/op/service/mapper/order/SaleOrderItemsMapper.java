package com.bizark.op.service.mapper.order;

import com.bizark.op.api.dto.apportion.QuerySellerSkuToOrderDTO;
import com.bizark.op.api.entity.op.order.SaleOrderItemVO;
import com.bizark.op.api.entity.op.order.SaleOrderItems;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bizark.op.api.vo.order.SaleOrderItemPromotionVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Entity generator.domain.SaleOrderItems
 */
public interface SaleOrderItemsMapper extends BaseMapper<SaleOrderItems> {



    /*更新折后价格*/
    int updateDiscountPrice(@Param("asin") String asin, @Param("discountPrice") BigDecimal discountPrice, @Param("usTime") String usTime);

    /*更新运费折扣*/
    void updateShippingSubsidy(@Param("id") Integer id, @Param("shippingSubsidy") BigDecimal shippingSubsidy);


    List<SaleOrderItems> selectByOrgIdAndOrderNo(@Param("orgId") Integer orgId, @Param("orderNo") String orderNo);

    List<SaleOrderItemVO> selectByOrderNoAndParentAsin(@Param("orderNo") String orderNo,@Param("parentAsin") String parentAsin,@Param("shopId") Integer shopId);

    List<SaleOrderItems> selectByHeadId(@Param("headId") Long headId);

    List<SaleOrderItems> selectByHeadIds(@Param("headIds") List<Long> headIds);

    List<SaleOrderItems> selectByHeadIdLimitNumber(@Param("headId") Long headId);


    int selectOrderCountByOrderDateAndPstChannelDate(@Param("startDate") String startDate, @Param("endDate") String endDate);


    List<Long> selectOrderIdByOrderDateAndPstChannelDate(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 查询部门根据订单号
     * @param orderNo 订单号
     * @return 部门ID
     */
    Long selectDeptIdByOrderNo(String orderNo);


    List<SaleOrderItemPromotionVO> querySaleOrderItemPromotionList(QuerySellerSkuToOrderDTO querySellerSkuToOrderDTO);

    List<SaleOrderItemPromotionVO> querySaleOrderItemPromotionBySellerSkuList(QuerySellerSkuToOrderDTO querySellerSkuToOrderDTO);

    List<SaleOrderItems> selectOrderByTemuData(@Param("orgId") Integer orgId,@Param("accountId") Integer accountId,@Param("sellerSku") String sellerSku, @Param("skuId") String skuId);

    int updateSaleOrderItemsPriceById(@Param("id") Integer id,@Param("price") BigDecimal price);

    /**
     * 根据采购单号和asin查询
     * @param orderNo
     * @param asin
     * @param contextId
     * @return
     */
    List<SaleOrderItems> selectSaleOrderItemsByOrderNoAndAsin(@Param("orderNo") String orderNo, @Param("asin") String asin, @Param("contextId") Integer contextId);

    /*根据订单id和组织id查询订单项*/
    List<SaleOrderItems> selectByHeadIdAndOrgId(@Param("headId") Integer headId, @Param("orgId") Integer orgId);


    /**
     * @param
     * @description: 查询订单明细行信息
     * @author: Moore
     * @date: 2024/4/22 14:35
     * @return: java.util.List<com.bizark.op.api.entity.op.order.SaleOrderItems>
     **/
    List<SaleOrderItemVO> selectOrderitemInfoList(SaleOrderItems saleOrderItems);


    List<SaleOrderItemVO> selectOrderShipmentInfo(@Param("orderId") Long orderId);

    /*根据订单id和组织id查询订单项*/
    List<SaleOrderItems> selectByOrderNoAndShopId(@Param("orgId") Long orgId,@Param("orderNo") String orderNo, @Param("shopId") Long shopId,@Param("poNumber") String poNumber);


    /**
     * 根据SellerSKu 及渠道获取对应销量及销售额
     *
     * @param sellerSKu
     * @param channel
     * @return
     */
    List<SaleOrderItems> selectQuantityAndItemAmount(@Param("sellerSkuList") List<String> sellerSKu, @Param("channel") String channel,
                                                     @Param("purchaseDateFrom") Date fromDate, @Param("purchaseDateTo")  Date toDate);


    /**
     * Description: 根据channelOrderItemId 查询出qty,skuId,
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2025/2/11
     */
    List<SaleOrderItemVO> selectQuantityAndSkuIdByParam(@Param("channelOrderItemId") String channelOrderItemId);
}




