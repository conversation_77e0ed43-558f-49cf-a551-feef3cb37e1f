package com.bizark.op.service.service.ticket;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bizark.common.exception.CommonException;
import com.bizark.op.api.cons.ScTicketConstant;
import com.bizark.op.api.enm.config.SystemGlobalConfigEnum;
import com.bizark.op.api.enm.sale.AccountSaleChannelEnum;
import com.bizark.op.api.entity.op.order.SaleOrders;
import com.bizark.op.api.entity.op.order.SaleOrdersMatch;
import com.bizark.op.api.entity.op.refund.OrderRefund;
import com.bizark.op.api.entity.op.refund.OrderRefundItem;
import com.bizark.op.api.entity.op.ticket.*;
import com.bizark.op.api.entity.op.ticket.VO.ScManualOrderVO;
import com.bizark.op.api.service.conf.SystemGlobalConfigService;
import com.bizark.op.api.service.customer.ICusCenterOffsiteService;
import com.bizark.op.api.service.product.SkuChildrenService;
import com.bizark.op.api.service.refund.IOrderRefundItemService;
import com.bizark.op.api.service.refund.IOrderRefundService;
import com.bizark.op.api.service.ticket.*;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.service.mapper.ticket.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 手工订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-11
 */
@Service
public class ScManualOrderServiceImpl implements IScManualOrderService {


    @Autowired
    @Lazy
    private IScCustomerService scCustomerService;


    @Autowired
    @Lazy
    private IScTicketService ticketService;


    @Autowired
    private SaleOrdersMapper saleOrdersMapper;



    @Autowired
    private ScCustomerMapper scCustomerMapper;

    @Autowired
    private IScTicketLogService scTicketLogService;

    @Autowired
    private ScTicketMapper scTicketMapper;

    @Resource
    @Lazy
    private IOrderRefundService orderRefundService;

    @Resource
    @Lazy
    private IOrderRefundItemService orderRefundItemService;

    @Autowired
    private ScOrderReissueMapper scOrderReissueMapper;

    @Autowired
    private ScReissueAccessoriesMapper scReissueAccessoriesMapper;

    @Autowired
    private ScReissueShipmentMapper scReissueShipmentMapper;

    @Autowired
    private ScReissueGoodsMapper scReissueGoodsMapper;


    @Autowired
    private SystemGlobalConfigService systemGlobalConfigService;

    @Autowired
    @Lazy
    private ICusCenterOffsiteService iCusCenterOffsiteService;



    /**
     * 匹配订单的订单号下拉框
     *
     * @param orderNo
     * @param ticketId
     * @param orgId    组织ID
     * @return
     */
    @Override
    public List<SaleOrdersMatch> manualOrderNoSelect(Integer orgId, String orderNo, Long ticketId, String poNumber,String email) {
        if (StringUtils.isEmpty(orderNo) && StringUtils.isEmpty(poNumber)&&StringUtils.isEmpty(email)) {
            throw new CommonException("缺少必要查询条件");
        }

        if ( null == ticketId) {
            throw new CommonException("工单获取失败!");
        }

        ScTicket scTicket = ticketService.selectScTicketByBaseTicketId(ticketId);
        if (null == scTicket) {
            throw new CommonException("无对应工单信息!");
        }
        SaleOrdersMatch saleOrdersUpate = new SaleOrdersMatch();
        saleOrdersUpate.setOrgId(orgId.longValue());
        saleOrdersUpate.setOrderNo(orderNo); //订单号
        saleOrdersUpate.setPoNumber(poNumber); //客户单号
        saleOrdersUpate.setChannelId(scTicket.getShopId());
        saleOrdersUpate.setEmail(email);//邮箱
        List<SaleOrdersMatch> saleOrdersDB = saleOrdersMapper.selectSaleOrderMatchList(saleOrdersUpate);
        return saleOrdersDB;
    }


    /**
     * @param orgId   组织ID
     * @param orderId 订单主键
     * @description: 有匹配订单查询客户相关信息
     * @author: Moore
     * @date: 2023/10/23 0:22
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.ScManualOrder>
     **/
    @Override
    public List<ScManualOrderVO> getMatchOrderNO(Integer orgId, Long orderId) {
        if (orderId == null) {
            throw new CustomException("订单号为空");
        }
        //根据订单号获取用户信息
        List<ScManualOrderVO> scManualOrderVOS = saleOrdersMapper.selectSaleOrderAndCustomerByOrderId(orderId);
        return scManualOrderVOS;
    }


    /**
     * @param
     * @param scManualOrderVO
     * @description: 保存匹配订单信息
     * @author: Moore
     * @date: 2023/10/23 9:41
     * @return: void
     **/
    @Transactional
    @Override
    public void updateMatchOrder(ScManualOrderVO scManualOrderVO) {
        if (StringUtils.isEmpty(scManualOrderVO.getOrderNo()) || scManualOrderVO.getId() == null) {
            throw new CommonException("订单号不能为空!");
        }

        if (null == scManualOrderVO.getTicketId()) {
            throw new CommonException("工单ID不能为空!");
        }


        ScTicket scTicket = ticketService.selectScTicketByBaseTicketId(scManualOrderVO.getTicketId());
        if (null == scTicket) {
            throw new CommonException("无对应工单信息!");
        }

        //更新操作
        if ("1".equals(scManualOrderVO.getUpdateFlag())) {
            //todo 判断是不是有保存站内退款，站外退款信息
            LambdaQueryWrapper<OrderRefund> lambdaQueryWrapper = new LambdaQueryWrapper<OrderRefund>().eq(OrderRefund::getParentTicketId, scManualOrderVO.getTicketId()).isNull(OrderRefund::getTicketId);
            List<OrderRefund> orderRefundList = orderRefundService.list(lambdaQueryWrapper);
            if (CollectionUtil.isNotEmpty(orderRefundList)) {
                OrderRefund orderRefund = orderRefundList.get(0);
                QueryWrapper<OrderRefundItem> orderRefundItemQueryWrapper = new QueryWrapper<>();
                orderRefundItemQueryWrapper.eq("order_refund_id", orderRefund.getId());
                orderRefundItemService.remove(orderRefundItemQueryWrapper);
                orderRefundService.removeById(orderRefund.getId());
            }
            //todo 判断是不是有保存补发配件，补发产品
            List<ScOrderReissue> scOrderReissues = scOrderReissueMapper.selectScOrderReissueByTicketId(scManualOrderVO.getTicketId());
            scOrderReissues = scOrderReissues.stream().filter(item -> "new".equalsIgnoreCase(item.getStatus())).collect(Collectors.toList());
            for (ScOrderReissue orderReissue : scOrderReissues) {
                scOrderReissueMapper.deleteScOrderReissueById(orderReissue.getId());
                if ("ACCESSORIES".equals(orderReissue.getReissueType())) {//删除补发配件
                    scReissueAccessoriesMapper.deleteScReissueAccessoriesByReissueId(orderReissue.getId());
                } else if ("SHIPMENT".equals(orderReissue.getReissueType())) {   //补发发货
                    scReissueShipmentMapper.deleteScReissueShipmentByReissueId(orderReissue.getId());
                } else { //补发产品
                    scReissueGoodsMapper.deleteScReissueGoodsByReissueId(orderReissue.getId());
                }
            }
            //清空匹配相关  工单信息
            scTicketMapper.updateManualMatchScTicketById(scManualOrderVO.getTicketId());
        }

        String oldAmazonOrderId = "";
        oldAmazonOrderId = scTicket.getAmazonOrderId();
        if (ScTicketConstant.MATCH_TYPE_AUTO != scManualOrderVO.getMatchType()) {
            throw new CommonException("匹配类型非系统订单!");
        }
        ScCustomer scCustomer = new ScCustomer();
        scCustomer.setAmzCustomerId(scManualOrderVO.getCustomerOuterId()); //TK用户唯一标识
        scCustomer.setRecipientName(scManualOrderVO.getCustomerName()); //收件人姓名
        scCustomer.setCustomerName(scManualOrderVO.getCustomerName()); //客户名
        scCustomer.setEmail(scManualOrderVO.getEmail());//邮箱
        scCustomer.setPhone(scManualOrderVO.getPhone());//手机
        scCustomer.setCountryCode(scManualOrderVO.getCountryCode());//国家
        scCustomer.setCountry(scManualOrderVO.getCountryCode());
        scCustomer.setCity(scManualOrderVO.getCity());//城市
        scCustomer.setStateOrRegion(scManualOrderVO.getState()); //州或地区
        scCustomer.setAddressLine1(scManualOrderVO.getAddressline1()); //地址1
        scCustomer.setAddressLine2(scManualOrderVO.getAddressline2());//地址2
        scCustomer.setAddressLine3(scManualOrderVO.getAddressline3());//地址3
        scCustomer.setPostalCode(scManualOrderVO.getPostalCode());//邮政编码
        scCustomer.settingDefaultCreate();
        scCustomerService.insertScCustomer(scCustomer);

        //更新 工单订单号信息，并设置订单号
        ScTicket ticket = new ScTicket();
        ticket.setId(scManualOrderVO.getTicketId());
        ticket.setAmazonOrderId(scManualOrderVO.getOrderNo()); //订单号
        ticket.setOrderId(scManualOrderVO.getId()); //订单ID
        if (ticket.getOrderId() != null) {   //渠道
            SaleOrders saleOrders = saleOrdersMapper.selectById(ticket.getOrderId());
            ticket.setTicketSource(saleOrders != null ? saleOrders.getChannel() : null);
        }
        ticket.setSellerSku(scManualOrderVO.getSellerSku()); //sellersku
        ticket.setAsin(scManualOrderVO.getAsin()); //asin
        ticket.setGoodsSku(scManualOrderVO.getErpSku());//erpsku
        if (systemGlobalConfigService.isEnableConfig(scManualOrderVO.getOrgId().intValue(), SystemGlobalConfigEnum.SKU_VERSION_INIT_CONFIG)) {
            ticket.setParentSku(StringUtil.isNotEmpty(scManualOrderVO.getParentSku()) ? scManualOrderVO.getParentSku() : null);
            //其他站外信，回写对应源数据表(异步)
            if (ScTicketConstant.TICKET_TYPE_OFFSITE_LETTER.equalsIgnoreCase(scTicket.getTicketType())) {
                iCusCenterOffsiteService.updateSkuAndParentSku(ticket.getGoodsSku(), ticket.getParentSku(), scTicket.getSourceId());
            }
        }
        ticket.setCustomerId(scCustomer.getCustomerId());
        ticket.setMatchOrderType(ScTicketConstant.MATCH_TYPE_AUTO);
        ticket.setShopId(scManualOrderVO.getShopId()); //始终跟随匹配的订单类型
//        if (ScTicketConstant.TICKET_TYPE_OFFSITE_LETTER.equalsIgnoreCase(scTicket.getTicketType()) || ScTicketConstant.TICKET_TYPE_EMAIL.equalsIgnoreCase(scTicket.getTicketType())) { //其他站外信更新店铺ID
//            ticket.setShopId(scManualOrderVO.getShopId());
//        }
        ticket.settingDefaultUpdate();
        ticketService.updateScTickeByTicketId(ticket); //更新订单号

        //添加日志
        ScTicketLog ticketLog = new ScTicketLog();
        ticketLog.setTicketId(scTicket.getId());
        ticketLog.setOperateType("Match");
        ticketLog.setOperateTime(new Date());
        if ("1".equals(scManualOrderVO.getUpdateFlag())) {
            ticketLog.setOperateContent("修改自动匹配订单,订单号由" + oldAmazonOrderId + "变成" + scTicket.getAmazonOrderId());
        } else {
            ticketLog.setOperateContent("自动匹配订单");
        }
        scTicketLogService.insertScTicketLog(ticketLog);

    }


    /**
     * @description: 非弹窗保存工单信息(站内信工单保存匹配订单)
     * @author: Moore
     * @date: 2024/1/4 0:16
     * @param
     * @param scManualOrderVOreq
     * @return: void
    **/
    @Override
    @Transactional
    public void updateMatchTikTokOrder(ScManualOrderVO scManualOrderVOreq) {
        if (StringUtils.isEmpty(scManualOrderVOreq.getOrderNo()) || scManualOrderVOreq.getId() == null) {
            throw new CommonException("订单号不能为空!");
        }
        if (null == scManualOrderVOreq.getTicketId()) {
            throw new CommonException("工单ID不能为空!");
        }

        ScTicket ticket = new ScTicket();
        //客户信息绑定(根据订单号获取用户信息)
        List<ScManualOrderVO> scManualOrderVOS = saleOrdersMapper.selectSaleOrderAndCustomerByOrderId(scManualOrderVOreq.getId());
        ScCustomer scCustomer = new ScCustomer();
        if (!CollectionUtils.isEmpty(scManualOrderVOS)) {
            ScManualOrderVO scManualOrderItem = scManualOrderVOS.get(0);
            //更新客戶信息
            scCustomer.setAmzCustomerId(scManualOrderItem.getCustomerOuterId()); //TK用户唯一标识
            scCustomer.setOrganizationId(scManualOrderVOreq.getOrgId());
            scCustomer.setRecipientName(scManualOrderItem.getCustomerName()); //收件人姓名
            scCustomer.setCustomerName(scManualOrderItem.getCustomerName()); //收件人姓名
            scCustomer.setEmail(scManualOrderItem.getEmail());//邮箱
            scCustomer.setPhone(scManualOrderItem.getPhone());//手机
            scCustomer.setCountryCode(scManualOrderItem.getCountryCode());//国家
            scCustomer.setCountry(scManualOrderItem.getCountryCode());
            scCustomer.setCity(scManualOrderItem.getCity());//城市
            scCustomer.setStateOrRegion(scManualOrderItem.getState()); //州或地区
            scCustomer.setAddressLine1(scManualOrderItem.getAddressline1()); //地址
            scCustomer.setAddressLine2(scManualOrderItem.getAddressline2());
            scCustomer.setAddressLine3(scManualOrderItem.getAddressline3());
            scCustomer.setPostalCode(scManualOrderItem.getPostalCode());//邮政编码
            scCustomer.settingDefaultCreate();
            scCustomerService.insertScCustomer(scCustomer);
        }
        ticket.setCustomerId(scCustomer.getCustomerId());
        ticket.setId(scManualOrderVOreq.getTicketId()); //工单ID
        ticket.setAmazonOrderId(scManualOrderVOreq.getOrderNo()); //订单号
        ticket.setOrderId(scManualOrderVOreq.getId()); //订单ID
        ticket.setSellerSku(scManualOrderVOreq.getSellerSku());//sellerSKu
        ticket.setAsin(scManualOrderVOreq.getAsin());//ASIN
        ticket.setGoodsSku(scManualOrderVOreq.getErpSku()); //ERP SKU
        ticket.settingDefaultUpdate();
        ticketService.updateScTickeByTicketId(ticket); //更新订单号
    }


}
