package com.bizark.op.service.api;

import bizark.ApiException;
import bizark.amz.CatalogApi;
import bizark.amz.ListingsApi;
import bizark.amz.catalog.Item;
import bizark.amz.catalog.ItemSearchResults;
import bizark.amz.catalog.Pagination;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bizark.boss.api.entity.dashboard.amazon.AmazonApplicationEntity;
import com.bizark.common.exception.CommonException;
import com.bizark.op.api.cons.SaleConstant;
import com.bizark.op.api.enm.sale.AccountTypeEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.account.AmazonMarketPlace;
import com.bizark.op.api.entity.op.amazon.AmazonApiEnum;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.account.AmazonMarketPlaceService;
import com.bizark.op.api.service.amazon.AmazonApplicationService;
import com.bizark.op.service.service.amazon.AmazonBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class AmazonApi {

    public static final String DEFAULT_PLACE_ID = "ATVPDKIKX0DER";
    public static final String SKU_IDENTIFIERS_TYPE = "SKU";


    @Autowired
    private AmazonMarketPlaceService amazonMarketPlaceService;

    @Autowired
    private AmazonApplicationService amazonApplicationService;

    @Autowired
    private AccountService accountService;

    public ItemSearchResults searchCatalogItems(String sellerSku, Account account) {
        // 获取店铺连接配置信息
        String connectStr = account.getConnectStr();
        JSONObject connObj = JSON.parseObject(connectStr);
        if (!connObj.containsKey(SaleConstant.REFRESH_TOKEN)) {
            throw new CommonException("未获取到店铺配置Token信息:" + account.getFlag());
        }

        if (StrUtil.isBlank(account.getSellerId()) && !connObj.containsKey(SaleConstant.SELLER_ID)) {
            throw new CommonException("未获取到店铺SellerId : " + account.getFlag());
        }
        String sellerId = StrUtil.isBlank(account.getSellerId()) ?
                connObj.getString(SaleConstant.SELLER_ID) : account.getSellerId();
        String countryCode = StrUtil.isNotBlank(account.getCountryCode()) ?
                account.getCountryCode() : connObj.getString(SaleConstant.COUNTRY_CODE);

        // 获取 marketPlaceId
        // 获取 marketPlaceId
        String marketPlaceId = AmazonApiEnum.getByCountryCode(countryCode).getMarketPlace();
        AccountTypeEnum type = account.getAccountType().equals("Amazon_VC") ? AccountTypeEnum.VC : AccountTypeEnum.SC;

        String applicationId = account.getAmazonApplicationId();

        if (type == AccountTypeEnum.SC && StrUtil.isBlank(applicationId)) {
            throw new CommonException("未获取到应用ID");
        }

        List<AmazonApplicationEntity> applications = amazonApplicationService.findAllByApplicationId(applicationId);
        if (type == AccountTypeEnum.SC && CollectionUtil.isEmpty(applications)) {
            throw new CommonException("未获取应用信息");
        }

        AmazonApplicationEntity application = type == AccountTypeEnum.SC ? CollectionUtil.getFirst(applications) : null;

        CatalogApi catalogApi = new CatalogApi.Builder()
                .awsAuthenticationCredentials(AmazonBaseService.getAwsCre(application, countryCode, type))
                .lwaAuthorizationCredentials(AmazonBaseService.getLwaCre(application, connObj.getString(SaleConstant.REFRESH_TOKEN), type))
                .awsAuthenticationCredentialsProvider(AmazonBaseService.getAwsProvider(application, type))
                .endpoint(AmazonBaseService.getEndpoint(countryCode)).build();

        String uuid = UUID.randomUUID().toString();
        try {


            List<String> includedData = Arrays.asList("attributes", "dimensions", "identifiers", "images", "productTypes", "relationships", "salesRanks", "summaries");
            List<String> marketplaceList = new ArrayList<>();
            marketplaceList.add(marketPlaceId);
            String identifiersType = SKU_IDENTIFIERS_TYPE;
            List<String> identifiers = new ArrayList<>();
            //seller_sku
            identifiers.add(sellerSku);
            log.error("UPDATE_ASIN - 数据查询 - {} - {}", uuid, sellerSku);
            ItemSearchResults searchResults = catalogApi.searchCatalogItems(marketplaceList, identifiers, identifiersType, includedData, null, sellerId, null, null, null, null, null, null);
            log.error("UPDATE_ASIN - 数据响应 - {} - {} - {} ", uuid, sellerSku, JSON.toJSONString(searchResults));
            return searchResults;
        } catch (ApiException e) {
            log.error("UPDATE_ASIN - 响应异常ApiException - {} - {}", uuid, sellerSku, e);
            throw new CommonException(e.getMessage());
        } catch (Exception e) {
            log.error("UPDATE_ASIN - 响应异常 - {} - {}", uuid, sellerSku, e);
            throw new CommonException(e.getMessage());
        }
    }

    public ItemSearchResults searchCatalogItems(String sellerSku, String flag) {
        Account account = accountService.lambdaQuery()
                .eq(Account::getFlag, flag)
                .one();
        if (Objects.isNull(account)) {
            throw new CommonException("未获取到店铺信息");
        }
        return searchCatalogItems(sellerSku, account);
    }

    public ItemSearchResults searchCatalogItems(String sellerSku, Integer shopId) {
        Account account = accountService.getById(shopId);
        if (Objects.isNull(account)) {
            throw new CommonException("未获取到店铺信息");
        }
        return searchCatalogItems(sellerSku, account);
    }


    public ItemSearchResults searchCatalogItems(List<String> allSellerSku, Account account) {
        ItemSearchResults results = new ItemSearchResults();
        try {
            results.setItems(new ArrayList<>());
            results.setNumberOfResults(0);
            ItemSearchResults searchResults = null;
            String nextCursor = null;
            while (Objects.nonNull(searchResults = getCataLogItems(allSellerSku, account, nextCursor)) && CollectionUtil.isNotEmpty(searchResults.getItems())) {
                log.info("更新asin信息响应:{}", JSON.toJSONString(searchResults));
                List<Item> items = searchResults.getItems();
                results.getItems().addAll(items);
                results.setNumberOfResults(results.getNumberOfResults());
                Pagination pagination = searchResults.getPagination();
                if (Objects.isNull(pagination)) {
                    break;
                }
                if (StrUtil.isBlank(pagination.getNextToken())) {
                    break;
                }
                nextCursor = pagination.getNextToken();
            }
            return results;
        } catch (Exception e) {
            log.error("UPDATE_ASIN 异常信息：{}", e.getMessage());
        }
        return results;
    }

    public bizark.amz.listing.Item getListingItem(String sellerSku, Account account) {

        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            log.error("Listing 查询 - 获取到店铺连接信息 ：{} ", account.getFlag());
            return null;
        }
        JSONObject connObj = JSON.parseObject(connectStr);
        if (!connObj.containsKey(SaleConstant.REFRESH_TOKEN)) {
            throw new CommonException("未获取到店铺配置Token信息！");
        }

        if (StrUtil.isBlank(account.getSellerId()) && !connObj.containsKey(SaleConstant.SELLER_ID)) {
            throw new CommonException("未获取到店铺SellerId!");
        }


        AccountTypeEnum type = account.getAccountType().equals("Amazon_VC") ? AccountTypeEnum.VC : AccountTypeEnum.SC;

        String sellerId = StrUtil.isBlank(account.getSellerId()) ?
                connObj.getString(SaleConstant.SELLER_ID) : account.getSellerId();
        String countryCode = StrUtil.isNotBlank(account.getCountryCode()) ?
                account.getCountryCode() : connObj.getString(SaleConstant.COUNTRY_CODE);


        String applicationId = account.getAmazonApplicationId();


        if (type == AccountTypeEnum.SC && StrUtil.isBlank(applicationId)) {
            throw new CommonException("未获取到应用ID");
        }

        List<AmazonApplicationEntity> applications = amazonApplicationService.findAllByApplicationId(applicationId);
        if (type == AccountTypeEnum.SC && CollectionUtil.isEmpty(applications)) {
            throw new CommonException("未获取应用信息");
        }

        AmazonApplicationEntity application = type == AccountTypeEnum.SC ? CollectionUtil.getFirst(applications) : null;


        // 获取 marketPlaceId
        String marketPlaceId = AmazonApiEnum.getByCountryCode(countryCode).getMarketPlace();

        ListingsApi api = new ListingsApi.Builder()
                .awsAuthenticationCredentials(AmazonBaseService.getAwsCre(application, countryCode, type))
                .lwaAuthorizationCredentials(AmazonBaseService.getLwaCre(application, connObj.getString(SaleConstant.REFRESH_TOKEN), type))
                .awsAuthenticationCredentialsProvider(AmazonBaseService.getAwsProvider(application, type))
                .endpoint(AmazonBaseService.getEndpoint(countryCode)).build();


        List<String> includeData = Arrays.asList("summaries", "attributes", "issues", "offers", "fulfillmentAvailability", "procurement");

        try {
            log.info("Listing 查询 - getListingsItem - {} - 参数 - sellerId：{}  marketPlaceId:{}  includeData:{}", sellerSku, sellerId,marketPlaceId,includeData);
            bizark.amz.listing.Item listingsItem = api.getListingsItem(sellerId, sellerSku, Arrays.asList(marketPlaceId), null, includeData);
            log.info("Listing 查询 - getListingsItem - {} - 响应:{}", sellerSku, JSON.toJSONString(listingsItem));
            return listingsItem;
        } catch (Exception e) {
            log.error("Listing 查询 - getListingsItem 调用失败： {} - {}", sellerSku, e.getMessage(), e);
        }
        return null;
    }


    ItemSearchResults getCataLogItems(List<String> sellerSkus, Account account,String nextCursor) {
        // 获取店铺连接配置信息
        String connectStr = account.getConnectStr();
        JSONObject connObj = JSON.parseObject(connectStr);
        if (!connObj.containsKey(SaleConstant.REFRESH_TOKEN)) {
            throw new CommonException("未获取到店铺配置Token信息: " + account.getFlag());
        }

        if (StrUtil.isBlank(account.getSellerId()) && !connObj.containsKey(SaleConstant.SELLER_ID)) {
            throw new CommonException("未获取到店铺SellerId:" + account.getFlag());
        }
        String sellerId = StrUtil.isBlank(account.getSellerId()) ?
                connObj.getString(SaleConstant.SELLER_ID) : account.getSellerId();
        String countryCode = StrUtil.isNotBlank(account.getCountryCode()) ?
                account.getCountryCode() : connObj.getString(SaleConstant.COUNTRY_CODE);

        // 获取 marketPlaceId
        String marketPlaceId = AmazonApiEnum.getByCountryCode(countryCode).getMarketPlace();

        AccountTypeEnum type = account.getAccountType().equals("Amazon_VC") ? AccountTypeEnum.VC : AccountTypeEnum.SC;


        String applicationId = account.getAmazonApplicationId();

        if (type == AccountTypeEnum.SC && StrUtil.isBlank(applicationId)) {
            throw new CommonException("未获取到应用ID");
        }

        List<AmazonApplicationEntity> applications = amazonApplicationService.findAllByApplicationId(applicationId);
        if (type == AccountTypeEnum.SC && CollectionUtil.isEmpty(applications)) {
            throw new CommonException("未获取应用信息");
        }

        AmazonApplicationEntity application = type == AccountTypeEnum.SC ? CollectionUtil.getFirst(applications) : null;


        CatalogApi catalogApi = new CatalogApi.Builder()
                .awsAuthenticationCredentials(AmazonBaseService.getAwsCre(application, countryCode, type))
                .lwaAuthorizationCredentials(AmazonBaseService.getLwaCre(application, connObj.getString(SaleConstant.REFRESH_TOKEN), type))
                .awsAuthenticationCredentialsProvider(AmazonBaseService.getAwsProvider(application, type))
                .endpoint(AmazonBaseService.getEndpoint(countryCode)).build();

        try {
            List<String> includedData = Arrays.asList("attributes", "dimensions", "identifiers", "images", "productTypes", "relationships", "salesRanks", "summaries");
            List<String> marketplaceList = new ArrayList<>();
            marketplaceList.add(marketPlaceId);
            log.info("UPDATE_ASIN - {} 请求参数 -  marketplaceList:{}  - sellerSkus:{}  -  includedData:{}  -  sellerId:{}", sellerSkus, marketplaceList, sellerSkus, includedData, sellerId);
            ItemSearchResults searchResults = catalogApi.searchCatalogItems(marketplaceList, sellerSkus, SKU_IDENTIFIERS_TYPE, includedData, null, sellerId, null, null, null, null, nextCursor, null);
            log.info("UPDATE_ASIN - {} - 响应信息:{}", sellerSkus, JSON.toJSONString(searchResults));
            return searchResults;
        } catch (ApiException e) {
            log.error("同步catalog item失败 {} {}：",sellerSkus, e.getMessage());
        } catch (Exception e) {
            log.error("同步catalog item异常：{} {}", sellerSkus, e.getMessage());
        }
        return null;
    }
}
