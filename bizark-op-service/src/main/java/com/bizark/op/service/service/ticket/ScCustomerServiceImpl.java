package com.bizark.op.service.service.ticket;

import cn.hutool.core.collection.CollectionUtil;
import com.bizark.op.api.cons.ScTicketConstant;
import com.bizark.op.api.enm.sale.AccountSaleChannelEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.order.SaleOrders;
import com.bizark.op.api.entity.op.ticket.ScCustomer;
import com.bizark.op.api.entity.op.ticket.ScManualOrder;
import com.bizark.op.api.entity.op.ticket.ScTicket;
import com.bizark.op.api.entity.op.ticket.VO.EmailInfoVO;
import com.bizark.op.api.entity.op.ticket.VO.SaleOrderVoTicket;
import com.bizark.op.api.entity.op.ticket.VO.ScManualOrderVO;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.ticket.IScCustomerService;
import com.bizark.op.api.service.order.SaleOrdersService;
import com.bizark.op.api.service.ticket.IScTicketService;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.service.mapper.ticket.ScCustomerMapper;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 客户管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-20
 */
@Service
public class ScCustomerServiceImpl implements IScCustomerService {
    @Autowired
    private ScCustomerMapper scCustomerMapper;

    @Autowired
    private SaleOrdersService saleOrdersService;


    @Autowired
    @Lazy
    private IScTicketService scTicketService;

//    @Autowired
//    private AmzOrderOriginalMapper amzOrderOriginalMapper;
//
//    @Autowired
//    private AmzVcOrderMapper amzVcOrderMapper;
//
//    @Autowired
//    private AmzOrderAddressOriginalMapper amzOrderAddressOriginalMapper;
//
//    @Autowired
//    private IScOrderService scOrderService;
//
//    @Autowired
//    private AmzShopMapper shopMapper;

    @Autowired
    private AccountService accountService;


    /**
     * 查询客户管理
     *
     * @param customerId 客户管理ID
     * @return 客户管理
     */
    @Override
    public ScCustomer selectScCustomerById(Long customerId) {
        if (null == customerId) {
            return new ScCustomer();
        }
        ScCustomer scCustomer = scCustomerMapper.selectScCustomerById(customerId);
        if (null==scCustomer) {
            return new ScCustomer();
        }
        scCustomer.setAddress(scCustomer.getAddressLine1());
        scCustomer.setAddress2(scCustomer.getAddressLine2());
        return scCustomer;
    }

    @Override
    public List<ScCustomer> selectScCustomerByIds(List<Integer> customerIds) {
        return scCustomerMapper.selectScCustomerByIds(customerIds);
    }

    /**
     * 查询客户管理列表
     *
     * @param scCustomer 客户管理
     * @return 客户管理
     */
    @Override
    public List<ScCustomer> selectScCustomerList(ScCustomer scCustomer) {
        return scCustomerMapper.selectScCustomerList(scCustomer);
    }

    /**
     * 新增客户管理
     *
     * @param scCustomer 客户管理
     * @return 结果
     */
    @Override
    public int insertScCustomer(ScCustomer scCustomer) {
        return scCustomerMapper.insertScCustomer(scCustomer);
    }

    /**
     * 修改客户管理
     *
     * @param scCustomer 客户管理
     * @return 结果
     */
    @Override
    public int updateScCustomer(ScCustomer scCustomer) {
        scCustomer.setUpdatedAt(DateUtils.getNowDate());
        return scCustomerMapper.updateScCustomer(scCustomer);
    }


    /**
     * @description:根据平台客户ID更新客户信息
     * @author: Moore
     * @date: 2024/1/4 0:49
     * @param
     * @param scCustomer
     * @return: void
    **/
    @Override
    public int updateScCustomerByAmzCustomerId(ScCustomer scCustomer) {
        return scCustomerMapper.updateScCustomerByAmzCustomerId(scCustomer);
    }

    @Override
    public int updateScCustomerEmail(UserEntity user, ScCustomer scCustomer) {
        scCustomer.setUpdatedBy(user.getId());
        scCustomer.setUpdatedAt(DateUtils.getNowDate());
        return scCustomerMapper.updateScCustomerEmail(scCustomer);
    }

    /**
     * 批量删除客户管理
     *
     * @param customerIds 需要删除的客户管理ID
     * @return 结果
     */
    @Override
    public int deleteScCustomerByIds(Long[] customerIds) {
        return scCustomerMapper.deleteScCustomerByIds(customerIds);
    }

    /**
     * 删除客户管理信息
     *
     * @param customerId 客户管理ID
     * @return 结果
     */
    @Override
    public int deleteScCustomerById(Long customerId) {
        return scCustomerMapper.deleteScCustomerById(customerId);
    }

//    private Long getDeptIdByShop(Long shopId) {
//        AmzShop shop = shopMapper.selectAmzShopById(shopId);
//        return shop.getDeptId();
//    }

//    @Override
//    public int saveScCustomerByOrder(Address shippingAddress, BuyerInfo buyerInfo, Long organizationId, Long orderId, Long shopId) {
//        int changeCount = 0;
//
//        if ((null != shippingAddress && StringUtil.isNotEmpty(shippingAddress.getName())) || (null != buyerInfo && StringUtil.isNotEmpty(buyerInfo.getBuyerName()))) {
//            ScCustomer customer = new ScCustomer();
//            String customerName = null;
//            if (StringUtil.isNotEmpty(buyerInfo.getBuyerName())) {
//                customerName = buyerInfo.getBuyerName();
//                customer.setAmazonEmail(buyerInfo.getBuyerEmail());
//                customer.setCountry(buyerInfo.getBuyerCounty());
//            } else {
//                if (StringUtil.isNotEmpty(shippingAddress.getName()) && !StringUtil.hasStr(shippingAddress.getName().toUpperCase(), "Warehouse".toUpperCase())) {
//                    customerName = shippingAddress.getName();
//
//                    customer.setPhone(shippingAddress.getPhone());
//                    customer.setAddressLine1(shippingAddress.getAddressLine1());
//                    customer.setAddressLine2(shippingAddress.getAddressLine2());
//                    customer.setAddressLine3(shippingAddress.getAddressLine3());
//                    customer.setCity(shippingAddress.getCity());
//                    customer.setCounty(shippingAddress.getCounty());
//                    customer.setCountryCode(shippingAddress.getCountryCode());
//                    customer.setDistrict(shippingAddress.getDistrict());
//                    customer.setStateOrRegion(shippingAddress.getStateOrRegion());
//                    customer.setMunicipality(shippingAddress.getMunicipality());
//                    customer.setPostalCode(shippingAddress.getPostalCode());
//                    customer.setAddressType(null != shippingAddress.getAddressType() ? shippingAddress.getAddressType().getValue() : null);
//                }
//            }
//            customer.setCustomerName(customerName);
//            customer.setNickName(customerName);
//
//            List<ScCustomer> scCustomerList = scCustomerMapper.selectScCustomerList(customer);
//            if (CollectionUtils.isEmpty(scCustomerList)) {
//                customer.setOrganizationId(organizationId);
//                customer.setDeptId(getDeptIdByShop(shopId));
//                changeCount = this.insertScCustomer(customer);
//                amzOrderOriginalMapper.updateOrderCustomerId(orderId, customer.getCustomerId());
//                ScOrder scOrder = new ScOrder();
//                scOrder.setSourceId(orderId);
//                scOrder.setSourceDocument(SaleConstant.ORDER_SOURCE_DOCUMENT_SC);
//                scOrder.setCustomerId(customer.getCustomerId());
//                scOrderService.updateScOrderCustomerId(scOrder);
//            } else {
//                if (scCustomerList.size() == 1) {
//                    changeCount = amzOrderOriginalMapper.updateOrderCustomerId(orderId, scCustomerList.get(0).getCustomerId());
//                    ScOrder scOrder = new ScOrder();
//                    scOrder.setSourceId(orderId);
//                    scOrder.setSourceDocument(SaleConstant.ORDER_SOURCE_DOCUMENT_SC);
//                    scOrder.setCustomerId(scCustomerList.get(0).getCustomerId());
//                    scOrderService.updateScOrderCustomerId(scOrder);
//                }
//            }
//        }
//
//        return changeCount;
//    }
//
//    @Override
//    public int saveScCustomerByOrder(OrderBuyerInfo buyerInfo, Long orderId, Long shopId) {
//        int changeCount = 0;
//
//        if (null != buyerInfo && StringUtil.isNotEmpty(buyerInfo.getBuyerName())) {
//            ScCustomer customer = new ScCustomer();
//            customer.setCustomerName(buyerInfo.getBuyerName());
//            customer.setAmazonEmail(buyerInfo.getBuyerEmail());
//
//            List<ScCustomer> scCustomerList = scCustomerMapper.selectScCustomerList(customer);
//
//            if (CollectionUtils.isEmpty(scCustomerList)) {
//                customer.setCountry(buyerInfo.getBuyerCounty());
//                customer.setNickName(buyerInfo.getBuyerName());
//
//                AmzOrderAddressOriginal record = new AmzOrderAddressOriginal();
//                record.setOrderId(orderId);
//                List<AmzOrderAddressOriginal> orderAddressOriginalList = amzOrderAddressOriginalMapper.selectAmzOrderAddressOriginalList(record);
//                if (!CollectionUtils.isEmpty(orderAddressOriginalList)) {
//                    AmzOrderAddressOriginal shippingAddress = orderAddressOriginalList.get(0);
//                    customer.setRecipientName(shippingAddress.getName());
//                    customer.setPhone(shippingAddress.getPhone());
//                    customer.setAddressLine1(shippingAddress.getAddressLine1());
//                    customer.setAddressLine2(shippingAddress.getAddressLine2());
//                    customer.setAddressLine3(shippingAddress.getAddressLine3());
//                    customer.setCity(shippingAddress.getCity());
//                    customer.setCounty(shippingAddress.getCounty());
//                    customer.setCountryCode(shippingAddress.getCountryCode());
//                    customer.setDistrict(shippingAddress.getDistrict());
//                    customer.setStateOrRegion(shippingAddress.getStateOrRegion());
//                    customer.setMunicipality(shippingAddress.getMunicipality());
//                    customer.setPostalCode(shippingAddress.getPostalCode());
//                }
//                AmzShop amzShop = shopMapper.selectAmzShopById(shopId);
//                customer.setOrganizationId(amzShop.getOrganizationId());
//                customer.setDeptId(amzShop.getDeptId());
//                changeCount = this.insertScCustomer(customer);
//                amzOrderOriginalMapper.updateOrderCustomerId(orderId, customer.getCustomerId());
//                ScOrder scOrder = new ScOrder();
//                scOrder.setSourceId(orderId);
//                scOrder.setSourceDocument(SaleConstant.ORDER_SOURCE_DOCUMENT_SC);
//                scOrder.setCustomerId(customer.getCustomerId());
//                scOrderService.updateScOrderCustomerId(scOrder);
//            } else {
//                if (scCustomerList.size() == 1) {
//                    changeCount = amzOrderOriginalMapper.updateOrderCustomerId(orderId, scCustomerList.get(0).getCustomerId());
//                    ScOrder scOrder = new ScOrder();
//                    scOrder.setSourceId(orderId);
//                    scOrder.setSourceDocument(SaleConstant.ORDER_SOURCE_DOCUMENT_SC);
//                    scOrder.setCustomerId(scCustomerList.get(0).getCustomerId());
//                    scOrderService.updateScOrderCustomerId(scOrder);
//                }
//            }
//        }
//
//        return changeCount;
//    }
//
//    @Override
//    public int updateCustomerAddress(Address shippingAddress, Long orderId) {
//        AmzOrderOriginal amzOrderOriginal = amzOrderOriginalMapper.selectAmzOrderOriginalById(orderId);
//        if (null != amzOrderOriginal.getCustomerId()) {
//            ScCustomer customer = new ScCustomer();
//            customer.setOrganizationId(amzOrderOriginal.getOrganizationId());
//            customer.setCustomerId(amzOrderOriginal.getCustomerId());
//            customer.setRecipientName(shippingAddress.getName());
//            customer.setPhone(shippingAddress.getPhone());
//            customer.setAddressLine1(shippingAddress.getAddressLine1());
//            customer.setAddressLine2(shippingAddress.getAddressLine2());
//            customer.setAddressLine3(shippingAddress.getAddressLine3());
//            customer.setCity(shippingAddress.getCity());
//            customer.setCounty(shippingAddress.getCounty());
//            customer.setCountryCode(shippingAddress.getCountryCode());
//            customer.setDistrict(shippingAddress.getDistrict());
//            customer.setStateOrRegion(shippingAddress.getStateOrRegion());
//            customer.setMunicipality(shippingAddress.getMunicipality());
//            customer.setPostalCode(shippingAddress.getPostalCode());
//            customer.setUpdateTime(new Date());
//
//            return scCustomerMapper.updateScCustomerAddress(customer);
//        }
//        return 0;
//    }
//
//    @Override
//    public int saveScCustomerByVcOrder(com.aofis.amazon.vendor.directFulfillmentOrder.model.Address orderAddress, Long organizationId, Long orderId, Long shopId) {
//        int count = 0;
//        Long customerId = null;
//        ScCustomer record = new ScCustomer();
//        record.setCustomerName(orderAddress.getName());
//        record.setPhone(orderAddress.getPhone());
//        List<ScCustomer> scCustomerList = scCustomerMapper.selectScCustomerList(record);
//        if (CollectionUtils.isEmpty(scCustomerList)) {
//            record.setOrganizationId(organizationId);
//            record.setNickName(orderAddress.getName());
//            record.setRecipientName(orderAddress.getName());
//            record.setAddressLine1(orderAddress.getAddressLine1());
//            record.setAddressLine2(orderAddress.getAddressLine2());
//            record.setAddressLine3(orderAddress.getAddressLine3());
//            record.setCity(orderAddress.getCity());
//            record.setCounty(orderAddress.getCounty());
//            record.setCountryCode(orderAddress.getCountryCode());
//            record.setDistrict(orderAddress.getDistrict());
//            record.setStateOrRegion(orderAddress.getStateOrRegion());
//            record.setPostalCode(orderAddress.getPostalCode());
//            record.setDeptId(getDeptIdByShop(shopId));
//            count = this.insertScCustomer(record);
//            customerId = record.getCustomerId();
//        } else {
//            customerId = scCustomerList.get(0).getCustomerId();
//        }
//        amzVcOrderMapper.updateOrderCustomerId(orderId, customerId);
//        ScOrder scOrder = new ScOrder();
//        scOrder.setSourceId(orderId);
//        scOrder.setSourceDocument(SaleConstant.ORDER_SOURCE_DOCUMENT_VC);
//        scOrder.setCustomerId(customerId);
//        scOrderService.updateScOrderCustomerId(scOrder);
//
//        return count;
//    }

    @Override
    public Long saveScCustomerByEmailInfo(String email, String nickName, Long shopId) {
        Long customerId = null;
        ScCustomer record = new ScCustomer();
        record.setAmazonEmail(email);
        List<ScCustomer> scCustomerList = scCustomerMapper.selectScCustomerList(record);
        if (CollectionUtils.isEmpty(scCustomerList)) {
            record.setNickName(nickName);
            record.setCustomerName(nickName);

//            AmzShop shop = shopMapper.selectAmzShopById(shopId);
//            record.setOrganizationId(null != shop ? shop.getOrganizationId() : null);
//            record.setDeptId(null != shop ? shop.getDeptId() : null);
            this.insertScCustomer(record);
            customerId = record.getCustomerId();
        } else {
            for (ScCustomer customer : scCustomerList) {
                if (nickName.equals(customer.getNickName())) {
                    customerId = customer.getCustomerId();
                    break;
                }
            }
            if (null == customerId) {
                customerId = scCustomerList.get(0).getCustomerId();
            }
        }

        return customerId;
    }

    @Override
    public Long saveCustomerByManualOrder(ScManualOrder scManualOrder) {
        Long customerId = null;
        ScCustomer record = new ScCustomer();
        record.setEmail(scManualOrder.getEmail());
        List<ScCustomer> scCustomerList = scCustomerMapper.selectScCustomerList(record);
        if (CollectionUtils.isEmpty(scCustomerList)) {
            record.setNickName(scManualOrder.getBuyerName());
            record.setCustomerName(scManualOrder.getBuyerName());
//            AmzShop shop = shopMapper.selectAmzShopById(scManualOrder.getShopId());
//            record.setDeptId(null != shop ? shop.getDeptId() : null);
            record.setOrganizationId(scManualOrder.getOrganizationId());
            this.insertScCustomer(record);
            customerId = record.getCustomerId();
        } else {
            customerId = scCustomerList.get(0).getCustomerId();
        }

        return customerId;
    }

//    @Override
//    public Long saveWayfairCustomer(WayfairOrderInfoRes wayfairOrderInfoRes) {
//        ScCustomer customer = new ScCustomer();
//        customer.setOrganizationId(wayfairOrderInfoRes.getOrganizationId());
//        customer.setCustomerName(wayfairOrderInfoRes.getCustomerName());
//        customer.setAddressLine1(wayfairOrderInfoRes.getCustomerAddress1());
//        customer.setPostalCode(wayfairOrderInfoRes.getCustomerPostalCode());
//        List<ScCustomer> customerList = scCustomerMapper.selectScCustomerList(customer);
//        if (CollectionUtils.isEmpty(customerList)) {
//            customer.setAddressLine2(wayfairOrderInfoRes.getCustomerAddress2());
//            customer.setCity(wayfairOrderInfoRes.getCustomerCity());
//            customer.setStateOrRegion(wayfairOrderInfoRes.getCustomerState());
//            this.insertScCustomer(customer);
//        } else {
//            customer = customerList.get(0);
//        }
//        return customer.getCustomerId();
//    }
//
//    @Override
//    public Long saveWalmartCustomer(Order walOrder) {
//        ScCustomer customer = new ScCustomer();
//        ShippingInfo shippingInfo = walOrder.getShippingInfo();
//        if (walOrder.getCustomerEmailId().indexOf("nocxemail@walmartone") == -1) {
//            customer.setAmazonEmail(walOrder.getCustomerEmailId());
//        } else {
//            if (null != shippingInfo || StringUtil.isEmpty(shippingInfo.getPhone())) {
//                return null;
//            }
//            customer.setPhone(shippingInfo.getPhone());
//        }
//        List<ScCustomer> customerList = scCustomerMapper.selectScCustomerList(customer);
//        if (CollectionUtils.isEmpty(customerList)) {
//            if (null != shippingInfo) {
//                customer.setOrganizationId(walOrder.getOrganizationId());
//                customer.setPhone(shippingInfo.getPhone());
//                if (null != shippingInfo.getPostalAddress()) {
//                    customer.setCustomerName(shippingInfo.getPostalAddress().getName());
//                    customer.setRecipientName(shippingInfo.getPostalAddress().getName());
//                    customer.setAddressLine1(shippingInfo.getPostalAddress().getAddress1());
//                    customer.setAddressLine2(shippingInfo.getPostalAddress().getAddress2());
//                    customer.setCity(shippingInfo.getPostalAddress().getCity());
//                    customer.setStateOrRegion(shippingInfo.getPostalAddress().getState());
//                    customer.setPostalCode(shippingInfo.getPostalAddress().getPostalCode());
//                }
//            }
//            this.insertScCustomer(customer);
//        } else {
//            customer = customerList.get(0);
//        }
//
//        return customer.getCustomerId();
//    }
//
//    @Override
//    public Long saveShopifyCustomer(Long organizationId, Customer spfCustomer) {
//        ScCustomer customer = new ScCustomer();
//        if (null != spfCustomer) {
//            customer.setEmail(spfCustomer.getEmail());
//            List<ScCustomer> customerList = scCustomerMapper.selectScCustomerList(customer);
//            if (CollectionUtils.isEmpty(customerList)) {
//                customer.setCustomerName(spfCustomer.getFirstName() + " " + spfCustomer.getLastName());
//
//                CustomerDefaultAddress customerDefaultAddress = spfCustomer.getDefaultAddress();
//                if (null != customerDefaultAddress) {
//                    customer.setOrganizationId(organizationId);
//                    customer.setPhone(customerDefaultAddress.getPhone());
//                    customer.setRecipientName(customerDefaultAddress.getName());
//                    customer.setAddressLine1(customerDefaultAddress.getAddress1());
//                    customer.setAddressLine2(customerDefaultAddress.getAddress2());
//                    customer.setCity(customerDefaultAddress.getCity());
//                    customer.setStateOrRegion(customerDefaultAddress.getProvinceCode());
//                    customer.setPostalCode(customerDefaultAddress.getZip());
//                    customer.setCountryCode(customerDefaultAddress.getCountryCode());
//                    customer.setCountry(customerDefaultAddress.getCountry());
//                }
//                this.insertScCustomer(customer);
//            } else {
//                customer = customerList.get(0);
//            }
//        }
//        return customer.getCustomerId();
//    }
//
//    /*保存overstock客户信息*/
//    @Override
//    public Long saveOverStockCustomer(Long organizationId, OverstockOrderAddress overstockOrderAddress) {
//        ScCustomer customer = new ScCustomer();
//        if (null != overstockOrderAddress) {
//            customer.setOrganizationId(organizationId);
//            customer.setPhone(overstockOrderAddress.getPhone());
//            List<ScCustomer> customerList = scCustomerMapper.selectScCustomerList(customer);  //手机号确定唯一值
//            if (CollectionUtils.isEmpty(customerList)) {
//                customer.setCustomerName(overstockOrderAddress.getContactName());//姓名
//                customer.setPhone(overstockOrderAddress.getPhone());
//                customer.setRecipientName(overstockOrderAddress.getContactName()); //收件人姓名
//                customer.setAddressLine1(overstockOrderAddress.getAddress1());//地址1
//                customer.setAddressLine1(overstockOrderAddress.getAddress2());//地址2
//                customer.setCity(overstockOrderAddress.getCity());//城市
//                customer.setStateOrRegion(overstockOrderAddress.getStateOrProvince()); //州或地区
//                customer.setPostalCode(overstockOrderAddress.getPostalCode()); //邮编
//                customer.setCountryCode(overstockOrderAddress.getCountryCode()); //国家CODE
//                customer.setCountry(overstockOrderAddress.getCountryCode()); //国家
//                this.insertScCustomer(customer);
//            } else {
//                customer = customerList.get(0);
//            }
//        }
//        return customer.getCustomerId();
//    }
//
//    @Override
//    public int saveScCustomerByReview(XsmAmzCustomer xsmAmzCustomer) {
//        int count = 0;
//        ScCustomer record = new ScCustomer();
//        record.setAmzCustomerId(xsmAmzCustomer.getCustomerId());
//        List<ScCustomer> scCustomerList = this.selectScCustomerList(record);
//        if (CollectionUtils.isEmpty(scCustomerList)) {
//            record.setCustomerName(xsmAmzCustomer.getCustomerName());
//            record.setNickName(xsmAmzCustomer.getCustomerName());
//            record.setCustomizedUrl(XsmApiConstant.PROFILE_PREFIX_URL + xsmAmzCustomer.getCustomerId());
//            count = this.insertScCustomer(record);
//        }
//        return count;
//    }

    @Override
    public int updateScCustomerAmzEmail(ScCustomer scCustomer) {
        return scCustomerMapper.updateScCustomerAmzEmail(scCustomer);
    }

    @Override
    public int updateCustomerEmail(Long shopId, String amazonOrderId, String email) {
        SaleOrders scOrder = saleOrdersService.lambdaQuery()
                .eq(SaleOrders::getChannelId, shopId)
                .eq(SaleOrders::getOrderNo, amazonOrderId)
                .one();
        if (null != scOrder && null != scOrder.getCustomerId()) {
            ScCustomer scCustomer = scCustomerMapper.selectScCustomerById(scOrder.getCustomerId().longValue());
            if (StringUtil.isEmpty(scCustomer.getEmail())) {
                scCustomer.setEmail(email);
                scCustomer.setSkipInterceptor(true);
                scCustomer.setUpdatedAt(new Date());
                return scCustomerMapper.updateScCustomerEmail(scCustomer);
            }
        }
        return 0;
    }


    /*
    *
    客户看板数据（客户数量统计）
    *
    */
//    @Override
//    public CustomerCount getCustomerCount(HomeStatQuery homeStatQuery) {
//        ScCustomer scCustomer = new ScCustomer();
//        scCustomer.setCountryCode(homeStatQuery.getCountryCode());
//        List<ScCustomer> scCustomerCount = scCustomerMapper.selectScCustomerList(scCustomer);
//        Long customerCnt = scCustomerCount.stream().map(ScCustomer::getCustomerId).distinct().count();
//
//        //计算今日新增
//        scCustomer = new ScCustomer();
//        scCustomer.setDateFrom(homeStatQuery.getDateFrom());
//        scCustomer.setDateTo(homeStatQuery.getDateTo());
//        scCustomer.setCountryCode(homeStatQuery.getCountryCode());
//        List<ScCustomer> scCustomers = scCustomerMapper.selectScCustomerList(scCustomer);
//        Long customerNewCnt = scCustomers.stream().map(ScCustomer::getCustomerId).distinct().count();
//
//        //查询复购人数
//        Integer repurchaseCount = scCustomerMapper.selectScCustomerRepurchaseCount(scCustomer);
//
//        CustomerCount customerCount = new CustomerCount();
//        customerCount.setTotalCount(customerCnt.intValue()); //总数量
//        customerCount.setNewCount(customerNewCnt.intValue());  // 时间范围内客户条数
//        customerCount.setRepurchaseCount(repurchaseCount);//时间范围内客户复购
//        return customerCount;
//    }


    /*
     *
     客户看板数据（客户购买数量）
    *
    */
//    @Override
//    public ScCustomerShopCount getCustomerShopCount(CustomerCommonRequest customerCommonRequest) {
//        //若使用时间区间查询，时间类型查询失效
//        if (!StringUtils.isEmpty(customerCommonRequest.getDateFrom()) || !StringUtils.isEmpty(customerCommonRequest.getDateTo())) {
//            customerCommonRequest.setDateType(null);
//        }
//        ScCustomerShopCount scCustomerStatCommon = scCustomerMapper.selectCustomerShopCount(customerCommonRequest);
//        //百分比转换
//        if (scCustomerStatCommon != null) {
//            scCustomerStatCommon.setCustomerShopCount1Ratio(scCustomerStatCommon.getCustomerShopCount1Ratio() == null ? "0%" : scCustomerStatCommon.getCustomerShopCount1Ratio() + "%");
//            scCustomerStatCommon.setCustomerShopCount2Ratio(scCustomerStatCommon.getCustomerShopCount2Ratio() == null ? "0%" : scCustomerStatCommon.getCustomerShopCount2Ratio() + "%");
//            scCustomerStatCommon.setCustomerShopCount3Ratio(scCustomerStatCommon.getCustomerShopCount3Ratio() == null ? "0%" : scCustomerStatCommon.getCustomerShopCount3Ratio() + "%");
//        }
//        return scCustomerStatCommon;
//    }
//
//    /*
//     * 客户看板数据（客户购买次数排名）
//     */
//    @Override
//    public List<ScCustomerOrderCount> getCustomerOrderRanking(CustomerCommonRequest customerCommonRequest) {
//        //若使用时间区间查询，时间类型查询失效
//        if (!StringUtils.isEmpty(customerCommonRequest.getDateFrom()) || !StringUtils.isEmpty(customerCommonRequest.getDateTo())) {
//            customerCommonRequest.setDateType(null);
//        }
//        return scCustomerMapper.selectCustomerOrderRanking(customerCommonRequest);
//    }
//
//    /*客户地区分布*/
//    @Override
//    public List<ScCustomerRegionCount> getCustomerRegionStat(CustomerCommonRequest customerCommonRequest) {
//        //若使用时间区间查询，时间类型查询失效
//        if (!StringUtils.isEmpty(customerCommonRequest.getDateFrom()) || !StringUtils.isEmpty(customerCommonRequest.getDateTo())) {
//            customerCommonRequest.setDateType(null);
//        }
//        List<ScCustomerRegionCount> scCustomerRegionCounts = scCustomerMapper.selectCustomerRegionStat(customerCommonRequest);
//        Integer sumTotal = scCustomerRegionCounts.stream().mapToInt(ScCustomerRegionCount::getCustomerCount).sum();    //总条数，求占比使用
//        DecimalFormat df = new DecimalFormat("0.00");
//        scCustomerRegionCounts.stream().forEach(
//                ss -> {
//                    if (sumTotal != 0) {
//                        ss.setRegionCode(ss.getRegionCode() == null ? "其他" : ss.getRegionCode());
//                        ss.setProportion(df.format((float) ss.getCustomerCount().intValue() / sumTotal * 100) + "%");
//                    }
//                }
//        );
//        return scCustomerRegionCounts;
//    }

    @Override
    public List<ScCustomer> selectScCustomerListByEmail(List<String> emails) {
        return scCustomerMapper.selectScCustomerListByEmail(emails);
    }

    @Override
    public int saveBatchCustomer(List<ScCustomer> customerRecords) {
        return scCustomerMapper.insertBatchCustomer(customerRecords);
    }

    @Override
    public void genNewCustomers(List<EmailInfoVO> infoVOS, Long shopId) {
        List<ScCustomer> customers = new ArrayList<>();
        Account shop = accountService.getById(shopId);
        Date date = DateUtils.getNowDate();
        for (EmailInfoVO infoVO : infoVOS) {
            ScCustomer record = new ScCustomer();
            record.setAmazonEmail(infoVO.getEmail());
            List<ScCustomer> scCustomerList = scCustomerMapper.selectScCustomerList(record);
            if (CollectionUtils.isEmpty(scCustomerList)) {
                record.setNickName(infoVO.getSendName());
                record.setCustomerName(infoVO.getSendName());
                record.setOrganizationId(null != shop ? shop.getOrgId().longValue() : null);
                // TODO 店铺不存在部门ID
//                record.setDeptId(null != shop ? shop.getDeptId() : null);
                record.setCreatedAt(date);
                customers.add(record);
            } else {


            }
        }

        if (CollectionUtil.isNotEmpty(customers)) {
            scCustomerMapper.insertBatchCustomer(customers);
        }

    }


    /**
     * @param
     * @param scManualOrderVO
     * @param scTicket
     * @description: 保存客户信息及工单信息关联客户ID
     * @author: Moore
     * @date: 2023/10/25 20:10
     * @return: void
     **/
    @Override
    @Transactional
    @Async("threadPoolTaskExecutor")
    public void saveCustomerInfoAndJoinTicket(ScManualOrderVO scManualOrderVO, ScTicket scTicket) {

        ScCustomer scCustomer = null;
        if (AccountSaleChannelEnum.TIKTOK.getValue().equals(scTicket.getTicketSource())) {
            if (StringUtils.isEmpty(scManualOrderVO.getCustomerOuterId())) {
                return;
            }
            //获取OuterId数据
             scCustomer = scCustomerMapper.selectScCustomerByAmzCustomerId(scManualOrderVO.getCustomerOuterId());
            if (null == scCustomer) {
                //保存客户信息
                //更新客戶信息
                scCustomer = new ScCustomer();
                scCustomer.setAmzCustomerId(scManualOrderVO.getCustomerOuterId()); //会话ID
                scCustomer.setNickName(scManualOrderVO.getCustomerName());//昵称
                scCustomer.setRecipientName(scManualOrderVO.getCustomerName()); //收件人姓名
                scCustomer.setEmail(scManualOrderVO.getEmail());//邮箱

                scCustomer.setPhone(scManualOrderVO.getPhone());//手机
                scCustomer.setCountryCode(scManualOrderVO.getCountryCode());
                scCustomer.setCity(scManualOrderVO.getCity());//城市
                scCustomer.setStateOrRegion(scManualOrderVO.getState()); //州或地区
                scCustomer.setAddressLine1(scManualOrderVO.getAddressline1()); //地址
                scCustomer.setAddressLine2(scManualOrderVO.getAddressline2());
                scCustomer.setAddressLine3(scManualOrderVO.getAddressline3());
                scCustomer.setPostalCode(scManualOrderVO.getPostalCode());
                scCustomer.settingDefaultCreate(); //保存客户信息
                scCustomerMapper.insertScCustomer(scCustomer);
            }
        }else{
            //其他渠道为新用户
            scCustomer = new ScCustomer();
            scCustomer.setAmzCustomerId(scManualOrderVO.getCustomerOuterId()); //会话ID
            scCustomer.setNickName(scManualOrderVO.getCustomerName());//昵称
            scCustomer.setRecipientName(scManualOrderVO.getCustomerName()); //收件人姓名
            scCustomer.setEmail(scManualOrderVO.getEmail());//邮箱

            scCustomer.setPhone(scManualOrderVO.getPhone());//手机
            scCustomer.setCountryCode(scManualOrderVO.getCountryCode());
            scCustomer.setCity(scManualOrderVO.getCity());//城市
            scCustomer.setStateOrRegion(scManualOrderVO.getState()); //州或地区
            scCustomer.setAddressLine1(scManualOrderVO.getAddressline1()); //地址
            scCustomer.setAddressLine2(scManualOrderVO.getAddressline2());
            scCustomer.setAddressLine3(scManualOrderVO.getAddressline3());
            scCustomer.setPostalCode(scManualOrderVO.getPostalCode());
            scCustomer.settingDefaultCreate(); //保存客户信息
            scCustomerMapper.insertScCustomer(scCustomer);
        }

        //更新工单上客户信息
        ScTicket scTicketRecod = new ScTicket();
        scTicketRecod.setId(scTicket.getId());
        scTicketRecod.setCustomerId(scCustomer.getCustomerId());
        scTicketRecod.settingDefaultUpdate();
        scTicketService.updateScTickeByTicketId(scTicketRecod);
    }

    @Override
    @Transactional
    @Async("threadPoolTaskExecutor")
    public void saveCustomerAndJoinTicket(SaleOrderVoTicket saleOrderVoTicket, ScTicket scTicket) {
        if (scTicket.getId() == null) {
            return;
        }

        //保存客户信息
        ScCustomer scCustomer = new ScCustomer();
        scCustomer.setAmzCustomerId(saleOrderVoTicket.getCustomerOuterId()); //TK用户唯一标识
        scCustomer.setNickName(saleOrderVoTicket.getCustomerName());//昵称
        scCustomer.setRecipientName(saleOrderVoTicket.getCustomerName()); //收件人姓名
        scCustomer.setCustomerName(saleOrderVoTicket.getCustomerName()); //收件人姓名

        scCustomer.setEmail(saleOrderVoTicket.getBuyEmail());//邮箱
        scCustomer.setPhone(saleOrderVoTicket.getPhone());//手机

        scCustomer.setCountryCode(saleOrderVoTicket.getCountryCode());//国家
        scCustomer.setCountry(saleOrderVoTicket.getCountryCode());//国家

        scCustomer.setCity(saleOrderVoTicket.getCity());//城市
        scCustomer.setStateOrRegion(saleOrderVoTicket.getState()); //州或地区
        scCustomer.setAddressLine1(saleOrderVoTicket.getAddressline1()); //地址
        scCustomer.setAddressLine2(saleOrderVoTicket.getAddressline2());
        scCustomer.setAddressLine3(saleOrderVoTicket.getAddressline3());
        scCustomer.setPostalCode(saleOrderVoTicket.getPostalCode());
        scCustomer.settingDefaultCreate(); //保存客户信息
        scCustomerMapper.insertScCustomer(scCustomer);

        //更新工单上客户信息
        ScTicket scTicketRecod = new ScTicket();
        scTicketRecod.setId(scTicket.getId());
        scTicketRecod.setCustomerId(scCustomer.getCustomerId());
        scTicketRecod.settingDefaultUpdate();
        scTicketService.updateScTickeByTicketId(scTicketRecod);
    }

    /**
     * @param
     * @param outerId
     * @description: 根据outerID获取客户信息
     * @author: Moore
     * @date: 2023/10/26 23:29
     * @return: com.bizark.op.api.entity.op.ticket.ScCustomer
     **/
    @Override
    public ScCustomer selectScCustomerByOuterId(String outerId) {
        return scCustomerMapper.selectScCustomerByAmzCustomerId(outerId);
    }


}
