package com.bizark.op.service.service.sale;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.op.api.enm.sale.temu.FileUploadTypeEnum;
import com.bizark.op.api.entity.op.sale.ProductChannelsDraft;
import com.bizark.op.api.entity.op.sale.TemuFileUpload;
import com.bizark.op.api.entity.op.sale.vo.TemuFileVO;
import com.bizark.op.api.service.sale.TemuFileUploadService;
import com.bizark.op.service.mapper.sale.TemuFileUploadMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TemuFileUploadServiceImpl extends ServiceImpl<TemuFileUploadMapper,TemuFileUpload> implements TemuFileUploadService {

    @Autowired
    private TemuFileUploadMapper temuFileUploadMapper;

    @Override
    public void handleFileUpload(ProductChannelsDraft draft, TemuFileVO fileVO) {
        List<TemuFileUpload> fileUploads = new ArrayList<>();
        List<TemuFileVO.Result> uploadResult = fileVO.getUploadResult();
        if (CollectionUtil.isNotEmpty(uploadResult)) {

            AtomicInteger atomicInteger = new AtomicInteger(1);

            List<TemuFileUpload> uploads = uploadResult.stream()
                    .map(c -> {
                        TemuFileUpload temuFileUpload = new TemuFileUpload();
                        temuFileUpload.setSkcId(fileVO.getSkcId());
                        temuFileUpload.setVersion(fileVO.getVersion());
                        temuFileUpload.setUploadResult(c.getUploadResult());
                        temuFileUpload.setType(c.getType());

                        if (Objects.equals(c.getType(), FileUploadTypeEnum.DETAIL_IMAGE.getCode())) {
                            // 商详默认值
                            temuFileUpload.setGoodsLayerType("image");
                            temuFileUpload.setGoodsLayerPriority(atomicInteger.getAndIncrement());
                            temuFileUpload.setGoodsLayerKey("DecImage");
                        }

                        temuFileUpload.setGroupNo(c.getGroupNo());
                        temuFileUpload.setMaterialId(c.getMaterialId());
                        temuFileUpload.setProductId(c.getProductId());
                        temuFileUpload.setErpSku(c.getSku());
                        temuFileUpload.setSellerSku(c.getSellerSku());
                        temuFileUpload.setMaterialName(c.getFileName());
                        temuFileUpload.setApiResult(c.getApiResult());
                        temuFileUpload.setSize(c.getFileSize());
                        temuFileUpload.setDraftId(c.getDraftId());
                        temuFileUpload.setSizeMode(c.getSizeMode());
                        temuFileUpload.setWidth(c.getWidth());
                        temuFileUpload.setHeight(c.getHeight());
                        temuFileUpload.setShopId(draft.getNewShopId());
                        return temuFileUpload;
                    }).collect(Collectors.toList());
            fileUploads.addAll(uploads);
        }


        if (CollectionUtil.isNotEmpty(fileUploads)) {
            this.saveBatch(fileUploads);
        }
    }

    @Override
    public void handleImageSize() {
        List<TemuFileUpload> fileUploads = this.lambdaQuery()
                .eq(TemuFileUpload::getSizeMode, 0)
                .isNotNull(TemuFileUpload::getUploadResult)
                .isNull(TemuFileUpload::getWidth)
                .list();

        for (TemuFileUpload upload : fileUploads) {
            try {
                URL url = new URL(upload.getUploadResult());
                BufferedImage image = ImageIO.read(url);
                upload.setWidth(image.getWidth());
                upload.setHeight(image.getHeight());
                this.updateById(upload);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }


    }

    @Override
    public int realDeleteBySkcAndVersion(String skcId, Long version) {
        return temuFileUploadMapper.realDeleteBySkcAndVersion(skcId, version);
    }
}
