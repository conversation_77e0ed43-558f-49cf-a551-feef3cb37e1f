package com.bizark.op.service.event.listener;


import bizark.amz.FeedsApi;
import bizark.amz.feed.Feed;
import bizark.amz.feed.FeedDocument;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.bizark.common.util.XmlUtils;
import com.bizark.op.api.enm.amazon.AmazonInventoryStatus;
import com.bizark.op.api.entity.op.amazon.AmazonFeed;
import com.bizark.op.api.entity.op.inventory.enums.InventoryStatus;
import com.bizark.op.api.entity.op.inventory.enums.TaskStatusEnum;
import com.bizark.op.api.entity.op.amazon.AmazonInventoryUpdateXml;
import com.bizark.op.api.service.task.PublishTaskService;
import com.bizark.op.service.event.AmazonInventoryUpdateEvent;
import com.bizark.op.service.handler.task.InventoryPublishTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class AmazonInventoryUpdateEventListener implements ApplicationListener<AmazonInventoryUpdateEvent> {

    @Autowired
    private PublishTaskService publishTaskService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private InventoryPublishTask inventoryPublishTask;


    @Override
    public void onApplicationEvent(AmazonInventoryUpdateEvent event) {
        log.info("SC/VC-队列事务 获取到SC事务事件，重试次数：{},当前尝试次数：{} ,任务ID：{}", event.getCount(), event.getFrequency(), event.getAmazonFeed().getTaskId());
        AmazonFeed amazonFeed = event.getAmazonFeed();
        FeedsApi feedsApi = event.getFeedsApi();
        BufferedReader reader = null;
        try {
            if (event.getFrequency() >= event.getCount()) {
                log.info("SC/VC-队列事务 获取到SC事务事件，重试结束 ,任务ID：{}", amazonFeed.getTaskId());
                publishTaskService.updatePublishTaskStatus(amazonFeed.getTaskId(), TaskStatusEnum.FAIL, "FeedId:" + amazonFeed.getFeedId() + ",未获取到执行结果");
//                inventoryPublishTask.channelModify(event.getTask().getChannelId(), InventoryStatus.NORMAL.getValue());
                return;
            }
            Feed apiFeed = feedsApi.getFeed(amazonFeed.getFeedId());
            event.incr();
            String resultFeedDocumentId = apiFeed.getResultFeedDocumentId();
            if (StrUtil.isBlank(resultFeedDocumentId)) {
                // 等待30s再次发布当前事件
                try {
                    log.info("SC/VC-队列事务 未获取到文档结果ID，等待后继续处理");
                    TimeUnit.SECONDS.sleep(60);
                    applicationEventPublisher.publishEvent(event);
                    return;
                } catch (InterruptedException e) {
                    log.error("SC/VC队列事务，当前次数：{}，等待获取报告异常：{}", event.getFrequency(), e.getMessage());
                    publishTaskService.updatePublishTaskStatus(amazonFeed.getTaskId(), TaskStatusEnum.FAIL, "库存修改报告获取异常：" + e.getMessage());
//                    inventoryPublishTask.channelModify(event.getTask().getChannelId(), InventoryStatus.NORMAL.getValue());
                    return;
                }
            }
            // 进行报告处理
            log.info("SC/VC-队列事务 获取到报告ID：{}", resultFeedDocumentId);
            FeedDocument document = feedsApi.getFeedDocument(resultFeedDocumentId);
            // 下载报告
            StrBuilder builder = new StrBuilder();
            URL url = new URL(document.getUrl());
            URLConnection connection = url.openConnection();
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
            String line;
            while ((line = reader.readLine()) != null) {
                builder.append(line);
            }
            String reportXml = builder.toString();
            log.info("SC/VC-队列事务 报告下载完毕:{}", reportXml);
            AmazonInventoryUpdateXml updateXml = JSON.parseObject(XmlUtils.xmlToJson(reportXml), AmazonInventoryUpdateXml.class);
            AmazonInventoryUpdateXml.AmazonEnvelope amazonEnvelope = updateXml.getAmazonEnvelope();
            AmazonInventoryUpdateXml.Message message = amazonEnvelope.getMessage();
            AmazonInventoryUpdateXml.ProcessingReport processingReport = message.getProcessingReport();
            if (AmazonInventoryStatus.SUCCESS.getValue().equals(processingReport.getStatusCode())) {
                log.info("SC/VC-队列事务 处理成功，任务ID:{}", event.getAmazonFeed().getTaskId());
                publishTaskService.updatePublishTaskStatus(amazonFeed.getTaskId(), TaskStatusEnum.SUCCESS, "库存修改成功");
                inventoryPublishTask.inventoryModify(event.getTask());
//                inventoryPublishTask.channelModify(event.getTask().getChannelId(), InventoryStatus.NORMAL.getValue());
                // 更新库存信息
            }
        } catch (Exception e) {
            log.error("SC/VC-队列事务 处理异常，任务ID:{}", event.getAmazonFeed().getTaskId());
            publishTaskService.updatePublishTaskStatus(amazonFeed.getTaskId(), TaskStatusEnum.FAIL, e.getMessage());
//            inventoryPublishTask.channelModify(event.getTask().getChannelId(), InventoryStatus.NORMAL.getValue());
        } finally {
            // 关闭连接
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }
}
