package com.bizark.op.service.service.ticket;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.erp.api.entity.erp.purchase.request.PurchaseApplyApiRequest;
import com.bizark.erp.api.entity.erp.purchase.request.PurchaseApplyLineApiRequest;
import com.bizark.op.api.amazon.common.ApiClient;
import com.bizark.op.api.amazon.common.ApiException;
import com.bizark.op.api.amazon.common.InitApiClient;
import com.bizark.op.api.amazon.vendor.order.api.VendorOrdersApi;
import com.bizark.op.api.amazon.vendor.order.model.*;
import com.bizark.op.api.cons.ScTicketConstant;
import com.bizark.op.api.entity.op.order.SaleOrderItems;
import com.bizark.op.api.entity.op.sys.SysIfInvokeOutbound;
import com.bizark.op.api.entity.op.ticket.*;
import com.bizark.op.api.entity.op.ticket.order.AmzVcPoOrderV;
import com.bizark.op.api.service.order.SaleOrderItemsService;
import com.bizark.op.api.service.sys.ISysIfInvokeOutboundService;
import com.bizark.op.api.service.ticket.AmzVcPoOrderAcceptedTsService;
import com.bizark.op.api.service.ticket.IAmzVcPoOrderTimeRecordService;
import com.bizark.op.api.service.ticket.IScTicketLogService;
import com.bizark.op.api.service.ticket.IScTicketService;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.DateUtil;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.service.mapper.order.SaleOrderItemsMapper;
import com.bizark.op.service.mapper.ticket.AmzVcPoOrderTimeRecordItemMapper;
import com.bizark.op.service.mapper.ticket.AmzVcPoOrderTimeRecordMapper;
import com.bizark.op.service.mapper.ticket.AmzVcPoOrderVMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.bizark.op.service.service.finance.AmzReportServiceImpl.GET_REPORTS_RATE_LIMIT_PERMIT;

//import com.bizark.op.api.entity.op.purchase.request.PurchaseApplyApiRequest;
//import com.bizark.op.api.entity.op.purchase.request.PurchaseApplyLineApiRequest;


/**
 * PO订单同步时间记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-07-22
 */
@Service
@Slf4j
public class AmzVcPoOrderTimeRecordServiceImpl extends ServiceImpl<AmzVcPoOrderTimeRecordMapper, AmzVcPoOrderTimeRecord> implements IAmzVcPoOrderTimeRecordService {

    @Autowired
    private AmzVcPoOrderTimeRecordMapper amzVcPoOrderTimeRecordMapper;

    @Autowired
    private AmzVcPoOrderTimeRecordItemMapper amzVcPoOrderTimeRecordItemMapper;

    @Autowired
    private IScTicketService scTicketService;

    @Autowired
    private AmzVcPoOrderTimeRecordServiceImpl amzVcPoOrderTimeRecordService;

    @Autowired
    private AmzVcPoOrderVMapper amzVcPoOrderVMapper;


    @Autowired
    private IScTicketLogService scTicketLogService;

    @Autowired
    private ISysIfInvokeOutboundService invokeLogStrategy;

    //接单数暂存数量
    @Autowired
    private AmzVcPoOrderAcceptedTsService amzVcPoOrderAcceptedTsService;


    @Autowired
    private SaleOrderItemsService saleOrderItemsService;

    @Autowired
    private InitApiClient initApiClient;

    // 采购申请
    //暂时注释
//    @Autowired
//   private IPurchaseApplyBatchService purchaseApplyBatchService;

    @Autowired
    private SaleOrderItemsMapper saleOrderItemsMapper;


    /**
     * 查询PO订单同步时间记录
     *
     * @param recordId PO订单同步时间记录ID
     * @return PO订单同步时间记录
     */
    @Override
    public AmzVcPoOrderTimeRecord selectAmzVcPoOrderTimeRecordById(Long recordId) {
        return amzVcPoOrderTimeRecordMapper.selectAmzVcPoOrderTimeRecordById(recordId);
    }

    /**
     * 查询PO订单同步时间记录列表
     *
     * @param amzVcPoOrderTimeRecord PO订单同步时间记录
     * @return PO订单同步时间记录
     */
    @Override
    public List<AmzVcPoOrderTimeRecord> selectAmzVcPoOrderTimeRecordList(AmzVcPoOrderTimeRecord amzVcPoOrderTimeRecord) {
        return amzVcPoOrderTimeRecordMapper.selectAmzVcPoOrderTimeRecordList(amzVcPoOrderTimeRecord);
    }


    /**
     * 查询PO订单同步时间记录列表
     *
     * @param amazonOrderNo 亚马逊po订单号
     * @return PO订单同步时间记录
     */
    @Override
    public List<AmzVcPoOrderTimeRecord> selectAmzVcPoOrderTimeRecordByAmazonOrderNo(String amazonOrderNo) {
        return amzVcPoOrderTimeRecordMapper.selectAmzVcPoOrderTimeRecordByAmazonOrderNo(amazonOrderNo);
    }


    /**
     * 查询PO订单同步时间记录
     *
     * @param amzVcPoOrderTimeRecord PO订单同步时间记录
     * @return PO订单同步时间记录
     */
    @Override
    public AmzVcPoOrderTimeRecord selectAmzVcPoOrderTimeRecord(AmzVcPoOrderTimeRecord amzVcPoOrderTimeRecord) {
        return amzVcPoOrderTimeRecordMapper.selectAmzVcPoOrderTimeRecord(amzVcPoOrderTimeRecord);
    }

    /**
     * 新增PO订单同步时间记录
     *
     * @param amzVcPoOrderTimeRecord PO订单同步时间记录
     * @return 结果
     */
    @Override
    public int insertAmzVcPoOrderTimeRecord(AmzVcPoOrderTimeRecord amzVcPoOrderTimeRecord) {
        return amzVcPoOrderTimeRecordMapper.insertAmzVcPoOrderTimeRecord(amzVcPoOrderTimeRecord);
    }

    /**
     * 修改PO订单同步时间记录
     *
     * @param amzVcPoOrderTimeRecord PO订单同步时间记录
     * @return 结果
     */
    @Override
    public int updateAmzVcPoOrderTimeRecord(AmzVcPoOrderTimeRecord amzVcPoOrderTimeRecord) {
        amzVcPoOrderTimeRecord.settingDefaultUpdate();
        return amzVcPoOrderTimeRecordMapper.updateAmzVcPoOrderTimeRecord(amzVcPoOrderTimeRecord);
    }

    /**
     * 批量删除PO订单同步时间记录
     *
     * @param recordIds 需要删除的PO订单同步时间记录ID
     * @return 结果
     */
    @Override
    public int deleteAmzVcPoOrderTimeRecordByIds(Long[] recordIds) {
        return amzVcPoOrderTimeRecordMapper.deleteAmzVcPoOrderTimeRecordByIds(recordIds);
    }

    /**
     * 删除PO订单同步时间记录信息
     *
     * @param recordId PO订单同步时间记录ID
     * @return 结果
     */
    @Override
    public int deleteAmzVcPoOrderTimeRecordById(Long recordId) {
        return amzVcPoOrderTimeRecordMapper.deleteAmzVcPoOrderTimeRecordById(recordId);
    }


    /**
     * 创建USPO工单(默认只接收USPO订单)
     */
    @Override
    @Transactional
    public void createAmzPoOrderTicket(String jsonOrderIds) {

//        List<Long> orderIds = JSONObject.parseArray(jsonOrderIds, Long.class);
//
//
//        List<SaleOrders> saleOrders = saleOrdersMapper.selectBatchIds(orderIds);
//
//        if (CollectionUtils.isEmpty(saleOrders)) {
//            return;
//        }
//
//        for (SaleOrders order : saleOrders) {
//            //3为new状态， new状态下 创建PO订单信息
//            if (order != null &&order.getPurchaseOrderState()!=null&&3==order.getPurchaseOrderState() && !StringUtils.isEmpty(order.getChannelCreated())) {
//
//
//
//                //获取店铺信息
//                Account accountInfo = accountService.getById(order.getChannelId().intValue());
//                String accountName = accountInfo == null ? "" : accountInfo.getAccountInit();
//
//
//                //判断同店铺同时间内 是否存在记录主信息
//                AmzVcPoOrderTimeRecord amzVcPoOrderTimeRecord = new AmzVcPoOrderTimeRecord();
////                amzVcPoOrderTimeRecord.setOrderDate(order.getChannelCreated().substring(0, 10));  //获取订单年月日时间
//                amzVcPoOrderTimeRecord.setShopId(Long.valueOf(order.getChannelId())); //店铺ID
//                amzVcPoOrderTimeRecord.setSellingPartyId(order.getCustomerReferNo()); //供应商编码
//                AmzVcPoOrderTimeRecord amzVcPoOrderTimeRecordInfo = amzVcPoOrderTimeRecordService.selectAmzVcPoOrderTimeRecord(amzVcPoOrderTimeRecord);
//                AmzVcPoOrderTimeRecordItem amzVcPoOrderTimeRecordItem = null;
//                if (amzVcPoOrderTimeRecordInfo == null) {
//                    amzVcPoOrderTimeRecord.settingDefaultValue();//设置默认值
//                    //新增至主表
//                    amzVcPoOrderTimeRecord.setSellingPartyId(order.getCustomerReferNo());
//                     amzVcPoOrderTimeRecord.setOrganizationId(order.getOrgId()); //记录组织ID根据订单主表进行获取
//                    amzVcPoOrderTimeRecordService.insertAmzVcPoOrderTimeRecord(amzVcPoOrderTimeRecord);
//
//                    //新增至明细表
//                    amzVcPoOrderTimeRecordItem = new AmzVcPoOrderTimeRecordItem();
//                    amzVcPoOrderTimeRecordItem.setPurchaseOrderNumber(order.getOrderNo()); //PO订单号
//                    amzVcPoOrderTimeRecordItem.setRecordId(amzVcPoOrderTimeRecord.getRecordId());//关联ID
//                    amzVcPoOrderTimeRecordItem.setShopId(order.getChannelId());//店铺ID
//                    amzVcPoOrderTimeRecordItem.setOrderId(order.getId()); //PO主表ID
//                    amzVcPoOrderTimeRecordItem.setOrderDate(amzVcPoOrderTimeRecord.getOrderDate());//订单格式化时间
//                    amzVcPoOrderTimeRecordItem.setOrganizationId(order.getOrgId()); //组织ID
//                    amzVcPoOrderTimeRecordItemService.insertAmzVcPoOrderTimeRecordItem(amzVcPoOrderTimeRecordItem);//新增至明细
//
//                    //创建工单
//                    ScTicket scTicket = scTicketService.selectScTicketBySource(amzVcPoOrderTimeRecord.getRecordId(), ScTicketConstant.TICKET_SOURCE_DOCUMENT_PO_ORDER_RECORD);
//                    if (null != scTicket) { //是否存在此时间类型工单
//                        continue;
//                    } else {
//                        scTicket = new ScTicket();
//                    }
//
//                    //创建工单
//                    //使用格式化后的时间
//                    String ticketName = amzVcPoOrderTimeRecord.getOrderDate() + "（USPO订单确认)";
//                    scTicket.setTicketName(ticketName); //工单名称
//                    scTicket.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(amzVcPoOrderTimeRecord.getRecordId().toString(), "l", 5, "0")); //工单编号
//                    scTicket.setTicketSource(ScTicketConstant.TICKET_PO_SOURCE_ORDER_API);// 工单来源
//                    scTicket.setTicketType(ScTicketConstant.TICKET_TYPE_PO_USPO_ORDER); //工单类型
//                    scTicket.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH); //优先级（高）
//                    scTicket.setSourceId(amzVcPoOrderTimeRecord.getRecordId());//来源ID
//                    scTicket.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_PO_ORDER_RECORD); //来源单据
//                    scTicket.setShopId(order.getChannelId());  //店铺ID
//                    scTicket.setRemark("店铺名称：" + accountName); //工单描述
//                    scTicketService.insertScTicket(scTicket);
//
//
//                    if (amzVcPoOrderTimeRecord.getRecordId()!=null){
//                        //更新工单编号至明细表中
//                        amzVcPoOrderTimeRecordService.updateTicketNumber(amzVcPoOrderTimeRecord.getRecordId(), scTicket.getTicketNumber());
//                    }
//                } else {
//                    //工单存在（关联到对应工单中）
//                    amzVcPoOrderTimeRecordItem = new AmzVcPoOrderTimeRecordItem();
//                    amzVcPoOrderTimeRecordItem.setPurchaseOrderNumber(order.getOrderNo()); //PO订单号
//                    amzVcPoOrderTimeRecordItem.setRecordId(amzVcPoOrderTimeRecordInfo.getRecordId());//关联ID
//
//                    //判断是否重复
//                    List<AmzVcPoOrderTimeRecordItem> amzVcPoOrderTimeRecordItems = amzVcPoOrderTimeRecordItemService.selectAmzVcPoOrderTimeRecordItemList(amzVcPoOrderTimeRecordItem);
//                    if (CollectionUtils.isEmpty(amzVcPoOrderTimeRecordItems)) {
//                        amzVcPoOrderTimeRecordItem.setShopId(order.getChannelId());//店铺ID
//                        amzVcPoOrderTimeRecordItem.setOrderId(order.getId()); //PO主表ID
//                        amzVcPoOrderTimeRecordItem.setOrderDate(amzVcPoOrderTimeRecord.getOrderDate());//订单格式化时间
//                        amzVcPoOrderTimeRecordItem.setOrganizationId(order.getOrgId()); //设置组织ID
//                        amzVcPoOrderTimeRecordItemService.insertAmzVcPoOrderTimeRecordItem(amzVcPoOrderTimeRecordItem);//新增至明细
//                    }
//                }
//            }
//        }
    }



    /**
     * @param
     * @param recordId
     * @description: 查询po确认工单所有的订单信息
     * @author: Moore
     * @date: 2024/3/11 22:55
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.order.AmzVcPoOrderV>
     **/
    @Override
    public List<AmzVcPoOrderV> selectAmzVcPoOrderTimeRecordVById(Long recordId) {
        //获取记录ID对应的工单信息
        List<AmzVcPoOrderTimeRecordItem> amzVcPoOrderTimeRecordItems = amzVcPoOrderTimeRecordItemMapper.selectAmzVcPoOrderTimeRecordItemByRecordId(recordId);
        //获取该工单下所有订单号
        List<Long> orderIds = amzVcPoOrderTimeRecordItems.stream().map(AmzVcPoOrderTimeRecordItem::getOrderId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(orderIds)) {
            //查询订单信息
            List<AmzVcPoOrderV> amzvcPoorderVS = amzVcPoOrderVMapper.selectPoOrderAndAcceptedTsByPoNumberList(orderIds);
            //获取采购申请单
            if (!StringUtils.isEmpty(amzVcPoOrderTimeRecordItems.get(0).getPurchaseReqNo())) {
                //一个记录总行，仅会有一个 采购申请号
//                List<PurchaseApplyCheckApiResult> purchaseApplyCheckApiResults = purchaseApplyBatchService.checkByApplyNo(amzVcPoOrderTimeRecordItems.get(0).getPurchaseReqNo());
//                Map<String, PurchaseApplyCheckApiResult> asinApplyList = purchaseApplyCheckApiResults.stream().collect(Collectors.toMap(PurchaseApplyCheckApiResult::fetchGroupKey, Function.identity(), (v1, v2) -> v1));
//                amzvcPoorderVS.forEach(item -> {
//                    String asinGroupFlag = item.getAsin() + "#" + item.getOrderNo();
//                    item.setPurchaseReqNo(amzVcPoOrderTimeRecordItems.get(0).getPurchaseReqNo()); //设置采购申请号
//                    PurchaseApplyCheckApiResult purchaseApplyCheckApiResult = asinApplyList.get(asinGroupFlag);
//                    item.setCheckStatus(!ObjectUtils.isEmpty(purchaseApplyCheckApiResult) ? purchaseApplyCheckApiResult.getCheckStatus() : 0);//设置采购订单号
//                });
            }else {
                amzvcPoorderVS.forEach(item->item.setCheckStatus(0)); //无采购单号
            }
            return amzvcPoorderVS;
        }
        return Collections.emptyList();
    }




    /**
     * @param
     * @param recordId 记录ID
     * @param ticketId 工单ID
     * @param shopId   店铺ID
     * @description:
     * @author: Moore
     * @date: 2024/3/13 23:54
     * @return: java.lang.Integer
     **/
    @Override
    public void confirmBatchSendPoOrder(Long recordId, Long ticketId, Long shopId, Integer organizationId) {

        //查询记录主表
        AmzVcPoOrderTimeRecord amzVcPoOrderTimeRecord = amzVcPoOrderTimeRecordMapper.selectAmzVcPoOrderTimeRecordById(recordId);

        //获取接单明细（仅到订单号维度）
        List<AmzVcPoOrderTimeRecordItem> amzVcPoOrderTimeRecordItems = amzVcPoOrderTimeRecordItemMapper.selectAmzVcPoOrderTimeRecordItemByRecordId(recordId);
        if (CollectionUtils.isEmpty(amzVcPoOrderTimeRecordItems)) {
            throw new CustomException("无对应可接单信息");
        }

        //获取对应工单的订单信息
        List<AmzVcPoOrderV> amzVcPoOrderVS = this.selectAmzVcPoOrderTimeRecordVById(recordId);

        if (CollectionUtils.isEmpty(amzVcPoOrderVS)) {
            throw new CustomException("订单信息获取失败");
        }
        List<AmzVcPoOrderV> endShipDate = amzVcPoOrderVS.stream().filter(item -> item.getLatestShipDate() == null).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(endShipDate)) {
            throw new CustomException("最晚到货时间不能为空");
        }


        //获取接受状态数据
        List<AmzVcPoOrderV> statusFilter = amzVcPoOrderVS.stream().filter(item -> "Accepted".equals(item.getAcknowledgementCode())).collect(Collectors.toList());

        //  校验是否符合提交发货申请
        List<AmzVcPoOrderV> shipDate = statusFilter.stream().filter(item -> item.getWarehouseId() == null).collect(Collectors.toList());
        List<AmzVcPoOrderV> sellingPartyList = amzVcPoOrderVS.stream().filter(item -> StringUtils.isEmpty(item.getSellingPartyId())).collect(Collectors.toList());
        List<AmzVcPoOrderV> skuList = amzVcPoOrderVS.stream().filter(item -> StringUtils.isEmpty(item.getSku())).collect(Collectors.toList());


        //有接单，且接单行有未找到目的仓配置
        if (!CollectionUtils.isEmpty(statusFilter)&&!CollectionUtils.isEmpty(shipDate)) {
            throw new CustomException("发货申请，目的仓库未维护!" + shipDate.stream().map(AmzVcPoOrderV::getShipToPartyId).distinct().collect(Collectors.joining(",")));
        }

        if (!CollectionUtils.isEmpty(sellingPartyList)) {
            throw new CustomException("发货申请，供应商获取失败!");
        }
        if (!CollectionUtils.isEmpty(skuList)) {
            throw new CustomException("发货申请，SKU不能为空!");
        }

        //同一单号归类分组
        Map<String, List<AmzVcPoOrderV>> amzVcPoOrderItemCollect = amzVcPoOrderVS.stream().collect(Collectors.groupingBy(AmzVcPoOrderV::getOrderNo));

        //请求参数
        SubmitAcknowledgementRequest submitAcknowledgementRequest = new SubmitAcknowledgementRequest();

        //请求参数封装
        for (String orderNoKey : amzVcPoOrderItemCollect.keySet()) {
            OrderAcknowledgement orderAcknowledgement = this.confirmDataPack(orderNoKey, amzVcPoOrderItemCollect.get(orderNoKey)); //数据封装
            submitAcknowledgementRequest.addAcknowledgementsItem(orderAcknowledgement); //设置每行信息
        }

        ApiClient apiClient = initApiClient.initApiClientNew(shopId.intValue(), GET_REPORTS_RATE_LIMIT_PERMIT);
        if (apiClient == null) {
            throw new CustomException("鉴权信息获取失败！");
        }
        VendorOrdersApi api = new VendorOrdersApi(apiClient);
        log.info("获取到amzApi信息：{}", JSONObject.toJSONString(api));
        SysIfInvokeOutbound ifInvokeOutbound = new SysIfInvokeOutbound();
        ifInvokeOutbound.setInterfaceName("发送（确认）亚马逊PO订单接单数量信息");
        ifInvokeOutbound.setInterfaceUrl(api.getApiClient().getBasePath() + "/vendor/orders/v1/acknowledgements");
        ifInvokeOutbound.setRequestParameter(JSONObject.toJSONString(submitAcknowledgementRequest));
        ifInvokeOutbound.setRequestTime(new Date());
        log.info("PO订单接单数量请求参数：{}", JSONObject.toJSONString(submitAcknowledgementRequest));
        //记录工单日志
        ScTicketLog ticketLog = new ScTicketLog();
        ticketLog.setTicketId(ticketId);
        ticketLog.setOperateType("SUBMIT");

        try {
            SubmitAcknowledgementResponse submitAcknowledgementResponse = api.submitAcknowledgement(submitAcknowledgementRequest);

            //插入接口调用日志
            ifInvokeOutbound.setResponseTime(System.currentTimeMillis());
            ifInvokeOutbound.setResponseCode("200");
            ifInvokeOutbound.setResponseContent(submitAcknowledgementResponse.toString()); //响应内容
            invokeLogStrategy.insertSysIfInvokeOutbound(ifInvokeOutbound);
            log.info("PO订单接单数量响应：{}", submitAcknowledgementResponse.toString());

            //更新主表确认状态及时间
            AmzVcPoOrderTimeRecord amzVcPoOrderTimeRecords = new AmzVcPoOrderTimeRecord();
            amzVcPoOrderTimeRecords.setId(recordId);
            if (amzVcPoOrderTimeRecord != null && "Y".equals(amzVcPoOrderTimeRecord.getConfirmAcceptedStatus())) {
                amzVcPoOrderTimeRecords.setUpdateConfirmAcceptedTime(DateUtils.getNowDate()); //二次确认时间更新
            } else {
                amzVcPoOrderTimeRecords.setConfirmAcceptedStatus("Y"); //首次确认
                amzVcPoOrderTimeRecords.setConfirmAcceptedTime(DateUtils.getNowDate());
            }
            amzVcPoOrderTimeRecordService.updateAmzVcPoOrderTimeRecord(amzVcPoOrderTimeRecords);

            //记录工单日志
            ticketLog.setOperateTime(new Date());
            try {
                ticketLog.setOperateContent("接单确认成功，" + "transactionId： " + submitAcknowledgementResponse.getPayload().getTransactionId());
            } catch (Exception e) {
                log.info("获取工单");
            }
            scTicketLogService.insertScTicketLog(ticketLog);

            log.info("uspo确认后更新订单信息：{}", JSONObject.toJSONString(amzVcPoOrderVS));
            for (AmzVcPoOrderV amzVcPoOrderV : amzVcPoOrderVS) {
                if (amzVcPoOrderV.getItemId() == null) {
                    continue;
                }
                //更新接单数据至订单表
                LambdaUpdateWrapper<SaleOrderItems> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.
                        eq(SaleOrderItems::getId, amzVcPoOrderV.getItemId())
                        .set(SaleOrderItems::getAcknowledgementCode, amzVcPoOrderV.getAcknowledgementCode())//接受状态
                        .set(SaleOrderItems::getConfirmedQty, amzVcPoOrderV.getAcceptedQuantity()); //接单数量
                saleOrderItemsMapper.update(null, lambdaUpdateWrapper);

                //更新临时表已接单数量
                LambdaUpdateWrapper<AmzVcPoOrderAcceptedTs> tsUpdate = new LambdaUpdateWrapper<>();
                tsUpdate.
                        eq(AmzVcPoOrderAcceptedTs::getPurchaseOrderNumber, amzVcPoOrderV.getOrderNo())
                        .eq(AmzVcPoOrderAcceptedTs::getAsin, amzVcPoOrderV.getAsin())
                        .set(AmzVcPoOrderAcceptedTs::getConfirmAcknowledgementCode, amzVcPoOrderV.getAcknowledgementCode())//接受原因
                        .set(AmzVcPoOrderAcceptedTs::getConfirmAcceptedQuantity, amzVcPoOrderV.getAcceptedQuantity()) //接单数量
                        .set(AmzVcPoOrderAcceptedTs::getConfirmPlanArriveTime, amzVcPoOrderV.getPlanArriveTime()); //预计交货时间
                if (StringUtils.isEmpty(amzVcPoOrderTimeRecord.getPurchaseReqNo())) {
                    tsUpdate.set(AmzVcPoOrderAcceptedTs::getConfirmSku, amzVcPoOrderV.getSku()); //接单SKU，只记录第一次
                }
                amzVcPoOrderAcceptedTsService.update(null, tsUpdate);
            }

            //只对有接收的行，创建采购单
            if (!CollectionUtils.isEmpty(statusFilter)){
                this.createPurchasingReq(statusFilter, ticketId, recordId, organizationId, StringUtils.isEmpty(amzVcPoOrderTimeRecord.getPurchaseReqNo()));
            }

        } catch (ApiException e) {
            ifInvokeOutbound.setResponseTime(System.currentTimeMillis());
            ifInvokeOutbound.setResponseContent(e.getMessage()); //响应内容
            ifInvokeOutbound.setResponseCode(String.valueOf(e.getCode()));
            invokeLogStrategy.insertSysIfInvokeOutbound(ifInvokeOutbound);
            log.error("发送亚马逊PO订单接单数量信息异常:{}", e.toString());
            ticketLog.setOperateContent("USPO订单，商品接单数量确认失败：" + e.getMessage()); //确认工单日志
            //记录工单日志
            ticketLog.setOperateTime(new Date());
            scTicketLogService.insertScTicketLog(ticketLog);

            throw new CustomException("确认失败,请联系管理员!");

        }
    }

    /**
     * @description: 前置校验
     * @author: Moore
     * @date: 2024/3/20 15:38
     * @param
     * @param recordId
     * @param ticketId
     * @param shopId
     * @return: java.lang.String
    **/
    @Override
    public String confirmBatchSendPoOrderVerify(Long recordId, Long ticketId, Long shopId) {

        StringBuilder stringBuilder = new StringBuilder();
        AmzVcPoOrderTimeRecord amzVcPoOrderTimeRecord = amzVcPoOrderTimeRecordMapper.selectById(recordId);
        //无申请即不会有采购订单，即不做校验
        if ("Y".equals(amzVcPoOrderTimeRecord.getConfirmAcceptedStatus())) { //是否已确认
            if (StringUtils.isEmpty(amzVcPoOrderTimeRecord.getPurchaseReqNo())) { //代表申请单创建失败
                return null;
            }
            //查询接单及采购订单信息
            List<AmzVcPoOrderV> amzVcPoOrderVS = this.selectAmzVcPoOrderTimeRecordVById(recordId);

            //已存在采购订单，但修改了接单数量或接单原因
            List<AmzVcPoOrderV> filterAmzVc = amzVcPoOrderVS.stream().filter(item ->
                    item.getCheckStatus() == 1 && (item.getAcceptedQuantity() != item.getConfirmAcceptedQuantity() ||
                            !item.getAcknowledgementCode().equals(item.getConfirmAcknowledgementCode())
                            || cn.hutool.core.date.DateUtil.compare(item.getPlanArriveTime(), item.getConfirmPlanArriveTime()) != 0)
            ).collect(Collectors.toList());

            ArrayList<AmzVcPoOrderAcceptedTs> amzVcPoOrderAcceptedTs = new ArrayList<>();

            if (!CollectionUtils.isEmpty(filterAmzVc)) {
                stringBuilder.append("以下ASIN已存在采购订单,确认数量或原因将被还原:");
                for (AmzVcPoOrderV amzVcPoOrderV : filterAmzVc) {
                    stringBuilder.append("<br/>" + amzVcPoOrderV.getOrderNo() + "_" + amzVcPoOrderV.getAsin());
                    AmzVcPoOrderAcceptedTs amzVcPoOrderAcceptedTsUpate = new AmzVcPoOrderAcceptedTs();
                    amzVcPoOrderAcceptedTsUpate.setId(amzVcPoOrderV.getTsId());
                    amzVcPoOrderAcceptedTsUpate.setAcknowledgementCode(amzVcPoOrderV.getConfirmAcknowledgementCode());
                    amzVcPoOrderAcceptedTsUpate.setAcceptedQuantity(amzVcPoOrderV.getConfirmAcceptedQuantity());
                    amzVcPoOrderAcceptedTs.add(amzVcPoOrderAcceptedTsUpate);
                }
                //还原接单数据
                amzVcPoOrderAcceptedTsService.updateBatchById(amzVcPoOrderAcceptedTs);
                return stringBuilder.toString();
            }
        }
        return null;
    }


    /**
     * 创建采购申请
     */

    /**
     * @param amzVcPoOrderVS po单参数
     * @description: 创建采购申请
     * @author: Moore
     * @date: 2024/3/19 10:35
     * @return: void
     **/

    /**
     * @description:创建采购申请
     * @author: Moore
     * @date: 2024/3/21 13:59
     * @param
     * @param amzVcPoOrderVS  申请信息
     * @param ticketId      工单信息
     * @param recordId  recordId 主键
     * @param updateFlage 是否更新 true 更新
     * @return: void
    **/
    public void createPurchasingReq(List<AmzVcPoOrderV> amzVcPoOrderVS, Long ticketId, Long recordId,Integer organizationId, Boolean updateFlage) {

        ScTicketLog ticketLog = new ScTicketLog();
        //暂时注释
        ticketLog.setTicketId(ticketId);
        ticketLog.setOperateType("SUBMIT");
        if (updateFlage) { //更新
            try {
                PurchaseApplyApiRequest purchaseApplyApiRequest = new PurchaseApplyApiRequest();
                purchaseApplyApiRequest.setApprovalState(0);
                purchaseApplyApiRequest.setBusinessType(2); //VC-DI业务
                purchaseApplyApiRequest.setOrganizationId(organizationId);//组织ID
                List<PurchaseApplyLineApiRequest> purchaseApplyLineApiRequests = new ArrayList<>();
                for (AmzVcPoOrderV amzVcPoOrderV : amzVcPoOrderVS) {
                    PurchaseApplyLineApiRequest purchaseApplyLineApiRequest = new PurchaseApplyLineApiRequest();
                    purchaseApplyLineApiRequest.setSku(amzVcPoOrderV.getSku()); //款号
                    purchaseApplyLineApiRequest.setAsin(amzVcPoOrderV.getAsin());//ASIN
                    purchaseApplyLineApiRequest.setSalesOrderNo(amzVcPoOrderV.getOrderNo());//订单号
                   purchaseApplyLineApiRequest.setTargetWarehouseId(amzVcPoOrderV.getWarehouseId()); //目的仓ID
                    purchaseApplyLineApiRequest.setVendor(amzVcPoOrderV.getSellingPartyId()); //供应商
                    purchaseApplyLineApiRequest.setPlanPurchaseQuantity(amzVcPoOrderV.getAcceptedQuantity()); //数量
                    purchaseApplyLineApiRequest.setWindowEnd(amzVcPoOrderV.getLatestShipDate()); //最晚发货时间
                    purchaseApplyLineApiRequests.add(purchaseApplyLineApiRequest);
                }
                purchaseApplyApiRequest.setPurchaseApplyLineApiRequests(purchaseApplyLineApiRequests); //设置行信息
                log.info("创建采购申请参数：{}", JSONObject.toJSONString(purchaseApplyLineApiRequests));
//                String purchaseReqNumber = purchaseApplyBatchService.createPurchaseRequestApi(purchaseApplyApiRequest);
                String purchaseReqNumber = null;
                ticketLog.setOperateContent("创建采购申请成功,申请号：" + purchaseReqNumber);
                ticketLog.setOperateTime(new Date());
                scTicketLogService.insertScTicketLog(ticketLog);
                //更新采购申请申请单号
                if (!StringUtils.isEmpty(purchaseReqNumber)) {
                    AmzVcPoOrderTimeRecord amzVcPoOrderTimeRecord = new AmzVcPoOrderTimeRecord();
                    amzVcPoOrderTimeRecord.setId(recordId);
                    amzVcPoOrderTimeRecord.setPurchaseReqNo(purchaseReqNumber);
                    amzVcPoOrderTimeRecordMapper.updateById(amzVcPoOrderTimeRecord);
                }
            } catch (Exception e) {
                log.error("创建采购申请单失败：{}", e);
                ticketLog.setOperateTime(new Date());
                ticketLog.setOperateContent("创建采购申请失败,原因：" + e.getMessage());
                scTicketLogService.insertScTicketLog(ticketLog);
                throw new CustomException("确认接单成功,创建采购申请失败！");
            }
        } else {
            for (AmzVcPoOrderV amzVcPoOrderV : amzVcPoOrderVS)  {
                if (amzVcPoOrderV.getCheckStatus()==1){ //有采购单不更新
                    continue;
                }

                PurchaseApplyLineApiRequest purchaseApplyLineApiRequest = new PurchaseApplyLineApiRequest();
                purchaseApplyLineApiRequest.setAsin(amzVcPoOrderV.getAsin());
                purchaseApplyLineApiRequest.setSalesOrderNo(amzVcPoOrderV.getOrderNo());
                purchaseApplyLineApiRequest.setPlanPurchaseQuantity(amzVcPoOrderV.getAcceptedQuantity());
//                purchaseApplyBatchService.updatePurchaseLineRequestApi(purchaseApplyLineApiRequest);
            }
            ticketLog.setOperateContent("更新采购单数据");
            ticketLog.setOperateTime(new Date());
            scTicketLogService.insertScTicketLog(ticketLog);
        }
    }


    /**
     * @Parm 更新订单明细表接单数量
     * 创建shipment工单信息
     */
    private void updateSaleOrderItemsAcceptedQuantity(List<AmzVcPoOrderV> amzVcPoOrderVS) {
        for (AmzVcPoOrderV amzVcPoOrderV : amzVcPoOrderVS) {
            if (amzVcPoOrderV.getItemId() == null) {
                continue;
            }
            SaleOrderItems saleOrderItems = new SaleOrderItems();
            saleOrderItems.setId(amzVcPoOrderV.getItemId().intValue());//订单行ID
            saleOrderItems.setConfirmedQty(amzVcPoOrderV.getAcceptedQuantity() == null ? 0 : amzVcPoOrderV.getAcceptedQuantity()); //接单数量
            saleOrderItemsService.updateConfirmedQtyById(saleOrderItems);
        }


    }


    /**
     * @Parm 父工单 ,要创建的子工单信息
     * 创建shipment工单信息
     */
    private void createShipemntTicket(ScTicket parentscTicket, Map<String, List<AmzVcPoOrderV>> amzVcPoOrderItemCollect) {
        //为每个订单号生成 单独的发货工单
        amzVcPoOrderItemCollect.forEach((key, value) -> {
            Long sourceId = value.get(0).getId();//来源ID，记录明细表主键
            ScTicket scTicketSoure = scTicketService.selectScTicketBySource(sourceId, ScTicketConstant.TICKET_SOURCE_DOCUMENT_PO_ORDER_RECORD_ITEM);
            if (scTicketSoure != null) {
                return;//跳过当前
            }
            AmzVcPoOrderV amzVcPoOrderV = value.get(0);
            //工单编号
            String ticketNumber = DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(amzVcPoOrderV.getId().toString(), "l", 5, "0");
            ScTicket scTicket = new ScTicket();
            scTicket.setOrganizationId(amzVcPoOrderV.getOrganizationId().longValue()); //使用订单的组织ID
            scTicket.setTicketNumber(ticketNumber);//工单编号
            scTicket.setTicketType(ScTicketConstant.TICKET_TYPE_ORDER_SHIPMENT); //工单类型 （订单发货）
            scTicket.setTicketName(key + "订单发货"); //工单名称(订单号 + 订单发货)
            scTicket.setParentTicketId(parentscTicket.getId());//
            scTicket.setRemark("店铺名称" + amzVcPoOrderV.getShopName()); //工单描述
            scTicket.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_PO_ORDER_RECORD_ITEM);
            scTicket.setSourceId(sourceId);
            scTicket.setCreatedAt(new Date()); //创建时间
            scTicket.setAmazonOrderId(key);//订单号
            scTicketService.insertScTicket(scTicket);
        });


    }


    /**
     * @param
     * @param orderNo
     * @param poOrderitem
     * @description: 确认接单工单请求参数封装
     * @author: Moore
     * @date: 2024/3/13 10:05
     * @return: com.bizark.op.api.amazon.vendor.order.model.OrderAcknowledgement
     **/
    private OrderAcknowledgement confirmDataPack(String orderNo, List<AmzVcPoOrderV> poOrderitem) {
        //请求参数对象信息
        OrderAcknowledgement orderAcknowledgement = new OrderAcknowledgement();
        orderAcknowledgement.setPurchaseOrderNumber(orderNo);//订单编号

        ////收货方名称/地址/税务信息
        PartyIdentification partyIdentification = new PartyIdentification();
        partyIdentification.setPartyId(poOrderitem.get(0).getSellingPartyId());   //供应商代码partyId
        orderAcknowledgement.setSellingParty(partyIdentification);

        //确认采购订单日期(ISO-8601) UTC时间
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        df.setTimeZone(TimeZone.getTimeZone("Africa/Abidjan"));
        String nowAsISO = df.format(DateUtils.getNowDate());
        orderAcknowledgement.setAcknowledgementDate(nowAsISO);

        /*确认行信息*/
        List<OrderAcknowledgementItem> itmesList = new ArrayList<OrderAcknowledgementItem>();

        //订购数量

        for (AmzVcPoOrderV vcPoOrderV : poOrderitem) {
            OrderAcknowledgementItem orderAcknowledgementItem = new OrderAcknowledgementItem();
            orderAcknowledgementItem.setItemSequenceNumber(vcPoOrderV.getItemSequenceNumber());//项目编号(选填)
            orderAcknowledgementItem.setAmazonProductIdentifier(vcPoOrderV.getAsin()); //ASIN (选填)
            orderAcknowledgementItem.setVendorProductIdentifier(vcPoOrderV.getVendorProductIdentifier());// 供应商产品标识(选填)

            //订购数量详细信息（即订单信息）
            ItemQuantity orderedQuantity = new ItemQuantity();
            orderedQuantity.setAmount(vcPoOrderV.getQuantity()); //订购数量（选填）
            if ("Cases".equals(vcPoOrderV.getUnitOfMeasure())) {
                orderedQuantity.setUnitOfMeasure(ItemQuantity.UnitOfMeasureEnum.CASES); //单位
            } else if ("Eaches".equals(vcPoOrderV.getUnitOfMeasure())) {
                orderedQuantity.setUnitOfMeasure(ItemQuantity.UnitOfMeasureEnum.EACHES);
            }
            if (StringUtils.isNotEmpty(vcPoOrderV.getUnitSize())) {
                orderedQuantity.setUnitSize(Integer.parseInt(vcPoOrderV.getUnitSize()));  //单位长度 （选填）
            }
            orderAcknowledgementItem.setOrderedQuantity(orderedQuantity); //设置订购数量

            //确认接单数量
            List<OrderItemAcknowledgement> acknowledgements = new ArrayList<OrderItemAcknowledgement>();

            /*确认CODE Accepted （接受/同意） Backordered(延期交货) Rejected (拒绝) */
            if ("Accepted".equals(vcPoOrderV.getAcknowledgementCode())) {
                this.amzVcPoorderAcknowledgedQuantitySync(acknowledgements, vcPoOrderV);
            } else if ("Backordered".equals(vcPoOrderV.getAcknowledgementCode())) { //延期交货
                this.amzVcPoorderAcknowledgedQuantityBackorderedSync(acknowledgements, vcPoOrderV);
            } else if ("Rejected1".equals(vcPoOrderV.getAcknowledgementCode()) || "Rejected2".equals(vcPoOrderV.getAcknowledgementCode()) || "Rejected3".equals(vcPoOrderV.getAcknowledgementCode())) {
                OrderItemAcknowledgement orderItemAcknowledgementReject = new OrderItemAcknowledgement();
                //设置拒绝
                orderItemAcknowledgementReject.setAcknowledgementCode(OrderItemAcknowledgement.AcknowledgementCodeEnum.REJECTED);
                //设置拒绝原因
                if ("Rejected1".equals(vcPoOrderV.getAcknowledgementCode())) {
                    orderItemAcknowledgementReject.setRejectionReason(OrderItemAcknowledgement.RejectionReasonEnum.TEMPORARILYUNAVAILABLE);
                } else if ("Rejected2".equals(vcPoOrderV.getAcknowledgementCode())) {
                    orderItemAcknowledgementReject.setRejectionReason(OrderItemAcknowledgement.RejectionReasonEnum.INVALIDPRODUCTIDENTIFIER);
                } else if ("Rejected3".equals(vcPoOrderV.getAcknowledgementCode())) {
                    orderItemAcknowledgementReject.setRejectionReason(OrderItemAcknowledgement.RejectionReasonEnum.OBSOLETEPRODUCT);
                }
                //设置拒绝参数(拒绝数量)
                ItemQuantity acknowledgedQuantityReject = new ItemQuantity();
                acknowledgedQuantityReject.setAmount(vcPoOrderV.getQuantity()); //拒绝均为全量拒绝,将所有下单数量拒绝
                if ("Cases".equals(vcPoOrderV.getUnitOfMeasure())) {
                    acknowledgedQuantityReject.setUnitOfMeasure(ItemQuantity.UnitOfMeasureEnum.CASES); //数量单位（选填）
                } else if ("Eaches".equals(vcPoOrderV.getUnitOfMeasure())) {
                    acknowledgedQuantityReject.setUnitOfMeasure(ItemQuantity.UnitOfMeasureEnum.EACHES);
                }
                acknowledgedQuantityReject.setUnitSize(Integer.parseInt(vcPoOrderV.getUnitSize()));//单位尺寸 （选填）

                orderItemAcknowledgementReject.setAcknowledgedQuantity(acknowledgedQuantityReject); //确认数量信息设置
                acknowledgements.add(orderItemAcknowledgementReject); //添加进拒绝项
            } else {
                throw new CustomException("请填写接单状态");
            }
            orderAcknowledgementItem.setItemAcknowledgements(acknowledgements); //明细行-确认信息
            itmesList.add(orderAcknowledgementItem);
        }
        orderAcknowledgement.setItems(itmesList); //设置行信息
        return orderAcknowledgement;
    }


    /**
     * 封装确认接单(接收)
     *
     * @param orderItemAcknowledgements 确认接单结果集信息
     * @param vcPoOrderV                单个订单每行接单信息
     */
    private void amzVcPoorderAcknowledgedQuantitySync(List<OrderItemAcknowledgement> orderItemAcknowledgements, AmzVcPoOrderV vcPoOrderV) {

        Integer acceptedQuantity = vcPoOrderV.getAcceptedQuantity();//接单数量
        Integer orderedQuantity1 = vcPoOrderV.getQuantity();//下单数量
        if (acceptedQuantity == 0) {
            throw new CustomException("接受状态下，接单数量不能为0");
        }

        //预计到货时间
        Date latestShipDate = vcPoOrderV.getLatestShipDate();
        String shipWindow = cn.hutool.core.date.DateUtil.format(latestShipDate,  "yyyy-MM-dd'T'HH:mm:ss'Z'");

        if (acceptedQuantity == orderedQuantity1) {             //全量接收
            OrderItemAcknowledgement orderItemAcknowledgement = new OrderItemAcknowledgement();
            orderItemAcknowledgement.setAcknowledgementCode(OrderItemAcknowledgement.AcknowledgementCodeEnum.ACCEPTED);
            orderItemAcknowledgement.setScheduledShipDate(shipWindow); //预计交货时间

            //确认接单信息
            ItemQuantity acknowledgedQuantity = new ItemQuantity();
            acknowledgedQuantity.setAmount(vcPoOrderV.getAcceptedQuantity()); //订购数量(此处填写实际接单数量)
            if ("Cases".equals(vcPoOrderV.getUnitOfMeasure())) {
                acknowledgedQuantity.setUnitOfMeasure(ItemQuantity.UnitOfMeasureEnum.CASES); //数量单位（选填）
            } else if ("Eaches".equals(vcPoOrderV.getUnitOfMeasure())) {
                acknowledgedQuantity.setUnitOfMeasure(ItemQuantity.UnitOfMeasureEnum.EACHES);
            }
            if (StringUtils.isNotEmpty(vcPoOrderV.getUnitSize())) {
                acknowledgedQuantity.setUnitSize(Integer.parseInt(vcPoOrderV.getUnitSize()));//单位尺寸 （选填）
            }

            orderItemAcknowledgement.setAcknowledgedQuantity(acknowledgedQuantity); //确认数量信息设置
            orderItemAcknowledgements.add(orderItemAcknowledgement);
        } else {

            BigDecimal bigDecimalOrer = new BigDecimal(orderedQuantity1); //下单数量
            BigDecimal bigDecimalAccepted = new BigDecimal(acceptedQuantity);//接单数量
            BigDecimal rejectCount = bigDecimalOrer.subtract(bigDecimalAccepted); // 拒绝数量数量

            //接收
            OrderItemAcknowledgement orderItemAcknowledgement = new OrderItemAcknowledgement();
            orderItemAcknowledgement.setAcknowledgementCode(OrderItemAcknowledgement.AcknowledgementCodeEnum.ACCEPTED);
            orderItemAcknowledgement.setScheduledShipDate(shipWindow);

            //确认接单信息
            ItemQuantity acknowledgedQuantity = new ItemQuantity();
            acknowledgedQuantity.setAmount(acceptedQuantity); //订购数量(此处填写实际接单数量)
            if ("Cases".equals(vcPoOrderV.getUnitOfMeasure())) {
                acknowledgedQuantity.setUnitOfMeasure(ItemQuantity.UnitOfMeasureEnum.CASES); //数量单位（选填）
            } else if ("Eaches".equals(vcPoOrderV.getUnitOfMeasure())) {
                acknowledgedQuantity.setUnitOfMeasure(ItemQuantity.UnitOfMeasureEnum.EACHES);
            }
            if (StringUtil.isNotEmpty(vcPoOrderV.getUnitSize())) {
                acknowledgedQuantity.setUnitSize(Integer.parseInt(vcPoOrderV.getUnitSize()));//单位尺寸 （选填）
            }
            orderItemAcknowledgement.setAcknowledgedQuantity(acknowledgedQuantity); //确认数量信息设置

            //拒绝
            OrderItemAcknowledgement orderItemAcknowledgementReject = new OrderItemAcknowledgement();

            ItemQuantity acknowledgedQuantityReject = new ItemQuantity();
            acknowledgedQuantityReject.setAmount(rejectCount.intValue());
            if ("Cases".equals(vcPoOrderV.getUnitOfMeasure())) {
                acknowledgedQuantityReject.setUnitOfMeasure(ItemQuantity.UnitOfMeasureEnum.CASES); //数量单位（选填）
            } else if ("Eaches".equals(vcPoOrderV.getUnitOfMeasure())) {
                acknowledgedQuantityReject.setUnitOfMeasure(ItemQuantity.UnitOfMeasureEnum.EACHES);
            }
            if (StringUtils.isNotEmpty(vcPoOrderV.getUnitSize())) {
                acknowledgedQuantityReject.setUnitSize(Integer.parseInt(vcPoOrderV.getUnitSize()));
            }
            orderItemAcknowledgementReject.setAcknowledgedQuantity(acknowledgedQuantityReject); //设置拒绝确认信息
            orderItemAcknowledgementReject.setAcknowledgementCode(OrderItemAcknowledgement.AcknowledgementCodeEnum.REJECTED); //接单状态拒绝
            orderItemAcknowledgementReject.rejectionReason(OrderItemAcknowledgement.RejectionReasonEnum.TEMPORARILYUNAVAILABLE);//拒绝原因（默认原因）

            orderItemAcknowledgements.add(orderItemAcknowledgement);
            orderItemAcknowledgements.add(orderItemAcknowledgementReject);
        }


    }


    /**
     * 封装确认接单(延期交货)
     *
     * @param orderItemAcknowledgements 确认接单结果集信息
     * @param vcPoOrderV                单个订单每行接单信息
     */
    private void amzVcPoorderAcknowledgedQuantityBackorderedSync(List<OrderItemAcknowledgement> orderItemAcknowledgements, AmzVcPoOrderV vcPoOrderV) {


        Integer acceptedQuantity = vcPoOrderV.getAcceptedQuantity();//接单数量
        Integer orderedQuantity1 = vcPoOrderV.getQuantity();//下单数量
        Date latestShipDate = vcPoOrderV.getLatestShipDate();
        String shipWindow = cn.hutool.core.date.DateUtil.format(latestShipDate, "yyyy-MM-dd'T'HH:mm:ss'Z'");

        if (acceptedQuantity == orderedQuantity1) {             //全量延期交货
            OrderItemAcknowledgement orderItemAcknowledgement = new OrderItemAcknowledgement();
            orderItemAcknowledgement.setAcknowledgementCode(OrderItemAcknowledgement.AcknowledgementCodeEnum.BACKORDERED);
            Date planArriveTime = vcPoOrderV.getPlanArriveTime();//预计送达时间

            if (planArriveTime != null) {
                DateTime endArriveTime = cn.hutool.core.date.DateUtil.endOfDay(planArriveTime);
                String formatPlanArriveTime = cn.hutool.core.date.DateUtil.format(endArriveTime, "yyyy-MM-dd'T'HH:mm:ss'Z'");
                orderItemAcknowledgement.setScheduledShipDate(formatPlanArriveTime); //预计送达时间
            } else {
                throw new CustomException("延期交货时间不能为空");
            }

            //确认延期
            ItemQuantity acknowledgedQuantity = new ItemQuantity();
            acknowledgedQuantity.setAmount(vcPoOrderV.getAcceptedQuantity()); //订购数量(此处填写实际接单数量)
            if ("Cases".equals(vcPoOrderV.getUnitOfMeasure())) {
                acknowledgedQuantity.setUnitOfMeasure(ItemQuantity.UnitOfMeasureEnum.CASES); //数量单位（选填）
            } else if ("Eaches".equals(vcPoOrderV.getUnitOfMeasure())) {
                acknowledgedQuantity.setUnitOfMeasure(ItemQuantity.UnitOfMeasureEnum.EACHES);
            }
            acknowledgedQuantity.setUnitSize(Integer.parseInt(vcPoOrderV.getUnitSize()));//单位尺寸 （选填）

            orderItemAcknowledgement.setAcknowledgedQuantity(acknowledgedQuantity); //确认数量信息设置
            orderItemAcknowledgements.add(orderItemAcknowledgement);
        } else {
            //部分延期
            BigDecimal bigDecimalOrer = new BigDecimal(orderedQuantity1); //下单数量
            BigDecimal bigDecimalAccepted = new BigDecimal(acceptedQuantity);//延期交货数量
            BigDecimal acceptOrder = bigDecimalOrer.subtract(bigDecimalAccepted); // 接收数量

            //接收
            OrderItemAcknowledgement orderItemAcknowledgement = new OrderItemAcknowledgement();
            orderItemAcknowledgement.setAcknowledgementCode(OrderItemAcknowledgement.AcknowledgementCodeEnum.BACKORDERED);
            Date planArriveTime = vcPoOrderV.getPlanArriveTime(); //计划送达时间
            if (planArriveTime != null) {
                DateTime endArriveTime = cn.hutool.core.date.DateUtil.endOfDay(planArriveTime);
                String formatPlanArriveTime = cn.hutool.core.date.DateUtil.format(endArriveTime, "yyyy-MM-dd'T'HH:mm:ss'Z'");
                orderItemAcknowledgement.setScheduledShipDate(formatPlanArriveTime); //预计送达时间
            } else {
                throw new CustomException("延期交货时间不能为空");
            }


            //确认延期交货数量
            ItemQuantity acknowledgedQuantity = new ItemQuantity();
            acknowledgedQuantity.setAmount(acceptedQuantity); //订购数量(此处填写实际接单数量)
            if ("Cases".equals(vcPoOrderV.getUnitOfMeasure())) {
                acknowledgedQuantity.setUnitOfMeasure(ItemQuantity.UnitOfMeasureEnum.CASES); //数量单位（选填）
            } else if ("Eaches".equals(vcPoOrderV.getUnitOfMeasure())) {
                acknowledgedQuantity.setUnitOfMeasure(ItemQuantity.UnitOfMeasureEnum.EACHES);
            }
            acknowledgedQuantity.setUnitSize(Integer.parseInt(vcPoOrderV.getUnitSize()));//单位尺寸 （选填）
            orderItemAcknowledgement.setAcknowledgedQuantity(acknowledgedQuantity); //确认数量信息设置

            //接单
            OrderItemAcknowledgement orderItemAcknowledgementReject = new OrderItemAcknowledgement();
            ItemQuantity acknowledgedQuantityReject = new ItemQuantity();
            acknowledgedQuantityReject.setAmount(acceptOrder.intValue()); //接单数量
            if ("Cases".equals(vcPoOrderV.getUnitOfMeasure())) {
                acknowledgedQuantityReject.setUnitOfMeasure(ItemQuantity.UnitOfMeasureEnum.CASES); //数量单位（选填）
            } else if ("Eaches".equals(vcPoOrderV.getUnitOfMeasure())) {
                acknowledgedQuantityReject.setUnitOfMeasure(ItemQuantity.UnitOfMeasureEnum.EACHES);
            }
            acknowledgedQuantityReject.setUnitSize(Integer.parseInt(vcPoOrderV.getUnitSize()));
            orderItemAcknowledgementReject.setAcknowledgedQuantity(acknowledgedQuantityReject); //设置拒绝确认信息
            orderItemAcknowledgementReject.setAcknowledgementCode(OrderItemAcknowledgement.AcknowledgementCodeEnum.ACCEPTED); //接单状态拒绝
            orderItemAcknowledgementReject.setScheduledDeliveryDate(shipWindow);

            orderItemAcknowledgements.add(orderItemAcknowledgement);
            orderItemAcknowledgements.add(orderItemAcknowledgementReject);
        }


    }




    /**
     * 更新记录表对应工单ID
     */
    @Override
    public int updateTicketNumber(Long recordId, String ticketNumber) {
        if (null == recordId) {
            return 0;
        }
        AmzVcPoOrderTimeRecord amzVcPoOrderTimeRecord = new AmzVcPoOrderTimeRecord();
        amzVcPoOrderTimeRecord.setId(recordId);
        amzVcPoOrderTimeRecord.setTicketNumber(ticketNumber);
        return amzVcPoOrderTimeRecordMapper.updateAmzVcPoOrderTimeRecord(amzVcPoOrderTimeRecord);
    }


}
