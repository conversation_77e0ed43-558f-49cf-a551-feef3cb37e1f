package com.bizark.op.service.service.mar.material;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.boss.api.service.TaskCenterService;
import com.bizark.boss.api.vo.dashboard.TaskCenterRequest;
import com.bizark.common.exception.CommonException;
import com.bizark.op.api.dto.material.*;
import com.bizark.op.api.enm.mar.creatify.CreatifyRenderVideoStatusEnum;
import com.bizark.op.api.enm.mar.mater.MaterialGenStatusEnum;
import com.bizark.op.api.enm.mar.mater.MaterialWayEnum;
import com.bizark.op.api.entity.op.mar.material.*;
import com.bizark.op.api.entity.op.mar.material.creatify.request.*;
import com.bizark.op.api.entity.op.mar.material.creatify.response.*;
import com.bizark.op.api.entity.op.mar.vo.*;
import com.bizark.op.api.form.mar.material.MarMaterialQuery;
import com.bizark.op.api.service.mar.material.*;
import com.bizark.op.common.core.domain.BaseEntity;
import com.bizark.op.common.util.AliyunOssClientUtil;
import com.bizark.op.common.util.BeanCopyUtils;
import com.bizark.op.service.api.CreatifyApi;
import com.bizark.op.service.mapper.mar.material.MarMaterialMakeInfoMapper;
import com.bizark.op.service.util.ConvertUtils;
import com.bizark.op.service.util.MaterialIdInitUtils;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.io.File;
import java.io.FileInputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【mar_material_make_info(tiktok素材制作表)】的数据库操作Service实现
* @createDate 2025-02-10 17:06:17
*/
@Service
@Slf4j
public class MarMaterialMakeInfoServiceImpl extends ServiceImpl<MarMaterialMakeInfoMapper, MarMaterialMakeInfo>
    implements MarMaterialMakeInfoService {

    @Autowired
    private MarMaterialMakeInfoMapper marMaterialMakeInfoMapper;

    @Autowired
    private MarMaterialCharacterImageService marMaterialCharacterImageService;

    @Autowired
    private MarMaterialMusicsService marMaterialMusicsService;

    @Autowired
    private MarMaterialVoiceService marMaterialVoiceService;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private MarMaterialCenterInfoService marMaterialCenterInfoService;

    @Autowired
    private MarMaterialOpLogService marMaterialOpLogService;

    @Autowired
    private TaskCenterService taskCenterService;

    @Value("${task.center.file.path}")
    private String filePath;

    @Autowired
    private MarMaterialCreatifyLinkParamService marMaterialCreatifyLinkParamService;


    public String WEB_HOOK_URL = "https://op-api.ehengjian.com/callback/creatify/renderVideo";

    @Override
    public List<CreatifyGenPreviewVideoVO> generatePreviewVideo(CreatifyGeneratePreviewVideoDTO dto) {

        List<MarMaterialCenterInfo> centerInfos = checkAndGetMaterial(dto.getMaterialNumbers());

        List<String> videoUrls = centerInfos.stream().map(MarMaterialCenterInfo::getMaterialUrl).collect(Collectors.toList());

        // 检查数据
        CreatifyDataVO dataVO = checkAndResult(dto);
        // 生成链接
        CreatifyCreateLinkWithParamRequest request = new CreatifyCreateLinkWithParamRequest();
        request.setTitle(dto.getVideoNames().get(0));
        for (MarMaterialCenterInfo info : centerInfos) {
            if ("VIDEO".equalsIgnoreCase(info.getContentType())) {
                request.joinVideoUrl(info.getMaterialUrl());
            }
            if ("IMAGE".equalsIgnoreCase(info.getContentType())) {
                request.joinImageUrl(info.getMaterialUrl());
            }
        }

        CreatifyCreateLinkWithParamResponse linkWithParamResponse = CreatifyApi.createLinkWithParam(request);

        if (!linkWithParamResponse.isSuccess()) {
            throw new CommonException("视频链接创建失败:" + linkWithParamResponse.getErrorMsg());
        }

        CreatifyCreateVideoFromLinkRequest fromLinkRequest = genVideoFromLinkParam(dto, linkWithParamResponse.getId(), dataVO.getVoice(), dataVO.getMusic(), dataVO.getCharacterImage());

        CreatifyGenPreviewVideoFromLinkRequest linkRequest = new CreatifyGenPreviewVideoFromLinkRequest();
        BeanUtils.copyProperties(fromLinkRequest, linkRequest);

        // 设置回调
        linkRequest.setWebhookUrl(WEB_HOOK_URL);

        //  创建参数，记录参数内容
        marMaterialCreatifyLinkParamService.recordLinkParam(linkWithParamResponse.getId(), linkRequest);

        List<CreatifyGenPreviewVideoFromLinkResponse> previewResponses = CreatifyApi.generatePreviewVideoFromLink(linkRequest, dto.getVideoCount());


        return previewResponses.stream()
                .map(previewResponse -> {
                    CreatifyGenPreviewVideoVO videoVO = new CreatifyGenPreviewVideoVO();
                    videoVO.setCreatifyId(previewResponse.getId());
                    videoVO.setName(previewResponse.getName());
                    videoVO.setLink(previewResponse.getLink());
                    videoVO.setVideoDuration(previewResponse.getVideoLength());
                    videoVO.setAspectRatio(previewResponse.getAspectRatio());
                    videoVO.setScriptStyle(previewResponse.getScriptStyle());
                    videoVO.setPreview(previewResponse.getPreview());
                    // 替换域名返回mp4地址
//                    String previewMp4 = CreatifyApi.previewMp4Url(previewResponse.getPreview());
//                    videoVO.setPreviewMp4(previewMp4);
                    videoVO.setSuccess(previewResponse.isSuccess());
                    videoVO.setErrorMsg(previewResponse.getErrorMsg());
                    return videoVO;
                }).collect(Collectors.toList());

    }


    @NotNull
    private List<MarMaterialCenterInfo> checkAndGetMaterial(List<String> materialNumbers) {
        List<MarMaterialCenterInfo> centerInfos = marMaterialCenterInfoService.lambdaQuery()
                .in(MarMaterialCenterInfo::getMaterialNum, materialNumbers)
                .list();

        if (CollectionUtil.isEmpty(centerInfos)) {
            throw new CommonException("素材中心素材信息不存在");
        }

        Map<String, List<MarMaterialCenterInfo>> modelMap = centerInfos.stream().peek(c -> c.setModel(StrUtil.blankToDefault(c.getModel(), "")))
                .collect(Collectors.groupingBy(MarMaterialCenterInfo::getModel));

        if (modelMap.size() > 1) {
            throw new CommonException("存在不同型号素材");
        }
        return centerInfos;
    }

    @NotNull
    private CreatifyDataVO checkAndResult(CreatifyGeneratePreviewVideoDTO dto) {
        MarMaterialCharacterImage characterImage = null;
        if (Objects.nonNull(dto.getAvatarId())) {
            characterImage = marMaterialCharacterImageService.getById(dto.getAvatarId());
            if (Objects.isNull(characterImage)) {
                throw new CommonException("人物形象不存在");
            }
        }
        MarMaterialMusics music = null;
        if (Objects.nonNull(dto.getMusicId())) {
            music = marMaterialMusicsService.getById(dto.getMusicId());
            if (Objects.isNull(music)) {
                throw new CommonException("背景音乐不存在");
            }
        }

        MarMaterialVoice voice = null;
        if (Objects.nonNull(dto.getVoiceId())) {
            voice = marMaterialVoiceService.getById(dto.getVoiceId());
            if (Objects.isNull(voice)) {
                throw new CommonException("人物声音不存在");
            }
        }

        if (!Objects.equals(dto.getVideoCount(), dto.getVideoNames().size())) {
            throw new CommonException("视频名称数量需和视频生成数量一致");
        }
        return new CreatifyDataVO(characterImage, music, voice);
    }


    @Override
    @Transactional
    public void recordGenerateLog(CreatifyRecordGenerateDTO dto) {

        List<MarMaterialCenterInfo> centerInfos = checkAndGetMaterial(dto.getMaterialNumbers());


        long imageCount = centerInfos.stream()
                .filter(c -> !Objects.equals(c.getContentType(), "IMAGE"))
                .count();

        // 记录视频顺序

        List<CreatifyRecordGenerateDTO.PreviewVideoInfo> previewVideoInfos = dto.getPreviewVideoInfos();

        List<MarMaterialOpLog> logs = new ArrayList<>();
        List<MarMaterialMakeInfo> makeInfos = new ArrayList<>();


        for (int i = 0; i < previewVideoInfos.size(); i++) {
            CreatifyRecordGenerateDTO.PreviewVideoInfo videoInfo = previewVideoInfos.get(i);
            MarMaterialMakeInfo makeInfo = createInfo(videoInfo.getCreatifyPreviewId(), videoInfo.getLink(), MaterialWayEnum.CREATIFT_AI);
            makeInfo.setAspectRatio(dto.getAspectRatio());
            makeInfo.setTextStyle(dto.getScriptStyle());
            makeInfo.setVisionStyle(dto.getVisualStyle());
            makeInfo.setPreview(videoInfo.getPreview());
            makeInfo.setOrgMaterialNum(String.join(",", dto.getMaterialNumbers()));

            makeInfo.setMaterialName(dto.getVideoNames().get(i));
            if (Objects.nonNull(dto.getVideoDuration())) {
                makeInfo.setVideoDuration(dto.getVideoDuration().longValue());
            }
            makeInfo.setVideoFlag(Objects.equals(imageCount, 0L) ? 1 : 0);
            MarMaterialOpLog opLog = new MarMaterialOpLog();
            opLog.setOrgId(makeInfo.getOrgId());
            opLog.setMaterialNum(makeInfo.getGenId());
            opLog.settingDefaultCreate();
            opLog.settingDefaultUpdate();

            CreatifyRenderVideoRequest renderVideoRequest = new CreatifyRenderVideoRequest(makeInfo.getTaskId());

            // 渲染视频
            CreatifyRenderVideoResponse renderResponse = CreatifyApi.renderVideo(renderVideoRequest);

            if (Objects.isNull(renderResponse) || StrUtil.isNotBlank(renderResponse.getFailedReason())) {
                makeInfo.setGenStatus(MaterialGenStatusEnum.FAIL.getValue());
                opLog.setOperateContent(
                        String.format("预览链接生成视频,使用素材: %s , 预览链接:%s, 生成失败:%s",
                                dto.getMaterialNumbers(),
                                makeInfo.getPreview(),
                                Objects.isNull(renderResponse) ? "未知错误" : renderResponse.getFailedReason())
                );
            } else {
                opLog.setOperateContent(
                        String.format("预览链接生成视频,使用素材: %s , 预览链接:%s, 开始生成视频",
                                dto.getMaterialNumbers(),
                                makeInfo.getPreview())
                );
                makeInfos.add(makeInfo);
            }
            logs.add(opLog);

        }

        // 累加下载次数
        marMaterialCenterInfoService.incrUsageByMaterialNumbers(dto.getMaterialNumbers());

        this.saveBatch(makeInfos);

        // 写入日志
        marMaterialOpLogService.saveBatch(logs);

    }

    @Override
    public void generateVideo(CreatifyGeneratePreviewVideoDTO dto) {

        CreatifyDataVO dataVO = checkAndResult(dto);

        List<MarMaterialCenterInfo> centerInfos = checkAndGetMaterial(dto.getMaterialNumbers());


        long imageCount = centerInfos.stream()
                .filter(c -> !Objects.equals(c.getContentType(), "IMAGE"))
                .count();


        // 创建一个Link
        // 生成链接
        CreatifyCreateLinkWithParamRequest request = new CreatifyCreateLinkWithParamRequest();
        request.setTitle(dto.getVideoNames().get(0));

        for (MarMaterialCenterInfo centerInfo : centerInfos) {
            for (MarMaterialCenterInfo info : centerInfos) {
                if ("VIDEO".equalsIgnoreCase(info.getContentType())) {
                    request.joinVideoUrl(info.getMaterialUrl());
                }
                if ("IMAGE".equalsIgnoreCase(info.getContentType())) {
                    request.joinImageUrl(info.getMaterialUrl());
                }
            }
        }

        CreatifyCreateLinkWithParamResponse linkWithParamResponse = CreatifyApi.createLinkWithParam(request);
        if (!linkWithParamResponse.isSuccess() || Objects.isNull(linkWithParamResponse.getLink())) {
            throw new CommonException("链接创建失败：" + linkWithParamResponse.getErrorMsg());
        }

        MarMaterialCenterInfo centerInfo = CollectionUtil.getFirst(centerInfos);

        CreatifyCreateVideoFromLinkRequest fromLinkRequest = genVideoFromLinkParam(dto, linkWithParamResponse.getId(), dataVO.getVoice(), dataVO.getMusic(), dataVO.getCharacterImage());

        // 写入参数内容
        marMaterialCreatifyLinkParamService.recordLinkParam(linkWithParamResponse.getId(), fromLinkRequest);


        boolean single = Objects.equals(dto.getVideoCount(), 1);


        // 写入制作记录
        for (int i = 0; i < dto.getVideoCount(); i++) {
            MarMaterialMakeInfo makeInfo = createInfo(null, linkWithParamResponse.getId(), MaterialWayEnum.CREATIFT_AI);
            makeInfo.setAspectRatio(dto.getAspectRatio());
            makeInfo.setTextStyle(dto.getScriptStyle());
            makeInfo.setVisionStyle(dto.getVisualStyle());
            if (Objects.nonNull(dto.getVideoDuration())) {
                makeInfo.setVideoDuration(dto.getVideoDuration().longValue());
            }
            makeInfo.setModel(centerInfo.getModel());
            makeInfo.setOrgMaterialNum(String.join(",", dto.getMaterialNumbers()));
            makeInfo.setVideoFlag(Objects.equals(imageCount, 0L) ? 1 : 0);

            makeInfo.setMaterialName(dto.getVideoNames().get(i));

//            if (StrUtil.isNotBlank(dto.getVideoTitle())) {
//                makeInfo.setMaterialName(single ? dto.getVideoTitle() : dto.getVideoTitle() + "-" + (i + 1));
//            }else{
//                makeInfo.setMaterialName(makeInfo.getGenId());
//            }

            // 生成视频
//            CreatifyCreateVideoFromLinkRequest fromLinkRequest = genVideoFromLinkParam(dto, linkWithParamResponse.getId(), dataVO.getVoice(), dataVO.getMusic(), dataVO.getCharacterImage());
            fromLinkRequest.setWebhookUrl(WEB_HOOK_URL);

            CreatifyCreateVideoFromLinkResponse videoFromLinkResponse = CreatifyApi.createVideoFromLink(fromLinkRequest);

            MarMaterialOpLog opLog = new MarMaterialOpLog();
            opLog.setOrgId(makeInfo.getOrgId());
            opLog.setMaterialNum(makeInfo.getGenId());
            opLog.settingDefaultCreate();
            opLog.settingDefaultUpdate();



            if (Objects.isNull(videoFromLinkResponse) || StrUtil.isNotBlank(videoFromLinkResponse.getFailedReason())) {
                // 生成失败，写入错误信息
                makeInfo.setGenStatus(MaterialGenStatusEnum.FAIL.getValue());
                makeInfo.setRemark(Objects.nonNull(videoFromLinkResponse.getFailedReason()) ? videoFromLinkResponse.getFailedReason() : "未知错误");
                opLog.setOperateContent(
                        String.format("预览链接生成视频,使用素材: %s , 生成失败:%s",
                                dto.getMaterialNumbers(),
                                makeInfo.getRemark())
                );

            } else {
                makeInfo.setGenStatus(MaterialGenStatusEnum.PROCESSING.getValue());
                makeInfo.setTaskId(videoFromLinkResponse.getId());
                opLog.setOperateContent(String.format("预览链接生成视频,使用素材: %s ,开始生成视频", dto.getMaterialNumbers()));

            }
            this.save(makeInfo);


            marMaterialCenterInfoService.incrUsageByMaterialNumbers(dto.getMaterialNumbers());


            // 保存操作日志
            marMaterialOpLogService.save(opLog);

            // TODO 模拟等待发起回调
//            callback(makeInfo);
        }
    }

    private static void callback(MarMaterialMakeInfo makeInfo) {
        new Thread(() -> {
            try {
                TimeUnit.SECONDS.sleep(30);
            } catch (InterruptedException e) {

            }
            HttpRequest post = HttpUtil.createPost("http://127.0.0.1:6261/callback/creatify/renderVideo");
            CreatifyRenderVideoCallbackDTO callbackDTO = new CreatifyRenderVideoCallbackDTO();
            callbackDTO.setId(makeInfo.getTaskId());
            callbackDTO.setStatus(CreatifyRenderVideoStatusEnum.DONE.getValue());
            callbackDTO.setVideo_output("https://hengjian-prod-public.oss-cn-hangzhou.aliyuncs.com/erp/tk/video/mp47314324263631113502.mp4");
            post.body(JSON.toJSONString(callbackDTO));
            HttpResponse httpResponse = post.execute();
            if (!httpResponse.isOk()) {
                log.info("回调失败 - {} - {}", httpResponse.getStatus(), httpResponse.body());
            } else {
                log.info("发送回调成功 - {}", JSON.toJSONString(callbackDTO));
            }
        }).start();
    }

    @NotNull
    private static CreatifyCreateVideoFromLinkRequest genVideoFromLinkParam(CreatifyGeneratePreviewVideoDTO dto, String link, MarMaterialVoice voice, MarMaterialMusics music, MarMaterialCharacterImage characterImage) {

        // 生成预览视频
        CreatifyCreateVideoFromLinkRequest videoRequest = new CreatifyCreateVideoFromLinkRequest();
        videoRequest.setLink(link);
//        videoRequest.setName(dto.getVideoTitle());
        videoRequest.setName(dto.getVideoNames().get(0));
        videoRequest.setTargetPlatform(dto.getTargetPlatform());
        videoRequest.setTargetAudience(dto.getTargetAudience());
        videoRequest.setLanguage(dto.getLanguage());
        videoRequest.setVideoLength(dto.getVideoDuration());
        videoRequest.setAspectRatio(dto.getAspectRatio());
        videoRequest.setScriptStyle(dto.getScriptStyle());
        videoRequest.setVisualStyle(dto.getVisualStyle());
        videoRequest.setOverrideScript(dto.getOverrideText());
        videoRequest.setNoEmotion(dto.getEmotion());

        if (Objects.nonNull(dto.getVoiceId())) {
            videoRequest.setOverrideVoice(voice.getAccentId());
        }

        if (Objects.nonNull(dto.getMusicId())) {
            videoRequest.setBackgroundMusicUrl(music.getUrl());
            videoRequest.setBackgroundMusicVolume(dto.getMusicVolume());
            videoRequest.setNoBackgroundMusic(false);
        }

        if (StrUtil.isNotBlank(dto.getCaptionFontStyle()) || Objects.nonNull(dto.getCaptionOffsetX()) || Objects.nonNull(dto.getCaptionOffsetY())) {
            CreatifyCreateVideoFromLinkRequest.CaptionSetting captionSetting = new CreatifyCreateVideoFromLinkRequest.CaptionSetting();

            if (StrUtil.isNotBlank(dto.getCaptionFontStyle())) {
                captionSetting.setStyle(dto.getCaptionFontStyle());
            }

            if (Objects.nonNull(dto.getCaptionOffsetX()) || Objects.nonNull(dto.getCaptionOffsetY())) {
                CreatifyCreateVideoFromLinkRequest.Offset offset = new CreatifyCreateVideoFromLinkRequest.Offset();
                offset.setX(dto.getCaptionOffsetX());
                offset.setY(dto.getCaptionOffsetY());
                captionSetting.setOffset(offset);
            }
            videoRequest.setCaptionSetting(captionSetting);
        }

        if (Objects.nonNull(dto.getAvatarId())) {
            videoRequest.setOverrideAvatar(characterImage.getCreatifyId());
        }
        return videoRequest;
    }

    @Override
    public List<MarMaterialMakeInfo> findByIdAsc(Long id, List<String> taskIdList) {
        return marMaterialMakeInfoMapper.findByIdAsc(id, taskIdList);
    }


    /**
     * 素材制作调用导出
     *
     * @param query
     * @param authUserEntity
     */
    @Override
    public void exportmaterialMakeList(MarMaterialQuery query, UserEntity authUserEntity) {
        TaskCenterRequest request = new TaskCenterRequest();
        request.setOrgId(query.getOrgId());
        request.setTaskCode("erp.mar_material_make.export");
        List<Object> list = new ArrayList<>();
        list.add(JSON.toJSONString(query));
        request.setArgs(list);
        taskCenterService.startTask(request, authUserEntity);
    }


    /** 素材制作导出
     *
     * @param toJSONString
     * @return
     */
    @Override
    public String asyncExportMakeList(String queryParm) {
        log.info("asyncExportmaterialMakeList start ... ");
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "素材制作" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);


        MarMaterialQuery marMaterialQuery = JSON.parseObject(queryParm, MarMaterialQuery.class);
        String uploadPath = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            ExcelWriter writer = EasyExcel.write(file, MarMaterialCenterInfoVO.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "素材制作导出").head(MarMaterialMakeInfoVO.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<MarMaterialMakeInfo> marMaterialMakeInfos = this.queryMaterialMarkList(marMaterialQuery);
                if (CollUtil.isEmpty(marMaterialMakeInfos)) {
                    break;
                }
                List<MarMaterialMakeInfoVO> exports = BeanCopyUtils.copyBeanList(marMaterialMakeInfos, MarMaterialMakeInfoVO.class);
                ConvertUtils.dictConvert(exports);
                log.info("query pageNo:{}, list.size = {}, ", pageNo, exports.size());
                log.info("exportList.size = " + exports.size());
                writer.write(exports, writeSheet);
                pageNo++;
            }

            writer.finish();

            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "mar/request/material/");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        }
        log.info("asyncExportmaterialMakeList end ... ");
        return uploadPath;
    }

    @Override
    public void updateMakeStatus(Long id, MaterialGenStatusEnum statusEnum, String errorMsg) {
        LambdaUpdateChainWrapper<MarMaterialMakeInfo> updateChainWrapper = this.lambdaUpdate()
                .eq(MarMaterialMakeInfo::getId, id)
                .set(MarMaterialMakeInfo::getGenStatus, statusEnum.getValue());
        if (statusEnum == MaterialGenStatusEnum.FAIL && StrUtil.isNotBlank(errorMsg)) {
            updateChainWrapper.set(BaseEntity::getRemark, errorMsg);
        }
        updateChainWrapper.update();
    }

    @Override
    public void regenerateCreatifyVideos(List<MarMaterialMakeInfo> makeInfos) {

        List<MarMaterialOpLog> logs = new ArrayList<>();

        for (MarMaterialMakeInfo makeInfo : makeInfos) {
            MarMaterialOpLog opLog = new MarMaterialOpLog();
            logs.add(opLog);
            try {

                opLog.setOrgId(makeInfo.getOrgId());
                opLog.setMaterialNum(makeInfo.getGenId());
                opLog.settingDefaultCreate();
                opLog.settingDefaultUpdate();


                String link = makeInfo.getApiMaterialId();
                // 获取参数内容
                MarMaterialCreatifyLinkParam linkParam = marMaterialCreatifyLinkParamService.selectByLink(link);
                if (Objects.isNull(linkParam)) {
                    // 更新状态
                    this.updateMakeStatus(makeInfo.getId(), MaterialGenStatusEnum.FAIL, "重新生成失败,未获取到参数信息");
                    opLog.setOperateContent("重新生成：失败，未获取到参数信息");
                    continue;
                }
                CreatifyCreateVideoFromLinkRequest fromLinkRequest = JSON.parseObject(linkParam.getParam(), CreatifyCreateVideoFromLinkRequest.class);
                fromLinkRequest.setWebhookUrl(WEB_HOOK_URL);
                if (Objects.isNull(fromLinkRequest)) {
                    // 更新状态
                    this.updateMakeStatus(makeInfo.getId(), MaterialGenStatusEnum.FAIL, "重新生成失败,参数异常");
                    opLog.setOperateContent("重新生成：失败，参数异常");
                    continue;
                }

                CreatifyCreateVideoFromLinkResponse videoFromLinkResponse = CreatifyApi.createVideoFromLink(fromLinkRequest);
                if (videoFromLinkResponse.isSuccess()) {
                    // 更新任务id，状态正在生成
                    this.lambdaUpdate()
                            .eq(MarMaterialMakeInfo::getId, makeInfo.getId())
                            .set(MarMaterialMakeInfo::getTaskId, videoFromLinkResponse.getId())
                            .set(MarMaterialMakeInfo::getGenStatus, MaterialGenStatusEnum.PROCESSING.getValue())
                            .update();
                    opLog.setOperateContent("重新生成：视频重新生成中");
                    continue;
                }
                // 更新状态
                this.updateMakeStatus(makeInfo.getId(), MaterialGenStatusEnum.FAIL, videoFromLinkResponse.getErrorMsg());
                opLog.setOperateContent("重新生成：失败");
            } catch (Exception e) {
                this.updateMakeStatus(makeInfo.getId(), MaterialGenStatusEnum.FAIL, e.getMessage());
                opLog.setOperateContent("重新生成：" + e.getMessage());
            }
            String materialNum = makeInfo.getMaterialNum();
            // 累加下载次数
            if (StrUtil.isNotBlank(materialNum)) {
                marMaterialCenterInfoService.incrUsageByMaterialNumbers(Arrays.asList(materialNum.split(",")));
            }





        }
        if (CollectionUtil.isNotEmpty(logs)) {
            marMaterialOpLogService.saveBatch(logs);
        }

    }


    @Override
    public List<MarMaterialMakeInfo> queryMaterialMarkList(MarMaterialQuery query) {
        return this.baseMapper.selectMaterialMarkList(query);
    }


    public static MarMaterialMakeInfo createInfo(String taskId,String apiMaterialId, MaterialWayEnum way) {
        MarMaterialMakeInfo makeInfo = new MarMaterialMakeInfo();
        makeInfo.setGenId(MaterialIdInitUtils.createId("G"));
        makeInfo.setOrgId(1000049);
        makeInfo.setTaskId(taskId);
        makeInfo.setGenStatus(MaterialGenStatusEnum.PROCESSING.getValue());
        makeInfo.setApiMaterialId(apiMaterialId);
        makeInfo.setGenWay(way.getValue());
        makeInfo.setVideoFlag(0);
        return makeInfo;
    }





    @Override
    public void videoResult(CreatifyVideoResultDTO dto) {
        CreatifyGetVideoResultResponse resultResponse = CreatifyApi.getVideoResult(new CreatifyGetVideoResultRequest(dto.getTaskId()));
        if (!resultResponse.isSuccess()) {
            return;
        }
        String videoOutput = resultResponse.getVideoOutput();
        // 构建回调数据，调用回调接口
        CreatifyRenderVideoCallbackDTO callbackDTO = new CreatifyRenderVideoCallbackDTO();
        callbackDTO.setStatus(CreatifyRenderVideoStatusEnum.DONE.getValue());
        callbackDTO.setVideo_output(videoOutput);
        callbackDTO.setId(dto.getTaskId());
        marMaterialCenterInfoService.handleCallRenderCallback(callbackDTO);
    }



    public void updateStatusByApiMaterialId(String apiMaterialId, MaterialGenStatusEnum status, String remark) {
        LambdaUpdateChainWrapper<MarMaterialMakeInfo> updateChainWrapper = this.lambdaUpdate()
                .eq(MarMaterialMakeInfo::getApiMaterialId, apiMaterialId)
                .set(MarMaterialMakeInfo::getGenStatus, status.getValue());
        if (StrUtil.isNotBlank(remark)) {
            updateChainWrapper.set(BaseEntity::getRemark, remark);
        }
        updateChainWrapper.update();

    }

    public void updateStatusByIds(List<Long> ids, MaterialGenStatusEnum status, String remark) {
        LambdaUpdateChainWrapper<MarMaterialMakeInfo> updateChainWrapper = this.lambdaUpdate()
                .in(MarMaterialMakeInfo::getId, ids)
                .set(MarMaterialMakeInfo::getGenStatus, status.getValue());
        if (StrUtil.isNotBlank(remark)) {
            updateChainWrapper.set(BaseEntity::getRemark, remark);
        }
        updateChainWrapper.update();

    }

    @Override
    public MarMaterialMakeInfo selectByGenId(String genId) {
        return this.lambdaQuery()
                .eq(MarMaterialMakeInfo::getGenId, genId)
                .one();
    }

    @Override
    @Transactional
    public void updateMaterialNameByGenId(String genId,String materialName) {
        MarMaterialMakeInfo makeInfo = selectByGenId(genId);
        if (Objects.isNull(makeInfo)) {
            throw new CommonException("制作记录不存在：" + genId);
        }

        this.lambdaUpdate()
                .eq(MarMaterialMakeInfo::getGenId, genId)
                .set(MarMaterialMakeInfo::getMaterialName, materialName)
                .update();

        if (!Objects.equals(makeInfo.getGenStatus(), MaterialGenStatusEnum.SUCCESS.getValue())) {
            return;
        }

        marMaterialCenterInfoService.lambdaUpdate()
                .eq(MarMaterialCenterInfo::getMaterialNum, makeInfo.getMaterialNum())
                .set(MarMaterialCenterInfo::getMaterialName, materialName)
                .update();

    }


}




