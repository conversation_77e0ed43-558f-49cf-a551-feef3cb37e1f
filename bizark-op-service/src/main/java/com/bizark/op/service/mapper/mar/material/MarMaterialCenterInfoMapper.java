package com.bizark.op.service.mapper.mar.material;

import com.bizark.op.api.entity.op.mar.material.MarMaterialCenterInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bizark.op.api.entity.op.sale.Products;
import com.bizark.op.api.form.mar.material.MarMaterialQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【mar_material_center_info(tiktok素材中心表)】的数据库操作Mapper
* @createDate 2025-02-10 17:06:16
* @Entity com.bizark.op.api.entity.op.mar.material.MarMaterialCenterInfo
*/
public interface MarMaterialCenterInfoMapper extends BaseMapper<MarMaterialCenterInfo> {


    /** 查询素材中心
     *
     * @param query
     */
    List<MarMaterialCenterInfo> queryMaterialCenterList(MarMaterialQuery query);



    /**
     * 根据 sku查型号
     *
     * @param erpSku
     * @param orgId
     * @return
     */
    Products selectMaterialSkuModel(@Param("erpSku") String erpSku, @Param("orgId") Integer orgId);

    /** SKU
     *
     * @param orgId
     * @return
     */
    List<Products> selectMaterialSkuList(@Param("skus")List<String> skus, @Param("orgId")  Integer orgId);


    /**
     * model
     *
     * @param models
     * @param orgId
     * @return
     */
    List<Products> selectMaterialModelList(@Param("models")List<String> models,@Param("orgId") Integer orgId);


    int incrUsageByMaterialNumbers(@Param("materialNumbers") List<String> materialNumbers);
    List<MarMaterialCenterInfo> syncSale(@Param("id") Long id);

    int deleteByMaterialNum(@Param("materialNum") String materialNum);
}




