package com.bizark.op.service.handler.inventory.sync;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.bizark.op.api.enm.inventory.InventoryType;
import com.bizark.op.api.enm.sale.SaleChannelMappingEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.inventory.InventorySyncBean;
import com.bizark.op.api.entity.op.inventory.ProductChannelsInventory;
import com.bizark.op.api.entity.op.inventory.SelectInventoryBaseResponse;
import com.bizark.op.api.entity.op.inventory.SyncInventoryResult;
import com.bizark.op.api.entity.op.inventory.conf.vo.SellerSkuInventoryTaskMessage;
import com.bizark.op.api.entity.op.inventory.response.query.ShopifyInventoryLevelResponse;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.entity.op.task.PublishTask;
import com.bizark.op.api.service.sale.ProductChannelsService;
import com.bizark.op.service.api.InventorySelectApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Array;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bizark.op.api.enm.sale.SaleChannelMappingEnum.SHOPIFY;

@Component
@Slf4j
public class ShopifyInventorySynchronizer extends AbstractInventorySynchronizer{

    @Autowired
    private InventorySelectApi inventorySelectApi;

    @Autowired
    private ProductChannelsService productChannelsService;

    @Override
    public InventoryType getType() {
        return InventoryType.SHOPIFY;
    }

    @Override
    public SyncInventoryResult sync(InventorySyncBean commonBean) {
        SyncInventoryResult result = new SyncInventoryResult();
        List<ProductChannels> channels = commonBean.getChannels();
        List<List<ProductChannels>> splitChannels = CollectionUtil.split(channels, 100);
        for (List<ProductChannels> channel : splitChannels) {
            InventorySyncBean syncBean = new InventorySyncBean();
            syncBean.setChannels(channel);
            syncBean.setAccount(commonBean.getAccount());
            result.appendResult(super.sync(syncBean));
        }
        return result;
    }

    @Override
    public SelectInventoryBaseResponse getInventory(InventorySyncBean commonBean) {
        List<ProductChannels> channels = commonBean.getChannels();

        // 设置库存ID
        productChannelsService.buildInventoryItemId(channels, commonBean.getAccount());

        List<String> inventoryItemIds = channels.stream()
                .filter(c -> StrUtil.isNotBlank(c.getInventoryItemId()))
                .map(ProductChannels::getInventoryItemId)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(inventoryItemIds)) {
            return null;
        }
        return inventorySelectApi.selectShopifyInventory(commonBean.getAccount(), inventoryItemIds);

    }


    @Override
    public SelectInventoryBaseResponse buildInventoryByTask(InventorySyncBean syncBean) {

        List<ProductChannels> channels = syncBean.getChannels();

        Map<String, ProductChannels> channelsMap = channels.stream()
                .filter(c -> StrUtil.isNotBlank(c.getInventoryItemId()))
                .collect(Collectors.toMap(ProductChannels::getSellerSku, Function.identity(), (v1, v2) -> v1));

        ShopifyInventoryLevelResponse levelResponse = new ShopifyInventoryLevelResponse();

        List<SellerSkuInventoryTaskMessage> taskMessages = syncBean.getInventoryTaskMessages();
        List<ShopifyInventoryLevelResponse.InventoryLevelInfo> inventoryLevels = new ArrayList<>();
        for (SellerSkuInventoryTaskMessage taskMessage : taskMessages) {
            PublishTask task = taskMessage.getTask();

            ShopifyInventoryLevelResponse.InventoryLevelInfo levelInfo = new ShopifyInventoryLevelResponse.InventoryLevelInfo();
            levelInfo.setInventoryItemId(Long.parseLong(channelsMap.get(task.getSellerSku()).getInventoryItemId()));
            levelInfo.setLocationId(Long.parseLong(task.getWarehouseId()));
            levelInfo.setAvailable(Integer.parseInt(task.getTaskTarget()));
            inventoryLevels.add(levelInfo);
        }

        levelResponse.setInventoryLevels(inventoryLevels);
        levelResponse.setUpdateType(2);
        return levelResponse;
    }

    @Override
    public SelectInventoryBaseResponse handleResponse(SelectInventoryBaseResponse response) {
        if (response.getStatus() == SelectInventoryBaseResponse.Status.ERROR) {
            log.error("Shopify库存同步失败，请求库存数据失败！");
            return response.convertError("请求库存数据失败");
        }
        ShopifyInventoryLevelResponse shopifyResponse = (ShopifyInventoryLevelResponse) response;
        if (CollectionUtil.isEmpty(shopifyResponse.getInventoryLevels())) {
            log.error("Shopify库存同步失败，请求库存数据为空！");
            return response.convertError("请求库存数据为空");
        }
        List<ProductChannels> productChannels = shopifyResponse.getCommonBean().getChannels();
        Account account = shopifyResponse.getCommonBean().getAccount();
        Map<String, ProductChannels> channelsMap = productChannels.stream()
                .filter(c -> StrUtil.isNotBlank(c.getInventoryItemId()))
                .collect(Collectors.toMap(ProductChannels::getInventoryItemId, Function.identity(), (a, b) -> a));

        List<ShopifyInventoryLevelResponse.InventoryLevelInfo> levels = shopifyResponse.getInventoryLevels().stream()
                .filter(l -> channelsMap.containsKey(String.valueOf(l.getInventoryItemId())))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(levels)) {
            log.error("Shopify库存同步失败，未获取到对应SellerSku库存信息！");
            return response.convertError("未获取到对应SellerSku库存信息");
        }

        Map<Long, List<ShopifyInventoryLevelResponse.InventoryLevelInfo>> localGroup = levels.stream().collect(Collectors.groupingBy(ShopifyInventoryLevelResponse.InventoryLevelInfo::getInventoryItemId));
        Date date = new Date();
        List<ProductChannelsInventory> allInventory = new ArrayList<>();
        for (ProductChannels channel : productChannels) {
            if (StrUtil.isBlank(channel.getInventoryItemId()) || !localGroup.containsKey(Long.parseLong(channel.getInventoryItemId()))) {
                log.error("InventoryItemId is empty : {}", channel.getSellerSku());
                continue;
            }
            List<ShopifyInventoryLevelResponse.InventoryLevelInfo> levelInfos = localGroup.get(Long.parseLong(channel.getInventoryItemId()));
            List<ProductChannelsInventory> inventories = levelInfos.stream()
                    .map(l -> {
                        ProductChannelsInventory inventory = new ProductChannelsInventory();
                        inventory.setOrgId(channel.getOrgId());
                        inventory.setChannelId(channel.getId());
                        inventory.setAccountId(account.getId());
                        inventory.setWarehouseId(String.valueOf(l.getLocationId()));
                        inventory.setQuantity(String.valueOf(l.getAvailable()));
                        inventory.setAccountFlag(channel.getAccountId());
                        inventory.setSellerSku(channel.getSellerSku());

                        if (Objects.equals(1, response.getUpdateType())) {
                            inventory.setSyncLastTime(date);
                        }else{
                            inventory.setUpdateLastTime(date);
                        }

                        return inventory;
                    }).collect(Collectors.toList());
            allInventory.addAll(inventories);
        }

        recordInventory(response.getCommonBean().getChannels(), allInventory);
        buildErrorChannels(response, allInventory);
        return response;
    }




}
