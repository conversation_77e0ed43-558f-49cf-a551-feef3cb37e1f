package com.bizark.op.service.service.finance;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bizark.op.api.amazon.common.ApiClient;
import com.bizark.op.api.amazon.common.ApiException;
import com.bizark.op.api.amazon.common.InitApiClient;
import com.bizark.op.api.amazon.common.report.api.ReportsApi;
import com.bizark.op.api.amazon.common.report.model.*;
import com.bizark.op.api.cons.ScTicketConstant;
import com.bizark.op.api.enm.YesOrNoEnum;
import com.bizark.op.api.enm.finance.FinModelEnum;
import com.bizark.op.api.enm.sale.AccountSaleChannelEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.finance.FinConfPeriodEntity;
import com.bizark.op.api.entity.op.finance.sc.*;
import com.bizark.op.api.entity.op.order.SaleOrders;
import com.bizark.op.api.entity.op.sys.SysIfInvokeOutbound;
import com.bizark.op.api.entity.op.ticket.AmzSellerFeedback;
import com.bizark.op.api.entity.op.ticket.ScTicket;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.finance.FinConfPeriodService;
import com.bizark.op.api.service.finance.IAmzCreateReportService;
import com.bizark.op.api.service.finance.IAmzReportService;
import com.bizark.op.api.service.finance.IAmzReportTypeService;
import com.bizark.op.api.service.order.SaleOrdersService;
import com.bizark.op.api.service.sys.ISysIfInvokeOutboundService;
import com.bizark.op.api.service.ticket.IScTicketService;
import com.bizark.op.common.util.DateUtil;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.service.mapper.finance.AmzReportMapper;
import com.bizark.op.service.mapper.finance.AmzReportTypeMapper;
import com.bizark.op.service.mapper.ticket.AmzSellerFeedbackMapper;
import com.bizark.op.service.mapper.ticket.SaleOrdersMapper;
import com.bizark.op.service.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 亚马逊报告Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-13
 */
@Service
@Slf4j
public class AmzReportServiceImpl implements IAmzReportService {

    private static Logger logger = LoggerFactory.getLogger(AmzReportServiceImpl.class);
    public static final String BASE_PATH = "/home/<USER>/docker/attachment/amz_report";
    public static final Double GET_REPORTS_RATE_LIMIT_PERMIT = 0.0222;
    public static final Double GET_REPORT_DEFAULT_RATE_LIMIT_PERMIT = 0.0167;
    public static final Double GET_REPORT_RATE_LIMIT_PERMIT = 2.0;

    @Autowired
    private IAmzReportTypeService amzReportTypeService;

    @Autowired
    private AccountService accountService;

    //同步日志记录
    @Autowired
    private ISysIfInvokeOutboundService invokeLogStrategy;

    @Autowired
    private AmzReportMapper amzReportMapper;

//    @Autowired
//    private IAmzPaymentSettlementSummaryService amzPaymentSettlementSummaryService;
//
//    @Autowired
//    private IAmzPaymentSettlementService amzPaymentSettlementService;

    @Autowired
    private InitApiClient initApiClient;

    @Autowired
    private AmzReportTypeMapper amzReportTypeMapper;

    @Autowired
    private AmzSellerFeedbackMapper amzSellerFeedbackMapper;

    @Autowired
    private SaleOrdersMapper saleOrdersMapper;

    @Autowired
    private IScTicketService scTicketService;

    @Autowired
    private SaleOrdersService saleOrdersService;

    @Autowired
    private IAmzCreateReportService amzCreateReportService;

    @Autowired
    private FinConfPeriodService finConfPeriodService;

    @Override
    public void syncShopReportJob() {
        List<AmzReportType> amzReportTypeList = amzReportTypeService.queryReportTypeByAutoFlag("Y");
        if (CollectionUtils.isEmpty(amzReportTypeList)) {
            return;
        }

        //根据库存组织查询当期账期信息
        List<FinConfPeriodEntity> periodList = this.finConfPeriodService.list(
                Wrappers.<FinConfPeriodEntity>lambdaQuery()
                        .select(FinConfPeriodEntity::getOrganizationId)
                        .eq(FinConfPeriodEntity::getModel, FinModelEnum.CAPITAL.value())
                        .eq(FinConfPeriodEntity::getState, YesOrNoEnum.YES.value())
                        .isNull(FinConfPeriodEntity::getDisabledName));
        List<Integer> organizationIdList = periodList.stream().map(FinConfPeriodEntity::getOrganizationId).distinct().collect(Collectors.toList());

        if (CollUtil.isEmpty(organizationIdList)) {
            return;
        }

        List<Account> shopList = this.accountService.list(
                Wrappers.<Account>lambdaQuery()
                        .in(Account::getOrgId, organizationIdList)
                        .eq(Account::getAccountType, "Amazon_SC")
                        .eq(Account::getStatus, "runing")
                        .isNull(Account::getIsDelete));

        List<String> processingStatuses = Collections.singletonList("DONE");
        for (Account amzShop : shopList) {
            try {
                ApiClient apiClient = initApiClient.initApiClient(amzShop.getId(), GET_REPORTS_RATE_LIMIT_PERMIT);
                ReportsApi reportsApi = new ReportsApi(apiClient);

                String marketplaceId;
                if (StringUtils.isNotEmpty(amzShop.getConnectStr())) {
                    JSONObject jsonObject = JSON.parseObject(amzShop.getConnectStr());
                    marketplaceId = jsonObject.getString("marketplaceId");
                } else {
                    logger.error("获取账户信息为空，accountId: {}", amzShop.getId());
                    return;
                }
                if (StringUtils.isEmpty(marketplaceId)) {
                    logger.error("marketplaceId为空，accountId: {}", amzShop.getId());
                    return;
                }

                List<String> marketplaceIds = CollUtil.newArrayList(marketplaceId);

                for (AmzReportType amzReportType : amzReportTypeList) {
                    List<String> reportTypes = new ArrayList<>();
                    reportTypes.add(amzReportType.getReportType());

                    String createdSince = null;
                    if (StringUtil.isNotEmpty(amzReportType.getLastSyncTime())) {
                        createdSince = DateUtil.convertStringToISODate(amzReportType.getLastSyncTime(), DateUtil.ISO_PATTERN_YMDHMSSZ);
                    } else {
                        createdSince = LocalDateTime.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.ISO_PATTERN_YMDHMSSZ));
                    }
                    ReportParam reportParam = ReportParam.builder()
                            .reportTypes(reportTypes)
                            .processingStatuses(processingStatuses)
                            .marketplaceIds(marketplaceIds)
                            .createdSince(createdSince)
                            .pageSize(100)
                            .build();

                    syncShopReportByTypeCommon(reportsApi, amzShop.getId(), reportParam);

                    //修改最近同步时间
                    String newDate = LocalDateTime.now().minusDays(amzReportType.getSyncFrequency()).format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN));
                    amzReportTypeService.updateReportTypeLastSyncTime(amzReportType.getReportTypeId(), newDate);
                }
            } catch (Exception e) {
                log.error("syncShopReportJob ", e);
            }
        }
    }

    private void syncShopReportByTypeCommon(ReportsApi reportsApi, Integer shopId, ReportParam reportParam) {
        SysIfInvokeOutbound jrapInterfaceOutbound = new SysIfInvokeOutbound();
        jrapInterfaceOutbound.setInterfaceName("getReports");
        jrapInterfaceOutbound.setInterfaceUrl(reportsApi.getApiClient().getBasePath() + "/reports/2021-06-30/reports");
        jrapInterfaceOutbound.setRequestParameter(shopId.toString() + ";" + reportParam.toString());
        jrapInterfaceOutbound.setRequestTime(new Date());

        try {
            GetReportsResponse response = reportsApi.getReports(reportParam.getReportTypes(), reportParam.getProcessingStatuses(), reportParam.getMarketplaceIds(), reportParam.getPageSize(), reportParam.getCreatedSince(), reportParam.getCreatedUntil(), reportParam.getNextToken());

            jrapInterfaceOutbound.setResponseTime(System.currentTimeMillis());
            jrapInterfaceOutbound.setResponseCode("200");
            invokeLogStrategy.insertSysIfInvokeOutbound(jrapInterfaceOutbound);

            ReportList reports = response.getReports();
            saveShopReports(reports, shopId);

            String newNextToken = response.getNextToken();
            if (StringUtil.isNotEmpty(newNextToken)) {
                reportParam.setNextToken(newNextToken);
                syncShopReportByTypeCommon(reportsApi, shopId, reportParam);
            }

        } catch (ApiException e) {
            jrapInterfaceOutbound.setResponseTime(System.currentTimeMillis());
            jrapInterfaceOutbound.setResponseContent(e.getMessage() + "\n" + e.toString());
            jrapInterfaceOutbound.setResponseCode(String.valueOf(e.getCode()));
            invokeLogStrategy.insertSysIfInvokeOutbound(jrapInterfaceOutbound);

            logger.error("亚马逊报表数据同步异常", e);
        }
    }

    private void saveShopReports(ReportList reports, Integer shopId) {
        Account account = accountService.getById(shopId);
        for (Report report : reports) {
            saveReport(report, account.getOrgId(), shopId.longValue());
        }
    }

    private void saveReport(Report report, Integer organizationId, Long shopId) {
        AmzReport amzReport = amzReportMapper.queryReportAmzReportId(report.getReportId());

        AmzReport record = new AmzReport();

        if (null != amzReport) {
            //判断报表状态信息是否一致
            if (report.getProcessingStatus().getValue().equals(amzReport.getProcessingStatus())) {
                return;
            } else {
                record.setReportId(amzReport.getReportId());
            }
        }

        record.setOrganizationId(organizationId);
        record.setShopId(shopId);

        List<String> marketplaceIds = report.getMarketplaceIds();
        if (null != marketplaceIds && !marketplaceIds.isEmpty()) {
            record.setMarketplaceId(StringUtil.paserListToStr(marketplaceIds, ","));
        }
        record.setAmzReportId(report.getReportId());
        record.setReportType(report.getReportType());
        record.setDataStartTime(DateUtils.formatDate(report.getDataStartTime(), DateUtil.ISO_PATTERN_YMDHMSSZ));
        record.setDataEndTime(DateUtils.formatDate(report.getDataEndTime(), DateUtil.ISO_PATTERN_YMDHMSSZ));
        record.setReportScheduleId(report.getReportScheduleId());
        record.setProcessingStatus(report.getProcessingStatus().getValue());
        record.setProcessingStartTime(null != report.getProcessingStartTime() ? DateUtils.formatDate(report.getProcessingStartTime(), DateUtil.ISO_PATTERN_YMDHMSSZ) : null);
        record.setProcessingEndTime(null != report.getProcessingEndTime() ? DateUtils.formatDate(report.getProcessingEndTime(), DateUtil.ISO_PATTERN_YMDHMSSZ) : null);
        record.setReportDocumentId(report.getReportDocumentId());
        record.setDownloadFlag("N");

        if (null == record.getReportId()) {
            this.insertAmzReport(record);
        } else {
            this.updateAmzReport(record);
        }
    }

    /**
     * 新增亚马逊报告
     *
     * @param amzReport 亚马逊报告
     * @return 结果
     */
    public int insertAmzReport(AmzReport amzReport) {
        amzReport.setCreatedAt(DateUtils.getNowDate());
        return amzReportMapper.insertAmzReport(amzReport);
    }

    /**
     * 修改亚马逊报告
     *
     * @param amzReport 亚马逊报告
     * @return 结果
     */
    public int updateAmzReport(AmzReport amzReport) {
        amzReport.setUpdatedAt(DateUtils.getNowDate());
        return amzReportMapper.updateAmzReport(amzReport);
    }
    @Override
    public void syncShopReportDocumentJob() {
        List<AmzReport> amzReportList = amzReportMapper.queryUnDownloadReportJob();
        for (AmzReport amzReport : amzReportList) {
            //TODO 临时设置，只下载结算报表
            if ("GET_V2_SETTLEMENT_REPORT_DATA_FLAT_FILE_V2".equals(amzReport.getReportType())){
                syncShopReportDocument(amzReport.getShopId(), amzReport.getReportType());
            }
        }
    }

    /**
     * @description: 下载指定类型amz报表
     * @author: Moore
     * @date: 2024/6/11 14:23
     * @param
     * @param reportType
     * @return: void
     **/
    @Override
    public void syncShopReportDocumentByReportJob(String reportType) {
        if (StringUtils.isEmpty(reportType)){
            return;
        }
        List<AmzReport> amzReportList = amzReportMapper.queryUnDownloadReportJob();
        for (AmzReport amzReport : amzReportList) {
            if (reportType.equals(amzReport.getReportType())){
                syncShopReportDocument(amzReport.getShopId(), amzReport.getReportType());
            }
        }
    }


    /**
     * @description: 下在amz报表内容
     * @author:
     * @date: 2024/5/29 17:31
     * @param
     * @param shopId
     * @param reportType
     * @return: void
     **/
    @Override
    public void syncShopReportDocument(Long shopId, String reportType) {
        ApiClient apiClient = initApiClient.initApiClient(shopId.intValue(), GET_REPORTS_RATE_LIMIT_PERMIT);
        ReportsApi reportsApi = new ReportsApi(apiClient);

        List<AmzReport> amzReportList = amzReportMapper.queryUnDownloadReport(shopId, reportType);
        for (AmzReport amzReport : amzReportList) {

            SysIfInvokeOutbound jrapInterfaceOutbound = new SysIfInvokeOutbound();
            jrapInterfaceOutbound.setInterfaceName("getReportDocument");
            jrapInterfaceOutbound.setInterfaceUrl(reportsApi.getApiClient().getBasePath() + "/reports/2021-06-30/documents/{reportDocumentId}");
            jrapInterfaceOutbound.setRequestParameter(amzReport.getAmzReportId() + ";" + amzReport.getReportDocumentId());
            jrapInterfaceOutbound.setRequestTime(new Date());

            try {
                ReportDocument response = reportsApi.getReportDocument(amzReport.getReportDocumentId());

                if (null == response.getCompressionAlgorithm()) {
                    downloadReportDocument(amzReport.getOrganizationId(), shopId, amzReport.getReportId(), amzReport.getAmzReportId(), reportType, response.getUrl(), null);
                }
                jrapInterfaceOutbound.setResponseTime(System.currentTimeMillis());
                jrapInterfaceOutbound.setResponseCode("200");
                invokeLogStrategy.insertSysIfInvokeOutbound(jrapInterfaceOutbound);

            } catch (ApiException e) {
                jrapInterfaceOutbound.setResponseTime(System.currentTimeMillis());
                jrapInterfaceOutbound.setResponseContent(e.getMessage() + "\n" + e.toString());
                jrapInterfaceOutbound.setResponseCode(String.valueOf(e.getCode()));
                invokeLogStrategy.insertSysIfInvokeOutbound(jrapInterfaceOutbound);

                logger.error("亚马逊数据同步异常 {}", e.getMessage());
            } catch (Exception e) {
                logger.error("{} {} 亚马逊报告获取异常 {}", shopId, reportType, e.getMessage());
            }


        }
    }

    private void downloadReportDocument(Integer organizationId, Long shopId, Long reportId, String amzReportId, String reportType, String docUrl, String compressionAlgorithm) {
        String filePath = BASE_PATH + File.separator + shopId.toString();
        String fileName = amzReportId + ".txt";
        String reportPathName = filePath + File.separator + fileName;
        List<String[]> list = FileUtil.downLoadRemoteFile(docUrl, filePath, fileName);

        if("GET_SELLER_FEEDBACK_DATA".equals(reportType)) {
            this.saveSellerFeedbackByReport(Long.valueOf(organizationId), shopId, list, reportType);
        }
        amzReportMapper.updateReportDocumentDownload(reportId, docUrl, fileName, reportPathName, compressionAlgorithm);
    }


    public int saveSellerFeedbackByReport(Long organizationId, Long shopId, List<String[]> list, String reportType) {
        int insertCount = 0;
        if (null != list && !list.isEmpty()) {
            AmzReportType amzReportType = amzReportTypeMapper.queryReportTypeByType(reportType);
            int maxLength = 0;
            for (String[] strings : list) {
                String str = strings[0];
                if (str.equals(amzReportType.getFirstColumnName())) {
                    maxLength = strings.length;
                    continue;
                }

                String[] newStringArray = StringUtil.copyStringArray(strings, maxLength);
                AmzSellerFeedback amzSellerFeedback = new AmzSellerFeedback();
                amzSellerFeedback.setFeedbackDate(newStringArray[0]);
                amzSellerFeedback.setFeedbackDateFormat(!StringUtils.isEmpty(newStringArray[0]) ? cn.hutool.core.date.DateUtil.parse(newStringArray[0], "MM/dd/yy") : null); //评论时间格式化
                amzSellerFeedback.setRating((StringUtils.isNotEmpty(newStringArray[1]) && NumberUtil.isNumber(newStringArray[1])) ? Integer.valueOf(newStringArray[1]) : null);
                amzSellerFeedback.setComments(newStringArray[2]);
                amzSellerFeedback.setResponse(newStringArray[3]);
                amzSellerFeedback.setAmzOrderId(newStringArray[4]);
                amzSellerFeedback.setRaterEmail(newStringArray[5]);

                List<AmzSellerFeedback> feedbackList = amzSellerFeedbackMapper.selectAmzOrderSellerFeedbackList(amzSellerFeedback);
                if (CollectionUtils.isEmpty(feedbackList)) {
                    if (!StringUtils.isEmpty(newStringArray[4])){
                        SaleOrders querySaleOrders = new SaleOrders();
                        querySaleOrders.setOrderNo(newStringArray[4]);//平台订单号
                        querySaleOrders.setChannel(AccountSaleChannelEnum.SC.getValue()); //渠道
                        querySaleOrders.setOrgId(organizationId); //组织ID
                        querySaleOrders.setChannelId(shopId);//店铺ID
                        List<SaleOrders> saleOrdersLists = saleOrdersService.selectOrderNoList(querySaleOrders);

                        if (CollectionUtil.isNotEmpty(saleOrdersLists)) {
                            amzSellerFeedback.setOrderId(saleOrdersLists.get(0).getId());
                            String buyName = saleOrdersMapper.getBuyNameByOrderId(saleOrdersLists.get(0).getId());           //设置客户名称
                            amzSellerFeedback.setCustomerName(buyName);
                        }
                    }

                    amzSellerFeedback.setShopId(shopId);
                    amzSellerFeedback.setOrganizationId(organizationId);
                    amzSellerFeedback.settingDefaultSystemCreate();
                    insertCount = this.amzSellerFeedbackMapper.insertAmzSellerFeedback(amzSellerFeedback);


                    ScTicket scTicket = new ScTicket();
                    String ticketName = newStringArray[2];
                    if (ticketName.length() > 20) {
                        ticketName = ticketName.substring(0, 20);
                    }
                    scTicket.setOrganizationId(organizationId);

                    scTicket.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(amzSellerFeedback.getFeedbackId().toString(), "l", 5, "0")+ RandomUtil.randomNumbers(4));

                    scTicket.setTicketType("NF");
                    scTicket.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH);
                    scTicket.setShopId(shopId);
                    //设置店铺邮箱，工单渠道
                    if (shopId != null) {
                        Account one = accountService.lambdaQuery().select(Account::getType).eq(Account::getId, shopId).one();
                        scTicket.setTicketSource(one != null ? one.getType() : null);
                    }

                    scTicket.setSourceId(amzSellerFeedback.getFeedbackId());
                    scTicket.setSourceDocument("amz_seller_feedback");
                    //获取店铺名
                    QueryWrapper<Account> queryWrapper = new QueryWrapper<>();
                    queryWrapper.select("title");
                    queryWrapper.eq("id",shopId);
                    List<Account> accountList = accountService.list(queryWrapper);
                    scTicket.setTicketName("店铺："+(CollectionUtil.isNotEmpty(accountList)?accountList.get(0).getTitle():"") +" 订单号: "+newStringArray[4]+" 星级："+amzSellerFeedback.getRating());
                    scTicketService.insertScTicket(scTicket);
                }
            }
        }
        return insertCount;
    }


//    private void saveReportDocument(Integer organizationId, Long shopId, List<String[]> list, String reportType) {
//        if (null != list && !list.isEmpty()) {
//            AmzReportType amzReportType = amzReportTypeService.queryReportTypeByType(reportType);
//            Long settlementSummaryId = null;
//            int maxLength = 0;
//            int firstRowLength = amzReportType.getFirstRowLength();
//            for (String[] strings : list) {
//                String str = strings[0];
//                if (str.equals(amzReportType.getFirstColumnName())) {
//                    maxLength = strings.length;
//                    if (null == amzReportType.getFirstRowLength()) {
//                        firstRowLength = maxLength;
//                    }
//                    continue;
//                }
//                if (strings.length == firstRowLength) {
//                    settlementSummaryId = saveAmzPaymentSettlementSummary(organizationId, shopId, strings);
//                } else {
//                    saveAmzPaymentSettlement(organizationId, shopId, settlementSummaryId, StringUtil.copyStringArray(strings, maxLength));
//                }
//            }
//        }
//    }


//    private void saveAmzPaymentSettlement(Integer organizationId, Long shopId, Long settlementSummaryId, String[] strings) {
//        AmzPaymentSettlement record = new AmzPaymentSettlement();
//
//        record.setOrganizationId(organizationId);
//        record.setShopId(shopId);
//        record.setSettlementSummaryId(settlementSummaryId);
//        record.setSettlementId(strings[0]);
//        record.setSettlementStartDate(strings[1]);
//        record.setSettlementEndDate(strings[2]);
//        record.setDepositDate(strings[3]);
//        if (StringUtil.isNotEmpty(strings[4])) {
//            record.setTotalAmount(BigDecimal.valueOf(Double.parseDouble(strings[4])));
//        }
//        record.setCurrency(strings[5]);
//        record.setTransactionType(strings[6]);
//        record.setAmzOrderId(strings[7]);
//        record.setMerchantOrderId(strings[8]);
//        record.setAdjustmentId(strings[9]);
//        record.setShipmentId(strings[10]);
//        record.setMarketplaceName(strings[11]);
//        record.setAmountType(strings[12]);
//        record.setAmountDescription(strings[13]);
//        if (StringUtil.isNotEmpty(strings[14])) {
//            record.setAmount(BigDecimal.valueOf(Double.parseDouble(strings[14])));
//        }
//        record.setFulfillmentId(strings[15]);
//        record.setPostedDate(strings[16]);
//        record.setPostedDateTime(strings[17]);
//        record.setOrderItemCode(strings[18]);
//        record.setMerchantOrderItemId(strings[19]);
//        record.setMerchantAdjustmentItemId(strings[20]);
//        record.setSku(strings[21]);
//        if (StringUtil.isNotEmpty(strings[22])) {
//            record.setQuantityPurchased(Integer.valueOf(strings[22]));
//        }
//
//        amzPaymentSettlementService.insertAmzPaymentSettlement(record);
//
//    }


    /**
     * @description: 获取非自动生成amz报表类型
     * @author: Moore
     * @date: 2024/6/11 14:04
     * @param
     * @param amzManualReportType
     * @return: void
     **/
    @Override
    public void syncCreateReportJob(String amzManualReportType) {
        //获取非自动生成报表b
        if (StringUtils.isEmpty(amzManualReportType)){
            return;
        }
        AmzReportType amzReportTypeRecod = amzReportTypeService.queryReportTypeByType(amzManualReportType);
        ArrayList<AmzReportType> amzReportTypeList = new ArrayList<>();
        if (amzReportTypeRecod == null) {
            return;
        }
        amzReportTypeList.add(amzReportTypeRecod);

        List<Account> sellerShopList = this.accountService.list(
                Wrappers.<Account>lambdaQuery()
                        .eq(Account::getOrgId, 1000049)
                        .eq(Account::getAccountType, "Amazon_SC")
                        .eq(Account::getStatus, "runing")
                        .isNull(Account::getIsDelete));


        for (AmzReportType amzReportType : amzReportTypeList) {
            try {
                Thread.sleep(8000L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            String currentDate = DateUtil.getSysDate();
            String newDate = DateUtil.getSysDate();
            if ("M".equals(amzReportType.getFrequencyUnit())) {
                //频率-月
                if (DateUtil.getMonth(currentDate) - DateUtil.getMonth(amzReportType.getLastSyncTime()) < amzReportType.getSyncFrequency()) {
                    continue;
                }
                newDate = DateUtil.convertDateToString(DateUtil.addDate(DateUtil.convertStringToDate(amzReportType.getLastSyncTime(), DatePattern.NORM_DATETIME_PATTERN), amzReportType.getSyncFrequency(), "M"), DatePattern.NORM_DATETIME_PATTERN);
            } else if ("D".equals(amzReportType.getFrequencyUnit())) {
                //频率-天
                int betweenDay = DateUtil.getBetweenDayNumber(amzReportType.getLastSyncTime(), currentDate);//上次更新时间距今天数
                if (betweenDay < amzReportType.getSyncFrequency()) { //如当天已操作，不可重复操作
                    continue;
                }
                //每次执行 增加指定跨度时间
                newDate = DateUtil.convertDateToString(DateUtil.addDate(DateUtil.convertStringToDate(amzReportType.getLastSyncTime(), DatePattern.NORM_DATETIME_PATTERN), amzReportType.getSyncFrequency()), DatePattern.NORM_DATETIME_PATTERN);
            }
            String marketplaceId;
            for (Account account : sellerShopList) {
                if (StringUtils.isNotEmpty(account.getConnectStr())) {
                    JSONObject jsonObject = JSON.parseObject(account.getConnectStr());
                    marketplaceId = jsonObject.getString("marketplaceId");
                } else {
                    logger.error("获取账户信息为空，accountId: {}", account.getId());
                    return;
                }
                if (StringUtils.isEmpty(marketplaceId)) {
                    logger.error("marketplaceId为空，accountId: {}", account.getId());
                    return;
                }
                this.createShopReportByType(account.getId(), amzReportType.getReportType(), amzReportType.getLastSyncTime(), newDate, amzReportType.getReportOption(),marketplaceId);
            }
            logger.info("获取非自动生成amz报表类型：{},时间：{}", amzReportType.getReportType(),JSONObject.toJSONString(amzReportType.getLastSyncTime()),JSONObject.toJSONString(newDate));
            amzReportTypeService.updateReportTypeLastSyncTime(amzReportType.getReportTypeId(), newDate);
        }
    }

    @Override
    public void syncCreateReportJobBytime(String dateFrom, String dateto, Long shopId) {
        //获取非自动生成报表（feedBack）
        AmzReportType amzReportTypeRecod = amzReportTypeService.queryReportTypeByType("GET_SELLER_FEEDBACK_DATA");
        ArrayList<AmzReportType> amzReportTypeList = new ArrayList<>();
        if (amzReportTypeRecod == null) {
            return;
        }
        amzReportTypeList.add(amzReportTypeRecod);

        List<Account> sellerShopList = this.accountService.list(
                Wrappers.<Account>lambdaQuery()
                        .eq(Account::getOrgId, 1000049)
                        .eq(Account::getAccountType, "Amazon_SC")
                        .eq(Account::getStatus, "runing")
                        .isNull(Account::getIsDelete));


        for (AmzReportType amzReportType : amzReportTypeList) {
            String marketplaceId;
            for (Account account : sellerShopList) {
                if (shopId != null && !shopId.equals(account.getId())) {
                    continue;
                }
                if (StringUtils.isNotEmpty(account.getConnectStr())) {
                    JSONObject jsonObject = JSON.parseObject(account.getConnectStr());
                    marketplaceId = jsonObject.getString("marketplaceId");
                } else {
                    logger.error("获取账户信息为空，accountId: {}", account.getId());
                    return;
                }
                if (StringUtils.isEmpty(marketplaceId)) {
                    logger.error("marketplaceId为空，accountId: {}", account.getId());
                    return;
                }
                this.createShopReportByType(account.getId(), amzReportType.getReportType(), dateFrom, dateto, amzReportType.getReportOption(),marketplaceId);
            }
        }
    }

    @Override
    public void createShopReportByType(Integer shopId, String reportType, String dataStartTime, String dataEndTime, String reportOptions,String marketplaceId) {
        ApiClient apiClient = initApiClient.initApiClient(shopId, GET_REPORT_DEFAULT_RATE_LIMIT_PERMIT);
        ReportsApi reportsApi = new ReportsApi(apiClient);
        CreateReportSpecification body = new CreateReportSpecification();
        body.setReportType(reportType);
        body.setDataStartTime(DateUtil.convertStringToISODate(dataStartTime, DateUtil.ISO_PATTERN_YMDHMSSZ));
        body.setDataEndTime(DateUtil.convertStringToISODate(dataEndTime, DateUtil.ISO_PATTERN_YMDHMSSZ));
        List<String> marketplaceIds = CollUtil.newArrayList(marketplaceId);
        body.setMarketplaceIds(marketplaceIds);
        SysIfInvokeOutbound jrapInterfaceOutbound = new SysIfInvokeOutbound();
        jrapInterfaceOutbound.setInterfaceName("createReport" + reportType);
        jrapInterfaceOutbound.setInterfaceUrl(reportsApi.getApiClient().getBasePath() + "/reports/2021-06-30/reports");
        jrapInterfaceOutbound.setRequestParameter(shopId.toString() + ";\n" + JSONObject.toJSONString(body));
        jrapInterfaceOutbound.setRequestTime(new Date());
        try {
            CreateReportResponse response = reportsApi.createReport(body);

            jrapInterfaceOutbound.setResponseTime(System.currentTimeMillis());
            jrapInterfaceOutbound.setResponseCode("200");
            invokeLogStrategy.insertSysIfInvokeOutbound(jrapInterfaceOutbound);
            String reportId = response.getReportId();
            saveAmzCreateReport(shopId, body, reportId);
        } catch (ApiException e) {
            jrapInterfaceOutbound.setResponseTime(System.currentTimeMillis());
            jrapInterfaceOutbound.setResponseContent(e.getMessage() + "\n" + e.toString());
            jrapInterfaceOutbound.setResponseCode(String.valueOf(e.getCode()));
            invokeLogStrategy.insertSysIfInvokeOutbound(jrapInterfaceOutbound);
            logger.error("亚马逊数据同步异常：{},report_type:{},shop:{}", e, reportType, shopId);
        }
    }

    private void saveAmzCreateReport(Integer shopId, CreateReportSpecification body, String amzReportId) {
        Account account = this.accountService.getById(shopId);
        AmzCreateReport record = new AmzCreateReport();
        if(account != null) {
            record.setOrganizationId(account.getOrgId());
            record.setShopId(shopId.longValue());
            record.setMarketplaceIds(StringUtil.paserListToStr(body.getMarketplaceIds(), ","));
            record.setReportType(body.getReportType());
            record.setAmzReportId(amzReportId);
            record.setDataStartTime(body.getDataStartTime());
            record.setDataEndTime(body.getDataEndTime());
            record.setGetFlag("N");
            amzCreateReportService.insertAmzCreateReport(record);
        }
    }




    /**
     * Description: 根据creatReport 创建amzReport
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/5/11
     */

    @Override
    public void getCreateReport() {
        List<Account> sellerShopList = this.accountService.list(
                Wrappers.<Account>lambdaQuery()
                        .eq(Account::getOrgId, 1000049)
                        .eq(Account::getAccountType, "Amazon_SC")
                        .eq(Account::getStatus, "runing")
                        .isNull(Account::getIsDelete));
        for (Account account : sellerShopList) {
            try {
                //或许未生成报告项
                List<AmzCreateReport> amzCreateReportList = amzCreateReportService.queryShopReportUnGet(account.getId().longValue());
                if (CollectionUtils.isEmpty(amzCreateReportList)) {
                    continue;
                }
                ApiClient apiClient = initApiClient.initApiClient(account.getId(), GET_REPORT_RATE_LIMIT_PERMIT);
                ReportsApi reportsApi = new ReportsApi(apiClient);

                for (AmzCreateReport amzCreateReport : amzCreateReportList) {
                    SysIfInvokeOutbound jrapInterfaceOutbound = new SysIfInvokeOutbound();
                    jrapInterfaceOutbound.setInterfaceName("getReport");
                    jrapInterfaceOutbound.setInterfaceUrl(reportsApi.getApiClient().getBasePath() + "/reports/2021-06-30/reports/{reportId}");
                    jrapInterfaceOutbound.setRequestParameter(account.getId().toString() + ";\n" + amzCreateReport.getAmzReportId());
                    jrapInterfaceOutbound.setRequestTime(new Date());

                    try {
                        Report response = reportsApi.getReport(amzCreateReport.getAmzReportId());

                        jrapInterfaceOutbound.setResponseTime(System.currentTimeMillis());
                        jrapInterfaceOutbound.setResponseCode("200");
                        invokeLogStrategy.insertSysIfInvokeOutbound(jrapInterfaceOutbound);

                        saveReport(response, account.getOrgId(), account.getId().longValue());
                        //更新拉取报表状态为 Y
                        amzCreateReportService.updateCreateReportGet(amzCreateReport.getCreateReportId());
                    } catch (ApiException e) {
                        jrapInterfaceOutbound.setResponseTime(System.currentTimeMillis());
                        jrapInterfaceOutbound.setResponseContent(e.getMessage() + "\n" + e.toString());
                        jrapInterfaceOutbound.setResponseCode(String.valueOf(e.getCode()));
                        invokeLogStrategy.insertSysIfInvokeOutbound(jrapInterfaceOutbound);
                        logger.error("亚马逊数据同步异常：{}", e);
                    }
                }
            } catch (Exception e) {
                logger.error("店铺#{} 获取手动创建的报告异常：{}", account.getAccountInit(), e);
            }
        }
    }
}
