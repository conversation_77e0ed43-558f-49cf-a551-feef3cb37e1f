package com.bizark.op.service.service.mar.material;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.ad.api.entity.ad.release.AdAccountInfo;
import com.bizark.ad.api.service.ad.IAdAccountInfoService;
import com.bizark.boss.api.service.TaskCenterService;
import com.bizark.boss.api.vo.dashboard.TaskCenterRequest;
import com.bizark.common.exception.CommonException;
import com.bizark.op.api.dto.material.CreatifyRenderVideoCallbackDTO;
import com.bizark.op.api.dto.material.UpdateMaterialNameDTO;
import com.bizark.op.api.enm.RequestEnum;
import com.bizark.op.api.enm.mar.MarMaterialSoureEnum;
import com.bizark.op.api.enm.mar.creatify.CreatifyRenderVideoStatusEnum;
import com.bizark.op.api.enm.mar.mater.*;
import com.bizark.op.api.entity.op.mar.material.*;
import com.bizark.op.api.entity.op.mar.material.dto.AdMaterialSubmitRequest;
import com.bizark.op.api.entity.op.mar.material.dto.MarMaterialAdDTO;
import com.bizark.op.api.entity.op.mar.material.dto.UploadVideoDTO;
import com.bizark.op.api.entity.op.mar.material.request.UploadTiktokMaterialRequest;
import com.bizark.op.api.entity.op.mar.material.response.*;
import com.bizark.op.api.entity.op.mar.vo.MarMaterialAnalyseInfoVO;
import com.bizark.op.api.entity.op.mar.vo.MarMaterialCenterInfoVO;
import com.bizark.op.api.entity.op.mar.vo.MarMaterialUploadDTO;
import com.bizark.op.api.entity.op.sale.Products;
import com.bizark.op.api.entity.op.tabcut.vo.TabcutVideoTagRealtionDTO;
import com.bizark.op.api.entity.op.tk.expert.TkDataVideoDetails;
import com.bizark.op.api.form.mar.material.MarMaterialQuery;
import com.bizark.op.api.service.mar.material.*;
import com.bizark.op.api.service.mar.material.symphony.SymphonyService;
import com.bizark.op.api.service.sale.ProductsService;
import com.bizark.op.api.service.tabcut.TabcutCreatorVideoService;
import com.bizark.op.api.service.tabcut.TabcutCreatorVideoUrlService;
import com.bizark.op.api.service.tk.expert.TkDataVideoDetailsService;
import com.bizark.op.api.vo.mar.CreatorVideoVO;
import com.bizark.op.common.util.*;
import com.bizark.op.service.mapper.mar.material.MarMaterialCenterInfoMapper;
import com.bizark.op.service.util.ConvertUtils;
import com.bizark.op.service.util.MaterialIdInitUtils;
import com.bizark.op.service.util.tiktok.TiktokUserRequestUtils;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【mar_material_center_info(tiktok素材中心表)】的数据库操作Service实现
 * @createDate 2025-02-10 17:06:16
 */
@Service
@Slf4j
public class MarMaterialCenterInfoServiceImpl extends ServiceImpl<MarMaterialCenterInfoMapper, MarMaterialCenterInfo>
        implements MarMaterialCenterInfoService {

    @Autowired
    private MarMaterialUserInfoService materialUserInfoService;

    @Autowired
    private TiktokUserRequestUtils tiktokUserRequestUtils;

    @Autowired
    private MarMaterialReleaseInfoService marMaterialReleaseInfoService;

    @Autowired
    private MarMaterialMakeInfoService materialMakeInfoService;

    @Autowired
    private TaskCenterService taskCenterService;

    @Value("${task.center.file.path}")
    private String filePath;

    @Autowired
    private MarMaterialOpLogService marMaterialOpLogService;

    @Autowired
    private IAdAccountInfoService iAdAccountInfoService;

    @Autowired
    private MarMaterialCenterInfoMapper marMaterialCenterInfoMapper;

    @Autowired
    private TkDataVideoDetailsService tkDataVideoDetailsService;

    @Autowired
    private MarMaterialCreatifyLinkParamService marMaterialCreatifyLinkParamService;

    @Autowired
    private TabcutCreatorVideoUrlService tabcutCreatorVideoUrlService;

    @Autowired
    private TabcutCreatorVideoService tabcutCreatorVideoService;


    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ProductsService productsService;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private SymphonyService symphonyService;

    @Override
    public void uploadTiktokMaterial(UploadVideoDTO dto) {
        List<MarMaterialCenterInfo> centerInfoList = lambdaQuery().in(MarMaterialCenterInfo::getMaterialNum, dto.getMaterialIdList()).isNull(MarMaterialCenterInfo::getVideoId).or().eq(MarMaterialCenterInfo::getVideoId, "").list();
        if (CollectionUtils.isEmpty(centerInfoList)) {
            throw new CommonException("TK视频ID都已生成，不可重复发布");
        }

        MarMaterialUserInfo userInfo = materialUserInfoService.lambdaQuery().eq(MarMaterialUserInfo::getOpenId, dto.getOpenId()).one();
        if (null == userInfo) {
            throw new CommonException("未查询到用户账号信息");
        }

        List<String> materialNumList = centerInfoList.stream().map(e -> e.getMaterialNum()).distinct().collect(Collectors.toList());

        List<MarMaterialReleaseInfo> list = marMaterialReleaseInfoService.lambdaQuery().select(MarMaterialReleaseInfo::getMaterialNum)
                .in(MarMaterialReleaseInfo::getMaterialNum, materialNumList)
                .eq(MarMaterialReleaseInfo::getBusinessId, userInfo.getOpenId())
                .in(MarMaterialReleaseInfo::getStatus, Arrays.asList(MaterialGenStatusEnum.SUCCESS.getValue(), MaterialGenStatusEnum.PROCESSING.getValue()))
                .eq(MarMaterialReleaseInfo::getReleaseType, MaterialReleaseTypeEnum.USER.getCode())
                .list();
        if (!CollectionUtils.isEmpty(list)) {
            List<String> marList = list.stream().map(e -> e.getMaterialNum()).distinct().collect(Collectors.toList());

            centerInfoList = centerInfoList.stream().filter(e-> !marList.contains(e.getMaterialNum())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(centerInfoList)) {
            throw new CommonException("已有发布至TK账号且在发布中，不可重复发布");
        }


        for (MarMaterialCenterInfo centerInfo : centerInfoList) {

            String url = replaceUrl(centerInfo.getMaterialUrl());
            //新增发布记录
            MarMaterialReleaseInfo releaseInfo = new MarMaterialReleaseInfo();
            releaseInfo.setOrgId(centerInfo.getOrgId())
                    .setReleaseNum(MaterialIdInitUtils.createId("S"))
                    .setMaterialNum(centerInfo.getMaterialNum())
                    .setBusinessId(userInfo.getOpenId())
                    .setBusinessName(userInfo.getUserName())
                    .setVideoUrl(url)
                    .setReleaseType(MaterialReleaseTypeEnum.USER.getCode())
                    .setStatus(MaterialGenStatusEnum.PROCESSING.getValue());

            requestToTkUserUpload(url, userInfo, releaseInfo);
            marMaterialReleaseInfoService.save(releaseInfo);
            updateById(centerInfo);

            marMaterialOpLogService.saveOpLog(centerInfo.getMaterialNum(), "当前用户%s 进行了TK账号发布");
            marMaterialOpLogService.saveOpLog(releaseInfo.getReleaseNum(), "当前用户%s 新增了一条TK账号发布记录");
        }
    }

    private String replaceUrl(String url) {
        if(StringUtils.isEmpty(url)) return url;

        if (url.contains("https://hengjian-prod-public.oss-cn-hangzhou.aliyuncs.com")) {
           return url.replace("https://hengjian-prod-public.oss-cn-hangzhou.aliyuncs.com", "https://oss.ehengjian.com");
        }
        return url;
    }

    private void requestToTkUserUpload(String url, MarMaterialUserInfo userInfo, MarMaterialReleaseInfo releaseInfo) {
        UploadTiktokMaterialRequest request = new UploadTiktokMaterialRequest();
        request.setBusiness_id(userInfo.getOpenId());
        request.setVideo_url(url);
        UploadTiktokMaterialRequest.UploadTiktokMaterialPostInfo info = new UploadTiktokMaterialRequest.UploadTiktokMaterialPostInfo();
        request.setPost_info(info);
        info.setUpload_to_draft(Boolean.TRUE);
        String result = tiktokUserRequestUtils.sendRequest(TiktokUserUrlEnum.PUBLISH_VIDEO.getUrl(), RequestEnum.POST.getCode(), userInfo, JSON.toJSONString(request));
        UploadTiktokMaterialResponse response = JSONObject.parseObject(result, UploadTiktokMaterialResponse.class);
        if (response.getCode() != 0) {
            log.error("当前发布TIKTOK视频到个人账户失败,当前素材中心ID:{}, 返回参数为:{}", releaseInfo.getMaterialNum(), response);
            releaseInfo.setFailMessage(response.getMessage());
            releaseInfo.setStatus(MaterialGenStatusEnum.FAIL.getValue());
        } else {
            releaseInfo.setShareId(response.getData().getShare_id());
        }
    }

    /**
     * 上传素材
     * @param dto
     */
    @Override
    public PostToTiktokVO uploadTiktokAdAccountForMaterial(UploadVideoDTO dto) {
        List<MarMaterialCenterInfo> centerInfoList = lambdaQuery().in(MarMaterialCenterInfo::getMaterialNum, dto.getMaterialIdList()).list();
        if (CollectionUtils.isEmpty(centerInfoList)) {
            throw new CommonException("素材库数据获取异常");
        }


        AdAccountInfo info = iAdAccountInfoService.findByScopeAndSaleChannel(dto.getOpenId(), "tiktok");
        if (null == info || StringUtils.isEmpty(info.getAdAccessToken())) {
            throw new CommonException("未查询到用户账号信息");
        }
        List<String> materialNumList = centerInfoList.stream().map(e -> e.getMaterialNum()).distinct().collect(Collectors.toList());

        List<MarMaterialReleaseInfo> list = marMaterialReleaseInfoService.lambdaQuery().select(MarMaterialReleaseInfo::getMaterialNum)
                .in(MarMaterialReleaseInfo::getMaterialNum, materialNumList)
                .eq(MarMaterialReleaseInfo::getBusinessId, info.getProfileId())
                .in(MarMaterialReleaseInfo::getStatus, Arrays.asList(MaterialGenStatusEnum.SUCCESS.getValue(), MaterialGenStatusEnum.PROCESSING.getValue()))
                .eq(MarMaterialReleaseInfo::getReleaseType, MaterialReleaseTypeEnum.AD.getCode())
                .list();
        if (!CollectionUtils.isEmpty(list)) {
            List<String> marList = list.stream().map(e -> e.getMaterialNum()).distinct().collect(Collectors.toList());

            centerInfoList = centerInfoList.stream().filter(e-> !marList.contains(e.getMaterialNum())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(centerInfoList)) {
                throw new CommonException("已全部在对应广告账号内发布");
            }
            if (!Objects.equals(dto.getConfirm(), 1)) {
                // 直接响应给前端
                PostToTiktokVO tiktokVO = new PostToTiktokVO();
                tiktokVO.setMaterialNumber(marList);
                tiktokVO.setConfirm(1);
                tiktokVO.setMessage("已生成广告Video id的会自动过滤掉");
                return tiktokVO;
            }

        }
        startUploadAdMaterial(centerInfoList, info);

        PostToTiktokVO tiktokVO = new PostToTiktokVO();
        tiktokVO.setConfirm(-1);
        tiktokVO.setMessage("已生成发布记录，后续请在发布记录查看结果");
        return tiktokVO;
    }



    @Override
    public void uploadTiktokAdAccountForMaterialAi(UploadVideoDTO dto) {
        List<MarMaterialCenterInfo> centerInfoList = lambdaQuery().in(MarMaterialCenterInfo::getMaterialNum, dto.getMaterialIdList()).list();
        if (CollectionUtils.isEmpty(centerInfoList)) {
            throw new CommonException("素材库数据获取异常");
        }
        List<String> modelList = centerInfoList.stream().map(e -> e.getModel()).distinct().collect(Collectors.toList());
        if (modelList.size()>1) {
            throw new CommonException("当前存在多个不同型号的素材, 请选择相同模型的素材");
        }
        String model = modelList.get(0);


        AdAccountInfo info = iAdAccountInfoService.findByScopeAndSaleChannel(dto.getOpenId(), "tiktok");
        if (null == info || StringUtils.isEmpty(info.getAdAccessToken())) {
            throw new CommonException("未查询到用户账号信息");
        }
        List<String> materialNumList = centerInfoList.stream().map(e -> e.getMaterialNum()).distinct().collect(Collectors.toList());
        List<MarMaterialReleaseInfo> list = marMaterialReleaseInfoService.lambdaQuery().select(MarMaterialReleaseInfo::getMaterialNum)
                .in(MarMaterialReleaseInfo::getMaterialNum, materialNumList)
                .eq(MarMaterialReleaseInfo::getBusinessId, info.getProfileId())
                .in(MarMaterialReleaseInfo::getStatus, Arrays.asList(MaterialGenStatusEnum.SUCCESS.getValue(), MaterialGenStatusEnum.PROCESSING.getValue()))
                .eq(MarMaterialReleaseInfo::getReleaseType, MaterialReleaseTypeEnum.AD.getCode())
                .list();
        if (!CollectionUtils.isEmpty(list)) {
            List<String> marList = list.stream().map(e -> e.getMaterialNum()).distinct().collect(Collectors.toList());

            centerInfoList = centerInfoList.stream().filter(e-> !marList.contains(e.getMaterialNum())).collect(Collectors.toList());
        }

        if (!CollectionUtils.isEmpty(centerInfoList)) {
            startUploadAdMaterial(centerInfoList, info);
        }

        List<MarMaterialReleaseInfo> videoList = marMaterialReleaseInfoService.lambdaQuery().select(MarMaterialReleaseInfo::getVideoId)
                .in(MarMaterialReleaseInfo::getMaterialNum, materialNumList)
                .eq(MarMaterialReleaseInfo::getBusinessId, info.getProfileId())
                .in(MarMaterialReleaseInfo::getStatus, Arrays.asList(MaterialGenStatusEnum.SUCCESS.getValue(), MaterialGenStatusEnum.PROCESSING.getValue()))
                .eq(MarMaterialReleaseInfo::getReleaseType, MaterialReleaseTypeEnum.AD.getCode())
                .list();

        List<String> videoIdList = videoList.stream().map(e -> e.getVideoId()).distinct().collect(Collectors.toList());

        List<List<String>> splitIdList = CollectionUtil.split(videoIdList, 10);

        AdMaterialSubmitRequest request = new AdMaterialSubmitRequest();
        List<AdMaterialSubmitRequest.AdMaterialSubmitSlots> slots = request.getSlots();
        request.setAuto_soundtrack(Boolean.FALSE);
        request.setAdvertiser_id(info.getProfileId());
        for (int number = 0; number < splitIdList.size(); number++) {
            List<String> videoSplitList = splitIdList.get(number);
            AdMaterialSubmitRequest.AdMaterialSubmitSlots submitSlots = new AdMaterialSubmitRequest.AdMaterialSubmitSlots();
            submitSlots.setOrder(number);
            submitSlots.setTag("tiktok");
            submitSlots.setVideo_ids(videoSplitList);
            submitSlots.setMute(Boolean.FALSE);
            submitSlots.setBgm_volume(30);
            slots.add(submitSlots);
        }


        String result = tiktokUserRequestUtils.sendAdRequest(TiktokUserUrlEnum.AD_MATERIAL_SUBMIT.getUrl(), JSON.toJSONString(request),info.getAdAccessToken(), RequestEnum.POST.getCode());
        TkAdVideoSubmitResponse tkAdVideoSubmitResponse = JSONObject.parseObject(result, TkAdVideoSubmitResponse.class);

        //上传失败
        if (tkAdVideoSubmitResponse.getCode() != 0) {
            log.error("TIKTOK广告素材AI生成发布异常, 请求ID:{},返回对象为:{}",info.getProfileId(), tkAdVideoSubmitResponse);
            throw new CommonException(String.format("TIKTOK广告素材AI生成发布异常,原因:%s", tkAdVideoSubmitResponse.getMessage()));
        }

        String materialPackageId = tkAdVideoSubmitResponse.getData().get(0).getMaterial_package_id();

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("advertiser_id", info.getProfileId());
        jsonObject.put("material_package_id", materialPackageId);
        //开始异步创建任务
        List<String> taskIdList = new ArrayList<>();
        for (int i = 0; i < dto.getNumber(); i++) {
            MarMaterialMakeInfo marMaterialMakeInfo = new MarMaterialMakeInfo();
            marMaterialMakeInfo.setOrgId(centerInfoList.get(0).getOrgId())
                    .setGenId(MaterialIdInitUtils.createId("VAI"))
                    .setGenStatus(MaterialGenStatusEnum.PROCESSING.getValue())
                    .setGenWay(MaterialWayEnum.TIKTOK_AD_AI.getValue())
                    .setOrgMaterialNum(org.apache.commons.lang3.StringUtils.join(videoIdList, ","))
                    .setAdScope(info.getProfileId()).setApiMaterialId(materialPackageId).setVideoFlag(1).setModel(model);
            materialMakeInfoService.save(marMaterialMakeInfo);

            reuploadToGetTaskId(jsonObject, info, marMaterialMakeInfo, taskIdList);
            materialMakeInfoService.updateById(marMaterialMakeInfo);
            marMaterialOpLogService.saveOpLog(marMaterialMakeInfo.getGenId(), "当前用户%s 新增TK广告AI");
        }
        if (!CollectionUtils.isEmpty(taskIdList)) {
            getTkMaterialAiResult(taskIdList);
        }
    }

    private void reuploadToGetTaskId(JSONObject jsonObject, AdAccountInfo info, MarMaterialMakeInfo marMaterialMakeInfo, List<String> taskIdList) {
        String taskResult = tiktokUserRequestUtils.sendAdRequest(TiktokUserUrlEnum.AD_ASYNC_CREATE_TASK.getUrl(), jsonObject.toJSONString(), info.getAdAccessToken(), RequestEnum.POST.getCode());
        TkAdVideoAsyncTaskResponse taskResponse = JSONObject.parseObject(taskResult, TkAdVideoAsyncTaskResponse.class);
        if (taskResponse.getCode() != 0) {
            log.error("TIKTOK广告异步素材AI生成创建任务异常, 请求ID:{},返回对象为:{}", info.getProfileId(), taskResponse);
            marMaterialMakeInfo.setGenStatus(MaterialGenStatusEnum.FAIL.getValue());
            marMaterialMakeInfo.setRemark(taskResponse.getMessage());
        } else {
            marMaterialMakeInfo.setTaskId(taskResponse.getData().getTask_id());
            taskIdList.add(taskResponse.getData().getTask_id());
        }
    }

    @Override
    public void reissue(List<Long> ids) {
        List<MarMaterialReleaseInfo> releaseInfoList = marMaterialReleaseInfoService.listByIds(ids);
        if (CollectionUtils.isEmpty(releaseInfoList)) {
            throw new CommonException("发布记录获取异常");
        }
        List<MarMaterialReleaseInfo> failList = releaseInfoList.stream().filter(e -> e.getStatus().equals(MaterialGenStatusEnum.FAIL.getValue())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(failList)) {
            throw new CommonException("当前无失败的发布记录信息");
        }
        Map<Integer, List<MarMaterialReleaseInfo>> listMap = failList.stream().collect(Collectors.groupingBy(e -> e.getReleaseType()));

        for (Map.Entry<Integer, List<MarMaterialReleaseInfo>> entry : listMap.entrySet()) {
            Integer key = entry.getKey();
            List<MarMaterialReleaseInfo> entryValue = entry.getValue();

            List<String> businessIdList = entryValue.stream().map(e -> e.getBusinessId()).distinct().collect(Collectors.toList());
            List<String> materialNumList = entryValue.stream().map(e -> e.getMaterialNum()).distinct().collect(Collectors.toList());
            if (key.equals(MaterialReleaseTypeEnum.USER.getCode())) {
                List<MarMaterialUserInfo> userInfoList = materialUserInfoService.lambdaQuery().in(MarMaterialUserInfo::getOpenId, businessIdList).list();
                Map<String, MarMaterialUserInfo> userInfoMap = userInfoList.stream().collect(Collectors.toMap(e -> e.getOpenId(), Function.identity(), (a, b) -> a));
                for (MarMaterialReleaseInfo info : entryValue) {
                    MarMaterialUserInfo userInfo = userInfoMap.get(info.getBusinessId());
                    info.setStatus(MaterialGenStatusEnum.PROCESSING.getValue());
                    requestToTkUserUpload(info.getVideoUrl(), userInfo, info);
                    marMaterialOpLogService.saveOpLog(info.getMaterialNum(), "当前用户%s 重新进行发布至TK账号");
                }
                marMaterialReleaseInfoService.updateBatchById(entryValue);
            } else {
                List<AdAccountInfo> accountInfos = iAdAccountInfoService.findByScopeAndSaleChannel(businessIdList, "tiktok");
                Map<String, AdAccountInfo> accountInfoMap = accountInfos.stream().collect(Collectors.toMap(e -> e.getProfileId(), Function.identity(), (a, b) -> a));
                List<MarMaterialCenterInfo> list = lambdaQuery().in(MarMaterialCenterInfo::getMaterialNum, materialNumList).list();
                Map<String, MarMaterialCenterInfo> centerInfoMap = list.stream().collect(Collectors.toMap(e -> e.getMaterialNum(), Function.identity(), (a, b) -> a));
                List<MarMaterialAdDTO> marMaterialAdDTOS = new ArrayList<>();
                for (MarMaterialReleaseInfo info : entryValue) {
                    AdAccountInfo tokenInfo = accountInfoMap.get(info.getBusinessId());
                    MarMaterialCenterInfo centerInfo = centerInfoMap.get(info.getMaterialNum());
                    info.setStatus(MaterialGenStatusEnum.PROCESSING.getValue());
                    //requestToAdTkUpload(tokenInfo, centerInfo, info);
                    MarMaterialAdDTO marMaterialAdDTO = new MarMaterialAdDTO();
                    marMaterialAdDTO.setAccountInfo(tokenInfo);
                    marMaterialAdDTO.setCenterInfo(centerInfo);
                    marMaterialAdDTO.setInfo(info);
                    marMaterialAdDTOS.add(marMaterialAdDTO);
                    marMaterialOpLogService.saveOpLog(info.getMaterialNum(), "当前用户%s 重新进行发布至TK广告后台");
                }
                marMaterialReleaseInfoService.updateBatchById(entryValue);

                log.info("当前开始执行异步重新发布,当前需要发布得记录条数:{}", marMaterialAdDTOS.size());
                if (CollectionUtils.isEmpty(marMaterialAdDTOS)) return;
                startPublishToAd(marMaterialAdDTOS);
            }

        }


    }

    @Override
    public void anewReload(List<Long> ids) {

        List<MarMaterialMakeInfo> marMaterialMakeInfos = materialMakeInfoService.lambdaQuery().in (MarMaterialMakeInfo::getId, ids)
                .eq(MarMaterialMakeInfo::getGenStatus, MaterialGenStatusEnum.FAIL.getValue())
                .list();
        if (CollectionUtils.isEmpty(marMaterialMakeInfos)) {
            throw new CommonException("未获取到失败的生成记录");
        }

        Map<Integer, List<MarMaterialMakeInfo>> wayMap = marMaterialMakeInfos.stream().collect(Collectors.groupingBy(MarMaterialMakeInfo::getGenWay));

        for (Map.Entry<Integer, List<MarMaterialMakeInfo>> entry : wayMap.entrySet()) {
            if (Objects.equals(entry.getKey(), MaterialWayEnum.CREATIFT_AI.getValue())) {
                materialMakeInfoService.regenerateCreatifyVideos(entry.getValue());
            }
            if (Objects.equals(entry.getKey(), MaterialWayEnum.SYMPHONY.getValue())) {
                symphonyService.regenerateVideos(entry.getValue());
            }


        }

//        List<MarMaterialMakeInfo> creatifyInfo = marMaterialMakeInfos.stream()
//                .filter(c -> Objects.equals(c.getGenWay(), MaterialWayEnum.CREATIFT_AI.getValue()))
//                .collect(Collectors.toList());
//
//        if (CollectionUtil.isEmpty(creatifyInfo)) {
//            throw new CommonException("只有creatifyAi生成记录可重新生成");
//        }
//        materialMakeInfoService.regenerateCreatifyVideos(creatifyInfo);

//        Map<Integer, List<MarMaterialMakeInfo>> getWayMap = marMaterialMakeInfos.stream().collect(Collectors.groupingBy(e -> e.getGenWay()));
//
//        for (Map.Entry<Integer, List<MarMaterialMakeInfo>> entry : getWayMap.entrySet()) {
//            Integer key = entry.getKey();
//            List<MarMaterialMakeInfo> makeInfos = entry.getValue();
//            if (key.equals(MaterialWayEnum.TIKTOK_AD_AI.getValue())) {
//                List<String> scopeList = makeInfos.stream().map(e -> e.getAdScope()).distinct().collect(Collectors.toList());
//                List<AdAccountInfo> accountInfos = iAdAccountInfoService.findByScopeAndSaleChannel(scopeList, "tiktok");
//                Map<String, AdAccountInfo> accountInfoMap = accountInfos.stream().collect(Collectors.toMap(e -> e.getProfileId(), Function.identity(), (a, b) -> a));
//                List<String> taskIdList = new ArrayList<>();
//                for (MarMaterialMakeInfo makeInfo : makeInfos) {
//                    JSONObject jsonObject = new JSONObject();
//                    jsonObject.put("advertiser_id", makeInfo.getAdScope());
//                    jsonObject.put("material_package_id", makeInfo.getApiMaterialId());
//                    AdAccountInfo info = accountInfoMap.get(makeInfo.getAdScope());
//                    reuploadToGetTaskId(jsonObject, info, makeInfo, taskIdList);
//                }
//                materialMakeInfoService.updateBatchById(makeInfos);
//                if (!CollectionUtils.isEmpty(taskIdList)) {
//                    getTkMaterialAiResult(taskIdList);
//                }
//            } else {
//                // todo 写creatify AI
//                materialMakeInfoService.regenerateCreatifyVideos(makeInfos);
//            }
//        }
    }



    @Override
    public void getTkMaterialAiResult(List<String> taskIdList) {
        Long id = null;
        while (true) {
            List<MarMaterialMakeInfo> infoList = materialMakeInfoService.findByIdAsc(id, taskIdList);
            if (CollectionUtils.isEmpty(infoList)) break;
            id = infoList.get(infoList.size() - 1).getId();

            List<String> scopeList = infoList.stream().map(e -> e.getAdScope()).distinct().collect(Collectors.toList());
            List<AdAccountInfo> accountInfos = iAdAccountInfoService.findByScopeAndSaleChannel(scopeList, "tiktok");
            Map<String, String> scopeMap = accountInfos.stream().collect(Collectors.toMap(e -> e.getProfileId(), e -> e.getAdAccessToken(), (a, b) -> a));
            List<MarMaterialOpLog> logs = new ArrayList<>();
            for (MarMaterialMakeInfo info : infoList) {
                MarMaterialOpLog opLog = new MarMaterialOpLog();
                logs.add(opLog);
                opLog.setMaterialNum(info.getMaterialNum());
                opLog.setOperateContent(String.format("当前用户%s 重新进行TIKTOK广告素材AI生成", UserUtils.getCurrentUserName()));
                String token = scopeMap.get(info.getAdScope());
                if (StringUtils.isBlank(token)) {
                    log.error("TIKTOK广告素材AI生成获取结果异常, MARE_ID:{}, 请求ID:{},原因:{}", info.getId(), info.getAdScope(), "无法获取账号对应TOKEN");
                    continue;
                }
                String result = TiktokUserUrlEnum.AD_ASYNC_GET_TASK_RESULT.getUrl() + "?advertiser_id =" + info.getAdScope() + "&task_id=" + info.getTaskId();

                TkAdVideoAsyncTaskResultResponse response = JSONObject.parseObject(result, TkAdVideoAsyncTaskResultResponse.class);
                if (response.getCode() != 0) {
                    log.error("TIKTOK广告素材AI生成获取结果异常, MARE_ID:{}, 请求ID:{},返回结果:{}", info.getId(), info.getAdScope(), response);
                    continue;
                }

                if (response.getData().getStatus().equals(MaterialGenStatusEnum.SUCCESS.getTiktokStatusValue())) {
                    log.info("TIKTOK广告素材AI生成获取结果成功, id:{}", info.getId());
                    info.setGenStatus(MaterialGenStatusEnum.SUCCESS.getValue());
                    info.setResVideoId(response.getData().getVideos().get(0).getVideo_id());
                    //创建一条素材中心的数据
                    MarMaterialCenterInfo centerInfo = new MarMaterialCenterInfo();
                    centerInfo.setOrgId(info.getOrgId())
                            .setMaterialNum(MaterialIdInitUtils.createId("V"))
                            .setContentType("VIDEO").setMaterialUrl(response.getData().getVideos().get(0).getUrl())
                            .setModel(info.getModel()).setMaterialSource(2)
                            .setDownloadTotal(0).setAiTotal(0).settingDefaultSystemCreate();
                    save(centerInfo);
                    continue;
                }
                if (response.getData().getStatus().equals(MaterialGenStatusEnum.FAIL.getTiktokStatusValue())) {
                    log.info("TIKTOK广告素材AI生成获取结果失败, id:{}, 返回结果:{}", info.getId(), response);
                    info.setGenStatus(MaterialGenStatusEnum.FAIL.getValue());
                    info.setRemark(response.getData().getError_msg());
                    continue;
                }
                //进行中
                log.info("TIKTOK广告素材AI生成获取结果还在进行中, id:{}", info.getId());
            }
            marMaterialOpLogService.saveBatch(logs);
            materialMakeInfoService.updateBatchById(infoList);
        }








    }


    /**
     * 查询素材中心列表查询
     *
     * @param query
     * @return
     */
    @Override
    public List<MarMaterialCenterInfo> queryMaterialCenterList(MarMaterialQuery query) {
        query.settingQueryParam();
        return this.baseMapper.queryMaterialCenterList(query);
    }


    /**
     * @description: 根据ERPSKU获取型号
     * @author: Moore
     * @date: 2025/2/14 15:33
     * @param
     * @param erpSku
     * @return: com.bizark.op.api.entity.op.sale.Products
    **/
    @Override
    public JSONObject queryErpskuModel(String erpSku,Integer orgId) {
        JSONObject res = new JSONObject();
        res.put("model", "");
        Products products = this.baseMapper.selectMaterialSkuModel(erpSku, orgId);
        if (products != null) {
            res.put("model", products.getProductMidModel());
        }
        return res;
    }

    @Override
    public void syncSaleToVideo() {
        Long id = null;
        while (true) {
            List<MarMaterialCenterInfo> infoList = marMaterialCenterInfoMapper.syncSale(id);
            if (CollectionUtils.isEmpty(infoList)) {
                break;
            }
            id = infoList.get(infoList.size() - 1).getId();

            List<String> videoIdList = infoList.stream().map(e -> e.getVideoId()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(videoIdList)) continue;

            List<TkDataVideoDetails> tkDataVideoDetails = tkDataVideoDetailsService.syncSaleToVideo(videoIdList);
            if (CollectionUtils.isEmpty(tkDataVideoDetails)) continue;
            Map<String, TkDataVideoDetails> videoMap = tkDataVideoDetails.stream().collect(Collectors.toMap(e -> e.getVideoId(), Function.identity(), (a, b) -> a));
            for (MarMaterialCenterInfo info : infoList) {
                TkDataVideoDetails details = videoMap.get(info.getVideoId());
                if (null == details) continue;

                info.setQuantity(null == details.getProductSales() ? 0 : details.getProductSales().intValue());
                info.setSaleAmount(details.getRevenue());
            }
            updateBatchById(infoList);
        }
    }

    /**
     * 素材中心导出
     *
     * @param query
     * @param authUserEntity
     */
    @Override
    public void exportmaterialCenterList(MarMaterialQuery query, UserEntity authUserEntity) {
        TaskCenterRequest request = new TaskCenterRequest();
        request.setOrgId(query.getOrgId());
        request.setTaskCode("erp.mar_material_center.export");
        List<Object> list = new ArrayList<>();
        list.add(JSON.toJSONString(query));
        request.setArgs(list);
        taskCenterService.startTask(request, authUserEntity);
    }

    /** 素材分析导出
     *
     * @param query
     * @param authUserEntity
     */
    @Override
    public void exportMaterialAnalyseList(MarMaterialQuery query, UserEntity authUserEntity) {
        TaskCenterRequest request = new TaskCenterRequest();
        request.setOrgId(query.getOrgId());
        request.setTaskCode("erp.mar_material_analyse.export");
        List<Object> list = new ArrayList<>();
        list.add(JSON.toJSONString(query));
        request.setArgs(list);
        taskCenterService.startTask(request, authUserEntity);
    }

    /** 素材中心异步导出
     *
     * @param queryParm
     */
    @Override
    public String asyncExportmaterialCenterList(String queryParm) {
        log.info("asyncExportmaterialCenterList start ... ");
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "素材中心" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);


        MarMaterialQuery marMaterialQuery = JSON.parseObject(queryParm, MarMaterialQuery.class);
        String uploadPath = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            ExcelWriter writer = EasyExcel.write(file, MarMaterialCenterInfoVO.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "素材中心导出").head(MarMaterialCenterInfoVO.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<MarMaterialCenterInfo> marMaterialCenterInfos = this.queryMaterialCenterList(marMaterialQuery);
                if (CollUtil.isEmpty(marMaterialCenterInfos)) {
                    break;
                }
                List<MarMaterialCenterInfoVO> exports = BeanCopyUtils.copyBeanList(marMaterialCenterInfos, MarMaterialCenterInfoVO.class);
                ConvertUtils.dictConvert(exports);
                log.info("query pageNo:{}, list.size = {}, ", pageNo, exports.size());
                log.info("exportList.size = " + exports.size());


                writer.write(exports, writeSheet);
                pageNo++;
            }

            writer.finish();

            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "mar/request/material/");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        }
        log.info("asyncExportmaterialCenterList end ... ");
        return uploadPath;

    }



    /** 素材中心异步导出
     *
     * @param queryParm
     */
    @Override
    public String asyncExportmaterialAnalyseList(String queryParm) {
        log.info("asyncExportmaterialAnalyseList start ... ");
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "素材分析" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);


        MarMaterialQuery marMaterialQuery = JSON.parseObject(queryParm, MarMaterialQuery.class);
        String uploadPath = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            ExcelWriter writer = EasyExcel.write(file, MarMaterialAnalyseInfoVO.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "素材分析导出").head(MarMaterialAnalyseInfoVO.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<MarMaterialCenterInfo> marMaterialCenterInfos = this.queryMaterialCenterList(marMaterialQuery);
                if (CollUtil.isEmpty(marMaterialCenterInfos)) {
                    break;
                }
                List<MarMaterialAnalyseInfoVO> exports = BeanCopyUtils.copyBeanList(marMaterialCenterInfos, MarMaterialAnalyseInfoVO.class);
                ConvertUtils.dictConvert(exports);
                log.info("query pageNo:{}, list.size = {}, ", pageNo, exports.size());
                log.info("exportList.size = " + exports.size());


                writer.write(exports, writeSheet);
                pageNo++;
            }

            writer.finish();

            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "mar/request/material/");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        }
        log.info("asyncExportMaterialAnalyseList end ... ");
        return uploadPath;

    }


    /**
     * 添加标签
     *
     * @param tagRelation
     * @param contextId
     */
    @Override
    public void addTag(TabcutVideoTagRealtionDTO tagRelation, Integer contextId,  String opType) {

        if (CollectionUtils.isEmpty(tagRelation.getIds())) {
            throw new CommonException("未选择素材数据");
        }


        // 默认是升序排序
        String savetagIds = tagRelation.getTagIds().stream().sorted().map(String::valueOf).collect(Collectors.joining(","));

        for (Long id : tagRelation.getIds()) {
            MarMaterialCenterInfo marMaterialCenterInfo = this.getById(id);
            String dbTagsIds = marMaterialCenterInfo.getMaterialTag();
            if (marMaterialCenterInfo == null) {
                continue;
            }

            if ("remove".equals(opType)) { //移除,传入的非空
                if ( CollectionUtils.isEmpty(tagRelation.getTagIds())){
                    throw new CommonException("请选择移除的标签!");
                }
                if (StringUtils.isEmpty(dbTagsIds)) { //库中为空，移除无意义，不操作
                    continue;
                }else {
                    List<Long> dbList =  Arrays.stream(dbTagsIds.split(","))
                            .map(Long::parseLong)
                            .collect(Collectors.toList());
                    dbList.removeAll(tagRelation.getTagIds());
                    savetagIds = dbList.stream().distinct().sorted().map(String::valueOf).collect(Collectors.joining(","));
                }
            }else if ("add".equals(opType)&&CollectionUtils.isNotEmpty(tagRelation.getTagIds())){ //追加
                if (!StringUtils.isEmpty(dbTagsIds)){
                    List<Long> dbList =  Arrays.stream(dbTagsIds.split(","))
                            .map(Long::parseLong)
                            .collect(Collectors.toList());
                    dbList.addAll(tagRelation.getTagIds());
                    savetagIds = dbList.stream().distinct().sorted().map(String::valueOf).collect(Collectors.joining(","));
                }
            }
            LambdaUpdateWrapper<MarMaterialCenterInfo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(MarMaterialCenterInfo::getId, tagRelation.getIds());
            updateWrapper.set(MarMaterialCenterInfo::getMaterialTag, StringUtils.isEmpty(savetagIds) ? null : savetagIds);
            this.update(null, updateWrapper);
            marMaterialOpLogService.saveMaterialTagLog(savetagIds, dbTagsIds, marMaterialCenterInfo.getMaterialNum(), contextId);
        }


    }



    private void startUploadAdMaterial(List<MarMaterialCenterInfo> centerInfoList, AdAccountInfo info) {

        List<MarMaterialAdDTO> dtoList = new ArrayList<>();
        for (MarMaterialCenterInfo centerInfo : centerInfoList) {
            MarMaterialAdDTO marMaterialAdDTO = new MarMaterialAdDTO();
            //新增发布记录
            MarMaterialReleaseInfo releaseInfo = new MarMaterialReleaseInfo();
            releaseInfo.setOrgId(centerInfo.getOrgId())
                    .setReleaseNum(MaterialIdInitUtils.createId("S"))
                    .setMaterialNum(centerInfo.getMaterialNum())
                    .setBusinessId(info.getProfileId())
                    .setBusinessName(info.getAccountName())
                    .setVideoUrl(centerInfo.getMaterialUrl())
                    .setReleaseType(MaterialReleaseTypeEnum.AD.getCode())
                    .setStatus(MaterialGenStatusEnum.PROCESSING.getValue());
            marMaterialReleaseInfoService.save(releaseInfo);
            marMaterialAdDTO.setCenterInfo(centerInfo);
            marMaterialAdDTO.setInfo(releaseInfo);
            marMaterialAdDTO.setAccountInfo(info);
            dtoList.add(marMaterialAdDTO);
            marMaterialOpLogService.saveOpLog(centerInfo.getMaterialNum(), "当前用户%s 进行了TK广告账号发布");
            marMaterialOpLogService.saveOpLog(releaseInfo.getReleaseNum(), "当前用户%s 新增了一条TK广告账号发布记录");
        }

        log.info("当前开始执行异步发布,当前需要发布得记录条数:{}", dtoList.size());
        if (CollectionUtils.isEmpty(dtoList)) return;
        startPublishToAd(dtoList);


    }

    private void startPublishToAd(List<MarMaterialAdDTO> dtoList) {
        List<List<MarMaterialAdDTO>> split = CollectionUtil.split(dtoList, 5);

        for (List<MarMaterialAdDTO> adDTOS : split) {
            CompletableFuture.runAsync(()->{
                for (MarMaterialAdDTO dto : adDTOS) {
                    requestToAdTkUpload(dto.getAccountInfo(), dto.getCenterInfo(), dto.getInfo());
                    marMaterialReleaseInfoService.updateById(dto.getInfo());
                }
            }, taskExecutor).exceptionally(e -> {
                log.error("当前进行广告流程发布异常,原因:{}", e);
                return null;
            });
        }
    }

    private void requestToAdTkUpload(AdAccountInfo info, MarMaterialCenterInfo centerInfo, MarMaterialReleaseInfo releaseInfo) {
        JSONObject object = new JSONObject();
        object.put("file_name", centerInfo.getMaterialName());
        object.put("advertiser_id", info.getProfileId());
        object.put("upload_type", "UPLOAD_BY_URL");
        object.put("video_url", centerInfo.getMaterialUrl());
        object.put("is_third_party",  Boolean.FALSE);
        object.put("flaw_detect",  Boolean.FALSE);
        object.put("auto_fix_enabled",  Boolean.FALSE);
        object.put("auto_bind_enabled",  Boolean.FALSE);
        String result = tiktokUserRequestUtils.sendAdRequest(TiktokUserUrlEnum.AD_UPLOAD_VIDEO.getUrl(), JSON.toJSONString(object), info.getAdAccessToken(), RequestEnum.POST.getCode());
        TkAdVideoUploadResponse response = JSONObject.parseObject(result, TkAdVideoUploadResponse.class);

        if (null == response || response.getCode() != 0) {
            log.error("当前发布TIKTOK视频到广告账户失败,当前素材中心ID:{}, 返回参数为:{}", releaseInfo.getMaterialNum(), response);
            releaseInfo.setFailMessage(null == response ? null : response.getMessage());
            releaseInfo.setStatus(MaterialGenStatusEnum.FAIL.getValue());
        } else {
            releaseInfo.setVideoId(response.getData().get(0).getVideo_id());
            releaseInfo.setStatus(MaterialGenStatusEnum.SUCCESS.getValue());
            //广告不写素材中心videoId
        }
    }

    /**
     * @description: 附件上传
     * @author: Moore
     * @date: 2025/2/11 16:40
     * @param
     * @param marMaterialUploadDTO
     * @param contentType
     * @param orgId
     * @return: void
    **/
    @Override
    public void uploadFile(List<MarMaterialUploadDTO> marMaterialUploadDTO, String contentType, String opType, Integer orgId) {
        if (CollectionUtils.isEmpty(marMaterialUploadDTO)) {
            throw new CommonException("附件不能为空");
        }
        if (StringUtils.isEmpty(opType)) {
            throw new CommonException("操作类型不能为空");
        }


        //去除标题中逗号
        marMaterialUploadDTO = marMaterialUploadDTO.stream().filter(item -> !StringUtils.isEmpty(item.getFileName())).map(
                item -> {
                    //去除标题后缀
                    int idxflag = item.getFileName().lastIndexOf('.');
                    if (idxflag != -1) {
                        item.setFileName(item.getFileName().substring(0, idxflag));
                    }
                    return item;
                }).collect(Collectors.toList());




        if (opType.equals("single")) {
            if (marMaterialUploadDTO.size() > 1) {
                throw new CommonException("单个上传数量超限");
            }

            MarMaterialUploadDTO marMaterialUploadDTOInfo = marMaterialUploadDTO.get(0);
            if (StringUtils.isEmpty(marMaterialUploadDTOInfo.getFileName()) || StringUtils.isEmpty(marMaterialUploadDTOInfo.getFileUrl())) {
                throw new CommonException("附件名或附件不能为空");
            }


            if (StringUtils.isNotEmpty(marMaterialUploadDTOInfo.getErpSku())) {
                List<Products> products = this.baseMapper.selectMaterialSkuList(Arrays.asList(marMaterialUploadDTOInfo.getErpSku()), orgId);
                if (CollectionUtils.isEmpty(products)) {
                    throw new CommonException("SKU未在系统中维护");
                }
                Products productsInfo = products.get(0);
                if (StringUtils.isNotEmpty(marMaterialUploadDTOInfo.getModel()) && !marMaterialUploadDTOInfo.getModel().equals(productsInfo.getProductMidModel())) {
                    throw new CommonException("型号未在系统中维护");
                }
            } else if (StringUtils.isNotEmpty(marMaterialUploadDTOInfo.getModel())) {
                List<Products> modelList = this.baseMapper.selectMaterialModelList(Arrays.asList(marMaterialUploadDTOInfo.getModel()), orgId);
                if (CollectionUtils.isEmpty(modelList)) {
                    throw new CommonException("型号未在系统中维护");
                }
            }


            MarMaterialCenterInfo marMaterialCenterInfo = new MarMaterialCenterInfo();
            marMaterialCenterInfo.setOrgId(orgId);
            marMaterialCenterInfo.setMaterialNum("IMAGE".equals(contentType)?MaterialIdInitUtils.createId("I"):MaterialIdInitUtils.createId("V"));
            marMaterialCenterInfo.setMaterialName(marMaterialUploadDTOInfo.getFileName());//素材名称
            marMaterialCenterInfo.setContentType(contentType);
            marMaterialCenterInfo.setMaterialUrl(marMaterialUploadDTOInfo.getFileUrl());
            marMaterialCenterInfo.setErpSku(marMaterialUploadDTOInfo.getErpSku());
            marMaterialCenterInfo.setModel(marMaterialUploadDTOInfo.getModel());
            marMaterialCenterInfo.setMaterialSource(MarMaterialSoureEnum.MANUAL.getValue()); //手动
            marMaterialCenterInfo.settingDefaultCreate();
            this.save(marMaterialCenterInfo);
            marMaterialOpLogService.saveOpLog(marMaterialCenterInfo.getMaterialNum(), "上传"+(contentType.equals("IMAGE")?"图片":"视频"));
        } else {
            if (marMaterialUploadDTO.stream().anyMatch(item -> !item.getFileName().contains("商品"))) {
                throw new CommonException("标题需按照指定格式设定");
            }

            //标题解析
            List<MarMaterialUploadDTO> processedList = marMaterialUploadDTO.stream().map(item -> {
                String processedName = item.getFileName().trim().replace("——", "—")
                        .replaceFirst(".*?商品-", "");
                item.setTransitionParm(processedName);  // 更新 username
                return item;
            }).collect(Collectors.toList());
            //转换后sku指
            List<String> skuOrModel = processedList.stream().map(MarMaterialUploadDTO::getTransitionParm).distinct().collect(Collectors.toList());
            log.info("批量上传附件解析后名称：{}", skuOrModel);
            //查询sku
            Map<String, Products> skuMap = this.baseMapper.selectMaterialSkuList(skuOrModel, orgId).stream().collect(Collectors.toMap(Products::getErpsku, Function.identity(), (V1, V2) -> V1));

            //查询model
            List<String> modelList = this.baseMapper.selectMaterialModelList(skuOrModel, orgId).stream().map(Products::getProductMidModel).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(skuMap.keySet()) && CollectionUtils.isEmpty(modelList)) {
                throw new CommonException("附件标题未匹配SKU或型号");
            }
            //判断是否即不存在sku又不存在model
            List<String> noSku = skuOrModel.stream().filter(item -> !skuMap.keySet().contains(item)).collect(Collectors.toList()); //sku中不存在
            List<String> noModel = noSku.stream().filter(item -> !modelList.contains(item)).collect(Collectors.toList()); //在判断model中是否存在

            if (CollectionUtils.isNotEmpty(noModel)) {
                throw new CommonException("SKU或型号不存在" + noSku.stream().distinct().collect(Collectors.joining(",")));
            }
            marMaterialUploadDTO.forEach(item -> {
                        MarMaterialCenterInfo marMaterialCenterInfo = new MarMaterialCenterInfo();
                        marMaterialCenterInfo.setOrgId(orgId);
                        marMaterialCenterInfo.setMaterialNum("IMAGE".equals(contentType)?MaterialIdInitUtils.createId("I"):MaterialIdInitUtils.createId("V"));
                        marMaterialCenterInfo.setMaterialName(item.getFileName()); //素材名称
                        marMaterialCenterInfo.setContentType(contentType);
                        marMaterialCenterInfo.setMaterialUrl(item.getFileUrl());
                        Products products = skuMap.get(item.getTransitionParm()); //获取文件名转换后的数值，进行SKU或者型号匹配
                        if (products != null) {
                            marMaterialCenterInfo.setErpSku(item.getTransitionParm());
                            marMaterialCenterInfo.setModel(products.getProductMidModel());
                        } else {
                            marMaterialCenterInfo.setModel(item.getTransitionParm());
                        }
                        marMaterialCenterInfo.setMaterialSource(MarMaterialSoureEnum.MANUAL.getValue()); //手动
                        marMaterialCenterInfo.settingDefaultCreate();
                        this.save(marMaterialCenterInfo);

                        //记录日志
                        marMaterialOpLogService.saveOpLog(marMaterialCenterInfo.getMaterialNum(), "上传"+(contentType.equals("IMAGE")?"图片":"视频"));
                    }
            );
        }



    }




    @Override
    public void handleCallRenderCallback(CreatifyRenderVideoCallbackDTO dto) {
        String status = dto.getStatus();

        CreatifyRenderVideoStatusEnum renderVideoStatus = CreatifyRenderVideoStatusEnum.byValue(status);
        if (Objects.isNull(renderVideoStatus)) {
            log.info("CreatifyAi渲染视频回调处理失败 - 状态异常:{}", JSON.toJSONString(dto));
            return;
        }

        MarMaterialMakeInfo info = materialMakeInfoService.lambdaQuery()
                .eq(MarMaterialMakeInfo::getTaskId, dto.getId())
                .one();
        if (Objects.isNull(info)) {
            log.info("CreatifyAi渲染视频回调处理失败 - 未获取到生成记录:{}", JSON.toJSONString(dto));
            return;
        }

        String erpSku = null;
        if (StrUtil.isNotBlank(info.getOrgMaterialNum())) {
            List<MarMaterialCenterInfo> centerInfos = this.lambdaQuery()
                    .in(MarMaterialCenterInfo::getMaterialNum, Arrays.asList(info.getOrgMaterialNum().split(",")))
                    .list();
            if (CollectionUtil.isNotEmpty(centerInfos)) {
                erpSku = centerInfos.stream().map(MarMaterialCenterInfo::getErpSku).distinct().collect(Collectors.joining(","));
            }
        }

        MarMaterialOpLog opLog = new MarMaterialOpLog();
        opLog.setOrgId(info.getOrgId());
        opLog.setMaterialNum(info.getGenId());
        opLog.settingDefaultCreate();
        opLog.settingDefaultUpdate();

        switch (renderVideoStatus) {
            case DONE:
                MarMaterialCenterInfo centerInfo = new MarMaterialCenterInfo();
                centerInfo.setOrgId(1000049);
                centerInfo.setMaterialNum(MaterialIdInitUtils.createId("VAI"));
                centerInfo.setMaterialName(info.getMaterialName());
                centerInfo.setContentType("VIDEO");
                centerInfo.setModel(info.getModel());
                centerInfo.setMaterialSource(MaterialWayEnum.CREATIFT_AI.getValue());
                centerInfo.setDownloadTotal(0);
                centerInfo.setAiTotal(0);
                centerInfo.setModel(info.getModel());
                centerInfo.setErpSku(erpSku);
                centerInfo.setCreatedAt(new Date());
                centerInfo.setCreatedBy(info.getCreatedBy());
                centerInfo.setCreatedName(info.getCreatedName());
                centerInfo.setUpdatedBy(info.getUpdatedBy());
                centerInfo.setUpdatedName(info.getUpdatedName());
                centerInfo.setUpdatedAt(centerInfo.getCreatedAt());
                // 设置原视频地址
                info.setOriginVideoUrl(dto.getVideo_output());
                info.setGenVideoUrl(dto.getVideo_output());
                // 下载视频
                try {
                    String uploadResult = FileUtil.downLoadAndUpload(dto.getVideo_output(), filePath + centerInfo.getMaterialNum() + ".mp4", "erp/mar/material", true);
                    centerInfo.setMaterialUrl(uploadResult);
                    info.setGenVideoUrl(uploadResult);
                    log.info("CreatifyAi渲染视频视频下载完毕 - {} - {}", dto.getId(), uploadResult);
                } catch (Exception e) {
                    try {
                        String uploadResult = FileUtil.downLoadAndUpload(dto.getVideo_output(), filePath + centerInfo.getMaterialNum() + ".mp4", "erp/mar/material", true);
                        centerInfo.setMaterialUrl(uploadResult);
                        info.setGenVideoUrl(uploadResult);
                    } catch (Exception ex) {
                        log.info("CreatifyAi渲染视频重试下载失败 - {} - {}", dto.getId(), dto.getVideo_output(), e);
                    }
                    centerInfo.setMaterialUrl(dto.getVideo_output());
                    log.info("CreatifyAi渲染视频视频下载失败 - {} - {}", dto.getId(), dto.getVideo_output(), e);
                }
                this.save(centerInfo);
                info.setGenStatus(MaterialGenStatusEnum.SUCCESS.getValue());
                info.setMaterialNum(centerInfo.getMaterialNum());
                materialMakeInfoService.updateById(info);
                log.info("任务更新完毕：{}", info.getGenId());
                //　操作日志
                opLog.setOperateContent("视频生成成功,生成结果:" + dto.getVideo_output());
                break;
            case FAILED:
                info.setGenStatus(MaterialGenStatusEnum.FAIL.getValue());
                materialMakeInfoService.updateById(info);
                opLog.setOperateContent("视频生成失败:" + dto.getFailed_reason());
                break;

            default:
                break;

        }


        marMaterialOpLogService.save(opLog);
    }

    @Override
    public int incrUsageByMaterialNumbers(List<String> materialNumbers) {
        return marMaterialCenterInfoMapper.incrUsageByMaterialNumbers(materialNumbers);
    }




    @Override
    public void initMateriarTaskCenter() {
        Long curosrId = 0L;
        int limit = 200;

        String redisKey = "material_center:init";

        List<CreatorVideoVO> videos;

        String cursor = stringRedisTemplate.opsForValue().get(redisKey);
        if (cursor != null && !cursor.isEmpty()) {
            curosrId = Long.parseLong(cursor);
        }

        log.info("初始化素材中心数据 - 起始ID:{}", curosrId);

        while (CollectionUtil.isNotEmpty((videos = tabcutCreatorVideoService.selectOwnVideoUrl(curosrId, limit)))) {
            curosrId = CollectionUtil.getLast(videos).getId();

            List<String> erpSkus = videos.stream().map(CreatorVideoVO::getErpSku).filter(StrUtil::isNotBlank)
                    .map(c -> Arrays.asList(c.split(",")))
                    .flatMap(List::stream)
                    .distinct().collect(Collectors.toList());

            List<Products> products = productsService.lambdaQuery()
                    .eq(Products::getOrgId, 1000049)
                    .in(Products::getErpsku, erpSkus)
                    .list();
            Map<String, String> modelMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(products)) {

                Map<String, String> productModelMap = products.stream()
                        .filter(c -> StrUtil.isNotBlank(c.getProductMidModel()))
                        .collect(Collectors.toMap(Products::getErpsku, Products::getProductMidModel, (v1, v2) -> v1));
                modelMap.putAll(productModelMap);
            }


            List<String> videoIds = videos.stream().map(CreatorVideoVO::getVideoId).collect(Collectors.toList());

            List<MarMaterialCenterInfo> dbCenterInfos = this.lambdaQuery()
                    .in(MarMaterialCenterInfo::getVideoId, videoIds)
                    .list();


            Map<String, MarMaterialCenterInfo> centerInfoMap = new HashMap<>();

            if (CollectionUtil.isNotEmpty(dbCenterInfos)) {
                Map<String, MarMaterialCenterInfo> map = dbCenterInfos.stream().collect(Collectors.toMap(MarMaterialCenterInfo::getVideoId, Function.identity(), (v1, v2) -> v1));
                centerInfoMap.putAll(map);
            }




            // 写入素材中心
            List<MarMaterialCenterInfo> centerInfos = videos.stream()
                    .filter(c -> !centerInfoMap.containsKey(c.getVideoId()))
                    .filter(c -> StrUtil.isNotBlank(c.getVideoUrl()) && c.getVideoUrl().contains("newVideo"))
                    .map(c -> {
                        MarMaterialCenterInfo centerInfo = new MarMaterialCenterInfo();
                        centerInfo.setOrgId(1000049);
                        centerInfo.setMaterialNum(MaterialIdInitUtils.createId("V"));
                        centerInfo.setErpSku(c.getErpSku());
                        centerInfo.setContentType("VIDEO");
                        centerInfo.setVideoId(c.getVideoId());
                        centerInfo.setMaterialUrl(c.getVideoUrl());
                        centerInfo.setMaterialName(c.getVideoDesc());
                        if (StrUtil.isNotBlank(c.getErpSku())) {
                            String model = Arrays.stream(c.getErpSku().split(","))
                                    .map(modelMap::get)
                                    .filter(StrUtil::isNotBlank)
                                    .findFirst().orElse(null);
                            centerInfo.setModel(model);
                        }
                        centerInfo.setMaterialSource(3);
                        centerInfo.setSaleAmount(c.getVideoGvm());
                        centerInfo.setQuantity(c.getSoldCount());
                        centerInfo.setCreatedAt(new Date());
                        centerInfo.setCreatedName("System");
                        centerInfo.setUpdatedAt(new Date());
                        centerInfo.setUpdatedName("System");
                        return centerInfo;
                    }).collect(Collectors.toList());

            this.saveBatch(centerInfos);

            // 标记CursorId
            stringRedisTemplate.opsForValue().set(redisKey, String.valueOf(curosrId));

        }
    }

    @Override
    public void updateMaterialName(UpdateMaterialNameDTO dto) {

        MaterialNameUpdateTypeEnum type = MaterialNameUpdateTypeEnum.typeOf(dto.getType());
        if (Objects.isNull(type)) {
            throw new CommonException("未知的修改类型");
        }
        switch (type) {
            case MATERIAL_MAKE:
                materialMakeInfoService.updateMaterialNameByGenId(dto.getMaterialNumber(), dto.getMaterialName());
                break;
            case MATERIAL_CENTER:
                updateMaterialCenterName(dto);
                break;
            default:
                break;
        }

    }

    private void updateMaterialCenterName(UpdateMaterialNameDTO dto) {
        MarMaterialCenterInfo centerInfo = this.lambdaQuery()
                .eq(MarMaterialCenterInfo::getMaterialNum, dto.getMaterialNumber())
                .one();
        if (Objects.isNull(centerInfo)) {
            throw new CommonException("当前素材不存在：" + dto.getMaterialNumber());
        }

        if (Objects.equals(centerInfo.getMaterialName(), dto.getMaterialName())) {
            return;
        }

        this.lambdaUpdate()
                .eq(MarMaterialCenterInfo::getMaterialNum, dto.getMaterialNumber())
                .set(MarMaterialCenterInfo::getMaterialName, dto.getMaterialName())
                .update();

        // 如果不是达人视频还需要更新制作数据
        if (Objects.equals(MaterialWayEnum.TIKTOK_AD_AI.getValue(), centerInfo.getMaterialSource())) {
            return;
        }
        materialMakeInfoService.lambdaUpdate()
                .eq(MarMaterialMakeInfo::getMaterialNum, dto.getMaterialNumber())
                .set(MarMaterialMakeInfo::getMaterialName, dto.getMaterialName())
                .update();
    }

}




