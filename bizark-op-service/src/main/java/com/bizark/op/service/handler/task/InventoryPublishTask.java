package com.bizark.op.service.handler.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.bizark.op.api.enm.log.LogEnums;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.inventory.ProductChannelsInventory;
import com.bizark.op.api.entity.op.inventory.enums.InventoryStatus;
import com.bizark.op.api.entity.op.inventory.enums.PublishTaskTypeEnum;
import com.bizark.op.api.entity.op.inventory.enums.TaskStatusEnum;
import com.bizark.op.api.entity.op.inventory.response.update.BaseInventoryResponse;
import com.bizark.op.api.entity.op.inventory.response.update.InventoryUpdateResult;
import com.bizark.op.api.entity.op.log.ErpOperateLog;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.entity.op.sale.vo.AddressEntityVO;
import com.bizark.op.api.entity.op.task.PublishTask;
import com.bizark.op.api.entity.op.task.PublishTaskInfo;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.inventory.ProductChannelsInventoryService;
import com.bizark.op.api.service.log.ErpOperateLogService;
import com.bizark.op.api.service.sale.ProductChannelsService;
import com.bizark.op.api.service.sale.ProductChannelsSlaveService;
import com.bizark.op.api.service.task.PublishTaskService;
import com.bizark.op.common.util.UserUtils;
import com.bizark.op.common.util.spring.SpringUtils;
import com.bizark.op.service.handler.inventory.AbstractInventoryHandler;
import com.bizark.op.service.handler.inventory.InventoryHandlerFactory;
import com.bizark.op.service.handler.inventory.eunm.InventoryHandleType;
import com.bizark.op.service.mapper.inventory.ProductChannelsInventoryMapper;
import com.bizark.op.service.mapper.sale.ProductChannelsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class InventoryPublishTask extends AbstractPublishTask{

    @Autowired
    private ProductChannelsService productChannelsService;

    @Autowired
    private ProductChannelsInventoryService productChannelsInventoryService;

    @Autowired
    private ProductChannelsSlaveService productChannelsSlaveService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private InventoryHandlerFactory inventoryHandlerFactory;

    @Autowired
    private ProductChannelsMapper productChannelsMapper;
    @Autowired
    private ErpOperateLogService erpOperateLogService;

    @Autowired
    private PublishTaskService publishTaskService;


    @Override
    PublishTaskTypeEnum getType() {
        return PublishTaskTypeEnum.INVENTORY_UPDATE;
    }


    @Override
    public void execute(PublishTaskInfo taskInfo) {


        PublishTask task = taskInfo.getPublishTask();

        log.info("库存任务开始执行 - 任务ID:{} - {} - {} - {} - {}", task.getId(), task.getSaleChannel(), task.getAccountFlag(), task.getSellerSku(), task.getContent());


        taskModify(task.getId(), TaskStatusEnum.PROCESSING, "任务开始执行");

        // 获取SKU映射数据
        ProductChannels channels = productChannelsService.getOne(task.getSellerSku(), task.getAccountFlag());
        if (Objects.isNull(channels)) {
            log.error("InventoryPublishTask not found channels from db... ");
            taskModify(task.getId(), TaskStatusEnum.FAIL, "未获取到SKU映射信息！");
            return;
        }
        Account account = accountService.lambdaQuery()
                .eq(Account::getFlag, channels.getAccountId())
                .eq(Account::getOrgId, channels.getOrgId())
                .one();
        if (Objects.isNull(account)) {
            log.error("account not found from db... ");
            taskModify(task.getId(), TaskStatusEnum.FAIL, "未获取到店铺信息！");
            return;
        }


        // 设置SKU映射数据
        taskInfo.setChannels(channels);
        taskInfo.setAccount(account);
        if (!checkTask(taskInfo)) {
            return;
        }
        // TODO 修改库存逻辑

        try {
            // 更新库存修改中状态
//            productChannelsMapper.updateInventoryStatusByIds(Collections.singletonList(channels.getId()), InventoryStatus.PROCESSING.getValue());

            AbstractInventoryHandler inventoryHandler = InventoryHandlerFactory.getHandler(InventoryHandleType.typeOf(taskInfo.getPublishTask().getSaleChannel()));
            InventoryUpdateResult execute = inventoryHandler.execute(taskInfo);

            log.info("库存任务执行结果 - 任务ID:{} - {}", task.getId(), JSON.toJSONString(execute));

            List<? extends BaseInventoryResponse> executeData = execute.getData();
            if (CollectionUtil.isNotEmpty(executeData)) {
                for (BaseInventoryResponse response : executeData) {
                    // 任务信息更新
                    if (response.getStatus() == TaskStatusEnum.SUCCESS) {
                        try {
                            inventoryModify(task);
                        } catch (Exception e) {
                            log.error("更新本地库存失败：任务ID：{}  异常信息：{}", task.getId(), e.getMessage());
                        }
                    }
                    taskModify(response.getTaskInfo().getPublishTask().getId(), response.getStatus(), response.getTransactionId(), response.getMessage());
//                channelModify(response.getChannels().getId(), InventoryStatus.NORMAL.getValue());
                }
            }
        } catch (Exception e) {
            log.info("库存任务执行异常 - 任务ID:{} - {} - {} - {} - {}", task.getId(), task.getSaleChannel(), task.getAccountFlag(), task.getSellerSku(), task.getContent(), e);

        }finally {
//            productChannelsMapper.updateInventoryStatusByIds(Collections.singletonList(channels.getId()), InventoryStatus.NORMAL.getValue());

            log.info("库存任务执行完毕 - 任务ID:{} - {} - {} - {} - {}", task.getId(), task.getSaleChannel(), task.getAccountFlag(), task.getSellerSku(), task.getContent());

        }


    }

    /**
     * 返回库存映射关系
     * @param accountFlag
     * @return
     */
    protected List<AddressEntityVO> getAddressWarehouse(String accountFlag) {
        ProductChannelsInventoryMapper mapper = SpringUtils.getBean(ProductChannelsInventoryMapper.class);
        return mapper.selectStockAccountAddressByAccountFlags(Lists.newArrayList(accountFlag));
    }


    protected String getWarehouseId(String accountFlag, String warehouse) {
        List<AddressEntityVO> addressWarehouse = getAddressWarehouse(accountFlag);
        if (CollectionUtil.isEmpty(addressWarehouse)) {
            return null;
        }
        String[] warehouseSplit = warehouse.split(",");
        for (String one : warehouseSplit) {
            AddressEntityVO address = addressWarehouse.stream()
                    .filter(a -> Objects.equals(a.getOrgWarehouseCode(), one))
                    .findAny().orElse(null);
            if (Objects.nonNull(address)) {
                return address.getAddressCode();
            }

        }
        return null;
    }



    /**
     * 更新库存信息
     * @param task
     */
    public void inventoryModify(PublishTask task) {
        Integer channelId = task.getChannelId();
        Account account = accountService.lambdaQuery().eq(Account::getFlag, task.getAccountFlag())
                .eq(Account::getOrgId, task.getOrgId())
                .one();
        if (Objects.isNull(account)) {
            log.error("库存修改失败，未获取到对应店铺信息：{}", task.getAccountFlag());
            return;
        }

        // 查询当前库存数据
        List<ProductChannelsInventory> dbInventories = productChannelsInventoryService.lambdaQuery()
                .eq(ProductChannelsInventory::getChannelId, channelId)
                .list();

        // 计算原始库存
        int sourceQty = CollectionUtil.isEmpty(dbInventories) ? 0 :
                dbInventories.stream().map(ProductChannelsInventory::getQuantity).filter(StrUtil::isNotBlank)
                        .map(Integer::parseInt).reduce(Integer::sum).get();


        if (Objects.nonNull(task.getStockAccountId())) {
            handleHasWarehouse(account, task, dbInventories);
        } else {
            handleNoWarehouse(account, task, dbInventories);
        }
        try {
            productChannelsSlaveService.handleSlaves(channelId);
        } catch (Exception e) {
            log.info("从表数据更新异常 - {}", channelId, e);
        }

        try {
            recordInventoryLog(task, channelId, dbInventories, sourceQty);
        } catch (Exception e) {
            log.info("库存变动记录异常 - {} - {}", task.getId(), channelId, e);
        }

    }

    private void recordInventoryLog(PublishTask task, Integer channelId, List<ProductChannelsInventory> dbInventories, int sourceQty) {
        // 重新查询库存数据
        List<ProductChannelsInventory> currentInventories = productChannelsInventoryService.lambdaQuery()
                .eq(ProductChannelsInventory::getChannelId, channelId)
                .list();

        int currentQty = CollectionUtil.isEmpty(currentInventories) ? 0 :
                currentInventories.stream().map(ProductChannelsInventory::getQuantity).filter(StrUtil::isNotBlank)
                        .map(Integer::parseInt).reduce(Integer::sum).get();

        if (Objects.equals(sourceQty, currentQty)) {
            return;
        }

        recordInventoryLog(task, channelId, sourceQty, currentQty);
    }

    private void handleNoWarehouse(Account account, PublishTask task, List<ProductChannelsInventory> dbInventories) {
        if (CollectionUtil.isEmpty(dbInventories)) {
            // 没有获取到库存信息，插入一条新的库存信息
            insertInventory(task, task.getChannelId(), account, null);
            return;
        }
        // 更新库存
        productChannelsInventoryService.lambdaUpdate()
                .eq(ProductChannelsInventory::getId, dbInventories.get(0).getId())
                .set(ProductChannelsInventory::getQuantity, task.getTaskTarget())
                .update();
    }

    private void handleHasWarehouse(Account account, PublishTask task,List<ProductChannelsInventory> dbInventories) {
        String addressCode = productChannelsInventoryService.selectAddressCodeById(task.getStockAccountId());
        if (StrUtil.isBlank(addressCode)) {
            return;
        }
        ProductChannelsInventory inventory = dbInventories.stream().filter(c -> Objects.equals(c.getWarehouseId(), addressCode))
                .findAny().orElse(null);
        if (Objects.isNull(inventory)) {
            insertInventory(task, task.getChannelId(), account, addressCode);
            return;
        }
        inventory.setQuantity(task.getTaskTarget());
        // 更新库存
        productChannelsInventoryService.lambdaUpdate()
                .eq(ProductChannelsInventory::getId, inventory.getId())
                .set(ProductChannelsInventory::getQuantity, task.getTaskTarget())
                .update();
    }

    private void recordInventoryLog(PublishTask task, Integer channelId, int sourceInventory, int targetInventory) {
        log.info("库存变动日志记录 - 任务ID:{} - SKU映射ID:{} - 原始库存:{} - 目标库存:{}", task.getId(), channelId, sourceInventory, targetInventory);
        if (Objects.equals(sourceInventory, targetInventory)) {
            return;
        }

        ErpOperateLog operateLog = new ErpOperateLog();
        operateLog.setOperateName("SKU映射");
        operateLog.setOperateUserId(Objects.nonNull(task.getCreatedBy()) ? task.getCreatedBy().longValue() : 0);
        operateLog.setOperateUserName(task.getCreatedName());
        operateLog.setLogType(1);
        operateLog.setOperateAt(LocalDateTime.now());
        operateLog.setBusinessId(channelId.longValue());
        operateLog.setFieldDesc("平台库存");
        operateLog.setOperateTable("dashboard.product_channels");
        operateLog.setOperateOldValue(String.valueOf(sourceInventory));
        operateLog.setOperateNewValue(String.valueOf(targetInventory));
        operateLog.setOperateTarget("inventory");
        operateLog.setOperateType(LogEnums.OperateTypeEnum.UPDATE.getValue());
        operateLog.setCreatedBy(task.getCreatedBy());
        operateLog.setCreatedName(task.getCreatedName());
        operateLog.setCreatedAt(task.getCreatedAt());
        erpOperateLogService.save(operateLog);
    }




    private ProductChannelsInventory insertInventory(PublishTask task, Integer channelId, Account account,String addressCode) {
        ProductChannelsInventory productChannelsInventory = new ProductChannelsInventory();
        productChannelsInventory.setChannelId(channelId);
        productChannelsInventory.setSellerSku(task.getSellerSku());
        productChannelsInventory.setQuantity(task.getTaskTarget());
        productChannelsInventory.setOrgId(task.getOrgId());
        productChannelsInventory.setWarehouseId(addressCode);
        productChannelsInventory.setUnit(task.getUnit());
        productChannelsInventory.setUpdateLastTime(new Date());
        productChannelsInventory.setAccountId(account.getId());
        productChannelsInventory.setAccountFlag(task.getAccountFlag());
        Integer userId = UserUtils.getCurrentUserId();
        String userName = UserUtils.getCurrentUserName();
        productChannelsInventory.setCreatedBy(userId);
        productChannelsInventory.setCreatedName(userName);
        productChannelsInventoryService.save(productChannelsInventory);
        return productChannelsInventory;
    }


    protected boolean checkTask(PublishTaskInfo taskInfo) {
        Account account = taskInfo.getAccount();
        ProductChannels channels = taskInfo.getChannels();
//        if (Objects.equals(channels.getIsPush(), "N")) {
//            taskModify(taskInfo.getPublishTask().getId(), TaskStatusEnum.FAIL,"SKU映射是否推送库存配置已关闭");
//            channelModify(channels.getId(), InventoryStatus.NORMAL.getValue());
//            return false;
//        }
        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            taskModify(taskInfo.getPublishTask().getId(), TaskStatusEnum.FAIL,"店铺 connectStr 为空，无法查看是否开启库存同步");
//            channelModify(channels.getId(), InventoryStatus.NORMAL.getValue());
            return false;
        }
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey("new_flag")) {
            taskModify(taskInfo.getPublishTask().getId(), TaskStatusEnum.FAIL, "店铺未配置库存自动同步");
//            channelModify(channels.getId(), InventoryStatus.NORMAL.getValue());
            return false;
        }
        JSONObject object = jsonObject.getObject("new_flag", JSONObject.class);
        if (!object.containsKey("stock")) {
            taskModify(taskInfo.getPublishTask().getId(), TaskStatusEnum.FAIL, "店铺未配置库存自动同步");
//            channelModify(channels.getId(), InventoryStatus.NORMAL.getValue());
            return false;
        }
        if (Objects.equals(object.getString("stock"), "N")) {
            taskModify(taskInfo.getPublishTask().getId(), TaskStatusEnum.FAIL, "店铺库存推送配置已关闭");
//            channelModify(channels.getId(), InventoryStatus.NORMAL.getValue());
            return false;
        }
        return true;
    }
}
