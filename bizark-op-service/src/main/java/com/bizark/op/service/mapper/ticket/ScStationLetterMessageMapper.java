package com.bizark.op.service.mapper.ticket;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bizark.op.api.entity.op.ticket.ScStationLetter;
import com.bizark.op.api.entity.op.ticket.ScStationLetterMessage;
import com.bizark.op.api.entity.op.ticket.TicketAttaches;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ScStationLetterMessageMapper  extends BaseMapper<ScStationLetterMessage>  {

    /**
     * 新增站内信消息
     *
     * @param scStationLetterMessage 站内信消息
     * @return 结果
     */
    int insertScStationLetterMessage(ScStationLetterMessage scStationLetterMessage);


    /**
     * 批量插入
     * @param messages
     * @return
     */
    int insertBatchScStationLetterMessage(@Param("list") List<ScStationLetterMessage> messages);

    /**
     * 根据会话id查询历史消息
     * @param
     * @return
     */
    List<ScStationLetterMessage> selectScStationLetterMessageList(ScStationLetterMessage message);


    List<ScStationLetterMessage> selectScStationLetterMessageListAsc(ScStationLetterMessage scStationLetterMessage);

    /**
     * 根据信息id来修改消息
     * @param scStationLetterMessage 消息
     * @return
     */
    int updateScStationLetterMessage(ScStationLetterMessage scStationLetterMessage);


    /**
     * @description: 关联会话主表 获取用户下所有会话内容
     * @author: Moore
     * @date: 2023/10/19 14:46
     * @param
     * @param scStationLetter
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.ScStationLetterMessage>
    **/
    List<ScStationLetterMessage> selectConversationListByConv(ScStationLetter scStationLetter);




    /**
     * @description: 根据msgId 获取会话
     * @author: Moore
     * @date: 2023/10/19 14:46
     * @param
     * @param msgId
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.ScStationLetterMessage>
     **/
    ScStationLetterMessage selectScStationLetterMessageByMsgId(String msgId);



    /**
     * @description:根据msg Id更新会话消息
     * @author: Moore
     * @date: 2023/10/20 1:07
     * @param
     * @param scStationLetterMessage
     * @return: java.lang.Integer
    **/
    Integer updateScStationLetterMessageByMsgId(@Param("scStation") ScStationLetterMessage scStationLetterMessage,@Param("updateMsgId") String msgId);


    /**
     * Description: 根据messageId查找站内信消息
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/5/17
     */
    ScStationLetterMessage selectScStationLetterMessageByMessageId(Long messageId);

    void updateErpContentById(@Param("id") Long msgId, @Param("erpContent") String erpContent);
}
