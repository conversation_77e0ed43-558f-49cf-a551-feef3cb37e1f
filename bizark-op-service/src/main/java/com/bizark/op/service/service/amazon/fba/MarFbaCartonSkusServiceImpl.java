package com.bizark.op.service.service.amazon.fba;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.op.api.entity.op.amazon.fba.DTO.FbaFnskuPrintDTO;
import com.bizark.op.api.entity.op.amazon.fba.MarFbaCartonSkus;
import com.bizark.op.api.entity.op.amazon.fba.VO.MarFnSkuPrintVO;
import com.bizark.op.api.service.amazon.fba.MarFbaCartonSkusService;
import com.bizark.op.service.mapper.amazon.fba.MarFbaCartonSkusMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class MarFbaCartonSkusServiceImpl extends ServiceImpl<MarFbaCartonSkusMapper, MarFbaCartonSkus>
implements MarFbaCartonSkusService {


    /**
     * fnskuD打印具体信息
     *
     * @param fbaIds
     * @return
     */
    @Override
    public List<MarFnSkuPrintVO.SellerSkuBean> selectFnSkuPrintInfoList(FbaFnskuPrintDTO fbaFnskuPrintDTO) {
        return this.baseMapper.selectFnSkuPrintInfoList(fbaFnskuPrintDTO);
    }
}




