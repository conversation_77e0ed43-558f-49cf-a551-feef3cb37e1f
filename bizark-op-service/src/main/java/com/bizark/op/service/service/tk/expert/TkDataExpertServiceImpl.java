package com.bizark.op.service.service.tk.expert;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.read.metadata.holder.ReadSheetHolder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.boss.api.entity.dashboard.CommonRegionEntity;
import com.bizark.boss.api.service.CommonRegionService;
import com.bizark.common.exception.CommonException;
import com.bizark.common.util.LocalDateTimeUtils;
import com.bizark.common.util.LocalDateUtils;
import com.bizark.op.api.cons.MQDefine;
import com.bizark.op.api.cons.RedisCons;
import com.bizark.op.api.enm.tabcut.TabcutUrl;
import com.bizark.op.api.enm.tk.TkDataExpertStateEnum;
import com.bizark.op.api.enm.tk.expert.SampleStatus;
import com.bizark.op.api.entity.op.excel.RowRequireCheck;
import com.bizark.op.api.entity.op.log.ErpOperateLog;
import com.bizark.op.api.entity.op.order.SaleOrders;
import com.bizark.op.api.entity.op.tabcut.TabcutCreatorCategory;
import com.bizark.op.api.entity.op.tabcut.TabcutCreatorDayKpi;
import com.bizark.op.api.entity.op.tabcut.TabcutCreatorVideo;
import com.bizark.op.api.entity.op.tabcut.TabcutCreatorVideoKpi;
import com.bizark.op.api.entity.op.tabcut.vo.TabcutCreatorVideoVO;
import com.bizark.op.api.entity.op.tabcut.vo.TabcutCsvData;
import com.bizark.op.api.entity.op.tabcut.vo.TabcutVideoKpiDTO;
import com.bizark.op.api.entity.op.ticket.ScTicket;
import com.bizark.op.api.entity.op.tk.expert.TkDataExpert;
import com.bizark.op.api.entity.op.tk.expert.TkDataExpertAddress;
import com.bizark.op.api.entity.op.tk.expert.dto.*;
import com.bizark.op.api.entity.op.tk.expert.tabcut.req.TabcutCreatorBasicRequest;
import com.bizark.op.api.entity.op.tk.expert.tabcut.req.TabcutCreatorTrendRequest;
import com.bizark.op.api.entity.op.tk.expert.tabcut.res.TabcutCreatorBasicResponse;
import com.bizark.op.api.entity.op.tk.expert.tabcut.res.TabcutCreatorTrendResponse;
import com.bizark.op.api.entity.op.tk.expert.vo.*;
import com.bizark.op.api.exception.ErpCommonException;
import com.bizark.op.api.service.customer.ICusCenterOffsiteService;
import com.bizark.op.api.service.log.ErpOperateLogService;
import com.bizark.op.api.service.sale.ProductChannelsService;
import com.bizark.op.api.service.sys.ISysDictTypeService;
import com.bizark.op.api.service.tabcut.*;
import com.bizark.op.api.service.ticket.IScTicketService;
import com.bizark.op.api.service.tk.expert.ITkDataExpertService;
import com.bizark.op.api.service.tk.expert.TkDataExpertAddressService;
import com.bizark.op.common.core.domain.BaseEntity;
import com.bizark.op.common.core.domain.sys.SysDictData;
import com.bizark.op.common.core.page.PageData;
import com.bizark.op.common.core.page.TableDataInfo;
import com.bizark.op.common.util.*;
import com.bizark.op.common.util.spring.SpringUtils;
import com.bizark.op.service.api.TabcutApi;
import com.bizark.op.service.handler.tk.ExpertExportResultHandler;
import com.bizark.op.service.mapper.tabcut.TabcutCreatorVideoMapper;
import com.bizark.op.service.mapper.ticket.SaleOrdersMapper;
import com.bizark.op.service.mapper.tk.expert.TkDataExpertMapper;
import com.bizark.op.service.util.ConvertUtils;
import com.bizark.op.service.util.EasyExcelUtils;
import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.bizark.usercenter.api.service.UserService;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bizark.op.api.cons.ScTicketConstant.TICKET_PRIORITY_MEDIUM;
import static com.bizark.op.api.cons.ScTicketConstant.TICKET_TYPE_OFFSITE_LETTER;
import static com.bizark.op.service.service.tabcut.TabcutCreatorVideoServiceImpl.TMP_VIDEO_PATH;
import static com.bizark.op.service.service.tabcut.TabcutCreatorVideoServiceImpl.downloadAndUploadUrl;

/**
 * @remarks:tiktok 达人服务
 * @Author: Ailill
 * @Date: 2023/10/31 11:01
 */
@Service
@Slf4j
public class TkDataExpertServiceImpl extends ServiceImpl<TkDataExpertMapper, TkDataExpert> implements ITkDataExpertService {

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private TkDataExpertMapper tkExpertMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private TabcutVideoCategoryService tabcutVideoCategoryService;

    @Autowired
    private UserService userService;

    @Autowired
    private TkDataExpertAddressService tkDataAddressService;

    @Autowired
    private ICusCenterOffsiteService cusCenterOffsiteService;

    @Autowired
    private ErpOperateLogService erpOperateLogService;

    @Autowired
    private TabcutCreatorVideoService tabcutCreatorVideoService;

    @Autowired
    private TabcutCreatorVideoMapper tabcutCreatorVideoMapper;

    @Autowired
    private TabcutCreatorVideoKpiService tabcutCreatorVideoKpiService;


    @Autowired
    private TabcutCreatorDayKpiService tabcutCreatorDayKpiService;

    @Autowired
    private TabcutCreatorCategoryService tabcutCreatorCategoryService;

    @Autowired
    private TabcutApi tabcutApi;

    @Autowired
    private SaleOrdersMapper saleOrdersMapper;

    @Autowired
    private ProductChannelsService productChannelsService;

    @Autowired
    private CommonRegionService commonRegionService;

    @Autowired
    private IScTicketService scTicketService;

    @Autowired
    private RedissonClient redissonClient;


    @Autowired
    private ISysDictTypeService sysDictTypeService;

    /**
     * 查询列表
     *
     * @param
     * @return
     */
    @Override
    public TableDataInfo selectTkDataExpertList(TkDataExpertDTO dto, HttpServletRequest request) {
        long start = System.currentTimeMillis();
        TableDataInfo tableDataInfo = new TableDataInfo();
        Integer page = Integer.parseInt(request.getParameter("page"));
        Integer rows = Integer.parseInt(request.getParameter("rows"));
        Integer offset = page * rows - rows;
        HttpRequest post = HttpUtil.createPost("http://172.28.193.50:5555/api/v1/TkExpertSearch?limit=" + rows + "&offset=" + offset);
        Long[] operatorIds = dto.getOperatorIds();
        if (operatorIds != null && operatorIds.length > 0) {
            dto.setOperators(Arrays.stream(operatorIds).map(String::valueOf).collect(Collectors.joining(",")));
            dto.setOperatorIds(null);
        }
        dto.querySetting();
        String body = JSON.toJSONString(dto);
        log.info("参数: \n {}", body);
        post.body(body);
        HttpResponse httpResponse = post.execute();

        if (!httpResponse.isOk()) {
            throw new CommonException(httpResponse.body());
        }
        TkExpertResponseListVO vo = JSON.parseObject(httpResponse.body(), TkExpertResponseListVO.class);
        PageData<TkDataExpertListVO> data = new PageData<>();
        data.setSize(vo.getSize());
        data.setFirst(vo.isFirst());
        data.setLast(vo.isLast());
        data.setTotalElements(vo.getTotalElements());
        data.setTotalPages(vo.getTotalPages());
        tableDataInfo.setData(data);
        tableDataInfo.setCode(200);
        tableDataInfo.setMessage("查询成功");
        tableDataInfo.setStatusCode(200);

        List<TkDataExpertListVO> results = vo.getResults();
        if (CollectionUtil.isEmpty(results)) {
            data.setContent(new ArrayList<>());
            return tableDataInfo;
        }

//        List<TkDataExpertListVO> experts = tkExpertMapper.selectExpertList(dto);
        tabcutCreatorCategoryService.categorySetting(results);
        long end = System.currentTimeMillis();
        System.out.println("执行耗时" + (end - start));
        data.setContent(results);
        return tableDataInfo;

    }


    /**
     * 添加
     *
     * @param tkDataExpert
     * @param entity
     * @return
     */
    @Override
    public boolean add(TkDataExpert tkDataExpert, UserEntity entity) {

        AssertUtil.isFalse(StringUtil.isEmpty(tkDataExpert.getCreator()), "创作者不能为空");
        String key = tkDataExpert.getCreatorId();
        RLock rLock =redissonClient.getLock(key);
        try {
            if (rLock.tryLock(0, 1, TimeUnit.MINUTES)) {
                Date nowDate = DateUtils.getNowDate();
                tkDataExpert.setUpdatedBy(null == entity ? -1 : (entity.getId() != null ? entity.getId() : -1));
                tkDataExpert.setUpdatedName(null == entity ? "System" : (StringUtil.isNotEmpty(entity.getName()) ? entity.getName() : "System"));
                tkDataExpert.setUpdatedAt(nowDate);
                tkDataExpert.setContextId(tkDataExpert.getContextId() != null ? tkDataExpert.getContextId() : 1000049);
                tkDataExpert.setState(tkDataExpert.getOperatorId() != null ? TkDataExpertStateEnum.COMPLETED.getLabel() : TkDataExpertStateEnum.HANDLING.getLabel());
                TkDataExpert one = this.getOne(Wrappers.<TkDataExpert>lambdaQuery().eq(TkDataExpert::getCreatorId, tkDataExpert.getCreatorId()).last(" limit 1"));
                if (one == null) {
                    tkDataExpert.setCreatedBy(null == entity ? -1 : (entity.getId() != null ? entity.getId() : -1));
                    tkDataExpert.setCreatedName(null == entity ? "System" : (StringUtil.isNotEmpty(entity.getName()) ? entity.getName() : "System"));
                    tkDataExpert.setCreatedAt(nowDate);
                    return this.save(tkDataExpert);
                } else {
                    tkDataExpert.setId(one.getId());
                    return this.updateById(tkDataExpert);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ErpCommonException("添加达人信息出错" + e.getMessage());
        } finally {
            rLock.unlock();
        }
        return true;
    }


    /**
     * 修改
     *
     * @param tkDataExpertList
     * @return
     */
    @Override
    @Transactional
    public boolean updateTkDataExpert(List<TkDataExpert> tkDataExpertList) {
        return this.updateBatchById(tkDataExpertList);
    }

    /**
     * 导入
     *
     * @param file
     * @param contextId
     * @param entity
     * @return
     */
    @Override
    @Transactional
    public ApiResponseResult importExcel(MultipartFile file, Integer contextId, UserEntity entity) {
        Date nowDate = DateUtils.getNowDate();
        if (file == null) {
            return ApiResponseResult.buildFailureResult("请上传文件");
        } else {
            String originalFilename = file.getOriginalFilename();
            if (StringUtil.isEmpty(originalFilename)) {
                return ApiResponseResult.buildFailureResult("文件名为空");
            } else {
                String suffix = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
                if (!suffix.equalsIgnoreCase("xlsx") && !suffix.equalsIgnoreCase("xls")) {
                    return ApiResponseResult.buildFailureResult("请选择excel文件");
                }
            }
        }
        // 获取导入实体数据
        List<ExpertImportDTO> imports = readFileSettingRowIndex(file, contextId);
        // 检查必填项目
        RowRequireCheck check = EasyExcelUtils.checkImportRequire(imports);
        if (!check.isEmpty()) {
            String errorMsg = check.getIndex().stream()
                    .map(c -> String.format("第 %s 行，%s必须填写！", imports.get(c).getRowIndex(), check.getTarget()))
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }
        // 检查邮件或微信必填一个
        List<ExpertImportDTO> wechatAndEmailFilter = imports.stream()
                .filter(c -> StrUtil.isBlank(c.getEmail()) && StrUtil.isBlank(c.getWechat()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(wechatAndEmailFilter)) {
            String errorMsg = wechatAndEmailFilter.stream()
                    .map(c -> "第 " + c.getRowIndex() + " 行，邮箱和WechatApp 至少填写一项")
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }
        checkOwnSelector(imports);
        // 设置国家编码信息
        countryCodeSetting(imports);
        // 字段数据设置
        dictDataConvert(imports);
        // 检查重复信息
        checkRepeat(imports);
        // 检查是否存在信息
        checkExist(imports);
        // 检查运营信息
        checkAndSettingOperatorId(contextId, imports);
        // 写入数据库
        Integer currentUserId = UserUtils.getCurrentUserId();
        String currentUserName = UserUtils.getCurrentUserName();
        List<TkDataExpert> experts = imports.stream()
                .map(c -> {
                    TkDataExpert expert = BeanCopyUtils.copyBean(c, TkDataExpert.class);
                    TkDataExpertAddress address = BeanCopyUtils.copyBean(c, TkDataExpertAddress.class);
                    address.setAddress(c.getAddressFirst());
                    address.setAddressSecond(c.getAddressSecond());
                    expert.setRegion(address.getCountryCode());
                    expert.setAddress(address);
                    expert.setUpdatedAt(new Date());
                    expert.setUpdatedBy(currentUserId);
                    expert.setUpdatedName(currentUserName);
                    return expert;
                }).collect(Collectors.toList());
        saveOrUpdateExpert(experts);
        return ApiResponseResult.buildSuccessResult("导入成功");
    }

    private void dictDataConvert(List<ExpertImportDTO> imports) {

        List<SysDictData> dictData = sysDictTypeService.selectDictDataByType("tk_creator_source");
        if (CollectionUtil.isEmpty(dictData)) {
            throw new CommonException("达人来源数据转换失败,导入失败");
        }
        Map<String, String> map = dictData.stream()
                .collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue, (v1, v2) -> v1));


        List<ExpertImportDTO> dtos = imports.stream()
                .filter(c -> !map.containsKey(c.getSourceDict()))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(dtos)) {
            String errorMsg = dtos.stream()
                            .map(c -> "第 " + c.getRowIndex() + " 行,达人来源无效")
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }

        for (ExpertImportDTO dto : imports) {
            dto.setSource(Integer.parseInt(map.get(dto.getSourceDict())));
        }

    }

    private void checkOwnSelector(List<ExpertImportDTO> imports) {
        List<ExpertImportDTO> dtos = imports.stream()
                .peek(d -> d.setOwnAccount(Objects.equals(d.getIsOwnAccount(), "是") ? 1 : 0))
                .filter(c -> !Arrays.asList("是", "否").contains(c.getIsOwnAccount()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(dtos)) {
            String errorMsg = dtos.stream()
                    .map(c -> "第 " + c.getRowIndex() + " 行，是否自有账号无效")
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }
    }

    private void countryCodeSetting(List<ExpertImportDTO> imports) {

        List<CommonRegionEntity> allCountry = commonRegionService.getAllList();
        if (CollectionUtil.isEmpty(allCountry)) {
            throw new CommonException("未获取到国家信息，导入失败");
        }
        Map<String, CommonRegionEntity> entityMap = allCountry.stream()
                .collect(Collectors.toMap(CommonRegionEntity::getNameEn, Function.identity(), (a, b) -> a));

        for (ExpertImportDTO dto : imports) {
            if (entityMap.containsKey(dto.getCountryEn())) {
                CommonRegionEntity entity = entityMap.get(dto.getCountryEn());
                dto.setCountryCode(entity.getCode());
            }
            if (entityMap.containsKey(dto.getRegionEn())) {
                CommonRegionEntity entity = entityMap.get(dto.getRegionEn());
                dto.setRegion(entity.getCode());
            }
        }

        List<ExpertImportDTO> countryError = imports.stream()
                .filter(c -> !entityMap.containsKey(c.getCountryEn()))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(countryError)) {
            String errorMsg = countryError.stream()
                    .map(c -> "第 " + c.getRowIndex() + " 行，国家信息无效")
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }
        List<ExpertImportDTO> regionError = imports.stream()
                .filter(c -> !entityMap.containsKey(c.getRegionEn()))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(regionError)) {
            String errorMsg = regionError.stream()
                    .map(c -> "第 " + c.getRowIndex() + " 行 ,州/地区 信息无效")
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }


        List<ExpertImportDTO> errors = new ArrayList<>();
        for (ExpertImportDTO dto : imports) {
            CommonRegionEntity country = entityMap.get(dto.getCountryEn());
            CommonRegionEntity region = entityMap.get(dto.getRegionEn());
            if (!country.getId().equals(region.getParentId())) {
                errors.add(dto);
            }
        }
        if (CollectionUtil.isNotEmpty(errors)) {
            String errorMsg = errors.stream()
                    .map(c -> "第 " + c.getRowIndex() + " 行 ,国家 与 州/地区 不匹配")
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }

    }

    private void checkExist(List<ExpertImportDTO> imports) {

        List<String> creators = imports.stream().map(ExpertImportDTO::getCreator).collect(Collectors.toList());
        List<String> creatorIds = imports.stream().map(ExpertImportDTO::getCreatorId).collect(Collectors.toList());
        List<String> creatorCids = imports.stream().map(ExpertImportDTO::getCreatorCid).collect(Collectors.toList());

        List<TkDataExpert> experts = this.lambdaQuery()
                .in(TkDataExpert::getCreator, creators)
                .or()
                .in(TkDataExpert::getCreatorCid, creatorCids)
                .or()
                .in(TkDataExpert::getCreatorId, creatorIds)
                .select(TkDataExpert::getId, TkDataExpert::getCreatorCid, TkDataExpert::getCreatorId)
                .list();

        if (CollectionUtil.isEmpty(experts)) {
            return;
        }

        // 检查重复信息,数据库CreatorId唯一
        Map<String, TkDataExpert> creatorIdMap = experts.stream()
                .collect(Collectors.toMap(TkDataExpert::getCreatorId, Function.identity(), (a, b) -> a));
        Map<String, TkDataExpert> creatorCidMap = experts.stream()
                .collect(Collectors.toMap(TkDataExpert::getCreatorCid, Function.identity(), (a, b) -> a));


        List<ExpertImportDTO> creatorIdNotExistAndCreatorCidExist = new ArrayList<>();
        List<ExpertImportDTO> creatorCidNotEquals = new ArrayList<>();


        for (ExpertImportDTO dto : imports) {
            TkDataExpert expert = creatorIdMap.get(dto.getCreatorId());
            boolean hasCreatorId = creatorIdMap.containsKey(dto.getCreatorId());
            boolean hasCreatorCId = creatorCidMap.containsKey(dto.getCreatorCid());
            if (hasCreatorId && !Objects.equals(expert.getCreatorCid(), dto.getCreatorCid())) {
                creatorCidNotEquals.add(dto);
            }
            if (!hasCreatorId && hasCreatorCId) {
                creatorIdNotExistAndCreatorCidExist.add(dto);
            }
        }
        if (CollectionUtil.isNotEmpty(creatorCidNotEquals)) {
            String errorMsg = creatorCidNotEquals.stream()
                    .map(c -> String.format("第 %s 行，Creator ID对应的达人cid与系统中的不一致", c.getRowIndex()))
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }
        if (CollectionUtil.isNotEmpty(creatorIdNotExistAndCreatorCidExist)) {
            String errorMsg = creatorIdNotExistAndCreatorCidExist.stream()
                    .map(c -> String.format("第 %s 行，达人cid对应的Creator ID与系统中的不一致", c.getRowIndex()))
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }


    }


    private void checkRepeat(List<ExpertImportDTO> imports) {
        List<List<ExpertImportDTO>> creatorRepeat = imports.stream()
                .collect(Collectors.groupingBy(ExpertImportDTO::getCreator))
                .entrySet()
                .stream()
                .filter(c -> c.getValue().size() > 1)
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(creatorRepeat)) {
            List<String> errorCreator = creatorRepeat.stream()
                    .flatMap(List::stream)
                    .map(ExpertImportDTO::getCreator)
                    .distinct().collect(Collectors.toList());
            throw new CommonException("表格内用户名 " + errorCreator + "重复");
        }

        List<List<ExpertImportDTO>> creatorIdRepeat = imports.stream()
                .collect(Collectors.groupingBy(ExpertImportDTO::getCreatorId))
                .entrySet()
                .stream()
                .filter(c -> c.getValue().size() > 1)
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(creatorIdRepeat)) {
            List<String> errorCreatorId = creatorRepeat.stream()
                    .flatMap(List::stream)
                    .map(ExpertImportDTO::getCreatorId)
                    .distinct().collect(Collectors.toList());
            throw new CommonException("表格内Creator ID " + errorCreatorId + "重复");
        }


        List<List<ExpertImportDTO>> creatorCidRepeat = imports.stream()
                .collect(Collectors.groupingBy(ExpertImportDTO::getCreatorCid))
                .entrySet()
                .stream()
                .filter(c -> c.getValue().size() > 1)
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(creatorCidRepeat)) {
            List<String> errorCreatorId = creatorRepeat.stream()
                    .flatMap(List::stream)
                    .map(ExpertImportDTO::getCreatorCid)
                    .distinct().collect(Collectors.toList());
            throw new CommonException("表格内Creator Cid " + errorCreatorId + "重复");
        }

    }

    private void checkAndSettingOperatorId(Integer contextId, List<ExpertImportDTO> imports) {
        List<String> userNames = imports.stream().map(ExpertImportDTO::getOperator)
                .distinct()
                .collect(Collectors.toList());
        List<UserEntity> users = userService.queryByNames(userNames, contextId);
        AssertUtil.isFalse(CollectionUtil.isEmpty(users), "未获取到运营信息，请检查运营填写是否正确");

        Map<String, UserEntity> userEntityMap = users.stream()
                .collect(Collectors.toMap(UserEntity::getName, Function.identity(), (a, b) -> a));

        List<ExpertImportDTO> filterUsers = imports.stream()
                .filter(c -> !userEntityMap.containsKey(c.getOperator()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(filterUsers)) {
            String errorMsg = filterUsers.stream().map(c -> String.format("第 %s 行运营 [%s] 填写错误，请核对后重新录入", c.getRowIndex(), c.getOperator()))
                    .collect(Collectors.joining("\n"));
            throw new CommonException(errorMsg);
        }
        for (ExpertImportDTO dto : imports) {
            UserEntity user = userEntityMap.get(dto.getOperator());
            dto.setOperatorId(user.getId().longValue());
        }

    }


    @SneakyThrows
    private <T> List<ExpertImportDTO> readFileSettingRowIndex(MultipartFile file, Integer contextId) {
        List<ExpertImportDTO> imports = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), ExpertImportDTO.class, new AnalysisEventListener<ExpertImportDTO>() {
            @Override
            public void invoke(ExpertImportDTO dto, AnalysisContext analysisContext) {
                // 只读取第一个Sheet页
                ReadSheetHolder readSheetHolder = analysisContext.readSheetHolder();
                ReadSheet readSheet = readSheetHolder.getReadSheet();
                Integer sheetNo = readSheet.getSheetNo();
                if (sheetNo != 0) {
                    return;
                }
                dto.setAllowUpdate(1);
                dto.setContextId(contextId);
                dto.setRowIndex(readSheetHolder.getRowIndex() + 1);
                imports.add(dto);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                System.out.println(123);
            }
        }).doReadAll();
        return imports;
    }

    /**
     * @param contextId     组织id
     * @param entity        用户信息(可为空)
     * @param nowDate       日期
     * @param tkDataExperts 达人信息
     */
    @Transactional
    @Override
    public void batchImport(Integer contextId, UserEntity entity, Date nowDate, List<TkDataExpert> tkDataExperts) {
        List<String> creatorList = tkDataExperts.stream().map(TkDataExpert::getCreator).collect(Collectors.toList());
        long count = creatorList.stream().distinct().count();
        if (count != creatorList.size()) {
            throw new ErpCommonException("表格内达人名称重复");
        }
        List<String> operatorList = tkDataExperts.stream().map(TkDataExpert::getOperator).distinct().collect(Collectors.toList());
        List<UserEntity> userEntities = SpringUtils.getBean(UserService.class).queryByNames(operatorList, contextId);
        if (CollectionUtil.isEmpty(userEntities) || operatorList.size() != userEntities.size()) {
            throw new ErpCommonException("表格含有不存在的运营");
        }
        //系统已有的达人
        List<TkDataExpert> list = this.list(Wrappers.<TkDataExpert>lambdaQuery().in(TkDataExpert::getCreator, creatorList));
        //新增
        if (CollectionUtil.isEmpty(list)) {
            tkDataExperts.stream().forEach(s -> setAttr(contextId, entity, nowDate, userEntities, s, true));
            this.saveBatch(tkDataExperts);
        } else {
            //更新
            List<String> needUpdate = list.stream().map(TkDataExpert::getCreator).collect(Collectors.toList());
            List<TkDataExpert> needUpdateList = tkDataExperts.stream().filter(t -> needUpdate.contains(t.getCreator())).map(s -> {
                List<Long> idList = list.stream().filter(r -> s.getCreator().equalsIgnoreCase(r.getCreator())).map(TkDataExpert::getId).collect(Collectors.toList());
                AssertUtil.isFalse(CollectionUtil.isEmpty(idList), "导入失败");
                setAttr(contextId, entity, nowDate, userEntities, s, false);
                s.setId(idList.get(0));
                return s;
            }).collect(Collectors.toList());
            this.updateBatchById(needUpdateList);
            //新增
            List<TkDataExpert> needAddList = tkDataExperts.stream().filter(t -> !needUpdate.contains(t.getCreator())).map(s -> {
                setAttr(contextId, entity, nowDate, userEntities, s, true);
                return s;
            }).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(needAddList)) {
                this.saveBatch(needAddList);
            }
        }
    }


    @Override
    @Transactional
    public void saveOrUpdateExpert(List<TkDataExpert> experts) {
        List<String> creatorIds = experts.stream().map(TkDataExpert::getCreatorId).collect(Collectors.toList());
        List<String> creators = experts.stream().map(TkDataExpert::getCreator).collect(Collectors.toList());
        List<String> creatorCids = experts.stream().map(TkDataExpert::getCreatorCid).collect(Collectors.toList());


        List<TkDataExpert> tkDataExperts = this.lambdaQuery()
                .in(TkDataExpert::getCreatorId, creatorIds)
                .or()
                .in(TkDataExpert::getCreator, creators)
                .or()
                .in(TkDataExpert::getCreatorCid, creatorCids)
                .list();

        List<TkDataExpert> dataExperts = this.lambdaQuery()
                .in(TkDataExpert::getCreatorId, creatorIds)
                .list();
        if (CollectionUtil.isEmpty(dataExperts)) {
            this.saveBatch(experts);
            saveAddress(experts);
            erpOperateLogService.recordInsert(experts);
            return;
        }

        Map<String, TkDataExpert> expertMap = dataExperts.stream().collect(Collectors.toMap(c -> c.getCreatorId() + c.getCreatorCid(), Function.identity(), (a, b) -> a));

        List<TkDataExpert> updates = new ArrayList<>();
        List<TkDataExpert> inserts = new ArrayList<>();

        for (TkDataExpert expert : experts) {
            String key = expert.getCreatorId() + expert.getCreatorCid();
            if (expertMap.containsKey(key)) {
                expert.setId(expertMap.get(key).getId());
                updates.add(expert);
                continue;
            }
            inserts.add(expert);
        }


        if (CollectionUtil.isNotEmpty(inserts)) {
            this.saveBatch(inserts);
            saveAddress(inserts);
            recordExpertInsertLog(BeanCopyUtils.copyBeanList(inserts, TkDataExpertDTO.class));
            threadPoolTaskExecutor.execute(() -> erpOperateLogService.recordInsert(inserts));
            return;
        }
        if (CollectionUtil.isNotEmpty(updates)) {
            this.updateBatchById(updates, 500);
            // 记录日志
            threadPoolTaskExecutor.execute(() -> {
                for (TkDataExpert update : updates) {
                    recordExpertUpdateLog(expertMap.get(update.getCreatorId() + update.getCreatorCid()), update);
                }
            });
        }
        // 保存地址信息
        saveAddress(experts);

    }

    private void saveAddress(List<TkDataExpert> experts) {
        List<Long> expertIds = new ArrayList<>();
        for (TkDataExpert expert : experts) {
            expertIds.add(expert.getId());
            if (Objects.nonNull(expert.getAddress())) {
                expert.getAddress().setExpertId(expert.getId());
            }
        }

        List<TkDataExpertAddress> addresses = experts.stream().peek(expert -> {
            expertIds.add(expert.getId());
            if (Objects.nonNull(expert.getAddress())) {
                expert.getAddress().setExpertId(expert.getId());
            }
        }).map(TkDataExpert::getAddress).filter(address -> Objects.nonNull(address)).collect(Collectors.toList());


        tkDataAddressService.lambdaUpdate()
                .in(TkDataExpertAddress::getExpertId, expertIds)
                .remove();

        if (CollectionUtil.isNotEmpty(addresses)) {
            tkDataAddressService.saveBatch(addresses,500);
        }
    }


    @Override
    @Transactional
    public void saveOrUpdateExpert(TkDataExpertDTO dto) {

        TkDataExpert expert = BeanCopyUtils.copyBean(dto, TkDataExpert.class);

        // 检查达人用户名、CreatorId、Creator-cid 是否重复
//        checkUniqueness(expert);
        expert.setUpdatedName(UserUtils.getCurrentUserName());
        expert.setUpdatedBy(UserUtils.getCurrentUserId());
        expert.setUpdatedAt(new Date());

        UserEntity user = userService.getById(expert.getOperatorId().intValue());
        AssertUtil.isFalse(Objects.isNull(user), "未获取到运营信息");
        expert.setOperator(user.getName());

        // 查询数据，用于重复验证
        List<TkDataExpert> experts = this.lambdaQuery()
                .eq(TkDataExpert::getCreator, expert.getCreator())
                .or()
                .eq(TkDataExpert::getCreatorCid, expert.getCreatorCid())
                .or()
                .eq(TkDataExpert::getCreatorId, expert.getCreatorId())
                .list();

        Map<String, TkDataExpert> creatorMap = new HashMap<>();
        Map<String, TkDataExpert> creatorIdMap = new HashMap<>();
        Map<String, TkDataExpert> creatorCidMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(experts)) {
            creatorMap = experts.stream().filter(c -> StrUtil.isNotBlank(c.getOperator())).collect(Collectors.toMap(TkDataExpert::getCreator, Function.identity(), (a, b) -> a));
            creatorIdMap = experts.stream().filter(c -> StrUtil.isNotBlank(c.getCreatorId())).collect(Collectors.toMap(TkDataExpert::getCreatorId, Function.identity(), (a, b) -> a));
            creatorCidMap = experts.stream().filter(c -> StrUtil.isNotBlank(c.getCreatorCid())).collect(Collectors.toMap(TkDataExpert::getCreatorCid, Function.identity(), (a, b) -> a));
        }
        expert.setRegion(expert.getAddress().getCountryCode());
        if (Objects.isNull(expert.getId())) {

            AssertUtil.isFalse(creatorMap.containsKey(expert.getCreator()), "达人用户名不可重复");
            AssertUtil.isFalse(creatorIdMap.containsKey(expert.getCreatorId()), "Creator ID 不可重复");
            AssertUtil.isFalse(creatorCidMap.containsKey(expert.getCreatorCid()), "达人Cid不可重复");
            expert.setAllowUpdate(1);
            this.save(expert);
            expert.getAddress().setExpertId(expert.getId());
            tkDataAddressService.save(expert.getAddress());
            // 记录新增日志
            dto.setId(expert.getId());
            recordExpertInsertLog(dto);
            return;
        }
        TkDataExpert source = this.getById(expert.getId());
        AssertUtil.isFalse(Objects.isNull(source), "未获取到达人信息");


        TkDataExpert creatorExpert = creatorMap.get(expert.getCreator());
        TkDataExpert creatorIdExpert = creatorMap.get(expert.getCreatorId());
        TkDataExpert creatorCidExpert = creatorMap.get(expert.getCreatorCid());
        if (Objects.nonNull(creatorExpert) && !Objects.equals(creatorExpert.getId(), source.getId())) {
            throw new CommonException("达人用户名不可重复");
        }
        if (Objects.nonNull(creatorIdExpert) && !Objects.equals(creatorIdExpert.getId(), source.getId())) {
            throw new CommonException("Creatod ID不可重复");
        }
        if (Objects.nonNull(creatorCidExpert) && !Objects.equals(creatorCidExpert.getId(), source.getId())) {
            throw new CommonException("达人Cid不可重复");
        }
        TkDataExpert dbExpert = this.getById(expert.getId());
        TkDataExpertAddress address = tkDataAddressService.lambdaQuery().eq(TkDataExpertAddress::getExpertId, expert.getId())
                .one();
        dbExpert.setAddress(address);
        // 直接删除地址信息
        tkDataAddressService.lambdaUpdate()
                .eq(TkDataExpertAddress::getExpertId, expert.getId())
                .remove();
        TkDataExpert bean = BeanCopyUtils.copyBean(expert, TkDataExpert.class);
        this.updateById(bean);
        expert.getAddress().setExpertId(expert.getId());
        tkDataAddressService.save(expert.getAddress());
        // 记录操作日志
//        erpOperateLogService.logRecord(source, expert, null, false, false, null);
        try {
            recordExpertUpdateLog(dbExpert, bean);
        } catch (Exception e) {
            log.error("达人日志记录错误：{} ", e.getMessage(), e);
        }

    }

    @SneakyThrows
    private void recordExpertUpdateLog(TkDataExpert dbExpert, TkDataExpert expert) {
        Integer userId = UserUtils.getCurrentUserId(0);
        String userName = UserUtils.getCurrentUserName("System");
        LocalDateTime now = LocalDateTime.now(ZoneId.of("America/Los_Angeles"));
        Date date = LocalDateUtils.localDateTimeToDate(now);
        List<Field> logFields = erpOperateLogService.getLogFields(dbExpert, expert, new ArrayList<>(), false, false);
        if (CollectionUtil.isNotEmpty(logFields)) {
            List<ErpOperateLog> logs = new ArrayList<>();
            for (Field field : logFields) {
                if (field.getName().equals("address")) {
                    continue;
                }
                Schema schema = field.getAnnotation(Schema.class);
                String fileDesc = schema == null ? field.getName() : schema.description();
                ErpOperateLog operateLog = new ErpOperateLog();
                field.setAccessible(true);
                Object before = field.get(dbExpert);
                Object after = field.get(expert);
                operateLog.setOperateUserId(userId.longValue());
                operateLog.setOperateUserName(userName);
                operateLog.setOperateAt(now);
                operateLog.setFieldDesc(fileDesc);
                operateLog.setOperateName("TIKTOK达人");
                operateLog.setCreatedAt(date);
                operateLog.setCreatedBy(userId);
                operateLog.setCreatedName(userName);
                operateLog.setUpdatedAt(date);
                operateLog.setUpdatedName(userName);
                operateLog.setUpdatedBy(userId);
                operateLog.setLogType(100);
                operateLog.setOperateType(1);
                operateLog.setOperateTable("tk_data_expert");
                operateLog.setBusinessId(expert.getId());
                operateLog.setOperateNewValue(
                        String.format(
                                " %s 字段由 %s 变为 %s",
                                fileDesc, before == null ? "空" : String.valueOf(before),
                                after == null ? "空" : String.valueOf(after))
                );
                logs.add(operateLog);
            }
            erpOperateLogService.saveBatch(logs, 500);
        }

        TkDataExpertAddress beforeAddress = dbExpert.getAddress() == null ? new TkDataExpertAddress() : dbExpert.getAddress();
        TkDataExpertAddress afterAddress = expert.getAddress() == null ? new TkDataExpertAddress() : expert.getAddress();
        List<Field> addressLogFields = erpOperateLogService.getLogFields(beforeAddress, afterAddress, Arrays.asList("expertId","id"), false, false);
        if (CollectionUtil.isNotEmpty(addressLogFields)) {
            List<ErpOperateLog> logs = new ArrayList<>();
            for (Field field : addressLogFields) {
                if (field.getName().equals("address")) {
                    continue;
                }
                Schema schema = field.getAnnotation(Schema.class);
                String fileDesc = schema == null ? field.getName() : schema.description();
                ErpOperateLog operateLog = new ErpOperateLog();
                field.setAccessible(true);
                Object before = field.get(beforeAddress);
                Object after = field.get(afterAddress);
                operateLog.setOperateUserId(userId.longValue());
                operateLog.setOperateUserName(userName);
                operateLog.setFieldDesc(fileDesc);
                operateLog.setLogType(100);
                operateLog.setOperateType(1);
                operateLog.setOperateTable("tk_data_expert");
                operateLog.setBusinessId(expert.getId());
                operateLog.setOperateAt(now);
                operateLog.setCreatedAt(date);
                operateLog.setCreatedBy(userId);
                operateLog.setOperateName("TIKTOK达人");
                operateLog.setCreatedName(userName);
                operateLog.setUpdatedAt(date);
                operateLog.setUpdatedName(userName);
                operateLog.setUpdatedBy(userId);
                operateLog.setOperateNewValue(
                        String.format(
                                "%s 字段由 %s 变为 %s",
                                fileDesc, before == null ? "空" : String.valueOf(before),
                                after == null ? "空" : String.valueOf(after))
                );
                logs.add(operateLog);
            }
            erpOperateLogService.saveBatch(logs, 500);
        }

    }

    private void recordExpertInsertLog(List<TkDataExpertDTO> experts) {
        for (TkDataExpertDTO expert : experts) {
            recordExpertInsertLog(BeanCopyUtils.copyBean(expert, TkDataExpertDTO.class));
        }

    }

    private void recordExpertInsertLog(TkDataExpertDTO expert) {
        Integer userId = UserUtils.getCurrentUserId(0);
        String userName = UserUtils.getCurrentUserName("System");
        LocalDateTime now = LocalDateTime.now(ZoneId.of("America/Los_Angeles"));
        Date date = LocalDateTimeUtils.localDateTimeToDate(now);
        ErpOperateLog operateLog = new ErpOperateLog();
        operateLog.setOperateTable("tk_data_expert");
        operateLog.setBusinessId(expert.getId());
        operateLog.setOperateAt(now);
        operateLog.setFieldDesc("新增");
        operateLog.setCreatedAt(date);
        operateLog.setCreatedBy(userId);
        operateLog.setOperateName("TIKTOK达人");
        operateLog.setCreatedName(userName);
        operateLog.setUpdatedAt(date);
        operateLog.setUpdatedName(userName);
        operateLog.setUpdatedBy(userId);
        operateLog.setOperateNewValue("添加了Creator ID " + expert.getCreatorId());
        operateLog.setOperateType(2);
        operateLog.setLogType(100);
        operateLog.setOperateUserId(userId.longValue());
        operateLog.setOperateUserName(userName);
        erpOperateLogService.save(operateLog);
    }


    /**
     * 检查唯一性
     * @param expert
     */
    private void checkUniqueness(TkDataExpertDTO expert) {
        TkDataExpertDTO dto = new TkDataExpertDTO();
        dto.setCreator(expert.getCreator());
        dto.setCreatorCid(expert.getCreatorCid());
        dto.setCreatorId(expert.getCreatorCid());
        List<TkDataExpert> experts = tkExpertMapper.selectExpertOrProperty(dto);
        if (CollectionUtil.isEmpty(experts)) {
            return;
        }
        if (experts.size() == 1 && Objects.nonNull(expert.getId()) && experts.get(0).getId().equals(expert.getId())) {
            return;
        }
        // 获取重复信息
        StringBuilder builder = new StringBuilder();
        Map<String, List<TkDataExpert>> creatorMap = experts.stream()
                .filter(c -> StrUtil.isNotBlank(c.getCreator()))
                .collect(Collectors.groupingBy(TkDataExpert::getCreator));

        Map<String, List<TkDataExpert>> creatorIdMap = experts.stream()
                .filter(c -> StrUtil.isNotBlank(c.getCreatorId()))
                .collect(Collectors.groupingBy(TkDataExpert::getCreatorId));

        Map<String, List<TkDataExpert>> creatorCidMap = experts.stream()
                .filter(c -> StrUtil.isNotBlank(c.getCreatorCid()))
                .collect(Collectors.groupingBy(TkDataExpert::getCreatorCid));

        if (creatorMap.containsKey(expert.getCreator())) {
            builder.append("达人用户名不可重复").append(" ");
        }
        if (creatorIdMap.containsKey(expert.getCreatorId())) {
            builder.append("Creator ID不可重复").append(" ");
        }
        if (creatorCidMap.containsKey(expert.getCreatorCid())) {
            builder.append("达人cid不可重复");
        }
        throw new CommonException(builder.toString());
    }

    @Override
    @Transactional
    public void updateOperateUser(Integer[] ids, Integer operatorId) {
        Integer currentUserId = UserUtils.getCurrentUserId(0);
        String currentUserName = UserUtils.getCurrentUserName("System");


        log.info("达人运营分配:{} - {}  操作人信息:{} - {}", ids, operatorId, currentUserId, currentUserName);

        UserEntity user = userService.getById(operatorId);
        if (Objects.isNull(user)) {
            throw new CommonException("未获取到运营信息");
        }

        List<TkDataExpert> experts = this.listByIds(CollectionUtil.toList(ids));
        if (CollectionUtil.isEmpty(experts)) {
            throw new CommonException("未获取到达人信息");
        }

        this.lambdaUpdate()
                .set(TkDataExpert::getOperatorId, operatorId)
                .set(TkDataExpert::getOperator, user.getName())
                .set(TkDataExpert::getUpdatedAt, new Date())
                .set(TkDataExpert::getUpdatedName, currentUserName)
                .in(TkDataExpert::getId, ids)
                .update();
        // 更新视频达人字段
        tabcutCreatorVideoService.lambdaUpdate()
                .in(TabcutCreatorVideo::getExpertId, ids)
                .set(TabcutCreatorVideo::getCreatorOperatorId, operatorId)
                .set(TabcutCreatorVideo::getCreatorOperator, user.getName())
                .update();



        // 写入操作日志
        threadPoolTaskExecutor.execute(() -> {
            List<ErpOperateLog> logs = new ArrayList<>();

            for (TkDataExpert expert : experts) {
                if (!Objects.equals(user.getName(), expert.getOperator())) {

                    ErpOperateLog operateLog = new ErpOperateLog();
                    operateLog.setOperateUserId(Long.valueOf(currentUserId));
                    operateLog.setOperateUserName(currentUserName);
                    operateLog.setOperateAt(LocalDateTime.now());
                    operateLog.setFieldDesc("运营");
                    operateLog.setOperateName("TIKTOK达人");
                    operateLog.setCreatedAt(new Date());
                    operateLog.setCreatedBy(currentUserId);
                    operateLog.setCreatedName(currentUserName);
                    operateLog.setUpdatedAt(operateLog.getCreatedAt());
                    operateLog.setUpdatedName(currentUserName);
                    operateLog.setUpdatedBy(currentUserId);
                    operateLog.setLogType(100);
                    operateLog.setOperateType(1);
                    operateLog.setOperateTable("tk_data_expert");
                    operateLog.setBusinessId(expert.getId());
                    String operator = expert.getOperator();
                    if (StrUtil.isNotBlank(operator)) {
                        operateLog.setOperateOldValue(operator);
                    }
                    operateLog.setOperateNewValue(user.getName());
                    logs.add(operateLog);
                }
            }
            if (CollectionUtil.isNotEmpty(logs)) {
                erpOperateLogService.saveBatch(logs);
            }
        });
    }

    @Override
    @Transactional
    public void updateGroup(Integer[] ids, Integer group) {

        List<TkDataExpert> dataExperts = this.listByIds(Arrays.asList(ids));
        if (CollectionUtil.isEmpty(dataExperts)) {
            throw new CommonException("未获取到达人信息");
        }

        this.lambdaUpdate()
                .in(TkDataExpert::getId, ids)
                .set(TkDataExpert::getGroup, group)
                .update();

        // 更新视频信息
        tabcutCreatorVideoService.lambdaUpdate()
                .in(TabcutCreatorVideo::getExpertId, ids)
                .set(TabcutCreatorVideo::getCreatorGroup, group)
                .update();
    }


    private void setAttr(Integer contextId, UserEntity entity, Date nowDate, List<UserEntity> userEntities, TkDataExpert s, boolean b) {
        s.setState(TkDataExpertStateEnum.COMPLETED.getLabel());
        List<UserEntity> userList = userEntities.stream().filter(r -> s.getOperator().equalsIgnoreCase(r.getName())).collect(Collectors.toList());
        s.setOperatorId(Long.valueOf(userList.get(0).getId()));
        setBaseAttr(contextId, entity, nowDate, s, b);
    }

    private void setBaseAttr(Integer contextId, UserEntity entity, Date nowDate, TkDataExpert s, boolean saveOrUpdate) {
        if (saveOrUpdate) {
            s.setCreatedBy(entity != null ? entity.getId() : -1);
            s.setCreatedName(entity != null ? entity.getName() : "System");
            s.setCreatedAt(nowDate);
        }
        s.setContextId(contextId);
        s.setUpdatedBy(entity != null ? entity.getId() : -1);
        s.setUpdatedName(entity != null ? entity.getName() : "System");
        s.setUpdatedAt(nowDate);
    }

    /**
     * 导出
     *
     * @param dto
     * @param contextId
     * @return
     */
    @SneakyThrows
    @Override
    public void exportExcel(TkDataExpertDTO dto, Integer contextId, HttpServletResponse response) {
        dto.setContextId(contextId);
        String property = System.getProperties().getProperty("os.name");
        String desktopPath = System.getProperty("user.home") + "\\Desktop\\";
        String path = property.contains("win") || property.contains("Win") ? desktopPath : "/";
        ExpertExportResultHandler handler = new ExpertExportResultHandler(path + "expert.xlsx");
        tkExpertMapper.streamQueryList(handler, dto);
        byte[] buffer = handler.load();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("expert.xlsx", "UTF-8"));
        response.setCharacterEncoding("UTF-8");
        IoUtil.write(response.getOutputStream(), true, buffer);
    }

    @Override
    public TkDataExpert selectExpertById(Long id) {
        // 获取当前达人信息
        TkDataExpert expert = this.getById(id);
        AssertUtil.isFalse(Objects.isNull(expert), "未获取到达人信息");
        // 默认获取当前日期
        TkDataExpertAddress address = tkDataAddressService.lambdaQuery()
                .eq(TkDataExpertAddress::getExpertId, id)
                .one();
        if (Objects.nonNull(address)) {
            expert.setAddress(address);
        }
        return expert;

    }

    @Override
    public void syncCreatorBasic() {
        long start = System.currentTimeMillis();
        Long cursor = -1L;
        List<TkDataExpert> experts;
        while ((experts = selectExpertByCursorIdAndLimit(cursor, 100)) != null && CollectionUtil.isNotEmpty(experts)) {

            cursor = experts.get(experts.size() - 1).getId();
            try {
                handleExpertData(experts);
            } catch (Exception e) {
                log.info("达人基础信息同步异常 - ", e);
            }

        }
        long end = System.currentTimeMillis();
        log.info("达人基础数同步 - 耗时：{}", end - start);
    }

    private void handleExpertData(List<TkDataExpert> experts) {
        List<String> creatorIds = experts.stream().map(TkDataExpert::getCreatorId).collect(Collectors.toList());
        TabcutCreatorBasicResponse basicResponse = tabcutApi.tabcutRequest(
                TabcutUrl.ITEM_CREATOR_BASIC,
                new TabcutCreatorBasicRequest(creatorIds),
                new TypeReference<TabcutCreatorBasicResponse>() {
                }
        );
        if (Objects.isNull(basicResponse)) {
            log.error("==============达人信息获取失败 : {}================", creatorIds);
            return;
        }
        List<TabcutCreatorBasicResponse.Result> result = basicResponse.getResult();
        Map<String, TkDataExpert> expertMap = experts.stream().collect(Collectors.toMap(TkDataExpert::getCreatorId, Function.identity(), (a, b) -> a));

        List<TkDataExpertAddress> expertAddresses = new ArrayList<>();
        // 更新达人信息
        for (TabcutCreatorBasicResponse.Result r : result) {
            TkDataExpert expert = expertMap.get(r.getUid());
            expert.setSkipInterceptor(true);
            expert.setOriginCreatorUrl(r.getOriginCreatorUrl());
            if (StrUtil.isBlank(expert.getAvatarUrl())) {
                try {
                    String url = downloadAndUploadUrl(r.getAvatarUrl(), TMP_VIDEO_PATH + r.getUid() + ".png", 2);
                    expert.setAvatarUrl(url);
                } catch (Exception e) {

                }
            }
            expert.setRegion(r.getRegion());
            expert.setUniqueId(r.getUniqueId());
            expert.setCreator(r.getUniqueId());
            expert.setNickName(r.getName());
            if (Objects.nonNull(r.getVideoDuration())) {
                expert.setAvgVideoDuration(r.getVideoDuration().getAvg());
            }

            if (Objects.nonNull(r.getFollowerCountInfo())) {
                expert.setFansCount(r.getFollowerCountInfo().getTotal());// 粉丝数量
            }
            if (Objects.nonNull(r.getPlayCountInfo())) {
                expert.setPlayCount(r.getPlayCountInfo().getTotal());
            }
            if (Objects.nonNull(r.getLikeCountInfo())) {
                expert.setLikeCount(r.getLikeCountInfo().getTotal());
            }
            if (Objects.nonNull(r.getShareCountInfo())) {
                expert.setShareCount(r.getShareCountInfo().getTotal());
            }
            if (Objects.nonNull(r.getCommentCountInfo())) {
                expert.setReviewCount(r.getCommentCountInfo().getTotal());
            }
            if (Objects.nonNull(r.getVideoCountInfo())) {
                expert.setVideoCount(r.getVideoCountInfo().getTotal());
            }
            if (Objects.nonNull(r.getItemVideoCountInfo())) {
                expert.setItemVideoCount(r.getItemVideoCountInfo().getTotal());

            }
            if (Objects.nonNull(r.getGpmInfo())) {
                expert.setGpm(r.getGpmInfo().getVideo());
            }

            if (Objects.nonNull(r.getGmvInfo()) && Objects.nonNull(r.getGmvInfo().getVideo())) {
                expert.setGmv(r.getGmvInfo().getVideo().getLocal());
            }

            if (StrUtil.isNotBlank(r.getRegion())) {
                TkDataExpertAddress address = new TkDataExpertAddress();
                address.setExpertId(address.getExpertId());
                address.setCountryCode(r.getRegion());
                expertAddresses.add(address);
            }


        }
        if (CollectionUtil.isNotEmpty(expertAddresses)) {
            try {
//                    tkDataAddressService.batchUpdateCountryCodeByExpertId(expertAddresses);
            } catch (Exception e) {

            }
        }

        this.updateBatchById(experts);
    }


    @Override
    public TKDataExpertDetailVO expertDetailFixed(TkDataExpertDTO dto) {
        AssertUtil.isFalse(Objects.isNull(dto.getId()) && StrUtil.isBlank(dto.getCreatorId()), "达人ID不能为空");
        TkDataExpert expert = Objects.nonNull(dto.getId()) ? this.getById(dto.getId())
                : this.lambdaQuery().eq(TkDataExpert::getCreatorId, dto.getCreatorId()).one();
        AssertUtil.isFalse(Objects.isNull(expert), "未获取到达人数据");
        ConvertUtils.dictConvert(expert);
        TKDataExpertDetailVO vo = new TKDataExpertDetailVO();
        vo.setExpert(expert);
        TkDataExpertAddress address = tkDataAddressService.lambdaQuery()
                .eq(TkDataExpertAddress::getExpertId, expert.getId())
                .one();
        expert.setAddress(address);


        CompletableFuture<TKDataExpertDetailVO> future = CompletableFuture.supplyAsync(() -> StrUtil.isNotBlank(expert.getCreatorCid()) ? saleOrdersMapper.selectCommissonByCreatorCid(expert.getCreatorCid()) : null, threadPoolTaskExecutor).exceptionally(e -> null)
                .thenCombine(CompletableFuture.supplyAsync(() -> {
                    return tabcutCreatorCategoryService.lambdaQuery()
                            .eq(TabcutCreatorCategory::getCreatorId, expert.getCreatorId())
                            .select(TabcutCreatorCategory::getCategoryId, TabcutCreatorCategory::getCategoryName)
                            .groupBy(TabcutCreatorCategory::getCreatorId)
                            .list();
                }), (r1, r2) -> {
                    TKDataExpertDetailVO.Overview overview = new TKDataExpertDetailVO.Overview();
                    if (Objects.nonNull(r1)) {
                        r1.setCommission(r1.getCommission());
                    }
                    if (CollectionUtil.isNotEmpty(r2)) {
                        String categoryNames = r2.stream().map(TabcutCreatorCategory::getCategoryName).distinct().collect(Collectors.joining(","));
                        expert.setCategoryNames(categoryNames);
                    }

                    // 设置数据总览指标数据
                    overview.setFansCount(NumberUtil.numberToStr(expert.getFansCount()));
                    overview.setLikeCount(NumberUtil.numberToStr(expert.getLikeCount()));
                    overview.setVideoCount(NumberUtil.numberToStr(expert.getVideoCount()));
                    overview.setVideoGpm("$" + NumberUtil.numberToStr(expert.getGpm()));
                    overview.setLiveCount(NumberUtil.numberToStr(expert.getLiveCount()));
                    overview.setGmv("$" + NumberUtil.numberToStr(expert.getGmv()));
                    overview.setCommission("$" + NumberUtil.numberToStr(expert.getCommission()));
                    vo.setOverview(overview);
                    return vo;
                });


        if (StrUtil.isNotBlank(expert.getRegion())) {
            CommonRegionEntity entity = commonRegionService.findByCode(2, expert.getRegion());
            if (Objects.nonNull(entity)) {
                expert.setCountry(entity.getName());
            } else {
                expert.setCountry(expert.getRegion());
            }
        }
        try {
            future.get(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        } catch (TimeoutException e) {
            throw new RuntimeException(e);
        }
        return vo;
    }

    @Override
    @SneakyThrows
    public TKDataExpertDetailVO expertDetailTrendAnalysis(TabcutCreatorDetailDTO dto) {
        TKDataExpertDetailVO vo = new TKDataExpertDetailVO();
//        TabcutCreatorDayDTO dayDTO = new TabcutCreatorDayDTO();
//        dayDTO.setBeginTime(dto.getBeginTime().replace("-",""));
//        dayDTO.setEndTime(dto.getEndTime().replace("-",""));
//        dayDTO.setCreatorId(dto.getCreatorId());
//        TabcutVideoKpiDTO creatorVideoDTO = new TabcutVideoKpiDTO();
//        creatorVideoDTO.setOwnProductQuery(dto.getOwnProductQuery());
//        creatorVideoDTO.setBeginTime(dto.getBeginTime());
//        creatorVideoDTO.setEndTime(dayDTO.getEndTime());
//        creatorVideoDTO.setCreatorId(dayDTO.getCreatorId());
//        creatorVideoDTO.setGroupBy("biz_date");

        String beginTime = dto.getBeginTime().replace("-", "");
        String endTime = dto.getEndTime().replace("-", "");
        List<TabcutCreatorDayKpi> dayKpis = tabcutCreatorDayKpiService.selectByCreatorIdAndBewteen(
                dto.getOwnProductQuery(),
                dto.getCreatorId(),
                beginTime,
                endTime
        );

//        List<TabcutCreatorVideoVO> videoVOS = tabcutCreatorVideoMapper.selectVideoKpi(creatorVideoDTO);
        // 获取开始到结束的所有时间
        List<String> allDay = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date endDate = sdf.parse(endTime);
        Date startDate = sdf.parse(beginTime);
        allDay.add(beginTime);
        while (((startDate = DateUtils.addDays(startDate, 1)).compareTo(endDate)) <= 0) {
            allDay.add(sdf.format(startDate));
        }
        TKDataExpertDetailVO.TrendAnalysis trendAnalysis = new TKDataExpertDetailVO.TrendAnalysis();
        Map<String, TabcutCreatorDayKpi> map = dayKpis.stream().collect(Collectors.toMap(TabcutCreatorDayKpi::getBizDate, Function.identity(), (a, b) -> a));

        Long totalPlayCount = dayKpis.stream().map(TabcutCreatorDayKpi::getPlayCount).filter(Objects::nonNull).reduce(Long::sum).orElse(0L);
        Long totalLikeCount = dayKpis.stream().map(TabcutCreatorDayKpi::getLikeCount).filter(Objects::nonNull).reduce(Long::sum).orElse(0L);
        Long totalShareCount = dayKpis.stream().map(TabcutCreatorDayKpi::getShareCount).filter(Objects::nonNull).reduce(Long::sum).orElse(0L);
        Long totalReviewCount = dayKpis.stream().map(TabcutCreatorDayKpi::getCommentCount).filter(Objects::nonNull).reduce(Long::sum).orElse(0L);
        Long totalFansCount = dayKpis.stream().map(TabcutCreatorDayKpi::getFansIncr).filter(Objects::nonNull).reduce(Long::sum).orElse(0L);

        Map<String, TabcutCreatorDayKpi> dayKpiMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(dayKpis)) {
            dayKpiMap = dayKpis.stream().collect(Collectors.toMap(TabcutCreatorDayKpi::getBizDate, Function.identity(), (a, b) -> a));
        }

        for (String day : allDay) {
            String key = day.substring(0, 4) + "-" + day.substring(4, 6) + "-" + day.substring(6);
            Long playCount = map.containsKey(day) ? map.get(day).getPlayCount() : 0;
            Long likeCount = map.containsKey(day) ? map.get(day).getLikeCount() : 0;
            Long reviewCount = map.containsKey(day) ? map.get(day).getCommentCount() : 0;
            Long shareCount = map.containsKey(day) ? map.get(day).getShareCount() : 0;
            Long fansIncr = map.containsKey(day) ? map.get(day).getFansIncr() : 0;


            trendAnalysis.getPlayCount().put(key, playCount);
            trendAnalysis.getLikeCount().put(key, likeCount);
            trendAnalysis.getReviewCount().put(key, reviewCount);
            trendAnalysis.getShareCount().put(key, shareCount);
            trendAnalysis.getFanCount().put(key, fansIncr);

        }
        trendAnalysis.setTotalPlayCount(totalPlayCount);
        trendAnalysis.setTotalLikeCount(totalLikeCount);
        trendAnalysis.setTotalShareCount(totalShareCount);
        trendAnalysis.setTotalReviewCount(totalReviewCount);
        trendAnalysis.setTotalFansCount(totalFansCount);
        vo.setTrendAnalysis(trendAnalysis);
        return vo;

    }

    @Override
    @Transactional
    public void removeGroup(ExpertGroupDTO dto) {
        List<TkDataExpert> experts = this.listByIds(dto.getExpertIds());
        if (CollectionUtil.isEmpty(experts)) {
            return;
        }
        List<String> creatorIds = experts.stream().map(TkDataExpert::getCreatorId).collect(Collectors.toList());
        this.lambdaUpdate()
                .in(TkDataExpert::getId, dto.getExpertIds())
                .in(TkDataExpert::getGroup, dto.getGroups())
                .set(TkDataExpert::getGroup, 1)
                .update();

        tabcutCreatorVideoService.lambdaUpdate()
                .in(TabcutCreatorVideo::getCreatorId, creatorIds)
                .set(TabcutCreatorVideo::getCreatorGroup, 1)
                .update();

    }


    @Override
    @SneakyThrows
    public TKDataExpertDetailVO expertDetailRecent(TabcutCreatorDetailDTO dto) {
        boolean isOwn = Objects.nonNull(dto.getOwnProductQuery()) && Objects.equals(dto.getOwnProductQuery(), 1);
        dto.setBeginTime(dto.getBeginTime().replace("-", ""));
        dto.setEndTime(dto.getEndTime().replace("-", ""));

        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date begin = sdf1.parse(dto.getBeginTime());
        Date end = sdf1.parse(dto.getEndTime());


        CompletableFuture<List<TabcutCreatorDayKpi>> f1 = CompletableFuture.supplyAsync(() -> tabcutCreatorDayKpiService.selectByCreatorIdAndBewteen(dto.getOwnProductQuery(), dto.getCreatorId(), dto.getBeginTime(), dto.getEndTime()), threadPoolTaskExecutor);
        CompletableFuture<List<TabcutCreatorVideo>> f2 = CompletableFuture.supplyAsync(() -> tabcutCreatorVideoService.lambdaQuery()
                .between(TabcutCreatorVideo::getVideoReleaseDate, sdf2.format(begin), sdf2.format(end))
                .eq(TabcutCreatorVideo::getCreatorId, dto.getCreatorId())
                .list(), threadPoolTaskExecutor);


        List<TabcutCreatorDayKpi> dayKpis = f1.get(20, TimeUnit.SECONDS);
        List<TabcutCreatorVideo> videos = f2.get(20, TimeUnit.SECONDS);


        TKDataExpertDetailVO.RecentKpi.BasicData basicData = new TKDataExpertDetailVO.RecentKpi.BasicData();
        TKDataExpertDetailVO.RecentKpi.VideoData video = new TKDataExpertDetailVO.RecentKpi.VideoData();
        TKDataExpertDetailVO.RecentKpi.ItemVideoData itemVideoData = new TKDataExpertDetailVO.RecentKpi.ItemVideoData();


        if (CollectionUtil.isNotEmpty(dayKpis)) {
            // 粉丝增量 视频数 带货视频数 带货直播数
            Long fansIncr = dayKpis.stream().filter(c -> Objects.nonNull(c.getFansIncr())).map(TabcutCreatorDayKpi::getFansIncr).reduce(Long::sum).orElse(0L);
//            Long itemVideoCount = dayKpis.stream().map(TabcutCreatorDayKpi::getItemVideoCount).filter(Objects::nonNull).reduce(Long::sum).orElse(0L);
            basicData.setFansIncr(NumberUtil.numberToStr(fansIncr));
//            if (!isOwn) {
//                itemVideoData.setItemVideoCount(NumberUtil.numberToStr(itemVideoCount));
//                itemVideoData.setVideoItemCount(NumberUtil.numberToStr(itemVideoCount));
//            }

//            Long videoCount = dayKpis.stream().map(TabcutCreatorDayKpi::getVideoCount).filter(Objects::nonNull).reduce(Long::sum).orElse(0L);


            Long playCount = dayKpis.stream().map(TabcutCreatorDayKpi::getPlayCount).filter(Objects::nonNull).reduce(Long::sum).orElse(0L);
            Long videoCount = dayKpis.stream().map(TabcutCreatorDayKpi::getVideoCount).filter(Objects::nonNull).reduce(Long::sum).orElse(0L);
            Long likeCount = dayKpis.stream().map(TabcutCreatorDayKpi::getLikeCount).filter(Objects::nonNull).reduce(Long::sum).orElse(0L);
            Long commentCount = dayKpis.stream().map(TabcutCreatorDayKpi::getCommentCount).filter(Objects::nonNull).reduce(Long::sum).orElse(0L);
            Long shareCount = dayKpis.stream().map(TabcutCreatorDayKpi::getShareCount).filter(Objects::nonNull).reduce(Long::sum).orElse(0L);
            Long soldCount = dayKpis.stream().map(TabcutCreatorDayKpi::getSoldCount).filter(Objects::nonNull).reduce(Long::sum).orElse(0L);
            BigDecimal gmv = dayKpis.stream().map(TabcutCreatorDayKpi::getGmv).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            Long itemVideoCount = dayKpis.stream().map(TabcutCreatorDayKpi::getItemVideoCount).filter(Objects::nonNull).reduce(Long::sum).orElse(0L);
            long days = DateUtil.between(begin, end, DateUnit.DAY) + 1;
            BigDecimal avgPlayCount = BigDecimal.valueOf(playCount).divide(BigDecimal.valueOf(days), 2, RoundingMode.HALF_UP);
            BigDecimal avgLikeCount = BigDecimal.valueOf(likeCount).divide(BigDecimal.valueOf(days), 2, RoundingMode.HALF_UP);
            BigDecimal avgCommentCount = BigDecimal.valueOf(commentCount).divide(BigDecimal.valueOf(days), 2, RoundingMode.HALF_UP);
            BigDecimal avgShareCount = BigDecimal.valueOf(shareCount).divide(BigDecimal.valueOf(days), 2, RoundingMode.HALF_UP);

            basicData.setVideoCount(NumberUtil.numberToStr(videoCount));
            basicData.setItemVideoCount(NumberUtil.numberToStr(itemVideoCount));


            itemVideoData.setItemTotalCount(NumberUtil.numberToStr(itemVideoCount));

            video.setPlayCount(NumberUtil.numberToStr(playCount));
            video.setLikeCount(NumberUtil.numberToStr(likeCount));
            video.setReviewCount(NumberUtil.numberToStr(commentCount));
            video.setShareCount(NumberUtil.numberToStr(shareCount));
            video.setInteractionRate(playCount == 0L ? "0%" :
                    BigDecimal.valueOf(likeCount + shareCount + commentCount)
                            .divide(BigDecimal.valueOf(playCount), 4, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100)) + "%"
            );
            video.setAvgPlayCount(NumberUtil.numberToStr(avgPlayCount));
            video.setAvgLikeCount(NumberUtil.numberToStr(avgLikeCount));
            video.setAvgReviewCount(NumberUtil.numberToStr(avgCommentCount));
            video.setAvgShareCount(NumberUtil.numberToStr(avgShareCount));
            itemVideoData.setSoldTotalCount(NumberUtil.numberToStr(soldCount));
            itemVideoData.setSaleTotalCount(NumberUtil.numberToStr(gmv));
            itemVideoData.setItemVideoCount(NumberUtil.numberToStr(itemVideoCount));
            itemVideoData.setVideoItemCount(NumberUtil.numberToStr(itemVideoCount));


            itemVideoData.setSoldVideoCount(NumberUtil.numberToStr(soldCount));
            itemVideoData.setSaleVideoCount(NumberUtil.numberToStr(gmv));
            itemVideoData.setInteractionRate(video.getInteractionRate());
        }


        TKDataExpertDetailVO vo = new TKDataExpertDetailVO();
        // 查询指标
        long day = getBetween(dto.getBeginTime(), dto.getEndTime());
        TabcutVideoKpiDTO videoKpiDTO = new TabcutVideoKpiDTO();
        videoKpiDTO.setCreatorId(dto.getCreatorId());
        videoKpiDTO.setBeginTime(dto.getBeginTime());
        videoKpiDTO.setEndTime(dto.getEndTime());
        videoKpiDTO.setOwnProductQuery(dto.getOwnProductQuery());
//        List<TabcutCreatorVideoVO> videoVOS = tabcutCreatorVideoService.selectVideoKpi(videoKpiDTO);
//        if (CollectionUtil.isEmpty(videoVOS)) {
//            return vo;
//        }
// 近期数据构建
        TKDataExpertDetailVO.RecentKpi recentKpi = new TKDataExpertDetailVO.RecentKpi();
        recentKpi.setBasic(basicData);
        recentKpi.setVideo(video);
//        recentKpi.setItemVideo(buildItemVideoData(recentKpi.getBasic(),videoVOS));
        recentKpi.setItemVideo(itemVideoData);
        vo.setRecentKpi(recentKpi);

        return vo;

    }


    @Override
    public void updateProgress(Integer[] ids, Integer progress) {
        this.lambdaUpdate()
                .in(TkDataExpert::getId, ids)
                .set(TkDataExpert::getProgress, progress)
                .update();
    }

    @Override
    public List<CreatorRelationSampleVO> relationSample(CreatorRelationSampleDTO dto) {
        String creatorId = dto.getCreatorId();
        TkDataExpert expert = this.lambdaQuery()
                .eq(TkDataExpert::getCreatorId, creatorId)
                .one();
        if (Objects.isNull(expert)) {
            throw new CommonException("未获取到达人信息");
        }
        if (StrUtil.isBlank(expert.getCreatorCid())) {
            throw new CommonException("当前达人Cid为空,无法获取关联样品");
        }
        dto.setCreatorCid(expert.getCreatorCid());
        List<CreatorRelationSampleVO> sampleVOS = saleOrdersMapper.selectCreatorSampleOrder(dto);
        if (CollectionUtil.isEmpty(sampleVOS)) {
            return new ArrayList<>();
        }
        List<CreatorRelationSampleVO> itemIds = sampleVOS.stream()
                .filter(c -> Objects.nonNull(c.getItemId()))
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(itemIds)) {
            return sampleVOS;
        }

        List<TabcutCreatorVideo> creatorVideos = tabcutCreatorVideoService.lambdaQuery()
                .in(TabcutCreatorVideo::getItemId, itemIds)
                .select(TabcutCreatorVideo::getItemId, TabcutCreatorVideo::getItemName)
                .groupBy(TabcutCreatorVideo::getItemId)
                .list();
        if (CollectionUtil.isEmpty(creatorVideos)) {
            return sampleVOS;
        }
        Map<String, String> map = creatorVideos.stream().collect(Collectors.toMap(TabcutCreatorVideo::getItemId, TabcutCreatorVideo::getItemName, (a, b) -> a));

        sampleVOS.stream()
                .filter(c -> StrUtil.isNotBlank(c.getItemId()) && map.containsKey(c.getItemId()))
                .forEach(c -> c.setItemName(map.get(c.getItemId())));
        return sampleVOS;
    }

    @Override
    public List<TkDataExpert> selectExpertByCreator(String creator, String cid) {

        if (StringUtils.isEmpty(creator)&&StringUtils.isEmpty(cid)){
            return Collections.emptyList();
        }
        LambdaQueryChainWrapper<TkDataExpert> queryChainWrapper = this.lambdaQuery();
        if (!StringUtils.isEmpty(creator)) {
            queryChainWrapper.like(TkDataExpert::getCreator, creator);
        }
        if (!StringUtils.isEmpty(cid)){
            queryChainWrapper.eq(TkDataExpert::getCreatorCid, cid);
        }
        List<TkDataExpert> experts = queryChainWrapper.list();
        if (CollectionUtil.isEmpty(experts)) {
            return experts;
        }
        List<Long> expertIds = experts.stream().map(TkDataExpert::getId).collect(Collectors.toList());
        List<TkDataExpertAddress> addresses = tkDataAddressService.lambdaQuery()
                .in(TkDataExpertAddress::getExpertId, expertIds)
                .list();
        if (CollectionUtil.isEmpty(addresses)) {
            return experts;
        }
        Map<Long, TkDataExpertAddress> map = addresses.stream().collect(Collectors.toMap(TkDataExpertAddress::getExpertId, Function.identity(), (a, b) -> a));

        experts.stream()
                .filter(c -> map.containsKey(c.getId()))
                .forEach(c -> c.setAddress(map.get(c.getId())));
        return experts;
    }


    private Map<String, String> getCategoryData(String creatorId, String beginTime, String endTime) {
        List<TabcutCreatorCategory> creatorCategories = tabcutCreatorCategoryService.selectCateoryByCreatorIdAndBizDate(creatorId, beginTime, endTime);
        if (CollectionUtil.isNotEmpty(creatorCategories)) {
            Map<String, String> categoryMap = new HashMap<>();
            Map<Long, List<TabcutCreatorCategory>> map = creatorCategories.stream()
                    .collect(Collectors.groupingBy(TabcutCreatorCategory::getCategoryId));

            Integer total = creatorCategories.stream().map(TabcutCreatorCategory::getVideoCount).reduce(Integer::sum).orElse(0);

            for (Map.Entry<Long, List<TabcutCreatorCategory>> entry : map.entrySet()) {
                List<TabcutCreatorCategory> categories = entry.getValue();
                TabcutCreatorCategory category = categories.get(0);

                Integer current = categories.stream().map(TabcutCreatorCategory::getVideoCount).reduce(Integer::sum).orElse(0);

                BigDecimal value = BigDecimal.valueOf(current).divide(BigDecimal.valueOf(total), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                categoryMap.put(category.getCategoryName(), value + "%");
            }
            return categoryMap;
        }
        return null;
    }

    @SneakyThrows
    private static long getBetween(String beginTime, String endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        long day = DateUtil.between(sdf.parse(beginTime), sdf.parse(endTime), DateUnit.DAY);
        return day;
    }


    private static TKDataExpertDetailVO.RecentKpi.ItemVideoData buildItemVideoData(TKDataExpertDetailVO.RecentKpi.BasicData basic, List<TabcutCreatorVideoVO> videoVOS) {
        if (CollectionUtil.isEmpty(videoVOS)) {
            return null;
        }

        Long itemVideoCount = basic.getVideoCountNumber() == null ? 0L : basic.getVideoCountNumber();
        Long soldCount = videoVOS.stream().map(TabcutCreatorVideoVO::getSoldCount).reduce(Long::sum).orElse(0L);
        BigDecimal saleCount = videoVOS.stream().map(TabcutCreatorVideoVO::getVideoGvm).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        List<TabcutCreatorVideoVO> itemVideos = videoVOS.stream().filter(c -> StrUtil.isNotBlank(c.getItemId())).collect(Collectors.toList());
        Map<String, List<TabcutCreatorVideoVO>> map = itemVideos.stream().collect(Collectors.groupingBy(TabcutCreatorVideo::getItemId));
        Long videoItemSoldCount = itemVideos.stream().filter(c -> Objects.nonNull(c.getSoldCount())).map(TabcutCreatorVideo::getSoldCount).reduce(Long::sum).orElse(0L);
        BigDecimal saleVideoCount = itemVideos.stream().map(TabcutCreatorVideo::getVideoGvm).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        Long playCount = itemVideos.stream().map(TabcutCreatorVideo::getPlayCount).reduce(Long::sum).orElse(0L);
        Long likeCount = itemVideos.stream().map(TabcutCreatorVideo::getLikeCount).reduce(Long::sum).orElse(0L);
        Long commentCount = itemVideos.stream().map(TabcutCreatorVideo::getCommentCount).reduce(Long::sum).orElse(0L);
        Long shareCount = itemVideos.stream().map(TabcutCreatorVideo::getShareCount).reduce(Long::sum).orElse(0L);
        BigDecimal interactionRate = Objects.equals(playCount, 0L) ? BigDecimal.ZERO :
                BigDecimal.valueOf(likeCount + commentCount + shareCount).divide(BigDecimal.valueOf(playCount), 2, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
        // 带货总览数据
        TKDataExpertDetailVO.RecentKpi.ItemVideoData itemVideoData = new TKDataExpertDetailVO.RecentKpi.ItemVideoData();
        itemVideoData.setItemTotalCount(NumberUtil.numberToStr(itemVideoCount));
        itemVideoData.setSoldTotalCount(NumberUtil.numberToStr(soldCount));
        itemVideoData.setSaleTotalCount("$" + NumberUtil.numberToStr(saleCount));
        // 视频带货数据
        itemVideoData.setItemVideoCount(NumberUtil.numberToStr(itemVideoCount));
        itemVideoData.setVideoItemCount(NumberUtil.numberToStr(map.size()));
        itemVideoData.setSoldVideoCount(NumberUtil.numberToStr(videoItemSoldCount));
        itemVideoData.setSaleVideoCount("$" + NumberUtil.numberToStr(saleVideoCount));
        itemVideoData.setInteractionRate(interactionRate + "%");
        // 直播带货数据
        itemVideoData.setLiveItemCount(NumberUtil.numberToStr(0));
        itemVideoData.setItemLiveCount(NumberUtil.numberToStr(0));
        itemVideoData.setSoldLiveCount(NumberUtil.numberToStr(0));
        itemVideoData.setSaleLiveCount(NumberUtil.numberToStr(0));
        return itemVideoData;
    }

    private TKDataExpertDetailVO.RecentKpi.BasicData buildBasicData(boolean isOwn, String creatorId, String beginTime, String endTime, List<TabcutCreatorVideoVO> videoVOS) {
        if (CollectionUtil.isEmpty(videoVOS)) {
            return null;
        }


        // 基础数据
        TKDataExpertDetailVO.RecentKpi.BasicData basic = new TKDataExpertDetailVO.RecentKpi.BasicData();
        List<TabcutCreatorDayKpi> dayKpis = tabcutCreatorDayKpiService.lambdaQuery()
                .eq(TabcutCreatorDayKpi::getCreatorId, creatorId)
                .between(TabcutCreatorDayKpi::getBizDate, beginTime, endTime)
                .list();
        if (CollectionUtil.isNotEmpty(dayKpis)) {
            Long fansCountIncr = dayKpis.stream().filter(c -> Objects.nonNull(c.getFansIncr())).map(TabcutCreatorDayKpi::getFansIncr).reduce(Long::sum).orElse(0L);
            basic.setFansIncr(NumberUtil.numberToStr(fansCountIncr));

        }
        try {
            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMdd");
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date begin = sdf1.parse(beginTime);
            Date end = sdf1.parse(endTime);
            List<TabcutCreatorVideo> creatorVideos = tabcutCreatorVideoService.lambdaQuery()
                    .between(TabcutCreatorVideo::getVideoReleaseDate, sdf2.format(begin), sdf2.format(end))
                    .eq(TabcutCreatorVideo::getCreatorId, creatorId)
                    .isNotNull(isOwn, TabcutCreatorVideo::getErpSku)
                    .list();
            if (CollectionUtil.isNotEmpty(creatorVideos)) {
                basic.setItemVideoCount(NumberUtil.numberToStr(creatorVideos.size()));
//        basic.setItemLiveCount(NumberUtil.numberToStr(isOwn ? kpi.getOwnItemLiveCount() : kpi.getItemLiveCount()));
                basic.setVideoCount(NumberUtil.numberToStr(creatorVideos.size()));
                basic.setVideoCountNumber((long) creatorVideos.size());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


        return basic;
    }

    private static TKDataExpertDetailVO.RecentKpi.VideoData buildVideoData(TKDataExpertDetailVO.RecentKpi.BasicData basic, List<TabcutCreatorVideoVO> videoVOS, long day) {
        if (CollectionUtil.isEmpty(videoVOS)) {
            return null;
        }
        Long videoCount = basic.getVideoCountNumber() == null ? 0L : basic.getVideoCountNumber();
        Long playCount = videoVOS.stream().map(TabcutCreatorVideo::getPlayCount).reduce(Long::sum).orElse(0L);
        Long likeCount = videoVOS.stream().map(TabcutCreatorVideo::getLikeCount).reduce(Long::sum).orElse(0L);
        Long commentCount = videoVOS.stream().map(TabcutCreatorVideo::getCommentCount).reduce(Long::sum).orElse(0L);
        Long shareCount = videoVOS.stream().map(TabcutCreatorVideo::getShareCount).reduce(Long::sum).orElse(0L);
        BigDecimal interactionRate = Objects.equals(playCount, 0L) ? BigDecimal.ZERO :
                BigDecimal.valueOf(likeCount + commentCount + shareCount).divide(BigDecimal.valueOf(playCount), 2, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
        // 视频数据
        TKDataExpertDetailVO.RecentKpi.VideoData video = new TKDataExpertDetailVO.RecentKpi.VideoData();
        video.setPlayCount(NumberUtil.numberToStr(playCount));
        video.setLikeCount(NumberUtil.numberToStr(likeCount));
        video.setReviewCount(NumberUtil.numberToStr(commentCount));
        video.setShareCount(NumberUtil.numberToStr(shareCount));
        video.setInteractionRate(interactionRate + "%");

        long denominator = Objects.equals(day, 0L) ? 1L : day;
        // 平均播放量
        BigDecimal avgPlayCount = Objects.equals(videoCount, 0L) ? BigDecimal.ZERO : BigDecimal.valueOf(playCount).divide(BigDecimal.valueOf(videoCount), 2, RoundingMode.HALF_UP);
        video.setAvgPlayCount(NumberUtil.numberToStr(avgPlayCount));
        // 平均点赞数
        BigDecimal avgLikeCount = Objects.equals(videoCount, 0L) ? BigDecimal.ZERO : BigDecimal.valueOf(likeCount).divide(BigDecimal.valueOf(videoCount), 2, RoundingMode.HALF_UP);
        // 平均评论数
        BigDecimal avgReviewCount = Objects.equals(videoCount, 0L) ? BigDecimal.ZERO : BigDecimal.valueOf(commentCount).divide(BigDecimal.valueOf(videoCount), 2, RoundingMode.HALF_UP);
        // 平均分享数
        BigDecimal avgShareCount = Objects.equals(videoCount, 0L) ? BigDecimal.ZERO : BigDecimal.valueOf(shareCount).divide(BigDecimal.valueOf(videoCount), 2, RoundingMode.HALF_UP);
        video.setAvgShareCount(NumberUtil.numberToStr(avgShareCount));
        return video;
    }


    public List<TkDataExpert> selectExpertByCursorIdAndLimit(Long cursorId, Integer limit) {
        return this.lambdaQuery()
                .gt(TkDataExpert::getId, cursorId)
                .last("limit " + limit)
                .orderByAsc(TkDataExpert::getId)
                .list();
    }


    public List<TkDataExpert> selectExpertByCursorIdAndLimit(Long cursorId, Integer limit, String... column) {
        QueryWrapper<TkDataExpert> wrapper = new QueryWrapper<>();
        wrapper.select(column);
        wrapper.gt("id", cursorId);
        wrapper.orderByAsc("id");
        wrapper.last("limit " + limit);
        return this.list(wrapper);
    }


    @Override
    public ScTicket expertConcat(ContactExpertDTO dto) {
        Long expertId = dto.getExpertId();
        TkDataExpert expert = this.getById(expertId);
        if (Objects.isNull(expert)) {
            throw new CommonException("未获取到达人信息");
        }
        Long operatorId = expert.getOperatorId();
        if (Objects.isNull(operatorId)) {
            throw new CommonException("当前达人尚未分配运营");
        }
        if (StrUtil.isEmpty(expert.getCreatorCid())) {
            throw new CommonException("未获取到当前达人Cid");
        }

        List<ScTicket> tickets = scTicketService.selectTicketByCustomerIdAndTicketType(Long.parseLong(expert.getCreatorCid()), TICKET_TYPE_OFFSITE_LETTER);

        if (CollectionUtil.isNotEmpty(tickets)) {

            List<ScTicket> processTicket = tickets.stream()
                    .filter(c -> !Objects.equals(c.getTicketStatus(), "FINISH") && !Objects.equals(c.getTicketStatus(), "CLOSED"))
                    .collect(Collectors.toList());
            AssertUtil.isFalse(CollectionUtil.isNotEmpty(processTicket), "当前达人存在未关闭未完成的其他站外信工单");
        }


        ScTicket ticket = new ScTicket();
        ticket.setOrganizationId(dto.getOrgId().longValue());
        ticket.setPriority(TICKET_PRIORITY_MEDIUM);
        ticket.setCustomerOuterId(expert.getCreatorCid());
        ticket.setUserId(operatorId);
        ticket.setTicketStatus("NEW");
        ticket.setTicketType("OFFSITE_LETTER");
        ticket.setTicketName("联系人ID：" + expert.getCreatorId());
        ScTicket scTicket = scTicketService.manualSaveScTicketForExpert(ticket);
        // 写入

        // 记录操作日志
        Date date = new Date();
        LocalDateTime now = LocalDateTime.now();
        Integer userId = UserUtils.getCurrentUserId(-1);
        String userName = UserUtils.getCurrentUserName("System");
        threadPoolTaskExecutor.execute(() -> {
            ErpOperateLog operateLog = new ErpOperateLog();
            operateLog.setOperateUserId(userId.longValue());
            operateLog.setOperateUserName(userName);
            operateLog.setOperateAt(now);
            operateLog.setFieldDesc("联系");
            operateLog.setOperateName("TIKTOK达人");
            operateLog.setCreatedAt(date);
            operateLog.setCreatedBy(userId);
            operateLog.setCreatedName(userName);
            operateLog.setUpdatedAt(date);
            operateLog.setUpdatedName(userName);
            operateLog.setUpdatedBy(userId);
            operateLog.setLogType(100);
            operateLog.setOperateType(3);
            operateLog.setOperateTable("tk_data_expert");
            operateLog.setBusinessId(expert.getId());
            operateLog.setOperateNewValue(
                    String.format(
                            "联系方式:%s ,生成工单编码：%s"
                            , dto.getContactType(), scTicket.getTicketNumber()
                    )
            );
            erpOperateLogService.save(operateLog);
        });
        return scTicket;
    }

    @Override
    public void syncCreatorTrend(String beginTime, String endTime, Integer thread, Integer isContinue) throws InterruptedException {
        List<BatchRange> batchRange = getBatchRange(thread);


        long taskStart = System.currentTimeMillis();


        CountDownLatch downLatch = new CountDownLatch(batchRange.size());
        for (BatchRange range : batchRange) {
            long threadStart = System.currentTimeMillis();
            Thread currentThread = new Thread(() -> {
                try {
                    doSyncCreatorTrend(range, beginTime, endTime);
                } finally {
                    downLatch.countDown();
                    log.info("{} - 执行完毕，耗时:{}", Thread.currentThread().getName(), System.currentTimeMillis() - threadStart);
                }
            }, String.format("达人趋势线程[%s - %s]", range.getStartId(), range.getEndId()));
            // 执行
            currentThread.start();
        }
        downLatch.await();


        log.info("达人趋势数据同步 - 任务全部执行完毕，耗时：{}", System.currentTimeMillis() - taskStart);

    }

    void doSyncCreatorTrend(BatchRange range, String beginTime, String endTime) {
        TkDataExpert expert = null;
        Long cursor = range.getStartId() - 1;
        while (((expert = this.selectNextVideo(cursor)) != null) && cursor <= range.getEndId()) {
            cursor = expert.getId();
            try {
                requestAndHandlCreatorTrend(beginTime, endTime, expert);
            } catch (Exception e) {
                log.error("达人趋势数据同步 - 任务：{} - {} 达人趋势数据异常:", beginTime,endTime, e);
            }
        }
    }

    public void syncCreatorTrendbak(String beginTime, String endTime, Integer thread, Integer isContinue) {

        thread = thread < 1 ? 1 : thread > 3 ? 3 : thread;
        log.info("达人趋势数据同步 - 开始时间：{}  结束时间：{} 线程数：{} 是否继续：{}", beginTime, endTime, thread, isContinue);
        String key = RedisCons.CREATOR_BASIC_MARK + "-" + thread;
        boolean flag = Objects.nonNull(isContinue) && Objects.equals(isContinue, 1);

        // 查询当前最小ID
        TkDataExpert minExpert = this.lambdaQuery().orderByAsc(TkDataExpert::getId).last("limit 1").select(TkDataExpert::getId).one();
        // 查询当前最大ID
        TkDataExpert maxExpert = this.lambdaQuery().orderByDesc(TkDataExpert::getId).last("limit 1").select(TkDataExpert::getId).one();
        Long minId = minExpert.getId();
//        Long minId = minVideo.getId();
        Long maxId = maxExpert.getId();
        long diff = maxId - minId;
        long every = diff / thread;
        log.info("达人趋势数据同步 - 任务拆分：{}", every);
        List<TkDataExpert> experts;
        long start = System.currentTimeMillis();

        List<Integer> threadNo = new ArrayList<>();
        for (Integer i = 0; i < thread; i++) {
            threadNo.add(i);
        }

        List<Long> finallyCursor = threadNo.parallelStream()
                .map(c -> {
                    try {
                        Long cursor = minId + c * every;
                        Long maxCursor = cursor + every;

                        String currentKey = key + c;
                        log.info("达人趋势数据同步 - 任务 {} ，开始游标 ：{} 结束游标 ：{}", c, cursor, maxCursor);

                        try {
                            if (flag && stringRedisTemplate.hasKey(currentKey)) {
                                String lastCursor = stringRedisTemplate.opsForValue().get(currentKey);
                                log.info("达人趋势数据同步 - 任务：{} 重置开始游标：{} -> {}", c, cursor, lastCursor);
                                cursor = Long.parseLong(lastCursor);
                            }
                        } catch (Exception e) {
                            log.error("达人趋势数据同步 - 任务:{} 异常：", c, e);
                        }
                        TkDataExpert expert;
                        while ((expert = this.selectNextVideo(cursor)) != null) {
                            cursor = expert.getId();
                            try {
                                requestAndHandlCreatorTrend(beginTime, endTime, expert);
                                stringRedisTemplate.opsForValue().set(currentKey, String.valueOf(cursor));
                            } catch (Exception e) {
                                log.error("达人趋势数据同步 - 任务：{} 达人趋势数据异常:", c, e);
                            }
                            if (cursor >= maxCursor) {
                                break;
                            }
                        }
                        log.info("达人趋势数据同步 - 任务 :{} ，任务结束  结束游标 ：{} =======", c, cursor);
                        return cursor;
                    } catch (Exception e) {
                        log.error("达人趋势数据同步 - 任务 {} 执行错误：{}", c, e.getMessage(), e);
                    }
                    return -1L;
                })
                .collect(Collectors.toList());
        long end = System.currentTimeMillis();
        log.info("达人趋势数据同步 - 任务结束游标：{}", finallyCursor);
        log.info("达人趋势数据同步 - 任务全部执行完毕，耗时：{}", end - start);

    }

    @Override
    public void syncCreatorCid() {
        List<TkDataExpert> experts = this.lambdaQuery()
                .select(TkDataExpert::getCreatorId)
                .isNull(TkDataExpert::getCreatorCid)
                .list();
        if (CollectionUtil.isEmpty(experts)) {
            log.info("CreatorCidJob 未获取到获取Cid的达人信息");
            return;
        }
        List<String> creatorIds = experts.stream()
                .filter(c -> StrUtil.isNotBlank(c.getCreatorId()))
                .map(TkDataExpert::getCreatorId)
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(creatorIds)) {
            log.info("CreatorCidJob 未获取到获取Cid的达人creatorId");
            return;
        }

        log.info("CreatorCidJob 获取达人Cid，条目：{}", creatorIds.size());

        List<List<String>> lists = CollectionUtil.split(creatorIds, 5);

        for (List<String> creatorIdMsg : lists) {
            rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_E_ERP, MQDefine.TK_CREATOR_CID_REQ_ROUTING_KEY, creatorIdMsg);
        }
    }

    @Override
    public TkDataExpert selectNextVideo(Long cursor) {
        return this.lambdaQuery()
                .gt(TkDataExpert::getId, cursor)
                .orderByAsc(TkDataExpert::getId)
                .last("limit 1")
                .one();
    }
    public TkDataExpert selectNextCreator(Long cursor) {
        return this.lambdaQuery()
                .gt(TkDataExpert::getId, cursor)
                .orderByAsc(TkDataExpert::getId)
                .last("limit 1")
                .one();
    }

    @Override
    public List<TkDataExpert> selectExpertLimitAndEnd(Long cursor, Integer limit, Long endCursor) {
        return this.lambdaQuery()
                .gt(TkDataExpert::getId, cursor)
                .le(TkDataExpert::getId, endCursor)
                .last("limit " + limit)
                .list();
    }

    @Override
    public void computeCreatorDayKpi(String beginDate, String endDate, Integer thread, Integer isContinue) throws InterruptedException {

        List<BatchRange> batchRange = getBatchRange(thread);
        long taskStart = System.currentTimeMillis();
        CountDownLatch downLatch = new CountDownLatch(batchRange.size());
        for (BatchRange range : batchRange) {
            long threadStart = System.currentTimeMillis();
            Thread currentThread = new Thread(() -> {

                try {
                    doComputeCreatorDayKpi(beginDate, endDate, range, threadStart);
                } finally {
                    downLatch.countDown();
                    log.info("{} - 执行完毕，耗时：{}", Thread.currentThread().getName(), System.currentTimeMillis() - taskStart);
                }
            }, String.format("达人日指标计算线程[%s - %s]", range.getStartId(), range.getEndId()));
            currentThread.start();

        }
        downLatch.await();
        log.info("达人日指标计算 - 任务全部执行完毕，耗时：{}", System.currentTimeMillis() - taskStart);

    }

    private void doComputeCreatorDayKpi(String beginDate, String endDate, BatchRange range, long threadStart) {
        Long cursor = range.getStartId() - 1;
        TkDataExpert expert = null;
        while ((expert = this.selectNextCreator(cursor)) != null && cursor <= range.getEndId()) {
            cursor = expert.getId();
            try {
                computeCreatorDayKpi(expert, beginDate, endDate);
            } catch (Exception e) {
                log.info("{} - 执行完毕，耗时:{}", Thread.currentThread().getName(), System.currentTimeMillis() - threadStart);
            }
        }
    }

    public void computeCreatorDayKpiBak(String beginDate, String endDate, Integer thread, Integer isContinue) {

        thread = thread < 1 ? 1 : thread > 3 ? 3 : thread;

        String key = RedisCons.COMPUTE_DAY_API + "-" + thread;

        log.info("达人每日指标计算 - 开始日期：{} 结束日期：{} 线程数：{} 是否继续：{}", beginDate, endDate, thread, isContinue);

        boolean flag = Objects.nonNull(isContinue) && Objects.equals(isContinue, 1);

        // 查询当前最小ID
        TkDataExpert minExpert = this.lambdaQuery().orderByAsc(TkDataExpert::getId).last("limit 1").select(TkDataExpert::getId).one();
        // 查询当前最大ID
        TkDataExpert maxExpert = this.lambdaQuery().orderByDesc(TkDataExpert::getId).last("limit 1").select(TkDataExpert::getId).one();
        Long minId = minExpert.getId();
//        Long minId = minVideo.getId();
        Long maxId = maxExpert.getId();
        long diff = maxId - minId;
        long every = diff / thread;
        log.info("达人每日指标计算 - 任务拆分：{}", every);
        List<TkDataExpert> experts;
        long start = System.currentTimeMillis();
        List<Integer> threadNo = new ArrayList<>();
        for (Integer i = 0; i < thread; i++) {
            threadNo.add(i);
        }
        List<Long> finallyCursor = threadNo.parallelStream()
                .map(c -> {
                    try {
                        Long cursor = minId + c * every;
                        Long maxCursor = cursor + every;
                        log.info("达人每日指标计算 - 任务 {} ，开始游标 ：{} 结束游标 ：{}", c, cursor, maxCursor);
                        String currentKey = key + c;
                        try {
                            if (flag && stringRedisTemplate.hasKey(currentKey)) {
                                String lastCursor = stringRedisTemplate.opsForValue().get(currentKey);
                                log.info("达人每日指标计算 - 任务：{} 重置游标：{} -> {}", c, cursor, lastCursor);
                                cursor = Long.parseLong(lastCursor);
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage());
                        }
                        TkDataExpert expert;
                        while ((expert = this.selectNextVideo(cursor)) != null) {
                            cursor = expert.getId();
                            try {
                                computeCreatorDayKpi(expert, beginDate, endDate);
                                stringRedisTemplate.opsForValue().set(currentKey, String.valueOf(cursor));
                            } catch (Exception e) {

                            }

                            if (cursor >= maxCursor) {
                                break;
                            }
                        }
                        log.info("达人每日指标计算 - 任务 :{} ，任务结束  结束游标 ：{} =======", c, cursor);
                        return cursor;
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("达人每日指标计算 - 任务 {} 执行错误：{}", c, e.getMessage());
                    }
                    return -1L;
                })
                .collect(Collectors.toList());
        long end = System.currentTimeMillis();
        log.info("达人每日指标计算 - 任务结束游标：{}", finallyCursor);
        log.info("达人每日指标计算 - 任务全部执行完毕，耗时：{}", end - start);
    }

    @Override
    public void sampleStatusSet() {
        List<TkDataExpert> experts;
        Long curosr = 0L;
        while (CollectionUtil.isNotEmpty((experts = selectExpertByCursorIdAndLimit(curosr, 300,"id","creator_cid","creator_id")))) {
            curosr = experts.get(experts.size() - 1).getId();

            // 直接更新无Cid的达人

            Map<Boolean, List<TkDataExpert>> hasCidMap = experts.stream().collect(Collectors.groupingBy(c -> StrUtil.isNotBlank(c.getCreatorCid())));

            if (hasCidMap.containsKey(Boolean.FALSE)) {
                List<TkDataExpert> dataExperts = hasCidMap.get(Boolean.FALSE);
                dataExperts.forEach(c-> c.setSampleCondition(SampleStatus.NO_SAMPLE_ORDER.getCode()));
                updateBatchById(dataExperts);

            }

            experts = hasCidMap.get(Boolean.TRUE);
            if (CollectionUtil.isEmpty(experts)) {
                continue;
            }

            List<String> creatorCids = experts.stream()
                    .map(TkDataExpert::getCreatorCid)
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(creatorCids)) {
                continue;
            }
            List<CreatorRelationSampleVO> sampleVOS = saleOrdersMapper.selectCreatorSampleOrderByCreatorIds(creatorCids);
            if (CollectionUtil.isEmpty(sampleVOS)) {
                experts.forEach(c -> c.setSampleCondition(SampleStatus.NO_SAMPLE_ORDER.getCode()));
                this.updateBatchById(experts);
                continue;
            }


            Map<String, List<CreatorRelationSampleVO>> sampleMap = sampleVOS.stream()
                    .collect(Collectors.groupingBy(CreatorRelationSampleVO::getCreatorCid));

            Map<Boolean, List<TkDataExpert>> cidHasOrderMap = experts.stream().collect(Collectors.groupingBy(c -> sampleMap.containsKey(c.getCreatorCid())));


            if (cidHasOrderMap.containsKey(Boolean.FALSE)) {
                List<TkDataExpert> noOrderExpert = cidHasOrderMap.get(Boolean.FALSE);
                noOrderExpert.forEach(c -> c.setSampleCondition(SampleStatus.NO_SAMPLE_ORDER.getCode()));
                updateBatchById(noOrderExpert);
            }


            experts = cidHasOrderMap.get(Boolean.TRUE);

            List<String> creatorIds = experts.stream().map(TkDataExpert::getCreatorId).collect(Collectors.toList());

            List<String> itemIds = sampleVOS.stream()
                    .map(CreatorRelationSampleVO::getItemId)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());
            if (CollectionUtil.isEmpty(itemIds)) {
                log.error("更新达人样品情况 - 样品订单未获取到 itemId :{}", creatorCids);
                continue;
            }

            // 默认全部有视频
            sampleVOS.forEach(c -> c.setSampleStatus(SampleStatus.ALL_WITH_VIDEO.getCode()));

            List<TabcutCreatorVideo> creatorVideos = tabcutCreatorVideoService.lambdaQuery()
                    .in(TabcutCreatorVideo::getItemId, itemIds)
                    .in(TabcutCreatorVideo::getCreatorId, creatorIds)
                    .select(TabcutCreatorVideo::getVideoId,TabcutCreatorVideo::getCreatorId)
                    .list();

            if (CollectionUtil.isEmpty(creatorVideos)) {
                this.lambdaUpdate()
                        .in(TkDataExpert::getCreatorCid)
                        .set(TkDataExpert::getSampleCondition, SampleStatus.NO_VIDEO_IS_PRESENT.getCode())
                        .update();
                continue;
            }

            Map<String, TabcutCreatorVideo> videoMap = creatorVideos.stream().collect(Collectors.toMap(TabcutCreatorVideo::getItemId, Function.identity(), (a, b) -> a));


            List<String> hasVideoCreatorIds = creatorVideos.stream()
                    .map(TabcutCreatorVideo::getCreatorId)
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(hasVideoCreatorIds)) {
                this.lambdaUpdate()
                        .in(TkDataExpert::getCreatorId, hasVideoCreatorIds)
                        .set(TkDataExpert::getSampleCondition, SampleStatus.ALL_WITH_VIDEO.getCode())
                        .update();
                continue;
            }

            // 更新状态
            for (CreatorRelationSampleVO vo : sampleVOS) {
                String itemId = vo.getItemId();
                if (!videoMap.containsKey(itemId)) {
                    vo.setSampleStatus(SampleStatus.NO_VIDEO_IS_PRESENT.getCode());
                }
            }

            Map<Integer, List<CreatorRelationSampleVO>> map = sampleVOS.stream().collect(Collectors.groupingBy(CreatorRelationSampleVO::getSampleStatus));

            for (Map.Entry<Integer, List<CreatorRelationSampleVO>> entry : map.entrySet()) {
                Integer sampleStatus = entry.getKey();

                List<String> cids = entry.getValue().stream().map(CreatorRelationSampleVO::getCreatorCid).collect(Collectors.toList());

                this.lambdaUpdate()
                        .in(TkDataExpert::getCreatorCid, cids)
                        .set(TkDataExpert::getSampleCondition, sampleStatus)
                        .update();
            }

        }
    }

    @Override
    public void bringGoodsStatusSet() {
        Long cursor = 0L;
        List<TkDataExpert> experts;
        log.info("达人是否带货 - ");
        while (CollectionUtil.isNotEmpty((experts = selectExpertByCursorIdAndLimit(cursor, 500, "id", "creator_id")))) {
            cursor = experts.get(experts.size() - 1).getId();

            List<String> creatorIds = experts.stream()
                    .map(TkDataExpert::getCreatorId)
                    .collect(Collectors.toList());

            // 查询视频信息
            List<TabcutCreatorVideo> creatorVideos = tabcutCreatorVideoService.lambdaQuery()
                    .in(TabcutCreatorVideo::getCreatorId, creatorIds)
                    .select(TabcutCreatorVideo::getCreatorId, TabcutCreatorVideo::getItemId)
                    .list();

            if (CollectionUtil.isEmpty(creatorVideos)) {
                this.lambdaUpdate()
                        .in(TkDataExpert::getCreatorId, creatorIds)
                        .set(TkDataExpert::getIsBringGoods, 0)
                        .update();
                continue;
            }

            Map<String, List<TabcutCreatorVideo>> map = creatorVideos.stream()
                    .collect(Collectors.groupingBy(TabcutCreatorVideo::getCreatorId));


            Map<Boolean, List<TkDataExpert>> bringMap = experts.stream()
                    .collect(Collectors.groupingBy(c -> map.containsKey(c.getCreatorId())));

            if (bringMap.containsKey(Boolean.FALSE)) {

                List<TkDataExpert> dataExperts = bringMap.get(Boolean.FALSE);

                List<Long> noBringGoods = dataExperts.stream()
                        .map(TkDataExpert::getId)
                        .collect(Collectors.toList());

                this.lambdaUpdate()
                        .in(TkDataExpert::getId, noBringGoods)
                        .set(TkDataExpert::getIsBringGoods, 0)
                        .update();
            }

            if (bringMap.containsKey(Boolean.TRUE)) {
                List<TkDataExpert> dataExperts = bringMap.get(Boolean.TRUE);

                for (TkDataExpert expert : dataExperts) {
                    List<TabcutCreatorVideo> tabcutCreatorVideos = map.get(expert.getCreatorId());
                    List<TabcutCreatorVideo> videos = tabcutCreatorVideos.stream()
                            .filter(c -> StrUtil.isNotBlank(c.getItemId()))
                            .collect(Collectors.toList());

                    if (CollectionUtil.isEmpty(videos)) {
                        expert.setIsBringGoods(0);
                    } else {
                        expert.setIsBringGoods(1);
                    }

                }

                this.updateBatchById(dataExperts);

            }




        }
    }

    @Override
    public void syncCreatorTrendByCreator(String beginTime, String endTime, String creatorId) {
        log.info("指定同步达人趋势数据 - {}", creatorId);
        TkDataExpert expert = this.lambdaQuery()
                .eq(TkDataExpert::getCreatorId, creatorId)
                .one();
        if (Objects.isNull(expert)) {
            log.error("指定同步达人趋势数据 - 为获取到达人视频信息");
            return;
        }
        requestAndHandlCreatorTrend(beginTime, endTime, expert);
    }

    @Override
    public void computeCreatorDayKpiByCreatorId(String beginDate, String endDate, Integer creatorId) {
        log.info("指定计算达人单日指标数据 - {}", creatorId);
        TkDataExpert expert = this.lambdaQuery()
                .eq(TkDataExpert::getCreatorId, creatorId)
                .one();
        if (Objects.isNull(expert)) {
            log.error("指定计算达人单日指标数据 - 为获取到达人视频信息");
            return;
        }
        computeCreatorDayKpi(expert, beginDate, endDate);
    }

    @Override
    public void execSyncTask() {
//        Date now = DateUtils.getNowDate();
//        String today = DateUtils.formatDate(now, "yyyyMMdd");
//        long start = System.currentTimeMillis();
//        log.info("特看数据同步 - 同步达人视频信息，今日时间：{}", today);
//        try {
//            tabcutCreatorVideoKpiService.syncCreatorVideo(2, 0);
//        } catch (InterruptedException e) {
//
//        }
//        log.info("特看数据同步 - 同步达人视频信息，耗时：{}", System.currentTimeMillis() - start);
//
//        String beginDate = DateUtils.formatDate(com.bizark.op.common.util.DateUtil.addDate(now, -1));
//        //  开始同步视频趋势
//        CompletableFuture<Void> f1 = CompletableFuture.runAsync(() -> {
//
//            Long startTimeMillis = System.currentTimeMillis();
//            // 设置更新最后一天视频主信息
//            log.info("特看数据同步 - 开始同步视频趋势 开始日期：{} 结束日期：{}", beginDate, today);
//            tabcutCreatorVideoService.syncVideoTrendLimit(1, beginDate, today, 0, true);
//            log.info("特看数据同步 - 同步视频趋势执行完毕 耗时：{}", System.currentTimeMillis() - startTimeMillis);
//
//        }, threadPoolTaskExecutor).exceptionally(e->{
//            log.error("特看数据同步 - 同步视频趋势异常：{}", e.getMessage(), e);
//            return null;
//        });
//
//        CompletableFuture<Void> f2 = CompletableFuture.runAsync(() -> {
//            Long startTimeMillis = System.currentTimeMillis();
//            //  开始同时抓取达人趋势数据
//            log.info("特看数据同步 - 开始同步达人趋势 开始日期：{} 结束日期：{} ", beginDate, today);
//            this.syncCreatorTrend(beginDate, today, 1, 0);
//            log.info("特看数据同步 - 达人趋势数据同步完毕 耗时：{}", System.currentTimeMillis() - startTimeMillis);
//        }, threadPoolTaskExecutor).exceptionally(e -> {
//            log.error("特看数据同步 - 同步达人趋势异常：{}", e.getMessage(), e);
//            return null;
//        });
//
//        try {
//            CompletableFuture.allOf(f1, f2).get(5, TimeUnit.HOURS);
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("特看数据同步执行异常：{}", e.getMessage(), e);
//        }
//        // 开始计算达人趋势
//        long startTimeMillis = System.currentTimeMillis();
//        log.info("特看数据同步 - 开始计算达人趋势 开始日期：{} 结束日期：{} ", beginDate, today);
//        this.computeCreatorDayKpi(beginDate, today, 1, 0);
//        log.info("特看数据同步 - 达人趋势计算完毕，耗时：{}  ", System.currentTimeMillis() - startTimeMillis);
    }



    @Override
    public void updateSampleOrderNo() {
        List<TkDataExpert> experts = this.lambdaQuery()
                .isNotNull(TkDataExpert::getCreatorCid)
                .select(TkDataExpert::getId, TkDataExpert::getCreatorCid)
                .list();

        if (CollectionUtil.isEmpty(experts)) {
            return;
        }
        for (TkDataExpert expert : experts) {
            SaleOrders orders = saleOrdersMapper.selectSampleOrderByCreatorCid(expert.getCreatorCid());
            if (Objects.nonNull(orders)) {
                expert.setSampleOrderNo(orders.getOrderNo());
                this.lambdaUpdate()
                        .eq(TkDataExpert::getId, expert.getId())
                        .set(TkDataExpert::getSampleOrderNo, orders.getOrderNo())
                        .update();
            }

        }

    }

    private void writeCreatorVideo(List<TabcutCsvData> result, TkDataExpert expert) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<TabcutCsvData> distinct = result.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(
                                        TabcutCsvData::getVideoId
                                )
                        ))
                        , ArrayList::new));


    }


    // 计算达人指标情况
    @SneakyThrows
    private void computeCreatorDayKpi(TkDataExpert expert, String beginDate, String endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date begin = DateUtils.addDays(sdf.parse(beginDate), -1);
        Date end = sdf.parse(endDate);
        while ((begin = DateUtils.addDays(begin, 1)).compareTo(end) <= 0) {
            String bizDate = sdf.format(begin);
            TabcutCreatorDayKpi dayKpi = tabcutCreatorVideoMapper.selectExpertDayKpi(expert.getCreatorId(), bizDate);
            if (Objects.isNull(dayKpi)) {
                continue;
            }
            dayKpi.setPlayCount(null);
            dayKpi.setLikeCount(null);
            dayKpi.setCommentCount(null);
            dayKpi.setShareCount(null);
            dayKpi.setGmv(null);

            dayKpi.setPlayCount(null);
            dayKpi.setExpertId(expert.getId());
            dayKpi.setOrgId(expert.getContextId());
            TabcutCreatorDayKpi kpi = tabcutCreatorDayKpiService.lambdaQuery()
                    .eq(TabcutCreatorDayKpi::getBizDate, dayKpi.getBizDate())
                    .eq(TabcutCreatorDayKpi::getCreatorId, expert.getCreatorId())
                    .one();

            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String beginRelease = simpleDateFormat.format(begin);
            String endRelease = beginRelease + " 23:59:59";
            beginRelease += " 00:00:00";

            // 查询当前自由视频数量
            List<TabcutCreatorVideo> creatorVideos = tabcutCreatorVideoService.lambdaQuery()
                    .eq(TabcutCreatorVideo::getCreatorId, expert.getCreatorId())
                    .ge(TabcutCreatorVideo::getVideoReleaseDate, beginRelease)
                    .le(TabcutCreatorVideo::getVideoReleaseDate, endRelease)
                    .isNotNull(TabcutCreatorVideo::getErpSku)
                    .list();


            dayKpi.setOwnItemVideoCount(CollectionUtil.isEmpty(creatorVideos) ? 0L : creatorVideos.size());
            dayKpi.setOwnVideoCount(CollectionUtil.isEmpty(creatorVideos) ? 0L : creatorVideos.size());


            if (Objects.nonNull(kpi)) {
                dayKpi.setId(kpi.getId());
                tabcutCreatorDayKpiService.updateById(dayKpi);
                continue;
            }
            tabcutCreatorDayKpiService.save(dayKpi);


        }

    }


    private void creatorInfoSetting(TabcutCreatorDayKpi dayKpi, TkDataExpert expert) {
        dayKpi.setCreatorId(expert.getCreatorId());
        dayKpi.setOrgId(expert.getContextId());
        dayKpi.setExpertId(expert.getId());
    }

    private void requestAndHandlCreatorTrend(String beginTime, String endTime, TkDataExpert expert) {
        TabcutCreatorTrendRequest request = new TabcutCreatorTrendRequest();
        request.setUid(expert.getCreatorId());
        request.setStartDate(beginTime);
        request.setEndDate(endTime);
        TabcutCreatorTrendResponse trendResponse = tabcutApi.tabcutRequest(TabcutUrl.ITEM_CREATOR_TREND, request, new TypeReference<TabcutCreatorTrendResponse>() {
        });

        if (Objects.isNull(trendResponse) || !Objects.equals(trendResponse.getCode(), "200")) {
            return;
        }

        TabcutCreatorTrendResponse.CreatorTrendResult result = trendResponse.getResult();
        if (Objects.isNull(result) || StrUtil.isBlank(result.getUid())) {
            return;
        }

        Map<String, TabcutCreatorDayKpi> dayKpiMap = getDayKpiMap(expert, result);
        if (CollectionUtil.isEmpty(dayKpiMap)) {
            return;
        }

        Set<String> bizDates = dayKpiMap.keySet();

        List<TabcutCreatorDayKpi> dbKpis = tabcutCreatorDayKpiService.lambdaQuery()
                .eq(TabcutCreatorDayKpi::getCreatorId, expert.getCreatorId())
                .in(TabcutCreatorDayKpi::getBizDate, bizDates)
                .list();

        if (CollectionUtil.isEmpty(dbKpis)) {
            tabcutCreatorDayKpiService.saveBatch(dayKpiMap.values());
            return;
        }
        Map<String, TabcutCreatorDayKpi> kpiMap = dbKpis.stream()
                .collect(Collectors.toMap(TabcutCreatorDayKpi::getBizDate, Function.identity()));

        Collection<TabcutCreatorDayKpi> creatorDayKpis = dayKpiMap.values();


        List<TabcutCreatorDayKpi> insertData = new ArrayList<>();
        List<TabcutCreatorDayKpi> updateData = new ArrayList<>();

        for (TabcutCreatorDayKpi dayKpi : creatorDayKpis) {
            if (kpiMap.containsKey(dayKpi.getBizDate())) {
                TabcutCreatorDayKpi kpi = kpiMap.get(dayKpi.getBizDate());
                dayKpi.setId(kpi.getId());
                updateData.add(dayKpi);
            } else {
                insertData.add(dayKpi);
            }
        }

        if (CollectionUtil.isNotEmpty(insertData)) {
            tabcutCreatorDayKpiService.saveBatch(insertData);
        }
        if (CollectionUtil.isNotEmpty(updateData)) {
            tabcutCreatorDayKpiService.updateBatchById(updateData);
        }

    }

    private Map<String, TabcutCreatorDayKpi> getDayKpiMap(TkDataExpert expert, TabcutCreatorTrendResponse.CreatorTrendResult result) {
        Map<String, TabcutCreatorDayKpi> kpiMap = new HashMap<>();

        List<TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo> playCount = result.getPlayCount();
        for (TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo info : playCount) {
            if (!kpiMap.containsKey(info.getDate())) {
                TabcutCreatorDayKpi dayKpi = new TabcutCreatorDayKpi();
                dayKpi.setBizDate(info.getDate());
                kpiMap.put(info.getDate(), dayKpi);
            }
            TabcutCreatorDayKpi dayKpi = kpiMap.get(info.getDate());
            creatorInfoSetting(dayKpi, expert);
//            dayKpi.setPlayCountIncr(info.getScore());
            dayKpi.setPlayCount(info.getScore());
        }

        List<TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo> likeCount = result.getLikeCount();
        for (TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo info : likeCount) {
            if (!kpiMap.containsKey(info.getDate())) {
                TabcutCreatorDayKpi dayKpi = new TabcutCreatorDayKpi();
                dayKpi.setBizDate(info.getDate());
                kpiMap.put(info.getDate(), dayKpi);
            }
            TabcutCreatorDayKpi dayKpi = kpiMap.get(info.getDate());
            creatorInfoSetting(dayKpi, expert);
            dayKpi.setLikeCount(info.getScore());

        }

        List<TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo> shareCount = result.getShareCount();

        for (TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo info : shareCount) {
            if (!kpiMap.containsKey(info.getDate())) {
                TabcutCreatorDayKpi dayKpi = new TabcutCreatorDayKpi();
                dayKpi.setBizDate(info.getDate());
                kpiMap.put(info.getDate(), dayKpi);
            }
            TabcutCreatorDayKpi dayKpi = kpiMap.get(info.getDate());
            creatorInfoSetting(dayKpi, expert);
            dayKpi.setShareCount(info.getScore());

        }

        List<TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo> commentCount = result.getCommentCount();
        for (TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo info : commentCount) {
            if (!kpiMap.containsKey(info.getDate())) {
                TabcutCreatorDayKpi dayKpi = new TabcutCreatorDayKpi();
                dayKpi.setBizDate(info.getDate());
                kpiMap.put(info.getDate(), dayKpi);
            }
            TabcutCreatorDayKpi dayKpi = kpiMap.get(info.getDate());
            creatorInfoSetting(dayKpi, expert);
            dayKpi.setCommentCount(info.getScore());

        }


        List<TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo> videoCount = result.getVideoCount();
        for (TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo info : videoCount) {
            if (!kpiMap.containsKey(info.getDate())) {
                TabcutCreatorDayKpi dayKpi = new TabcutCreatorDayKpi();
                dayKpi.setBizDate(info.getDate());
                kpiMap.put(info.getDate(), dayKpi);
            }
            TabcutCreatorDayKpi dayKpi = kpiMap.get(info.getDate());
            creatorInfoSetting(dayKpi, expert);
            dayKpi.setVideoCount(info.getScore());

        }


        List<TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo> soldCount = result.getSoldCount();
        for (TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo info : soldCount) {
            if (!kpiMap.containsKey(info.getDate())) {
                TabcutCreatorDayKpi dayKpi = new TabcutCreatorDayKpi();
                kpiMap.put(info.getDate(), dayKpi);
            }
            TabcutCreatorDayKpi dayKpi = kpiMap.get(info.getDate());
            dayKpi.setBizDate(info.getDate());
            creatorInfoSetting(dayKpi, expert);
            dayKpi.setSoldCount(info.getScore());

        }

        List<TabcutCreatorTrendResponse.CreatorTrendResult.DecimalInfo> localGmv = result.getLocalGmv();
        for (TabcutCreatorTrendResponse.CreatorTrendResult.DecimalInfo info : localGmv) {
            if (!kpiMap.containsKey(info.getDate())) {
                TabcutCreatorDayKpi dayKpi = new TabcutCreatorDayKpi();
                dayKpi.setBizDate(info.getDate());
                kpiMap.put(info.getDate(), dayKpi);
            }
            TabcutCreatorDayKpi dayKpi = kpiMap.get(info.getDate());
            creatorInfoSetting(dayKpi, expert);
            dayKpi.setGmv(info.getScore());

        }


        List<TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo> itemVideoCount = result.getItemVideoCount();
        for (TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo info : itemVideoCount) {
            if (!kpiMap.containsKey(info.getDate())) {
                TabcutCreatorDayKpi dayKpi = new TabcutCreatorDayKpi();
                dayKpi.setBizDate(info.getDate());
                kpiMap.put(info.getDate(), dayKpi);
            }
            TabcutCreatorDayKpi dayKpi = kpiMap.get(info.getDate());
            creatorInfoSetting(dayKpi, expert);
            dayKpi.setItemVideoCount(info.getScore());

        }


        List<TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo> followerCount = result.getFollowerCount();
        for (TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo info : followerCount) {
            if (!kpiMap.containsKey(info.getDate())) {
                TabcutCreatorDayKpi dayKpi = new TabcutCreatorDayKpi();
                dayKpi.setBizDate(info.getDate());
                kpiMap.put(info.getDate(), dayKpi);
            }
            TabcutCreatorDayKpi dayKpi = kpiMap.get(info.getDate());
            creatorInfoSetting(dayKpi, expert);
            dayKpi.setFansIncr(info.getScore());

        }


        List<TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo> itemSoldCount = result.getItemSoldCount();

        for (TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo info : itemSoldCount) {
            if (!kpiMap.containsKey(info.getDate())) {
                TabcutCreatorDayKpi dayKpi = new TabcutCreatorDayKpi();
                dayKpi.setBizDate(info.getDate());
                kpiMap.put(info.getDate(), dayKpi);
            }
            TabcutCreatorDayKpi dayKpi = kpiMap.get(info.getDate());
            creatorInfoSetting(dayKpi, expert);
            dayKpi.setSoldCount(info.getScore());
        }


        List<TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo> totalPlayCount = result.getTotalPlayCount();
        for (TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo info : totalPlayCount) {
            if (!kpiMap.containsKey(info.getDate())) {
                TabcutCreatorDayKpi dayKpi = new TabcutCreatorDayKpi();
                dayKpi.setBizDate(info.getDate());
                kpiMap.put(info.getDate(), dayKpi);
            }
            TabcutCreatorDayKpi dayKpi = kpiMap.get(info.getDate());
            creatorInfoSetting(dayKpi, expert);
            dayKpi.setTotalPlayCount(info.getScore());

        }

        List<TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo> totalLikeCount = result.getTotalLikeCount();

        for (TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo info : totalLikeCount) {
            if (!kpiMap.containsKey(info.getDate())) {
                TabcutCreatorDayKpi dayKpi = new TabcutCreatorDayKpi();
                dayKpi.setBizDate(info.getDate());
                kpiMap.put(info.getDate(), dayKpi);
            }
            TabcutCreatorDayKpi dayKpi = kpiMap.get(info.getDate());
            creatorInfoSetting(dayKpi, expert);
            dayKpi.setTotalLikeCount(info.getScore());

        }

        List<TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo> totalShareCount = result.getTotalShareCount();
        for (TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo info : totalShareCount) {
            if (!kpiMap.containsKey(info.getDate())) {
                TabcutCreatorDayKpi dayKpi = new TabcutCreatorDayKpi();
                dayKpi.setBizDate(info.getDate());
                kpiMap.put(info.getDate(), dayKpi);
            }
            TabcutCreatorDayKpi dayKpi = kpiMap.get(info.getDate());
            creatorInfoSetting(dayKpi, expert);
            dayKpi.setTotalShareCount(info.getScore());

        }


        List<TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo> totalCommentCount = result.getTotalCommentCount();
        for (TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo info : totalCommentCount) {
            if (!kpiMap.containsKey(info.getDate())) {
                TabcutCreatorDayKpi dayKpi = new TabcutCreatorDayKpi();
                dayKpi.setBizDate(info.getDate());
                kpiMap.put(info.getDate(), dayKpi);
            }
            TabcutCreatorDayKpi dayKpi = kpiMap.get(info.getDate());
            creatorInfoSetting(dayKpi, expert);
            dayKpi.setTotalCommentCount(info.getScore());

        }

        List<TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo> totalVideoCount = result.getTotalVideoCount();

        for (TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo info : totalVideoCount) {
            if (!kpiMap.containsKey(info.getDate())) {
                TabcutCreatorDayKpi dayKpi = new TabcutCreatorDayKpi();
                dayKpi.setBizDate(info.getDate());
                kpiMap.put(info.getDate(), dayKpi);
            }
            TabcutCreatorDayKpi dayKpi = kpiMap.get(info.getDate());
            creatorInfoSetting(dayKpi, expert);
            dayKpi.setTotalVideoCount(info.getScore());

        }

        List<TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo> totalSoldCount = result.getTotalSoldCount();
        for (TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo info : totalSoldCount) {
            if (!kpiMap.containsKey(info.getDate())) {
                TabcutCreatorDayKpi dayKpi = new TabcutCreatorDayKpi();
                dayKpi.setBizDate(info.getDate());
                kpiMap.put(info.getDate(), dayKpi);
            }
            TabcutCreatorDayKpi dayKpi = kpiMap.get(info.getDate());
            creatorInfoSetting(dayKpi, expert);
            dayKpi.setTotalSoldCount(info.getScore());
        }

        List<TabcutCreatorTrendResponse.CreatorTrendResult.DecimalInfo> totalLocalGmv = result.getTotalLocalGmv();
        for (TabcutCreatorTrendResponse.CreatorTrendResult.DecimalInfo info : totalLocalGmv) {
            if (!kpiMap.containsKey(info.getDate())) {
                TabcutCreatorDayKpi dayKpi = new TabcutCreatorDayKpi();
                dayKpi.setBizDate(info.getDate());
                kpiMap.put(info.getDate(), dayKpi);
            }
            TabcutCreatorDayKpi dayKpi = kpiMap.get(info.getDate());
            creatorInfoSetting(dayKpi, expert);
            dayKpi.setTotalGmv(info.getScore());

        }

        List<TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo> totalItemVideoCount = result.getTotalItemVideoCount();
        for (TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo info : totalItemVideoCount) {
            if (!kpiMap.containsKey(info.getDate())) {
                TabcutCreatorDayKpi dayKpi = new TabcutCreatorDayKpi();
                dayKpi.setBizDate(info.getDate());
                kpiMap.put(info.getDate(), dayKpi);
            }
            TabcutCreatorDayKpi dayKpi = kpiMap.get(info.getDate());
            creatorInfoSetting(dayKpi, expert);
            dayKpi.setTotalItemVideoCount(info.getScore());


        }

        List<TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo> totalFollowerCount = result.getTotalFollowerCount();
        for (TabcutCreatorTrendResponse.CreatorTrendResult.NumberInfo info : totalFollowerCount) {
            if (!kpiMap.containsKey(info.getDate())) {
                TabcutCreatorDayKpi dayKpi = new TabcutCreatorDayKpi();
                dayKpi.setBizDate(info.getDate());
                kpiMap.put(info.getDate(), dayKpi);
            }
            TabcutCreatorDayKpi dayKpi = kpiMap.get(info.getDate());
            creatorInfoSetting(dayKpi, expert);
            dayKpi.setFansCount(info.getScore());
        }

        return kpiMap;
    }



    @Override
    @SneakyThrows
    public void handleCsvData() {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        tkExpertMapper.streamCsvQuery(resultContext->{


            try {
                TkDataExpert expert = resultContext.getResultObject();
                String beginDate = "20230401";
                String endDate = "20231231";
                Date begin = DateUtils.addDays(sdf.parse(beginDate), -1);
                Date end = sdf.parse(endDate);

                List<TabcutCsvData> csvData = tabcutCreatorVideoMapper.selectExpertVideoData(expert.getCreatorId());
                if (CollectionUtil.isNotEmpty(csvData)) {
                    writeCreatorVideo(csvData, expert);
                }

                while ((begin = DateUtils.addDays(begin, 1)).compareTo(end) <= 0) {
                    String bizDate = sdf.format(begin);
                    // 写入达人单日数据
                    TabcutCreatorDayKpi dayKpi = tabcutCreatorVideoMapper.selectCreatorDayKpiFromCsvData(expert.getCreatorId(), bizDate);
                    if (Objects.nonNull(dayKpi)) {
                        dayKpi.setExpertId(expert.getId());
                        dayKpi.setOrgId(expert.getContextId());
                        tabcutCreatorDayKpiService.save(dayKpi);
                    }
                    // 写入视频数据
                    List<TabcutCreatorVideoKpi> videoKpis = tabcutCreatorVideoMapper.selectCreatorVideoFromCsvData(expert.getCreatorId(), bizDate);
                    if (CollectionUtil.isNotEmpty(videoKpis)) {
                        videoKpis.forEach(v-> {
                            v.setOrgId(expert.getContextId());
                            v.setExpertId(expert.getId());
                        });
                        tabcutCreatorVideoKpiService.saveBatch(videoKpis);
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
            }



        });

    }

    @Override
    public List<BatchRange> getBatchRange(int thread) {
        IdRange idRange = idRangeSelect();
        return getBatchRange(thread, idRange);
    }

    @NotNull
    public List<BatchRange> getBatchRange(int thread, IdRange idRange) {
        Long size = idRange.getSize();

        Long avgSize = size / thread;
        // 下一线程起步id
        Long nextStartId = null;

        List<BatchRange> ranges = new ArrayList<>();

        for (int i = 0; i < thread; i++) {
            if (Objects.isNull(nextStartId)) {
                Long currentEndId = idRange.getMinId() + avgSize;
                BatchRange range = new BatchRange(idRange.getMinId(), currentEndId);
                // 标记下一条线程起步ID
                nextStartId = currentEndId + 1;
                ranges.add(range);
                continue;
            }
            if (i == thread - 1) {
                // 最后一条线程的起步线程
                BatchRange range = new BatchRange(nextStartId, idRange.getMaxId());
                ranges.add(range);
                continue;
            }
            BatchRange range = new BatchRange(nextStartId, nextStartId + avgSize);
            nextStartId = range.getEndId() + 1;
            ranges.add(range);
        }
        return ranges;
    }

    public IdRange idRangeSelect(){
        // 查询当前最小ID
        Long minId = tkExpertMapper.selectMinId();
        Long maxId = tkExpertMapper.selectMaxId();


//        TkDataExpert minExpert = tkDataExpertService.lambdaQuery().orderByAsc(TkDataExpert::getId).last("limit 1").select(TkDataExpert::getId).one();
        // 查询当前最大ID
//        TkDataExpert maxExpert = tkDataExpertService.lambdaQuery().orderByDesc(TkDataExpert::getId).last("limit 1").select(TkDataExpert::getId).one();
        return new IdRange(minId, maxId);
    }


}
