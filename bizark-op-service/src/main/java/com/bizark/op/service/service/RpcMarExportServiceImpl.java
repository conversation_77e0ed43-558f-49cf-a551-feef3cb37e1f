package com.bizark.op.service.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.bizark.common.util.LocalDateTimeUtils;
import com.bizark.op.api.entity.op.ExportExcelEntity;
import com.bizark.op.api.entity.op.amazon.fba.DTO.MarStaTaskListQueryDTO;
import com.bizark.op.api.entity.op.document.ConfTicketProblem;
import com.bizark.op.api.entity.op.mar.*;
import com.bizark.op.api.entity.op.returns.StatReturnQuery;
import com.bizark.op.api.entity.op.returns.VO.ReturnAnalyzeVO;
import com.bizark.op.api.entity.op.returns.entity.ReturnInfoEntity;
import com.bizark.op.api.entity.op.sale.Products;
import com.bizark.op.api.entity.op.ticket.ScTicket;
import com.bizark.op.api.entity.op.ticket.VO.*;
import com.bizark.op.api.entity.op.ticket.customercomplaint.VO.CustomerComplaintDetailVO;
import com.bizark.op.api.entity.op.ticket.customercomplaint.VO.CustomerComplaintDimensionTotalTwoVO;
import com.bizark.op.api.entity.op.ticket.customercomplaint.VO.CustomerComplaintDimensionTotalVO;
import com.bizark.op.api.entity.op.ticket.customercomplaint.query.CustomerComplaintMainQuery;
import com.bizark.op.api.entity.op.ticket.vcCustomerComplaint.query.MarVcCustomerComplaintQuery;
import com.bizark.op.api.entity.op.tkpromotion.TkPromotionQueryRequest;
import com.bizark.op.api.entity.op.xsm.XsmReviewCommonDTO;
import com.bizark.op.api.entity.op.xsm.XsmReviewCommonExport;
import com.bizark.op.api.entity.op.xsm.XsmReviewCommonVO;
import com.bizark.op.api.form.customer.*;
import com.bizark.op.api.request.financialConstraints.MarFinancialConstraintsQuery;
import com.bizark.op.api.service.RpcMarExportService;
import com.bizark.op.api.service.amazon.AmazonVineInfoService;
import com.bizark.op.api.service.amazon.fba.MarStaTaskInfoService;
import com.bizark.op.api.service.customer.*;

import com.bizark.op.api.service.mar.*;
import com.bizark.op.api.service.mar.appeal.MarAppealService;
import com.bizark.op.api.service.mar.asinreach.MarAsinRangeInfoService;
import com.bizark.op.api.service.mar.contactCustomer.MarSendMessageOrdersService;
import com.bizark.op.api.service.mar.contactCustomer.MarSendMessageRuleService;
import com.bizark.op.api.service.mar.financialConstraints.MarFinancialConstraintsService;
import com.bizark.op.api.service.mar.material.MarMaterialCenterInfoService;
import com.bizark.op.api.service.mar.material.MarMaterialMakeInfoService;
import com.bizark.op.api.service.promotions.*;
import com.bizark.op.api.service.promotions.fee.IAmzPromotionFeeService;
import com.bizark.op.api.service.refund.IOrderRefundRequestInfoService;
import com.bizark.op.api.service.returns.ReturnAnalyzeService;
import com.bizark.op.api.service.returns.ReturnInfoService;
import com.bizark.op.api.service.sale.ProductChannelsService;
import com.bizark.op.api.service.sale.ProductsService;
import com.bizark.op.api.service.ticket.IScTicketService;
import com.bizark.op.api.service.ticket.ScTicketProblemRateService;
import com.bizark.op.api.service.ticket.ScTicketTagRelationService;
import com.bizark.op.api.service.ticket.customercomplaint.CustomerComplaintDetailService;
import com.bizark.op.api.service.ticket.customercomplaint.CustomerComplaintDimensionTotalService;
import com.bizark.op.api.service.tkpromotion.TkPromotionService;
import com.bizark.op.api.service.xms.XsmReviewCommonService;
import com.bizark.op.api.vo.customer.*;
import com.bizark.op.api.vo.mar.MarProductPopularizeLogSellerSkuVo;
import com.bizark.op.api.vo.mar.MarProductPopularizeLogSkuVo;
import com.bizark.op.api.vo.mar.MarProductPopularizeShopVo;
import com.bizark.op.common.util.AliyunOssClientUtil;
import com.bizark.op.common.util.BeanCopyUtils;
import com.bizark.op.common.util.DictUtils;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.service.util.ConvertUtils;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @ClassName RpcMarExportServiceImpl
 * @description: 营销业务导入Servcie
 * @date 2024年03月15日
 */
@Service
@Slf4j
public class RpcMarExportServiceImpl implements RpcMarExportService {


    @Value("${task.center.file.path}")
    private String filePath;


    @Autowired
    private IMarPlatformPromotionsInfoService marPlatformPromotionsInfoService;

    @Autowired
    private IMarPromotionsReportProductService marPromotionsReportProductService;


    @Autowired
    private MarProductPopularizeLogAsinService marProductPopularizeLogAsinService;

    @Autowired
    private MarProductPopularizeLogSellerSkuService marProductPopularizeLogSellerSkuService;

    @Autowired
    private MarProductPopularizeLogSkuService marProductPopularizeLogSkuService;

    @Autowired
    private MarProductPopularizeShopService marProductPopularizeShopService;

    @Autowired
    private ICusCenterOffsiteService cusCenterOffsiteService;

    @Autowired
    @Lazy
    private ReturnAnalyzeService returnAnalyzeService;

    @Autowired
    private ICusCenterOrderRefundService cusCenterOrderRefundService;

    @Autowired
    private XsmReviewCommonService xsmReviewService;

    @Autowired
    private ProductsService productsService;

    @Autowired
    private ICusCenterFeedbackService cusCenterFeedbackService;

    @Autowired
    private ICusCenterOrderReissueService cusCenterOrderReissueService;

    @Autowired
    private ICusCenterEmailInfoService cusCenterEmailInfoService;

    @Autowired
    private IScTicketService scTicketService;

    @Autowired
    private IAmzPromotionExportService amzPromotionExportService;

    @Autowired
    private CustomerComplaintDetailService customerComplaintDetailService;

    @Autowired
    private CustomerComplaintDimensionTotalService customerComplaintDimensionTotalService;

    @Autowired
    private MarSendMessageOrdersService marSendMessageOrdersService;

    @Autowired
    private MarSendMessageRuleService marSendMessageRuleService;

    @Autowired
    private ICusCenterSaleOrderCancelService cusCenterSaleOrderCancelService;


    @Autowired
    private MarFollowWhiteListService marFollowWhiteListService;

    @Autowired
    private MarFollowSellerInfoService marFollowSellerInfoService;
    @Autowired
    private MarAdjustmentPriceManagementService marAdjustmentPriceManagementService;

    @Autowired
    private ProductChannelsService productChannelsService;

    @Autowired
    private ReturnInfoService returnInfoService;

    @Autowired
    private IOrderRefundRequestInfoService orderRefundRequestInfoService;

    @Autowired
    private IAmzPromotionFeeService amzPromotionFeeService;

    @Autowired
    private MarTemuPromotionService marTemuPromotionService;

    @Autowired
    private MarAppealService marAppealService;

    @Autowired
    private MarShopServiceQualityService marShopServiceQualityService;

    @Autowired
    private MarFinancialConstraintsService marFinancialConstraintsService;

    @Autowired
    private IMarDeliveryFeeClaimService marDeliveryFeeClaimService;

    @Autowired
    private MarTkCouponInfoService marTkCouponInfoService;

    @Autowired
    private MarMaterialCenterInfoService marMaterialCenterInfoService;

    @Autowired
    private MarMaterialMakeInfoService marMaterialMakeInfoService;

    @Autowired
    private MarAsinRangeInfoService marAsinRangeInfoService;


    @Autowired
    private MarCouponInfoService marCouponInfoService;

    @Autowired
    private TemuSupplementaryReportPromotionService temuSupplementaryReportPromotionService;

    @Autowired
    private MarLastMileFeeClaimService marLastMileFeeClaimService;

    @Autowired
    private AmazonVineInfoService amazonVineInfoService;

    @Autowired
    private MarVcCustomerComplaintService marVcCustomerComplaintService;


    @Autowired
    private WalmartShipmentAttachService walmartShipmentAttachService;

    @Autowired
    @Lazy
    private ScTicketTagRelationService scTicketTagRelationService;
    @Autowired
    @Lazy
    private TkPromotionService tkPromotionService;

    @Autowired
    @Lazy
    private ScTicketProblemRateService scTicketProblemRateService;

    @Autowired
    @Lazy
    private MarStaTaskInfoService marStaTaskInfoService;

    /**
     * @description: 父AISN导出
     * @author: Moore
     * @date: 2024/3/15 13:45
     * @param
     * @param param
     * @return: java.lang.String
    **/
    @Override
    public String findMarProductPopularizeLogParentAsinExport(String param) {
        log.info("findMarProductPopularizeLogParentAsinExport start ... ");
        int pageNo = 1;
        int pageSize = 200;
        String path = filePath + "listing列表PARETNASIN维度" + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        MarListingQuery marListingQuery = JSON.parseObject(param, MarListingQuery.class);
        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            Set<String> excludeColumnFieldNames = new HashSet<>();
            excludeColumnFieldNames.add("pstChannelCreatedDate");
            writer = EasyExcel.write(file, MarProductPopularizeLogParentAsinVO.class).excludeColumnFieldNames(excludeColumnFieldNames).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "listing列表PARENTASIN维度").head(MarProductPopularizeLogParentAsinVO.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<MarProductPopularizeLogParentAsinVO> marProductPopularizeLogParentAsinVOList =  marProductPopularizeLogAsinService.selectPopularizeLogByParentAsinListExport(marListingQuery);
                if (CollUtil.isEmpty(marProductPopularizeLogParentAsinVOList)) {
                    break;
                }
                log.info("query pageNo:{}, list.size = {}, ", pageNo, marProductPopularizeLogParentAsinVOList.size());
                log.info("exportList.size = " + marProductPopularizeLogParentAsinVOList.size());
                writer.write(marProductPopularizeLogParentAsinVOList, writeSheet);
                pageNo++;
            }
            //要忽略的属性
            writer.finish();
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/listing/parentAsin");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }



    /**
     * @description: Listing整合子AISN导出
     * @author: Moore
     * @date: 2024/3/15 13:48
     * @param
     * @param param
     * @return: java.lang.String
    **/
    @Override
    public String findMarProductPopularizeLogAsinExport(String param) {
        log.info("findMarProductPopularizeLogAsinExport start ... ");
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "listing列表ASIN维度" + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        MarListingQuery marListingQuery = JSON.parseObject(param, MarListingQuery.class);
        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file, MarProductPopularizeLogAsin.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "listing列表ASIN维度").head(MarProductPopularizeLogAsin.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<MarProductPopularizeLogAsin> marProductPopularizeLogAsinList=  marProductPopularizeLogAsinService.selectPopularizeLogByAsinList(marListingQuery);
                if (CollUtil.isEmpty(marProductPopularizeLogAsinList)) {
                    break;
                }
                log.info("query pageNo:{}, list.size = {}, ", pageNo, marProductPopularizeLogAsinList.size());
                log.info("exportList.size = " + marProductPopularizeLogAsinList.size());
                writer.write(marProductPopularizeLogAsinList, writeSheet);
                pageNo++;
            }
            writer.finish();
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/listing/asin");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }

    /**
     * Description: 导出
     *
     * @param param
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/2/29
     */
    @Override
    public String findPopularizeLogBySellerSkuExport(String param) {
        log.info("findPopularizeLogBySellerSkuExport start ... ");
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "listing列表SellerSku维度" + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        MarListingQuery marListingQuery = JSON.parseObject(param, MarListingQuery.class);
        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file, MarProductPopularizeLogSellerSkuVo.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "listing列表SellerSku维度").head(MarProductPopularizeLogSellerSkuVo.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<MarProductPopularizeLogSellerSkuVo> marProductPopularizeLogSellerSkuVoList =  marProductPopularizeLogSellerSkuService.selectPopularizeLogBySellerSkuList(marListingQuery);
                if (CollUtil.isEmpty(marProductPopularizeLogSellerSkuVoList)) {
                    break;
                }
                log.info("query pageNo:{}, list.size = {}, ", pageNo, marProductPopularizeLogSellerSkuVoList.size());
                log.info("exportList.size = " + marProductPopularizeLogSellerSkuVoList.size());
                writer.write(marProductPopularizeLogSellerSkuVoList, writeSheet);
                pageNo++;
            }
            writer.finish();
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/listing/SellerSku");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }


    /**
     * Description: 导出
     *
     * @param param
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/2/29
     */
    @Override
    public String findMarProductPopularizeLogSkuExport(String param) {
        log.info("findMarProductPopularizeLogSkuExport start ... ");
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "listing列表SKU维度" + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        MarListingQuery marListingQuery = JSON.parseObject(param, MarListingQuery.class);
        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file, MarProductPopularizeLogSkuVo.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "listing列表SKU维度").head(MarProductPopularizeLogSkuVo.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<MarProductPopularizeLogSkuVo> marProductPopularizeLogSkuVoList =  marProductPopularizeLogSkuService.selectPopularizeLogBySKuList(marListingQuery);
                if (CollUtil.isEmpty(marProductPopularizeLogSkuVoList)) {
                    break;
                }
                log.info("query pageNo:{}, list.size = {}, ", pageNo, marProductPopularizeLogSkuVoList.size());
                log.info("exportList.size = " + marProductPopularizeLogSkuVoList.size());
                writer.write(marProductPopularizeLogSkuVoList, writeSheet);
                pageNo++;
            }
            writer.finish();
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/listing/sku");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
        return uploadPath;

    }

    /**
     * Description: 导出
     *
     * @param param
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2023/12/29
     */
    @Override
    public String findMarProductPopularizeShopExport(String param) {
        log.info("findMarProductPopularizeShopExport start ... ");
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "listing列表shop维度" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        MarListingQuery marListingQuery = JSON.parseObject(param, MarListingQuery.class);
        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file, MarProductPopularizeShopVo.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "Listing店铺维度").head(MarProductPopularizeShopVo.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<MarProductPopularizeShopVo> marProductPopularizeShopVoList =  marProductPopularizeShopService.queryMarProductPopularizeShopList(marListingQuery);
                if (CollUtil.isEmpty(marProductPopularizeShopVoList)) {
                    break;
                }
                log.info("query pageNo:{}, list.size = {}, ", pageNo, marProductPopularizeShopVoList.size());
                log.info("exportList.size = " + marProductPopularizeShopVoList.size());
                writer.write(marProductPopularizeShopVoList, writeSheet);
                pageNo++;
            }
            writer.finish();
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/listing/shop");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
        return uploadPath;

    }


    /**
     * 其他站外信导出
     * @param query
     * @param contextId
     * @return
     */
    @Override
    public String asyncExportOffsiteList(String query, Integer contextId) {

        log.info("asyncExportOffsite start ... ");
        int index = 0;
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "站外信信息" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);

        CusCenterOffsiteQuery offsiteQuery = JSON.parseObject(query, CusCenterOffsiteQuery.class);
        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file, CusCenterOffsiteVO.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "站外信信息").head(CusCenterOffsiteVO.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<CusCenterOffsiteVO> exportList = cusCenterOffsiteService.selectCusCenterOffsitexportList(offsiteQuery);
                if (CollUtil.isEmpty(exportList)) {
                    break;
                }
                log.info("query pageNo:{}, list.size = {}, ", pageNo, exportList.size());
                log.info("exportList.size = " + exportList.size());
                writer.write(exportList, writeSheet);
                index++;
                pageNo++;
            }
            writer.finish();

            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/cusOffsite/");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        }  finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }

    /**
     * Description: 退货分析B2c详情导出
     *
     * @param param
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/22
     */
    @Override
    public String listExportB2CListDetailExport(String param) {
        log.info("listExportB2CListDetailExport start ... ");
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "退货分析B2C详情导出" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        StatReturnQuery statReturnQuery = JSON.parseObject(param, StatReturnQuery.class);
        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file, ReturnAnalyzeVO.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "退货分析B2C详情导出").head(ReturnAnalyzeVO.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<ReturnAnalyzeVO> returnAnalyzeVOList = returnAnalyzeService.getB2CReturnAnalyzePageListDetailNew(statReturnQuery);
                if (CollUtil.isEmpty(returnAnalyzeVOList)) {
                    break;
                }
                log.info("query pageNo:{}, list.size = {}, ", pageNo, returnAnalyzeVOList.size());
                log.info("exportList.size = " + returnAnalyzeVOList.size());
                writer.write(returnAnalyzeVOList, writeSheet);
                pageNo++;
            }
            writer.finish();
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/returnInfo");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }


    /**
     * @Description: 退款信息任务导出
     * @Author: wly
     * @Date: 2024/4/29 10:09
     * @Params: [query, contextId]
     * @Return: java.lang.String
     **/
    @Override
    public String asyncExportOrderRefundList(String query, Integer contextId) {
        log.info("asyncExportOrderRefund start ... ");
        int index = 0;
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "退款信息" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);

        CusCenterOrderRefundQuery refundQuery = JSON.parseObject(query, CusCenterOrderRefundQuery.class);
        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file, CusCenterOrderRefundVO.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "退款信息").head(CusCenterOrderRefundVO.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<CusCenterOrderRefundVO> exportList = cusCenterOrderRefundService.selectCusCenterOrderRefundexportList(refundQuery);
                if (CollUtil.isEmpty(exportList)) {
                    break;
                }
                log.info("query pageNo:{}, list.size = {}, ", pageNo, exportList.size());
                log.info("exportList.size = " + exportList.size());
                writer.write(exportList, writeSheet);
                index++;
                pageNo++;
            }
            writer.finish();

            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/orderRefund/");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }

    /**
     * Description: 评论任务导出
     *
     * @param param
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/28
     */
    @Override
    public String findXsmReviewCommonExport(String param) {
        log.info("async xsm_amz_review.export start ... ");
        int index = 0;
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "商品评论" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);

        XsmReviewCommonDTO xsmReviewCommonDTO = JSON.parseObject(param, XsmReviewCommonDTO.class);
        ExportExcelEntity excelInfo = new ExportExcelEntity(path);
        String uploadPath = null;
        try {
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<XsmReviewCommonVO> reviewCommons = xsmReviewService.selectXsmReviews(xsmReviewCommonDTO);
                if (CollectionUtil.isEmpty(reviewCommons)) {
                    break;
                }
                List<XsmReviewCommonExport> exports = BeanCopyUtils.copyBeanList(reviewCommons, XsmReviewCommonExport.class);
                ConvertUtils.dictConvert(exports);
                log.info("query pageNo:{}, list.size = {}, ", pageNo, exports.size());
                log.info("exportList.size = " + exports.size());
                excelInfo.writeData(exports, index == 0);
                index++;
                pageNo++;
            }
            excelInfo.complete();
            InputStream is = new FileInputStream(excelInfo.getExcelFile());
            uploadPath = AliyunOssClientUtil.uploadFile(excelInfo.getExcelFile().getName(), is, "erp/XsmReviewCommon/");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            excelInfo.del();
        }
        return uploadPath;
    }


    /**
     * @Description: FeedBack导出
     * @Author: wly
     * @Date: 2024/5/7 14:12
     * @Params: [query, contextId]
     * @Return: java.lang.String
     **/
    @Override
    public String asyncExportFeedBackList(String query, Integer contextId) {

        log.info("asyncExportFeedBack start ... ");
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "FeedBack信息" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);

        CusCenterFeedbackQuery feedbackQuery = JSON.parseObject(query, CusCenterFeedbackQuery.class);
        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            List<CusCenterFeedbackVO> result = new ArrayList<>();
            writer = EasyExcel.write(file, CusCenterFeedbackVO.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "FeedBack信息").head(CusCenterFeedbackVO.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<CusCenterFeedbackVO> exportList = cusCenterFeedbackService.selectList(feedbackQuery,null);
                if (CollUtil.isEmpty(exportList)) {
                    break;
                }
                log.info("query pageNo:{}, list.size = {}, ", pageNo, exportList.size());
                log.info("exportList.size = " + exportList.size());
                writer.write(exportList, writeSheet);
                pageNo++;
            }
            writer.finish();
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/feedBack/");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }

    /**
     * @Description: 补发信息导出
     * @Author: wly
     * @Date: 2024/5/7 14:12
     * @Params: [query, contextId]
     * @Return: java.lang.String
     **/
    @Override
    public String asyncExportReissueList(String query, Integer contextId) {
        log.info("asyncExportReissue start ... ");
        int index = 0;
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "补发信息" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        CusCenterOrderReissueQuery reissueQuery = JSON.parseObject(query, CusCenterOrderReissueQuery.class);

        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");

            List<CusCenterOrderReissueVO> result = new ArrayList<>();
            writer = EasyExcel.write(file, CusCenterOrderReissueVO.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "补发信息").head(CusCenterOrderReissueVO.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<CusCenterOrderReissueVO> exportList = cusCenterOrderReissueService.selectCusCenterReissuexportList(reissueQuery);

                if (CollUtil.isEmpty(exportList)) {
                    break;
                }
                writer.write(exportList, writeSheet);
                log.info("query pageNo:{}, list.size = {}, ", pageNo, exportList.size());
                log.info("exportList.size = " + exportList.size());
                pageNo++;
            }

            writer.finish();

            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/cusReissue/");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }

    /**
     * @param param
     * @description: 邮件信息任务导出
     * @author: Moore
     * @date: 2024/5/21 16:22
     * @return: java.lang.String
     **/
    @Override
    public String exportEmailInfo(String param) {
        log.info("export_email_info start ... ");
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "邮件信息" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        CusCenterEmailInfoQuery cusCenterEmailInfoQuery = JSON.parseObject(param, CusCenterEmailInfoQuery.class);
        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file, CusCenterEmailInfoVO.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "邮件信息").head(CusCenterEmailInfoVO.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<CusCenterEmailInfoVO> cusCenterEmailInfoVOS = cusCenterEmailInfoService.selectCusCenterEmailInfoList(cusCenterEmailInfoQuery);
                ConvertUtils.dictConvert(cusCenterEmailInfoVOS);
                if (CollUtil.isEmpty(cusCenterEmailInfoVOS)) {
                    break;
                }
                writer.write(cusCenterEmailInfoVOS, writeSheet);
                log.info("query pageNo:{}, list.size = {}, ", pageNo, cusCenterEmailInfoVOS.size());
                log.info("exportList.size = " + cusCenterEmailInfoVOS.size());
                pageNo++;
            }
            writer.finish();

            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/email/");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }



    @Override
    public String asyncExportScTicketList(String query, Integer contextId) {
        log.info("asyncExportScTicket start ... ");
        int pageNo = 1;
        int pageSize = 5000;
        String path = filePath + "工单信息" + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file, ScTicketExportVo.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "工单信息").head(ScTicketExportVo.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                ScTicket scTicket = JSON.parseObject(query, ScTicket.class);
                List<ScTicket> exportList = scTicketService.selectScTicketListNoParent(scTicket);
                if (CollUtil.isEmpty(exportList)) {
                    break;
                }
                List<ScTicketExportVo> scTicketExportVos = ConvertUtils.dictConvert(BeanCopyUtils.asmCopyList(exportList, ScTicketExportVo.class));
                log.info("query pageNo:{}, list.size = {}, ", pageNo, exportList.size());
                log.info("exportList.size = " + exportList.size());
                writer.write(scTicketExportVos, writeSheet);
                pageNo++;
            }
            writer.finish();
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/ticket/");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        }finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }

    @Override
    public String asyncExportPromotionList(String query, Integer contextId) {
        return amzPromotionExportService.asyncExportPromotionList(query,contextId);
    }

    @Override
    public String asyncExportCouponList(String query, Integer contextId) {
        return amzPromotionExportService.asyncExportCouponList(query,contextId);
    }

    @Override
    public String exportCustomerComplaintDetailList(String query, Integer contextId) {
        log.info("asyncExportCustomerComplaintDetailList start ... ");
        int pageNo = 1;
        int pageSize = 5000;
        String path = filePath + "客诉明细" + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file, CustomerComplaintDetailVO.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "客诉明细").head(CustomerComplaintDetailVO.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                CustomerComplaintMainQuery complaintMainQuery = JSON.parseObject(query, CustomerComplaintMainQuery.class);
                List<CustomerComplaintDetailVO> exportList = customerComplaintDetailService.selectCustomerComplaintDetailList(complaintMainQuery);
                if (CollUtil.isEmpty(exportList)) {
                    break;
                }
                log.info("query pageNo:{}, list.size = {}, ", pageNo, exportList.size());
                log.info("exportList.size = " + exportList.size());
                writer.write(exportList, writeSheet);
                pageNo++;
            }
            writer.finish();
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/customercomplaintdeatail/");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        }finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }

    /**
     * Description: 客诉分析-一级导出
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/6/12
     */
    @Override
    public String listCustomerComplaintDimensionOneListDetailExport(String customerComplaintMainQuery ) {
        CustomerComplaintMainQuery query = JSON.parseObject(customerComplaintMainQuery, CustomerComplaintMainQuery.class);
        log.info("listCustomerComplaintDimensionOneListDetailExport start ... ");
        int pageNo = 1;
        int pageSize = 5000;
        String path = filePath + "客诉分析一级" + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN) + ".xlsx";
        //动态头数据
        List<List<String>> dynamicHeadList = new ArrayList<>();
        dynamicHeadList.add(Collections.singletonList("客诉时间"));
        dynamicHeadList.add(Collections.singletonList("sku"));
        dynamicHeadList.add(Collections.singletonList("model"));
        dynamicHeadList.add(Collections.singletonList("品类"));
        dynamicHeadList.add(Collections.singletonList("asin"));
        dynamicHeadList.add(Collections.singletonList("店铺"));
        dynamicHeadList.add(Collections.singletonList("渠道"));
        for(ConfTicketProblem c:query.getConfTicketProblemList()) {
            dynamicHeadList.add(Collections.singletonList(c.getProblemName()));
            dynamicHeadList.add(Collections.singletonList(c.getProblemName() + "百分比"));
        }
        dynamicHeadList.add(Collections.singletonList("合计"));

        //实际列字段
        List<List<String>> dynamicColumnNameList = new ArrayList<>();
        dynamicColumnNameList.add(Collections.singletonList("createdAt"));
        dynamicColumnNameList.add(Collections.singletonList("sku"));
        dynamicColumnNameList.add(Collections.singletonList("model"));
        dynamicColumnNameList.add(Collections.singletonList("categoryName"));
        dynamicColumnNameList.add(Collections.singletonList("asin"));
        dynamicColumnNameList.add(Collections.singletonList("shopName"));
        dynamicColumnNameList.add(Collections.singletonList("channel"));
        for(ConfTicketProblem c:query.getConfTicketProblemList()) {
            dynamicColumnNameList.add(Collections.singletonList(c.getProblemId().toString()));
            dynamicColumnNameList.add(Collections.singletonList(c.getProblemId() + "Rate"));
        }
        dynamicColumnNameList.add(Collections.singletonList("total"));

        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file).excelType(ExcelTypeEnum.XLSX).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "客诉分析一级").head(dynamicHeadList).build();   //设置头信息
            while(true) {
                //每行数据集合
                List<List<Object>> rowDataList = new ArrayList<>();
                PageHelper.startPage(pageNo, pageSize);
                //获取数据（查询）
                List<Map<String, Object>> list = customerComplaintDimensionTotalService.selectCustomerComplaintOneDetailList(query);
                if (CollectionUtil.isEmpty(list)) {
                    break;
                }
                list.stream().forEach(item -> {
                    List<Object> flagList = new ArrayList<>();
                    for (List<String> dynamicColumn : dynamicColumnNameList) {
                        flagList.add(item.get(dynamicColumn.get(0)));
                    }
                    rowDataList.add(flagList);
                });
                writer.write(rowDataList, writeSheet);
                pageNo++;
            }
            writer.finish();
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/customercomplaintdeatail/one");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }

    /**
     * Description: 客诉分析-二级导出
     *
     * @param customerComplaintMainQuery
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/6/12
     */
    @Override
    public String listCustomerComplaintDimensionTwoListDetailExport(String customerComplaintMainQuery) {
        CustomerComplaintMainQuery query = JSON.parseObject(customerComplaintMainQuery, CustomerComplaintMainQuery.class);
        log.info("listCustomerComplaintDimensionTwoListDetailExport start ... ");
        int pageNo = 1;
        int pageSize = 5000;
        String path = filePath + "客诉分析二级" + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN) + ".xlsx";
        //动态头数据
        List<List<String>> dynamicHeadList = new ArrayList<>();
        dynamicHeadList.add(Collections.singletonList("客诉时间"));
        dynamicHeadList.add(Collections.singletonList("sku"));
        dynamicHeadList.add(Collections.singletonList("model"));
        dynamicHeadList.add(Collections.singletonList("品类"));
        dynamicHeadList.add(Collections.singletonList("asin"));
        dynamicHeadList.add(Collections.singletonList("店铺"));
        dynamicHeadList.add(Collections.singletonList("渠道"));
        for(ConfTicketProblem c:query.getConfTicketProblemList()) {
            dynamicHeadList.add(Collections.singletonList(c.getParent().getProblemName() + "/" + c.getProblemName()));
            dynamicHeadList.add(Collections.singletonList(c.getParent().getProblemName() + "/" + c.getProblemName() + "百分比"));
        }
        dynamicHeadList.add(Collections.singletonList("合计"));

        //实际列字段
        List<List<String>> dynamicColumnNameList = new ArrayList<>();
        dynamicColumnNameList.add(Collections.singletonList("createdAt"));
        dynamicColumnNameList.add(Collections.singletonList("sku"));
        dynamicColumnNameList.add(Collections.singletonList("model"));
        dynamicColumnNameList.add(Collections.singletonList("categoryName"));
        dynamicColumnNameList.add(Collections.singletonList("asin"));
        dynamicColumnNameList.add(Collections.singletonList("shopName"));
        dynamicColumnNameList.add(Collections.singletonList("channel"));
        for(ConfTicketProblem c:query.getConfTicketProblemList()) {
            dynamicColumnNameList.add(Collections.singletonList(c.getProblemId().toString()));
            dynamicColumnNameList.add(Collections.singletonList(c.getProblemId() + "Rate"));
        }
        dynamicColumnNameList.add(Collections.singletonList("total"));

        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file).excelType(ExcelTypeEnum.XLSX).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "客诉分析二级").head(dynamicHeadList).build();   //设置头信息
            while(true) {
                //每行数据集合
                List<List<Object>> rowDataList = new ArrayList<>();
                PageHelper.startPage(pageNo, pageSize);
                //获取数据（查询）
                List<Map<String, Object>> list = customerComplaintDimensionTotalService.selectCustomerComplaintTwoDetailList(query);
                if (CollectionUtil.isEmpty(list)) {
                    break;
                }
                list.stream().forEach(item -> {
                    List<Object> flagList = new ArrayList<>();
                    for (List<String> dynamicColumn : dynamicColumnNameList) {
                        flagList.add(item.get(dynamicColumn.get(0)));
                    }
                    rowDataList.add(flagList);
                });
                writer.write(rowDataList, writeSheet);
                pageNo++;
            }
            writer.finish();
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/customercomplaintdeatail/two");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }

    @Override
    public String asyncExportContactCustomerSendList(String query, Integer contextId) {
        return marSendMessageOrdersService.asyncExportContactCustomerSendList(query,contextId);
    }

    @Override
    public String asyncExportContactCustomerRuleList(String query, Integer contextId) {
        return marSendMessageRuleService.asyncExportContactCustomerRuleList(query,contextId);
    }

    @Override
    public String asyncExportSaleOrderCancelList(String query, Integer contextId) {
        log.info("asyncExportCustomerSaleOrderCancelList start ... ");
        int pageNo = 1;
        int pageSize = 5000;
        String path = filePath + "订单取消信息" + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file, CusCenterSaleOrderCancelVO.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "订单取消信息").head(CusCenterSaleOrderCancelVO.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                CusCenterSaleOrderCancelQuery cusCenterSaleOrderCancelQuery = JSON.parseObject(query, CusCenterSaleOrderCancelQuery.class);
                List<CusCenterSaleOrderCancelVO> exportList = cusCenterSaleOrderCancelService.selectCusCenterSaleOrderCancelExportList(cusCenterSaleOrderCancelQuery);
                if (CollUtil.isEmpty(exportList)) {
                    break;
                }
                log.info("query pageNo:{}, list.size = {}, ", pageNo, exportList.size());
                log.info("exportList.size = " + exportList.size());
                writer.write(exportList, writeSheet);
                pageNo++;
            }
            writer.finish();
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/customersaleordercancel/");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        }finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }

    /**
     * @param param
     */
    @Override
    public String findlistMarFollowExport(String param) {
        log.info("listMarFollowExport start ... ");
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "白名单导出" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        MarFollowWhiteList marFollowWhiteList = JSON.parseObject(param, MarFollowWhiteList.class);
        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file, MarFollowWhiteList.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "白名单").head(MarFollowWhiteList.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                LambdaQueryChainWrapper<MarFollowWhiteList> queryWhiteList = marFollowWhiteListService.lambdaQuery();
                queryWhiteList.eq(MarFollowWhiteList::getOrganizationId, marFollowWhiteList.getOrganizationId());
                if(CollectionUtils.isNotEmpty(marFollowWhiteList.getSellingIdList())) {
                    queryWhiteList.in(MarFollowWhiteList::getSellingId, marFollowWhiteList.getSellingIdList());
                }
                List<MarFollowWhiteList> marFollowWhiteListList = queryWhiteList.list();
                if (CollUtil.isEmpty(marFollowWhiteListList)) {
                    break;
                }
                marFollowWhiteListList.stream().forEach(i -> i.setCreatedAtString(i.getCreatedAt()));
                log.info("query pageNo:{}, list.size = {}, ", pageNo, marFollowWhiteListList.size());
                log.info("exportList.size = " + marFollowWhiteListList.size());
                writer.write(marFollowWhiteListList, writeSheet);
                pageNo++;
            }
            writer.finish();
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/followWhite/");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }

    /**
     * @param param
     * @return
     */
    @Override
    public String findMarFollowSellerInfoExport(String param) {
        log.info("findMarFollowSellerInfoExport start ... ");
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "跟卖监控导出" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        MarFollowSellerInfo marFollowSellerInfo = JSON.parseObject(param, MarFollowSellerInfo.class);
        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file, MarFollowSellerInfoV.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "跟卖监控导出").head(MarFollowSellerInfoV.class).build();
            List<MarFollowWhiteList> marFollowWhiteLists =  marFollowWhiteListService.lambdaQuery().eq(MarFollowWhiteList::getOrganizationId, marFollowSellerInfo.getOrganizationId()).list();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                marFollowSellerInfo.setOrganizationId(marFollowSellerInfo.getOrganizationId());
                List<MarFollowSellerInfoV> marFollowSellerInfoVList =  marFollowSellerInfoService.selectMarFollowSellerInfoListView(marFollowSellerInfo, marFollowWhiteLists);
                if (CollUtil.isEmpty(marFollowSellerInfoVList)) {
                    break;
                }
                log.info("query pageNo:{}, list.size = {}, ", pageNo, marFollowSellerInfoVList.size());
                log.info("exportList.size = " + marFollowSellerInfoVList.size());
                writer.write(marFollowSellerInfoVList, writeSheet);
                pageNo++;
            }
            writer.finish();
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/followSellerInfo");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }

    @Override
    public String asyncMarAdjustmentPriceManagementExport(String query, Integer contextId) {
        return marAdjustmentPriceManagementService.asyncMarAdjustmentPriceManagementExport(query,contextId);
    }

    @Override
    public String exportProductChannels(String param) {
        return productChannelsService.exportProductChannelAsync(param);
    }

    /**
     * 退货信息导出
     * @param
     */
    @Override
    public String findReturnInfoExport(String param) {
        log.info("findReturnInfoExport start ... ");
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "退货信息" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        ReturnInfoEntityVo returnInfoEntityVo = JSON.parseObject(param, ReturnInfoEntityVo.class);
        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file, ReturnInfoEntityExportVo.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "退货信息").head(ReturnInfoEntityExportVo.class).build();
            while (true) {
                returnInfoEntityVo.setPageNum(pageNo);
                returnInfoEntityVo.setPageSize(pageSize);
                List<ReturnInfoEntity> returnInfoEntityList = returnInfoService.getReturnInfoPageList(returnInfoEntityVo);
                if (CollUtil.isEmpty(returnInfoEntityList)) {
                    break;
                }
                List<ReturnInfoEntityExportVo> exports = BeanCopyUtils.copyBeanList(returnInfoEntityList, ReturnInfoEntityExportVo.class);
                ConvertUtils.dictConvert(exports);

                log.info("query pageNo:{}, list.size = {}, ", pageNo, exports.size());
                log.info("exportList.size = " + exports.size());
                writer.write(exports, writeSheet);
                pageNo++;
            }
            writer.finish();
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/returnInfo/");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }

    /**
     * Description: 退款请求信息导出
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/8/26
     */
    @Override
    public String asyncExportOrderRequestRefundList(String query) {
        log.info("asyncExportOrderRequestRefundList start ... ");
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "退款请求信息" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);

        CusCenterOrderRefundQuery refundQuery = JSON.parseObject(query, CusCenterOrderRefundQuery.class);
        String uploadPath = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            ExcelWriter writer = EasyExcel.write(file, OrderRequestRefundVO.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "退款请求信息").head(OrderRequestRefundVO.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<CusCenterOrderRefundVO> cusCenterOrderRefundVOS = orderRefundRequestInfoService.selectExportList(refundQuery);
                if (CollUtil.isEmpty(cusCenterOrderRefundVOS)) {
                    break;
                }
                List<OrderRequestRefundVO> exports = BeanCopyUtils.copyBeanList(cusCenterOrderRefundVOS, OrderRequestRefundVO.class);
                ConvertUtils.dictConvert(exports);
                log.info("query pageNo:{}, list.size = {}, ", pageNo, exports.size());
                log.info("exportList.size = " + exports.size());


                writer.write(exports, writeSheet);
                pageNo++;
            }

            writer.finish();

            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/request/orderRefund/");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        }
        return uploadPath;
    }


    @Override
    public String exportPromotionFee(String query) {
        return amzPromotionFeeService.exportPromotionFee(query);
    }

    @Override
    public String exportCouponFee(String query) {
        return amzPromotionFeeService.exportCouponFee(query);
    }

    /**
     * temu促销活动导出
     * @param query
     * @param contextId
     * @return
     */
    @Override
    public String asyncExportTemuPromotionList(String query, Integer contextId) {
        return marTemuPromotionService.asyncExportTemuPromotionList(query, contextId);
    }

    /** temu平台活动列表导出
     *
     * @param
     * @return
     */
    @Override
    public String exportPlatformPromotionsInfoList(String query) {
        return marPlatformPromotionsInfoService.exportPlatformPromotionsInfoList(query);
    }


    /**
     * @description: temu 提报商品列表导出
     * @author: Moore
     * @date: 2024/9/26 17:18
     * @param
     * @param query
     * @return: java.lang.String
    **/
    @Override
    public String exportTemuReportProduct(String query) {
        return marPromotionsReportProductService.exportTemuReportProduct(query);
    }

    /**
     * 包裹申诉信息导出
     * @param query
     * @param contextId
     * @return
     */
    @Override
    public String asyncExportMarAppealList(String query, Integer contextId) {
        return marAppealService.asyncExport(query, contextId);
    }


    @Override
    public String exportShopServiceQuality(String param) {
        log.info("exportShopServiceQuality start ... ");
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "店铺服务质量" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);

        MarFinancialConstraintsQuery query = JSON.parseObject(param, MarFinancialConstraintsQuery.class);
        String uploadPath = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            List<MarShopServiceQuality> result = new ArrayList<>();
            ExcelWriter writer = EasyExcel.write(file, MarShopServiceQuality.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "店铺服务质量").head(MarShopServiceQuality.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<MarShopServiceQuality> exports = marShopServiceQualityService.listShopServiceQuality(query);
                if (CollUtil.isEmpty(exports)) {
                    break;
                }

                ConvertUtils.dictConvert(exports);
                log.info("query pageNo:{}, list.size = {}, ", pageNo, exports.size());
                log.info("exportList.size = " + exports.size());

                result.addAll(exports);
                writer.write(exports, writeSheet);
                pageNo++;
            }

            writer.finish();

            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/shopService/quality");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        }
        return uploadPath;
    }


    /**
     * 资金限制列表导出
     * @param query
     * @param contextId
     * @return
     */
    @Override
    public String asyncExportFinancialConstraintsList(String query, Integer contextId) {
        return marFinancialConstraintsService.asyncExport(query, contextId);
    }

    /**
     * 资金限制详情导出
     * @param query
     * @param contextId
     * @return
     */
    @Override
    public String asyncExportFinancialConstraintsItemInfo(String query, Integer contextId) {
        return marFinancialConstraintsService.asyncDetailExport(query, contextId);
    }


    /**
     * @Description:费用索赔列表导出
     * @Author: wly
     * @Date: 2024/11/12 16:49
     * @Params: [query]
     * @Return: java.lang.String
     **/

    @Override
    public String asyncExportMarDeliveryFeeClaimList(String query) {
        return marDeliveryFeeClaimService.asyncExportMarDeliveryFeeClaimList(query);
    }


    /**
     * @description:素材中心导出
     * @author: Moore
     * @date: 2025/2/11 9:37
     * @param
     * @param query
     * @return: java.lang.String
    **/
    @Override
    public String asyncExportMarMaterialCenterList(String query) {
        return marMaterialCenterInfoService.asyncExportmaterialCenterList(query);
    }



   /**
    * @description: 素材制作导出
    * @author: Moore
    * @date: 2025/2/17 13:52
    * @param
    * @param query
    * @return: java.lang.String
   **/
    @Override
    public String asyncExportMarMaterialMakeList(String query) {
        return marMaterialMakeInfoService.asyncExportMakeList(query);
    }


    /**
     * @description:素材分析导出
     * @author: Moore
     * @date: 2025/2/17 14:35
     * @param
     * @param query
     * @return: java.lang.String
    **/
    @Override
    public String asyncExportMarMaterialAnalyseList(String query) {
        return marMaterialCenterInfoService.asyncExportmaterialAnalyseList(query);
    }


    @Override
    public String asyncExportMarTkCouponList(String query) {
        return marTkCouponInfoService.exportInfoListString(query);
    }

    @Override
    public String asyncExportRangeDataList(String query) {
        return marAsinRangeInfoService.asyncExportRangeDataList(query);
    }




    /**
     * temu优惠券导出
     * @param query
     * @return
     */
    @Override
    public String exportInfoListString(String query) {
        return marCouponInfoService.exportInfoListString(query);
    }

    /**
     * temu补报活动导出
     * @param query
     * @return
     */
    @Override
    public String asyncExportSupplementaryTemuPromotionList(String query) {
        return temuSupplementaryReportPromotionService.asyncExportTemuPromotionList(query);
    }

    @Override
    public String asyncExportClaimList(String query) {
        return marLastMileFeeClaimService.asyncExportClaimList(query);
    }

    /**
     * vine信息导出
     * @param query
     * @return
     */
    @Override
    public String asyncExportVineInfoList(String query) {
        return amazonVineInfoService.asyncExportList(query);
    }

    /**
     * 换货信息导出
     * @param
     */
    @Override
    public String findReplaceInfoExport(String param) {
        log.info("findReplaceInfoExport start ... ");
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "换货信息" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        ReturnInfoEntityVo returnInfoEntityVo = JSON.parseObject(param, ReturnInfoEntityVo.class);
        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file, ReplaceInfoEntityExportVo.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "换货信息").head(ReplaceInfoEntityExportVo.class).build();
            while (true) {
                returnInfoEntityVo.setPageNum(pageNo);
                returnInfoEntityVo.setPageSize(pageSize);
                List<ReturnInfoEntity> returnInfoEntityList = returnInfoService.getReplaceInfoPageList(returnInfoEntityVo);
                if (CollUtil.isEmpty(returnInfoEntityList)) {
                    break;
                }
                List<ReplaceInfoEntityExportVo> exports = BeanCopyUtils.copyBeanList(returnInfoEntityList, ReplaceInfoEntityExportVo.class);
                ConvertUtils.dictConvert(exports);

                log.info("query pageNo:{}, list.size = {}, ", pageNo, exports.size());
                log.info("exportList.size = " + exports.size());
                writer.write(exports, writeSheet);
                pageNo++;
            }
            writer.finish();
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/returnInfo/");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }

    /**
     * @param query return
     * @desc vc客诉分析导出
     * <AUTHOR>
     * @date 2025/5/7 16:00
     * param
     */
    @Override
    public String vcCustomerComplainListExport(String param) {
        log.info("vcCustomerComplainListExport start ... ");
        int pageNo = 1;
        int pageSize = 10000;
        String path = filePath + "vc客诉客诉分析信息" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        MarVcCustomerComplaintQuery marVcCustomerComplaintQuery = JSON.parseObject(param, MarVcCustomerComplaintQuery.class);
        String uploadPath = null;
        ExcelWriter writer = null;
        try {
            File file = File.createTempFile(path, ".xlsx");
            writer = EasyExcel.write(file, MarVcCustomerComplaint.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "vc客诉客诉分析信息").head(MarVcCustomerComplaint.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<MarVcCustomerComplaint> marVcCustomerComplaintList = marVcCustomerComplaintService.queryVcCustomerComplainList(marVcCustomerComplaintQuery);
                if (CollUtil.isEmpty(marVcCustomerComplaintList)) {
                    break;
                }
                log.info("query pageNo:{}, list.size = {}, ", pageNo, marVcCustomerComplaintList.size());
                log.info("exportList.size = " + marVcCustomerComplaintList.size());
                writer.write(marVcCustomerComplaintList, writeSheet);
                pageNo++;
            }
            writer.finish();
            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/vcCustomerComplainListExport/");
            log.info("upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            log.info("upload oss fail! excelInfo:{}", e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
        return uploadPath;
    }

    @Override
    public String downLoadWfsAttach(String query) {

        return walmartShipmentAttachService.downLoadAttachFileAsync(query);

    }

    @Override
    public String problemRateDetailListExportAsync(String jsonString) {
        return scTicketProblemRateService.problemRateDetailListExportAsync(jsonString);
    }

    @Override
    public String selectProblemRateDimensionListExportAsync(String jsonString) {
        return scTicketProblemRateService.problemRateDimensionListExportAsync(jsonString);
    }
    @Override
    public String exportTkPromotionListAsync(String request) {
        return tkPromotionService.exportTkPromotionListAsync(request);
    }


    /** 导出STA列表数据
     *
     * @param jsonString
     * @return
     */
    @Override
    public String marStaInfoListExportAsync(String jsonString) {
        return  marStaTaskInfoService.marStaTaskInfoExport(JSONObject.parseObject(jsonString, MarStaTaskListQueryDTO.class));
    }
}
