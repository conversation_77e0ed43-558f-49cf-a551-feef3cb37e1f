package com.bizark.op.service.service.mar;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.boss.api.service.TaskCenterService;
import com.bizark.boss.api.vo.dashboard.TaskCenterRequest;
import com.bizark.common.util.BeanUtils;
import com.bizark.erp.common.util.ListUtils;
import com.bizark.fbb.api.entity.fbb.reimbursement.request.ReimbursementRequest;
import com.bizark.fbb.api.entity.fbb.reimbursement.response.ReimbursementResponse;
import com.bizark.fbb.api.parameter.reimbursement.spec.ReimbursementShipTailDataProps;
import com.bizark.fbb.api.service.reimbursement.ReimbursementService;
import com.bizark.op.api.cons.ScTicketConstant;
import com.bizark.op.api.enm.mar.claim.*;
import com.bizark.op.api.enm.sale.AccountSaleChannelEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.mar.*;
import com.bizark.op.api.entity.op.mar.calim.response.MarClaimProveInfo;
import com.bizark.op.api.entity.op.mar.calim.response.MarClaimTemuInfo;
import com.bizark.op.api.entity.op.sale.Products;
import com.bizark.op.api.entity.op.ticket.ScTicket;
import com.bizark.op.api.entity.op.ticket.ScTicketLog;
import com.bizark.op.api.request.mar.mile.MarLastMileFeeClaimProveReq;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.mar.MarLastMileFeeClaimAttachesService;
import com.bizark.op.api.service.mar.MarLastMileFeeClaimLogService;
import com.bizark.op.api.service.mar.MarLastMileFeeClaimService;
import com.bizark.op.api.service.mar.MarRpaServiceStatusClaimLogService;
import com.bizark.op.api.service.sale.ProductsService;
import com.bizark.op.api.service.ticket.IScTicketLogService;
import com.bizark.op.api.service.ticket.IScTicketService;
import com.bizark.op.api.vo.mar.MarLastMileFeeClaimDTO;
import com.bizark.op.api.vo.mar.MarLastMileFeeClaimInterfaceVo;
import com.bizark.op.api.vo.mar.MarLastMileFeeClaimListVO;
import com.bizark.op.api.vo.mar.MarLastMileFeeClaimQuery;
import com.bizark.op.api.vo.order.SaleOrderShipemtInfoVO;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.AliyunOssClientUtil;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.service.annotation.MarLastMileFeeClaimVerify;
import com.bizark.op.service.mapper.account.AccountMapper;
import com.bizark.op.service.mapper.mar.MarLastMileFeeClaimMapper;
import com.bizark.op.service.mapper.sale.ScGoodsPriceMapper;
import com.bizark.op.service.util.ConvertUtils;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 *
 */
@Service
@Slf4j
public class MarLastMileFeeClaimServiceImpl extends ServiceImpl<MarLastMileFeeClaimMapper, MarLastMileFeeClaim>
        implements MarLastMileFeeClaimService {


    @Autowired
    private MarLastMileFeeClaimLogService marLastMileFeeClaimLogService;

    @Autowired
    private ScGoodsPriceMapper scGoodsPriceMapper;

    @Autowired
    private IScTicketService scTicketService;

    @Autowired
    private IScTicketLogService scTicketLogService;

    @Autowired
    private MarLastMileFeeClaimAttachesService marLastMileFeeClaimAttachesService;

    @Autowired
    private TaskCenterService taskCenterService;

    @Value("${task.center.file.path}")
    private String filePath;


    @Autowired
    private AccountService accountService;

    @Autowired
    private AccountMapper accountMapper;

    @Value("${rpa_query_url}")
    private String rpaReqUrl;


    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private ProductsService productsService;

    @Autowired
    private MarRpaServiceStatusClaimLogService marRpaServiceStatusClaimLogService;

    @Autowired
    private ReimbursementService reimbursementService;

    @Value("${home_query_bi_url}")
    private String QUERY_SALE_NUM_MASTER_URL ;


    /**
     * @param
     * @param marLastMileFeeClaim
     * @description: 商家面单自动发起流程
     * @author: Moore
     * @date: 2025/3/11 14:41
     * @return: void
     **/

    public void getProve(MarLastMileFeeClaim marLastMileFeeClaim) {
        //非商家面单不获取证明图片
        if (marLastMileFeeClaim.getId() == null ||
                !MarLastMileClaimShippingSource.SHOP.getValue().equals(marLastMileFeeClaim.getShippingSource())) {
            return;
        }

        if (marRpaServiceStatusClaimLogService.verifyOrgFlag(marLastMileFeeClaim.getOrganizationId(), MarClaimRpaUrlEnum.CLAIM_PROVE_GET.getCode())) {

            String storeId = "";
            Account account = accountMapper.selectById(marLastMileFeeClaim.getShopId());
            if (account == null) {
                return;
            }

            if (AccountSaleChannelEnum.TIKTOK.getValue().equals(account.getType())) {
                storeId = account.getStoreName();
            } else if (AccountSaleChannelEnum.WALMART.getValue().equals(account.getType())) {
                storeId = account.getFlag();
            } else {
                return;
            }
            if (StringUtils.isEmpty(storeId)) {
                log.info("尾程索赔店铺唯一标识未获取：{}",JSONObject.toJSONString(marLastMileFeeClaim));
                MarLastMileFeeClaimLog marLastMileFeeClaimLog = new MarLastMileFeeClaimLog();
                marLastMileFeeClaimLog.setClaimId(marLastMileFeeClaim.getId());
                marLastMileFeeClaimLog.setOperateContent("调用RPA进行抓取,未获取到店铺唯一标识!");
                marLastMileFeeClaimLog.settingDefaultUpdate();
                marLastMileFeeClaimLogService.save(marLastMileFeeClaimLog);
                return;
            }

            MarLastMileFeeClaimProveReq marLastMileFeeClaimProveReq = new MarLastMileFeeClaimProveReq();
            marLastMileFeeClaimProveReq.setChannel(marLastMileFeeClaim.getChannel());
            marLastMileFeeClaimProveReq.setOrderNo(marLastMileFeeClaim.getOrderNo());
            marLastMileFeeClaimProveReq.setStoreId(storeId);
            marLastMileFeeClaimProveReq.setRpcType(MarClaimRpaUrlEnum.CLAIM_PROVE_GET.getRpcType());
            marLastMileFeeClaimProveReq.setBusinessId(marLastMileFeeClaim.getId());
            marLastMileFeeClaimProveReq.setProveType(Arrays.asList("1", "2"));
            try {
                //调用获取图片信息
                this.sendRpaProveHttp(marLastMileFeeClaim.getShopId(), MarClaimRpaUrlEnum.CLAIM_PROVE_GET.getRpcType(), JSONObject.toJSONString(marLastMileFeeClaimProveReq));
                //设置获取证明中
                marLastMileFeeClaim.setClaimStatus(MarLastMileClaimStatusEnmu.OBTAINING_PROOF.getValue());
                marLastMileFeeClaim.settingDefaultUpdate();
                this.updateById(marLastMileFeeClaim);

                //记录日志
                MarLastMileFeeClaimLog marLastMileFeeClaimLog = new MarLastMileFeeClaimLog();
                marLastMileFeeClaimLog.setClaimId(marLastMileFeeClaim.getId());
                marLastMileFeeClaimLog.setOperateContent("调用RPA抓取证明，并修改状态为：" + MarLastMileClaimStatusEnmu.OBTAINING_PROOF.getName());
                marLastMileFeeClaimLog.settingDefaultUpdate();
                marLastMileFeeClaimLogService.save(marLastMileFeeClaimLog);
            } catch (Exception e) {
                //后续定时补偿
                MarLastMileFeeClaimLog marLastMileFeeClaimLog = new MarLastMileFeeClaimLog();
                marLastMileFeeClaimLog.setClaimId(marLastMileFeeClaim.getId());
                marLastMileFeeClaimLog.setOperateContent("调用RPA进行抓取证明失败!");
                marLastMileFeeClaimLog.settingDefaultUpdate();
                marLastMileFeeClaimLogService.save(marLastMileFeeClaimLog);
                e.printStackTrace();
            }
        }
    }

    /**
     * 索赔提交
     *
     * @param marLastMileFeeClaimDTO
     */
    @Override
    public JSONObject subClaim(MarLastMileFeeClaimDTO marLastMileFeeClaimDTO) {
        JSONObject resJSON = new JSONObject();

        ArrayList<MarLastMileFeeClaim> marLastMileFeeClaimsRes = new ArrayList<>();

        List<Long> ids = marLastMileFeeClaimDTO.getIds();
        boolean ticketFlag = "Y".equals(marLastMileFeeClaimDTO.getTicketFlag());

        if (CollectionUtils.isEmpty(ids)) {
            throw new CustomException("请选择提交行");
        }
        List<MarLastMileFeeClaim> marLastMileFeeClaims = this.baseMapper.selectIdsByFile(ids);
        marLastMileFeeClaims = marLastMileFeeClaims.stream().filter(item -> MarLastMileClaimStatusEnmu.TO_BE_SUBMIT.getValue().equals(item.getClaimStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(marLastMileFeeClaims)) {
            if (ticketFlag) {
                throw new CustomException("当前状态不支持操作提交!");
            } else {
                throw new CustomException("所选数据状态不支持操作提交!");
            }
        }

        //工单相关校验
        if (ticketFlag) {
            if (marLastMileFeeClaims.size() > 1) {
                throw new CustomException("工单操作数量错误!");
            }
            marLastMileFeeClaims.stream().forEach(item -> {
                item.setClaimCause(marLastMileFeeClaimDTO.getClaimCause()); //索赔原因
                item.setRemark(marLastMileFeeClaimDTO.getRemark()); //索赔备注
                item.setPackageSn(marLastMileFeeClaimDTO.getPackageSn()); //包裹号
            });
        }
        //面单来源分组
        Map<Integer, List<MarLastMileFeeClaim>> shipSourceMap = marLastMileFeeClaims.stream().collect(Collectors.groupingBy(MarLastMileFeeClaim::getShippingSource));


        for (Integer shipSource : shipSourceMap.keySet()) {
            List<MarLastMileFeeClaim> marLastMileFeeClaimsList = shipSourceMap.get(shipSource);

            if (MarLastMileClaimShippingSource.PLATFORM.getValue().equals(shipSource)) {
                List<MarLastMileFeeClaim> filterClaimsList = marLastMileFeeClaimsList.stream().filter(item -> StringUtils.isEmpty(item.getClaimCause()) || StringUtils.isEmpty(item.getPackageSn())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(filterClaimsList)) {
                    if (ticketFlag) {
                        throw new CustomException("索赔原因和包裹号需必填!");
                    }
                    marLastMileFeeClaimsRes.addAll(filterClaimsList);
                }
            } else {
                List<MarLastMileFeeClaim> filterClaimsList = marLastMileFeeClaimsList.stream().filter(item -> StringUtils.isEmpty(item.getClaimCause()) || StringUtils.isEmpty(item.getFiles())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(filterClaimsList)) {
                    if (ticketFlag) {
                        throw new CustomException("索赔原因和索赔证据需必填!");
                    }
                    marLastMileFeeClaimsRes.addAll(filterClaimsList);
                }
            }

        }

        //校验未通过
        if (!CollectionUtils.isEmpty(marLastMileFeeClaimsRes)) {
            resJSON.put("errorType", "1"); //
            resJSON.put("dataList", marLastMileFeeClaimsRes); //
            return resJSON;
        }


        ArrayList<MarLastMileFeeClaim> marLastMileFeeClaims1Error = new ArrayList<>();

        //大于4条，开启多任务
        int count = marLastMileFeeClaims.size() / 4;
        List<List<MarLastMileFeeClaim>> lists = ListUtils.splitList(marLastMileFeeClaims, count == 0 ? 1 : count);
        List<CompletableFuture<Object>> claimFuture = lists.stream().map(batch -> CompletableFuture.supplyAsync(() -> {
            batch.forEach(item -> this.commonSubOrResetClaim(item, MarClaimOpTypeEnum.SUMBIT.getValue(), ticketFlag, marLastMileFeeClaims1Error));
            return null;
        }, taskExecutor)).collect(Collectors.toList());

        CompletableFuture.allOf(claimFuture.toArray(new CompletableFuture<?>[0])).join();


        //工单操作，直抛出异常
        if (ticketFlag && !CollectionUtils.isEmpty(marLastMileFeeClaims1Error)) {
            throw new CustomException("调用失败，请稍后重试!");
        }

        //列表操作返回数据给前端展示异常
        if (!CollectionUtils.isEmpty(marLastMileFeeClaims1Error)) {
            resJSON.put("errorType", "2"); //
            resJSON.put("dataList", marLastMileFeeClaims1Error);
        }
        return resJSON;
    }


    /**
     * @param
     * @param marLastMileFeeClaimDTO
     * @description: 索赔手动新增
     * @author: Moore
     * @date: 2025/3/11 16:18
     * @return: void
     **/
    @Override
    public void addClaimInfo(MarLastMileFeeClaimDTO marLastMileFeeClaimDTO) {
        if (StringUtils.isEmpty(marLastMileFeeClaimDTO.getTrackNo()) || marLastMileFeeClaimDTO.getClaimSource() == null) {
            throw new CustomException("track或索赔来源不能为空");
        }


        //校验是否已存在
        LambdaQueryWrapper<MarLastMileFeeClaim> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MarLastMileFeeClaim::getTrackNo, marLastMileFeeClaimDTO.getTrackNo());
        MarLastMileFeeClaim claim = this.baseMapper.selectOne(queryWrapper);
        if (claim != null) {
            throw new CustomException("当前track索赔单已存在");
        }

        //校验发货trak是否存在
        SaleOrderShipemtInfoVO saleOrderShipemtInfoVO = this.baseMapper.selectShipmentTrackingInfo(marLastMileFeeClaimDTO.getOrgId(), marLastMileFeeClaimDTO.getTrackNo(), null);
        if (saleOrderShipemtInfoVO == null) {
            throw new CustomException("发货单无对应track信息");
        }

        //分销仓库校验
        List<String> distrbutionCode = this.baseMapper.selectDistributionWarehouseCode();
        if (!CollectionUtils.isEmpty(distrbutionCode) && (distrbutionCode.stream().anyMatch(item -> item.equals(saleOrderShipemtInfoVO.getOrgWarehouseCode())))) {
            throw new CustomException("分销仓不支持索赔!");
        }


        MarLastMileFeeClaim marLastMileFeeClaim = new MarLastMileFeeClaim();
        marLastMileFeeClaim.setOrganizationId(saleOrderShipemtInfoVO.getOrgId());
        marLastMileFeeClaim.setOrderNo(saleOrderShipemtInfoVO.getOrderNo());//订单号
        marLastMileFeeClaim.setOrderId(saleOrderShipemtInfoVO.getId()); //订单ID
        marLastMileFeeClaim.setOrderItemId(saleOrderShipemtInfoVO.getHeadItemId()); //订单行ID
        marLastMileFeeClaim.setChannel(saleOrderShipemtInfoVO.getChannel()); //渠道
        marLastMileFeeClaim.setShopId(saleOrderShipemtInfoVO.getChannelId()); //店铺ID
        marLastMileFeeClaim.setPstChannelCreated(saleOrderShipemtInfoVO.getPstChannelCreated()); //订单时间
        marLastMileFeeClaim.setTrackNo(saleOrderShipemtInfoVO.getTrackNo()); //track
        //面单类型
        if (AccountSaleChannelEnum.TIKTOK.getValue().equals(saleOrderShipemtInfoVO.getChannel()) || AccountSaleChannelEnum.WALMART.getValue().equals(saleOrderShipemtInfoVO.getChannel())) {
            marLastMileFeeClaim.setShippingSource(MarLastMileClaimShippingSource.SHOP.getValue());
        } else if (AccountSaleChannelEnum.TEMU.getValue().equals(saleOrderShipemtInfoVO.getChannel())) {
            List<Integer> platCode = Arrays.asList(1, 2, 3, 4);
            if (platCode.stream().anyMatch(item -> item.equals(saleOrderShipemtInfoVO.getShippingType()))) {
                marLastMileFeeClaim.setShippingSource(MarLastMileClaimShippingSource.PLATFORM.getValue());
            } else {
                throw new CustomException("TEMU渠道不支持商家面单");
            }
        }

        marLastMileFeeClaim.setPackageSn(saleOrderShipemtInfoVO.getPackageSn()); //包裹号
        marLastMileFeeClaim.setCarrierCode(saleOrderShipemtInfoVO.getCarrierCode()); //承运商
        marLastMileFeeClaim.setServiceCode(saleOrderShipemtInfoVO.getServiceCode());//发货方式
        marLastMileFeeClaim.setLogisticStatus(saleOrderShipemtInfoVO.getLogisticStatus()); //物流状态
        marLastMileFeeClaim.setShipmentDate(saleOrderShipemtInfoVO.getShipmentDate()); //发货时间
        //索赔方
        marLastMileFeeClaim.setClaimant(marLastMileFeeClaim.getShippingSource().equals(MarLastMileClaimShippingSource.SHOP.getValue()) ? MarLastMileclaimantEnmu.EFULFILL.getValue() : MarLastMileclaimantEnmu.TEMU.getValue());
        //索赔来源
        marLastMileFeeClaim.setClaimSource(marLastMileFeeClaimDTO.getClaimSource());
        marLastMileFeeClaim.setErpSku(saleOrderShipemtInfoVO.getErpSku()); //SKu
        marLastMileFeeClaim.setSellerSku(saleOrderShipemtInfoVO.getSellerSku()); //sellerSku
        marLastMileFeeClaim.setShipedQuantity(saleOrderShipemtInfoVO.getShipedQuantity()); //发运数量
        marLastMileFeeClaim.setCurrency(saleOrderShipemtInfoVO.getCurrencyCode()); //币种
        marLastMileFeeClaim.setItemPrice(saleOrderShipemtInfoVO.getItemPrice()); //单价，理解为DDP价格
        marLastMileFeeClaim.setEstimateShipCost(saleOrderShipemtInfoVO.getEstimateShipCost()); //预估物流费
        //索赔金额 （DDP（行单价）x数量）
        if (marLastMileFeeClaim.getItemPrice() != null && marLastMileFeeClaim.getShipedQuantity() != null) {
            marLastMileFeeClaim.setClaimAmount(marLastMileFeeClaim.getItemPrice().multiply(marLastMileFeeClaim.getShipedQuantity()));
        }
        // 面单类型为平台面单的，设置状态为待提交，
        if (MarLastMileClaimShippingSource.PLATFORM.getValue().equals(marLastMileFeeClaim.getShippingSource())) {
            marLastMileFeeClaim.setClaimStatus(MarLastMileClaimStatusEnmu.TO_BE_SUBMIT.getValue());
        }

        // 面单类型为商家面单的，设置状态为待提供证明，
        if (MarLastMileClaimShippingSource.SHOP.getValue().equals(marLastMileFeeClaim.getShippingSource())) {
            marLastMileFeeClaim.setClaimStatus(MarLastMileClaimStatusEnmu.TO_BE_PROVED.getValue());
        }

        // 保存数据
        this.save(marLastMileFeeClaim);

        //记录索赔保存日志
        MarLastMileFeeClaimLog marLastMileFeeClaimLog = new MarLastMileFeeClaimLog();
        marLastMileFeeClaimLog.setClaimId(marLastMileFeeClaim.getId()).setOperateContent("手动新增索赔信息！").settingDefaultCreate();
        marLastMileFeeClaimLogService.save(marLastMileFeeClaimLog);

        //提交证明
        this.getProve(marLastMileFeeClaim);

        //针对面单来源为平台面单且承运商为UPS或者是AMSP_HL (原则上AMSP_HL不会是平台面单  )  的不生成工单
        if (MarLastMileClaimShippingSource.PLATFORM.getValue().equals(marLastMileFeeClaim.getShippingSource()) &&
                (MarLastMileclaimCarrierCodeEnum.UPS.getValue().equalsIgnoreCase(marLastMileFeeClaim.getCarrierCode()) || MarLastMileclaimCarrierCodeEnum.AMSP_HL.getValue().equalsIgnoreCase(marLastMileFeeClaim.getCarrierCode()))) {
            return;
        }
        //创建工单
        Long tkID = this.lastMileFeeClaimTicket(marLastMileFeeClaim);
        MarLastMileFeeClaim marLastMileFeeClaimUpdate = new MarLastMileFeeClaim();
        marLastMileFeeClaimUpdate.setId(marLastMileFeeClaim.getId());
        marLastMileFeeClaimUpdate.setTicketId(tkID);
        this.updateById(marLastMileFeeClaimUpdate);


    }


    /**
     * @Description:索赔列表
     * @Author: wly
     * @Date: 2025-03-12 15:42
     * @Params: [query]
     * @Return: java.util.List<com.bizark.op.api.vo.mar.MarLastMileFeeClaimListVO>
     **/
    @Override
    public List<MarLastMileFeeClaimListVO> getList(MarLastMileFeeClaimQuery query) {

        List<MarLastMileFeeClaimListVO> list = this.baseMapper.getList(query);
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MarLastMileFeeClaimAttaches> queryWrapper = Wrappers.lambdaQuery(MarLastMileFeeClaimAttaches.class).in(MarLastMileFeeClaimAttaches::getClaimId, list.stream().map(MarLastMileFeeClaimListVO::getId).collect(Collectors.toList()));
        List<MarLastMileFeeClaimAttaches> attaches = marLastMileFeeClaimAttachesService.list(queryWrapper);
        if (CollectionUtil.isEmpty(attaches)) {
            return list;
        }
        list.forEach(t -> {
            List<MarLastMileFeeClaimAttaches> collect = attaches.stream().filter(a -> a.getClaimId().equals(t.getId())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)) {
                t.setAttaches(collect);
                t.setClaimEvidence(collect.stream().map(MarLastMileFeeClaimAttaches::getFileUrl).distinct().collect(Collectors.joining(",")));
            }

        });
        return list;
    }

    /**
     * @Description:导出索赔列表
     * @Author: wly
     * @Date: 2025-03-12 15:43
     * @Params: [query, authUserEntity]
     * @Return: void
     **/
    @Override
    public void exportClaimList(MarLastMileFeeClaimQuery query, UserEntity authUserEntity) {
        TaskCenterRequest request = new TaskCenterRequest();
        request.setOrgId(query.getContextId());
        request.setTaskCode("op.fee.claim.list.export");
        List<Object> list = new ArrayList<>();
        list.add(JSON.toJSONString(query));
        request.setArgs(list);
        this.taskCenterService.startTask(request, authUserEntity);
    }

    @Override
    public String asyncExportClaimList(String query) {
        log.info("asyncExportClaimList start ... ");
        int pageNo = 1;
        int pageSize = 5000;
        String path = filePath + "尾程索赔" + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);

        MarLastMileFeeClaimQuery request = JSON.parseObject(query, MarLastMileFeeClaimQuery.class);
        String uploadPath = null;
        File file = null;
        try {
            file = File.createTempFile(path, ".xlsx");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        try (ExcelWriter writer = EasyExcel.write(file, MarLastMileFeeClaimListVO.class).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "尾程索赔").head(MarLastMileFeeClaimListVO.class).build();
            while (true) {
                PageHelper.startPage(pageNo, pageSize, "id desc");

                List<MarLastMileFeeClaimListVO> result = this.getList(request);
                if (CollUtil.isEmpty(result)) {
                    break;
                }
                ConvertUtils.dictConvert(result);

                log.info("尾程索赔导出 query pageNo:{}, list.size = {}, ", pageNo, result.size());
                log.info("尾程索赔导出 exportList.size = " + result.size());
                writer.write(result, writeSheet);
                pageNo++;
            }
            writer.finish();

            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), Files.newInputStream(file.toPath()), "op/lastMileFeeClaim/");
            log.info("尾程索赔导出upload oss success! uploadPath: {}", uploadPath);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("尾程索赔导出upload oss fail! excelInfo:{}", e.getMessage());
        }
        return uploadPath;
    }


    /**
     * @Description:获取承运商列表
     * @Author: wly
     * @Date: 2025-03-12 17:41
     * @Params: [contextId]
     * @Return: java.util.List<java.lang.String>
     **/
    @Override
    public List<String> getCarrierCodeList(Integer contextId) {
        QueryWrapper<MarLastMileFeeClaim> queryWrapper = new QueryWrapper<MarLastMileFeeClaim>()
                .select("distinct carrier_code")
                .eq("organization_id", contextId)
                .isNotNull("carrier_code")
                .ne("carrier_code", "");
        List<MarLastMileFeeClaim> list = this.list(queryWrapper);
        return list.stream().map(MarLastMileFeeClaim::getCarrierCode).collect(Collectors.toList());
    }


    /**
     * @param
     * @param marLastMileFeeClaimDTO
     * @description: 索赔取消
     * @author: Moore
     * @date: 2025/3/12 18:05
     * @return: void
     **/
    @Override
    public void cancelClaim(MarLastMileFeeClaimDTO marLastMileFeeClaimDTO) {

        List<Long> ids = marLastMileFeeClaimDTO.getIds();
        boolean ticketFlag = "Y".equals(marLastMileFeeClaimDTO.getTicketFlag());

        if (CollectionUtils.isEmpty(ids)) {
            throw new CustomException("请选择取消行!");
        }

        List<MarLastMileFeeClaim> marLastMileFeeClaims = this.baseMapper.selectIdsByFile(ids);
        marLastMileFeeClaims = marLastMileFeeClaims.stream().filter(item ->
                MarLastMileClaimStatusEnmu.TO_BE_SUBMIT.getValue().equals(item.getClaimStatus()) //待提交
                        || MarLastMileClaimStatusEnmu.TO_BE_PROVED.getValue().equals(item.getClaimStatus()) //待提供证明状态
                        || (MarLastMileClaimStatusEnmu.OBTAINING_PROOF.getValue().equals(item.getClaimStatus()) && MarLastMileClaimExEnum.ERROR.getValue().equals(item.getExceptionFlag())) //获取证明中且存在异常
                        || (MarLastMileClaimStatusEnmu.CLAIM_SUBMIT_UNDERWAY.getValue().equals(item.getClaimStatus()) && MarLastMileClaimExEnum.ERROR.getValue().equals(item.getExceptionFlag())) //索赔提交中存在异常
        ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(marLastMileFeeClaims)) {
            throw new CustomException(ticketFlag ? "当前数据状态不支持操作取消!" : "所选数据状态不支持操作取消!");
        }


        for (MarLastMileFeeClaim marLastMileFeeClaim : marLastMileFeeClaims) {
            //更新状态
            marLastMileFeeClaim.setExceptionFlag(MarLastMileClaimExEnum.SUCCESS.getValue());
            marLastMileFeeClaim.setClaimStatus(MarLastMileClaimStatusEnmu.CLAIM_CANCEL.getValue()); //索赔中
            marLastMileFeeClaim.settingDefaultUpdate();
            this.updateById(marLastMileFeeClaim);
            //记录索赔日志
            MarLastMileFeeClaimLog marLastMileFeeClaimLog = new MarLastMileFeeClaimLog();
            marLastMileFeeClaimLog.setClaimId(marLastMileFeeClaim.getId()).setOperateContent("取消索赔").settingDefaultCreate();
            marLastMileFeeClaimLogService.save(marLastMileFeeClaimLog);


            if (marLastMileFeeClaim.getTicketId() != null) {
                if (ticketFlag) { //记录工单日志
                    ScTicketLog scTicketLog = new ScTicketLog();
                    scTicketLog.setOperateContent("取消索赔");
                    scTicketLog.setTicketId(marLastMileFeeClaim.getTicketId());
                    scTicketLog.settingDefaultCreate();
                    scTicketLogService.insertScTicketLog(scTicketLog);
                }


                ScTicketLog ticketLog = new ScTicketLog();
                ticketLog.settingDefaultCreate();
                ticketLog.setTicketId(marLastMileFeeClaim.getTicketId());
                ticketLog.setOperateType("Closed");
                ticketLog.setOperateContent("关闭工单:");
                ticketLog.settingDefaultCreate();
                scTicketLogService.insertScTicketLog(ticketLog);
                //关闭
                ScTicket ticket = new ScTicket();
                ticket.setId(marLastMileFeeClaim.getTicketId());
                ticket.setTicketStatus(ScTicketConstant.TICKET_STATUS_CLOSED);
                ticket.settingDefaultUpdate();
                scTicketService.updateScTicketStatus(ticket);
            }


        }

    }


    /**
     * 异常重置
     *
     * @param marLastMileFeeClaimDTO
     */
    @Override
    public JSONObject errorResetClaim(MarLastMileFeeClaimDTO marLastMileFeeClaimDTO) {
        List<Long> ids = marLastMileFeeClaimDTO.getIds();
        boolean ticketFlag = "Y".equals(marLastMileFeeClaimDTO.getTicketFlag());

        if (CollectionUtils.isEmpty(ids)) {
            throw new CustomException("请选择取消行!");
        }

        //仅索赔提交中 或 获取证明中 存在异常
        List<MarLastMileFeeClaim> marLastMileFeeClaims = this.baseMapper.selectIdsByFile(ids);
        marLastMileFeeClaims = marLastMileFeeClaims.stream().filter(item ->
                MarLastMileClaimExEnum.ERROR.getValue().equals(item.getExceptionFlag()) && (
                        MarLastMileClaimStatusEnmu.CLAIM_SUBMIT_UNDERWAY.getValue().equals(item.getClaimStatus()) ||
                                MarLastMileClaimStatusEnmu.OBTAINING_PROOF.getValue().equals(item.getClaimStatus()))
        ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(marLastMileFeeClaims)) {
            throw new CustomException(ticketFlag ? "当前数据无异常,不可重置!" : "请至少勾选一条状态存在异常的数据!");
        }


        if (ticketFlag) { //工单需保存此两项数据，并在后续操作中使用最新参数
            marLastMileFeeClaims.stream().forEach(item -> {
                item.setClaimCause(marLastMileFeeClaimDTO.getClaimCause()); //索赔原因
                item.setRemark(marLastMileFeeClaimDTO.getRemark()); //索赔备注
                item.setPackageSn(marLastMileFeeClaimDTO.getPackageSn()); //包裹号
            });
        }


        ArrayList<MarLastMileFeeClaim> res = new ArrayList<>();
        for (MarLastMileFeeClaim marLastMileFeeClaim : marLastMileFeeClaims) {
            this.commonSubOrResetClaim(marLastMileFeeClaim, MarClaimOpTypeEnum.RESET.getValue(), ticketFlag, res);
        }

        //工单操作直接抛出异常
        if (ticketFlag && !CollectionUtils.isEmpty(res)) {
            throw new CustomException("调用失败，请稍后重试!");
        }


        //列表操作返回数据给前端展示异常
        JSONObject resJSON = new JSONObject();
        //调用异常
        if (!CollectionUtils.isEmpty(res)) {
            resJSON.put("errorType", "2"); //
            resJSON.put("dataList", res);
        }
        return resJSON;


    }


    /**
     * @param
     * @param marLastMileFeeClaim 提交数据
     * @param opType              操作类型 提交/重试
     * @param ticketFlag          是否工单操作
     * @param
     * @description: 通用索赔提交或重置接口
     * @author: Moore
     * @date: 2025/3/12 15:31
     * @return: void
     **/
    public void commonSubOrResetClaim(MarLastMileFeeClaim marLastMileFeeClaim, String opType, Boolean ticketFlag, List<MarLastMileFeeClaim> errorMsg) {

        Integer shippingSource = marLastMileFeeClaim.getShippingSource();
        List<Account> accounts = accountMapper.selectShopIdMapRegisterCompanyName(Arrays.asList(marLastMileFeeClaim.getShopId().intValue()), null);

        if (MarLastMileClaimShippingSource.PLATFORM.getValue().equals(shippingSource)) { //平台采用rpc
            Boolean subFlag = Boolean.TRUE;
            String erMsg = "";
            String reimbursementNo = "";

            //凭条面单异常重置，只可能走RPA
            if (marRpaServiceStatusClaimLogService.verifyOrgFlag(marLastMileFeeClaim.getOrganizationId(), MarClaimRpaUrlEnum.SUB_CLAIM.getCode())) { //开启判断
                //调用
                JSONObject reqParm = new JSONObject();
                reqParm.put("rpcType", MarClaimRpaUrlEnum.SUB_CLAIM.getRpcType()); //RPC 类型
                reqParm.put("businessId", marLastMileFeeClaim.getId()); //业务ID
                reqParm.put("maillId", accounts.get(0).getSellerId()); //店铺MailID
                reqParm.put("domainName", accounts.get(0).getDomainName()); //域名
                reqParm.put("registerCompanyName", accounts.get(0).getRegisterCompanyName()); //主体名
                reqParm.put("appealType", MarLastMileTemuCauseTransitionEnum.getApiBySys(marLastMileFeeClaim.getClaimCause())); //索赔类型 丢失或损坏(系统转换后台TYPE)
                reqParm.put("regionId1", 211); //固定美国
                if (MarLastMileclaimCarrierCodeEnum.USPS.getValue().equalsIgnoreCase(marLastMileFeeClaim.getCarrierCode())) {
                    reqParm.put("shipCompanyId", *********);
                } else if (MarLastMileclaimCarrierCodeEnum.fedex.getValue().equalsIgnoreCase(marLastMileFeeClaim.getCarrierCode())) {
                    reqParm.put("shipCompanyId", *********);
                }
                reqParm.put("packageSn", marLastMileFeeClaim.getPackageSn()); //包裹号
                try {
                    this.sendRpaProveHttp(null, MarClaimRpaUrlEnum.SUB_CLAIM.getRpcType(), JSONObject.toJSONString(reqParm));
                } catch (Exception e) {
                    subFlag = Boolean.FALSE;
                    erMsg = "调用失败";
                }

                if (subFlag) {  //操作成功
                    marLastMileFeeClaim.setExceptionFlag(MarLastMileClaimExEnum.SUCCESS.getValue()); //重置状态
                    marLastMileFeeClaim.setProveExeType(""); //重置异常类型
                    marLastMileFeeClaim.setClaimStatus(MarLastMileClaimStatusEnmu.CLAIM_SUBMIT_UNDERWAY.getValue()); //索赔提交中
                    this.updateById(marLastMileFeeClaim);
                    //记录日志
                    this.commonLogAndticketLog(marLastMileFeeClaim.getId(), (MarClaimOpTypeEnum.RESET.getValue().equals(opType) ? ("异常重置:RPA提交索赔:成功") : "PPA提交索赔：成功"), marLastMileFeeClaim.getTicketId(), Boolean.FALSE, ticketFlag);
                } else {
                    //操作失败
                    this.commonLogAndticketLog(marLastMileFeeClaim.getId(), (MarClaimOpTypeEnum.RESET.getValue().equals(opType) ? ("异常重置:RPA提交索赔，失败(" + erMsg + ")") : "PPA提交索赔：失败(" + erMsg + ")"), marLastMileFeeClaim.getTicketId(), Boolean.TRUE, ticketFlag);
                    errorMsg.add(marLastMileFeeClaim); //记录异常行，供前端使用
                    if (ticketFlag) { //工单操作，可能要更新 索赔原因和 包裹号
                        this.updateById(marLastMileFeeClaim);
                    }
                }
            } else {
                //更新状态
                marLastMileFeeClaim.setExceptionFlag(MarLastMileClaimExEnum.SUCCESS.getValue());
                marLastMileFeeClaim.setClaimStatus(MarLastMileClaimStatusEnmu.CLAIM_UNDERWAY.getValue()); //索赔中
                marLastMileFeeClaim.settingDefaultUpdate();
                this.updateById(marLastMileFeeClaim);
                //记录索赔日志
                this.commonLogAndticketLog(marLastMileFeeClaim.getId(), "提交索赔（未开启RPA）,更新状态为索赔中", marLastMileFeeClaim.getTicketId(), Boolean.FALSE, ticketFlag);

                //完成工单
                ScTicket ticket = new ScTicket();
                ticket.setId(marLastMileFeeClaim.getTicketId());
                ticket.setTicketStatus("FINISH");
                ticket.settingDefaultUpdate();
                scTicketService.updateScTicketStatus(ticket);
            }

        }

        if (MarLastMileClaimShippingSource.SHOP.getValue().equals(shippingSource)) { //商家面单
            Boolean subFlag = Boolean.TRUE;
            String erMsg = "";
            String reimbursementNo = "";
            try {
                if (MarClaimOpTypeEnum.RESET.getValue().equals(opType)) {  //异常重置，商家面单重试只有是获取证明
                    //判断是否服务开启
                    if (marRpaServiceStatusClaimLogService.verifyOrgFlag(marLastMileFeeClaim.getOrganizationId(), MarClaimRpaUrlEnum.CLAIM_PROVE_GET.getCode())) {
                        MarLastMileFeeClaimProveReq marLastMileFeeClaimProveReq = new MarLastMileFeeClaimProveReq();
                        marLastMileFeeClaimProveReq.setChannel(marLastMileFeeClaim.getChannel());
                        marLastMileFeeClaimProveReq.setOrderNo(marLastMileFeeClaim.getOrderNo());
                        marLastMileFeeClaimProveReq.setRpcType(MarClaimRpaUrlEnum.CLAIM_PROVE_GET.getRpcType());
                        marLastMileFeeClaimProveReq.setBusinessId(marLastMileFeeClaim.getId());
                        marLastMileFeeClaimProveReq.setProveType(Arrays.asList(marLastMileFeeClaim.getProveExeType().split(",")));


                        String storeId = "";
                        Account account = accountMapper.selectById(marLastMileFeeClaim.getShopId());
                        if (account == null) {
                            subFlag = Boolean.FALSE;
                            erMsg = "未获取到店铺信息";
                            return;
                        }

                        if (AccountSaleChannelEnum.TIKTOK.getValue().equals(account.getType())) {
                            JSONObject jsonObject = JSONObject.parseObject(account.getConnectStr());
                            storeId = jsonObject.getString("storeId");
                        } else if (AccountSaleChannelEnum.WALMART.getValue().equals(account.getType())) {
                            storeId = account.getFlag();
                        } else {
                            return;
                        }
                        if (StringUtils.isEmpty(storeId)) {
                            subFlag = Boolean.FALSE;
                            erMsg = "未获取到店铺唯一标识";
                            return;
                        }
                        marLastMileFeeClaimProveReq.setStoreId(storeId);
                        //调用获取图片信息
                        this.sendRpaProveHttp(marLastMileFeeClaim.getShopId(), MarClaimRpaUrlEnum.CLAIM_PROVE_GET.getRpcType(), JSONObject.toJSONString(marLastMileFeeClaimProveReq));
                    } else {
                        subFlag = Boolean.FALSE;
                        erMsg = "未开启RPA服务";
                    }
                } else {
                    List<MarLastMileFeeClaimAttaches> fileList = marLastMileFeeClaimAttachesService.lambdaQuery().in(MarLastMileFeeClaimAttaches::getClaimId, marLastMileFeeClaim.getId()).list();
                    UserEntity userEntity = new UserEntity();
                    userEntity.setId(-1);
                    userEntity.setName("system");
                    ReimbursementRequest reimbursementRequest = new ReimbursementRequest();
                    reimbursementRequest.setOrganizationId(marLastMileFeeClaim.getOrganizationId()); //组织ID
                    reimbursementRequest.setStatus(2); // 2即为待审批
                    reimbursementRequest.setReimbursementType(2); //代表尾程
                    reimbursementRequest.setReimbursementReason(Integer.valueOf(marLastMileFeeClaim.getClaimCause())); //索赔原因
                    ReimbursementShipTailDataProps reimbursementShipTailDataProps = new ReimbursementShipTailDataProps();
                    reimbursementShipTailDataProps.setShippingNo(marLastMileFeeClaim.getOrderNo()); //发货订单号
                    reimbursementShipTailDataProps.setTrackingNo(marLastMileFeeClaim.getTrackNo()); //发货track
                    reimbursementShipTailDataProps.setFreight(marLastMileFeeClaim.getEstimateShipCost()); //运费（实际取预估物流费）

                    //发货时间
                    if (marLastMileFeeClaim.getShipmentDate()!=null){
                        Date shipmentDate = marLastMileFeeClaim.getShipmentDate();
                        LocalDate localDate = shipmentDate.toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDate();;
                        reimbursementShipTailDataProps.setShipDate(localDate);
                    }

                    reimbursementShipTailDataProps.setErpsku(marLastMileFeeClaim.getErpSku()); //ERPSKU
                    reimbursementShipTailDataProps.setSaleCarrier(marLastMileFeeClaim.getServiceCode()); //发货方式
                    if (!CollectionUtils.isEmpty(fileList)){
                        JSONObject attachMetaItmes = new JSONObject();
                        List<JSONObject> items = fileList.stream().map(item -> {
                            JSONObject itemsImage = new JSONObject();
                            itemsImage.put("attachUrl", item.getFileUrl());  //文件URL
                            itemsImage.put("attachTitle", item.getFileName()); //文件标题
                            itemsImage.put("attachBrief", item.getFileUrl()); //文件URl
                            return itemsImage;
                        }).collect(Collectors.toList());
                        attachMetaItmes.put("items", items);
                        reimbursementShipTailDataProps.setAttachMeta(JSONObject.toJSONString(attachMetaItmes));
                    }

                   //数量信息
                    JSONObject productInfo = new JSONObject();
                    productInfo.put("qty", marLastMileFeeClaim.getShipedQuantity()); //数量
                    productInfo.put("ddp", marLastMileFeeClaim.getItemPrice());//ddp 价
                    productInfo.put("prrice", marLastMileFeeClaim.getClaimAmount());//产品价格  （即为索赔金额）
                    reimbursementShipTailDataProps.setProductItems(JSONObject.toJSONString(Arrays.asList(productInfo)));
                    reimbursementRequest.setProps(JSONObject.toJSONString(reimbursementShipTailDataProps)); //设置prop

                    log.info("调用OMS索赔信息：{},tracking:{}",JSONObject.toJSONString(reimbursementRequest),marLastMileFeeClaim.getTrackNo());
                    ReimbursementResponse reimbursementResponse = reimbursementService.saveOrUpdate(reimbursementRequest, userEntity);
                    log.info("调用后获取到索赔信息：{}",JSONObject.toJSONString(reimbursementRequest));
                    if (reimbursementResponse != null&&!StringUtils.isEmpty(reimbursementResponse.getReimbursementNo())) {
                         reimbursementNo = reimbursementResponse.getReimbursementNo();
                    }else {
                        subFlag = Boolean.FALSE;
                        erMsg = "索赔单号获取为空";
                    }
                }
            } catch (Exception e) {
                subFlag = Boolean.FALSE;
                erMsg = "调用失败";
            }


            if (subFlag) {  //操作成功
                marLastMileFeeClaim.setExceptionFlag(MarLastMileClaimExEnum.SUCCESS.getValue()); //重置状态
                marLastMileFeeClaim.setProveExeType(""); //重置异常类型
                if (MarClaimOpTypeEnum.SUMBIT.getValue().equals(opType)) { //提交操作 且成功修改工单
                    marLastMileFeeClaim.setClaimStatus(MarLastMileClaimStatusEnmu.CLAIM_UNDERWAY.getValue());
                    marLastMileFeeClaim.setClaimId(reimbursementNo); //索赔单号
                    ScTicket ticket = new ScTicket();
                    ticket.setId(marLastMileFeeClaim.getTicketId());
                    ticket.setTicketStatus("FINISH");      //完成工单
                    ticket.settingDefaultUpdate();
                    scTicketService.updateScTicketStatus(ticket);
                }
                this.updateById(marLastMileFeeClaim);
                //记录日志
                this.commonLogAndticketLog(marLastMileFeeClaim.getId(), (MarClaimOpTypeEnum.RESET.getValue().equals(opType) ? ("异常重置:提交RPA获取证明") : "提交索赔"), marLastMileFeeClaim.getTicketId(), MarClaimOpTypeEnum.RESET.getValue().equals(opType), ticketFlag);
            } else {
                //记录日志
                this.commonLogAndticketLog(marLastMileFeeClaim.getId(), (MarClaimOpTypeEnum.RESET.getValue().equals(opType) ? ("异常重置:提交RPA获取证明，失败(" + erMsg + ")") : "提交索赔：失败(" + erMsg + ")"), marLastMileFeeClaim.getTicketId(), MarClaimOpTypeEnum.RESET.getValue().equals(opType), ticketFlag);
                errorMsg.add(marLastMileFeeClaim); //记录异常行，供前端使用
                if (ticketFlag) { //工单操作，可能要更新 索赔原因和 包裹号
                    this.updateById(marLastMileFeeClaim);
                }
            }
        }

    }


    public void sendRpaProveHttp(Long shopId, Integer proveType, String reqParm) throws Exception {
        //记录异常日志
        String reqUrl = rpaReqUrl + MarClaimRpaUrlEnum.getByValue(proveType).getUrl();
        HttpRequest request = HttpUtil.createPost(reqUrl);
        request.body(reqParm);
        log.info("调用rpa进行业务操作" + MarClaimRpaUrlEnum.getByValue(proveType).getValue() + "{},Body：{}", reqUrl, reqParm);
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            TimeUnit.SECONDS.sleep(2);
            response = request.execute();
            if (!response.isOk()) {
                log.info("调用RPC进行业务操作" + MarClaimRpaUrlEnum.getByValue(proveType).getValue() + "失败URL：{},Body：{},Res:{}", reqUrl, reqParm, JSONObject.toJSONString(response));
                throw new CustomException("调用失败");
            }
        }
    }

    public Boolean rpcServiceOpenStatus(Integer orgId) {
        return Boolean.TRUE;
    }


    /**
     * 记录促销日志,及工单日志
     */
    public void commonLogAndticketLog(Long claimId, String logMsg,
                                      Long ticketId,
                                      Boolean logError,
                                      Boolean ticketLogFlag) {
        MarLastMileFeeClaimLog marLastMileFeeClaimLog = new MarLastMileFeeClaimLog();
        marLastMileFeeClaimLog.setClaimId(claimId).setOperateContent(logMsg).settingDefaultCreate();
        if (logError) {
            marLastMileFeeClaimLog.setExceptionFlag(MarLastMileClaimExEnum.ERROR.getValue()); //异常
        }
        marLastMileFeeClaimLogService.save(marLastMileFeeClaimLog);

        if (ticketId != null) {
            if (ticketLogFlag) { //记录工单日志
                ScTicketLog scTicketLog = new ScTicketLog();
                scTicketLog.setOperateContent(logMsg);
                scTicketLog.setTicketId(ticketId);
                scTicketLog.settingDefaultCreate();
                scTicketLogService.insertScTicketLog(scTicketLog);
            }
        }
    }

    /**
     * @Description:标记成功
     * @Author: wly
     * @Date: 2025-03-13 10:48
     * @Params: [marLastMileFeeClaim]
     * @Return: void
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    @MarLastMileFeeClaimVerify
    public void markSuccess(MarLastMileFeeClaim marLastMileFeeClaim) {

        MarLastMileFeeClaim update = new MarLastMileFeeClaim();
        update.setId(marLastMileFeeClaim.getId());
        update.settingDefaultUpdate();
        update.setClaimStatus(MarLastMileClaimStatusEnmu.CLAIM_SUCCESS.getValue());
        update.setCompensationAmount(marLastMileFeeClaim.getCompensationAmount());
        this.updateById(update);

        MarLastMileFeeClaimLog marLastMileFeeClaimLog = new MarLastMileFeeClaimLog();
        marLastMileFeeClaimLog.setClaimId(marLastMileFeeClaim.getId())
                .setOrgId(marLastMileFeeClaim.getOrganizationId().longValue())
                .setOperateContent(MarLastMileclaimOperateLogEnum.MARK_SUCCESS.getName())
                .settingDefaultCreate();
        marLastMileFeeClaimLogService.save(marLastMileFeeClaimLog);
        if (marLastMileFeeClaim.getTicketViewOperate()) {
            ScTicketLog scTicketLog = new ScTicketLog();
            scTicketLog.settingDefaultCreate();
            scTicketLog.setOperateTime(DateUtils.getNowDate());
            scTicketLog.setOperateContent(MarLastMileclaimOperateLogEnum.MARK_SUCCESS.getName());
            scTicketLog.setTicketId(marLastMileFeeClaim.getTicketId());
            scTicketLogService.insertScTicketLog(scTicketLog);
        }

    }

    /**
     * @Description:标记失败
     * @Author: wly
     * @Date: 2025-03-13 10:49
     * @Params: [marLastMileFeeClaim]
     * @Return: void
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    @MarLastMileFeeClaimVerify
    public void markFail(MarLastMileFeeClaim marLastMileFeeClaim) {
        MarLastMileFeeClaim update = new MarLastMileFeeClaim();
        update.setId(marLastMileFeeClaim.getId());
        update.settingDefaultUpdate();
        update.setClaimStatus(MarLastMileClaimStatusEnmu.CLAIM_FAIL.getValue());
        update.setClaimFailMsg(marLastMileFeeClaim.getClaimFailMsg());
        this.updateById(update);

        MarLastMileFeeClaimLog marLastMileFeeClaimLog = new MarLastMileFeeClaimLog();
        marLastMileFeeClaimLog.setClaimId(marLastMileFeeClaim.getId())
                .setOrgId(marLastMileFeeClaim.getOrganizationId().longValue())
                .setOperateContent(MarLastMileclaimOperateLogEnum.MARK_FAIL.getName())
                .settingDefaultCreate();
        marLastMileFeeClaimLogService.save(marLastMileFeeClaimLog);
        if (marLastMileFeeClaim.getTicketViewOperate()) {
            ScTicketLog scTicketLog = new ScTicketLog();
            scTicketLog.settingDefaultCreate();
            scTicketLog.setOperateTime(DateUtils.getNowDate());
            scTicketLog.setOperateContent(MarLastMileclaimOperateLogEnum.MARK_FAIL.getName());
            scTicketLog.setTicketId(marLastMileFeeClaim.getTicketId());
            scTicketLogService.insertScTicketLog(scTicketLog);
        }
    }


    /**
     * @Description:刷新物流状态
     * @Author: wly
     * @Date: 2025-03-13 11:27
     * @Params: []
     * @Return: void
     **/
    @Override
    public void updateLogisticStatus() {

        int pageNo = 1;
        int pageSize = 5000;
        List<Integer> conditionList = Arrays.asList(MarLastMileClaimStatusEnmu.CLAIM_UNDERWAY.getValue(),
                MarLastMileClaimStatusEnmu.CLAIM_SUCCESS.getValue(),
                MarLastMileClaimStatusEnmu.CLAIM_FAIL.getValue(),
                MarLastMileClaimStatusEnmu.CLAIM_CANCEL.getValue());
        Long maxId = 0L;
        while (true) {
            PageHelper.startPage(pageNo, pageSize);
            //非(索赔中、索赔成功、索赔失败、索赔取消)的
            MarLastMileFeeClaim marLastMileFeeClaim = new MarLastMileFeeClaim();
            marLastMileFeeClaim.setClaimStatusList(conditionList);
            marLastMileFeeClaim.setId(maxId);
            List<MarLastMileFeeClaim> updteList = this.baseMapper.selectNeedUpdateLogisticStatusList(marLastMileFeeClaim);
            if (CollectionUtil.isEmpty(updteList)) {
                break;
            }
            maxId = updteList.stream().max(Comparator.comparing(MarLastMileFeeClaim::getId)).get().getId();
            pageNo++;
            updteList.forEach(t -> t.setUpdatedAt(DateUtils.getNowDate()));
            this.updateBatchById(updteList);
        }
    }


    /**
     * Description: 定时任务同步退货数据生成尾程索赔数据
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2025/3/14
     */
    @Override
    public void syncReturnInfoShipmentTrackingInfo() {
        List<String> distrbutionCode = this.baseMapper.selectDistributionWarehouseCode();
        int pageNo = 1;
        int pageSize = 2000;
        while (true) {
            PageHelper.startPage(pageNo, pageSize);
            List<SaleOrderShipemtInfoVO> saleOrderShipemtInfoVOList = this.baseMapper.syncReturnInfoShipmentTrackingInfo();
            if (CollectionUtil.isEmpty(saleOrderShipemtInfoVOList)) {
                return;
            }
            for (SaleOrderShipemtInfoVO saleOrderShipemtInfoVO : saleOrderShipemtInfoVOList) {
                this.saveMarLastMileFeeClaimInfo(saleOrderShipemtInfoVO, 2, distrbutionCode);
                pageNo++;
            }
        }
    }

    /**
     * Description: 定时任务同步退货数据生成尾程索赔数据
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2025/3/14
     */
    @Override
    public void walmartSyncReturnInfoShipmentTrackingInfo() {
        List<String> distrbutionCode = this.baseMapper.selectDistributionWarehouseCode();
        int pageNo = 1;
        int pageSize = 500;
        while (true) {
            PageHelper.startPage(pageNo, pageSize);

            List<SaleOrderShipemtInfoVO> saleOrderShipemtInfoVOList = this.baseMapper.walmartSyncReturnInfoShipmentTrackingInfo();
            if (CollectionUtil.isEmpty(saleOrderShipemtInfoVOList)) {
                return;
            }
            for (SaleOrderShipemtInfoVO saleOrderShipemtInfoVO : saleOrderShipemtInfoVOList) {
                this.saveMarLastMileFeeClaimInfo(saleOrderShipemtInfoVO, 2, distrbutionCode);
                pageNo++;
            }
        }
    }


    /**
     * Description: 定时任务同步退款数据生成尾程索赔数据
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2025/3/14
     */
    @Override
    public void syncRefundInfoShipmentTrackingInfo() {

        //查询分销仓
        List<String> distrbutionCode = this.baseMapper.selectDistributionWarehouseCode();
        int pageNo = 1;
        int pageSize = 1000;
        while (true) {
            PageHelper.startPage(pageNo, pageSize);
            List<SaleOrderShipemtInfoVO> saleOrderShipemtInfoVOList = this.baseMapper.syncRefundInfoShipmentTrackingInfo();
            if (CollectionUtil.isEmpty(saleOrderShipemtInfoVOList)) {
                return;
            }
            for (SaleOrderShipemtInfoVO saleOrderShipemtInfoVO : saleOrderShipemtInfoVOList) {
                this.saveMarLastMileFeeClaimInfo(saleOrderShipemtInfoVO, MarLastMileClaimSourceEnum.REFUND.getValue(), distrbutionCode);
            }
            pageNo++;
        }
    }


    /**
     * 尾程索赔生成工单
     *
     * @param marLastMileFeeClaim
     */
    @Override
    public Long lastMileFeeClaimTicket(MarLastMileFeeClaim marLastMileFeeClaim) {
        ScTicket scTicket = new ScTicket();
        scTicket.setOrganizationId(marLastMileFeeClaim.getOrganizationId() != null ? marLastMileFeeClaim.getOrganizationId().longValue() : null);
        scTicket.setTicketName("跟踪号:" + marLastMileFeeClaim.getTrackNo() + " 尾程索赔");
        scTicket.setTicketNumber(com.bizark.op.common.util.DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(marLastMileFeeClaim.getId().toString(), "l", 5, "0") + RandomUtil.randomNumbers(4));
        scTicket.setTicketSource(marLastMileFeeClaim.getChannel());
        scTicket.setTicketType(ScTicketConstant.TICKET_TYPE_CLAIM);
        scTicket.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH);
        scTicket.setSourceId(marLastMileFeeClaim.getId());
        scTicket.setSourceDocument("mar_last_mile_fee_claim");
        String shopName = null;
        //TODO 是否去除？
        if (marLastMileFeeClaim.getShopId() != null) {
            Account account = accountService.getById(marLastMileFeeClaim.getShopId());
            if (account != null) {
                shopName = account.getTitle();
            }
        }
        String remark = "店铺名称：" + shopName;
        scTicket.setRemark(remark);
        //匹配订单
        scTicket.setOrderId(marLastMileFeeClaim.getOrderId());
        scTicket.setAmazonOrderId(marLastMileFeeClaim.getOrderNo());
        scTicket.setSellerSku(marLastMileFeeClaim.getSellerSku());
        scTicket.setGoodsSku(marLastMileFeeClaim.getErpSku());
        scTicket.setShopId(marLastMileFeeClaim.getShopId());
        scTicketService.insertScTicket(scTicket);
        return scTicket.getId();
    }


    /**
     * @param
     * @param scTicket
     * @description: 获取索赔工单详情信息
     * @author: Moore
     * @date: 2025/3/17 13:41
     * @return: com.bizark.op.api.entity.op.mar.MarLastMileFeeClaimTicketVO
     **/
    @Override
    public MarLastMileFeeClaimTicketVO queryClaimDetailById(ScTicket scTicket) {
        if (scTicket == null || scTicket.getSourceId() == null || StringUtils.isEmpty(scTicket.getSourceDocument())) {
            return new MarLastMileFeeClaimTicketVO();
        }

        MarLastMileFeeClaimTicketVO marLastMileFeeClaimTicketVO = new MarLastMileFeeClaimTicketVO();
        MarLastMileFeeClaim claimInfo = this.getById(scTicket.getSourceId());
        BeanUtils.copyProperties(claimInfo, marLastMileFeeClaimTicketVO);

        //获取明细信息
        MarLastMileFeeClaimTicketVO.SkuInfoList skuInfoList = new MarLastMileFeeClaimTicketVO.SkuInfoList();
        if (!StringUtils.isEmpty(claimInfo.getErpSku())) {
            Products one = productsService.lambdaQuery().select(Products::getName)
                    .eq(Products::getOrgId, scTicket.getOrganizationId()).
                            eq(Products::getErpsku, claimInfo.getErpSku()).one();
            skuInfoList.setProductName(one != null ? one.getName() : null);
        }
        //索赔信息
        skuInfoList.setClaimAmount(claimInfo.getClaimAmount()); //索赔金额
        skuInfoList.setCurrency(claimInfo.getCurrency()); //币种
        skuInfoList.setErpSku(claimInfo.getErpSku()); //款号
        skuInfoList.setQuantity(claimInfo.getShipedQuantity()); //发货数量
        marLastMileFeeClaimTicketVO.setClaimList(Arrays.asList(skuInfoList));
        //附件信息
        marLastMileFeeClaimTicketVO.setUrlList(marLastMileFeeClaimAttachesService.lambdaQuery().eq(MarLastMileFeeClaimAttaches::getClaimId, scTicket.getSourceId()).list());

        //日志信息
        if (MarLastMileClaimExEnum.ERROR.getValue().equals(claimInfo.getExceptionFlag())) {
            MarLastMileFeeClaimLog marLastMileFeeClaimLog =
                    marLastMileFeeClaimLogService.lambdaQuery().
                            eq(MarLastMileFeeClaimLog::getClaimId, claimInfo.getId()).
                            eq(MarLastMileFeeClaimLog::getExceptionFlag, -1)
                            .orderByDesc(MarLastMileFeeClaimLog::getId).last("limit 1").one();
            if (marLastMileFeeClaimLog != null) {
                marLastMileFeeClaimTicketVO.setLogMsg(marLastMileFeeClaimLog.getOperateContent());
            }
        }
        marLastMileFeeClaimTicketVO.setExceptionFlag(claimInfo.getExceptionFlag());
        return marLastMileFeeClaimTicketVO;

    }


    /**
     * 接收证明结果
     *
     * @param receive
     */
    @Override
    public void receiveProve(MarClaimProveInfo receive) {
        Long businessId = receive.getBusinessId();
        List<MarClaimProveInfo.FilesBean> files = receive.getFiles();
        if (businessId == null) {
            return;
        }

        if (CollectionUtils.isEmpty(files)) {
            return;
        }

        //获取业务数据
        MarLastMileFeeClaim marLastMileFeeClaim = this.baseMapper.selectById(businessId);
        if (marLastMileFeeClaim == null || !MarLastMileClaimStatusEnmu.OBTAINING_PROOF.getValue().equals(marLastMileFeeClaim.getClaimStatus())) {
            log.info("证明状态不符：{}", JSONObject.toJSONString(receive));
            return;
        }
        //异常标识（只要有一个URL为空即为空）
        boolean exFlag = files.stream().anyMatch(item -> StringUtils.isEmpty(item.getUrl()));


        for (MarClaimProveInfo.FilesBean file : files) {
            String url = file.getUrl();
            if (StringUtils.isEmpty(url)) {
                continue;
            }
            MarLastMileFeeClaimAttaches marLastMileFeeClaimAttaches = new MarLastMileFeeClaimAttaches();
            marLastMileFeeClaimAttaches.setClaimId(businessId);
            marLastMileFeeClaimAttaches.setFileUrl(url);
            marLastMileFeeClaimAttaches.setFileName(file.getFileName());
            marLastMileFeeClaimAttaches.settingDefaultSystemCreate();
            marLastMileFeeClaimAttaches.setFileType(file.getProveType()); //操作类型
            marLastMileFeeClaimAttachesService.save(marLastMileFeeClaimAttaches);
        }

        //记录日志


        //更新主表
        MarLastMileFeeClaim marLastMileFeeClaimUpdate = new MarLastMileFeeClaim();
        marLastMileFeeClaimUpdate.setId(businessId);
        if (exFlag) { //异常
            marLastMileFeeClaimUpdate.setExceptionFlag(MarLastMileClaimExEnum.ERROR.getValue());
            String exType = files.stream().filter(item -> StringUtils.isEmpty(item.getUrl())).map(MarClaimProveInfo.FilesBean::getProveType).map(String::valueOf).collect(Collectors.joining(","));
            marLastMileFeeClaimUpdate.setProveExeType(exType);//证明异常类型
        } else {
            marLastMileFeeClaimUpdate.setExceptionFlag(MarLastMileClaimExEnum.SUCCESS.getValue());
            marLastMileFeeClaimUpdate.setProveExeType("");
            marLastMileFeeClaimUpdate.setClaimStatus(MarLastMileClaimStatusEnmu.TO_BE_SUBMIT.getValue()); //待提交
        }
        marLastMileFeeClaimUpdate.settingDefaultSystemUpdate();
        this.updateById(marLastMileFeeClaimUpdate);

        String logMsg = "获取到证明信息" + (exFlag ? ":异常" : ":成功");
        MarLastMileFeeClaimLog marLastMileFeeClaimLog = new MarLastMileFeeClaimLog();
        marLastMileFeeClaimLog.setClaimId(businessId).setOperateContent(logMsg).setExceptionFlag(exFlag ? MarLastMileClaimExEnum.ERROR.getValue() : MarLastMileClaimExEnum.SUCCESS.getValue()).settingDefaultSystemCreate();
        marLastMileFeeClaimLogService.save(marLastMileFeeClaimLog);

    }

    /**
     * @param
     * @param receive
     * @description:接收申诉索赔提交结果
     * @author: Moore
     * @date: 2025/4/1 15:06
     * @return: void
     **/
    @Override
    public void receiveSubResule(MarClaimProveInfo receive) {
        Long businessId = receive.getBusinessId();
        if (businessId == null) {
            return;
        }

        //获取业务数据
        MarLastMileFeeClaim marLastMileFeeClaim = this.baseMapper.selectById(businessId);
        if (marLastMileFeeClaim == null || !MarLastMileClaimStatusEnmu.CLAIM_SUBMIT_UNDERWAY.getValue().equals(marLastMileFeeClaim.getClaimStatus())) {
            log.info("申诉提交结果状态不符：{},dbType:{}", JSONObject.toJSONString(receive), marLastMileFeeClaim.getClaimStatus());
            return;
        }

        //异常
        boolean exFlag = !new Integer(200).equals(receive.getCode());

        //更新主表
        MarLastMileFeeClaim marLastMileFeeClaimUpdate = new MarLastMileFeeClaim();
        marLastMileFeeClaimUpdate.setId(businessId);
        if (exFlag) {
            marLastMileFeeClaimUpdate.setExceptionFlag(MarLastMileClaimExEnum.ERROR.getValue());
        } else {
            marLastMileFeeClaimUpdate.setExceptionFlag(MarLastMileClaimExEnum.SUCCESS.getValue());
            //索赔ID
            marLastMileFeeClaimUpdate.setClaimId(receive.getClaimId());
            //转换索赔时间
            if (!StringUtils.isEmpty(receive.getClaimTime())) {
                try {
                    String pattern = receive.getClaimTime().contains(":") ? "yyyy/MM/dd HH:mm:ss" : "yyyy/MM/dd";
                    marLastMileFeeClaimUpdate.setClaimTime(DateUtil.parse(receive.getClaimTime(), pattern));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            marLastMileFeeClaimUpdate.setProveExeType("");
            marLastMileFeeClaimUpdate.setClaimStatus(MarLastMileClaimStatusEnmu.CLAIM_UNDERWAY.getValue()); //索赔中
        }
        marLastMileFeeClaimUpdate.settingDefaultSystemUpdate();
        this.updateById(marLastMileFeeClaimUpdate);

        String logMsg = "获取到RPA提交索赔：" + (exFlag ? "提交失败" : "提交成功");
        MarLastMileFeeClaimLog marLastMileFeeClaimLog = new MarLastMileFeeClaimLog();
        marLastMileFeeClaimLog.setClaimId(businessId).setOperateContent(logMsg).setExceptionFlag(exFlag ? MarLastMileClaimExEnum.ERROR.getValue() : MarLastMileClaimExEnum.SUCCESS.getValue()).settingDefaultSystemCreate();
        marLastMileFeeClaimLogService.save(marLastMileFeeClaimLog);
    }


    /**
     * @Description:索赔列表合计
     * @Author: wly
     * @Date: 2025-03-20 11:55
     * @Params: [query]
     * @Return: com.bizark.op.api.vo.mar.MarLastMileFeeClaimListVO
     **/

    @Override
    public MarLastMileFeeClaimListVO total(MarLastMileFeeClaimQuery query) {
        return this.baseMapper.getListTotal(query);
    }


    /**
     * Description: 同步来源为长期未妥投的尾程索赔
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2025/3/21
     */
    @Override
    public void syncLongTimeNotArrive(String orderStart, String endStart) {


        //查询分销仓
        List<String> distrbutionCode = this.baseMapper.selectDistributionWarehouseCode();


        int page = 1;
        while (true) {
            //当前PST时间
            LocalDateTime localNow = LocalDateTime.now(TimeZone.getTimeZone("America/Los_Angeles").toZoneId());
            String nowPstTime = localNow.format(DateTimeFormatter.ISO_DATE);
            DateTime nowPstDate = cn.hutool.core.date.DateUtil.parse(nowPstTime);
            String nowPstDateString = com.bizark.op.common.util.DateUtil.convertDateToString(nowPstDate);

            //当前pst时间-20天
            String shipmentDate = DateUtil.format(cn.hutool.core.date.DateUtil.offsetDay(nowPstDate, -20), DatePattern.NORM_DATE_PATTERN);
            //当前页
            JSONObject jsonObject = this.useLongTimeNotArriveInterfaceRequest(String.valueOf(page), (StringUtil.isEmpty(orderStart) ? "2025-01-01" : orderStart), (StringUtil.isEmpty(endStart) ? nowPstDateString : endStart), shipmentDate);

            //分销仓库校验
            if (jsonObject != null) {
                JSONArray jsonArray = jsonObject.getJSONArray("content");
                if (jsonArray != null && jsonArray.size() > 0) {
                    List<MarLastMileFeeClaimInterfaceVo> marLastMileFeeClaimInterfaceVoList = jsonArray.toJavaList(MarLastMileFeeClaimInterfaceVo.class);
                    this.longTimeNotArriveAddClaimInfo(marLastMileFeeClaimInterfaceVoList, distrbutionCode);
                } else {
                    return;
                }
            } else {
                return;
            }
            page++;
        }


    }

    /**
     * @param marLastMileFeeClaimListVOList
     * @description: 索赔新增来源为长期未妥投的尾程索赔
     * @author: Moore
     * @date: 2025/3/11 16:17
     * @return: void
     **/
    @Override
    public void longTimeNotArriveAddClaimInfo(List<MarLastMileFeeClaimInterfaceVo> marLastMileFeeClaimListVOList, List<String> distrbutionCode) {

        for (MarLastMileFeeClaimInterfaceVo marLastMileFeeClaimInterfaceVo : marLastMileFeeClaimListVOList) {
            String trackNo = marLastMileFeeClaimInterfaceVo.getTrackNo();
            if (StringUtils.isEmpty(trackNo)) {
                continue;
            }
            MarLastMileFeeClaim marLastMileFeeClaimInfo = this.lambdaQuery().eq(MarLastMileFeeClaim::getTrackNo, marLastMileFeeClaimInterfaceVo.getTrackNo()).one();
            if (marLastMileFeeClaimInfo != null) {
                log.info("已存在重复track：{}", JSONObject.toJSONString(marLastMileFeeClaimInfo));
                continue;
            }

            try {
                //分销仓库校验
                if (!CollectionUtils.isEmpty(distrbutionCode) && (distrbutionCode.stream().anyMatch(item -> item.equals(marLastMileFeeClaimInterfaceVo.getOrgWarehouseCode())))) {
                    log.error("分销仓不支持索赔!");
                    continue;
                }
                MarLastMileFeeClaim marLastMileFeeClaim = new MarLastMileFeeClaim();
                marLastMileFeeClaim.setOrganizationId(marLastMileFeeClaimInterfaceVo.getOrgId()); //组织ID
                marLastMileFeeClaim.setOrderNo(marLastMileFeeClaimInterfaceVo.getOrderNo());//订单号
                marLastMileFeeClaim.setOrderId(marLastMileFeeClaimInterfaceVo.getId()); //订单ID
                marLastMileFeeClaim.setOrderItemId(marLastMileFeeClaimInterfaceVo.getHeadItemId()); //订单行ID
                marLastMileFeeClaim.setChannel(marLastMileFeeClaimInterfaceVo.getChannel()); //渠道
                marLastMileFeeClaim.setShopId(marLastMileFeeClaimInterfaceVo.getChannelId()); //店铺ID
                marLastMileFeeClaim.setPstChannelCreated(marLastMileFeeClaimInterfaceVo.getPstChannelCreated()); //订单时间
                marLastMileFeeClaim.setTrackNo(marLastMileFeeClaimInterfaceVo.getTrackNo()); //跟踪号
                //面单类型设置
                if (AccountSaleChannelEnum.TIKTOK.getValue().equals(marLastMileFeeClaimInterfaceVo.getChannel()) || AccountSaleChannelEnum.WALMART.getValue().equals(marLastMileFeeClaimInterfaceVo.getChannel())) {
                    marLastMileFeeClaim.setShippingSource(MarLastMileClaimShippingSource.SHOP.getValue());
                } else if (AccountSaleChannelEnum.TEMU.getValue().equals(marLastMileFeeClaimInterfaceVo.getChannel())) {
                    List<Integer> platCode = Arrays.asList(1, 2, 3, 4);  //TEMU根据行类型判断
                    if (platCode.stream().anyMatch(item -> item.equals(marLastMileFeeClaimInterfaceVo.getShippingType()))) {
                        marLastMileFeeClaim.setShippingSource(MarLastMileClaimShippingSource.PLATFORM.getValue());
                    } else {
                        log.error("TEMU渠道不支持商家面单：{}", JSONObject.toJSONString(marLastMileFeeClaimInterfaceVo));
                        continue;
                    }
                } else {
                    continue;
                }
                marLastMileFeeClaim.setPackageSn(marLastMileFeeClaimInterfaceVo.getPackageSn()); //包裹号
                marLastMileFeeClaim.setCarrierCode(marLastMileFeeClaimInterfaceVo.getCarrierCode()); //承运商
                marLastMileFeeClaim.setServiceCode(marLastMileFeeClaimInterfaceVo.getServiceCode());//发货方式
                marLastMileFeeClaim.setLogisticStatus(marLastMileFeeClaimInterfaceVo.getLogisticStatus()); //物流状态
                marLastMileFeeClaim.setShipmentDate(marLastMileFeeClaimInterfaceVo.getShipmentDate()); //发货时间
                //索赔方
                marLastMileFeeClaim.setClaimant(marLastMileFeeClaim.getShippingSource().equals(MarLastMileClaimShippingSource.SHOP.getValue()) ? MarLastMileclaimantEnmu.EFULFILL.getValue() : MarLastMileclaimantEnmu.TEMU.getValue());
                //索赔来源 //长期未妥投
                marLastMileFeeClaim.setClaimSource(MarLastMileClaimSourceEnum.LONG_TIME_NOTICE_LEFT.getValue());
                marLastMileFeeClaim.setErpSku(marLastMileFeeClaimInterfaceVo.getErpSku()); //SKu
                marLastMileFeeClaim.setSellerSku(marLastMileFeeClaimInterfaceVo.getSellerSku()); //sellerSku
                marLastMileFeeClaim.setShipedQuantity(marLastMileFeeClaimInterfaceVo.getShipedQuantity()); //发运数量
                marLastMileFeeClaim.setCurrency(marLastMileFeeClaimInterfaceVo.getCurrencyCode()); //币种
                marLastMileFeeClaim.setItemPrice(marLastMileFeeClaimInterfaceVo.getItemPrice()); //单价
                //索赔金额 （DDP（行单价）x数量）
                if (marLastMileFeeClaim.getItemPrice() != null && marLastMileFeeClaim.getShipedQuantity() != null) {
                    marLastMileFeeClaim.setClaimAmount(marLastMileFeeClaim.getItemPrice().multiply(marLastMileFeeClaim.getShipedQuantity()));
                }
                //预估物流费
                marLastMileFeeClaim.setEstimateShipCost(marLastMileFeeClaimInterfaceVo.getEstimateShipCost());
                // 面单类型为平台面单的，设置状态为待提交，
                if (MarLastMileClaimShippingSource.PLATFORM.getValue().equals(marLastMileFeeClaim.getShippingSource())) {
                    marLastMileFeeClaim.setClaimStatus(MarLastMileClaimStatusEnmu.TO_BE_SUBMIT.getValue());
                }
                // 面单类型为商家面单的，设置状态为待提供证明，
                if (MarLastMileClaimShippingSource.SHOP.getValue().equals(marLastMileFeeClaim.getShippingSource())) {
                    marLastMileFeeClaim.setClaimStatus(MarLastMileClaimStatusEnmu.TO_BE_PROVED.getValue());
                }
                try {
                    this.save(marLastMileFeeClaim);
                } catch (Exception e) {
                    log.info("新增长期索赔信息异常：{}", e);
                    continue;
                }

                //提交证明
                this.getProve(marLastMileFeeClaim);

                //针对面单来源为平台面单且承运商为UPS或者是AMSP_HL (原则上AMSP_HL不会是平台面单  )  的不生成工单
                if (MarLastMileClaimShippingSource.PLATFORM.getValue().equals(marLastMileFeeClaim.getShippingSource()) &&
                        (MarLastMileclaimCarrierCodeEnum.UPS.getValue().equalsIgnoreCase(marLastMileFeeClaim.getCarrierCode()) || MarLastMileclaimCarrierCodeEnum.AMSP_HL.getValue().equalsIgnoreCase(marLastMileFeeClaim.getCarrierCode()))) {
                    continue;
                }
                //创建工单
                Long tkID = this.lastMileFeeClaimTicket(marLastMileFeeClaim);
                MarLastMileFeeClaim marLastMileFeeClaimUpdate = new MarLastMileFeeClaim();
                marLastMileFeeClaimUpdate.setId(marLastMileFeeClaim.getId());
                marLastMileFeeClaimUpdate.setTicketId(tkID);
                this.updateById(marLastMileFeeClaimUpdate);

            } catch (Exception e) {
                log.error("保存索赔新增来源为长期未妥投的尾程索赔异常:{}", e);
            }
        }
    }


    /**
     * Description: 同步来源为长期未妥投的尾程索赔
     *
     * @param
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2025/3/21
     */
    public JSONObject useLongTimeNotArriveInterfaceRequest(String page, String pstOrderStart, String pstOrderEnd, String shipmentDate) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("organizationId", 1000049);
        paramMap.put("pstChannelCreatedStart", pstOrderStart);
        paramMap.put("pstChannelCreatedEnd", pstOrderEnd);
        paramMap.put("shipmentDate", shipmentDate);
        paramMap.put("page", page);
        paramMap.put("rows", "100");

        String url = QUERY_SALE_NUM_MASTER_URL + "/api/v1/LastMileClaimQuery";
        HttpRequest request = HttpUtil.createPost(url);
        request.setReadTimeout(120 * 1000);
        request.body(JSON.toJSONString(paramMap));
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            log.error("长期未妥投尾程索赔接口异常: req:{}, res:{}", JSON.toJSONString(paramMap), JSONObject.toJSONString(response));
            throw new CustomException("调用来源为长期未妥投的尾程索赔接口异常");
        }
        if (response.body() == null) {
            return new JSONObject();
        } else {
            log.error("长期未妥投尾程索赔SUCESS: req:{}, res:{}", JSON.toJSONString(paramMap), JSONObject.parseObject(response.body()));
            return JSONObject.parseObject(response.body());
        }

    }


    /**
     * 保存索赔信息
     *
     * @param saleOrderShipemtInfoVO
     * @param claimSource
     * @param distrbutionCode
     */
    @Override
    public void saveMarLastMileFeeClaimInfo(SaleOrderShipemtInfoVO saleOrderShipemtInfoVO, Integer claimSource, List<String> distrbutionCode) {
        try {
            //分销仓库校验
            if (!CollectionUtils.isEmpty(distrbutionCode) && (distrbutionCode.stream().anyMatch(item -> item.equals(saleOrderShipemtInfoVO.getOrgWarehouseCode())))) {
                log.error("分销仓不支持索赔!");
                return;
            }
            MarLastMileFeeClaim marLastMileFeeClaim = new MarLastMileFeeClaim();
            marLastMileFeeClaim.setOrganizationId(saleOrderShipemtInfoVO.getOrgId());
            marLastMileFeeClaim.setOrderNo(saleOrderShipemtInfoVO.getOrderNo());//订单号
            marLastMileFeeClaim.setOrderId(saleOrderShipemtInfoVO.getId()); //订单ID
            marLastMileFeeClaim.setOrderItemId(saleOrderShipemtInfoVO.getHeadItemId()); //订单行ID
            marLastMileFeeClaim.setChannel(saleOrderShipemtInfoVO.getChannel()); //渠道
            marLastMileFeeClaim.setShopId(saleOrderShipemtInfoVO.getChannelId()); //店铺ID
            marLastMileFeeClaim.setPstChannelCreated(saleOrderShipemtInfoVO.getPstChannelCreated()); //订单时间
            marLastMileFeeClaim.setTrackNo(saleOrderShipemtInfoVO.getTrackNo());
            //面单类型
            if (AccountSaleChannelEnum.TIKTOK.getValue().equals(saleOrderShipemtInfoVO.getChannel()) || AccountSaleChannelEnum.WALMART.getValue().equals(saleOrderShipemtInfoVO.getChannel())) {
                marLastMileFeeClaim.setShippingSource(MarLastMileClaimShippingSource.SHOP.getValue());
            } else if (AccountSaleChannelEnum.TEMU.getValue().equals(saleOrderShipemtInfoVO.getChannel())) {
                List<Integer> platCode = Arrays.asList(1, 2, 3, 4);
                if (platCode.stream().anyMatch(item -> item.equals(saleOrderShipemtInfoVO.getShippingType()))) {
                    marLastMileFeeClaim.setShippingSource(MarLastMileClaimShippingSource.PLATFORM.getValue());
                } else {
                    log.error("TEMU渠道不支持商家面单!");
                    return;
                }

            }

            marLastMileFeeClaim.setPackageSn(saleOrderShipemtInfoVO.getPackageSn()); //包裹号
            marLastMileFeeClaim.setCarrierCode(saleOrderShipemtInfoVO.getCarrierCode()); //承运商
            marLastMileFeeClaim.setServiceCode(saleOrderShipemtInfoVO.getServiceCode());//发货方式
            marLastMileFeeClaim.setLogisticStatus(saleOrderShipemtInfoVO.getLogisticStatus()); //物流状态
            marLastMileFeeClaim.setShipmentDate(saleOrderShipemtInfoVO.getShipmentDate()); //发货时间
            //索赔方
            marLastMileFeeClaim.setClaimant(marLastMileFeeClaim.getShippingSource().equals(MarLastMileClaimShippingSource.SHOP.getValue()) ? MarLastMileclaimantEnmu.EFULFILL.getValue() : MarLastMileclaimantEnmu.TEMU.getValue());
            //索赔来源
            marLastMileFeeClaim.setClaimSource(claimSource);
            marLastMileFeeClaim.setErpSku(saleOrderShipemtInfoVO.getErpSku()); //SKu
            marLastMileFeeClaim.setSellerSku(saleOrderShipemtInfoVO.getSellerSku()); //sellerSku
            marLastMileFeeClaim.setShipedQuantity(saleOrderShipemtInfoVO.getShipedQuantity()); //发运数量
            marLastMileFeeClaim.setCurrency(saleOrderShipemtInfoVO.getCurrencyCode()); //币种
            marLastMileFeeClaim.setItemPrice(saleOrderShipemtInfoVO.getItemPrice()); //单价
            //索赔金额 （DDP（行单价）x数量）
            if (marLastMileFeeClaim.getItemPrice() != null && marLastMileFeeClaim.getShipedQuantity() != null) {
                marLastMileFeeClaim.setClaimAmount(marLastMileFeeClaim.getItemPrice().multiply(marLastMileFeeClaim.getShipedQuantity()));
            }
            //预估物流费
            marLastMileFeeClaim.setEstimateShipCost(saleOrderShipemtInfoVO.getEstimateShipCost());
            // 面单类型为平台面单的，设置状态为待提交，
            if (MarLastMileClaimShippingSource.PLATFORM.getValue().equals(marLastMileFeeClaim.getShippingSource())) {
                marLastMileFeeClaim.setClaimStatus(MarLastMileClaimStatusEnmu.TO_BE_SUBMIT.getValue());
            }

            // 面单类型为商家面单的，设置状态为待提供证明，
            if (MarLastMileClaimShippingSource.SHOP.getValue().equals(marLastMileFeeClaim.getShippingSource())) {
                marLastMileFeeClaim.setClaimStatus(MarLastMileClaimStatusEnmu.TO_BE_PROVED.getValue());
            }

            try {
                this.save(marLastMileFeeClaim);
            } catch (Exception e) {
                log.info("退款来源索赔保存失败：{},saveBody:{}", e, JSONObject.toJSONString(marLastMileFeeClaim));
                return;
            }

            //针对面单来源为平台面单且承运商为UPS或者是AMSP_HL (原则上AMSP_HL不会是平台面单  )  的不生成工单
            if (MarLastMileClaimShippingSource.PLATFORM.getValue().equals(marLastMileFeeClaim.getShippingSource()) &&
                    (MarLastMileclaimCarrierCodeEnum.UPS.getValue().equalsIgnoreCase(marLastMileFeeClaim.getCarrierCode()) || MarLastMileclaimCarrierCodeEnum.AMSP_HL.getValue().equalsIgnoreCase(marLastMileFeeClaim.getCarrierCode()))) {
                return;
            }
            //创建工单
            Long tkID = this.lastMileFeeClaimTicket(marLastMileFeeClaim);
            MarLastMileFeeClaim marLastMileFeeClaimUpdate = new MarLastMileFeeClaim();
            marLastMileFeeClaimUpdate.setId(marLastMileFeeClaim.getId());
            marLastMileFeeClaimUpdate.setTicketId(tkID);
            this.updateById(marLastMileFeeClaimUpdate);
        } catch (Exception e) {
            log.error("保存退款尾程索赔费信息异常：索赔信息:{},异常原因:{}", saleOrderShipemtInfoVO, e.getMessage());
        }
    }


    /**
     * @param
     * @param fileUrl
     * @param contextId
     * @param sourceId
     * @description: 添加图片
     * @author: Moore
     * @date: 2025/3/27 10:22
     * @return: void
     **/
    @Override
    @Transactional
    public void addImage(List<MarLastMileFeeClaimAttaches> fileUrl, Integer contextId, Long sourceId) {
        if (sourceId == null) {
            throw new CustomException("业务信息获取失败");
        }
        if (fileUrl.stream().anyMatch(item -> StringUtils.isEmpty(item.getFileName()) || StringUtils.isEmpty(item.getFileUrl()))) {
            throw new CustomException("附件或附件名不能为空");
        }

        MarLastMileFeeClaim marLastMileFeeClaim = this.lambdaQuery().eq(MarLastMileFeeClaim::getId, sourceId).one();
        if (marLastMileFeeClaim == null) {
            throw new CustomException("未获取到索赔信息");
        }

        //非待提供证明，及非待提交，及 获取中中，非异常 不可上传图片
        if (!MarLastMileClaimStatusEnmu.TO_BE_PROVED.getValue().equals(marLastMileFeeClaim.getClaimStatus())
                && (MarLastMileClaimStatusEnmu.OBTAINING_PROOF.getValue().equals(marLastMileFeeClaim.getClaimStatus())
                && !MarLastMileClaimExEnum.ERROR.getValue().equals(marLastMileFeeClaim.getExceptionFlag()))
                && !MarLastMileClaimStatusEnmu.TO_BE_SUBMIT.getValue().equals(marLastMileFeeClaim.getClaimStatus())) {
            throw new CustomException("仅待提供证明，待提交，获证明中存在异常状态下,可上传图片");
        }


        LambdaQueryWrapper<MarLastMileFeeClaimAttaches> removePam = new LambdaQueryWrapper<>();
        removePam.eq(MarLastMileFeeClaimAttaches::getClaimId, sourceId);
        marLastMileFeeClaimAttachesService.remove(removePam);
        fileUrl.stream().forEach(item -> {
            item.setId(null);
            item.setClaimId(sourceId);
            item.settingDefaultCreate();
        });
        marLastMileFeeClaimAttachesService.saveBatch(fileUrl);


        //更新状态
        if (!MarLastMileClaimStatusEnmu.TO_BE_SUBMIT.getValue().equals(marLastMileFeeClaim.getClaimStatus())) {
            //更新工单状态
            marLastMileFeeClaim.setClaimStatus(MarLastMileClaimStatusEnmu.TO_BE_SUBMIT.getValue());
            marLastMileFeeClaim.settingDefaultUpdate();
            this.updateById(marLastMileFeeClaim);

            ScTicketLog scTicketLog = new ScTicketLog();
            scTicketLog.setOperateContent("上传图片,并修改工单状态为：待提交");
            scTicketLog.setTicketId(marLastMileFeeClaim.getTicketId());
            scTicketLog.settingDefaultCreate();
            scTicketLogService.insertScTicketLog(scTicketLog);
        }


    }


    /**
     * @param
     * @description: 获取平台商
     * @author: Moore
     * @date: 2025/4/8 15:08
     * @return: void
     **/
    @Override
    public void planManulStatus() {
        //获取rpa开启状态
        List<MarRpaServiceStatusClaimLog> rapResult = marRpaServiceStatusClaimLogService.lambdaQuery().eq(MarRpaServiceStatusClaimLog::getCode, MarClaimRpaUrlEnum.GET_CLAIM_RESULT.getCode()).list();
        //获取开启结果Rpa组织
        List<Long> openOriList = rapResult.stream().map(MarRpaServiceStatusClaimLog::getOrgId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(openOriList)) {
            log.info("无开启获取索赔结果组织");
            return;
        }

        int pageNo = 1;
        int pageSize = 100;
        while (true) {
            PageHelper.startPage(pageNo, pageSize);
            List<MarLastMileFeeClaim> list = this.lambdaQuery()
                    .eq(MarLastMileFeeClaim::getShippingSource, MarLastMileClaimShippingSource.PLATFORM.getValue())
                    .eq(MarLastMileFeeClaim::getClaimStatus, MarLastMileClaimStatusEnmu.CLAIM_UNDERWAY.getValue())
                    .eq(MarLastMileFeeClaim::getChannel, AccountSaleChannelEnum.TEMU.getValue()) //暂时平台面单只有TEMU
                    .in(MarLastMileFeeClaim::getOrganizationId, openOriList)
                    .list();
            if (CollectionUtil.isEmpty(list)) {
                return;
            }
            //店铺+索赔原因分组
            Map<String, List<MarLastMileFeeClaim>> shopMap = list.stream().collect(Collectors.groupingBy(item -> item.getShopId() + "_" + item.getClaimCause()));
            for (String shopIds : shopMap.keySet()) {
                List<MarLastMileFeeClaim> marLastMileFeeClaims = shopMap.get(shopIds);
                List<Account> accounts = accountMapper.selectShopIdMapRegisterCompanyName(Arrays.asList(marLastMileFeeClaims.get(0).getShopId().intValue()), null);
                //调用
                JSONObject reqParm = new JSONObject();
                reqParm.put("rpcType", MarClaimRpaUrlEnum.GET_CLAIM_RESULT.getRpcType()); //RPC 类型
                reqParm.put("maillId", accounts.get(0).getSellerId()); //店铺MailID
                reqParm.put("registerCompanyName", accounts.get(0).getRegisterCompanyName()); //主体名
                reqParm.put("domainName", accounts.get(0).getDomainName()); //域名
                reqParm.put("appealType", MarLastMileTemuCauseTransitionEnum.getApiBySys(marLastMileFeeClaims.get(0).getClaimCause())); //索赔原因 丢失或损坏（系统转后台）
                reqParm.put("claimIds", marLastMileFeeClaims.stream().filter(item -> !StringUtils.isEmpty(item.getClaimId())).map(MarLastMileFeeClaim::getClaimId).collect(Collectors.toList())); //索赔类型 丢失或损坏
                try {
                    log.info("获取平台面单尾程索赔最终结果查询：{}", JSONObject.toJSONString(reqParm));
                    this.sendRpaProveHttp(null, MarClaimRpaUrlEnum.GET_CLAIM_RESULT.getRpcType(), JSONObject.toJSONString(reqParm));
                } catch (Exception e) {
                    log.info("调用Rpa获取结果失败:{}", e);
                }
            }
            pageNo++;
        }
    }


    /**
     * @param
     * @description: 商家面单信息同步
     * @author: Moore
     * @date: 2025/4/7 13:39
     * @return: void
     **/
    @Override
    public void shopManulStatus() {
        int pageNo = 1;
        int pageSize = 200;
        while (true) {
            PageHelper.startPage(pageNo, pageSize);
            List<MarLastMileFeeClaim> list = this.lambdaQuery()
                    .eq(MarLastMileFeeClaim::getShippingSource, MarLastMileClaimShippingSource.SHOP.getValue())
                    .eq(MarLastMileFeeClaim::getClaimStatus, MarLastMileClaimStatusEnmu.CLAIM_UNDERWAY.getValue())
                    .list();
            if (CollectionUtil.isEmpty(list)) {
                return;
            }

            for (MarLastMileFeeClaim claimInfo : list) {
                if (StringUtils.isEmpty(claimInfo.getClaimId())) {
                    continue;
                }
                UserEntity userEntity = new UserEntity();
                userEntity.setId(-1);
                userEntity.setName("system");
                ReimbursementRequest reimbursementRequest = new ReimbursementRequest();
                reimbursementRequest.setReimbursementNo(claimInfo.getClaimId());
                reimbursementRequest.setOrganizationId(claimInfo.getOrganizationId());

                ReimbursementResponse detailsRes = reimbursementService.getDetails(reimbursementRequest, userEntity);
                log.info("OMS索赔结果：{},{}", JSONObject.toJSONString(reimbursementRequest), JSONObject.toJSONString(detailsRes));
                if (detailsRes == null) {
                    continue;
                }
                if (new Integer(0).equals(detailsRes.getReimbursementResult())) { //成功
                    MarLastMileFeeClaim claimUpdate = new MarLastMileFeeClaim();
                    claimUpdate.setId(claimInfo.getId());
                    claimUpdate.setClaimStatus(MarLastMileClaimStatusEnmu.CLAIM_SUCCESS.getValue());
                    claimUpdate.setCompensationAmount(detailsRes.getActualAmount()); //实际索赔金额
                    claimUpdate.settingDefaultSystemUpdate();
                    this.updateById(claimUpdate);
                }
                if (new Integer(1).equals(detailsRes.getReimbursementResult())) { //失败
                    MarLastMileFeeClaim claimUpdate = new MarLastMileFeeClaim();
                    claimUpdate.setId(claimInfo.getId());
                    claimUpdate.setClaimStatus(MarLastMileClaimStatusEnmu.CLAIM_FAIL.getValue());
                    claimUpdate.setClaimFailMsg(detailsRes.getFailureReason()); //失败原因
                    claimUpdate.settingDefaultSystemUpdate();
                    this.updateById(claimUpdate);
                }
            }
            pageNo++;
        }


    }


    /**
     * 接收最终平台面单尾程索赔结果
     *
     * @param receive
     */
    @Override
    public void receiveStatusTemuResule(List<MarClaimTemuInfo> receive) {
        for (MarClaimTemuInfo marClaimTemuInfo : receive) {
            MarLastMileFeeClaim marLastMileFeeClaim = this.lambdaQuery()
                    .eq(MarLastMileFeeClaim::getClaimId, marClaimTemuInfo.getAppealTicketSn())
                    .eq(MarLastMileFeeClaim::getChannel, AccountSaleChannelEnum.TEMU.getValue())
                    .one();


            if (marLastMileFeeClaim == null) {
                marLastMileFeeClaim = this.lambdaQuery()
                        .eq(MarLastMileFeeClaim::getTrackNo, marClaimTemuInfo.getTrackingNumber())
                        .eq(MarLastMileFeeClaim::getChannel, AccountSaleChannelEnum.TEMU.getValue())
                        .one();
                if (marLastMileFeeClaim == null) {
                    this.saveInitTemuClaim(marClaimTemuInfo);
                    continue;
                }
            }


            //业务不是索赔中,或者接收到的是审核中，均不更新
            if (!MarLastMileClaimStatusEnmu.CLAIM_UNDERWAY.getValue().equals(marLastMileFeeClaim.getClaimStatus())
                    || new Integer(1).equals(marClaimTemuInfo.getAppealStatus())) {
                continue;
            }

            //更新索赔状态  失败3 成功2   审核中（即系统中的索赔中） 1
            String logMsg = "";
            if (new Integer(2).equals(marClaimTemuInfo.getAppealStatus())) {
                MarLastMileFeeClaim marLastMileFeeClaimInfo = new MarLastMileFeeClaim();
                marLastMileFeeClaimInfo.setId(marLastMileFeeClaim.getId());
                marLastMileFeeClaimInfo.setClaimStatus(MarLastMileClaimStatusEnmu.CLAIM_SUCCESS.getValue());
                marLastMileFeeClaimInfo.settingDefaultSystemUpdate();
                this.updateById(marLastMileFeeClaimInfo);
                logMsg = "获取到索赔结果：索赔成功";
            } else if (new Integer(3).equals(marClaimTemuInfo.getAppealStatus())) {
                MarLastMileFeeClaim marLastMileFeeClaimInfo = new MarLastMileFeeClaim();
                marLastMileFeeClaimInfo.setId(marLastMileFeeClaim.getId());
                marLastMileFeeClaimInfo.setClaimStatus(MarLastMileClaimStatusEnmu.CLAIM_FAIL.getValue());
                marLastMileFeeClaimInfo.settingDefaultSystemUpdate();
                this.updateById(marLastMileFeeClaimInfo);
                logMsg = "获取到索赔结果：索赔失败";
            }
            if (!StringUtils.isEmpty(logMsg)) {
                MarLastMileFeeClaimLog marLastMileFeeClaimLog = new MarLastMileFeeClaimLog();
                marLastMileFeeClaimLog.setClaimId(marLastMileFeeClaim.getId()).
                        setOperateContent(logMsg).
                        settingDefaultSystemCreate();
                marLastMileFeeClaimLogService.save(marLastMileFeeClaimLog);
            }
        }


    }


    /**
     * 保存Temu初始索赔信息
     *
     * @param receive
     */
    private void saveInitTemuClaim(MarClaimTemuInfo receive) {

        try {
            //校验订单号
            List<String> parentOrderSnList = receive.getParentOrderSnList();
            if (CollectionUtils.isEmpty(parentOrderSnList) || parentOrderSnList.size() > 1) {
                log.info("接收新索赔数据，订单信息异常：{}", JSONObject.toJSONString(parentOrderSnList));
                return;
            }
            String reciveOrderNo = parentOrderSnList.get(0);
            String trackingNumber = receive.getTrackingNumber();
            if (StringUtils.isEmpty(trackingNumber)) {
                log.info("接收新索赔数据，无Tracking信息：{}", JSONObject.toJSONString(parentOrderSnList));
                return;
            }


            //校验发货trak是否存在
            SaleOrderShipemtInfoVO saleOrderShipemtInfoVO = this.baseMapper.selectShipmentTrackingInfo(1000049L, trackingNumber, reciveOrderNo);

            if (saleOrderShipemtInfoVO == null) {
                log.info("未获取到索赔订单信息：{}", JSONObject.toJSONString(saleOrderShipemtInfoVO));
                return;
            }

            //保存索赔信息
            MarLastMileFeeClaim marLastMileFeeClaim = new MarLastMileFeeClaim();
            marLastMileFeeClaim.setClaimId(receive.getAppealTicketSn()); //索赔编号
            try {
                if (StringUtil.isNotEmpty(receive.getApplyTimeStr())) {
                    DateTime parse = DateUtil.parse(receive.getApplyTimeStr(), "yyyy/MM/dd HH:mm:ss");
                    marLastMileFeeClaim.setClaimTime(parse); //索赔时间
                }
            } catch (Exception e) {
                log.info("初始化索赔信息,时间转换异常：{}", e);
            }
            //索赔原因
            Integer appealType = receive.getAppealType();
            if (new Integer(2).equals(appealType)) {
                marLastMileFeeClaim.setClaimCause(MarLastMileTemuCauseTransitionEnum.LOSE.getSys());
            } else if (new Integer(6).equals(appealType)) {
                marLastMileFeeClaim.setClaimCause(MarLastMileTemuCauseTransitionEnum.DAMAGE.getSys());
            }


            marLastMileFeeClaim.setOrganizationId(saleOrderShipemtInfoVO.getOrgId()); //组织ID
            marLastMileFeeClaim.setOrderNo(reciveOrderNo);//订单号
            marLastMileFeeClaim.setOrderId(saleOrderShipemtInfoVO.getId()); //订单ID
            marLastMileFeeClaim.setOrderItemId(saleOrderShipemtInfoVO.getHeadItemId()); //订单行ID
            marLastMileFeeClaim.setChannel(AccountSaleChannelEnum.TEMU.getValue()); //渠道
            marLastMileFeeClaim.setShopId(saleOrderShipemtInfoVO.getChannelId()); //店铺ID
            marLastMileFeeClaim.setPstChannelCreated(saleOrderShipemtInfoVO.getPstChannelCreated()); //订单时间
            marLastMileFeeClaim.setTrackNo(trackingNumber); //track

            //面单类型（默认平台面单）
            marLastMileFeeClaim.setShippingSource(MarLastMileClaimShippingSource.PLATFORM.getValue());

            marLastMileFeeClaim.setPackageSn(receive.getPackageSn()); //包裹号
            marLastMileFeeClaim.setCarrierCode(receive.getShipCompanyName()); //承运商
            marLastMileFeeClaim.setServiceCode(saleOrderShipemtInfoVO.getServiceCode());//发货方式
            marLastMileFeeClaim.setLogisticStatus(saleOrderShipemtInfoVO.getLogisticStatus()); //物流状态
            marLastMileFeeClaim.setShipmentDate(saleOrderShipemtInfoVO.getShipmentDate()); //发货时间
            //索赔方（默认TEMU）
            marLastMileFeeClaim.setClaimant(MarLastMileclaimantEnmu.TEMU.getValue());
            //索赔来源,用户手动输入，无需操作
            marLastMileFeeClaim.setClaimSource(MarLastMileClaimSourceEnum.LONG_TIME_NOTICE_LEFT.getValue()); //系统
            marLastMileFeeClaim.setErpSku(saleOrderShipemtInfoVO.getErpSku()); //SKu
            marLastMileFeeClaim.setSellerSku(saleOrderShipemtInfoVO.getSellerSku()); //sellerSku
            marLastMileFeeClaim.setShipedQuantity(saleOrderShipemtInfoVO.getShipedQuantity()); //发运数量
            marLastMileFeeClaim.setCurrency(saleOrderShipemtInfoVO.getCurrencyCode()); //币种
            marLastMileFeeClaim.setItemPrice(saleOrderShipemtInfoVO.getItemPrice()); //单价，理解为DDP价格
            marLastMileFeeClaim.setEstimateShipCost(saleOrderShipemtInfoVO.getEstimateShipCost()); //预估物流费
            //索赔金额 （DDP（行单价）x数量）
            if (marLastMileFeeClaim.getItemPrice() != null && marLastMileFeeClaim.getShipedQuantity() != null) {
                marLastMileFeeClaim.setClaimAmount(marLastMileFeeClaim.getItemPrice().multiply(marLastMileFeeClaim.getShipedQuantity()));
            }


            // 面单类型为商家面单的，设置状态为待提供证明，
            //失败3 成功2   审核中（即系统中的索赔中） 1   （转换为系统状态）
            Integer appealStatus = receive.getAppealStatus();
            if (new Integer(1).equals(appealStatus)) {
                marLastMileFeeClaim.setClaimStatus(MarLastMileClaimStatusEnmu.CLAIM_UNDERWAY.getValue());
            } else if (new Integer(2).equals(appealStatus)) {
                marLastMileFeeClaim.setClaimStatus(MarLastMileClaimStatusEnmu.CLAIM_SUCCESS.getValue());
            } else if (new Integer(3).equals(appealStatus)) {
                marLastMileFeeClaim.setClaimStatus(MarLastMileClaimStatusEnmu.CLAIM_FAIL.getValue());
            }
            // 保存数据
            this.save(marLastMileFeeClaim);

            //记录索赔保存日志
            MarLastMileFeeClaimLog marLastMileFeeClaimLog = new MarLastMileFeeClaimLog();
            marLastMileFeeClaimLog.setClaimId(marLastMileFeeClaim.getId()).setOperateContent("初始化索赔数据！").settingDefaultCreate();
            marLastMileFeeClaimLogService.save(marLastMileFeeClaimLog);
        } catch (Exception e) {
            log.info("初始索赔结果异常：{}", e);
        }
    }


}




