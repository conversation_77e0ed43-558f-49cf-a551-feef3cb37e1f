package com.bizark.op.service.mapper.mar.material;

import com.bizark.op.api.entity.op.mar.material.MarMaterialReleaseInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bizark.op.api.form.mar.material.MarMaterialQuery;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【mar_material_release_info(tiktok素材发布记录表)】的数据库操作Mapper
* @createDate 2025-02-10 17:06:17
* @Entity com.bizark.op.api.entity.op.mar.material.MarMaterialReleaseInfo
*/
public interface MarMaterialReleaseInfoMapper extends BaseMapper<MarMaterialReleaseInfo> {

    void updateCallback(MarMaterialReleaseInfo one);

    /**
     * 素材发布记录
     *
     * @param query
     * @return
     */
    List<MarMaterialReleaseInfo> selectMaterialReleaseList(MarMaterialQuery query);
}




