package com.bizark.op.service.event.listener;


import bizark.amz.vendor.ds.VendorTransactionApi;
import bizark.amz.vendor.ds.transaction.GetTransactionResponse;
import bizark.amz.vendor.ds.transaction.Transaction;
import bizark.amz.vendor.ds.transaction.TransactionStatus;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.bizark.op.api.cons.MQDefine;
import com.bizark.op.api.entity.op.inventory.enums.InventoryStatus;
import com.bizark.op.api.entity.op.inventory.enums.TaskStatusEnum;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.entity.op.task.PublishTask;
import com.bizark.op.api.entity.op.task.PublishTaskInfo;
import com.bizark.op.api.service.sale.ProductChannelsService;
import com.bizark.op.api.service.task.PublishTaskService;
import com.bizark.op.service.event.AmazonVendorsInventoryUpdateEvent;
import com.bizark.op.service.handler.inventory.VCDFInventoryHandler;
import com.bizark.op.service.handler.task.InventoryPublishTask;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AmazonVendorsInventoryUpdateEventListener implements ApplicationListener<AmazonVendorsInventoryUpdateEvent>{

    @Autowired
    private PublishTaskService publishTaskService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private InventoryPublishTask inventoryPublishTask;

    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private ProductChannelsService productChannelsService;


    @Autowired
    private VCDFInventoryHandler vcdfInventoryHandler;


    @Override
    public void onApplicationEvent(AmazonVendorsInventoryUpdateEvent event) {
        VendorTransactionApi transactionApi = event.getTransactionApi();
        PublishTaskInfo taskInfo = event.getTaskInfo();
        PublishTask task = taskInfo.getPublishTask();
        log.info("SC/VC-队列事务 获取到SC事务事件，总尝试次数:{}  当前尝试次数：{} ,事务ID:{} ,任务ID：{}", event.getCount(), event.getFrequency(), task.getTransactionId(), task.getId());
        try {
            if (event.getFrequency() >= event.getCount()) {
                log.info("SC/VC-队列事务 获取到SC事务事件 ,重试结束 - 事务ID:{} 任务ID:{}", task.getTransactionId(), task.getId());
                publishTaskService.updatePublishTaskStatus(task.getId(), TaskStatusEnum.FAIL, "事务ID:" + task.getTransactionId() + ",未获取到执行结果");
//                inventoryPublishTask.channelModify(task.getChannelId(), InventoryStatus.NORMAL.getValue());
                return;
            }
            // 等待10s后调用，防止notFound
            TimeUnit.SECONDS.sleep(30);
            GetTransactionResponse transactionStatus = transactionApi.getTransactionStatus(task.getTransactionId());
            log.info("SC/VC-队列事务 获取到SC事务事件 ,获取任务结果 - 事务ID:{} 任务ID:{} - 推送结果:{}", task.getTransactionId(), task.getId(), JSONObject.toJSONString(transactionStatus.getPayload()));

            event.incr();
            TransactionStatus payload = transactionStatus.getPayload();
            Transaction status = payload.getTransactionStatus();
            if (status.getStatus() == Transaction.StatusEnum.PROCESSING) {
                log.info("SC/VC-队列事务 当前事务未处理完毕，等待继续处理");
                TimeUnit.SECONDS.sleep(60);
                applicationEventPublisher.publishEvent(event);
                return;
            }
            if (status.getStatus() == Transaction.StatusEnum.SUCCESS) {
                log.info("SC/VC-队列事务 当前事务处理成功，任务ID：{} SellerSku:{} content:{}", task.getId(), task.getSellerSku(), task.getContent());
                publishTaskService.updatePublishTaskStatus(task.getId(), TaskStatusEnum.SUCCESS, "库存修改成功");
                modify(task);
                return;
            }

            if (status.getStatus() == Transaction.StatusEnum.FAILURE) {
                log.error("SC/VC-队列事务 当前事务处理失败，任务ID：{} SellerSku:{} content:{}", task.getId(), task.getSellerSku(), task.getContent());
                String errorMsg = "库存请求失败";
                TransactionStatus errorPaylod = transactionStatus.getPayload();

                if (CollectionUtil.isNotEmpty(errorPaylod.getTransactionStatus().getErrors())) {
                    errorMsg = errorPaylod.getTransactionStatus().getErrors().stream()
                            .map(e -> e.getMessage() + ";" + e.getDetails())
                            .collect(Collectors.joining("\n"));
                }

                // 如果是受限，则库存修改也是成功的
                if (errorMsg.contains("Review the suppression reasons under Items")) {
                    log.info("SC/VC-队列事务 当前ASIN受限，默认成功，任务ID：{}", task.getId());
                    publishTaskService.updatePublishTaskStatus(task.getId(), TaskStatusEnum.SUCCESS, errorMsg);
                    modify(task);
                    modifyRestricted(task);
                    return;
                }

                // TODO 如果当前为无效 ProductID 错误，重试
                if (errorMsg.contains("is not a valid product ID") && !taskInfo.isRetry()) {
                    taskInfo.setRetry(Boolean.TRUE);
                    vcdfInventoryHandler.execute(event.getTaskInfo());
                } else {
                    publishTaskService.updatePublishTaskStatus(task.getId(), TaskStatusEnum.FAIL, errorMsg);
//                    inventoryPublishTask.channelModify(task.getChannelId(), InventoryStatus.NORMAL.getValue());
                }

            }

        } catch (Exception e) {
            log.error("SC/VC-队列事务 当前事务处理异常，事务ID:{}  任务ID：{}", task.getTransactionId(), task.getId(), e);
            publishTaskService.updatePublishTaskStatus(task.getId(), TaskStatusEnum.FAIL, e.getMessage());
//            inventoryPublishTask.channelModify(task.getChannelId(), InventoryStatus.NORMAL.getValue());
        }




    }

    private void modifyRestricted(PublishTask task) {
        Integer channelId = task.getChannelId();
        ProductChannels channel = productChannelsService.getById(channelId);
        if (Objects.isNull(channel)) {
            return;
        }
        JSONObject object = new JSONObject();
        object.put("sku", channel.getSellerSku());
        object.put("storeName", channel.getAccountId());
        rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_E_ERP, MQDefine.SKU_RESTRICTED_REQ_ROUTING_KEY, object);
    }


    public void modify(PublishTask task){
        // 更新SKU映射库存修改状态
//        channelStatusModify(task.getChannelId());
        // 加锁防止库存重复写入
        RLock lock = redissonClient.getLock("inventory:task:update:" + task.getChannelId() + task.getStockAccountId());
        try {
            lock.lock(5, TimeUnit.SECONDS);
            inventoryPublishTask.inventoryModify(publishTaskService.getById(task.getId()));
        } catch (Exception e) {
            log.info("库存数据变更异常：{}", e.getMessage());
        }finally {
            lock.unlock();
        }
    }

}
