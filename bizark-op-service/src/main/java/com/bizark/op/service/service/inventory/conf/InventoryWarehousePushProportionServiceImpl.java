package com.bizark.op.service.service.inventory.conf;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.op.api.entity.op.inventory.conf.InventoryWarehousePushProportion;
import com.bizark.op.api.entity.op.inventory.conf.dto.PushProportionDTO;
import com.bizark.op.api.entity.op.inventory.conf.vo.PushProportionVO;
import com.bizark.op.api.service.inventory.conf.InventoryWarehousePushProportionService;
import com.bizark.op.service.mapper.inventory.conf.InventoryWarehousePushProportionMapper;
import com.bizark.fbb.api.entity.fbb.WarehouseEntity;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.bizark.op.api.cons.RedisCons.INVENTORY_WAREHOUSE_PUSH;

@Service
@Slf4j
public class InventoryWarehousePushProportionServiceImpl extends ServiceImpl<InventoryWarehousePushProportionMapper,InventoryWarehousePushProportion> implements InventoryWarehousePushProportionService {

    @Autowired
    private InventoryWarehousePushProportionMapper inventoryWarehousePushProportionMapper;

    @Autowired
    private RedissonClient redissonClient;


    @Override
    @Transactional
    public void modifyPushProportion(Integer contextId, PushProportionDTO dto) {

        RLock lock = redissonClient.getLock(INVENTORY_WAREHOUSE_PUSH + contextId);
        try {
            lock.lock(10, TimeUnit.SECONDS);
            // 直接删除
            inventoryWarehousePushProportionMapper.deleteByOrgId(contextId);
            // 写入
            List<InventoryWarehousePushProportion> pushProportions = dto.getWarehouse().stream()
                    .map(c -> {
                        InventoryWarehousePushProportion proportion = new InventoryWarehousePushProportion();
                        proportion.setWarehouse(c);
                        proportion.setProportion(dto.getPushProportion());
                        proportion.setOrgId(contextId);
                        return proportion;
                    }).collect(Collectors.toList());

            this.saveBatch(pushProportions);
        }finally {
            lock.unlock();
        }


    }

    @Override
    public PushProportionVO selectPushProportionByOrgId(Integer contextId) {
        List<InventoryWarehousePushProportion> proportions = this.lambdaQuery()
                .eq(InventoryWarehousePushProportion::getOrgId, contextId)
                .list();

        if (CollectionUtil.isEmpty(proportions)) {
            return null;
        }
        List<String> warehouse = proportions.stream()
                .map(InventoryWarehousePushProportion::getWarehouse)
                .collect(Collectors.toList());
        return new PushProportionVO(warehouse, proportions.get(0).getProportion());
    }

    @Override
    public List<WarehouseEntity> selectWarehouse(Integer contextId) {
        return inventoryWarehousePushProportionMapper.selectWarehouse(contextId);
    }

}
