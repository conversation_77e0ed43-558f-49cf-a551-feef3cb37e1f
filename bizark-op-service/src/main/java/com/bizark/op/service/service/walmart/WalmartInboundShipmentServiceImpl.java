package com.bizark.op.service.service.walmart;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.boss.api.service.TaskCenterService;
import com.bizark.boss.api.vo.dashboard.TaskCenterRequest;
import com.bizark.common.contract.AuthUserDetails;
import com.bizark.common.exception.CommonException;
import com.bizark.common.util.LocalDateTimeUtils;
import com.bizark.fbb.api.entity.fbb.transfer.TransferOutboundPlanEntity;
import com.bizark.fbb.api.service.fbbtransfer.TransferOutboundPlanService;
import com.bizark.framework.security.AuthContextHolder;
import com.bizark.op.api.cons.MQDefine;
import com.bizark.op.api.enm.mar.MarWalmartInboundShipmentType;
import com.bizark.op.api.enm.mar.MarWalmartTagType;
import com.bizark.op.api.enm.mar.claim.MarClaimRpaUrlEnum;
import com.bizark.op.api.enm.sale.AccountSaleChannelEnum;
import com.bizark.op.api.enm.sale.walmart.WalmartUrlEnum;
import com.bizark.op.api.enm.walmart.ShipmentOperateTypeEnum;
import com.bizark.op.api.enm.walmart.ShipmentProcessStatusEnum;
import com.bizark.op.api.enm.walmart.ShipmentTypeEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.conf.SystemGlobalConfig;
import com.bizark.op.api.entity.op.mar.WalmartInboundTag;
import com.bizark.op.api.entity.op.mar.WalmartShipmentAttach;
import com.bizark.op.api.entity.op.product.SkuChildren;
import com.bizark.op.api.entity.op.walmart.*;
import com.bizark.op.api.entity.op.walmart.MarWalmartInboundShipmentLog;
import com.bizark.op.api.entity.op.walmart.WalmartInboundShipment;
import com.bizark.op.api.entity.op.walmart.WalmartInboundShipmentItem;
import com.bizark.op.api.entity.op.walmart.WalmartShipAddress;
import com.bizark.op.api.entity.op.walmart.dto.WalmartCreateShipmentDTO;
import com.bizark.op.api.entity.op.walmart.dto.WalmartInboundShipmentQueryListDTO;
import com.bizark.op.api.entity.op.walmart.dto.WalmartInboundShipmentSetShipmentDTO;
import com.bizark.op.api.entity.op.walmart.dto.WalmartInboundShipmentSyncShipmentDTO;
import com.bizark.op.api.entity.op.walmart.dto.*;
import com.bizark.op.api.entity.op.walmart.message.WalmartInboundShipmentMessage;
import com.bizark.op.api.entity.op.walmart.query.CheckCommonShipmentRuleQuery;
import com.bizark.op.api.entity.op.walmart.vo.*;
import com.bizark.op.api.exception.ErpCommonException;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.mar.MarRpaServiceStatusClaimLogService;
import com.bizark.op.api.service.mar.WalmartShipmentAttachService;
import com.bizark.op.api.service.walmart.*;
import com.bizark.op.common.core.domain.BaseEntity;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.*;
import com.bizark.op.common.util.spring.SpringUtils;
import com.bizark.op.service.annotation.MarWalmartShipmentVerify;
import com.bizark.op.service.api.WalmartApi;
import com.bizark.op.service.handler.sale.AbstractExportResultHandler;
import com.bizark.op.service.mapper.account.AccountMapper;
import com.bizark.op.service.mapper.conf.SystemGlobalConfigMapper;
import com.bizark.op.service.mapper.mar.WalmartInboundTagMapper;
import com.bizark.op.service.mapper.mar.WalmartShipmentAttachMapper;
import com.bizark.op.service.mapper.product.SkuChildrenMapper;
import com.bizark.op.service.mapper.walmart.WalmartInboundShipmentItemMapper;
import com.bizark.op.service.mapper.walmart.WalmartInboundShipmentMapper;
import com.bizark.op.service.mapper.walmart.WalmartShipAddressMapper;
import com.bizark.op.service.util.ConvertUtils;
import com.bizark.op.service.util.ExcelTemplateUtils;
import com.bizark.op.service.util.WalmartInboundUtil;
import com.bizark.op.service.util.third.qywx.QywxUtil;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WalmartInboundShipmentServiceImpl extends ServiceImpl<WalmartInboundShipmentMapper, WalmartInboundShipment> implements WalmartInboundShipmentService {

    @Autowired
    private WalmartInboundShipmentMapper walmartInboundShipmentMapper;

    @Autowired
    private TransferOutboundPlanService transferOutboundService;

    @Autowired
    private TaskCenterService taskCenterService;

    @Autowired
    private WalmartInboundShipmentItemService walmartInboundShipmentItemService;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;


    @Autowired
    private WalmartInboundShipmentService fulfillmentInboundShipmentsService;

    @Value("${task.center.file.path}")
    private String filePath;

    @Autowired
    private AccountService accountService;

    @Autowired
    private WalmartShipmentAttachMapper walmartShipmentAttachMapper;

    @Autowired
    private WalmartInboundTagMapper walmartInboundTagMapper;

    @Autowired
    private WalmartInboundShipmentItemMapper walmartInboundShipmentItemMapper;

    @Autowired
    private WalmartShipAddressMapper walmartShipAddressMapper;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private WalmartShipmentAttachService walmartShipmentAttachService;

    @Autowired
    private WalmartApi walmartApi;

    @Autowired
    private WalmartShipAddressService walmartShipAddressService;

    @Autowired
    private MarWalmartInboundPackService marWalmartInboundPackService;

    @Autowired
    private MarWalmartInboundShipmentLogService marWalmartInboundShipmentLogService;

    @Autowired
    private WalmartInboundShipmentService walmartInboundShipmentProxyService;

    @Autowired
    private MarRpaServiceStatusClaimLogService marRpaServiceStatusClaimLogService;

    @Value("${rpa_query_url}")
    private String rpaReqUrl;

    @Value("${enviorment}")
    private String env;

    @Autowired
    private AccountMapper accountMapper;

    @Autowired
    private MarCommonShipmentRuleService marCommonShipmentRuleService;



    @Autowired
    private SystemGlobalConfigMapper systemGlobalConfigMapper;

    @Autowired
    private SkuChildrenMapper skuChildrenMapper;


    @Override
    public void saveOrUpdateShipmentFromMessage(WalmartInboundShipmentMessage shipmentsMessage) {
        shipmentsMessage.setId(null);
        WalmartInboundShipment inboundShipments = new WalmartInboundShipment();
        BeanUtils.copyProperties(shipmentsMessage, inboundShipments);

        WalmartInboundShipment shipments = this.lambdaQuery()
                .eq(WalmartInboundShipment::getInboundOrderId, shipmentsMessage.getInboundOrderId())
                .eq(WalmartInboundShipment::getShipmentId, shipmentsMessage.getShipmentId())
                .eq(WalmartInboundShipment::getChannelId, shipmentsMessage.getChannelId())
                .eq(WalmartInboundShipment::getOrgId, shipmentsMessage.getOrgId())
                .one();

        if (Objects.nonNull(shipments)) {
            inboundShipments.setId(shipments.getId());
            this.updateById(inboundShipments);
            if (shipments.getEstimatedFreight() == null) {
                threadPoolTaskExecutor.execute(()->{
                    getEstimatedFreight(inboundShipments,shipmentsMessage);
                });
            }
            return;
        }
        this.save(inboundShipments);

        threadPoolTaskExecutor.execute(()->{
            getEstimatedFreight(inboundShipments,shipmentsMessage);
        });
    }

    private void getEstimatedFreight(WalmartInboundShipment inboundShipments, WalmartInboundShipmentMessage shipmentsMessage) {
        if (inboundShipments.getOrgId() != null && inboundShipments.getOrgId().equals(1000049) && StringUtils.isNotEmpty(inboundShipments.getChannelId()) && StringUtils.isNotEmpty(inboundShipments.getInboundOrderId())) {
            Account account = accountService.getOne(Wrappers.<Account>lambdaQuery()
                    .eq(Account::getOrgId, inboundShipments.getOrgId())
                    .eq(Account::getFlag, inboundShipments.getChannelId())
                    .eq(Account::getType, "walmart")
                    .eq(Account::getActive, "Y")
                    .isNotNull(Account::getTitle)
                    .ne(Account::getTitle, "")
                    .last("limit 1"));
            if (account == null) {
                log.info("同步货件信息后未找到符合条件的店铺获取预估物流费用--货件ID--{}--msg--{}", inboundShipments.getShipmentId(), JSONObject.toJSONString(shipmentsMessage));
                return;
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("account", account.getTitle());
            List<JSONObject> shipmentIdList = new ArrayList<>();
            JSONObject item = new JSONObject();
            item.put("shipmentId", inboundShipments.getShipmentId());
            item.put("inboundOrderId", inboundShipments.getInboundOrderId());
            item.put("shipmentType", inboundShipments.getInboundOrderId().contains("-US-") ? "International" : "Domestic");
            shipmentIdList.add(item);
            jsonObject.put("shipmentIdList", shipmentIdList);
            rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_E_ERP, MQDefine.MAR_WAR_INBOUND_FEE_SEND_QUEUE, jsonObject);
        }else{
            log.info("同步货件信息获取预估物流费用--货件ID--{}--msg--{}条件不满足", inboundShipments.getShipmentId(), JSONObject.toJSONString(shipmentsMessage));
        }
    }

    @Override
    public List<WalmartInboundShipmentPageVO> selectInboundShipmentPage(WalmartInboundShipmentQueryListDTO dto) {
        List<WalmartInboundShipmentPageVO> vos = walmartInboundShipmentMapper.selectInboundShipmentsPage(dto);
        if (CollectionUtil.isEmpty(vos)) {
            return new ArrayList<>();
        }

        CompletableFuture<List<WalmartInboundShipmentPageVO>> future = CompletableFuture.supplyAsync(() -> {
            // 获取调拨单数据
            List<String> shipmentIds = vos.stream().map(WalmartInboundShipment::getShipmentId).distinct().collect(Collectors.toList());
            return transferOutboundService.findAllByShipmentIds(shipmentIds);
        }, threadPoolTaskExecutor).exceptionally(e -> {
            log.info("WFS列表异常 - 调拨单查询异常 ", e);
            return null;
        }).thenCombine(CompletableFuture.supplyAsync(() -> {
            // 获取发运单数据
            List<Long> shippingOrderIds = vos.stream().map(WalmartInboundShipmentPageVO::getShippingId).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(shippingOrderIds)) {
                return null;
            }
            return walmartInboundShipmentMapper.selectShippingOrdersById(shippingOrderIds);
        }, threadPoolTaskExecutor).exceptionally(e -> {
            log.info("WFS列表异常 - 发运单查询异常 ", e);
            return null;
        }), (r1, r2) -> {
            Map<String, TransferOutboundPlanEntity> trasnferMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(r1)) {
                Map<String, TransferOutboundPlanEntity> map = r1.stream().collect(Collectors.toMap(TransferOutboundPlanEntity::getShipmentId, Function.identity(), (v1, v2) -> v1.getCreatedAt().isAfter(v2.getCreatedAt()) ? v1 : v2));
                trasnferMap.putAll(map);
            }
            Map<Long, ShippingOrderVO> shippingOrderMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(r2)) {
                Map<Long, ShippingOrderVO> map = r2.stream().collect(Collectors.toMap(ShippingOrderVO::getId, Function.identity(), (v1, v2) -> v1));
                shippingOrderMap.putAll(map);
            }
            for (WalmartInboundShipmentPageVO vo : vos) {
                vo.setStatus(vo.getShipmentStatus());
                vo.splicingAddress();
                if (trasnferMap.containsKey(vo.getShipmentId())) {
                    TransferOutboundPlanEntity transferOutboundPlanEntity = trasnferMap.get(vo.getShipmentId());
                    vo.setTransferOrderNo(transferOutboundPlanEntity.getTransferOutboundPlanNo());
                    if (transferOutboundPlanEntity.getTransferOutboundPlanStatus() != null && transferOutboundPlanEntity.getTransferOutboundPlanStatus().equals(110)) {
                        vo.setTransferOrderNo(null);
                    }
                }
                if (shippingOrderMap.containsKey(vo.getShippingId())) {
                    vo.setShippingOrderNo(shippingOrderMap.get(vo.getShippingId()).getShippingNo());
                }
                /*if (StringUtils.isNotEmpty(vo.getPickUpType())) {
                    vo.setPickUpType(MarWalmartInboundShipmentType.getDescByType(vo.getPickUpType()));
                }*/
            }
            return vos;
        });
        try {
            return future.get(1, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        } catch (TimeoutException e) {
            throw new RuntimeException("查询超时");
        }


    }

    @Override
    public void editRemark(Long id, String remark) {
        WalmartInboundShipment shipment = this.getById(id);
        if (Objects.isNull(shipment)) {
            throw new CommonException("未获取到对应ID数据：" + id);
        }
        this.lambdaUpdate()
                .set(BaseEntity::getRemark, remark)
                .eq(WalmartInboundShipment::getId, id)
                .update();
    }

    @Override
    public void export(WalmartInboundShipmentQueryListDTO dto) throws IOException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        AbstractExportResultHandler<WalmartInboundShipmentExportVO> handler = new AbstractExportResultHandler<WalmartInboundShipmentExportVO>(filePath + "/walmartInboundShipment.xlsx") {
            @Override
            protected void handle(List<WalmartInboundShipmentExportVO> data) {
                ConvertUtils.dictConvert(data);
                CompletableFuture<Void> future = CompletableFuture.supplyAsync(() -> {
                    // 获取调拨单数据
                    List<String> shipmentIds = data.stream().map(WalmartInboundShipmentExportVO::getShipmentId).distinct().collect(Collectors.toList());
                    return transferOutboundService.findAllByShipmentIds(shipmentIds);
                }, threadPoolTaskExecutor).exceptionally(e -> {
                    log.info("WFS列表异常 - 调拨单查询异常 ", e);
                    return null;
                }).thenCombine(CompletableFuture.supplyAsync(() -> {
                    // 获取发运单数据
                    List<Long> shippingOrderIds = data.stream().filter(s->s.getShippingId() != null).map(s->s.getShippingId().longValue()).collect(Collectors.toList());
                    if (CollectionUtil.isEmpty(shippingOrderIds)) {
                        return null;
                    }
                    return walmartInboundShipmentMapper.selectShippingOrdersById(shippingOrderIds);
                }, threadPoolTaskExecutor).exceptionally(e -> {
                    log.info("WFS列表异常 - 发运单查询异常 ", e);
                    return null;
                }), (r1, r2) -> {
                    Map<String, TransferOutboundPlanEntity> trasnferMap = new HashMap<>();
                    if (CollectionUtil.isNotEmpty(r1)) {
                        Map<String, TransferOutboundPlanEntity> map = r1.stream().collect(Collectors.toMap(TransferOutboundPlanEntity::getShipmentId, Function.identity(), (v1, v2) -> v1.getCreatedAt().isAfter(v2.getCreatedAt()) ? v1 : v2));
                        trasnferMap.putAll(map);
                    }
                    Map<Long, ShippingOrderVO> shippingOrderMap = new HashMap<>();
                    if (CollectionUtil.isNotEmpty(r2)) {
                        Map<Long, ShippingOrderVO> map = r2.stream().collect(Collectors.toMap(ShippingOrderVO::getId, Function.identity(), (v1, v2) -> v1));
                        shippingOrderMap.putAll(map);
                    }
                    for (WalmartInboundShipmentExportVO vo : data) {
                        if (trasnferMap.containsKey(vo.getShipmentId())) {
                            TransferOutboundPlanEntity transferOutboundPlanEntity = trasnferMap.get(vo.getShipmentId());
                            vo.setTransferOrderNo(transferOutboundPlanEntity.getTransferOutboundPlanNo());
                            if (transferOutboundPlanEntity.getTransferOutboundPlanStatus() != null && transferOutboundPlanEntity.getTransferOutboundPlanStatus().equals(110)) {
                                vo.setTransferOrderNo(null);
                            }
                        }
                        if (vo.getShippingId() != null && shippingOrderMap.containsKey(vo.getShippingId().longValue())) {
                            vo.setTransferOrderNo(shippingOrderMap.get(vo.getShippingId().longValue()).getShippingNo());
                        }

                    }
                   return null;
                });
                try {future.get(1, TimeUnit.MINUTES);
                } catch (Exception e) {

                }

                for (WalmartInboundShipmentExportVO vo : data) {
                    vo.splicingAddress();
                    // 处理日期数据
                    vo.handleDate(sdf);
                    vo.setSyncAttachmentCompleteStr(vo.getSyncAttachmentComplete() == null ? null : (vo.getSyncAttachmentComplete().equals(1) ? "是" : "否"));
                }
            }
        };
        walmartInboundShipmentMapper.steamExportQuery(dto, handler);
        HttpServletResponse response = ServletUtils.getResponse();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + URLEncoder.encode("walmartInboundShipment.xlsx", "UTF-8").replace("+", "%20"));
        response.setCharacterEncoding("UTF-8");
        IoUtil.write(response.getOutputStream(), true, handler.load());
    }

    @Override
    public String asyncExport(WalmartInboundShipmentQueryListDTO dto) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        AbstractExportResultHandler<WalmartInboundShipmentExportVO> handler = new AbstractExportResultHandler<WalmartInboundShipmentExportVO>(filePath + "/walmartInboundShipment-" + System.currentTimeMillis() + ".xlsx") {
            @Override
            protected void handle(List<WalmartInboundShipmentExportVO> data) {
                ConvertUtils.dictConvert(data);
                for (WalmartInboundShipmentExportVO vo : data) {
                    vo.splicingAddress();
                    // 处理日期数据
                    vo.handleDate(sdf);
                }
            }
        };
        walmartInboundShipmentMapper.steamExportQuery(dto, handler);
        File file = handler.file();
        return AliyunOssClientUtil.uploadFile(file.getName(), FileUtil.getInputStream(file), "erp/walmartShipment/export");
    }

    @Override
    public void createExportTask(WalmartInboundShipmentQueryListDTO dto, UserEntity authUserEntity) {
        TaskCenterRequest request = new TaskCenterRequest();
        request.setTaskCode("erp.walmart.inboundorder.shipment.export");
        request.setOrgId(dto.getContextId());
        List<Object> list = new ArrayList<>();
        list.add(JSON.toJSONString(dto));
        request.setArgs(list);
        taskCenterService.startTask(request, authUserEntity);
    }

    @Override
    public List<WalmartInboundShipmentInfoVO> selectShipmentInfo(Integer orgId, List<String> shipmentIds) {

        CompletableFuture<List> future = CompletableFuture.supplyAsync(() -> this.lambdaQuery()
                .eq(WalmartInboundShipment::getOrgId, orgId)
                .in(WalmartInboundShipment::getShipmentId, shipmentIds)
                .list(), threadPoolTaskExecutor).exceptionally(e -> {
            log.error("WalmartInboundShipment查询异常 - ", e);
            return null;
        }).thenCombine(CompletableFuture.supplyAsync(() -> walmartInboundShipmentItemService.lambdaQuery()
                .eq(WalmartInboundShipmentItem::getOrgId, orgId)
                .in(WalmartInboundShipmentItem::getShipmentId, shipmentIds)
                .list(), threadPoolTaskExecutor).exceptionally(e -> {
            log.error("WalmartInboundShipmentItem查询异常 - ", e);
            return new ArrayList<>();
        }), (r1, r2) -> {
            if (CollectionUtil.isEmpty(r1)) {
                return new ArrayList();
            }
            Map<String, List<WalmartInboundShipmentItem>> shipmentMap = r2.stream().collect(Collectors.groupingBy(WalmartInboundShipmentItem::getShipmentId));

            List<WalmartInboundShipmentInfoVO> shipments = new ArrayList<>();

            for (WalmartInboundShipment shipment : r1) {
                WalmartInboundShipmentInfoVO vo = new WalmartInboundShipmentInfoVO();
                vo.setShipmentId(shipment.getShipmentId());
                vo.setAppointmentDateTime(shipment.getAppointmentDateTime());
                vo.setActualDeliveryDate(shipment.getActualDeliveryDate());

                if (shipmentMap.containsKey(shipment.getShipmentId())) {
                    List<WalmartInboundShipmentInfoVO.Info> infos = shipmentMap.get(shipment.getShipmentId()).stream()
                            .map(c -> {
                                WalmartInboundShipmentInfoVO.Info info = new WalmartInboundShipmentInfoVO.Info();
                                info.setSku(c.getErpSku());
                                info.setItemQty(c.getItemQty());
                                info.setSellerSku(c.getSku());
                                return info;
                            }).collect(Collectors.toList());
                    vo.setItems(infos);
                }
                shipments.add(vo);
            }
            return shipments;
        }).exceptionally(e -> {
            log.info("WalmartInboundShipment处理异常 - ", e);
            return new ArrayList();
        });


        try {
            return future.get(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        } catch (TimeoutException e) {
            throw new RuntimeException("查询超时");
        }
    }

    @Override
    public List<InboundInventoryVO> selectInboundByChannelIdAndShipmentId(String channelId, String shipmentId) {
        return walmartInboundShipmentMapper.selectInboundByChannelIdAndShipmentId(channelId, shipmentId);
    }

    @Override
    public void repeat() {
        Long cursor = 0L;
        List<WalmartInboundShipment> shipments = null;

        while (CollectionUtil.isNotEmpty((shipments = selectByIdCursorAsc(cursor)))) {
            cursor = CollectionUtil.getLast(shipments).getId();


            for (WalmartInboundShipment shipment : shipments) {



                List<InboundInventoryVO> inboundInventoryVOS = fulfillmentInboundShipmentsService.selectInboundByChannelIdAndShipmentId(shipment.getChannelId(), shipment.getShipmentId());

                if (CollectionUtil.isEmpty(inboundInventoryVOS)) {
                    continue;
                }


                LambdaUpdateChainWrapper<WalmartInboundShipment> chainWrapper = this.lambdaUpdate()
                        .eq(WalmartInboundShipment::getId, shipment.getId());

                // 设置主表
                Integer sum = inboundInventoryVOS.stream()
                        .map(InboundInventoryVO::getQty)
                        .filter(Objects::nonNull)
                        .reduce(Integer::sum)
                        .orElse(0);
                shipment.setInboundQty(sum);

                chainWrapper.set(WalmartInboundShipment::getInboundQty, sum);
                if (Objects.nonNull(shipment.getReceivedUnits())) {
                    chainWrapper.set(WalmartInboundShipment::getDiffQty, Math.abs(sum - shipment.getReceivedUnits()));
                }

                chainWrapper.update();


                if (CollectionUtil.isEmpty(shipment.getItems())) {
                    continue;
                }

                Map<String, Integer> qtyMap = new HashMap<>();

                if (CollectionUtil.isNotEmpty(inboundInventoryVOS)) {
                    Map<String, Integer> map = inboundInventoryVOS.stream().collect(Collectors.groupingBy(c -> c.getSellerSku() + c.getSku() + c.getShipmentId(), Collectors.summingInt(InboundInventoryVO::getQty)));
                    qtyMap.putAll(map);
                }


                List<WalmartInboundShipmentItem> updateItems = new ArrayList<>();

                for (WalmartInboundShipmentItem item : shipment.getItems()) {
                    String key = item.getSku() + item.getErpSku() + item.getShipmentId();
                    if (qtyMap.containsKey(key)) {
                        Integer stockQty = qtyMap.get(key);
                        item.setInboundQty(stockQty);
                        if (Objects.nonNull(item.getReceivedQty())) {
                            item.setDiffQty(Math.abs(stockQty - item.getReceivedQty()));
                        }
                        updateItems.add(item);
                    }
                }

                if (CollectionUtil.isNotEmpty(updateItems)) {
                    walmartInboundShipmentItemService.updateBatchById(updateItems);
                }


            }

        }



    }


    private List<WalmartInboundShipment> selectByIdCursorAsc(Long idCursor) {
        List<WalmartInboundShipment> shipments = this.lambdaQuery()
                .gt(WalmartInboundShipment::getId, idCursor)
                .orderByAsc(WalmartInboundShipment::getId)
                .last("limit 500")
                .list();

        List<String> shipmentIds = shipments.stream().map(WalmartInboundShipment::getShipmentId).collect(Collectors.toList());

        List<WalmartInboundShipmentItem> shipmentItems = walmartInboundShipmentItemService.lambdaQuery()
                .in(WalmartInboundShipmentItem::getShipmentId, shipmentIds)
                .list();

        Map<String, List<WalmartInboundShipmentItem>> map = shipmentItems.stream()
                .collect(Collectors.groupingBy(c -> c.getInboundOrderId() + c.getShipmentId()));

        for (WalmartInboundShipment shipment : shipments) {
            String key = shipment.getInboundOrderId() + shipment.getShipmentId();
            if (map.containsKey(key)) {
                shipment.setItems(map.get(key));
            }
        }


        return shipments;
    }

    /**
     * 同步货件
     *
     * @param dto
     */
    @Override
    public void syncShipment(WalmartInboundShipmentSyncShipmentDTO dto) {

        Account account = accountService.getOne(Wrappers.lambdaQuery(Account.class).eq(Account::getOrgId, dto.getOrganizationId()).eq(Account::getFlag, dto.getAccount()).last("limit 1"));
        AssertUtil.isFalse(account == null, "店铺不存在");
        dto.setShipmentIds(dto.getShipmentIds().stream().distinct().collect(Collectors.toList()));
        if (dto.getShipmentIds().size() > 100) {
            throw new ErpCommonException("最多同步100个!");
        }
        rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_E_ERP, MQDefine.MAR_WALMART_SYNC_SHIPMENT_QUEUE, JSONObject.parseObject(JSONObject.toJSONString(dto), JSONObject.class));

    }

    /**
     * 设置货件信息
     *
     * @param dto
     * @param contextId
     * @param thisUser
     */
    @Override
    public void setShipment(WalmartInboundShipmentSetShipmentDTO dto, Integer contextId, AuthUserDetails thisUser) {

        WalmartInboundShipment byId = this.getById(dto.getId());
        AssertUtil.isFalse(byId == null, "查询不到对应数据");
       log.info("开始设置货件信息当前用户:{}--原数据:{},设置数据:{}", thisUser.getName(), byId, dto);
        if (!MarWalmartInboundShipmentType.existsDesc(dto.getPickUpType())) {
            throw new ErpCommonException("承运方有误");
        }
        if (ShipmentTypeEnum.getEnumByValue(dto.getShippingType()) == null) {
            throw new ErpCommonException("货运类型有误");
        }
        if (dto.getPickUpType().equals(MarWalmartInboundShipmentType.PLATFORM_PICKUP.getDesc()) && !Arrays.asList(ShipmentTypeEnum.LTL.getValue(), ShipmentTypeEnum.FTL.getValue()).contains(dto.getShippingType())) {
            throw new ErpCommonException("平台承运必须选择LTL或FTL");
        }
        if (dto.getPickUpType().equals(MarWalmartInboundShipmentType.SELF_PICKUP.getDesc()) && ShipmentTypeEnum.SMALL_PARCEL.getValue().equals(dto.getShippingType())) {
            throw new ErpCommonException("自约承运暂不支持Small parcel");
        }

        String businessStatusByShipmentId = null;
        try {
            businessStatusByShipmentId = getBusinessStatusByShipmentId(byId.getShipmentId());
        } catch (Exception e) {
            log.error("设置货件信息查询货件状态异常--{}--信息--{}", dto.getId(), e.getMessage());
        }
        Integer shipmentCarrier = MarWalmartInboundShipmentType.getTypeByDesc(dto.getPickUpType());
        String shipmentType = dto.getShippingType();
        if ((dto.getPalletNumber() != null && dto.getPalletNumber() < 0) || dto.getCartonNumber() <= 0) {
            throw new ErpCommonException("托盘数必须为大于等于0的整数或者空，箱数必须为正整数");
        }
        //查询是否存在托盘标或者箱标，如果存在且修改了托盘标或者箱标，则更新palletTagIsChanged和cartonTagIsChanged为1,未修改则更新为0
        //如果不存在，则更新为1,（初始默认为1）
        LambdaQueryWrapper<WalmartShipmentAttach> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WalmartShipmentAttach::getShipmentId, byId.getShipmentId()).eq(WalmartShipmentAttach::getOrgId, byId.getOrgId());
        List<WalmartShipmentAttach> walmartShipmentAttachList = walmartShipmentAttachMapper.selectList(wrapper);

        Map<String, Boolean> palletAndCartonMap = new HashMap<>();
        assertPalletAndCartonChange(walmartShipmentAttachList, dto.getPalletNumber(), dto.getCartonNumber(), byId, palletAndCartonMap);


        this.lambdaUpdate().eq(WalmartInboundShipment::getId, dto.getId())
                .set(WalmartInboundShipment::getPalletNumber, dto.getPalletNumber())
                .set(WalmartInboundShipment::getCartonNumber, dto.getCartonNumber())
                .set(WalmartInboundShipment::getPickUpType, dto.getPickUpType())
                .set(WalmartInboundShipment::getUpdatedAt, DateUtils.getNowDate())
                .set(WalmartInboundShipment::getUpdatedBy, thisUser.getId())
                .set(WalmartInboundShipment::getUpdatedName, thisUser.getName())
                .set(WalmartInboundShipment::getPalletTagIsChanged, palletAndCartonMap.get("palletNumberChange"))
                .set(WalmartInboundShipment::getCartonTagIsChanged, palletAndCartonMap.get("cartonNumberChange"))
                .set(WalmartInboundShipment::getShipmentCarrier,shipmentCarrier)
                .set(WalmartInboundShipment::getShippingType,shipmentType)
                .set(businessStatusByShipmentId != null,WalmartInboundShipment::getShipmentBusinessStatus,businessStatusByShipmentId)
                .update();

        threadPoolTaskExecutor.execute(() -> {
            try {
                Thread.sleep(30000L);
                walmartShipmentAttachService.updateSyncAttachmentByPalletLabelAndCartonLabel();
            } catch (Exception e) {
                e.printStackTrace();
                log.error("设置货件信息拉取托盘标和箱标失败", e);
            }
        });

        threadPoolTaskExecutor.execute(() -> {
            try {
                Thread.sleep(70000L);
                walmartShipmentAttachService.updateSyncAttachComplete();
            } catch (Exception e) {
                e.printStackTrace();
                log.error("设置货件信息同步完成状态失败", e);
            }
        });
    }


    /**
     * 下载导入设置货件信息模板
     *
     * @param contextId
     * @param response
     */
    @Override
    public void downloadShipmentTemplate(Integer contextId, HttpServletResponse response) {

        List<String[]> downData = new ArrayList<>();
        downData.add(MarWalmartInboundShipmentType.getAllDesc());
        downData.add(new String[]{"LTL","FTL","LCL","FCL"});
        int[] downRows = {1,2};
        int[] dateCells = {};
        String[] title = {
                "货件ID*",
                "承运方*",
                "货运类型*",
                "托盘数",
                "箱数*"
        };
        Map<Integer, String> titleText = new HashMap<>();
        try {
            ExcelTemplateUtils.createExcelTemplateAndText(this.getClass().getClassLoader().getResource("").getPath() + "templates/walmartInboundShipment/template.xls",
                    title, titleText, downData, downRows, dateCells, response);
        } catch (Exception e) {
            e.printStackTrace();
            throw new CommonException("示例下载失败");
        }
    }

    /**
     * 导入设置货件信息
     *
     * @param file
     * @param contextId
     * @param thisUser
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importShipment(MultipartFile file, Integer contextId, AuthUserDetails thisUser) {

        Date nowDate = DateUtils.getNowDate();
        log.info("开始导入货件信息,当前时间:{},当前用户:{}", nowDate, thisUser.getName());
        AssertUtil.isFalse(file == null, "请上传文件");
        String originalFilename = file.getOriginalFilename();
        AssertUtil.isFalse(StringUtil.isEmpty(originalFilename), "文件名为空");
        String suffix = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
        if (!suffix.equalsIgnoreCase("xlsx") && !suffix.equalsIgnoreCase("xls")) {
            throw new ErpCommonException("请选择excel文件");
        }
        List<WalmartInboundShipmentSetShipmentDTO> dataList = ExcelUtils.excelImportFilterAnnotation(file, WalmartInboundShipmentSetShipmentDTO.class);
        if (CollectionUtil.isEmpty(dataList)) {
            throw new ErpCommonException("导入数据为空");
        }
        dataList.forEach(t -> t.setRowNum(dataList.indexOf(t) + 1));
        dataList.forEach(t -> {
            String match = "^[1-9]\\d*$";
            String palletNumberMatch = "^[0-9]\\d*$";
            if (StringUtils.isNotEmpty(t.getPalletNumberStr()) && !t.getPalletNumberStr().matches(palletNumberMatch)) {
                throw new ErpCommonException(String.format("第[%s]行托盘数[%s]填写有误，请填写大于等于0的整数或不填", t.getRowNum(), t.getPalletNumberStr()));
            }
            if (!t.getCartonNumberStr().matches(match)) {
                throw new ErpCommonException(String.format("第[%s]行箱数[%s]填写有误，请填写正整数", t.getRowNum(), t.getCartonNumberStr()));
            }
            if (StringUtils.isNotEmpty(t.getPalletNumberStr())) {
                t.setPalletNumber(Integer.parseInt(t.getPalletNumberStr()));
            }

            t.setCartonNumber(Integer.parseInt(t.getCartonNumberStr()));
        });
        Map<String, List<WalmartInboundShipmentSetShipmentDTO>> shipmentIdMap = dataList.stream().collect(Collectors.groupingBy(WalmartInboundShipmentSetShipmentDTO::getShipmentId));
        shipmentIdMap.forEach((shipmentId, dtoList) -> {
            if (dtoList.size() > 1) {
                throw new ErpCommonException(String.format("第[%s]行货件ID[%s]存在重复数据", dtoList.stream().map(q -> q.getRowNum().toString()).collect(Collectors.joining(",")), shipmentId));
            }
        });

        dataList.forEach(t -> {
            if (!MarWalmartInboundShipmentType.existsDesc(t.getPickUpType())) {
                throw new ErpCommonException(String.format("第[%s]行承运方[%s]有误，请选择[%s]以下承运方", t.getRowNum(), t.getPickUpType(), Arrays.asList(MarWalmartInboundShipmentType.getAllDesc()).stream().collect(Collectors.joining(","))));
            }

            if (ShipmentTypeEnum.getEnumByValue(t.getShippingType()) == null) {
                throw new ErpCommonException(String.format("第[%s]行货运类型有误", t.getRowNum()));
            }
            if (t.getPickUpType().equals(MarWalmartInboundShipmentType.PLATFORM_PICKUP.getDesc()) && !Arrays.asList(ShipmentTypeEnum.LTL.getValue(), ShipmentTypeEnum.FTL.getValue()).contains(t.getShippingType())) {
                throw new ErpCommonException(String.format("第[%s]行平台承运必须选择LTL或FTL", t.getRowNum()));
            }
            if (t.getPickUpType().equals(MarWalmartInboundShipmentType.SELF_PICKUP.getDesc()) && ShipmentTypeEnum.SMALL_PARCEL.getValue().equals(t.getShippingType())) {
                throw new ErpCommonException(String.format("第[%s]行自约承运暂不支持Small parcel", t.getRowNum()));
            }
        });
        List<String> shipmentIdList = dataList.stream().map(t -> t.getShipmentId()).collect(Collectors.toList());
        List<WalmartInboundShipment> walmartInboundShipmentList = this.lambdaQuery().eq(WalmartInboundShipment::getOrgId, contextId).in(WalmartInboundShipment::getShipmentId, shipmentIdList).list();
        if (CollectionUtil.isEmpty(walmartInboundShipmentList)) {
            throw new ErpCommonException("导入数据不存在系统!");
        }
        log.info("导入设置货件信息,当前用户:{},导入数据:{}", thisUser.getName(), JSONObject.toJSONString(dataList));
        LambdaQueryWrapper<WalmartShipmentAttach> attachWrapper = new LambdaQueryWrapper<>();
        attachWrapper.in(WalmartShipmentAttach::getShipmentId, shipmentIdList)
                .in(WalmartShipmentAttach::getOrgId, contextId);
        List<WalmartShipmentAttach> walmartShipmentAttachList = walmartShipmentAttachMapper.selectList(attachWrapper);
        walmartInboundShipmentList.forEach(t -> {
            List<WalmartShipmentAttach> shipmentAttachList = CollectionUtil.isEmpty(walmartInboundShipmentList) ? new ArrayList<>() : walmartShipmentAttachList.stream().filter(q -> q.getShipmentId().equals(t.getShipmentId()) && q.getOrgId().equals(t.getOrgId())).collect(Collectors.toList());
            Integer palletNumber = dataList.stream().filter(q -> q.getShipmentId().equals(t.getShipmentId())).findFirst().get().getPalletNumber();
            Integer cartonNumber = dataList.stream().filter(q -> q.getShipmentId().equalsIgnoreCase(t.getShipmentId())).findFirst().get().getCartonNumber();
            Map<String, Boolean> palletAndCartonMap = new HashMap<>();
            assertPalletAndCartonChange(shipmentAttachList, palletNumber, cartonNumber, t, palletAndCartonMap);
//            t.setPickUpType(dataList.stream().filter(q -> q.getShipmentId().equalsIgnoreCase(t.getShipmentId())).findFirst().get().getPickUpType());
            t.setUpdatedAt(nowDate);
            t.setUpdatedBy(thisUser.getId());
            t.setUpdatedName(thisUser.getName());


            t.setPalletNumber(palletNumber);
            t.setCartonNumber(cartonNumber);
            t.setPalletTagIsChanged(palletAndCartonMap.get("palletNumberChange"));
            t.setCartonTagIsChanged(palletAndCartonMap.get("cartonNumberChange"));

            Integer shipmentCarrier = MarWalmartInboundShipmentType.getTypeByDesc(dataList.stream().filter(q -> q.getShipmentId().equalsIgnoreCase(t.getShipmentId())).findFirst().get().getPickUpType());
            t.setShipmentCarrier(shipmentCarrier);
            t.setShippingType(dataList.stream().filter(q -> q.getShipmentId().equals(t.getShipmentId())).findFirst().get().getShippingType());

            String businessStatusByShipmentId = null;
            try {
                businessStatusByShipmentId = getBusinessStatusByShipmentId(t.getShipmentId());
            } catch (Exception e) {
                log.error("导入设置货件信息查询货件状态异常--{}--信息--{}", t.getId(), e.getMessage());
            }
            t.setShipmentBusinessStatus(businessStatusByShipmentId);
        });
        List<Long> collect = walmartInboundShipmentList.stream().filter(q -> q.getPalletNumber() == null).map(q -> q.getId()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(collect)) {
            this.lambdaUpdate().in(WalmartInboundShipment::getId, collect).set(WalmartInboundShipment::getPalletNumber, null).update();
        }

        this.updateBatchById(walmartInboundShipmentList);
    }

    /**
     * @Description: 根据id获取创建调拨计划单所需信息
     * @Author: wly
     * @Date: 2024/12/13 16:52
     * @Params: [contextId, id]
     * @Return: com.bizark.op.api.entity.op.walmart.vo.WalmartCreateTransferPlanInfoVO
     **/
    @Override
    public WalmartCreateTransferPlanInfoVO getCreateTransferPlanInfoVo(Integer contextId, Long id) {

        WalmartCreateTransferPlanInfoVO vo = new WalmartCreateTransferPlanInfoVO();
        vo.setContextId(contextId);
        WalmartInboundShipment byId = this.getById(id);
        AssertUtil.isFalse(byId == null, "数据不存在");
        AssertUtil.isFalse(StringUtils.isEmpty(byId.getShipmentId()), "货件ID为空");
        List<TransferOutboundPlanEntity> allByShipmentIds = transferOutboundService.findAllByShipmentIds(Arrays.asList(byId.getShipmentId()));

        if (CollectionUtil.isNotEmpty(allByShipmentIds)) {
            if (allByShipmentIds.stream().anyMatch(t -> t.getTransferOutboundPlanStatus() != null && !t.getTransferOutboundPlanStatus().equals(110))) {
                throw new ErpCommonException("已创建调拨计划单，不可重复创建");
            }
        }

        LambdaQueryWrapper<WalmartInboundShipmentItem> itemWrapper = new LambdaQueryWrapper<>();
        itemWrapper.eq(WalmartInboundShipmentItem::getChannelId, byId.getChannelId())
                .eq(WalmartInboundShipmentItem::getInboundOrderId, byId.getInboundOrderId())
                .eq(WalmartInboundShipmentItem::getShipmentId, byId.getShipmentId());
        List<WalmartInboundShipmentItem> walmartInboundShipmentItemList = walmartInboundShipmentItemService.list(itemWrapper);
        if (CollectionUtil.isEmpty(walmartInboundShipmentItemList)) {
            throw new ErpCommonException(String.format("货件ID[%s]下无商品信息", byId.getShipmentId()));
        }
        //  如货件明细中的SellerSKU存在未匹配到SKU（即货件明细里没有erpsku(这个erpsku的值在定时推送货件信息时根据查询sku映射表审批通过的erpsku写入)），则报错提示“SellerSKU：XXXX，YYYY，未匹配到SKU，不可创建调拨计划单”。

        List<String> noMatchSku = walmartInboundShipmentItemList.stream().filter(t -> StringUtils.isEmpty(t.getErpSku())).map(t -> t.getSku()).distinct().collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(noMatchSku)) {
            throw new ErpCommonException("SellerSKU：" + String.join(",", noMatchSku) + "未匹配到SKU，不可创建调拨计划单");
        }

        //判断是否开启了多版本
        SystemGlobalConfig skuVersionInitConfig = systemGlobalConfigMapper.selectByOrgIdAndConfigKey(contextId, "skuVersionInitConfig");

        Boolean skuMulVersion = skuVersionInitConfig != null && new Integer(1).equals(skuVersionInitConfig.getStatus());
        //如启用了多版本开关，则如果SellerSKU如果映射的不是父SKU就报错提示(即根据货件明细的erpsku（当做子sku表的父sku查询）去查子sku表无数据)，是父SKU则赋值到产品信息时，调整为赋值父SKU和子SKU，父SKU是货件明细中存储的，子SKU查询SKU多版本中该父SKU下面发货优先级高的那个SKU来赋值，数量赋值逻辑不变。
        //     如未启用多版本开关，不赋值父SKU，赋值的直接子SKU。
        //开启了多版本，那sku里存的erpsku理论上都是父sku,未开启则是子sku
        List<SkuChildren> skuChildrenList;
        if (skuMulVersion) {
            List<String> parentSkuList = walmartInboundShipmentItemList.stream().map(t -> t.getErpSku()).distinct().collect(Collectors.toList());
            skuChildrenList = skuChildrenMapper.selectByOrgIdAndParentSku(contextId, parentSkuList);
            if (CollectionUtil.isEmpty(skuChildrenList)) {
                throw new ErpCommonException("SKU[" + String.join(",", parentSkuList) + "]映射的不是父SKU");
            } else {
                parentSkuList.removeIf(q -> skuChildrenList.stream().map(t -> t.getParentSku()).distinct().collect(Collectors.toList()).contains(q));
                if (CollectionUtil.isNotEmpty(parentSkuList)) {
                    throw new ErpCommonException("SKU[" + String.join(",", parentSkuList) + "]映射的不是父SKU");
                }
            }

        } else {
            skuChildrenList = new ArrayList<>();
        }
        //根据sellerSku+组织查询项目ID
        Integer lineId = walmartInboundShipmentItemMapper.selectProjectIdBySellerSkuAndChannelId(walmartInboundShipmentItemList.stream().map(WalmartInboundShipmentItem::getSku).distinct().collect(Collectors.toList()), byId.getChannelId(), byId.getOrgId());
        vo.setLineId(lineId);//项目ID
        vo.setOrgWarehouseId(byId.getShipWarehouseId());//调出仓库ID
        // 取对应店铺的平台仓
        vo.setInOrgWarehouseId(this.baseMapper.selectWarehouseIdByChannelId(byId.getChannelId()));
        vo.setCarrierName(byId.getCarrierName());//承运商
        vo.setBolNumber(byId.getBolNumber());//BOL编号
        vo.setSourceNo(byId.getInboundOrderId());//来源单号
        vo.setEstimateInboundTime(byId.getExpectedDeliveryDate() == null ? null : LocalDateTimeUtils.parseDateStrToLocalDate(DateUtil.convertDateToString(byId.getExpectedDeliveryDate())));//期望入库日期
        vo.setShipmentId(byId.getShipmentId());
        LambdaQueryWrapper<WalmartShipAddress> shipAddressWrapper = new LambdaQueryWrapper<>();
        shipAddressWrapper.eq(WalmartShipAddress::getChannelId, byId.getChannelId())
                .eq(WalmartShipAddress::getInboundOrderId, byId.getInboundOrderId())
                .eq(WalmartShipAddress::getShipmentId, byId.getShipmentId())
                .eq(WalmartShipAddress::getType, 1)
                .last("limit 1");
        WalmartShipAddress walmartShipAddress = walmartShipAddressMapper.selectOne(shipAddressWrapper);
        if (walmartShipAddress != null) {
            vo.setReceiveStore(walmartShipAddress.getFcName());//目的地址CODE
            vo.setShipToCountry(walmartShipAddress.getCountryCode());
            vo.setShipToState(walmartShipAddress.getStateCode());
            vo.setShipToCity(walmartShipAddress.getCity());
            vo.setShipToAddress1(walmartShipAddress.getAddressLine1());
            vo.setShipToAddress2(walmartShipAddress.getAddressLine2());
            vo.setShipToPostal(walmartShipAddress.getPostalCode());
            vo.setShipToContact("walmart");

        }
        List<WalmartCreateTransferPlanInfoVO.TransferOutboundPlanItemModel> itemModelList = new ArrayList<>();
        List<WalmartInboundShipmentItem> productIdList = walmartInboundShipmentItemMapper.selectProductIdByErpSkuAndOrgId(walmartInboundShipmentItemList.stream().filter(t -> StringUtils.isNotEmpty(t.getErpSku())).map(WalmartInboundShipmentItem::getErpSku).distinct().collect(Collectors.toList()), contextId);
        for (WalmartInboundShipmentItem walmartInboundShipmentItem : walmartInboundShipmentItemList) {
            WalmartCreateTransferPlanInfoVO.TransferOutboundPlanItemModel transferOutboundPlanItemModel = new WalmartCreateTransferPlanInfoVO.TransferOutboundPlanItemModel();
            transferOutboundPlanItemModel.setSellerSku(walmartInboundShipmentItem.getSku());
            if (skuMulVersion) {
                transferOutboundPlanItemModel.setParentSku(walmartInboundShipmentItem.getErpSku());
                //查询子sku
                List<SkuChildren> skuAllChildSku = skuChildrenList.stream().filter(t -> walmartInboundShipmentItem.getErpSku().equalsIgnoreCase(t.getParentSku())).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(skuAllChildSku)) {
                    throw new ErpCommonException("SKU[" + walmartInboundShipmentItem.getErpSku() + "]没有子SKU");
                }
                if (skuAllChildSku.stream().allMatch(t->t.getPriority() == null)) {
                    throw new ErpCommonException("SKU[" + walmartInboundShipmentItem.getErpSku() + "]所有子SKU都未配置优先级");
                }
                String highestLevel = skuAllChildSku.stream().min(Comparator.comparing(SkuChildren::getPriority)).get().getSku();
                transferOutboundPlanItemModel.setSku(highestLevel);
                List<WalmartInboundShipmentItem> highestLevelProduct = walmartInboundShipmentItemMapper.selectProductIdByErpSkuAndOrgId(Arrays.asList(highestLevel), contextId);
                if (CollectionUtil.isNotEmpty(highestLevelProduct)) {
                    transferOutboundPlanItemModel.setProductId(highestLevelProduct.get(0).getId());
                }

            } else {
                transferOutboundPlanItemModel.setSku(walmartInboundShipmentItem.getErpSku());
                if (CollectionUtil.isNotEmpty(productIdList)) {
                    productIdList.stream().filter(t -> StringUtils.isNotEmpty(walmartInboundShipmentItem.getErpSku()) && walmartInboundShipmentItem.getErpSku().equalsIgnoreCase(t.getErpSku())).findFirst().ifPresent(q -> transferOutboundPlanItemModel.setProductId(q.getId()));
                }
            }

            transferOutboundPlanItemModel.setQty(walmartInboundShipmentItem.getItemQty());
            transferOutboundPlanItemModel.setPlatFormProductCode(walmartInboundShipmentItem.getGtin());
            transferOutboundPlanItemModel.setCartonNumber(walmartInboundShipmentItem.getItemQty());

            itemModelList.add(transferOutboundPlanItemModel);
        }
        vo.setTransferOutboundPlanItemModelList(itemModelList);

        return vo;
    }

    @Override
    public void getCreateTransferPlanInfoVoPre(Integer contextId, Long id) {

        WalmartCreateTransferPlanInfoVO vo = new WalmartCreateTransferPlanInfoVO();
        vo.setContextId(contextId);
        WalmartInboundShipment byId = this.getById(id);
        AssertUtil.isFalse(byId == null, "数据不存在");
        AssertUtil.isFalse(StringUtils.isEmpty(byId.getShipmentId()), "货件ID为空");
        List<TransferOutboundPlanEntity> allByShipmentIds = transferOutboundService.findAllByShipmentIds(Arrays.asList(byId.getShipmentId()));

        if (CollectionUtil.isNotEmpty(allByShipmentIds)) {
            if (allByShipmentIds.stream().anyMatch(t -> t.getTransferOutboundPlanStatus() != null && !t.getTransferOutboundPlanStatus().equals(110))) {
                throw new ErpCommonException("已创建调拨计划单，不可重复创建");
            }
        }

        LambdaQueryWrapper<WalmartInboundShipmentItem> itemWrapper = new LambdaQueryWrapper<>();
        itemWrapper.eq(WalmartInboundShipmentItem::getChannelId, byId.getChannelId())
                .eq(WalmartInboundShipmentItem::getInboundOrderId, byId.getInboundOrderId())
                .eq(WalmartInboundShipmentItem::getShipmentId, byId.getShipmentId());
        List<WalmartInboundShipmentItem> walmartInboundShipmentItemList = walmartInboundShipmentItemService.list(itemWrapper);
        if (CollectionUtil.isEmpty(walmartInboundShipmentItemList)) {
            throw new ErpCommonException(String.format("货件ID[%s]下无商品信息", byId.getShipmentId()));
        }
        //  如货件明细中的SellerSKU存在未匹配到SKU（即货件明细里没有erpsku(这个erpsku的值在定时推送货件信息时根据查询sku映射表审批通过的erpsku写入)），则报错提示“SellerSKU：XXXX，YYYY，未匹配到SKU，不可创建调拨计划单”。

        List<String> noMatchSku = walmartInboundShipmentItemList.stream().filter(t -> StringUtils.isEmpty(t.getErpSku())).map(t -> t.getSku()).distinct().collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(noMatchSku)) {
            throw new ErpCommonException("SellerSKU：" + String.join(",", noMatchSku) + "未匹配到SKU，不可创建调拨计划单");
        }

        //判断是否开启了多版本
        SystemGlobalConfig skuVersionInitConfig = systemGlobalConfigMapper.selectByOrgIdAndConfigKey(contextId, "skuVersionInitConfig");

        Boolean skuMulVersion = skuVersionInitConfig != null && new Integer(1).equals(skuVersionInitConfig.getStatus());
        //如启用了多版本开关，则如果SellerSKU如果映射的不是父SKU就报错提示(即根据货件明细的erpsku（当做子sku表的父sku查询）去查子sku表无数据)，是父SKU则赋值到产品信息时，调整为赋值父SKU和子SKU，父SKU是货件明细中存储的，子SKU查询SKU多版本中该父SKU下面发货优先级高的那个SKU来赋值，数量赋值逻辑不变。
        //     如未启用多版本开关，不赋值父SKU，赋值的直接子SKU。
        //开启了多版本，那sku里存的erpsku理论上都是父sku,未开启则是子sku
        List<SkuChildren> skuChildrenList;
        if (skuMulVersion) {
            List<String> parentSkuList = walmartInboundShipmentItemList.stream().map(t -> t.getErpSku()).distinct().collect(Collectors.toList());
            skuChildrenList = skuChildrenMapper.selectByOrgIdAndParentSku(contextId, parentSkuList);
            if (CollectionUtil.isEmpty(skuChildrenList)) {
                throw new ErpCommonException("SKU[" + String.join(",", parentSkuList) + "]映射的不是父SKU");
            } else {
                parentSkuList.removeIf(q -> skuChildrenList.stream().map(t -> t.getParentSku()).distinct().collect(Collectors.toList()).contains(q));
                if (CollectionUtil.isNotEmpty(parentSkuList)) {
                    throw new ErpCommonException("SKU[" + String.join(",", parentSkuList) + "]映射的不是父SKU");
                }
            }

        }else {
            skuChildrenList = new ArrayList<>();
        }

        List<WalmartInboundShipmentItem> productIdList = walmartInboundShipmentItemMapper.selectProductIdByErpSkuAndOrgId(walmartInboundShipmentItemList.stream().filter(t -> StringUtils.isNotEmpty(t.getErpSku())).map(WalmartInboundShipmentItem::getErpSku).distinct().collect(Collectors.toList()), contextId);
        for (WalmartInboundShipmentItem walmartInboundShipmentItem : walmartInboundShipmentItemList) {
            WalmartCreateTransferPlanInfoVO.TransferOutboundPlanItemModel transferOutboundPlanItemModel = new WalmartCreateTransferPlanInfoVO.TransferOutboundPlanItemModel();
            transferOutboundPlanItemModel.setSellerSku(walmartInboundShipmentItem.getSku());
            if (skuMulVersion) {
                transferOutboundPlanItemModel.setParentSku(walmartInboundShipmentItem.getErpSku());
                //查询子sku
                List<SkuChildren> skuAllChildSku = skuChildrenList.stream().filter(t -> walmartInboundShipmentItem.getErpSku().equalsIgnoreCase(t.getParentSku())).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(skuAllChildSku)) {
                    throw new ErpCommonException("SKU[" + walmartInboundShipmentItem.getErpSku() + "]没有子SKU");
                }
                if (skuAllChildSku.stream().allMatch(t->t.getPriority() == null)) {
                    throw new ErpCommonException("SKU[" + walmartInboundShipmentItem.getErpSku() + "]所有子SKU都未配置优先级");
                }
                String highestLevel = skuAllChildSku.stream().min(Comparator.comparing(SkuChildren::getPriority)).get().getSku();
                transferOutboundPlanItemModel.setSku(highestLevel);
                List<WalmartInboundShipmentItem> highestLevelProduct = walmartInboundShipmentItemMapper.selectProductIdByErpSkuAndOrgId(Arrays.asList(highestLevel), contextId);
                if (CollectionUtil.isNotEmpty(highestLevelProduct)) {
                    transferOutboundPlanItemModel.setProductId(highestLevelProduct.get(0).getId());
                }

            } else {
                transferOutboundPlanItemModel.setSku(walmartInboundShipmentItem.getErpSku());
                if (CollectionUtil.isNotEmpty(productIdList)) {
                    productIdList.stream().filter(t -> StringUtils.isNotEmpty(walmartInboundShipmentItem.getErpSku()) && walmartInboundShipmentItem.getErpSku().equalsIgnoreCase(t.getErpSku())).findFirst().ifPresent(q -> transferOutboundPlanItemModel.setProductId(q.getId()));
                }
            }

        }

    }

    /**
     * @Description: 根据id获取附件信息
     * @Author: wly
     * @Date: 2024/12/16 16:10
     * @Params: [id]
     * @Return: java.util.List<com.bizark.op.api.entity.op.walmart.vo.WalmartInboundShipmentAttachInfo>
     **/

    @Override
    public List<WalmartInboundShipmentAttachInfo> getNotCompleteAttachmentInfo(Long id) {
        List<WalmartInboundShipmentAttachInfo> result = new ArrayList<>();
        for (MarWalmartTagType value : MarWalmartTagType.values()) {
            WalmartInboundShipmentAttachInfo attachInfo = new WalmartInboundShipmentAttachInfo();
            attachInfo.setType(value.getType());
            attachInfo.setCompleted(false);
            //todo 暂时排除发票
            if (MarWalmartTagType.NVO_FREIGHT_INVOICE.getType().equals(value.getType())) {
                continue;
            }
            result.add(attachInfo);
        }
        WalmartInboundShipment byId = this.getById(id);


        LambdaQueryWrapper<WalmartShipmentAttach> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WalmartShipmentAttach::getShipmentId, byId.getShipmentId())
                .eq(WalmartShipmentAttach::getOrgId, byId.getOrgId());
        List<WalmartShipmentAttach> walmartShipmentAttachList = walmartShipmentAttachMapper.selectList(wrapper);
        if (CollectionUtil.isNotEmpty(walmartShipmentAttachList)) {
            for (WalmartShipmentAttach attach : walmartShipmentAttachList) {
                result.stream().filter(t -> t.getType().equals(attach.getType())).findFirst().ifPresent(t -> {
                    t.setCompleted(true);
                    t.setCreatedAt(attach.getCreatedAt());
                    t.setUrl(Arrays.asList(attach.getUrl()));
                });
            }

        }
        LambdaQueryWrapper<WalmartInboundShipmentItem> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WalmartInboundShipmentItem::getChannelId, byId.getChannelId())
                .eq(WalmartInboundShipmentItem::getInboundOrderId, byId.getInboundOrderId())
                .eq(WalmartInboundShipmentItem::getShipmentId, byId.getShipmentId())
                .isNotNull(WalmartInboundShipmentItem::getSku)
                .ne(WalmartInboundShipmentItem::getSku, "");
        List<WalmartInboundShipmentItem> walmartInboundShipmentItemList = walmartInboundShipmentItemService.list(lambdaQueryWrapper);
        if (CollectionUtil.isNotEmpty(walmartInboundShipmentItemList)) {
            List<WalmartInboundTag> walmartInboundTagList = walmartInboundTagMapper.selectList(Wrappers.lambdaQuery(WalmartInboundTag.class)
                    .eq(WalmartInboundTag::getChannelId, byId.getChannelId())
                    .in(WalmartInboundTag::getSellerSku, walmartInboundShipmentItemList.stream().map(WalmartInboundShipmentItem::getSku).distinct().collect(Collectors.toList()))
                    .in(WalmartInboundTag::getGtin, walmartInboundShipmentItemList.stream().map(WalmartInboundShipmentItem::getGtin).distinct().collect(Collectors.toList())));
            if (CollectionUtil.isNotEmpty(walmartInboundTagList)) {
                walmartInboundTagList = walmartInboundTagList.stream().filter(t -> walmartInboundShipmentItemList.stream().anyMatch(q -> t.getSellerSku().equals(q.getSku()) && t.getGtin().equals(q.getGtin()))).collect(Collectors.toList());
                List<WalmartInboundTag> finalWalmartInboundTagList = walmartInboundTagList;
                result.stream().filter(t -> t.getType().equals(MarWalmartTagType.GTIN.getType())).findFirst().ifPresent(t -> {
                    t.setCompleted(true);
                    t.setUrl(finalWalmartInboundTagList.stream().map(WalmartInboundTag::getUrl).distinct().collect(Collectors.toList()));
                    List<WalmartInboundShipmentAttachInfo.TypeUrl> typeUrlArrayList = new ArrayList<>();
                    Map<String, List<WalmartInboundTag>> typeUrlList = finalWalmartInboundTagList.stream().collect(Collectors.groupingBy(WalmartInboundTag::getType));
                    typeUrlList.forEach((type, urlList) -> {
                        WalmartInboundShipmentAttachInfo.TypeUrl typeUrl = new WalmartInboundShipmentAttachInfo.TypeUrl();
                        typeUrl.setType(type);
                        typeUrl.setCreatedAt(urlList.get(0).getCreatedAt());
                        typeUrl.setUrl(urlList.stream().map(WalmartInboundTag::getUrl).distinct().collect(Collectors.toList()));
                        typeUrlArrayList.add(typeUrl);
                    });
                    t.setTypeUrlList(typeUrlArrayList);
                });
            }
        }
        return result;
    }


    @Override
    public List<WalmartInboundShipmentDetailAttachInfo> getDetailAttachmentInfo(Long id) {
        List<WalmartInboundShipmentDetailAttachInfo> result = new ArrayList<>();
        WalmartInboundShipment byId = this.getById(id);
        LambdaQueryWrapper<WalmartShipmentAttach> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WalmartShipmentAttach::getShipmentId, byId.getShipmentId())
                .eq(WalmartShipmentAttach::getOrgId, byId.getOrgId())
                .eq(WalmartShipmentAttach::getType,MarWalmartTagType.NVO_FREIGHT_INVOICE.getType());
        List<WalmartShipmentAttach> walmartShipmentAttachList = walmartShipmentAttachMapper.selectList(wrapper);
        if (CollectionUtil.isNotEmpty(walmartShipmentAttachList)) {
            WalmartShipmentAttach walmartShipmentAttach = walmartShipmentAttachList.get(0);
            Arrays.stream(walmartShipmentAttach.getUrl().split(",")).forEach(url -> {
                WalmartInboundShipmentDetailAttachInfo attach = new WalmartInboundShipmentDetailAttachInfo();
                attach.setFileUrl(url);
                attach.setCreatedAt(walmartShipmentAttach.getCreatedAt());
                attach.setType(MarWalmartTagType.NVO_FREIGHT_INVOICE.getType());
                attach.setFileName(url.substring(walmartShipmentAttach.getUrl().lastIndexOf("/") + 1));
                result.add(attach);
            });
        }
        return result;
    }

    @Override
    public List<WalmartInboundShipmentAttachInfo> getAttachmentInfo(Long id, Integer warehouseId) {
        List<WalmartInboundShipmentAttachInfo> notCompleteAttachmentInfo = this.getNotCompleteAttachmentInfo(id);
        if (CollectionUtil.isEmpty(notCompleteAttachmentInfo)) {
            return new ArrayList<>();
        }
        //  EFNJ03、EFGA  采用一页一个的类型
        //EFGA01、EFNJ01、EFNJ04、EFTX01、EFIL01   采用一页30个的类型
        /*String gtinUrlType = null; //
        Integer shipWarehouseId =  warehouseId != null ? warehouseId : this.getById(id).getShipWarehouseId();
        String code = shipWarehouseId == null ? null : this.baseMapper.selectWarehouseCodeById(shipWarehouseId);
        if (StringUtils.isNotEmpty(code)) {
            if (Arrays.asList("EFNJ03", "EFGA").contains(code)) {
                gtinUrlType = "onePageOne";
            } else if (Arrays.asList("EFGA01", "EFNJ04", "EFTX01", "EFIL01").contains(code)) {
                gtinUrlType = "onePageThirty";
            }
        }*/
        List<WalmartInboundShipmentAttachInfo> result = new ArrayList<>();
        for (WalmartInboundShipmentAttachInfo attachInfo : notCompleteAttachmentInfo) {

            if (MarWalmartTagType.GTIN.getType().equals(attachInfo.getType())) {

                if (CollectionUtil.isNotEmpty(attachInfo.getTypeUrlList())) {
                    for (WalmartInboundShipmentAttachInfo.TypeUrl typeUrl : attachInfo.getTypeUrlList()) {
                        //todo  后续根据仓库类型判断是否显示
                        if (!"onePageOne".equals(typeUrl.getType())) {
                            continue;
                        }
                        if (CollectionUtil.isEmpty(typeUrl.getUrl())) {
                            continue;
                        }
                        for (String url : typeUrl.getUrl()) {

                            WalmartInboundShipmentAttachInfo info = new WalmartInboundShipmentAttachInfo();
                            info.setType("itemlabel");
                            if (StringUtils.isNotEmpty(url)) {
                                info.setFileName(url.substring(url.lastIndexOf("/") + 1).replace("-1.pdf", "-1页1个.pdf"));
                                info.setFileUrl(url);
                            }
                            info.setCreatedAt(typeUrl.getCreatedAt());
                            result.add(info);
                        }
                    }
                }

            } else if (MarWalmartTagType.BOL.getType().equals(attachInfo.getType())) {
                if (CollectionUtil.isNotEmpty(attachInfo.getUrl())) {
                    for (String url : attachInfo.getUrl()) {
                        WalmartInboundShipmentAttachInfo info = new WalmartInboundShipmentAttachInfo();
                        info.setType("bol");
                        info.setFileName(url.substring(url.lastIndexOf("/") + 1));
                        info.setFileUrl(url);
                        info.setCreatedAt(attachInfo.getCreatedAt());
                        result.add(info);
                    }
                }

            } else if (MarWalmartTagType.PALLET.getType().equals(attachInfo.getType())) {
                if (CollectionUtil.isNotEmpty(attachInfo.getUrl())) {
                    for (String url : attachInfo.getUrl()) {
                        WalmartInboundShipmentAttachInfo info = new WalmartInboundShipmentAttachInfo();
                        info.setType("palletlabel");
                        info.setFileName(url.split("attachPallet")[1]);
                        info.setFileUrl(url);
                        info.setCreatedAt(attachInfo.getCreatedAt());
                        result.add(info);
                    }
                }

            } else if (MarWalmartTagType.CARTON.getType().equals(attachInfo.getType())) {
                if (CollectionUtil.isNotEmpty(attachInfo.getUrl())) {
                    for (String url : attachInfo.getUrl()) {
                        WalmartInboundShipmentAttachInfo info = new WalmartInboundShipmentAttachInfo();
                        info.setType("cartonlabel");
                        info.setFileName(url.split("attachCarton")[1]);
                        info.setFileUrl(url);
                        info.setCreatedAt(attachInfo.getCreatedAt());
                        result.add(info);
                    }
                }

            }
        }
        return result;
    }

    private void assertPalletAndCartonChange(List<WalmartShipmentAttach> walmartShipmentAttachList, Integer palletNumber, Integer cartonNumber, WalmartInboundShipment byId, Map<String, Boolean> map) {

        Boolean palletNumberChange = null;
        Boolean cartonNumberChange = null;
        if (CollectionUtil.isNotEmpty(walmartShipmentAttachList)) {

            WalmartShipmentAttach palletAttach = walmartShipmentAttachList.stream().filter(t -> MarWalmartTagType.PALLET.getType().equals(t.getType())).findFirst().orElse(null);
            WalmartShipmentAttach cartonAttach = walmartShipmentAttachList.stream().filter(t -> MarWalmartTagType.CARTON.getType().equals(t.getType())).findFirst().orElse(null);
            if (palletAttach == null) {
                palletNumberChange = true;
            } else {
                if (palletNumber == null) {
                    if (byId.getPalletNumber() == null) {
                        palletNumberChange = false;
                    } else {
                        palletNumberChange = true;
                    }
                } else {
                    if (byId.getPalletNumber() == null) {
                        palletNumberChange = true;
                    } else {
                        palletNumberChange = !palletNumber.equals(byId.getPalletNumber());
                    }
                }
            }
            if (cartonAttach == null) {
                cartonNumberChange = true;
            } else {
                cartonNumberChange = !cartonNumber.equals(byId.getCartonNumber());
            }

        } else {
            palletNumberChange = true;
            cartonNumberChange = true;
        }
        map.put("palletNumberChange", palletNumberChange);
        map.put("cartonNumberChange", cartonNumberChange);
    }

    @Override
    public void updateIsCreatePlan(Integer contextId, String shipmentId, AuthUserDetails authUser, Integer createPlanFlag) {
        this.lambdaUpdate().eq(WalmartInboundShipment::getShipmentId, shipmentId)
                .eq(WalmartInboundShipment::getOrgId, contextId)
                .set(WalmartInboundShipment::getTransportPlanGenerated, createPlanFlag)
                .set(WalmartInboundShipment::getUpdatedAt, DateUtils.getNowDate())
                .set(WalmartInboundShipment::getUpdatedBy, authUser.getId())
                .set(WalmartInboundShipment::getUpdatedName, authUser.getName())
                //取消调拨单，更新货件业务状态为待发货，便于重新创建挑拨单
                .set(createPlanFlag.equals(0),WalmartInboundShipment::getShipmentBusinessStatus,ShipmentProcessStatusEnum.PENDING_SHIPPING.getValue())
                .update();
    }


    @Override
    public WalmartInboundShipmentPageVO selectInboundShipmentPageTotal(WalmartInboundShipmentQueryListDTO dto) {
        WalmartInboundShipmentPageVO shipmentList = walmartInboundShipmentMapper.selectInboundShipmentsPageTotal(dto);
        return shipmentList;

    }

    /**
     * @Description: 创建货件
     * @Author: wly
     * @Date: 2025-05-14 17:47
     * @Params: shopId 店铺ID
     * @Params: inboundOrderId 货件唯一ID
     * @Params: orgWarehouseId 组织仓ID
     * @Return: com.bizark.op.api.entity.op.walmart.dto.WalmartInboundShipmentCreateResponse
     **/
    @Override
    public WalmartInboundShipmentCreateResponse createInboundShipment(String accountFlag,
                                                                      List<WalmartInboundShipmentItem> walmartItems,
                                                                      String inboundOrderId,
                                                                      WalmartShipAddress walmartShipAddress) {

        Account account = accountService.getOne(Wrappers.<Account>lambdaQuery().eq(Account::getFlag, accountFlag));

        List<WalmartInboundShipmentCreateRequest.OrderItem> orderItems = new ArrayList<>();
        WalmartInboundShipmentCreateRequest request = new WalmartInboundShipmentCreateRequest();
        WalmartInboundShipmentCreateRequest.ReturnAddress returnAddress = new WalmartInboundShipmentCreateRequest.ReturnAddress();
        returnAddress.setAddressLine1(walmartShipAddress.getAddressLine1());
        returnAddress.setAddressLine2(walmartShipAddress.getAddressLine2());
        returnAddress.setCity(walmartShipAddress.getCity()); //城市
        returnAddress.setStateCode(walmartShipAddress.getStateCode()); //州
//        returnAddress.setCountryCode(walmartShipAddress.getCountryCode());//国家
        returnAddress.setCountryCode("USA");//国家
        returnAddress.setPostalCode(walmartShipAddress.getPostalCode());//邮编
        request.setReturnAddress(returnAddress);
        for (WalmartInboundShipmentItem item : walmartItems) {
            WalmartInboundShipmentCreateRequest.OrderItem orderItem = new WalmartInboundShipmentCreateRequest.OrderItem();
            orderItem.setProductId(item.getGtin()); //TODO  待确认
            orderItem.setProductType("GTIN"); //产品类型
            orderItem.setSku(item.getSku()); //TODO 待确认是否为Seller SKU
            orderItem.setItemDesc(item.getItemDesc());//描述
            orderItem.setItemQty(item.getItemQty()); //总数 （箱数x每箱个数）
            orderItem.setVendorPackQty(item.getVendorPackQty()); //箱数
            orderItem.setInnerPackQty(item.getInnerPackQty());//每箱个数
            orderItem.setExpectedDeliveryDate(DateUtil.convertStringToISODate(DateUtil.convertDateToString(item.getExpectedDeliveryDate(), DateUtils.YYYY_MM_DD_HH_MM_SS)));
            List<WalmartInboundShipmentCreateRequest.AddOnService> addOnServices = new ArrayList<>();

            if (!StringUtil.isNotEmpty(item.getWfsService())) {
                String[] split = item.getWfsService().split(",");
                for (String wfsServiceFla : split) {
                    if (wfsServiceFla.equals("NO_NEED")){
                        continue;
                    }
                    WalmartInboundShipmentCreateRequest.AddOnService addOnService = new WalmartInboundShipmentCreateRequest.AddOnService();
                    addOnService.setServiceType(wfsServiceFla);
                    addOnServices.add(addOnService);
                }
            }
            orderItem.setAddOnServices(addOnServices);
//            orderItem.setItemNbr();
//            orderItem.setDimensions();
//            orderItem.setItemWeightQty();
//            orderItem.setShipNode();
            orderItems.add(orderItem);
        }
        request.setOrderItems(orderItems);
        request.setInboundOrderId(inboundOrderId);


        try {
            return walmartApi.walmartCommonPost(account, WalmartUrlEnum.WALMART_CREATE_INBOUND_SHIPMENT.getValue(), JSONObject.toJSONString(request), WalmartInboundShipmentCreateResponse.class);
        } catch (Exception e) {
            log.info("创建货件调用失败：{}", e);
        }
        return null;
    }


    /**
     * 获取发货仓CODE接口
     *
     * @param codes
     * @param orgId
     * @return
     */
    @Override
    public List<WalmartShipmentOrganizationWarehousesVO> selectWarehouseAddressByCode(List<String> codes, Integer orgId) {
        return this.baseMapper.selectWarehouseAddressByCode(codes,orgId);
    }


    /**
     * @param
     * @param shipmentDTO
     * @param opType
     * @description: 保存或创建WFS货件信息，页面 （暂存和提交）
     * @author: Moore
     * @date: 2025/5/14 16:42
     * @return: void
     **/
    @Override
    @Transactional
    public void saveOrSubCreateWfsShipment(WalmartCreateShipmentDTO shipmentDTO, String opType) {
        Long id = shipmentDTO.getId();
        if (StringUtil.isEmpty(opType)) {
            throw new ErpCommonException("操作类型不能为空!");
        }
        List<WalmartInboundShipmentItem> filterItems = shipmentDTO.getWalmartItems().stream().filter(item -> StringUtil.isNotEmpty(item.getSku())).collect(Collectors.toList());
        shipmentDTO.setWalmartItems(filterItems);
        if (CollectionUtils.isEmpty(shipmentDTO.getWalmartItems())) {
            throw new ErpCommonException("明细不能为空或未填写SellerSKu!");
        }

        Account account = accountService.lambdaQuery().eq(Account::getFlag, shipmentDTO.getChannelId()).one();
        if (account == null) {
            throw new ErpCommonException("店铺信息获取失败");
        }
        String prefixFlag = StringUtils.isNotEmpty(account.getStoreName()) ? (account.getStoreName().length() >= 3 ? account.getStoreName().substring(0, 3) : account.getStoreName()) : "WAL";


        //校验sellerKSU是否归属店铺
        WalmartInboundShipmentDetailQueryListDTO queryListDTO = new WalmartInboundShipmentDetailQueryListDTO();
        queryListDTO.setContextId(shipmentDTO.getContextId());
        queryListDTO.setAccountFlag(shipmentDTO.getChannelId());

        List<String> saveSellerSKu = filterItems.stream().map(WalmartInboundShipmentItem::getSku).distinct().collect(Collectors.toList());
        queryListDTO.setSellerSkuQuery(saveSellerSKu);
        List<WalmartInboundShipmentItemPageVO> walmartInboundShipmentItemPageVOS = walmartInboundShipmentItemService.selectGoodsSellerSKuInfoList(queryListDTO);
        if (CollectionUtils.isEmpty(walmartInboundShipmentItemPageVOS)) {
            throw new CustomException("导入SellerSku均不存在店铺中!");
        }

        List<String> dbLsit = walmartInboundShipmentItemPageVOS.stream().map(WalmartInboundShipmentItemPageVO::getSku).distinct().collect(Collectors.toList());

        //未查询出的sellerSku
        List<String> noSellerSKu = saveSellerSKu.stream().filter(item -> !dbLsit.contains(item)).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(noSellerSKu)){
            throw new CustomException("SellerSKu:"+noSellerSKu.stream().distinct().collect(Collectors.joining(","))+" 在店铺中不存在!");
        }

        //保存或更新货件信息   注入代理，异常不回滚
        walmartInboundShipmentProxyService.saveOrUpdateDb(shipmentDTO, prefixFlag);

        if ("CREATED".equalsIgnoreCase(opType)) {  //创建
            if (id != null) {//仅对已暂存数据校验
                this.baseMapper.selectById(shipmentDTO.getId());
                WalmartInboundShipment walmartInboundShipment = this.baseMapper.selectById(shipmentDTO.getId());
                if (!ShipmentProcessStatusEnum.DRAFT.getValue().equals(walmartInboundShipment.getShipmentBusinessStatus())) {
                    throw new ErpCommonException("状态非草稿不可创建货件!");
                }
            }

            //调用创建货件
            List<WalmartShipAddress> walmartShipAddress = this.baseMapper.selectWarehouseAddressById(Arrays.asList(shipmentDTO.getShipWarehouseId()));
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(walmartShipAddress)) {
                throw new ErpCommonException("组织仓地址未配置!");
            }

            WalmartInboundShipmentCreateResponse inboundShipment = this.createInboundShipment(shipmentDTO.getChannelId(), shipmentDTO.getWalmartItems(), shipmentDTO.getInboundOrderId(), walmartShipAddress.get(0));

            if (inboundShipment == null) {
                marWalmartInboundShipmentLogService.saveCreateLog("创建货件失败", ShipmentOperateTypeEnum.CREATED.getType(), shipmentDTO.getId(), false);
                throw new ErpCommonException("创建货件失败，请稍后重试!");
            }
            List<WalmartInboundShipmentCreateResponse.Payload> payload = inboundShipment.getPayload();
            if (CollectionUtils.isEmpty(payload)) {
                log.info("创建货件ResoponseNUll：{}", JSONObject.toJSONString(payload));
                return;
            }
            WalmartInboundShipmentCreateResponse.Payload payloadFlag = payload.get(0);

            //修改状态记录sipmentID
            WalmartInboundShipment walmartInboundShipment = new WalmartInboundShipment();
            walmartInboundShipment.setId(shipmentDTO.getId());
            walmartInboundShipment.setShipmentId(payloadFlag.getShipmentId());
            walmartInboundShipment.setShipmentBusinessStatus(ShipmentProcessStatusEnum.SET_SHIPMENT.getValue());
            walmartInboundShipment.setExceptionExist(Boolean.FALSE); //是否异常
            walmartInboundShipment.settingDefaultUpdate();
            this.updateById(walmartInboundShipment);


            //明细行设置 ShipmentId
            LambdaUpdateWrapper<WalmartInboundShipmentItem> objectLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            objectLambdaUpdateWrapper.set(WalmartInboundShipmentItem::getShipmentId, walmartInboundShipment.getShipmentId());
            objectLambdaUpdateWrapper.eq(WalmartInboundShipmentItem::getChannelId, shipmentDTO.getChannelId());
            objectLambdaUpdateWrapper.eq(WalmartInboundShipmentItem::getInboundOrderId, shipmentDTO.getInboundOrderId());
            walmartInboundShipmentItemService.update(objectLambdaUpdateWrapper);


            //保存地址信息
            WalmartInboundShipmentCreateResponse.ShipToAddress shipToAddress = payloadFlag.getShipToAddress();
            if (shipToAddress != null) {
                WalmartShipAddress walmartShipAddressInfo = new WalmartShipAddress();
                walmartShipAddressInfo.setOrgId(shipmentDTO.getContextId().intValue());
                walmartShipAddressInfo.setChannelId(shipmentDTO.getChannelId());//店铺Flag
                walmartShipAddressInfo.setInboundOrderId(shipmentDTO.getInboundOrderId());//创建货件唯一ID
                walmartShipAddressInfo.setShipmentId(walmartInboundShipment.getShipmentId());//货件ID
                walmartShipAddressInfo.setType(1); //1.发货地址 2.退货地址
                walmartShipAddressInfo.setAddressLine1(shipToAddress.getAddressLine1()); //地址1
                walmartShipAddressInfo.setAddressLine1(shipToAddress.getAddressLine2()); //地址2
                walmartShipAddressInfo.setCity(shipToAddress.getCity()); //城市
                walmartShipAddressInfo.setStateCode(shipToAddress.getStateCode()); //州代码
                walmartShipAddressInfo.setCountryCode(shipToAddress.getCountryCode()); //国家
                walmartShipAddressInfo.setPostalCode(shipToAddress.getPostalCode()); //邮编
                walmartShipAddressMapper.insert(walmartShipAddressInfo);
            }

            //发起同步货件信息
            try {
                WalmartInboundShipmentSyncShipmentDTO syncShipmentDTO = new WalmartInboundShipmentSyncShipmentDTO();
                syncShipmentDTO.setOrganizationId(shipmentDTO.getContextId());
                syncShipmentDTO.setAccount(shipmentDTO.getChannelId());
                syncShipmentDTO.setShipmentIds(Arrays.asList(payloadFlag.getShipmentId()));
                this.syncShipment(syncShipmentDTO);
            } catch (Exception e) {
                log.info("发起同步货件异常:{}", e);
            }

            //记录日志（true正常）
            marWalmartInboundShipmentLogService.saveCreateLog("创建货件成功,ShipmentId:" + walmartInboundShipment.getShipmentId(), ShipmentOperateTypeEnum.CREATED.getType(), shipmentDTO.getId(), true);
        } else if ("TS".equals(opType)) { //暂存
            marWalmartInboundShipmentLogService.saveCreateLog("暂存创建货件信息", ShipmentOperateTypeEnum.ADD.getType(), shipmentDTO.getId(), true);
        }
    }

    /**
     * @param
     * @param shipmentDTO
     * @param prefixFlag
     * @param
     * @description:
     * @author: Moore
     * @date: 2025/5/15 16:46
     * @return: void
     **/
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveOrUpdateDb(WalmartCreateShipmentDTO shipmentDTO, String prefixFlag) {

        //总箱数
        Integer sumCartonNumber = shipmentDTO.getWalmartItems().stream().filter(item -> item.getVendorPackQty() != null).mapToInt(WalmartInboundShipmentItem::getVendorPackQty).sum();

        //总申报数量（货件出库数）
        Integer sumItemQty = shipmentDTO.getWalmartItems().stream().filter(item -> item.getItemQty() != null).mapToInt(WalmartInboundShipmentItem::getItemQty).sum();

        WalmartInboundShipment walmartInboundShipment = new WalmartInboundShipment();
        walmartInboundShipment.setId(shipmentDTO.getId()); //主键ID
        walmartInboundShipment.setOrgId(shipmentDTO.getContextId());//组织id
        walmartInboundShipment.setChannelId(shipmentDTO.getChannelId()); //店铺
        walmartInboundShipment.setShipWarehouseId(shipmentDTO.getShipWarehouseId()); //发货仓库
        walmartInboundShipment.setExpectedDeliveryDate(shipmentDTO.getExpectedDeliveryDate()); //期望送达时间
        walmartInboundShipment.setRemark(shipmentDTO.getRemark()); //备注
        walmartInboundShipment.setCartonNumber(sumCartonNumber); //总箱数
        walmartInboundShipment.setReceivedUnits(sumItemQty); //总申报数（即货件出库数）
        walmartInboundShipment.settingDefaultCreate();
        if (shipmentDTO.getId() == null) { //新增
            walmartInboundShipment.setInboundOrderId(WalmartInboundUtil.createInboundOrderId(prefixFlag)); //生成
            walmartInboundShipment.setShipmentBusinessStatus(ShipmentProcessStatusEnum.DRAFT.getValue()); //草稿
            this.save(walmartInboundShipment);
            shipmentDTO.setInboundOrderId(walmartInboundShipment.getInboundOrderId());//创建api使用
            shipmentDTO.setId(walmartInboundShipment.getId()); //主键

            //保存明细
            List<WalmartInboundShipmentItem> walmartItems = shipmentDTO.getWalmartItems();
            walmartItems.forEach(item -> {
                item.setOrgId(shipmentDTO.getContextId());//组织ID
                item.setInboundOrderId(walmartInboundShipment.getInboundOrderId()); //货件唯一ID
                item.setChannelId(walmartInboundShipment.getChannelId());//店铺
                item.setExpectedDeliveryDate(shipmentDTO.getExpectedDeliveryDate()); //期望送达日期
                item.settingDefaultCreate();
            });
            walmartInboundShipmentItemService.saveBatch(walmartItems);

        } else {
            //更新主表
            walmartInboundShipment.settingDefaultUpdate();
            this.updateById(walmartInboundShipment);

            WalmartInboundShipment walmartInboundShipmentDB = this.getById(shipmentDTO.getId());

            //更新明细
            List<WalmartInboundShipmentItem> walmartItems = shipmentDTO.getWalmartItems();
            walmartItems.forEach(item -> {
                        item.setOrgId(shipmentDTO.getContextId());//组织ID
                        item.setChannelId(shipmentDTO.getChannelId());//店铺
                        item.setInboundOrderId(walmartInboundShipmentDB.getInboundOrderId()); //货件唯一ID
                        item.setExpectedDeliveryDate(shipmentDTO.getExpectedDeliveryDate()); // 期望送达
                        if (item.getId() == null) {
                            item.settingDefaultCreate();
                            walmartInboundShipmentItemService.save(item);
                        } else {
                            item.settingDefaultUpdate();
                            walmartInboundShipmentItemService.updateById(item);
                        }
                    }
            );
        }
    }


    /**
     * @param
     * @param shipmentImportDTOS
     * @description: 导入创建信息
     * @author: Moore
     * @date: 2025/5/20 11:09
     * @return: void
     **/
    @Override
    public String importCreatedImport(List<WalmartInboundShipmentImportDTO> shipmentImportDTOS, Integer contextId) {

        StringBuilder failureMsg = new StringBuilder();

        //过滤每行必填
        List<WalmartInboundShipmentImportDTO> exShipmentList = shipmentImportDTOS.stream().
                filter(item -> item.getNumber() == null || StringUtils.isEmpty(item.getShopName())
                        || StringUtils.isEmpty(item.getSellerSku()) || StringUtils.isEmpty(item.getShipWarehouseCode()) ||
                        StringUtils.isEmpty(item.getExpectedDeliveryDate()) || item.getItemQty() == null || item.getVendorPackQty() == null||item.getInnerPackQty()==null
                        || ("需要".equals(item.getWfsServiceFlag()) && StringUtils.isEmpty(item.getWfsServiceContent()))
                ).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(exShipmentList)) {
            failureMsg.append("<br/>" + "序号、sellersku、店铺、发货仓库、期望送达、申报数量、箱数、每箱产品数量，wfs服务内容在（需要）时必填");
        }
        //去除空格
        shipmentImportDTOS.forEach(item -> trimAllStringFields(item, false));

        //相同序号校验
        Map<Integer, List<WalmartInboundShipmentImportDTO>> numberGroup = shipmentImportDTOS.stream().filter(item -> item.getNumber() != null).collect(Collectors.groupingBy(WalmartInboundShipmentImportDTO::getNumber));
        Set<Integer> invalidNumbers = numberGroup.entrySet().stream()
                .filter(entry -> {
                    List<WalmartInboundShipmentImportDTO> list = entry.getValue();
                    // 获取第一个元素为参照
                    WalmartInboundShipmentImportDTO first = list.get(0);

                    // 判断是否存在任意字段不一致
                    return list.stream().anyMatch(item ->
                            !Objects.equals(item.getShopName(), first.getShopName()) ||
                                    !Objects.equals(item.getShipWarehouseCode(), first.getShipWarehouseCode()) ||
                                    !Objects.equals(item.getExpectedDeliveryDate(), first.getExpectedDeliveryDate())
                    );
                }).map(Map.Entry::getKey) // 提取 number
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(invalidNumbers)) {
            String msg = "<br/>" + "相同序号（归为同一货件）的店铺、发货仓库、期望送达日期必须一致";
            failureMsg.append(msg);
        }

        //SellerSku校验
        List<String> selelrSkus = shipmentImportDTOS.stream().filter(item -> item.getNumber() != null).map(WalmartInboundShipmentImportDTO::getSellerSku).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(selelrSkus)) {
            WalmartInboundShipmentDetailQueryListDTO queryListDTO = new WalmartInboundShipmentDetailQueryListDTO();
            queryListDTO.setContextId(contextId);
            queryListDTO.setSellerSkuQuery(selelrSkus);
            List<WalmartInboundShipmentItemPageVO> walmartInboundShipmentItemPageVOS = walmartInboundShipmentItemService.selectGoodsSellerSKuInfoList(queryListDTO);
            //全部无
            if (CollectionUtils.isEmpty(walmartInboundShipmentItemPageVOS)) {
                String msg = "<br/>" + " SellerSKU不可创建货件,请调整!";
                failureMsg.append(msg);
            } else {
                //部分无
                List<String> noDbSellerSku = selelrSkus.stream().filter(item -> !walmartInboundShipmentItemPageVOS.stream().map(WalmartInboundShipmentItemPageVO::getSku).collect(Collectors.toList()).contains(item)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(noDbSellerSku)) {
                    String msg = "<br/>" + "以下SellerSKU不可创建货件,请调整:" + noDbSellerSku.stream().collect(Collectors.joining(","));
                    failureMsg.append(msg);
                } else {   //全部有，设置sellerSku信息
                    Map<String, WalmartInboundShipmentItemPageVO> sellerSkuMap = walmartInboundShipmentItemPageVOS.stream().collect(Collectors.toMap(WalmartInboundShipmentItemPageVO::getSku, Function.identity(), (a, b) -> a));
                    shipmentImportDTOS.forEach(item -> item.setSellerSkuInfo(sellerSkuMap.get(item.getSellerSku())));
                }
            }
        }

        //WFS服务内容在WFS服务为需要时必填，且必须为LABEL、BAG、TAPE、WRAP种的一个或者多个
        List<String> wfsServiceFlagList = Arrays.asList("LABEL", "BAG", "TAPE", "WRAP");
        List<WalmartInboundShipmentImportDTO> serviveInfo = shipmentImportDTOS.stream().filter(
                item -> "需要".equals(item.getWfsServiceFlag())
                        && (Arrays.stream(item.getWfsServiceContent().split(",")).noneMatch(wfsServiceFlagList::contains)))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(serviveInfo)) {
            String msg = "<br/>" + "WFS服务必须为LABEL、BAG、TAPE、WRAP中的一个或者多个";
            failureMsg.append(msg);
        }


        //店铺系统校验
        List<String> shopNames = shipmentImportDTOS.stream().filter(item -> StringUtils.isNotEmpty(item.getShopName())).map(WalmartInboundShipmentImportDTO::getShopName).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(shopNames)) {
            //判断店铺是否存在,根据店铺名查询所有店铺信息
            LambdaQueryWrapper<Account> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(Account::getId, Account::getTitle, Account::getType, Account::getFlag);
            queryWrapper.in(Account::getTitle, shopNames);
            queryWrapper.eq(Account::getActive, "Y");
            queryWrapper.eq(Account::getOrgId, contextId);
            queryWrapper.eq(Account::getType, AccountSaleChannelEnum.WALMART.getValue());
            List<Account> accounts = accountMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(accounts)) {
                String msg = "<br/>" + "店铺名系统不存在!";
                failureMsg.append(msg);
            } else {
                List<@NotNull String> dbShopName = accounts.stream().map(Account::getTitle).collect(Collectors.toList());
                List<String> noShopNames = shopNames.stream().filter(item -> !dbShopName.contains(item)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(noShopNames)) {
                    String shopNameFlag = noShopNames.stream().collect(Collectors.joining(","));
                    String msg = "<br/>" + "以下店铺名称系统中不存在：" + shopNameFlag;
                    failureMsg.append(msg);
                } else { //店铺全部存在，设置店铺信息
                    Map<@NotNull String, Account> accountMap = accounts.stream().collect(Collectors.toMap(Account::getTitle, Function.identity(), (a, b) -> a));
                    shipmentImportDTOS.forEach(item -> item.setAccount(accountMap.get(item.getShopName())));
                }
            }
        }

        //发货仓校验
        List<String> shipWarehouseCode = shipmentImportDTOS.stream().filter(item -> StringUtils.isNotEmpty(item.getShipWarehouseCode())).map(WalmartInboundShipmentImportDTO::getShipWarehouseCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(shipWarehouseCode)) {
            //发货仓系统校验
            List<WalmartShipmentOrganizationWarehousesVO> organizationWarehousesVOS = this.baseMapper.selectWarehouseAddressByCode(shipWarehouseCode, contextId);
            //全部无
            if (CollectionUtils.isEmpty(organizationWarehousesVOS)) {
                String msg = "<br/>" + "发货仓库存未在组织中维护";
                failureMsg.append(msg);
            } else {
                //部分无
                List<String> noDBorgWareHouses = shipWarehouseCode.stream().filter(item -> !organizationWarehousesVOS.stream().map(WalmartShipmentOrganizationWarehousesVO::getCode).collect(Collectors.toList()).contains(item)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(noDBorgWareHouses)) {
                    String msg = "<br/>" + "以下发货仓,未在组织中维护:" + noDBorgWareHouses.stream().collect(Collectors.joining(","));
                    failureMsg.append(msg);
                } else {
                    //配置组织仓ID
                    Map<String, Integer> codeIdMap = organizationWarehousesVOS.stream().collect(Collectors.toMap(WalmartShipmentOrganizationWarehousesVO::getCode, WalmartShipmentOrganizationWarehousesVO::getId, (a1, a2) -> a1));
                    shipmentImportDTOS.forEach(item -> item.setShipWarehouseId(codeIdMap.get(item.getShipWarehouseCode())));
                    //仓库完全匹配，校验仓库店铺
                    List<Integer> orgIdWarehouseIds = organizationWarehousesVOS.stream().map(WalmartShipmentOrganizationWarehousesVO::getId).collect(Collectors.toList());
                    //地址信息
                    List<WalmartShipAddress> walmartShipAddresses = this.baseMapper.selectWarehouseAddressById(orgIdWarehouseIds);
                    if (CollectionUtils.isEmpty(walmartShipAddresses)) {
                        throw new ErpCommonException("组织仓地址信息未配置!");
                    } else {
                        //部分地址不存在
                        List<String> codeOriList = shipWarehouseCode.stream().filter(item -> !walmartShipAddresses.stream().map(WalmartShipAddress::getWarehouseCode).collect(Collectors.toList()).contains(item)).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(codeOriList)) {
                            String msg = "<br/>" + "以下发货仓,未在地址:" + codeOriList.stream().collect(Collectors.joining(","));
                            failureMsg.append(msg);
                        } else {
                            //设置仓库地址
                            Map<Long, WalmartShipAddress> addressMap = walmartShipAddresses.stream().collect(Collectors.toMap(WalmartShipAddress::getId, Function.identity(), (a1, a2) -> a1));
                            shipmentImportDTOS.forEach(item -> item.setWalmartShipAddress(addressMap.get(Long.valueOf(item.getShipWarehouseId()))));
                        }
                    }
                }
            }
        }

        if (failureMsg.length() > 0) {
            return failureMsg.toString();
        }


        ArrayList<WalmartInboundShipment> errorShipment = new ArrayList<>();

        //多线程
        for (Integer number : numberGroup.keySet()) {
            //单个行信息
            List<WalmartInboundShipmentImportDTO> shipmentDTO = numberGroup.get(number);

            WalmartInboundShipmentImportDTO walmartInboundShipmentImportDTO = shipmentDTO.get(0);
            Account account = walmartInboundShipmentImportDTO.getAccount();


            String prefixFlag = StringUtils.isNotEmpty(account.getStoreName()) ? (account.getStoreName().length() >= 3 ? account.getStoreName().substring(0, 3) : account.getStoreName()) : "WAL";


            Integer sumCartonNumber = shipmentDTO.stream().filter(item -> item.getVendorPackQty() != null).mapToInt(WalmartInboundShipmentImportDTO::getVendorPackQty).sum();
            Integer sumItemQty = shipmentDTO.stream().filter(item -> item.getItemQty() != null).mapToInt(WalmartInboundShipmentImportDTO::getItemQty).sum();

            //主表存储
            WalmartInboundShipment walmartInboundShipment = new WalmartInboundShipment();
            walmartInboundShipment.setOrgId(contextId);//组织id
            walmartInboundShipment.setChannelId(account.getFlag()); //店铺Flag
            walmartInboundShipment.setShipWarehouseId(walmartInboundShipmentImportDTO.getShipWarehouseId()); //发货仓库
            try {
                walmartInboundShipment.setExpectedDeliveryDate(cn.hutool.core.date.DateUtil.parse(walmartInboundShipmentImportDTO.getExpectedDeliveryDate(), DatePattern.NORM_DATETIME_PATTERN)); //期望送达时间
            } catch (Exception e) {
                walmartInboundShipment.setExpectedDeliveryDate(cn.hutool.core.date.DateUtil.parse(walmartInboundShipmentImportDTO.getExpectedDeliveryDate(), DatePattern.NORM_DATE_PATTERN)); //期望送达时间
            }
            walmartInboundShipment.setCartonNumber(sumCartonNumber); //总箱数
            walmartInboundShipment.setReceivedUnits(sumItemQty); //总申报数（即货件出库数）
            walmartInboundShipment.settingDefaultCreate();
            walmartInboundShipment.setInboundOrderId(WalmartInboundUtil.createInboundOrderId(prefixFlag)); //生成
            walmartInboundShipment.setShipmentBusinessStatus(ShipmentProcessStatusEnum.DRAFT.getValue()); //草稿
            this.save(walmartInboundShipment);

            //明细存储
            List<WalmartInboundShipmentItem> itemsList = Lists.newArrayList(shipmentDTO).stream().map(item -> {
                WalmartInboundShipmentItemPageVO sellerSkuInfo = item.getSellerSkuInfo();
                WalmartInboundShipmentItem walmartInboundShipmentItem = new WalmartInboundShipmentItem();
                walmartInboundShipmentItem.setOrgId(contextId);//组织ID
                walmartInboundShipmentItem.setInboundOrderId(walmartInboundShipment.getInboundOrderId()); //货件唯一ID
                walmartInboundShipmentItem.setChannelId(walmartInboundShipment.getChannelId());//店铺
                walmartInboundShipmentItem.setImageUrl(sellerSkuInfo.getImageUrl());//图片
                walmartInboundShipmentItem.setSku(sellerSkuInfo.getSku()); //SellerSKu
                walmartInboundShipmentItem.setGoodsId(sellerSkuInfo.getGoodsId());//商品ID
                walmartInboundShipmentItem.setGtin(sellerSkuInfo.getGtin());//GTIN
                walmartInboundShipmentItem.setErpSku(sellerSkuInfo.getErpSku()); //ERPSKU
                walmartInboundShipmentItem.setGoodsType("GTIN");//商品类型，暂时一种GTIN
                walmartInboundShipmentItem.setItemDesc(sellerSkuInfo.getItemDesc()); //商品描述
                walmartInboundShipmentItem.setItemQty(item.getItemQty()); //申报数量
                walmartInboundShipmentItem.setVendorPackQty(item.getVendorPackQty());//箱数
                walmartInboundShipmentItem.setInnerPackQty(item.getInnerPackQty()); //每箱个数
                walmartInboundShipmentItem.setWfsService(item.getWfsServiceContent()); //wfs服务类型
                walmartInboundShipmentItem.setExpectedDeliveryDate(walmartInboundShipment.getExpectedDeliveryDate()); //期望送达日期
                walmartInboundShipmentItem.settingDefaultCreate();
                return walmartInboundShipmentItem;
            }).collect(Collectors.toList());
            walmartInboundShipmentItemService.saveBatch(itemsList);

            marWalmartInboundShipmentLogService.saveCreateLog("导入货件信息", ShipmentOperateTypeEnum.ADD.getType(), walmartInboundShipment.getId(), Boolean.TRUE);


            //创建货件
            WalmartInboundShipmentCreateResponse inboundShipment = this.createInboundShipment(account.getFlag(), itemsList, walmartInboundShipment.getInboundOrderId(), shipmentDTO.get(0).getWalmartShipAddress());
            if (inboundShipment == null) {
                errorShipment.add(walmartInboundShipment);
                marWalmartInboundShipmentLogService.saveCreateLog("创建货件失败", ShipmentOperateTypeEnum.CREATED.getType(), walmartInboundShipment.getId(), Boolean.FALSE);
            } else {
                List<WalmartInboundShipmentCreateResponse.Payload> payload = inboundShipment.getPayload();
                if (CollectionUtils.isEmpty(payload)) {
                    log.info("创建货件ResoponseNUll：{}", JSONObject.toJSONString(payload));
                    continue;
                }
                WalmartInboundShipmentCreateResponse.Payload payloadFlag = payload.get(0);

                //修改状态记录sipmentID
                WalmartInboundShipment walmartInboundShipmentUpdate = new WalmartInboundShipment();
                walmartInboundShipmentUpdate.setId(walmartInboundShipment.getId());
                walmartInboundShipmentUpdate.setShipmentId(payloadFlag.getShipmentId());
                walmartInboundShipmentUpdate.setShipmentBusinessStatus(ShipmentProcessStatusEnum.SET_SHIPMENT.getValue());
                walmartInboundShipmentUpdate.setExceptionExist(Boolean.FALSE); //是否异常
                walmartInboundShipmentUpdate.settingDefaultUpdate(); //更新信息
                this.updateById(walmartInboundShipmentUpdate);


                //明细行设置 ShipmentId
                LambdaUpdateWrapper<WalmartInboundShipmentItem> objectLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                objectLambdaUpdateWrapper.set(WalmartInboundShipmentItem::getShipmentId, walmartInboundShipmentUpdate.getShipmentId());
                objectLambdaUpdateWrapper.eq(WalmartInboundShipmentItem::getChannelId, account.getFlag());
                objectLambdaUpdateWrapper.eq(WalmartInboundShipmentItem::getInboundOrderId, walmartInboundShipment.getInboundOrderId());
                walmartInboundShipmentItemService.update(objectLambdaUpdateWrapper);


                //保存地址信息
                WalmartInboundShipmentCreateResponse.ShipToAddress shipToAddress = payloadFlag.getShipToAddress();
                if (shipToAddress != null) {
                    WalmartShipAddress walmartShipAddressInfo = new WalmartShipAddress();
                    walmartShipAddressInfo.setOrgId(contextId);
                    walmartShipAddressInfo.setChannelId(account.getFlag());//店铺
                    walmartShipAddressInfo.setInboundOrderId(walmartInboundShipment.getInboundOrderId());//创建货件唯一ID
                    walmartShipAddressInfo.setShipmentId(walmartInboundShipmentUpdate.getShipmentId());//货件ID
                    walmartShipAddressInfo.setType(1); //1.发货地址 2.退货地址
                    walmartShipAddressInfo.setAddressLine1(shipToAddress.getAddressLine1()); //地址1
                    walmartShipAddressInfo.setAddressLine2(shipToAddress.getAddressLine2()); //地址2
                    walmartShipAddressInfo.setCity(shipToAddress.getCity()); //城市
                    walmartShipAddressInfo.setStateCode(shipToAddress.getStateCode()); //州代码
                    walmartShipAddressInfo.setCountryCode(shipToAddress.getCountryCode()); //国家
                    walmartShipAddressInfo.setPostalCode(shipToAddress.getPostalCode()); //邮编
                    walmartShipAddressMapper.insert(walmartShipAddressInfo);
                }

                //发起同步货件信息
                try {
                    WalmartInboundShipmentSyncShipmentDTO syncShipmentDTO = new WalmartInboundShipmentSyncShipmentDTO();
                    syncShipmentDTO.setOrganizationId(contextId);
                    syncShipmentDTO.setAccount(account.getFlag()); //店铺flag
                    syncShipmentDTO.setShipmentIds(Arrays.asList(payloadFlag.getShipmentId()));
                    this.syncShipment(syncShipmentDTO);
                } catch (Exception e) {
                    log.info("发起同步货件异常:{}", e);
                }

                //记录日志
                marWalmartInboundShipmentLogService.saveCreateLog("创建货件成功,ShipmentId:" + walmartInboundShipment.getShipmentId(), ShipmentOperateTypeEnum.CREATED.getType(), walmartInboundShipment.getId(), Boolean.TRUE);
            }
        }

        if (CollectionUtils.isNotEmpty(errorShipment)) {
            String msg = "<br/>" + "以下入库订单编号,创建货件失败:" + errorShipment.stream().map(WalmartInboundShipment::getInboundOrderId).collect(Collectors.joining(","));
            return msg;
        }
        return null;
    }



    public static void trimAllStringFields(Object obj, boolean removeInnerSpaces) {
        if (obj == null) return;
        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            if (field.getType() == String.class) {
                field.setAccessible(true);
                try {
                    String value = (String) field.get(obj);
                    if (value != null) {
                        if (removeInnerSpaces) {
                            // 去除所有空白字符（包括中间）
                            field.set(obj, value.replaceAll("\\s+", ""));
                        } else {
                            // 只去除前后空格
                            field.set(obj, value.trim());
                        }
                    }
                } catch (IllegalAccessException ignored) {}
            }
        }
    }


    /**
     * 提交草稿货件信息
     *
     * @param ids
     * @param contextId
     */
    @Override
    public List<WalmartInboundShipment> subDraftShipment(List<Long> ids, Integer contextId) {

        List<WalmartInboundShipment> walmartInboundShipments = this.lambdaQuery().eq(WalmartInboundShipment::getShipmentBusinessStatus, ShipmentProcessStatusEnum.DRAFT.getValue())
                .in(WalmartInboundShipment::getId, ids).list();
        if (CollectionUtils.isEmpty(walmartInboundShipments)) {
            throw new ErpCommonException("无符合条件货件数据!");
        }

        List<Integer> shipIds = walmartInboundShipments.stream().map(WalmartInboundShipment::getShipWarehouseId).distinct().collect(Collectors.toList());


        //地址信息
        List<WalmartShipAddress> walmartShipAddresses = this.baseMapper.selectWarehouseAddressById(shipIds);
        if (CollectionUtils.isEmpty(walmartShipAddresses)) {
            throw new ErpCommonException("组织仓地址信息未配置!");
        }


        // 目的仓 在查询中地址中不存在的数据
        List<Integer> notInList = shipIds.stream()
                .filter(id -> walmartShipAddresses.stream().map(WalmartShipAddress::getId).noneMatch(addrId -> addrId.equals(id.longValue())))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(notInList)) {
            List<WalmartInboundShipment> matchedShipments = walmartInboundShipments.stream()
                    .filter(shipment -> notInList.contains(shipment.getId()))
                    .collect(Collectors.toList());

            throw new ErpCommonException("以下入库订单编号信息，未设置组织仓地址：" + matchedShipments.stream().map(WalmartInboundShipment::getInboundOrderId).distinct().collect(Collectors.joining(",")));
        }


        Map<Long, WalmartShipAddress> walmartShipAddress = walmartShipAddresses.stream().collect(Collectors.toMap(WalmartShipAddress::getId, Function.identity(), (a, b) -> a));


        //查询明细
        List<WalmartInboundShipmentItem> items = walmartInboundShipmentItemService.lambdaQuery()
                .in(WalmartInboundShipmentItem::getChannelId, walmartInboundShipments.stream().map(WalmartInboundShipment::getChannelId).distinct().collect(Collectors.toList()))
                .in(WalmartInboundShipmentItem::getInboundOrderId, walmartInboundShipments.stream().map(WalmartInboundShipment::getInboundOrderId).distinct().collect(Collectors.toList())).list();
        //分组(存储无shipmentID，且在新版本InboundOrderId既可确定唯一)
        Map<String, List<WalmartInboundShipmentItem>> groupedMap = items.stream()
                .collect(Collectors.groupingBy(item -> item.getChannelId() + "_" + item.getInboundOrderId()));


        ArrayList<WalmartInboundShipment> errorShipment = new ArrayList<>();

        //大于4条，开启多任务
        int count = walmartInboundShipments.size() / 4;
        List<List<WalmartInboundShipment>> lists = com.bizark.erp.common.util.ListUtils.splitList(walmartInboundShipments, count == 0 ? 1 : count);
        List<CompletableFuture<Object>> claimFuture = lists.stream().map(batch -> CompletableFuture.supplyAsync(() -> {
            batch.forEach(item -> {
                if (walmartShipAddress.get(Long.valueOf(item.getShipWarehouseId())) != null) {
                    WalmartInboundShipmentCreateResponse inboundShipment = this.createInboundShipment(item.getChannelId(), groupedMap.get(item.getChannelId() + "_" + item.getInboundOrderId()), item.getInboundOrderId(), walmartShipAddress.get(Long.valueOf(item.getShipWarehouseId())));
                    if (inboundShipment == null) {
                        errorShipment.add(item);
                        marWalmartInboundShipmentLogService.saveCreateLog("创建货件失败", ShipmentOperateTypeEnum.CREATED.getType(), item.getId(), false);
                    } else {
                        List<WalmartInboundShipmentCreateResponse.Payload> payload = inboundShipment.getPayload();
                        if (CollectionUtils.isEmpty(payload)) {
                            log.info("创建货件ResoponseNUll：{}", JSONObject.toJSONString(payload));
                            return;
                        }
                        WalmartInboundShipmentCreateResponse.Payload payloadFlag = payload.get(0);

                        //修改状态记录sipmentID
                        WalmartInboundShipment walmartInboundShipment = new WalmartInboundShipment();
                        walmartInboundShipment.setId(item.getId());
                        walmartInboundShipment.setShipmentId(payloadFlag.getShipmentId());
                        walmartInboundShipment.setShipmentBusinessStatus(ShipmentProcessStatusEnum.SET_SHIPMENT.getValue());
                        walmartInboundShipment.setExceptionExist(Boolean.FALSE); //是否异常
                        walmartInboundShipment.settingDefaultUpdate();
                        this.updateById(walmartInboundShipment);


                        //明细行设置 ShipmentId
                        LambdaUpdateWrapper<WalmartInboundShipmentItem> objectLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                        objectLambdaUpdateWrapper.set(WalmartInboundShipmentItem::getShipmentId, walmartInboundShipment.getShipmentId());
                        objectLambdaUpdateWrapper.eq(WalmartInboundShipmentItem::getChannelId, item.getChannelId());
                        objectLambdaUpdateWrapper.eq(WalmartInboundShipmentItem::getInboundOrderId, item.getInboundOrderId());
                        walmartInboundShipmentItemService.update(objectLambdaUpdateWrapper);


                        //保存地址信息
                        WalmartInboundShipmentCreateResponse.ShipToAddress shipToAddress = payloadFlag.getShipToAddress();
                        if (shipToAddress != null) {
                            WalmartShipAddress walmartShipAddressInfo = new WalmartShipAddress();
                            walmartShipAddressInfo.setOrgId(item.getOrgId());
                            walmartShipAddressInfo.setChannelId(item.getChannelId());//店铺
                            walmartShipAddressInfo.setInboundOrderId(item.getInboundOrderId());//创建货件唯一ID
                            walmartShipAddressInfo.setShipmentId(walmartInboundShipment.getShipmentId());//货件ID
                            walmartShipAddressInfo.setType(1); //1.发货地址 2.退货地址
                            walmartShipAddressInfo.setAddressLine1(shipToAddress.getAddressLine1()); //地址1
                            walmartShipAddressInfo.setAddressLine2(shipToAddress.getAddressLine2()); //地址2
                            walmartShipAddressInfo.setCity(shipToAddress.getCity()); //城市
                            walmartShipAddressInfo.setStateCode(shipToAddress.getStateCode()); //州代码
                            walmartShipAddressInfo.setCountryCode(shipToAddress.getCountryCode()); //国家
                            walmartShipAddressInfo.setPostalCode(shipToAddress.getPostalCode()); //邮编
                            walmartShipAddressMapper.insert(walmartShipAddressInfo);
                        }
                        //记录日志
                        marWalmartInboundShipmentLogService.saveCreateLog("创建货件成功,ShipmentId:" + walmartInboundShipment.getShipmentId(), ShipmentOperateTypeEnum.CREATED.getType(), item.getId(), Boolean.TRUE);
                    }
                }
            });
            return null;
        }, threadPoolTaskExecutor)).collect(Collectors.toList());

        CompletableFuture.allOf(claimFuture.toArray(new CompletableFuture<?>[0])).join();

        return errorShipment;
    }


    /**
     * 创建货件详情信息查询
     *
     * @param id
     * @return
     */
    @Override
    public WalmartCreateShipmentDTO getInboundShipmentCreatedDetail(Long id) {
        WalmartCreateShipmentDTO walmartCreateShipmentDTO = new WalmartCreateShipmentDTO();
        WalmartInboundShipment walmartInboundShipments = this.getById(id);
        if (walmartInboundShipments == null) {
            return walmartCreateShipmentDTO;
        }
        BeanUtils.copyProperties(walmartInboundShipments, walmartCreateShipmentDTO);
        //查询明细
        List<WalmartInboundShipmentItem> items = walmartInboundShipmentItemService.lambdaQuery()
                .eq(WalmartInboundShipmentItem::getChannelId, walmartInboundShipments.getChannelId())
                .eq(WalmartInboundShipmentItem::getInboundOrderId, walmartInboundShipments.getInboundOrderId()).list();
        walmartCreateShipmentDTO.setWalmartItems(items);
        return walmartCreateShipmentDTO;
    }

    /**
     * @Description:获取货件详情
     * @Author: wly
     * @Date: 2025-05-15 16:15
     * @Params: [id]
     * @Return: com.bizark.op.api.entity.op.walmart.dto.WalmartInboundShipmentDetailDTO
     **/
    @Override
    public WalmartInboundShipmentDetailDTO getInboundShipmentDetail(Long id) {

        WalmartInboundShipment inboundShipment = this.getById(id);
        Account account = accountService.lambdaQuery().eq(Account::getFlag, inboundShipment.getChannelId()).eq(Account::getActive, "Y").last("limit 1").one();
        WalmartShipAddress shipAddress = walmartShipAddressService.getOne(Wrappers.<WalmartShipAddress>lambdaQuery()
                .eq(WalmartShipAddress::getChannelId, inboundShipment.getChannelId())
                .eq(WalmartShipAddress::getInboundOrderId, inboundShipment.getInboundOrderId())
                .eq(StringUtils.isNotEmpty(inboundShipment.getShipmentId()), WalmartShipAddress::getShipmentId, inboundShipment.getShipmentId())
                .eq(WalmartShipAddress::getType, 1)
                .eq(WalmartShipAddress::getOrgId, inboundShipment.getOrgId())
                .last("limit 1"));
        List<WalmartInboundShipmentItem> shipmentItemList = walmartInboundShipmentItemService.list(new LambdaQueryWrapper<WalmartInboundShipmentItem>()
                .eq(WalmartInboundShipmentItem::getChannelId, inboundShipment.getChannelId())
                .eq(WalmartInboundShipmentItem::getInboundOrderId, inboundShipment.getInboundOrderId())
                .eq(StringUtils.isNotEmpty(inboundShipment.getShipmentId()), WalmartInboundShipmentItem::getShipmentId, inboundShipment.getShipmentId())
                .eq(WalmartInboundShipmentItem::getOrgId, inboundShipment.getOrgId()))
                ;
        List<MarWalmartInboundPack> inboundPackList = marWalmartInboundPackService.lambdaQuery()
                .eq(StringUtils.isNotEmpty(inboundShipment.getShipmentId()), MarWalmartInboundPack::getShipmentId, inboundShipment.getShipmentId())
                .eq(MarWalmartInboundPack::getInboundOrderId, inboundShipment.getInboundOrderId())
                .list();
        WalmartInboundShipmentDetailDTO dto = new WalmartInboundShipmentDetailDTO();
        BeanUtils.copyProperties(inboundShipment, dto);
        if (StringUtils.isNotEmpty(dto.getInboundOrderId())) {
            if (dto.getInboundOrderId().contains("US")) {
                dto.setShipmentType("International");
            } else {
                dto.setShipmentType("Domestic");
            }
        }
        dto.setContextId(inboundShipment.getOrgId());
        dto.setShopId(account.getId().longValue());
        dto.setShopName(account.getTitle());
//        dto.setShipWarehouseCode();
        if (shipAddress != null) {
            dto.setLogisticsCenterCode(shipAddress.getFcName());
            dto.setShipToAddress(StrUtil.blankToDefault(shipAddress.getAddressLine1(), "") + ", "
                    + StrUtil.blankToDefault(shipAddress.getAddressLine2(), "") + ", "
                    + StrUtil.blankToDefault(shipAddress.getCity(), "") + ", "
                    + StrUtil.blankToDefault(shipAddress.getStateCode(), "") + ", "
                    + StrUtil.blankToDefault(shipAddress.getPostalCode(), "") + ", "
                    + StrUtil.blankToDefault(shipAddress.getCountryCode(), ""));
        }


        dto.setGoodsInfoList(shipmentItemList);
        dto.setPackageInfoList(inboundPackList);

        if (CollectionUtil.isNotEmpty(inboundPackList)) {
            dto.setStackablePalletNumber(inboundPackList.stream().filter(t -> Boolean.TRUE.equals(t.getStackedFlag()) && t.getPalletNum() !=null).mapToInt(t -> t.getPalletNum()).sum());
            dto.setUnStackablePalletNumber(inboundPackList.stream().filter(t -> Boolean.FALSE.equals(t.getStackedFlag()) && t.getPalletNum() !=null).mapToInt(t -> t.getPalletNum()).sum());
        }
        List<WalmartInboundShipmentDetailAttachInfo> detailAttachmentInfo = this.getDetailAttachmentInfo(id);
        dto.setAttachInfoList(detailAttachmentInfo);
        return dto;
    }

    /**
     * @Description:设置货件信息
     * @Author: wly
     * @Date: 2025-05-15 16:17
     * @Params: [dto, contextId, thisUser]
     * @Return: void
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    @MarWalmartShipmentVerify
    public JSONObject setShipmentInfo(WalmartInboundShipmentDetailDTO dto, AuthUserDetails thisUser) {

        JSONObject result = null;
        WalmartInboundShipment byId = this.getById(dto.getId());
        List<WalmartInboundShipmentItem> shipmentItemList = walmartInboundShipmentItemService.list(new LambdaQueryWrapper<WalmartInboundShipmentItem>()
                .eq(WalmartInboundShipmentItem::getChannelId, byId.getChannelId())
                .eq(WalmartInboundShipmentItem::getInboundOrderId, byId.getInboundOrderId())
                .eq(WalmartInboundShipmentItem::getShipmentId, byId.getShipmentId()));
        List<MarWalmartInboundPack> packList = marWalmartInboundPackService.list(new LambdaQueryWrapper<MarWalmartInboundPack>()
                .eq(MarWalmartInboundPack::getShipmentId, byId.getShipmentId())
                .eq(MarWalmartInboundPack::getInboundOrderId, byId.getInboundOrderId()));
        String originalStatus = byId.getShipmentBusinessStatus();
        Boolean originalExceptionExist = byId.getExceptionExist();
        this.lambdaUpdate().eq(WalmartInboundShipment::getId, dto.getId())
                .set(WalmartInboundShipment::getExceptionExist, false).update();
        //1.查询货件后台状态是否为PENDING_SHIPMENT_DETAILS
        String backShipmentStatus = byId.getShipmentStatus();
        LambdaUpdateChainWrapper<WalmartInboundShipment> updateChainWrapper = this.lambdaUpdate().eq(WalmartInboundShipment::getId, dto.getId())
                .set(WalmartInboundShipment::getShipWarehouseId, dto.getShipWarehouseId())
                .set(WalmartInboundShipment::getShipmentCarrier, dto.getShipmentCarrier())
                .set(WalmartInboundShipment::getShippingType, dto.getShippingType())
                .set(WalmartInboundShipment::getExpectedClaimGoodsDate, dto.getExpectedClaimGoodsDate())
                .set(dto.getSingleSellerSkuPackQty() != null, WalmartInboundShipment::getSingleSellerSkuPackQty, dto.getSingleSellerSkuPackQty())
                .set(dto.getMixtureSellerSkuPackQty() != null, WalmartInboundShipment::getMixtureSellerSkuPackQty, dto.getMixtureSellerSkuPackQty())
                .set(StringUtils.isNotEmpty(dto.getFreightLevel()), WalmartInboundShipment::getFreightLevel, dto.getFreightLevel())
                .set(dto.getDeclarationPrice() != null, WalmartInboundShipment::getDeclarationPrice, dto.getDeclarationPrice())
                .set(dto.getPalletNumber() != null, WalmartInboundShipment::getPalletNumber, dto.getPalletNumber())
                .set(WalmartInboundShipment::getUpdatedAt, DateUtils.getNowDate())
                .set(WalmartInboundShipment::getUpdatedBy, thisUser.getId())
                .set(WalmartInboundShipment::getUpdatedName, thisUser.getName())
                .set(StringUtils.isNotEmpty(dto.getCarrierName()), WalmartInboundShipment::getCarrierName, dto.getCarrierName());

        //设置托盘数和箱数
        if (CollectionUtil.isNotEmpty(dto.getGoodsInfoList())) {
            int totalVendorPackQty = dto.getGoodsInfoList().stream().filter(t -> t.getVendorPackQty() != null).mapToInt(t -> t.getVendorPackQty()).sum();
            updateChainWrapper.set(WalmartInboundShipment::getCartonNumber, totalVendorPackQty);
        }
        if (CollectionUtil.isNotEmpty(dto.getPackageInfoList())) {
            int totalPalletNum = dto.getPackageInfoList().stream().filter(t -> t.getPalletNum() != null).mapToInt(t -> t.getPalletNum()).sum();
            updateChainWrapper.set(WalmartInboundShipment::getPalletNumber, totalPalletNum);
        }

        if (!"PENDING_SHIPMENT_DETAILS".equalsIgnoreCase(backShipmentStatus)) {
            //有一个非已取消的调拨计划单为已发货，否则为待发货
            updateChainWrapper.set(WalmartInboundShipment::getShipmentBusinessStatus, getBusinessStatusByShipmentId(dto.getShipmentId()));
        } else {
            //1.2如果是pending shipment details
            //1.2.1 自约承运
            if (!dto.getShipmentCarrier().equals(1)) {
                if (!rpaService(byId, updateChainWrapper)) {
                    updateChainWrapper.set(WalmartInboundShipment::getShipmentBusinessStatus, originalStatus)
                            .set(WalmartInboundShipment::getExceptionExist, originalExceptionExist);
                    result = JSONObject.parseObject(JSONObject.toJSONString(byId), JSONObject.class);
                }
            } else {
                //1.2.2 平台承运
                //******* 货运类型为small parcel
                if (dto.getShippingType().equalsIgnoreCase(ShipmentTypeEnum.SMALL_PARCEL.getValue())) {
                    //TODO  暂不开发
                    //走API接 https://developer.walmart.com/usmarketplace/reference/createcarrierratequotes）
                    //调用失败则直接报错不关闭弹窗。
                    //调用成功则更新状态为待确认运费，更新运费剩余确认时间为 提交成功北京时间+24小时减去当前北京时间
                } else {
                    //******* 货运类型为非small parcel
                    if (!rpaService(byId, updateChainWrapper)) {
                        updateChainWrapper.set(WalmartInboundShipment::getShipmentBusinessStatus, originalStatus)
                                .set(WalmartInboundShipment::getExceptionExist, originalExceptionExist);
                        result = JSONObject.parseObject(JSONObject.toJSONString(byId), JSONObject.class);
                    }
                }
            }

        }

        updateChainWrapper.update();
        walmartInboundShipmentItemService.saveOrUpdateBatch(dto.getGoodsInfoList());
        marWalmartInboundPackService.saveOrUpdateBatch(dto.getPackageInfoList());
        //记录日志
        MarWalmartInboundShipmentLog shipmentLog = new MarWalmartInboundShipmentLog();
        shipmentLog.setOrgId(dto.getContextId().longValue());
        shipmentLog.setShipmentId(dto.getId());
        //设置货件进入
        if (StringUtils.isEmpty(dto.getLogContent())) {
            shipmentLog.setOperateContent("设置货件信息");
            shipmentLog.setOperateType(ShipmentOperateTypeEnum.SET_SHIPMENT.getType());
            if (result == null) {
                String detailContent = changeContent(byId, shipmentItemList, packList, dto);
                if (StringUtils.isNotEmpty(detailContent)) {
                    shipmentLog.setOperateContent(shipmentLog.getOperateContent() + "，详细变更内容：" + detailContent);
                }
            } else {
                //设置货件接口调用失败，不记录详细变更内容
                shipmentLog.setOperateContent(shipmentLog.getOperateContent() + "接口调用失败");
            }
            this.setShipmentPullPalletLabelAndCartonLabel(byId, dto);

        } else {
            //异常重置进入
            shipmentLog.setOperateContent(dto.getLogContent());
            shipmentLog.setOperateType(ShipmentOperateTypeEnum.EXCEPTION_RESET.getType());
            if (result != null) {
                shipmentLog.setOperateContent(shipmentLog.getOperateContent() + "接口调用失败");
            }
        }
        shipmentLog.settingDefaultCreate();
        shipmentLog.settingDefaultUpdate();
        marWalmartInboundShipmentLogService.save(shipmentLog);

        return result;
    }


    /**
     * 设置货件托盘数箱数是否变更
     * @param byId
     * @param dto
     */
    public void setShipmentPullPalletLabelAndCartonLabel(WalmartInboundShipment byId, WalmartInboundShipmentDetailDTO dto) {

        boolean needUpdate = false;
        LambdaUpdateChainWrapper<WalmartInboundShipment> updateWrapper = this.lambdaUpdate().eq(WalmartInboundShipment::getId, byId.getId());
        LambdaQueryWrapper<WalmartShipmentAttach> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WalmartShipmentAttach::getShipmentId, byId.getShipmentId())
                .in(WalmartShipmentAttach::getType, Arrays.asList(MarWalmartTagType.PALLET.getType(), MarWalmartTagType.CARTON.getType()));
        List<WalmartShipmentAttach> list = walmartShipmentAttachService.list(wrapper);
        if (CollectionUtil.isEmpty(list)) {
            updateWrapper.set(WalmartInboundShipment::getPalletTagIsChanged, true)
                    .set(WalmartInboundShipment::getCartonTagIsChanged, true);
                    needUpdate = true;
        } else {
            if (list.stream().noneMatch(t -> MarWalmartTagType.PALLET.getType().equals(t.getType()))) {
                updateWrapper.set(WalmartInboundShipment::getPalletTagIsChanged, true);
                needUpdate = true;
            }else {
                if (CollectionUtil.isNotEmpty(dto.getPackageInfoList()) && dto.getPackageInfoList().stream().anyMatch(t -> t.getPalletNum() != null)) {
                    int sum = dto.getPackageInfoList().stream().mapToInt(t -> t.getPalletNum()).sum();
                    if (byId.getPalletNumber() == null || byId.getPalletNumber().intValue() != sum) {
                        updateWrapper.set(WalmartInboundShipment::getPalletTagIsChanged, true);
                        needUpdate = true;
                    }
                }
            }
            if (list.stream().noneMatch(t -> MarWalmartTagType.CARTON.getType().equals(t.getType()))) {
                updateWrapper.set(WalmartInboundShipment::getCartonTagIsChanged, true);
                needUpdate = true;
            }else {
                if (CollectionUtil.isNotEmpty(dto.getGoodsInfoList()) && dto.getGoodsInfoList().stream().anyMatch(t -> t.getVendorPackQty() != null)) {
                    int sum = dto.getGoodsInfoList().stream().mapToInt(t -> t.getVendorPackQty()).sum();
                    if (byId.getCartonNumber() == null || byId.getCartonNumber().intValue() != sum) {
                        updateWrapper.set(WalmartInboundShipment::getCartonTagIsChanged, true);
                        needUpdate = true;
                    }
                }
            }
        }
        if (needUpdate) {
            updateWrapper.update();
        }
    }
    private String changeContent(WalmartInboundShipment originalShipment, List<WalmartInboundShipmentItem> shipmentItemList, List<MarWalmartInboundPack> packList, WalmartInboundShipmentDetailDTO dto) {
        StringBuilder sb = new StringBuilder();
        try {

            Map<String, String> codeIdMap = new HashMap<>();
            if (originalShipment.getShipWarehouseId() != null) {
                List<WalmartShipmentOrganizationWarehousesVO> codeById = walmartInboundShipmentMapper.getOrganizationWarehousesCodeById(Arrays.asList(originalShipment.getShipWarehouseId()));
                if (CollectionUtil.isNotEmpty(codeById)) {
                    codeIdMap.put(originalShipment.getShipWarehouseId().toString(), codeById.get(0).getCode());
                }
            }
            if (dto.getShipWarehouseId() != null) {
                List<WalmartShipmentOrganizationWarehousesVO> codeById = walmartInboundShipmentMapper.getOrganizationWarehousesCodeById(Arrays.asList(dto.getShipWarehouseId()));
                if (CollectionUtil.isNotEmpty(codeById)) {
                    codeIdMap.put(dto.getShipWarehouseId().toString(), codeById.get(0).getCode());
                }
            }
            recordChange(sb, originalShipment.getShipWarehouseId() == null ? "" : originalShipment.getShipWarehouseId().toString(), dto.getShipWarehouseId() == null ? "" : dto.getShipWarehouseId().toString(), "发货仓库", codeIdMap);
            for (WalmartInboundShipmentItem walmartInboundShipmentItem : shipmentItemList) {
                WalmartInboundShipmentItem newItem = dto.getGoodsInfoList().stream().filter(t -> walmartInboundShipmentItem.getSku().equals(t.getSku())).findFirst().orElse(null);
                recordChange(sb, walmartInboundShipmentItem.getItemQty() == null ? "" : walmartInboundShipmentItem.getItemQty().toString(), newItem == null ? "" : newItem.getItemQty().toString(), walmartInboundShipmentItem.getErpSku() + " 申报数量", null);
            }
            HashMap<String, String> shipmentCarrierMap = new HashMap<>();
            shipmentCarrierMap.put("1", "平台承运");
            shipmentCarrierMap.put("2", "自约承运");
            recordChange(sb, originalShipment.getShipmentCarrier() == null ? "" : originalShipment.getShipmentCarrier().toString(), dto.getShipmentCarrier() == null ? "" : dto.getShipmentCarrier().toString(), "承运方", shipmentCarrierMap);

            recordChange(sb, originalShipment.getShippingType(), dto.getShippingType(), "货运类型", null);
            recordChange(sb, originalShipment.getExpectedClaimGoodsDate() == null ? "" : DateUtil.convertDateToString(originalShipment.getExpectedClaimGoodsDate(), DateUtils.DEFAULT_TIME_FORMAT), dto.getExpectedClaimGoodsDate() == null ? "" : DateUtil.convertDateToString(dto.getExpectedClaimGoodsDate(), DateUtils.DEFAULT_TIME_FORMAT), "期望提货日期", null);
            recordChange(sb, originalShipment.getSingleSellerSkuPackQty() == null ? "" : originalShipment.getSingleSellerSkuPackQty().toString(), dto.getSingleSellerSkuPackQty() == null ? "" : dto.getSingleSellerSkuPackQty().toString(), "单一sellerSKU包装箱数量", null);
            recordChange(sb, originalShipment.getMixtureSellerSkuPackQty() == null ? "" : originalShipment.getMixtureSellerSkuPackQty().toString(), dto.getMixtureSellerSkuPackQty() == null ? "" : dto.getMixtureSellerSkuPackQty().toString(), "混合sellerSKU包装箱数量", null);
            recordChange(sb, originalShipment.getFreightLevel(), dto.getFreightLevel(), "货运等级", null);
            recordChange(sb, originalShipment.getDeclarationPrice() == null ? "" : originalShipment.getDeclarationPrice().setScale(4).toString(), dto.getDeclarationPrice() == null ? "" : dto.getDeclarationPrice().setScale(4).toString(), "申报价值", null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sb.toString();
    }

    private void recordChange(StringBuilder sb, String original, String current, String fieldName, Map<String, String> map) {
        if (StringUtils.isNotEmpty(original) || StringUtils.isNotEmpty(current)) {
            if (!current.equals(original)) {
                if (CollectionUtil.isEmpty(map)) {
                    sb.append(fieldName).append(":[").append(original == null ? "空" : original).append("-->").append(current).append("]");
                } else {
                    sb.append(fieldName).append(":[").append(original == null ? "空" : map.get(original)).append("-->").append(map.get(current)).append("]");
                }
            }
        }
    }

    private String getBusinessStatusByShipmentId(String shipmentId) {
        List<TransferOutboundPlanEntity> plans = transferOutboundService.findAllByShipmentIds(Arrays.asList(shipmentId));
        if (CollectionUtil.isEmpty(plans)) {
            return ShipmentProcessStatusEnum.PENDING_SHIPPING.getValue();
        } else {
            if (plans.stream().anyMatch(t -> t.getTransferOutboundPlanStatus() != null && !t.getTransferOutboundPlanStatus().equals(110))) {
                return ShipmentProcessStatusEnum.SHIPPED.getValue();
            } else {
                return ShipmentProcessStatusEnum.PENDING_SHIPPING.getValue();
            }
        }
    }

    public boolean rpaService(WalmartInboundShipment byId, LambdaUpdateChainWrapper<WalmartInboundShipment> updateChainWrapper) {

        //*******是否购买了RPA服务
        Boolean isRpaService = marRpaServiceStatusClaimLogService.verifyOrgFlag(byId.getOrgId(), MarClaimRpaUrlEnum.WFS_RPA_SERVICE.getCode());
        if (isRpaService) {

            JSONObject param = this.getRpaRequestByShipmentIdOrIdAndType(byId, null, MarClaimRpaUrlEnum.WFS_SET_SHIPMENT);
            try {
                if ("prod".equals(env)) {
                    sendRpaProveHttp(null, MarClaimRpaUrlEnum.WFS_SET_SHIPMENT.getRpcType(), JSONObject.toJSONString(param));
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("设置货件RPA请求失败--param--{}---错误信息--{}", param, e.getMessage());
                return false;
            }
            updateChainWrapper.set(WalmartInboundShipment::getShipmentBusinessStatus, ShipmentProcessStatusEnum.SETTING_SHIPMENT.getValue());
        } else {
            updateChainWrapper.set(WalmartInboundShipment::getShipmentBusinessStatus, getBusinessStatusByShipmentId(byId.getShipmentId()));
        }
        return true;
    }

    /**
     * @Description:设置货件后RPA结果返回
     * @Author: wly
     * @Date: 2025-05-16 10:57
     * @Params: [json, classZ]
     * @Return: void
     **/
    @Override
    public void setShipmentInfoRpaReturn(String json) {

        log.info("设置货件RPA返回--{}", json);
        MarWalmartInboundShipmentLog shipmentLog = new MarWalmartInboundShipmentLog();
        shipmentLog.setOperateType(ShipmentOperateTypeEnum.RPA_SET_SHIPMENT.getType());
        shipmentLog.settingDefaultSystemCreate();
        shipmentLog.settingDefaultSystemUpdate();
        WalmartInboundShipmentRpaResponse response = JSONObject.parseObject(json, WalmartInboundShipmentRpaResponse.class);
        WalmartInboundShipment walmartInboundShipment = this.getOne(Wrappers.<WalmartInboundShipment>lambdaQuery()
                .eq(WalmartInboundShipment::getChannelId, response.getChannelId())
                .eq(WalmartInboundShipment::getInboundOrderId, response.getInboundOrderId())
                .eq(WalmartInboundShipment::getShipmentId, response.getShipmentId()).last("limit 1"));
        walmartInboundShipment.settingDefaultSystemUpdate();
        shipmentLog.setShipmentId(walmartInboundShipment.getId());
        shipmentLog.setOrgId(walmartInboundShipment.getOrgId().longValue());

        //1.1自约承运
        if (!walmartInboundShipment.getShipmentCarrier().equals(1)) {
            //rpa返回结果成功：
            //无非已取消的调拨计划单则记为  待发货。
            //有非已取消的调拨计划单为已发货。
            //失败：标记异常，并记录失败日志
            if ("success".equalsIgnoreCase(response.getResult())) {
                String businessStatus = getBusinessStatusByShipmentId(response.getShipmentId());
                walmartInboundShipment.setShipmentBusinessStatus(businessStatus);
                walmartInboundShipment.setExceptionExist(false);
                shipmentLog.setOperateContent("设置货件RPA返回成功");
            } else {
                //标记异常，并记录失败日志
                walmartInboundShipment.setExceptionExist(true);
                shipmentLog.setOperateContent("设置货件RPA返回失败");
                if (StringUtils.isNotEmpty(response.getErrorMsg())) {
                    shipmentLog.setOperateContent(shipmentLog.getOperateContent() + ",错误信息:" + response.getErrorMsg());
                }
            }

        } else {
            //1.2平台承运且货运类型为非small parcel：
            //失败：标记异常，并记录失败日志
            //成功：
            //1.完成后RPA返回货件ID、
            //运费值(新字段)、提货日期(新字段)、申报价值(报关价值|新字段)、提交成功北京时间，后端需要根据提交成功北京时间设置剩余时间为 提交成功北京时间+1小时减去当前北京时间，无剩余则不展示。
            //此处仅保存：运费值 及 提货日期、提交成功时间 即可
            //3.状态：
            //3.1 如果是平台承运且small parcel需要更新为待确认运费
            //3.2 如果是平台承运且非small parcel且未超过运费预警上限则直接更新状态（无非已取消的调拨计划单则记为待发货，有非已取消的调拨计划单为已发货）
            //3.3 如果是平台承运且非small parcel且超过运费预警上限则更新状态为待确认运费。
            //平台承运
            if (!ShipmentTypeEnum.SMALL_PARCEL.getValue().equalsIgnoreCase(walmartInboundShipment.getShippingType())) {

                if ("success".equalsIgnoreCase(response.getResult())) {
                    walmartInboundShipment.setCurrency(response.getCurrency());
                    walmartInboundShipment.setShippingPrice(response.getShippingPrice());
                    walmartInboundShipment.setClaimGoodsDate(DateUtil.convertStringToDate(response.getClaimGoodsDate(), "yyyy-MM-dd HH:mm:ss"));
                    walmartInboundShipment.setDeclarationPrice(response.getDeclarationPrice());
                    walmartInboundShipment.setSubDate(DateUtil.convertStringToDate(response.getSubDate(), "yyyy-MM-dd HH:mm:ss"));

                    CheckCommonShipmentRuleQuery ruleQuery = new CheckCommonShipmentRuleQuery();
                    Account account = accountService.getOne(new LambdaQueryWrapper<Account>().eq(Account::getOrgId, walmartInboundShipment.getOrgId()).eq(Account::getFlag, walmartInboundShipment.getChannelId()).eq(Account::getActive, "Y").last("limit 1"));
                    WalmartShipAddress shipAddress = walmartShipAddressService.getOne(Wrappers.lambdaQuery(WalmartShipAddress.class).eq(WalmartShipAddress::getChannelId, walmartInboundShipment.getChannelId())
                            .eq(WalmartShipAddress::getInboundOrderId, walmartInboundShipment.getInboundOrderId())
                            .eq(WalmartShipAddress::getShipmentId, walmartInboundShipment.getShipmentId())
                            .eq(WalmartShipAddress::getType, 1).last("limit 1"));
                    List<WalmartInboundShipmentItem> shipmentItemList = walmartInboundShipmentItemService.list(new LambdaQueryWrapper<WalmartInboundShipmentItem>()
                            .eq(WalmartInboundShipmentItem::getChannelId, walmartInboundShipment.getChannelId())
                            .eq(WalmartInboundShipmentItem::getInboundOrderId, walmartInboundShipment.getInboundOrderId())
                            .eq(WalmartInboundShipmentItem::getShipmentId, walmartInboundShipment.getShipmentId()));
                    ruleQuery.setShopId(account.getId());
                    ruleQuery.setShipWarehouseId(walmartInboundShipment.getShipWarehouseId());
                    ruleQuery.setTargetWarehouseId(shipAddress != null ? shipAddress.getFcName() : null);
                    ruleQuery.setFreight(response.getShippingPrice());
                    ruleQuery.setQty(shipmentItemList.stream().mapToInt(t -> t.getItemQty()).sum());
                    try {
                        CheckCommonShipmentRuleResult isOverWarningLimit = marCommonShipmentRuleService.checkShippingCostExceeded(ruleQuery);
                        log.info("设置货件[{}]RPA返回成功查询是否超过预警上限返回--{}", response.getShipmentId(), isOverWarningLimit);
                        //判断类型运费上限设置是 按发货仓-目的仓配置的运费上限 还是 按单个数量配置的运费上限
                        if (isOverWarningLimit.getCostExceededFlag() == null) {
                            if (new Integer(1).equals(isOverWarningLimit.getCheckType())) {
                                log.info("设置货件RPA返回成功查询是否超过预警上限返回为空店铺未配置规则--{}", json);
                                shipmentLog.setOperateContent("运费预警店铺未配置规则");
                            } else if (new Integer(2).equals(isOverWarningLimit.getCheckType())) {
                                log.info("设置货件RPA返回成功查询是否超过预警上限返回为空发货仓-目的仓未配置规则--{}", json);
                                shipmentLog.setOperateContent("运费预警发货仓-目的仓未配置规则");
                            } else {
                                log.info("设置货件RPA返回成功查询是否超过预警上限返回类型错误--{}", json);
                                shipmentLog.setOperateContent("查询是否超过预警上限返回为空");
                            }
                        } else {
                            if (isOverWarningLimit.getCostExceededFlag()) {

                                walmartInboundShipment.setFreightWarning(true);
                                walmartInboundShipment.setShipmentBusinessStatus(ShipmentProcessStatusEnum.CONFIRM_FEE.getValue());
                                if (new Integer(2).equals(isOverWarningLimit.getCheckType())) {
                                    shipmentLog.setOperateContent("触发按发货仓-目的仓配置的运费上限" + isOverWarningLimit.getFreight() + "美金”");
                                } else if (new Integer(3).equals(isOverWarningLimit.getCheckType())) {
                                    shipmentLog.setOperateContent("触发按单个数量配置的运费上限 数量*" + isOverWarningLimit.getFreight() + "上限 美金");
                                } else {

                                }
                            } else {
                                walmartInboundShipment.setFreightWarning(false);
                                walmartInboundShipment.setShipmentBusinessStatus(getBusinessStatusByShipmentId(response.getShipmentId()));
                                shipmentLog.setOperateContent("运费预警解除");
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("设置货件RPA返回成功查询是否超过预警上限异常--{}", json);
                        shipmentLog.setOperateContent("设置货件RPA返回成功查询是否超过预警上限异常");
                    }


                } else {
                    //标记异常，并记录失败日志
                    walmartInboundShipment.setExceptionExist(true);
                    shipmentLog.setOperateContent("设置货件RPA返回失败");
                    if (StringUtils.isNotEmpty(response.getErrorMsg())) {
                        shipmentLog.setOperateContent(shipmentLog.getOperateContent() + ",错误信息:" + response.getErrorMsg());
                        if (shipmentLog.getOperateContent().length() > 1000) {
                            shipmentLog.setOperateContent(shipmentLog.getOperateContent().substring(0, 1000));
                        }
                    }
                }

            } else {
                log.info("平台承运且货运类型为small parcel：暂不开发--{}", json);
            }
        }

        this.updateById(walmartInboundShipment);
        marWalmartInboundShipmentLogService.save(shipmentLog);
    }


    /**
     * @Description:取消运费rpa返回
     * @Author: wly
     * @Date: 2025-05-16 18:08
     * @Params: [json]
     * @Return: void
     **/
    @Override
    public void cancelShipmentFeeRpaReturn(String json) {

        MarWalmartInboundShipmentLog shipmentLog = new MarWalmartInboundShipmentLog();
        shipmentLog.setOperateType(ShipmentOperateTypeEnum.RPA_CANCEL_FEE.getType());
        shipmentLog.settingDefaultSystemCreate();
        shipmentLog.settingDefaultSystemUpdate();
        WalmartInboundShipmentRpaResponse response = JSONObject.parseObject(json, WalmartInboundShipmentRpaResponse.class);
        WalmartInboundShipment walmartInboundShipment = this.getOne(Wrappers.<WalmartInboundShipment>lambdaQuery()
                .eq(WalmartInboundShipment::getChannelId, response.getChannelId())
                .eq(WalmartInboundShipment::getInboundOrderId, response.getInboundOrderId())
                .eq(WalmartInboundShipment::getShipmentId, response.getShipmentId()).last("limit 1"));
        walmartInboundShipment.settingDefaultSystemUpdate();
        shipmentLog.setShipmentId(walmartInboundShipment.getId());
        shipmentLog.setOrgId(walmartInboundShipment.getOrgId().longValue());

        //成功  修改状态为待设置货件，记录日志
        if ("success".equals(response.getResult())) {
            shipmentLog.setOperateContent("取消运费RPA返回成功");
            walmartInboundShipment.setShipmentBusinessStatus(ShipmentProcessStatusEnum.SET_SHIPMENT.getValue());
        } else {
            //失败  标记异常， 记录日志，状态不变
            shipmentLog.setOperateContent("取消运费RPA返回失败");
            walmartInboundShipment.setExceptionExist(true);
        }

        this.updateById(walmartInboundShipment);
        marWalmartInboundShipmentLogService.save(shipmentLog);

    }

    /**
     * @Description:异常重置
     * @Author: wly
     * @Date: 2025-05-16 15:56
     * @Params: [exceptionIds]
     * @Return: void
     **/
    @Override
    @MarWalmartShipmentVerify
    public String exceptionReset(List<Long> exceptionIds, AuthUserDetails thisUser) {

        List<JSONObject> failList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        log.info("当前用户--{}异常重置货件--{}", thisUser.getName(), exceptionIds);
        WalmartInboundShipmentQueryListDTO dto = new WalmartInboundShipmentQueryListDTO();
        dto.setIds(exceptionIds);
        List<WalmartInboundShipmentPageVO> walmartInboundShipmentPageVOS = this.selectInboundShipmentPage(dto);
        List<Long> skuMapExceptionIds = walmartInboundShipmentPageVOS.stream().filter(t -> Boolean.TRUE.equals(t.getSkuMapExceptionFlag())).map(t -> t.getId()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(skuMapExceptionIds)) {
            exceptionIds.removeIf(skuMapExceptionIds::contains);
        }
        if (CollectionUtil.isEmpty(exceptionIds)) {
            throw new IllegalArgumentException("没有可重置的异常货件");
        }
        List<WalmartInboundShipment> walmartInboundShipments = this.listByIds(exceptionIds);
        walmartInboundShipments.removeIf(t -> !Boolean.TRUE.equals(t.getExceptionExist()));

        //2.设置货件中的异常重置为重新调用RPA提交设置货件信息
        //3.待确认运费的异常重置为提交确认运费
        //4.取消运费中的异常重置为取消运费
        List<WalmartInboundShipment> settingShipmentList = walmartInboundShipments.stream().filter(t -> ShipmentProcessStatusEnum.SETTING_SHIPMENT.getValue().equals(t.getShipmentBusinessStatus())).collect(Collectors.toList());
        List<WalmartInboundShipment> confirmFeeShipmentList = walmartInboundShipments.stream().filter(t -> ShipmentProcessStatusEnum.CONFIRM_FEE.getValue().equals(t.getShipmentBusinessStatus())).collect(Collectors.toList());
        List<WalmartInboundShipment> cancelFeeShipmentList = walmartInboundShipments.stream().filter(t -> ShipmentProcessStatusEnum.FEE_CANCELLING.getValue().equals(t.getShipmentBusinessStatus())).collect(Collectors.toList());
        WalmartInboundShipmentService bean = SpringUtils.getBean(WalmartInboundShipmentService.class);

        //调用设置货件
        if (CollectionUtil.isNotEmpty(settingShipmentList)) {
            for (WalmartInboundShipment shipment : settingShipmentList) {
                WalmartInboundShipmentDetailDTO walmartInboundShipmentDetailDTO = this.getInboundShipmentDetail(shipment.getId());
                walmartInboundShipmentDetailDTO.setLogContent("异常重置设置货件");
                try {
                    JSONObject jsonObject = bean.setShipmentInfo(walmartInboundShipmentDetailDTO, thisUser);
                    if (jsonObject != null) {
                        failList.add(jsonObject);
                        sb.append("货件ID:").append(shipment.getShipmentId()).append("异常重置设置货件失败,失败原因:RPA接口调用失败;");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("货件[{}]异常重置设置货件异常--{}", shipment.getId(), e.getMessage());
                    failList.add(JSONObject.parseObject(JSONObject.toJSONString(shipment), JSONObject.class));
                    sb.append("货件ID:").append(shipment.getShipmentId()).append("异常重置设置货件失败,失败原因:").append(e.getMessage()).append(";");
                }

            }
        }
        //确认运费
        if (CollectionUtil.isNotEmpty(confirmFeeShipmentList)) {
            for (WalmartInboundShipment shipment : confirmFeeShipmentList) {
                try {
                    JSONObject jsonObject = bean.confirmFreight(shipment.getId(), thisUser, "异常重置确认运费");
                    if (jsonObject != null) {
                        failList.add(jsonObject);
                        sb.append("货件ID:").append(shipment.getShipmentId()).append("异常重置确认运费失败,失败原因:RPA接口调用失败;");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("货件[{}]异常重置确认运费异常--{}", shipment.getId(), e.getMessage());
                    failList.add(JSONObject.parseObject(JSONObject.toJSONString(shipment), JSONObject.class));
                    sb.append("货件ID:").append(shipment.getShipmentId()).append("异常重置确认运费失败,失败原因:").append(e.getMessage()).append(";");
                }

            }
        }
        //取消运费
        if (CollectionUtil.isNotEmpty(cancelFeeShipmentList)) {
            for (WalmartInboundShipment shipment : cancelFeeShipmentList) {
                try {
                    JSONObject jsonObject = bean.cancelFreight(shipment.getId(), thisUser, "异常重置取消运费");
                    if (jsonObject != null) {
                        failList.add(jsonObject);
                        sb.append("货件ID:").append(shipment.getShipmentId()).append("异常重置取消运费失败,失败原因:RPA接口调用失败;");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("货件[{}]异常重置取消运费异常--{}", shipment.getId(), e.getMessage());
                    failList.add(JSONObject.parseObject(JSONObject.toJSONString(shipment), JSONObject.class));
                    sb.append("货件ID:").append(shipment.getShipmentId()).append("异常重置取消运费失败,失败原因:").append(e.getMessage()).append(";");
                }

            }
        }
        return sb.toString();
    }

    /**
     * @Description:取消货件
     * @Author: wly
     * @Date: 2025-05-16 15:59
     * @Params: [cancelIds]
     * @Return: void
     **/
    @Override
    @MarWalmartShipmentVerify
    public String cancelShipment(List<Long> cancelIds, AuthUserDetails authUser) {

        log.info("当前用户--{}取消货件--{}", authUser.getName(), cancelIds);
        StringBuilder cancelFailReason = new StringBuilder();

        List<WalmartInboundShipment> walmartInboundShipments = this.listByIds(cancelIds);
        AssertUtil.isFalse(CollectionUtil.isEmpty(walmartInboundShipments), "勾选数据不存在");
        List<WalmartInboundShipment> noShipmentIdList = walmartInboundShipments.stream().filter(t -> StringUtils.isEmpty(t.getShipmentId())).collect(Collectors.toList());
        List<WalmartInboundShipment> hasShipmentIdList = walmartInboundShipments.stream().filter(t -> StringUtils.isNotEmpty(t.getShipmentId()) && (Arrays.asList("CREATED", "PENDING_SHIPMENT_DETAILS")).contains(t.getShipmentStatus())).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(noShipmentIdList)) {
            log.info("货件ID为空的取消货件--{}", noShipmentIdList.stream().map(t -> t.getId()).collect(Collectors.toList()));
            List<MarWalmartInboundShipmentLog> logList = new ArrayList<>();
            noShipmentIdList.forEach(t -> {
                t.setShipmentBusinessStatus(ShipmentProcessStatusEnum.CANCEL.getValue());
                t.setShipmentStatus("CANCELLED");
                t.settingDefaultUpdate();
                MarWalmartInboundShipmentLog log = new MarWalmartInboundShipmentLog();
                log.setOperateType(ShipmentOperateTypeEnum.CANCEL_SHIPMENT.getType());
                log.settingDefaultCreate();
                log.settingDefaultUpdate();
                log.setShipmentId(t.getId());
                log.setOrgId(t.getOrgId().longValue());
                log.setOperateContent("取消货件");
                logList.add(log);
            });
            this.updateBatchById(noShipmentIdList);
            marWalmartInboundShipmentLogService.saveBatch(logList);
        }
        if (CollectionUtil.isNotEmpty(hasShipmentIdList)) {
            log.info("货件ID不为空的取消货件--{}", hasShipmentIdList.stream().map(t -> t.getId()).collect(Collectors.toList()));
            List<Account> accountList = accountService.list(new LambdaQueryWrapper<Account>().eq(Account::getOrgId, hasShipmentIdList.get(0).getOrgId()).in(Account::getFlag, hasShipmentIdList.stream().map(t -> t.getChannelId()).distinct().collect(Collectors.toList())).eq(Account::getActive, "Y"));
            List<MarWalmartInboundShipmentLog> logList = new ArrayList<>();
            hasShipmentIdList.forEach(t -> {
                t.settingDefaultUpdate();
                MarWalmartInboundShipmentLog shipmentLog = new MarWalmartInboundShipmentLog();
                shipmentLog.setOperateType(ShipmentOperateTypeEnum.CANCEL_SHIPMENT.getType());
                shipmentLog.settingDefaultCreate();
                shipmentLog.settingDefaultUpdate();
                shipmentLog.setShipmentId(t.getId());
                shipmentLog.setOrgId(t.getOrgId().longValue());

                try {
                    log.info("货件[{}]订单[{}]渠道[{}]开始取消货件接口调用", t.getShipmentId(), t.getInboundOrderId(), t.getChannelId());
                    WalmartInboundShipmentCancelResponse response = null;
                    if ("prod".equalsIgnoreCase(env)) {
                        response = walmartApi.walmartCommonDelete(accountList.stream().filter(q -> t.getChannelId().equals(q.getFlag())).findFirst().orElse(null), WalmartUrlEnum.WALMART_CANCEL_INBOUND_SHIPMENT.getValue().replace("{inboundOrderId}", t.getInboundOrderId()), null, WalmartInboundShipmentCancelResponse.class);
                    } else {
                        response = new WalmartInboundShipmentCancelResponse();
                        response.setStatus("OK");
                    }

                    log.info("货件[{}]订单[{}]渠道[{}]取消货件接口调用返回结果--{}", t.getShipmentId(), t.getInboundOrderId(), t.getChannelId(), JSONObject.toJSONString(response));
                    if (response == null) {
                        shipmentLog.setOperateContent("取消货件失败,失败原因:接口调用失败;");
                        logList.add(shipmentLog);
                        cancelFailReason.append(t.getShipmentId()).append(shipmentLog.getOperateContent());
                    } else {
                        if (!"OK".equalsIgnoreCase(response.getStatus())) {
                            //TODO 失败原因取？
                            shipmentLog.setOperateContent("取消货件失败,失败原因:" + response.getErrors().get(0).getDescription());
                            if (shipmentLog.getOperateContent().length() > 1000) {
                                shipmentLog.setOperateContent(shipmentLog.getOperateContent().substring(0, 1000));
                            }
                            logList.add(shipmentLog);
                            cancelFailReason.append(t.getShipmentId()).append(shipmentLog.getOperateContent());
                        } else {
                            t.setShipmentBusinessStatus(ShipmentProcessStatusEnum.CANCEL.getValue());
                            t.setShipmentStatus("CANCELLED");
                            shipmentLog.setOperateContent("取消货件");
                            logList.add(shipmentLog);
                        }
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("货件{}取消失败,失败原因:{}", t.getShipmentId(), e.getMessage());
                    shipmentLog.setOperateContent("取消货件失败,失败原因:取消接口调用异常;");
                    logList.add(shipmentLog);
                    cancelFailReason.append(t.getShipmentId()).append("取消货件失败,失败原因:取消接口调用异常;");

                }
            });
            this.updateBatchById(hasShipmentIdList);
            marWalmartInboundShipmentLogService.saveBatch(logList);
        }
        return StringUtils.isEmpty(cancelFailReason.toString()) ? null : cancelFailReason.toString();
    }


    /**
     * @Description:确认运费
     * @Author: wly
     * @Date: 2025-05-16 16:04
     * @Params: [confirmFreightId, authUserDetails, logType]
     * @Return: void
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    @MarWalmartShipmentVerify
    public JSONObject confirmFreight(Long confirmFreightId, AuthUserDetails thisUser, String logType) {

        log.info("当前用户--{}确认运费--{}", thisUser.getName(), confirmFreightId);
        WalmartInboundShipment walmartInboundShipment = this.getById(confirmFreightId);
        MarWalmartInboundShipmentLog shipmentLog = new MarWalmartInboundShipmentLog();
        shipmentLog.setOperateType(StringUtils.isEmpty(logType) ? ShipmentOperateTypeEnum.CONFIRM_FEE.getType() : ShipmentOperateTypeEnum.EXCEPTION_RESET.getType());
        shipmentLog.settingDefaultCreate();
        shipmentLog.settingDefaultUpdate();
        shipmentLog.setShipmentId(walmartInboundShipment.getId());
        shipmentLog.setOrgId(walmartInboundShipment.getOrgId().longValue());
        if (StringUtils.isEmpty(logType)) {
            shipmentLog.setOperateContent("确认运费");
        } else {
            shipmentLog.setOperateContent(logType);
        }
        if (!walmartInboundShipment.getShipmentCarrier().equals(1)) {
            log.info("自约承运确认运费暂不支持--{}", confirmFreightId);
            return null;
        }
        //平台承运
        if (!ShipmentTypeEnum.SMALL_PARCEL.getValue().equals(walmartInboundShipment.getShippingType())) {

            LambdaUpdateChainWrapper<WalmartInboundShipment> updateChainWrapper = this.lambdaUpdate().eq(WalmartInboundShipment::getId, confirmFreightId)
                    .set(WalmartInboundShipment::getShipmentBusinessStatus, getBusinessStatusByShipmentId(walmartInboundShipment.getShipmentId()))
                    .set(WalmartInboundShipment::getUpdatedAt, DateUtils.getNowDate())
                    .set(WalmartInboundShipment::getUpdatedBy, thisUser.getId())
                    .set(WalmartInboundShipment::getUpdatedName, thisUser.getName());
            if (StringUtils.isNotEmpty(logType)) {
                //如果是异常重置，则异常状态置为false
                updateChainWrapper.set(WalmartInboundShipment::getExceptionExist, false);
            }
            updateChainWrapper.update();
            marWalmartInboundShipmentLogService.save(shipmentLog);
        } else {
            //small parcel暂不开发
            log.info("small parcel确认运费暂不开发--{}", confirmFreightId);
        }

        return null;
    }

    /**
     * @Description:取消运费
     * @Author: wly
     * @Date: 2025-05-16 16:05
     * @Params: [cancelFreightId, thisUser, logType]
     * @Return: void
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    @MarWalmartShipmentVerify
    public JSONObject cancelFreight(Long cancelFreightId, AuthUserDetails thisUser, String logType) {
        log.info("当前用户--{}取消运费--{}", thisUser.getName(), cancelFreightId);
        WalmartInboundShipment walmartInboundShipment = this.getById(cancelFreightId);
        MarWalmartInboundShipmentLog shipmentLog = new MarWalmartInboundShipmentLog();
        shipmentLog.setOperateType(StringUtils.isEmpty(logType) ? ShipmentOperateTypeEnum.CANCEL_FEE.getType() : ShipmentOperateTypeEnum.EXCEPTION_RESET.getType());
        shipmentLog.settingDefaultCreate();
        shipmentLog.settingDefaultUpdate();
        shipmentLog.setShipmentId(walmartInboundShipment.getId());
        shipmentLog.setOrgId(walmartInboundShipment.getOrgId().longValue());
        if (StringUtils.isEmpty(logType)) {
            shipmentLog.setOperateContent("取消运费");
        } else {
            shipmentLog.setOperateContent(logType);
        }
        if (!walmartInboundShipment.getShipmentCarrier().equals(1)) {
            log.info("自约承运取消运费暂不支持--{}", cancelFreightId);
            return null;
        }
        //平台承运
        if (!ShipmentTypeEnum.SMALL_PARCEL.getValue().equals(walmartInboundShipment.getShippingType())) {

            LambdaUpdateChainWrapper<WalmartInboundShipment> chainWrapper = this.lambdaUpdate()
                    .eq(WalmartInboundShipment::getId, cancelFreightId)
                    .set(WalmartInboundShipment::getUpdatedAt, DateUtils.getNowDate())
                    .set(WalmartInboundShipment::getUpdatedBy, thisUser.getId())
                    .set(WalmartInboundShipment::getUpdatedName, thisUser.getName());
            //是否购买了rpa服务  购买了rpa服务，则更新运费状态为运费取消中，未购买就更新为待设置货件
            Boolean isRpaService = marRpaServiceStatusClaimLogService.verifyOrgFlag(walmartInboundShipment.getOrgId(), MarClaimRpaUrlEnum.WFS_RPA_SERVICE.getCode());
            if (isRpaService) {
                try {
                    JSONObject jsonObject = getRpaRequestByShipmentIdOrIdAndType(walmartInboundShipment, null, MarClaimRpaUrlEnum.WFS_CANCEL_FREIGHT);
                    if ("prod".equalsIgnoreCase(env)) {
                        sendRpaProveHttp(null, MarClaimRpaUrlEnum.WFS_CANCEL_FREIGHT.getRpcType(), JSONObject.toJSONString(jsonObject));
                    }
                    chainWrapper.set(WalmartInboundShipment::getShipmentBusinessStatus, ShipmentProcessStatusEnum.FEE_CANCELLING.getValue());
                    if (StringUtils.isNotEmpty(logType)) {
                        //如果是异常重置，则异常状态置为false
                        chainWrapper.set(WalmartInboundShipment::getExceptionExist, false);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("用户[{}]取消货件:[{}]运费失败,失败原因:{}", thisUser.getName(), walmartInboundShipment.getShipmentId(), e.getMessage());
                    shipmentLog.setOperateContent(shipmentLog.getOperateContent() + "取消运费失败,失败原因:调用接口失败");
                    return JSONObject.parseObject(JSONObject.toJSONString(walmartInboundShipment), JSONObject.class);
                }

            } else {
                chainWrapper.set(WalmartInboundShipment::getShipmentBusinessStatus, ShipmentProcessStatusEnum.SET_SHIPMENT.getValue());
                if (StringUtils.isNotEmpty(logType)) {
                    //如果是异常重置，则异常状态置为false
                    chainWrapper.set(WalmartInboundShipment::getExceptionExist, false);
                }
            }
            chainWrapper.update();
            marWalmartInboundShipmentLogService.save(shipmentLog);
        } else {
            //small parcel暂不开发
            log.info("small parcel取消运费暂不开发--{}", cancelFreightId);
        }
        return null;
    }


    public void sendRpaProveHttp(Long shopId, Integer proveType, String reqParm) throws Exception {
        //记录异常日志
        String reqUrl = rpaReqUrl + MarClaimRpaUrlEnum.getByValue(proveType).getUrl();
        HttpRequest request = HttpUtil.createPost(reqUrl);
        request.body(reqParm);
        log.info("调用rpa进行业务操作" + MarClaimRpaUrlEnum.getByValue(proveType).getValue() + "{},Body：{}", reqUrl, reqParm);
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            TimeUnit.SECONDS.sleep(2);
            response = request.execute();
            if (!response.isOk()) {
                log.info("调用RPC进行业务操作" + MarClaimRpaUrlEnum.getByValue(proveType).getValue() + "失败URL：{},Body：{},Res:{}", reqUrl, reqParm, JSONObject.toJSONString(response));
                throw new CustomException("调用失败");
            }
        }
    }

    private JSONObject getRpaRequestByShipmentIdOrIdAndType(WalmartInboundShipment shipment, Long id, MarClaimRpaUrlEnum rpaUrlEnum) {
        if (shipment == null) {
            shipment = this.getById(id);
        }
        Account account = accountService.getOne(Wrappers.lambdaQuery(Account.class).eq(Account::getOrgId, shipment.getOrgId()).eq(Account::getFlag, shipment.getChannelId()).eq(Account::getActive, "Y").last("LIMIT 1"));
        List<Account> accounts = accountMapper.selectShopIdMapRegisterCompanyName(Arrays.asList(account.getId()), null);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("shipmentId", shipment.getShipmentId());
        jsonObject.put("inboundOrderId", shipment.getInboundOrderId());
        jsonObject.put("channelId", shipment.getChannelId());
        jsonObject.put("businessId", shipment.getId());
        jsonObject.put("maillId", account.getSellerId());
        jsonObject.put("domainName", accounts.get(0).getDomainName());
        jsonObject.put("registerCompanyName", accounts.get(0).getRegisterCompanyName());
        switch (rpaUrlEnum) {
            case WFS_SET_SHIPMENT:  //设置货件
                jsonObject.put("rpcType", MarClaimRpaUrlEnum.WFS_SET_SHIPMENT.getRpcType());
                jsonObject.put("shipmentCarrier", shipment.getShipmentCarrier());
                jsonObject.put("shippingType", shipment.getShippingType());
                List<WalmartShipmentOrganizationWarehousesVO> codeById = walmartInboundShipmentMapper.getOrganizationWarehousesCodeById(Arrays.asList(shipment.getShipWarehouseId()));
                if (CollectionUtil.isNotEmpty(codeById)) {
                    jsonObject.put("shipWarehouseCode", codeById.get(0).getCode());
                }

                jsonObject.put("expectedClaimGoodsDate", shipment.getExpectedClaimGoodsDate() != null ? DateUtil.convertDateToString(shipment.getExpectedClaimGoodsDate(), "yyyy-MM-dd HH:mm:ss") : null);
                jsonObject.put("singleSellerSkuPackQty", shipment.getSingleSellerSkuPackQty());
                jsonObject.put("mixtureSellerSkuPackQty", shipment.getMixtureSellerSkuPackQty());
                jsonObject.put("freightLevel", shipment.getFreightLevel());
                jsonObject.put("declarationPrice", shipment.getDeclarationPrice());
                List<WalmartInboundShipmentItem> shipmentItemList = walmartInboundShipmentItemService.list(new LambdaQueryWrapper<WalmartInboundShipmentItem>()
                        .eq(WalmartInboundShipmentItem::getChannelId, shipment.getChannelId())
                        .eq(WalmartInboundShipmentItem::getInboundOrderId, shipment.getInboundOrderId())
                        .eq(WalmartInboundShipmentItem::getShipmentId, shipment.getShipmentId()));
                if (CollectionUtil.isNotEmpty(shipmentItemList)) {
                    List<JSONObject> itemJsonList = new ArrayList<>();
                    shipmentItemList.forEach(t -> {
                        JSONObject item = new JSONObject();
                        item.put("sku", t.getSku());
                        item.put("gtin", t.getGtin());
                        item.put("itemQty", t.getItemQty());
                        item.put("vendorPackQty", t.getVendorPackQty());
                        item.put("innerPackQty", t.getInnerPackQty());
                        item.put("wfsService", t.getWfsService());
                        itemJsonList.add(item);
                    });
                    jsonObject.put("itemList", itemJsonList);
                }
                List<MarWalmartInboundPack> packList = marWalmartInboundPackService.list(new LambdaQueryWrapper<MarWalmartInboundPack>()
                        .eq(MarWalmartInboundPack::getShipmentId, shipment.getShipmentId())
                        .eq(MarWalmartInboundPack::getInboundOrderId, shipment.getInboundOrderId()));
                if (CollectionUtil.isNotEmpty(packList)) {
                    List<JSONObject> packJsonList = new ArrayList<>();
                    packList.forEach(t -> {
                        JSONObject pack = new JSONObject();
                        pack.put("palletNo", t.getPalletNo());
                        pack.put("high", t.getHigh());
                        pack.put("weight", t.getWeight());
                        pack.put("palletNum", t.getPalletNum());
                        pack.put("stackedFlag", t.getStackedFlag());
                        packJsonList.add(pack);
                    });
                    jsonObject.put("packageInfoList", packJsonList);
                }
                break;
            case WFS_CANCEL_FREIGHT://取消运费
                jsonObject.put("rpcType", MarClaimRpaUrlEnum.WFS_CANCEL_FREIGHT.getRpcType());
                break;
            default:
                break;
        }
        return jsonObject;
    }


    /**
     * 获取物流中心
     *
     * @param contextId
     * @return
     */
    @Override
    public List<String> getWmsCenter(Integer contextId) {
        return walmartInboundShipmentMapper.getWmsCenter(contextId);
    }


    /**
     * @Description:定时更新运费预警 5分钟执行一次运费预警规则，获取承运方为平台承运、货运类型不为Small parcel且有运费且无预警值且发货仓目的仓都不为空的数据进行查询预警
     * @Author: wly
     * @Date: 2025-05-20 14:18
     * @Params: []
     * @Return: void
     **/
    @Override
    public void updateShipmentFreightWarning() {

        int pageNum = 1;
        int pageSize = 5000;
        Long maxId = 0L;
        Map<Integer, Boolean> shopNoSetWarnRuleAdviceMap = new HashMap<>();
        Map<String, Boolean> warehouseNoSetWarnRuleAdviceMap = new HashMap<>();
        Date nowBjDate = DateUtil.pacificTimeToBjDate(DateUtil.UtcToPacificDate(DateUtils.getNowDate()));
        while (true) {
            PageHelper.startPage(pageNum, pageSize);
            List<WalmartInboundShipment> shipmentList = this.baseMapper.selectUpdateShipmentFreightWarning(maxId,nowBjDate);
            if (CollectionUtils.isEmpty(shipmentList)) {
                break;
            }
            List<String> adviceList = new ArrayList<>();
            List<MarWalmartInboundShipmentLog> logList = new ArrayList<>();
            List<Account> accountList = accountService.list(new LambdaQueryWrapper<Account>()
                    .in(Account::getOrgId, shipmentList.stream().map(WalmartInboundShipment::getOrgId).distinct().collect(Collectors.toList()))
                    .in(Account::getFlag, shipmentList.stream().map(WalmartInboundShipment::getChannelId).distinct().collect(Collectors.toList()))
                    .eq(Account::getActive, "Y"));
            List<CheckCommonShipmentRuleQuery> queryList = new ArrayList<>();
            Map<Integer, String> organizationWarehousesCodeById = walmartInboundShipmentMapper.getOrganizationWarehousesCodeById(shipmentList.stream().map(WalmartInboundShipment::getShipWarehouseId).distinct().collect(Collectors.toList()))
                    .stream().collect(Collectors.toMap(t -> t.getId().intValue(), WalmartShipmentOrganizationWarehousesVO::getCode));
            shipmentList.forEach(t -> {
                CheckCommonShipmentRuleQuery query = new CheckCommonShipmentRuleQuery();
                query.setId(t.getId());
                query.setShopId(accountList.stream().filter(r -> t.getOrgId().equals(r.getOrgId()) && t.getChannelId().equals(r.getFlag())).findFirst().get().getId());
                query.setFreight(t.getShippingPrice());
                query.setShipWarehouseId(t.getShipWarehouseId());
                query.setTargetWarehouseId(t.getFcName());
                query.setQty(t.getTotalItemQty());
                queryList.add(query);
            });
            List<CheckCommonShipmentRuleResult> results = marCommonShipmentRuleService.batchCheckShippingCostExceeded(queryList);
            if (CollectionUtils.isEmpty(results)) {
                break;
            }
            shipmentList.forEach(t -> {
                results.stream().filter(q -> t.getId().equals(q.getId())).findFirst().ifPresent(r -> {
                    if (r.getCostExceededFlag() == null) {
                        if (new Integer(1).equals(r.getCheckType())) {
                            if (!shopNoSetWarnRuleAdviceMap.getOrDefault(t.getShopId(), false)) {
                                String advice = "店铺" + accountList.stream().filter(account -> t.getShopId().equals(account.getId())).findFirst().get().getTitle() + "未配置自提运费上限";
                                adviceList.add(advice);
                            }
                            shopNoSetWarnRuleAdviceMap.put(t.getShopId(), true);
                        } else if (new Integer(2).equals(r.getCheckType())) {
                            String key = t.getFcName() + "_" + t.getShipWarehouseId().toString();
                            if (!warehouseNoSetWarnRuleAdviceMap.getOrDefault(key, false)) {
                                String advice = "发货仓：" + organizationWarehousesCodeById.get(t.getShipWarehouseId()) + "，目的仓：" + t.getFcName() + "，未配置自提运费上限";
                                adviceList.add(advice);
                            }
                            warehouseNoSetWarnRuleAdviceMap.put(key, true);
                        } else {
                        }
                    } else if (r.getCostExceededFlag()) {
                        if (new Integer(2).equals(r.getCheckType())) {
                            t.setFreightWarning(true);
                            t.setShipmentBusinessStatus(ShipmentProcessStatusEnum.CONFIRM_FEE.getValue());
                            String advice = "货件ID：" + t.getShipmentId() + "，发货仓：" + organizationWarehousesCodeById.get(t.getShipWarehouseId()) + "，目的仓：" + t.getFcName() + "，运费为" + t.getShippingPrice() + t.getCurrency() + "，请及时处理";
                            MarWalmartInboundShipmentLog shipmentLog = new MarWalmartInboundShipmentLog();
                            shipmentLog.setShipmentId(t.getId());
                            shipmentLog.setOrgId(t.getOrgId().longValue());
                            shipmentLog.setOperateType(ShipmentOperateTypeEnum.UPDATE_FEE_WARNING.getType());
                            shipmentLog.setOperateContent("触发按发货仓-目的仓配置的运费上限" + r.getFreight() + "美金");
                            shipmentLog.settingDefaultSystemCreate();
                            logList.add(shipmentLog);
                            adviceList.add(advice);

                        } else if (new Integer(3).equals(r.getCheckType())) {
                            t.setFreightWarning(true);
                            t.setShipmentBusinessStatus(ShipmentProcessStatusEnum.CONFIRM_FEE.getValue());
                            MarWalmartInboundShipmentLog shipmentLog = new MarWalmartInboundShipmentLog();
                            shipmentLog.setShipmentId(t.getId());
                            shipmentLog.setOrgId(t.getOrgId().longValue());
                            shipmentLog.setOperateType(ShipmentOperateTypeEnum.UPDATE_FEE_WARNING.getType());
                            shipmentLog.setOperateContent("触发按单个数量配置的运费上限 数量*" + r.getFreight() + " 美金");
                            shipmentLog.settingDefaultSystemCreate();
                            logList.add(shipmentLog);
                        } else {
                        }
                    } else {
                        if (!new Integer(1).equals(r.getCheckType())) {
                            t.setFreightWarning(false);
                            t.setShipmentBusinessStatus(getBusinessStatusByShipmentId(t.getShipmentId()));
                            MarWalmartInboundShipmentLog shipmentLog = new MarWalmartInboundShipmentLog();
                            shipmentLog.setShipmentId(t.getId());
                            shipmentLog.setOrgId(t.getOrgId().longValue());
                            shipmentLog.setOperateType(ShipmentOperateTypeEnum.UPDATE_FEE_WARNING.getType());
                            shipmentLog.setOperateContent("运费预警解除");
                            shipmentLog.settingDefaultSystemCreate();
                            logList.add(shipmentLog);
                        } else {
                        }
                    }
                });
            });
            maxId = shipmentList.get(shipmentList.size() - 1).getId();

            this.updateBatchById(shipmentList);
            if (CollectionUtil.isNotEmpty(logList)) {
                marWalmartInboundShipmentLogService.saveBatch(logList);
            }
            if (CollectionUtil.isNotEmpty(adviceList)) {
                threadPoolTaskExecutor.execute(() -> {
                    adviceList.forEach(advice -> {
                        QywxUtil.wxMessageSend(advice, "13298184330");
                        QywxUtil.wxMessageSend(advice, "13420560853");
                    });
                });
            }
        }
    }

    /**
     * <AUTHOR>
     * @Description  根据货件id获取货件是否存在及相关店铺渠道
     * @Date 10:57 2025/5/27
     * @Param [dto]
     * @return com.bizark.op.api.entity.op.walmart.WalmartShipmentExistwalmart_inbound_shipment
     **/
    @Override
    public WalmartShipmentExist getShipmentExist(WalmartShipmentExistDTO dto) {
        // 打印版本号
        log.info("货件渠道查询接口版本号:version:{}",dto.getVersion());
        if (dto == null || StringUtils.isBlank(dto.getShipmentId())){
            throw new CommonException("ShipmentId不可为空!");
        }
        // 根据shipmentId判断所属渠道
        WalmartShipmentExist walmartShipmentExist = new WalmartShipmentExist();
        WalmartShipmentExist shipmentExist = walmartInboundShipmentMapper.getShipmentExist(dto.getShipmentId());
        if (shipmentExist != null){
            walmartShipmentExist = shipmentExist;
        }else  {
            // todo 添加wayfair渠道数据
            walmartShipmentExist.setExist(false);
            walmartShipmentExist.setShipmentId(dto.getShipmentId());
        }
        walmartShipmentExist.setVersion(StringUtils.isNotEmpty(dto.getVersion())?dto.getVersion():"1.0.0");
        return walmartShipmentExist;
    }


    @Override
    public List<String> getShipmentCarrierName(Integer contextId) {
        return this.lambdaQuery()
                .eq(WalmartInboundShipment::getOrgId, contextId)
                .isNotNull(WalmartInboundShipment::getCarrierName)
                .ne(WalmartInboundShipment::getCarrierName, "")
                .groupBy(WalmartInboundShipment::getCarrierName)
                .list().stream().map(WalmartInboundShipment::getCarrierName).collect(Collectors.toList());
    }


    /**
     * @Description:提供接口给供应链获取货件状态
     * @Author: wly
     * @Date: 2025-06-04 14:49
     * @Params: [shipmentIds, orgId]
     * @Return: java.util.List<com.alibaba.fastjson.JSONObject>
     **/
    @Override
    public List<JSONObject> getShipmentStatusByShipmentIdsAndOrgId(List<String> shipmentIds, Integer orgId) {
        List<WalmartInboundShipment> list = this.list(Wrappers.<WalmartInboundShipment>lambdaQuery().eq(WalmartInboundShipment::getOrgId, orgId)
                .in(WalmartInboundShipment::getShipmentId, shipmentIds));
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        } else {
            List<JSONObject> result = new ArrayList<>();
            list.forEach(t -> {
                JSONObject object = new JSONObject();
                object.put("shipmentId", t.getShipmentId());
                object.put("shipmentStatus", t.getShipmentStatus());
                result.add(object);
            });
            return result;
        }
    }


    @Override
    public JSONObject getShipmentSkuAndChildSkuByShipmentId(Integer orgId, Integer shopId, String shipmentId) {
        log.info("海外仓获取货件信息入参--{},{},{}", orgId, shopId, shipmentId);
        AssertUtil.isFalse(orgId == null, "组织id不能为空");
        AssertUtil.isFalse(shopId == null, "店铺不能为空");
        AssertUtil.isFalse(StringUtils.isEmpty(shipmentId), "ShipmentID不能为空");
        Account accountFromDb = accountService.getById(shopId);
        AssertUtil.isFalse(accountFromDb == null, "店铺不存在");
        WalmartInboundShipment walmartInboundShipment = this.getOne(Wrappers.<WalmartInboundShipment>lambdaQuery().eq(WalmartInboundShipment::getOrgId, orgId)
                .eq(WalmartInboundShipment::getChannelId, accountFromDb.getFlag())
                .eq(WalmartInboundShipment::getShipmentId, shipmentId)
                .last("LIMIT 1"));
        AssertUtil.isFalse(walmartInboundShipment == null, "货件不存在");
        LambdaQueryWrapper<WalmartInboundShipmentItem> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WalmartInboundShipmentItem::getChannelId, walmartInboundShipment.getChannelId())
                .eq(WalmartInboundShipmentItem::getInboundOrderId, walmartInboundShipment.getInboundOrderId())
                .eq(WalmartInboundShipmentItem::getShipmentId, walmartInboundShipment.getShipmentId());
        List<WalmartInboundShipmentItem> walmartInboundShipmentItemList = walmartInboundShipmentItemService.list(lambdaQueryWrapper);
        AssertUtil.isFalse(CollectionUtil.isEmpty(walmartInboundShipmentItemList), "货件明细不存在");
        JSONObject result = new JSONObject();
        result.put("orgId", orgId);
        result.put("shopId", shopId);
        result.put("shipmentId", shipmentId);
        JSONArray skuList = new JSONArray();

        SystemGlobalConfig skuVersionInitConfig = systemGlobalConfigMapper.selectByOrgIdAndConfigKey(orgId, "skuVersionInitConfig");
        Boolean skuMulVersion = skuVersionInitConfig != null && new Integer(1).equals(skuVersionInitConfig.getStatus());
        result.put("skuMulVersion", skuMulVersion);
        List<SkuChildren> skuChildrenList = skuMulVersion ? skuChildrenMapper.selectByOrgIdAndParentSku(orgId, walmartInboundShipmentItemList.stream().map(WalmartInboundShipmentItem::getErpSku).collect(Collectors.toList())) : new ArrayList<>();
        for (WalmartInboundShipmentItem shipmentItem : walmartInboundShipmentItemList) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("SellerSKU",shipmentItem.getSku());
            jsonObject.put("SKU", shipmentItem.getErpSku());
            jsonObject.put("itemQty", shipmentItem.getItemQty());
            if (skuMulVersion) {
                if (CollectionUtil.isNotEmpty(skuChildrenList)) {
                    List<SkuChildren> erpSkuMapChildSku = skuChildrenList.stream().filter(t -> t.getParentSku().equals(shipmentItem.getErpSku())).collect(Collectors.toList());
                    List<String> childSkuList = erpSkuMapChildSku.stream().filter(t->StringUtils.isNotEmpty(t.getSku())).map(SkuChildren::getSku).distinct().collect(Collectors.toList());
                    jsonObject.put("childSkuList", childSkuList);
                    if (CollectionUtil.isNotEmpty(childSkuList) && erpSkuMapChildSku.stream().anyMatch(t -> t.getPriority() != null)) {
                        jsonObject.put("highPriorityChildSku", erpSkuMapChildSku.stream().filter(t->t.getPriority() != null).min(Comparator.comparing(SkuChildren::getPriority)).get().getSku());
                    }else {
                        jsonObject.put("highPriorityChildSku", null);
                    }

                }else {
                    jsonObject.put("childSkuList", new ArrayList<String>());
                }
            }
            skuList.add(jsonObject);
        }
        result.put("skuList", skuList);
        log.info("海外仓获取货件信息入参--{},{},{}--出参--{}", orgId, shopId, shipmentId,result);
        return result;
    }

    @Override
    public List<WalmartInboundShipment> selectShipments(WalmartInboundShipmentQueryDTO dto) {
        dto.validate();
        List<WalmartInboundShipment> shipments = walmartInboundShipmentMapper.selectShipments(dto);
        return completeShipmentItems(shipments);
    }


    @Override
    public List<WalmartInboundShipmentInfo> selectShipmentInfo(WalmartInboundShipmentQueryDTO dto) {
        return walmartInboundShipmentItemMapper.selectShipments(dto);
    }

    private List<WalmartInboundShipment> completeShipmentItems(List<WalmartInboundShipment> shipments) {
        if (CollectionUtil.isEmpty(shipments)) {
            return shipments;
        }
        List<String> shipmentIds = shipments.stream().map(WalmartInboundShipment::getShipmentId).collect(Collectors.toList());

        List<WalmartInboundShipmentItem> shipmentItems = walmartInboundShipmentItemService.lambdaQuery()
                .in(WalmartInboundShipmentItem::getShipmentId, shipmentIds)
                .list();

        if (CollectionUtil.isEmpty(shipmentItems)) {
            return shipments;
        }

        Map<String, List<WalmartInboundShipmentItem>> shipmentIdMap = shipmentItems.stream()
                .collect(Collectors.groupingBy(c-> c.getInboundOrderId() + c.getShipmentId()));

        for (WalmartInboundShipment shipment : shipments) {
            shipment.setItems(shipmentIdMap.getOrDefault(shipment.getInboundOrderId() + shipment.getShipmentId(), new ArrayList<>()));
        }
        return shipments;
    }


    /**
     * 接收预估运费消息
     * @param message
     */
    @Override
    public void receiveEstimatedFreightMessage(String message) {
        log.info("wfs货件预估运费消息:{}", message);
        if (StringUtils.isEmpty(message)) {
            return;
        }
        JSONObject jsonObject = JSONObject.parseObject(message, JSONObject.class);
        String shipmentId = jsonObject.getString("shipmentId");
        String fee = jsonObject.getString("fee");
        if (StringUtils.isEmpty(shipmentId) || StringUtils.isEmpty(fee)) {
            log.error("wfs货件预估运费消息中没有shipmentId或fee字段--{}", message);
            return;
        }
        LambdaQueryWrapper<WalmartInboundShipment> queryWrapper = new LambdaQueryWrapper<WalmartInboundShipment>()
                .eq(WalmartInboundShipment::getShipmentId, shipmentId)
                .eq(WalmartInboundShipment::getOrgId, 1000049).last("LIMIT 1");
        WalmartInboundShipment existShipment = this.getOne(queryWrapper);
        if (existShipment == null) {
            for (int i = 0; i < 15; i++) {
                try {
                    TimeUnit.SECONDS.sleep(2);
                    existShipment = this.getOne(queryWrapper);
                    if (existShipment != null) {
                        break;
                    }
                } catch (Exception e) {
                }
            }
        }
        if (existShipment == null) {
            log.error("wfs货件预估运费消息在系统中不存在--{}", message);
            return;
        }

        WalmartInboundShipment update = new WalmartInboundShipment();
        update.setId(existShipment.getId());
        String val = fee.replaceAll("[^\\d.]", "");
        update.setEstimatedFreight(new BigDecimal(val));
        this.updateById(update);
    }
}
