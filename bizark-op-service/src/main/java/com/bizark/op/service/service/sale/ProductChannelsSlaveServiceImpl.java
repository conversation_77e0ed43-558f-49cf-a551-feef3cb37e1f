package com.bizark.op.service.service.sale;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.op.api.enm.product.ProductTypeEnum;
import com.bizark.op.api.enm.sale.ProductRelateTypeEnum;
import com.bizark.op.api.enm.sale.SyncMappingEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.inventory.ProductChannelsInventory;
import com.bizark.op.api.entity.op.product.SkuChildren;
import com.bizark.op.api.entity.op.sale.*;
import com.bizark.op.api.entity.op.sale.vo.AmazonProductChannelVO;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.product.SkuChildrenService;
import com.bizark.op.api.service.sale.*;
import com.bizark.op.service.mapper.inventory.ProductChannelsInventoryMapper;
import com.bizark.op.service.mapper.sale.ProductChannelsSlaveMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class ProductChannelsSlaveServiceImpl extends ServiceImpl<ProductChannelsSlaveMapper, ProductChannelsSlave> implements ProductChannelsSlaveService {

    @Autowired
    private ProductChannelsService productChannelsService;

    @Autowired
    private ScGoodsCategoryService goodsCategoryService;

    @Autowired
    private ProductsService productsService;

    @Autowired
    private ProductChannelRelateService productChannelRelateService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private ProductChannelsInventoryMapper productChannelsInventoryMapper;

    @Autowired
    private ProductChannelsRestrictService productChannelsRestrictService;
    @Autowired
    private SkuChildrenService skuChildrenService;


    @Override
    public void initSlaveSkuInventory(String sellerSku) {
        if (StrUtil.isNotBlank(sellerSku)) {
            List<ProductChannels> productChannels = productChannelsService.lambdaQuery()
                    .eq(ProductChannels::getSellerSku, sellerSku)
                    .list();
            if (CollectionUtil.isNotEmpty(productChannels)) {
                // 设置运营以及部门数据
                syncSkuInventory(productChannels);
            }
            return;
        }
        int cursor = 0;
        List<ProductChannels> channels;
        while (CollectionUtil.isNotEmpty(channels = productChannelsService.selectByIdCursor(cursor, 2000))) {
            cursor = channels.get(channels.size() - 1).getId();

            // 设置运营以及部门数据
            syncSkuInventory(channels);
        }
    }


    @Override
    public void init(String sellerSku) {
        if (StrUtil.isNotBlank(sellerSku)) {
            List<ProductChannels> productChannels = productChannelsService.lambdaQuery()
                    .eq(ProductChannels::getSellerSku, sellerSku)
                    .list();
            if (CollectionUtil.isNotEmpty(productChannels)) {
                // 设置运营以及部门数据
                doHandleChannelsSlave(productChannels);
//                syncSkuInventory(productChannels);
            }
            return;
        }

        int cursor = 0;
        List<ProductChannels> channels;
        while (CollectionUtil.isNotEmpty(channels = productChannelsService.selectByIdCursor(cursor, 2000))) {
            cursor = channels.get(channels.size() - 1).getId();

            // 设置运营以及部门数据
            doHandleChannelsSlave(channels);
//            syncSkuInventory(channels);
        }
    }

    private void doHandleChannelsSlave(List<ProductChannels> channels) {
        List<ProductChannelsSlave> channelsSlaves = buildChannelsSlaves(channels);
        if (CollectionUtil.isNotEmpty(channelsSlaves)) {
            this.saveOrUpdateBatch(channelsSlaves);
        }
    }


    public void syncSkuInventory(List<ProductChannels> channels) {

        Map<Integer, List<ProductChannels>> orgMap = channels.stream().collect(Collectors.groupingBy(ProductChannels::getOrgId));

        for (Map.Entry<Integer, List<ProductChannels>> entry : orgMap.entrySet()) {
            doSyncSkuInventory(entry.getKey(),entry.getValue());
        }

    }

    private void doSyncSkuInventory(Integer orgId,List<ProductChannels> channels) {
        List<Integer> channelIds = channels.stream().map(ProductChannels::getId).distinct().collect(Collectors.toList());

        otherDataSetting(orgId, channels);

//        List<ProductChannelsInventory> inventories = productChannelsInventoryMapper.selectInventoryByChannelIds(channelIds);


//        Map<Integer, Integer> qtyMap = new HashMap<>();
//        if (CollectionUtil.isNotEmpty(inventories)) {
//            Map<Integer, Integer> map = inventories.stream()
//                    .collect(Collectors.toMap(ProductChannelsInventory::getChannelId, c -> Integer.parseInt(c.getQuantity()), Integer::sum));
//            qtyMap.putAll(map);
//        }


        List<ProductChannelsSlave> slaves = this.lambdaQuery()
                .in(ProductChannelsSlave::getProductChannelId, channelIds)
                .list();

        if (CollectionUtil.isEmpty(slaves)) {
            List<ProductChannelsSlave> channelsSlaves = buildChannelsSlaves(channels);
            this.saveOrUpdateBatch(channelsSlaves);
            return;
        }


        // 获取不存在从数据的映射，直接生成从表数据
        List<Integer> productChannelIds = slaves.stream()
                .map(ProductChannelsSlave::getProductChannelId)
                .collect(Collectors.toList());

        List<ProductChannels> needGenChannels = channels.stream()
                .filter(c -> !productChannelIds.contains(c.getId()))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(needGenChannels)) {
            List<ProductChannelsSlave> channelsSlaves = buildChannelsSlaves(needGenChannels);
            this.saveOrUpdateBatch(channelsSlaves);
        }

        List<ProductChannelsSlave> updates = new ArrayList<>();


        Map<Integer, ProductChannels> channelsMap = channels.stream().collect(Collectors.toMap(ProductChannels::getId, Function.identity(), (v1, v2) -> v1));

        for (ProductChannelsSlave slave : slaves) {
//            if (qtyMap.containsKey(slave.getProductChannelId())) {
//                Integer inventory = qtyMap.get(slave.getProductChannelId());
//                if (!Objects.equals(inventory, slave.getInventory())) {
//                    slave.setInventory(inventory);
//                    updates.add(slave);
//                }
//            }
            if (channelsMap.containsKey(slave.getProductChannelId())) {
                ProductChannels p = channelsMap.get(slave.getProductChannelId());
                slave.setSkuMinInventory(p.getSkuMinInventory());
                updates.add(slave);
            }
        }

        if (CollectionUtil.isNotEmpty(updates)) {
            this.updateBatchById(updates);
        }
    }


    @NotNull
    private List<ProductChannelsSlave> buildChannelsSlaves(List<ProductChannels> channels) {

        // 数据去重,防止生成重复数据
        channels = channels.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ProductChannels::getId)))
                        , ArrayList::new));


        productChannelsService.settingUserInfo(channels);


        Map<Integer, List<ProductChannels>> orgMap = channels.stream()
                .collect(Collectors.groupingBy(ProductChannels::getOrgId));


        for (Map.Entry<Integer, List<ProductChannels>> entry : orgMap.entrySet()) {
            try {
                otherDataSetting(entry.getKey(), entry.getValue());
            } catch (Exception e) {
                log.info("从表数据设置异常 - ", e);
            }
        }


        // 查询从表数据
        List<Integer> channelIds = channels.stream().map(ProductChannels::getId).collect(Collectors.toList());

        List<ProductChannelsSlave> slaves = this.lambdaQuery()
                .select(ProductChannelsSlave::getProductChannelId, ProductChannelsSlave::getId)
                .in(ProductChannelsSlave::getProductChannelId, channelIds)
                .list();


        Map<Integer, Long> slaveMap = new HashMap<>();

        if (CollectionUtil.isNotEmpty(slaves)) {
            Map<Integer, Long> map = slaves.stream().collect(Collectors.toMap(ProductChannelsSlave::getProductChannelId, ProductChannelsSlave::getId));
            slaveMap.putAll(map);
        }


        List<ProductChannelsInventory> inventories = productChannelsInventoryMapper.selectInventoryByChannelIds(channelIds);


        Map<Integer, Integer> qtyMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(inventories)) {
            Map<Integer, Integer> map = inventories.stream()
                    .collect(Collectors.toMap(ProductChannelsInventory::getChannelId, c -> Integer.parseInt(c.getQuantity()), Integer::sum));
            qtyMap.putAll(map);
        }

        // 受限信息
        List<ProductChannelsRestrict> channelsRestricts = productChannelsRestrictService.lambdaQuery()
                .in(ProductChannelsRestrict::getChannelId, channelIds)
                .list();

        Map<Integer, List<String>> restrictMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(channelsRestricts)) {
            Map<Integer, List<String>> map = channelsRestricts.stream()
                    .collect(Collectors.groupingBy(ProductChannelsRestrict::getChannelId, Collectors.mapping(ProductChannelsRestrict::getRestrictedReason, Collectors.toList())));
            restrictMap.putAll(map);
        }
        // 插入从表数据
        List<ProductChannelsSlave> channelsSlaves = new ArrayList<>();

        for (ProductChannels channel : channels) {
            ProductChannelsSlave slave = new ProductChannelsSlave();

            if (restrictMap.containsKey(channel.getId())) {
                slave.setRestrictedReason(String.join(",", CollectionUtil.distinct(restrictMap.get(channel.getId()))));
            }else{
                slave.setRestrictedReason(StrUtil.EMPTY);
            }
            if (slaveMap.containsKey(channel.getId())) {
                slave.setId(slaveMap.get(channel.getId()));
            }
            if (qtyMap.containsKey(channel.getId())) {
                slave.setInventory(qtyMap.get(channel.getId()));
            }
            slave.setOrgId(channel.getOrgId());
            slave.setProductChannelId(channel.getId());
            slave.setOperateUserId(channel.getOperationUserId());
            slave.setOperateUserName(channel.getOperationUserName());
            slave.setDeptId(channel.getDeptId());
            slave.setDeptName(channel.getDeptName());
            slave.setSkuMinInventory(channel.getSkuMinInventory());
            slave.setAccountTitle(channel.getAccountTitle());
            slave.setAccountInit(channel.getAccountInit());
            slave.setCountry(channel.getCountry());

            if (StrUtil.isNotBlank(channel.getErpSku()) && Objects.equals(channel.getType(), ProductRelateTypeEnum.SINGLE.getValue())) {
                slave.setErpSkuQuantity(channel.getErpSku() + " * 1");
            }
            if (CollectionUtil.isNotEmpty(channel.getProductRelates())) {
                List<ProductChannelRelate> relates = channel.getProductRelates();
                List<String> erpSku = relates.stream().map(ProductChannelRelate::getErpsku).filter(StrUtil::isNotBlank).collect(Collectors.toList());
                List<String> categoryNames = relates.stream().map(ProductChannelRelate::getCategoryName).filter(StrUtil::isNotBlank).collect(Collectors.toList());

                List<String> erpSkuQuantity = relates.stream().map(c -> c.getErpsku() + " * " + c.getQty()).collect(Collectors.toList());

//                List<String> categoryIds = relates.stream().map(ProductChannelRelate::getCategoryId).filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList());

                List<String> models = relates.stream().map(ProductChannelRelate::getModel).filter(StrUtil::isNotBlank).collect(Collectors.toList());


                if (CollectionUtil.isNotEmpty(erpSkuQuantity)) {
                    slave.setErpSkuQuantity(String.join(",", erpSkuQuantity));
                }

                if (CollectionUtil.isNotEmpty(models)) {
                    slave.setModel(String.join(",", models));
                }

                Map<Integer, List<ProductChannelRelate>> typeMap = relates.stream().filter(c -> Objects.nonNull(c.getCategoryType())).collect(Collectors.groupingBy(ProductChannelRelate::getCategoryType));

                for (Map.Entry<Integer, List<ProductChannelRelate>> entry : typeMap.entrySet()) {
                    List<String> categoryIds = entry.getValue().stream().map(ProductChannelRelate::getCategoryId).filter(StrUtil::isNotBlank).collect(Collectors.toList());

                    if (CollectionUtil.isEmpty(categoryIds)) {
                        continue;
                    }
                    if (Objects.equals(entry.getKey(), ProductTypeEnum.Finished.getValue())) {
                        slave.setCategoryId(String.join(",", categoryIds));
                    } else {
                        slave.setAccessoriesCategoryId(String.join(",", categoryIds));
                    }

                }
                if (CollectionUtil.isNotEmpty(erpSku)) {
                    slave.setErpSku(String.join(",", erpSku));
                }
                if (CollectionUtil.isNotEmpty(categoryNames)) {
                    slave.setCategoryName(String.join(",", categoryNames));
                }
            }
            slave.setSkipInterceptor(true);
            channelsSlaves.add(slave);
        }
        return channelsSlaves;
    }

    @Override
    public void handleMessage(AmazonProductChannelVO channelVO, ProductChannels channels) {
        ProductChannelsSlave slave = this.lambdaQuery()
                .eq(ProductChannelsSlave::getProductChannelId, channels.getId())
                .one();
        if (Objects.isNull(slave)) {
            // 构建一条从表数据
            List<ProductChannelsSlave> channelsSlaves = buildChannelsSlaves(Lists.newArrayList(channels));
            slave = channelsSlaves.get(0);
        }
        if (Objects.nonNull(channels.getInventory()) && Objects.equals(channels.getSaleChannel(), SyncMappingEnum.SkuGoodsMapSaleChannelEnum.TEMU.getEhengjian())) {
            slave.setInventory(channels.getInventory());
        }
        slave.setAverageRating(channelVO.getAverageRating());
        slave.setPriceType(channelVO.getPriceType());
        slave.setPromoStartDate(channelVO.getPromoStartDate());
        slave.setPromoEndDate(channelVO.getPromoEndDate());
        slave.setComparisonPrice(channelVO.getComparisonPrice());
        slave.setComparisonPriceType(channelVO.getComparisonPriceType());
        slave.setBuyBoxItemPrice(channelVO.getBuyBoxItemPrice());
        slave.setBuyBoxShippingPrice(channelVO.getBuyBoxShippingPrice());
        slave.setBuyBoxEligible(channelVO.getBuyBoxEligible());
        slave.setReviewsCount(channelVO.getReviewsCount());
        this.saveOrUpdate(slave);
    }

    @Override
    public void handleChannelInventory(Integer id) {
        handleChannelInventory(productChannelsService.getById(id));
    }


    @Override
    public void handleChannelInventory(ProductChannels channels) {
        if (Objects.isNull(channels)) {
            return;
        }

        // 查询库存数据
        List<ProductChannelsSlave> channelsSlaves = buildChannelsSlaves(Arrays.asList(channels));
        if (CollectionUtil.isEmpty(channelsSlaves)) {
            log.info("SKU映射从表数据构建失败: {}", JSON.toJSONString(channels));
            return;
        }
        this.saveOrUpdate(channelsSlaves.get(0));
    }

    @Override
    public void handleSlaves(List<ProductChannels> update) {
        doHandleChannelsSlave(update);
    }

    @Override
    public void handleSlaves(ProductChannels channels) {
        handleSlaves(Collections.singletonList(channels));
    }

    @Override
    public void handleSlaves(Integer productChannelId) {
        ProductChannels channels = productChannelsService.getById(productChannelId);
        if (Objects.nonNull(channels)) {
            handleSlaves(channels);
        }
    }

    private void otherDataSetting(Integer orgId, List<ProductChannels> channels) {

        List<String> flags = channels.stream().map(ProductChannels::getAccountId).filter(StrUtil::isNotBlank).collect(Collectors.toList());


        if (CollectionUtil.isNotEmpty(flags)) {
            List<Account> accounts = accountService.lambdaQuery()
                    .eq(Account::getOrgId, orgId)
                    .in(Account::getFlag, flags)
                    .list();

            if (CollectionUtil.isNotEmpty(accounts)) {
                Map<String, Account> accountMap = accounts.stream().collect(Collectors.toMap(Account::getFlag, Function.identity(), (a, b) -> a));
                for (ProductChannels channel : channels) {
                    if (StrUtil.isNotBlank(channel.getAccountId()) && accountMap.containsKey(channel.getAccountId())) {
                        channel.setCountry(accountMap.get(channel.getAccountId()).getCountryCode());
                        channel.setAccountTitle(accountMap.get(channel.getAccountId()).getTitle());
                        channel.setCountry(accountMap.get(channel.getAccountId()).getCountryCode());
                        channel.setAccountInit(accountMap.get(channel.getAccountId()).getAccountInit());
                    }
                }
            }
        }

        // 设置产品关联数据
        productChannelRelateService.assignChannelRelates(channels);


        // 获取需要查询到的所有产品数据
        List<String> allSkus = channels.stream()
                .filter(c -> StrUtil.isNotBlank(c.getErpSku()))
                .map(c -> c.getErpSku().split(","))
                .flatMap(Stream::of)
                .distinct().collect(Collectors.toList());

        // 把当前所有SKU作为父查询
        Map<String, List<SkuChildren>> childrenMap = skuChildrenService.selectSkuByParentSku(orgId, allSkus);


        // 库存数据
        productChannelsService.skuInventorySettingWithMultipleVersionSku(orgId, childrenMap,channels, false);

        // 设置分类数据
        productChannelsService.buildCategoryNameWithMultipleVersionSku(channels, childrenMap);

        // 设置分类数据
//        Map<Integer, List<ProductChannels>> map = channels.stream().collect(Collectors.groupingBy(ProductChannels::getIsParentSku));

//        for (Map.Entry<Integer, List<ProductChannels>> entry : map.entrySet()) {
//
//            List<ProductChannels> currentChannels = entry.getValue();
//
//            if (Objects.equals(entry.getKey(), 0)) {
//                // 如果是子SKU,继续走原逻辑
//                List<String> erpSkus = currentChannels.stream()
//                        .filter(c -> StrUtil.isNotBlank(c.getErpSku()))
//                        .map(c -> CollectionUtil.newArrayList(c.getErpSku().split(",")))
//                        .flatMap(List::stream)
//                        .collect(Collectors.toList());
//
//                if (CollectionUtil.isEmpty(erpSkus)) {
//                    continue;
//                }
//
//                List<Products> products = productsService.lambdaQuery()
//                        .in(Products::getErpsku, erpSkus)
//                        .eq(Products::getOrgId, orgId)
//                        .list();
//
//                if (CollectionUtil.isNotEmpty(products)) {
//                    List<ProductChannelRelate> relates = currentChannels.stream().map(ProductChannels::getProductRelates).filter(CollectionUtil::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());
//                    productChannelsService.buildCategoryName(currentChannels, childrenMap);
//                }
//                continue;
//            }
//
//
//
//
//        }



    }
}
