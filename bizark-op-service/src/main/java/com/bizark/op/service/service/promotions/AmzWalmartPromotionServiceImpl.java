package com.bizark.op.service.service.promotions;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bizark.op.api.enm.mar.MarPromotionInvitationTypeEnum;
import com.bizark.op.api.enm.promotions.*;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.promotions.*;
import com.bizark.op.api.exception.ErpCommonException;
import com.bizark.op.api.request.msg.receive.AmzTemuPromotionMessageLimitReceiveMsg;
import com.bizark.op.api.request.msg.receive.AmzTemuPromotionMessageReceiveMsg;
import com.bizark.op.api.request.msg.receive.AmzWalmartPromotionMessageReceiveMsg;
import com.bizark.op.api.request.msg.receive.TemuPromotionMeiBenReceiveMsg;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.promotions.*;
import com.bizark.op.api.service.promotions.fee.AmzPromotionFeeDateHisRecordService;
import com.bizark.op.common.config.WeChatBootConfigure;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.*;
import com.bizark.op.common.util.spring.SpringUtils;
import com.bizark.op.service.mapper.promotions.AmzPromotionAccountMapper;
import com.bizark.op.service.mapper.promotions.AmzPromotionCheckMapper;
import com.bizark.op.service.mapper.promotions.AmzPromotionsMapper;
import com.bizark.op.service.mapper.promotions.MarTemuPromotionMapper;
import com.bizark.op.service.util.third.qywx.QywxUtil;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.bizark.usercenter.api.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * walmart promotion service implementation
 *
 * @Author: Ailill
 * @Date: 2024/7/18 14:23
 */
@Service
@Slf4j
public class AmzWalmartPromotionServiceImpl implements AmzWalmartPromotionService {

    @Autowired
    private IAmzPromotionsService amzPromotionsService;

    @Autowired
    private IAmzPromotionsSkuDetailService amzPromotionsSkuDetailService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private AmzPromotionsMapper amzPromotionsMapper;

    @Autowired
    private AmzPromotionAccountMapper amzPromotionAccountMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private AmzPromotionFeeDateHisRecordService amzPromotionFeeDateHisRecordService;

    @Autowired
    private IMarPromotionsReportProductService marPromotionsReportProductService;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private AmzPromotionCheckMapper amzPromotionCheckMapper;

    @Autowired
    private WeChatBootConfigure weChatBootConfigure;

    @Autowired
    private IMarPlatformPromotionsInfoService marPlatformPromotionsInfoService;

    @Autowired
    private IAmzPromotionsOperateLogService amzPromotionsOperateLogService;

    @Autowired
    private MarTemuPromotionMapper marTemuPromotionMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void walmartPromotionMessageReceive(String message) {
        if (StringUtils.isEmpty(message)) {
            log.error("Received empty message from walmart promotion service");
            return;
        }
        log.info("Received message from walmart promotion service:{} ", message);
        AmzWalmartPromotionMessageReceiveMsg msg = JSONObject.parseObject(message, AmzWalmartPromotionMessageReceiveMsg.class);
        if (StringUtils.isEmpty(msg.getFlashDealsName())) {
            log.error("Received message from walmart promotion service without flashDealsName");
            return;
        }
        if (StringUtils.isEmpty(msg.getStatus())) {
            log.error("Received message from walmart promotion service without status");
            return;
        }
        if (StringUtils.isEmpty(msg.getStartEndDate())) {
            log.error("Received message from walmart promotion service without startEndDate");
            return;
        }
        if (StringUtils.isEmpty(msg.getAccount())) {
            log.error("Received message from walmart promotion service without account");
            return;
        }
        RLock lock = redissonClient.getLock(msg.getFlashDealsName() + "walmart");
        try {
            if (lock.tryLock(0, 1, TimeUnit.MINUTES)) {
                if (msg.getStatus().equalsIgnoreCase("Not started") || msg.getStatus().equalsIgnoreCase("Started")) {

                    List<AmzWalmartPromotionMessageReceiveMsg.AmzWalmartPromotionDetail> detailList = msg.getDetailList();
                    AmzPromotions promotions = getAmzPromotions(msg);
                    promotions.setPromotionsState(msg.getStatus().equalsIgnoreCase("Not started") ? AmzPromotionsStateEnum.APPROVED_AND_RUNNING.value() : AmzPromotionsStateEnum.APPROVED_AND_ENDED.value());
                    amzPromotionsService.saveOrUpdate(promotions);
                    if (CollectionUtil.isNotEmpty(detailList)) {
                        boolean itemIdIsNull = detailList.stream().anyMatch(t -> StringUtils.isEmpty(t.getItemId()));
                        if (itemIdIsNull) {
                            log.error("msg.getFlashDealsName:{},msg.getStatus:{},Received message from walmart promotion service with null itemId", msg.getFlashDealsName(), msg.getStatus());
                        } else {
                            List<String> asinList = detailList.stream().map(AmzWalmartPromotionMessageReceiveMsg.AmzWalmartPromotionDetail::getItemId).collect(Collectors.toList());
                            List<AmzPromotionsSkuDetail> detailListFromDb = amzPromotionsSkuDetailService.lambdaQuery().eq(AmzPromotionsSkuDetail::getPromotionsId, promotions.getId()).in(AmzPromotionsSkuDetail::getAsin, asinList).list();
                            List<AmzPromotionsSkuDetail> skuDetailList = new ArrayList<>();
                            for (AmzWalmartPromotionMessageReceiveMsg.AmzWalmartPromotionDetail detail : detailList) {

                                AmzPromotionsSkuDetail skuDetail = new AmzPromotionsSkuDetail();
                                skuDetail.setShopName(promotions.getShopName());
                                skuDetail.setAsin(detail.getItemId());
                                if (promotions.getShopId() != null && StringUtils.isNotEmpty(skuDetail.getAsin())) {
                                    List<AmzPromotionsSkuDetail> details = amzPromotionAccountMapper.selectPromotionSellerSkuByShopId(promotions.getShopId().intValue(), new String[]{skuDetail.getAsin()});
                                    if (CollectionUtil.isNotEmpty(details)) {
                                        skuDetail.setParentAsin(details.get(0).getParentAsin());
                                        skuDetail.setImage(details.get(0).getImage());
                                        skuDetail.setSellerSku(details.get(0).getSellerSku());
                                        skuDetail.setTitle(details.get(0).getTitle());
                                    }
                                }
                                skuDetail.setOrganizationId(promotions.getOrganizationId());
                                skuDetail.setPromotionsId(promotions.getId());
                                skuDetail.setFlag("$");
                                skuDetail.setRegPrice(detail.getRegPrice());
                                skuDetail.setDiscountPrice(StringUtils.isEmpty(detail.getPromoPrice()) ? null : new BigDecimal(detail.getPromoPrice()));
                                skuDetail.setSuggestedPrice(detail.getSuggestedPrice());
                                skuDetail.setPromoPriceStatus(detail.getPromoPriceStatus());
                                skuDetail.setAvailableInventory(StringUtils.isEmpty(detail.getAvailableInventory()) ? null : Integer.parseInt(detail.getAvailableInventory()));
                                skuDetail.setForecastQuantity(StringUtils.isEmpty(detail.getForecastQuantity()) ? null : Integer.parseInt(detail.getForecastQuantity()));
                                skuDetail.setExclusivePricing(detail.getExclusivePricing());
                                skuDetail.setFastShipping(detail.getFastShipping());
                                skuDetail.setWfsPromo(detail.getWfsPromo());
                                skuDetail.setSubmissionStatus(detail.getSubmissionStatus());
                                skuDetail.setReasonForApprovalOrRejection(detail.getReasonForApprovalOrRejection());
                                skuDetail.setWalmartAsin(detail.getWalmartAsin());
                                skuDetail.setDealQuality(detail.getDealQuality());
                                skuDetail.setDealQualityFeedBack(detail.getDealQualityFeedback());
                                if (CollectionUtil.isNotEmpty(detailListFromDb)) {
                                    detailListFromDb.stream().filter(t -> t.getAsin().equals(detail.getItemId()) && t.getPromotionsId().equals(promotions.getId())).findFirst().ifPresent(t -> skuDetail.setId(t.getId()));
                                }
                                skuDetailList.add(skuDetail);
                            }
                            List<List<AmzPromotionsSkuDetail>> lists = ListUtils.partition(skuDetailList, 10);
                            lists.forEach(t -> amzPromotionsSkuDetailService.saveOrUpdateBatch(t));

                        }
                    } else {
                        log.error("msg.getFlashDealsName:{},msg.getStatus:{},Received message from walmart promotion service with empty detailList", msg.getFlashDealsName(), msg.getStatus());
                    }

                } else {
                    log.error("message:{}-------Received message from walmart promotion service with unknown status:{}", message, msg.getStatus());
                }
            }
        } catch (Exception e) {
            log.error("message:{}-------Received message from walmart promotion service with error:{}", message, e.getMessage());
            throw new ErpCommonException("接收walmart消息异常");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }


    private AmzPromotions getAmzPromotions(AmzWalmartPromotionMessageReceiveMsg msg) {
        AmzPromotions promotions = new AmzPromotions();
        promotions.settingDefaultSystemCreate();
        promotions.setCreatedAt(DateUtil.UtcToPacificDate(promotions.getCreatedAt()));
        promotions.settingDefaultSystemUpdate();
        promotions.setPromotionsType(AmzPromotionsTypeEnum.BEST_DEAL.value());
        promotions.setPromotionsName(msg.getFlashDealsName());
        promotions.setBeginTime(DateUtils.parseDate(addHms(msg.getStartEndDate(), true), DateUtils.DEFAULT_TIME_FORMAT));
        promotions.setEndTime(DateUtils.parseDate(addHms(msg.getStartEndDate(), false), DateUtils.DEFAULT_TIME_FORMAT));
        promotions.setSubmissionDueDate(getDate(msg.getSubmissionDueDate()));
        promotions.setEnrollment(msg.getEnrollment());
        promotions.setChannel(3);
        promotions.setApprovalStatus(AmzPromotionApprovalEnum.APPROVED.getLabel());
        promotions.setSubShopBack(true);
        if (StringUtils.isNotEmpty(msg.getAccount())) {
            Account account = accountService.list(new LambdaQueryWrapper<Account>().eq(Account::getTitle, msg.getAccount()).eq(Account::getType, "walmart")).stream().findFirst().orElse(null);
            if (account == null) {
                log.error("msg.getFlashDealsName:{},msg.getStatus:{},Received message from walmart promotion service with unknown account:{}", msg.getFlashDealsName(), msg.getStatus(), msg.getAccount());
                throw new ErpCommonException("Received message from walmart promotion service with unknown account");
            } else {
                promotions.setShopId(account.getId().longValue());
                promotions.setShopName(account.getAccountInit());
                promotions.setOrganizationId(account.getOrgId());
                AccountWithCountry country = amzPromotionsMapper.selectAccountWithCountry(account.getId().longValue());
                promotions.setCountry(country != null ? country.getNameCn() : null);
            }
        }

        AmzPromotions one = amzPromotionsService.getOne(new LambdaQueryWrapper<AmzPromotions>().eq(AmzPromotions::getPromotionsName, msg.getFlashDealsName()).eq(AmzPromotions::getChannel, 3));
        if (one != null) {
            promotions.setId(one.getId());
        }
        return promotions;
    }

    private String getDate(String date) {
        if (StringUtils.isEmpty(date)) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("MMM d, yyyy", Locale.ENGLISH);
        try {
            Integer year = DateUtils.getYear(DateUtils.getNowDate());
            Date date1 = simpleDateFormat.parse(date + ", " + year);
            String format = new SimpleDateFormat("yyyy-MM-dd").format(date1);
            return format;
        } catch (ParseException e) {
            throw new CustomException("Invalid date format:" + date);
        }
    }

    private String addHms(String date, Boolean flag) {
        if (flag == null) {
            return getDate(date);
        } else {
            String[] split = date.split("-");
            if (flag) {
                return getDate(split[0].trim()) + " 00:00:00";
            } else {
                return getDate(split[1].trim()) + " 23:59:59";
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void temuPromotionMessageReceive(String msgStr) {
        Date nowDate = DateUtil.UtcToPacificDate(DateUtils.getNowDate());
        if (StringUtils.isEmpty(msgStr)) {
            log.error("Received empty message from temu promotion service");
            return;
        }
        log.info("Received message from temu promotion service:{} ", msgStr);
        AmzTemuPromotionMessageReceiveMsg msg = JSONObject.parseObject(msgStr, AmzTemuPromotionMessageReceiveMsg.class);
        if (CollectionUtil.isEmpty(msg.getSkcList()) || StringUtils.isEmpty(msg.getSkcList().get(0).getSkcId())) {
            log.error("Received message from temu promotion service without skcList:{}", msgStr);
            return;
        }
        if (StringUtils.isEmpty(msg.getInvitationId())) {
            log.error("Received message from temu promotion service without invitationId,msgStr:{}", msgStr);
            return;
        }

        if (StringUtils.isEmpty(msg.getPromotionId())) {
            log.error("Received message from temu promotion service without promotionId:{}", msgStr);
            return;
        }
        /*if (StringUtils.isEmpty(msg.getShopName())) {
            log.error("Received message from temu promotion service without shopName");
            return;
        }*/
        RLock lock = redissonClient.getLock(msg.getInvitationId() + msg.getSkcList().get(0).getSkcId() + "temu");
        try {
            if (lock.tryLock(0, 1, TimeUnit.MINUTES)) {
                marPromotionsReportProductService.refreshTemuProductSingUpStatus(msgStr); //异步刷新提报商品报名状态

//                Account account = accountService.getOne(Wrappers.lambdaQuery(Account.class).eq(Account::getSellerId, msg.getMallId()).eq(Account::getType, "temu").eq(Account::getActive, "Y"));
                Account account = marTemuPromotionMapper.selectAccountBySellerIdAndSkcId(msg.getMallId(), msg.getSkcList().get(0).getSkcId());
                if (account == null) {
                    log.error("Received message from temu promotion service with unknown shopName:{}", msg.getShopName());
                    return;
                }
                AmzPromotions promotions = amzPromotionsMapper.selectAmzPromotionByInvitationIdAndParentAsin(msg.getInvitationId(), msg.getSkcList().get(0).getSkcId(), account.getId());
                if (promotions != null) {
                    log.error("createData update,msg.getInvitationId:{},msg.getSkcId:{}", msg.getInvitationId(), msg.getSkcList().get(0).getSkcId());
                } else {
                    log.error("insert data,msg.getPromotionId:{}", msg.getPromotionId());
                }
                Integer currentRemainStock = promotions == null ? null : promotions.getRemainStock();
                AccountWithCountry country = amzPromotionsMapper.selectAccountWithCountry(account.getId().longValue());
                AmzPromotions amzPromotions = new AmzPromotions();
                if (promotions != null) {
                    amzPromotions.setId(promotions.getId());
                    amzPromotions.settingDefaultSystemUpdate();
                } else {
                    amzPromotions.settingDefaultSystemCreate();
                    amzPromotions.settingDefaultSystemUpdate();
                    amzPromotions.setPromotionsState(AmzPromotionsStateEnum.APPROVED.value());
                    amzPromotions.setCreatedAt(DateUtil.UtcToPacificDate(DateUtils.getNowDate()));
                    amzPromotions.setApprovalStatus("已通过");
                }

                amzPromotions.setInvitationId(msg.getInvitationId());
                amzPromotions.setInvitationType(msg.getInvitationType());
                amzPromotions.setPromotionsId(msg.getPromotionId());
                amzPromotions.setProductId(msg.getProductId());
                amzPromotions.setPromotionsName(msg.getPromotionName());
                amzPromotions.setCountry(country != null ? country.getNameCn() : null);
                amzPromotions.setShopName(account.getAccountInit());
                amzPromotions.setPromotionsType(AmzPromotionsTypeEnum.BEST_DEAL.value());
                amzPromotions.setBeginTime(DateUtil.UtcToPacificDate(DateUtils.timestampToDate(msg.getBeginTime(), DateUtils.DEFAULT_TIME_FORMAT)));
                amzPromotions.setEndTime(DateUtil.UtcToPacificDate(DateUtils.timestampToDate(msg.getEndTime(), DateUtils.DEFAULT_TIME_FORMAT)));
                boolean state = promotions != null && promotions.getPlatformStatus() != null && (TemuPromotionsStateEnum.PENDING.getState().equals(promotions.getPlatformStatus()) || TemuPromotionsStateEnum.MODIFYING.getState().equals(promotions.getPlatformStatus()));
                if (state) {
                    amzPromotions.setLastSkcStock(msg.getNum());
                } else {
                    amzPromotions.setSkcStock(msg.getNum());
                    amzPromotions.setLastSkcStock(msg.getNum());
                }
                if (promotions == null || (promotions.getPlatformStatus() == null || TemuPromotionsStateEnum.NOT_STARTED.getState().equals(promotions.getPlatformStatus()) || TemuPromotionsStateEnum.RUNNING.getState().equals(promotions.getPlatformStatus()) || TemuPromotionsStateEnum.SIGNUP_FAILED.getState().equals(promotions.getPlatformStatus()) || TemuPromotionsStateEnum.HASED_RETURN.getState().equals(promotions.getPlatformStatus()) || TemuPromotionsStateEnum.SYNCING.getState().equals(promotions.getPlatformStatus()))) {
                    if (CollectionUtil.isNotEmpty(msg.getAssignSessionList()) && msg.getAssignSessionList().get(0).getSessionStatus() != null) {
                        Integer sessionStatus = msg.getAssignSessionList().get(0).getSessionStatus();
                        if (sessionStatus.equals(4)) {
                            amzPromotions.setPlatformStatus(TemuPromotionsStateEnum.SIGNUP_FAILED.getState());
                        } else if (sessionStatus.equals(6)) {
                            amzPromotions.setPlatformStatus(TemuPromotionsStateEnum.HASED_RETURN.getState());
                        } else {
                            amzPromotions.setPlatformStatus(TemuPromotionsStateEnum.getStateByDate(nowDate, amzPromotions.getBeginTime(), amzPromotions.getEndTime()).getState());
                        }
                    } else {
                        amzPromotions.setPlatformStatus(TemuPromotionsStateEnum.getStateByDate(nowDate, amzPromotions.getBeginTime(), amzPromotions.getEndTime()).getState());
                    }

                }
                amzPromotions.setChannel(4);
                amzPromotions.setFlag(MarPromotionCountryCurrencyEnum.getCurrencyFlagByCountryCode(country == null ? null : country.getCountryCode()));
                amzPromotions.setShopId(account.getId().longValue());
                amzPromotions.setCurrency(MarPromotionCountryCurrencyEnum.getCurrencyByCountryCode(country == null ? null : country.getCountryCode()));
                amzPromotions.setOrganizationId(account.getOrgId());
                amzPromotions.setSubShopBack(true);
                amzPromotions.setSubmissionDueDate(DateUtil.convertDateToString(DateUtil.UtcToPacificDate(DateUtils.timestampToDate(msg.getSubmitAt(), DateUtils.DEFAULT_TIME_FORMAT)), DateUtils.DEFAULT_TIME_FORMAT));

                amzPromotions.setRemainStock(msg.getRemainStock());
                if (promotions != null && promotions.getSkcStock() != null && promotions.getRemainStock() != null && amzPromotions.getSkcStock() != null && amzPromotions.getRemainStock() != null
                        && ((amzPromotions.getSkcStock() - amzPromotions.getRemainStock()) - (promotions.getSkcStock() - promotions.getRemainStock()) < 0)) {
                    log.error("半托管旧促销接收消息库存错误--{}", msgStr);
                    return;
                }

                amzPromotionsService.saveOrUpdate(amzPromotions);
                threadPoolTaskExecutor.execute(() -> this.adviceOperator(msg, account, amzPromotions.getPlatformStatus(), currentRemainStock));
                List<AmzPromotionsSkuDetail> skuDetailList = new ArrayList<>();
                List<AmzTemuPromotionMessageReceiveMsg.SkcList> skcList = msg.getSkcList();
                for (AmzTemuPromotionMessageReceiveMsg.SkcList skcInfo : skcList) {
                    String skcId = skcInfo.getSkcId();
                    List<AmzTemuPromotionMessageReceiveMsg.SkuList> list = skcInfo.getSkuList();
                    for (AmzTemuPromotionMessageReceiveMsg.SkuList skuInfo : list) {
                        AmzPromotionsSkuDetail skuDetail = new AmzPromotionsSkuDetail();
                        skuDetail.setParentAsin(skcId);
                        skuDetail.setPromotionsId(amzPromotions.getId());
                        skuDetail.setPromotionsBackId(msg.getPromotionId());
                        skuDetail.setOrganizationId(amzPromotions.getOrganizationId());
                        skuDetail.setShopName(amzPromotions.getShopName());
                        skuDetail.setShopId(amzPromotions.getShopId());
                        skuDetail.setAsin(skuInfo.getSellerSku());
                        skuDetail.setSellerSku(skuInfo.getSellerSku());
                        skuDetail.setExtCode(skuInfo.getExtCode());
                        List<AmzTemuPromotionMessageReceiveMsg.SitePriceList> sitePriceList = skuInfo.getSitePriceList();
                        boolean noInsert = false;
                        if (CollectionUtil.isEmpty(sitePriceList) || sitePriceList.get(0).getActivityPrice() == null) {
                            if (skuInfo.getActivityPrice() == null) {
                                noInsert = true;
                            }
                        } else {
                            skuInfo.setActivityPrice(sitePriceList.get(0).getActivityPrice());
                        }
                        if (CollectionUtil.isEmpty(sitePriceList) || sitePriceList.get(0).getDailyPrice() == null) {
                            if (skuInfo.getDailyPrice() == null) {
                                noInsert = true;
                            }
                        } else {
                            skuInfo.setDailyPrice(sitePriceList.get(0).getDailyPrice());
                        }
                        if (!noInsert) {
                            AmzPromotionsSkuDetail skuOne = amzPromotionsSkuDetailService.getOne(Wrappers.lambdaQuery(AmzPromotionsSkuDetail.class).eq(AmzPromotionsSkuDetail::getPromotionsId, amzPromotions.getId()).eq(AmzPromotionsSkuDetail::getSellerSku, skuInfo.getSellerSku()));
                            if (skuOne != null) {
                                skuDetail.setId(skuOne.getId());
                                skuDetail.settingDefaultSystemUpdate();
                            } else {
                                skuDetail.settingDefaultSystemCreate();
                                skuDetail.settingDefaultSystemUpdate();
                            }
                            skuDetail.setPrice(skuInfo.getDailyPrice().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                            if (state) {
                                skuDetail.setLastDiscountPrice(skuInfo.getActivityPrice().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                            } else {
                                skuDetail.setDiscountPrice(skuInfo.getActivityPrice().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                                skuDetail.setLastDiscountPrice(skuDetail.getDiscountPrice());
                            }
                            skuDetail.setSiteId(sitePriceList.get(0).getSiteId());
                            skuDetail.setDiscountFlag("$");
                            skuDetail.setDiscount((skuInfo.getDailyPrice().subtract(skuInfo.getActivityPrice())).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                            skuDetail.setPerFunding("$" + skuDetail.getDiscount().toString());
                            List<AmzPromotionsSkuDetail> details = amzPromotionAccountMapper.selectPromotionSellerSkuByShopId(amzPromotions.getShopId().intValue(), new String[]{skuDetail.getAsin()});
                            if (CollectionUtil.isNotEmpty(details)) {
                                skuDetail.setImage(details.get(0).getImage());
                                skuDetail.setTitle(details.get(0).getTitle());
                            }
                            skuDetail.setNum(msg.getNum());
                            skuDetailList.add(skuDetail);
                            amzPromotionsSkuDetailService.saveOrUpdate(skuDetail);
                        }

                        AmzPromotionFeeDateHisRecord hisRecord = buildDateHisRecord(amzPromotions, skuDetail, nowDate, skuInfo, msg);
                        if (hisRecord != null) {
                            amzPromotionFeeDateHisRecordService.saveOrUpdate(hisRecord);
                        }
                    }
                }

                /*if (CollectionUtil.isNotEmpty(skuDetailList)) {
                    amzPromotionsSkuDetailService.saveOrUpdateBatch(skuDetailList);
                }*/

                threadPoolTaskExecutor.execute(() -> recordSyncStatusStockAndPriceChange(amzPromotions, skuDetailList, promotions));
            }
        } catch (Exception e) {
            log.error("Received message from temu promotion service with error:{}", e.getMessage());
            throw new ErpCommonException("接收temu消息异常");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void adviceOperator(AmzTemuPromotionMessageReceiveMsg msg, Account account, Integer platformStatus, Integer currentRemainStock) {
        try {
            List<AmzTemuPromotionMessageReceiveMsg.SkuList> skuList = msg.getSkcList().get(0).getSkuList();
            List<String> collect = skuList.stream().map(AmzTemuPromotionMessageReceiveMsg.SkuList::getSellerSku).distinct().collect(Collectors.toList());
            List<AmzPromotionCheck> amzPromotionChecks = amzPromotionCheckMapper.selectListByShopIdOrAsinOrSellerSku(account.getId().longValue(), collect.toArray(new String[0]), null);
            boolean stateFlag = TemuPromotionsStateEnum.NOT_STARTED.getState().equals(platformStatus)
                    || TemuPromotionsStateEnum.RUNNING.getState().equals(platformStatus)
                    || TemuPromotionsStateEnum.MODIFYING.getState().equals(platformStatus)
                    || TemuPromotionsStateEnum.MODIFY_FAILED.getState().equals(platformStatus);
            if (msg.getRemainStock() != null && msg.getRemainStock().equals(0) && stateFlag) {

                if (currentRemainStock != null && currentRemainStock.equals(0)) {
                    return;
                }
                String advice = String.format("促销ID:[%s],店铺:[%s],活动:[%s],SKC:[%s],剩余库存数量:[%s]", msg.getPromotionId(),
                        account.getTitle(), msg.getPromotionName(), msg.getSkcList().get(0).getSkcId(),
                        msg.getRemainStock());
                log.info("temu-促销消息:{},通知运营:{}", msg, advice);
                if (CollectionUtil.isNotEmpty(amzPromotionChecks) && amzPromotionChecks.get(0).getOperationUserId() != null) {
                    UserEntity byId = SpringUtils.getBean(UserService.class).getById(amzPromotionChecks.get(0).getOperationUserId());
                    if (byId != null && StringUtils.isNotEmpty(byId.getPhone())) {
                        String pattern = "^1[3-9]\\d{9}$";
                        if (byId.getPhone().matches(pattern)) {
                            QywxUtil.wxMessageSend(advice, byId.getPhone());
                        } else {
                            log.error("通知内容:{},运营:{},手机号:{}校验不合格", advice, byId.getName(), byId.getPhone());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("temu-促销消息:{},通知运营失败:{}", msg, e.getMessage());
        }
    }

    private void adviceOperatorLimit(AmzTemuPromotionMessageLimitReceiveMsg msg, Account account, Integer platformStatus, Integer currentRemainStock) {
        try {
            List<AmzTemuPromotionMessageLimitReceiveMsg.SkuList> skuList = msg.getSkcList().get(0).getSkuList();
            List<String> collect = skuList.stream().map(AmzTemuPromotionMessageLimitReceiveMsg.SkuList::getSellerSku).distinct().collect(Collectors.toList());
            List<AmzPromotionCheck> amzPromotionChecks = amzPromotionCheckMapper.selectListByShopIdOrAsinOrSellerSku(account.getId().longValue(), collect.toArray(new String[0]), null);
            boolean stateFlag = TemuPromotionsStateEnum.NOT_STARTED.getState().equals(platformStatus)
                    || TemuPromotionsStateEnum.RUNNING.getState().equals(platformStatus)
                    || TemuPromotionsStateEnum.MODIFYING.getState().equals(platformStatus)
                    || TemuPromotionsStateEnum.MODIFY_FAILED.getState().equals(platformStatus);
            if (msg.getRemainStock() != null && msg.getRemainStock().equals(0) && stateFlag) {
                if (currentRemainStock != null && currentRemainStock.equals(0)) {
                    return;
                }
                String advice = String.format("活动类型:[%s],店铺:[%s],SKC:[%s],剩余库存数量:[%s]", MarPromotionInvitationTypeEnum.getType(msg.getInvitationType(), account.getSaleChannel()).getDesc(),
                        account.getTitle(), msg.getSkcList().get(0).getSkcId(),
                        msg.getRemainStock());
                log.info("temu--促销消息:{},通知运营:{}", msg, advice);
                if (CollectionUtil.isNotEmpty(amzPromotionChecks) && amzPromotionChecks.get(0).getOperationUserId() != null) {
                    UserEntity byId = SpringUtils.getBean(UserService.class).getById(amzPromotionChecks.get(0).getOperationUserId());
                    if (byId != null && StringUtils.isNotEmpty(byId.getPhone())) {
                        String pattern = "^1[3-9]\\d{9}$";
                        if (byId.getPhone().matches(pattern)) {
                            QywxUtil.wxMessageSend(advice, byId.getPhone());
                        } else {
                            log.error("通知内容:{},运营:{},手机号:{}校验不合格", advice, byId.getName(), byId.getPhone());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("temu-促销消息:{},通知运营失败:{}", msg, e.getMessage());
        }
    }

    private AmzPromotionFeeDateHisRecord buildDateHisRecord(AmzPromotions amzPromotions, AmzPromotionsSkuDetail skuDetail, Date nowDate, AmzTemuPromotionMessageReceiveMsg.SkuList skuInfo, AmzTemuPromotionMessageReceiveMsg msg) {

        if (DateUtil.compareDate(DateUtil.convertDateToString(amzPromotions.getEndTime(), DateUtils.DEFAULT_DATE_FORMAT), DateUtil.convertDateToString(DateUtil.addDate(nowDate, -1), DateUtils.DEFAULT_DATE_FORMAT)) < 0) {
            log.error("Received message from temu promotion service with expired promotion,promotionId:{}", amzPromotions.getPromotionsId());
            return null;
        }
        LambdaQueryWrapper<AmzPromotionFeeDateHisRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AmzPromotionFeeDateHisRecord::getPromotionId, amzPromotions.getId())
                .eq(AmzPromotionFeeDateHisRecord::getSkcId, skuDetail.getParentAsin())
                .eq(AmzPromotionFeeDateHisRecord::getSellerSku, skuDetail.getSellerSku())
                .ge(AmzPromotionFeeDateHisRecord::getDate, DateUtil.convertDateToString(nowDate, DateUtils.DEFAULT_DATE_FORMAT))
                .lt(AmzPromotionFeeDateHisRecord::getDate, DateUtil.convertDateToString(DateUtil.addDate(nowDate, 1), DateUtils.DEFAULT_DATE_FORMAT))

                .orderByDesc(AmzPromotionFeeDateHisRecord::getDate)
                .last("limit 1");
        AmzPromotionFeeDateHisRecord one = amzPromotionFeeDateHisRecordService.getOne(wrapper);
        AmzPromotionFeeDateHisRecord record = new AmzPromotionFeeDateHisRecord();
        if (one != null) {
            record.setId(one.getId());
            record.settingDefaultSystemUpdate();
        } else {
            record.settingDefaultSystemCreate();
        }

        record.setActivityType(amzPromotions.getInvitationType());
        record.setPromotionId(amzPromotions.getId());
        record.setAmzSkuDetailId(skuDetail.getId());
        record.setAsin(skuDetail.getAsin());
        record.setSellerSku(skuDetail.getSellerSku());
        record.setShopId(amzPromotions.getShopId());
        record.setDailyPrice(skuInfo.getDailyPrice());
        record.setActivityPrice(skuInfo.getActivityPrice());
        record.setSkcStock(msg.getNum());
        record.setRemainStock(msg.getRemainStock());
        record.setSkcId(skuDetail.getParentAsin());
        record.setDate(nowDate);
        return record;
    }


    private AmzPromotionFeeDateHisRecord buildDateHisRecordLimit(AmzPromotions amzPromotions, AmzPromotionsSkuDetail skuDetail, Date nowDate, AmzTemuPromotionMessageLimitReceiveMsg.SkuList skuInfo, AmzTemuPromotionMessageLimitReceiveMsg msg) {

        if (DateUtil.compareDate(DateUtil.convertDateToString(amzPromotions.getEndTime(), DateUtils.DEFAULT_DATE_FORMAT), DateUtil.convertDateToString(DateUtil.addDate(nowDate, -1), DateUtils.DEFAULT_DATE_FORMAT)) < 0) {
            log.error("Received message from temu promotion service with expired promotion,promotionId:{}", amzPromotions.getPromotionsId());
            return null;
        }
        LambdaQueryWrapper<AmzPromotionFeeDateHisRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AmzPromotionFeeDateHisRecord::getPromotionId, amzPromotions.getId())
                .eq(AmzPromotionFeeDateHisRecord::getSkcId, skuDetail.getParentAsin())
                .eq(AmzPromotionFeeDateHisRecord::getSellerSku, skuDetail.getSellerSku())
                .ge(AmzPromotionFeeDateHisRecord::getDate, DateUtil.convertDateToString(nowDate, DateUtils.DEFAULT_DATE_FORMAT))
                .lt(AmzPromotionFeeDateHisRecord::getDate, DateUtil.convertDateToString(DateUtil.addDate(nowDate, 1), DateUtils.DEFAULT_DATE_FORMAT))

                .orderByDesc(AmzPromotionFeeDateHisRecord::getDate)
                .last("limit 1");
        AmzPromotionFeeDateHisRecord one = amzPromotionFeeDateHisRecordService.getOne(wrapper);
        AmzPromotionFeeDateHisRecord record = new AmzPromotionFeeDateHisRecord();
        if (one != null) {
            record.setId(one.getId());
            record.settingDefaultSystemUpdate();
        } else {
            record.settingDefaultSystemCreate();
        }
        record.setActivityType(amzPromotions.getInvitationType());
        record.setPromotionId(amzPromotions.getId());
        record.setAmzSkuDetailId(skuDetail.getId());
        record.setAsin(skuDetail.getAsin());
        record.setSellerSku(skuDetail.getSellerSku());
        record.setShopId(amzPromotions.getShopId());
        record.setDailyPrice(skuInfo.getDailyPrice());
        record.setActivityPrice(skuInfo.getActivityPrice());
        record.setSkcStock(msg.getNum());
        record.setRemainStock(msg.getRemainStock());
        record.setSkcId(skuDetail.getParentAsin());
        record.setDate(nowDate);
        return record;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void temuPromotionMessageLimitReceive(String msgStr) {
        Date nowDate = DateUtil.UtcToPacificDate(DateUtils.getNowDate());
        if (StringUtils.isEmpty(msgStr)) {
            log.error("Received empty message from temu limit promotion service");
            return;
        }
        log.info("Received message from temu limit promotion service:{} ", msgStr);
        AmzTemuPromotionMessageLimitReceiveMsg msg = JSONObject.parseObject(msgStr, AmzTemuPromotionMessageLimitReceiveMsg.class);
        if (CollectionUtil.isEmpty(msg.getSkcList()) || StringUtils.isEmpty(msg.getSkcList().get(0).getSkcId())) {
            log.error("Received message from temu limit promotion service without skcList:{}", msgStr);
            return;
        }


        if (msg.getInvitationType() == null) {
            log.error("Received message from temu limit promotion service without invitationType:{}", msgStr);
            return;
        }
        if (StringUtils.isEmpty(msg.getMallId())) {
            log.error("Received message from temu limit promotion service without mallId:{}", msgStr);
            return;
        }
        if ((StringUtils.isEmpty(msg.getGoodsId()))) {
            log.error("Received message from temu limit promotion service without goodsId:{}", msgStr);
            return;
        }
        if (msg.getProductId() == null) {
            log.error("Received message from temu limit promotion service without productId:{}", msgStr);
            return;
        }
        if (StringUtils.isEmpty(msg.getEnrollId())) {
            log.error("Received message from temu limit promotion service without enrollId:{}", msgStr);
            return;
        }
        Account account = marTemuPromotionMapper.selectAccountBySellerIdAndSkcId(msg.getMallId(), msg.getSkcList().get(0).getSkcId());
        if (account == null) {
            log.error("Received message from temu limit promotion service with invalid mallId:{}", msgStr);
            return;
        }
        boolean shortActive = MarPromotionInvitationTypeEnum.assertOldActive(msg.getInvitationType(), account.getSaleChannel()).equals(1);
        if (shortActive && StringUtils.isEmpty(msg.getActivityThematicId())) {
            log.error("大促活动Received message from temu limit promotion service without activityThematicId:{}", msgStr);
            return;
        }


        if (CollectionUtil.isEmpty(msg.getAssignSessionList())) {
            log.info("Received message from temu limit promotion service without assignSessionList:{}", msgStr);
            return;
        }
        if (MarPromotionInvitationTypeEnum.assertOldActive(msg.getInvitationType(), account.getSaleChannel()).equals(2)) {
            if (CollectionUtil.isEmpty(msg.getAssignSessionList())) {
                log.error("Received message from temu limit promotion service without assignSessionList:{}", msgStr);
                return;
            }
            if (msg.getAssignSessionList().stream().anyMatch(t -> t.getSessionId() == null)) {
                log.error("Received message from temu limit promotion service with null assignSessionList:{}", msgStr);
                return;
            }
        }

        String platformActiveId = msg.getActivityThematicId();

        String sessionIdsStr = null;
        if (CollectionUtil.isNotEmpty(msg.getAssignSessionList())) {
            msg.getAssignSessionList().sort(Comparator.comparing(AmzTemuPromotionMessageLimitReceiveMsg.AssignSessionList::getSessionId));
            sessionIdsStr = msg.getAssignSessionList().stream().map(t -> t.getSessionId().toString()).distinct().collect(Collectors.joining(","));
        }

        String baseKey = msg.getMallId() + msg.getSkcList().get(0).getSkcId() + "temu" + msg.getInvitationType().toString();
        String key = shortActive ? (baseKey + platformActiveId) : (baseKey + msg.getProductId().toString() + sessionIdsStr);
        RLock lock = redissonClient.getLock(key);
        log.info("Received message:{}, from temu limit promotion service with lock:{}", msgStr, key);
        try {
            if (lock.tryLock(0, 1, TimeUnit.MINUTES)) {
                marPromotionsReportProductService.refreshTemuProductSingUpStatus(msgStr); //异步刷新提报商品报名状态

                AmzPromotions promotions = null;
                if (shortActive) {
                    promotions = amzPromotionsMapper.selectAmzPromotionByInvitationIdAndParentAsin(platformActiveId, msg.getSkcList().get(0).getSkcId(), account.getId());
                    if (promotions == null) {
                        log.info("根据平台活动id和skcID和店铺查询不到活动信息推测可能是新活动或是后台直接创建活动,{}", msgStr);
                        if (CollectionUtil.isNotEmpty(msg.getAssignSessionList())) {
                            promotions = amzPromotionsMapper.selectTemuPromotionByShopIdAndInvitationTypeAndSkcIdAndSessionId(account.getId(), msg.getInvitationType(), msg.getSkcList().get(0).getSkcId(), msg.getAssignSessionList().stream().map(AmzTemuPromotionMessageLimitReceiveMsg.AssignSessionList::getSessionId).distinct().collect(Collectors.toList()));
                            if (promotions == null) {
                                log.error("根据店铺id,活动类型,skcId,会话id查询不到活动信息,为新活动{}", msgStr);
                            } else {
                                log.info("根据店铺id,活动类型,skcId,会话id查询到活动信息,为后台直接创建的老活动{}", msgStr);
                            }
                        }

                    }
                } else {
                    promotions = amzPromotionsMapper.selectTemuPromotionByShopIdAndInvitationTypeAndSkcIdAndSessionId(account.getId(), msg.getInvitationType(), msg.getSkcList().get(0).getSkcId(), msg.getAssignSessionList().stream().map(AmzTemuPromotionMessageLimitReceiveMsg.AssignSessionList::getSessionId).distinct().collect(Collectors.toList()));
                }

                if (promotions != null) {
                    log.error("hisData update,msg.getPromotionId:{}", msgStr);
                } else {
                    log.error("insert data,msg.getPromotionId:{}", msgStr);
                }
                Integer currentRemainStock = promotions != null ? promotions.getRemainStock() : null;
                AccountWithCountry country = amzPromotionsMapper.selectAccountWithCountry(account.getId().longValue());
                AmzPromotions amzPromotions = new AmzPromotions();
                if (promotions != null) {
                    amzPromotions.setId(promotions.getId());
                    amzPromotions.settingDefaultSystemUpdate();
                    if (shortActive && StringUtils.isEmpty(promotions.getInvitationId())) {
                        amzPromotions.setInvitationId(platformActiveId);
                    }
                } else {
                    amzPromotions.settingDefaultSystemCreate();
                    amzPromotions.settingDefaultSystemUpdate();
                    amzPromotions.setPromotionsState(AmzPromotionsStateEnum.APPROVED.value());
                    amzPromotions.setCreatedAt(DateUtil.UtcToPacificDate(DateUtils.getNowDate()));
                    amzPromotions.setApprovalStatus("已通过");
                    String promotionId = DateUtil.convertDateToString(DateUtil.pacificTimeToBjDate(DateUtil.UtcToPacificDate(DateUtils.getNowDate())), "yyyyMMddHHmmss") + msg.getInvitationType().toString() + msg.getSkcList().get(0).getSkcId();
                    amzPromotions.setPromotionsId(promotionId);
                    if (StringUtils.isNotEmpty(platformActiveId)) {
                        amzPromotions.setInvitationId(platformActiveId);
                    }
                }
                amzPromotions.setPromotionsName(msg.getActivityThematicName());
                if (CollectionUtil.isNotEmpty(msg.getAssignSessionList())) {
                    amzPromotions.setSessionIds(msg.getAssignSessionList().stream().map(t -> t.getSessionId().toString()).distinct().collect(Collectors.joining(",")));
                }

                amzPromotions.setInvitationType(msg.getInvitationType());
                amzPromotions.setGoodsId(msg.getGoodsId());
                amzPromotions.setProductId(msg.getProductId());
                amzPromotions.setEnrollId(msg.getEnrollId());
                amzPromotions.setVersion(msg.getVersion());
                amzPromotions.setCountry(country != null ? country.getNameCn() : null);
                amzPromotions.setShopName(account.getAccountInit());
                amzPromotions.setPromotionsType(AmzPromotionsTypeEnum.BEST_DEAL.value());
                getBeginAndEndTime(msg.getAssignSessionList(), amzPromotions);
                boolean state = promotions != null && promotions.getPlatformStatus() != null && (TemuPromotionsStateEnum.PENDING.getState().equals(promotions.getPlatformStatus()) || TemuPromotionsStateEnum.MODIFYING.getState().equals(promotions.getPlatformStatus()));
                if (state) {
                    amzPromotions.setLastSkcStock(msg.getNum());
                } else {
                    amzPromotions.setSkcStock(msg.getNum());
                    amzPromotions.setLastSkcStock(msg.getNum());
                }
                if (promotions == null || (promotions.getPlatformStatus() == null || TemuPromotionsStateEnum.NOT_STARTED.getState().equals(promotions.getPlatformStatus()) || TemuPromotionsStateEnum.RUNNING.getState().equals(promotions.getPlatformStatus()) || TemuPromotionsStateEnum.SIGNUP_FAILED.getState().equals(promotions.getPlatformStatus()) || TemuPromotionsStateEnum.HASED_RETURN.getState().equals(promotions.getPlatformStatus()) || TemuPromotionsStateEnum.SYNCING.getState().equals(promotions.getPlatformStatus()))) {
                    if (CollectionUtil.isNotEmpty(msg.getAssignSessionList()) && msg.getAssignSessionList().get(0).getSessionStatus() != null) {
                        Integer sessionStatus = msg.getAssignSessionList().get(0).getSessionStatus();
                        if (sessionStatus.equals(4)) {
                            amzPromotions.setPlatformStatus(TemuPromotionsStateEnum.SIGNUP_FAILED.getState());
                        } else if (sessionStatus.equals(6)) {
                            amzPromotions.setPlatformStatus(TemuPromotionsStateEnum.HASED_RETURN.getState());
                        } else {
                            amzPromotions.setPlatformStatus(TemuPromotionsStateEnum.getStateByDate(nowDate, amzPromotions.getBeginTime(), amzPromotions.getEndTime()).getState());
                        }
                    } else {
                        amzPromotions.setPlatformStatus(TemuPromotionsStateEnum.getStateByDate(nowDate, amzPromotions.getBeginTime(), amzPromotions.getEndTime()).getState());
                    }

                }
                amzPromotions.setChannel(4);
                amzPromotions.setFlag(MarPromotionCountryCurrencyEnum.getCurrencyFlagByCountryCodeAndSaleChannel(country == null ? null : country.getCountryCode(),account.getSaleChannel()));
                amzPromotions.setShopId(account.getId().longValue());
                amzPromotions.setCurrency(MarPromotionCountryCurrencyEnum.getCurrencyByCountryCodeAndSaleChannel(country == null ? null : country.getCountryCode(),account.getSaleChannel()));
                amzPromotions.setOrganizationId(account.getOrgId());
                amzPromotions.setSubShopBack(true);


                amzPromotions.setRemainStock(msg.getRemainStock());

                /*if (promotions != null && promotions.getSkcStock() != null && promotions.getRemainStock() != null && amzPromotions.getSkcStock() != null && amzPromotions.getRemainStock() != null
                        && ((amzPromotions.getSkcStock() - amzPromotions.getRemainStock()) - (promotions.getSkcStock() - promotions.getRemainStock()) < 0)) {
                    log.error("半托管促销接收消息库存错误--{}", msgStr);
                    return;
                }*/
                amzPromotionsService.saveOrUpdate(amzPromotions);
                threadPoolTaskExecutor.execute(() -> this.adviceOperatorLimit(msg, account, amzPromotions.getPlatformStatus(), currentRemainStock));

                List<AmzPromotionsSkuDetail> skuDetailList = new ArrayList<>();
                List<AmzTemuPromotionMessageLimitReceiveMsg.SkcList> skcList = msg.getSkcList();
                for (AmzTemuPromotionMessageLimitReceiveMsg.SkcList skcInfo : skcList) {
                    String skcId = skcInfo.getSkcId();
                    List<AmzTemuPromotionMessageLimitReceiveMsg.SkuList> list = skcInfo.getSkuList();
                    for (AmzTemuPromotionMessageLimitReceiveMsg.SkuList skuInfo : list) {
                        AmzPromotionsSkuDetail skuDetail = new AmzPromotionsSkuDetail();
                        skuDetail.setParentAsin(skcId);
                        skuDetail.setPromotionsId(amzPromotions.getId());
                        skuDetail.setProductId(amzPromotions.getProductId());
                        skuDetail.setSiteId(CollectionUtil.isNotEmpty(msg.getSites()) ? msg.getSites().get(0).getSiteId() : null);
                        skuDetail.setOrganizationId(amzPromotions.getOrganizationId());
                        skuDetail.setShopName(amzPromotions.getShopName());
                        skuDetail.setShopId(amzPromotions.getShopId());
                        skuDetail.setAsin(skuInfo.getSellerSku());
                        skuDetail.setSellerSku(skuInfo.getSellerSku());
                        skuDetail.setExtCode(skuInfo.getExtCode());
                        List<AmzTemuPromotionMessageLimitReceiveMsg.SitePriceList> sitePriceList = skuInfo.getSitePriceList();
                        boolean noInsert = false;
                        if (CollectionUtil.isEmpty(sitePriceList) || sitePriceList.get(0).getActivityPrice() == null) {
                            if (skuInfo.getActivityPrice() == null) {
                                noInsert = true;
                            }
                        } else {
                            skuInfo.setActivityPrice(sitePriceList.get(0).getActivityPrice());
                        }
                        if (CollectionUtil.isEmpty(sitePriceList) || sitePriceList.get(0).getDailyPrice() == null) {
                            if (skuInfo.getDailyPrice() == null) {
                                noInsert = true;
                            }
                        } else {
                            skuInfo.setDailyPrice(sitePriceList.get(0).getDailyPrice());
                        }
                        if (!noInsert) {
                            AmzPromotionsSkuDetail skuOne = amzPromotionsSkuDetailService.getOne(Wrappers.lambdaQuery(AmzPromotionsSkuDetail.class).eq(AmzPromotionsSkuDetail::getPromotionsId, amzPromotions.getId()).eq(AmzPromotionsSkuDetail::getSellerSku, skuInfo.getSellerSku()));
                            if (skuOne != null) {
                                skuDetail.setId(skuOne.getId());
                                skuDetail.settingDefaultSystemUpdate();
                            } else {
                                skuDetail.settingDefaultSystemCreate();
                                skuDetail.settingDefaultSystemUpdate();
                            }
                            skuDetail.setPrice(skuInfo.getDailyPrice().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                            if (state) {
                                skuDetail.setLastDiscountPrice(skuInfo.getActivityPrice().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                            } else {
                                skuDetail.setDiscountPrice(skuInfo.getActivityPrice().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                                skuDetail.setLastDiscountPrice(skuDetail.getDiscountPrice());
                            }

                            skuDetail.setDiscountFlag(StringUtils.isEmpty(amzPromotions.getFlag()) ? "$" : amzPromotions.getFlag());
                            skuDetail.setDiscount((skuInfo.getDailyPrice().subtract(skuInfo.getActivityPrice())).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                            skuDetail.setPerFunding(skuDetail.getDiscountFlag() + skuDetail.getDiscount().toString());
                            List<AmzPromotionsSkuDetail> details = amzPromotionAccountMapper.selectPromotionSellerSkuByShopId(amzPromotions.getShopId().intValue(), new String[]{skuDetail.getAsin()});
                            if (CollectionUtil.isNotEmpty(details)) {
                                skuDetail.setImage(details.get(0).getImage());
                                skuDetail.setTitle(details.get(0).getTitle());
                            }
                            skuDetail.setNum(msg.getNum());

                            amzPromotionsSkuDetailService.saveOrUpdate(skuDetail);
                            skuDetailList.add(skuDetail);
                        }

                        AmzPromotionFeeDateHisRecord hisRecord = buildDateHisRecordLimit(amzPromotions, skuDetail, nowDate, skuInfo, msg);
                        if (hisRecord != null) {
                            amzPromotionFeeDateHisRecordService.saveOrUpdate(hisRecord);
                        }
                    }
                }


                AmzPromotions byIdFromDb = promotions == null ? null :BeanCopyUtils.copyBean(promotions, AmzPromotions.class);
                threadPoolTaskExecutor.execute(() -> recordSyncStatusStockAndPriceChange(amzPromotions, skuDetailList, byIdFromDb));
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Received message from temu promotion service with error--{}---msgStr--{}", e.getMessage(), msgStr);
            throw new ErpCommonException("接收temu消息异常");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void getBeginAndEndTime(List<AmzTemuPromotionMessageLimitReceiveMsg.AssignSessionList> assignSessionList, AmzPromotions amzPromotions) {
        assignSessionList.stream().min(Comparator.comparing(t -> DateUtil.convertStringToDate(t.getStartDateStr(), "yyyy-MM-dd"))).ifPresent(t -> amzPromotions.setBeginTime(DateUtil.bjDateToPacificTime(DateUtil.convertStringToDate(t.getStartDateStr() + " 00:00:00", "yyyy-MM-dd HH:mm:ss"))));
        assignSessionList.stream().max(Comparator.comparing(t -> DateUtil.convertStringToDate(t.getEndDateStr(), "yyyy-MM-dd"))).ifPresent(t -> amzPromotions.setEndTime(DateUtil.bjDateToPacificTime(DateUtil.convertStringToDate(t.getEndDateStr() + " 23:59:59", "yyyy-MM-dd HH:mm:ss"))));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void temuPromotionMessageMeiBenReceive(String msgStr) {

        Date nowDate = DateUtil.UtcToPacificDate(DateUtils.getNowDate());

        TemuPromotionMeiBenReceiveMsg msg = JSON.parseObject(msgStr, TemuPromotionMeiBenReceiveMsg.class);
        log.info("meiben promotion message Received:{}", msg);
        if (StringUtils.isEmpty(msg.getMallId())) {
            log.error("Received message from temu meiben promotion service without mall_id:{}", msgStr);
            return;
        }
        if (StringUtils.isEmpty(msg.getActivityGoodsId())) {
            log.error("Received message from temu meiben promotion service without activityGoodsId:{}", msgStr);
            return;
        }
        if (StringUtils.isEmpty(msg.getGoodsId())) {
            log.error("Received message from temu meiben promotion service without goodsId:{}", msgStr);
            return;
        }
        if (CollectionUtil.isEmpty(msg.getSkuInfoList())) {
            log.error("Received message from temu meiben promotion service without skuInfoList:{}", msgStr);
            return;
        }
        if (msg.getSkuInfoList().stream().allMatch(t -> StringUtils.isEmpty(t.getSkuId()))) {
            log.error("Received message from temu meiben promotion service with null skuId:{}", msgStr);
            return;
        }
        if (StringUtils.isEmpty(msg.getBeginTime()) || StringUtils.isEmpty(msg.getEndTime())) {
            log.error("Received message from temu meiben promotion service without beginTime or endTime:{}", msgStr);
            return;
        }
        if (msg.getActivityType() == null) {
            log.error("Received message from temu meiben promotion service without activityType or subActivityType:{}", msgStr);
            return;
        }
        /*if (!msg.getActivityType().equals(13) && !msg.getActivityType().equals(14)) {

            log.error("Received message from temu meiben promotion service with invalid activityType:{}", msgStr);
            return;
        }*/
        if (StringUtils.isEmpty(msg.getPlatFormActivityId())) {
            log.error("Received message from temu meiben promotion service without platFormActivityId:{}", msgStr);
            return;
        }

        String key = msg.getActivityGoodsId() + "temu" + msg.getMallId();
        RLock lock = redissonClient.getLock(key);
        log.info("Received message:{}, from temu limit promotion service with lock:{}", msgStr, key);
        try {
            if (lock.tryLock(0, 1, TimeUnit.MINUTES)) {

//                Account account = accountService.getOne(Wrappers.lambdaQuery(Account.class).eq(Account::getSellerId, msg.getMallId()).eq(Account::getType, "temu").eq(Account::getActive, "Y"));
                Account account = marTemuPromotionMapper.selectAccountBySellerIdAndSkcId(msg.getMallId(), msg.getGoodsId());
                if (account == null) {
                    log.error("Received message from temu meiben promotion service with invalid mallId:{}", msgStr);
                    return;
                }
                AmzPromotions byId = amzPromotionsMapper.selectAmzPromotionByInvitationIdAndParentAsin(msg.getPlatFormActivityId(), msg.getGoodsId(), account.getId());
                if (byId != null) {
                    log.error("meiben hisData update,msg.getPromotionId:{}", msgStr);
                    if (TemuPromotionsStateEnum.CANCELED.getState().equals(byId.getPlatformStatus())) {
                        log.error("meiben promotion has been canceled,msg:{}", msgStr);
                        return;
                    }
                } else {
                    log.error("meiben nsert data,msg.getPromotionId:{}", msgStr);
                }
                Integer currentRemainStock = byId != null ? byId.getRemainStock() : null;
                AccountWithCountry country = amzPromotionsMapper.selectAccountWithCountry(account.getId().longValue());
                AmzPromotions amzPromotions = new AmzPromotions();
                if (byId != null) {
                    amzPromotions.setId(byId.getId());
                    amzPromotions.settingDefaultSystemUpdate();
                } else {
                    amzPromotions.settingDefaultSystemCreate();
                    amzPromotions.settingDefaultSystemUpdate();
                    amzPromotions.setPromotionsState(AmzPromotionsStateEnum.APPROVED.value());
                    amzPromotions.setCreatedAt(DateUtil.UtcToPacificDate(DateUtils.getNowDate()));
                    amzPromotions.setApprovalStatus("已通过");

                }
                //后台直接创建的活动，没有促销id，传过来是一个生成的唯一值，如果本活动原本就有促销ID，则不更新促销ID
                if (byId == null || StringUtils.isEmpty(byId.getPromotionsId())) {
                    amzPromotions.setPromotionsId(msg.getActivityGoodsId());
                }

                amzPromotions.setInvitationId(msg.getPlatFormActivityId());
                amzPromotions.setPromotionsName(msg.getActivityName());
                setMeiBenDate(msg, amzPromotions);
                if (byId != null && byId.getPlatformStatus() != null &&
                        (TemuPromotionsStateEnum.PENDING.getState().equals(byId.getPlatformStatus()) ||
                                TemuPromotionsStateEnum.SUBMITTING.getState().equals(byId.getPlatformStatus()) ||
                                TemuPromotionsStateEnum.CANCELLING.getState().equals(byId.getPlatformStatus()) ||
                                TemuPromotionsStateEnum.MODIFYING.getState().equals(byId.getPlatformStatus()))) {

                    log.error("meiben promotion 状态为中间状态不更新状态--msg--{}", msgStr);
                } else {
                    amzPromotions.setPlatformStatus(TemuPromotionsStateEnum.getStateByDate(nowDate, amzPromotions.getBeginTime(), amzPromotions.getEndTime()).getState());
                }

                amzPromotions.setInvitationType(msg.getActivityType());
                amzPromotions.setSubActivityType(msg.getSubActivityType());
                amzPromotions.setCountry(country != null ? country.getNameCn() : null);
                amzPromotions.setShopName(account.getAccountInit());
                amzPromotions.setPromotionsType(AmzPromotionsTypeEnum.BEST_DEAL.value());
                boolean state = byId != null && byId.getPlatformStatus() != null && (TemuPromotionsStateEnum.PENDING.getState().equals(byId.getPlatformStatus()) || TemuPromotionsStateEnum.MODIFYING.getState().equals(byId.getPlatformStatus()));
                if (state) {
                    amzPromotions.setLastSkcStock(msg.getNum());
                } else {
                    amzPromotions.setSkcStock(msg.getNum());
                    amzPromotions.setLastSkcStock(msg.getNum());
                }

                amzPromotions.setChannel(4);
                amzPromotions.setFlag(MarPromotionCountryCurrencyEnum.getCurrencyFlagByCountryCodeAndSaleChannel(country == null ? null : country.getCountryCode(),account.getSaleChannel()));
                amzPromotions.setShopId(account.getId().longValue());
                amzPromotions.setCurrency(MarPromotionCountryCurrencyEnum.getCurrencyByCountryCodeAndSaleChannel(country == null ? null : country.getCountryCode(),account.getSaleChannel()));
                amzPromotions.setOrganizationId(account.getOrgId());
                amzPromotions.setSubShopBack(true);

                amzPromotions.setRemainStock(msg.getRemainStock());

                if (byId != null && byId.getSkcStock() != null && byId.getRemainStock() != null && amzPromotions.getSkcStock() != null && amzPromotions.getRemainStock() != null
                        && ((amzPromotions.getSkcStock() - amzPromotions.getRemainStock()) - (byId.getSkcStock() - byId.getRemainStock()) < 0)) {
                    log.error("美本促销接收消息库存错误--{}", msgStr);
//                    return;
                }

                amzPromotionsService.saveOrUpdate(amzPromotions);
                threadPoolTaskExecutor.execute(() -> this.adviceOperatorMeiBen(msg, account, amzPromotions.getPlatformStatus(), currentRemainStock));

                List<AmzPromotionsSkuDetail> skuDetailList = new ArrayList<>();
                List<TemuPromotionMeiBenReceiveMsg.SkuInfoList> skuInfoList = msg.getSkuInfoList().stream().filter(t -> !StringUtils.isEmpty(t.getSkuId())).collect(Collectors.toList());
                for (TemuPromotionMeiBenReceiveMsg.SkuInfoList skuInfo : skuInfoList) {
                    AmzPromotionsSkuDetail skuDetail = new AmzPromotionsSkuDetail();
                    skuDetail.setParentAsin(msg.getGoodsId());
                    skuDetail.setPromotionsId(amzPromotions.getId());
                    skuDetail.setOrganizationId(amzPromotions.getOrganizationId());
                    skuDetail.setShopName(amzPromotions.getShopName());
                    skuDetail.setShopId(amzPromotions.getShopId());
                    skuDetail.setAsin(skuInfo.getSkuId());
                    skuDetail.setSellerSku(skuInfo.getSkuId());
                    skuDetail.setSiteId(msg.getSiteId());
                    boolean noInsert = false;
                    if (skuInfo.getDailyPrice() == null || skuInfo.getActivityPrice() == null) {
                        noInsert = true;
                    }
                    if (!noInsert) {
                        AmzPromotionsSkuDetail skuOne = amzPromotionsSkuDetailService.getOne(Wrappers.lambdaQuery(AmzPromotionsSkuDetail.class).eq(AmzPromotionsSkuDetail::getPromotionsId, amzPromotions.getId()).eq(AmzPromotionsSkuDetail::getSellerSku, skuInfo.getSkuId()));
                        if (skuOne != null) {
                            skuDetail.setId(skuOne.getId());
                            skuDetail.settingDefaultSystemUpdate();
                        } else {
                            skuDetail.settingDefaultSystemCreate();
                            skuDetail.settingDefaultSystemUpdate();
                        }
                        skuDetail.setPrice(skuInfo.getDailyPrice().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                        if (state) {
                            skuDetail.setLastDiscountPrice(skuInfo.getActivityPrice().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                        } else {
                            skuDetail.setDiscountPrice(skuInfo.getActivityPrice().divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                            skuDetail.setLastDiscountPrice(skuDetail.getDiscountPrice());
                        }

                        skuDetail.setDiscountFlag(StringUtils.isEmpty(amzPromotions.getFlag()) ? "$" : amzPromotions.getFlag());
                        skuDetail.setDiscount((skuInfo.getDailyPrice().subtract(skuInfo.getActivityPrice())).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                        skuDetail.setPerFunding(skuDetail.getDiscountFlag() + skuDetail.getDiscount().toString());
                        List<AmzPromotionsSkuDetail> details = amzPromotionAccountMapper.selectPromotionSellerSkuByShopId(amzPromotions.getShopId().intValue(), new String[]{skuDetail.getAsin()});
                        if (CollectionUtil.isNotEmpty(details)) {
                            skuDetail.setImage(details.get(0).getImage());
                            skuDetail.setTitle(details.get(0).getTitle());
                        }
                        skuDetail.setNum(msg.getNum());
                        amzPromotionsSkuDetailService.saveOrUpdate(skuDetail);
                        skuDetailList.add(skuDetail);

                    }
                    AmzPromotionFeeDateHisRecord hisRecord = buildDateHisRecordMeiBen(amzPromotions, skuDetail, nowDate, skuInfo, msg);
                    if (hisRecord != null) {
                        amzPromotionFeeDateHisRecordService.saveOrUpdate(hisRecord);
                    }
                }
                threadPoolTaskExecutor.execute(() -> recordSyncStatusStockAndPriceChange(amzPromotions, skuDetailList, byId));
            }
        } catch (Exception e) {
            log.error("Received message from temu promotion service with error:{}", e.getMessage());
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }


    }

    private void adviceOperatorMeiBen(TemuPromotionMeiBenReceiveMsg msg, Account account, Integer platformStatus, Integer currentRemainStock) {
        try {
            List<TemuPromotionMeiBenReceiveMsg.SkuInfoList> skuList = msg.getSkuInfoList();
            List<String> collect = skuList.stream().map(TemuPromotionMeiBenReceiveMsg.SkuInfoList::getSkuId).distinct().collect(Collectors.toList());
            List<AmzPromotionCheck> amzPromotionChecks = amzPromotionCheckMapper.selectListByShopIdOrAsinOrSellerSku(account.getId().longValue(), collect.toArray(new String[0]), null);
            boolean stateFlag = TemuPromotionsStateEnum.NOT_STARTED.getState().equals(platformStatus)
                    || TemuPromotionsStateEnum.RUNNING.getState().equals(platformStatus)
                    || TemuPromotionsStateEnum.MODIFYING.getState().equals(platformStatus)
                    || TemuPromotionsStateEnum.MODIFY_FAILED.getState().equals(platformStatus);
            if (msg.getRemainStock() != null && msg.getRemainStock().equals(0) && stateFlag) {
                if (currentRemainStock != null && currentRemainStock.equals(0)) {
                    return;
                }
                String advice = String.format("促销ID:[%s],店铺:[%s],SKC:[%s],剩余库存数量:[%s]", msg.getActivityGoodsId(),
                        account.getTitle(), msg.getGoodsId(),
                        msg.getRemainStock());
                log.info("temu--促销消息:{},通知运营:{}", msg, advice);
                if (CollectionUtil.isNotEmpty(amzPromotionChecks) && amzPromotionChecks.get(0).getOperationUserId() != null) {
                    UserEntity byId = SpringUtils.getBean(UserService.class).getById(amzPromotionChecks.get(0).getOperationUserId());
                    if (byId != null && StringUtils.isNotEmpty(byId.getPhone())) {
                        String pattern = "^1[3-9]\\d{9}$";
                        if (byId.getPhone().matches(pattern)) {
                            QywxUtil.wxMessageSend(advice, byId.getPhone());
                        } else {
                            log.error("通知内容:{},运营:{},手机号:{}校验不合格", advice, byId.getName(), byId.getPhone());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("temu-促销消息:{},通知运营失败:{}", msg, e.getMessage());
        }
    }


    private AmzPromotionFeeDateHisRecord buildDateHisRecordMeiBen(AmzPromotions amzPromotions, AmzPromotionsSkuDetail skuDetail, Date nowDate, TemuPromotionMeiBenReceiveMsg.SkuInfoList skuInfo, TemuPromotionMeiBenReceiveMsg msg) {

        if (DateUtil.compareDate(DateUtil.convertDateToString(amzPromotions.getEndTime(), DateUtils.DEFAULT_DATE_FORMAT), DateUtil.convertDateToString(DateUtil.addDate(nowDate, -1), DateUtils.DEFAULT_DATE_FORMAT)) < 0) {
            log.error("Received message from temu promotion service with expired promotion,promotionId:{}", amzPromotions.getPromotionsId());
            return null;
        }
        LambdaQueryWrapper<AmzPromotionFeeDateHisRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AmzPromotionFeeDateHisRecord::getPromotionId, amzPromotions.getId())
                .eq(AmzPromotionFeeDateHisRecord::getSkcId, skuDetail.getParentAsin())
                .eq(AmzPromotionFeeDateHisRecord::getSellerSku, skuDetail.getSellerSku())
                .ge(AmzPromotionFeeDateHisRecord::getDate, DateUtil.convertDateToString(nowDate, DateUtils.DEFAULT_DATE_FORMAT))
                .lt(AmzPromotionFeeDateHisRecord::getDate, DateUtil.convertDateToString(DateUtil.addDate(nowDate, 1), DateUtils.DEFAULT_DATE_FORMAT))

                .orderByDesc(AmzPromotionFeeDateHisRecord::getDate)
                .last("limit 1");
        AmzPromotionFeeDateHisRecord one = amzPromotionFeeDateHisRecordService.getOne(wrapper);
        AmzPromotionFeeDateHisRecord record = new AmzPromotionFeeDateHisRecord();
        if (one != null) {
            record.setId(one.getId());
            record.settingDefaultSystemUpdate();
        } else {
            record.settingDefaultSystemCreate();
        }        //TODO
        record.setActivityType(amzPromotions.getInvitationType());
        record.setPromotionId(amzPromotions.getId());
        record.setAmzSkuDetailId(skuDetail.getId());
        record.setAsin(skuDetail.getAsin());
        record.setSellerSku(skuDetail.getSellerSku());
        record.setShopId(amzPromotions.getShopId());
        record.setDailyPrice(skuInfo.getDailyPrice());
        record.setActivityPrice(skuInfo.getActivityPrice());
        record.setSkcStock(msg.getNum());
        record.setRemainStock(msg.getRemainStock());
        record.setSkcId(skuDetail.getParentAsin());
        record.setDate(nowDate);
        return record;
    }


    public void setMeiBenDate(TemuPromotionMeiBenReceiveMsg msg, AmzPromotions amzPromotions) {

        if ("api".equals(msg.getPullType())) {
            amzPromotions.setBeginTime(DateUtil.convertStringToDate(msg.getBeginTime(), DateUtils.DEFAULT_TIME_FORMAT));
            amzPromotions.setEndTime(DateUtil.convertStringToDate(msg.getEndTime(), DateUtils.DEFAULT_TIME_FORMAT));
            return;
        }
        try {
            String[] beginTimeSplit = msg.getBeginTime().split(" ");
            if ("PST".equalsIgnoreCase(beginTimeSplit[0]) || "PDT".equalsIgnoreCase(beginTimeSplit[0])) {
                Date beginTime = DateUtils.parseDate(beginTimeSplit[1], "MM/dd/yyyy");
                String beginTimeStr = DateUtil.convertDateToString(beginTime, DateUtils.DEFAULT_DATE_FORMAT) + " " + beginTimeSplit[2];

                String[] endTimeSplit = msg.getEndTime().split(" ");
                Date endTime = DateUtils.parseDate(endTimeSplit[1], "MM/dd/yyyy");
                String endTimeStr = DateUtil.convertDateToString(endTime, DateUtils.DEFAULT_DATE_FORMAT) + " " + endTimeSplit[2];

                amzPromotions.setBeginTime(DateUtil.convertStringToDate(beginTimeStr, DateUtils.DEFAULT_TIME_FORMAT));
                amzPromotions.setEndTime(DateUtil.convertStringToDate(endTimeStr, DateUtils.DEFAULT_TIME_FORMAT));
            } else {
                Date beginTime = DateUtils.parseDate(beginTimeSplit[0], "MM/dd/yyyy");
                String beginTimeStr = DateUtil.convertDateToString(beginTime, DateUtils.DEFAULT_DATE_FORMAT) + " " + beginTimeSplit[1];

                String[] endTimeSplit = msg.getEndTime().split(" ");
                Date endTime = DateUtils.parseDate(endTimeSplit[0], "MM/dd/yyyy");
                String endTimeStr = DateUtil.convertDateToString(endTime, DateUtils.DEFAULT_DATE_FORMAT) + " " + endTimeSplit[1];

                amzPromotions.setBeginTime(DateUtil.convertStringToDate(beginTimeStr, DateUtils.DEFAULT_TIME_FORMAT));
                amzPromotions.setEndTime(DateUtil.convertStringToDate(endTimeStr, DateUtils.DEFAULT_TIME_FORMAT));
            }

        } catch (Exception e) {
            String advice = String.format("促销ID:[%s],SKC:[%s],开始结束时间时间格式不正确:[%s],[%s]", msg.getActivityGoodsId(), msg.getGoodsId(), msg.getBeginTime(), msg.getEndTime());
            threadPoolTaskExecutor.execute(() -> WeComRobotUtil.sendTextMsgNew(advice, "TEMU_PROMOTION", weChatBootConfigure));
            throw new RuntimeException("Received message from temu meiben promotion service with invalid beginTime or endTime:" + e.getMessage());
        }

    }


    public void recordSyncStatusStockAndPriceChange(AmzPromotions amzPromotions, List<AmzPromotionsSkuDetail> skuDetailList, AmzPromotions byId) {

        Boolean needRecord = false;
        Integer stock = null;

        if (byId == null) {

            if (amzPromotions.getSkcStock() == null || amzPromotions.getRemainStock() == null) {
                needRecord = true;
            } else {
                if (amzPromotions.getSkcStock().compareTo(amzPromotions.getRemainStock()) != 0) {
                    needRecord = true;
                    stock = amzPromotions.getSkcStock() - amzPromotions.getRemainStock();
                }
            }
        } else {
            boolean b = amzPromotions.getSkcStock() == null || amzPromotions.getRemainStock() == null || byId.getSkcStock() == null || byId.getRemainStock() == null;

            if (b) {
                needRecord = true;
            } else {
                stock = (amzPromotions.getSkcStock() - amzPromotions.getRemainStock()) - (byId.getSkcStock() - byId.getRemainStock());
                if (stock.compareTo(0) != 0) {
                    needRecord = true;
                }
            }
        }
        if (!needRecord) {
            return;
        }

        //本次消耗库存 （返回的提报库存-剩余库存）-（系统里的提报库存-系统里的剩余库存）

        AmzPromotionsOperateLog operateLog = new AmzPromotionsOperateLog();

        StringBuilder content = new StringBuilder(String.format("同步活动:[提报库存]:%s,[剩余库存]:%s,[本次消耗库存]:%s。", amzPromotions.getSkcStock(), amzPromotions.getRemainStock(), stock));
        if (CollectionUtil.isNotEmpty(skuDetailList)) {
            skuDetailList.forEach(t -> {
                content.append("[商品ID]:").append(t.getAsin()).append("[活动申报价格]:").append(t.getDiscountPrice() != null ? t.getDiscountPrice().toString() : null);
            });
        }
        operateLog.setPromotionId(amzPromotions.getId());
        operateLog.setOperateType(AmzPromotionsOperateTypeEnum.SYSTEM_UPDATE.value());
//        operateLog.setResult("success");
        operateLog.setDetail(content.toString());
        operateLog.settingDefaultSystemCreate();
        operateLog.settingDefaultSystemUpdate();
        operateLog.setOrganizationId(amzPromotions.getOrganizationId());
        amzPromotionsOperateLogService.save(operateLog);

    }
}

