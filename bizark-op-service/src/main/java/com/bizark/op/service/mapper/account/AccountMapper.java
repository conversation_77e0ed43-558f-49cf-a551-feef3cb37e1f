package com.bizark.op.service.mapper.account;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.account.AccountApplication;
import com.bizark.op.api.entity.op.promotions.AccountCompanyNameVo;
import com.bizark.op.api.request.AmzPromotionsShopQueryRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AccountMapper extends BaseMapper<Account> {
    /**
     * 获取店铺信息列表
     * @param account
     * @return
     */
    List<Account> selectAccountList(Account account);

    /**
     * 根据渠道，店铺id查询店铺信息
     * @param account
     * @return
     */
    List<Account> selectAccountListByChannelOrId(AmzPromotionsShopQueryRequest account);


    /**
     * 获取店铺信息列表
     * @param account
     * @return
     */
    List<Account> selectAccountListRefactor(Account account);


    /**
     * @description: 获取店铺退款账号（默认账号）
     * @author: Moore
     * @date: 2023/12/1 10:41
     * @param
     * @param shopId 店铺ID
     * @return: java.util.List<java.lang.String>
     **/
    List<String> selectAccountRefundInfo(@Param("accountId") Long shopId);

    Account selectAccountByFlag(@Param("flag") String flag);


    /**
     * 获取店铺退款账号（默认账号）
     * @param shopId
     * @param defaultWarehouseFlag 是否默认 Y 默认
     * @return
     */
    List<Account> selectAccountRefundInfoNew(@Param("accountId") Long shopId, @Param("defaultWarehouseFlag")String defaultWarehouseFlag);

    List<Account> selectAll();

    List<Long> selectAccountIdListById(@Param("shopId")Long shopId, @Param("contextId")Long contextId);

    List<Account> selectEbayAccount();


    Account selectByOrgIdAndFlag(@Param("orgId") Integer orgId, @Param("flag") String flag);


    List<Account> selectByAccountsCountry(@Param("orgId") Integer orgId, @Param("accountFlag") String accountFlag,@Param("types") List<String> types);

    List<Account> selectByOrgIdAndFlags(@Param("orgId") Integer orgId, @Param("flags") List<String> flags);


    /**
     * 根据店铺id列表和渠道查询店铺注册公司名称
     * @param ids
     * @return
     */
    List<Account> selectShopIdMapRegisterCompanyName(@Param("ids") List<Integer> ids, @Param("channel") String channel);

    AccountApplication selectApplication(@Param("applicationId") String applicationId);

    List<String> selectAccountCompany(@Param("orgId") Integer orgId);

    List<Account> selectAccountCompanyByFlag(@Param("orgId") Integer orgId, @Param("flags") List<String> flags);

    List<AccountCompanyNameVo> temuCompanyNameList(@Param("contextId")Integer contextId);


    /**
     * 根据storeId，类型，orgId，父asin/asin/sellerSku查询店铺
     * @param storeId
     * @param type
     * @param orgId
     * @param parentAsin
     * @param asin
     * @param sellerSku
     * @return
     */
    Account selectAccountIdAndTypeAndOrgIdByStoreIdAndParentAsinOrAsinOrSellerSku(@Param("storeId") String storeId, @Param("type") String type, @Param("orgId") Integer orgId, @Param("parentAsin") String parentAsin, @Param("asin") String asin, @Param("sellerSku") String sellerSku);
}
