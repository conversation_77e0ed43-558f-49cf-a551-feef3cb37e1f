package com.bizark.op.service.api;

import bizark.amz.ApiException;
import bizark.amz.FeedsApi;
import bizark.amz.feed.*;
import bizark.amz.vendor.ds.UpdateInventoryApi;
import bizark.amz.vendor.ds.VendorTransactionApi;
import bizark.amz.vendor.ds.inv.SubmitInventoryUpdateResponse;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazon.SellingPartnerAPIAA.AWSAuthenticationCredentials;
import com.amazon.SellingPartnerAPIAA.AWSAuthenticationCredentialsProvider;
import com.amazon.SellingPartnerAPIAA.LWAAuthorizationCredentials;
import com.beust.jcommander.internal.Lists;
import com.bizark.boss.api.service.stock.StockAccountAddressService;
import com.bizark.common.constant.ConfigConstants;
import com.bizark.op.api.enm.TikTokApiEnums;
import com.bizark.op.api.enm.temu.TemuUrl;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.amazon.AmazonApiEnum;
import com.bizark.op.api.entity.op.amazon.AmazonFeed;
import com.bizark.op.api.entity.op.inventory.enums.ErrorMsgEnum;
import com.bizark.op.api.entity.op.inventory.enums.InventoryUrl;
import com.bizark.op.api.entity.op.inventory.enums.SheinUrl;
import com.bizark.op.api.entity.op.inventory.enums.TaskStatusEnum;
import com.bizark.op.api.entity.op.inventory.request.*;
import com.bizark.op.api.entity.op.inventory.response.query.EbayInventorySelectResponse;
import com.bizark.op.api.entity.op.inventory.response.update.*;
import com.bizark.op.api.entity.op.task.PublishTask;
import com.bizark.op.api.parameter.util.TikTokUtil;
import com.bizark.op.api.service.amazon.AmazonFeedFeedService;
import com.bizark.op.api.service.task.PublishTaskService;
import com.bizark.op.common.util.ValidateUtil;
import com.bizark.op.service.event.AmazonInventoryUpdateEvent;
import com.bizark.op.service.event.AmazonVendorsInventoryUpdateEvent;
import com.bizark.op.service.handler.inventory.extra.EbayInventoryStatusCall;
import com.bizark.op.service.handler.inventory.extra.EbayInventoryStatusType;
import com.bizark.op.service.util.AccessTokenUtils;
import com.bizark.op.service.util.XxlConfig;
import com.ebay.sdk.ApiAccount;
import com.ebay.sdk.ApiContext;
import com.ebay.sdk.ApiCredential;
import com.ebay.sdk.call.ReviseInventoryStatusCall;
import com.ebay.soap.eBLBaseComponents.AckCodeType;
import com.ebay.soap.eBLBaseComponents.ErrorType;
import com.ebay.soap.eBLBaseComponents.InventoryStatusType;
import com.ebay.soap.eBLBaseComponents.ReviseInventoryStatusResponseType;
import com.squareup.okhttp.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.mvel2.sh.CommandException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.bizark.op.api.entity.op.inventory.enums.ErrorMsgEnum.CONNECT_STR_EMPTY;
import static com.bizark.op.service.service.amazon.AmazonBaseService.*;

@Component
@Slf4j
public class InventoryUpdateApi {

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;


    @Autowired
    private TikTokUtil tikTokUtil;


    @Autowired
    private AccessTokenUtils accessTokenUtils;

    public static final String FEED_XML_PATH = "C:\\Users\\<USER>\\Desktop\\Feed\\";


    @Value("${inventory.sc.path}")
    private String scXmlPath;

    @Autowired
    private PublishTaskService publishTaskService;


    @Autowired
    private AmazonFeedFeedService amazonFeedFeedService;



    /**
     * 更新微软渠道产品库存
     * @param account
     * @param body
     * @return
     */
    public MicrosoftInventoryUpdateResponse microsoftInventoryUpdate(Account account, String body) {
        log.info("microSoft inventory update [{}] : {}", account.getId(), body);
        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            log.error("microSoft inventory update fail , connectStr is empty : {}", account.getId());
            return MicrosoftInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, CONNECT_STR_EMPTY.getValue());
        }
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey("storeId")) {
            log.error("microSoft inventory update fail , stroreId is empty : {}", account.getId());
            return MicrosoftInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, ErrorMsgEnum.STORE_ID_EMPTY.getValue());
        }


        String token = accessTokenUtils.getToken(account);

        if (StrUtil.isBlank(token)) {
            log.error("microSoft inventory select fail , auth error : {} ", account.getId());
            return MicrosoftInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, ErrorMsgEnum.ACCESS_TOKEN_AUTH_FAIL.getValue());
        }
        String url = String.format(InventoryUrl.MICROSOFT.getValue(), jsonObject.get("storeId"));
//        HttpRequest patch = HttpUtil.createRequest(Method.PATCH, url);
        try {
            OkHttpClient client = new OkHttpClient();
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
            Request request = new Request.Builder()
                    .url(url)
                    .header("Authorization", token)
                    .patch(requestBody) // 设置请求方法为PATCH，并传入请求体
                    .build();
            Response execute = client.newCall(request).execute();
            if (!execute.isSuccessful()) {
                return MicrosoftInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, execute.message());
            }
            ResponseBody responseBody = execute.body();
            String response = new String(responseBody.bytes(), StandardCharsets.UTF_8);
            log.info("响应：{}", response);
            System.out.println();
            MicrosoftInventoryUpdateResponse updateResponse = JSON.parseObject(response, MicrosoftInventoryUpdateResponse.class);
            updateResponse.setStatus(TaskStatusEnum.SUCCESS);
            return updateResponse;
        } catch (IOException e) {
            return MicrosoftInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, "请求失败：" + e.getMessage());
        }


//        patch.header("Authorization", token);
//        patch.body(body);
//        HttpResponse httpResponse = patch.execute();
//        log.info("微软修改库存URL:{}", url);
//        log.info("微软修改库存Token:{}", token);
//        log.info("微软修改库存参数:{}", body);
//        if (!httpResponse.isOk()) {
//            log.error("microSoft inventory update fail , request error : {}", httpResponse.body());
//            return MicrosoftInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, "请求失败：" + httpResponse.body());
//        }
//        MicrosoftInventoryUpdateResponse response = JSON.parseObject(httpResponse.body(), MicrosoftInventoryUpdateResponse.class);
//        response.setStatus(TaskStatusEnum.SUCCESS);
    }

    public static String getServiceUrl(String countryCode) {
        if (!(countryCode.isEmpty())) {
            return ConfigConstants.getProperty("amazon." + countryCode.toLowerCase() + ".serviceurl");
        } else {
            return null;
        }
    }


    @SneakyThrows
    public AmazonInventoryUpdateResponse amazonInventoryUpdate(Account account, AmazonInventoryUpdate amazonInventoryUpdate, PublishTask task) {

        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            log.error("connectStr is empty :{}", account.getId());
            return AmazonInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, CONNECT_STR_EMPTY.getValue());
        }
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey("merchantToken")) {
            log.error("merchantToken is empty : {}", account.getId());
            return AmazonInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, ErrorMsgEnum.MERCHANT_TOKEN_EMPTY.getValue());
        }
        if (!jsonObject.containsKey("mwsAuthToken")) {
            log.error("mwsAuthToken is empty : {}", account.getId());
            return AmazonInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, ErrorMsgEnum.MWS_TOKEN_EMPTY.getValue());

        }

        if (!jsonObject.containsKey("marketplaceId")) {
            log.error("marketplaceId is empty : {}", account.getId());
            return AmazonInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, ErrorMsgEnum.MARKETPLACE_ID_EMPTY.getValue());

        }
        String sellerId = StrUtil.isBlank(account.getSellerId()) ? jsonObject.getString("sellerId") : account.getSellerId();
        if (StrUtil.isBlank(sellerId)) {
            log.error("sellerId is empty : {}", account.getId());
            return AmazonInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, ErrorMsgEnum.SELLER_ID_EMPTY.getValue());
        }

        FeedsApi api = new FeedsApi.Builder()
                .awsAuthenticationCredentials(getAwsCre(account.getCountryCode()))
                .lwaAuthorizationCredentials(getLwaCre(jsonObject.getString("refreshToken")))
                .awsAuthenticationCredentialsProvider(getAwsProvider())
                .endpoint(getEndpoint(account.getCountryCode()))
                .build();

        CreateFeedDocumentSpecification doc = new CreateFeedDocumentSpecification();
        doc.setContentType(String.format("text/xml; charset=%s", StandardCharsets.UTF_8));
        CreateFeedDocumentResponse feedDocument = null;
        try {
            feedDocument = api.createFeedDocument(doc);
        } catch (Exception e) {
            return AmazonInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, "feedDocument 创建失败！");
        }
        if (Objects.isNull(feedDocument)) {
            return AmazonInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, "feedDocument 创建失败！");
        }
        // 写入xml文件地址
        String curDate = new SimpleDateFormat("yyyyMMdd")
                .format(new Date());
        String xmlPath = scXmlPath + account.getTitle() + "/" + curDate + "/";
        String xmlStr = writeFeedXml(
                xmlPath,
                jsonObject.getString("merchantToken"),
                String.valueOf(System.currentTimeMillis()),
                amazonInventoryUpdate
        );
        if (StrUtil.isBlank(xmlStr)) {
            return AmazonInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, "xml库存文件写人失败！");
        }
        String feedDocumentId = feedDocument.getFeedDocumentId();
        String uploadUrl = feedDocument.getUrl();
        // 直接上传
        byte[] bytes = String.format(xmlStr, amazonInventoryUpdate.getSku(), amazonInventoryUpdate.getQuantity()).getBytes();
        try {
            uploadFeedContent(bytes, feedDocument.getUrl(), "text/xml; charset=%s");
        } catch (Exception e) {
            return AmazonInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, "xml库存文件上传失败！");
        }
        CreateFeedSpecification feedDocumentSpecification = new CreateFeedSpecification();
        feedDocumentSpecification.setInputFeedDocumentId(feedDocumentId);
        feedDocumentSpecification.setFeedType("POST_INVENTORY_AVAILABILITY_DATA");
        feedDocumentSpecification.setMarketplaceIds(Lists.newArrayList(jsonObject.getString("marketplaceId")));
        CreateFeedResponse feed = null;
        try {
            feed = api.createFeed(feedDocumentSpecification);
        } catch (Exception e) {
            if (e instanceof bizark.ApiException) {
                String responseBody = ((bizark.ApiException) e).getResponseBody();
                return AmazonInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, "Feed创建失败:" + ((bizark.ApiException) e).getResponseBody());
            }
            return AmazonInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, "Feed创建失败 ");
        }

        if (Objects.isNull(feed)) {
            return AmazonInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, "Feed创建失败！");
        }
        String feedId = feed.getFeedId();

        Feed apiFeed = api.getFeed(feedId);
        if (Objects.isNull(apiFeed)) {
            return AmazonInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, "Feed获取失败！");
        }

        // 创建AmazonFeed
        AmazonFeed amazonFeed = amazonFeedFeedService.createFeed(feedDocumentId, uploadUrl, xmlPath, feedId, task.getId());
        threadPoolTaskExecutor.execute(() -> applicationEventPublisher.publishEvent(new AmazonInventoryUpdateEvent(api, amazonFeed, task, 5, this)));
        return AmazonInventoryUpdateResponse.buildResult(TaskStatusEnum.PROCESSING, "xml库存文件上传完毕，等待报告返回");


    }


    String writeFeedXml(String path,String fileName, String merchantToken, AmazonInventoryUpdate amazonInventoryUpdate) {

        File outputDir = FileUtils.getFile(path);
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }
        java.io.File tempFeed = FileUtils.getFile(path + fileName + ".xml");
        BufferedWriter writer = null;
        BufferedReader reader = null;
        try {
            writer = new BufferedWriter(new FileWriter(tempFeed));
            writer.write("<?xml version=\"1.0\" encoding=\"UTF-8\" ?>\r\n");
            writer.write("<AmazonEnvelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:noNamespaceSchemaLocation=\"amzn-envelope.xsd\">\r\n");
            writer.write("<Header>\r\n");
            writer.write("<DocumentVersion>1.01</DocumentVersion>\r\n");
            writer.write("<MerchantIdentifier>" + merchantToken + "</MerchantIdentifier>\r\n");
            writer.write("</Header>\r\n");
            writer.write("<MessageType>Inventory</MessageType>\r\n");
            writer.write("<Message>\r\n");
            writer.write("<MessageID>" + 1 + "</MessageID>\r\n");
            writer.write("<OperationType>Update</OperationType>\r\n");
            writer.write("<Inventory>\r\n");
            writer.write("<SKU>" + amazonInventoryUpdate.getSku() + "</SKU>\r\n");
            writer.write("<Quantity>" + amazonInventoryUpdate.getQuantity() + "</Quantity>\r\n");
            if (Objects.nonNull(amazonInventoryUpdate.getHandingTime())) {
                writer.write("<FulfillmentLatency>" + amazonInventoryUpdate.getHandingTime() + "</FulfillmentLatency>\r\n");
            }
            writer.write("</Inventory>\r\n");
            writer.write("</Message>\r\n");
            writer.write("</AmazonEnvelope>");
            writer.close();
            String line;
            StringBuilder builder = new StringBuilder();
            reader = new BufferedReader(new FileReader(tempFeed));
            while ((line = reader.readLine()) != null) {
                builder.append(line);
            }
            ;
            return builder.toString();
        } catch (Exception e) {
            return null;
        } finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                }
            }
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                }
            }
        }
    }

    /**
     * Upload content to the given URL.
     *
     * @param source the content to upload
     * @param url    the URL to upload content
     */
    private void uploadFeedContent(byte[] source, String url, String format) throws IOException {
        OkHttpClient client = new OkHttpClient();

        String contentType = String.format(format, StandardCharsets.UTF_8);
        log.info("uploadFeedContent url: {}", url);
        Request request = new Request.Builder()
                .url(url)
                .put(RequestBody.create(MediaType.parse(contentType), source))
                .build();

        Response response = client.newCall(request).execute();
        if (!response.isSuccessful()) {
            log.error(
                    String.format("Call to upload document failed with response code: %d and message: %s",
                            response.code(), response.message()));
        }


    }


    public ShopifySetInventoryUpdateResponse shopifyInventoryUpdate(Account account, String jsonString) {
        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            log.error("connectStr is empty :{}", account.getId());
            return ShopifySetInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, CONNECT_STR_EMPTY.getValue());
        }
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey("password")) {
            return ShopifySetInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, ErrorMsgEnum.PASS_WORD_EMPTY.getValue());
        }
        if (!jsonObject.containsKey("hostname")) {
            return ShopifySetInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, ErrorMsgEnum.HOST_NAME_EMPTY.getValue());
        }
        HttpRequest post = HttpUtil.createPost(String.format(InventoryUrl.SHOPIFY.getValue(), jsonObject.getString("hostname")));
        post.header("X-Shopify-Access-Token", jsonObject.getString("password"));
        post.body(jsonString);
        HttpResponse httpResponse = post.execute();
        if (!httpResponse.isOk()) {
            return ShopifySetInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, httpResponse.body());
        }
        ShopifySetInventoryUpdateResponse response = JSON.parseObject(httpResponse.body(), ShopifySetInventoryUpdateResponse.class);
        response.setStatus(TaskStatusEnum.SUCCESS);
        return response;
    }


    /**
     *
     * @param account
     * @param body
     * @see EbayInventorySelectResponse body体
     * @return
     */
    public EbayInventoryUpdateResponse ebayInventoryUpdate(Account account, EbayInventoryUpdate ebayInventoryUpdate) throws Exception {

        String token = accessTokenUtils.getToken(account);
        if (StrUtil.isBlank(token)) {
            return EbayInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, "AccessToken获取失败");
        }
        ApiContext apiContext = new ApiContext();
        apiContext.setApiServerUrl("https://api.ebay.com/wsapi");
        ApiCredential apiCredential = apiContext.getApiCredential();
        ApiAccount apiAccount = apiCredential.getApiAccount();
        apiContext.setRouting("new");
        apiAccount.setApplication(XxlConfig.EBAY_APPID);
        apiAccount.setCertificate(XxlConfig.EBAY_CERTID);
        apiAccount.setDeveloper(XxlConfig.EBAY_DEVID);
        apiCredential.seteBayToken(token);
        EbayInventoryStatusCall call = new EbayInventoryStatusCall(apiContext);
        InventoryStatusType type = new InventoryStatusType();
        type.setSKU(ebayInventoryUpdate.getSku());
        type.setItemID(ebayInventoryUpdate.getItemId());
        type.setQuantity(ebayInventoryUpdate.getQuantity());
        call.setInventoryStatus(new InventoryStatusType[]{type});
        try {
            InventoryStatusType[] inventoryStatusTypes = call.reviseInventoryStatus();
            if (Objects.isNull(inventoryStatusTypes) || inventoryStatusTypes.length == 0) {
                return EbayInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, "未获取到Ebay库存修改响应，库存修改失败");
            }
            EbayInventoryStatusType statusType = (EbayInventoryStatusType) inventoryStatusTypes[0];
            ReviseInventoryStatusResponseType responseType = statusType.getResponseType();
            AckCodeType ack = responseType.getAck();
            if (ack == AckCodeType.SUCCESS) {
                return EbayInventoryUpdateResponse.buildResult(TaskStatusEnum.SUCCESS, "库存修改成功");
            }
            String errorMsg = "修改失败，未获取到错误原因";
            ErrorType[] errors = responseType.getErrors();
            if (Objects.nonNull(errors) && errors.length > 0) {
                errorMsg = Arrays.stream(errors)
                        .map(ErrorType::getLongMessage)
                        .filter(StrUtil::isNotBlank)
                        .collect(Collectors.joining(" "));
            }
             return EbayInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, errorMsg);
        } catch (Exception e) {
            log.error("Ebay 库存修改失败：{}", e.getMessage(), e);
            return EbayInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, e.getMessage());
        }
    }

    public VCDFInventoryUpdateResponse amazonVendorsInventoryUpdate(Account account, VCDFInventoryUpdate param) {
        PublishTask task = param.getTaskInfo().getPublishTask();
        String connectStr = account.getConnectStr();
        JSONObject jsonObject = JSON.parseObject(connectStr);

        UpdateInventoryApi api = new UpdateInventoryApi.Builder().awsAuthenticationCredentials(genAWS(account.getCountryCode())).
                lwaAuthorizationCredentials(genLWA(jsonObject.getString("refreshToken"), account.getCountryCode())).
                awsAuthenticationCredentialsProvider(genAWSProvider()).
                endpoint(getEndPoint(account.getCountryCode())).
                build();
        SubmitInventoryUpdateResponse updateResponse = null;
        try {
            updateResponse = api.submitInventoryUpdate(param.getSubmitInventoryUpdateRequest(), param.getWarehouseId());
            log.info("VC-DF 修改库存，任务ID：{} 请求参数：{}", task.getId(), JSON.toJSONString(param.getSubmitInventoryUpdateRequest()));
        } catch (ApiException e) {
            e.printStackTrace();
            return VCDFInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, e.getMessage());
        }


        try {
            VendorTransactionApi transactionApi = new VendorTransactionApi.Builder().awsAuthenticationCredentials(genAWS(account.getCountryCode())).
                    lwaAuthorizationCredentials(genLWA(jsonObject.getString("refreshToken"), account.getCountryCode())).
                    awsAuthenticationCredentialsProvider(genAWSProvider()).
                    endpoint(getEndPoint(account.getCountryCode())).
                    build();
            String transactionId = updateResponse.getPayload().getTransactionId();
            task.setTransactionId(transactionId);
            log.info("VC-DF 修改库存，获取到事务响应：{} 任务ID：{}", JSON.toJSONString(updateResponse), task.getId());
            publishTaskService.updateTaskTransactionId(param.getTaskInfo().getPublishTask().getId(), transactionId);
            threadPoolTaskExecutor.execute(() -> applicationEventPublisher.publishEvent(new AmazonVendorsInventoryUpdateEvent(transactionApi, param.getTaskInfo(), 8, this)));
            return VCDFInventoryUpdateResponse.buildResult(TaskStatusEnum.PROCESSING, "请求已提交,当前事务处理中", transactionId);
        } catch (Exception e) {
            e.printStackTrace();
            return VCDFInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, Objects.nonNull(e.getCause()) ? e.getCause().getMessage() : e.getMessage());
        }

    }


    public static AWSAuthenticationCredentialsProvider genAWSProvider() {
        AWSAuthenticationCredentialsProvider awsAuthenticationCredentialsProvider = AWSAuthenticationCredentialsProvider.builder()
                .roleArn(XxlConfig.AMAZON_VENDOR_ARN)
                .roleSessionName("amazonvendor")
                .build();
        return awsAuthenticationCredentialsProvider;
    }

    public static String getEndPoint(String countryCode) {
        AmazonApiEnum amazonApiEnum = AmazonApiEnum.getByCountryCode(countryCode);
        if (amazonApiEnum != null) {
            return amazonApiEnum.getEndPoint();
        }
        return null;
    }

    public static AWSAuthenticationCredentials genAWS(String countryCode) {
        String region = null;
        if (!(countryCode.isEmpty())) {
            AmazonApiEnum amazonApiEnum = AmazonApiEnum.getByCountryCode(countryCode);
            if (amazonApiEnum != null) {
                region = amazonApiEnum.getAwsRegion();
            }
        }
        AWSAuthenticationCredentials awsAuthenticationCredentials =
                AWSAuthenticationCredentials.builder()
                        .accessKeyId(XxlConfig.AMAZON_VENDOR_ACCESSKEY_ID)
                        .secretKey(XxlConfig.AMAZON_VENDOR_SECRETKEY)
                        .region(region)
                        .build();
        return awsAuthenticationCredentials;
    }


    public static LWAAuthorizationCredentials genLWA(String refreshToken, String countryCode) {
        LWAAuthorizationCredentials lwaAuthorizationCredentials =
                LWAAuthorizationCredentials.builder()
                        .clientId(XxlConfig.AMAZON_VENDOR_CLIENTID)
                        .clientSecret(XxlConfig.AMAZON_VENDOR_CLIENTSECRET)
                        .refreshToken(refreshToken)
                        .endpoint("https://api.amazon.com/auth/o2/token")
                        .build();
        return lwaAuthorizationCredentials;
    }

    public TiktokInventoryUpdateResponse tiktokInventoryUpdate(Account account, TiktokInventoryUpdate tiktokUpdate) {

        String productId = tiktokUpdate.getProductId();

//        Map<String, Object> map = BeanUtil.beanToMap(tiktokUpdate);
        TiktokInventoryUpdateResponse response = tikTokUtil.postTikTokShopV2(
                JSON.toJSONString(tiktokUpdate),
                TikTokApiEnums.PRODUCT_STOCK_UPDATE_202309.getUrl(),
                TikTokApiEnums.PRODUCT_STOCK_UPDATE_202309.getPath().replace("{product_id}", productId),
                TiktokInventoryUpdateResponse.class,
                Long.valueOf(account.getId()));
        if (response == null) {
            throw new CommandException("Tiktok 访问失败！");
        }
        log.info("Tiktok库存修改 - {} - 响应:{} ", account.getFlag(), JSON.toJSONString(response));

        if (Objects.isNull(response.getData()) || CollectionUtil.isNotEmpty(response.getData().getFailedSkus())) {
            response.setMessage("库存修改失败");
            response.setStatus(TaskStatusEnum.FAIL);
            return response;
        }
        response.setMessage("Success");
        response.setStatus(TaskStatusEnum.SUCCESS);
        return response;
    }

    /**
     * wayfair修改库存
     * @param account
     * @param param
     */
    public WayfairInventoryUpdateResponse wayfairInventoryUpdate(Account account, WayfairInventoryUpdate param) {


        String token = accessTokenUtils.getToken(account);
        HttpRequest post = HttpUtil.createRequest(Method.POST, InventoryUrl.WAYFAIR.getValue());
        post.header("Accept", "application/json");
        post.header("Authorization", token);
        post.header("Content-Type", "application/json");
        String jsonBody = JSON.toJSONString(param);
        log.info("Wayfair库存修改参数：{}", jsonBody);
        HttpRequest body = post.body(jsonBody);
        HttpResponse httpResponse = body.execute();
        String responseBody = httpResponse.body();
        log.info("Wayfair库存修改 - 响应：{}", responseBody);
        if (!httpResponse.isOk()) {
            log.info("Wayfair库存修改失败：{}", responseBody);
            return null;
        }
        WayfairInventoryUpdateResponse response = JSON.parseObject(responseBody, WayfairInventoryUpdateResponse.class);
        return response;
    }

    public SheinInventoryUpdateResponse updateSheinInventory(Account account,SheinInventoryUpdate param) {
        long timeStamp = System.currentTimeMillis();
        String sign = accessTokenUtils.getSheinSign(account, SheinUrl.INVENTORY_UPDATE.getUrl(), timeStamp);
        log.info("SHEIN-INVENTORY-UPDATE 获取签名:{}", sign);
        if (StrUtil.isBlank(sign)) {
            log.error("SHEIN-INVENTORY-UPDATE 签名获取失败");
            return SheinInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, "获取签名失败");
        }
        JSONObject connecStrObj = JSON.parseObject(account.getConnectStr());
        HttpRequest post = HttpUtil.createPost(SheinUrl.INVENTORY_UPDATE.getFullUrl());
        post.header("Content-Type", "application/json");
        post.header("x-lt-openKeyId", connecStrObj.getString("openKeyId"));
        post.header("x-lt-signature", sign);
        post.header("x-lt-timestamp", String.valueOf(timeStamp));
        post.body(JSON.toJSONString(param));
        HttpResponse httpResponse = post.execute();
        if (!httpResponse.isOk()) {
            log.error("SHEIN-INVENTORY-UPDATE 请求失败,库存更新失败");
            return SheinInventoryUpdateResponse.buildResult(TaskStatusEnum.FAIL, String.format("请求失败,状态码:%s 响应:%s", httpResponse.getStatus(), httpResponse.body()));
        }
        SheinInventoryUpdateResponse updateResponse = JSON.parseObject(httpResponse.body(), SheinInventoryUpdateResponse.class);
        updateResponse.setStatus(TaskStatusEnum.SUCCESS);
        log.info("SHEIN-INVENTORY-UPDATE 库存更新完毕");
        return updateResponse;
    }

    public TemuInventoryUpdateResponse temuInventoryUpdate(BaseTemuInventoryUpdate param) {
        HttpRequest post = HttpUtil.createPost(TemuUrl.INVENTORY_UPDATE.getCnBaseUrl());
        String sign = InventorySelectApi.getTemuSign(param);
        JSONObject paramObj = JSON.parseObject(JSON.toJSONString(param));
        paramObj.put("sign", sign);
        String body = JSON.toJSONString(paramObj);
        log.info("TEMU 库存修改,参数:{}", body);
        post.body(body);
        HttpResponse httpResponse = post.execute();
        if (!httpResponse.isOk()) {
            log.error("TEMU 库存修改 - 失败 :{} - {}", httpResponse.getStatus(), httpResponse.body());
            return null;
        }
        return JSON.parseObject(httpResponse.body(), TemuInventoryUpdateResponse.class);
    }


    private String getSign(String appSecret, Map<String, Object> param) {
        String sign = "";
        StringBuilder unSign = new StringBuilder(appSecret);
        List<String> list = param.keySet().stream().sorted(Comparator.comparing(String::valueOf)).collect(Collectors.toList());
        for (String key : list) {
            unSign.append(key).append(param.get(key));
        }
        unSign.append(appSecret);
        log.info("temu待签名字符串#{}", unSign);
        sign = encryptMD5(unSign.toString()).toUpperCase();
        log.info("temu签名字符串#{}", sign);
        return sign;
    }


    public static String encryptMD5(String input) {
        try {
            // 创建MD5加密对象
            MessageDigest md = MessageDigest.getInstance("MD5");

            // 将输入字符串转换为字节数组
            byte[] inputBytes = input.getBytes();

            // 执行加密操作
            byte[] encryptedBytes = md.digest(inputBytes);

            // 将加密后的字节数组转换为字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : encryptedBytes) {
                sb.append(String.format("%02x", b));
            }

            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }

        return null;
    }

}
