package com.bizark.op.service.service.tiktok.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bizark.op.api.cons.MQDefine;
import com.bizark.op.api.enm.TikTokApiEnums;
import com.bizark.op.api.enm.refund.OrderRefundEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.refund.OrderRefundRequest;
import com.bizark.op.api.entity.op.refund.OrderRefundRequestInfo;
import com.bizark.op.api.entity.op.ticket.ScTicket;
import com.bizark.op.api.entity.op.titok.request.*;
import com.bizark.op.api.entity.op.titok.response.*;
import com.bizark.op.api.entity.op.titok.webhook.TikTokBusinessData;
import com.bizark.op.api.exception.ErpCommonException;
import com.bizark.op.api.parameter.util.TikTokUtil;

import com.bizark.op.api.service.refund.IOrderRefundRequestInfoService;
import com.bizark.op.api.service.refund.IOrderRefundRequestService;
import com.bizark.op.api.service.ticket.IScTicketService;
import com.bizark.op.common.enm.TikTokEnum;
import com.bizark.op.common.handler.handler.platform.AbstractRefundHandler;

import com.bizark.op.common.util.RedisUtils;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.service.annotation.ClearCache;
import com.bizark.op.service.manager.refund.AccountSaleMsgManger;
import com.bizark.op.service.manager.refund.RefundManger;
import com.bizark.op.service.mapper.ticket.ScTicketMapper;
import com.xxl.conf.core.annotation.XxlConf;
import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.bizark.op.api.enm.TikTokApiEnums.*;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/10/18 10:22
 */
@Slf4j
@Lazy
@Component("tikTokShopApi")
public class TikTokShopApiHandler extends AbstractRefundHandler<TikTokResponseBaseEntity, TikTokRequestBaseEntity> {

    @Resource
    private AccountSaleMsgManger accountSaleMsgManger;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private IOrderRefundRequestService orderRefundRequestService;

    @Resource
    private RedisUtils redisUtils;


    @Resource
    private IScTicketService scTicketService;
    @Resource
    private ScTicketMapper scTicketMapper;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    private TikTokUtil tikTokUtil;

    @Resource
    private RefundManger refundManger;

    @Resource
    private IOrderRefundRequestInfoService orderRefundRequestInfoService;

    // true 是正式,false是测试
    @XxlConf(value = "bizark-erp.tiktok.switch", defaultValue = "false")
    public static Boolean TIKTOK_SWITCH;

    @Override
    public TikTokResponseBaseEntity confirmReverseRequest(Map query) {
        String reverseOrderId = String.valueOf(query.get("reverseOrderId"));
        String shopId = String.valueOf(query.get("shopId"));
        String decision = String.valueOf(query.get("decision"));
        ApprovalRefundReq approvalRefundReq = new ApprovalRefundReq();
        approvalRefundReq.setDecision(decision);
        return tikTokUtil.postTikTokShopV2(JSONObject.toJSONString(approvalRefundReq), CONFIRM_REVERSE_REQUEST_NEW.getUrl(), CONFIRM_REVERSE_REQUEST_NEW.getPath().replace("{return_id}", reverseOrderId), ConfirmReverseResp.class, Long.valueOf(shopId), null);
//        return tikTokUtil.getTikTokShopReturn(reverseOrderId, CONFIRM_REVERSE_REQUEST.getUrl(), CONFIRM_REVERSE_REQUEST.getPath(), ConfirmReverseResp.class, shopId);
    }

    @Override
    public List<String> getRejectReasonList(Map query) {
        try {
            String shopId = String.valueOf(query.get("shopId"));
            String returnOrCancelId = String.valueOf(query.get("returnOrCancelId"));
            List<ReverseReasonNew> reverseReasonList = new ArrayList<>();
            if (TIKTOK_SWITCH) {
                Map<String, Object> map = new HashMap<>();
                map.put("return_or_cancel_id", returnOrCancelId);
                TikTokResponseBaseEntity tikTokV2 = tikTokUtil.getTikTokV2(map, GET_REJECT_REASON_LIST_NEW.getUrl(), GET_REJECT_REASON_LIST_NEW.getPath(), TikTokResponseBaseEntity.class, Long.valueOf(shopId));
//                Object result = tikTokUtil.tikTokFinanceBaseApi(Integer.valueOf(shopId), map, null, GET_REJECT_REASON_LIST_NEW, RequestMethod.GET, false, StrUtil.EMPTY);
                RejectReasonDataResp rejectReasonDataResp = JSONObject.parseObject(JSON.toJSONString(tikTokV2.getData()), RejectReasonDataResp.class);
                reverseReasonList = rejectReasonDataResp.getReasons();
                log.info("returnId:{},拒绝原因如下:{}",returnOrCancelId,JSON.toJSONString(reverseReasonList));
            } else {
                ReverseReasonNew reverseReasonNew = new ReverseReasonNew();
                reverseReasonNew.setName("seller_reject_apply_you_have_reached_an_agreement_with_the_buyer");
                reverseReasonNew.setText("You have reached an agreement with the buyer");
                reverseReasonList.add(reverseReasonNew);
                ReverseReasonNew reverseReasonNew1 = new ReverseReasonNew();
                reverseReasonNew1.setName("return_reason_test");
                reverseReasonNew1.setText("You you you");
                ReverseReasonNew reverseReasonNew2 = new ReverseReasonNew();
                reverseReasonNew2.setName("seller_reject_apply_reason_is_unclear_/_lack_of_evidence");
                reverseReasonNew2.setText("Reason is unclear or lack of evidence");
                ReverseReasonNew reverseReasonNew3 = new ReverseReasonNew();
                reverseReasonNew3.setName("seller_reject_apply_buyers_responsibility_for_incorrect_address");
                reverseReasonNew3.setText("The package has been successfully delivered to the shipping address that was provided");
                reverseReasonList.add(reverseReasonNew1);
                reverseReasonList.add(reverseReasonNew2);
                reverseReasonList.add(reverseReasonNew3);
            }
            return reverseReasonList.stream().map(i->{
                String jsonString = JSON.toJSONString(i);
                return JSON.parseObject(jsonString, String.class);
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询拒绝原因失败:{}",e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public TikTokResponseBaseEntity rejectReverseRequest(Map<String, Object> query,List<String> imageIdList,String comment) {
        /*RejectReverseReq req = new RejectReverseReq();
        String reasonKey = "_" + query.get("reasonKey");
        req.setReverseOrderId(String.valueOf(query.get("reverseOrderId")));
        req.setReverseRejectReasonKey(reasonKey);
        String shopId = String.valueOf(query.get("shopId"));
        return tikTokUtil.getTikTokShopReturn(req, REJECT_REVERSE_REQUEST.getUrl(), REJECT_REVERSE_REQUEST.getPath(), GeneralResp.class, shopId);*/
        Object reasonKey = query.get("reasonKey");
        RejectReverserReqNew rejectReverserReqNew = new RejectReverserReqNew();
        rejectReverserReqNew.setRejectReason(reasonKey != null ? String.valueOf(reasonKey) : null);
        rejectReverserReqNew.setDecision("REJECT_REFUND");

        if (StringUtils.isNotEmpty(rejectReverserReqNew.getRejectReason()) &&
                ("seller_reject_apply_reason_is_unclear_/_lack_of_evidence".equalsIgnoreCase(rejectReverserReqNew.getRejectReason())
         || "seller_reject_apply_buyers_responsibility_for_incorrect_address".equalsIgnoreCase(rejectReverserReqNew.getRejectReason()))) {
            if (CollectionUtil.isEmpty(imageIdList)) {
                throw new ErpCommonException("证据不能为空");
            }

        }
        if (CollectionUtil.isNotEmpty(imageIdList)) {
            List<RejectReverserReqNew.RejectReverserReqImage> images = new ArrayList<>();
            imageIdList.forEach(t -> {
                RejectReverserReqNew.RejectReverserReqImage rejectReverserReqImage = new RejectReverserReqNew.RejectReverserReqImage();
                rejectReverserReqImage.setImageId(t);
//        rejectReverserReqImage.setHeight(200);
//        rejectReverserReqImage.setWidth(200);
//        rejectReverserReqImage.setMimeType("image/jpeg");
                images.add(rejectReverserReqImage);
            });
            rejectReverserReqNew.setImages(images);
        }

        if (StringUtils.isNotEmpty(comment)) {
            rejectReverserReqNew.setComment(comment);
        }
        String reverseOrderId = String.valueOf(query.get("reverseOrderId"));
        log.info("拒绝逆向订单请求参数:{}", JSON.toJSONString(rejectReverserReqNew));
        String shopId = String.valueOf(query.get("shopId"));
        return tikTokUtil.postTikTokShopV2(JSONObject.toJSONString(rejectReverserReqNew), REJECT_REVERSE_REQUEST_NEW.getUrl(), REJECT_REVERSE_REQUEST_NEW.getPath().replace("{return_id}", reverseOrderId), GeneralResp.class, Long.valueOf(shopId), null);

    }

    /**
     * tk上传图片 NoShopCipher
     * @param file
     * @param shopId
     * @return
     */
    @Override
    public List<TikTokResponseBaseEntity> batchUploadImage(MultipartFile[] file, Long shopId) {


        List<TikTokResponseBaseEntity> resultList = new ArrayList<>();
        for (MultipartFile multipartFile : file) {
            TikTokResponseBaseEntity t = tikTokUtil.postTikTokShopV2UploadNoShopCipher("", UPLOAD_PRODUCT_IMAGE.getUrl(), UPLOAD_PRODUCT_IMAGE.getPath(), TikTokResponseBaseEntity.class, shopId, multipartFile);
            resultList.add(t);
        }
        return resultList;

    }

    @Override
    @ClearCache
    public void insertBusinessData(JSONObject json, String channel, Map dataMap) {
        // 先拆成 webhook 然后拿到orderId和reverseId,然后调用逆向订单接口 resp shopId webhookJson
        ReverseOrderResponseMsg resp = (ReverseOrderResponseMsg) dataMap.get("resp");
        log.info("逆向订单数据:{}", JSON.toJSON(resp));
        // 拆解属性,不同属性存不同的表,同时保存关联表
        ReverseOrderResponseData data = resp.getData();
        log.info("逆向订单数据详情:{}", JSON.toJSON(data));
        // 退款请求表/退款表/明细表/附件表/ 一起保存 ,有异常一起回滚
        if (TikTokEnum.TIK_TOK.getEnumType().equals(channel)) {
            channel = TikTokEnum.TIK_TOK.getDesc();
        }
        // 避免重复接,也要保证即使没有货物数据也要接进来,后续要通过更新操作完成数据更新 business_type
        ScTicket scTicket = orderRefundRequestService.selectFromScTicketForTikTokTypeRefund(json);
        Boolean isExist = refundManger.checkRefundItem(json, resp);
        // 不存在退款临时单,存在退款项--生成正式单
        if (ObjectUtil.isNull(scTicket) && isExist) {
            refundManger.insertByWebHook(data, channel, json);
        }
        // 不存在退款单,不存在退款项--生成临时单
        if (ObjectUtil.isNull(scTicket) && !isExist) {
            // 生成临时单
            refundManger.insertTemporaryByWebHook(data, channel, json);
        }
        // 存在退款单,不存在退款项--放入补偿死信队列等待补偿
        if (ObjectUtil.isNotNull(scTicket) && !isExist) {
            rabbitTemplate.convertAndSend(MQDefine.TIKTOK_REFUND_EXCHANGE, MQDefine.TIKTOK_REFUND_FOR_COMPENSATION_ROUTING_KEY, json);
        }
        // 存在退款单,存在退款项--判断退款单是否已经转变为正式单
        if (ObjectUtil.isNotNull(scTicket) && isExist) {
            // 已存在退款单,也存在退款项 ,判断临时单是否已经转变为正式单 正式单 应该在前期就拦截了
            LambdaQueryWrapper<OrderRefundRequest> lqw = new LambdaQueryWrapper<>();
            lqw.eq(OrderRefundRequest::getReverseOrderId, resp.getData().reverseList.get(0).getReverseOrderId());
            List<OrderRefundRequest> requests = orderRefundRequestService.list(lqw);
            if (ObjectUtil.isEmpty(requests)) {
                // 临时单更新为正式单
                refundManger.replenishForUpdateScTicket(data, channel, json);
            }
        }
    }


    @Override
    public Map<String, Object> webHookDataAnalysis(JSONObject json, String channel) {
        // webhook数据分析 ,逆向json shopId webhookJson
        Map<String, Object> dataMap = new HashMap<>();

        Map<String, Object> msgMap = new HashMap<>();
        ReverseOrderResponseMsg msg = new ReverseOrderResponseMsg();
        TikTokBusinessData webHookData = JSON.toJavaObject(json, TikTokBusinessData.class);
        TikTokBusinessData tikTokData = new TikTokBusinessData();
        OrderMsg orderMsg = new OrderMsg();
        // 正式
        if (TIKTOK_SWITCH) {
            orderMsg.setOrderId((webHookData.getData().getOrderId()));
            orderMsg.setReverseOrderId(webHookData.getData().getReverseOrderId());
        } else {
            tikTokData = JSONObject.parseObject(getTestWeb(webHookData.getData().getOrderId(), webHookData.getData().getReverseOrderId()), TikTokBusinessData.class);
            orderMsg.setOrderId((tikTokData.getData().getOrderId()));
            orderMsg.setUpdateTimeFrom(tikTokData.getTimestamp());
            orderMsg.setReverseOrderId(tikTokData.getData().getReverseOrderId());
        }
        // 测试
        // tikTokData转换为 ReverseOrderResponseMsg 对象
        orderMsg.setReverseType(2);
        orderMsg.setSortType(1);
        orderMsg.setSize(100);
        orderMsg.setSortBy(1);
        orderMsg.setOffset(0);
        msgMap.put("req", orderMsg);


        if (TIKTOK_SWITCH) {
            // 正式版本
            Account shopId = accountSaleMsgManger.getShopId(webHookData.getShopId());
            msgMap.put("shopId", shopId.getId());
            dataMap.put("shopId", shopId.getId());
            // 得到逆向订单数据 数据转换异常
            msg = (ReverseOrderResponseMsg) getReverseOrderMsg(msgMap);
            if(msg.getData().getReverseList().isEmpty()){
                // tiktok有的时候调用第二次的时候就有数据了,所以这里再调用一次
                // 休眠5s
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    log.info(e.getMessage());
                }
                msg = (ReverseOrderResponseMsg) getReverseOrderMsg(msgMap);
            }
            log.info("正式环境逆向订单数据:{}", JSON.toJSON(msg));
        } else {
            String testMsg = getTest(tikTokData.getData().getOrderId(), tikTokData.getData().getReverseOrderId());
            // 测试版本
            Account shopId = accountSaleMsgManger.getShopId(webHookData.getShopId());
            msgMap.put("shopId", shopId.getId());
            msg = JSONObject.parseObject(testMsg, ReverseOrderResponseMsg.class);
        }
        dataMap.put("resp", msg);
        dataMap.put("webhookJson", json);
        return dataMap;

    }

    private String getTestWeb(String orderId, String reverseOrderId) {
        return "{\n" +
                "  \"type\": 2,\n" +
                "  \"shop_id\": \"7495138550629238920\",\n" +
                "  \"timestamp\": **********,\n" +
                "  \"data\": {\n" +
                "    \"order_id\":" + orderId + ",\n" +
                "    \"reverse_type\": 2,\n" +
                "    \"reverse_user\": 1,\n" +
                "    \"reverse_order_status\": 3,\n" +
                "    \"reverse_event_type\": \"ORDER_REQUEST_CANCEL\",\n" +
                "    \"reverse_order_id\":" + reverseOrderId + ",\n" +
                "    \"update_time\": **********\n" +
                "  }\n" +
                "}";
    }

    private String getTest(String orderId, String reverseOrderId) {
        return "{\n" +
                "    \"data\": {\n" +
                "        \"more\": true,\n" +
                "        \"reverse_list\": [\n" +
                "            {\n" +
                "                \"currency\": \"IDR\",\n" +
                "                \"order_id\": \"" + orderId + "\",\n" +
                "                \"refund_total\": \"5000\",\n" +
                "                \"return_item_list\": [\n" +
                "                    {\n" +
                "                        \"product_images\": \"https://pic1.zhimg.com/70/v2-d094166746ca67d2a2ed61042e2675ce_1440w.avis?source=172ae18b&biz_tag=Post\",\n" +
                "                        \"return_product_id\": \"647622\",\n" +
                "                        \"return_product_name\": \"VIVA-3822-BLK办公椅\",\n" +
                "                        \"return_quantity\": 2,\n" +
                "                        \"seller_sku\": \"C-3822-BK\",\n" +
                "                        \"sku_id\": \"2729382476852921560\",\n" +
                "                        \"sku_name\": \"椅子\"\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"return_reason\": \"Package wasn't received\",\n" +
                "                \"return_tracking_id\": \"E0h1y0j4l1F8G3U6X7p8\",\n" +
                "                \"return_type\": 2,\n" +
                "                \"reverse_order_id\": \"" + reverseOrderId + "\",\n" +
                "                \"reverse_record_list\": [\n" +
                "                    {\n" +
                "                        \"additional_image_list\": \"\",\n" +
                "                        \"additional_message\": \"I want to return\",\n" +
                "                        \"description\": \"\",\n" +
                "                        \"reason_text\": \"Package wasn't received\",\n" +
                "                        \"update_time\": **********\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"reverse_request_time\": **********,\n" +
                "                \"reverse_status_value\": 1,\n" +
                "                \"reverse_type\": 2,\n" +
                "                \"reverse_update_time\": **********\n" +
                "            }\n" +
                "        ]\n" +
                "    }}";
    }


    @Override
    public boolean isNeedByChannel() {
        // 可以改成更具XXlConfig配置来做

        return TIKTOK_SWITCH;


    }

    @Override
    public boolean isNeedByChannel(JSONObject json) {
        TikTokBusinessData tikTokData = JSON.toJavaObject(json, TikTokBusinessData.class);
        // cannel = 1 refun = 2
        if (ObjectUtil.isNotNull(tikTokData)) {
            if (ObjectUtil.isNotNull(tikTokData.getData())) {
                if (ObjectUtil.isNotNull(tikTokData.getData().getReverseType())
                        &&
                        ObjectUtil.isNotNull(tikTokData.getType())) {
                }
                log.info("逆向数据:{},退款业务检查", JSON.toJSON(json));
                boolean isRefund = 2 == tikTokData.getType()
                        && "2".equals(String.valueOf(tikTokData.getData().getReverseType()))
                        && !"99".equals(String.valueOf(tikTokData.getData().getReverseOrderStatus()));
                if (isRefund) {
                    String reverseOrderId = tikTokData.getData().getReverseOrderId();
                    //  以reverseOrderId记为redis key,判断redis中有没有这个值如果有返回true,如果没有返回false并将key和value塞入 设置过期10分钟,写一个lua脚本
                    List<String> keys = new ArrayList<>();
                    keys.add("tiktok_refund_reverseOrderId:"+reverseOrderId);
                    List<String> values = new ArrayList<>();
                    values.add(reverseOrderId);

                    if (!redisUtils.isExist(keys, values)) {
                        List<OrderRefundRequest> requests = orderRefundRequestService.list(new LambdaQueryWrapper<OrderRefundRequest>().eq(OrderRefundRequest::getReverseOrderId, reverseOrderId).last("limit 1"));
                        // 不存在请求单,可能是tiktok的item项没来,但是系统内可能已经有了临时单了,
                        if (CollUtil.isEmpty(requests)) {
                            // 发送mq,1分钟后进行查询,查询scTicket表中是否已经有了此id,如果没有就记录到日志中
                            rabbitTemplate.convertAndSend(MQDefine.TIKTOK_REFUND_EXCHANGE, MQDefine.TIKTOK_REFUND_ROUTING_KEY, json);
                            return Boolean.TRUE;
                        }
                    }
                }
            }
        }
        return Boolean.FALSE;
    }

    @Override
    public TikTokResponseBaseEntity getReverseOrderMsg(Map<String, Object> query) {
        Object req = query.get("req");
        String shopId = String.valueOf(query.get("shopId"));
        log.info("参数检查:{}", JSON.toJSON(req));
        ReverseOrderResponseMsg tikTokShopReturn = tikTokUtil.getTikTokShopReturn(req, GET_REVERSE_ORDER_LIST_V2.getUrl(), GET_REVERSE_ORDER_LIST_V2.getPath(), ReverseOrderResponseMsg.class, shopId);
        log.info("返回数据校验:{}", JSON.toJSON(tikTokShopReturn));
        return tikTokShopReturn;
    }

    @Override
    public TikTokResponseBaseEntity jsonObjectToBusinessData(JSONObject json) {
        return JSON.toJavaObject(json, ReverseOrderResponseMsg.class);
    }


    private String getTestNew(String orderId, String reverseOrderId) {
        return "{\n" +
                "  \"code\": 0,\n" +
                "  \"data\": {\n" +
                "    \"next_page_token\": \"aDU2dHIzMlFhME5CUzJKUDhDdVJhTDM1WmJkeFVTVW9LTkRaSnNaZCtuWjJXVU5CSDhlaA==\",\n" +
                "    \"return_orders\": [\n" +
                "      {\n" +
                "        \"arbitration_status\": \"IN_PROGRESS\",\n" +
                "        \"can_buyer_keep_item\": true,\n" +
                "        \"create_time\": 1708582684,\n" +
                "        \"discount_amount\": [\n" +
                "          {\n" +
                "            \"currency\": \"USD\",\n" +
                "            \"product_platform_discount\": \"26.29\",\n" +
                "            \"product_seller_discount\": \"25\",\n" +
                "            \"shipping_fee_platform_discount\": \"7.9\",\n" +
                "            \"shipping_fee_seller_discount\": \"0\"\n" +
                "          }\n" +
                "        ],\n" +
                "        \"handover_method\": \"DROP_OFF\",\n" +
                "        \"next_return_id\": \"4035310341095393463\",\n" +
                "        \"order_id\": \"" +orderId + "\",\n" +
                "        \"pre_return_id\": \"4035310341095393452\",\n" +
                "        \"refund_amount\": {\n" +
                "          \"currency\": \"USD\",\n" +
                "          \"refund_shipping_fee\": \"0\",\n" +
                "          \"refund_subtotal\": \"54.89\",\n" +
                "          \"refund_tax\": \"6.19\",\n" +
                "          \"refund_total\": \"54.89\",\n" +
                "          \"retail_delivery_fee\": \"0.1\"\n" +
                "        },\n" +
                "        \"return_id\": \""+reverseOrderId+"\",\n" +
                "        \"return_line_items\": [\n" +
                "          {\n" +
                "            \"order_line_item_id\": \"576585205442188205\",\n" +
                "            \"product_image\": {\n" +
                "              \"height\": 200,\n" +
                "              \"url\": \"https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/004797ebfd8c4d3da2df1cc4bfdb0614~tplv-o3syd03w52-origin-jpeg.jpeg?from=4246405447\",\n" +
                "              \"width\": 200\n" +
                "            },\n" +
                "            \"product_name\": \"SMUG Furniture Indoor Furniture Chairs Height Adjustable Criss Cross Chair - Armless Desk Chair No Wheels Cross Legged Office Chair Wide Swivel Home Office Desk Chairs\",\n" +
                "            \"refund_amount\": {\n" +
                "              \"currency\": \"USD\",\n" +
                "              \"refund_shipping_fee\": \"0\",\n" +
                "              \"refund_subtotal\": \"54.89\",\n" +
                "              \"refund_tax\": \"6.19\",\n" +
                "              \"refund_total\": \"54.89\",\n" +
                "              \"retail_delivery_fee\": \"0.1\"\n" +
                "            },\n" +
                "            \"return_line_item_id\": \"4035235471478461357\",\n" +
                "            \"seller_sku\": \"TKC-7261-BG\",\n" +
                "            \"sku_id\": \"1729408751750058470\",\n" +
                "            \"sku_name\": \"Fabric-Beige\"\n" +
                "          }\n" +
                "        ],\n" +
                "        \"return_provider_id\": \"TH27014E9R5Q4G\",\n" +
                "        \"return_provider_name\": \"J&T Express\",\n" +
                "        \"return_reason\": \"ecom_order_delivered_refund_reason_not_received\",\n" +
                "        \"return_reason_text\": \"Package wasn't received\",\n" +
                "        \"return_status\": \"RETURN_OR_REFUND_REQUEST_PENDING\",\n" +
                "        \"return_tracking_number\": \"213456789098765433456\",\n" +
                "        \"return_type\": \"REFUND\",\n" +
                "        \"role\": \"BUYER\",\n" +
                "        \"seller_next_action_response\": [\n" +
                "          {\n" +
                "            \"action\": \"SELLER_RESPOND_REFUND\",\n" +
                "            \"deadline\": **********\n" +
                "          }\n" +
                "        ],\n" +
                "        \"shipment_type\": \"PLATFORM\",\n" +
                "        \"shipping_fee_amount\": [\n" +
                "          {\n" +
                "            \"buyer_paid_return_shipping_fee\": \"0.1\",\n" +
                "            \"currency\": \"USD\",\n" +
                "            \"platform_paid_return_shipping_fee\": \"0.1\",\n" +
                "            \"seller_paid_return_shipping_fee\": \"0.1\"\n" +
                "          }\n" +
                "        ],\n" +
                "        \"update_time\": 1690453136\n" +
                "      }\n" +
                "    ],\n" +
                "    \"total_count\": 100\n" +
                "  },\n" +
                "  \"message\": \"Success\",\n" +
                "  \"request_id\": \"202203070749000101890810281E8C70B7\"\n" +
                "}";
    }
    private String getTestWebNew(String orderId, String reverseOrderId) {
        return "{\n" +
                "  \"type\": 2,\n" +
                "  \"shop_id\": \"7495341470283958758\",\n" +
                "  \"timestamp\": 1708755489,\n" +
                "  \"data\": {\n" +
                "    \"order_id\":" + orderId + ",\n" +
                "    \"reverse_type\": 2,\n" +
                "    \"reverse_user\": 1,\n" +
                "    \"reverse_order_status\": \"RETURN_OR_REFUND_REQUEST_PENDING\",\n" +
                "    \"reverse_event_type\": \"UNSUPPORTED\",\n" +
                "    \"reverse_order_id\":" + reverseOrderId + ",\n" +
                "    \"update_time\": 1708755489\n" +
                "  }\n" +
                "}";
    }
    @Override
    public TikTokResponseBaseEntity getReverseOrderMsgNew(Map<String, Object> query) {
        Object req = query.get("req");
        String shopId = String.valueOf(query.get("shopId"));
        log.info("参数检查:{}", JSON.toJSON(req));
        ReverseOrderResponseMsg tikTokShopReturn = tikTokUtil.postTikTokShopV2(JSONObject.toJSONString(req), GET_REVERSE_ORDER_LIST_V2.getUrl(), GET_REVERSE_ORDER_LIST_V2_NEW.getPath(), ReverseOrderResponseMsg.class, Long.valueOf(shopId),null);
        log.info("返回数据校验:{}", JSON.toJSON(tikTokShopReturn));
        return tikTokShopReturn;
    }

    @Override
    public Map<String, Object> webHookDataAnalysisNew(JSONObject json, String channel) {
        // webhook数据分析 ,逆向json shopId webhookJson
        Map<String, Object> dataMap = new HashMap<>();

        Map<String, Object> msgMap = new HashMap<>();
        ReverseOrderResponseMsg msg = new ReverseOrderResponseMsg();
        TikTokBusinessData webHookData = JSON.toJavaObject(json, TikTokBusinessData.class);
        TikTokBusinessData tikTokData = new TikTokBusinessData();
//        OrderMsg orderMsg = new OrderMsg();
        OrderNewMsg orderNewMsg = new OrderNewMsg();
        // 正式
        if (TIKTOK_SWITCH) {
            orderNewMsg.setOrderIds(new String[]{webHookData.getData().getOrderId()});
            orderNewMsg.setReturnIds(new String[]{webHookData.getData().getReverseOrderId()});
//            orderNewMsg.setReturnTypes(new String[]{"REFUND"});
//            orderNewMsg.setReturnStatus(webHookData.getData().getReverseOrderStatus());
//            orderNewMsg.setBuyerUserIds(webHookData.getData().getReverseUser());
//            orderNewMsg.setReturnTypes(webHookData.getData().getReverseType());
//            orderMsg.setOrderId((webHookData.getData().getOrderId()));
//            orderMsg.setReverseOrderId(webHookData.getData().getReverseOrderId());
        } else {
//            tikTokData = JSONObject.parseObject(getTestWeb(webHookData.getData().getOrderId(), webHookData.getData().getReverseOrderId()), TikTokBusinessData.class);
//            orderMsg.setOrderId((tikTokData.getData().getOrderId()));
//            orderMsg.setUpdateTimeFrom(tikTokData.getTimestamp());
//            orderMsg.setReverseOrderId(tikTokData.getData().getReverseOrderId());
            tikTokData = JSONObject.parseObject(getTestWebNew(webHookData.getData().getOrderId(), webHookData.getData().getReverseOrderId()), TikTokBusinessData.class);
            orderNewMsg.setOrderIds(new String[]{tikTokData.getData().getOrderId()});
            orderNewMsg.setReturnIds(new String[]{tikTokData.getData().getReverseOrderId()});
            orderNewMsg.setReturnStatus(new String[]{tikTokData.getData().getReverseOrderStatus()});
            orderNewMsg.setBuyerUserIds(new String[]{webHookData.getData().getReverseUser()});
            orderNewMsg.setReturnTypes(new String[]{"REFUND"});
        }
        // 测试
        // tikTokData转换为 ReverseOrderResponseMsg 对象
       /* orderMsg.setReverseType(2);
        orderMsg.setSortType(1);
        orderMsg.setSize(100);
        orderMsg.setSortBy(1);
        orderMsg.setOffset(0);
         msgMap.put("req", orderMsg);*/
        msgMap.put("req", orderNewMsg);


        if (TIKTOK_SWITCH) {
            // 正式版本
            Account shopId = accountSaleMsgManger.getShopId(webHookData.getShopId());
            msgMap.put("shopId", shopId.getId());
            dataMap.put("shopId", shopId.getId());
            // 得到逆向订单数据 数据转换异常
            msg = (ReverseOrderResponseMsg) getReverseOrderMsgNew(msgMap);
            if(msg.getData().getReturnOrdersList().isEmpty()){
                // tiktok有的时候调用第二次的时候就有数据了,所以这里再调用一次
                // 休眠5s
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    log.info(e.getMessage());
                }
                msg = (ReverseOrderResponseMsg) getReverseOrderMsgNew(msgMap);
            }
            log.info("正式环境逆向订单数据:{}", JSON.toJSON(msg));
        } else {
            String testMsg = getTestNew(tikTokData.getData().getOrderId(), tikTokData.getData().getReverseOrderId());
            // 测试版本
            Account shopId = accountSaleMsgManger.getShopId(webHookData.getShopId());
            msgMap.put("shopId", shopId.getId());
            msg = JSONObject.parseObject(testMsg, ReverseOrderResponseMsg.class);
//            msg.setData(msg.getData().setReverseList(null));
        }
        dataMap.put("resp", msg);
        dataMap.put("webhookJson", json);
        return dataMap;

    }


    @Override
    public boolean isNeedByChannelNew(JSONObject json) {
        TikTokBusinessData tikTokData = JSON.toJavaObject(json, TikTokBusinessData.class);
        // cannel = 1 refun = 2
        if (ObjectUtil.isNotNull(tikTokData)) {
            if (ObjectUtil.isNotNull(tikTokData.getData())) {
                if (ObjectUtil.isNotNull(tikTokData.getData().getReverseType())
                        &&
                        ObjectUtil.isNotNull(tikTokData.getType())) {
                }
                log.info("逆向数据:{},退款业务检查", JSON.toJSON(json));
                boolean isRefund = 2 == tikTokData.getType()
                        && "2".equals(String.valueOf(tikTokData.getData().getReverseType()))
                        && !"99".equals(String.valueOf(tikTokData.getData().getReverseOrderStatus()));
                if (isRefund) {
                    String reverseOrderId = tikTokData.getData().getReverseOrderId();
                    //  以reverseOrderId记为redis key,判断redis中有没有这个值如果有返回true,如果没有返回false并将key和value塞入 设置过期10分钟,写一个lua脚本
                    List<String> keys = new ArrayList<>();
                    keys.add("tiktok_refund_reverseOrderId:"+reverseOrderId);
                    List<String> values = new ArrayList<>();
                    values.add(reverseOrderId);

                    if (!redisUtils.isExist(keys, values)) {
//                        List<OrderRefundRequest> requests = orderRefundRequestService.list(new LambdaQueryWrapper<OrderRefundRequest>().eq(OrderRefundRequest::getReverseOrderId, reverseOrderId).last("limit 1"));
                        List<OrderRefundRequestInfo> requests = orderRefundRequestInfoService.list(new LambdaQueryWrapper<OrderRefundRequestInfo>().eq(OrderRefundRequestInfo::getReturnId, reverseOrderId).last("limit 1"));
                        // 不存在请求单,可能是tiktok的item项没来,但是系统内可能已经有了临时单了,
                        if (CollUtil.isEmpty(requests)) {
                            // 发送mq,1分钟后进行查询,查询scTicket表中是否已经有了此id,如果没有就记录到日志中
                            rabbitTemplate.convertAndSend(MQDefine.TIKTOK_REFUND_EXCHANGE, MQDefine.TIKTOK_REFUND_ROUTING_KEY, json);
                            return Boolean.TRUE;
                        }
                    }
                }
            }
        }
        return Boolean.FALSE;
    }

    @Override
    @ClearCache
    public void insertBusinessDataNew(JSONObject json, String channel, Map dataMap) {
        // 先拆成 webhook 然后拿到orderId和reverseId,然后调用逆向订单接口 resp shopId webhookJson
        ReverseOrderResponseMsg resp = (ReverseOrderResponseMsg) dataMap.get("resp");
        log.info("逆向订单数据:{}", JSON.toJSON(resp));
        // 拆解属性,不同属性存不同的表,同时保存关联表
        ReverseOrderResponseData data = resp.getData();
        log.info("逆向订单数据详情:{}", JSON.toJSON(data));
        // 退款请求表/退款表/明细表/附件表/ 一起保存 ,有异常一起回滚
        if (TikTokEnum.TIK_TOK.getEnumType().equals(channel)) {
            channel = TikTokEnum.TIK_TOK.getDesc();
        }
        // 避免重复接,也要保证即使没有货物数据也要接进来,后续要通过更新操作完成数据更新 business_type
        ScTicket scTicket = orderRefundRequestInfoService.selectFromScTicketForTikTokTypeRefund(json);
        Boolean isExist = refundManger.checkRefundItem(json, resp);
        // 不存在退款临时工单,存在退款项--生成正式退款工单
        if (ObjectUtil.isNull(scTicket) && isExist) {
            refundManger.insertByWebHookNew(data, channel, json);
        }
        // 不存在退款工单,不存在退款项--生成临时退款工单
        if (ObjectUtil.isNull(scTicket) && !isExist) {
            // 生成临时单 临时单是不是也需要补偿策略 客服如果都是等待webhook的自动退款的话就不必要
            refundManger.insertTemporaryByWebHook(data, channel, json);
        }
        // 存在退款工单,不存在退款项--放入补偿死信队列等待补偿
        if (ObjectUtil.isNotNull(scTicket) && !isExist) {
            rabbitTemplate.convertAndSend(MQDefine.TIKTOK_REFUND_EXCHANGE, MQDefine.TIKTOK_REFUND_FOR_COMPENSATION_ROUTING_KEY, json);
        }
        if (ObjectUtil.isNotNull(scTicket) && isExist) {
            // 已存在退款工单,也存在退款项 ,判断临时工单是否已经转变为正式工单 正式单 应该在前期就拦截了
            LambdaQueryWrapper<OrderRefundRequestInfo> lqw = new LambdaQueryWrapper<>();
            lqw.eq(OrderRefundRequestInfo::getReturnId, resp.getData().getReturnOrdersList().get(0).getReturnId());
            List<OrderRefundRequestInfo> requests = orderRefundRequestInfoService.list(lqw);
            if (ObjectUtil.isEmpty(requests)) {
                // 临时工单更新为正式工单
                refundManger.replenishForUpdateScTicketNew(data, channel, json);
            }
        }
    }
}
