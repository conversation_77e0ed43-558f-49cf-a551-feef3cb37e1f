package com.bizark.op.service.service.inventory;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.beust.jcommander.internal.Lists;
import com.bizark.op.api.enm.inventory.InventoryType;
import com.bizark.op.api.enm.log.LogEnums;
import com.bizark.op.api.enm.sale.SyncMappingEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.amazon.AmazonReportData;
import com.bizark.op.api.entity.op.inventory.InventorySyncBean;
import com.bizark.op.api.entity.op.inventory.ProductChannelsInventory;
import com.bizark.op.api.entity.op.inventory.ProductChannelsStockMessage;
import com.bizark.op.api.entity.op.log.ErpOperateLog;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.inventory.ProductChannelsInventoryService;
import com.bizark.op.api.service.log.ErpOperateLogService;
import com.bizark.op.api.service.sale.ProductChannelsService;
import com.bizark.op.common.util.BeanCopyUtils;
import com.bizark.op.common.util.UserUtils;
import com.bizark.op.service.handler.inventory.sync.AbstractInventorySynchronizer;
import com.bizark.op.service.handler.inventory.sync.AmazonInventorySynchronizer;
import com.bizark.op.service.handler.inventory.sync.InventorySynchronizerFactory;
import com.bizark.op.service.mapper.inventory.ProductChannelsInventoryMapper;
import lombok.extern.slf4j.Slf4j;
import org.mvel2.sh.CommandException;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bizark.op.api.cons.RedisCons.SKU_MAP_INVENTORY_LOCK;

@Service
@Slf4j
public class ProductChannelsInventoryServiceImpl extends ServiceImpl<ProductChannelsInventoryMapper,ProductChannelsInventory> implements ProductChannelsInventoryService {

    @Autowired
    private ProductChannelsService productChannelsService;

    @Autowired
    private InventorySynchronizerFactory inventorySynchronizerFactory;

    @Autowired
    private AccountService accountService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private AmazonInventorySynchronizer amazonInventorySynchronizer;

    @Autowired
    private ProductChannelsInventoryMapper productChannelsInventoryMapper;

    @Autowired
    private ErpOperateLogService erpOperateLogService;

    @Override
    public void syncProductChannelsInventory(Integer[] ids) {
        List<ProductChannels> channels = productChannelsService.listByIds(Arrays.asList(ids));
        if (CollectionUtil.isEmpty(channels)) {
            log.error("product channels not found : {}", Arrays.toString(ids));
            return;
        }
        syncProductChannelsInventory(channels);

    }

    @Override
    public void syncProductChannelsInventory(List<ProductChannels> channels) {
        List<String> accountIds = channels.stream()
                .map(ProductChannels::getAccountId)
                .distinct().collect(Collectors.toList());
        List<Account> accounts = accountService.lambdaQuery()
                .in(Account::getFlag, accountIds)
                .list();
        if (CollectionUtil.isEmpty(accounts)) {
            log.error("inventory sync error , accounts not found : {}", accountIds);
            return;
        }

        Map<String, Account> accountMap = accounts.stream()
                .collect(Collectors.toMap(Account::getFlag, Function.identity(), (a, b) -> a));

        channels = channels.stream()
                .filter(c -> accountMap.containsKey(c.getAccountId()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(channels)) {
            log.error("inventory sync error , productChannel account not found ");
            return;
        }

        Map<String, List<ProductChannels>> flagGroup = channels.stream()
                .collect(Collectors.groupingBy(ProductChannels::getAccountId));

        for (Map.Entry<String, List<ProductChannels>> entry : flagGroup.entrySet()) {
            Account account = accountMap.get(entry.getKey());
            List<ProductChannels> productChannels = entry.getValue();
            String type = account.getType();


        }


    }

    @Override
    public void generateAmazonInventory(Integer accountId, List<AmazonReportData> amazonReportData) {
        Account account = accountService.getById(accountId);
        if (Objects.isNull(account)) {
            log.error("SYNC_INVENTORY_AMAZON - 未获取到店铺：{}", accountId);
            return;
        }
        log.info("SYNC_INVENTORY_AMAZON - 开始获取Amazon同步器：{}", accountId);
        InventorySyncBean syncBean = new InventorySyncBean();
        syncBean.setAccount(account);
        syncBean.setReportData(amazonReportData);
        amazonInventorySynchronizer.sync(syncBean);

    }

    @Override
    public List<ProductChannelsInventory> getInventoryByChannelIds(Integer[] channelIds) {
        return productChannelsInventoryMapper.selectInventoryByChannelIds(Arrays.asList(channelIds));
    }


    @Override
    public List<ProductChannelsInventory> getInventory(List<Integer> channelIds) {
        List<ProductChannelsInventory> inventories = productChannelsInventoryMapper.selectByChannelIds(channelIds);
        for (ProductChannelsInventory inventory : inventories) {
            if (StrUtil.isBlank(inventory.getOrgWarehouse())) {
                inventory.setOrgWarehouse(inventory.getWarehouseId());
            }
        }
        List<ProductChannelsInventory> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(inventories)) {
            Map<String, List<ProductChannelsInventory>> map = inventories.stream()
                    .collect(Collectors.groupingBy(c -> c.getChannelId() + "&&" + c.getWarehouseId()));
            for (Map.Entry<String, List<ProductChannelsInventory>> entry : map.entrySet()) {
                List<ProductChannelsInventory> value = entry.getValue();
                ProductChannelsInventory inventory = value.get(0);
                if (value.size() > 1) {
                    String warehouseStr = value.stream().map(ProductChannelsInventory::getOrgWarehouse).collect(Collectors.joining(","));
                    inventory.setOrgWarehouse(warehouseStr);
                }
                result.add(inventory);
            }
        }
        List<ProductChannelsInventory> finallyData = result.stream()
                .filter(c -> StrUtil.isBlank(c.getOrgWarehouse()))
                .collect(Collectors.toList());

        // 处理多仓库映射
        Map<String, List<ProductChannelsInventory>> map = result.stream()
                .filter(c -> StrUtil.isNotBlank(c.getOrgWarehouse()))
                .collect(Collectors.groupingBy(ProductChannelsInventory::getOrgWarehouse));

        for (Map.Entry<String, List<ProductChannelsInventory>> entry : map.entrySet()) {
            if (entry.getValue().size() == 1) {
                finallyData.addAll(entry.getValue());
                continue;
            }
            List<ProductChannelsInventory> value = entry.getValue();
            ProductChannelsInventory inventory = BeanCopyUtils.copyBean(value.get(0), ProductChannelsInventory.class);

            BigDecimal total = value.stream()
                    .peek(c -> c.setQuantity(StrUtil.isBlank(c.getQuantity()) ? "0" : c.getQuantity()))
                    .map(ProductChannelsInventory::getQuantity)
                    .map(BigDecimal::new)
                    .reduce(BigDecimal::add)
                    .get();

            inventory.setQuantity(total.toString());
            finallyData.add(inventory);
        }


        return finallyData;
    }

    @Override
    public void syncInventory(Integer orgId, String type) {
        LambdaQueryChainWrapper<Account> chainWrapper = accountService.lambdaQuery().eq(Account::getType, type);

        if (Objects.nonNull(orgId)) {
            chainWrapper.eq(Account::getOrgId, orgId);
        } else {
            chainWrapper.notIn(Account::getOrgId, Arrays.asList(1000049, 1000337));
        }

        log.info("平台库存同步任务 - {} - {}", orgId, type);

        List<Account> accounts = chainWrapper.list();
        if (CollectionUtil.isEmpty(accounts)) {
            log.error("未获取到店铺信息");
            throw new CommandException("未获取到店铺信息:" + type);
        }
        for (Account account : accounts) {
            try {
                doSyncInventory(account);
            } catch (Exception e) {
                log.info("库存数据同步异常 - {}", account.getFlag(), e);
            }
        }
    }
    @Override
    public void syncInventory( String type) {
        LambdaQueryChainWrapper<Account> chainWrapper = accountService.lambdaQuery().eq(Account::getType, type);
        log.info("平台库存同步任务 - {}", type);
        List<Account> accounts = chainWrapper.list();
        if (CollectionUtil.isNotEmpty(accounts)) {
            for (Account account : accounts) {
                try {
                    doSyncInventory(account);
                } catch (Exception e) {
                    log.info("库存数据同步异常 - {}", account.getFlag(), e);
                }
            }
        }
    }

    private void doSyncInventory(Account account) {
       List<ProductChannels> channels = null;
       Integer cursor = 0;

       while (CollectionUtil.isNotEmpty(channels = selectByAccountAndIdCursorAsc(account.getFlag(), cursor, 100))) {
           // 更新游标
           cursor = channels.get(channels.size() - 1).getId();

           InventoryType inventoryType = InventoryType.typeOf(account.getType());
           if (Objects.isNull(inventoryType)) {
               log.error("SYNC_INVENTORY_ERROR - 未获取到对应同步器类型:{}", account.getType());
               continue;
           }

           try {
               AbstractInventorySynchronizer synchronizer = inventorySynchronizerFactory.getSynchronizer(inventoryType);
               synchronizer.sync(new InventorySyncBean(account, channels));
           } catch (Exception e) {
               e.printStackTrace();
               log.error("SYNC_INVENTORY_ERROR - 库存同步异常 ：{}", e.getMessage());
           }

       }
    }

    private List<ProductChannels> selectByAccountAndIdCursorAsc(String accountFlag, Integer cursor, int limit) {
        return productChannelsService.lambdaQuery()
                .eq(ProductChannels::getAccountId, accountFlag)
                .gt(ProductChannels::getId, cursor)
                .eq(ProductChannels::getDisabledAt, 0)
                .eq(ProductChannels::getDisabledName, "")
                .orderByAsc(ProductChannels::getId)
                .last("limit 100")
                .list();
    }


    @Override
    public List<ProductChannelsInventory> selectTotalInventoryByChannelIds( List<Integer> channelIds) {
        return productChannelsInventoryMapper.selectTotalInventoryByChannelIds(channelIds);
    }

    @Override
    public String selectAddressCodeById(Integer stockAccountId) {
        return productChannelsInventoryMapper.selectAddressCodeById(stockAccountId);
    }

    @Override
    public void handleInventoryMessage(List<ProductChannelsStockMessage> stockMessage,String type) {
        if (StrUtil.isBlank(type)) {
            log.error("erp.sale.stock.queue 消费异常：type is empty");
            return;
        }
        String storeName = stockMessage.get(0).getStoreName();
        Account account = accountService.lambdaQuery()
                .eq(Account::getFlag, storeName)
                .eq(Account::getActive, "Y")
                .one();
        if (Objects.isNull(account)) {
            log.error("erp.sale.stock.queue 库存同步更新失败，店铺未获取到：{}", storeName);
            return;
        }
        Map<String, List<ProductChannelsStockMessage>> map = stockMessage.stream().collect(Collectors.groupingBy(ProductChannelsStockMessage::getSku));

        for (Map.Entry<String, List<ProductChannelsStockMessage>> entry : map.entrySet()) {
            List<ProductChannelsStockMessage> entryValue = entry.getValue();
            // 数据封装
            InventoryType typeEnum = InventoryType.typeOf(type.toLowerCase());
            if (Objects.isNull(typeEnum)) {
                log.error("erp.sale.stock.queue 消费异常：未获取到渠道对应枚举类型 - {}", type);
                return;
            }
            AbstractInventorySynchronizer synchronizer = inventorySynchronizerFactory.getSynchronizer(typeEnum);
            log.info("erp.sale.stock.queue SKU映射库存监听，获取到同步器：{}", synchronizer.getClass().getName());
            synchronizer.sync(new InventorySyncBean(entryValue));
        }


    }

    @Override
    public void handleInventoryMessage(ProductChannelsStockMessage stockMessage, String type) {
        if (StrUtil.isBlank(type)) {
            log.error("erp.sale.stock.queue 消费异常：type is empty");
            return;
        }
        Account account = accountService.lambdaQuery()
                .eq(Account::getFlag, stockMessage.getStoreName())
                .one();
        if (Objects.isNull(account)) {
            log.error("库存同步更新失败，店铺未获取到：{}", stockMessage.getStoreName());
            return;
        }
        InventoryType typeEnum = InventoryType.typeOf(type.toLowerCase());
        if (Objects.isNull(typeEnum)) {
            log.error("erp.sale.stock.queue 消费异常：未获取到渠道对应枚举类型 - {}", type);
            return;
        }
        AbstractInventorySynchronizer synchronizer = inventorySynchronizerFactory.getSynchronizer(typeEnum);
        log.info("SKU映射库存监听，获取到同步器：{}", synchronizer.getClass().getName());
        synchronizer.sync(new InventorySyncBean(Lists.newArrayList(stockMessage)));
    }

    @Override
    public void repairInventory() {
        List<ProductChannelsInventory> errorSku = productChannelsInventoryMapper.selectErrorInventory();
        if (CollectionUtil.isEmpty(errorSku)) {
            log.error("repairInventory - 未获取到 errorSku");
            return;
        }

        for (ProductChannelsInventory inventory : errorSku) {
            ProductChannelsInventory one = this.lambdaQuery()
                    .eq(ProductChannelsInventory::getChannelId, inventory.getChannelId())
                    .eq(ProductChannelsInventory::getWarehouseId, inventory.getWarehouseId())
                    .orderByDesc(ProductChannelsInventory::getSyncLastTime)
                    .last("limit 1")
                    .one();
            if (Objects.nonNull(one)) {
                log.info("删除库存信息：{} {} {} {}", inventory.getChannelId(), inventory.getSellerSku(), inventory.getWarehouseId(), one.getId());
                productChannelsInventoryMapper.deleteErrorInventory(inventory.getChannelId(), inventory.getWarehouseId(), one.getId());
            }
        }

    }

    @Override
    @Transactional
    public void handleTemuInventory(ProductChannels channels) {

        if (Objects.isNull(channels.getInventory())) {
            return;
        }


        if (!Objects.equals(channels.getSaleChannel(), SyncMappingEnum.SkuGoodsMapSaleChannelEnum.TEMU.getEhengjian())) {
            return;
        }

        Account account = accountService.lambdaQuery()
                .eq(Account::getFlag, channels.getAccountId())
                .eq(Account::getOrgId, channels.getOrgId())
                .one();

        if (Objects.isNull(account)) {
            log.error("Temu库存更新失败 - 未获取到店铺信息:{}", JSON.toJSONString(channels));
            return;
        }

        // 获取当前库存
        List<ProductChannelsInventory> channelsInventories = selectTotalInventoryByChannelIds(CollectionUtil.newArrayList(channels.getId()));

        Integer currentInventory = CollectionUtil.isEmpty(channelsInventories) ? 0 : new BigDecimal(channelsInventories.get(0).getQuantity()).intValue();
        Integer channelsInventory = channels.getInventory();

        RLock lock = redissonClient.getLock(SKU_MAP_INVENTORY_LOCK + channels.getId());

        try {


            lock.lock(5, TimeUnit.SECONDS);

            List<ProductChannelsInventory> inventories = this.lambdaQuery()
                    .eq(ProductChannelsInventory::getChannelId, channels.getId())
                    .orderByDesc(ProductChannelsInventory::getUpdateLastTime)
                    .list();


            Long dbId = null;
            Date updateLastTime = null;
            if (CollectionUtil.isNotEmpty(inventories)) {
                ProductChannelsInventory inventory = inventories.get(0);
                dbId = inventory.getId();
                updateLastTime = inventory.getUpdateLastTime();
                if (inventories.size() > 1) {
                    List<Long> ids = inventories.stream()
                            .skip(1)
                            .map(ProductChannelsInventory::getId)
                            .collect(Collectors.toList());
                    productChannelsInventoryMapper.deleteByIds(ids);
                }

            }
            Date date = new Date();

            Integer currentUserId = UserUtils.getCurrentUserId(0);
            String currentUserName = UserUtils.getCurrentUserName("System");
            ProductChannelsInventory inventory = new ProductChannelsInventory();
            inventory.setOrgId(account.getOrgId());
            inventory.setChannelId(channels.getId());
            inventory.setSellerSku(channels.getSellerSku());
            inventory.setAccountId(account.getId());
            inventory.setAccountFlag(account.getFlag());
            inventory.setQuantity(channels.getInventory().toString());
            inventory.setCreatedBy(currentUserId);
            inventory.setCreatedName(currentUserName);
            inventory.setUpdatedBy(currentUserId);
            inventory.setUpdatedName(currentUserName);
            inventory.setId(dbId);
            inventory.setCreatedAt(date);
            inventory.setUpdatedAt(date);
            inventory.setSyncLastTime(date);
            inventory.setUpdateLastTime(updateLastTime);
            this.saveOrUpdate(inventory);

        }finally {
            lock.unlock();

        }

        if (!Objects.equals(currentInventory, channelsInventory)) {
            // 记录日志
            ErpOperateLog operateLog = new ErpOperateLog();
            operateLog.setOperateName("SKU映射");
            operateLog.setOperateUserId(0L);
            operateLog.setOperateUserName("System");
            operateLog.setLogType(1);
            operateLog.setOperateAt(LocalDateTime.now());
            operateLog.setBusinessId(channels.getId().longValue());
            operateLog.setFieldDesc("平台库存");
            operateLog.setOperateTable("dashboard.product_channels");
            operateLog.setOperateOldValue(String.valueOf(currentInventory));
            operateLog.setOperateNewValue(String.valueOf(channelsInventory));
            operateLog.setOperateTarget("inventory");
            operateLog.setOperateType(LogEnums.OperateTypeEnum.UPDATE.getValue());
            operateLog.setCreatedBy(0);
            operateLog.setCreatedName("System");
            operateLog.setCreatedAt(new Date());
            erpOperateLogService.save(operateLog);
        }



    }

}
