package com.bizark.op.service.mapper.mar.contactCustomer;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bizark.op.api.entity.op.mar.contactCustomer.entity.MarSendMessageRule;
import com.bizark.op.api.entity.op.mar.contactCustomer.request.ContactCustomerOrderRequest;
import com.bizark.op.api.entity.op.mar.contactCustomer.request.ContactCustomerRuleListRequest;
import com.bizark.op.api.entity.op.mar.contactCustomer.vo.ContactCustomerOrderVo;
import com.bizark.op.api.entity.op.mar.contactCustomer.vo.ContactCustomerRuleListVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【mar_send_message_rule(运营发送信息规则表)】的数据库操作Mapper
 * @createDate 2024-06-19 15:11:09
 * @Entity generator.domain.MarSendMessageRule
 */
public interface MarSendMessageRuleMapper extends BaseMapper<MarSendMessageRule> {

    /**
     * @Description:查询规则列表
     * @Author: wly
     * @Date: 2024/6/26 15:03
     * @Params: [request]
     * @Return: java.util.List<com.bizark.op.api.entity.op.mar.contactCustomer.vo.ContactCustomerRuleListVo>
     **/

    List<ContactCustomerRuleListVo> selectContactCustomerRuleList(ContactCustomerRuleListRequest request);


    List<ContactCustomerOrderVo> queryOrderList(ContactCustomerOrderRequest contactCustomerOrderRequest);


    /**
     * Description: 根据条件查询退货订单
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/7/8
     */
    List<ContactCustomerOrderVo> queryReturnOrderList(ContactCustomerOrderRequest contactCustomerOrderRequest);



    /**
     * Description: 根据条件查询退款订单
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/7/8
     */
    List<ContactCustomerOrderVo> queryRefoundOrderList(ContactCustomerOrderRequest contactCustomerOrderRequest);


    /**
     * Description: 根据条件查询退货订单
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/7/8
     */
    List<String> queryParentAsin(@Param("channelId") Integer channelId, @Param("orderNo") String orderNo);


    /**
     * Description: 根据条件查询退货跟踪号数量。
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/7/8
     */
    ContactCustomerOrderVo queryTracking(@Param("channelId") Integer channelId, @Param("orderNo") String orderNo);


    /**
     * Description: 根据条件查询退货跟踪号数量。
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/7/8
     */
    List<ContactCustomerOrderVo> queryTrackingByItem(@Param("channelId") Integer channelId, @Param("orderNo") String orderNo);
}




