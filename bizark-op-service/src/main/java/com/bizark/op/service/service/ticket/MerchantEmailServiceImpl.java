package com.bizark.op.service.service.ticket;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bizark.common.contract.AuthUserDetails;
//import com.bizark.op.api.cons.ScTicketConstant;
//import com.bizark.op.api.enm.ticket.EmailFolderEnum;
import com.bizark.op.api.cons.MQDefine;
import com.bizark.op.api.cons.RedisKeyConstant;
import com.bizark.op.api.cons.ScTicketConstant;
import com.bizark.op.api.enm.mar.ReturnBusinessStatus;
import com.bizark.op.api.enm.ticket.EmailFolderEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.order.SaleOrderItems;
import com.bizark.op.api.entity.op.order.SaleOrders;
import com.bizark.op.api.entity.op.returns.entity.ReturnInfoEntity;
import com.bizark.op.api.entity.op.returns.wayFair.WayfairReturnInfo;
import com.bizark.op.api.entity.op.returns.wayFair.WayfairReturnItemInfo;
import com.bizark.op.api.entity.op.sale.Products;
import com.bizark.op.api.entity.op.ticket.*;
//import com.bizark.op.api.entity.op.ticket.VO.EmailInfoVO;
import com.bizark.op.api.entity.op.ticket.VO.*;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.amazon.AmazonReturnService;
import com.bizark.op.api.service.order.SaleOrderCancelService;
import com.bizark.op.api.service.order.SaleOrderItemsService;
import com.bizark.op.api.service.order.SaleOrdersService;
import com.bizark.op.api.service.sale.ProductsService;
import com.bizark.op.api.service.sys.ISysConfigService;
import com.bizark.op.api.service.ticket.*;
import com.bizark.op.api.service.wayfair.WayfairReturnInfoService;
import com.bizark.op.api.service.wayfair.WayfairReturnItemInfoService;
import com.bizark.op.common.config.WeChatBootConfigure;
import com.bizark.op.common.util.DateUtil;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.common.util.WeComRobotUtil;
import com.bizark.op.service.mapper.returns.ReturnInfoMapper;
import com.bizark.op.service.mapper.ticket.*;
import com.bizark.op.service.util.EmailApi;
import com.bizark.op.service.util.GraphEmailApi;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.protocol.HTTP;
import org.apache.shiro.SecurityUtils;
import org.htmlparser.Node;
import org.htmlparser.Parser;
import org.htmlparser.filters.StringFilter;
import org.htmlparser.lexer.Lexer;
import org.htmlparser.util.NodeList;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 邮箱配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-12
 */
@Slf4j
@Service
public class MerchantEmailServiceImpl implements IMerchantEmailService {

    @Autowired
    private IScTicketHandleService scTicketHandleService;

    @Autowired
    private MerchantEmailMapper merchantEmailMapper;

    @Autowired
    private ISyncEmailInfoService emailInfoService;

    @Autowired
    private IScStationLetterService scStationLetterService;

    @Autowired
    private IScTicketService scTicketService;

//    @Autowired
//    private ScTicketSourceHisMapper scTicketSourceHisMapper;

    @Autowired
    private IScTicketLogService scTicketLogService;

    @Autowired
    private ScTicketHandleMapper scTicketHandleMapper;

//    @Autowired
//    private IObsAttachmentService obsAttachmentService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private SaleOrdersService saleOrdersService;



    @Autowired
    private IConfTicketAssignService confTicketAssignService;

//    @Autowired
//    private AmzOrderVMapper amzOrderVMapper;

    @Autowired
    private SyncEmailInfoMapper emailInfoMapper;

    @Autowired(required = false)
    private ISysConfigService sysConfigService;

    @Autowired
    private IScTicketSourceHisService scTicketSourceHisService;



    @Autowired
    private IScCustomerService scCustomerService;



    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private ScCustomerMapper scCustomerMapper;

    @Autowired
    private TicketAttachesService ticketAttachesService;


    @Autowired
    private WayfairReturnInfoService wayfairReturnInfoService;

    @Autowired
    private WayfairReturnItemInfoService wayfairReturnItemInfoService;

    @Resource
    private SaleOrderItemsService saleOrderItemsService;

    @Autowired
    private ReturnInfoMapper returnInfoMapper;

    @Autowired
    private ProductsService productsService;

    @Autowired
    @Lazy
    private AmazonReturnService amazonReturnService;


    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private WeChatBootConfigure weChatBootConfigure;

    @Autowired
    @Lazy
    private SaleOrderCancelService saleOrderCancelService;

    @Autowired
    private EmailApi emailApi;

    @Autowired
    private GraphEmailApi graphEmailApi;


    @Autowired
    private SaleOrdersMapper saleOrdersMapper;


    /**
     * 查询邮箱配置
     *
     * @param emailId 邮箱配置ID
     * @return 邮箱配置
     */
    @Override
    public MerchantEmail selectMerchantEmailById(Long emailId) {
        return merchantEmailMapper.selectMerchantEmailById(emailId);
    }

    /**
     * 查询邮箱配置列表
     *
     * @param merchantEmail 邮箱配置
     * @return 邮箱配置
     */
    @Override
    public List<MerchantEmail> selectMerchantEmailList(MerchantEmail merchantEmail) {
        return merchantEmailMapper.selectMerchantEmailList(merchantEmail);
    }


    @Override
    public String getoutLookAccessToken(MerchantEmail merchantEmail) {

        if (merchantEmail == null) {
            return null;
        }
        merchantEmail.getClientId();
        String clientId = merchantEmail.getClientId();
        String clientSecret = merchantEmail.getClientSecret();
        String refreshToken = merchantEmail.getRefreshToken();
        String scope = "https://graph.microsoft.com/.default";


        if (StringUtils.isEmpty(clientId) || StringUtils.isEmpty(clientSecret) || StringUtils.isEmpty(refreshToken)) {
            log.info("无效鉴权信息：{}", JSONObject.toJSONString(merchantEmail));
            return null;
        }


        String redisKey = RedisKeyConstant.OUTLOOK_ACCESS_TOKEN + merchantEmail.getEmail();
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(redisKey))) {
            return stringRedisTemplate.opsForValue().get(redisKey);
        }

        OkHttpClient client = new OkHttpClient();

        String url = "https://login.microsoftonline.com/common/oauth2/v2.0/token";


        FormBody formBody = new FormBody.Builder()
                .add("grant_type", "refresh_token")
                .add("refresh_token", refreshToken)
                .add("client_id", clientId)
                .add("client_secret", clientSecret)
                .add("scope", scope)
                .build();

        Request request = new Request.Builder()
                .url(url)
                .post(formBody)
                .build();
        String responseBody = null;
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                responseBody = response.body().string();
                log.info("获取outlook_token_success:{},邮箱：{}", responseBody, merchantEmail.getEmail());
                stringRedisTemplate.opsForValue().set(redisKey,  JSON.parseObject(responseBody).getString("access_token"),  10 * 60, TimeUnit.SECONDS);
                return JSON.parseObject(responseBody).getString("access_token");
            } else {
                log.info("获取outlook_token_error:{},邮箱：{}", response, merchantEmail.getEmail());
            }
        } catch (IOException e) {
            log.info("获取outlook_token_异常:{},邮箱：{}", e, merchantEmail.getEmail());
        }
        return null;
    }

    @Override
    public MerchantEmail selectInsideEmail(Long shopId) {
        MerchantEmail record = new MerchantEmail();
        record.setShopId(shopId);
        record.setEmailType("1");
        record.setEnabledFlag("Y");
        List<MerchantEmail> merchantEmailList = merchantEmailMapper.selectMerchantEmailList(record);
        if (!CollectionUtils.isEmpty(merchantEmailList)) {
            return merchantEmailList.get(0);
        }
        return null;
    }

    @Override
    public List<MerchantEmail> selectOutsideEmail(MerchantEmail merchantEmail) {
        return merchantEmailMapper.selectOutsideEmail(merchantEmail);
    }

    /**
     * 新增邮箱配置
     *
     * @param merchantEmail 邮箱配置
     * @return 结果
     */
    @Override
    public int insertMerchantEmail(MerchantEmail merchantEmail,UserEntity userEntity) {
        if(userEntity != null) {
            merchantEmail.setCreatedBy(userEntity.getId());
            merchantEmail.setCreatedName(userEntity.getName());
            merchantEmail.setUpdatedBy(userEntity.getId());
            merchantEmail.setUpdatedName(userEntity.getName());
        }
        merchantEmail.setCreatedAt(DateUtils.getNowDate());
        merchantEmail.setUpdatedAt(DateUtils.getNowDate());
        return merchantEmailMapper.insertMerchantEmail(merchantEmail);
    }

    /**
     * 修改邮箱配置
     *
     * @param merchantEmail 邮箱配置
     * @return 结果
     */
    @Override
    public int updateMerchantEmail(MerchantEmail merchantEmail, UserEntity userEntity) {
        if(userEntity != null) {
            merchantEmail.setUpdatedBy(userEntity.getId());
            merchantEmail.setUpdatedName(userEntity.getName());
        }
        merchantEmail.setUpdatedAt(DateUtils.getNowDate());
        return merchantEmailMapper.updateMerchantEmail(merchantEmail);
    }

    @Override
    public int updateMerchantEmailEnabledFlag(MerchantEmail merchantEmail, UserEntity userEntity) {
        if(userEntity != null) {
            merchantEmail.setUpdatedBy(userEntity.getId());
            merchantEmail.setUpdatedName(userEntity.getName());
        }
        merchantEmail.setUpdatedAt(DateUtils.getNowDate());
        return merchantEmailMapper.updateMerchantEmailEnabledFlag(merchantEmail);
    }

    /**
     * 批量删除邮箱配置
     *
     * @param emailIds 需要删除的邮箱配置ID
     * @return 结果
     */
    @Override
    public int deleteMerchantEmailByIds(Long[] emailIds) {
        return merchantEmailMapper.deleteMerchantEmailByIds(emailIds);
    }

    /**
     * 删除邮箱配置信息
     *
     * @param emailId 邮箱配置ID
     * @return 结果
     */
    @Override
    public int deleteMerchantEmailById(Long emailId) {
        return merchantEmailMapper.deleteMerchantEmailById(emailId);
    }

//    @Async
//    public void asyncMerchantEmail(Long emailId) {
//        MerchantEmail merchantEmail = this.selectMerchantEmailById(emailId);
//        if (null != merchantEmail) {
//            syncEmail(merchantEmail);
//        }
//    }


    /**
     * @description: 定时同步
     * @author: Moore
     * @date: 2024/6/19 15:20
     * @param
     * @return: void
    **/
    @Override
    public void syncEmailInfo(MerchantEmail merchantEmailQuery) {
        List<MerchantEmail> merchantEmailList = merchantEmailMapper.selectEnabledAndNotWayFairMerchantEmail();

        if (merchantEmailQuery.getShopId() != null) {
            log.error("指定店铺：{},邮件开始拉取", merchantEmailQuery.getShopId());
            merchantEmailList = merchantEmailList.stream().filter(item -> merchantEmailQuery.getShopId().equals(item.getShopId())).collect(Collectors.toList());
        }
        if ("1".equals(merchantEmailQuery.getEmailType())) { //站内邮箱,过滤公丕刚，公丕刚有单独任务
            Long filterShop = 2325L;
            merchantEmailList = merchantEmailList.stream().filter(item -> "1".equals(item.getEmailType()) && !filterShop.equals(item.getShopId())).collect(Collectors.toList());
        }
        if ("2".equals(merchantEmailQuery.getEmailType())) { //站外邮箱，指定组织ID
            merchantEmailList = merchantEmailList.stream().filter(item -> "2".equals(item.getEmailType())&&merchantEmailQuery.getOrganizationId().equals(item.getOrganizationId())
            ).collect(Collectors.toList());
            log.error("站外邮箱执行：{}", JSONObject.toJSONString(merchantEmailList));
        }
        if (CollectionUtils.isEmpty(merchantEmailList)) {
            return;
        }

        for (MerchantEmail merchantEmail : merchantEmailList) {
            try {
                syncEmail(merchantEmail);
            } catch (Exception e) {
                log.error("EMAIL-同步邮件失败.ID:{}, 邮箱：{},异常原因:{}", merchantEmail.getId(), merchantEmail.getEmail(), e.getMessage());
            }
        }
    }


    @Override
    public List<MerchantEmail> selectEnabledMerchantEmail() {
        return merchantEmailMapper.selectEnabledMerchantEmail();
    }



//    @Override
//    public void saveEmailInfo(List<Map<String, Object>> emails, String emailAccount, Long emailId, Long organizationId, Long shopId, String emailType, Date beginTime, Date endDate) {
//        log.error("--------邮件开始处理,当前邮箱账号：{}", emailAccount);
//        if (CollectionUtils.isEmpty(emails)) {
//            log.error("--------邮件数据为空,当前邮箱账号：{}", emailAccount);
//            return;
//        }
//
//        List<Map<String, Object>> emailList = new ArrayList<>();
//
//        for (Map<String, Object> email : emails) {
//            if (email.get("messageId") == null) {
//                continue;
//            }
//            if (containsEmail(emailList, email)) {
//                continue;
//            }
//            emailList.add(email);
//        }
//        if (CollectionUtils.isEmpty(emailList)) {
//            return;
//        }
//        log.error("--------emailList长度--------:{}", emailList.size());
//        Account shop = accountService.getById(shopId);
//        log.error("-------当前处理电子邮件账号：{}", emailAccount);
//        for (Map<String, Object> email : emailList) {
////            log.error("--------邮件信息--------:{}|{}|{}",email.get("messageId"),email.get("sentDate"),email.get("subject"));
//            SyncEmailInfo record = new SyncEmailInfo();
//            record.setEmailId((String) email.get("messageId"));
//            record.setSendTime((Date) email.get("sentDate"));
//            record.setEmailTitle((String) email.get("subject"));
//
//            List<SyncEmailInfo> emailInfos = emailInfoMapper.selectEmailInfoList(record);
//            if (!CollectionUtils.isEmpty(emailInfos)) {
//                continue;
//            }
//
//            SyncEmailInfo emailInfo = new SyncEmailInfo();
//            emailInfo.setOrganizationId(organizationId);
//            String folder = convertFolder((String) email.get("folder"), (String) email.get("from"));
//            if ("1".equals(emailType)) {
//                log.error("------当前电子邮箱账号：{},邮箱配置ID：{}, Folder:{}, EmailType: 1", emailAccount, emailId, folder);
//                emailInfo.setEmailFolder(folder);
//            } else {
//                emailInfo.setEmailFolder("EMAIL");
//                log.error("------站外信：【EMAIL】,当前电子邮箱账户：{},邮箱配置ID：{}", emailId);
//            }
//            emailInfo.setEmailId((String) email.get("messageId"));
//            emailInfo.setSendTime((Date) email.get("sentDate"));
//            emailInfo.setEmailTitle((String) email.get("subject"));
//            emailInfo.setEmailContent((String) email.get("content"));
//            String from = (String) email.get("from");
//            emailInfo.setSendName(from.substring(0, from.indexOf("<")));
//            emailInfo.setFromAddress(from.substring(from.indexOf("<") + 1, from.indexOf(">")));
//            emailInfo.setToAddress((String) email.get("to"));
//            emailInfo.setCcAddress((String) email.get("cc"));
//            emailInfo.setBccAddress((String) email.get("bcc"));
//            emailInfo.setCreatedAt(new Date());
//            emailInfo.setCreatedBy(0);
//            emailInfo.setUpdatedBy(0);
//            emailInfo.setCreatedName("System");
//            emailInfo.setUpdatedAt(new Date());
//            emailInfo.setUpdatedName("System");
//
//            emailInfo.setAttachments(JSONArray.toJSONString(email.get("attachments")));
//            emailInfo.setMerchantEmailId(emailId);
////            emailInfo.setDeptId(null != shop ? shop.getDeptId() : null);
//            if (EmailFolderEnum.AMZ_QA.getValue().equals(folder)) {
//                emailInfo.setAttribute1(getAsin((String) email.get("content")));
//                emailInfo.setAttribute2(getCustomerName((String) email.get("content")));
//            }
//
//            String amazonOrderId = getAmazonOrderId(emailInfo.getEmailTitle(), emailInfo.getEmailContent());
//            emailInfo.setAttribute3(amazonOrderId);
//            //订单号不为空，修改客户邮箱
//            if (!StringUtils.isEmpty(shop) && StringUtil.isNotEmpty(amazonOrderId) && emailInfo.getFromAddress().indexOf("amazon.com") == -1 && emailInfo.getFromAddress().indexOf("walmartone.com") == -1 && emailInfo.getFromAddress().indexOf("walmart.com") == -1) {
//                log.error("-----修改客户邮箱信息，当前电子邮箱账号：{},邮箱配置ID：{}", emailAccount, emailId);
//                scCustomerService.updateCustomerEmail(shop.getOrgId().longValue(), amazonOrderId, emailInfo.getFromAddress());
//            }
//            log.error("开始插入邮件数据，EmailId:{},SendTime:{}", emailId, emailInfo.getSendTime());
//            emailInfoService.insertEmailInfo(emailInfo);
//
//
//            //创建退货工单（
//            log.error("------创建退货工单，当前邮箱账号：{},当前配置ID：{}", emailAccount, emailId);
//            this.createReturnTicket(emailInfo);
//
//            //保存附件
//            saveAttachmentObs(emailInfo.getInfoId(), (List<String>) email.get("attachments"));
//            log.info("附件保存完毕，当前邮箱账号：{},邮箱配置ID：{}", emailAccount, emailId);
//            String title = emailInfo.getEmailTitle();
//            if (title.length() > 50) {
//                title = title.substring(0, 49);
//            }
//            log.error("当前邮件类型：{},当前邮件folder：{}", emailType, folder);
//            if ("1".equals(emailType)) {
//                //店铺邮箱，保存站内信，非站内信则生成工单
//                if (EmailFolderEnum.AMZ_SITEMSG.getValue().equals(folder)) {
//                    log.error("------开始处理站内信，当前邮箱账号：{},邮箱配置ID：{}", emailAccount, emailId);
//                    scStationLetterService.saveStationLetterByEmailInfo(emailInfo, shopId);
//                } else {
//
//                    Long parentId = getParentMail(emailInfo);
//                    log.error("111获取父邮件，父邮件ID：{}，当前邮箱账号：{},邮箱配置ID：{}", parentId, emailAccount, emailId);
//                    if (null != parentId) {
//                        ScTicket ticket = scTicketService.selectScTicketBySource(parentId, ScTicketConstant.TICKET_SOURCE_DOCUMENT_EMAIL);
//                        if (null != ticket) {
//                            ticket.setSourceId(emailInfo.getInfoId());
//                            ticket.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_EMAIL);
//                            ticket.setTicketStatus(ScTicketConstant.TICKET_STATUS_NEW);
//                            ticket.setSkipInterceptor(true);
//                            scTicketService.updateScTicketSource(ticket);
//
//                            scTicketSourceHisService.saveScTicketSourceHis(ticket.getId(), ticket.getSourceId(), ticket.getSourceDocument(), parentId);
//                        }
//                    } else {
//                        ScTicket scTicket = new ScTicket();
//                        scTicket.setSkipInterceptor(true);
//                        scTicket.setOrganizationId(organizationId);
//                        scTicket.setTicketName(title);
//                        scTicket.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(emailInfo.getInfoId().toString(), "l", 5, "0"));
//                        scTicket.setTicketSource("SHOP_MAIL");
//                        scTicket.setTicketType(folder);
//                        String priority = ScTicketConstant.TICKET_PRIORITY_LOW;
//                        if (EmailFolderEnum.AMZ_QA.getValue().equals(folder) || EmailFolderEnum.AMZ_ATOZ.getValue().equals(folder) || EmailFolderEnum.CHARGE_BACK.getValue().equals(folder)) {
//                            priority = ScTicketConstant.TICKET_PRIORITY_HIGH;
//                        }
//                        scTicket.setPriority(priority);
//                        scTicket.setShopId(shopId);
//                        scTicket.setAmazonOrderId(amazonOrderId);
//                        scTicket.setSourceId(emailInfo.getInfoId());
//                        scTicket.setSourceDocument("sync_email_info");
//                        scTicket.setAsin(emailInfo.getAttribute1());
//                        String remark = StringUtils.isEmpty(shop) ? "" : shop.getTitle();
//                        if (StringUtil.isNotEmpty(emailInfo.getAttribute1())) {
//                            remark = remark + "、ASIN：" + emailInfo.getAttribute1();
//                        }
//                        scTicket.setRemark(remark);
//
//                        scTicketService.insertScTicket(scTicket);
//                        log.error("111保存工单信息完毕，父邮件ID：{},当前邮箱账号：{},邮箱配置ID：{}", parentId, emailAccount, emailId);
//                    }
//                }
//            } else {
//                Long parentId = getParentMail(emailInfo);
//                log.error("222获取父邮件，父邮件ID：{}，当前邮箱账号：{},邮箱配置ID：{}", parentId, emailAccount, emailId);
//                if (null != parentId) {
//                    ScTicket ticket = scTicketService.selectScTicketBySource(parentId, ScTicketConstant.TICKET_SOURCE_DOCUMENT_EMAIL);
//                    if (null != ticket) {
//                        log.error("222获取工单来源为邮箱的单据，单据信息：{}", ticket.getId());
//                        ticket.setSourceId(emailInfo.getInfoId());
//                        ticket.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_EMAIL);
//                        ticket.setTicketStatus(ScTicketConstant.TICKET_STATUS_NEW);
//                        ticket.setSkipInterceptor(true);
//                        scTicketService.updateScTicketSource(ticket);
//
//                        scTicketSourceHisService.saveScTicketSourceHis(ticket.getId(), ticket.getSourceId(), ticket.getSourceDocument(), parentId);
//                    }
//                } else {
//                    ScTicket scTicket = new ScTicket();
//
//                    scTicket.setOrganizationId(organizationId);
//                    scTicket.setTicketName(title);
//                    scTicket.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(emailInfo.getInfoId().toString(), "l", 5, "0"));
//                    scTicket.setTicketSource("OUTSITE_MAIL");
//                    scTicket.setTicketType("EMAIL");
//                    scTicket.setPriority(ScTicketConstant.TICKET_PRIORITY_MEDIUM);
//                    scTicket.setShopId(shopId);
//                    scTicket.setAmazonOrderId(amazonOrderId);
//                    scTicket.setSourceId(emailInfo.getInfoId());
//                    scTicket.setSourceDocument("sync_email_info");
//                    scTicket.setAsin(emailInfo.getAttribute1());
//                    String remark = "发件人：" + emailInfo.getFromAddress();
//                    scTicket.setRemark(remark);
//                    scTicket.setSkipInterceptor(true);
//                    scTicketService.insertScTicket(scTicket);
//                    log.error("222保存工单信息完毕，当前邮箱账号：{},邮箱配置ID：{}", parentId, emailAccount, emailId);
//                }
//            }
//        }
//
//        // 修改同步时间信息
//        updateMerchanEmailTime(emailId, beginTime, endDate, emailList);
//    }

    private void syncEmail(MerchantEmail merchantEmail) {
        Date endDate = new Date();
        log.error("--------邮件开始获取,当前邮箱账号：{}", merchantEmail.getEmail());
        // 多同步一天的数据
        Date beginTime = DateUtil.addDate(merchantEmail.getBeginTime(), -1);
        List<Map<String, Object>> emails = null;
        if(merchantEmail.getEmail().contains("@outlook.com")) {
            String token = this.getoutLookAccessToken(merchantEmail);
            if(StringUtil.isNotEmpty(token)) {
                emails = graphEmailApi.readEmail(merchantEmail,merchantEmail.getEmail(),
                        merchantEmail.getPassword(), beginTime, endDate,token);
            }
        } else {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endDate);
            calendar.add(Calendar.HOUR_OF_DAY, 24);
            emails = emailApi.readEmail(merchantEmail,merchantEmail.getEmail(),
                    merchantEmail.getPassword(), beginTime, calendar.getTime(), merchantEmail.getId().toString(),null);
        }

        saveEmailInfo(emails,merchantEmail, merchantEmail.getEmail(), merchantEmail.getId(), merchantEmail.getOrganizationId(), merchantEmail.getShopId(), merchantEmail.getEmailType(), merchantEmail.getBeginTime(), endDate);
    }

    /**
     * @description: 保存邮件信息及生成工单
     * @author:
     * @date: 2024/6/21 14:39
     * @param
     * @param emails
     * @param emailAccount
     * @param emailId
     * @param organizationId
     * @param shopId
     * @param emailType
     * @param beginTime
     * @param endDate
     * @return: void
    **/
    @Transactional
    public void saveEmailInfo(List<Map<String, Object>> emails, MerchantEmail merchantEmail,String emailAccount, Long emailId, Integer organizationId, Long shopId, String emailType, Date beginTime, Date endDate) {log.error("EMAIL:--------邮件开始处理,当前邮箱账号：{}", emailAccount);
        if (CollectionUtils.isEmpty(emails)) {
            log.error("EMAIL:--------邮件数据为空,当前邮箱账号：{}", emailAccount);
            return;
        }

        List<Map<String, Object>> emailList = new ArrayList<>();

        for (Map<String, Object> email : emails) {
            if (email == null || email.get("messageId") == null) {
                continue;
            }
            if (containsEmail(emailList, email)) {
                continue;
            }
            emailList.add(email);
        }
        if (CollectionUtils.isEmpty(emailList)) {
            return;
        }
        log.error("EMAIL:--------emailList长度--------:{}", emailList.size());
        Account account = accountService.getById(shopId);
        String shopName = null;
        if(account != null) {
            shopName = account.getTitle();
        }
        log.error("EMAIL:-------当前处理电子邮件账号：{}", emailAccount);
        //对所有邮件发送时间进行排序
        emailList = emailList.stream().sorted(Comparator.comparing(i -> (Date)i.get("sentDate"))).collect(Collectors.toList());
        for (Map<String, Object> email : emailList) {
//            log.error("--------邮件信息--------:{}|{}|{}",email.get("messageId"),email.get("sentDate"),email.get("subject"));
            SyncEmailInfo record = new SyncEmailInfo();
            record.setEmailId((String) email.get("messageId"));
            record.setSendTime((Date) email.get("sentDate"));
            record.setEmailTitle((String) email.get("subject"));

            List<SyncEmailInfo> emailInfos = emailInfoMapper.selectEmailInfoList(record);
            if (!CollectionUtils.isEmpty(emailInfos)) {
                continue;
            }

            SyncEmailInfo emailInfo = new SyncEmailInfo();
            emailInfo.setOrganizationId(organizationId.longValue());
            String folder = convertFolder((String) email.get("folder"), (String) email.get("from"));
            if ("1".equals(emailType)) { //真内
                emailInfo.setEmailFolder(folder);
            } else {
                emailInfo.setEmailFolder("EMAIL");
            }

            if (!Objects.isNull(account)) {
                emailInfo.setShopId(account.getId().longValue());
            }
            emailInfo.setEmailId((String) email.get("messageId"));
            emailInfo.setSendTime((Date) email.get("sentDate"));
            emailInfo.setEmailTitle((String) email.get("subject"));
            emailInfo.setEmailContent((String) email.get("content"));
            String to = null;
            if(merchantEmail.getEmail().contains("@outlook.com")) {
                String from = (String) email.get("from");
                emailInfo.setSendName((String) email.get("sendName"));
                emailInfo.setFromAddress(from);
                //接收到真实的TO邮箱
                to = (String) email.get("to");
            } else {
                String from = (String) email.get("from");
                emailInfo.setSendName(from.substring(0, from.indexOf("<")));
                emailInfo.setFromAddress(from.substring(from.indexOf("<") + 1, from.indexOf(">")));
                //接收到真实的TO邮箱
                to = (String) email.get("to");
                if (StringUtil.isNotEmpty(to)) {
                    to = to.substring(to.indexOf("<") + 1, to.indexOf(">"));
                    to = to.replace(" ", "");
                }
            }
            emailInfo.setToAddress(merchantEmail.getEmail()); //设置邮箱配置中邮箱为TO邮箱

            emailInfo.setCcAddress((String) email.get("cc"));
            emailInfo.setBccAddress((String) email.get("bcc"));
            emailInfo.setAttachments(JSONArray.toJSONString(email.get("attachments")));
            emailInfo.setContentImages(JSONArray.toJSONString(email.get("contentImages")));
            emailInfo.setMerchantEmailId(emailId);
            //TODO 店铺无部门ID？
//            emailInfo.setDeptId(null != account ? account.getDeptId() : null);
            if (EmailFolderEnum.AMZ_QA.getValue().equals(folder)) {
                emailInfo.setAttribute1(getAsin((String) email.get("content")));
                emailInfo.setAttribute2(getCustomerName((String) email.get("content")));
            }
            String amazonOrderId = null;
            if(!"<EMAIL>".equals(merchantEmail.getEmail())) { //防止邮件误读。
                amazonOrderId = getAmazonOrderId(emailInfo.getEmailTitle(), emailInfo.getEmailContent());
                //因为有些标题，内容含有关键字，现在查下订单表，去除掉这种情况
                QueryWrapper<SaleOrders> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("order_no",amazonOrderId);
                List<SaleOrders> saleOrdersList = saleOrdersMapper.selectList(queryWrapper);
                if(CollectionUtil.isEmpty(saleOrdersList)) {
                    amazonOrderId = null;
                }
                emailInfo.setAttribute3(amazonOrderId);
            }
            //订单号不为空，修改客户邮箱
            if (!StringUtils.isEmpty(account) && StringUtil.isNotEmpty(amazonOrderId) && emailInfo.getFromAddress().indexOf("amazon.com") == -1 && emailInfo.getFromAddress().indexOf("walmartone.com") == -1 && emailInfo.getFromAddress().indexOf("walmart.com") == -1) {
                log.error("EMAIL:修改客户邮箱信息，当前电子邮箱账号：{},邮箱配置ID：{}", emailAccount, emailId);
                scCustomerService.updateCustomerEmail(shopId, amazonOrderId, emailInfo.getFromAddress());
            }
            log.error("EMAIL:开始插入邮件数据，EmailId:{},SendTime:{}", emailId, emailInfo.getSendTime());
            emailInfoService.insertEmailInfo(emailInfo);


            //创建退货工单（
//            log.error("EMAIL:创建退货工单，当前邮箱账号：{},当前配置ID：{}", emailAccount, emailId);
//            this.createReturnTicket(emailInfo);

            //保存附件
            if (StringUtil.isNotEmpty(emailInfo.getAttachments())) {
                List<String> stringList = JSONArray.parseArray(emailInfo.getAttachments(), String.class);
                List<TicketAttaches> ticketAttachesList = stringList.stream().map(i -> {
                    TicketAttaches ticketAttaches = new TicketAttaches();
                    ticketAttaches.setOrganizationId(emailInfo.getOrganizationId() != null ? emailInfo.getOrganizationId().longValue() : null);
                    int lastIndex = i.lastIndexOf("/");
                    String fileName = i.substring(lastIndex + 1);
                    int index = fileName.indexOf('.');
                    // 如果找到了 '.'，则截取后面的字符串
                    if (index != -1) {
                        fileName = fileName.substring(index + 1);
                    }
                    ticketAttaches.setFileName(fileName);
                    ticketAttaches.setFileUrl(i);
                    ticketAttaches.setDocumentId(emailInfo.getInfoId());
                    ticketAttaches.setDocumentType("sync_email_info");
                    ticketAttaches.settingDefaultCreate();
                    return ticketAttaches;
                }).collect(Collectors.toList());
                ticketAttachesService.saveBatch(ticketAttachesList);
            }
            log.info("EMAIL:附件保存完毕，当前邮箱账号：{},邮箱配置ID：{}", emailAccount, emailId);
            String title = emailInfo.getEmailTitle();
            if (StringUtil.isNotEmpty(title) && title.length() > 450) {
                title = title.substring(0, 49);
            }
            if ("1".equals(emailType)) {
                //店铺邮箱，保存站内信，非站内信则生成工单
                if (EmailFolderEnum.AMZ_SITEMSG.getValue().equals(folder)) {
                    log.error("EMAIL:开始处理站内信，当前邮箱账号：{},邮箱配置ID：{}", emailAccount, emailId);
                    scStationLetterService.saveStationLetterByEmailInfo(emailInfo, shopId,shopName,"amazon");
                } else {

                    Long parentId = getParentMail(emailInfo);
                    log.error("EMAIL:---获取父邮件，父邮件ID：{}，当前邮箱账号：{},邮箱配置ID：{}", parentId, emailAccount, emailId);
                    if (null != parentId) {
                        ScTicket ticket = scTicketService.selectScTicketBySource(parentId, ScTicketConstant.TICKET_SOURCE_DOCUMENT_EMAIL);
                        if (null != ticket) {
//                            ticket.setSourceId(emailInfo.getInfoId());
//                            ticket.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_EMAIL);
//                            ticket.setTicketStatus(ScTicketConstant.TICKET_STATUS_NEW);
//                            scTicketService.updateScTicketSource(ticket);
//                            scTicketSourceHisService.saveScTicketSourceHis(ticket.getId(), ticket.getSourceId(), ticket.getSourceDocument(), parentId);
                        }
                    } else {
                        ScTicket scTicket = new ScTicket();

                        scTicket.setOrganizationId(organizationId.longValue());
                        scTicket.setTicketName(title);
                        scTicket.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(emailInfo.getInfoId().toString(), "l", 5, "0")+ RandomUtil.randomNumbers(4));

                        //设置店铺邮箱，工单渠道
                        if (shopId != null) {
                            Account one = accountService.lambdaQuery().select(Account::getType).eq(Account::getId, shopId).one();
                            scTicket.setTicketSource(one != null ? one.getType() : null);
                        }

                        scTicket.setTicketType(folder);
                        String priority = ScTicketConstant.TICKET_PRIORITY_LOW;
                        if (EmailFolderEnum.AMZ_QA.getValue().equals(folder) || EmailFolderEnum.AMZ_ATOZ.getValue().equals(folder) || EmailFolderEnum.CHARGE_BACK.getValue().equals(folder)) {
                            priority = ScTicketConstant.TICKET_PRIORITY_HIGH;
                        }
                        scTicket.setPriority(priority);
                        scTicket.setShopId(shopId);
                        scTicket.setAmazonOrderId(amazonOrderId);
                        scTicket.setSourceId(emailInfo.getInfoId());
                        scTicket.setSourceDocument("sync_email_info");
                        scTicket.setAsin(emailInfo.getAttribute1());
                        String remark = StringUtils.isEmpty(account) ? "" : account.getTitle();
                        if (StringUtil.isNotEmpty(emailInfo.getAttribute1())) {
                            remark = remark + "、ASIN：" + emailInfo.getAttribute1();
                        }
                        scTicket.setRemark(remark);

                        scTicketService.insertScTicket(scTicket);
                        //给邮件信息展示用的
                        //todo 跟新 emailInfo表
                        SyncEmailInfo newEmailInfo = new SyncEmailInfo();
                        newEmailInfo.setInfoId(emailInfo.getInfoId());
                        if(scTicket.getId() != null) {
                            newEmailInfo.setSourceId(scTicket.getId().toString());
                        }
                        newEmailInfo.setSourceDocument("sc_ticket");
                        if(emailAccount.equals(emailInfo.getToAddress())) {//买家发给卖家
                            newEmailInfo.setType(ScTicketConstant.MESSAGE_TYPE_BUY_TO_SELLER);
                        }else {
                            newEmailInfo.setType(ScTicketConstant.MESSAGE_TYPE_SELLER_TO_BUY);
                        }
                        emailInfoService.updateEmailInfo(newEmailInfo);
                        log.error("EMAIL:保存工单信息完毕，父邮件ID：{},当前邮箱账号：{},邮箱配置ID：{}", parentId, emailAccount, emailId);
                    }
                }
            } else {
                //查询有没有没有关闭站外信工单
                SyncEmailInfo qEmail = new SyncEmailInfo();
                qEmail.setMerchantEmailId(emailId);
                qEmail.setFromAddress(emailInfo.getFromAddress());
                qEmail.setInfoId(emailInfo.getInfoId());
                qEmail.setEmailTitle(emailInfo.getEmailTitle());
                ScTicket stationScticket = getOpenScTicket(qEmail);
                log.error("EMAIL:===获取父邮件，当前邮箱账号：{},邮箱配置ID：{}", emailAccount, emailId);
                if (null != stationScticket) {
                    log.error("EMAIL:===获取站外信工单的单据，单据信息：{}", stationScticket);
                    SyncEmailInfo newEmailInfo = new SyncEmailInfo();
                    newEmailInfo.setInfoId(emailInfo.getInfoId());
                    newEmailInfo.setSourceId(stationScticket.getId().toString());
                    newEmailInfo.setSourceDocument("sc_ticket");
                    if(emailAccount.equals(emailInfo.getToAddress())) {//买家发给卖家
                        newEmailInfo.setType(ScTicketConstant.MESSAGE_TYPE_BUY_TO_SELLER);
                    }else {
                        newEmailInfo.setType(ScTicketConstant.MESSAGE_TYPE_SELLER_TO_BUY);
                    }
                    //设置未回复数加1
                    if(stationScticket.getNoReplyQty() == null) {
                        stationScticket.setNoReplyQty(new Integer(1));
                    } else {
                        stationScticket.setNoReplyQty(stationScticket.getNoReplyQty().intValue() + 1);
                    }
                    //设置成未回复
                    stationScticket.setTicketReplyStatus(new Integer(0));
                    //todo 设置为待处理
                    stationScticket.setTicketStatus(ScTicketConstant.TICKET_STATUS_PROCESSING);
                    stationScticket.settingDefaultUpdate();
                    scTicketService.updateScTickeByTicketId(stationScticket);
                    emailInfoService.updateEmailInfo(newEmailInfo);
                    List<String> attachList = new ArrayList<>();
                    //插入附件表
                    if (StringUtil.isNotEmpty(emailInfo.getAttachments())) {
                        List<String> stringList = JSONArray.parseArray(emailInfo.getAttachments(), String.class);
                        attachList.addAll(stringList);
                    }
                    if(StringUtil.isNotEmpty(emailInfo.getContentImages())) {
                        List<String> stringList = JSONArray.parseArray(emailInfo.getContentImages(), String.class);
                        attachList.addAll(stringList);
                    }

                    List<TicketAttaches> ticketAttachesList = attachList.stream().map(i -> {
                        TicketAttaches ticketAttaches = new TicketAttaches();
                        ticketAttaches.setOrganizationId(emailInfo.getOrganizationId() != null ? emailInfo.getOrganizationId().longValue() : null);
                        int lastIndex = i.lastIndexOf("/");
                        String fileName = i.substring(lastIndex + 1);
                        int index = fileName.indexOf('.');
                        // 如果找到了 '.'，则截取后面的字符串
                        if (index != -1) {
                            fileName = fileName.substring(index + 1);
                        }
                        ticketAttaches.setFileName(fileName);
                        ticketAttaches.setFileUrl(i);
                        ticketAttaches.setDocumentId(stationScticket.getId());
                        ticketAttaches.setDocumentType("sc_ticket");
                        ticketAttaches.settingDefaultCreate();
                        return ticketAttaches;
                    }).collect(Collectors.toList());
                    ticketAttachesService.saveBatch(ticketAttachesList);
                } else {
                    ScTicket scTicket = new ScTicket();

                    scTicket.setOrganizationId(organizationId.longValue());
                    scTicket.setTicketName(title);
                    scTicket.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(emailInfo.getInfoId().toString(), "l", 5, "0")+ RandomUtil.randomNumbers(4));
//                    scTicket.setTicketSource("OUTSITE_MAIL"); 后续通过页面匹配订单设置渠道
                    scTicket.setTicketType("EMAIL");
                    scTicket.setPriority(ScTicketConstant.TICKET_PRIORITY_MEDIUM);
                    // scTicket.setShopId(shopId);
                    scTicket.setAmazonOrderId(amazonOrderId);
                    scTicket.setSourceId(emailInfo.getInfoId());
                    scTicket.setSourceDocument("sync_email_info");
                    scTicket.setAsin(emailInfo.getAttribute1());
                    String remark = "发件人：" + emailInfo.getSendName() + "，" + emailInfo.getFromAddress() +  "收件人：" + emailInfo.getToAddress();
                    if (!(emailInfo.getToAddress().toLowerCase()).equals(to.toLowerCase())){
                        remark += " 实际收件人:" + to;
                    }
                    scTicket.setRemark(remark);
                    //设置回复状态为未回复
                    scTicket.setTicketReplyStatus(new Integer(0));
                    //设置未回复数为1
                    scTicket.setNoReplyQty(new Integer(1));
                    scTicket.setToAddress(emailInfo.getToAddress());
                    scTicketService.insertScTicket(scTicket);
                    //给邮件信息展示用的
                    SyncEmailInfo newEmailInfo = new SyncEmailInfo();
                    newEmailInfo.setInfoId(emailInfo.getInfoId());
                    if(scTicket.getId() != null) {
                        newEmailInfo.setSourceId(scTicket.getId().toString());
                    }
                    newEmailInfo.setSourceDocument("sc_ticket");
                    if(emailAccount.equals(emailInfo.getToAddress())) {//买家发给卖家
                        newEmailInfo.setType(ScTicketConstant.MESSAGE_TYPE_BUY_TO_SELLER);
                    }else {
                        newEmailInfo.setType(ScTicketConstant.MESSAGE_TYPE_SELLER_TO_BUY);
                    }
                    emailInfoService.updateEmailInfo(newEmailInfo);
                    log.error("EMAIL:----保存工单信息完毕，当前邮箱账号：{},邮箱配置ID：{}", emailAccount, emailId);

                    List<String> attachList = new ArrayList<>();
                    //插入附件表
                    if (StringUtil.isNotEmpty(emailInfo.getAttachments())) {
                        List<String> stringList = JSONArray.parseArray(emailInfo.getAttachments(), String.class);
                        attachList.addAll(stringList);
                    }
                    if(StringUtil.isNotEmpty(emailInfo.getContentImages())) {
                        List<String> stringList = JSONArray.parseArray(emailInfo.getContentImages(), String.class);
                        attachList.addAll(stringList);
                    }

                    List<TicketAttaches> ticketAttachesList = attachList.stream().map(i -> {
                        TicketAttaches ticketAttaches = new TicketAttaches();
                        ticketAttaches.setOrganizationId(emailInfo.getOrganizationId() != null ? emailInfo.getOrganizationId().longValue() : null);
                        int lastIndex = i.lastIndexOf("/");
                        String fileName = i.substring(lastIndex + 1);
                        int index = fileName.indexOf('.');
                        // 如果找到了 '.'，则截取后面的字符串
                        if (index != -1) {
                            fileName = fileName.substring(index + 1);
                        }
                        ticketAttaches.setFileName(fileName);
                        ticketAttaches.setFileUrl(i);
                        ticketAttaches.setDocumentId(scTicket.getId());
                        ticketAttaches.setDocumentType("sc_ticket");
                        ticketAttaches.settingDefaultCreate();
                        return ticketAttaches;
                    }).collect(Collectors.toList());
                    ticketAttachesService.saveBatch(ticketAttachesList);
                }
            }
        }

        // 修改同步时间信息
        updateMerchanEmailTime(emailId, beginTime, endDate, emailList);


    }

//    @Async("threadPoolTaskExecutor")
//    @Override
//    @Transactional
//    public void asyncEmailInfo(Date beginTime, Date endTime, MerchantEmail email, String type) {
//        //开始时间推迟一天
//        Date newbeginTime = DateUtil.addDate(beginTime, -1);
//        //初始时间
//        beginTime = cn.hutool.core.date.DateUtil.beginOfDay(newbeginTime);
//
//        List<Map<String, Object>> emails = EmailApi.readEmail(email.getEmail(),
//                email.getPassword(), beginTime, endTime, email.getId().toString());
//        if ("1".equals(type)) {
//            saveEmailInfo(emails, email.getEmail(), email.getId(), email.getOrganizationId(),
//                    email.getShopId(), email.getEmailType(), beginTime, endTime
//            );
//        } else {
//            saveEmailInfoCopy(emails, email.getEmail(), email.getId(), email.getOrganizationId().longValue(),
//                    email.getShopId(), email.getEmailType(), beginTime, endTime
//            );
//        }
//    }

    @Override
    public List<MerchantEmail> selectMerchantEmailList(Long organizationId) {
        return merchantEmailMapper.selectMerchantEmailBaseList(organizationId);
    }

    @Override
    public List<MerchantEmail> selectMerchantEmailByIds(Integer[] ids, Integer organizationId) {
        return merchantEmailMapper.selectMerchantEmailByIds(ids == null || ids.length == 0 ? null : Arrays.asList(ids), organizationId);
    }



    @Override
    public List<MerchantEmail> selectMerchantEmail(Integer organizationId) {
        return merchantEmailMapper.selectMerchantEmail(organizationId);
    }


    /*手动同步指定邮箱信息*/
//    @Override
//    @Transactional
//    public void syncEmailManual(MerchantEmail merchantEmail) {
//        if (merchantEmail.getId() == null) {
//            return;
//        }
//        MerchantEmail merchantEmailRes = merchantEmailMapper.selectMerchantEmailById(merchantEmail.getId());
//        if (merchantEmailRes == null) {
//            return;
//        }
//        if (null != merchantEmail.getBeginTime()) {
//            merchantEmailRes.setBeginTime(merchantEmail.getBeginTime());
//        }
//        Date nowDate = DateUtils.getNowDate(); //当前时间
//        List<Map<String, Object>> emails = EmailApi.readEmail(merchantEmailRes.getEmail(),
//                merchantEmailRes.getPassword(), merchantEmailRes.getBeginTime(), nowDate, merchantEmailRes.getId().toString());
//        saveEmailInfo(emails, merchantEmailRes.getEmail(), merchantEmailRes.getId(), merchantEmailRes.getOrganizationId(),
//                merchantEmailRes.getShopId(), merchantEmailRes.getEmailType(), merchantEmailRes.getBeginTime(), nowDate);
//
//    }

//    public void updateMerchanEmailTimeCopy(Long emailId, Date beginTime, Date endDate, List<SyncEmailInfo> emailList) {
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
//        // 对比同步插入后的数据数量  更新 beginTime
//        int size = emailList.size();
//        log.error("sync email count : {} ", size);
//        // 获取插入后数据
//        log.error("邮件查询 BeginTime:{}，EndTime:{}", dateFormat.format(beginTime), dateFormat.format(endDate));
//        int count = emailInfoMapper.selectEmailCountByTimeAndMerchantEmailId(emailId, beginTime, endDate);
//        log.error("对比邮件数据，EmailId: {},同步到的数量：{},查询数量：{}", emailId, size, count);
//
//        if (size <= count && emailList.size() > 0) {
//            // 修改更新 beginTime 为同步对比之后
//            MerchantEmail merchantEmail = new MerchantEmail();
//            merchantEmail.setId(emailId);
//            merchantEmail.setLastSyncTime(beginTime);
//            merchantEmail.setBeginTime(endDate);
//            log.error("邮件数据量对比通过，修改同步时间为 endTime:{},lastSyncTime:{}，EmeailID: {}", dateFormat.format(endDate), dateFormat.format(beginTime), emailId);
//            merchantEmailMapper.updateMerchantEmailSyncTime(merchantEmail);
//        }
//    }

    public void updateMerchanEmailTime(Long emailId, Date beginTime, Date endDate, List<Map<String, Object>> emailList) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        // 对比同步插入后的数据数量  更新 beginTime
        int size = emailList.size();
      //  log.error("EMAIL:sync email count : {} ", size);
        // 获取插入后数据
       // log.error("EMAIL:邮件查询 BeginTime:{}，EndTime:{}", dateFormat.format(beginTime), dateFormat.format(endDate));

//         endDate = cn.hutool.core.date.DateUtil.endOfDay(endDate);
//        int count = emailInfoMapper.selectEmailCountByTimeAndMerchantEmailId(emailId, beginTime, endDate);
      //  log.error("EMAIL:对比邮件数据，EmailId: {},同步到的数量：{},查询数量：{}", emailId, size, count);

        if (emailList.size() > 0) {
            // 修改更新 beginTime 为同步对比之后
            MerchantEmail merchantEmail = new MerchantEmail();
            merchantEmail.setId(emailId);
            merchantEmail.setLastSyncTime(beginTime);
            merchantEmail.setBeginTime(endDate);
            merchantEmail.setSkipInterceptor(true);
            log.error("EMAIL:邮件数据量对比通过，修改同步时间为 endTime:{},lastSyncTime:{}，EmeailID: {}", dateFormat.format(endDate), dateFormat.format(beginTime), emailId);
            merchantEmail.settingDefaultUpdate();
            merchantEmailMapper.updateMerchantEmailSyncTime(merchantEmail);
        } else {
            MerchantEmail merchantEmail = new MerchantEmail();
            merchantEmail.settingDefaultUpdate();
            merchantEmailMapper.updateMerchantEmailSyncTime(merchantEmail);
        }
    }

//    private void saveAttachmentObs(Long infoId, List<String> attachmentList) {
//        for (String filePath : attachmentList) {
//            ObsAttachment attachment = new ObsAttachment();
//            attachment.setFileUrl(filePath.replace(FileUploadUtils.getDefaultBaseDir(), AofisConfig.getFileUrlPrefix()));
//            attachment.setFilePath(filePath);
//            attachment.setDocumentId(infoId);
//            attachment.setDocumentType("sync_email_info");
//
//            String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
//            attachment.setFileName(fileName);
//            attachment.setFileType(ObsUtil.getFileType(fileName));
//
//            String keyValue = sysConfigService.selectConfigByKey("obs.upload_flag");
//            if ("Y".equals(keyValue)) {
//                Map<String, String> result = ObsUtil.uploadFile(filePath);
//                if (null != result) {
//                    attachment.setObjectKey(result.get("objectKey"));
//                    attachment.setObsUrl(result.get("fileUrl"));
//                }
//            }
//            obsAttachmentService.insertObsAttachment(attachment);
//        }
//    }

    /**
     * 是否包含相同邮箱ID、发件时间和主题的邮件
     *
     * @param emailList
     * @param subEmail
     * @return
     */
    private boolean containsEmail(List<Map<String, Object>> emailList, Map<String, Object> subEmail) {
        String subMessageId = (String) subEmail.get("messageId");
        Date subSentDate = (Date) subEmail.get("sentDate");
        String subSubject = (String) subEmail.get("subject");
        for (Map<String, Object> email : emailList) {
            String messageId = (String) email.get("messageId");
            Date sentDate = (Date) email.get("sentDate");
            String subject = (String) email.get("subject");
            if (subMessageId.equals(messageId) && DateUtil.compareDate(subSentDate, sentDate) == 0 && subSubject.equals(subject)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取同主题父邮件
     *
     * @param emailInfo
     * @return
     */
    private Long getParentMail(SyncEmailInfo emailInfo) {
        if(StringUtil.isNotEmpty(emailInfo.getEmailTitle())) {
            if (emailInfo.getEmailTitle().toUpperCase().indexOf("RE:") > -1) {
                String title = emailInfo.getEmailTitle().replace("RE:", "").replace("Re:", "").replace("re:", "").trim();
                emailInfo.setEmailTitle(title);
                List<SyncEmailInfo> emailInfoList = emailInfoMapper.selectParentEmail(emailInfo);
                if (!CollectionUtils.isEmpty(emailInfoList)) {
                    SyncEmailInfo emailInfo1 = emailInfoList.get(0);
                    return emailInfo1.getInfoId();
                }
            } else if (emailInfo.getEmailTitle().toUpperCase().indexOf("FWD:") > -1) {
                String title = emailInfo.getEmailTitle().replace("FWD:", "").replace("FWd:", "").replace("Fwd:", "").replace("fwd:", "").trim();
                emailInfo.setEmailTitle(title);
                List<SyncEmailInfo> emailInfoList = emailInfoMapper.selectParentEmail(emailInfo);
                if (!CollectionUtils.isEmpty(emailInfoList)) {
                    SyncEmailInfo emailInfo1 = emailInfoList.get(0);
                    return emailInfo1.getInfoId();
                }
            } else if (emailInfo.getEmailTitle().indexOf("回复：") > -1) {
                String title = emailInfo.getEmailTitle().replace("回复：", "").trim();
                emailInfo.setEmailTitle(title);
                List<SyncEmailInfo> emailInfoList = emailInfoMapper.selectParentEmail(emailInfo);
                if (!CollectionUtils.isEmpty(emailInfoList)) {
                    SyncEmailInfo emailInfo1 = emailInfoList.get(0);
                    return emailInfo1.getInfoId();
                }
            }
        }
        return null;
    }

    /**
     * 获取同主题父邮件
     *
     * @param emailInfo
     * @return
     */
    private List<String> sents = Arrays.asList("SENT", "已发送", "发件箱");
    private List<String> deletes = Arrays.asList("DELETED", "DELETE", "已删除");
    private List<String> draftss = Arrays.asList("DRAFTS", "草稿夹", "草稿箱");

    /**
     * 邮件分类
     *
     * @param oldFolder
     * @param from
     * @return
     */
    private String convertFolder(String oldFolder, String from) {
        String foler = EmailFolderEnum.INBOX.getValue();
        if (sents.contains(oldFolder.toUpperCase())) {
            foler = EmailFolderEnum.SENT.getValue();
        } else if (deletes.contains(oldFolder.toUpperCase())) {
            foler = EmailFolderEnum.DELETED.getValue();
        } else if (draftss.contains(oldFolder.toUpperCase())) {
            foler = EmailFolderEnum.DRAFTS.getValue();
        } else if (from.contains("@marketplace.amazon.com")) {
            foler = EmailFolderEnum.AMZ_SITEMSG.getValue();
        } else if (from.contains("<EMAIL>")) {
            foler = EmailFolderEnum.AMZ_QA.getValue();
        } else if (from.contains("<EMAIL>")) {
            foler = EmailFolderEnum.AMZ_ATOZ.getValue();
        } else if (from.contains("@amazon.com")) {
            foler = EmailFolderEnum.AMZ_EMAIL.getValue();
        }
        return foler;
    }

    private String getAmazonOrderId(String title, String content) {
        if (StringUtil.isNotEmpty(title) && (title.toLowerCase().indexOf("order id") != -1 || title.toLowerCase().indexOf("order") != -1)) {
            if (title.indexOf("Order ID") != -1) {
                return title.substring(title.toLowerCase().indexOf("order id") + 8).trim();
            } else if (title.indexOf("order") != -1) {
                return title.substring(title.toLowerCase().indexOf("order") + 5).trim();
            }
        }

        if (StringUtil.isEmpty(content) || content.indexOf("Order ID:") == -1) {
            return null;
        }
        try {
            String txtContent = content.substring(content.indexOf("Order ID:") + 9, content.indexOf("Order ID:") + 40);
            txtContent = txtContent.replaceAll("</?[^>]+>", "");
            txtContent = txtContent.replaceAll("<a>\\s*|\t|\r|\n</a>", "");
            return txtContent.substring(txtContent.indexOf("-") - 3, txtContent.lastIndexOf("-") + 8);
        } catch (StringIndexOutOfBoundsException e) {
            log.error("提取站内信订单号错误");
            return null;
        }
    }

    private String getAsin(String content) {
        if (StringUtil.isEmpty(content) || content.indexOf("https://www.amazon.com/dp/") == -1) {
            return null;
        }
        int start = content.indexOf("https://www.amazon.com/dp/");
        return content.substring(start + 26, start + 36);
    }

    private String getCustomerName(String content) {
        if (StringUtil.isEmpty(content) || content.indexOf("</a> asked<") == -1) {
            return null;
        }
        int start = content.indexOf("</a> asked<");
        String subContent = content.substring(start - 30, start);
        return subContent.substring(subContent.indexOf("\">") + 2);
    }

    /*创建退货请求工单*/
    private void createReturnTicket(SyncEmailInfo emailInfo) {

        String emailTitle = emailInfo.getEmailTitle();
        if (StringUtils.isEmpty(emailTitle) || !emailTitle.contains("Return authorization notification for Order")) {
            log.error("---退货工单创建不通过，当前配置ID：{}", emailInfo.getMerchantEmailId());
            return;
        }

        ScTicket scTicket = new ScTicket();
        scTicket.setOrganizationId(emailInfo.getOrganizationId());
        scTicket.setTicketName(emailTitle); //工单名称
        scTicket.setSkipInterceptor(true);
        //工单编号
        scTicket.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(emailInfo.getInfoId().toString(), "l", 5, "0"));
        scTicket.setTicketSource(ScTicketConstant.TICKET_SOURCE_SHOP_EMAIL);// 工单来源(授权邮箱)
        scTicket.setTicketType(ScTicketConstant.TICKET_TYPE_RETURN); //工单类型（退货）
        scTicket.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH); //优先级（高）
        scTicket.setAmazonOrderId(emailInfo.getAttribute3()); //亚马逊订单号
        scTicket.setSourceId(emailInfo.getInfoId());//来源ID
        scTicket.setSourceDocument("sync_email_info"); //来源单据
        List<SaleOrders> saleOrders = saleOrdersService.lambdaQuery()
                .eq(SaleOrders::getOrderNo, emailInfo.getAttribute3())
                .eq(SaleOrders::getOrgId, emailInfo.getOrganizationId())
                .list();
        if (CollectionUtil.isNotEmpty(saleOrders)) {
            scTicket.setShopId(saleOrders.get(0).getChannelId());  //店铺ID
            scTicket.setCustomerId(saleOrders.get(0).getCustomerId() == null ? null : saleOrders.get(0).getCustomerId().longValue()); //客户ID
        }
        scTicket.setAsin(emailInfo.getAttribute1()); //ASIN
        scTicketService.insertScTicket(scTicket);
//        ScOrder scOrder = scOrderService.selectScOrderByAmazonOrderId(emailInfo.getOrganizationId(), emailInfo.getAttribute3());


    }


    /**
     * 集合切割
     *
     * @param data
     * @param count
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> splitList(List<T> data, int count) {
        log.error("切分数据，份数：{}", count);
        if (count == 1) {
            return Arrays.asList(data);
        }
        boolean flag = data.size() % count != 0;
        // 记录份数
        AtomicInteger copyCount = new AtomicInteger();
        // 记录索引
        AtomicInteger incr = new AtomicInteger();
        // 每份个数
        AtomicInteger everyCount = new AtomicInteger(data.size() % count);
        return data.stream()
                .map(item -> {
                    boolean splitFlag = copyCount.get() == count - 1 && flag;
                    if (splitFlag) {
                        everyCount.set(everyCount.get() + data.size() % count);
                    }
                    List<T> t = data.stream()
                            .skip(incr.get())
                            .limit(everyCount.get())
                            .collect(Collectors.toList());
                    copyCount.incrementAndGet();
                    incr.set(incr.get() + everyCount.get());
                    return t;
                }).filter(item -> item != null && CollectionUtil.isNotEmpty(item))
                .collect(Collectors.toList());
    }


//    public void saveEmailInfoCopy(List<Map<String, Object>> emails, String emailAccount, Long emailId, Long organizationId, Long shopId, String emailType, Date beginTime, Date endDate) {
//        log.error("--------邮件开始处理,当前邮箱账号：{}", emailAccount);
//        if (CollectionUtils.isEmpty(emails)) {
//            log.error("--------邮件数据为空,当前邮箱账号：{}", emailAccount);
//            return;
//        }
//
//        // 筛选有效数据
//        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        List<Map<String, Object>> emailList = emails.stream()
//                .filter(item -> !StringUtils.isEmpty(item.get("messageId")))
//                .collect(Collectors.collectingAndThen(
//                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(
//                                item -> item.get("messageId") + format.format(item.get("sentDate")) + item.get("subject")
//                        )))
//                        , ArrayList::new));
//
//        log.error("当前处理账号：{},数据长度：{}", emailAccount, emailList.size());
//        // 查询店铺信息
//        Account shop = accountService.getById(shopId);
//        // 客户信息修改数据
//        List<String> customerUpdateInfo = new ArrayList<>();
//
//        // 获取所有 MessageId
//        List<String> messageIds = emailList.stream()
//                .map(email -> (String) email.get("messageId"))
//                .collect(Collectors.toList());
//        // 查询所有的 MessageId 的邮件数据
//        List<SyncEmailInfo> infos = emailInfoMapper.selectEmailInfoByEmailIds(messageIds);
//        log.error("当前账号：{},查询邮件信息，获取到邮件条目：{}", emailAccount, infos.size());
//        List<String> emailFlag = infos.stream()
//                .map(item -> item.getEmailId() + item.getEmailTitle() + format.format(item.getSendTime()))
//                .collect(Collectors.toList());
//        // 筛选邮件信息
//        Map<String, String> amazonOrderIdMap = new HashMap<>();
//        // 筛选没有获取到对应的邮件信息的数据，进行新增数据的书
//        List<SyncEmailInfo> emailInfoList = emailList.stream().filter(email -> {
//            String messageId = (String) email.get("messageId");
//            String emailTitle = (String) email.get("subject");
//            String date = format.format((Date) email.get("sentDate"));
//            return !emailFlag.contains(messageId + emailTitle + date);
//        }).map(email -> {
//            SyncEmailInfo emailInfo = new SyncEmailInfo();
//            emailInfo.setOrganizationId(organizationId);
//            String folder = convertFolder((String) email.get("folder"), (String) email.get("from"));
//            if ("1".equals(emailType)) {
//                log.error("------当前电子邮箱账号：{},邮箱配置ID：{}, Folder:{}, EmailType: 1", emailAccount, emailId, folder);
//                emailInfo.setEmailFolder(folder);
//            } else {
//                emailInfo.setEmailFolder("EMAIL");
//                log.error("------站外信：【EMAIL】,当前电子邮箱账户：{},邮箱配置ID：{}", emailId);
//            }
//            emailInfo.setEmailId((String) email.get("messageId"));
//            emailInfo.setSendTime((Date) email.get("sentDate"));
//            emailInfo.setEmailTitle((String) email.get("subject"));
//            emailInfo.setEmailContent((String) email.get("content"));
//            String from = (String) email.get("from");
//            emailInfo.setSendName(from.substring(0, from.indexOf("<")));
//            emailInfo.setFromAddress(from.substring(from.indexOf("<") + 1, from.indexOf(">")));
//            emailInfo.setToAddress((String) email.get("to"));
//            emailInfo.setCcAddress((String) email.get("cc"));
//            emailInfo.setBccAddress((String) email.get("bcc"));
//            emailInfo.setAttachments(JSONArray.toJSONString(email.get("attachments")));
//            emailInfo.setMerchantEmailId(emailId);
//            // TODO 店铺中不存在部门信息
////            emailInfo.setDeptId(null != shop ? shop.getDeptId() : null);
//            if (EmailFolderEnum.AMZ_QA.getValue().equals(folder)) {
//                emailInfo.setAttribute1(getAsin((String) email.get("content")));
//                emailInfo.setAttribute2(getCustomerName((String) email.get("content")));
//            }
//            String amazonOrderId = getAmazonOrderId(emailInfo.getEmailTitle(), emailInfo.getEmailContent());
//            emailInfo.setAttribute3(amazonOrderId);
//            if (!StringUtils.isEmpty(shop) && StringUtil.isNotEmpty(amazonOrderId) && emailInfo.getFromAddress().indexOf("amazon.com") == -1 && emailInfo.getFromAddress().indexOf("walmartone.com") == -1 && emailInfo.getFromAddress().indexOf("walmart.com") == -1) {
//                // 保存客户信息修改数据
//                customerUpdateInfo.add(shop.getOrgId() + "&" + amazonOrderId);
////                scCustomerService.updateCustomerEmail(shop.getOrganizationId(), amazonOrderId, emailInfo.getFromAddress());
//            }
//            amazonOrderIdMap.put(emailInfo.getEmailTitle() + emailInfo.getEmailContent(), amazonOrderId);
//            //保存附件
//            saveAttachmentObs(emailInfo.getInfoId(), (List<String>) email.get("attachments"));
//            log.info("邮箱账号：{},邮箱配置ID：{},附件保存完毕，", emailAccount, emailId);
//            return emailInfo;
//        }).collect(Collectors.toList());
//        log.error("邮箱账号：{},邮件数据构建完毕，数据条目：{}", emailAccount, emailInfoList.size());
//        // 修改客户信息
//        if (CollectionUtil.isNotEmpty(customerUpdateInfo)) {
//            log.error("邮箱账号：{},修改客户信息：{}", emailAccount);
//            updateCustomers(shopId,customerUpdateInfo);
//        }
//        // 插入邮件数据
//        if (CollectionUtil.isEmpty(emailInfoList)) {
//            log.error("邮箱账号：{}，数据为空", emailAccount);
//            return;
//        }
//        int insertCount = emailInfoMapper.insertBatchEmailInfos(emailInfoList);
//        log.error("邮箱账号：{},邮件数据插入完毕，插入条目：{}", emailAccount, insertCount);
//
//        List<SyncEmailInfo> emailInfos = emailInfoList.stream().filter(item ->
//                !(StringUtils.isEmpty(item.getEmailTitle()) && !item.getEmailTitle().contains("Return authorization notification for Order"))
//        ).collect(Collectors.toList());
//
//        List<ScTicket> scTickets = emailInfos.stream()
//                .map(item -> buildReturnTicket(
//                                shopId,
//                                item,
//                                ScTicketConstant.TICKET_SOURCE_SHOP_EMAIL,
//                                ScTicketConstant.TICKET_TYPE_RETURN,
//                                ScTicketConstant.TICKET_PRIORITY_HIGH,
//                                null,
//                                null
//                        )
//                )
//                .collect(Collectors.toList());
//        log.error("构建退货工单完毕，条目数：{}", scTickets.size());
//        // 处理退货工单信息
//        if (CollectionUtil.isNotEmpty(scTickets)) {
//            log.error("需要插入退货工单，邮箱账号：{}，条目：{}", emailAccount, scTickets.size());
//            scTicketService.insertScTickets(emailInfos, scTickets);
//        }
//        // 保存附件
//        taskExecutor.execute(() -> {
//            log.info("邮箱账号：{},邮箱配置ID：{},附件保存", emailAccount, emailId);
//            for (SyncEmailInfo emailInfo : emailInfoList) {
//                if (!StringUtils.isEmpty(emailInfo.getAttachments())) {
//                    String attachments = emailInfo.getAttachments();
//                    saveAttachmentObs(emailInfo.getInfoId(), JSON.parseArray(attachments, String.class));
//                }
//            }
//        });
//        if ("1".equals(emailType)) {
//            List<SyncEmailInfo> stations = emailInfoList.stream().filter(item -> EmailFolderEnum.AMZ_SITEMSG.getValue().equals(item.getEmailFolder())).collect(Collectors.toList());
//            //店铺邮箱，保存站内信，非站内信则生成工单
//            if (CollectionUtil.isNotEmpty(stations)) {
//                // 保存站内信
//                log.error("邮箱账号：{},邮箱配置ID：{},开始处理站内信------", emailAccount, emailId);
//                scStationLetterService.saveStationLetterByEmailInfos(stations, shopId);
//            }
//            // 过滤非站内信邮件
//            List<SyncEmailInfo> noStationEmails = emailInfos.stream()
//                    .filter(item -> !EmailFolderEnum.AMZ_SITEMSG.getValue().equals(item.getEmailFolder()))
//                    .collect(Collectors.toList());
//
//            if (CollectionUtil.isNotEmpty(noStationEmails)) {
//                List<EmailInfoVO> vos = noStationEmails.stream().map(item -> {
//                    EmailInfoVO vo = new EmailInfoVO();
//                    BeanUtil.copyProperties(item, vo);
//                    return vo;
//                }).collect(Collectors.toList());
//                handlerTicketsUpdate(vos);
//                List<EmailInfoVO> noParentEmails = vos.stream()
//                        .filter(item -> StringUtils.isEmpty(item.getParentInfoId()))
//                        .collect(Collectors.toList());
//                if (CollectionUtil.isNotEmpty(noParentEmails)) {
//                    List<ScTicket> noParentScTickets = noParentEmails.stream()
//                            .map(item -> {
//                                SyncEmailInfo emailInfo = new SyncEmailInfo();
//                                BeanUtil.copyProperties(item, emailInfo);
//                                String priority = ScTicketConstant.TICKET_PRIORITY_LOW;
//                                String folder = emailInfo.getEmailFolder();
//                                if (EmailFolderEnum.AMZ_QA.getValue().equals(folder) || EmailFolderEnum.AMZ_ATOZ.getValue().equals(folder) || EmailFolderEnum.CHARGE_BACK.getValue().equals(folder)) {
//                                    priority = ScTicketConstant.TICKET_PRIORITY_HIGH;
//                                }
//                                String remark = StringUtils.isEmpty(shop) ? "" : shop.getTitle();
//                                if (StringUtil.isNotEmpty(emailInfo.getAttribute1())) {
//                                    remark = remark + "、ASIN：" + emailInfo.getAttribute1();
//                                }
//                                return buildReturnTicket(
//                                        shopId,
//                                        emailInfo,
//                                        "SHOP_MAIL",
//                                        folder,
//                                        priority,
//                                        emailInfo.getAttribute1(),
//                                        remark
//                                );
//                            }).collect(Collectors.toList());
//                    scTicketService.insertScTickets(noParentEmails, noParentScTickets);
//                }
//            }
//        } else {
//            List<EmailInfoVO> vos = emailInfos.stream().map(item -> {
//                EmailInfoVO vo = new EmailInfoVO();
//                BeanUtil.copyProperties(item, vo);
//                return vo;
//            }).collect(Collectors.toList());
//            handlerTicketsUpdate(vos);
//            List<EmailInfoVO> noParentEmails = vos.stream()
//                    .filter(item -> StringUtils.isEmpty(item.getParentInfoId()))
//                    .collect(Collectors.toList());
//            if (CollectionUtil.isNotEmpty(noParentEmails)) {
//                List<ScTicket> tickets = noParentEmails.stream()
//                        .map(item -> {
//                            SyncEmailInfo emailInfo = new SyncEmailInfo();
//                            BeanUtil.copyProperties(item, emailInfo);
//                            String remark = "发件人：" + emailInfo.getFromAddress();
//                            return buildReturnTicket(
//                                    shopId,
//                                    emailInfo,
//                                    "OUTSITE_MAIL",
//                                    "EMAIL",
//                                    ScTicketConstant.TICKET_PRIORITY_MEDIUM,
//                                    emailInfo.getAttribute1(),
//                                    remark
//                            );
//
//                        }).collect(Collectors.toList());
//                scTicketService.insertScTickets(noParentEmails, tickets);
//            }
//        }
//
//        // 修改同步时间信息
//        updateMerchanEmailTimeCopy(emailId, beginTime, endDate, emailInfoList);
//        log.error("邮箱账号：{}，同步完毕-----------", emailAccount);
//
//    }

//    private void handlerTicketsUpdate(List<EmailInfoVO> vos) {
//        // 构建父邮件ID
//        buildParentEmails(vos);
//        // 获取父邮件ID不为空的邮件数据
//        List<EmailInfoVO> hasParentEmails = vos.stream()
//                .filter(item -> !StringUtils.isEmpty(item.getParentInfoId()))
//                .collect(Collectors.toList());
//        if (CollectionUtil.isNotEmpty(hasParentEmails)) {
//            Map<Long, List<EmailInfoVO>> parentGroup = hasParentEmails.stream()
//                    .collect(Collectors.groupingBy(EmailInfoVO::getParentInfoId));
//            List<ScTicket> parentTickets = scTicketService.selectScTicketBySources(parentGroup.keySet(), ScTicketConstant.TICKET_SOURCE_DOCUMENT_EMAIL);
//            if (CollectionUtil.isNotEmpty(parentTickets)) {
//                for (ScTicket ticket : parentTickets) {
//                    ticket.setSourceId(parentGroup.get(ticket.getSourceId()).get(0).getInfoId());
//                    ticket.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_EMAIL);
//                    ticket.setTicketStatus(ScTicketConstant.TICKET_STATUS_NEW);
//                    scTicketService.updateScTicketSource(ticket);
//                }
//            }
//        }
//    }

//    private void handlerNoStation(String emailAccount, Long organizationId, Long shopId, Account shop, List<SyncEmailInfo> notStations) {
//        if (CollectionUtil.isEmpty(notStations)) {
//            return;
//        }
//        List<EmailInfoVO> vos = notStations.stream().map(item -> {
//            EmailInfoVO vo = new EmailInfoVO();
//            BeanUtil.copyProperties(item, vo);
//            return vo;
//        }).collect(Collectors.toList());
//
//        buildParentEmails(vos);
//
//        List<EmailInfoVO> hasParentInfos = vos.stream()
//                .filter(item -> !StringUtils.isEmpty(item.getParentInfoId()))
//                .collect(Collectors.toList());
//        // 存在父工单
//        if (CollectionUtil.isNotEmpty(hasParentInfos)) {
//            Map<Long, List<EmailInfoVO>> parentEmailGroup = hasParentInfos.stream()
//                    .collect(Collectors.groupingBy(EmailInfoVO::getParentInfoId));
//            List<ScTicket> tickets = scTicketService.selectScTicketBySources(parentEmailGroup.keySet(), ScTicketConstant.TICKET_SOURCE_DOCUMENT_EMAIL);
//            if (CollectionUtil.isNotEmpty(tickets)) {
//                for (ScTicket ticket : tickets) {
//                    if (parentEmailGroup.containsKey(ticket.getSourceId())) {
//                        EmailInfoVO infoVO = parentEmailGroup.get(ticket.getSourceId()).get(0);
//                        ticket.setSourceId(infoVO.getInfoId());
//                        ticket.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_EMAIL);
//                        ticket.setTicketStatus(ScTicketConstant.TICKET_STATUS_NEW);
//                        scTicketService.updateScTicketSource(ticket);
//                    }
//                }
//                // 保存历史
//                taskExecutor.execute(() -> {
//                    List<ScTicketSourceHis> ticketHis = tickets.stream()
//                            .map(item -> {
//                                ScTicketSourceHis scTicketSourceHis = new ScTicketSourceHis();
//                                scTicketSourceHis.setTicketId(item.getId());
//                                scTicketSourceHis.setSourceId(item.getSourceId());
//                                scTicketSourceHis.setSourceDocument(item.getSourceDocument());
//                                scTicketSourceHis.setParentSourceId(item.getSourceId());
//                                return scTicketSourceHis;
//                            }).collect(Collectors.toList());
//                    scTicketSourceHisMapper.saveBatchTicketHis(ticketHis);
//                });
//            }
//        } else {
//            List<EmailInfoVO> noParentInfos = vos.stream()
//                    .filter(item -> StringUtils.isEmpty(item.getParentInfoId()))
//                    .peek(item -> {
//                        ScTicket ticket = item.getScTicket();
//                        ticket.setTicketSource("OUTSITE_MAIL");
//                        ticket.setTicketType("EMAIL");
//                        ticket.setSourceDocument("sync_email_info");
//                        ticket.setPriority(ScTicketConstant.TICKET_PRIORITY_MEDIUM);
//                    })
//                    .collect(Collectors.toList());
//            // 保存工单信息
//            insertTickets(organizationId, shopId, shop, noParentInfos);
//            log.error("邮箱账号：{},工单信息保存完毕", emailAccount);
//        }
//    }

    private void insertTickets(Long organizationId, Long shopId, Account shop, List<EmailInfoVO> noParentInfos) {
        List<ScTicket> tickets = noParentInfos.stream()
                .map(item -> {
                    ScTicket scTicket = new ScTicket();
                    scTicket.setOrganizationId(organizationId);

                    String title = item.getEmailTitle();
                    if (title.length() > 50) {
                        title = title.substring(0, 49);
                    }
                    scTicket.setTicketName(title);
                    scTicket.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(item.getInfoId().toString(), "l", 5, "0")+ RandomUtil.randomNumbers(4));
                    scTicket.setTicketSource("SHOP_MAIL");
                    scTicket.setTicketType(item.getEmailFolder());
                    String priority = ScTicketConstant.TICKET_PRIORITY_LOW;
                    if (EmailFolderEnum.AMZ_QA.getValue().equals(item.getEmailFolder()) || EmailFolderEnum.AMZ_ATOZ.getValue().equals(item.getEmailFolder()) || EmailFolderEnum.CHARGE_BACK.getValue().equals(item.getEmailFolder())) {
                        priority = ScTicketConstant.TICKET_PRIORITY_HIGH;
                    }
                    scTicket.setPriority(priority);
                    scTicket.setShopId(shopId);
                    scTicket.setAmazonOrderId(item.getAmazonOrderId());
                    scTicket.setSourceId(item.getInfoId());
                    scTicket.setSourceDocument("sync_email_info");
                    scTicket.setAsin(item.getAttribute1());
                    String remark = StringUtils.isEmpty(shop) ? "" : shop.getTitle();
                    if (StringUtil.isNotEmpty(item.getAttribute1())) {
                        remark = remark + "、ASIN：" + item.getAttribute1();
                    }
                    scTicket.setRemark(remark);
                    return scTicket;
                }).collect(Collectors.toList());
        scTicketService.insertScTickets(noParentInfos, tickets);

    }


//    private void buildParentEmails(List<EmailInfoVO> vos) {
//        CompletableFuture<Void> f1 = CompletableFuture.runAsync(() -> {
//            List<EmailInfoVO> rule1 = vos.stream()
//                    .filter(item -> !StringUtils.isEmpty(item.getEmailTitle()))
//                    .filter(item -> item.getEmailTitle().toUpperCase().indexOf("RE:") > -1)
//                    .peek(item ->
//                            item.setTmpTitle(item.getEmailTitle()
//                                    .replace("RE:", "")
//                                    .replace("Re:", "")
//                                    .replace("re:", "").trim())
//                    )
//                    .collect(Collectors.toList());
//            if (CollectionUtil.isNotEmpty(rule1)) {
//                settingParentEmail(rule1);
//            }
//        }, taskExecutor);
//        CompletableFuture<Void> f2 = CompletableFuture.runAsync(() -> {
//            List<EmailInfoVO> rule2 = vos.stream()
//                    .filter(item -> !StringUtils.isEmpty(item.getEmailTitle()))
//                    .filter(item -> item.getEmailTitle().toUpperCase().indexOf("FWD:") > -1)
//                    .peek(item -> item.setTmpTitle(item.getEmailTitle()
//                            .replace("FWD:", "")
//                            .replace("FWd:", "")
//                            .replace("Fwd:", "")
//                            .replace("fwd:", "").trim())
//                    )
//                    .collect(Collectors.toList());
//            if (CollectionUtil.isNotEmpty(rule2)) {
//                settingParentEmail(rule2);
//            }
//        }, taskExecutor);
//        CompletableFuture<Void> f3 = CompletableFuture.runAsync(() -> {
//            List<EmailInfoVO> rule3 = vos.stream()
//                    .filter(item -> !StringUtils.isEmpty(item.getEmailTitle()))
//                    .filter(item -> item.getEmailTitle().indexOf("回复：") > -1)
//                    .peek(item -> item.getEmailTitle().replace("回复：", "").trim())
//                    .collect(Collectors.toList());
//            if (CollectionUtil.isNotEmpty(rule3)) {
//                settingParentEmail(rule3);
//            }
//        }, taskExecutor);
//
//        try {
//            CompletableFuture.allOf(f1, f2, f3).get(60, TimeUnit.SECONDS);
//        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
//        } catch (ExecutionException e) {
//            throw new RuntimeException(e);
//        } catch (TimeoutException e) {
//            throw new RuntimeException(e);
//        }
//
//    }

    /**
     * 设置父邮件ID
     *
     * @param
     */
//    private void settingParentEmail(List<EmailInfoVO> infos) {
//        for (EmailInfoVO info : infos) {
//            List<SyncEmailInfo> emailInfoList = emailInfoMapper.selectParentEmail(info);
//            if (!CollectionUtils.isEmpty(emailInfoList)) {
//                SyncEmailInfo emailInfo1 = emailInfoList.get(0);
//                info.setParentInfoId(emailInfo1.getInfoId());
//            }
//        }
//    }

    private void updateCustomers(Long shopId,List<String> customerUpdateInfo) {
        Map<String, List<String>> map = customerUpdateInfo.stream()
                .collect(
                        Collectors.groupingBy(
                                item -> item.split("&")[0] + item.split("&")[1]
                        )
                );
        List<String> amazonOrderIds = customerUpdateInfo.stream()
                .map(item -> item.split("&")[1])
                .collect(Collectors.toList());
        List<SaleOrders> saleOrders = saleOrdersService.lambdaQuery()
                .eq(SaleOrders::getChannelId, shopId)
                .in(SaleOrders::getOrderNo, amazonOrderIds)
                .list();
        if (CollectionUtil.isNotEmpty(saleOrders)) {
            // 获取对应的客户数据
            List<SaleOrders> orders = saleOrders.stream()
                    .filter(item -> {
                        String orderId = item.getOrderNo();
                        Long orgId = item.getOrgId();
                        return map.containsKey(orderId + orgId);
                    }).collect(Collectors.toList());

            Map<Integer, List<SaleOrders>> orderGroup = orders.stream().collect(Collectors.groupingBy(SaleOrders::getCustomerId));
            List<ScCustomer> customers = scCustomerMapper.selectScCustomerByIds(orderGroup.keySet());
            Date updateTime = new Date();
            if (CollectionUtil.isNotEmpty(customers)) {
                for (ScCustomer customer : customers) {
                    SaleOrders order = orderGroup.get(customer.getCustomerId()).get(0);
                    List<String> infoStr = map.get(order.getOrgId() + order.getOrderNo());
                    customer.setEmail(infoStr.get(0).split("&")[2]);
                    customer.setUpdatedAt(updateTime);
                    scCustomerMapper.updateScCustomerEmail(customer);
                }
            }
        }
    }

    private ScTicket buildReturnTicket(Long shopId,SyncEmailInfo emailInfo, String ticketSource, String ticketType, String priority, String asin, String remark) {
        String emailTitle = emailInfo.getEmailTitle();
        ScTicket scTicket = new ScTicket();
        scTicket.setOrganizationId(emailInfo.getOrganizationId());
        scTicket.setTicketName(emailTitle); //工单名称
        //工单编号
        scTicket.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(emailInfo.getInfoId().toString(), "l", 5, "0")+ RandomUtil.randomNumbers(4));
        scTicket.setTicketSource(ticketSource);// 工单来源(授权邮箱)
        scTicket.setTicketType(ticketType); //工单类型（退货）
        // 设置处理人
        if (ticketType.equals("EMAIL")) {

        }

        scTicket.setPriority(priority); //优先级（高）
        scTicket.setAmazonOrderId(emailInfo.getAttribute3()); //亚马逊订单号
        scTicket.setSourceId(emailInfo.getInfoId());//来源ID
        scTicket.setSourceDocument("sync_email_info"); //来源单据
        SaleOrders order = saleOrdersService.lambdaQuery()
                .eq(SaleOrders::getChannelId, shopId)
                .eq(SaleOrders::getOrderNo, emailInfo.getAttribute3())
                .one();
        if (order != null) {


            scTicket.setShopId(order.getChannelId());  //店铺ID
            scTicket.setCustomerId(order.getCustomerId().longValue()); //客户ID
        }
        scTicket.setAsin(asin); //ASIN
        scTicket.setCreatedAt(DateUtils.getNowDate());
        scTicket.setUpdatedAt(DateUtils.getNowDate());
        scTicket.setRemark(remark);
        try {
            AuthUserDetails userDetail = (AuthUserDetails) SecurityUtils.getSubject().getPrincipal();
            scTicket.setCreatedBy(userDetail.getId());
            scTicket.setUpdatedBy(userDetail.getId());

        } catch (Exception e) {

        }


        return scTicket;
    }

    /**
     * Description: 根据ticketId 查询 站外邮件
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/1/17
     */
    @Override
    public List<SyncEmailInfo> getAmzSiteMsgEmailList(Long ticketId) {
        SyncEmailInfo syncEmailInfo = new SyncEmailInfo();
        syncEmailInfo.setSourceId(ticketId.toString());
        syncEmailInfo.setSourceDocument("sc_ticket");
        List<SyncEmailInfo> syncEmailInfoList = emailInfoMapper.selectEmailInfoAndAttachList(syncEmailInfo);
        return syncEmailInfoList;
    }

    /**
     * Description: 根据ticketId 查询所有往来的历史邮件
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/1/17
     */
    @Override
    public List<SyncEmailInfo> getAmzSiteMsgEmailHistoryList(Long ticketId) {
        QueryWrapper<SyncEmailInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("source_id",ticketId.toString());
        queryWrapper.eq("source_document","sc_ticket");
        List<SyncEmailInfo> syncEmailInfos = emailInfoMapper.selectList(queryWrapper);
        String fromAddress = "";
        String toAddress = "";
        if(CollectionUtil.isNotEmpty(syncEmailInfos)) {
            fromAddress = syncEmailInfos.get(0).getFromAddress();
            toAddress = syncEmailInfos.get(0).getToAddress();
        }else {
            return null;
        }
        SyncEmailInfo syncEmailInfo = new SyncEmailInfo();
        //是否查询往来邮件
        syncEmailInfo.setIsQueryFromAndTo("true");
        syncEmailInfo.setFromAddress(fromAddress);
        syncEmailInfo.setToAddress(toAddress);
        List<SyncEmailInfo> syncEmailInfoList = emailInfoMapper.selectEmailInfoAndAttachList(syncEmailInfo);
        return syncEmailInfoList;
    }

    /**
     * 同步邮箱内容为WayFair的邮件
     */
    @Override
    public void syncEmailInfoWithWayFair(String startDateString,String endDateString) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        List<MerchantEmail> merchantEmailList = merchantEmailMapper.selectEnabledAndWayFairMerchantEmail();
         //调试用的
//        List<MerchantEmail> merchantEmailList = new ArrayList<>();
//        MerchantEmail mer = merchantEmailMapper.selectMerchantEmailById(1074l);
//        merchantEmailList.add(mer);

        if (CollectionUtils.isEmpty(merchantEmailList)) {
            return;
        }
        for (MerchantEmail merchantEmail : merchantEmailList) {
            Date endDate = StringUtil.isNotEmpty(endDateString) ? simpleDateFormat.parse(endDateString): new Date();
            log.error("--------邮件开始获取,当前邮箱账号：{}", merchantEmail.getEmail());
            // 多同步一天的数据，怕漏了。
            Date beginTime = StringUtil.isNotEmpty(startDateString) ? DateUtil.addDate(simpleDateFormat.parse(startDateString), -1) : DateUtil.addDate(merchantEmail.getBeginTime(), -1);
            Long start = System.currentTimeMillis();
            List<Map<String, Object>> emails = emailApi.readEmail(merchantEmail,merchantEmail.getEmail(),
                    merchantEmail.getPassword(), beginTime, endDate, merchantEmail.getId().toString(),"wayfair");
            //todo 筛选出是wayFair 退货的邮件
            if (CollectionUtil.isEmpty(emails)) {
               continue;
            }
            String emailAccount = merchantEmail.getEmail();
            Long emailId = merchantEmail.getId();
            Integer organizationId = merchantEmail.getOrganizationId();
            Long shopId = merchantEmail.getShopId();
            String emailType = merchantEmail.getEmailType();
            log.error("EMAIL:--------邮件开始处理,当前邮箱账号：{}", emailAccount);
            if (CollectionUtils.isEmpty(emails)) {
                log.error("EMAIL:--------邮件数据为空,当前邮箱账号：{}", emailAccount);
                return;
            }
            List<Map<String, Object>> emailList = new ArrayList<>();

            for (Map<String, Object> email : emails) {
                if (email == null || email.get("messageId") == null) {
                    continue;
                }
                if (containsEmail(emailList, email)) {
                    continue;
                }
                emailList.add(email);
            }
            if (CollectionUtils.isEmpty(emailList)) {
                return;
            }
            log.error("EMAIL:--------emailList长度--------:{}", emailList.size());
            Account account = accountService.getById(shopId);
            log.error("EMAIL:-------当前处理电子邮件账号：{}", emailAccount);
            for (Map<String, Object> email : emailList) {
//            log.error("--------邮件信息--------:{}|{}|{}",email.get("messageId"),email.get("sentDate"),email.get("subject"));
                SyncEmailInfo record = new SyncEmailInfo();
                record.setEmailId((String) email.get("messageId"));
                record.setSendTime((Date) email.get("sentDate"));
                record.setEmailTitle((String) email.get("subject"));

                List<SyncEmailInfo> emailInfos = emailInfoMapper.selectEmailInfoList(record);
                if (!CollectionUtils.isEmpty(emailInfos)) {
                    continue;
                }

                SyncEmailInfo emailInfo = new SyncEmailInfo();
                emailInfo.setOrganizationId(organizationId.longValue());
                String folder = convertFolder((String) email.get("folder"), (String) email.get("from"));
                if ("1".equals(emailType)) {
                    emailInfo.setEmailFolder(folder);
                } else {
                    emailInfo.setEmailFolder("EMAIL");
                }

                if (!Objects.isNull(account)) {
                    emailInfo.setShopId(account.getId().longValue());
                }
                emailInfo.setEmailId((String) email.get("messageId"));
                emailInfo.setSendTime((Date) email.get("sentDate"));
                emailInfo.setEmailTitle((String) email.get("subject"));
                emailInfo.setEmailContent((String) email.get("content"));
                String from = (String) email.get("from");
                emailInfo.setSendName(from.substring(0, from.indexOf("<")));
                emailInfo.setFromAddress(from.substring(from.indexOf("<") + 1, from.indexOf(">")));
                emailInfo.setToAddress((String) email.get("to"));
                emailInfo.setCcAddress((String) email.get("cc"));
                emailInfo.setBccAddress((String) email.get("bcc"));
                emailInfo.setAttachments(JSONArray.toJSONString(email.get("attachments")));
                emailInfo.setMerchantEmailId(emailId);
                log.error("EMAIL:开始插入邮件数据，EmailId:{},SendTime:{}", emailId, emailInfo.getSendTime());
                emailInfoService.insertEmailInfo(emailInfo);
                //todo 解析wayfair退货工单
                if("Action Required: Return Shipment Notification".equals(email.get("subject"))) {
                    this.parseReturnInfoHtml(emailInfo);
                } else { //解析订单取消
                    this.parseOrderCannelHtml(emailInfo);
                }
                log.error("EMAIL:完成解析html文本");
                //保存附件
                if (StringUtil.isNotEmpty(emailInfo.getAttachments())) {
                    List<String> stringList = JSONArray.parseArray(emailInfo.getAttachments(), String.class);
                    List<TicketAttaches> ticketAttachesList = stringList.stream().map(i -> {
                        TicketAttaches ticketAttaches = new TicketAttaches();
                        ticketAttaches.setOrganizationId(emailInfo.getOrganizationId() != null ? emailInfo.getOrganizationId().longValue() : null);
                        int lastIndex = i.lastIndexOf("/");
                        String fileName = i.substring(lastIndex + 1);
                        int index = fileName.indexOf('.');
                        // 如果找到了 '.'，则截取后面的字符串
                        if (index != -1) {
                            fileName = fileName.substring(index + 1);
                        }
                        ticketAttaches.setFileName(fileName);
                        ticketAttaches.setFileUrl(i);
                        ticketAttaches.setDocumentId(emailInfo.getInfoId());
                        ticketAttaches.setDocumentType("sync_email_info");
                        ticketAttaches.settingCreated();
                        return ticketAttaches;
                    }).collect(Collectors.toList());
                    ticketAttachesService.saveBatch(ticketAttachesList);
                }
            }
            Long end = System.currentTimeMillis();
            Long s = end - start;
            log.error("解析wayfair花费时间为:{}",s);
            // 修改同步时间信息
            updateMerchanEmailTime(emailId, beginTime, endDate, emailList);
        }
    }

    /**
     * Description: 解析退货html文件
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/11
     */
    public void parseReturnInfoHtml(SyncEmailInfo syncEmailInfo) {
        //todo 解析
        log.error("EMAIL:开始插入解析邮件数据:{}",syncEmailInfo.getEmailContent());
        WayFairReturnVO wayFairReturnVO = null;
        WayFairReturnItemVO wayFairReturnItemVO = null;
        WayFairReturnTrackVO wayFairReturnTrackVO = null;
        try {
            Lexer lexer = new Lexer(syncEmailInfo.getEmailContent());
            Parser parser = new Parser(lexer);
            StringFilter stringFilter = new StringFilter("PO Number");
            //获取匹配到的节点
            NodeList nodeList = parser.extractAllNodesThatMatch(stringFilter);
            //遍历
            for (int i = 0; i < nodeList.size(); i++) {
                Node node = nodeList.elementAt(i);
                node = node.getParent().getParent().getParent().getParent();//找到/table 结尾的一个完整的节点然后再去遍历子节点拿到下面的值。
                node.getChildren().remove(node.getChildren().size() - 1);//去除尾
                node.getChildren().remove(0);//去除头
                node.getChildren().remove(0);//去除标题
                for (Node childNode : node.getChildren().toNodeArray()) {
                    Node[] nodeArray = childNode.getChildren().toNodeArray();
                    String[] strings = new String[3];
                    for (int j = 0; j<nodeArray.length ;j++) {
                        if(nodeArray[j].getFirstChild().getFirstChild() !=null) {
                            strings[j] = nodeArray[j].getFirstChild().getFirstChild().getText();
                        }
                    }
                    wayFairReturnVO = new WayFairReturnVO(strings[0],strings[1],strings[2]);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        try {
            Lexer lexer = new Lexer(syncEmailInfo.getEmailContent());
            Parser parser = new Parser(lexer);
            //获取匹配到的节点
            StringFilter stringFilter = new StringFilter("Customer Name");
            NodeList nodeList = parser.extractAllNodesThatMatch(stringFilter);
            //遍历
            for (int i = 0; i < nodeList.size(); i++) {
                Node node = nodeList.elementAt(i);
                node = node.getParent().getParent().getParent().getParent();//找到/table 结尾的一个完整的节点然后再去遍历子节点拿到下面的值。
                node.getChildren().remove(node.getChildren().size() - 1);//去除尾
                node.getChildren().remove(0);//去除头
                node.getChildren().remove(0);//去除标题
                for (Node childNode : node.getChildren().toNodeArray()) {
                    Node[] nodeArray = childNode.getChildren().toNodeArray();
                    String[] strings = new String[4];
                    for (int j = 0; j<nodeArray.length ;j++) {
                        if(nodeArray[j].getFirstChild().getFirstChild() != null) {
                            strings[j] = nodeArray[j].getFirstChild().getFirstChild().getText();
                        }
                    }
                    wayFairReturnItemVO = new WayFairReturnItemVO(strings[0],strings[1],strings[2],strings[3]);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        try {
            Lexer lexer = new Lexer(syncEmailInfo.getEmailContent());
            Parser parser = new Parser(lexer);
            //获取匹配到的节点
            StringFilter stringFilter = new StringFilter("Tracking");
            NodeList nodeList = parser.extractAllNodesThatMatch(stringFilter);
            //遍历
            for (int i = 0; i < nodeList.size(); i++) {
                Node node = nodeList.elementAt(i);
                node = node.getParent().getParent().getParent().getParent();//找到/table 结尾的一个完整的节点然后再去遍历子节点拿到下面的值。
                node.getChildren().remove(node.getChildren().size() - 1);//去除尾
                node.getChildren().remove(0);//去除头
                node.getChildren().remove(0);//去除标题
                for (Node childNode : node.getChildren().toNodeArray()) {
                    Node[] nodeArray = childNode.getChildren().toNodeArray();
                    String[] strings = new String[3];
                    for (int j = 0; j<nodeArray.length ;j++) {
                        if(nodeArray[j].getFirstChild().getFirstChild() !=null) {
                            strings[j] = nodeArray[j].getFirstChild().getFirstChild().getText();
                        }
                    }
                    wayFairReturnTrackVO = new WayFairReturnTrackVO(strings[0],strings[1],strings[2]);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        //todo 查询订单明细表
        if(wayFairReturnVO == null) {
            throw new RuntimeException("wayFairReturnVO为空发送时间为"+syncEmailInfo.getSendTime() + "，发件地址:" + syncEmailInfo.getFromAddress() + ",收件地址" + syncEmailInfo.getToAddress());
        }
        String poNumber = wayFairReturnVO.getPoNumber();
        if(StringUtil.isEmpty(poNumber)) {
            throw new RuntimeException("id:" + syncEmailInfo.getInfoId() + "的poNumber为空");
        }
        LambdaQueryWrapper<SaleOrderItems> saleOrderItemsQueryWrapper = new LambdaQueryWrapper<>();
        saleOrderItemsQueryWrapper.eq(SaleOrderItems::getDisabledAt, 0);
        saleOrderItemsQueryWrapper.eq(SaleOrderItems::getOrderNo,poNumber);
        saleOrderItemsQueryWrapper.select(SaleOrderItems::getChannelId,SaleOrderItems::getOrgId,SaleOrderItems::getChannelFlag,SaleOrderItems::getQuantity,
                SaleOrderItems::getItemAmount,SaleOrderItems::getCurrencyCode,SaleOrderItems::getChannelCreated,
                SaleOrderItems::getErpSku,SaleOrderItems::getSellerSku);
        List<SaleOrderItems> saleOrderItems = saleOrderItemsService.list(saleOrderItemsQueryWrapper);
        SaleOrderItems saleOrderItem = new SaleOrderItems();
        Account account = new Account();
        if(CollectionUtil.isNotEmpty(saleOrderItems)) {
            saleOrderItem = saleOrderItems.get(0);
            //查询店铺
            account = accountService.getById(saleOrderItem.getChannelId());
        }
        //todo 保存退货主表
        WayfairReturnInfo wayfairReturnInfo = new WayfairReturnInfo();
        wayfairReturnInfo.setOrganizationId(syncEmailInfo.getOrganizationId().intValue());
        wayfairReturnInfo.setChannelFlag(saleOrderItem.getChannelFlag());
        wayfairReturnInfo.setEmailInfoId(syncEmailInfo.getInfoId());
        wayfairReturnInfo.setPoNumber(wayFairReturnVO.getPoNumber());
        wayfairReturnInfo.setOriginalInvoice(wayFairReturnVO.getOriginalInvoice());
        wayfairReturnInfo.setReasonReturn(wayFairReturnVO.getReasonReturn());
        wayfairReturnInfo.setCustomerName(wayFairReturnItemVO.getCustomerName());
        wayfairReturnInfo.settingDefaultValue();
        wayfairReturnInfoService.save(wayfairReturnInfo);

        //todo 保存退货详情表
        WayfairReturnItemInfo wayfairReturnItemInfo = new WayfairReturnItemInfo();
        wayfairReturnItemInfo.setOrganizationId(syncEmailInfo.getOrganizationId().intValue());
        wayfairReturnItemInfo.setChannelFlag(saleOrderItem.getChannelFlag());
        wayfairReturnItemInfo.setWayfairReturnInfoId(wayfairReturnInfo.getId());
        wayfairReturnItemInfo.setEmailInfoId(syncEmailInfo.getInfoId());
        wayfairReturnItemInfo.setPoNumber(wayFairReturnVO.getPoNumber());
        wayfairReturnItemInfo.setItem(wayFairReturnItemVO.getItem());
        wayfairReturnItemInfo.setRaNumber(wayFairReturnItemVO.getRaNumber());
        wayfairReturnItemInfo.setExpectedCredit(wayFairReturnItemVO.getExpectedCredit());
        wayfairReturnItemInfo.setTracking(wayFairReturnTrackVO.getTracking());
        //单独处理时间
        if(StringUtil.isNotEmpty(wayFairReturnTrackVO.getShippedOn())) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("MM/dd/yyyy");
            try {
                Date shippedOnDate = simpleDateFormat.parse(wayFairReturnTrackVO.getShippedOn());
                wayfairReturnItemInfo.setShippedOn(shippedOnDate);
            }catch (Exception e) {
                log.error("WayfairReturnItemInfo表中ShippedOn字段转化成时间异常" + e.getMessage());
            }
        }
        wayfairReturnItemInfo.setShippedVia(wayFairReturnTrackVO.getShippedVia());
        wayfairReturnItemInfo.settingDefaultValue();
        wayfairReturnItemInfoService.save(wayfairReturnItemInfo);

        //todo returnInfo表信息
        ReturnInfoEntity returnInfoEntity = new ReturnInfoEntity();
        returnInfoEntity.setCreatedAt(LocalDateTime.now()).setCreatedBy(1).setCreatedName("system");
        //todo
        returnInfoEntity.setOrganizationId(syncEmailInfo.getOrganizationId().intValue()).setShopId(saleOrderItem.getChannelId()).setShopName(account.getFlag());
        returnInfoEntity.setOrderNo(wayfairReturnInfo.getPoNumber())
                .setReturnReason(wayfairReturnInfo.getReasonReturn())
                .setTrackNo(wayfairReturnItemInfo.getTracking())
                .setReturnStatus("Shipped")
                .setBusinessStatus(ReturnBusinessStatus.WAIT.getValue())
                .setReturnCarrier(wayfairReturnItemInfo.getShippedVia())
                .setReturnId(syncEmailInfo.getInfoId().toString());

        if(CollectionUtil.isNotEmpty(saleOrderItems)) {
            if(saleOrderItems.size() == 1) {
                returnInfoEntity.setOrderNum(saleOrderItem.getQuantity()).setReturnNum(saleOrderItem.getQuantity()).setOrderAmount(saleOrderItem.getItemAmount()).setReturnAmount(saleOrderItem.getItemAmount()).setCurrency(saleOrderItem.getCurrencyCode()).setOrderDate(LocalDateTime.ofInstant(saleOrderItem.getChannelCreated().toInstant(), ZoneId.systemDefault()));
                returnInfoEntity.setSku(saleOrderItem.getErpSku());
                returnInfoEntity.setSellerSku(saleOrderItem.getSellerSku());
                if (StringUtil.isNotEmpty(saleOrderItem.getSellerSku())) {
                    ReturnInfoEntity asinAndOperateInfo = returnInfoMapper.getAsinAndOperateInfoBysellerSku(saleOrderItem.getSellerSku(),saleOrderItem.getChannelId().toString());
                    if (null != asinAndOperateInfo) {
                        returnInfoEntity.setOperateId(asinAndOperateInfo.getOperateId()).setOperateName(asinAndOperateInfo.getOperateName());
                    }
                    QueryWrapper<Products> productsQueryWrapper = new QueryWrapper<>();
                    productsQueryWrapper.eq("erpsku",saleOrderItem.getErpSku());
                    List<Products> productsList = productsService.list(productsQueryWrapper);
                    if (CollectionUtil.isNotEmpty(productsList)) {
                        returnInfoEntity.setProductName(productsList.get(0).getName());
                    }
                }
            }else {
                returnInfoEntity.setCurrency(saleOrderItem.getCurrencyCode()).setOrderDate(LocalDateTime.ofInstant(saleOrderItem.getChannelCreated().toInstant(), ZoneId.systemDefault()));
            }
        }
        if(wayfairReturnItemInfo.getShippedOn() != null) {
            returnInfoEntity.setReturnDate(LocalDateTime.ofInstant(wayfairReturnItemInfo.getShippedOn().toInstant(), ZoneId.systemDefault()));
        }
        returnInfoEntity.setChannel("wayfair");
        List<ReturnInfoEntity> returnInfoEntityList = new ArrayList<>();
        returnInfoEntityList.add(returnInfoEntity);
        returnInfoMapper.insertReturnInfoEntity(returnInfoEntityList);
        amazonReturnService.createWayfairReturnTicket(returnInfoEntity);

    }

    /**
     * Description: 解析订单取消html文件
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/11
     */
    public void parseOrderCannelHtml(SyncEmailInfo syncEmailInfo) {
        //todo 解析
        log.error("EMAIL:开始插入解析wayfair订单取消邮件数据:{}",syncEmailInfo.getEmailContent());
        WayFairOrderCancleVO wayFairOrderCancleVO = new WayFairOrderCancleVO();
        wayFairOrderCancleVO.setInfoId(syncEmailInfo.getInfoId());
        wayFairOrderCancleVO.setSendDate(syncEmailInfo.getSendTime());
        wayFairOrderCancleVO.setShopId(syncEmailInfo.getShopId());
        try {
            Lexer lexer = new Lexer(syncEmailInfo.getEmailContent());
            Parser parser = new Parser(lexer);
            StringFilter stringFilter = new StringFilter("PO Number");
            //获取匹配到的节点
            NodeList nodeList = parser.extractAllNodesThatMatch(stringFilter);
            //遍历
            for (int i = 0; i < nodeList.size(); i++) {
                Node node = nodeList.elementAt(i);
                node = node.getParent().getParent().getParent().getParent();//找到/table 结尾的一个完整的节点然后再去遍历子节点拿到下面的值。
                String poNumber = node.getChildren().elementAt(2).getFirstChild().getFirstChild().getFirstChild().getText(); //获取字节点对应的值。
                wayFairOrderCancleVO.setPoNumber(poNumber);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        try {
            Lexer lexer = new Lexer(syncEmailInfo.getEmailContent());
            Parser parser = new Parser(lexer);
            //获取匹配到的节点
            StringFilter stringFilter = new StringFilter("Qty");
            NodeList nodeList = parser.extractAllNodesThatMatch(stringFilter);
            //遍历
            for (int i = 0; i < nodeList.size(); i++) {
                Node node = nodeList.elementAt(i);
                node = node.getParent().getParent().getParent().getParent();;//找到/table 结尾的一个完整的节点然后再去遍历子节点拿到下面的值。
                String qty = node.getChildren().elementAt(3).getFirstChild().getFirstChild().getFirstChild().getText();
                String itemCode = node.getChildren().elementAt(3).getChildren().elementAt(1).getFirstChild().getFirstChild().getText();
                wayFairOrderCancleVO.setQty(new Integer(qty));
                wayFairOrderCancleVO.setItemCode(itemCode);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        //todo 订单取消使用wayFairOrderCancleVO当入参。
        saleOrderCancelService.emailSyncWairOrderCancel(wayFairOrderCancleVO);
        log.error("EMAIL:解析wayfair订单取消邮参数:{}",wayFairOrderCancleVO);

    }
    /**
     * 获取查询是否有未关闭的站外信工单
     *
     * @param emailInfo
     * @return
     */
    @Override
    public ScTicket getOpenScTicket(SyncEmailInfo emailInfo) {
        Long id = this.getParentMail(emailInfo);
        if(id != null) {
            ScTicket scTicket = scTicketService.selectScTicketBySource(id, ScTicketConstant.TICKET_SOURCE_DOCUMENT_EMAIL);
            if (scTicket != null) {
                if (ScTicketConstant.TICKET_TYPE_EMAIL.equals(scTicket.getTicketType())) {
                    return scTicket;
                }
            }
        }
        return null;
    }

    /**
     * 单独同步某个邮箱内容
     *
     * @param merchantEmailId
     * @param start
     * @param end
     */
    @Override
    public void syncEmailInfo(Long merchantEmailId, String start, String end) {
        List<MerchantEmail> merchantEmailList = new ArrayList<>();
        MerchantEmail m = merchantEmailMapper.selectMerchantEmailById(merchantEmailId);
        merchantEmailList.add(m);
        if (CollectionUtils.isEmpty(merchantEmailList)) {
            return;
        }

        for (MerchantEmail merchantEmail : merchantEmailList) {
            try {
                log.error("开始手动同步邮件");
                manulSyncEmail(merchantEmail,start,end);
            } catch (Exception e) {
                log.error("EMAIL-手动同步邮件失败.ID:{}, 邮箱：{},异常原因:{}", merchantEmail.getId(), merchantEmail.getEmail(),e.getMessage());
            }
        }
    }

    private void manulSyncEmail(MerchantEmail merchantEmail,String start,String end) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date endDate = null;
        Date beginTime = null;
        try {
            endDate = simpleDateFormat.parse(end);
            // 多同步一天的数据
            beginTime = simpleDateFormat.parse(start);
        } catch (Exception e) {
            log.error("--------邮件时间解析异常：{}",e.getMessage());
        }

        List<Map<String, Object>> emails = null;
        if(merchantEmail.getEmail().contains("@outlook.com")) {
            String token = this.getoutLookAccessToken(merchantEmail);
            if(StringUtil.isNotEmpty(token)) {
                emails = graphEmailApi.readEmail(merchantEmail, merchantEmail.getEmail(),
                        merchantEmail.getPassword(), beginTime, endDate,token);
            }
        } else {
            emails = emailApi.readEmail(merchantEmail,merchantEmail.getEmail(),
                    merchantEmail.getPassword(), beginTime, endDate, merchantEmail.getId().toString(),null);
        }
        saveEmailInfo(emails, merchantEmail,merchantEmail.getEmail(), merchantEmail.getId(), merchantEmail.getOrganizationId(), merchantEmail.getShopId(), merchantEmail.getEmailType(),beginTime, endDate);
    }

    /**
     * 同步邮箱内容为walmart的订单取消的邮件
     */
    @Override
    @Transactional
    public void syncEmailInfoWithwalmartOrderCancel(Long id,String startDateString,String endDateString) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        MerchantEmail queryMerchantEmail = new MerchantEmail();
        queryMerchantEmail.setOrganizationId(1000049);
        queryMerchantEmail.setType("walmart");
        queryMerchantEmail.setId(id);
        queryMerchantEmail.setEnabledFlag("Y");
        List<MerchantEmail> merchantEmailList = merchantEmailMapper.selectMerchantEmailList(queryMerchantEmail);
        if (CollectionUtils.isEmpty(merchantEmailList)) {
            return;
        }
        merchantEmailList = merchantEmailList.stream().filter(i -> !"<EMAIL>".equals(i.getEmail())).collect(Collectors.toList());
        for (MerchantEmail merchantEmail : merchantEmailList) {
            String storeName = merchantEmail.getStoreName();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.HOUR_OF_DAY, 8);
            Date endDate = StringUtil.isNotEmpty(endDateString) ? simpleDateFormat.parse(endDateString): calendar.getTime();
            log.error("--------邮件开始获取,当前邮箱账号：{}", merchantEmail.getEmail());
            // 多同步一天的数据，怕漏了。
            Date beginTime = StringUtil.isNotEmpty(startDateString) ? DateUtil.addDate(simpleDateFormat.parse(startDateString), -1) : DateUtil.addDate(merchantEmail.getBeginTime(), -1);
            Long start = System.currentTimeMillis();
            List<Map<String, Object>> emails = emailApi.readEmail(merchantEmail,merchantEmail.getEmail(),
                    merchantEmail.getPassword(), beginTime, endDate, merchantEmail.getId().toString(),"walmart");
            //todo 筛选出是walmart的订单取消邮件
            if (CollectionUtil.isEmpty(emails)) {
                continue;
            }
            String emailAccount = merchantEmail.getEmail();
            Long emailId = merchantEmail.getId();
            Integer organizationId = merchantEmail.getOrganizationId();
            Long shopId = merchantEmail.getShopId();
            String emailType = merchantEmail.getEmailType();
            log.error("EMAIL:--------邮件开始处理,当前邮箱账号：{}", emailAccount);
            if (CollectionUtils.isEmpty(emails)) {
                log.error("EMAIL:--------邮件数据为空,当前邮箱账号：{}", emailAccount);
                return;
            }
            List<Map<String, Object>> emailList = new ArrayList<>();

            for (Map<String, Object> email : emails) {
                if (email == null || email.get("messageId") == null) {
                    continue;
                }
                if (containsEmail(emailList, email)) {
                    continue;
                }
                emailList.add(email);
            }
            if (CollectionUtils.isEmpty(emailList)) {
                return;
            }
            log.error("EMAIL:--------emailList长度--------:{}", emailList.size());
            Account account = accountService.getById(shopId);
            log.error("EMAIL:-------当前处理电子邮件账号：{}", emailAccount);
            for (Map<String, Object> email : emailList) {
//            log.error("--------邮件信息--------:{}|{}|{}",email.get("messageId"),email.get("sentDate"),email.get("subject"));
                SyncEmailInfo record = new SyncEmailInfo();
                record.setEmailId((String) email.get("messageId"));
                record.setSendTime((Date) email.get("sentDate"));
                record.setEmailTitle((String) email.get("subject"));

                List<SyncEmailInfo> emailInfos = emailInfoMapper.selectEmailInfoList(record);
                if (!CollectionUtils.isEmpty(emailInfos)) {
                    continue;
                }

                SyncEmailInfo emailInfo = new SyncEmailInfo();
                emailInfo.setOrganizationId(organizationId.longValue());
                String folder = convertFolder((String) email.get("folder"), (String) email.get("from"));
                if ("1".equals(emailType)) {
                    emailInfo.setEmailFolder(folder);
                } else {
                    emailInfo.setEmailFolder("EMAIL");
                }

                if (!Objects.isNull(account)) {
                    emailInfo.setShopId(account.getId().longValue());
                }
                emailInfo.setEmailId((String) email.get("messageId"));
                emailInfo.setSendTime((Date) email.get("sentDate"));
                emailInfo.setEmailTitle((String) email.get("subject"));
                emailInfo.setEmailContent((String) email.get("content"));
                String from = (String) email.get("from");
                emailInfo.setSendName(from.substring(0, from.indexOf("<")));
                emailInfo.setFromAddress(from.substring(from.indexOf("<") + 1, from.indexOf(">")));
                emailInfo.setToAddress((String) email.get("to"));
                emailInfo.setCcAddress((String) email.get("cc"));
                emailInfo.setBccAddress((String) email.get("bcc"));
                emailInfo.setAttachments(JSONArray.toJSONString(email.get("attachments")));
                emailInfo.setContentImages(JSONArray.toJSONString(email.get("contentImages")));
                emailInfo.setMerchantEmailId(emailId);
                log.error("EMAIL:开始插入邮件数据，EmailId:{},SendTime:{}", emailId, emailInfo.getSendTime());
                emailInfoService.insertEmailInfo(emailInfo);
                String title = "Message from Walmart.com Customer: Cancel my order";
                //todo 解析walmart 取消订单
                if (((String) email.get("subject")).contains(title)) {
                    this.parseWalmartOrderCancleHtml(emailInfo, storeName);
                }
                //todo 解析
                //提取订单号：Purchase Order No后面的内容
                //
                //提取站内信内容：Agent Message 后面的内容
                Boolean contactFalg = false;
                Map<String, String> contactMap = new HashMap<>();
                String title2 = "Please contact the customer";
                if (((String) email.get("subject")).contains(title2) && !((String) email.get("subject")).toLowerCase().contains("re:")) {
                    contactMap = parseWalmartContactTheCustomerHtml(emailInfo);
                    contactFalg = true;
                    if(((String) email.get("content")).contains("This is Walmart Customer Care, contacting you on behalf of the customer for the order below.")) {
                        emailInfo.setShowFlag(1);
                    }
                }
                log.error("EMAIL:完成解析html文本");
                //保存附件
                if (StringUtil.isNotEmpty(emailInfo.getAttachments())) {
                    List<String> stringList = JSONArray.parseArray(emailInfo.getAttachments(), String.class);
                    List<TicketAttaches> ticketAttachesList = stringList.stream().map(i -> {
                        TicketAttaches ticketAttaches = new TicketAttaches();
                        ticketAttaches.setOrganizationId(emailInfo.getOrganizationId() != null ? emailInfo.getOrganizationId().longValue() : null);
                        int lastIndex = i.lastIndexOf("/");
                        String fileName = i.substring(lastIndex + 1);
                        int index = fileName.indexOf('.');
                        // 如果找到了 '.'，则截取后面的字符串
                        if (index != -1) {
                            fileName = fileName.substring(index + 1);
                        }
                        ticketAttaches.setFileName(fileName);
                        ticketAttaches.setFileUrl(i);
                        ticketAttaches.setDocumentId(emailInfo.getInfoId());
                        ticketAttaches.setDocumentType("sync_email_info");
                        ticketAttaches.settingDefaultCreate();
                        return ticketAttaches;
                    }).collect(Collectors.toList());
                    ticketAttachesService.saveBatch(ticketAttachesList);
                }
                if (!contactFalg) {
                    //todo 解析站内信包括取消订单
                    String content = (String) email.get("content");
                    // 1. 使用正则表达式去除 HTML 标签
                    String noHtmlTags = content.replaceAll("<[^>]*>", "");  // 去除所有 HTML 标签
                    // 2. 替换 HTML 实体，比如 &nbsp;
                    noHtmlTags = noHtmlTags.replaceAll("\\u00A0", " ");  // 替换 &nbsp;（unicode 编码）
                    noHtmlTags = noHtmlTags.replaceAll("&nbsp;", " ");  // 替换所有的 &nbsp; 为普通空格
                    noHtmlTags = noHtmlTags.replaceAll("&amp;", "&");    // 替换 &amp; 为 &
                    noHtmlTags = noHtmlTags.replaceAll("&lt;", "<");      // 替换 &lt; 为 <
                    noHtmlTags = noHtmlTags.replaceAll("&gt;", ">");      // 替换 &gt; 为 >
                    noHtmlTags = noHtmlTags.replaceAll("&quot;", "\"");   // 替换 &quot; 为 "
                    noHtmlTags = noHtmlTags.replaceAll("&apos;", "'");    // 替换 &apos; 为 '
                    if (StringUtil.isNotEmpty(noHtmlTags)) {
                        /**
                         1、Customer Message之后且what you need to do now之前的
                         2、You have received an inquiry from ... 之后且You can respond to the customer之前
                         3、Sent from my iPhone  前面的
                         4、On...wrote 前面的
                         */
                        // 正则表达式模式，匹配日期时间部分和 'wrote:' 部分作为一个整体
                        String regex = "On\\s(.*?)\\swrote:";
                        Pattern pattern = Pattern.compile(regex);
                        Matcher matcher = pattern.matcher(noHtmlTags);
                        String stationLetterContent = noHtmlTags;
                        if (noHtmlTags.contains("Customer Message:") && noHtmlTags.contains("What you need to do now")) {
                            stationLetterContent = noHtmlTags.substring(noHtmlTags.indexOf("Customer Message:") + 17, noHtmlTags.indexOf("What you need to do now")).trim();
                        } else if (noHtmlTags.contains("You have received an inquiry from") && noHtmlTags.contains("You can respond to the customer")) {
                            stationLetterContent = noHtmlTags.substring(noHtmlTags.indexOf("You have received an inquiry from") + 33, noHtmlTags.indexOf("You can respond to the customer")).trim();
                        } else if (noHtmlTags.contains("Sent from my iPhone")) {
                            stationLetterContent = noHtmlTags.substring(0, noHtmlTags.indexOf("Sent from my iPhone")).trim();
                        } else if (matcher.find()) {
                            int startIndex = matcher.start();
                            stationLetterContent = noHtmlTags.substring(0, startIndex).trim();
                        }
                        emailInfo.setStationLetterContent(stationLetterContent);
                        scStationLetterService.saveStationLetterByEmailInfo(emailInfo, shopId, merchantEmail.getTitle(), "walmart");
                    }
                } else {
                    String purchaseOrderNo = contactMap.get("PurchaseOrderNo");
                    emailInfo.setPurchaseOrderNo(purchaseOrderNo);
                    String agentMessage = contactMap.get("AgentMessage");
                    emailInfo.setStationLetterContent(agentMessage);
                    scStationLetterService.saveStationLetterByEmailInfo(emailInfo, shopId, merchantEmail.getTitle(), "walmart");

                }

            }
            Long end = System.currentTimeMillis();
            Long s = end - start;
            log.error("解析walmart花费时间为:{}",s);
            // 修改同步时间信息
            updateMerchanEmailTime(emailId, beginTime, endDate, emailList);
        }
    }

    /**
     * 单独同步某个邮箱内容
     *
     * @param merchantEmailId
     * @param start
     * @param end
     */
    @Override
    public void syncWalmartEmailInfo(Long merchantEmailId, String start, String end) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date endDate = null;
        Date beginTime = null;
        try {
            endDate = simpleDateFormat.parse(end);
            // 多同步一天的数据
            beginTime = simpleDateFormat.parse(start);
        } catch (Exception e) {
            log.error("--------邮件时间解析异常：{}",e.getMessage());
        }
        this.syncEmailInfoWithwalmartOrderCancel(merchantEmailId,start,end);

    }

    /**
     * Description: 解析html文件
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/11
     */
    public void parseWalmartOrderCancleHtml(SyncEmailInfo syncEmailInfo,String storeName) {
        if(StringUtil.isEmpty(storeName)) {
            log.error("解析walmart邮件storeName为空");
            return;
        }
        //todo 解析
        log.error("EMAIL:开始插入解析邮件数据:{}",syncEmailInfo.getEmailContent());
        String po = null; //订单号
        String records = null;
        //todo 获取订单号
        try {
            Lexer lexer = new Lexer(syncEmailInfo.getEmailContent());
            Parser parser = new Parser(lexer);
            StringFilter stringFilter = new StringFilter("Purchase Order No");
            //获取匹配到的节点
            NodeList nodeList = parser.extractAllNodesThatMatch(stringFilter);
            //遍历
            String orderNo = nodeList.elementAt(0).getParent().getChildren().elementAt(3).getText();
            if(StringUtil.isNotEmpty(orderNo)) {
                po = orderNo.substring(orderNo.indexOf(":&nbsp; ") + 8,orderNo.length());
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        //todo 获取 item_number
        try {
            Lexer lexer = new Lexer(syncEmailInfo.getEmailContent());
            Parser parser = new Parser(lexer);
            StringFilter stringFilter = new StringFilter("Item name/number:");
            //获取匹配到的节点
            NodeList nodeList = parser.extractAllNodesThatMatch(stringFilter);
            //遍历
            String itemNumber = nodeList.elementAt(0).getParent().getChildren().elementAt(3).getText();
            if(StringUtil.isNotEmpty(itemNumber)) {
                records = itemNumber.substring(itemNumber.lastIndexOf("/") + 1,itemNumber.length() - 1);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        if(StringUtil.isEmpty(po)) {
            log.error("解析walmart邮件po为空");
            return;
        }
        if(StringUtil.isEmpty(records)) {
            log.error("解析walmart邮件item_number为空");
            return;
        }
        JSONObject parmaJsonObject = new JSONObject();
        parmaJsonObject.put("PO#",po);
        parmaJsonObject.put("store_name",storeName);
        parmaJsonObject.put("email_id",syncEmailInfo.getInfoId());
        JSONArray itemNumberJSONArray = new JSONArray();
        Map<String,Object> map = new HashMap<>();
        map.put("item_number",records);
        itemNumberJSONArray.add(map);
        parmaJsonObject.put("Records",itemNumberJSONArray);
        log.error("解析walmart邮件发送mq参数{}",parmaJsonObject.toJSONString());
        rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_E_ERP, MQDefine.SALE_ORDER_CANCEL_WALMART_MESSAGE_ROUTING_KEY, parmaJsonObject);
    }


    /**
     * Description: 监听邮箱拉取
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2025/1/2
     */
    @Override
    public void listeneringEmailJob() {
        //todo 查询出所有的站外信邮箱
        List<MerchantEmail> merchantEmailList = merchantEmailMapper.selectEnabledAndNotWayFairMerchantEmail();
        //todo  筛选站外信的
        if(CollectionUtil.isNotEmpty(merchantEmailList)) {
            merchantEmailList = merchantEmailList.stream().filter(i -> "2".equals(i.getEmailType())).collect(Collectors.toList());
        }
        if(CollectionUtil.isNotEmpty(merchantEmailList)) {
            //消息内容,发送人
            for (MerchantEmail merchantEmail : merchantEmailList) {
                Date update = merchantEmail.getUpdatedAt();
                Date now = DateUtils.getNowDate();
                if(update != null) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(update);
                    calendar.add(Calendar.HOUR_OF_DAY, 2);
                    Long updateMills = calendar.getTimeInMillis();
                    Calendar calendar2 = Calendar.getInstance();
                    calendar2.setTime(now);
                    Long nowMills = calendar2.getTimeInMillis();
                    // 获取两个时间的毫秒差
                    long millisDifference = updateMills - nowMills;
                    if(millisDifference < 0 ) {
                        String advice = "该邮箱" + merchantEmail.getEmail() + "已超过两个小时未跟新";
                        WeComRobotUtil.sendTextMsgNew(advice, "EMAIL_ADVICE", weChatBootConfigure);
                    }
                }
            }
        }
    }

    /**
     * Description: 解析html文件
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/11
     */
    public Map<String,String> parseWalmartContactTheCustomerHtml(SyncEmailInfo syncEmailInfo) {

        Map<String,String> resultMap = new HashMap<>();
        //todo 解析
        log.error("EMAIL:开始插入解析邮件数据:{}",syncEmailInfo.getEmailContent());

        //todo 获取订单号
        try {
            Lexer lexer = new Lexer(syncEmailInfo.getEmailContent());
            Parser parser = new Parser(lexer);
            StringFilter stringFilter = new StringFilter("Purchase Order No:");
            //获取匹配到的节点
            NodeList nodeList = parser.extractAllNodesThatMatch(stringFilter);
            String orderNo = nodeList.elementAt(0).getParent().getChildren().elementAt(4).getText();
            if(StringUtil.isNotEmpty(orderNo)) {
                String purchaseOrderNo = orderNo.substring(orderNo.indexOf(":&nbsp;") + 7,orderNo.length());
                resultMap.put("PurchaseOrderNo",purchaseOrderNo);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        //todo 获取 Agent message
        try {
            Lexer lexer = new Lexer(syncEmailInfo.getEmailContent());
            Parser parser = new Parser(lexer);
            StringFilter stringFilter = new StringFilter("Agent message:");
            //获取匹配到的节点
            NodeList nodeList = parser.extractAllNodesThatMatch(stringFilter);
            String agentMessageString = nodeList.elementAt(0).getParent().getChildren().elementAt(3).getText();
            if(StringUtil.isNotEmpty(agentMessageString)) {
                String  agentMessage = agentMessageString.substring(agentMessageString.indexOf(":&nbsp;") + 7,agentMessageString.length());
                resultMap.put("AgentMessage", agentMessage);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return resultMap;

    }


}
