package com.bizark.op.service.mapper.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bizark.op.api.entity.op.sale.Products;
import com.bizark.op.api.entity.op.sale.vo.ProductCategoryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProductsMapper extends BaseMapper<Products> {

    List<Products> selectProductsList(Products products);


    int updateInventory(Products products);

    int updateCategoryFirstId(@Param("id") Integer id,@Param("categoryFirstId") Long categoryFirstId);

    int updateCategoryId(@Param("id") Integer id,@Param("categoryFirstId") Long categoryFirstId,@Param("categorySecondId") Long categorySecondId);

    List<ProductCategoryVO> selectByErpSkus(@Param("erpSkus") List<String> erpSkus);

    List<Products> selectProductInfoByErpSkus(@Param("erpSkus") List<String> erpSkus);


    /**
     * @description: 查询获取二级SKU二级分类
     * @author: Moore
     * @date: 2024/5/30 11:00
     * @param
     * @param erpSkus
     * @return: java.util.List<java.lang.String>
    **/
    List<String> selectProductCategoryName(@Param("erpSkus") List<String> erpSkus);
    /**
     * @description: 修改产品编号标记为空
     * @param productIds 产品ID
     * @return: 影响条数
    **/
    int updateProductNumberMarkNull(@Param("productIds") List<Integer> productIds);

    List<Products> selectProductsByOrgIdAndErpSkus(@Param("orgId") Integer orgId, @Param("erpSkus") List<String> erpSkus);

    List<Integer> selectOrgIds();

    List<Products> selectByOrgId(@Param("orgId") Integer orgId);
}
