package com.bizark.op.service.mapper.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bizark.op.api.dto.sale.ChannelsInventoryConfReq;
import com.bizark.op.api.dto.sale.TiktokProductQueryDTO;
import com.bizark.op.api.dto.temu.TemuCategoryInvestmentDTO;
import com.bizark.op.api.entity.op.mar.MarListingQuery;
import com.bizark.op.api.entity.op.mar.MarProductPopularizeLogParentAsinVO;
import com.bizark.op.api.entity.op.mar.ProductPopularizeResEntity;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.entity.op.sale.ProductChannelsExportQuery;
import com.bizark.op.api.entity.op.sale.vo.ErpSkuStockVO;
import com.bizark.op.api.entity.op.sale.vo.ProductChannelsExportVO;
import com.bizark.op.api.entity.op.sale.vo.WalmartAsinVo;
import com.bizark.op.api.entity.op.stat.StatOrderSalesQuery;
import com.bizark.op.api.entity.op.ticket.TicketMsgCardInfo;
import com.bizark.op.api.vo.mar.ListingVo;
import com.bizark.usercenter.api.parameter.user.UserDeptVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.ResultHandler;

import java.util.Collection;
import java.util.List;

@Mapper
public interface ProductChannelsMapper extends BaseMapper<ProductChannels> {


    /**
     * 查询列表数据
     * @param channels
     * @return
     */
    List<ProductChannels> selectProductChannels(ProductChannels channels);

    void streamQueryProductChannels(ResultHandler<ProductChannelsExportVO> handler, ProductChannels channels);

    List<ProductChannelsExportVO> productChannelsExportQuery(ProductChannelsExportQuery channels);

    List<ProductChannels> selectProductChannelsByShop(ProductChannels productChannels);

    int updateSellerStatus(@Param("id") Integer id,@Param("sellStatus") String sellStatus);

    int updateApprovalStatus(@Param("ids") List<Integer> ids, @Param("apporvalStatus") String approvalStatus, @Param("userId") Integer userId, @Param("userName") String userName);

    /**
     * @description: 获取所有映射ASIN信息
     * @author: Moore
     * @date: 2023/9/21 10:46
     * @param
     * @param listingVo
     * @return: java.util.List<com.bizark.op.api.entity.op.sale.ProductChannels>
     **/
    List<ListingVo> selectProductChannelsList(ListingVo listingVo);


    List<ListingVo> selectReviewAsinList(ProductChannels productChannels);


    int updateInventoryItemIdById(@Param("id") Integer id, @Param("inventoryItemId") String inventoryItemId);

    int updateInventoryById(@Param("id") Integer id,@Param("inventory") Integer inventory);

    int updateInventoryStatusByIds(@Param("ids") List<Integer> ids,@Param("inventoryStatus")Integer inventoryStatus);

    int updateItemIdById(@Param("id") Integer id, @Param("itemId") String itemId);

    void updateParentAsinById(@Param("id") Integer id, @Param("asin1") String asin1);

    List<ProductChannels> selectBySellerSkus(List<String> sellerSkus);

    List<ProductChannels> selectByItemIds(@Param("productIds") List<String> productIds);

    List<ErpSkuStockVO> selectErpSkuInventory(@Param("orgId") Integer orgId,@Param("erpSkus") List<String> erpSkus);

    List<UserDeptVO> selectOperateUserInfoByUserIds(@Param("userIds") List<Integer> userIds);

    List<ProductChannels> selectProductChannelsByAccountAndSellSku(ProductChannels productChannel);


    /**
     * @param
     * @param
     * @description:根据子ASIN获取父ASIN
     * @author: Moore
     * @date: 2024/1/17 16:33
     * @return: 返回父ASIN
     **/
    List<ProductPopularizeResEntity> selectParenAsin(StatOrderSalesQuery statOrderSalesQuery);

    /**
     * @param
     * @param asin
     * @description:根据父ASIN获取子ASIN
     * @author: Moore
     * @date: 2024/1/17 16:33
     * @return: java.lang.String
     **/
    List<String> selectChildAsinByParentAsin(@Param("parentAsin") String asin);


    /**
     * @param
     * @param
     * @description:根据子ASIN获取父ASIN_listing整合使用
     * @author: Moore
     * @date: 2024/1/17 16:33
     * @return: 返回父ASIN
     **/
    List<MarProductPopularizeLogParentAsinVO> selectParenAsinByListing(MarListingQuery marListingQuery);

    /**
     * 根据erpSku获取子asin
     *
     * @param erpSku
     * @return
     */
    List<String> selectAsinByErpSku(@Param("erpSku")String erpSku);

    List<ProductChannels> selectProductChannelList(@Param("orgId") Long orgId, @Param("erpskus") Collection<String> erpskus, @Param("accountIds") Collection<String> accountIds, @Param("asins") Collection<String> asins);

    void updateInventoryPushConf(ChannelsInventoryConfReq req);


    List<ProductChannels> selectRapairProductChannels();

    /**
     * @description:
     * @author: Moore
     * @date: 2024/4/18 14:59
     * @param
     * @return: java.util.List<com.bizark.op.api.entity.op.sale.ProductChannels>
     **/
    List<ProductChannels> selectListingAsinGroupShop();


    int recordPriceLog(ProductChannels channel);

    int repairProductChannels();


    /**
     * @description: 查询是否存在此在售SKU
     * @author: Moore
     * @date: 2024/5/27 16:50
     * @param
     * @param erpSku
     * @return: java.util.List<com.bizark.op.api.entity.op.sale.ProductChannels>
     **/
    List<String> selectHitShelveByErpSku(@Param("erpSku")String erpSku,@Param("orgId")Long orgId);


    void streamQueryByOrgIdAndSaleChannel(ResultHandler<ProductChannels> handler, @Param("orgId") Integer orgId, @Param("saleChannel") String saleChannel);

    ProductChannels selectProductChannelById(@Param("id") Integer id);


   /**
    * Description: 获取所有Walmart所有asin信息
    * @Param: * @param null
    * @Return: {@link null}
    * @Author: Fountain
    * @Date: 2024/7/23
    */
    List<WalmartAsinVo> selectWalmartProductChannelsList(ProductChannels productChannels);

    int repairSlave();

    List<String> selectAsinByOrgId(@Param("orgId") Integer orgId);

    List<ProductChannels> selectByOrgIdAndAsinsAndSaleChannels(@Param("orgId") Integer orgId, @Param("asins") List<String> asins, @Param("saleChannels") List<String> saleChannels);

    List<String> selectBrandsByOrgId(@Param("orgId") Integer orgId);

    Integer selectChannelsCount(@Param("channel") ProductChannels query);

    ProductChannels selectByAccountIdAndSellerSku(@Param("accountId") String accountId,@Param("sellerSku") String sellerSku);



    /**
     * 根据tiktok父商品ID查询对应商品信息
     *
     * @param itemId
     * @return
     */
    List<TicketMsgCardInfo.SkuInfo> selectTiktokParentInfo(@Param("itemId") List<String> itemId);

    /**
     * 根据tiktok父商品ID查询对应商品信息,分页查询使用
     *
     * @param channelFlag 店铺Flag
     * @param commonParam 通用查询参数
     * @return
     */
    List<TicketMsgCardInfo.SkuInfo> selectTiktokParentInfoByPage(@Param("channelFlag") String channelFlag, @Param("commonParam") String commonParam);


    ProductChannels selectByAccountIdAndSellSku(@Param("accountId") String accountId,@Param("sellerSku") String sellerSku);

    int updateProductChannelsInvestment(@Param("list") List<TemuCategoryInvestmentDTO> dtos);

    List<ProductChannels> selectTiktokProductList(TiktokProductQueryDTO dto);

    Integer resetInventoryStatus();

    List<ProductChannels> selectByOrgIdAndAsinsAndAccountIds(@Param("orgId") Integer orgId, @Param("asins") List<String> asins, @Param("accountIds") List<String> accountIds);

    List<ProductChannels> selectByOrgIdAndAccountIdsAndErpSkuLikeAndAsinNotEmpty(@Param("orgId") Integer orgId , @Param("accountIds") List<String> accountIds, @Param("erpSkus") List<String> erpSkus);

    List<ProductChannels> selectByOrgIdAndAccountIdsSkuAsinNotEmpty(@Param("orgId") Integer orgId, @Param("accountIds") List<String> accountIds);
    int selectCountByOrgIdAndAccountIdsSkuAsinNotEmpty(@Param("orgId") Integer orgId, @Param("accountIds") List<String> accountIds);

    int updateSkuVersionSerial(@Param("orgId") Integer orgId, @Param("erpSku") String erpSku,@Param("skuVersionSerial") Integer skuVersionSerial);
    List<Integer> distinctOrgId();

}
