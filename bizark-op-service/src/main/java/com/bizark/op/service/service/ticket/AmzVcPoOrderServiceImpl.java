package com.bizark.op.service.service.ticket;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.bizark.boss.api.enm.AccountSaleChannelEnum;
import com.bizark.erp.api.entity.erp.purchase.result.PurchaseApplyCheckApiResult;
import com.bizark.op.api.cons.ScTicketConstant;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.order.SaleOrders;
import com.bizark.op.api.entity.op.ticket.AmzVcPoOrderAcceptedTs;
import com.bizark.op.api.entity.op.ticket.AmzVcPoOrderTimeRecord;
import com.bizark.op.api.entity.op.ticket.AmzVcPoOrderTimeRecordItem;
import com.bizark.op.api.entity.op.ticket.ScTicket;
import com.bizark.op.api.entity.op.ticket.order.AmzVcPoOrderV;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.ticket.*;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.DateUtil;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.service.mapper.ticket.SaleOrdersMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 亚马逊PO订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-19
 */
@Service
@Slf4j
public class AmzVcPoOrderServiceImpl implements IAmzVcPoOrderService {

    @Autowired
    private AmzVcPoOrderAcceptedTsService amzVcPoOrderAcceptedTsService;

    //暂时注释
//    @Autowired
//    private IPurchaseApplyBatchService purchaseApplyBatchService;

    /**
     * 工单信息
     */
    @Autowired
    private IScTicketService scTicketService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private IAmzVcPoOrderTimeRecordService amzVcPoOrderTimeRecordService;

    @Autowired
    private IAmzVcPoOrderTimeRecordItemService amzVcPoOrderTimeRecordItemService;

    @Autowired
    private SaleOrdersMapper saleOrdersMapper;



    /**
     * @description: Po确认工单保存
     * @author: Moore
     * @date: 2024/3/19 17:44
     * @param
     * @param amzVcPoOrderItems 保存明细行
     * @param ticketId 工单ID
     * @param purchaseReqNo 采购申请号
     * @return: void
    **/
    @Override
    @Transactional
    public String updateAmzVcPoItemOrderList(List<AmzVcPoOrderV> amzVcPoOrderItems, Long ticketId, Long orgId, String purchaseReqNo) {
        if (CollectionUtils.isEmpty(amzVcPoOrderItems)) {
            throw new CustomException("保存数据不能为空");
        }

        ScTicket scTicketOne = scTicketService.selectScTicketByBaseTicketId(ticketId);

        if (scTicketOne == null || scTicketOne.getShopId() == null) {
            throw new CustomException("工单信息获取失败！");
        }


        //获取未生成采购订单行
        if (amzVcPoOrderItems.stream().allMatch(item -> item.getCheckStatus() == 1)) {
            throw new CustomException("该工单所有订单已创建采购申请单，不可保存");
        }
        //获取未生成采购订单行
        if (amzVcPoOrderItems.stream().anyMatch(item -> item.getItemId() == null)) {
            throw new CustomException("订单信息获取失败！");
        }

        ArrayList<AmzVcPoOrderV> noSavaOrderList = new ArrayList<>();//不符合插入条件行


        Map<String, PurchaseApplyCheckApiResult> asinApplyList = new HashMap<>();
        //对无采购订单的行进行判断，是否存在采购订单
        if (!StringUtils.isEmpty(purchaseReqNo)) {
            //暂时注释
//            List<PurchaseApplyCheckApiResult> purchaseApplyCheckApiResults = purchaseApplyBatchService.checkByApplyNo(purchaseReqNo);
//            purchaseApplyCheckApiResults = purchaseApplyCheckApiResults.stream().filter(item -> Objects.equals(1, item.getCheckStatus())).collect(Collectors.toList());
//            if (!CollectionUtils.isEmpty(purchaseApplyCheckApiResults)) {
//                //ASIN+订单号分组
//                asinApplyList = purchaseApplyCheckApiResults.stream().collect(Collectors.toMap(PurchaseApplyCheckApiResult::fetchGroupKey, Function.identity(), (v1, v2) -> v1));
//            }
        }

        for (AmzVcPoOrderV amzVcPoOrderV : amzVcPoOrderItems) {

            // 存在采购订单的行
            if (Objects.equals(1, amzVcPoOrderV.getCheckStatus()) || asinApplyList.get(amzVcPoOrderV.getAsin() + "#" + amzVcPoOrderV.getOrderNo()) != null) {
                noSavaOrderList.add(amzVcPoOrderV);
                continue;
            }

            if (StringUtils.isEmpty(amzVcPoOrderV.getOrderNo())) {
                throw new CustomException("PO订单号不能为空");
            }
            if (StringUtils.isEmpty(amzVcPoOrderV.getAsin())) {
                throw new CustomException("ASIN不能为空");
            }

            if (StringUtils.isEmpty(amzVcPoOrderV.getAcknowledgementCode())) {
                throw new CustomException("接单状态不能为空");
            }

            //接受状态
            if ("Accepted".equals(amzVcPoOrderV.getAcknowledgementCode())) {
                if (StringUtils.isEmpty(amzVcPoOrderV.getAcceptedQuantity())) {
                    throw new CustomException("PO订单号" + amzVcPoOrderV.getOrderNo() + "接受状态下，接单数量不能为空");
                }
                if (amzVcPoOrderV.getAcceptedQuantity() == 0) {
                    throw new CustomException("PO订单号" + amzVcPoOrderV.getOrderNo() + "接受状态下，接单数量不能为0");
                }
            }
            //拒绝状态下, 接单数量摸默认设置为0
            if (amzVcPoOrderV.getAcknowledgementCode().contains("Rejected")) {
                amzVcPoOrderV.setAcceptedQuantity(0);
            }

            //延期交货，延期交货时间不能为空
            if (amzVcPoOrderV.getAcknowledgementCode().equals("Backordered") && amzVcPoOrderV.getPlanArriveTime() == null) {
                throw new CustomException("PO订单号" + amzVcPoOrderV.getOrderNo() + "延期交货状态下，延期交货时间不能为空");
            }

            amzVcPoOrderV.setAcceptedQuantity(null == amzVcPoOrderV.getAcceptedQuantity() ? 0 : amzVcPoOrderV.getAcceptedQuantity()); //接单数量
            //操作数据至临时表
            this.savePoOrderAcceptedQuanntiyTs(amzVcPoOrderV, orgId, scTicketOne.getShopId());

            //调整为处理中
            if ("NEW".equals(scTicketOne.getTicketStatus())) {
                scTicketService.setTikceStatusProcessing(ticketId);
            }
        }

        //返回不可保存订单行信息
        StringBuilder failureMsg = new StringBuilder();

        if (!CollectionUtils.isEmpty(noSavaOrderList)) {
            for (AmzVcPoOrderV amzVcPoOrderV : noSavaOrderList) {
                String msg = "<br/>" + "PO订单号：" + amzVcPoOrderV.getOrderNo() + "ASIN:" + amzVcPoOrderV.getAsin() + "已生成采购订单号,未进行修改";
                failureMsg.append(msg);
            }
        }
        return failureMsg.length() > 0 ? failureMsg.toString() : null;
    }


    /**
     * @param
     * @param amzVcPoOrderItem
     * @description: 保存接单数据到临时接单表
     * @author: Moore
     * @date: 2024/3/12 17:18
     * @return: void
     **/
    @Transactional
    public void savePoOrderAcceptedQuanntiyTs(AmzVcPoOrderV amzVcPoOrderItem,Long orgId,Long shopId) {
        //获取接单数据行信息
        if (amzVcPoOrderItem.getTsId() == null) {
            AmzVcPoOrderAcceptedTs amzVcPoOrderAcceptedTsSave = new AmzVcPoOrderAcceptedTs();
            amzVcPoOrderAcceptedTsSave.setShopId(shopId);//店铺ID
            amzVcPoOrderAcceptedTsSave.setOrderId(amzVcPoOrderItem.getId()); // 订单主键
            amzVcPoOrderAcceptedTsSave.setOrderItemId(amzVcPoOrderItem.getItemId());//订单明细表主键
            amzVcPoOrderAcceptedTsSave.setAcknowledgementCode(amzVcPoOrderItem.getAcknowledgementCode());//拒绝内容
            amzVcPoOrderAcceptedTsSave.setPurchaseOrderNumber(amzVcPoOrderItem.getOrderNo());//Po订单号
            amzVcPoOrderAcceptedTsSave.setAsin(amzVcPoOrderItem.getAsin());//ASIN
            amzVcPoOrderAcceptedTsSave.setOrganizationId(orgId);//组织ID
            amzVcPoOrderAcceptedTsSave.setAcceptedQuantity(amzVcPoOrderItem.getAcceptedQuantity()); //接单数量
            amzVcPoOrderAcceptedTsSave.setPlanArriveTime(amzVcPoOrderItem.getPlanArriveTime());//预计到货时间
            amzVcPoOrderAcceptedTsSave.settingDefaultCreate();
            amzVcPoOrderAcceptedTsService.save(amzVcPoOrderAcceptedTsSave);
        } else {
            AmzVcPoOrderAcceptedTs amzVcPoOrderAcceptedTsUpate = new AmzVcPoOrderAcceptedTs();
            amzVcPoOrderAcceptedTsUpate.setId(amzVcPoOrderItem.getTsId()); //更新的主键
            amzVcPoOrderAcceptedTsUpate.setAcknowledgementCode(amzVcPoOrderItem.getAcknowledgementCode());//拒绝内容
            amzVcPoOrderAcceptedTsUpate.setAcceptedQuantity(amzVcPoOrderItem.getAcceptedQuantity()); //接单数量
            amzVcPoOrderAcceptedTsUpate.setPlanArriveTime(amzVcPoOrderItem.getPlanArriveTime());//预计到货时间
            amzVcPoOrderAcceptedTsUpate.settingDefaultUpdate();
            amzVcPoOrderAcceptedTsService.updateById(amzVcPoOrderAcceptedTsUpate);
        }
    }


    /**
     * @description: 导入PO订单接单信息
     * @author: Moore
     * @date: 2024/3/22 11:15
     * @param
     * @param amzVcPoOrderV
     * @param contextId
     * @param purchaseReqNo
     * @return: java.lang.String
    **/
    @Override
    public String importVcPoOrderItemExcel(List<AmzVcPoOrderV> amzVcPoOrderV, Integer contextId,Long recordId,String purchaseReqNo) {
        return this.importPoOrderItem(amzVcPoOrderV, contextId, recordId, purchaseReqNo);
    }



    /**
     * @description: 导入
     * @author: Moore
     * @date: 2024/3/22 11:16
     * @param
     * @param amzVcPoOrderTimeRecordVS VO
     * @param contextId 组织ID
     * @param purchaseReqNo 采购申请号
     * @return: java.lang.String
    **/
    public String importPoOrderItem(List<AmzVcPoOrderV> amzVcPoOrderTimeRecordVS, Integer contextId,Long recordId, String purchaseReqNo) {
        if (CollectionUtils.isEmpty(amzVcPoOrderTimeRecordVS)) {
            throw new CustomException("导入商品信息不能为空！");
        }

        //接单数据
        List<AmzVcPoOrderV> amzVcPoOrderVS = amzVcPoOrderTimeRecordService.selectAmzVcPoOrderTimeRecordVById(recordId);
        if (CollectionUtils.isEmpty(amzVcPoOrderTimeRecordVS)){
            throw new CustomException("订单数据获取失败！");
        }

        Map<String, AmzVcPoOrderV> amzVcPoOrderListMap= amzVcPoOrderVS.stream().collect(Collectors.toMap(AmzVcPoOrderV::fetchGroupKey , Function.identity(), (v1, v2) -> v1));


        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (AmzVcPoOrderV amzVcPoOrderTimeRecordV : amzVcPoOrderTimeRecordVS) {
            try {
                if (StringUtils.isEmpty(amzVcPoOrderTimeRecordV.getOrderNo())) {
                    failureNum++;
                    failureMsg.append("<br/>" + "PO订单号未填写的行导入失败");
                    continue;
                }

                if (StringUtils.isEmpty(amzVcPoOrderTimeRecordV.getAsin())) {
                    failureNum++;
                    failureMsg.append("<br/>" + "ASIN未填写的行导入失败");
                    continue;
                }

                AmzVcPoOrderV amzVcPoOrderVItem = amzVcPoOrderListMap.get(amzVcPoOrderTimeRecordV.getAsin() + "#" + amzVcPoOrderTimeRecordV.getOrderNo());

                //订单对应asin不存在
                if (amzVcPoOrderVItem == null) {
                    failureNum++;
                    String msg = "<br/>" + "PO订单号：" + amzVcPoOrderTimeRecordV.getOrderNo() + "ASIN:" + amzVcPoOrderTimeRecordV.getAsin() + "系统中不存在";
                    failureMsg.append(msg);
                    continue;
                }

                //存在采购申请
                if (amzVcPoOrderVItem.getCheckStatus().equals(1)) {
                    failureNum++;
                    String msg = "<br/>" + "PO订单号：" + amzVcPoOrderTimeRecordV.getOrderNo() + "ASIN:" + amzVcPoOrderTimeRecordV.getAsin() + "已生成采购订单号,数据行未进行修改处理";
                    failureMsg.append(msg);
                    continue;
                }

                //接受数量不能大于下单数量
                if (amzVcPoOrderTimeRecordV.getQuantity() != null && amzVcPoOrderTimeRecordV.getAcceptedQuantity() != null && amzVcPoOrderTimeRecordV.getAcceptedQuantity() > amzVcPoOrderTimeRecordV.getQuantity()) {
                    String msg = "<br/>" + "PO订单号：" + amzVcPoOrderTimeRecordV.getOrderNo() + "ASIN:" + amzVcPoOrderTimeRecordV.getAsin() + "接受数量大于下单数量，未导入";
                    failureMsg.append(msg);
                    failureNum++;
                    continue;
                }

                AmzVcPoOrderV amzVcPoOrderV = new AmzVcPoOrderV();
                amzVcPoOrderV.setOrderNo(amzVcPoOrderTimeRecordV.getOrderNo()); //订单号
                amzVcPoOrderV.setAsin(amzVcPoOrderTimeRecordV.getAsin());//ASIN
                amzVcPoOrderV.setAcceptedQuantity(amzVcPoOrderTimeRecordV.getAcceptedQuantity()); //设置接受数量
                amzVcPoOrderV.setPlanArriveTime(amzVcPoOrderTimeRecordV.getPlanArriveTime());//延期交货时间
                //设置订单行信息
                amzVcPoOrderV.setId(amzVcPoOrderVItem.getId()); //订单主键
                amzVcPoOrderV.setItemId(amzVcPoOrderVItem.getItemId());//订单明细ID
                amzVcPoOrderV.setShopId(amzVcPoOrderVItem.getShopId());//店铺ID

                //接单状态
                if (!StringUtils.isEmpty(amzVcPoOrderTimeRecordV.getAcknowledgementCode())) {
                    String acknowledgementCode = amzVcPoOrderTimeRecordV.getAcknowledgementCode();
                    if ("接受".equals(acknowledgementCode)) {
                        amzVcPoOrderV.setAcknowledgementCode("Accepted");
                    } else if ("延期交货".equals(acknowledgementCode)) {
                        amzVcPoOrderV.setAcknowledgementCode("Backordered");
                    } else if ("拒绝 Temporarily Unavailable".equals(acknowledgementCode)) {
                        amzVcPoOrderV.setAcknowledgementCode("Rejected1");
                        amzVcPoOrderV.setAcceptedQuantity(0);
                    } else if ("拒绝 Invalid Product Identifier".equals(acknowledgementCode)) {
                        amzVcPoOrderV.setAcknowledgementCode("Rejected2");
                        amzVcPoOrderV.setAcceptedQuantity(0);
                    } else if ("拒绝 Obsolete Product".equals(acknowledgementCode)) {
                        amzVcPoOrderV.setAcceptedQuantity(0);
                        amzVcPoOrderV.setAcknowledgementCode("Rejected3");
                    }
                }
                //临时表更新接单数量
                this.importExcelTs(amzVcPoOrderV, contextId);
                //更新主表
                successNum++;
            } catch (Exception e) {
                if (e instanceof ParseException) {
                    failureNum++;
                    String msg = "<br/>" + "PO订单号：" + amzVcPoOrderTimeRecordV.getOrderNo() + "所在行导入失败：";
                    failureMsg.append(msg + "该行时间信息，格式化错误，请按照yyyy/MM/dd 格式填写");
                    log.error(msg, e);
                } else {
                    failureNum++;
                    String msg = "<br/>" + "PO订单号：" + amzVcPoOrderTimeRecordV.getOrderNo() + "所在行导入失败：";
                    failureMsg.append(msg + e.getMessage());
                    log.error(msg, e);
                }
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "共 " + successNum  + " 条数据导入成功， " + failureNum + " 条数据导入失败，错误如下：");
            return failureMsg.toString();
        } else {
            successMsg.insert(0, "数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }


    /**
     * @param
     * @param amzVcPoOrderV PO订单信息
     * @param contextId     组织ID
     * @description: 导入设置临时数据信息
     * @author: Moore
     * @date: 2024/3/15 10:34
     * @return: void
     **/
    @Transactional
    public void importExcelTs(AmzVcPoOrderV amzVcPoOrderV, Integer contextId) {
        //获取接单数据行信息（行信息只有一条）
        AmzVcPoOrderAcceptedTs amzVcPoOrderAcceptedTs = amzVcPoOrderAcceptedTsService.lambdaQuery()
                .eq(AmzVcPoOrderAcceptedTs::getOrderItemId, amzVcPoOrderV.getItemId()).one();
        if (amzVcPoOrderAcceptedTs == null) {  //新增
            AmzVcPoOrderAcceptedTs amzVcPoOrderAcceptedTsSave = new AmzVcPoOrderAcceptedTs();
            if (contextId != null) {
                amzVcPoOrderAcceptedTsSave.setOrganizationId(Long.valueOf(contextId)); //组织ID
            }
            amzVcPoOrderAcceptedTsSave.setShopId(amzVcPoOrderV.getShopId()); //店铺ID
            amzVcPoOrderAcceptedTsSave.setOrderId(amzVcPoOrderV.getId());//订单主键
            amzVcPoOrderAcceptedTsSave.setOrderItemId(amzVcPoOrderV.getItemId()); //订单明细主键

            amzVcPoOrderAcceptedTsSave.setPurchaseOrderNumber(amzVcPoOrderV.getOrderNo());//订单号
            amzVcPoOrderAcceptedTsSave.setAsin(amzVcPoOrderV.getAsin());//ASIN
            amzVcPoOrderAcceptedTsSave.setAcceptedQuantity(amzVcPoOrderV.getAcceptedQuantity());
            amzVcPoOrderAcceptedTsSave.setAcknowledgementCode(amzVcPoOrderV.getAcknowledgementCode());//接收类型
            amzVcPoOrderAcceptedTsSave.setPlanArriveTime(amzVcPoOrderV.getPlanArriveTime());
            amzVcPoOrderAcceptedTsSave.settingDefaultCreate();
            amzVcPoOrderAcceptedTsService.save(amzVcPoOrderAcceptedTsSave);
        } else {
            AmzVcPoOrderAcceptedTs amzVcPoOrderAcceptedTsUpate = new AmzVcPoOrderAcceptedTs();
            amzVcPoOrderAcceptedTsUpate.setId(amzVcPoOrderAcceptedTs.getId()); //更新的主键
            amzVcPoOrderAcceptedTsUpate.setAcceptedQuantity(amzVcPoOrderV.getAcceptedQuantity());//数量
            amzVcPoOrderAcceptedTsUpate.setAcknowledgementCode(amzVcPoOrderV.getAcknowledgementCode());//接收类型
            amzVcPoOrderAcceptedTsUpate.setPlanArriveTime(amzVcPoOrderV.getPlanArriveTime()); //交货时间
            amzVcPoOrderAcceptedTsUpate.settingDefaultUpdate();
            amzVcPoOrderAcceptedTsService.updateById(amzVcPoOrderAcceptedTsUpate);
        }
    }


    /**
     * @param
     * @param order
     * @description: DI确认工单创建
     * @author: Moore
     * @date: 2024/3/6 14:46
     * @return: void
     **/
    @Override
    @Transactional
    public void createAmzPoOrderTicket(SaleOrders order) {

        try {
            Thread.sleep(6000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }


        SaleOrders saleOrders = saleOrdersMapper.selectById(order.getOrderId());
        if (saleOrders == null) {
            log.error("DI工单未获取到订单号：{}",order.getOrderId());
            return;
        }
        log.info("DI工单获取到订单信息：{}", JSONObject.toJSONString(saleOrders));



        //订单状态
        if (order != null && order.getChannelId() != null && order.getPurchaseOrderState() != null && !StringUtils.isEmpty(order.getOrderNo())
                && order.getPstChannelCreated() != null
                && 3 == saleOrders.getPurchaseOrderState()) {

            //获取店铺ID
            Account accountOne = accountService.selectByAccountId(order.getChannelId().intValue());
            if (accountOne == null) {
                log.error("DI工单未获取到店铺：{}",order.getChannelId());
                return;
            }

            //判断记录表是否存在(同一店铺，同一渠道，同一天，且未确认的DI工单)
            AmzVcPoOrderTimeRecord amzVcPoOrderTimeRecord = new AmzVcPoOrderTimeRecord();
            amzVcPoOrderTimeRecord.setOrderDate(order.getPstChannelCreated()); //订单时间
            amzVcPoOrderTimeRecord.setShopId(order.getChannelId());//店铺
            amzVcPoOrderTimeRecord.setChannel(accountOne.getType());//渠道
            amzVcPoOrderTimeRecord.setConfirmAcceptedStatus("N"); //未发送接单信息
            AmzVcPoOrderTimeRecord amzVcPoOrderTimeRecordInfo = amzVcPoOrderTimeRecordService.selectAmzVcPoOrderTimeRecord(amzVcPoOrderTimeRecord);
            AmzVcPoOrderTimeRecordItem amzVcPoOrderTimeRecordItem = null;


            if (amzVcPoOrderTimeRecordInfo == null) {
                amzVcPoOrderTimeRecord.setOrganizationId(accountOne.getOrgId() != null ? accountOne.getOrgId().longValue() : null);
               //seller_party_id 是否为必要条件
                amzVcPoOrderTimeRecord.setSellingPartyId(saleOrders.getCustomerReferNo());//selling_party_id
                amzVcPoOrderTimeRecord.settingDefaultSystemCreate();
                amzVcPoOrderTimeRecordService.insertAmzVcPoOrderTimeRecord(amzVcPoOrderTimeRecord);

                //保存至记录明细表
                amzVcPoOrderTimeRecordItem = new AmzVcPoOrderTimeRecordItem();
                amzVcPoOrderTimeRecordItem.setRecordId(amzVcPoOrderTimeRecord.getId());//关联ID
                amzVcPoOrderTimeRecordItem.setOrderId(order.getOrderId()); //订单ID
                amzVcPoOrderTimeRecordItem.setPurchaseOrderNumber(order.getOrderNo()); //订单号
                amzVcPoOrderTimeRecordItem.setShopId(amzVcPoOrderTimeRecord.getShopId());//店铺
                amzVcPoOrderTimeRecordItem.setOrderDate(amzVcPoOrderTimeRecord.getOrderDate());//订单时间
                amzVcPoOrderTimeRecordItem.setOrganizationId(amzVcPoOrderTimeRecord.getOrganizationId()); //组织ID
                amzVcPoOrderTimeRecordItem.settingDefaultSystemCreate();
                amzVcPoOrderTimeRecordItemService.insertAmzVcPoOrderTimeRecordItem(amzVcPoOrderTimeRecordItem);

                //创建工单
                ScTicket scTicket = scTicketService.selectScTicketBySource(amzVcPoOrderTimeRecord.getId(), ScTicketConstant.TICKET_SOURCE_DOCUMENT_PO_ORDER_RECORD);
                if (null != scTicket) {
                    return;
                } else {
                    scTicket = new ScTicket();
                }
                String ticketName = "";
                String ticketType = "";
                Account account = accountService.selectByAccountId(order.getChannelId().intValue());
                if (AccountSaleChannelEnum.VCDI.getName().equals(accountOne.getType())) {
                    ticketName = cn.hutool.core.date.DateUtil.format(amzVcPoOrderTimeRecord.getOrderDate(), DatePattern.NORM_DATE_PATTERN) + " " + account == null ? "" : account.getTitle() + "（DI订单确认)";
                    ticketType = ScTicketConstant.TICKET_TYPE_PO_DI_ORDER;
                } else if (AccountSaleChannelEnum.VCUSPO.value().equals(accountOne.getType())) {
                    ticketName = cn.hutool.core.date.DateUtil.format(amzVcPoOrderTimeRecord.getOrderDate(), DatePattern.NORM_DATE_PATTERN) + " " + account == null ? "" : account.getTitle() + "（USPO订单确认)";
                    ticketType = ScTicketConstant.TICKET_TYPE_PO_USPO_ORDER;
                }
                scTicket.setAmazonOrderId(order.getOrderNo());
                scTicket.setTicketName(ticketName); //工单名称
                scTicket.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(amzVcPoOrderTimeRecord.getId().toString(), "l", 5, "0")+ RandomUtil.randomNumbers(4)); //工单编号
                scTicket.setTicketSource(accountOne.getType());// 工单来源（渠道）
                scTicket.setTicketType(ticketType); //工单类型
                scTicket.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH); //优先级（高）
                scTicket.setSourceId(amzVcPoOrderTimeRecord.getId());//来源ID
                scTicket.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_PO_ORDER_RECORD); //来源单据
                scTicket.setShopId(order.getChannelId());  //店铺ID
                scTicket.setOrderId(order.getOrderId()); //订单ID
                scTicket.setMatchOrderType(1);//系统订单
                scTicket.setRemark("店铺名称：" + account == null ? "" : account.getTitle() + " 订单日期：" + cn.hutool.core.date.DateUtil.format(amzVcPoOrderTimeRecord.getOrderDate(), DatePattern.NORM_DATE_PATTERN));
                scTicketService.insertScTicket(scTicket);

                //更新工单编号至至明细主表
                AmzVcPoOrderTimeRecord updateTicketnumberRecod = new AmzVcPoOrderTimeRecord();
                updateTicketnumberRecod.setId(amzVcPoOrderTimeRecord.getId());
                updateTicketnumberRecod.setTicketNumber(scTicket.getTicketNumber());
                updateTicketnumberRecod.setShopName(account != null ? account.getTitle() : null);
                amzVcPoOrderTimeRecordService.updateById(updateTicketnumberRecod);
            } else {
                //工单存在（关联到对应工单中）
                amzVcPoOrderTimeRecordItem = new AmzVcPoOrderTimeRecordItem();
                amzVcPoOrderTimeRecordItem.setPurchaseOrderNumber(order.getOrderNo()); //PO订单号
                amzVcPoOrderTimeRecordItem.setRecordId(amzVcPoOrderTimeRecordInfo.getId());//关联ID

                //判断是否重复
                List<AmzVcPoOrderTimeRecordItem> amzVcPoOrderTimeRecordItems = amzVcPoOrderTimeRecordItemService.selectAmzVcPoOrderTimeRecordItemList(amzVcPoOrderTimeRecordItem);
                if (CollectionUtils.isEmpty(amzVcPoOrderTimeRecordItems)) {
                    amzVcPoOrderTimeRecordItem.setOrderId(order.getOrderId()); //订单ID
                    amzVcPoOrderTimeRecordItem.setShopId(amzVcPoOrderTimeRecord.getShopId()); //店铺
                    amzVcPoOrderTimeRecordItem.setOrderDate(amzVcPoOrderTimeRecord.getOrderDate());//订单时间
                    amzVcPoOrderTimeRecordItem.setOrganizationId(accountOne.getOrgId() != null ? accountOne.getOrgId().longValue() : null); //组织ID
                    amzVcPoOrderTimeRecordItem.settingDefaultSystemCreate();
                    amzVcPoOrderTimeRecordItemService.insertAmzVcPoOrderTimeRecordItem(amzVcPoOrderTimeRecordItem);//新增至明细
                }

            }
        }
    }

}

