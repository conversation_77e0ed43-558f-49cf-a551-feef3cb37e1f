package com.bizark.op.service.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bizark.op.api.cons.ScTicketConstant;
import com.bizark.op.api.enm.ticket.EmailFolderEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.ticket.MerchantEmail;
import com.bizark.op.api.entity.op.ticket.ScTicket;
import com.bizark.op.api.entity.op.ticket.SyncEmailInfo;
import com.bizark.op.api.entity.op.ticket.TicketAttaches;
import com.bizark.op.api.service.ticket.ISyncEmailInfoService;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.AliyunOssClientUtil;
import com.bizark.op.common.util.DateUtil;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.common.util.StringUtils;
import com.sun.mail.imap.IMAPStore;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description: garph api 拉取邮件
 * @Author: Fountain
 * @Date: 2024/12/17
 */
@Slf4j
@Component
public class GraphEmailApi {


    @Autowired
    private ISyncEmailInfoService emailInfoService;

    public  List<Map<String, Object>> readEmail(MerchantEmail merchantEmail, String email, String emailPwd,
                                                      Date fromDateTime, Date toDateTime,String token) {
        List<Map<String, Object>> emails = new ArrayList<>();


        List<String> sents = Arrays.asList("SENT", "已发送", "发件箱","已发送邮件");
        List<String> deletes = Arrays.asList("DELETED", "DELETE", "已删除","已删除邮件");
        List<String> draftss = Arrays.asList("DRAFTS", "草稿夹", "草稿箱","草稿");

            //todo 获取邮箱对应的token
           try {
               if (StringUtils.isEmpty(token)) {
                   log.error("EMAIL:当前邮箱账号获取token异常：{}", email);
                   return null;
               }
               log.error("EMAIL:当前邮箱账号及token：{},{}", email, token);

               //todo 获取邮箱对应的邮件夹
               List<Map<String, String>> folders = getFolders(token);

               for (Map<String, String> folder : folders) {
                   //取map中的第一个元素 （这些的不拉取）
                   if (sents.contains(folder.keySet().iterator().next().toUpperCase()) || deletes.contains(folder.keySet().iterator().next().toUpperCase()) || draftss.contains(folder.keySet().iterator().next().toUpperCase())) {
                       continue;
                   }
                   //todo 获取邮件
                   getFolderAllEmails(folder.values().iterator().next(),folder.keySet().iterator().next(),token,fromDateTime,toDateTime,emails,null,email);
               }
           }catch (Exception e) {
                e.printStackTrace();
                log.error("EMAIL:当前邮箱账号拉取异常{},异常原因",email,e);
           }
        return emails;
    }


    /**
     * Description:
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/12/17
     */
    static List<Map<String,String>> getFolders(String token) {
        List<Map<String,String>> allFolders = new ArrayList<>();
        String FOLDERS = "https://graph.microsoft.com/v1.0/me/mailFolders";// 获取用户邮箱下面的邮件夹
        HttpRequest request = HttpUtil.createGet(FOLDERS);
        request.header("Authorization", token);
        log.info("调用graphApi获取邮件夹请求参数：{}", token);
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            log.error("调用graphApi获取邮件夹接口异常");
            throw new CustomException("调用graphApi获取邮件夹接口异常");
        }
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        if(jsonObject != null) {
            JSONArray jsonArray = jsonObject.getJSONArray("value");
            if(jsonArray != null && jsonArray.size() > 0) {
                for(Object obj: jsonArray) {
                    String displayName = ((JSONObject)obj).getString("displayName");
                    String id = ((JSONObject)obj).getString("id");
                    Map<String,String> map = new HashMap<>();
                    map.put(displayName,id);
                    allFolders.add(map);
                }
            }
        }
        return allFolders;
    }


    /**
     * Description: 获取该邮件下面的所有附件，包括邮件中图片也是附件
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/12/19
     */
    static Map<String,Object> getAttach(String token,String emailId ,List<String> cids,String contentStr) {
        Map<String,Object> resultMap = new HashMap<>();
        List<String> fileList = new ArrayList<>();
        List<String> contentImages = new ArrayList<>();
        String attach = "https://graph.microsoft.com/v1.0/me/messages/"+ emailId + "/attachments";// 获取该邮箱下面所有的附件
        HttpRequest request = HttpUtil.createGet(attach);
        request.header("Authorization", token);
        log.info("调用graphApi获取邮件附件请求参数：{}", token);
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            log.error("调用graphApi获取邮件附件接口异常");
            throw new CustomException("调用graphApi获取邮件附件接口异常");
        }
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        if(jsonObject != null) {
            JSONArray jsonArray = jsonObject.getJSONArray("value");
            for (Object obj : jsonArray) {
                String contentId = ((JSONObject) obj).getString("contentId");
                byte[] contentBytes = ((JSONObject) obj).getBytes("contentBytes");
                String name = ((JSONObject) obj).getString("name");
                boolean flag = false;
                for(String cid:cids) {
                    if(cid.equals(contentId)) { //说明是内容中的图片附件
                        flag = true;
                        //  上传文件
                        Random rand = new Random();
                        String randomUUID = System.currentTimeMillis() + "-" + rand.nextInt(10000) + ".";
                        String newFileName = randomUUID + name;
                        String filePath = AliyunOssClientUtil.uploadFile(newFileName, new ByteArrayInputStream(contentBytes), "erp/Email/");
                        // todo 替换掉里面的cid 为oss对象上传以后的url
                        contentStr = contentStr.replace("cid:" + contentId, filePath);
                        contentImages.add(filePath);
                    }
                }
                if(!flag) { //说明是附件
                    //  上传文件
                    Random rand = new Random();
                    String randomUUID = System.currentTimeMillis() + "-" + rand.nextInt(10000) + ".";
                    String newFileName = randomUUID + name;
                    String filePath = AliyunOssClientUtil.uploadFile(newFileName, new ByteArrayInputStream(contentBytes), "erp/Email/");
                    fileList.add(filePath);
                }
            }
        }
        resultMap.put("attachments",fileList);
        resultMap.put("content",contentStr);
        resultMap.put("contentImages",contentImages);
        return resultMap;
    }

    /**
     * Description: 获取邮件夹所有的邮件
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/12/17
     */
    void getFolderAllEmails(String folderId, String folderName, String token,Date fromDateTime, Date toDateTime, List<Map<String, Object>> emails,String nextLink,String email) {
        // 获得收件箱的邮件列表
        Map<String, Object> emailInfo;
        String emailsUrl = "https://graph.microsoft.com/v1.0/me/mailFolders/" + folderId + "/messages?$top=100&$orderby=receivedDateTime desc";// 获取用户邮箱下面的邮件夹
        //todo 拼接时间筛选范围时间为utc
        if(fromDateTime != null && toDateTime == null ) {
            // 将 Date 转换为 Instant
            Instant instant = fromDateTime.toInstant();

            // 将 Instant 转换为 OffsetDateTime 并设置为 UTC
            OffsetDateTime odt = instant.atOffset(ZoneOffset.UTC);

            // 使用预定义的 ISO_OFFSET_DATE_TIME 格式化器输出 ISO 8601 字符串
            String fromDateTimeString = odt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);
            String startTime =  "&$filter=receivedDateTime ge " + fromDateTimeString;
            emailsUrl = emailsUrl + startTime;
        }
        if(fromDateTime == null && toDateTime != null) {
            // 将 Date 转换为 Instant
            Instant instant = toDateTime.toInstant();

            // 将 Instant 转换为 OffsetDateTime 并设置为 UTC
            OffsetDateTime odt = instant.atOffset(ZoneOffset.UTC);

            // 使用预定义的 ISO_OFFSET_DATE_TIME 格式化器输出 ISO 8601 字符串
            String toDateTimeString = odt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);
            String endTime =  "&$filter=receivedDateTime le " + toDateTimeString;
            emailsUrl = emailsUrl + endTime;
        }

        if(fromDateTime != null && toDateTime != null) {
            // 将 Date 转换为 Instant
            Instant instant = fromDateTime.toInstant();

            // 将 Instant 转换为 OffsetDateTime 并设置为 UTC
            OffsetDateTime odt = instant.atOffset(ZoneOffset.UTC);

            // 使用预定义的 ISO_OFFSET_DATE_TIME 格式化器输出 ISO 8601 字符串
            String fromDateTimeString = odt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);
            String startTime =  "&$filter=receivedDateTime ge " + fromDateTimeString;

            // 将 Date 转换为 Instant
            Instant endInstant = toDateTime.toInstant();

            // 将 Instant 转换为 OffsetDateTime 并设置为 UTC
            OffsetDateTime endOdt = endInstant.atOffset(ZoneOffset.UTC);

            // 使用预定义的 ISO_OFFSET_DATE_TIME 格式化器输出 ISO 8601 字符串
            String toDateTimeString = endOdt.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);
            String endTime =  " and receivedDateTime le " + toDateTimeString;
            emailsUrl = emailsUrl + startTime + endTime;
        }

        HttpRequest request = null;
        if (StringUtils.isNotEmpty(nextLink)) {
            request = HttpUtil.createGet(nextLink);
        } else {
            request = HttpUtil.createGet(emailsUrl);
        }
        request.header("Authorization", token);
        log.info("调用graphApi获取邮件夹下面所有邮件请求参数：{}", folderId);
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            log.error("调用graphApi获取邮件夹下面所有邮件接口异常");
            throw new CustomException("调用graphApi获取邮件夹下面所有邮件接口异常");
        }
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        JSONArray jsonArray = jsonObject.getJSONArray("value");
        if (jsonArray != null && jsonArray.size() > 0) {
            for (Object obj : jsonArray) {
                QueryWrapper<SyncEmailInfo> queryWrapper = new QueryWrapper();
                String messageId = ((JSONObject) obj).getString("internetMessageId");
                queryWrapper.eq("email_id",messageId);
                List<SyncEmailInfo> syncEmailInfoList =  emailInfoService.list(queryWrapper);
                if(CollectionUtil.isNotEmpty(syncEmailInfoList)) {
                    continue;
                }
                emailInfo = new HashMap<>();
                emailInfo.put("folder", folderName);
                emailInfo.put("messageId", ((JSONObject) obj).getString("internetMessageId"));
                emailInfo.put("sentDate", ((JSONObject) obj).getDate("sentDateTime"));
                emailInfo.put("from", ((JSONObject) obj).getJSONObject("from").getJSONObject("emailAddress").getString("address"));
                emailInfo.put("sendName", ((JSONObject) obj).getJSONObject("from").getJSONObject("emailAddress").getString("name"));
                JSONArray toRecipients = ((JSONObject) obj).getJSONArray("toRecipients");
                if(toRecipients != null && toRecipients.size() > 0) {
                    emailInfo.put("to", ((JSONObject) toRecipients.get(0)).getJSONObject("emailAddress").getString("address"));
                } else {
                    emailInfo.put("to",email); //<EMAIL>
                }
                //json 串里看不出来是什么结构，暂时先不用，后面有再看在补充。
//                    emailInfo.put("cc",((JSONObject)obj).getString(""));
//                    emailInfo.put("bcc", ((JSONObject)obj).getString(""));
                emailInfo.put("subject", ((JSONObject) obj).getString("subject"));
                String content = ((JSONObject) obj).getJSONObject("body").getString("content");
                emailInfo.put("content",content);
                emailInfo.put("attachments", new ArrayList<>());
                emailInfo.put("contentImages", new ArrayList<>());
                //todo 处理邮件中图片 查找邮件中所有cid
                Pattern CID_PATTERN = Pattern.compile("src=\"cid:([^\"]+)\"");
                Matcher matcher = CID_PATTERN.matcher(content);
                List<String> cids = new ArrayList<>();
                while (matcher.find()) {
                    cids.add(matcher.group(1)); // 提取 cid 值，不包括 "cid:" 前缀
                }
                boolean hasAttachments = ((JSONObject) obj).getBooleanValue("hasAttachments");
                if (CollectionUtil.isNotEmpty(cids) || hasAttachments) {
                    //处理所有附件
                    Map<String, Object> resultMap = getAttach(token, ((JSONObject) obj).getString("id"), cids, content);
                    emailInfo.put("content", resultMap.get("content"));
                    emailInfo.put("attachments", resultMap.get("attachments"));
                    emailInfo.put("contentImages", resultMap.get("contentImages"));
                }
                emails.add(emailInfo);
            }
        }
        String newNextLink = jsonObject.getString("@odata.nextLink");

        if (StringUtils.isNotEmpty(newNextLink)) {
            getFolderAllEmails(folderId, folderName,token,null,null,emails,newNextLink,email);
        } else { //递归出口
            return;
        }
    }


    public static boolean sendEmail(String email, String emailPwd, List<String> toEmail, List<String> ccEmail,
                                    List<String> bccEmail, String subject, String content, String messageId,List<TicketAttaches> attachmentList,String token) {
        try {
            if (StringUtil.isEmpty(messageId)) {
                log.error("EMAIL:当前邮箱账号及token：{},{}", email, token);
                //todo
                String url = "https://graph.microsoft.com/v1.0/me/sendMail";
                HttpRequest request = HttpUtil.createPost(url);
                request.header("Authorization", token);
                log.info("调用graphApi发送邮件请求token：{}", token);
                request.contentType("application/json");
                JSONObject emailObject = new JSONObject();
                JSONObject message = new JSONObject();
                message.put("subject", subject);
                JSONObject body = new JSONObject();
                body.put("contentType", "HTML");
                body.put("content", content);
                message.put("body", body);

                JSONArray toRecipients = new JSONArray();
                JSONObject address = new JSONObject();
                JSONObject emailAddress = new JSONObject();
                emailAddress.put("address", toEmail.get(0));
                address.put("emailAddress", emailAddress);
                toRecipients.add(address);
                message.put("toRecipients", toRecipients);


                JSONArray attachments = new JSONArray();
                if(CollectionUtils.isNotEmpty(attachmentList)) {
                    for (TicketAttaches ticketAttaches : attachmentList) {
                        URL furl = new URL(ticketAttaches.getFileUrl());
                        URLConnection connection = furl.openConnection();
                        InputStream inputStream = connection.getInputStream();
                        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                        byte[] buffer = new byte[1024];
                        int length;
                        try {
                            while ((length = inputStream.read(buffer)) != -1) {
                                byteArrayOutputStream.write(buffer, 0, length);
                            }
                        } finally {
                            inputStream.close();
                        }
                        byte[] data = byteArrayOutputStream.toByteArray();
                        JSONObject attach = new JSONObject();
                        attach.put("@odata.type", "#microsoft.graph.fileAttachment");
                        attach.put("name", ticketAttaches.getFileName());
                        attach.put("contentType", connection.getContentType());
                        attach.put("contentBytes", data);
                        attachments.add(attach);
                    }
                    message.put("attachments", attachments);
                }
                emailObject.put("message", message);
                emailObject.put("saveToSentItems", "true");
                request.body(emailObject.toJSONString());
                log.error("调用graphApi发送邮件参数{}", emailObject.toJSONString());
                HttpResponse response = request.execute();
                if (!response.isOk()) {
                    log.error("调用graphApi发送邮件接口失败");
                    throw new CustomException("调用graphApi发送邮件件接口失败");
                }
            } else {
                String id = readEmailbyMessageId(messageId,token);
                if (StringUtil.isNotEmpty(id)) {
                    log.error("EMAIL:当前邮箱账号及token：{},{}", email, token);
                    //todo
                    String url = "https://graph.microsoft.com/v1.0/me/messages/" + id + "/reply";
                    HttpRequest request = HttpUtil.createPost(url);
                    request.header("Authorization", token);
                    log.info("调用graphApi发送邮件请求token：{}", token);
                    request.contentType("application/json");
                    JSONObject emailObject = new JSONObject();
                    JSONObject message = new JSONObject();
                    message.put("subject", subject);
                    JSONObject body = new JSONObject();
                    body.put("contentType", "HTML");
                    body.put("content", content);
                    message.put("body", body);

                    JSONArray toRecipients = new JSONArray();
                    JSONObject address = new JSONObject();
                    JSONObject emailAddress = new JSONObject();
                    emailAddress.put("address", toEmail.get(0));
                    address.put("emailAddress", emailAddress);
                    toRecipients.add(address);
                    message.put("toRecipients", toRecipients);
                    JSONArray attachments = new JSONArray();
                    if(CollectionUtils.isNotEmpty(attachmentList)) {
                        for (TicketAttaches ticketAttaches : attachmentList) {
                            URL furl = new URL(ticketAttaches.getFileUrl());
                            URLConnection connection = furl.openConnection();
                            InputStream inputStream = connection.getInputStream();
                            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                            byte[] buffer = new byte[1024];
                            int length;
                            try {
                                while ((length = inputStream.read(buffer)) != -1) {
                                    byteArrayOutputStream.write(buffer, 0, length);
                                }
                            } finally {
                                inputStream.close();
                            }
                            byte[] data = byteArrayOutputStream.toByteArray();
                            JSONObject attach = new JSONObject();
                            attach.put("@odata.type", "#microsoft.graph.fileAttachment");
                            attach.put("name", ticketAttaches.getFileName());
                            attach.put("contentType", connection.getContentType());
                            attach.put("contentBytes", data);
                            attachments.add(attach);
                        }
                        message.put("attachments", attachments);
                    }
                    emailObject.put("message", message);
                    emailObject.put("saveToSentItems", "true");
                    request.body(emailObject.toJSONString());
                    log.error("调用graphApi发送邮件参数{}", emailObject.toJSONString());
                    HttpResponse response = request.execute();
                    if (!response.isOk()) {
                        log.error("调用graphApi发送邮件接口失败");
                        throw new CustomException("调用graphApi发送邮件件接口失败");
                    }
                } else {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }




    /**
     * Description: 跟新已读标志
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/12/23
     */
    public static void readEmailByInternetMessageId(String token, String messageId) {
        OkHttpClient client = new OkHttpClient();
        MediaType mediaType = MediaType.get("application/json; charset=utf-8");
        String json = "{\"isRead\": true}";
        RequestBody body = RequestBody.create(mediaType, json);
        Request request = new Request.Builder()
                .url("https://graph.microsoft.com/v1.0/me/messages/" + messageId)
                .patch(body)
                .addHeader("Authorization", "Bearer " + token)
                .build();
        try {
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()){
                log.error("标记邮件为已读失败，{}" + messageId);
            } else{
                log.error("标记邮件为已读成功，{}" + messageId);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String readEmailbyMessageId(String messageId,String token) {
        String encodedUrlString = null;
        try {
            String emailsUrl = "https://graph.microsoft.com/v1.0/me/messages?$filter=internetMessageId";
            String query = " eq '" + messageId + "'";
            query = URLEncoder.encode(query, "UTF-8");
            encodedUrlString = emailsUrl + query;
        } catch (Exception e) {
            log.error("graphApi 获取邮件异常{}", e.getMessage());
            return "";
        }
        HttpRequest request = HttpUtil.createGet(encodedUrlString);
        request.header("Authorization", token);
        log.info("调用graphApi获取邮件请求url：{}", encodedUrlString);
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            log.error("调用graphApi获取邮件请求接口异常{}", messageId);
        }
        JSONObject jsonObject = JSONObject.parseObject(response.body());
        JSONArray jsonArray = jsonObject.getJSONArray("value");
        String id = null;
        if (jsonArray != null && jsonArray.size() > 0) {
            for (Object obj : jsonArray) {
                id = ((JSONObject) obj).getString("id");
            }
        }
        return id;
    }

}




