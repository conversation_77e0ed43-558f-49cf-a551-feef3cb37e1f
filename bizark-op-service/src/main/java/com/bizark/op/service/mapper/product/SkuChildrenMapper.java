package com.bizark.op.service.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bizark.op.api.entity.op.product.SkuChildren;
import com.bizark.op.api.vo.product.ProductLineInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SkuChildrenMapper extends BaseMapper<SkuChildren> {
    List<SkuChildren> selectByOrgIdAndParentSku(@Param("orgId") Integer orgId, @Param("parentSkus") List<String> parentSkus);

    List<ProductLineInfo> selectLineInfoByParentSkus(@Param("orgId") Integer orgId,@Param("parentSkus") List<String> parentSkus);

    List<SkuChildren> selectByOrgIdAndSkus(@Param("orgId") Integer orgId, @Param("erpSkus") List<String> erpSkus);
}
