package com.bizark.op.service.mapper.amazon.fba;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bizark.op.api.entity.op.amazon.fba.DTO.FbaFnskuPrintDTO;
import com.bizark.op.api.entity.op.amazon.fba.MarFbaCartonSkus;
import com.bizark.op.api.entity.op.amazon.fba.VO.MarFnSkuPrintVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Entity com.tospur.sa.tenant.domain.entity.MarFbaCartonSkus
 */
public interface MarFbaCartonSkusMapper extends BaseMapper<MarFbaCartonSkus> {


    /**  查询FNSKU打印信息
     *
     * @param
     * @return
     */
    List<MarFnSkuPrintVO.SellerSkuBean> selectFnSkuPrintInfoList( FbaFnskuPrintDTO fbaFnskuPrintDTO);
}




