package com.bizark.op.service.mapper.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bizark.op.api.entity.op.sale.TemuProductProperty;
import org.apache.ibatis.annotations.Param;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper
public interface TemuProductPropertyMapper extends BaseMapper<TemuProductProperty> {

    int deleteBySkcIds(@Param("skcIds") List<Long> skcIds);
}
