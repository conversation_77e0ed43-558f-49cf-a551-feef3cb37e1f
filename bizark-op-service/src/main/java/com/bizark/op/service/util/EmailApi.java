package com.bizark.op.service.util;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bizark.op.api.entity.op.ticket.MerchantEmail;
import com.bizark.op.api.entity.op.ticket.SyncEmailInfo;
import com.bizark.op.api.entity.op.ticket.TicketAttaches;
import com.bizark.op.api.service.ticket.ISyncEmailInfoService;
import com.bizark.op.common.util.AliyunOssClientUtil;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.common.util.StringUtils;
import com.sun.istack.ByteArrayDataSource;
import com.sun.mail.imap.IMAPStore;
import com.sun.mail.util.BASE64DecoderStream;
import com.sun.mail.util.MailSSLSocketFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.mail.util.MimeMessageParser;
import org.mvel2.ast.And;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.*;
import javax.mail.internet.*;
import javax.mail.search.*;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.security.GeneralSecurityException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 亚马逊API客户端授权配置
 */
@Slf4j
@Component
public class EmailApi {

    @Autowired
    private ISyncEmailInfoService emailInfoService;

    /**
     * 读取邮件(通过时间段筛选)
     *
     * @param email
     *
     *
     *
     *
     *
     * @param emailPwd
     * @param fromDateTime
     * @param toDateTime
     * @param dir
     * @return
     */
    public  List<Map<String, Object>> readEmail(MerchantEmail merchantEmail, String email, String emailPwd,
                                                      Date fromDateTime, Date toDateTime, String dir, String channelType) {
//        List<Map<String, Object>> emails = new ArrayList<>();
        AtomicReference<ArrayList<Map<String, Object>>> reference = new AtomicReference<>(new ArrayList<Map<String, Object>>());
        List<String> sents = Arrays.asList("SENT", "已发送", "发件箱","SENT MESSAGES");
        List<String> deletes = Arrays.asList("DELETED", "DELETE", "已删除","DELETED MESSAGES");
        List<String> draftss = Arrays.asList("DRAFTS", "草稿夹", "草稿箱");
        IMAPStore store = null;
        try {
            log.error("EMAIL:正在连接邮件服务器，获取session：{}", email);
            Session session = getImapSession(email);


            // 创建IMAP协议的Store对象
            store = (IMAPStore) session.getStore("imap");
            log.error("EMAIL:正在连接邮件服务器，当前邮箱账号：{}", email);

            // 连接邮件服务器
            store.connect(email, emailPwd);
            log.error("EMAIL:邮件服务器连接成功，当前邮箱账号：{}", email);
            if (email.toLowerCase().indexOf("@126.com") != -1 || email.toLowerCase().indexOf("@163.com") != -1) {
                //带上IMAP ID信息，由key和value组成，例如name，version，vendor，support-email等。
                HashMap IAM = new HashMap();
                IAM.put("name", "Aofis");
                IAM.put("version", "1.0.0");
                IAM.put("vendor", "AofisClient");
                IAM.put("support-email", "<EMAIL>");
                log.error("checkConnected,当前邮箱账号：{}", email);
                store.id(IAM);
            }
            log.error("EMAIL:--------------连接准备完毕------------");
            Folder[] allFolder = store.getDefaultFolder().list();
//            Folder labelName = store.getFolder("LabelName");
//            Folder inbox = store.getFolder("Inbox");
//            List<Folder> folderList = Arrays.asList(labelName, inbox);
            for (Folder folder : allFolder) {
                if(folder.getName().contains("Gmail")){
                    continue;
                }
                try {
                    folder.open(Folder.READ_ONLY);
                    if (sents.contains(folder.getName().toUpperCase()) || deletes.contains(folder.getName().toUpperCase()) || draftss.contains(folder.getName().toUpperCase())) {
                        continue;
                    }
                    handlerFolder(merchantEmail,email, fromDateTime, toDateTime, dir, reference.get(), folder,channelType);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("邮件读取异常:{},当前邮箱账号：{}，密码：{}", e.getMessage(), email, emailPwd);
                } finally {
                    try {
                        if (folder != null) {
                            folder.close(false);
                        }
                    } catch (Exception e) {
                        log.error("邮件读取异常:{},当前邮箱账号：{}，密码：{}", e.getMessage(), email, emailPwd);
                    }
                }
            }


//            Folder[] allFolder = {inbox};
//            log.error("获取邮件 Folder,当前邮箱账号：{}",email);
//            for (Folder folder : allFolder) {
//                try {
//                    handlerFolder(email, fromDateTime, toDateTime, dir, emails, folder);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                    log.error("邮件读取异常:{},当前邮箱账号：{}，密码：{}", e.getMessage(), email, emailPwd);
//                } finally {
//                    try {
//                        if (folder != null) {
//                            folder.close();
//                        }
//                    } catch (Exception e) {
//                        log.error("邮件读取异常:{},当前邮箱账号：{}，密码：{}", e.getMessage(), email, emailPwd);
//                    }
//                }
//            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("邮件读取异常:{},当前邮箱账号：{}，密码：{}", e.getMessage(), email, emailPwd);
        } finally {
            try {
                if (store != null) {
                    store.close();
                }
            } catch (Exception e) {
                log.error("邮件读取异常:{},当前邮箱账号：{}，密码：{}", e.getMessage(), email, emailPwd);
            }
        }
        return reference.get();
    }

    /**
     * 组装邮箱信息
     *
     * @param email
     * @param fromDateTime
     * @param toDateTime
     * @param dir
     * @param emails
     * @param folder
     */
    private  void handlerFolder(MerchantEmail merchantEmail,String email, Date fromDateTime, Date toDateTime, String dir, List<Map<String, Object>> emails, Folder folder,String channelType) throws Exception {
        // 以读写模式打开收件箱
//        folder.open(Folder.READ_ONLY);
//      if (sents.contains(folder.getName().toUpperCase()) || deletes.contains(folder.getName().toUpperCase()) || draftss.contains(folder.getName().toUpperCase())) {
//          continue;
//      }
        Message[] messages = null;
        if (email.toLowerCase().indexOf("@vivachair.com") != -1 || email.toLowerCase().indexOf("@colamychair.com") != -1) {
            int start = 1;
            if (folder.getMessageCount() > 50) {
                start = folder.getMessageCount() - 50;
            }
            messages = folder.getMessages(start, folder.getMessageCount());
        } else {
            //时间过滤
            SearchTerm searchTerm = null;
            SearchTerm comparisonTermGe = new SentDateTerm(ComparisonTerm.GE, fromDateTime);
            SearchTerm comparisonTermLe = new SentDateTerm(ComparisonTerm.LE, toDateTime);
            searchTerm = new AndTerm(comparisonTermGe, comparisonTermLe);
            if("wayfair".equals(channelType)) { //是不是wayfair类型的邮箱
                messages = folder.search(searchTerm);
            } else if ("walmart".equals(channelType)){ //判断是不是walmart渠道的邮箱
                messages = folder.search(searchTerm);
            } else {
                //判断是不是店铺邮箱 筛选AMZ_SITEMSG，AMZ_QA
                if("1".equals(merchantEmail.getEmailType()) && !email.contains("<EMAIL>")) {
                    FromStringTerm addressStringTerm1 = new FromStringTerm("@marketplace.amazon.com");
                    FromStringTerm addressStringTerm2 = new FromStringTerm("<EMAIL>");
                    FromStringTerm addressStringTerm3 = new FromStringTerm("<EMAIL>");
                    FromStringTerm addressStringTerm4 = new FromStringTerm("@amazon.com");
                    FromStringTerm addressStringTerm5 = new FromStringTerm("<EMAIL>");
                    SearchTerm[] terms = {addressStringTerm1,addressStringTerm2,addressStringTerm3,addressStringTerm4,addressStringTerm5};
                    SearchTerm orTerm = new OrTerm(terms);
                    SearchTerm finalSearchTerm = new AndTerm(searchTerm,orTerm);
                    messages = folder.search(finalSearchTerm);
                }else if("1".equals(merchantEmail.getEmailType()) && email.contains("<EMAIL>")) {
                    SearchTerm dateTerm = new AndTerm(
                            new ReceivedDateTerm(ComparisonTerm.GE, fromDateTime),
                            new ReceivedDateTerm(ComparisonTerm.LE, toDateTime)
                    );
                    messages = folder.search(dateTerm);
                } else {
                    messages = folder.search(searchTerm);
                }
            }
        }

        //开启预处理
        FetchProfile fp = new FetchProfile();
        fp.add(FetchProfile.Item.ENVELOPE);
        fp.add(FetchProfile.Item.FLAGS);
        fp.add(FetchProfile.Item.CONTENT_INFO);
        folder.fetch(messages, fp);

        //组装邮件列表
        log.error("EMAIL:组装邮箱列表，文件夹：{},Messages长度：{},当前邮箱账号：{}",folder.getName(), messages.length, email);
        if (Objects.nonNull(messages) && messages.length > 0) {
            long begin = System.currentTimeMillis();
            assembleEmail(merchantEmail,messages, dir, emails, fromDateTime, toDateTime,channelType);
            log.error("分装完成，总耗时：{}", System.currentTimeMillis() - begin);
            //此处并行流不可开启
//            int splitCount = messages.length <= 30 && messages.length >= 10 ? 4
//                    : messages.length > 30 && messages.length <= 50 ? 10
//                    : messages.length > 50 ? 15 : 1;
//            List<Message[]> msgs = splitArray(messages, splitCount);
//            CountDownLatch downLatch = new CountDownLatch(msgs.size());
//            msgs.parallelStream()
//                    .forEach(item->{
//                        try {
//                            assembleEmail(item, dir, emails, fromDateTime, toDateTime);
//                            downLatch.countDown();
//                        } catch (Exception e) {
//                            throw new RuntimeException(e);
//                        }
//                    });
//
//            downLatch.await();
//           assembleEmail(merchantEmail,messages, dir, emails, fromDateTime, toDateTime);
            log.error("分装完成，总耗时：{}", System.currentTimeMillis() - begin);
            log.error("EMAIL:邮箱列表数据组装完毕，当前邮箱账号：{}", email);
        }
    }


    public static <T> List<T[]> splitArray(T[] array, int count) {
        int length = array.length;
        int perCount = (length + count - 1) / count; // 计算每份的元素个数
        List<T[]> result = new ArrayList<>();
        for (int i = 0; i < length; i += perCount) {
            int endIndex = Math.min(i + perCount, length); // 计算每份的结束索引
            result.add(Arrays.copyOfRange(array, i, endIndex));
        }
        return result;
    }

    /**
     * 组装邮箱信息
     *
     * @param messages
     * @param dir
     * @param emails
     * @param fromDateTime
     * @param toDateTime
     * @throws Exception
     */
    private  void assembleEmail(MerchantEmail merchantEmail,Message[] messages, String dir, List<Map<String, Object>> emails,
                                      Date fromDateTime, Date toDateTime,String channelType) throws Exception {
        // 获得收件箱的邮件列表
        Map<String, Object> emailInfo;
        // 保存文件
//        List<Consumer> saveFilesList = new ArrayList<>();
        for (Message msg : messages) {
            //仅站外信二次过滤时间信息
//            if (msg.getSentDate().getTime() < fromDateTime.getTime() || msg.getSentDate().getTime() > toDateTime.getTime()) {
//                continue;
//            }

            long start = System.currentTimeMillis();
            MimeMessage mimeMsg = (MimeMessage) msg;
            QueryWrapper<SyncEmailInfo> queryWrapper = new QueryWrapper();
            String messageId = mimeMsg.getMessageID();
            queryWrapper.eq("email_id",messageId);
            List<SyncEmailInfo> syncEmailInfoList =  emailInfoService.list(queryWrapper);
            if(CollectionUtil.isNotEmpty(syncEmailInfoList)) {
                continue;
            }
            //todo 由于javaMail 不支持收件人模糊查询
            if("walmart".equals(channelType)) {
                String from = getFrom(mimeMsg);
                if(StringUtil.isEmpty(from) || !from.contains("@relay.walmart.com")) {
                    continue;
                }
                String title = "Message from Walmart.com Customer";
                String title2 = "Please contact the customer";
                String subject = mimeMsg.getSubject();
                if (StringUtils.isNotEmpty(subject)) {
                    if (!(subject.contains(title) || subject.contains(title2) || subject.equalsIgnoreCase("Re:"))) {
                        continue;
                    }
                } else {
                    continue;
                }

            }
            //todo 由于163邮箱不支持邮件主题模糊查询
            if("wayfair".equals(channelType)) {
                String subject = mimeMsg.getSubject();
                String regex = "Notification: PO# (.+) is Now Cancelled"; //订单取消
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(subject);
                if (StringUtil.isNotEmpty(subject) && (subject.contains("Action Required: Return Shipment Notification")    //退货工单
                        || matcher.find())) {
                } else {
                    continue;
                }
            }
            //StringBuffer content = new StringBuffer();
            //getMailTextContent(msg, content);
            //String contentStr = content.toString();

            MimeMessageParser parser = new MimeMessageParser(mimeMsg).parse();
            long end = System.currentTimeMillis();
            start = System.currentTimeMillis();
            String contentStr = parser.getHtmlContent();
            if (StringUtil.isEmpty(contentStr)) {
                contentStr = parser.getPlainContent();
                if (contentStr == null) {
                    contentStr = "";
                }
            }
            end = System.currentTimeMillis();
            Set<String> cids = (Set<String>) parser.getContentIds();

            List<String> attachments = new ArrayList<>();
            //todo 上传附件
            saveAttachmentByOss(msg,attachments);
            Iterator<String> it = cids.iterator();
            start = System.currentTimeMillis();
            //todo 这里上传邮件内容里面的图片
            List<String> contentImages = new ArrayList<>();
            while (it.hasNext()) {
                String cid = it.next();
                // contentStr判断是否含有cid,如果含有需要处理，如果不包含，不需要处理
                if (contentStr.contains(cid)) {
                    DataSource ds = parser.findAttachmentByCid(cid);
                    if (ds != null) {
                        //  上传文件
                        Random rand = new Random();
                        String randomUUID = System.currentTimeMillis() + "-" + rand.nextInt(10000) + ".";
                        String newFileName = randomUUID + ds.getName();
                        String filePath = AliyunOssClientUtil.uploadFile(newFileName,ds.getInputStream(),"erp/Email/");
                        // todo 替换掉里面的cid 为oss对象上传以后的url
                        contentStr = contentStr.replace("cid:" + cid, filePath);
                        contentImages.add(filePath);
                    }
                }
            }
            end = System.currentTimeMillis();
            log.info("EMAIL:第三步保存附件耗时：{},{}", mimeMsg.getMessageID(),end - start);
            start = System.currentTimeMillis();
            emailInfo = new HashMap<>();
            emailInfo.put("folder", mimeMsg.getFolder().getFullName());
            emailInfo.put("messageId", mimeMsg.getMessageID());
            emailInfo.put("sentDate", msg.getSentDate());
            emailInfo.put("from", getFrom(mimeMsg));
            emailInfo.put("to", getMailAddress(mimeMsg, "to"));
            emailInfo.put("cc", getMailAddress(mimeMsg, "cc"));
            emailInfo.put("bcc", getMailAddress(mimeMsg, "bcc"));
            emailInfo.put("subject", mimeMsg.getSubject() == null ? "" : mimeMsg.getSubject());
            emailInfo.put("content", contentStr);
            emailInfo.put("attachments",attachments);
            emailInfo.put("cids", cids);
            emailInfo.put("contentImages",contentImages);
            emails.add(emailInfo);
            end = System.currentTimeMillis();
            log.info("EMAIL:第四步封装耗时：{},{}", mimeMsg.getMessageID(),end - start);
//            end = System.currentTimeMillis();
//            log.info("EMAIL:封装邮件信息,执行完毕，耗时：{}", end - start);
        }
    }

    /**
     * 获取imap session
     *
     * @param email
     * @return
     */
    public static Session getImapSession(String email) {
        String imapHost = "outlook.office365.com";
        if (email.toLowerCase().indexOf("yahoo") != -1) {
            imapHost = "imap.mail.yahoo.com";
        } else if (email.toLowerCase().indexOf("aol.com") != -1) {
            imapHost = "imap.aol.com";
        } else if (email.toLowerCase().indexOf("gmail.com") != -1) {
            imapHost = "imap.gmail.com";
        } else if (email.toLowerCase().indexOf("@sina.com") != -1) {
            imapHost = "imap.sina.com";
        } else if (email.toLowerCase().indexOf("@vivachair.com") != -1) {
            imapHost = "imap.qiye.aliyun.com";
        } else if (email.toLowerCase().indexOf("@colamychair.com") != -1) {
            imapHost = "imap.qiye.aliyun.com";
        } else if (email.toLowerCase().indexOf("@126.com") != -1) {
            imapHost = "imap.126.com";
        } else if (email.toLowerCase().indexOf("@163.com") != -1) {
            imapHost = "imap.163.com";
        } else if (email.toLowerCase().indexOf("@vipclub.app") != -1) {
            imapHost = "imap.gmail.com";
        } else if (email.toLowerCase().indexOf("@smugdesk.net") != -1) {
            imapHost = "imap.gmail.com";
        } else if (email.toLowerCase().indexOf("@zjhjjjyxgs4.wecom.work") != -1) {  //微信企业邮箱
            imapHost = "imap.exmail.qq.com";
        }

        // 准备连接服务器的会话信息
        Properties props = new Properties();
        props.setProperty("mail.store.protocol", "imap");
        props.setProperty("mail.imap.host", imapHost);
        props.setProperty("mail.imap.port", "993");
        props.setProperty("mail.smtp.auth", "true");
        props.setProperty("mail.smtp.starttls.enable", "true");// outlook邮箱需要加上
        props.setProperty("mail.imap.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        props.setProperty("mail.imap.socketFactory.fallback", "false");
        props.setProperty("mail.imap.socketFactory.port", "993");
        props.setProperty("mail.imap.partialfetch", "false");
        props.setProperty("mail.imaps.partialfetch", "false");
        log.error("正在连接邮件服务器，当前Host：{}", imapHost);
        // 创建Session实例对象
        return Session.getInstance(props);
    }

    /**
     * 获取Smtp session
     *
     * @param email
     * @return
     */
    private static Session getSmtpSession(String email) throws GeneralSecurityException {
        String smtpHost = "smtp.office365.com";
        String popPort = "587";
        if (email.toLowerCase().indexOf("@yahoo.com") != -1) {
            smtpHost = "smtp.mail.yahoo.com";
        } else if (email.toLowerCase().indexOf("@aol.com") != -1) {
            smtpHost = "smtp.aol.com";
        } else if (email.toLowerCase().indexOf("@gmail.com") != -1) {
            smtpHost = "smtp.gmail.com";
            popPort = "465";
        } else if (email.toLowerCase().indexOf("@sina.com") != -1) {
            smtpHost = "smtp.sina.com";
        } else if (email.toLowerCase().indexOf("@vivachair.com") != -1) {
            smtpHost = "smtp.qiye.aliyun.com";
            popPort = "465";
        } else if (email.toLowerCase().indexOf("@colamychair.com") != -1) {
            smtpHost = "smtp.qiye.aliyun.com";
            popPort = "465";
        } else if (email.toLowerCase().indexOf("@126.com") != -1) {
            smtpHost = "smtp.126.com";
            popPort = "465";
        } else if (email.toLowerCase().indexOf("@163.com") != -1) {
            smtpHost = "smtp.163.com";
            popPort = "465";
        } else if (email.toLowerCase().indexOf("@vipclub.app") != -1) {
            smtpHost = "smtp.gmail.com";
            popPort = "465";
        } else if (email.toLowerCase().indexOf("@smugdesk.net") != -1) {
            smtpHost = "imap.gmail.com";
        }else if (email.toLowerCase().indexOf("@zjhjjjyxgs4.wecom.work") != -1) {  //微信企业邮箱
            smtpHost = "smtp.exmail.qq.com";
            popPort = "465";
        }

        // 准备连接服务器的会话信息
        Properties props = new Properties();
        props.setProperty("mail.smtp.host", smtpHost);
        props.setProperty("mail.store.protocol", "smtp");
        props.setProperty("mail.smtp.port", popPort);
        props.setProperty("mail.smtp.auth", "true");
        props.setProperty("mail.smtp.starttls.enable", "true");
        props.setProperty("mail.imap.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        props.setProperty("mail.smtp.socketFactory.port", popPort);
        props.setProperty("mail.smtp.socketFactory.fallback", "false");

        if(!"smtp.office365.com".equals(smtpHost)) {
            // 开启SSL加密，否则会失败
            MailSSLSocketFactory sf = new MailSSLSocketFactory();
            sf.setTrustAllHosts(true);
            props.put("mail.smtp.ssl.enable", "true");
            props.put("mail.smtp.ssl.socketFactory", sf);
        }

        return Session.getInstance(props);
    }

    /**
     * 发送邮件
     *
     * @param email
     * @param emailPwd
     * @param toEmail
     * @param ccEmail
     * @param bccEmail
     * @param subject
     * @param content
     */
    public static boolean sendEmail(String email, String emailPwd, List<String> toEmail, List<String> ccEmail,
                                    List<String> bccEmail, String subject, String content, List<TicketAttaches> attachments,String messageId) {
        Transport transport = null;
        try {
            // 1. 创建邮件对象
            Session smtpSession = getSmtpSession(email);
            MimeMessage message = new MimeMessage(smtpSession);

            // 2. From: 发件人
            message.setFrom(new InternetAddress(email));

            // 3. To: 收件人
            if (CollectionUtils.isEmpty(toEmail)) {
                return false;
            }
            for (String toItem : toEmail) {
                message.addRecipient(MimeMessage.RecipientType.TO, new InternetAddress(toItem));
            }
            //    Cc: 抄送（可选）
            if (!CollectionUtils.isEmpty(ccEmail) && ccEmail.size() > 0) {
                for (String ccItem : ccEmail) {
                    message.addRecipient(MimeMessage.RecipientType.CC, new InternetAddress(ccItem));
                }
            }
            //    Bcc: 密送（可选）
            if (!CollectionUtils.isEmpty(bccEmail) && bccEmail.size() > 0) {
                for (String bccItem : bccEmail) {
                    message.addRecipient(MimeMessage.RecipientType.BCC, new InternetAddress(bccItem));
                }
            }

            // 4. Subject: 邮件主题
            message.setSubject(subject, "UTF-8");

            // 5. 设置显示的发件时间
            message.setSentDate(new Date());

            // 6. Content: 邮件正文（可以使用html标签）
            if (CollectionUtils.isEmpty(attachments) || attachments.size() == 0) {
                message.setContent(content, "text/html;charset=UTF-8");
            } else {
                // 创建消息部分
                BodyPart messageBodyPart = new MimeBodyPart();
                messageBodyPart.setContent(content, "text/html;charset=UTF-8");

                // 创建多重消息
                Multipart multipart = new MimeMultipart();
                // 设置文本消息部分
                multipart.addBodyPart(messageBodyPart);

                //添加附件
                for (TicketAttaches ticketAttaches : attachments) {
                    // 附件部分
                    BodyPart attachmentPart = new MimeBodyPart();
                    attachmentPart.setDataHandler(urlToDataHandler(ticketAttaches.getFileUrl()));

                    //messageBodyPart.setFileName(filename);
                    //处理附件名称中文（附带文件路径）乱码问题
                    attachmentPart.setFileName(MimeUtility.encodeText(ticketAttaches.getFileName()));
                    multipart.addBodyPart(attachmentPart);
                }
                // 发送完整消息
                message.setContent(multipart);
            }

            // 7. 发送
            transport = smtpSession.getTransport("smtp");
            transport.connect(email, emailPwd);
            transport.sendMessage(message, message.getAllRecipients());
            if(StringUtil.isNotEmpty(messageId) && StringUtil.isNotEmpty(messageId)) { //跟新邮件的回复标识。
                //todo 异步执行跟新已回复标记。
                ExecutorService executorService = Executors.newSingleThreadExecutor();
                executorService.execute(() -> readAndWriteEmail(email,emailPwd,messageId));
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("发送邮件异常>>>", e.getMessage());
            return false;
        } finally {
            try {
                if (transport != null) {
                    transport.close();
                }
            } catch (Exception e) {
                log.error("发送邮件关闭异常>>>", e.getMessage());
            }
        }
        return true;
    }

    /**
     * @description:
     * @author: Moore
     * @date: 2024/5/16 0:27
     * @param
     * @param attachment
     * @return: javax.activation.DataHandler
     **/
    public  static  DataHandler urlToDataHandler(String attachment) throws Exception {
        URL url = new URL(attachment);
        URLConnection connection = url.openConnection();
        InputStream inputStream = connection.getInputStream();

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int length;

        try {
            while ((length = inputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, length);
            }
        } finally {
            inputStream.close();
        }
        byte[] data = byteArrayOutputStream.toByteArray();
        return new DataHandler(new ByteArrayDataSource(data, "application/octet-stream"));
    }

    /**
     * 获得发件人的地址和姓名
     */
    private static String getFrom(MimeMessage mimeMsg) throws Exception {
        InternetAddress[] address = (InternetAddress[]) mimeMsg.getFrom();
        if (address == null) {
            return "";
        }
        String from = address[0].getAddress();
        if (from == null)
            from = "";
        String personal = address[0].getPersonal();
        if (personal == null)
            personal = "";
        String fromaddr = personal + "<" + from + ">";
        return fromaddr;
    }

    /**
     * 获得邮件的收件人，抄送，和密送的地址和姓名，根据所传递的参数的不同 "to"----收件人 "cc"---抄送人地址 "bcc"---密送人地址
     */
    private static String getMailAddress(MimeMessage mimeMessage, String type) throws Exception {
        String mailaddr = "";
        String addtype = type.toUpperCase();
        InternetAddress[] address = null;
        if (addtype.equals("TO") || addtype.equals("CC")
                || addtype.equals("BCC")) {
            if (addtype.equals("TO")) {
                address = (InternetAddress[]) mimeMessage
                        .getRecipients(Message.RecipientType.TO);
            } else if (addtype.equals("CC")) {
                address = (InternetAddress[]) mimeMessage
                        .getRecipients(Message.RecipientType.CC);
            } else {
                address = (InternetAddress[]) mimeMessage
                        .getRecipients(Message.RecipientType.BCC);
            }
            if (address != null) {
                for (int i = 0; i < address.length; i++) {
                    String email = address[i].getAddress();
                    if (email == null)
                        email = "";
                    else {
                        email = MimeUtility.decodeText(email);
                    }
                    String personal = address[i].getPersonal();
                    if (personal == null)
                        personal = "";
                    else {
                        personal = MimeUtility.decodeText(personal);
                    }
                    String compositeto = personal + "<" + email + ">";
                    mailaddr += "," + compositeto;
                }
                if (StringUtil.isNotEmpty(mailaddr) && mailaddr.length() > 1) {
                    mailaddr = mailaddr.substring(1);
                }
            }
        } else {
            throw new Exception("Error emailaddr type!");
        }
        return mailaddr;
    }

//    /**
//     * 保存附件
//     *
//     * @param part    邮件中多个组合体中的其中一个组合体
//     * @param destDir 附件保存目录
//     * @throws UnsupportedEncodingException
//     * @throws MessagingException
//     * @throws FileNotFoundException
//     * @throws IOException
//     */
//    private static void saveAttachment(Part part, String destDir, List<String> fileList) throws Exception {
//        if (part.isMimeType("multipart/*")) {
//            Multipart multipart = (Multipart) part.getContent();    //复杂体邮件
//            //复杂体邮件包含多个邮件体
//            int partCount = multipart.getCount();
//            for (int i = 0; i < partCount; i++) {
//                //获得复杂体邮件中其中一个邮件体
//                BodyPart bodyPart = multipart.getBodyPart(i);
//                String fileName = decodeText(bodyPart.getFileName());
//
//                if (StringUtil.isNotEmpty(fileName)) {
//                    //某一个邮件体也有可能是由多个邮件体组成的复杂体
//                    String disp = bodyPart.getDisposition();
//                    if (StringUtil.isNotEmpty(disp) && (disp.equalsIgnoreCase(Part.ATTACHMENT) || disp.equalsIgnoreCase(Part.INLINE))) {
//                        InputStream is = bodyPart.getInputStream();
////                        saveFile(is, destDir, fileName);
//                        fileList.add(destDir + fileName);
//                    } else if (bodyPart.isMimeType("multipart/*")) {
//                        saveAttachment(bodyPart, destDir, fileList);
//                    } else {
//                        String contentType = bodyPart.getContentType();
//                        if (contentType.indexOf("name") != -1 || contentType.indexOf("application") != -1) {
////                            saveFile(bodyPart.getInputStream(), destDir, fileName);
//                            fileList.add(destDir + fileName);
//                        }
//                    }
//                }
//            }
//        } else if (part.isMimeType("message/rfc822")) {
//            saveAttachment((Part) part.getContent(), destDir, fileList);
//        }
//    }

    /**
     * 读取输入流中的数据保存至指定目录
     *
     * @param is       输入流
     * @param fileName 文件名
     * @param destDir  文件存储目录
     * @throws FileNotFoundException
     * @throws IOException
     */
    private static void saveFile(InputStream is, String destDir, String fileName) {
        BufferedInputStream bis = null;
        BufferedOutputStream bos = null;
        try {
            File file = new File(destDir);
            file.mkdirs();
            bis = new BufferedInputStream(is);
            bos = new BufferedOutputStream(
                    new FileOutputStream(new File(destDir + fileName)));
            int len = -1;
            while ((len = bis.read()) != -1) {
                bos.write(len);
                bos.flush();
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("邮件读取保存附件异常>>> {}", e.getMessage());
        } finally {
            try {
                if (bos != null) {
                    bos.close();
                }
                if (bis != null) {
                    bis.close();
                }
                if (is != null) {
                    is.close();
                }
            } catch (Exception e) {
                log.error("邮件读取保存附件关闭异常>>>", e.getMessage());
            }
        }
    }

    /**
     * 文本解码
     *
     * @param encodeText 解码MimeUtility.encodeText(String text)方法编码后的文本
     * @return 解码后的文本
     * @throws UnsupportedEncodingException
     */
    static String decodeText(String encodeText) throws UnsupportedEncodingException {
        if (encodeText == null || "".equals(encodeText)) {
            return "";
        } else {
            return MimeUtility.decodeText(encodeText);
        }
    }

    /**
     * 获得邮件文本内容
     *
     * @param part    邮件体
     * @param content 存储邮件文本内容的字符串
     * @throws MessagingException
     * @throws IOException
     */
    private static void getMailTextContent(Part part, StringBuffer content) throws MessagingException, IOException {
        //如果是文本类型的附件，通过getContent方法可以取到文本内容，但这不是我们需要的结果，所以在这里要做判断
        boolean isContainTextAttach = part.getContentType().indexOf("name") > 0;
        if (part.isMimeType("text/*") && !isContainTextAttach) {
            content.append(part.getContent().toString());
        } else if (part.isMimeType("message/rfc822")) {
            getMailTextContent((Part) part.getContent(), content);
        } else if (part.isMimeType("multipart/*")) {
            Multipart multipart = (Multipart) part.getContent();
            int partCount = multipart.getCount();
            for (int i = 0; i < partCount; i++) {
                BodyPart bodyPart = multipart.getBodyPart(i);
                if (bodyPart.getContentType().toLowerCase().indexOf("text/html") != -1) {
                    getMailTextContent(bodyPart, content);
                } else if (bodyPart.getContentType().toLowerCase().indexOf("multipart/alternative") != -1) {
                    getMailTextContent(bodyPart, content);
                }
            }
        }
    }


    /**
     * 上传附件
     *
     * @param multipartFile
     * @param dir
     */
    public String fileUpload(MultipartFile multipartFile, String dir) {
        //保存文件到本地
//        File filePath = new File(FileUploadUtils.getDefaultBaseDir() + "/" + "email/" + dir);
//        if (!filePath.exists()) {
//            filePath.mkdirs();
//        }
//        String originalFileName = multipartFile.getOriginalFilename();
//        File file = new File(filePath.getAbsolutePath() + "/" + originalFileName);
//        try {
//            multipartFile.transferTo(file);
//        } catch (Exception e) {
//        }
//        return "email/" + dir + "/" + originalFileName;
        return "/";
    }

    /**
     * Description: 邮件上传到oss 服务器
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2023/12/18
     */
    private static void saveAttachmentByOss(Part part, List<String> fileList) throws Exception {
        if (part.isMimeType("multipart/*")) {
            Multipart multipart = (Multipart) part.getContent();    //复杂体邮件
            //复杂体邮件包含多个邮件体
            int partCount = multipart.getCount();
            for (int i = 0; i < partCount; i++) {
                //获得复杂体邮件中其中一个邮件体
                BodyPart bodyPart = multipart.getBodyPart(i);
                String fileName = decodeText(bodyPart.getFileName());
                if (StringUtil.isNotEmpty(fileName)) {
                    //某一个邮件体也有可能是由多个邮件体组成的复杂体
                    String disp = bodyPart.getDisposition();
                    if (StringUtil.isNotEmpty(disp) && (disp.equalsIgnoreCase(Part.ATTACHMENT) || disp.equalsIgnoreCase(Part.INLINE))) {
                        //todo 上传到oss
                        InputStream is = bodyPart.getInputStream();
                        if(is instanceof BASE64DecoderStream) {//64位的decoder流，oss 转换不了
                            BASE64DecoderStream innerContentStream = (BASE64DecoderStream)is;
                            final byte[] actualContent = IOUtils.toByteArray(innerContentStream);
                            is = new ByteArrayInputStream(actualContent);
                        }
                        Random rand = new Random();
                        String randomUUID = System.currentTimeMillis() + "-" + rand.nextInt(10000) + ".";
                        String newFileName = randomUUID + fileName;
                        String filePath = AliyunOssClientUtil.uploadFile(newFileName, is, "erp/Email/");
                        fileList.add(filePath);
                    } else if (bodyPart.isMimeType("multipart/*")) {//递归解析
                        saveAttachmentByOss(bodyPart, fileList);
                    } else {
                        String contentType = bodyPart.getContentType();
                        if (contentType.indexOf("name") != -1 || contentType.indexOf("application") != -1) {
                            //todo 上传到oss
                            InputStream is = bodyPart.getInputStream();
                            if(is instanceof BASE64DecoderStream) {//是64位的decoder流
                                BASE64DecoderStream innerContentStream = (BASE64DecoderStream)is;
                                final byte[] actualContent = IOUtils.toByteArray(innerContentStream);
                                is = new ByteArrayInputStream(actualContent);
                            }
                            Random rand = new Random();
                            String randomUUID = System.currentTimeMillis() + "-" + rand.nextInt(10000) + ".";
                            String newFileName = randomUUID + fileName;
                            String filePath = AliyunOssClientUtil.uploadFile(newFileName, is, "erp/Email/");
                            fileList.add(filePath);
                        }
                    }
                }
            }
        } else if (part.isMimeType("message/rfc822")) {
            saveAttachmentByOss((Part) part.getContent(),fileList);
        }
    }


    /**
     * Description: 读取并修改该邮件为回复状态
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/7/24
     */
    public static void readAndWriteEmail(String email, String emailPwd,String messageId) {
        log.error("EMAIL:修改邮件标识正在连接邮件服务器，获取messageId：{}", messageId);
        List<String> sents = Arrays.asList("SENT", "已发送", "发件箱","SENT MESSAGES");
        List<String> deletes = Arrays.asList("DELETED", "DELETE", "已删除","DELETED MESSAGES");
        List<String> draftss = Arrays.asList("DRAFTS", "草稿夹", "草稿箱");
        IMAPStore store = null;
        try {
            log.error("EMAIL:修改邮件标识正在连接邮件服务器，获取session：{}", email);
            Session session = getImapSession(email);
            // 创建IMAP协议的Store对象
            store = (IMAPStore) session.getStore("imap");
            log.error("EMAIL:修改邮件标识正在连接邮件服务器，当前邮箱账号：{}", email);
            // 连接邮件服务器
            store.connect(email, emailPwd);
            log.error("EMAIL:修改邮件标识邮件服务器连接成功，当前邮箱账号：{}", email);
            if (email.toLowerCase().indexOf("@126.com") != -1 || email.toLowerCase().indexOf("@163.com") != -1) {
                //带上IMAP ID信息，由key和value组成，例如name，version，vendor，support-email等。
                HashMap IAM = new HashMap();
                IAM.put("name", "Aofis");
                IAM.put("version", "1.0.0");
                IAM.put("vendor", "AofisClient");
                IAM.put("support-email", "<EMAIL>");
                log.error("checkConnected,当前邮箱账号：{}", email);
                store.id(IAM);
            }
            log.error("EMAIL:--------------修改邮件标识连接准备完毕------------");
            Folder[] allFolder = store.getDefaultFolder().list();
            for (Folder folder : allFolder) {
                if(folder.getName().contains("Gmail")){
                    continue;
                }

                try {
                    folder.open(Folder.READ_WRITE);
                    if (sents.contains(folder.getName().toUpperCase()) || deletes.contains(folder.getName().toUpperCase()) || draftss.contains(folder.getName().toUpperCase())) {
                        continue;
                    }
                    Message[] messages = null;
                    if (email.toLowerCase().indexOf("@vivachair.com") != -1 || email.toLowerCase().indexOf("@colamychair.com") != -1) {
                        int start = 1;
                        if (folder.getMessageCount() > 50) {
                            start = folder.getMessageCount() - 50;
                        }
                        messages = folder.getMessages(start, folder.getMessageCount());
                    } else {
                        SearchTerm searchTerm = new SearchTerm() {
                            @Override
                            public boolean match(Message message) {
                                try {
                                    String msgID = message.getHeader("Message-ID")[0];
                                    return msgID != null && msgID.equals(messageId);
                                } catch (MessagingException e) {
                                    e.printStackTrace();
                                    return false;
                                }
                            }
                        };
                        messages = folder.search(searchTerm);
                    }
                    //开启预处理
                    FetchProfile fp = new FetchProfile();
                    fp.add(FetchProfile.Item.ENVELOPE);
                    fp.add(FetchProfile.Item.FLAGS);
                    fp.add(FetchProfile.Item.CONTENT_INFO);
                    folder.fetch(messages, fp);
                    for(Message m : messages) {
                        MimeMessage mimeMsg = (MimeMessage) m;
                        Flags flags = new Flags();
                        flags.add(Flags.Flag.SEEN);
                        if("<EMAIL>".equals(email)) {
                            flags.add(Flags.Flag.ANSWERED);
                        }
                        mimeMsg.setFlags(flags, true);
                        log.error("修改邮件标识成功:{},当前邮箱账号：{},messageId：{}", email, messageId);
                        break;
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("修改邮件标识邮件读取异常:{},当前邮箱账号：{}，messageId：{}", e.getMessage(), email, messageId);
                } finally {
                    try {
                        if (folder != null) {
                            folder.close(false);
                        }
                    } catch (Exception e) {
                        log.error("修改邮件标识邮件读取异常:{},当前邮箱账号：{}，messageId：{}", e.getMessage(), email, messageId);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("修改邮件标识邮件读取异常:{},当前邮箱账号：{}，messageId：{}", e.getMessage(), email, messageId);
        } finally {
            try {
                if (store != null) {
                    store.close();
                }
            } catch (Exception e) {
                log.error("修改邮件标识邮件读取异常:{},当前邮箱账号：{}，messageId：{}", e.getMessage(), email, messageId);
            }
        }
    }

}
