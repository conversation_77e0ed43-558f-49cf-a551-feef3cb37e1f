package com.bizark.op.service.service.ticket;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.op.api.entity.op.ticket.EmailFolderCount;
import com.bizark.op.api.entity.op.ticket.MerchantEmail;
import com.bizark.op.api.entity.op.ticket.SyncEmailInfo;
import com.bizark.op.api.service.ticket.IMerchantEmailService;
import com.bizark.op.api.service.ticket.ISyncEmailInfoService;
import com.bizark.op.common.util.spring.SpringUtils;
import com.bizark.op.service.mapper.ticket.SyncEmailInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 邮件信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-12
 */
@Service
public class SyncEmailInfoServiceImpl extends ServiceImpl<SyncEmailInfoMapper, SyncEmailInfo> implements ISyncEmailInfoService {

    @Autowired
    private SyncEmailInfoMapper emailInfoMapper;


    /**
     * 查询邮箱信息
     *
     * @param infoId 邮箱信息ID
     * @return 邮箱信息
     */
    @Override
    public SyncEmailInfo selectEmailInfoById(Long infoId) {
        return emailInfoMapper.selectEmailInfoById(infoId);
    }

  /*  @Override
    public List<SyncEmailInfo> selectEmailInfoList(SyncEmailInfo emailInfo) {
        return emailInfoMapper.selectEmailInfoList(emailInfo);
    }*/

    /**
     * 新增邮箱信息
     *
     * @param emailInfo 邮箱信息
     * @return 结果
     */
    @Override
    public int insertEmailInfo(SyncEmailInfo emailInfo) {
        return emailInfoMapper.insertEmailInfo(emailInfo);
    }

    /**
     * 修改邮箱信息
     *
     * @param emailInfo 邮箱信息
     * @return 结果
     */
    @Override
    public int updateEmailInfo(SyncEmailInfo emailInfo) {
        return emailInfoMapper.updateEmailInfo(emailInfo);
    }
//
//    /**
//     * 修改邮箱信息
//     *
//     * @param emailInfo 邮箱信息
//     * @return 结果
//     */
//    @Override
//    public int updateEmailInfo(SyncEmailInfo emailInfo) {
//        return emailInfoMapper.updateEmailInfo(emailInfo);
//    }
//
//    /**
//     * 批量删除邮箱信息
//     *
//     * @param infoIds 需要删除的邮箱信息ID
//     * @return 结果
//     */
//    @Override
//    public int deleteEmailInfoByIds(Long[] infoIds) {
//        return emailInfoMapper.deleteEmailInfoByIds(infoIds);
//    }
//
//    /**
//     * 删除邮箱信息信息
//     *
//     * @param infoId 邮箱信息ID
//     * @return 结果
//     */
//    @Override
//    public int deleteEmailInfoById(Long infoId) {
//        return emailInfoMapper.deleteEmailInfoById(infoId);
//    }
//
//    @Override
//    public List<EmailFolderCount> selectEmailFolderCountByEmailId(Long merchantEmailId) {
//        return emailInfoMapper.selectEmailFolderCountByEmailId(merchantEmailId);
//    }

    @Override
    public void syncEmailInfo() {
        IMerchantEmailService emailService = SpringUtils.getBean(IMerchantEmailService.class);
        emailService.syncEmailInfo(new MerchantEmail());
    }


}
