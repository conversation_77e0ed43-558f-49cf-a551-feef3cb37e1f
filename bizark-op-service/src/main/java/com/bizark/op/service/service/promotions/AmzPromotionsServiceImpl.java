package com.bizark.op.service.service.promotions;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.activiti.api.dto.cativiti.DeployStartDTO;
import com.bizark.activiti.api.service.activiti.ProcessStartService;
import com.bizark.common.contract.AuthUserDetails;
import com.bizark.op.api.cons.AmzPromotionsCouponConstants;
import com.bizark.op.api.cons.MQDefine;
import com.bizark.op.api.enm.promotions.*;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.promotions.*;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.entity.op.seller.AmzDiscount;
import com.bizark.op.api.entity.op.seller.AmzDiscountDetailEntity;
import com.bizark.op.api.exception.ErpCommonException;
import com.bizark.op.api.request.AmzPromotionsCreateRequest;
import com.bizark.op.api.request.AmzPromotionsQueryRequest;
import com.bizark.op.api.request.AmzPromotionsShopQueryRequest;
import com.bizark.op.api.request.msg.send.AmzPromotionsSendMsg;
import com.bizark.op.api.request.msg.send.AmzPromotionsSkuSendMsg;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.promotions.*;
import com.bizark.op.api.vo.promotions.AmzPromotionApprovalDetailVo;
import com.bizark.op.api.vo.promotions.AmzPromotionsDetailVO;
import com.bizark.op.api.vo.promotions.AmzPromotionsListVO;
import com.bizark.op.api.vo.promotions.AmzPromotionsSkuListVo;
import com.bizark.op.common.core.domain.BaseEntity;
import com.bizark.op.common.util.*;
import com.bizark.op.common.util.spring.SpringUtils;
import com.bizark.op.service.annotation.AmzPromotionVerify;
import com.bizark.op.service.mapper.account.AccountMapper;
import com.bizark.op.service.mapper.promotions.*;
import com.bizark.op.service.mapper.seller.AmzDiscountMapper;
import com.bizark.op.service.mapper.sale.ProductChannelsMapper;
import com.bizark.framework.web.view.ApiResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;


/**
 * @remarks: 促销管理service业务层处理--促销详情
 * @Author: Ailill
 * @date 2023-05-12 15:06
 */
@Service
@Slf4j
public class AmzPromotionsServiceImpl extends ServiceImpl<AmzPromotionsMapper, AmzPromotions> implements IAmzPromotionsService {
    @Autowired
    private AmzPromotionsMapper amzPromotionsMapper;

    @Autowired
    private IAmzPromotionsSkuDetailService iAmzPromotionsSkuDetailService;

    @Autowired
    private IAmzPromotionsDailyDetailService iAmzPromotionsDailyDetailService;

    @Autowired
    private IAmzPromotionsOperateLogService iAmzPromotionsOperateLogService;

    @Autowired
    private AmzPromotionsSkuDetailMapper amzPromotionsSkuDetailMapper;

    @Autowired
    private AccountMapper accountMapper;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private AmzDiscountMapper amzDiscountMapper;
    @Autowired
    private AccountService accountService;

    @Autowired
    private AmzPromotionsHisMapper amzPromotionsHisMapper;

    @Autowired
    private IAmzPromotionsHisService iAmzPromotionsHisService;

    @Autowired
    private IAmzPromotionAsyncService iAmzPromotionAsyncService;

    @Autowired
    private ProductChannelsMapper productChannelsMapper;

    @Autowired
    private IAmzPromotionApprovalAdviceService iAmzPromotionApprovalAdviceService;

    @Autowired
    private AmzPromotionCheckMapper amzPromotionCheckMapper;

    @Autowired
    private IAmzPromotionAccountService amzPromotionAccountService;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private AmzPromotionDateSetService amzPromotionDateSetService;
    @Autowired
    private AmzPromotionAccountMapper amzPromotionAccountMapper;

    @Autowired(required = false)
    private ProcessStartService processStartService;

    @Autowired
    @Lazy
    private IAmzPromotionsApprovalService iAmzPromotionsApprovalService;

    @Autowired
    private AmzPromotionsDetailExtraPriceService amzPromotionsDetailExtraPriceService;

    private static Map<String, String> shopMapBillingContact = new HashMap<>();

    static {
        shopMapBillingContact.put("OFC-VC-DF-US", "<EMAIL>");
        shopMapBillingContact.put("HJ-2-VC-DF-US", "<EMAIL>");
        shopMapBillingContact.put("HKHJ-VC-DF-US", "<EMAIL>");
        shopMapBillingContact.put("HJ-3-VC-DF-US", "<EMAIL>");
        shopMapBillingContact.put("HJ-1-VC-DF-US", "<EMAIL>");
        shopMapBillingContact.put("HJ-1-VC-VC-DF-US", "<EMAIL>");
        shopMapBillingContact.put("SW-DE-VC-VC-DF-DE", "<EMAIL>");
        shopMapBillingContact.put("SW-JP-VC-DF-VC-DF-JP", "<EMAIL>");
    }


    /**
     * 查询促销管理详情
     *
     * @param id 促销管理ID
     * @return 促销管理
     */
    @Override
    public AmzPromotionsDetailVO selectAmzPromotionsById(Long id) {


        AmzPromotionsDetailVO amzPromotionsDetailVO = new AmzPromotionsDetailVO();

        //1.促销详情基本信息封装(前台包括基本信息和数据表现)
        AmzPromotions amzPromotions = amzPromotionsMapper.selectById(id);
        BeanUtils.copyProperties(amzPromotions, amzPromotionsDetailVO);
        String promotionsTime = twoDateToString(amzPromotions.getBeginTime(), amzPromotions.getEndTime());
        //设置字符串格式 促销时间,促销类型，促销状态,是否分享,订购方式
        amzPromotionsDetailVO.setPromotionsTime(promotionsTime);
        amzPromotionsDetailVO.setPromotionsTypeName(amzPromotions.getPromotionsType() != null ? AmzPromotionsTypeEnum.labelOf(amzPromotions.getPromotionsType()) : null);
        amzPromotionsDetailVO.setPromotionsStateName(amzPromotions.getPromotionsState() != null ? AmzPromotionsStateEnum.labelOf(amzPromotions.getPromotionsState()) : null);
        amzPromotionsDetailVO.setShareString(amzPromotions.getShare() != null ? AmzPromotionsShareEnum.labelOf(amzPromotions.getShare()) : null);
        amzPromotionsDetailVO.setRedemptionsString(amzPromotions.getRedemptions() != null ? AmzPromotionsRedemptionsEnum.labelOf(amzPromotions.getRedemptions()) : null);

        //2.促销详情商品信息封装

        //根据promotionsId去查促销商品详情
        List<AmzPromotionsSkuDetail> amzPromotionsSkuDetailList = iAmzPromotionsSkuDetailService.list(Wrappers.lambdaQuery(AmzPromotionsSkuDetail.class).eq(AmzPromotionsSkuDetail::getPromotionsId, amzPromotions.getId()));
        if (CollectionUtil.isNotEmpty(amzPromotionsSkuDetailList)) {
            if (amzPromotionsDetailVO.getShopId() != null) {
                AccountWithCountry accountWithCountry = amzPromotionsMapper.selectAccountWithCountry(amzPromotionsDetailVO.getShopId());
                for (AmzPromotionsSkuDetail ast : amzPromotionsSkuDetailList) {
                    ast.setShopId(amzPromotionsDetailVO.getShopId());
                    ast.setCountry(accountWithCountry.getNameCn());
                }
            }
            amzPromotionsDetailVO.setSkuDetailList(amzPromotionsSkuDetailList);
            List<AmzPromotionsSkuDetail> heroProduct = amzPromotionsSkuDetailList.stream().filter(s -> s.getHeroProduct() != null && s.getHeroProduct()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(heroProduct)) {
                amzPromotionsDetailVO.setHeroProduct(heroProduct.get(0).getAsin());
            }
            //3.促销详情每日明细封装

            //通过促销详情初始进入每日明细页面默认查询该促销下昨天所有促销商品的明细
            List<AmzPromotionsDailyDetail> collect = amzPromotionsSkuDetailList.stream().map(skuDetail -> {
                String asin = skuDetail.getAsin();
                Date currentDate = DateUtils.parseDate(DateUtils.dateTimeNow(DateUtils.DEFAULT_DATE_FORMAT));
                Date date = DateUtils.nDaysAgo(1, currentDate);
                List<AmzPromotionsDailyDetail> amzPromotionsDailyDetailList = iAmzPromotionsDailyDetailService.selectAmzPromotionsDailyDetailList(id, date, date, new String[]{asin});
                if (CollectionUtil.isNotEmpty(amzPromotionsDailyDetailList)) {
                    AmzPromotionsDailyDetail dailyDetail = amzPromotionsDailyDetailList.get(0);
                    return dailyDetail;
                }
                return null;
            }).filter(s -> s != null).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(collect)) {
                amzPromotionsDetailVO.setDailyDetailList(collect);
            }

            //销量，浏览量，花费，销售额
            long unitSold = amzPromotionsSkuDetailList.stream().filter(r -> r.getUnitsSold() != null).mapToLong(AmzPromotionsSkuDetail::getUnitsSold).sum();
            amzPromotionsDetailVO.setUnitsSold(unitSold);
            BigDecimal amountSpent = amzPromotionsSkuDetailList.stream().filter(r -> r.getAmountSpent() != null).map(AmzPromotionsSkuDetail::getAmountSpent).reduce(BigDecimal.ZERO, BigDecimal::add);
            amzPromotionsDetailVO.setAmountSpent(amountSpent);
            BigDecimal revenue = amzPromotionsSkuDetailList.stream().filter(r -> r.getRevenue() != null).map(AmzPromotionsSkuDetail::getRevenue).reduce(BigDecimal.ZERO, BigDecimal::add);
            amzPromotionsDetailVO.setRevenue(revenue);
        }

        return amzPromotionsDetailVO;
    }

    @Override
    public AmzPromotionApprovalDetailVo selectAmzApprovalDetailPromotionsById(Long id) {
        return this.baseMapper.selectAmzApprovalDetailPromotionsById(id);
    }

    /**
     * 指定日期转换成字符串 例如 2023-04-01 12:00:00~2023-04-30 23:59:59
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    private String twoDateToString(Date beginTime, Date endTime) {
        String beginTimeStr = "";
        if (beginTime != null) {
            beginTimeStr = DateUtils.formatDate(beginTime, DateUtils.DEFAULT_TIME_FORMAT);
        }
        String endTimeStr = "";
        if (endTime != null) {
            endTimeStr = DateUtils.formatDate(endTime, DateUtils.DEFAULT_TIME_FORMAT);
        }
        StringBuilder promotionsTime = new StringBuilder();
        return promotionsTime.append(beginTimeStr).append("~").append(endTimeStr).toString();
    }

    /**
     * 查询促销管理列表
     *
     * @param amzPromotionsQueryRequest 促销管理
     * @return 促销管理
     */
    @Override
    public List<AmzPromotionsListVO> selectAmzPromotionsList(AmzPromotionsQueryRequest amzPromotionsQueryRequest) {

        amzPromotionsQueryRequest.reSetDate();

        List<AmzPromotionsListVO> amzPromotionsList = amzPromotionsMapper.selectAmzPromotionsList(amzPromotionsQueryRequest);
        if (CollectionUtil.isNotEmpty(amzPromotionsList)) {
            amzPromotionsList.forEach(t -> {
                if (t.getPromotionsState() != null) {
                    t.setPromotionsStateName(AmzPromotionsStateEnum.labelOf(t.getPromotionsState()));
                }
                if (t.getPromotionsType() != null) {
                    t.setPromotionsTypeName(AmzPromotionsTypeEnum.labelOf(t.getPromotionsType()));
                }
            });
            List<Long> shopIds = amzPromotionsList.stream().filter(s -> s.getShopId() != null).map(AmzPromotionsListVO::getShopId).distinct().collect(Collectors.toList());
            List<Long> promotionIdList = amzPromotionsList.stream().map(AmzPromotionsListVO::getId).collect(Collectors.toList());
            List<Long> exceptionIdList = amzPromotionsList.stream().filter(q ->
                            AmzPromotionsStateEnum.SUBMIT_EXCEPTION.value().equals(q.getPromotionsState())
                                    || AmzPromotionsStateEnum.SUBMIT_FAIL.value().equals(q.getPromotionsState())
                                    || AmzPromotionsStateEnum.MODIFY_EXCEPTION.value().equals(q.getPromotionsState())
                                    || AmzPromotionsStateEnum.CANCEL_EXCEPTION.value().equals(q.getPromotionsState()))
                    .map(AmzPromotionsListVO::getId).collect(Collectors.toList());

            CompletableFuture<Void> future1 = CompletableFuture.runAsync(() -> {
                if (CollectionUtil.isNotEmpty(shopIds)) {
                    List<Account> accounts = accountMapper.selectBatchIds(shopIds);
                    if (CollectionUtil.isNotEmpty(accounts)) {
                        for (AmzPromotionsListVO vv : amzPromotionsList) {
                            List<Account> accounts1 = accounts.stream().filter(s -> vv.getShopId().equals(s.getId().longValue())).collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(accounts1)) {
                                vv.setTitle(accounts1.get(0).getTitle());
                            }
                        }
                    }
                }
            }, threadPoolTaskExecutor).exceptionally(e -> {
                log.error("查询促销管理列表店铺信息异常", e);
                return null;
            });
            CompletableFuture<Void> future2 = CompletableFuture.runAsync(() -> {
                List<AmzPromotionsSkuDetail> detailList = amzPromotionsSkuDetailMapper.selectAmzPromotionsSkuDetailListByPromotionsId(promotionIdList, amzPromotionsQueryRequest.getNowPstDate(), true);
                List<AmzPromotionsSkuDetail> queryDetailList = new ArrayList<>();
                if ((amzPromotionsQueryRequest.getOrgType() != null && CollectionUtil.isNotEmpty(amzPromotionsQueryRequest.getOrgValue())) || amzPromotionsQueryRequest.getRepeatWithCoupon() != null) {
                    AmzPromotionsQueryRequest copyBean = BeanCopyUtils.copyBean(amzPromotionsQueryRequest, AmzPromotionsQueryRequest.class);
                    copyBean.setPromotionIds(promotionIdList);
                    queryDetailList = amzPromotionsSkuDetailMapper.selectAmzPromotionsSkuDetailListByPromotionsIdAndCondition(copyBean);
                } else {
                    queryDetailList = detailList;
                }

                if (CollectionUtil.isNotEmpty(detailList)) {
                    //查询erpsku,品牌
                    List<String> asinErpSkuList = detailList.stream().filter(r -> StringUtil.isNotEmpty(r.getAsin())).map(AmzPromotionsSkuDetail::getAsin).distinct().collect(Collectors.toList());
                    List<AmzPromotionCheck> erpSkuAndBrand = amzPromotionCheckMapper.selectListByShopIdAndAsin(shopIds, asinErpSkuList);
                    for (AmzPromotionsListVO vo : amzPromotionsList) {
                        if (vo.getSubShopBack() != null && StringUtil.isEmpty(vo.getShopBack())) {
                            vo.setShopBack(vo.getSubShopBack() ? "是" : "否");
                        }
                        if (StringUtils.isEmpty(vo.getAsin())) {
                            continue;
                        }
                        List<AmzPromotionsSkuListVo> skuListVoList = new ArrayList<>();
                        List<AmzPromotionsSkuDetail> collect = detailList.stream().filter(s -> vo.getId().equals(s.getPromotionsId())).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(collect)) {
                            List<AmzPromotionsSkuDetail> skuDetailList = new ArrayList<>();

                            skuDetailList = queryDetailList.stream().filter(s -> s.getPromotionsId().equals(vo.getId())).collect(Collectors.toList());
                            if (StringUtils.isNotEmpty(amzPromotionsQueryRequest.getSubmissionStatus())) {
                                String[] submissionStatusArray = Arrays.stream(amzPromotionsQueryRequest.getSubmissionStatus().split(",")).map(String::toUpperCase).toArray(String[]::new);
                                skuDetailList = skuDetailList.stream().filter(s -> Arrays.asList(submissionStatusArray).contains(s.getSubmissionStatus().toUpperCase())).collect(Collectors.toList());
                            }
                            skuDetailList.forEach(t -> {
                                AmzPromotionsSkuListVo skuListVo = new AmzPromotionsSkuListVo();
                                BeanUtils.copyProperties(t, skuListVo);
                                //涉及明细查询则无值需额外赋值
                                if (skuListVo.getRepeatWithCoupon() == null) {
                                    collect.stream().filter(original->original.getId().equals(skuListVo.getId())).findFirst().ifPresent(original->skuListVo.setRepeatWithCoupon(original.getRepeatWithCoupon()));
                                }
                                skuListVo.setAgreementFlag(vo.getAgreementFlag());
                                skuListVo.setNum((vo.getChannel() != null && new Integer(4).equals(vo.getChannel())) ? null : t.getNum());
                                skuListVo.setPromotionsStateName(vo.getPromotionsStateName());
                                skuListVo.setShopName(vo.getShopName());
                                skuListVo.setTitle(vo.getTitle());
                                skuListVo.setPromotionsId(vo.getPromotionsId());
                                skuListVo.setPromotionsName(vo.getPromotionsName());
                                skuListVo.setPromotionsTypeName(vo.getPromotionsTypeName());
                                skuListVo.setCountry(vo.getCountry());
                                skuListVo.setCreatedAt(vo.getCreatedAt());
                                skuListVo.setCreatedName(vo.getCreatedName());
                                skuListVo.setUpdatedAt(vo.getUpdatedAt());
                                skuListVo.setUpdatedName(vo.getUpdatedName());
                                skuListVo.setCountryCode(vo.getCountryCode());
                                if (StringUtils.isNotEmpty(t.getDiscountFlag()) && t.getDiscount() != null) {
                                    if (t.getDiscountFlag().equalsIgnoreCase("%")) {
                                        skuListVo.setDiscountString(t.getDiscount() + "" + t.getDiscountFlag());
                                    } else {
                                        skuListVo.setDiscountString(t.getDiscountFlag() + "" + t.getDiscount());
                                    }
                                }
                                skuListVo.setCompleteDate(vo.getCompleteDate());
                                //erpsku,brand设置
                                if (CollectionUtil.isNotEmpty(erpSkuAndBrand)) {
                                    List<AmzPromotionCheck> erpSkuAndAsinList = erpSkuAndBrand.stream().filter(r -> r.getShopId().equals(vo.getShopId()) && r.getAsin().equalsIgnoreCase(t.getAsin())).collect(Collectors.toList());
                                    if (CollectionUtil.isNotEmpty(erpSkuAndAsinList)) {
                                        AmzPromotionCheck amzPromotionCheck = erpSkuAndAsinList.get(0);
                                        skuListVo.setSku(amzPromotionCheck.getErpSku());
                                        skuListVo.setBrand(amzPromotionCheck.getBrand());
                                        skuListVo.setOperator(amzPromotionCheck.getOperationUserName());
                                    }
                                }
                                skuListVoList.add(skuListVo);
                            });

                            vo.setRepeatWithCoupon(skuListVoList.stream().anyMatch(t->Boolean.TRUE.equals(t.getRepeatWithCoupon())));
                            vo.setOperator(skuListVoList.stream().filter(r->StringUtils.isNotEmpty(r.getOperator())).map(AmzPromotionsSkuListVo::getOperator).distinct().collect(Collectors.joining(",")));
                            List<String> asinList = collect.stream().filter(asin -> StringUtil.isNotEmpty(asin.getAsin())).map(AmzPromotionsSkuDetail::getAsin).collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(asinList)) {
                                vo.setAsin(String.join(",", asinList));
                            }
                            List<String> skuList = collect.stream().filter(sku -> StringUtil.isNotEmpty(sku.getSellerSku())).map(AmzPromotionsSkuDetail::getSellerSku).collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(skuList)) {
                                vo.setSellerSku(String.join(",", skuList));
                            }
                            //销量，浏览量，花费，销售额
                            vo.setUnitsSold(collect.stream().filter(r -> r.getUnitsSold() != null).mapToLong(AmzPromotionsSkuDetail::getUnitsSold).sum());
                            vo.setAmountSpent(collect.stream().filter(r -> r.getAmountSpent() != null).map(AmzPromotionsSkuDetail::getAmountSpent).reduce(BigDecimal.ZERO, BigDecimal::add));
                            vo.setRevenue(collect.stream().filter(r -> r.getRevenue() != null).map(AmzPromotionsSkuDetail::getRevenue).reduce(BigDecimal.ZERO, BigDecimal::add));
                            vo.setNum(collect.stream().filter(r -> r.getNum() != null).mapToInt(AmzPromotionsSkuDetail::getNum).sum());
                            vo.setDiscountString(skuListVoList.stream().filter(t -> StringUtils.isNotEmpty(t.getDiscountString())).map(AmzPromotionsSkuListVo::getDiscountString).collect(Collectors.joining(",")));
                            vo.setGlanceViews(collect.stream().filter(r -> r.getGlanceViews() != null).mapToLong(AmzPromotionsSkuDetail::getGlanceViews).sum());
                            vo.setPerFunding(skuListVoList.stream().filter(t -> StringUtils.isNotEmpty(t.getPerFunding())).map(AmzPromotionsSkuListVo::getPerFunding).collect(Collectors.joining(",")));
                            vo.setDiscountPrice(skuListVoList.stream().filter(t -> t.getDiscountPrice() != null).map(t -> t.getDiscountPrice().toString()).collect(Collectors.joining(",")));
                        }
                        vo.setSkuList(skuListVoList);
                        //erpsku,brand设置
                        String[] voAsinStringAttr = vo.getAsin().split(",");
                        List<AmzPromotionCheck> voAsinSkuBrand = erpSkuAndBrand.stream().filter(q -> q.getShopId().equals(vo.getShopId()) && Arrays.asList(voAsinStringAttr).contains(q.getAsin())).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(voAsinSkuBrand)) {
                            String voSku = voAsinSkuBrand.stream().filter(q -> StringUtil.isNotEmpty(q.getErpSku())).map(AmzPromotionCheck::getErpSku).distinct().collect(Collectors.joining(","));
                            String voBrand = voAsinSkuBrand.stream().filter(q -> StringUtil.isNotEmpty(q.getBrand())).map(AmzPromotionCheck::getBrand).distinct().collect(Collectors.joining(","));
                            vo.setSku(StringUtil.isNotEmpty(voSku) ? voSku : null);
                            vo.setBrand(StringUtil.isNotEmpty(voBrand) ? voBrand : null);

                        }
                    }
                }
            }, threadPoolTaskExecutor).exceptionally(e -> {
                log.error("查询促销管理列表异常", e);
                return null;
            });
            CompletableFuture<Void> future3 = CompletableFuture.runAsync(() -> {
                if (amzPromotionsQueryRequest.getErrorInfoAllExport()) {
                    if (CollectionUtil.isNotEmpty(promotionIdList)) {
                        try {
                            QueryWrapper<AmzPromotionsOperateLog> logQueryWrapper = new QueryWrapper<>();
                            logQueryWrapper.in("promotion_id", promotionIdList);
                            logQueryWrapper.and(q -> {
                                q.gt("instr(detail,'具体错误信息')", 0).or().gt("instr(detail,'错误信息如下')", 0);
                            });
                            logQueryWrapper.groupBy("promotion_id").orderByDesc("id");
                            List<AmzPromotionsOperateLog> logList = iAmzPromotionsOperateLogService.list(logQueryWrapper);
                            if (CollectionUtil.isNotEmpty(logList)) {
                                Map<Long, List<AmzPromotionsOperateLog>> pIdAmzPromotionOperateLog = logList.stream().collect(Collectors.groupingBy(AmzPromotionsOperateLog::getPromotionId));
                                Set<Map.Entry<Long, List<AmzPromotionsOperateLog>>> entries = pIdAmzPromotionOperateLog.entrySet();
                                for (AmzPromotionsListVO aVo : amzPromotionsList) {
                                    for (Map.Entry<Long, List<AmzPromotionsOperateLog>> entry : entries) {
                                        if (aVo.getId().equals(entry.getKey())) {
                                            AmzPromotionsOperateLog exceptionLastLog = entry.getValue().stream().max(Comparator.comparing(AmzPromotionsOperateLog::getCreatedAt, Comparator.nullsLast(Date::compareTo))).orElse(new AmzPromotionsOperateLog());
                                            String detail = exceptionLastLog.getDetail();
                                            if (StringUtils.isNotEmpty(detail)) {
                                                if (detail.contains("具体错误信息")) {
                                                    aVo.setErrorInfo(detail.substring(detail.lastIndexOf("具体错误信息:")).replace("具体错误信息:", ""));
                                                } else if (detail.contains("错误信息如下")) {
                                                    aVo.setErrorInfo(detail.substring(detail.lastIndexOf("错误信息如下:")).replace("错误信息如下:", ""));
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.error("查询错误信息异常:{}", e.getMessage());
                        }
                    }
                } else {
                    if (CollectionUtil.isNotEmpty(exceptionIdList)) {
                        try {
                            List<AmzPromotionsOperateLog> logList = iAmzPromotionsOperateLogService.selectAmzPromotionOperateLogWhenPromotionIsException(exceptionIdList);
                            if (CollectionUtil.isNotEmpty(logList)) {
                                Map<Long, List<AmzPromotionsOperateLog>> pIdAmzPromotionOperateLog = logList.stream().collect(Collectors.groupingBy(AmzPromotionsOperateLog::getPromotionId));
                                Set<Map.Entry<Long, List<AmzPromotionsOperateLog>>> entries = pIdAmzPromotionOperateLog.entrySet();
                                for (AmzPromotionsListVO aVo : amzPromotionsList) {
                                    for (Map.Entry<Long, List<AmzPromotionsOperateLog>> entry : entries) {
                                        if (aVo.getId().equals(entry.getKey())) {
                                            AmzPromotionsOperateLog exceptionLastLog = entry.getValue().stream().max(Comparator.comparing(AmzPromotionsOperateLog::getCreatedAt, Comparator.nullsLast(Date::compareTo))).orElse(new AmzPromotionsOperateLog());
                                            String detail = exceptionLastLog.getDetail();
                                            if (StringUtil.isNotEmpty(detail) && detail.contains("具体错误信息")) {
                                                aVo.setErrorInfo(detail.substring(detail.lastIndexOf("具体错误信息:")).replace("具体错误信息:", ""));
                                            }
                                        }
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.error("查询错误信息异常:{}", e.getMessage());
                        }
                    }
                }

            }, threadPoolTaskExecutor).exceptionally(e -> {
                log.error("查询促销管理列表异常信息异常", e);
                return null;
            });
            CompletableFuture.allOf(future1, future2, future3).join();
        } else {
            return new ArrayList<AmzPromotionsListVO>();
        }
        return amzPromotionsList;
    }

    /**
     * 新增或修改促销
     *
     * @param amzPromotionsCreateRequest 新增或修改促销
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertAmzPromotions(AmzPromotionsCreateRequest amzPromotionsCreateRequest) {

        Long id = this.processInsertAmzPromotions(amzPromotionsCreateRequest);

        this.startApprovalProcess(id);

        //发起企业微信通知
        try {
            iAmzPromotionApprovalAdviceService.promotionsApprovalAdviceWx(id, amzPromotionsCreateRequest.getUpdatedAt(), amzPromotionsCreateRequest.getUpdatedName(),amzPromotionsCreateRequest.getUpdatedBy());
        } catch (Exception e) {
            log.error("发送企业微信消息失败:{}", e.getMessage());
        }

    }

    /**
     * 发起审批
     *
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startApprovalProcess(Long id) {

        AmzPromotions amzPromotions = this.getById(id);
        AuthUserDetails thisUser = UserUtils.getThisUser();

        Long outJsonId = processStartService.getOutJsonId(3, amzPromotions.getOrganizationId());
        if (outJsonId == null) {
            log.error("vc-promotion调用activitiapi异常,outJsonId为null,businessId：{}", amzPromotions.getId());
            //自动审批通过
            AmzPromotionsApproval amzPromotionsApproval = new AmzPromotionsApproval();
            amzPromotionsApproval.setOperationUserName(thisUser.getName());
            amzPromotionsApproval.setDate(DateUtils.getNowDate());
            amzPromotionsApproval.setOperationUserId(thisUser.getId());
            amzPromotionsApproval.setIds(new Long[]{id});
            amzPromotionsApproval.setContextId(amzPromotions.getOrganizationId());
            amzPromotionsApproval.setViewOrRpa(true);
            amzPromotionsApproval.setApprovalStatus(AmzPromotionApprovalEnum.APPROVED.getLabel());
            iAmzPromotionsApprovalService.promotionApproval(amzPromotionsApproval);
        } else {
            //走工作台审批
            log.info("vc-amzPromotions:{},获取jsonID成功，进入工作台审批,jsonId:{}", amzPromotions, outJsonId);
            DeployStartDTO deployStartDTO = new DeployStartDTO();
            deployStartDTO.setContextId(amzPromotions.getOrganizationId());
            deployStartDTO.setUserId(thisUser.getId());
            deployStartDTO.setOutJsonId(outJsonId);//模板id
            deployStartDTO.setBusinessId(amzPromotions.getId().toString());//绑定流程的id
            deployStartDTO.setSummary("");//摘要 可有可无
            deployStartDTO.setBindValue(amzPromotions.getPromotionsId());
            List<Object> arg = new ArrayList<>();
            arg.add(amzPromotions.getId());
            deployStartDTO.setArgument(JSONObject.toJSONString(arg));//回调参数
            log.info("vc-promotion-businessId--{}--发起申请--参数:{}", amzPromotions.getId(), JSONObject.toJSONString(deployStartDTO));
            Long instantId = processStartService.startProcess(deployStartDTO);
            if (instantId == null) {
                log.error("vc-promotion调用activitiapi异常instantId为null,businessId：{}", amzPromotions.getId());
                throw new ErpCommonException(String.format("vc-promotion调用工作流获取实例Id异常,业务id:%s", amzPromotions.getId()));
            }
            this.lambdaUpdate().eq(AmzPromotions::getId, amzPromotions.getId())
                    .set(AmzPromotions::getUpdatedAt, DateUtils.getNowDate())
                    .set(AmzPromotions::getUpdatedBy, thisUser.getId())
                    .set(AmzPromotions::getUpdatedName, thisUser.getName())
                    .set(AmzPromotions::getInstanceId, instantId)
                    .update();
            this.insertPromotionLog(amzPromotions.getId(), thisUser.getId(), DateUtils.getNowDate(), thisUser.getName(), AmzPromotionsOperateTypeEnum.SUBMIT_APPROVAL.value(), null, "发起工作流审批", amzPromotions.getOrganizationId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long processInsertAmzPromotions(AmzPromotionsCreateRequest amzPromotionsCreateRequest) {
        Integer updateType = amzPromotionsCreateRequest.getUpdateType();
        AmzPromotions amzPromotions = new AmzPromotions();
        AmzPromotions originalPromotions = new AmzPromotions();
        AmzPromotions byId = null;
        if (amzPromotionsCreateRequest.getId() != null) {
            byId = amzPromotionsMapper.selectById(amzPromotionsCreateRequest.getId());
            BeanUtils.copyProperties(byId, originalPromotions);
        }
        AccountWithCountry accountWithCountry = amzPromotionsMapper.selectAccountWithCountry(amzPromotionsCreateRequest.getShopId());
        BeanUtils.copyProperties(amzPromotionsCreateRequest, amzPromotions);

        amzPromotions.setFlag(MarPromotionCountryCurrencyEnum.getCurrencyFlagByCountryCode(accountWithCountry.getCountryCode(), amzPromotionsCreateRequest.getPromotionsType()));
        amzPromotions.setApprovalStatus(AmzPromotionApprovalEnum.APPROVING.getLabel());
        if (amzPromotions.getCreatedAt() != null) {
            amzPromotions.setCreatedAt(DateUtil.UtcToPacificDate(amzPromotions.getCreatedAt()));
        }
        if (amzPromotions.getEndTime() != null) {
            amzPromotions.setEndTime(DateUtil.convertStringToDate(DateUtil.convertDateToString(cn.hutool.core.date.DateUtil.endOfDay(amzPromotions.getEndTime()), "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd HH:mm:ss"));
        }
        List<AmzPromotionsSkuDetail> skuDetailList = amzPromotionsCreateRequest.getSkuDetailList();

        StringBuilder logDetail = new StringBuilder();

        //1.新增
        if (updateType.equals(1)) {
            amzPromotions.setCountry(accountWithCountry.getNameCn());
            amzPromotionsMapper.insert(amzPromotions);
            logDetail.append("创建").append(AmzPromotionsTypeEnum.labelOf(amzPromotions.getPromotionsType()));
            insertPromotionLog(amzPromotions.getId(), amzPromotionsCreateRequest.getCreatedBy(), amzPromotionsCreateRequest.getCreatedAt(),
                    amzPromotionsCreateRequest.getCreatedName(),
                    AmzPromotionsOperateTypeEnum.CREATE_PROMOTION.value(),
                    null, logDetail.toString(), amzPromotionsCreateRequest.getOrganizationId(), amzPromotionsCreateRequest.getRemark());
            iAmzPromotionsHisService.setCacheKey(amzPromotions, updateType);

        } else {
            iAmzPromotionsHisService.setCacheKey(amzPromotions, updateType);
            //存储修改前状态
            if (byId.getSubShopBack() != null && byId.getSubShopBack()) {

                iAmzPromotionsHisService.setPromotionState(byId.getId(), byId.getPromotionsState());
            }
            amzPromotionsMapper.updateById(amzPromotions);
            //2.提交异常的修改
            if (updateType.equals(2)) {
                if (byId.getSubShopBack() == null || !byId.getSubShopBack()) {
                    this.lambdaUpdate().eq(AmzPromotions::getId, amzPromotions.getId())
                            .set(AmzPromotions::getPromotionsState, null)
                            .update();
                }
                logDetail.append("创建").append(AmzPromotionsTypeEnum.labelOf(amzPromotions.getPromotionsType()));
                //3.普通修改
            } else if (updateType.equals(3)) {
                logDetail.append("修改Promotion");
                //4.Needs your attention、Approved、修改异常的修改
            } else if (updateType.equals(4)) {
            }
            dateModifyLog(originalPromotions, amzPromotions, logDetail);

            //修改操作删除（该促销原有的sku减去传来的含有skuId的sku之后）的sku
            List<AmzPromotionsSkuDetail> tempDetails = iAmzPromotionsSkuDetailService.list(Wrappers.<AmzPromotionsSkuDetail>query().lambda().eq(AmzPromotionsSkuDetail::getPromotionsId, amzPromotions.getId()));
            if (CollectionUtil.isNotEmpty(tempDetails)) {
                List<Long> idColl = tempDetails.stream().map(AmzPromotionsSkuDetail::getId).collect(Collectors.toList());
                int idCollSize = idColl.size();
                List<Long> frontSkuId = skuDetailList.stream().filter(s -> s.getId() != null).map(AmzPromotionsSkuDetail::getId).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(frontSkuId)) {
                    iAmzPromotionsSkuDetailService.removeByIds(idColl);
                } else {
                    idColl.removeAll(frontSkuId);
                    if (idColl.size() != 0 && idColl.size() != idCollSize) {
                        iAmzPromotionsSkuDetailService.removeByIds(idColl);
                    }
                }
            }
        }
        for (AmzPromotionsSkuDetail detail : skuDetailList) {
            AmzPromotionsSkuDetail originalSkuDetail = null;
            if (updateType.equals(1)) {
                setSkuDate(detail, amzPromotionsCreateRequest, true, accountWithCountry);
                detail.setPromotionsId(amzPromotions.getId());
                //页面创建修改除BD外都不会传折后价（BD折后价与折后价最大值校验前端校验），导入创建所有类型会可设置折后价，仅BD类型会设置折后价做校验且保存
                if (!AmzPromotionsTypeEnum.BEST_DEAL.value().equals(amzPromotions.getPromotionsType())) {
                    detail.setDiscountPrice(null);
                }
                iAmzPromotionsSkuDetailService.save(detail);
            } else {
                if (detail.getId() != null) {
                    setSkuDate(detail, amzPromotionsCreateRequest, false, accountWithCountry);
                    originalSkuDetail = iAmzPromotionsSkuDetailService.getById(detail.getId());
                    iAmzPromotionsSkuDetailService.updateById(detail);
                } else {
                    setSkuDate(detail, amzPromotionsCreateRequest, true, accountWithCountry);
                    detail.setPromotionsId(amzPromotions.getId());
                    iAmzPromotionsSkuDetailService.save(detail);
                }

                addLogDetail(originalPromotions, amzPromotions, originalSkuDetail, detail.getId() != null ? detail : null, updateType, amzPromotions.getPromotionsType(), logDetail);

            }
        }
        if (!updateType.equals(1)) {
            insertPromotionLog(amzPromotions.getId(), amzPromotionsCreateRequest.getUpdatedBy(), amzPromotionsCreateRequest.getUpdatedAt(),
                    amzPromotionsCreateRequest.getUpdatedName(),
                    updateType.equals(2) ? AmzPromotionsOperateTypeEnum.SUBMIT_EXCEPTION_MODIFY.value() : AmzPromotionsOperateTypeEnum.MODIFY_PROMOTION.value(),
                    null, logDetail.toString(), amzPromotionsCreateRequest.getOrganizationId(), amzPromotionsCreateRequest.getRemark());
        }


        log.info("vc-promotion创建或修改id--{}--当前时间--{}", amzPromotions.getId(), DateUtils.getNowDate());

        return amzPromotions.getId();
    }

    private void setSkuDate(AmzPromotionsSkuDetail detail, AmzPromotionsCreateRequest request, boolean createSkuOrUpdate, AccountWithCountry accountWithCountry) {
        detail.setShopName(request.getShopName());
        detail.setFlag(MarPromotionCountryCurrencyEnum.getCurrencyFlagByCountryCode(accountWithCountry.getCountryCode() , request.getPromotionsType()));
        if (StringUtils.isNotEmpty(detail.getDiscountFlag()) && !"%".equals(detail.getDiscountFlag().trim())) {
            detail.setDiscountFlag(detail.getFlag());
        } else {
            detail.setDiscountFlag("%");
        }
        detail.setOrganizationId(request.getOrganizationId());
        if (createSkuOrUpdate) {
            detail.setCreatedAt(request.getUpdatedAt());
            detail.setCreatedBy(request.getUpdatedBy());
            detail.setCreatedName(request.getUpdatedName());
        }
        detail.setUpdatedAt(request.getUpdatedAt());
        detail.setUpdatedBy(request.getUpdatedBy());
        detail.setUpdatedName(request.getUpdatedName());
        if (detail.getDiscountPrice() != null && detail.getExpectedDiscountPrice() != null && detail.getDiscountPrice().compareTo(detail.getExpectedDiscountPrice()) != 0) {
            detail.setDiscountPriceWarn(true);
        }
    }

    /**
     * promotions状态为Needs your attention、Approved、修改异常的修改时，日志详情的写入
     *
     * @param originPromotions 库中原有的promotions
     * @param amzPromotions    修改后的promotions
     * @param updateBefore     修改数据前的sku
     * @param updateAfter      修改数据后的sku
     * @param updateType       更新类型 新增或者各种修改
     * @param promotionsType   促销类型
     * @param logDetail        日志详情
     * @return
     */
    private void addLogDetail(AmzPromotions originPromotions, AmzPromotions amzPromotions, AmzPromotionsSkuDetail updateBefore, AmzPromotionsSkuDetail updateAfter, Integer updateType, Integer promotionsType, StringBuilder logDetail) {
        //类型为promo code时，可以修改促销的 折扣，budget,code
        if (AmzPromotionsTypeEnum.PROMO_CODE.value().equals(promotionsType)) {
            //折扣、Budget、code。
            addLog("promotion(" + originPromotions.getPromotionsName() + "):", originPromotions.getDiscountFlag(), originPromotions.getDiscount(), amzPromotions.getDiscountFlag(), amzPromotions.getDiscount(), "折扣", logDetail);
            addLog("promotion(" + originPromotions.getPromotionsName() + "):", originPromotions.getFlag(), originPromotions.getBudget(), amzPromotions.getFlag(), amzPromotions.getBudget(), "Budget", logDetail);
            addLog("promotion(" + originPromotions.getPromotionsName() + "):", originPromotions.getCode(), amzPromotions.getCode(), "code", logDetail);
        } else {
            if (updateBefore != null && updateAfter != null) {
                addLog(updateBefore.getAsin(), updateBefore.getDiscountFlag(), updateBefore.getDiscount(), updateAfter.getDiscountFlag(), updateAfter.getDiscount(), "Per-unit funding", logDetail);
                if (AmzPromotionsTypeEnum.LIGHTNING_DEAL.value().equals(promotionsType) || AmzPromotionsTypeEnum.BEST_DEAL.value().equals(promotionsType)) {
                    addLog(updateBefore.getAsin(), updateBefore.getNum(), updateAfter.getNum(), "数量", logDetail);
                    addLog(updateBefore.getAsin(), updateBefore.getExpectedDiscountPrice(), updateAfter.getExpectedDiscountPrice(), "预期折后价格", logDetail);
                    Boolean discountPriceWarn = false;
                    if (updateAfter.getDiscountPrice() != null && updateAfter.getExpectedDiscountPrice() != null && updateAfter.getDiscountPrice().compareTo(updateAfter.getExpectedDiscountPrice()) != 0) {
                        discountPriceWarn = true;
                    }
                    if (updateBefore.getDiscountPriceWarn() != null && !updateBefore.getDiscountPriceWarn().equals(discountPriceWarn)) {
                        logDetail.append(updateBefore.getAsin()).append(":").append("折后价格预警").append(updateBefore.getDiscountPriceWarn() ? "是" : "否").append(" 修改为 ").append(discountPriceWarn ? "是" : "否").append(";");
                    }
                }
            }
        }
    }

    private void addLog(String asin, String updateBeforeFlag, BigDecimal updateBeforeDiscount, String updateAfterFlag, BigDecimal updateAfterDiscount, String column, StringBuilder logDetail) {
        if (StringUtil.isNotEmpty(updateBeforeFlag) && updateBeforeDiscount != null) {
            if (updateBeforeFlag.trim().equalsIgnoreCase("%")) {
                if (StringUtil.isNotEmpty(updateAfterFlag) && updateAfterDiscount != null) {
                    if (updateAfterFlag.trim().equalsIgnoreCase("%")) {
                        if (updateBeforeDiscount.compareTo(updateAfterDiscount) != 0) {
                            logDetail.append(asin).append(":").append(column).append(updateBeforeDiscount).append(updateBeforeFlag.trim())
                                    .append(" 修改为 ").append(updateAfterDiscount).append(updateAfterFlag.trim()).append(";");
                        }
                    } else {
                        logDetail.append(asin).append(":").append(column).append(updateBeforeDiscount).append(updateBeforeFlag.trim())
                                .append(" 修改为 ").append(updateAfterFlag.trim()).append(updateAfterDiscount).append(";");
                    }
                } else {
                    logDetail.append(asin).append(":").append(column).append(updateBeforeDiscount).append(updateBeforeFlag.trim()).append(" 修改为 空;");
                }
            } else {
                if (StringUtil.isNotEmpty(updateAfterFlag) && updateAfterDiscount != null) {
                    if (updateAfterFlag.trim().equalsIgnoreCase("%")) {
                        logDetail.append(asin).append(":").append(column).append(updateBeforeFlag.trim()).append(updateBeforeDiscount)
                                .append(" 修改为 ").append(updateAfterDiscount).append(updateAfterFlag.trim()).append(";");
                    } else {
                        if (updateBeforeDiscount.compareTo(updateAfterDiscount) != 0) {
                            logDetail.append(asin).append(":").append(column).append(updateBeforeFlag.trim()).append(updateBeforeDiscount)
                                    .append(" 修改为 ").append(updateAfterFlag.trim()).append(updateAfterDiscount).append(";");
                        }
                    }
                } else {
                    logDetail.append(asin).append(":").append(column).append(updateBeforeFlag.trim()).append(updateBeforeDiscount).append(" 修改为 空;");
                }
            }
        } else {
            if (StringUtil.isNotEmpty(updateAfterFlag) && updateAfterDiscount != null) {
                if (updateAfterFlag.trim().equalsIgnoreCase("%")) {
                    logDetail.append(asin).append(":").append(column).append("：空")
                            .append(" 修改为 ").append(updateAfterDiscount).append(updateAfterFlag.trim()).append(";");

                } else {
                    logDetail.append(asin).append(":").append(column).append("：空")
                            .append(" 修改为 ").append(updateAfterFlag.trim()).append(updateAfterDiscount).append(";");
                }
            }
        }
    }

    private void addLog(String asin, Integer updateBefore, Integer updateAfter, String column, StringBuilder logDetail) {
        if (updateBefore != null) {
            if (!updateBefore.equals(updateAfter)) {
                logDetail.append(asin).append(":").append(column).append(":").append(updateBefore).append(" 修改为 ").append(updateAfter == null ? "空" : updateAfter).append(";");
            }
        } else {
            if (updateAfter != null) {
                logDetail.append(asin).append(":").append(column).append(":").append("：空 修改为").append(updateAfter).append(";");
            }
        }
    }
    private void addLog(String asin, BigDecimal updateBefore, BigDecimal updateAfter, String column, StringBuilder logDetail) {
        if (updateAfter == null || updateBefore == null) {
            logDetail.append(asin).append(":").append(column).append(":").append(updateBefore == null ? "空" : updateBefore).append(" 修改为 ").append(updateAfter == null ? "空" : updateAfter).append(";");
        }else {
            if (updateBefore.compareTo(updateAfter) != 0) {
                logDetail.append(asin).append(":").append(column).append(":").append(updateBefore).append(" 修改为 ").append(updateAfter).append(";");
            }
        }
    }

    private void addLog(String asin, String updateBefore, String updateAfter, String column, StringBuilder logDetail) {
        if (StringUtil.isNotEmpty(updateBefore)) {
            if (!updateBefore.equalsIgnoreCase(updateAfter)) {
                logDetail.append(asin).append(":").append(column).append(":").append(updateBefore).append(" 修改为 ").append(StringUtils.isEmpty(updateAfter) ? "空" : updateAfter).append(";");
            }
        } else {
            if (StringUtil.isNotEmpty(updateAfter)) {
                logDetail.append(asin).append(":").append(column).append(":").append("：空 修改为").append(updateAfter).append(";");
            }
        }
    }






    /**
     * 删除促销管理信息(不删除数据，更新删除者信息)
     *
     * @param id             促销管理ID
     * @param authUserEntity
     * @return 结果
     */


    /**
     * 取消促销
     *
     * @param id
     * @param remark
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelAmzPromotions(Long id, UserEntity authUserEntity, Integer contextId, String remark) {
        AmzPromotions amzPromotions = new AmzPromotions();
        amzPromotions.setId(id);
        AmzPromotions byId = amzPromotionsMapper.selectById(id);
        if (byId.getAgreementFlag().equals(1)) {
            this.batchCancelAgreementPromotion(Collections.singletonList(byId), authUserEntity, contextId, remark);
            return;
        }
        iAmzPromotionsHisService.setPromotionState(id,byId.getPromotionsState());
        StringBuilder promotionType = new StringBuilder();
        if (byId != null) {
            promotionType.append(AmzPromotionsTypeEnum.labelOf(byId.getPromotionsType()));
            if (AmzPromotionsStateEnum.CANCELING.value().equals(byId.getPromotionsState()) || AmzPromotionsStateEnum.CANCELED.value().equals(byId.getPromotionsState())) {
                throw new ErpCommonException("promotion正在取消或已取消");
            }
        } else {
            throw new ErpCommonException("查无此promotion");
        }
        amzPromotions.setUpdatedBy(authUserEntity.getId());
        amzPromotions.setUpdatedName(authUserEntity.getName());
        amzPromotions.setUpdatedAt(DateUtils.getNowDate());
        amzPromotions.setOrganizationId(contextId);
        amzPromotions.setPromotionsState(AmzPromotionsStateEnum.CANCELING.value());
        amzPromotions.setApprovalStatus(AmzPromotionApprovalEnum.APPROVED.getLabel());
        amzPromotions.setRemark(remark);
        amzPromotionsMapper.updateById(amzPromotions);
        String cancelDetail = "取消" + promotionType + (StringUtil.isEmpty(byId.getPromotionsId()) ? "" : byId.getPromotionsId());
        if (byId.getPromotionsState() == null) {
            cancelDetail = cancelDetail + " 状态由空更新为取消中";
        } else {
            cancelDetail = cancelDetail + " 状态由" + AmzPromotionsStateEnum.labelOf(byId.getPromotionsState()) + "更新为取消中";
        }
        insertPromotionLog(id, authUserEntity.getUpdatedBy(), amzPromotions.getUpdatedAt(), authUserEntity.getName(),
                AmzPromotionsOperateTypeEnum.CANCEL_PROMOTION.value(), null, cancelDetail, contextId);

        createPromotionMsgSend(amzPromotions, 5);
    }




    /**
     * 其他时区时间转换成本地时间
     *
     * @param utcTime
     * @param utcTimePatten
     * @param localTimePatten
     * @return
     */
    private static Date utcToLocalTime(Date utcTime, String utcTimePatten,
                                       String localTimePatten) {

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(localTimePatten);
        simpleDateFormat.setTimeZone(TimeZone.getDefault());
        String localTime = simpleDateFormat.format(utcTime.getTime());
        Date time = DateUtils.parseDate(localTime, utcTimePatten);
        return time;
    }

    /**
     * 写入日志
     *
     * @param id          日志对应的 promotions主键id
     * @param operateBy   操作人id
     * @param operateDate 操作时间
     * @param operateName 操作人
     * @param operateType 操作类型
     * @param result      结果
     * @param detail      详情
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertPromotionLog(Long id, Integer operateBy, Date operateDate, String operateName, Integer operateType, String result, String detail, Integer contextId) {

        AmzPromotionsOperateLog log = new AmzPromotionsOperateLog();
        log.setPromotionId(id);
        log.setCreatedBy(operateBy);
        log.setCreatedAt(operateDate);
        log.setCreatedName(operateName);
        log.setOperateType(operateType);
        log.setResult(result);
        log.setDetail(detail);
        log.setOrganizationId(contextId);
        return iAmzPromotionsOperateLogService.insertAmzPromotionsOperateLog(log);
    }

    /**
     * 校验创promotions参数
     *
     * @param amzPromotionsCreateRequest
     * @return
     */
    @Override
    @AmzPromotionVerify
    public void verifyCreatePromotions(AmzPromotionsCreateRequest amzPromotionsCreateRequest) {

    }


    /**
     * 提交异常如错误不是选择的ASIN不能参加promotion则显示提交按钮。直接提交。
     *
     * @param resubmitId             promotions主键id
     * @param operateBy      操作人id
     * @param createdName    操作人
     * @param promotionsType 促销类型
     * @param contextId      组织id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @AmzPromotionVerify
    public int submit(Long resubmitId, Integer operateBy, String createdName, Integer promotionsType, Integer contextId) {

        StringBuilder s = new StringBuilder();
        Date nowDate = DateUtils.getNowDate();
        AmzPromotions byId = amzPromotionsMapper.selectById(resubmitId);
        if (byId.getPromotionsState() != null && AmzPromotionsStateEnum.SUBMITTING.value().equals(byId.getPromotionsState())) {
            throw new ErpCommonException("提交异常直接提交操作正在进行中，请勿重复点击");
        }
        AccountWithCountry accountWithCountry = amzPromotionsMapper.selectAccountWithCountry(byId.getShopId());
        Date siteDate = MarPromotionCountryCurrencyEnum.getCurrencyDateByCountryCode(accountWithCountry.getCountryCode());
        ApiResponseResult apiResponseResult = submitExceptionDirectSubmit(byId, siteDate);
        if (!apiResponseResult.getCode().equals(200)) {
            throw new ErpCommonException(apiResponseResult.getMessage());
        }
        //开始时间结束时间校验
        if (!submitExceptionDirectSubmitVerifyDate(byId)) {
            throw new ErpCommonException("promotion中的产品已有且时间段重合。");
        }
        if (promotionsType != null) {
            s.append("创建").append(AmzPromotionsTypeEnum.labelOf(promotionsType).toString());
        } else {
            if (byId != null) {
                s.append("创建").append(AmzPromotionsTypeEnum.labelOf(byId.getPromotionsType()).toString());
            }
        }
        if (StringUtil.isEmpty(createdName)) {
            createdName = byId.getCreatedName();
        }
        AmzPromotions promotions = new AmzPromotions();
        promotions.setId(resubmitId);
        promotions.setUpdatedName(createdName);
        promotions.setUpdatedBy(operateBy);
        promotions.setUpdatedAt(nowDate);
        promotions.setPromotionsState(AmzPromotionsStateEnum.SUBMITTING.value());
        amzPromotionsMapper.updateById(promotions);
        int i = insertPromotionLog(resubmitId, operateBy, DateUtils.getNowDate(), createdName, AmzPromotionsOperateTypeEnum.SUBMIT_EXCEPTION.value(),
                null, s.toString(), contextId);
        createPromotionMsgSend(byId, 1);
        return i;
    }


    /**
     * 查询条件包含%或者_,添加/
     *
     * @param name
     * @return
     */
    public String getName(String name) {
        if (StringUtil.isNotEmpty(name) && (name.contains("_") || name.contains("%"))) {
            char[] chars = name.toCharArray();
            StringBuilder result = new StringBuilder();
            for (char ch : chars) {
                if (ch == '_' || ch == '%') {
                    result.append("/");
                }
                result.append(ch);
            }
            return result.toString();
        }
        return name;
    }



    /**
     * Promotion校验一下 同组织下相同asin 不能同（提交中、Approved、pending approval、取消中、取消异常、修改中、修改异常）的有相同时间段的promotion
     *
     * @param request
     * @param sb
     * @param asin
     * @return
     */
    public boolean dateVerify(AmzPromotionsCreateRequest request, StringBuilder sb, String asin) {

        List<Integer> promotionState = new ArrayList<>();
        promotionState.add(AmzPromotionsStateEnum.SUBMITTING.value());
        promotionState.add(AmzPromotionsStateEnum.APPROVED.value());
        promotionState.add(AmzPromotionsStateEnum.PENDING_APPROVAL.value());
        promotionState.add(AmzPromotionsStateEnum.CANCELING.value());
        promotionState.add(AmzPromotionsStateEnum.CANCEL_EXCEPTION.value());
        promotionState.add(AmzPromotionsStateEnum.MODIFYING.value());
        promotionState.add(AmzPromotionsStateEnum.MODIFY_EXCEPTION.value());
//        promotionState.add(AmzPromotionsStateEnum.NEED_YOUR_ATTENTION.value());
        promotionState.add(AmzPromotionsStateEnum.APPROVED_BUT_NOT_FEATURED.value());
        promotionState.add(AmzPromotionsStateEnum.APPROVED_BUT_NEEDS_YOUR_ATTENTION.value());
        promotionState.add(AmzPromotionsStateEnum.PROCESSING.value());
        //暂时不加店铺条件
        List<AccountWithCountry> accountWithCountryList = amzPromotionAccountMapper.selectAccountWithCountryList(new Long[]{request.getShopId()});
        String country = CollectionUtil.isEmpty(accountWithCountryList) ? null : accountWithCountryList.get(0).getCountryCode();
        List<AmzPromotions> promotionsList = amzPromotionsMapper.selectAmzPromotionsListByState(promotionState.toArray(new Integer[promotionState.size()]), request.getOrganizationId(), null, asin, country);
        if (CollectionUtil.isEmpty(promotionsList)) {
            return true;
        }
        Date startDate = request.getBeginTime();
        Date endDate = request.getEndTime();
        for (AmzPromotions s : promotionsList) {
            //修改情况，不比较本身
            if (request.getUpdateType() != 1 && request.getId().equals(s.getId())) {
                continue;
            }
            Date beginTime = s.getBeginTime();
            Date endTime = s.getEndTime();
            if (s.getPromotionsType() != null && AmzPromotionsTypeEnum.LIGHTNING_DEAL.value().equals(s.getPromotionsType())) {
                endTime = DateUtil.addDate(beginTime, 1);
            }
            //为true表示无重复
            boolean dateVerify = true;
            if (request.getPromotionsType() != null && !AmzPromotionsTypeEnum.LIGHTNING_DEAL.value().equals(request.getPromotionsType())) {
                dateVerify = DateUtil.compareDate(startDate, endTime) > 0 || DateUtil.compareDate(endDate, beginTime) < 0;
            } else {
                dateVerify = DateUtil.compareDate(startDate, endTime) > 0 || DateUtil.compareDate(DateUtil.addDate(request.getBeginTime(), 1), beginTime) < 0;
            }
            if (!dateVerify) {
                sb.append("promotion中的产品[").append(asin).append("]已有且时间段重合。");
                return false;
            }
        }
        return true;
    }


    /**
     * promotion消息发送
     *
     * @param amzPromotions
     * @param rpaType
     */
    @Transactional(rollbackFor = Exception.class)
    public void createPromotionMsgSend(AmzPromotions amzPromotions, Integer rpaType) {

        boolean needsAttentionsModify = rpaType == 4;
        //类型为创建或者提交异常的修改或提交异常直接提交对于店铺后台都算创建新的promotion
        if (rpaType == 1 || rpaType == 2) {
            rpaType = 1;
            //类型为其他修改对于店铺后台都算修改promotion
        } else if (rpaType == 3 || rpaType == 4) {
            rpaType = 2;
            //类型为取消对于店铺后台都算取消
        } else {
            rpaType = 4;
        }
        Long id = amzPromotions.getId();
        AmzPromotions byId = amzPromotionsMapper.selectById(id);
        log.info("promotion消息发送前:{}", JSONObject.toJSONString(byId));
        AmzPromotionsSkuDetail skuDetail = new AmzPromotionsSkuDetail();
        skuDetail.setPromotionsId(id);
        List<AmzPromotionsSkuDetail> detailList = amzPromotionsSkuDetailMapper.selectList(Wrappers.lambdaQuery(AmzPromotionsSkuDetail.class).eq(AmzPromotionsSkuDetail::getPromotionsId, id));

        AmzPromotionsSendMsg msg = new AmzPromotionsSendMsg();
        msg.setId(amzPromotions.getId());
        msg.setMarketplaceId(byId.getMarketplaceId());
        msg.setPromotionsId(byId.getPromotionsId());
        msg.setPromotionsName(byId.getPromotionsName());
        msg.setChannel(1);
        msg.setShopId(byId.getShopId());
        msg.setShopName(byId.getShopName());
        msg.setWebStoreName(getWebStoreName(msg.getShopId()));
        msg.setType(byId.getPromotionsType());
        msg.setRpaType(rpaType);
        msg.setPromotionState(byId.getPromotionsState());
        msg.setVendorCode(byId.getVendorCode());
//        msg.setBillingContact(amzPromotions.getBillingContact());
        msg.setBeginTime(DateUtils.parseDateToStr("yyyy-MM-dd", byId.getBeginTime()));
        if (Arrays.asList(1,3,5).contains(byId.getPromotionsType())){
            msg.setEndTime(DateUtils.parseDateToStr("yyyy-MM-dd", byId.getEndTime()));
        } else if (byId.getPromotionsType().equals(4)) {
            msg.setEndTime(DateUtils.parseDateToStr("yyyy-MM-dd", byId.getEndTime()));
            msg.setDiscount(byId.getDiscount() != null ? byId.getDiscount().toString() : null);
            msg.setDiscountFlag(byId.getDiscountFlag());
            msg.setFlag(StringUtil.isNotEmpty(amzPromotions.getFlag()) ? byId.getFlag() : "$");
            msg.setBudget(byId.getBudget() != null ? byId.getBudget().toString() : null);
            msg.setShare(byId.getShare());
            msg.setRedemptions(byId.getRedemptions());
            msg.setCode(byId.getCode());
        } else {
        }
        if (CollectionUtil.isNotEmpty(detailList)) {
            List<AmzPromotionsSkuSendMsg> skuSendMsg = detailList.stream().map(s -> {
                AmzPromotionsSkuSendMsg sku = new AmzPromotionsSkuSendMsg();
                sku.setAsin(s.getAsin());
                sku.setSellerSku(s.getSellerSku());
                sku.setFlag(getFlag(s.getFlag(),byId.getPromotionsType(),byId.getShopId()));
                sku.setNum(s.getNum() != null ? s.getNum().toString() : null);
                sku.setPerUnitFundFlag(getFlag(s.getDiscountFlag(),byId.getPromotionsType(),byId.getShopId()));
                sku.setPerUnitFund(s.getDiscount() != null ? s.getDiscount().toString() : null);
//                sku.setHeroProduct(s.getHeroProduct());
                return sku;
            }).collect(Collectors.toList());
            msg.setSkuDetailList(skuSendMsg);
        }
        //needs your attention的修改，传递已删除的asin(如果有)
        if (needsAttentionsModify) {
            String skuListKey = AmzPromotionsCouponConstants.PROMOTION_CREATE_OR_UPDATE_SKU_KEY + ":" + id;
            List<AmzPromotionsHis> amzPromotionsHis2 = amzPromotionsHisMapper.selectList(Wrappers.<AmzPromotionsHis>lambdaQuery().eq(AmzPromotionsHis::getSkuListKey, skuListKey));
            if (CollectionUtil.isNotEmpty(amzPromotionsHis2)) {
                List<String> originAsin = amzPromotionsHis2.stream().map(AmzPromotionsHis::getAsin).distinct().collect(Collectors.toList());
                List<String> nowAsin = detailList.stream().map(AmzPromotionsSkuDetail::getAsin).distinct().collect(Collectors.toList());
                List<String> deleteAsin = new ArrayList<>();
                for (String oAsin : originAsin) {
                    if (!nowAsin.contains(oAsin)) {
                        deleteAsin.add(oAsin);
                    }
                }
                if (CollectionUtil.isNotEmpty(deleteAsin)) {
                    log.info("needs your attention修改 promotion:{} 原有asin为:{},现有asin为:{},待删除的asin为:{}", id, String.join(",", originAsin), String.join(",", nowAsin), String.join(",", deleteAsin));
                    msg.setDeleteAsin(String.join(",", deleteAsin));
                } else {
                    log.info("needs your attention修改 promotion:{} 原有asin为:{},现有asin为:{}", id, String.join(",", originAsin), String.join(",", nowAsin));
                }
            }
        }

        JSONObject object = JSONObject.parseObject(JSONObject.toJSONString(msg));
        log.info("promotion消息发送:{}", JSONObject.toJSONString(msg));
        rabbitTemplate.convertAndSend(MQDefine.PROMOTIONS_PROMOTIONS_EXCHANGE, MQDefine.PROMOTION_CREATE_OR_UPDATE_SENG_ROUTING, object);

    }

    private String getFlag(String originalFlag, Integer promotionType,Long shopId) {

        Account accountFromDb = accountService.getById(shopId);
        if ("CA".equalsIgnoreCase(accountFromDb.getCountryCode())) {
            return "CAD";
        }
        if (AmzPromotionsTypeEnum.BEST_DEAL.value().equals(promotionType) || AmzPromotionsTypeEnum.LIGHTNING_DEAL.value().equals(promotionType)) {

            if (StringUtils.isEmpty(originalFlag)) {

                String currency = MarPromotionCountryCurrencyEnum.getCurrencyByCountryCode(accountFromDb.getCountryCode());
                return currency;
            }else{

                return MarPromotionCountryCurrencyEnum.getCurrencyByCurrencyFlag(originalFlag);
            }
        }else {
            if (StringUtils.isEmpty(originalFlag)) {
                return "$";
            }else {
                return originalFlag;
            }
        }
    }

    public String getWebStoreName(Long shopId) {
        if (shopId == null) {
            throw new ErpCommonException("店铺id为空");
        }
        Account accounts = accountMapper.selectById(shopId);
        if (accounts != null && StringUtil.isNotEmpty(accounts.getStoreName())) {
            return accounts.getStoreName();
        } else {
            throw new ErpCommonException("浏览器店铺名为空");
        }
    }

    /**
     * 定时获取店铺下的promotion
     *
     * @return
     */
    public List<AccountWithPromotionsList> getAccountWithPromotions() {
        Integer[] state = new Integer[]{
                AmzPromotionsStateEnum.SUBMITTING.value(), AmzPromotionsStateEnum.SUBMIT_EXCEPTION.value(),
                AmzPromotionsStateEnum.CANCELED.value(), AmzPromotionsStateEnum.CANCELING.value(),
                AmzPromotionsStateEnum.MODIFYING.value()
        };
        Date nowDate = com.bizark.op.common.util.DateUtil.UtcToPacificDate(DateUtils.getNowDate());
        List<AccountWithPromotions> accountWithPromotions = amzPromotionsMapper.getAccountWithPromotions(state, nowDate);
        if (CollectionUtil.isEmpty(accountWithPromotions)) {
            return new ArrayList<>();
        }
       /* Date nowDate = com.bizark.op.common.util.DateUtil.UtcToPacificDate(DateUtils.getNowDate());
        Date nowDateAdd = DateUtil.addDate(nowDate, 7);
        List<AccountWithPromotions> accountWithPromotions = accountWithPromotionsList.stream().filter(s ->
                (DateUtil.compareDate(DateUtils.parseDateToStr("yyyy-MM-dd", s.getBeginTime()), DateUtils.parseDateToStr("yyyy-MM-dd", nowDate)) >= 0
                && DateUtil.compareDate(DateUtils.parseDateToStr("yyyy-MM-dd", s.getBeginTime()), DateUtils.parseDateToStr("yyyy-MM-dd", nowDateAdd)) <= 0 )
        ).collect(Collectors.toList());*/
        List<AccountWithPromotionsList> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(accountWithPromotions)) {
            List<Long> collect = accountWithPromotions.stream().map(s -> s.getShopId()).distinct().collect(Collectors.toList());
            for (Long shopId : collect) {
                AccountWithPromotionsList withPromotions = new AccountWithPromotionsList();
                withPromotions.setShopId(shopId);
                List<PromotionsInfo> promotionList = new ArrayList<>();
                accountWithPromotions.stream().filter(s -> shopId.compareTo(s.getShopId()) == 0).forEach(s -> {
                    PromotionsInfo promotionsInfo = new PromotionsInfo();
                    promotionsInfo.setId(s.getId());
                    promotionsInfo.setPromotionId(s.getPromotionId());
                    if (StringUtil.isEmpty(withPromotions.getWebStoreName()) && StringUtil.isNotEmpty(s.getWebStoreName())) {
                        withPromotions.setWebStoreName(s.getWebStoreName());
                    }
                    promotionList.add(promotionsInfo);
                });
                withPromotions.setPromotionInfo(promotionList);
                result.add(withPromotions);
            }
        }
        return result;
    }

    /**
     * 同步amz_promotions
     *
     * @param amzDiscount amz_discount
     * @return
     */
    @Override
    @Transactional
    public AmzPromotions syncSaveAmzPromotions(AmzDiscount amzDiscount, List<AmzDiscountDetailEntity> details, Date nowDate, String flag) {

        if (amzDiscount == null || amzDiscount.getPromotionId() == null) {
            throw new ErpCommonException("amz_discount为空或id为空");
        }
        AmzDiscount amzDiscountFromDb = amzDiscountMapper.selectById(amzDiscount.getId());
        if (amzDiscountFromDb == null) {
            throw new ErpCommonException("库中查无此amz_discount");
        }
        if ("311603793813".equals(amzDiscountFromDb.getPromotionId())) {
            return null;
        }
        //通过组织id,店铺id,promotionsId（promotion后台id）查询是否已有此promotion
        AmzPromotions tempPromotions = new AmzPromotions();
        tempPromotions.setOrganizationId(amzDiscountFromDb.getOrganizationId());
        tempPromotions.setShopId(amzDiscountFromDb.getShopId());
        tempPromotions.setPromotionsId(amzDiscountFromDb.getPromotionId());
        List<AmzPromotions> list = amzPromotionsMapper.selectAmzPromotionsLists(tempPromotions);
        //待更新的amz_promotions
        AmzPromotions promotions = new AmzPromotions();

        setBaseAttr(promotions, amzDiscountFromDb, flag);
        promotions.setSubShopBack(true);
        promotions.setApprovalStatus(AmzPromotionApprovalEnum.APPROVED.getLabel());
        if (CollectionUtil.isNotEmpty(details)) {
            long count = details.stream().filter(s -> s.getGlanceViews() != null).mapToInt(AmzDiscountDetailEntity::getGlanceViews).sum();
            promotions.setGlanceViews(count);
        }
        if (CollectionUtil.isEmpty(list)) {
            setBaseAttr(promotions, amzDiscount, true, nowDate);
            //接口同步过来的新的数据状态如果是取消，那么完结时间赋值创建时间 避免计算促销费出现问题 modified by Buddy @20231129
            if (AmzPromotionsStateEnum.CANCELED.getLabel().equalsIgnoreCase(amzDiscount.getStatus().replaceAll("_", " ").trim())) {

                promotions.setCompleteDate(StringUtil.isNotEmpty(amzDiscount.getConvertCreatedOn()) ? DateUtil.UtcToPacificDate(DateUtils.parseDate(amzDiscount.getConvertCreatedOn(), "yyyy-MM-dd HH:mm:ss")) : null);
                log.error("接口同步到取消状态的数据，后台ID：{}，时间为：{}", promotions.getPromotionsId(), promotions.getCompleteDate());
            }
            amzPromotionsMapper.insert(promotions);
        } else {
            promotions.setId(list.get(0).getId());
            setBaseAttr(promotions, amzDiscount, false, nowDate);
            amzPromotionsMapper.updateById(promotions);
        }
        return promotions;
    }

    private void setBaseAttr(AmzPromotions promotions, AmzDiscount amzDiscount, String flag) {

        promotions.setOrganizationId(amzDiscount.getOrganizationId());
        promotions.setShopId(amzDiscount.getShopId());
        promotions.setPromotionsId(amzDiscount.getPromotionId());
        Account amzShop = accountMapper.selectById(amzDiscount.getShopId());
        promotions.setShopName(amzShop != null ? amzShop.getAccountInit() : null);
        AccountWithCountry accountWithCountry = amzPromotionsMapper.selectAccountWithCountry(amzDiscount.getShopId());
        promotions.setCountry(accountWithCountry != null ? accountWithCountry.getNameCn() : null);
        //促销状态
//        if ("COMMON".equals(flag)) {
//            setPromotionsState(promotions, amzDiscount);
//        }
        promotions.setVendorCode(amzDiscount.getVendorCode());
        promotions.setUnitsSold(amzDiscount.getUnitsSold() != null ? Long.valueOf(amzDiscount.getUnitsSold()) : null);
        promotions.setAmountSpent(amzDiscount.getAmountSpent());
        promotions.setRevenue(amzDiscount.getRevenue());
        //促销类型
        setPromotionsType(promotions, amzDiscount);
        promotions.setFlag("$");
        promotions.setBeginTime(convertAmzDate(amzDiscount.getStartDate()) != null ? DateUtil.UtcToPacificDate(convertAmzDate(amzDiscount.getStartDate())) : null);
        promotions.setEndTime(convertAmzDate(amzDiscount.getEndDate()) != null ? DateUtil.UtcToPacificDate(convertAmzDate(amzDiscount.getEndDate())) : null);
        promotions.setFundingAgreement(stringToLong(amzDiscount.getFundingAgreementId()));
        promotions.setPromotionsName(amzDiscount.getPromotionTitle());
        promotions.setLastUpdateTime(amzDiscount.getLastUpdateTime());
        promotions.setChannel(1);
    }

    private Date convertAmzDate(String amzDate) {
        return StringUtil.isEmpty(amzDate) ? null : DateUtils.parseDate(amzDate.replace("T", " ").replace("Z", ""), "yyyy-MM-dd HH:mm:ss");
    }

    private Long stringToLong(String s) {
        if (StringUtil.isEmpty(s) || "null".equalsIgnoreCase(s)) {
            return null;
        }
        return Long.valueOf(s.trim());
    }

    private void setBaseAttr(BaseEntity baseEntity, AmzDiscount amzDiscount, Boolean create, Date date) {
        if (create) {
            baseEntity.setCreatedAt(StringUtil.isNotEmpty(amzDiscount.getConvertCreatedOn()) ? DateUtil.UtcToPacificDate(DateUtils.parseDate(amzDiscount.getConvertCreatedOn(), "yyyy-MM-dd HH:mm:ss")) : date);
            baseEntity.setCreatedName("System");
            baseEntity.setCreatedBy(amzDiscount.getCreatedBy());
        }
        baseEntity.setUpdatedAt(amzDiscount.getUpdatedAt() != null ? amzDiscount.getUpdatedAt() : date);
        baseEntity.setUpdatedName("System");
        baseEntity.setUpdatedBy(amzDiscount.getUpdatedBy());
    }

    private void setPromotionsState(AmzPromotions promotions, AmzDiscount amzDiscount) {

        String status = amzDiscount.getStatus().replaceAll("_", " ").trim();
        if (AmzPromotionsStateEnum.APPROVED.getLabel().equalsIgnoreCase(status)) {
            promotions.setPromotionsState(AmzPromotionsStateEnum.APPROVED.value());
        } else if (AmzPromotionsStateEnum.PENDING_APPROVAL.getLabel().equalsIgnoreCase(status)) {
            promotions.setPromotionsState(AmzPromotionsStateEnum.PENDING_APPROVAL.value());
        } else if (AmzPromotionsStateEnum.CANCELED.getLabel().equalsIgnoreCase(status)) {
            promotions.setPromotionsState(AmzPromotionsStateEnum.CANCELED.value());
        } else if (AmzPromotionsStateEnum.NEED_YOUR_ATTENTION.getLabel().equalsIgnoreCase(status)) {
            promotions.setPromotionsState(AmzPromotionsStateEnum.NEED_YOUR_ATTENTION.value());
        } else if (AmzPromotionsStateEnum.APPROVED_BUT_NOT_FEATURED.getLabel().equalsIgnoreCase(status)) {
            promotions.setPromotionsState(AmzPromotionsStateEnum.APPROVED_BUT_NOT_FEATURED.value());
        } else if (AmzPromotionsStateEnum.APPROVED_AND_ENDED.getLabel().equalsIgnoreCase(status)) {
            promotions.setPromotionsState(AmzPromotionsStateEnum.APPROVED_AND_ENDED.value());
        } else {
            throw new ErpCommonException("promotion(" + amzDiscount.getPromotionId() + ")同步失败,促销状态(" + amzDiscount.getStatus() + ")不合法");
        }
    }

    private void setPromotionsType(AmzPromotions promotions, AmzDiscount amzDiscount) {
        String promotionType = amzDiscount.getPromotionType().replaceAll("_", " ").trim();
        if (AmzPromotionsTypeEnum.PRICE_DISCOUNT.getLabel().equalsIgnoreCase(promotionType)) {
            promotions.setPromotionsType(AmzPromotionsTypeEnum.PRICE_DISCOUNT.value());
        } else if (AmzPromotionsTypeEnum.LIGHTNING_DEAL.getLabel().equalsIgnoreCase(promotionType)) {
            promotions.setPromotionsType(AmzPromotionsTypeEnum.LIGHTNING_DEAL.value());
        } else if (AmzPromotionsTypeEnum.BEST_DEAL.getLabel().equalsIgnoreCase(promotionType)) {
            promotions.setPromotionsType(AmzPromotionsTypeEnum.BEST_DEAL.value());
        } else if (AmzPromotionsTypeEnum.PROMO_CODE.getLabel().equalsIgnoreCase(promotionType)) {
            promotions.setPromotionsType(AmzPromotionsTypeEnum.PROMO_CODE.value());
        }else if (AmzPromotionsTypeEnum.POINTS_PROMOTION.getLabel().equalsIgnoreCase(promotionType)) {
            promotions.setPromotionsType(AmzPromotionsTypeEnum.POINTS_PROMOTION.value());
        } else {
            throw new ErpCommonException("promotion(" + amzDiscount.getPromotionId() + ")同步失败,促销类型(" + amzDiscount.getPromotionType() + ")不合法");
        }
    }


    /**
     * 提交异常直接提交时间重复校验
     *
     * @param amzPromotions
     * @return
     */
    public boolean submitExceptionDirectSubmitVerifyDate(AmzPromotions amzPromotions) {

        if (amzPromotions == null) {
            throw new ErpCommonException("提交异常直接提交出错，查询无此promotion");
        }
        AmzPromotionsSkuDetail tempSkuDetail = new AmzPromotionsSkuDetail();
        tempSkuDetail.setPromotionsId(amzPromotions.getId());
        List<AmzPromotionsSkuDetail> list = amzPromotionsSkuDetailMapper.selectList(new LambdaQueryWrapper<AmzPromotionsSkuDetail>().eq(AmzPromotionsSkuDetail::getPromotionsId, amzPromotions.getId()));
        AmzPromotionsCreateRequest request1 = new AmzPromotionsCreateRequest();
        request1.setOrganizationId(amzPromotions.getOrganizationId());
        request1.setBeginTime(amzPromotions.getBeginTime());
        request1.setEndTime(amzPromotions.getEndTime());
        request1.setUpdateType(2);
        request1.setId(amzPromotions.getId());
        request1.setShopId(amzPromotions.getShopId());
        request1.setPromotionsType(amzPromotions.getPromotionsType());
        StringBuilder sb = new StringBuilder();
        if (CollectionUtil.isNotEmpty(list)) {
            for (AmzPromotionsSkuDetail skuDetail : list) {
                if (StringUtil.isNotEmpty(skuDetail.getAsin()) && !dateVerify(request1, sb, skuDetail.getAsin())) {
                    return false;
                }
            }
        }
        return true;
    }




    /**
     * 表格导入创建promotion
     *
     * @param file
     * @param entity
     * @param contextId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importExcel(MultipartFile file, UserEntity entity, Integer contextId) {

        Date nowDate = DateUtils.getNowDate();
        AssertUtil.isFalse(file == null, "请上传文件");
        String originalFilename = file.getOriginalFilename();
        AssertUtil.isFalse(StringUtil.isEmpty(originalFilename), "文件名为空");
        String suffix = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
        if (!suffix.equalsIgnoreCase("xlsx") && !suffix.equalsIgnoreCase("xls")) {
            throw new ErpCommonException("请选择excel文件");
        }
        List<AmzPromotionExcel> amzPromotionExcels = ExcelUtils.excelImportFilterAnnotation(file, AmzPromotionExcel.class);
        AssertUtil.isFalse(CollectionUtil.isEmpty(amzPromotionExcels), "表格数据为空");
        StringBuilder sb = new StringBuilder();

        //校验每一行必填项
        for (int s = 0; s < amzPromotionExcels.size(); s++) {
            amzPromotionExcels.get(s).setRowNum(Long.valueOf(s) + 1);
            validateRow(amzPromotionExcels.get(s), sb, contextId);
        }
        AssertUtil.isFalse(StringUtil.isNotEmpty(sb.toString()), sb.toString());
        //校验每一个promotion（多行）的数据重复是否正确
        Map<Long, List<AmzPromotionExcel>> map = amzPromotionExcels.stream().collect(Collectors.groupingBy(AmzPromotionExcel::getNum));
        for (Map.Entry<Long, List<AmzPromotionExcel>> entry : map.entrySet()) {
            List<AmzPromotionExcel> value = entry.getValue();
            validateRowsRepeat(value, sb);
        }
        AssertUtil.isFalse(StringUtil.isNotEmpty(sb.toString()), sb.toString());
        List<AmzPromotionsCreateRequest> promotionRequest = new ArrayList<>();
        IAmzPromotionsService bean = SpringUtils.getBean(IAmzPromotionsService.class);
        for (Map.Entry<Long, List<AmzPromotionExcel>> entry : map.entrySet()) {
            List<AmzPromotionExcel> value = entry.getValue();
            Long key = entry.getKey();
            AmzPromotionsCreateRequest request = new AmzPromotionsCreateRequest();
            setRequestBaseAttr(request, entity, nowDate);
            buildRequest(request, value, contextId);
            try {
                bean.verifyCreatePromotions(request);
            } catch (Exception e) {
                sb.append("序号" + key + e.getMessage() + ";");
                throw new ErpCommonException(sb.toString());
            }

            promotionRequest.add(request);
        }
        List<Long> shopIdList = promotionRequest.stream().map(AmzPromotionsCreateRequest::getShopId).distinct().collect(Collectors.toList());
        Map<Long, List<AmzPromotionDateSet>> shopIdMapDateSet = new HashMap<>();
        shopIdList.forEach(q -> {
            AmzPromotionDateSet amzPromotionDateSet = new AmzPromotionDateSet();
            amzPromotionDateSet.setShopId(q.intValue());
            List<AmzPromotionDateSet> info = amzPromotionDateSetService.getInfo(amzPromotionDateSet, contextId);
            shopIdMapDateSet.put(q, info);
        });
        StringBuilder typeNoSet = new StringBuilder();
        typeNoSet.append("设置按钮中未设置促销类型");
        StringBuilder dateSetError = new StringBuilder();
        dateSetError.append("表格的开始日期不符合设置按钮中促销类型");
        StringBuilder endDateSetError = new StringBuilder();
        endDateSetError.append("表格的结束日期不符合设置按钮中促销类型");
        promotionRequest.forEach(t -> {
            List<AmzPromotionDateSet> dateSets = shopIdMapDateSet.get(t.getShopId());
            AmzPromotionDateSet amzPromotionDateSet = dateSets.stream().filter(q -> q.getPromotionType().equals(t.getPromotionsType())).findFirst().orElse(null);
            if (amzPromotionDateSet == null) {
                typeNoSet.append(AmzPromotionsTypeEnum.labelOf(t.getPromotionsType())).append(",");
            } else {
                Date siteDate = MarPromotionCountryCurrencyEnum.getCurrencyDateByCountryCode(amzPromotionDateSet.getCountry());
                Date actureBeginDate = DateUtil.convertStringToDate(DateUtil.convertDateToString(siteDate, "yyyy-MM-dd"), "yyyy-MM-dd");
                Date excelBeginDate = DateUtil.convertStringToDate(DateUtil.convertDateToString(t.getBeginTime(), "yyyy-MM-dd"), "yyyy-MM-dd");
                Date excelEndDate = DateUtil.convertStringToDate(DateUtil.convertDateToString(t.getEndTime(), "yyyy-MM-dd"), "yyyy-MM-dd");
                if (DateUtil.compareDate(DateUtil.addDate(actureBeginDate, amzPromotionDateSet.getDateSet()), excelBeginDate) > 0) {
                    dateSetError.append(AmzPromotionsTypeEnum.labelOf(t.getPromotionsType())).append(",");
                }
                if (AmzPromotionsTypeEnum.BEST_DEAL.value().equals(t.getPromotionsType()) && amzPromotionDateSet.getEndDateSet() != null) {
                    if (DateUtil.compareDate(excelEndDate, DateUtil.addDate(excelBeginDate, amzPromotionDateSet.getDateSet())) > 0) {
                        endDateSetError.append(AmzPromotionsTypeEnum.labelOf(t.getPromotionsType())).append(",");
                    }
                }
            }
        });
        if (!typeNoSet.toString().equals("设置按钮中未设置促销类型")) {
            typeNoSet.deleteCharAt(typeNoSet.length() - 1);
            throw new ErpCommonException(typeNoSet.toString());
        }
        if (!dateSetError.toString().equals("表格的开始日期不符合设置按钮中促销类型")) {
            dateSetError.deleteCharAt(dateSetError.length() - 1);
            throw new ErpCommonException(dateSetError + "的开始日期条件，请检查");
        }
        if (!endDateSetError.toString().equals("表格的结束日期不符合设置按钮中促销类型")) {
            endDateSetError.deleteCharAt(dateSetError.length() - 1);
            throw new ErpCommonException(endDateSetError + "的结束日期条件，请检查");
        }
        for (AmzPromotionsCreateRequest request : promotionRequest) {
            log.info("表格导入创建promotion,{}", request);
            this.insertAmzPromotions(request);
        }
    }

    public void validateRowsRepeat(List<AmzPromotionExcel> value, StringBuilder sb) {
        //同一个promotion下，店铺，促销类型，Vendor Code,Billing contact,开始日期，（结束日期如果有），需要相同，sellerSku不允许相同
        AmzPromotionExcel amzPromotionExcel = value.get(0);
        int size = value.size();
        String shopName = amzPromotionExcel.getShopName();
        String promotionsType = amzPromotionExcel.getPromotionsType();
        String vendorCode = amzPromotionExcel.getVendorCode();
        String billingContact = amzPromotionExcel.getBillingContact();
        Date beginTime = amzPromotionExcel.getBeginTime();
        Date endTime = amzPromotionExcel.getEndTime();
        String fixDiscount = amzPromotionExcel.getFixDiscount() != null ? amzPromotionExcel.getFixDiscount().toString() : null;
        String discountRadio = amzPromotionExcel.getDiscountRadio() != null ? amzPromotionExcel.getDiscountRadio().toString() : null;
        String budget = amzPromotionExcel.getBudget() != null ? amzPromotionExcel.getBudget().toString() : null;
        String share = amzPromotionExcel.getShare();
        String redemptions = amzPromotionExcel.getRedemptions();
        String code = amzPromotionExcel.getCode();
        long shopNameRepeatNum = value.stream().filter(s -> shopName.equalsIgnoreCase(s.getShopName())).count();
        long promotionsTypeRepeatNum = value.stream().filter(s -> promotionsType.equalsIgnoreCase(s.getPromotionsType())).count();
        long vendorCodeRepeatNum = value.stream().filter(s -> vendorCode.equalsIgnoreCase(s.getVendorCode())).count();
        long billingContactRepeatNum = value.stream().filter(s -> billingContact.equalsIgnoreCase(s.getBillingContact())).count();
        long beginTimeRepeatNum = value.stream().filter(s -> DateUtil.compareDate(beginTime, s.getBeginTime()) == 0).count();

        addRepeatAttention(amzPromotionExcel.getNum(), shopNameRepeatNum, size, sb, "店铺");
        addRepeatAttention(amzPromotionExcel.getNum(), promotionsTypeRepeatNum, size, sb, "促销类型");
        addRepeatAttention(amzPromotionExcel.getNum(), vendorCodeRepeatNum, size, sb, "Vendor Code");
        addRepeatAttention(amzPromotionExcel.getNum(), billingContactRepeatNum, size, sb, "Billing Contact");
        addRepeatAttention(amzPromotionExcel.getNum(), beginTimeRepeatNum, size, sb, "开始时间");

        if (endTime != null && !AmzPromotionsTypeEnum.LIGHTNING_DEAL.value().equals(getPromotionType(promotionsType))) {
            long endTimeRepeatNum = value.stream().filter(s -> DateUtil.compareDate(endTime, s.getEndTime()) == 0).count();
            addRepeatAttention(amzPromotionExcel.getNum(), endTimeRepeatNum, size, sb, "结束时间");
        }
        if (AmzPromotionsTypeEnum.PROMO_CODE.value().equals(getPromotionType(promotionsType))) {
            long budgetRepeatNum = value.stream().filter(s -> budget.equalsIgnoreCase(s.getBudget() != null ? s.getBudget().toString() : null)).count();
            long shareRepeatNum = value.stream().filter(s -> share.equalsIgnoreCase(s.getShare())).count();
            long redemptionsRepeatNum = value.stream().filter(s -> redemptions.equalsIgnoreCase(s.getRedemptions())).count();
            long codeRepeatNum = value.stream().filter(s -> code.equalsIgnoreCase(s.getCode())).count();
            addRepeatAttention(amzPromotionExcel.getNum(), budgetRepeatNum, size, sb, "budget");
            addRepeatAttention(amzPromotionExcel.getNum(), shareRepeatNum, size, sb, "是否共享");
            addRepeatAttention(amzPromotionExcel.getNum(), redemptionsRepeatNum, size, sb, "领取限制");
            addRepeatAttention(amzPromotionExcel.getNum(), codeRepeatNum, size, sb, "code");
            if (StringUtil.isNotEmpty(fixDiscount)) {
                long fixDiscountRepeatNum = value.stream().filter(s -> fixDiscount.equalsIgnoreCase(s.getFixDiscount() != null ? s.getFixDiscount().toString() : null)).count();
                addRepeatAttention(amzPromotionExcel.getNum(), fixDiscountRepeatNum, size, sb, "折扣固定值");
            } else {
                long discountRadioRepeatNum = value.stream().filter(s -> discountRadio.equalsIgnoreCase(s.getDiscountRadio() != null ? s.getDiscountRadio().toString() : null)).count();
                addRepeatAttention(amzPromotionExcel.getNum(), discountRadioRepeatNum, size, sb, "折扣比例");
            }
        }

        for (int i = 0; i < value.size(); i++) {
            for (int j = i + 1; j < value.size(); j++) {
                if (value.get(i).getSellerSku().equalsIgnoreCase(value.get(j).getSellerSku())) {
                    sb.append("序号 " + amzPromotionExcel.getNum() + " 第 " + (i + 1) + " 个sellerSku" + "和第 " + (j + 1) + " 个sellerSku相同;");
                }
            }
        }

    }

    public void addRepeatAttention(long num, long repeatNum, int size, StringBuilder sb, String s) {
        if (repeatNum != size) {
            sb.append("序号" + num + "每一行的" + s + "需一致;");
        }
    }

    public void buildRequest(AmzPromotionsCreateRequest request, List<AmzPromotionExcel> value, Integer contextId) {

        AmzPromotionExcel amzPromotionExcel = value.get(0);
        String title = amzPromotionExcel.getShopName();

        List<Account> accounts = accountMapper.selectList(Wrappers.<Account>lambdaQuery().eq(Account::getTitle, title).eq(Account::getOrgId, contextId));
        AccountWithCountry accountWithCountry = amzPromotionsMapper.selectAccountWithCountry(accounts.get(0).getId().longValue());
        request.setRemark(amzPromotionExcel.getRemark());
        request.setOrganizationId(contextId);
        request.setChannel(1);
        request.setShopName(accounts.get(0).getAccountInit());
        request.setShopId(Long.valueOf(accounts.get(0).getId()));
        request.setPromotionsType(getPromotionType(amzPromotionExcel.getPromotionsType()));
        if (!MarPromotionCountryCurrencyEnum.JP.getCountryCode().equalsIgnoreCase(accountWithCountry.getCountryCode()) && AmzPromotionsTypeEnum.POINTS_PROMOTION.value().equals(request.getPromotionsType())) {
            throw new ErpCommonException(String.format("序号:[%s]仅日本店铺才可以选择Points promotion类型", value.get(0).getNum()));
        }
        request.setVendorCode(amzPromotionExcel.getVendorCode());
        request.setBillingContact(amzPromotionExcel.getBillingContact());
        request.setBeginTime(amzPromotionExcel.getBeginTime());
        request.setEndTime(amzPromotionExcel.getEndTime());
        request.setFlag(MarPromotionCountryCurrencyEnum.getCurrencyFlagByCountryCode(accountWithCountry.getCountryCode() , request.getPromotionsType()));
        if (AmzPromotionsTypeEnum.PROMO_CODE.value().equals(request.getPromotionsType())) {
            String fixDiscount = amzPromotionExcel.getFixDiscount() != null ? amzPromotionExcel.getFixDiscount().toString() : null;
            String discountRadio = amzPromotionExcel.getDiscountRadio() != null ? amzPromotionExcel.getDiscountRadio().toString() : null;
            if (StringUtil.isNotEmpty(fixDiscount)) {
                request.setDiscountFlag(request.getFlag());
                request.setDiscount(new BigDecimal(fixDiscount));
            } else {
                request.setDiscountFlag("%");
                request.setDiscount(new BigDecimal(discountRadio));
            }
            request.setBudget(amzPromotionExcel.getBudget());
            request.setShare("是".equalsIgnoreCase(amzPromotionExcel.getShare()) ? 1 : 2);
            Integer redemptions = getRedemptions(amzPromotionExcel.getRedemptions());
            if (Arrays.stream(AmzPromotionsRedemptionsEnum.values()).map(AmzPromotionsRedemptionsEnum::value).collect(Collectors.toList()).contains(redemptions)) {
                request.setRedemptions(redemptions);
            } else {
                throw new ErpCommonException("序号: " + amzPromotionExcel.getNum() + " 领取限制输入有误");
            }

            request.setCode(amzPromotionExcel.getCode());
        }

        ArrayList<AmzPromotionsSkuDetail> skuDetailList = new ArrayList<>();
        List<String> skuList = value.stream().filter(s -> StringUtil.isNotEmpty(s.getSellerSku())).map(AmzPromotionExcel::getSellerSku).collect(Collectors.toList());
        List<AmzPromotionsSkuDetail> listWithPrice = amzPromotionAccountService.selectPromotionSellerSkuByShopId(accounts.get(0).getId(), skuList.toArray(new String[skuList.size()]));
        for (AmzPromotionExcel promotionExcel : value) {

            AmzPromotionsSkuDetail detail = new AmzPromotionsSkuDetail();
            List<AmzPromotionsSkuDetail> collect = listWithPrice.stream().filter(s -> promotionExcel.getSellerSku().equalsIgnoreCase(s.getSellerSku())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(collect)) {
                throw new ErpCommonException("序号: " + amzPromotionExcel.getNum() + " 商品: " + promotionExcel.getSellerSku() + " 不存在");
            }
            AmzPromotionsSkuDetail skuDetail = collect.get(0);

            BeanUtils.copyProperties(skuDetail, detail);
            //折扣
            setDiscount(detail, promotionExcel);
            if (AmzPromotionsTypeEnum.LIGHTNING_DEAL.value().equals(request.getPromotionsType()) || AmzPromotionsTypeEnum.BEST_DEAL.value().equals(request.getPromotionsType())) {
                detail.setNum(promotionExcel.getSkuNum());
            }

            skuDetailList.add(detail);
        }
        if (AmzPromotionsTypeEnum.BEST_DEAL.value().equals(request.getPromotionsType())) {
            LambdaQueryWrapper<AmzPromotionsDetailExtraPrice> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AmzPromotionsDetailExtraPrice::getShopId, request.getShopId())
                    .eq(AmzPromotionsDetailExtraPrice::getPromotionsType, request.getPromotionsType())
                    .in(AmzPromotionsDetailExtraPrice::getAsin, skuDetailList.stream().map(AmzPromotionsSkuDetail::getAsin).filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
            List<AmzPromotionsDetailExtraPrice> extraPriceList = amzPromotionsDetailExtraPriceService.list(queryWrapper);
            skuDetailList.forEach(promotionsSkuDetail -> {
                AmzPromotionsDetailExtraPrice amzPromotionsDetailExtraPrice = CollectionUtil.isEmpty(extraPriceList) ? null : extraPriceList.stream().filter(t -> promotionsSkuDetail.getAsin().equals(t.getAsin())).max(Comparator.comparing(AmzPromotionsDetailExtraPrice::getUpdatedAt)).orElse(null);
                if (amzPromotionsDetailExtraPrice != null) {
                    promotionsSkuDetail.setReferencePrice(amzPromotionsDetailExtraPrice.getReferencePrice());
                    promotionsSkuDetail.setMaxDiscountPrice(amzPromotionsDetailExtraPrice.getMaxDiscountPrice());
                    promotionsSkuDetail.setMixPerUintFunding(amzPromotionsDetailExtraPrice.getMixPerUintFunding());
                    promotionsSkuDetail.setCppu(amzPromotionsDetailExtraPrice.getCppu());
                    promotionsSkuDetail.setReferencePriceUpdateDate(amzPromotionsDetailExtraPrice.getUpdatedAt());
                }
            });
        }
        request.setSkuDetailList(skuDetailList);
    }

    public Integer getRedemptions(String redemptions) {

        if ("一个订单一次".equalsIgnoreCase(redemptions.trim())) {
            return AmzPromotionsRedemptionsEnum.ONE_UNIT_IN_ONE_ORDER.value();
        } else if ("一个订单不限次".equalsIgnoreCase(redemptions.trim())) {
            return AmzPromotionsRedemptionsEnum.UNLIMITED_UNITS_IN_ONE_ORDER.value();
        } else if ("不限订单不限次".equalsIgnoreCase(redemptions.trim())) {
            return AmzPromotionsRedemptionsEnum.UNLIMITED_UNITS_IN_UNLIMITED_ORDERS.value();
        } else {
            return -1;
        }
    }

    public Integer getPromotionType(String promotionsType) {
        if (AmzPromotionsTypeEnum.PRICE_DISCOUNT.getLabel().equalsIgnoreCase(promotionsType.trim())) {
            return AmzPromotionsTypeEnum.PRICE_DISCOUNT.value();
        } else if (AmzPromotionsTypeEnum.LIGHTNING_DEAL.getLabel().equalsIgnoreCase(promotionsType.trim())) {
            return AmzPromotionsTypeEnum.LIGHTNING_DEAL.value();
        } else if (AmzPromotionsTypeEnum.BEST_DEAL.getLabel().equalsIgnoreCase(promotionsType.trim())) {
            return AmzPromotionsTypeEnum.BEST_DEAL.value();
        } else if (AmzPromotionsTypeEnum.PROMO_CODE.getLabel().equalsIgnoreCase(promotionsType.trim())) {
            return AmzPromotionsTypeEnum.PROMO_CODE.value();
        }else if (AmzPromotionsTypeEnum.POINTS_PROMOTION.getLabel().equalsIgnoreCase(promotionsType.trim())) {
            return AmzPromotionsTypeEnum.POINTS_PROMOTION.value();
        } else {
            return -1;
        }
    }

    public void setDiscount(AmzPromotionsSkuDetail detail, AmzPromotionExcel promotionExcel) {

        detail.setDiscountPrice(promotionExcel.getDiscountPrice());
        if (promotionExcel.getFixDiscount() != null) {
            detail.setDiscount(promotionExcel.getFixDiscount());
            detail.setDiscountFlag("$");
            /*if (detail.getFrontPrice() != null) {
                detail.setDiscountPrice(detail.getFrontPrice().subtract(promotionExcel.getFixDiscount()));
            }*/
        } else {
            detail.setDiscount(promotionExcel.getDiscountRadio());
            detail.setDiscountFlag("%");
            /*if (detail.getFrontPrice() != null) {
                detail.setDiscountPrice(detail.getFrontPrice().multiply(new BigDecimal("100").subtract(promotionExcel.getDiscountRadio())).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP));
            }*/
        }

    }

    public void setRequestBaseAttr(AmzPromotionsCreateRequest amzPromotionsCreateRequest, UserEntity entity, Date nowDate) {

        amzPromotionsCreateRequest.setUpdateType(1);
        amzPromotionsCreateRequest.setCreatedBy(entity.getId());
        amzPromotionsCreateRequest.setCreatedName(entity.getName());
        amzPromotionsCreateRequest.setCreatedAt(nowDate);
        amzPromotionsCreateRequest.setUpdatedBy(entity.getId());
        amzPromotionsCreateRequest.setUpdatedName(entity.getName());
        amzPromotionsCreateRequest.setUpdatedAt(nowDate);
    }

    public void validateRow(AmzPromotionExcel s, StringBuilder sb, Integer contextId) {

        List<AmzPromotionsSkuDetail> listWithPrice = new ArrayList<>();
        //1.店铺必须存在且启用
        Long rowNum = s.getRowNum();
        String shopName = s.getShopName();

        Integer promotionType = getPromotionType(s.getPromotionsType());
        List<Account> accounts = accountMapper.selectList(Wrappers.<Account>lambdaQuery()
                .eq(Account::getTitle, shopName)
                .eq(Account::getAccountType, AmzPromotionsChannelEnum.AMAZON_VC.getLabel())
                .eq(Account::getType, "amazonvendords")
                .isNull(Account::getIsDelete)
                .isNotNull(Account::getStoreName)
                .ne(Account::getStoreName, ""));
        if (CollectionUtil.isEmpty(accounts)) {
            sb.append("第 " + rowNum + " 行序号:" + s.getNum() + " 店铺:" + shopName + " 不存在或未启用;");
        } else {
            List<PromotionAccountVo> promotionAccountVos = this.selectPromotionAccountList(1, new Integer[]{accounts.get(0).getId()}, contextId);

            if (CollectionUtil.isEmpty(promotionAccountVos)) {
                sb.append("第 " + rowNum + " 行序号: " + s.getNum() + " Vendor Code与Billing Contact在店铺系统中不存在或不符合条件");
            }else{
                s.setVendorCode(promotionAccountVos.get(0).getVendorCode());
                s.setBillingContact(promotionAccountVos.get(0).getBillingContact());
                //TODO 店铺有多个，取一个
                if ("SW-JP-VC-DF-VC-DF-JP".equals(shopName)) {
                    s.setVendorCode("IMI9B");
                }
            }

            //sellerSku需要在该店铺下在售
            List<AmzPromotionsSkuDetail> skuInfoVOS = amzPromotionAccountService.selectPromotionSellerSkuByShopId(accounts.get(0).getId(), new String[]{s.getSellerSku()});
            if (CollectionUtil.isEmpty(skuInfoVOS)) {
                sb.append("第 " + rowNum + " 行序号:" + s.getNum() + " sellerSku:" + s.getSellerSku() + " 需要在店铺:" + shopName + " 下在售");
            } else {
                listWithPrice = amzPromotionAccountService.selectPromotionSellerSkuByShopId(accounts.get(0).getId(), new String[]{s.getSellerSku()});
            }
        }

        //结束时间必填
        if (!AmzPromotionsTypeEnum.LIGHTNING_DEAL.value().equals(promotionType)) {
            if (s.getEndTime() == null) {
                sb.append("第 " + rowNum + " 行序号:" + s.getNum() + " 结束时间必填;");
            }
        }
        //折扣固定值或者比例必须填其一
        String fixDiscount = s.getFixDiscount() != null ? s.getFixDiscount().toString() : null;
        String discountRadio = s.getDiscountRadio() != null ? s.getDiscountRadio().toString() : null;
        boolean disCountTrue = true;
        if ((StringUtil.isEmpty(fixDiscount) && StringUtil.isEmpty(discountRadio)) || (StringUtil.isNotEmpty(fixDiscount) && StringUtil.isNotEmpty(discountRadio))) {
            sb.append("第 " + rowNum + " 行序号:" + s.getNum() + " 折扣固定值或者比例必须填其一;");
            disCountTrue = false;
        }
        if (AmzPromotionsTypeEnum.PRICE_DISCOUNT.value().equals(promotionType)) {
            discountRadioBigValue(disCountTrue, fixDiscount, discountRadio, listWithPrice, "15", sb, rowNum, s.getNum());
        } else if (AmzPromotionsTypeEnum.LIGHTNING_DEAL.value().equals(promotionType)) {
            discountRadioBigValue(disCountTrue, fixDiscount, discountRadio, listWithPrice, "15", sb, rowNum, s.getNum());
        } else if (AmzPromotionsTypeEnum.BEST_DEAL.value().equals(promotionType)) {
            if (StringUtils.isEmpty(fixDiscount)) {
                sb.append("第 " + rowNum + " 行序号:" + s.getNum() + " 折扣固定值不能为空;");
            }
//            discountRadioBigValue(disCountTrue, fixDiscount, discountRadio, listWithPrice, "10", sb, rowNum, s.getNum());
        } else if (AmzPromotionsTypeEnum.PROMO_CODE.value().equals(promotionType)) {
            discountRadioBigValue(disCountTrue, fixDiscount, discountRadio, listWithPrice, "10", sb, rowNum, s.getNum());
            valueNeed(s.getBudget() != null ? s.getBudget().toString() : null, rowNum, s.getNum(), "预算", sb);
            valueNeed(s.getShare(), rowNum, s.getNum(), "是否共享", sb);
            if (StringUtil.isNotEmpty(s.getShare()) && !"是".equalsIgnoreCase(s.getShare()) && !"否".equalsIgnoreCase(s.getShare())) {
                sb.append("第 " + rowNum + " 行序号:" + s.getNum() + " 是否共享输入有误;");
            }
            valueNeed(s.getRedemptions(), rowNum, s.getNum(), "领取限制", sb);
            List<Integer> redemptionsList = Arrays.stream(AmzPromotionsRedemptionsEnum.values()).map(AmzPromotionsRedemptionsEnum::value).collect(Collectors.toList());
            if (StringUtil.isNotEmpty(s.getRedemptions()) && !redemptionsList.contains(getRedemptions(s.getRedemptions()))) {
                sb.append("第 " + rowNum + " 行序号:" + s.getNum() + " 是否共享输入有误;");
            }
            valueNeed(s.getCode(), rowNum, s.getNum(), "code", sb);
            String pattern = "[0-9a-zA-Z]{3,12}";
            if (StringUtil.isNotEmpty(s.getCode()) && !s.getCode().matches(pattern)) {
                sb.append("第 " + rowNum + " 行序号:" + s.getNum() + " code只能为3-12位的数字和英文字符;");
            }
        }else if (AmzPromotionsTypeEnum.POINTS_PROMOTION.value().equals(promotionType)){
            discountRadioBigValueJpy(disCountTrue, fixDiscount, discountRadio, listWithPrice, sb, rowNum, s.getNum());
        } else {
            sb.append("第 " + rowNum + " 行序号:" + s.getNum() + " 促销类型错误;");
        }
    }

    public boolean vendorCodeMatchBilling(String shopName, String billingContact) {
        return billingContact.equalsIgnoreCase(shopMapBillingContact.get(shopName));
    }

    public void valueNeed(String column, Long rowNum, Long num, String value, StringBuilder sb) {
        if (StringUtil.isEmpty(column)) {
            sb.append("第 " + rowNum + " 行序号:" + num + " " + value + " 必填;");
        }
    }

    public void discountRadioBigValue(boolean disCountTrue, String fixDiscount, String discountRadio, List<AmzPromotionsSkuDetail> listWithPrice, String number, StringBuilder sb, Long rowNum, Long num) {

        if (disCountTrue) {
            BigDecimal bigDecimal = BigDecimal.ZERO;
            if (StringUtil.isNotEmpty(fixDiscount)) {

            } else {
                bigDecimal = bigDecimal.add(stringToBigDecimal(discountRadio));
                if (bigDecimal.compareTo(BigDecimal.ZERO) <= 0 || bigDecimal.compareTo(new BigDecimal("80")) > 0) {
                    sb.append("第 " + rowNum + " 行序号:" + num + " 折扣比例需大于0小于等于80%;");
                }
            }

        }
    }


    public void discountRadioBigValueJpy(boolean disCountTrue, String fixDiscount, String discountRadio, List<AmzPromotionsSkuDetail> listWithPrice, StringBuilder sb, Long rowNum, Long num) {

        if (disCountTrue) {
            BigDecimal bigDecimal = BigDecimal.ZERO;
            if (StringUtil.isNotEmpty(fixDiscount)) {
                bigDecimal = bigDecimal.add(stringToBigDecimal(fixDiscount));

                if (CollectionUtil.isNotEmpty(listWithPrice)) {
                    BigDecimal frontPriceFb = listWithPrice.get(0).getFrontPrice();
                    if (frontPriceFb != null && BigDecimal.ZERO.compareTo(frontPriceFb) != 0) {
                        if (bigDecimal.multiply(new BigDecimal("100")).compareTo(frontPriceFb) < 0 || bigDecimal.multiply(new BigDecimal("2")).compareTo(frontPriceFb) > 0) {
                            sb.append("第 " + rowNum + " 行序号:" + num + " Pt限制数值为前台价格的1%-50%之间");
                        }
                    }
                }

            } else {
                bigDecimal = bigDecimal.add(stringToBigDecimal(discountRadio));
                if (bigDecimal.compareTo(new BigDecimal("1")) < 0 || bigDecimal.compareTo(new BigDecimal("50")) > 0) {
                    sb.append("第 " + rowNum + " 行序号:" + num + " 折扣比例需大于等于1%小于等于50%;");
                }
            }

        }
    }

    public BigDecimal stringToBigDecimal(String s) {
        if (StringUtil.isEmpty(s)) {
            return null;
        }
        return new BigDecimal(s.replaceAll("[^\\d.]","").replace(",", ""));
    }


    /**
     * 获取asin最新前台价格
     *
     * @param asin
     * @param shopId
     * @return
     */
    @Override
    public List<AmzPromotionsSkuDetail> getAsinFrontPrice(String[] asin, Integer shopId) {

        log.info("获取asin最新前台价格--{},{}", asin, shopId);
        List<AmzPromotionsSkuDetail> skuDetailList = new ArrayList<>();
        JSONObject object = new JSONObject();
        object.put("asin", asin);
        AccountWithCountry accountWithCountry = amzPromotionsMapper.selectAccountWithCountry(shopId.longValue());
        object.put("country", accountWithCountry.getCountryCode());
        try {
            String s = HttpUtils.doPostByJson("http://43.132.231.66:2345/spider/amazon/listing", JSONObject.toJSONString(object));
            skuDetailList = JSONObject.parseArray(s, AmzPromotionsSkuDetail.class);
        } catch (Exception e) {
            log.error("获取asin最新前台价格失败：{}",e.getMessage());
            throw new RuntimeException(e);
        }
        if (CollectionUtil.isNotEmpty(skuDetailList)) {
            List<AmzPromotionsSkuDetail> collect = skuDetailList.stream().filter(s -> s.getPrice() != null).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)) {
                collect.forEach(s -> {
                    s.setFrontPrice(s.getPrice());
                    s.setCountry(accountWithCountry.getCountryCode());
                });
                iAmzPromotionAsyncService.updateMarListingInfoCarPrice(collect);
            }
            return collect;
        } else {
            return skuDetailList;
        }
    }

    private void dateModifyLog(AmzPromotions originalPromotions, AmzPromotions amzPromotions, StringBuilder logDetail) {

        if (DateUtil.compareDate(cn.hutool.core.date.DateUtil.beginOfDay(originalPromotions.getBeginTime()), cn.hutool.core.date.DateUtil.beginOfDay(amzPromotions.getBeginTime())) != 0) {
            logDetail.append(" 开始时间: ").append(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", originalPromotions.getBeginTime())).append("修改为: ").append(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", amzPromotions.getBeginTime())).append(";");
        }
        if (originalPromotions.getEndTime() != null && amzPromotions.getEndTime() == null) {
            logDetail.append(" 结束时间: ").append(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", originalPromotions.getEndTime())).append("修改为空; ");
        }
        if (originalPromotions.getEndTime() == null && amzPromotions.getEndTime() != null) {
            logDetail.append(" 结束时间空: ").append("修改为: ").append(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", amzPromotions.getEndTime())).append(";");
        }
        if (originalPromotions.getEndTime() != null && amzPromotions.getEndTime() != null && DateUtil.compareDate(cn.hutool.core.date.DateUtil.beginOfDay(originalPromotions.getEndTime()), cn.hutool.core.date.DateUtil.beginOfDay(amzPromotions.getEndTime())) != 0) {
            logDetail.append(" 结束时间: ").append(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", originalPromotions.getEndTime())).append("修改为: ").append(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", amzPromotions.getEndTime())).append(";");
        }
    }


    /**
     * 根据运营id查询asin,sellerSku
     *
     * @param operatorId
     * @param orgId
     * @return
     */
    @Override
    public Map<String, String> selectProductChannels(Integer[] operatorId, Integer orgId) {

        List<ProductChannels> productChannels = productChannelsMapper.selectList(Wrappers.<ProductChannels>lambdaQuery().eq(ProductChannels::getOrgId, orgId).in(ProductChannels::getOperationUserId, operatorId));
        if (CollectionUtil.isNotEmpty(productChannels)) {
            Map<String, List<ProductChannels>> collect = productChannels.stream().filter(r -> StringUtil.isNotEmpty(r.getAsin()) && StringUtil.isNotEmpty(r.getSellerSku())).collect(Collectors.groupingBy(ProductChannels::getAsin));
            if (CollectionUtil.isNotEmpty(collect)) {
                Map<String, String> result = new HashMap<>();
                for (Map.Entry<String, List<ProductChannels>> entry : collect.entrySet()) {
                    String key = entry.getKey();
                    List<ProductChannels> value = entry.getValue();
                    String sellerSkuArray = value.stream().map(ProductChannels::getSellerSku).collect(Collectors.joining(","));
                    result.put(key, sellerSkuArray);
                }
                return result;
            }
            return null;
        }
        return null;
    }


    public ApiResponseResult submitExceptionDirectSubmit(AmzPromotions promotions, Date siteDate) {

        if (AmzPromotionsTypeEnum.LIGHTNING_DEAL.value().equals(promotions.getPromotionsType())) {
            /*String nowDateAdd25 = DateUtil.addDate(DateUtils.formatDate(DateUtil.UtcToPacificDate(DateUtils.getNowDate()), "yyyy-MM-dd"), 25);
            String beginTimeStr = DateUtils.formatDate(promotions.getBeginTime(), "yyyy-MM-dd");
            if (!(DateUtil.compareDate(beginTimeStr, nowDateAdd25) >= 0)) {
                return ApiResponseResult.buildFailureResult("Lightning Deal类型开始日期可选日期范围为大于等于当前站点日期+25天。");
            }*/
        }
        if (!AmzPromotionsTypeEnum.LIGHTNING_DEAL.value().equals(promotions.getPromotionsType())) {
            if (promotions.getEndTime() == null) {
                return ApiResponseResult.buildFailureResult("结束日期不能为空");
            }
            if (DateUtil.compareDate(promotions.getBeginTime(), promotions.getEndTime()) > 0) {
                return ApiResponseResult.buildFailureResult("开始日期不能大于结束日期");
            }
            if (AmzPromotionsTypeEnum.PRICE_DISCOUNT.value().equals(promotions.getPromotionsType())) {
                String nowDateAdd1 = DateUtil.addDate(DateUtils.formatDate(siteDate, "yyyy-MM-dd"), 1);
                String beginTimeStr = DateUtils.formatDate(promotions.getBeginTime(), "yyyy-MM-dd");
                if (!(DateUtil.compareDate(beginTimeStr, nowDateAdd1) > 0)) {
                    return ApiResponseResult.buildFailureResult("Price discount类型开始日期可选日期范围为大于当前站点日期+1天。");
                }
                if (DateUtil.getBetweenYearNumberBig(promotions.getBeginTime(), promotions.getEndTime()).compareTo(new BigDecimal(1)) > 0) {
                    return ApiResponseResult.buildFailureResult("开始日期结束日期时间跨度最大可选择一年");
                }
            }
            //Best Deal类型： 可选日期范围为 大于 当前站点日期+1天，如果开始日期为等于当前站点日期+2天，则结束日期必须小于等于开始日期+14天，否则结束日期必须小于等于开始日期+15天
            if (AmzPromotionsTypeEnum.BEST_DEAL.value().equals(promotions.getPromotionsType())) {
                String nowPstDate = DateUtils.formatDate(siteDate, "yyyy-MM-dd");
                String beginTimeStr = DateUtils.formatDate(promotions.getBeginTime(), "yyyy-MM-dd");
                String endTimeStr = DateUtils.formatDate(promotions.getEndTime(), "yyyy-MM-dd");
                if (!(DateUtil.compareDate(beginTimeStr, DateUtil.addDate(nowPstDate, 1)) > 0)) {
                    return ApiResponseResult.buildFailureResult("Best Deal类型开始日期可选日期范围为大于当前站点日期+1天。");
                }
                if (!(DateUtil.compareDate(endTimeStr, DateUtil.addDate(beginTimeStr, 29)) <= 0)) {
                    return ApiResponseResult.buildFailureResult("结束日期必须小于等于开始日期+30天");
                }
            }
            if (AmzPromotionsTypeEnum.PROMO_CODE.value().equals(promotions.getPromotionsType())) {
                String nowDateAdd1 = DateUtil.addDate(DateUtils.formatDate(DateUtil.UtcToPacificDate(DateUtils.getNowDate()), "yyyy-MM-dd"), 1);
                String beginTimeStr = DateUtils.formatDate(promotions.getBeginTime(), "yyyy-MM-dd");
                if (!(DateUtil.compareDate(beginTimeStr, nowDateAdd1) >= 0)) {
                    return ApiResponseResult.buildFailureResult("Promo code类型开始日期可选日期范围为大于等于当前站点日期+1天。");
                }
                if (DateUtil.getBetweenDayNumber(promotions.getBeginTime(), promotions.getEndTime()) > 119) {
                    return ApiResponseResult.buildFailureResult("开始日期结束日期时间跨度最大可选择120天");
                }
            }
        }
        return ApiResponseResult.buildSuccessResult();
    }


    public List<String[]> buildString(Integer contextId) {
        List<String[]> downData = new ArrayList<>();
        List<Account> accounts = accountService.lambdaQuery()
                .select(Account::getTitle, Account::getId)
                .eq(Account::getOrgId, contextId)
                .eq(Account::getAccountType, "Amazon_VC")
                .eq(Account::getType, "amazonvendords")
                .isNotNull(Account::getTitle)
                .ne(Account::getTitle, "")
                .isNotNull(Account::getStoreName)
                .ne(Account::getStoreName, "")
                .list();
        String[] account = accounts.stream().map(Account::getTitle).collect(Collectors.toList()).toArray(new String[accounts.size()]);
        downData.add(account);
        downData.add(AmzPromotionsTypeEnum.getAllLabel());

        downData.add(new String[]{"是", "否"});
        downData.add(new String[]{"是", "否"});
        downData.add(new String[]{"一个订单一次", "一个订单不限次", "不限订单不限次"});

        return downData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertPromotionLog(Long id, Integer operateBy, Date operateDate, String operateName, Integer operateType, String result, String detail, Integer contextId, String remark) {

        AmzPromotionsOperateLog log = new AmzPromotionsOperateLog();
        log.setPromotionId(id);
        log.setCreatedBy(operateBy);
        log.setCreatedAt(operateDate);
        log.setCreatedName(operateName);
        log.setOperateType(operateType);
        log.setResult(result);
//        log.setDetail(detail);
        log.setOrganizationId(contextId);
        log.setRemark(remark);
        if (StringUtil.isNotEmpty(detail)) {
            if (StringUtil.isNotEmpty(remark)) {
                detail = detail + " 备注:" + remark;
            }
        } else {
            detail = remark;
        }
        log.setDetail(detail);
        iAmzPromotionsOperateLogService.save(log);
    }


    @Override
    public boolean findAsinBySku(AmzPromotionsQueryRequest request) {
        /*Integer orgType = request.getOrgType();
        String[] orgValue = request.getOrgValue();
        Integer contextId = request.getContextId();
        if (orgType != null && orgType == 3 && ObjectUtil.isNotEmpty(orgValue)) {
            //根据sku查询对应的asin
            List<AmzPromotionCheck> amzPromotionChecks = amzPromotionCheckMapper.selectListBySku(orgValue, contextId);
            if (CollectionUtil.isEmpty(amzPromotionChecks)) {
                return false;
            }
            //sku查询转换为sellerSku查询
            request.setOrgType(2);
            String[] array = amzPromotionChecks.stream().map(AmzPromotionCheck::getSellerSku).toArray(String[]::new);
            request.setOrgValue(array);
            request.setSkuQuery(1);
        } else if (orgType != null && orgType == 4 && ObjectUtil.isNotEmpty(orgValue)) {
            //根据父asin查询子asin
            List<AmzPromotionCheck> amzPromotionChecks = amzPromotionCheckMapper.selectListByParentAsin(orgValue, contextId);
            if (CollectionUtil.isEmpty(amzPromotionChecks)) {
                return false;
            }
            request.setOrgType(1);
            String[] array = amzPromotionChecks.stream().map(AmzPromotionCheck::getAsin).toArray(String[]::new);
            request.setOrgValue(array);
            request.setParentAsinQuery(1);
        } else if (orgType != null && orgType == 5 && ObjectUtil.isNotEmpty(orgValue)) {
            //根据品牌获取asin
            LambdaQueryWrapper<ProductChannels> queryWrapper = Wrappers.lambdaQuery(ProductChannels.class).isNotNull(ProductChannels::getAsin).ne(ProductChannels::getAsin, "");
            List<ProductChannels> productChannels = productChannelsMapper.selectList(orgValue.length == 1 ? queryWrapper.like(ProductChannels::getBrand, orgValue[0]) : queryWrapper.in(ProductChannels::getBrand, Arrays.asList(orgValue)));
            if (CollectionUtil.isEmpty(productChannels)) {
                return false;
            }
            String[] array = productChannels.stream().map(ProductChannels::getAsin).distinct().toArray(String[]::new);
            request.setOrgType(1);
            request.setOrgValue(array);
            request.setSkuQuery(0);
            request.setParentAsinQuery(0);

        } else {
            request.setSkuQuery(0);
            request.setParentAsinQuery(0);
        }*/
        return true;
    }

    /**
     * @Description:批量取消agreement为是的promotion
     * @Author: wly
     * @Date: 2024/8/8 15:18
     * @Params: [amzPromotionsList, authUserEntity]
     * @Return: void
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCancelAgreementPromotion(List<AmzPromotions> amzPromotionsList, UserEntity authUserEntity, Integer contextId, String remark) {
        boolean anyMatch = amzPromotionsList.stream().anyMatch(t -> t.getId() == null || StringUtils.isEmpty(t.getPromotionsId()) || t.getAgreementFlag() == null);
        AssertUtil.isFalse(anyMatch, "id或promotionsId或agreement不能为空！");
        if (amzPromotionsList.size() == 1 && amzPromotionsList.get(0).getAgreementFlag() == 0) {
            throw new ErpCommonException("非Agreement数据不可直接标记取消");
        }
        boolean allMatch = amzPromotionsList.stream().allMatch(t -> t.getAgreementFlag() == 0);
        AssertUtil.isFalse(allMatch, "非Agreement数据不可直接标记取消");
        List<AmzPromotions> collect = amzPromotionsList.stream().filter(t -> t.getAgreementFlag() == 1).collect(Collectors.toList());
        List<Long> idList = collect.stream().map(AmzPromotions::getId).collect(Collectors.toList());
        List<AmzPromotions> listByIds = this.listByIds(idList);
        boolean alledMatch = listByIds.stream().allMatch(t -> t.getCompleteDate() != null);
        AssertUtil.isFalse(alledMatch, "所选活动已取消");
        List<Long> nonCancel = listByIds.stream().filter(t -> t.getCompleteDate() == null).map(AmzPromotions::getId).collect(Collectors.toList());
        collect = collect.stream().filter(t -> nonCancel.contains(t.getId())).collect(Collectors.toList());
        //更新完结时间为取消时间，记录日志
        List<AmzPromotions> update = new ArrayList<>();
        List<AmzPromotionsOperateLog> logs = new ArrayList<>();
        collect.forEach(t -> {
            Date completeDate = StringUtils.isEmpty(t.getCancelTime()) ? DateUtil.UtcToPacificDate(DateUtils.getNowDate()) : DateUtil.convertStringToDate(t.getCancelTime(), "yyyy-MM-dd HH:mm:ss");
            AmzPromotions promotions = new AmzPromotions();
            promotions.setId(t.getId());
            promotions.settingDefaultUpdate();
            promotions.setCompleteDate(completeDate);
            promotions.setPromotionsState(AmzPromotionsStateEnum.CANCELED.value());
            promotions.setRemark(remark);
            update.add(promotions);
        });
        this.updateBatchById(update);
        update.forEach(t -> {
            AmzPromotionsOperateLog operateLog = new AmzPromotionsOperateLog();
            operateLog.setPromotionId(t.getId());
            operateLog.settingDefaultCreate();
            operateLog.settingDefaultUpdate();
            operateLog.setOrganizationId(contextId);
            operateLog.setOperateType(AmzPromotionsOperateTypeEnum.CANCEL_PROMOTION.value());
            operateLog.setDetail("Agreement取消");
            logs.add(operateLog);
        });
        iAmzPromotionsOperateLogService.saveBatch(logs);
    }

    @Override
    public List<AmzPromotions> sekectTemuPromotionList(AmzPromotions amzPromotions) {
        return amzPromotionsMapper.selectAmzPromotionsListByTemu(amzPromotions);
    }


    /**
     * @param
     * @param amzPromotions
     * @description: 获取有效活动下的SKC
     * @author: Moore
     * @date: 2024/9/6 12:07
     * @return: java.util.List<com.bizark.op.api.entity.op.promotions.AmzPromotions>
     **/
    @Override
    public List<AmzPromotionsTemuVO> selectAmzPromotionsListBySkcGroup(AmzPromotions amzPromotions) {
        return amzPromotionsMapper.selectAmzPromotionsListBySkcGroup(amzPromotions);
    }


    /**
     * @param
     * @param promotionsIds
     * @description:根据促销Id获取指定促销信息，并计算出有效开始时间
     * @author: Moore
     * @date: 2024/10/25 9:44
     * @return: java.util.List<com.bizark.op.api.entity.op.promotions.AmzPromotionsTemuVO>
     **/
    @Override
    public List<TemuPromotionsPriceVO> selectAmzPromotionsListByPromotionsIds(List<String> promotionsIds) {
        return amzPromotionsMapper.selectAmzPromotionsListByPromotionsIds(promotionsIds);
    }


    /**
     * @param
     * @description: 获取所有含站点信息的促销活动
     * @author: Moore
     * @date: 2024/11/13 23:32
     * @return: java.util.List<com.bizark.op.api.entity.op.promotions.TemuPromotionsPriceVO>
     **/
    @Override
    public List<TemuPromotionsPriceVO> selectSessionProtionList(List<String> promotionsIds) {
        return amzPromotionsMapper.selectSessionProtionList(promotionsIds);
    }

    @Override
    public List<PromotionAccountVo> selectPromotionAccountList(Integer channel, Integer[] shopId, Integer contextId) {
        AmzPromotionsShopQueryRequest account = new AmzPromotionsShopQueryRequest();
        if (shopId != null) {
            account.setShopId(shopId);
        }
        if (channel != null) {
            if (channel == 1) {
                account.setAccountType(new String[]{AmzPromotionsChannelEnum.labelOf(AmzPromotionsChannelEnum.AMAZON_VC.value())});
                account.setType("amazonvendords");
            } else if (channel == 2) {
                account.setAccountType(new String[]{AmzPromotionsChannelEnum.labelOf(AmzPromotionsChannelEnum.AMAZON_SC.value())});
            } else {
                account.setAccountType(new String[]{AmzPromotionsChannelEnum.labelOf(AmzPromotionsChannelEnum.AMAZON_VC.value()),
                        AmzPromotionsChannelEnum.labelOf(AmzPromotionsChannelEnum.AMAZON_SC.value())});
            }
        } else {
            account.setAccountType(new String[]{AmzPromotionsChannelEnum.labelOf(AmzPromotionsChannelEnum.AMAZON_VC.value()),
                    AmzPromotionsChannelEnum.labelOf(AmzPromotionsChannelEnum.AMAZON_SC.value())});
        }
        account.setActive("Y");
        account.setOrganizationId(contextId);
        List<PromotionAccountVo> accountList = amzPromotionsMapper.selectPromotionAccount(account);

        if (CollectionUtil.isEmpty(accountList)) {
            return new ArrayList<>();
        }
        List<PromotionAccountVo> accounts = accountList.stream().filter(s -> StringUtil.isNotEmpty(s.getStoreName())).collect(Collectors.toList());
        for (PromotionAccountVo s : accounts) {
            if (StringUtils.isNotEmpty(s.getVendorInfo())) {
                List<PromotionAccountVo> promotionAccountVos = JSONArray.parseArray(s.getVendorInfo(), PromotionAccountVo.class);
                if (CollectionUtil.isNotEmpty(promotionAccountVos)) {
                    List<String> billingContacts = new ArrayList<>();
                    List<String> vendorCodes = new ArrayList<>();
                    for (PromotionAccountVo accountVo : promotionAccountVos) {
                        if (StringUtils.isNotEmpty(accountVo.getVendorCode()) && StringUtils.isNotEmpty(accountVo.getBillingContact())) {
                            if (accountVo.getBillingContact().matches("^\\w+([-+.']\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$")) {
                                billingContacts.add(accountVo.getBillingContact());
                                vendorCodes.add(accountVo.getVendorCode());
                            }
                        }
                    }
                    if (CollectionUtil.isNotEmpty(billingContacts)) {
                        s.setBillingContact(billingContacts.stream().distinct().collect(Collectors.joining(",")));
                    }
                    if (CollectionUtil.isNotEmpty(vendorCodes)) {
                        s.setVendorCode(vendorCodes.stream().distinct().collect(Collectors.joining(",")));
                    }
                }
            }
        }
//        accounts.removeIf(t -> StringUtils.isEmpty(t.getVendorCode()) || StringUtils.isEmpty(t.getBillingContact()));
        return accounts;
    }


    /**
     * @description: 查询Temu当日促销费
     * @author: Moore
     * @date: 2025/3/5 18:33
     * @param
     * @param dateTime
     * @param endTime
     * @return: java.math.BigDecimal
    **/
    @Override
    public BigDecimal selectOrderNowPromotionFee(DateTime dateTime, DateTime endTime) {
        return amzDiscountMapper.selectOrderNowPromotionFee(dateTime, endTime);
    }


    /**
     * 获取最新参考价格referencePrice,折后价格 Max最大值,Per-unit funding(折扣) mix最小值，cppu
     * @param request
     * @return
     */
    @Override
    public List<AmzPromotionsSkuDetail> getLastestReferencePrice(AmzPromotionsCreateRequest request) {

        Integer type = 1;
        AmzPromotions byId = null;
        if (request.getId() != null) {
            byId = this.getById(request.getId());
            type = StringUtils.isNotEmpty(byId.getPromotionsId()) ? 2 : 1;
        }
        Date nowDate = DateUtils.getNowDate();
        List<AmzPromotionsSkuDetail> promotionsSkuDetails = request.getSkuDetailList();
        Long shopId = request.getShopId();
        Integer promotionsType = request.getPromotionsType();
        Date beginTime = DateUtil.convertStringToDate(DateUtil.convertDateToString(request.getBeginTime()));
        Date endTime = DateUtil.convertStringToDate(DateUtil.convertDateToString(request.getEndTime()));
        Account account = accountService.getById(shopId);
        AccountWithCountry accountWithCountry = amzPromotionsMapper.selectAccountWithCountry(shopId);
        LambdaQueryWrapper<AmzPromotionsDetailExtraPrice> queryWrapper = new LambdaQueryWrapper<>();
        List<String> asinList = promotionsSkuDetails.stream().map(AmzPromotionsSkuDetail::getAsin).collect(Collectors.toList());
        queryWrapper.eq(AmzPromotionsDetailExtraPrice::getShopId, shopId)
                .eq(AmzPromotionsDetailExtraPrice::getPromotionsType, promotionsType)
                .in(AmzPromotionsDetailExtraPrice::getAsin, asinList)
                .le(AmzPromotionsDetailExtraPrice::getUpdatedAt, nowDate)
                .ge(AmzPromotionsDetailExtraPrice::getUpdatedAt, DateUtil.convertStringToDate(DateUtils.nMinsAfter(nowDate, -1), "yyyy-MM-dd HH:mm:ss"))
                .eq(AmzPromotionsDetailExtraPrice::getType, type);
        List<AmzPromotionsDetailExtraPrice> extraPriceList = amzPromotionsDetailExtraPriceService.list(queryWrapper);
        List<JSONObject> jsonObjectList = new ArrayList<>();
        List<AmzPromotionsSkuDetail> resultList = new ArrayList<>();

        for (AmzPromotionsSkuDetail promotionsSkuDetail : promotionsSkuDetails) {
            AmzPromotionsDetailExtraPrice amzPromotionsDetailExtraPrice = CollectionUtil.isEmpty(extraPriceList) ? null : extraPriceList.stream().filter(t -> promotionsSkuDetail.getAsin().equals(t.getAsin())).max(Comparator.comparing(AmzPromotionsDetailExtraPrice::getUpdatedAt)).orElse(null);
            if (amzPromotionsDetailExtraPrice != null) {
                promotionsSkuDetail.setReferencePrice(amzPromotionsDetailExtraPrice.getReferencePrice());
                promotionsSkuDetail.setMaxDiscountPrice(amzPromotionsDetailExtraPrice.getMaxDiscountPrice());
                promotionsSkuDetail.setMixPerUintFunding(amzPromotionsDetailExtraPrice.getMixPerUintFunding());
                promotionsSkuDetail.setCppu(amzPromotionsDetailExtraPrice.getCppu());
                promotionsSkuDetail.setReferencePriceUpdateDate(amzPromotionsDetailExtraPrice.getUpdatedAt());
                resultList.add(promotionsSkuDetail);
            } else {
                JSONObject object = new JSONObject();
                object.put("beginTime", DateUtil.convertDateToString(beginTime, "yyyy-MM-dd"));
                object.put("endTime", DateUtil.convertDateToString(endTime, "yyyy-MM-dd"));
                object.put("asin", promotionsSkuDetail.getAsin());
                object.put("rpaType", 5);
                object.put("type", promotionsType);
                object.put("vendorCode", request.getVendorCode());
                object.put("webStoreName", account.getStoreName());
                object.put("countryCode", accountWithCountry.getCountryCode());
                object.put("promotionId", byId == null ? null : byId.getPromotionsId());
                jsonObjectList.add(object);
            }
        }

        if (CollectionUtil.isNotEmpty(jsonObjectList)) {
            jsonObjectList.forEach(t -> {
                log.info("店铺[{}]促销类型[{}]开始时间[{}]结束时间[{}]asin[{}]请求参考价格请求参数[{}]", account.getStoreName(), promotionsType, t.getString("beginTime"), t.getString("endTime"), t.getString("asin"),JSONObject.toJSONString(t));
                rabbitTemplate.convertAndSend(MQDefine.PROMOTIONS_PROMOTIONS_EXCHANGE, MQDefine.PROMOTION_CREATE_OR_UPDATE_SENG_ROUTING, t);
            });
        }


        if (CollectionUtil.isNotEmpty(resultList)) {
            List<AmzPromotionsSkuDetail> update = resultList.stream().filter(t -> t.getId() != null).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(update)) {
                iAmzPromotionsSkuDetailService.updateBatchById(update);
            }
        }

        return resultList;

    }


    @Override
    public List<AmzPromotionsSkuDetail> getBatchReferencePrice(AmzPromotionsCreateRequest request) {

        List<AmzPromotionsSkuDetail> promotionsSkuDetails = request.getSkuDetailList();
        Long shopId = request.getShopId();
        Integer promotionsType = request.getPromotionsType();

        Integer type = 1;
        if (request.getId() != null) {
            AmzPromotions byId = this.getById(request.getId());
            type = StringUtils.isNotEmpty(byId.getPromotionsId()) ? 2 : 1;
        }
        LambdaQueryWrapper<AmzPromotionsDetailExtraPrice> queryWrapper = new LambdaQueryWrapper<>();
        List<String> asinList = promotionsSkuDetails.stream().map(AmzPromotionsSkuDetail::getAsin).collect(Collectors.toList());
        queryWrapper.eq(AmzPromotionsDetailExtraPrice::getShopId, shopId)
                .eq(AmzPromotionsDetailExtraPrice::getPromotionsType, promotionsType)
                .in(AmzPromotionsDetailExtraPrice::getAsin, asinList)
                .eq(AmzPromotionsDetailExtraPrice::getType, type);
        List<AmzPromotionsDetailExtraPrice> extraPriceList = amzPromotionsDetailExtraPriceService.list(queryWrapper);
        List<AmzPromotionsSkuDetail> resultList = new ArrayList<>();

        for (AmzPromotionsSkuDetail promotionsSkuDetail : promotionsSkuDetails) {
            AmzPromotionsDetailExtraPrice amzPromotionsDetailExtraPrice = CollectionUtil.isEmpty(extraPriceList) ? null : extraPriceList.stream().filter(t -> promotionsSkuDetail.getAsin().equals(t.getAsin())).max(Comparator.comparing(AmzPromotionsDetailExtraPrice::getUpdatedAt)).orElse(null);
            if (amzPromotionsDetailExtraPrice != null) {
                promotionsSkuDetail.setReferencePrice(amzPromotionsDetailExtraPrice.getReferencePrice());
                promotionsSkuDetail.setMaxDiscountPrice(amzPromotionsDetailExtraPrice.getMaxDiscountPrice());
                promotionsSkuDetail.setMixPerUintFunding(amzPromotionsDetailExtraPrice.getMixPerUintFunding());
                promotionsSkuDetail.setCppu(amzPromotionsDetailExtraPrice.getCppu());
                promotionsSkuDetail.setReferencePriceUpdateDate(amzPromotionsDetailExtraPrice.getUpdatedAt());
                resultList.add(promotionsSkuDetail);
            }
        }

        if (CollectionUtil.isNotEmpty(resultList)) {
            List<AmzPromotionsSkuDetail> update = resultList.stream().filter(t -> t.getId() != null).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(update)) {
                iAmzPromotionsSkuDetailService.updateBatchById(update);
            }
        }
        return resultList;

    }
}
