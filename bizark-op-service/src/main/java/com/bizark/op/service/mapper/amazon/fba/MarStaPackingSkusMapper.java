package com.bizark.op.service.mapper.amazon.fba;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bizark.op.api.entity.op.amazon.fba.DO.MarStaPackingSkuDO;
import com.bizark.op.api.entity.op.amazon.fba.MarStaPackingSkus;
import com.bizark.op.api.entity.op.amazon.fba.VO.MarFnSkuPrintVO;
import com.bizark.op.api.entity.op.amazon.fba.VO.MarStaPackingSkuVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
* @Entity generator.domain.MarStaPackingSkus
*/
public interface MarStaPackingSkusMapper extends BaseMapper<MarStaPackingSkus> {


    /**
     * @description: 一对多查询
     * @author: Moore
     * @date: 2025/8/13 14:42
     * @param
     * @param inboundPlanId
     * @return: java.util.List<com.bizark.op.api.entity.op.amazon.fba.DO.MarStaPackingSkuDO>
    **/
    List<MarStaPackingSkuDO> selectSkusByCartonSpec(@Param("inboundPlanId") String inboundPlanId);



    /**
     * @description: 连表查询（弃用？）
     * @author: Moore
     * @date: 2025/8/13 14:42
     * @param
     * @param inboundPlanId
     * @return: java.util.List<com.bizark.op.api.entity.op.amazon.fba.DO.MarStaPackingSkuDO>
     **/
    List<MarStaPackingSkuVO> selectSkusByCartonSpecRow(@Param("inboundPlanId") String inboundPlanId);


    /**
     * 查询打印FNSKU信息
     * if (StringUtils.isNotEmpty(category)) {
     *                     valueIgnoreCase = MessageServiceType.getValueIgnoreCaseList(Arrays.asList(category.split(","))).stream().collect(Collectors.joining(","));
     *                     log.info("转换后结果：{}", valueIgnoreCase);
     *                 }
     * @param ids
     * @return
     */
    List<MarFnSkuPrintVO.SellerSkuBean> selectPrintFnsku(@Param("taskId") List<Long> ids);
}
