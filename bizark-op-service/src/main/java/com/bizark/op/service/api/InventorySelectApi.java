package com.bizark.op.service.api;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bizark.common.util.XMLUtil;
import com.bizark.op.api.enm.TikTokApiEnums;
import com.bizark.op.api.enm.temu.TemuUrl;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.inventory.enums.SheinUrl;
import com.bizark.op.api.entity.op.inventory.request.EbayInventorySelect;
import com.bizark.op.api.entity.op.inventory.request.TemuInventorySelect;
import com.bizark.op.api.entity.op.inventory.response.query.*;
import com.bizark.op.api.parameter.util.TikTokUtil;
import com.bizark.op.common.util.Base64;
import com.bizark.op.service.util.AccessTokenUtils;
import com.bizark.op.service.util.XxlConfig;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Credentials;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.httpclient.DefaultHttpMethodRetryHandler;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.io.IOException;
import java.io.InputStream;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class InventorySelectApi {

    // TODO https://api-gateway.walmart.com/v3/inventory
    public static final String WALMART_DSV_INVENTORY_URL = "https://api-gateway.walmart.com/v3/inventory";
    public static final String WALMART_DSV_ITEM_INVENTORY_URL = "https://api-gateway.walmart.com/v3/inventories/%s?productIdType=%s";
    public static final String WALMART_INVENTORY_URL = "https://marketplace.walmartapis.com/v3/inventory";
    public static final String WALMART_ALL_INVENTORY_URL = "https://marketplace.walmartapis.com/v3/inventories";
    public static final String SHOPIFY_INVENTORY_URL = "https://%s/admin/api/2023-10/inventory_levels.json";
    public static final String MICROSOFT_INVENTORY_URL = "https://api.seller.ads.microsoft.com/api/external/v1/stores/%s/products/search";
    public static final String EBAY_INVENTORY_URL = "https://api.ebay.com/sell/inventory/v1/bulk_get_inventory_item";

    @Autowired
    private AccessTokenUtils accessTokenUtils;



    @Autowired
    private TikTokUtil tikTokUtil;


    public TiktokInventorySelectResponse selectTiktokInventory(Account account, List<String> itemIds) {
        if (itemIds.size() > 100) {
            log.error("tiktok inventory select max length is 100");
            return null;
        }

        // TODO 获取AccessToken
//        String accessToken = accessTokenUtils.getToken(account);
//        if (StrUtil.isBlank(accessToken)) {
//            log.error("tiktok inventory select error accessToken is empty : {}", account.getId());
//            return null;
//        }
//        String connectStr = account.getConnectStr();
//        JSONObject jsonObject = JSON.parseObject(connectStr);
//        if (!jsonObject.containsKey("shopId")) {
//            log.error("tiktok inventory select error shopId is empty : {}", account.getId());
//        }
        // TODO 获取Sign
//        long timeStamp = System.currentTimeMillis() / 1000;

        Map<String, Object> map = new HashMap<>();
        map.put("product_ids", itemIds);
        return tikTokUtil.postTikTokShopV2(JSON.toJSONString(map), TikTokApiEnums.PRODUCT_202309_INVENTORY_SEARCH.getUrl(), TikTokApiEnums.PRODUCT_202309_INVENTORY_SEARCH.getPath(), TiktokInventorySelectResponse.class, Long.valueOf(account.getId()));

//        long timeStamp = System.currentTimeMillis();
//        String sign = tikTokRequestUtils.getSign(
//                account.getId(),
//                XxlConfig.TIKTOK_APP_KEY,
//                XxlConfig.TIKTOK_APP_SECRET,
//                timeStamp,
//                "/api/products/stocks"
//        );
//        // TODO 获取库存信息
//        String tiktokUrl = String.format(TIKTOK_INVENTORY_URL_OLD, XxlConfig.TIKTOK_APP_KEY, accessToken, sign, timeStamp, "7495138550629238920");
//        HttpRequest post = HttpUtil.createPost(tiktokUrl);
//        post.body(JSON.toJSONString(inventorySelect));
//        post.header("Content-Type",  "application/json");
//        HttpResponse httpResponse = post.execute();
//        if (!httpResponse.isOk()) {
//            log.error("tiktok inventory request error: {}", httpResponse.body());
//            return null;
//        }
//        return JSON.parseObject(httpResponse.body(), TiktokInventorySelectOldResponse.class);
    }





    public ShopifyInventoryLevelResponse selectShopifyInventory(Account account, List<String> inventoryItemIds) {
        if (inventoryItemIds.size() > 100) {
            log.error("itemIds max length is 100");
            return null;
        }
        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            log.error("connectStr is not empty");
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey("password")) {
            log.error("password is not empty");
            return null;
        }
        if (!jsonObject.containsKey("hostname")) {
            log.error("hostname is not empty");
            return null;
        }

        // 构建请求 URL
        String url = String.format(SHOPIFY_INVENTORY_URL, jsonObject.getString("hostname")) + "?inventory_item_ids=" + String.join(",", inventoryItemIds);


        // 创建代理
        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("**************", 8888));


        // 创建 OkHttpClient
        OkHttpClient client = new OkHttpClient.Builder()
                .proxy(proxy)
                .proxyAuthenticator((route, response) -> {
                    String credential = Credentials.basic("temuapi", "iMEhAiG0phY01oszHwQG");
                    return response.request().newBuilder().header("Proxy-Authorization", credential).build();
                })
                .connectTimeout(120L, TimeUnit.SECONDS)
                .readTimeout(120L, TimeUnit.SECONDS)
                .build();

        // 创建请求
        Request request = new Request.Builder()
                .url(url)
                .addHeader("X-Shopify-Access-Token", jsonObject.getString("password"))
                .build();

        // 执行请求
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("request select shopify inventory error : {}", response.body() != null ? response.body().string() : "null");
                return null;
            }
            // 解析响应
            String resonseBody = response.body().string();
            log.info("Shopify库存查询 - {} - 参数:{} - 响应:{}", account.getFlag(), url, resonseBody);
            return JSON.parseObject(resonseBody, ShopifyInventoryLevelResponse.class);
        } catch (IOException e) {
            log.error("IOException occurred while requesting Shopify inventory: {}", e.getMessage());
            return null;
        }
    }


    public WalmartInventorySelectResponse selectWalmartDSVInventory(Account account, String gtin) {
        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            log.error("connectStr is not empty");
            return null;
        }
        //获取ID与秘钥
        JSONObject json = JSON.parseObject(connectStr);
        String token = accessTokenUtils.getToken(account.getId());
        if (StrUtil.isBlank(token)) {
            log.error("未获取到token信息");
            return null;
        }

        String str = json.get("clientId") + ":" + json.get("clientSecret");
        String authorization = "Basic " + Base64.encode(str.getBytes());

        HttpRequest get = HttpUtil.createGet(WALMART_DSV_INVENTORY_URL + "?shipNode=123&gtin=" + gtin);
        get.header("Authorization", authorization);
        get.header("WM_SVC.NAME", json.containsKey("serviceName") ? json.getString("serviceName") : "My API Key");
        get.header("WM_QOS.CORRELATION_ID", json.containsKey("correlationId") ? json.getString("correlationId") : UUID.randomUUID().toString());
        get.header("WM_SEC.ACCESS_TOKEN", token);

        HttpResponse httpResponse = get.execute();
        if (!httpResponse.isOk()) {
            log.error("request to walmart error ...");
            return null;
        }
        return JSON.parseObject(httpResponse.body(), WalmartInventorySelectResponse.class);
    }


    public WalmartBatchInventorySelectResponse selectWalmartInventory(Account account, Integer limit, String nextCursor) {
        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            log.error("connectStr is not empty");
            return null;
        }
        //获取ID与秘钥
        JSONObject json = JSON.parseObject(connectStr);
        String token = accessTokenUtils.getToken(account);
        if (StrUtil.isBlank(token)) {
            log.error("未获取到token信息");
            return null;
        }

        String str = json.getString("clientId") + ":" + json.getString("clientSecret");
        String authorization = "Basic " + Base64.encode(str.getBytes());

        String url = WALMART_ALL_INVENTORY_URL + "?limit=" + limit;
        if (StrUtil.isNotBlank(nextCursor)) {
            url += "&nextCursor=" + nextCursor;
        }
        HttpRequest get = HttpUtil.createGet(url);
        get.header("Authorization", authorization);
        get.header("WM_SVC.NAME", "My API Key");
        get.header("Content-Type", "application/json");
        get.header("WM_QOS.CORRELATION_ID", UUID.randomUUID().toString());
        get.header("WM_SEC.ACCESS_TOKEN", token);
        HttpResponse httpResponse = get.execute();
        if (!httpResponse.isOk()) {
            log.error("request to walmart error : {}", httpResponse.body());
            return null;
        }
        return JSON.parseObject(httpResponse.body(), WalmartBatchInventorySelectResponse.class);
    }


    public WalmartInventorySelectResponse selectWalmartInventory(Account account, String sellerSku) {
        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            log.error("connectStr is not empty");
            return null;
        }
        //获取ID与秘钥
        JSONObject json = JSON.parseObject(connectStr);
        String token = accessTokenUtils.getToken(account);
        if (StrUtil.isBlank(token)) {
            log.error("未获取到token信息");
            return null;
        }

        String str = json.get("clientId") + ":" + json.get("clientSecret");
        String authorization = "Basic " + Base64.encode(str.getBytes());

        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", authorization);
        headers.put("WM_SVC.NAME", "My API Key");
        headers.put("WM_QOS.CORRELATION_ID", UUID.randomUUID().toString());
        headers.put("WM_SEC.ACCESS_TOKEN", token);

        HttpRequest get = HttpUtil.createGet(WALMART_INVENTORY_URL + "?sku=" + sellerSku);
        get.headerMap(headers, true);
        HttpResponse httpResponse = get.execute();
        if (!httpResponse.isOk()) {
            log.error("request to walmart error : {}", httpResponse.body());
            return null;
        }
        return JSON.parseObject(httpResponse.body(), WalmartInventorySelectResponse.class);
    }


    /**
     * 查询微软SellerSku库存信息
     * @param account
     * @param sellerSKu
     */
    public MicroSoftInventorySelectResponse selectMicroSoftInventory(Account account, String sellerSKu) {

        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            log.error("microSoft inventory select fail , connectStr is empty : [accountId:{} , SellerSku:{}]", account.getId(), sellerSKu);
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey("storeId")) {
            log.error("microSoft inventory select fail , storeId is empty : [accountId:{} , SellerSku:{}]",  account.getId(), sellerSKu);
            return null;
        }

        String token = accessTokenUtils.getToken(account);
        if (StrUtil.isBlank(token)) {
            log.error("microSoft inventory select fail , auth error : [accountId:{} , SellerSku:{}]", account.getId(), sellerSKu);
            return null;
        }

        HttpRequest post = HttpUtil.createPost(String.format(MICROSOFT_INVENTORY_URL, jsonObject.getString("storeId")));
        Map<String,Object> bodyMap = new HashMap<>();
        bodyMap.put("StatusList", Arrays.asList(1, 2, 3, 4));
        bodyMap.put("SKU", sellerSKu);
        post.body(JSON.toJSONString(bodyMap));
        post.header("Authorization", token);
        HttpResponse httpResponse = post.execute();
        if (!httpResponse.isOk()) {
            log.error("microSoft inventory request fail [accountId:{} , SellerSku:{}] :{}", account.getId(), sellerSKu, httpResponse.body());
            return null;
        }
        return JSON.parseObject(httpResponse.body(), MicroSoftInventorySelectResponse.class);
    }


    public OverstockInventorySelectResponse selectOverStockInventory(Account account) {
        String authorization = accessTokenUtils.getToken(account);
        String connectStr = account.getConnectStr();
        JSONObject jsonObject = JSON.parseObject(connectStr);

        String host = jsonObject.getString("domain");
        String url = host + "/inventory";
        log.info("overstock channel:{}, getInventory url:{}", account.getFlag(), url);
        HttpClient client = new HttpClient();
        StringBuilder sb = new StringBuilder();
        InputStream ins = null;
        // Create a method instance.
        GetMethod method = new GetMethod(url);
        method.setRequestHeader("Connection", "keep-alive");
        method.setRequestHeader("content-type", "application/xml");
        method.setRequestHeader("Authorization", authorization);
        method.setRequestHeader("accept", "application/xml");
        method.getParams().setParameter(HttpMethodParams.SO_TIMEOUT, 50000);
        method.getParams().setParameter(HttpMethodParams.RETRY_HANDLER,
                new DefaultHttpMethodRetryHandler(3, false));

        try {
            // Execute the method.
            int statusCode = client.executeMethod(method);
            ins = method.getResponseBodyAsStream();
            byte[] b = new byte[24];
            int r_len = 0;
            while ((r_len = ins.read(b)) > 0) {
                sb.append(new String(b, 0, r_len, method
                        .getResponseCharSet()));
            }

            if (statusCode != HttpStatus.SC_OK) {    //请求执行成功
                log.error("channel:{} ,获取商品库存执行失败：{}#{}", account.getFlag(), statusCode, sb);
            }

            if (!"".equals(sb.toString())) {
                String xml = XMLUtil.xml2JSON(sb.toString());
                log.info("overstock请求商品库存结果为：{}", xml);
                return JSONObject.parseObject(xml, OverstockInventorySelectResponse.class);

            }
            return null;
        } catch (Exception e) {
            log.info("overstock channel:{}, getInventory异常:{}", account.getFlag(), e.getMessage());
            e.printStackTrace();
        }
        return null;
    }


    public EbayInventorySelectResponse selectEbayInventory(Account account, List<String> sellerSkus) {
        EbayInventorySelect select = new EbayInventorySelect();
        List<EbayInventorySelect.Data> data = sellerSkus.stream()
                .distinct()
                .map(EbayInventorySelect.Data::new)
                .collect(Collectors.toList());
        select.setRequests(data);

        HttpRequest post = HttpUtil.createPost(EBAY_INVENTORY_URL);
        post.header("Authorization", accessTokenUtils.getToken(account));
        post.header("Content-Type", "application/json");
        post.body(JSON.toJSONString(select));
        HttpResponse httpResponse = post.execute();
        if (!httpResponse.isOk()) {
            log.error("Ebay inventory request fail [accountId:{} , SellerSku:{}] :{}", account.getId(), sellerSkus, httpResponse.body());
            return null;
        }
        return JSON.parseObject(httpResponse.body(), EbayInventorySelectResponse.class);
    }


    public WalmartDSVInventorySelectResponse selectWalmartDSVItemInventory(Account account, String sellerSku) {
        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey("consumerId") || !jsonObject.containsKey("privateEncodedStr") || !jsonObject.containsKey("correlationId")) {
            return null;
        }
        String url = String.format(WALMART_DSV_ITEM_INVENTORY_URL, sellerSku, "SKU");
        HttpClient httpClient = new HttpClient();
        httpClient.setConnectionTimeout(1000 * 20);
        GetMethod getMethod = new GetMethod(url);

        StringBuilder sb = new StringBuilder();
        String timestamp = String.valueOf(System.currentTimeMillis());

        String accessToken = accessTokenUtils.getToken(account);
        if (StrUtil.isBlank(accessToken)) {
            log.info("Walmart_dsv库存失败 -  未获取到AccessToken {} - {}", account.getFlag(),sellerSku);
            return null;
        }

        InputStream ins = null;

        String signedString = getAuthSignature(jsonObject.getString("consumerId"), jsonObject.getString("privateEncodedStr"), url, timestamp, "GET");
        getMethod.setRequestHeader("WM_SVC.NAME", jsonObject.containsKey("serviceName") ? jsonObject.getString("serviceName") : "My API Key");
        getMethod.setRequestHeader("WM_SEC.AUTH_SIGNATURE", signedString);
        getMethod.setRequestHeader("WM_CONSUMER.ID", jsonObject.getString("clientId"));
        getMethod.setRequestHeader("WM_SEC.TIMESTAMP", timestamp);
        getMethod.setRequestHeader("WM_QOS.CORRELATION_ID", jsonObject.getString("correlationId"));
        getMethod.setRequestHeader("WM_CONSUMER.CHANNEL.TYPE", jsonObject.getString("channelType"));
        getMethod.setRequestHeader("Accept", "application/json");
        getMethod.setRequestHeader("WM_SEC.ACCESS_TOKEN", accessToken);
        try {
            httpClient.executeMethod(getMethod);
            ins = getMethod.getResponseBodyAsStream();
            byte[] b = new byte[24];
            int r_len = 0;
            while ((r_len = ins.read(b)) > 0) {
                sb.append(new String(b, 0, r_len, StandardCharsets.UTF_8));
            }
            String body = sb.toString();
            log.info("Walmart_dsv库存响应 - {} - {} - {}", account.getFlag(), sellerSku, body);
            return JSON.parseObject(body, WalmartDSVInventorySelectResponse.class);
        } catch (Exception e) {
            log.info("Walmart_dsv库存异常 - {} - {} - {}", account.getFlag(), sellerSku, e.getMessage(), e);
        }finally {
            try {
                ins.close();
            } catch (IOException e) {
            }
        }
        return null;
    }



    public String getAuthSignature(String consumerId,String privateEncodedStr,String url,String timestamp,String httpMethod) {


        String stringToBeSigned = consumerId + "\n" + url + "\n" + httpMethod + "\n" + timestamp + "\n";
        String authSignatureString = null;
        try {
            byte[] encodedKeyBytes = org.apache.commons.codec.binary.Base64.decodeBase64(privateEncodedStr);
            PKCS8EncodedKeySpec privSpec = new PKCS8EncodedKeySpec(encodedKeyBytes);
            KeyFactory kf = KeyFactory.getInstance("RSA");
            PrivateKey myPrivateKey = kf.generatePrivate(privSpec);
            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initSign(myPrivateKey);
            byte[] data = stringToBeSigned.getBytes("UTF-8");
            signature.update(data);
            byte[] signedBytes = signature.sign();
            authSignatureString = org.apache.commons.codec.binary.Base64.encodeBase64String(signedBytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return authSignatureString;
    }


    public SheinInventorySelectResponse selectSheinProductDetails(Account account, List<String> skuCodes) {
        log.info("SHEIN-INVENTORY-SYNC 请求商品详情:{}", skuCodes);
        long timeStamp = System.currentTimeMillis();
        String sign = accessTokenUtils.getSheinSign(account, SheinUrl.PRODUCT_DETAIL.getUrl(), timeStamp);
        log.info("SHEIN-INVENTORY-SYNC 签名获取完毕: {}", sign);
        if (StrUtil.isBlank(sign)) {
            log.error("SHEIN-INVENTORY-SYNC 签名获取失败:{}", account.getFlag());
            return null;
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("language", "zh-cn");
        jsonObject.put("skuCodes", skuCodes);
        JSONObject connecStrObj = JSON.parseObject(account.getConnectStr());
        HttpRequest post = HttpUtil.createPost(SheinUrl.PRODUCT_DETAIL.getFullUrl());
        post.header("Content-Type", "application/json");
        post.header("x-lt-openKeyId", connecStrObj.getString("openKeyId"));
        post.header("x-lt-signature", sign);
        post.header("x-lt-timestamp", String.valueOf(timeStamp));
        post.body(jsonObject.toJSONString());

        try {
            HttpResponse httpResponse = post.execute();
            if (!httpResponse.isOk()) {
                log.error("SHEI.N-INVENTORY-SYNC 请求Shein商品详情接口失败: {} - {}",httpResponse.getStatus(), httpResponse.body());
                return null;
            }
            String body = httpResponse.body();
            log.info("SHEIN-INVENTORY-SYNC 请求结束:{}", body);
            return JSON.parseObject(body, SheinInventorySelectResponse.class);
        } catch (Exception e) {
            return null;
        }
    }




    public TemuInventorySelectResponse selectTemuInventory(Account account, String skcId) {
        if (StrUtil.isBlank(account.getConnectStr())) {
            log.error("TEMU 库存查询 - 店铺 ConnectStr 为空 : {}", account.getFlag());
            return null;
        }

        JSONObject connecObj = JSONObject.parseObject(account.getConnectStr());
        if (!connecObj.containsKey("cnAccessToken")) {
            log.error("TEMU 库存查询 - 店铺 ConnectStr - cnAccessToken 为空 : {}", account.getFlag());
            return null;
        }

        TemuInventorySelect select = new TemuInventorySelect();
        select.setAccessToken(connecObj.getString("cnAccessToken"));
        select.setAppKey(XxlConfig.TEMU_APP_CN_KEY_ID);
        select.setProductSkcId(skcId);
        select.setType(TemuUrl.INVENTORY_QUERY.getValue());
        select.setTimestamp(TemuApi.getTimestamp() + "");
        select.setSign(getTemuSign(select));
        HttpRequest post = HttpUtil.createPost(TemuUrl.INVENTORY_QUERY.getCnBaseUrl());
        post.header("content-Type", "application/json");
        String body = JSON.toJSONString(select);
        log.info("TEMU 库存查询 - 请求参数：{}", body);
        post.body(body);
        HttpResponse httpResponse = post.execute();
        if (!httpResponse.isOk()) {
            log.error("TEMU 库存查询 - 请求失败 :{} - {}", httpResponse.getStatus(), httpResponse.body());
            return null;
        }
        log.info("Temu 库存查询 - 响应: {}", httpResponse.body());
        return JSONObject.parseObject(httpResponse.body(), TemuInventorySelectResponse.class);
    }

    public static String getTemuSign(Object param) {
        Map<String, Object> object = JSON.parseObject(JSON.toJSONString(param), Map.class);
        TreeMap<String, Object> sortedParams = new TreeMap<>(object);
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof String) {
                sb.append(entry.getKey()).append(entry.getValue());
            } else {
                sb.append(entry.getKey()).append(JSON.toJSONString(entry.getValue()));
            }
        }
        return DigestUtils.md5DigestAsHex((XxlConfig.TEMU_APP_CN_SECRET + sb + XxlConfig.TEMU_APP_CN_SECRET).getBytes()).toUpperCase();

    }



}
