package com.bizark.op.service.mapper.ticket;

import com.bizark.op.api.entity.op.ticket.ScTicket;
import com.bizark.op.api.entity.op.ticket.ScTicketOrderAndOrderItem;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.List;

/**
 * 工单管理Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-12
 */
public interface ScTicketMapper {
    /**
     * 查询工单管理
     *
     * @param ticketId 工单管理ID
     * @return 工单管理
     */
    ScTicket selectScTicketById(Long ticketId);


    /**
     * 根据工单编号获取工单信息
     *
     * @param ticketNumber
     * @return
     */
    ScTicket selectScTicketByTicketNumber(@Param("ticketNumber") String ticketNumber);
//

    /**
     * 查询工单管理(单表查询)
     *
     * @param ticketId 工单管理ID
     * @return 工单管理
     */
    ScTicket selectScTicketByTicketId(Long ticketId);
//


    /**
     * 查询工单管理(单表查询)
     *
     * @param ticketIds 工单管理ID
     * @return 工单管理
     */
    List<ScTicket> selectScTicketByTicketIds(@Param("ticketIds") List<Long> ticketIds);


    /**
     * 查询工单管理(单表查询)
     *
     * @param ticketIds 工单管理ID
     * @return 工单管理
     */
    List<ScTicket> selectScTicketByParentTicketIds(@Param("ticketIds") List<Long> ticketIds);

    /**
     * 查询工单管理列表
     *
     * @param scTicket 工单管理
     * @return 工单管理集合
     */
    List<ScTicket> selectScTicketList(ScTicket scTicket);

    /**
     * 通过母工单查询子工单列表
     *
     * @param parentTicketId
     * @return
     */
    List<ScTicket> selectScTicketByParentId(Long parentTicketId);
//

    /**
     * 根据来源单据查询工单
     *
     * @param sourceId
     * @param sourceDocument
     * @return
     */
    ScTicket selectScTicketBySource(@Param("sourceId") Long sourceId, @Param("sourceDocument") String sourceDocument);


    /**
     * @param
     * @param sourceId       来源ID
     * @param sourceDocument 来源单据
     * @description: 获取工单信息
     * @author: Moore
     * @date: 2023/10/18 1:17
     * @return: 工单信息
     **/
    List<ScTicket> selectScTicketBySourceList(@Param("sourceId")Long sourceId,@Param("sourceDocument")String sourceDocument);

    List<ScTicket> selectScTicketBySourceListV2(@Param("sourceIdList")List<Long> sourceIdList,@Param("sourceDocument")String sourceDocument);
//
//    /**
//     * 根据来源单据查询工单
//     *
//     * @param sourceId 来源ID
//     * @param sourceDocument 来源Ducument
//     * @param ticketType 工单类型
//     * @return
//     */
//    ScTicket selectScTicketBySourceType(@Param("sourceId")  Long sourceId, @Param("sourceDocument") String sourceDocument,  @Param("ticketType") String ticketType);
//
//    /**
//     * 查询工单统计数量
//     *
//     * @param scTicket 工单管理
//     * @return
//     */
//    ScTicketCount selectScTicketCount(ScTicket scTicket);
//
//    /**
//     * 查询未分配工单
//     *
//     * @return
//     */
//    List<ScTicket> selectScTicketUnAssign(ScTicket scTicket);
//
//    /**
//     * 根据ASIN查询待分配工单
//     *
//     * @param asin
//     * @return
//     */
//    List<ScTicket> selectScTicketByAsinToAssign(String asin);
//

    /**
     * 新增工单管理
     *
     * @param scTicket 工单管理
     * @return 结果
     */
    int insertScTicket(ScTicket scTicket);
//

    /**
     * 修改工单管理
     *
     * @param scTicket 工单管理
     * @return 结果
     */
    int updateScTicket(ScTicket scTicket);



    /**
     * @description:更新工单业务状态
     * @author: Moore
     * @date: 2025/5/7 17:12
     * @param
     * @param scTicket
     * @return: int
    **/
    int updateScTicketBusinessType(ScTicket scTicket);

    /**
     * 修改工单处理人
     *
     * @param scTicket
     * @return
     */
    int updateScTicketHandler(ScTicket scTicket);


    /**
     * 修改工单状态
     *
     * @param scTicket
     * @return
     */
    int updateScTicketStatus(ScTicket scTicket);




    /**
     * @description: 仅匹配订单，更新工单使用
     * @author: Moore
     * @date: 2025/7/11 9:40
     * @param
     * @param scTicket
     * @return: int
    **/
    int updateScTicketMatchOrder(ScTicket scTicket);



    /**
     * 修改工单订单信息
     *
     * @param scTicket
     * @return
     */
    int updateScTicketOrder(ScTicket scTicket);

//    /**
//     * 修改工单订单号
//     *
//     * @param scTicket
//     * @return
//     */
//    int updateScTicketAmazonOrderId(ScTicket scTicket);
//
    /**
     * 修改来源单据信息
     *
     * @param scTicket
     * @return
     */
    int updateScTicketSource(ScTicket scTicket);

    /**
     * 修改工单操作状态
     *
     * @param scTicket
     * @return
     */
    int updateScTicketOperateStatus(ScTicket scTicket);

//    /**
//     * 修改工单处理人
//     *
//     * @return
//     */
//    int batchUpdateTicketHandler(@Param("handlerBy") String handlerBy, @Param("userId") Long userId, @Param("deptId") Long deptId, @Param("ticketIds") Long[] ticketIds);
//
//    /**
//     * 删除工单管理
//     *
//     * @param ticketId 工单管理ID
//     * @return 结果
//     */
//    int deleteScTicketById(Long ticketId);
//
//    /**
//     * 批量删除工单管理
//     *
//     * @param ticketIds 需要删除的数据ID
//     * @return 结果
//     */
//    int deleteScTicketByIds(Long[] ticketIds);
//
//    /**
//     * 根据亚马逊订单ID查询对应工单
//     *
//     * @param amazonOrderId 订单ID
//     */
//    List<ScTicket> selectScTicketByAmzOrderId(String amazonOrderId);
//
//    /**
//     * 根据客户ID查询对应工单
//     *
//     * @param customerId 客户ID
//     */
//    List<ScTicket> selectScTicketByCustomerId(Long customerId);
//
//
    int insertBatch(List<ScTicket> scTickets);
    List<ScTicket> selectScTicketBySources(@Param("list") Collection<Long> sourceIds, @Param("source") String source);

    List<Long> selectDeptIdByUserId(@Param("userId") Long userId);


    /**
     * @description: 获取工单临时默认处理人
     * @author: Moore
     * @date: 2023/10/17 18:57
     * @param
     * @param nickName 用户名
     * @return: com.bizark.op.api.entity.op.ticket.ScTicket
    **/
    ScTicket selectTempOperationBy(String nickName);



    /**
     * @description: 根据系统回话ID获取工单信息
     * @author: Moore
     * @date: 2023/10/18 11:22
     * @param
     * @param convShortId
     * @param documentStation
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.ScTicket>
    **/
    List<ScTicket> selectScTicketBySourceAndConvShortList(@Param("convShortId") String convShortId,@Param("documentStation") String documentStation);

    Integer countList(@Param("orderId") String orderId, @Param("ticketStatusFinish") String ticketStatusFinish);

    Integer countByStatusAndOrderId(@Param("orderId")String orderId, @Param("channel")String channel);

    ScTicket existsScTicketByParentId(@Param("orderId") String orderId, @Param("ticketStatusFinish") String ticketStatusFinish, @Param("ticketId")Long ticketId, @Param("ticketType")String ticketType);

    ScTicket getTicketByTicketId(@Param("ticketId")Long ticketId);

    String selectUserName(@Param("id") Integer id);

    ScTicket selectScTicketByTicketSource(@Param("sourceId") Long sourceId, @Param("sourceDocument") String sourceDocument);

    void updateScTicketStatus(@Param("code")String code,@Param("ticketId") Long ticketId,@Param("refundType") String refundType);

    ScTicket getOne(@Param("orderNo")String orderNo,@Param("reverseOrderId") String reverseOrderId);

    void deleteByOrderNoAndReverseOrderId(@Param("orderNo")String orderNo,@Param("reverseOrderId")  String reverseOrderId);


    /**
     * @param
     * @param reverseOrderId
     * @description:根据逆向订单号获取工单
     * @author: Moore
     * @date: 2023/11/8 19:37
     * @return: com.bizark.op.api.entity.op.ticket.ScTicket
     **/
    ScTicket selectScTicketByReverseOrderId(String reverseOrderId);


    ScTicket getTicketByReverseOrderId(@Param("reverseOrderId") Long reverseOrderId);


    /**
     * @description: 根据逆行订单号及工单类型查询工单信息
     * @author: Moore
     * @date: 2023/12/4 14:47
     * @param
     * @param reverseOrderId
     * @param ticketType
     * @return: com.bizark.op.api.entity.op.ticket.ScTicket
     **/
    ScTicket selectScTicketByReverseOrderIdAndTicketType(@Param("reverseOrderId") String reverseOrderId, @Param("ticketType") String ticketType);






    /**
     * 根据工单类型，更新工单优先级
     *
     * @param  ticketType 工单类型
     * @param  priority 工单优先级别
     */
    void updateScTicketPriorityStatus(@Param("organizationId")Long organizationId,@Param("ticketType") String ticketType, @Param("priority") String priority);


    /**
     * 根据工单类型，更新工单状态
     *
     * @param  organizationId 组织ID
     * @param  ticketType 工单类型
     * @param  ticketStatus 工单状态
     */
    void updateScTicketStatusByType(@Param("organizationId") Long organizationId, @Param("ticketType") String ticketType, @Param("ticketStatus") String ticketStatus);

    List<ScTicketOrderAndOrderItem> selectManualOrder(@Param("orderNo") String orderNo, @Param("orgId") Long orgId);


    List<ScTicket> selectScTicketListByIds(@Param("ids") Long[] ids);


    /**
     * 根据来源id和工单类型查询工单
     *
     * @param sourceId 来源ID
     * @param ticketType 工单类型
     * @return
     */
    ScTicket selectScTicketBySourceType(@Param("sourceId")  Long sourceId,  @Param("ticketType") String ticketType);

    List<ScTicket> selectTicketByCustomerIdAndTicketType(@Param("customerId") Long customerId,@Param("ticketType") String ticketType);

    void updateScTicketStatusByIds(@Param("ids") Long[] ids, @Param("status") String status);


    /**
     * 根据父工单Id和工单类型查询指定状态的子工单
     * @param parentTicketId
     * @param ticketType
     * @return
     */
    List<ScTicket> selectScTicketFinishStateAByParentTicketIdAndTicketType(@Param("parentTicketId") Long parentTicketId,@Param("ticketType")String[] ticketType,@Param("ticketStatus")String[] ticketStatus);

    /**
     * 根据父工单id,工单类型查询不等于已完成,已关闭的子工单
     * @param parentTicketId
     * @param ticketType
     * @return
     */
    List<ScTicket> selectScTicketProcessStateAByParentTicketIdAndTicketType(@Param("parentTicketId") Long parentTicketId,@Param("ticketType")String[] ticketType);


    /**
     * Description: 查询取消的退货单的工单
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/11/28
     */
    List<ScTicket> temuReturnTicketAutoClosed(@Param("ticketType")String ticketType);


    List<ScTicket> queryReturnTicket(ScTicket scTicket);

    /**
     * Description: 根据订单号，组织id,工单类型，工单来源查找对应工单
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2025/2/24
     */
    List<ScTicket> queryScTicketByParam(ScTicket query);

    void deleteTicketById(Long id);

    /**
     * 清空匹配工单信息
     *
     * @param id 工单管理
     * @return 结果
     */
    void updateManualMatchScTicketById(Long id);

    void updateManualMatchScTicketByTicket(ScTicket query);

    void updateGoodSkuByTicket(ScTicket query);



    List<ScTicket> selectRemarkByIds(@Param("ids") List<Long> ticketIdList);

    @Update("update erp.sc_ticket_handle set remark = #{remark} where ticket_id = #{id}")
    void updateScTicketRemark(@Param("id") Long id,@Param("remark") String remark);
}
