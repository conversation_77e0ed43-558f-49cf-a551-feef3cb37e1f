package com.bizark.op.service.service.amazon.fba;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.amazon.fba.DTO.MarPlatformWarehouseAddressDTO;
import com.bizark.op.api.entity.op.amazon.fba.DTO.MarPlatformWarehouseAddressQueryDTO;
import com.bizark.op.api.entity.op.amazon.fba.MarPlatformWarehouseAddress;
import com.bizark.op.api.entity.op.amazon.fba.VO.MarPlatformWarehouseAddressVO;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.amazon.fba.MarPlatformWarehouseAddressService;
import com.bizark.op.common.util.BeanCopyUtils;
import com.bizark.op.service.mapper.account.AccountMapper;
import com.bizark.op.service.mapper.amazon.fba.MarPlatformWarehouseAddressMapper;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class MarPlatformWarehouseAddressServiceImpl extends ServiceImpl<MarPlatformWarehouseAddressMapper, MarPlatformWarehouseAddress> implements MarPlatformWarehouseAddressService {

    @Resource
    private AccountService accountService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createAddress(MarPlatformWarehouseAddressDTO dto) {
        MarPlatformWarehouseAddress marPlatformWarehouseAddress = BeanCopyUtils.copyBean(dto, MarPlatformWarehouseAddress.class);
        marPlatformWarehouseAddress.settingCreated();
        if (!save(marPlatformWarehouseAddress)) {
            throw new RuntimeException("新增失败!");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAddress(MarPlatformWarehouseAddressDTO dto) {
        MarPlatformWarehouseAddress address = getById(dto.getId());
        if (address == null) {
            throw new RuntimeException("地址不存在!");
        }
        BeanUtil.copyProperties(dto, address);
        address.settingUpdated();
        if (!updateById(address)) {
            throw new RuntimeException("更新失败!");
        }
    }

    @Override
    public MarPlatformWarehouseAddressVO detail(String id) {
        MarPlatformWarehouseAddress address = getById(id);
        if (address == null) {
            throw new RuntimeException("地址不存在!");
        }

        return BeanCopyUtils.copyBean(address, MarPlatformWarehouseAddressVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDefault(String id) {
        MarPlatformWarehouseAddress address = getById(id);
        if (address == null) {
            throw new RuntimeException("地址不存在!");
        }
        if (address.getDefaultFlag() == 1) {
            return;
        }
        //将店铺下所有的地址设在为非默认
        Long shopId = address.getShopId();
        lambdaUpdate()
                .set(MarPlatformWarehouseAddress::getDefaultFlag, 0)
                .eq(MarPlatformWarehouseAddress::getShopId, shopId)
                .update();
        //设置默认
        lambdaUpdate()
                .set(MarPlatformWarehouseAddress::getDefaultFlag, 1)
                .eq(MarPlatformWarehouseAddress::getId, id)
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void parseAddressExcelAndSave(MultipartFile file, Integer contextId) throws Exception {
        List<MarPlatformWarehouseAddress> addressDTOS = parseExcelFile(file, contextId);
        if (CollUtil.isEmpty(addressDTOS) || addressDTOS.size() > 500) {
            throw new RuntimeException("最多支持导入500条!");
        }
        //校验
        addressDTOS.forEach(this::validateAddress);
        //设置店铺ID
        batchGetShopIds(addressDTOS, contextId);

        saveBatch(addressDTOS);
    }

    private void batchGetShopIds(List<MarPlatformWarehouseAddress> addressList, Integer orgId) {
        // 收集所有唯一的店铺名称
        List<String> shopNames = addressList.stream()
                .map(MarPlatformWarehouseAddress::getShopName)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(shopNames)) {
            throw new IllegalArgumentException("没有有效的店铺名称");
        }

        // 批量查询店铺信息（这里需要根据实际的店铺实体和Mapper来实现）
        // 示例代码如下：
        List<Account> shops = accountService.list(new LambdaQueryWrapper<Account>()
                .in(Account::getTitle, shopNames)
                .eq(Account::getOrgId, orgId));

        // 创建店铺名称到店铺ID的映射
        Map<String, Integer> shopNameIdMap = shops.stream()
                .collect(Collectors.toMap(Account::getTitle, Account::getId));

        // 为每个地址设置店铺ID
        for (MarPlatformWarehouseAddress address : addressList) {
            String shopName = address.getShopName();
            if (StrUtil.isNotBlank(shopName)) {
                Integer shopId = shopNameIdMap.get(shopName);
                if (shopId == null) {
                    throw new RuntimeException("店铺名称不存在: " + shopName);
                }
                address.setShopId(Long.valueOf(shopId));
            }
        }

    }

    @Override
    public List<MarPlatformWarehouseAddressVO> pageList(MarPlatformWarehouseAddressQueryDTO queryDTO) {
        return this.baseMapper.pageList(queryDTO);
    }

    public List<MarPlatformWarehouseAddress> parseExcelFile(MultipartFile file, Integer orgId) throws Exception {
        if (file == null || orgId == null) {
            throw new IllegalArgumentException("文件或组织ID不能为空");
        }

        try (ExcelReader reader = ExcelUtil.getReader(file.getInputStream())) {
            Sheet sheet = reader.getSheet();
            List<MarPlatformWarehouseAddress> addressList = new ArrayList<>();

            // 获取实际行数，包括空行
            int lastRowNum = sheet.getLastRowNum();

            // 从第二行开始读取（跳过标题行）
            for (int i = 1; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);

                // 即使是空行也创建一个地址对象
                MarPlatformWarehouseAddress dto = new MarPlatformWarehouseAddress();
                dto.setOrgId(orgId);
                dto.setDefaultFlag(0);

                // 如果行不为空，则读取单元格数据
                if (row != null) {
                    dto.setShopName(getCellValueAsString(row, 0));
                    dto.setAddressAlias(getCellValueAsString(row, 1));
                    dto.setShipmentCountry(getCellValueAsString(row, 2));
                    dto.setSenderName(getCellValueAsString(row, 3));
                    dto.setPhoneNumber(getCellValueAsString(row, 4));
                    dto.setStreetAddress1(getCellValueAsString(row, 5));
                    dto.setStreetAddress2(getCellValueAsString(row, 6));
                    dto.setCity(getCellValueAsString(row, 7));
                    dto.setStateProvince(getCellValueAsString(row, 8));
                    dto.setDistrict(getCellValueAsString(row, 9));
                    dto.setPostalCode(getCellValueAsString(row, 10));
                    dto.setEmail(getCellValueAsString(row, 11));
                    dto.setCompanyName(getCellValueAsString(row, 12));
                }

                addressList.add(dto);
            }

            return addressList;
        }
    }

    private String getCellValueAsString(Row row, int cellIndex) {
        Cell cell = row.getCell(cellIndex);
        if (cell == null) {
            return null;
        }

        switch (cell.getCellTypeEnum()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return DateUtil.getJavaDate(cell.getNumericCellValue()).toString();
                } else {
                    DataFormatter formatter = new DataFormatter();
                    return formatter.formatCellValue(cell);
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    // 尝试获取公式计算结果
                    switch (cell.getCellTypeEnum()) {
                        case STRING:
                            return cell.getStringCellValue();
                        case NUMERIC:
                            if (DateUtil.isCellDateFormatted(cell)) {
                                return DateUtil.getJavaDate(cell.getNumericCellValue()).toString();
                            } else {
                                DataFormatter formatter = new DataFormatter();
                                return formatter.formatCellValue(cell);
                            }
                        case BOOLEAN:
                            return String.valueOf(cell.getBooleanCellValue());
                        default:
                            return cell.getCellFormula();
                    }
                } catch (Exception e) {
                    // 如果无法获取计算结果，返回公式本身
                    return cell.getCellFormula();
                }
            default:
                return null;
        }
    }




    private void validateAddress(MarPlatformWarehouseAddress address) {
        if (address.getShopName() == null) {
            throw new IllegalArgumentException("店铺不能为空");
        }
        if (StrUtil.isBlank(address.getAddressAlias())) {
            throw new IllegalArgumentException("地址别名不能为空");
        }
        if (StrUtil.isBlank(address.getSenderName())) {
            throw new IllegalArgumentException("发货方名称不能为空");
        }
        if (StrUtil.isBlank(address.getStreetAddress1())) {
            throw new IllegalArgumentException("街道地址1不能为空");
        }
        if (StrUtil.isBlank(address.getCity())) {
            throw new IllegalArgumentException("城市不能为空");
        }
        if (StrUtil.isBlank(address.getStateProvince())) {
            throw new IllegalArgumentException("州/省/地区不能为空");
        }
        if (StrUtil.isBlank(address.getPostalCode())) {
            throw new IllegalArgumentException("邮政编码不能为空");
        }
        if (StrUtil.isBlank(address.getShipmentCountry())) {
            throw new IllegalArgumentException("发货国家或地区不能为空");
        }
    }


    /**
     * 解析字符串值
     *
     * @param value 原始值
     * @return 字符串值
     */
    private String parseStringValue(Object value) {
        if (value == null) {
            return null;
        }
        return StrUtil.toString(value);
    }

    /**
     * 解析长整型值
     *
     * @param value 原始值
     * @return 长整型值
     */
    private Long parseLongValue(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof Number) {
            return ((Number) value).longValue();
        }

        try {
            return Long.parseLong(StrUtil.toString(value));
        } catch (NumberFormatException e) {
            return null;
        }
    }
}




