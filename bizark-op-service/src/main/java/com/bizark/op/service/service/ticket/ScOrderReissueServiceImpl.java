package com.bizark.op.service.service.ticket;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.bizark.fbb.api.parameter.warehouse.WarehouseAddress;
import com.bizark.fbb.api.parameter.warehouse.WarehouseInfoView;
import com.bizark.fbb.api.service.WarehouseService;
import com.bizark.op.api.enm.config.SystemGlobalConfigEnum;
import com.bizark.op.api.entity.op.product.SkuChildren;
import com.bizark.op.api.entity.op.sale.Products;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.conf.SystemGlobalConfigService;
import com.bizark.op.api.service.product.SkuChildrenService;
import com.bizark.op.api.service.sale.ProductsService;
import com.bizark.op.api.vo.customer.ScOrderReissueSplitBuyLabelDto.Parcel;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.boss.api.parameter.openapi.AttachInfoItemRequest;
import com.bizark.boss.api.parameter.openapi.SaleOrderApiRequest;
import com.bizark.boss.api.parameter.openapi.SaleOrderItemApiRequest;
import com.bizark.boss.api.service.order.openapi.OrderOpenApiService;
import com.bizark.common.contract.AuthUserDetails;
import com.bizark.op.api.cons.ScTicketConstant;
import com.bizark.op.api.entity.op.order.SaleOrderItemVO;
import com.bizark.op.api.entity.op.order.SaleOrderItems;
import com.bizark.op.api.entity.op.order.SaleOrders;
import com.bizark.op.api.entity.op.sys.SysIfInvokeOutbound;
import com.bizark.op.api.entity.op.ticket.*;
import com.bizark.op.api.exception.ErpCommonException;
import com.bizark.op.api.service.sys.ISysIfInvokeOutboundService;
import com.bizark.op.api.service.ticket.*;
import com.bizark.op.api.vo.customer.ReissueSplitWmsCallBack;
import com.bizark.op.api.vo.customer.ScOrderReissueSplitBuyLabelDto;
import com.bizark.op.api.vo.customer.ScOrderReissueSplitBuyLabelResponse;
import com.bizark.op.api.wms.WmsCommonResponse;
import com.bizark.op.api.wms.WmsGetTokenResponse;
import com.bizark.op.api.wms.WmsWorkOrderCreateRequest;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.*;
import com.bizark.op.service.mapper.order.SaleOrderItemsMapper;
import com.bizark.op.service.mapper.sale.ProductsMapper;
import com.bizark.op.service.mapper.ticket.*;
import com.bizark.framework.security.AuthContextHolder;
import com.bizark.op.service.util.wms.WmsUtil;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.bizark.usercenter.api.parameter.attachment.AttachItemRow;
import com.bizark.usercenter.api.parameter.attachment.ContextAttachMeta;
import com.bizark.usercenter.api.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 订单补发Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-16
 */
@Service
@Slf4j
public class ScOrderReissueServiceImpl extends ServiceImpl<ScOrderReissueMapper, ScOrderReissue> implements IScOrderReissueService {
    private static Logger logger = LoggerFactory.getLogger(ScOrderReissueServiceImpl.class);

    @Autowired
    private ScOrderReissueMapper scOrderReissueMapper;

    @Autowired
    private IScReissueAccessoriesService scReissueAccessoriesService;

    @Autowired
    private IScReissueGoodsService scReissueGoodsService;

    @Autowired
   private TicketAttachesService ticketAttachesService;

    @Autowired
    private IConfTicketAssignService confTicketAssignService;

    @Autowired
    private IScTicketService scTicketService;

    @Autowired
    private IScTicketLogService scTicketLogService;

    @Autowired
    private ISysIfInvokeOutboundService invokeLogStrategy;

    @Autowired
    private SaleOrderItemsMapper saleOrderItemsMapper;

    @Autowired
    private SaleOrdersMapper saleOrdersMapper;

    @Autowired
    private ScReissueGoodsMapper scReissueGoodsMapper;

    @Autowired
    private ScReissueAccessoriesMapper scReissueAccessoriesMapper;

    @Autowired
    UserService userService;

    @Autowired
    private OrderOpenApiService orderOpenApiService;

    @Autowired
    private IScReissueShipmentService scReissueShipmentService;

    @Autowired
    private ScReissueShipmentMapper scReissueShipmentMapper;

    @Autowired
    private IScTicketHandleService scTicketHandleService;

    @Autowired
    private ScManualOrderMapper scManualOrderMapper;

    @Autowired
    private ProductsMapper productsMapper;

    @Autowired
    private IScReissueSplitService scReissueSplitService;

    @Autowired(required = false)
    private WarehouseService warehouseService;

    @Value("${reissue.buy.label.url}")
    public  String REISSUE_BUY_LABEL_URL;

    @Autowired
    private WmsUtil wmsUtil;

    @Autowired
    private ScTicketMapper scTicketMapper;

    @Autowired
    private ProductsService productsService;

    @Autowired
    private SystemGlobalConfigService systemGlobalConfigService;

    @Autowired
    private SkuChildrenService skuChildrenService;

    @Autowired
    private AccountService accountService;

    /**
     * 查询订单补发
     *
     * @param id 订单补发ID
     * @return 订单补发
     */
    @Override
    public ScOrderReissue selectScOrderReissueById(Long id) {
        ScOrderReissue orderReissue = scOrderReissueMapper.selectById(id);

        if (null == orderReissue) {
            return new ScOrderReissue();
        }
        Long attachBelongReissueId = orderReissue.getId();
        if ("ACCESSORIES".equals(orderReissue.getReissueType())) {
            orderReissue.setAccessoriesList(scReissueAccessoriesService.selectScReissueAccessoriesByReissueId(orderReissue.getId()));
        } else if ("SHIPMENT".equals(orderReissue.getReissueType())) {
            orderReissue.setReissueShipmentList(scReissueShipmentService.selectScReissueShipmentByReissueId(orderReissue.getId()));
        } else if ("SPLIT".equals(orderReissue.getReissueType())) {
            orderReissue.setReissueSplitList(scReissueSplitService.list(new LambdaQueryWrapper<ScReissueSplit>().eq(ScReissueSplit::getReissueId, orderReissue.getId())));

        } else {
            orderReissue.setReissueGoodsList(scReissueGoodsService.selectScReissueGoodsByReissueId(orderReissue.getId()));
        }

        //获取附件信息
        LambdaQueryChainWrapper<TicketAttaches> queryObs = ticketAttachesService.lambdaQuery();
        queryObs.eq(TicketAttaches::getDocumentId, attachBelongReissueId);
        queryObs.eq(TicketAttaches::getDocumentType, "sc_order_reissue");
        List<TicketAttaches> listObs = queryObs.list();
        if (CollectionUtil.isNotEmpty(listObs)) {
            List<TicketAttaches> labelAttachs = listObs.stream().filter(t -> StringUtils.isEmpty(t.getType())).collect(Collectors.toList());
            List<TicketAttaches> imgAttachs = listObs.stream().filter(t -> StringUtils.isNotEmpty(t.getType()) && "img".equalsIgnoreCase(t.getType())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(labelAttachs)) {
                ContextAttachMeta contextAttachMeta = new ContextAttachMeta();
                List<AttachItemRow> items = new ArrayList<>();
                labelAttachs.forEach(fileItem -> {
                    AttachItemRow attachItemRow = new AttachItemRow();
                    attachItemRow.setAttachId(fileItem.getAttachId());
                    attachItemRow.setAttachTitle(fileItem.getFileName());
                    attachItemRow.setAttachBrief(fileItem.getFileName());
                    attachItemRow.setAttachUrl(fileItem.getFileUrl());
                    items.add(attachItemRow);
                });
                contextAttachMeta.setItems(items);
                orderReissue.setMeta(JSONObject.toJSONString(contextAttachMeta));
            }
            if (CollectionUtil.isNotEmpty(imgAttachs)) {
                ContextAttachMeta contextAttachMeta = new ContextAttachMeta();
                List<AttachItemRow> items = new ArrayList<>();
                imgAttachs.forEach(fileItem -> {
                    AttachItemRow attachItemRow = new AttachItemRow();
                    attachItemRow.setAttachId(fileItem.getAttachId());
                    attachItemRow.setAttachTitle(fileItem.getFileName());
                    attachItemRow.setAttachBrief(fileItem.getFileName());
                    attachItemRow.setAttachUrl(fileItem.getFileUrl());
                    items.add(attachItemRow);
                });
                contextAttachMeta.setItems(items);
                orderReissue.setImageMeta(JSONObject.toJSONString(contextAttachMeta));
            }
        }

        return orderReissue;
    }

    /**
     * 查询订单补发列表
     *
     * @param scOrderReissue 订单补发
     * @return 订单补发
     */
    @Override
    public List<ScOrderReissue> selectScOrderReissueList(ScOrderReissue scOrderReissue) {
        return scOrderReissueMapper.selectScOrderReissueList(scOrderReissue);
    }

    @Override
    public ScOrderReissue selectScOrderReissueByTicket(ScOrderReissue scOrderReissue) {

        if (null == scOrderReissue.getTicketId()||StringUtils.isEmpty(scOrderReissue.getReissueType())) {
            throw new CustomException("工单类型或补发类型不能为空");
        }
        List<ScOrderReissue> scOrderReissues = scOrderReissueMapper.selectScOrderReissueByTicketList(scOrderReissue);
        //存在补发信息
        //1 父工单下存在对应补发类型的子工单信息
        //1.1 补发类型为补发发货
        //1.1.1存在新建及处理中工单数据 ，抛出异常
        //1.1.2 否则，存在补发状态为NEW的补发信息,返回该补发信息，不存在则返回空
        //1.2补发类型为补发产品，补发配件 ，存在补发状态为NEW的补发信息,返回该补发信息，不存在则返回空
        //2.父工单下不存在对应补发类型的子工单信息
        //2.1（只保存了未提交去生成工单）,返回补发信息,只会有一条
        //不存在补发信息,返回空

        if (CollectionUtils.isEmpty(scOrderReissues)) {
            return null;
        }
        boolean existReissueTicket = scOrderReissues.stream().anyMatch(t -> StringUtils.isNotEmpty(t.getTicketStatus()));

        ScOrderReissue orderReissue = null;
        if (existReissueTicket) {
            if ("SHIPMENT".equalsIgnoreCase(scOrderReissue.getReissueType())) {
                List<ScOrderReissue> filteScorerReissue = scOrderReissues.stream().filter(item ->
                        ("NEW".equals(item.getTicketStatus()) || "PROCESSING".equals(item.getTicketStatus()))
                ).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(filteScorerReissue)) {
                    throw new CustomException("存在未处理的补发工单,不可重复创建");
                }
                List<ScOrderReissue> NewScorerReissue = scOrderReissues.stream().filter(item -> "NEW".equals(item.getStatus())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(NewScorerReissue)) {
                    return null;
                }
                orderReissue = NewScorerReissue.get(0);
            }else {
                List<ScOrderReissue> NewScorerReissue = scOrderReissues.stream().filter(item -> "NEW".equals(item.getStatus())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(NewScorerReissue)) {
                    return null;
                }
                orderReissue = NewScorerReissue.get(0);
            }
        }else {
            orderReissue = scOrderReissues.get(0);
        }

        if (null != orderReissue) {
            //补发配件
            if ("ACCESSORIES".equals(orderReissue.getReissueType())) {
                List<ScReissueAccessories> reissueAccessories = scReissueAccessoriesService.selectScReissueAccessoriesByReissueId(orderReissue.getId());

                orderReissue.setAccessoriesList(reissueAccessories);
                //补发发货
            }else if("SHIPMENT".equals(orderReissue.getReissueType())){
                List<ScReissueShipment> scReissueShipmentList = scReissueShipmentService.selectScReissueShipmentByReissueId(orderReissue.getId());
                orderReissue.setReissueShipmentList(scReissueShipmentList);
            } else {
                //补发产品
                List<ScReissueGoods> scReissueGoodsList = scReissueGoodsService.selectScReissueGoodsByReissueId(orderReissue.getId());
                orderReissue.setReissueGoodsList(scReissueGoodsList);
            }
        }


        //获取附件信息

        ScOrderReissue reissueAttach = this.selectScOrderReissueById(orderReissue.getId());
        orderReissue.setMeta(reissueAttach.getMeta());
        orderReissue.setImageMeta(reissueAttach.getImageMeta());


        return orderReissue;
    }

    /**
     * 新增订单补发
     *
     * @param scOrderReissue 订单补发
     * @return 结果
     */
    @Override
    public int insertScOrderReissue(ScOrderReissue scOrderReissue) {
        scOrderReissue.setStatus("NEW"); //新增补发默认为NEW
        scOrderReissue.settingDefaultCreate();
        return scOrderReissueMapper.insertScOrderReissue(scOrderReissue);
    }

    /**
     * 修改订单补发
     *
     * @param scOrderReissue 订单补发
     * @return 结果
     */
    @Override
    public int updateScOrderReissue(ScOrderReissue scOrderReissue) {
        scOrderReissue.settingDefaultUpdate();
        return scOrderReissueMapper.updateScOrderReissue(scOrderReissue);
    }



    /**
     * @description: 保存补发配件
     * @author: Moore
     * @date: 2023/11/21 0:25
     * @param
     * @param scOrderReissue
     * @return: void
    **/
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveScOrderReissue(ScOrderReissue scOrderReissue) {

        ScTicket scTicket = scTicketService.selectScTicketByBaseTicketId(scOrderReissue.getTicketId());
        //退货换货不校验sourceId
        if (null == scTicket || ((!ScTicketConstant.TICKET_TYPE_RETURN.equals(scTicket.getTicketType()) && !ScTicketConstant.TICKET_TYPE_REPLACEMENT.equals(scTicket.getTicketType())) && null == scTicket.getSourceId())) {
            throw new CustomException("工单信息获取失败!");
        }

        //判断修改工单状态为处理中
        if ("NEW".equals(scTicket.getTicketStatus())) {
            scTicketService.setTikceStatusProcessing(scOrderReissue.getTicketId());
        }

        if (scOrderReissue.getId() != null) {
            this.saveScOrderReissueDetail(scOrderReissue);
            return;
        }

        int count = 0;
        scOrderReissue.setStateOrRegion(StringUtil.isNotEmpty(scOrderReissue.getStateOrRegion()) ? scOrderReissue.getStateOrRegion().trim() : null);


        //判断是否存在处理中的工单
        ScOrderReissue reissue = new ScOrderReissue();
        reissue.setTicketId(scOrderReissue.getTicketId());
        reissue.setReissueType(scOrderReissue.getReissueType());
        List<ScOrderReissue> orderReissueList = scOrderReissueMapper.selectScOrderReissueByTicketList(reissue);

        //补发发货的父工单下只允许创建一个未处理完成的补发发货子工单，  补发产品，补发配件可以有多个
        if ("SHIPMENT".equalsIgnoreCase(scOrderReissue.getReissueType())) {
            //工单状态为 待处理或处理中   --或者  补发状态为 待补发  退回 发货系统异常的单据 禁止重复创建
            List<ScOrderReissue> filteScorerReissue = orderReissueList.stream().filter(item ->
                    "NEW".equals(item.getTicketStatus()) || "PROCESSING".equals(item.getTicketStatus())
            ).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filteScorerReissue)) {
                throw new CustomException("存在未处理的补发工单,不可重复创建");
            }
        }



        //始终操作NEW状态的工单
        ScOrderReissue orderReissueFlag = null;
       /* List<ScOrderReissue> NewScorerReissue = orderReissueList.stream().filter(item -> "NEW".equals(item.getStatus())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(NewScorerReissue)) {
            orderReissueFlag = NewScorerReissue.get(0);
        }*/

        //封装补发主表信息
        this.scOrderReissuePack(scOrderReissue);


        //存在附件后，发货方式不能为空
        if (!StringUtils.isEmpty(scOrderReissue.getMeta())&& StringUtil.isEmpty(scOrderReissue.getShippingType())){
            throw new CustomException("请维护发货方式后保存");
        }



        //保存补发信息
        if (null == orderReissueFlag) {

            //生成补发单号
            ScOrderReissue record = new ScOrderReissue();
            record.setReissueType(scOrderReissue.getReissueType()); //补发类型
            record.setAmazonOrderId(scOrderReissue.getAmazonOrderId());//补发单号
            List<ScOrderReissue> scOrderReissues = this.selectScOrderReissueList(record);
            int times = 1;
            if (!CollectionUtils.isEmpty(scOrderReissues)) {
                times = scOrderReissues.size() + 1;
            }

            if ("ACCESSORIES".equals(scOrderReissue.getReissueType())) {
                scOrderReissue.setReissueNumber(scOrderReissue.getAmazonOrderId() + "-SP-" + times);
            } else if ("SHIPMENT".equals(scOrderReissue.getReissueType())) {
                scOrderReissue.setReissueNumber(scOrderReissue.getAmazonOrderId() + "-FH-" + times);
            } else {
                scOrderReissue.setReissueNumber(scOrderReissue.getAmazonOrderId() + "-BF-" + times);
            }
            scOrderReissue.setOrderId(scTicket.getOrderId()); //订单号
            scOrderReissue.setShopId(scTicket.getShopId());//店铺ID
            count = this.insertScOrderReissue(scOrderReissue);
        } else {
            scOrderReissue.setId(orderReissueFlag.getId());
            scOrderReissue.setTicketId(null); //更新时,不更新主表工单ID
            count = this.updateScOrderReissue(scOrderReissue);
        }

        //保存补发配件
        if ("ACCESSORIES".equals(scOrderReissue.getReissueType())) {
            List<ScReissueAccessories> reissueAccessoriesList = scOrderReissue.getAccessoriesList();
            if (!CollectionUtils.isEmpty(reissueAccessoriesList)) {
                reissueAccessoriesList.forEach(scReissueAccessories -> {
                    scReissueAccessories.setReissueId(scOrderReissue.getId()); //会话主键
                    scReissueAccessories.setOrganizationId(scOrderReissue.getOrganizationId()); //组织ID
                    scReissueAccessories.setParentSku(getParentSkuByChildSku(StringUtils.isEmpty(scReissueAccessories.getErpSku()) ? null : Collections.singletonList(scReissueAccessories.getErpSku()),scOrderReissue.getOrganizationId().intValue()));
                });
              scReissueAccessoriesService.saveScReissueAccessories(reissueAccessoriesList);
//                scReissueAccessoriesService.saveScReissueAccessoriesDistinct(reissueAccessoriesList, scOrderReissue.getId());
            }
        } else if ("SHIPMENT".equals(scOrderReissue.getReissueType())) {
            List<ScReissueShipment> reissueShipmentList = scOrderReissue.getReissueShipmentList();
            if (!CollectionUtils.isEmpty(reissueShipmentList)) {
                reissueShipmentList.forEach(scReissueShipment -> {
                    scReissueShipment.setReissueId(scOrderReissue.getId());//会话主键
                    scReissueShipment.setOrganizationId(scOrderReissue.getOrganizationId());
                    scReissueShipment.setParentSku(getParentSkuByChildSku(StringUtils.isEmpty(scReissueShipment.getErpSku()) ? null : Collections.singletonList(scReissueShipment.getErpSku()),scOrderReissue.getOrganizationId().intValue()));
                });
                scReissueShipmentService.saveScReissueShipment(reissueShipmentList);
            }
        } else {
            List<ScReissueGoods> reissueGoodsList = scOrderReissue.getReissueGoodsList();
            if (!CollectionUtils.isEmpty(reissueGoodsList)) {
                reissueGoodsList.forEach(scReissueGoods -> {
                    scReissueGoods.setReissueId(scOrderReissue.getId());//会话主键
                    scReissueGoods.setOrganizationId(scOrderReissue.getOrganizationId()); //组织ID
                    scReissueGoods.setParentSku(getParentSkuByChildSku(StringUtils.isEmpty(scReissueGoods.getErpSku()) ? null : Collections.singletonList(scReissueGoods.getErpSku()),scOrderReissue.getOrganizationId().intValue()));
                });
                scReissueGoodsService.saveScReissueGoods(reissueGoodsList);
            }
        }
        //保存补发附件
        ticketAttachesService.saveMeta(scOrderReissue.getMeta(), "sc_order_reissue", scOrderReissue.getId(), scOrderReissue.getOrganizationId());
        ticketAttachesService.saveMetaSpecType(scOrderReissue.getImageMeta(), "sc_order_reissue", scOrderReissue.getId(), scOrderReissue.getOrganizationId(),"img");
    }





    /**
     * @param
     * @param scOrderReissue
     * @description:封装主表补发行数据
     * @author: Moore
     * @date: 2023/11/21 0:12
     * @return: void
     **/
    public void scOrderReissuePack(ScOrderReissue scOrderReissue) {
        if ("ACCESSORIES".equals(scOrderReissue.getReissueType())) {
            List<ScReissueAccessories> reissueAccessoriesList = scOrderReissue.getAccessoriesList();
            if (!CollectionUtils.isEmpty(reissueAccessoriesList)) {
                //设置主表信息
                String categoryName = reissueAccessoriesList.stream().map(e -> e.getAccessoriesCategoryName()).collect(Collectors.joining(","));
                String accessoriesName = reissueAccessoriesList.stream().map(e -> e.getAccessoriesName()).collect(Collectors.joining(","));
                String erpSku = reissueAccessoriesList.stream().map(e -> e.getErpSku()).collect(Collectors.joining(","));
                String reissueQuantity = reissueAccessoriesList.stream().map(e -> String.valueOf(e.getQuantity())).collect(Collectors.joining(","));
                scOrderReissue.setCategoryName(categoryName); //分类名
                scOrderReissue.setProductName(accessoriesName);//配件名
                scOrderReissue.setErpSku(erpSku);//erpsku
                scOrderReissue.setReissueQuantity(reissueQuantity);//补发数量
                scOrderReissue.setParentSku(getParentSkuByChildSku(reissueAccessoriesList.stream().map(ScReissueAccessories::getErpSku).filter(StringUtils::isNotEmpty).collect(Collectors.toList()),scOrderReissue.getOrganizationId().intValue()));
            }
        } else if ("SHIPMENT".equals(scOrderReissue.getReissueType())) {
//            scOrderReissue.setCategoryName("发货");
            List<ScReissueShipment> reissueShipmentList = scOrderReissue.getReissueShipmentList();
            if (!CollectionUtils.isEmpty(reissueShipmentList)) {
                String goodsName = reissueShipmentList.stream().map(e -> e.getGoodsName()).collect(Collectors.joining(","));
                String erpSku = reissueShipmentList.stream().map(e -> e.getErpSku()).collect(Collectors.joining(","));
                String reissueQuantity = reissueShipmentList.stream().map(e -> String.valueOf(e.getQuantity())).collect(Collectors.joining(","));
                scOrderReissue.setProductName(goodsName);
                scOrderReissue.setErpSku(erpSku);
                scOrderReissue.setReissueQuantity(reissueQuantity);
                scOrderReissue.setParentSku(getParentSkuByChildSku(reissueShipmentList.stream().map(ScReissueShipment::getErpSku).filter(StringUtils::isNotEmpty).collect(Collectors.toList()),scOrderReissue.getOrganizationId().intValue()));
                //获取分类
                List<String> erpSKuList = reissueShipmentList.stream().filter(e -> !StringUtils.isEmpty(e.getErpSku())).map(ScReissueShipment::getErpSku).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(erpSKuList)) {
                    List<String> erpCategoryNams = productsMapper.selectProductCategoryName(erpSKuList);
                    scOrderReissue.setCategoryName(erpCategoryNams.stream().filter(item -> !StringUtils.isEmpty(item)).collect(Collectors.joining(",")));
                }
            }
        } else {
//            scOrderReissue.setCategoryName("产品");
            List<ScReissueGoods> reissueGoodsList = scOrderReissue.getReissueGoodsList();
            if (!CollectionUtils.isEmpty(reissueGoodsList)) {
                String goodsName = reissueGoodsList.stream().map(e -> e.getGoodsName()).collect(Collectors.joining(","));
                String erpSku = reissueGoodsList.stream().map(e -> e.getErpSku()).collect(Collectors.joining(","));
                String reissueQuantity = reissueGoodsList.stream().map(e -> String.valueOf(e.getQuantity())).collect(Collectors.joining(","));
                scOrderReissue.setProductName(goodsName);
                scOrderReissue.setErpSku(erpSku);
                scOrderReissue.setReissueQuantity(reissueQuantity);
                scOrderReissue.setParentSku(getParentSkuByChildSku(reissueGoodsList.stream().map(ScReissueGoods::getErpSku).filter(StringUtils::isNotEmpty).collect(Collectors.toList()),scOrderReissue.getOrganizationId().intValue()));

                List<String> erpSKuList = reissueGoodsList.stream().filter(e -> !StringUtils.isEmpty(e.getErpSku())).map(ScReissueGoods::getErpSku).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(erpSKuList)) {
                    List<String> erpCategoryNams = productsMapper.selectProductCategoryName(erpSKuList);
                    scOrderReissue.setCategoryName(erpCategoryNams.stream().filter(item -> !StringUtils.isEmpty(item)).collect(Collectors.joining(",")));
                }
            }
        }
    }

    private String getParentSkuByChildSku(List<String> childSkuList,Integer orgId) {
        if (CollectionUtils.isEmpty(childSkuList)) {
            return null;
        }
        if (!systemGlobalConfigService.isEnableConfig(orgId, SystemGlobalConfigEnum.SKU_VERSION_INIT_CONFIG)) {
            return null;
        }
        List<SkuChildren> skuChildrenList = skuChildrenService.selectByOrgIdAndSkus(orgId, childSkuList);
        return CollectionUtil.isEmpty(skuChildrenList) ? null : skuChildrenList.stream().filter(item->StringUtils.isNotEmpty(item.getParentSku())).map(SkuChildren::getParentSku).distinct().collect(Collectors.joining(","));
    }


    /**
     * @description: 更新补发订单
     * @author: Moore
     * @date: 2023/11/21 0:29
     * @param
     * @param scOrderReissue
     * @return: void
    **/
    @Override
    public void saveScOrderReissueDetail(ScOrderReissue scOrderReissue) {
        if (null == scOrderReissue.getAmazonOrderId()) {
            throw new CustomException("补发订单不能为空!");
        }
        ScOrderReissue orderReissue = scOrderReissueMapper.selectScOrderReissueById(scOrderReissue.getId());
        if (null == orderReissue) {
            throw new CustomException("补发信息获取失败");
        }


        //存在附件后，发货方式不能为空
        if (!StringUtils.isEmpty(scOrderReissue.getMeta())&& StringUtil.isEmpty(scOrderReissue.getShippingType())){
            throw new CustomException("请维护发货方式后保存");
        }

        //封装主表信息
        scOrderReissue.setTicketId(null); //更细不刷新主表工单ID
        this.scOrderReissuePack(scOrderReissue);
        scOrderReissue.settingDefaultUpdate();
        //保存补发信息
        this.updateById(scOrderReissue);


        //保存补发配件
        if ("ACCESSORIES".equals(scOrderReissue.getReissueType())) {
            List<ScReissueAccessories> reissueAccessoriesList = scOrderReissue.getAccessoriesList();
            if (!CollectionUtils.isEmpty(reissueAccessoriesList)) {
                reissueAccessoriesList.forEach(scReissueAccessories -> {
                    scReissueAccessories.setReissueId(scOrderReissue.getId());
                    scReissueAccessories.setOrganizationId(scOrderReissue.getOrganizationId());
                    scReissueAccessories.setParentSku(getParentSkuByChildSku(StringUtils.isEmpty(scReissueAccessories.getErpSku()) ? null : Collections.singletonList(scReissueAccessories.getErpSku()),scOrderReissue.getOrganizationId().intValue()));
                });
                scOrderReissue.setParentSku(reissueAccessoriesList.stream().map(ScReissueAccessories::getParentSku).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.joining(",")));
                scReissueAccessoriesService.saveScReissueAccessories(reissueAccessoriesList);
            }
        } else if ("SHIPMENT".equals(scOrderReissue.getReissueType())) {
            List<ScReissueShipment> reissueShipmentList = scOrderReissue.getReissueShipmentList();
            if (!CollectionUtils.isEmpty(reissueShipmentList)) {
                reissueShipmentList.forEach(scReissueShipment -> {
                    scReissueShipment.setReissueId(scOrderReissue.getId());
                    scReissueShipment.setOrganizationId(scOrderReissue.getOrganizationId());
                    scReissueShipment.setParentSku(getParentSkuByChildSku(StringUtils.isEmpty(scReissueShipment.getErpSku()) ? null : Collections.singletonList(scReissueShipment.getErpSku()),scOrderReissue.getOrganizationId().intValue()));
                });
                scOrderReissue.setParentSku(reissueShipmentList.stream().map(ScReissueShipment::getParentSku).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.joining(",")));
                scReissueShipmentService.saveScReissueShipment(reissueShipmentList);
            }
        } else {
            List<ScReissueGoods> reissueGoodsList = scOrderReissue.getReissueGoodsList();
            if (!CollectionUtils.isEmpty(reissueGoodsList)) {
                reissueGoodsList.forEach(scReissueGoods -> {
                    scReissueGoods.setReissueId(scOrderReissue.getId());
                    scReissueGoods.setOrganizationId(scOrderReissue.getOrganizationId());
                    scReissueGoods.setParentSku(getParentSkuByChildSku(StringUtils.isEmpty(scReissueGoods.getErpSku()) ? null : Collections.singletonList(scReissueGoods.getErpSku()),scOrderReissue.getOrganizationId().intValue()));
                });
                scOrderReissue.setParentSku(reissueGoodsList.stream().map(ScReissueGoods::getParentSku).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.joining(",")));
                scReissueGoodsService.saveScReissueGoods(reissueGoodsList);
            }
        }

        if (StringUtils.isNotEmpty(scOrderReissue.getParentSku())) {
            this.updateById(scOrderReissue);
        }
        ticketAttachesService.saveMeta(scOrderReissue.getMeta(), "sc_order_reissue", scOrderReissue.getId(), scOrderReissue.getOrganizationId());
        ticketAttachesService.saveMetaSpecType(scOrderReissue.getImageMeta(), "sc_order_reissue", scOrderReissue.getId(), scOrderReissue.getOrganizationId(),"img");

    }

    /**
     *
     *
     * @param user
     * @param scOrderReissue
     */
    @Transactional
    @Override
    public synchronized void submitScOrderReissue(UserEntity user, ScOrderReissue scOrderReissue, Integer contextId) {
        if (null == scOrderReissue.getId()) {
            throw new CustomException("请保存后再提交!");
        }

        ScOrderReissue existScOrderReissue = this.getById(scOrderReissue.getId());
        ScTicket parentScTicket = scTicketService.selectScTicketById(existScOrderReissue.getTicketId());
        //配件为新建或退回状态，在可重新提交工单
        if (StringUtils.isEmpty(scOrderReissue.getStatus()) ) {
            throw new CustomException("提交状态不能为空!");
        }

        if (!scOrderReissue.getStatus().equals("RETURN")&&!scOrderReissue.getStatus().equals("NEW")) {
            throw new CustomException("提交状态错误!");
        }

        if (null == scOrderReissue.getTicketId()||StringUtils.isEmpty(scOrderReissue.getReissueType())) {
            throw new CustomException("工单ID或补发类型不能为空!");
        }


        if ("ACCESSORIES".equals(scOrderReissue.getReissueType())) {
            List<ScReissueAccessories> reissueAccessoriesList = scOrderReissue.getAccessoriesList().stream().filter(item -> null == item.getId()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(reissueAccessoriesList)) {
                throw new CustomException("请保存配件后再提交!");
            }
        } else if ("SHIPMENT".equals(scOrderReissue.getReissueType())) {
            List<ScReissueShipment> reissueShipmentList = scOrderReissue.getReissueShipmentList().stream().filter(item -> null == item.getId()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(reissueShipmentList)) {
                throw new CustomException("请保存发货信息后再提交");
            }
        } else {
            List<ScReissueGoods> reissueGoodsList = scOrderReissue.getReissueGoodsList().stream().filter(item -> null == item.getId()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(reissueGoodsList)) {
                throw new CustomException("请保存产品后再提交!");
            }
        }

        if ("ACCESSORIES".equals(scOrderReissue.getReissueType())) {
            ScReissueAccessories reissueAccessories = scOrderReissue.getAccessoriesList().stream().filter(item -> item.getQuantity() < 0 || item.getQuantity() == 0 || item.getQuantity() == null).findAny().orElse(null);
            if (reissueAccessories != null) {
                throw new CustomException("请填写补发数量,保存后提交！");
            }
        } else if ("SHIPMENT".equals(scOrderReissue.getReissueType())) {
            ScReissueShipment scReissueShipment = scOrderReissue.getReissueShipmentList().stream().filter(item -> item.getQuantity() == null || item.getQuantity() < 0 || item.getQuantity() == 0).findAny().orElse(null);
            if (scReissueShipment != null) {
                throw new CustomException("请填写补发数量，保存后提交！");
            }
        } else {
            ScReissueGoods reissueGoods = scOrderReissue.getReissueGoodsList().stream().filter(item -> item.getQuantity() < 0 || item.getQuantity() == 0 || item.getQuantity() == null).findAny().orElse(null);
            if (reissueGoods != null) {
                throw new CustomException("请填写补发数量,保存后提交！");
            }
        }

        //判断当前父工单下是否存在对应类型进行中的工单
        ScOrderReissue reissue = new ScOrderReissue();
        //父工单调用此提交方法，此处传的是父工单ID，那查询的是父工单下是否存在对应类型NEW或者PROCESSING的补发子数据，
        //补发工单调用此方法，只有一种情况， 此时补发工单被退回
        reissue.setTicketId(scOrderReissue.getTicketId()); //此处前端传入的是当前工单的ID，即在站内信传的站内工单，在补发传的补发工单ID（本质补发退回校验无意义）
        reissue.setReissueType(scOrderReissue.getReissueType());//补发类型
        List<ScOrderReissue> orderReissueList = scOrderReissueMapper.selectScOrderReissueByTicketList(reissue);
        List<ScOrderReissue> filteScorerReissue = orderReissueList.stream().filter(item ->
                "NEW".equals(item.getTicketStatus()) || "PROCESSING".equals(item.getTicketStatus())
        ).collect(Collectors.toList());

        if ("SHIPMENT".equalsIgnoreCase(scOrderReissue.getReissueType())) {
            if (!CollectionUtils.isEmpty(filteScorerReissue)) {
                throw new CustomException("存在未处理的补发工单,不可重复创建");
            }
        }

        //更新补发状态，并设置申请人
        ScOrderReissue scOrderReissueRecod = new ScOrderReissue();
        scOrderReissueRecod.setStatus("SUBMIT");
        scOrderReissueRecod.setApplyBy(user.getName());
        scOrderReissueRecod.setApplyTime(new Date());
        scOrderReissueRecod.setId(scOrderReissue.getId());
        scOrderReissueMapper.updateScOrderReissueStatus(scOrderReissueRecod);

        ScTicket ticket = scTicketService.selectScTicketBySource(scOrderReissue.getId(), ScTicketConstant.TICKET_SOURCE_DOCUMENT_REISSUE);
        if (null == ticket) {
            //生成工单编号
            ScTicket parentTicket = scTicketService.selectScTicketById(scOrderReissue.getTicketId());  //原则上此处传的是父工单ID，上方校验，一般初次必然是空。

           //根据订单号获取补发的次数,创建补发工单号
            ScOrderReissue record = new ScOrderReissue();
            record.setReissueType(scOrderReissue.getReissueType());
            record.setAmazonOrderId(scOrderReissue.getAmazonOrderId());
            List<ScOrderReissue> scOrderReissues = this.selectScOrderReissueList(record);
            int times = 1;
            if (!CollectionUtils.isEmpty(scOrderReissues)) {
                times = scOrderReissues.size(); //此处必然会存在补发单，即补发数量就是工单开始编号
            }
            String ticketNumber = "";
            if ("ACCESSORIES".equals(scOrderReissue.getReissueType())) {
                ticketNumber = parentTicket.getTicketNumber() + "-SP-" + times;
            } else if ("SHIPMENT".equals(scOrderReissue.getReissueType())) {
                ticketNumber = parentTicket.getTicketNumber() + "-FH-" + times;
            } else {
                ticketNumber = parentTicket.getTicketNumber() + "-BF-" + times;
            }

            //生成新工单
            ScTicket scTicket = new ScTicket();
            if ("ACCESSORIES".equals(scOrderReissue.getReissueType())) {
                scTicket.setTicketName(scOrderReissue.getAmazonOrderId() + " 补发配件");
                scTicket.setTicketType(ScTicketConstant.TICKET_TYPE_REISSUE_ACCESSORIES);
            } else if ("SHIPMENT".equals(scOrderReissue.getReissueType())) {
                scTicket.setTicketName(scOrderReissue.getAmazonOrderId() + " 发货");
                scTicket.setTicketType(ScTicketConstant.TICKET_TYPE_REISSUE_SHIPMENT);
            } else {
                scTicket.setTicketName(scOrderReissue.getAmazonOrderId() + " 补发产品");
                scTicket.setTicketType(ScTicketConstant.TICKET_TYPE_REISSUE_GOODS);
            }

            scTicket.setTicketNumber(ticketNumber);

            //工单渠道
            if (scOrderReissue.getOrderId() != null) {
                SaleOrders saleOrders = saleOrdersMapper.selectById(scOrderReissue.getOrderId());
                scTicket.setTicketSource(saleOrders != null ? saleOrders.getChannel() : null);
            }

            scTicket.setOrganizationId(contextId.longValue());
            scTicket.setPriority(ScTicketConstant.TICKET_PRIORITY_MEDIUM);
            scTicket.setAmazonOrderId(scOrderReissue.getAmazonOrderId());
            scTicket.setOrderId(scOrderReissue.getOrderId()); //提交补发设置订单ID
            scTicket.setMatchOrderType(parentTicket.getMatchOrderType()); //设置补发类型
            scTicket.setShopId(scOrderReissue.getShopId());
            scTicket.setCustomerId(scOrderReissue.getCustomerId());
            scTicket.setParentTicketId(scOrderReissue.getTicketId());
            scTicket.setSourceId(scOrderReissue.getId());
            scTicket.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_REISSUE);
            scTicket.setOperateStatus(ScTicketConstant.TICKET_OPERATE_APPROVE);
            scTicket.settingDefaultCreate();
            scTicket.setParentSku(parentScTicket.getParentSku());
            scTicket.setGoodsSku(parentScTicket.getGoodsSku());
            scTicketService.insertScTicket(scTicket);

            //记录工单日志
            ScTicketLog ticketLog = new ScTicketLog();
            ticketLog.setTicketId(scOrderReissue.getTicketId());
            ticketLog.setOperateType("SUBMIT");
            ticketLog.setOperateTime(new Date());
            if ("ACCESSORIES".equals(scOrderReissue.getReissueType())) {
                ticketLog.setOperateContent("提交补发配件申请，生成子工单：" + scTicket.getTicketNumber());
            } else if ("SHIPMENT".equals(scOrderReissue.getReissueType())) {
                ticketLog.setOperateContent("提交发货申请，生成子工单：" + scTicket.getTicketNumber());
            } else {
                ticketLog.setOperateContent("提交补发产品申请，生成子工单：" + scTicket.getTicketNumber());
            }
            scTicketLogService.insertScTicketLog(ticketLog);
        } else {
            //退回后二次提交逻辑
            //修改工单状态
            ticket.setTicketStatus(ScTicketConstant.TICKET_STATUS_NEW);
            ticket.setOperateStatus(ScTicketConstant.TICKET_OPERATE_APPROVE);
            scTicketService.updateScTicketStatus(ticket);

            //修改审批人 ,并记录状态，读取配置重新设置处理人
            ConfTicketAssign confTicketAssign = confTicketAssignService.selectConfTicketAssignByTicketType(ticket.getOrganizationId(), ticket.getTicketType());
            if (null != confTicketAssign) {
                scTicketService.updateScTicketHandlerBy(confTicketAssign, ticket);
            }

            //记录工单日志
            ScTicketLog ticketLog = new ScTicketLog();
            ticketLog.setTicketId(ticket.getId());
            ticketLog.setOperateType("RE_SUBMIT"); //重新提交
            ticketLog.setOperateTime(new Date());
            if ("ACCESSORIES".equals(scOrderReissue.getReissueType())) {
                ticketLog.setOperateContent("重新提交补发配件申请，子工单：" + ticket.getTicketNumber());
            } else if ("SHIPMENT".equals(scOrderReissue.getReissueType())) {
                ticketLog.setOperateContent("重新提交发货申请，子工单：" + ticket.getTicketNumber());
            } else {
                ticketLog.setOperateContent("重新提交补发产品申请，子工单：" + ticket.getTicketNumber());
            }
            scTicketLogService.insertScTicketLog(ticketLog);
        }

    }


    /**
     *
     *
     * @param user
     * @param scOrderReissue
     */
    @Override
    public String submitScOrderReissuePre(UserEntity user, ScOrderReissue scOrderReissue, Integer contextId) {
        if (null == scOrderReissue.getId()) {
            throw new CustomException("请保存后再提交!");
        }

        if (StringUtils.isEmpty(scOrderReissue.getStatus())) {
            throw new CustomException("提交状态不能为空!");
        }

        if (!scOrderReissue.getStatus().equals("RETURN") && !scOrderReissue.getStatus().equals("NEW")) {
            throw new CustomException("提交状态错误!");
        }

        if (null == scOrderReissue.getTicketId() || StringUtils.isEmpty(scOrderReissue.getReissueType())) {
            throw new CustomException("工单ID或补发类型不能为空!");
        }

        if ("ACCESSORIES".equals(scOrderReissue.getReissueType())) {
            List<ScReissueAccessories> reissueAccessoriesList = scOrderReissue.getAccessoriesList().stream().filter(item -> null == item.getId()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(reissueAccessoriesList)) {
                throw new CustomException("请保存配件后再提交!");
            }
        } else if ("SHIPMENT".equals(scOrderReissue.getReissueType())) {
            List<ScReissueShipment> reissueShipmentList = scOrderReissue.getReissueShipmentList().stream().filter(item -> null == item.getId()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(reissueShipmentList)) {
                throw new CustomException("请保存发货信息后再提交");
            }
        } else {
            List<ScReissueGoods> reissueGoodsList = scOrderReissue.getReissueGoodsList().stream().filter(item -> null == item.getId()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(reissueGoodsList)) {
                throw new CustomException("请保存产品后再提交!");
            }
        }

        if ("ACCESSORIES".equals(scOrderReissue.getReissueType())) {
            ScReissueAccessories reissueAccessories = scOrderReissue.getAccessoriesList().stream().filter(item -> item.getQuantity() == null || item.getQuantity() < 0 || item.getQuantity() == 0).findAny().orElse(null);
            if (reissueAccessories != null) {
                throw new CustomException("请填写补发数量,保存后提交！");
            }
        } else if ("SHIPMENT".equals(scOrderReissue.getReissueType())) {
            ScReissueShipment scReissueShipment = scOrderReissue.getReissueShipmentList().stream().filter(item -> item.getQuantity() == null || item.getQuantity() < 0 || item.getQuantity() == 0).findAny().orElse(null);
            if (scReissueShipment != null) {
                throw new CustomException("请填写补发数量，保存后提交！");
            }
        } else {
            ScReissueGoods reissueGoods = scOrderReissue.getReissueGoodsList().stream().filter(item -> item.getQuantity() == null || item.getQuantity() < 0 || item.getQuantity() == 0).findAny().orElse(null);
            if (reissueGoods != null) {
                throw new CustomException("请填写补发数量,保存后提交！");
            }
        }
        //仅对于保存后未提交的补发子工单(补发配件，补发产品)做提示，提交后的编辑再次提交不做校验
        ScTicket ticket = scTicketService.selectScTicketBySource(scOrderReissue.getId(), ScTicketConstant.TICKET_SOURCE_DOCUMENT_REISSUE);
        if (ticket != null) {
            return null;
        }
        ScOrderReissue reissue = new ScOrderReissue();
        reissue.setTicketId(scOrderReissue.getTicketId());
        reissue.setReissueType(scOrderReissue.getReissueType());
        List<ScOrderReissue> orderReissueList = scOrderReissueMapper.selectScOrderReissueByTicketList(reissue);
        List<ScOrderReissue> filteScorerReissue = orderReissueList.stream().filter(item ->
                "NEW".equals(item.getTicketStatus()) || "PROCESSING".equals(item.getTicketStatus())
        ).collect(Collectors.toList());
        //补发发货
        if ("SHIPMENT".equalsIgnoreCase(scOrderReissue.getReissueType())) {
            if (!CollectionUtils.isEmpty(filteScorerReissue)) {
                throw new CustomException("存在未处理的补发工单,不可重复创建");
            }
        } else {
            //补发产品,补发配件
            if (!CollectionUtils.isEmpty(filteScorerReissue)) {
                return "存在未处理的补发工单";
            }
        }
        return null;
    }


    /**
     * @param
     * @param scOrderReissue
     * @description: 确认补发配件，
     * @author: Moore
     * @date: 2023/11/20 23:36
     * @return: int
     **/
    @Transactional
    @Override
    public int confrimScOrderReissue(ScOrderReissue scOrderReissue) {
        if (null == scOrderReissue.getId() || null == scOrderReissue) {
            throw new CustomException("补发订单不存在,无法操作");
        }
        ScOrderReissue orderReissue = scOrderReissueMapper.selectScOrderReissueById(scOrderReissue.getId());
        if ("APPROVED".equals(orderReissue.getStatus())) {
            throw new CustomException("已发送补发，不可确认,确认仅为线下发货!");
        }
        if ("FINISHED".equals(orderReissue.getStatus())) {
            throw new CustomException("补发已完成，不可重复确认");
        }
        ScOrderReissue scOrderReissueUpdate= new ScOrderReissue();
        scOrderReissueUpdate.setId(scOrderReissue.getId());
        scOrderReissueUpdate.setTrackingNo(scOrderReissue.getTrackingNo());

        int count = 0;
        ScTicket scTicket = scTicketService.selectScTicketBySource(scOrderReissue.getId(), ScTicketConstant.TICKET_SOURCE_DOCUMENT_REISSUE);
        if (StringUtils.isEmpty(scOrderReissue.getTrackingNo())) {
            scOrderReissueUpdate.setStatus("APPROVED");//已补发
            scOrderReissueUpdate.setApproveTime(new Date());
            count = scOrderReissueMapper.updateScOrderReissueStatus(scOrderReissueUpdate);
        } else {
            scOrderReissueUpdate.setStatus("FINISHED");//已完成
            scOrderReissueUpdate.setApproveTime(new Date());
            count = scOrderReissueMapper.updateScOrderReissueStatus(scOrderReissueUpdate);
        }
        scTicket.setTicketStatus(ScTicketConstant.TICKET_STATUS_FINISH); //已结束
        scTicket.setOperateStatus("VIEW"); //操作状态
        scTicketService.updateScTicketStatus(scTicket);
        //记录工单日志
        ScTicketLog ticketLog = new ScTicketLog();
        ticketLog.setTicketId(scTicket.getId());
        ticketLog.setOperateType("PROCESS"); //处理
        ticketLog.setOperateTime(new Date());
        if ("GOODS".equals(orderReissue.getReissueType())) {
            ticketLog.setOperateContent("确认补发产品");
        } else if ("SHIPMENT".equals(orderReissue.getReissueType())) {
            ticketLog.setOperateContent("确认发货");
        } else {
            ticketLog.setOperateContent("确认补发配件");
        }
        scTicketLogService.insertScTicketLog(ticketLog);
        return count;
    }

    @Transactional
    @Override
    public  Map<String, Object>  approveScOrderReissue(ScOrderReissue scOrderReissue) {
        int count = 0;

        if (null==scOrderReissue.getId() || null==scOrderReissue) {
            throw new CustomException("补发订单不存在,无法操作");
        }
        ScOrderReissue orderReissue = scOrderReissueMapper.selectScOrderReissueById(scOrderReissue.getId());
        if (orderReissue==scOrderReissue) {
            throw new CustomException("补发订单不存在,无法操作");
        }
        if ("APPROVED".equals(orderReissue.getStatus())||"FINISHED".equals(orderReissue.getStatus())) {
            throw new CustomException("补发已操作，不可重复发送");
        }

        scOrderReissue.setStatus("APPROVED"); //补发状态修改为已补发
        scOrderReissue.setApproveTime(new Date());


        //修改工单操作状态
        ScTicket scTicket = scTicketService.selectScTicketBySource(scOrderReissue.getId(), ScTicketConstant.TICKET_SOURCE_DOCUMENT_REISSUE);
        if (null != scTicket) {
            //修改工单状态
            if ("NEW".equals(scTicket.getTicketStatus())) {
                scTicket.setTicketStatus(ScTicketConstant.TICKET_STATUS_PROCESSING); //处理中
                scTicketService.updateScTicketStatus(scTicket);
            }

        }

        //记录工单日志
        ScTicketLog ticketLog = new ScTicketLog();
        ticketLog.setTicketId(scTicket.getId());
        ticketLog.setOperateType("APPROVE");
        ticketLog.setOperateTime(new Date());
//        ticketLog.setOperateContent("审核通过补发申请工单");


        Map<String, Object> resultMap = submitShWareHouse(scOrderReissue.getId(),scTicket);
        ticketLog.setOperateContent((String) resultMap.get("message"));
        if (200 == (Integer) resultMap.get("code")) {
            scOrderReissue.setFbbCreateFlag("Y");
        } else {
            scOrderReissue.setStatus("FAIL");  //发货系统异常
        }
        scOrderReissueMapper.updateScOrderReissueStatus(scOrderReissue);
         scTicketLogService.insertScTicketLog(ticketLog);
         //判断补发产品工单生成的补发订单是否有退货计划且退货计划状态不是草稿，审核拒回，已取消，
        //如果满足，补发工单表的补发金额=sellersku的销售单价*补发数量
        if (ScTicketConstant.TICKET_TYPE_REISSUE_GOODS.equals(scTicket.getTicketType())) {

            ScTicket parentScTicket = scTicketService.selectScTicketById(scTicket.getParentTicketId());

            List<ScReissueGoods> scReissueGoods = scReissueGoodsService.selectScReissueGoodsByReissueId(scOrderReissue.getId());
            BigDecimal reissueAmount = scReissueGoods.stream().filter(goods -> goods.getQuantity() != null && goods.getSellerSkuPrice() != null).map(goods -> goods.getSellerSkuPrice().multiply(new BigDecimal(goods.getQuantity().toString()))).reduce(BigDecimal.ZERO, BigDecimal::add);
            ScTicket updateScticket = new ScTicket();
            updateScticket.setId(scTicket.getId());
            Integer returnPlanOrderCondition = scOrderReissueMapper.selectReturnPlanStatusByOrderNo(parentScTicket.getAmazonOrderId(), parentScTicket.getOrganizationId());
            if (new Integer(1).equals(returnPlanOrderCondition)) {
                updateScticket.setReissueAmount(reissueAmount);
            } else {
                updateScticket.setReissueAmount(BigDecimal.ZERO);
            }
            scTicketService.updateScTickeByTicketId(updateScticket);


        }
        return resultMap;
    }


    /**
     * @param
     * @param reissueId
     * @description:校验补发配件库存是否超标
     * @author: Moore
     * @date: 2025/6/16 13:44
     * @return: java.lang.Boolean true 通过，false 不通过
     **/
    public Boolean submitAccessoriesStockVerify(Long reissueId) {
        ScOrderReissue orderReissue = scOrderReissueMapper.selectScOrderReissueById(reissueId);
        if ("ACCESSORIES".equals(orderReissue.getReissueType())) {
            List<ScReissueAccessories> scReissueAccessories = scReissueAccessoriesService.selectScReissueAccessoriesByReissueId(orderReissue.getId());
            //库存小于发货数量
            List<ScReissueAccessories> filterStock = scReissueAccessories.stream().filter(item -> item.getQuantity() != null && item.getStockQuantity().intValue() < item.getQuantity()).collect(Collectors.toList());
            return CollectionUtils.isEmpty(filterStock);
        }
        return Boolean.TRUE;
    }




    /**
     * 提交到四海仓库系统
     */
    private Map<String, Object> submitShWareHouse(Long reissueId,ScTicket scTicket) {
        Map<String, Object> resultMap = new HashMap<>();
        ScOrderReissue orderReissue = scOrderReissueMapper.selectScOrderReissueById(reissueId);
        SaleOrderApiRequest shSaleOrder = new SaleOrderApiRequest();
        //补发产品或者补发配件，传递店铺
        if (ScTicketConstant.TICKET_TYPE_REISSUE_GOODS.equals(scTicket.getTicketType()) || ScTicketConstant.TICKET_TYPE_REISSUE_ACCESSORIES.equals(scTicket.getTicketType())) {
            shSaleOrder.setChannelAccount(accountService.getById(orderReissue.getShopId()).getFlag());
        }


        shSaleOrder.setOrderNo(orderReissue.getReissueNumber().trim());//订单号
        shSaleOrder.setShipToCountry(orderReissue.getCountryCode());//目的国家
        shSaleOrder.setShipToState(orderReissue.getStateOrRegion());//目的州
        shSaleOrder.setShipToCity(orderReissue.getCity());//目的城市
        shSaleOrder.setShipToPostal(orderReissue.getPostalCode());//邮编
        shSaleOrder.setShipToAddress1(orderReissue.getAddress());//地址
        shSaleOrder.setShipToAddress2(orderReissue.getAddress2()); //地址2
        shSaleOrder.setShipToAddress3(orderReissue.getAddress3());//地址3
        shSaleOrder.setShipToContact(orderReissue.getCustomerName());//联系人
        shSaleOrder.setShipToTelephone(orderReissue.getPhone());//联系电话

        //商品行
        List<SaleOrderItemApiRequest> productItemList = new ArrayList<>();

        List<ScReissueAccessories> reissueAccessories = new ArrayList<>();
        List<ScReissueGoods> scReissueGoodsList = new ArrayList<>();
        List<ScReissueShipment> scReissueShipmentList = new ArrayList<>();
        //判断补发类型
        if ("ACCESSORIES".equals(orderReissue.getReissueType())) {
            reissueAccessories = scReissueAccessoriesService.selectScReissueAccessoriesByReissueId(orderReissue.getId());
            reissueAccessories.forEach(scReissueAccessories -> {
                SaleOrderItemApiRequest productItem = new SaleOrderItemApiRequest();
                productItem.setSku(scReissueAccessories.getErpSku()); //发货产品
                productItem.setQuantity(scReissueAccessories.getQuantity());//发货数量
                productItem.setIsInsure(0);//保价
                productItem.setIsSignature(0);//是否签收
                productItemList.add(productItem);
            });

            List<ScReissueAccessories> noStockQuantity = reissueAccessories.stream().filter(item -> 0 == item.getStockQuantity()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(noStockQuantity)){
                throw new CustomException("当前存在无库存项,不可发送");
            }


        } else if ("SHIPMENT".equals(orderReissue.getReissueType())) {
            //查询对应工单事项是否是测评
            ScTicketHandle scTicketHandle = scTicketHandleService.selectScTicketHandleByTicketId(orderReissue.getTicketId());
            if ("20".equals(scTicketHandle.getMatter())) {
                shSaleOrder.setIs_sample_order("true");
            }else {
                shSaleOrder.setIs_sample_order("false");
            }
            //父工单id查询手工订单
            ScManualOrder scManualOrder = scManualOrderMapper.findByTicketId(orderReissue.getTicketId());
            if (scManualOrder != null && StringUtil.isNotEmpty(scManualOrder.getCustomerOuterId())) {
                shSaleOrder.setCustomer_outer_id(scManualOrder.getCustomerOuterId());
            }

            scReissueShipmentList = scReissueShipmentService.selectScReissueShipmentByReissueId(orderReissue.getId());
            //发货
            scReissueShipmentList.forEach(scReissueShipment -> {
                SaleOrderItemApiRequest productItem = new SaleOrderItemApiRequest();
                productItem.setSku(scReissueShipment.getErpSku());
                productItem.setQuantity(scReissueShipment.getQuantity());
                productItem.setIsInsure(0); //保价
                productItem.setIsSignature(0);//是否签收
                productItemList.add(productItem);
            });

            List<ScReissueShipment> noStockQuantity = scReissueShipmentList.stream().filter(item -> 0 == item.getStockQuantity()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(noStockQuantity)){
                throw new CustomException("当前存在无库存项,不可发送");
            }

        } else {
            //补发商品
            //判断补发产品工单生成的补发订单是否有退货计划且退货计划状态不是草稿，审核拒回，已取消，
            //如果满足，
            //发货订单列表中补发订单的销售金额=sellersku的销售单价*补发数量  （这个字段只针对于西昊项目）



            AtomicInteger returnPlanCondition = new AtomicInteger(0);
            scReissueGoodsList = scReissueGoodsService.selectScReissueGoodsByReissueId(orderReissue.getId());
            ScTicket parentScTicket = scTicketService.selectScTicketById(scTicket.getParentTicketId());
            returnPlanCondition.set(scOrderReissueMapper.selectReturnPlanStatusByOrderNo(parentScTicket.getAmazonOrderId(),parentScTicket.getOrganizationId()));
            scReissueGoodsList.forEach(scReissueGoods -> {
                SaleOrderItemApiRequest productItem = new SaleOrderItemApiRequest();
                productItem.setSku(scReissueGoods.getErpSku());
                productItem.setQuantity(scReissueGoods.getQuantity());
                productItem.setIsInsure(0);
                productItem.setIsSignature(0);
                //todo 西昊项目，补发产品时，需要将产品的销售单价传递给仓库系统,非西昊项目不用传
                if (new Integer(1).equals(returnPlanCondition.get())) {
                    productItem.setSkuAmount((Optional.ofNullable(scReissueGoods.getSellerSkuPrice()).orElse(BigDecimal.ZERO)).multiply(new BigDecimal(scReissueGoods.getQuantity().toString())));
                }else {
                    productItem.setSkuAmount(BigDecimal.ZERO);
                }

                productItemList.add(productItem);
            });
        }

        List<ScReissueGoods> noStockQuantity = scReissueGoodsList.stream().filter(item -> 0 == item.getStockQuantity()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(noStockQuantity)){
            throw new CustomException("当前存在无库存项目,不可发送");
        }

        shSaleOrder.setProductItems(productItemList);

        LambdaQueryChainWrapper<TicketAttaches> queryObs = ticketAttachesService.lambdaQuery();
        queryObs.eq(TicketAttaches::getDocumentId, reissueId);
        queryObs.eq(TicketAttaches::getDocumentType, "sc_order_reissue");
        queryObs.isNull(TicketAttaches::getType);
        List<TicketAttaches> listObs = queryObs.list();

        if (!CollectionUtils.isEmpty(listObs)) {
            //有上传面单,前端传入承运商
            shSaleOrder.setCarrier(orderReissue.getShippingType());
            shSaleOrder.setCarrierCode(orderReissue.getShippingType());
            if (StringUtils.isEmpty(orderReissue.getShippingType())) {
                throw new CustomException("发货方式不能为空");
            }

            ArrayList<AttachInfoItemRequest> obsFileList = new ArrayList<>();
            listObs.forEach(obsAttachment -> {
                AttachInfoItemRequest obsFile = new AttachInfoItemRequest();
                obsFile.setName(StringUtils.substringBeforeLast(obsAttachment.getFileName(), "."));//不带后缀文件名
                obsFile.setUrl(obsAttachment.getFileUrl());//访问路径
                obsFile.setType(0); //附件类型 面单
                obsFileList.add(obsFile);
            });
            shSaleOrder.setAttachInfoItems(obsFileList);
        }


        //日志
        SysIfInvokeOutbound ifInvokeOutbound = new SysIfInvokeOutbound();
        ifInvokeOutbound.setInterfaceName("补发配件/产品/发货");
        ifInvokeOutbound.setRequestParameter(JSONObject.toJSONString(shSaleOrder));//请求参数
        ifInvokeOutbound.setRequestTime(new Date());
        logger.info("补发配件/产品/发货数据请求参数:{},ID:{}", JSONObject.toJSONString(shSaleOrder),reissueId);
        try {
            //调用补发接口
            orderOpenApiService.createdOrder( AuthContextHolder.getAuthUserDetails(), orderReissue.getOrganizationId().intValue(), shSaleOrder);
            logger.info("补发配件/产品/发货数据响应内容：{}", shSaleOrder);
            if ("ACCESSORIES".equals(orderReissue.getReissueType())) {
                for (ScReissueAccessories reissueAccessory : reissueAccessories) {
                    reissueAccessory.setSendStatus(String.valueOf(0));
                    reissueAccessory.setRespMsg("创建补发配件订单成功");
                    scReissueAccessoriesService.updateReissueAccessoriesSendStatus(reissueAccessory);
                }
            } else if ("SHIPMENT".equals(orderReissue.getReissueType())) {
                for (ScReissueShipment scReissueShipment : scReissueShipmentList) {
                    scReissueShipment.setSendStatus("0");
                    scReissueShipment.setRespMsg("创建发货订单成功");
                    scReissueShipmentService.updateReissueShipmentSendStatus(scReissueShipment);

                }
            } else {
                for (ScReissueGoods scReissueGoods : scReissueGoodsList) {
                    scReissueGoods.setSendStatus(String.valueOf(0));
                    scReissueGoods.setRespMsg("创建补发商品订单成功");
                    scReissueGoodsService.updateReissueGoodsSendStatus(scReissueGoods);
                }
            }

            //插入接口调用日志
            ifInvokeOutbound.setResponseTime(System.currentTimeMillis());
            ifInvokeOutbound.setResponseCode("200");
            ifInvokeOutbound.setResponseContent(JSON.toJSONString(shSaleOrder));
            invokeLogStrategy.insertSysIfInvokeOutbound(ifInvokeOutbound);
            resultMap.put("code", 200);
            resultMap.put("message", "发送仓库系统成功");
        } catch (RuntimeException e) {
            if ("ACCESSORIES".equals(orderReissue.getReissueType())) {
                for (ScReissueAccessories reissueAccessory : reissueAccessories) {
                    reissueAccessory.setSendStatus(String.valueOf(1));
                    reissueAccessory.setRespMsg("创建补发配件失败");
                    scReissueAccessoriesService.updateReissueAccessoriesSendStatus(reissueAccessory);
                }
            } else if ("SHIPMENT".equals(orderReissue.getReissueType())) {
                for (ScReissueShipment scReissueShipment : scReissueShipmentList) {
                    scReissueShipment.setSendStatus("1");
                    scReissueShipment.setRespMsg("创建发货失败");
                    scReissueShipmentService.updateReissueShipmentSendStatus(scReissueShipment);
                }
            } else {
                for (ScReissueGoods scReissueGoods : scReissueGoodsList) {
                    scReissueGoods.setSendStatus(String.valueOf(1));
                    scReissueGoods.setRespMsg("创建补发产品失败");
                    scReissueGoodsService.updateReissueGoodsSendStatus(scReissueGoods);
                }
            }
            ifInvokeOutbound.setResponseTime(System.currentTimeMillis());
            ifInvokeOutbound.setResponseContent(e.getMessage());
            ifInvokeOutbound.setResponseCode("404");
            invokeLogStrategy.insertSysIfInvokeOutbound(ifInvokeOutbound);
            logger.error("补发配件/产品/发货数据异常，{}", e);
            resultMap.put("code", 999999);
            if (null != e) {
                resultMap.put("message", "发送仓库系统异常：" + e.getMessage() != null ? e.getMessage().substring(e.getMessage().indexOf(":") + 1) : e);
            } else {
                resultMap.put("message", "发送仓库系统异常");
            }
        } catch (Exception e) {
            if ("ACCESSORIES".equals(orderReissue.getReissueType())) {
                for (ScReissueAccessories reissueAccessory : reissueAccessories) {
                    reissueAccessory.setSendStatus(String.valueOf(1));
                    reissueAccessory.setRespMsg("创建订单失败");
                    scReissueAccessoriesService.updateReissueAccessoriesSendStatus(reissueAccessory);
                }
            } else if ("SHIPMENT".equals(orderReissue.getReissueType())) {
                for (ScReissueShipment scReissueShipment : scReissueShipmentList) {
                    scReissueShipment.setSendStatus("1");
                    scReissueShipment.setRespMsg("创建订单失败");
                    scReissueShipmentService.updateReissueShipmentSendStatus(scReissueShipment);
                }
            } else {
                for (ScReissueGoods scReissueGoods : scReissueGoodsList) {
                    scReissueGoods.setSendStatus(String.valueOf(1));
                    scReissueGoods.setRespMsg("创建订单失败");
                    scReissueGoodsService.updateReissueGoodsSendStatus(scReissueGoods);
                }
            }
            ifInvokeOutbound.setResponseTime(System.currentTimeMillis());
            ifInvokeOutbound.setResponseContent(e.getMessage());
            ifInvokeOutbound.setResponseCode("404");
            invokeLogStrategy.insertSysIfInvokeOutbound(ifInvokeOutbound);
            logger.error("补发配件/产品/发货数据同步异常，{}", e);
            resultMap.put("code", 999999);
            resultMap.put("message", "发送仓库系统异常");
        }
        return resultMap;
    }

    @Override
    public int resumeNormal(Long id) {
        int count = 0;
        ScOrderReissue orderReissue = scOrderReissueMapper.selectScOrderReissueById(id);
        if ("FAIL".equals(orderReissue.getStatus()) && "Y".equals(orderReissue.getFbbCreateFlag())) {
            ScOrderReissue scOrderReissue = new ScOrderReissue();
            scOrderReissue.setId(id);
            scOrderReissue.setStatus("APPROVED");
            count = scOrderReissueMapper.updateScOrderReissueStatus(scOrderReissue);
        } else {
            throw new CustomException("补发状态已发生变化，请刷新后再操作。");
        }
        return count;
    }



    /**
     * @description: 退回补发
     * @author: Moore
     * @date: 2023/11/20 22:19
     * @param
     * @param scOrderReissue
     * @return: int
    **/
    @Transactional
    @Override
    public int returnScOrderReissue(ScOrderReissue scOrderReissue,UserEntity userEntity) {
        int count = 0;
        if (null==scOrderReissue.getId()) {
            throw new CustomException("补发订单不存在,无法操作");
        }
        ScOrderReissue orderReissue = scOrderReissueMapper.selectScOrderReissueById(scOrderReissue.getId());
        if (null==orderReissue) {
            throw new CustomException("补发订单不存在,无法操作");
        }
        if ("FINISHED".equals(orderReissue.getStatus())) {
            throw new CustomException("补发已完成，不可退回");
        }


        //更新审批人
        ScOrderReissue scOrderReissueUpdate = new ScOrderReissue();
        scOrderReissueUpdate.setStatus("RETURN");
        scOrderReissueUpdate.setApproveTime(new Date());
        scOrderReissueUpdate.setId(scOrderReissue.getId());
        count = scOrderReissueMapper.updateScOrderReissueStatus(scOrderReissueUpdate);

        ScTicket scTicketOne = scTicketService.selectScTicketById(scOrderReissue.getTicketId());

        //修改工单状态（处理人还原为创建人）
        ScTicket scTicket = new ScTicket();
        scTicket.setId(scOrderReissue.getTicketId());
        scTicket.setHandlerBy(scTicketOne.getCreatedName());
        scTicket.setUserId(scTicketOne.getCreatedBy().longValue());
        scTicket.setTicketStatus(ScTicketConstant.TICKET_STATUS_NEW);
        scTicket.setOperateStatus(ScTicketConstant.TICKET_OPERATE_PROCESS);
        scTicketService.updateScTicketStatus(scTicket);

        //记录工单日志
        ScTicketLog ticketLog = new ScTicketLog();
        ticketLog.setTicketId(scTicket.getId());
        ticketLog.setOperateType("RETURN");
        ticketLog.setOperateTime(new Date());
        ticketLog.setOperateContent("退回补发申请工单"+(StringUtils.isEmpty(scOrderReissue.getApproveComment()) ? "" : ",原因：" + scOrderReissue.getApproveComment()));
        scTicketLogService.insertScTicketLog(ticketLog);
        return count;
    }


    /**
     * @param
     * @param erpSku
     * @param reissueType reissueType 补发类型
     * @param contextId 组织ID
     * @param productIds productIds 商品IDS
     * @description: 弹窗补发初次查询, 配件或商品
     * @author: Moore
     * @date: 2023/11/27 21:10
     * @return: com.bizark.op.api.entity.op.ticket.ScOrderReissue
     **/
    @Override
    public ScOrderReissue selectWindowMessage(String erpSku, String reissueType, Long contextId, Long[] productIds, Long ticketId) {
        ScOrderReissue scOrderReissue = new ScOrderReissue();
        //补发配件 原始逻辑，返回配件所有
        if ("ACCESSORIES".equals(reissueType)) {
            if (productIds == null || productIds.length <= 0) {
                throw new CustomException("商品ID为空,不能补发配件");
            }
            scOrderReissue.setAccessoriesList(scOrderReissueMapper.selectAccessoriesDetailsList(contextId, Arrays.asList(productIds), null));
        } else if("SHIPMENT".equals(reissueType)){
            if (StringUtils.isEmpty(erpSku)) {
                throw new CustomException("商品为空，无法发货");
            }
            List<ScReissueShipment> scReissueShipmentsRes = new ArrayList<>();

            List<ScReissueShipment> scReissueShipments = scReissueShipmentMapper.selectGoodsDetailsByErpSkus(Arrays.stream(erpSku.split(",")).distinct().collect(Collectors.joining(",")), contextId);
            if (!CollectionUtils.isEmpty(scReissueShipments)) {
                Map<String, List<ScReissueShipment>> collect = scReissueShipments.stream().collect(Collectors.groupingBy(ScReissueShipment::getErpSku));
                for (String erpSkuFlag : collect.keySet()) {
                    ScReissueShipment scReissueShipment = collect.get(erpSkuFlag).get(0);
                    scReissueShipment.setOrgWarehouseCode(null); //发货仓及库存值为空
                    scReissueShipment.setStockQuantity(0L);
                    scReissueShipmentsRes.add(scReissueShipment);
                }
            }
            scOrderReissue.setReissueShipmentList(scReissueShipmentsRes);
        //补发产品  只返回 该sku下一条
        }else {
            if (StringUtils.isEmpty(erpSku)) {
                throw new CustomException("商品为空，无法补发产品");
            }
            List<ScReissueGoods> scReissueGoodsRes = new ArrayList<>();
            ScTicket scTicket = scTicketService.selectScTicketById(ticketId);
            List<ScReissueGoods> erpSkuMapSellerSku = new ArrayList<>();
            //系统订单，查询全部sku
            Boolean systemOrder = new Integer(1).equals(scTicket.getMatchOrderType());
            if (systemOrder) {
                erpSkuMapSellerSku = scOrderReissueMapper.selectErpSkuMapSellerSkuAndPriceByOrderNoAndChannelId(scTicket.getAmazonOrderId(), scTicket.getShopId());
            }
            List<ScReissueGoods> scReissueGoods = scOrderReissueMapper.selectGoodsDetailsByErpSkus(Boolean.TRUE.equals(systemOrder) ? erpSkuMapSellerSku.stream().filter(t->StringUtils.isNotEmpty(t.getErpSku())).flatMap(t->Arrays.stream(t.getErpSku().split(","))).distinct().collect(Collectors.joining(",")) : Arrays.stream(erpSku.split(",")).distinct().collect(Collectors.joining(",")), contextId);
            if (!CollectionUtils.isEmpty(scReissueGoods)) {
                if (ticketId == null) {
                    throw new ErpCommonException("工单ID不能为空");
                }
                Map<String, List<ScReissueGoods>> collect = scReissueGoods.stream().collect(Collectors.groupingBy(ScReissueGoods::getErpSku));
                for (String erpSkuFlag : collect.keySet()) {
                    ScReissueGoods scReissueGoodsFlag = collect.get(erpSkuFlag).get(0);
                    scReissueGoodsFlag.setOrgWarehouseCode(null); //发货仓及库存值为空
                    scReissueGoodsFlag.setStockQuantity(0L);
                    erpSkuMapSellerSku.stream().filter(t->StringUtils.isNotEmpty(t.getErpSku()) && Arrays.asList(t.getErpSku().split(",")).contains(scReissueGoodsFlag.getErpSku())).findFirst().ifPresent(t->{
                        scReissueGoodsFlag.setSellerSku(t.getSellerSku());
                        scReissueGoodsFlag.setSellerSkuPrice(t.getSellerSkuPrice());
                    });
                    scReissueGoodsRes.add(scReissueGoodsFlag);
                }
            }
            scOrderReissue.setReissueGoodsList(scReissueGoodsRes);
        }
        return scOrderReissue;
    }

    @Override
    public int deleteGoodsOrAccessoriesByIds(Long[] ids, String reissueType) {
        int count = 0;
        if ("ACCESSORIES".equals(reissueType)) {
            for (Long id : ids) {
                deleteScReissueAccessoriesCheck(id);
            }
            count = scReissueAccessoriesMapper.deleteScReissueAccessoriesByIds(ids);
        } else if ("SHIPMENT".equals(reissueType)) {
            for (Long id : ids) {
                deleteScReissueShipmentCheck(id);
            }
            count = scReissueShipmentMapper.deleteScReissueShipmentByIds(ids);
        } else {
            for (Long id : ids) {
                deleteScReissueGoodsCheck(id);
            }
            count = scReissueGoodsMapper.deleteScReissueGoodsByIds(ids);
        }
        return count;
    }

    private void deleteScReissueAccessoriesCheck(Long id) {
        ScReissueAccessories scReissueAccessories = scReissueAccessoriesMapper.selectScReissueAccessoriesById(id);
        if (null != scReissueAccessories) {
            ScOrderReissue scOrderReissue = scOrderReissueMapper.selectScOrderReissueById(scReissueAccessories.getReissueId());
            if (!"NEW".equals(scOrderReissue.getStatus()) && !"SUBMIT".equals(scOrderReissue.getStatus()) && !"RETURN".equals(scOrderReissue.getStatus())) {
                throw new CustomException("已处理的补发不可删除配件明细");
            }
        }
    }

    private void deleteScReissueGoodsCheck(Long id) {
        ScReissueGoods scReissueGoods = scReissueGoodsMapper.selectScReissueGoodsById(id);
        if (null != scReissueGoods) {
            ScOrderReissue scOrderReissue = scOrderReissueMapper.selectScOrderReissueById(scReissueGoods.getReissueId());
            if (!"NEW".equals(scOrderReissue.getStatus()) && !"SUBMIT".equals(scOrderReissue.getStatus()) && !"RETURN".equals(scOrderReissue.getStatus())) {
                throw new CustomException("已处理的补发不可删除配件明细");
            }
        }
    }

    public void deleteScReissueShipmentCheck(Long id) {
        ScReissueShipment scReissueShipment = scReissueShipmentMapper.selectScReissueShipmentById(id);
        if (null != scReissueShipment) {
            ScOrderReissue scOrderReissue = scOrderReissueMapper.selectScOrderReissueById(scReissueShipment.getReissueId());
            if (!"NEW".equals(scOrderReissue.getStatus()) && !"SUBMIT".equals(scOrderReissue.getStatus()) && !"RETURN".equals(scOrderReissue.getStatus())) {
                throw new CustomException("已处理的发货不可删除配件明细");
            }
        }
    }

    /**
     * @param
     * @description: 定时同步补发配件Tracking
     * @author: Moore
     * @date: 2023/10/28 11:07
     * @return: void
     **/
    @Override
    public void syncReissueTrackingJob(Integer[] ids) {
        //获取已补发工单 (APPROVED)
        List<ScOrderReissue> orderReissueList = scOrderReissueMapper.selectScOrderReissueApproved(1000049L,ids);
        if (CollectionUtils.isNotEmpty(orderReissueList)) {
            for (ScOrderReissue orderReissue : orderReissueList) {
                if (!StringUtils.isEmpty(orderReissue.getAmazonOrderId())) {
                    syncReissueTracking(orderReissue, orderReissue.getOrganizationId());
                }
            }
        }
    }


    //获取地址信息
    @Override
    public List<JSONObject> selectRegion(String code) {
        if(StringUtils.isEmpty(code)){
            return scOrderReissueMapper.selectRegionAll();
        }
        return scOrderReissueMapper.selectRegionList(code).stream().filter(item->item!=null).collect(Collectors.toList());
    }



    /**
     * @description: 根据补发订单号查询tracking
     * @author: Moore
     * @date: 2023/11/29 15:30
     * @param
     * @param orderReissue
     * @param organizationId
     * @return: void
    **/
    private void syncReissueTracking(ScOrderReissue orderReissue, Long organizationId) {

        try {
            LambdaQueryWrapper<SaleOrderItems> saleOrderItemsQuert = new LambdaQueryWrapper<>();
            saleOrderItemsQuert.select(SaleOrderItems::getHeadId);
            saleOrderItemsQuert.eq(SaleOrderItems::getOrderNo, orderReissue.getReissueNumber());
            saleOrderItemsQuert.eq(SaleOrderItems::getOrgId, organizationId != null ? organizationId.intValue() : null);
            saleOrderItemsQuert.eq(SaleOrderItems::getDisabledAt, 0);
            List<SaleOrderItems> saleOrderItemsList = saleOrderItemsMapper.selectList(saleOrderItemsQuert);
            if (CollectionUtils.isEmpty(saleOrderItemsList)) {
                return;
            }
            List<SaleOrderItemVO> saleOrderItems = saleOrderItemsMapper.selectOrderShipmentInfo(saleOrderItemsList.get(0).getHeadId().longValue());
            if (!CollectionUtils.isEmpty(saleOrderItems)) {
                String TrackingStr = saleOrderItems.stream().filter(item -> !StringUtils.isEmpty(item.getTrackNo())).map(SaleOrderItemVO::getTrackNo).distinct().collect(Collectors.joining(","));
                if (!StringUtils.isEmpty(TrackingStr)) {
                    orderReissue.setStatus("FINISHED");
                    orderReissue.setTrackingNo(TrackingStr);
                    ScTicket scTicket = scTicketService.selectScTicketBySource(orderReissue.getId(), "sc_order_reissue");  //记录工单日志
                    if (null != scTicket) {
                        ScTicketLog ticketLog = new ScTicketLog();
                        ticketLog.setTicketId(scTicket.getId());
                        ticketLog.setOperateTime(DateUtils.getNowDate());
                        ticketLog.setOperateContent("获取到的跟踪号为：" + TrackingStr + " 修改补发状态为:FINISHED  工单状态为:FINISH"  );
                        ticketLog.settingDefaultSystemCreate();
                        ticketLog.setOperatorBy(ticketLog.getCreatedName());
                        scTicketLogService.insertScTicketLog(ticketLog);

                        //获取到tricking号后结束工单
                        ScTicket scTicketUpate = new ScTicket();
                        scTicketUpate.setId(scTicket.getId());
                        scTicketUpate.setTicketStatus(ScTicketConstant.TICKET_STATUS_FINISH); //已完成
                        scTicketUpate.setOperateStatus(ScTicketConstant.TICKET_OPERATE_VIEW); //操作状态只读
                        scTicketUpate.settingDefaultSystemUpdate();//更新人
                        scTicketService.updateScTicketStatus(scTicketUpate);
                    }
                    orderReissue.setUpdatedBy(-1);
                    orderReissue.setUpdatedName("system");
                    scOrderReissueMapper.updateScOrderReissueTrackingNo(orderReissue);
                }
            }
        } catch (Exception e) {
            logger.info("获取工单补发异常:{}", e);

            String errorMessage = "";
            if (e.getMessage() != null) {
                errorMessage = e.getMessage().substring(e.getMessage().indexOf(":") + 1);
                errorMessage = StrUtil.sub(errorMessage, 0, errorMessage.indexOf("\n") + 1);
            } else {
                errorMessage = StrUtil.subPre(e.toString(), 200);
            }
            orderReissue.setResponseMsg(errorMessage);
            //记录补发日志
            ScTicket scTicket = scTicketService.selectScTicketBySource(orderReissue.getId(), "sc_order_reissue");
            if (null == scTicket) {
                return;
            }
            //记录工单日志
            ScTicketLog ticketLog = new ScTicketLog();
            ticketLog.setTicketId(scTicket.getId());
            ticketLog.setOperateTime(DateUtils.getNowDate());
            ticketLog.setOperateContent("获取补发单号异常：" + errorMessage + " 并修改补发状态为 FAIL");
            ticketLog.settingDefaultSystemCreate();
            ticketLog.setOperatorBy(ticketLog.getCreatedName()); //操作人
            scTicketLogService.insertScTicketLog(ticketLog);

            //更新补发订单信息
            orderReissue.setUpdatedBy(-1);
            orderReissue.setUpdatedName("system");
            orderReissue.setStatus("FAIL"); //补发异常
            scOrderReissueMapper.updateScOrderReissueTrackingNo(orderReissue);
        }

    }


    /**
     * @Description:补发配件申请拆件补发
     * @Author: wly
     * @Date: 2025-06-11 18:52
     * @Params: [authUser, ticketId, contextId]
     * @Return: void
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void applySplitReissue(AuthUserDetails authUser, Long ticketId, Long contextId,String shippingType) {

        //查询补发配件工单
        ScTicket reissueAccessoriesTicket = scTicketService.selectScTicketById(ticketId);
        AssertUtil.isFalse(reissueAccessoriesTicket == null, "补发配件工单不存在");
        //补发配件工单仅待处理状态可拆分
        AssertUtil.isFalse(!ScTicketConstant.TICKET_STATUS_NEW.equals(reissueAccessoriesTicket.getTicketStatus()), "仅待处理状态可申请拆件补发!");
        //查询补发主表，补发主表状态为SUBMIT可拆分
        ScOrderReissue scOrderReissue = scOrderReissueMapper.selectById(reissueAccessoriesTicket.getSourceId());
        //查询补发配件的附件信息，复制到拆件补发

        //获取附件信息
        LambdaQueryChainWrapper<TicketAttaches> queryObs = ticketAttachesService.lambdaQuery();
        queryObs.eq(TicketAttaches::getDocumentId, scOrderReissue.getId());
        queryObs.eq(TicketAttaches::getDocumentType, "sc_order_reissue");
        queryObs.isNull(TicketAttaches::getType);
        List<TicketAttaches> listObs = queryObs.list();



        String type = StringUtils.isNotEmpty(shippingType) ? shippingType : scOrderReissue.getShippingType();
        /*if (scOrderReissue == null || !"SUBMIT".equals(scOrderReissue.getStatus())) {
            throw new ErpCommonException("补发配件提交后才可申请拆件补发!");
        }*/
        //查询补发配件信息
        List<ScReissueAccessories> scReissueAccessoriesList = scReissueAccessoriesMapper.selectScReissueAccessoriesByReissueId(reissueAccessoriesTicket.getSourceId());

        //查询补发配件已拆件补发的数据
        List<ScOrderReissue> existSplitScOrderReissues = this.list(new LambdaQueryWrapper<ScOrderReissue>()
               .eq(ScOrderReissue::getAmazonOrderId, scOrderReissue.getAmazonOrderId())
                .eq(ScOrderReissue::getReissueType, "SPLIT"));
        int times = 1;
        if (!CollectionUtils.isEmpty(existSplitScOrderReissues)) {
            times = existSplitScOrderReissues.size() + 1;
        }


        //保存补发主表信息和拆件补发信息
        ScOrderReissue splitScOrderReissue = BeanCopyUtils.copyBean(scOrderReissue, ScOrderReissue.class);
        splitScOrderReissue.setShippingType(type);
        splitScOrderReissue.setOrganizationId(contextId);
        splitScOrderReissue.setOrderId(scOrderReissue.getOrderId());
        splitScOrderReissue.setAmazonOrderId(scOrderReissue.getAmazonOrderId());
        splitScOrderReissue.setShopId(scOrderReissue.getShopId());
        splitScOrderReissue.setCustomerId(scOrderReissue.getCustomerId());

        splitScOrderReissue.setCustomerName(scOrderReissue.getCustomerName());
        splitScOrderReissue.setPhone(scOrderReissue.getPhone());
        splitScOrderReissue.setAddress(scOrderReissue.getAddress());
        splitScOrderReissue.setAddress2(scOrderReissue.getAddress2());
        splitScOrderReissue.setAddress3(scOrderReissue.getAddress3());
        splitScOrderReissue.setCity(scOrderReissue.getCity());
        splitScOrderReissue.setStateOrRegion(scOrderReissue.getStateOrRegion());
        splitScOrderReissue.setCountryCode(scOrderReissue.getCountryCode());
        splitScOrderReissue.setPostalCode(scOrderReissue.getPostalCode());
        splitScOrderReissue.setCategoryName(scOrderReissue.getCategoryName());
        splitScOrderReissue.setProductName(scOrderReissue.getProductName());
        splitScOrderReissue.setErpSku(scOrderReissue.getErpSku());
        splitScOrderReissue.setReissueQuantity(scOrderReissue.getReissueQuantity());
        splitScOrderReissue.setParentSku(scOrderReissue.getParentSku());

        splitScOrderReissue.settingDefaultCreate();
        splitScOrderReissue.settingDefaultUpdate();


        splitScOrderReissue.setTicketId(ticketId);
        splitScOrderReissue.setReissueNumber(scOrderReissue.getAmazonOrderId() + "-SPLIT-" + times);
        splitScOrderReissue.setReissueType("SPLIT");
        splitScOrderReissue.setStatus("SUBMIT");//拆件补发的补发状态待补发
        splitScOrderReissue.setApplyBy(authUser.getName());
        splitScOrderReissue.setApplyTime(DateUtils.getNowDate());

        ScOrderReissue scOrderReissueWithAttach = this.selectScOrderReissueById(reissueAccessoriesTicket.getSourceId());
        if (StringUtils.isNotEmpty(scOrderReissueWithAttach.getMeta())) {
            splitScOrderReissue.setShippingType(scOrderReissueWithAttach.getShippingType());
        }
        //面单附件：取父工单的值，不支持编辑，支持点击下载。
        //发货方式：若面单附件有值，则此字段取父工单的值并不支持编辑，若面单附件无值，则提供选项，AMSP_HL，FEDHD_HL，FEDXG_HL，默认是 FEDXG_HL，工单状态为 待处理 时支持编辑。

        scOrderReissueMapper.insert(splitScOrderReissue);

        if (CollectionUtil.isNotEmpty(listObs)) {
            listObs.forEach(t -> {
                t.setId(null);
                t.setDocumentId(splitScOrderReissue.getId());
                t.settingDefaultCreate();
            });
            ticketAttachesService.saveBatch(listObs);
        }

        List<ScReissueSplit> scReissueSplitList = BeanCopyUtils.copyBeanList(scReissueAccessoriesList, ScReissueSplit.class);
        scReissueSplitList.forEach(t->{
            t.setId(null);
            t.setReissueId(splitScOrderReissue.getId());
            t.setTrackingNo(null);
            t.setSendSite(null);
            t.setRespMsg(null);
            t.setRemark(null);
            t.settingDefaultCreate();
            t.settingDefaultUpdate();
        });
        scReissueSplitService.saveBatch(scReissueSplitList);
        //生成拆件补发工单
        //生成新工单
        ScTicket scTicket = new ScTicket();
        scTicket.setTicketStatus(ScTicketConstant.TICKET_STATUS_NEW);
        scTicket.setTicketName(scOrderReissue.getAmazonOrderId() + " 拆件补发");
        scTicket.setTicketType(ScTicketConstant.TICKET_TYPE_REISSUE_SPLIT);

        String ticketNumber = reissueAccessoriesTicket.getTicketNumber() + "-SPLIT-" + times;
        scTicket.setTicketNumber(ticketNumber);
        scTicket.setTicketSource(reissueAccessoriesTicket.getTicketSource());
        scTicket.setOrganizationId(contextId);
        scTicket.setPriority(ScTicketConstant.TICKET_PRIORITY_MEDIUM);
        scTicket.setAmazonOrderId(scOrderReissue.getAmazonOrderId());
        scTicket.setOrderId(scOrderReissue.getOrderId()); //提交补发设置订单ID
        scTicket.setShopId(scOrderReissue.getShopId());
        scTicket.setCustomerId(scOrderReissue.getCustomerId());
        scTicket.setParentTicketId(ticketId);
        scTicket.setSourceId(splitScOrderReissue.getId());
        scTicket.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_REISSUE);
        scTicket.setOperateStatus(ScTicketConstant.TICKET_OPERATE_APPROVE);
        scTicket.settingDefaultCreate();
        scTicket.setParentSku(reissueAccessoriesTicket.getParentSku());
        scTicket.setGoodsSku(reissueAccessoriesTicket.getGoodsSku());
        scTicketService.insertScTicket(scTicket);

        //记录父工单日志
        ScTicketLog ticketLog = new ScTicketLog();
        ticketLog.settingDefaultCreate();
        ticketLog.setOperatorBy(scTicket.getCreatedName());
        ticketLog.setOrganizationId(contextId.intValue());
        ticketLog.setTicketId(scTicket.getParentTicketId());
        ticketLog.setOperateType("SUBMIT");
        ticketLog.setOperateTime(DateUtils.getNowDate());
        ticketLog.setOperateContent("提交拆件补发申请，生成子工单：" + scTicket.getTicketNumber());
        scTicketLogService.insertScTicketLog(ticketLog);

        //更新补发配件及工单信息
        ScTicket updateParentTicket = new ScTicket();
        updateParentTicket.setId(reissueAccessoriesTicket.getId());
        updateParentTicket.setTicketStatus(ScTicketConstant.TICKET_STATUS_PROCESSING);
        scTicketService.updateScTicketStatus(updateParentTicket);
    }


    /**
     * @Description:拆件补发更新
     * @Author: wly
     * @Date: 2025-06-12 18:18
     * @Params: [contextId, ticketId, scOrderReissue]
     * @Return: void
     **/
    @Override
    public void updateSplitReissue(Integer contextId, Long ticketId, ScOrderReissue scOrderReissue) {
        ScTicket splitReissueTicket = scTicketService.selectScTicketById(ticketId);
        if (!ScTicketConstant.TICKET_STATUS_NEW.equals(splitReissueTicket.getTicketStatus())) {
            throw new ErpCommonException("工单状态仅待处理时允许编辑!");
        }
        if (StringUtils.isEmpty(scOrderReissue.getHandleWarehouse()) && StringUtils.isEmpty(scOrderReissue.getShippingType())) {
            throw new ErpCommonException("请选择发货仓库或发货方式!");
        }


        //更新发货方式
        //面单附件：取父工单的值，不支持编辑，支持点击下载。
        //发货方式：若面单附件有值，则此字段取父工单的值并不支持编辑，若面单附件无值，则提供选项，AMSP_HL，FEDHD_HL，FEDXG_HL，默认是 FEDXG_HL，工单状态为 待处理 时支持编辑。
        if (StringUtils.isNotEmpty(scOrderReissue.getShippingType())) {

            ScOrderReissue scOrderReissueById = this.selectScOrderReissueById(splitReissueTicket.getSourceId());
            if (StringUtils.isNotEmpty(scOrderReissueById.getMeta())) {
                throw new ErpCommonException("面单附件已上传，发货方式不可编辑!");
            }
            this.lambdaUpdate().set(ScOrderReissue::getShippingType, scOrderReissue.getShippingType())
                    .eq(ScOrderReissue::getId, splitReissueTicket.getSourceId())
                    .update();
        }
        if (StringUtils.isNotEmpty(scOrderReissue.getHandleWarehouse())) {
            this.lambdaUpdate().set(ScOrderReissue::getHandleWarehouse, scOrderReissue.getHandleWarehouse())
                    .eq(ScOrderReissue::getId, splitReissueTicket.getSourceId())
                    .update();
        }
    }


    /**
     * 购买面单
     *
     * @param contextId
     * @param ticketId
     * @param authUser
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void buyLabel(Integer contextId, Long ticketId, AuthUserDetails authUser,String shippingType,String handleWarehouse) {
        ScTicket scTicket = scTicketService.selectScTicketById(ticketId);

        ScOrderReissue splitReissue = this.getById(scTicket.getSourceId());
        if (StringUtils.isNotEmpty(splitReissue.getMeta())) {
            throw new ErpCommonException("已有面单附件，不需要重复购买");
        }

        if (StringUtils.isEmpty(splitReissue.getHandleWarehouse())) {
            throw new ErpCommonException("处理仓库不能为空!");
        }
        log.info("用户尝试为拆件补发工单[{}]购买面单",scTicket.getTicketNumber());

        this.lambdaUpdate().set(ScOrderReissue::getShippingType, shippingType)
                .set(ScOrderReissue::getHandleWarehouse, handleWarehouse)
                .eq(ScOrderReissue::getId, splitReissue.getId())
                .update();

        ScOrderReissueSplitBuyLabelDto dto = buyLabelData(contextId, ticketId);
        ValidateUtil.beanValidate(dto);
        ScOrderReissueSplitBuyLabelResponse response = commonBuyLabel(dto);
        log.info("用户为拆件补发工单[{}]购买面单完成返回数据--{}",scTicket.getTicketNumber(),JSONObject.toJSONString(response));
        if (response == null) {
            throw new ErpCommonException("购买面单接口异常");
        }
        if(response.getStatusCode() == null || !response.getStatusCode().equals(200)) {
            throw new ErpCommonException("购买面单失败，请求异常" + response.getMessage());
        }
        if (response.getCode() == null || !response.getCode().equals(200)) {
            throw new ErpCommonException("购买面单失败，处理结果错误" + response.getMessage());
        }
        if (response.getStatus() == null || !response.getStatus().equals(0)) {
            throw new ErpCommonException("购买面单失败，全局未知错误" + response.getMessage());
        }
        if (response.getData() == null) {
            throw new ErpCommonException("购买面单失败，返回主数据为空");
        }
        if (CollectionUtil.isEmpty(response.getData().getShipments())) {
            throw new ErpCommonException("购买面单失败，返回数据中无包裹信息");
        }
        ScOrderReissueSplitBuyLabelResponse.Shipment shipment = response.getData().getShipments().get(0);
        if (StringUtils.isEmpty(shipment.getTrackingCode())) {
            throw new ErpCommonException("购买面单失败，返回数据中无跟踪号");
        }
        if (shipment.getLabel() == null || StringUtils.isEmpty(shipment.getLabel().getLabelUrl())) {
            throw new ErpCommonException("购买面单失败，返回数据中无面单地址");
        }
        String trackingCode = shipment.getTrackingCode();
        String labelUrl = shipment.getLabel().getLabelUrl();
        BigDecimal conversionTotalRate = Optional.ofNullable(shipment.getSelectedRate()).orElse(new ScOrderReissueSplitBuyLabelResponse.SelectedRate()).getConversionTotalRate();
        //存储面单地址到父工单
        //存储跟踪号到拆件补发工单和父工单
        Long splitReissueId = scTicket.getSourceId();

        this.lambdaUpdate().in(ScOrderReissue::getId, splitReissueId)
                .set(ScOrderReissue::getTrackingNo, trackingCode)
                .set(conversionTotalRate != null, ScOrderReissue::getDiscountPrice, conversionTotalRate)
                .update();
        TicketAttaches ticketAttaches = new TicketAttaches();
        ticketAttaches.setOrganizationId(contextId.longValue());
        ticketAttaches.setAttachId(null);//TODO 暂无
        ticketAttaches.setFileName(labelUrl.lastIndexOf("/") > 0 ? labelUrl.substring(labelUrl.lastIndexOf("/") + 1) : labelUrl);
        ticketAttaches.setFileUrl(labelUrl);
        ticketAttaches.setDocumentType("sc_order_reissue");
        ticketAttaches.setDocumentId(splitReissueId);
        ticketAttaches.setType(null);
        ticketAttaches.settingDefaultSystemCreate();
        ticketAttaches.settingDefaultSystemUpdate();
        ticketAttachesService.save(ticketAttaches);

        ScTicketLog ticketLog = new ScTicketLog();
        ticketLog.setTicketId(ticketId);
        ticketLog.settingDefaultCreate();
        ticketLog.setOperatorBy(authUser.getName());
        ticketLog.setOrganizationId(contextId);
        ticketLog.setOperateTime(DateUtils.getNowDate());
        ticketLog.setOperateType("UPDATE");
        ticketLog.setOperateContent("购买面单成功，获取到的跟踪号为：" + trackingCode);
        scTicketLogService.insertScTicketLog(ticketLog);
    }

    /**
     * 推送仓库
     *
     * @param contextId
     * @param ticketId        拆件补发工单id
     * @param handleWarehouse
     */
    @Override
    public void pushWarehouse(Integer contextId, Long ticketId, String handleWarehouse) {

        ScTicket splitReissueTicket = scTicketService.selectScTicketByTicketId(ticketId);
        ScOrderReissue splitReissue = this.selectScOrderReissueById(splitReissueTicket.getSourceId());
        if (StringUtils.isEmpty(splitReissue.getMeta())) {
            throw new ErpCommonException("没有物流面单，无法推送");
        }
        ScTicket parentTicket = scTicketService.selectScTicketByTicketId(splitReissueTicket.getParentTicketId());
        splitReissueTicket.setGoodsSku(parentTicket.getGoodsSku());
        this.lambdaUpdate().set(ScOrderReissue::getHandleWarehouse, handleWarehouse)
                .eq(ScOrderReissue::getId, splitReissue.getId())
                .update();
        splitReissue.setHandleWarehouse(handleWarehouse);
        List<ScReissueSplit> scReissueSplitList = scReissueSplitService.list(new LambdaQueryWrapper<>(ScReissueSplit.class).eq(ScReissueSplit::getReissueId, splitReissue.getId()));
        String scReissueSplitSkuStr = scReissueSplitList.stream().map(t -> t.getErpSku() + "*" + t.getQuantity().toString()).collect(Collectors.joining("，"));
        //调用WMS接口推送相关信息生成仓库事项单
        WmsWorkOrderCreateRequest request = new WmsWorkOrderCreateRequest();
        request.setOrderCode(splitReissueTicket.getTicketNumber());
        request.setOrderType("PJBF");
        //todo 暂时写死
        request.setWarehouseCode("EFCA");
        request.setTitle(splitReissue.getTrackingNo() + "，配件补发SKU：" +scReissueSplitSkuStr);
        request.setSku(splitReissueTicket.getGoodsSku());
        //配件描述
        StringBuilder sb = new StringBuilder();
        for (ScReissueSplit split : scReissueSplitList) {
            sb.append("配件名称：").append(split.getAccessoriesName()).append("\n")
                    .append("配件补发SKU：").append(split.getErpSku()).append("\n")
                    .append("补发数量：").append(split.getQuantity().toString()).append("\n")
                    .append("成品SKU：").append(splitReissueTicket.getGoodsSku()).append("\n");
        }
        request.setDetails(sb.toString().substring(0, sb.toString().length() - 1));
        LambdaQueryChainWrapper<TicketAttaches> queryObs = ticketAttachesService.lambdaQuery();
        queryObs.eq(TicketAttaches::getDocumentId, splitReissue.getId());
        queryObs.eq(TicketAttaches::getDocumentType, "sc_order_reissue");
        List<TicketAttaches> listObs = queryObs.list();
        List<WmsWorkOrderCreateRequest.AttachmentList> attachmentList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(listObs)) {
            listObs.forEach(attach->{
                WmsWorkOrderCreateRequest.AttachmentList attachment = new WmsWorkOrderCreateRequest.AttachmentList();
                attachment.setName(attach.getFileName());
                attachment.setUrl(attach.getFileUrl());
                attachment.setType("img".equals(attach.getType()) ? "picture" : "shippinglabel");
                attachmentList.add(attachment);
            });
        }
        request.setAttachmentList(attachmentList);

        String orgCode = scOrderReissueMapper.selectOrganizationCodeByTitle(splitReissue.getHandleWarehouse());
        WmsCommonResponse wmsCommonResponse = pushWarehouseData(JSONObject.parseObject(JSONObject.toJSONString(request)),orgCode, WmsCommonResponse.class);

        if (wmsCommonResponse == null) {
            throw new ErpCommonException("推送仓库接口异常");
        }
        if (!wmsCommonResponse.getCode().equals(0)) {
            throw new ErpCommonException("推送仓库失败，返回信息" + wmsCommonResponse.getMsg());
        }
        splitReissueTicket.setTicketStatus(ScTicketConstant.TICKET_STATUS_PROCESSING);
        scTicketService.updateScTicketStatus(splitReissueTicket);
        ScTicketLog ticketLog = new ScTicketLog();
        ticketLog.setTicketId(ticketId);
        ticketLog.settingDefaultCreate();
        ticketLog.setOperatorBy(ticketLog.getCreatedName());
        ticketLog.setOrganizationId(contextId);
        ticketLog.setOperateTime(DateUtils.getNowDate());
        ticketLog.setOperateType("UPDATE");
        ticketLog.setOperateContent("推送仓库系统成功，仓库单号为:" + splitReissueTicket.getTicketNumber());
        scTicketLogService.insertScTicketLog(ticketLog);
    }

    /**
     * 拆件补发获取购买面单数据
     * @param contextId
     * @param ticketId 拆件补发工单id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ScOrderReissueSplitBuyLabelDto buyLabelData(Integer contextId, Long ticketId) {
        //拆件补发工单的附件取父工单
        ScTicket scTicket = scTicketService.selectScTicketById(ticketId);


        ScOrderReissue splitReissue = this.getById(scTicket.getSourceId());

        List<ScReissueSplit> splitList = scReissueSplitService.list(new LambdaQueryWrapper<>(ScReissueSplit.class).eq(ScReissueSplit::getReissueId, scTicket.getSourceId()));
        ScOrderReissueSplitBuyLabelDto dto = new ScOrderReissueSplitBuyLabelDto();
        dto.setReference(splitReissue.getReissueNumber());
        dto.setReturnShipment(false);
        dto.setCarrier(splitReissue.getShippingType());
        dto.setServiceCode(splitReissue.getShippingType());
        ScOrderReissueSplitBuyLabelDto.FromAddress fromAddress = new ScOrderReissueSplitBuyLabelDto.FromAddress();
        //通过处理仓库拿到仓库
        Integer warehouseId = scOrderReissueMapper.selectWarehouseIdByTitle(splitReissue.getHandleWarehouse());
        if (warehouseId == null) {
            throw new ErpCommonException("处理仓库不存在");
        }
        WarehouseInfoView warehouseInfoView = warehouseService.findOne(contextId, warehouseId);
        if (warehouseInfoView == null || warehouseInfoView.getWarehouseAddress() == null) {
            throw new ErpCommonException("处理仓库查询发货地址信息为空");
        }
        WarehouseAddress warehouseAddress = warehouseInfoView.getWarehouseAddress();
        //暂时写死
        fromAddress.setCode("HJ-EFCA");
        fromAddress.setCity(warehouseAddress.getCity());
//
        fromAddress.setContactName(warehouseAddress.getLinkman());
        fromAddress.setCountry(warehouseAddress.getCountry());
        fromAddress.setPhone(warehouseAddress.getPhone());
        fromAddress.setState(warehouseAddress.getState());
        fromAddress.setStreet1(warehouseAddress.getAddress());
        fromAddress.setStreet2(warehouseAddress.getAddress2());
        fromAddress.setZip(warehouseAddress.getPostal());
        dto.setFromAddress(fromAddress);


        List<ScOrderReissueSplitBuyLabelDto.Shipment> shipments = new ArrayList<>();
        ScOrderReissueSplitBuyLabelDto.Shipment shipment = new ScOrderReissueSplitBuyLabelDto.Shipment();
        //获取erpsku包装信息
        List<Products> productsList = productsService.list(new LambdaQueryWrapper<Products>()
                .select(Products::getErpsku,Products::getItemHeight, Products::getItemLength, Products::getItemWeight, Products::getItemWidth)
                .eq(Products::getOrgId, contextId).in(Products::getErpsku, splitList.stream().map(ScReissueSplit::getErpSku).collect(Collectors.toList())));
        if (CollectionUtil.isEmpty(productsList)) {
            throw new ErpCommonException("Sku包装信息为空");
        }
        if (productsList.stream().anyMatch(t -> t.getItemLength() == null || t.getItemHeight() == null || t.getItemWidth() == null || StringUtils.isEmpty(t.getItemWeight()))) {
            throw new ErpCommonException("Sku包装信息不全,长宽高重量有为空");
        }
        //当SKU有多个或1个SKU多个数量时，
        //每个SKU的三边按照从大到小排序，依次为第一边、第二边、第三边
        //所有SKU里第一边中最长的作为包裹的长，第二边中最长的作为包裹的宽、第三边全部加起来作为包裹的高，所有SKU的重量加起来作为包裹的重量，然后去购买物流面单。
        if (splitList.size() > 1 || splitList.stream().anyMatch(t -> t.getQuantity().compareTo(new Integer(1)) > 0)) {
            //创建totalSkuQuantity数量个数组，每个数组里存放一个SKU的三边信息
            List<BigDecimal[]> skuThreeSides = new ArrayList<>();
            for (ScReissueSplit split : splitList) {
                Products matchProduct = productsList.stream().filter(t -> t.getErpsku().equals(split.getErpSku())).findFirst().get();
                List<BigDecimal> threeSidesSort = Arrays.asList(matchProduct.getItemLength(), matchProduct.getItemWidth(), matchProduct.getItemHeight());
                threeSidesSort.sort(Comparator.reverseOrder());
                if (split.getQuantity().compareTo(new Integer(1)) > 0) {
                    for (int quantity = 0; quantity < split.getQuantity(); quantity++) {
                        BigDecimal[] threeSides = new BigDecimal[]{threeSidesSort.get(0), threeSidesSort.get(1), threeSidesSort.get(2)};
                        skuThreeSides.add(threeSides);
                    }
                }else {
                    BigDecimal[] threeSides = new BigDecimal[]{threeSidesSort.get(0), threeSidesSort.get(1), threeSidesSort.get(2)};
                    skuThreeSides.add(threeSides);
                }
            }
            BigDecimal maxItemLength = skuThreeSides.stream().map(t -> t[0]).max(Comparator.naturalOrder()).get().setScale(4, BigDecimal.ROUND_HALF_UP);
            BigDecimal maxItemWidth = skuThreeSides.stream().map(t -> t[1]).max(Comparator.naturalOrder()).get().setScale(4, BigDecimal.ROUND_HALF_UP);
            BigDecimal sumItemHeight = skuThreeSides.stream().map(t -> t[2]).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(4, BigDecimal.ROUND_HALF_UP);
            BigDecimal totalItemWeight = splitList.stream().map(t -> {
                Products matchProducts = productsList.stream().filter(pro -> t.getErpSku().equals(pro.getErpsku())).findFirst().get();
                return (StringUtils.isEmpty(matchProducts.getItemWeight()) ? new BigDecimal("0.00") : new BigDecimal(matchProducts.getItemWeight())).multiply(new BigDecimal(t.getQuantity().toString())).setScale(2, BigDecimal.ROUND_HALF_UP);
            }).reduce(BigDecimal.ZERO, BigDecimal::add);
            shipment.setParcel(new Parcel(sumItemHeight, maxItemLength, "cm", totalItemWeight, "g", maxItemWidth));
        }else {
            Products products = productsList.get(0);
            shipment.setParcel(new Parcel(products.getItemHeight() == null ? new BigDecimal("0.0000") :products.getItemHeight().setScale(4, BigDecimal.ROUND_HALF_UP),
                    products.getItemLength() == null ? new BigDecimal("0.0000") :products.getItemLength().setScale(4, BigDecimal.ROUND_HALF_UP),
                    "cm",
                    StringUtils.isEmpty(products.getItemWeight()) ? new BigDecimal("0.00") : new BigDecimal(products.getItemWeight()).setScale(2, BigDecimal.ROUND_HALF_UP),
                    "g",
                    products.getItemWidth() == null ? new BigDecimal("0.0000") :products.getItemWidth().setScale(4, BigDecimal.ROUND_HALF_UP)
            ));
        }

        shipments.add(shipment);

        dto.setShipments(shipments);

        ScOrderReissueSplitBuyLabelDto.ToAddress toAddress = new ScOrderReissueSplitBuyLabelDto.ToAddress();
        toAddress.setCity(splitReissue.getCity());
        toAddress.setCode(null);
//        toAddress.setCompany();
        toAddress.setContactName(splitReissue.getCustomerName());
        toAddress.setCountry(splitReissue.getCountryCode());
        toAddress.setPhone(splitReissue.getPhone());
//        toAddress.setResidential();
        toAddress.setState(splitReissue.getStateOrRegion());
        toAddress.setStreet1(splitReissue.getAddress());
        toAddress.setStreet2(splitReissue.getAddress2());
        toAddress.setZip(splitReissue.getPostalCode());
        dto.setToAddress(toAddress);
        return dto;

    }

    /**
     * 拆件补发推送仓库 WMS回调
     * @param callBack
     */
    @Override
    public void wmsCallBack(ReissueSplitWmsCallBack callBack) {
        log.info("拆件补发工单WMS回调接口--{}", JSONObject.toJSONString(callBack));
        ScTicket baseScTicket = scTicketMapper.selectScTicketByTicketNumber(callBack.getOrderCode());
        ScTicket splitReissueTicket = scTicketService.selectScTicketByBaseTicketId(baseScTicket.getId());

        ScTicket reissueAccessoriesTicket = scTicketService.selectScTicketById(splitReissueTicket.getParentTicketId());
        ScOrderReissue splitReissue = this.getById(splitReissueTicket.getSourceId());
        ScOrderReissue reissueAccessories = this.getById(reissueAccessoriesTicket.getSourceId());
        //返回成功：则更新子工单和父工单的工单状态更新为已完成 补发信息和拆件补发信息列表的补发状态 更新为 已完成。
        if ("success".equals(callBack.getResult())) {

            splitReissueTicket.setTicketStatus(ScTicketConstant.TICKET_STATUS_FINISH);
            splitReissueTicket.setHandleTime(DateUtils.getNowDate());
            splitReissueTicket.settingDefaultSystemUpdate();
            scTicketMapper.updateScTicketStatus(splitReissueTicket);
            ScTicketLog scTicketLog = new ScTicketLog();
            scTicketLog.setTicketId(splitReissueTicket.getId());
            scTicketLog.setOrganizationId(splitReissueTicket.getOrganizationId().intValue());
            scTicketLog.settingDefaultSystemCreate();
            scTicketLog.setOperatorBy(scTicketLog.getCreatedName());
            scTicketLog.setOperateTime(DateUtils.getNowDate());
            scTicketLog.setOperateType("UPDATE");
            scTicketLog.setOperateContent("仓库补发成功，修改工单状态为：FINISH");
            scTicketLogService.insertScTicketLog(scTicketLog);


            reissueAccessoriesTicket.setTicketStatus(ScTicketConstant.TICKET_STATUS_FINISH);
            scTicketService.updateScTicketStatus(reissueAccessoriesTicket);
            this.lambdaUpdate().set(ScOrderReissue::getStatus, "FINISHED")
                    .in(ScOrderReissue::getId, splitReissue.getId(), reissueAccessories.getId())
                    .update();


        }
        //返回驳回：则更新子工单的工单状态 为 已关闭，父工单的工单状态为待处理，处理人为创建子工单的人。并且补发信息和拆件补发信息列表的补发状态更新为 退回。
        else if ("reject".equals(callBack.getResult())) {
            splitReissueTicket.setTicketStatus(ScTicketConstant.TICKET_STATUS_CLOSED);
            reissueAccessoriesTicket.setTicketStatus(ScTicketConstant.TICKET_STATUS_NEW);
            scTicketService.updateScTicketStatus(splitReissueTicket);
            reissueAccessoriesTicket.setHandleTime(DateUtils.getNowDate());
            reissueAccessoriesTicket.settingDefaultSystemUpdate();
            scTicketMapper.updateScTicketStatus(reissueAccessoriesTicket);
            reissueAccessoriesTicket.setHandlerBy(splitReissueTicket.getCreatedName());
            reissueAccessoriesTicket.setUserId(splitReissueTicket.getCreatedBy().longValue());
            scTicketService.updateScTicketHandler(reissueAccessoriesTicket);


            this.lambdaUpdate().set(ScOrderReissue::getStatus, "RETURN")
                    .in(ScOrderReissue::getId, splitReissue.getId(), reissueAccessories.getId())
                    .update();
            //注：把回传结果保存在 父工单和子工单的操作记录中，若是驳回，需要包含驳回原因。
            ScTicketLog scTicketLog = new ScTicketLog();
            scTicketLog.setTicketId(splitReissueTicket.getId());
            scTicketLog.setOrganizationId(splitReissueTicket.getOrganizationId().intValue());
            scTicketLog.settingDefaultSystemCreate();
            scTicketLog.setOperatorBy(scTicketLog.getCreatedName());
            scTicketLog.setOperateTime(DateUtils.getNowDate());
            scTicketLog.setOperateType("UPDATE");
            scTicketLog.setOperateContent("仓库驳回拆件补发工单，原因：" + callBack.getReason() + "修改工单状态:已关闭");
            scTicketLogService.insertScTicketLog(scTicketLog);
            scTicketLog.setId(null);
            scTicketLog.setTicketId(reissueAccessoriesTicket.getId());
            scTicketLogService.insertScTicketLog(scTicketLog);

        }else {
            log.error("拆件补发工单WMS回调接口结果异常--{}", JSONObject.toJSONString(callBack));
        }

    }

    /**
     * 购买面单接口
     * @param dto
     * @return
     */
    @Override
    public ScOrderReissueSplitBuyLabelResponse commonBuyLabel(ScOrderReissueSplitBuyLabelDto dto) {

        try {
            String url = REISSUE_BUY_LABEL_URL + "/api/v2/orderShipment/create";
            log.info("购买面单接口--参数--{}--url--{}", JSONObject.toJSONString(dto), url);
            Map<String, String> headers = new HashMap<>();
            /*HttpServletRequest servletRequest = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String authorization = servletRequest.getHeader("Authorization");*/
            headers.put("Authorization", "Bearer be1664e37d6650cefe1581145dd7806r");
            headers.put("Content-Type", "application/json");
            HttpRequest request = HttpUtil.createPost(url);
            request.setConnectionTimeout(1000 * 20);
            request.headerMap(headers, true);
            log.info("购买面单接口--请求头--{}--请求体--{}", JSONObject.toJSONString(headers), JSONObject.toJSONString(dto));
            request.body(JSONObject.toJSONString(dto));
            HttpResponse httpResponse = request.execute();

            String responseBody = httpResponse.body();
            if (!httpResponse.isOk()) {
                log.error("购买面单接口失败--请求体{}--响应结果--{}", JSONObject.toJSONString(dto), responseBody);
                if (StringUtils.isEmpty(responseBody)) {
                    return null;
                }
            }
            if (StringUtils.isEmpty(responseBody)) {
                log.error("购买面单接口失败--返回值为空请求体--{}", JSONObject.toJSONString(dto));
                return null;
            }
            return JSONObject.parseObject(responseBody, ScOrderReissueSplitBuyLabelResponse.class);
        }catch (Exception e){
            e.printStackTrace();
            log.error("购买面单接口异常{}--参数--{}", e.getMessage(), JSONObject.toJSONString(dto));
            return null;
        }
    }

    public <T> T pushWarehouseData(JSONObject jsonObject, String providerOrgId, Class<T> tClass) {

        WmsGetTokenResponse token = wmsUtil.getToken();
        T t = wmsUtil.wmsCommmonPost(token.getData().getClientToken(), "workOrder.create", providerOrgId, null, null, null, JSONObject.toJSONString(jsonObject), tClass);
        return t;
    }
}
