package com.bizark.op.service.util;

import com.xxl.conf.core.annotation.XxlConf;
import org.springframework.stereotype.Component;

/**
 * @author: bai.yong<PERSON><PERSON>
 * @time: create at 2019-07-08 03:49
 * @description:
 */
@Component
public class XxlConfig {
    //多渠道配置中心前缀
    private static final String MULTICHANNEL_PREFIX = "bizark-multichannel.";


    @XxlConf(value = MULTICHANNEL_PREFIX + "temu.us.app.key")
    public static String TEMU_APP_US_KEY_ID;

    @XxlConf(value = MULTICHANNEL_PREFIX + "temu.us.app.secret")
    public static String TEMU_APP_US_SECRET;
    @XxlConf(value = MULTICHANNEL_PREFIX + "temu.cn.app.key")
    public static String TEMU_APP_CN_KEY_ID;

    @XxlConf(value = MULTICHANNEL_PREFIX + "temu.cn.app.secret")
    public static String TEMU_APP_CN_SECRET;


    @XxlConf(value = MULTICHANNEL_PREFIX + "temu.jp.app.key")
    public static String TEMU_JP_APP_KEY;

    /**
     * TEMU JP自研 APP SECRET
     */
    @XxlConf(value = MULTICHANNEL_PREFIX + "temu.jp.app.secret")
    public static String TEMU_JP_APP_SECRET;



    /**
     * TEMU EU自研 APP KEY
     */
    @XxlConf(value = MULTICHANNEL_PREFIX + "temu.eu.app.key")
    public static String TEMU_EU_APP_KEY;

    /**
     * TEMU EU自研 APP SECRET
     */
    @XxlConf(value = MULTICHANNEL_PREFIX + "temu.eu.app.secret")
    public static String TEMU_EU_APP_SECRET;


    //Amazon
    @XxlConf(value = MULTICHANNEL_PREFIX + "amz.accesskey.id")
    public static String AMAZON_APP_ACCESSKEY_ID;
    // VC
    @XxlConf(value = MULTICHANNEL_PREFIX + "amz.vendor.accesskey.id")
    public static String AMAZON_VC_APP_ACCESSKEY_ID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amz.clientid")
    public static String AMAZON_APP_CLIENTID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amz.vendor.clientid")
    public static String AMAZON_VC_APP_CLIENTID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amz.clientsecret")
    public static String AMAZON_APP_CLIENTSECRET;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amz.vendor.clientsecret")
    public static String AMAZON_VC_APP_CLIENTSECRET;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amz.secretkey")
    public static String AMAZON_APP_SECRETKEY;

    @XxlConf(value = MULTICHANNEL_PREFIX + "amz.vendor.secretkey")
    public static String AMAZON_VC_APP_SECRETKEY;

    @XxlConf(value = MULTICHANNEL_PREFIX + "amz.role.arn")
    public static String AMAZON_APP_ROLE_ARN;

    @XxlConf(value = MULTICHANNEL_PREFIX + "amz.vendor.role.arn")
    public static String AMAZON_VC_APP_ROLE_ARN;


    //亚马逊access_key
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.us.aws.access.key.id")
    public static String AMAZON_US_AWS_ACCESS_KEY_ID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.ca.aws.access.key.id")
    public static String AMAZON_CA_AWS_ACCESS_KEY_ID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.mx.aws.access.key.id")
    public static String AMAZON_MX_AWS_ACCESS_KEY_ID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.uk.aws.access.key.id")
    public static String AMAZON_UK_AWS_ACCESS_KEY_ID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.gb.aws.access.key.id")
    public static String AMAZON_GB_AWS_ACCESS_KEY_ID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.de.aws.access.key.id")
    public static String AMAZON_DE_AWS_ACCESS_KEY_ID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.it.aws.access.key.id")
    public static String AMAZON_IT_AWS_ACCESS_KEY_ID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.es.aws.access.key.id")
    public static String AMAZON_ES_AWS_ACCESS_KEY_ID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.in.aws.access.key.id")
    public static String AMAZON_IN_AWS_ACCESS_KEY_ID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.cn.aws.access.key.id")
    public static String AMAZON_CN_AWS_ACCESS_KEY_ID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.fr.aws.access.key.id")
    public static String AMAZON_FR_AWS_ACCESS_KEY_ID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.jp.aws.access.key.id")
    public static String AMAZON_JP_AWS_ACCESS_KEY_ID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.se.aws.access.key.id")
    public static String AMAZON_SE_AWS_ACCESS_KEY_ID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.nl.aws.access.key.id")
    public static String AMAZON_NL_AWS_ACCESS_KEY_ID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.au.aws.access.key.id")
    public static String AMAZON_AU_AWS_ACCESS_KEY_ID;
    //亚马逊secret_key
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.us.secret.key")
    public static String AMAZON_US_SECRET_KEY;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.ca.secret.key")
    public static String AMAZON_CA_SECRET_KEY;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.mx.secret.key")
    public static String AMAZON_MX_SECRET_KEY;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.uk.secret.key")
    public static String AMAZON_UK_SECRET_KEY;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.gb.secret.key")
    public static String AMAZON_GB_SECRET_KEY;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.de.secret.key")
    public static String AMAZON_DE_SECRET_KEY;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.it.secret.key")
    public static String AMAZON_IT_SECRET_KEY;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.es.secret.key")
    public static String AMAZON_ES_SECRET_KEY;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.in.secret.key")
    public static String AMAZON_IN_SECRET_KEY;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.cn.secret.key")
    public static String AMAZON_CN_SECRET_KEY;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.fr.secret.key")
    public static String AMAZON_FR_SECRET_KEY;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.jp.secret.key")
    public static String AMAZON_JP_SECRET_KEY;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.se.secret.key")
    public static String AMAZON_SE_SECRET_KEY;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.nl.secret.key")
    public static String AMAZON_NL_SECRET_KEY;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.au.secret.key")
    public static String AMAZON_AU_SECRET_KEY;

    //error钉钉token
    @XxlConf(value = MULTICHANNEL_PREFIX + "error.dingtalk.token")
    public static String ERROR_DINGTALK_TOKEN;
    //info钉钉token
    @XxlConf(value = MULTICHANNEL_PREFIX + "info.dingtalk.token")
    public static String INFO_DINGTALK_TOKEN;

    //oss配置
    @XxlConf(value = MULTICHANNEL_PREFIX + "oss.endpoint")
    public static String OSS_ENDPOINT;
    @XxlConf(value = MULTICHANNEL_PREFIX + "oss.access.key.id")
    public static String OSS_ACCESS_KEY_ID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "oss.access.key.secret")
    public static String OSS_ACCESS_KEY_SECRET;
    @XxlConf(value = MULTICHANNEL_PREFIX + "oss.url")
    public static String OSS_URL;
    @XxlConf(value = MULTICHANNEL_PREFIX + "oss.bucket.name")
    public static String OSS_BUCKET_NAME;
    @XxlConf(value = MULTICHANNEL_PREFIX + "oss.public.bucket.name")
    public static String OSS_PUBLIC_BUCKET_NAME;

    //ebay配置
    @XxlConf(value = MULTICHANNEL_PREFIX + "ebay.appid")
    public static String EBAY_APPID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "ebay.certid")
    public static String EBAY_CERTID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "ebay.devid")
    public static String EBAY_DEVID;

    //microsoft配置
    @XxlConf(value = MULTICHANNEL_PREFIX + "microsoft.clientid")
    public static String MICROSOFT_CLIENTID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "microsoft.client.secret")
    public static String MICROSOFT_CLIENT_SECRET;

    //tiktok配置
    @XxlConf(value = MULTICHANNEL_PREFIX + "tiktok.appkey")
    public static String TIKTOK_APP_KEY;
    @XxlConf(value = MULTICHANNEL_PREFIX + "tiktok.app.secret")
    public static String TIKTOK_APP_SECRET;

    //amazon vendor仓储对应码pdf的url
    @XxlConf(value = MULTICHANNEL_PREFIX + "amazon.vendor.fulfillment.center.address.list.pdf.url", defaultValue = "https://vendorcentral.amazon.com/cgi-bin/fileUpload/VCResourceCenter/getResourceCenterFile.cgi?_encoding=UTF8&fileId=HO4u5tTTFS8z2NNdBm3M08YiHK/JlA0cqBmchKdiwjA=&filename=F+jpHOoNdSaTH/LyICmFVljvMvk0p7TB5TXpcWkVjCP2rtdso4wKyJr7KA+Js5WF8xndewcWu/M=&type=Z2D/KCIlWDzSjE2iaXAkzg==")
    public static String AMAZON_VENDOR_FULFILLMENT_CENTER_ADDRESS_LIST_PDF_URL;

    @XxlConf(value = MULTICHANNEL_PREFIX + "order.send.mq.env.switch")
    public static String ORDER_SEND_MQ_ENV_SWITCH;

    @XxlConf(value = MULTICHANNEL_PREFIX + "tracking.return.send.mq.env.switch")
    public static String TRACKING_RETURN_SEND_MQ_ENV_SWITCH;

    @XxlConf(value = MULTICHANNEL_PREFIX + "bound.ack.send.mq.env.switch")
    public static String BOUND_ACK_SEND_MQ_ENV_SWITCH;

    @XxlConf(value = MULTICHANNEL_PREFIX + "inbound.shipment.send.mq.env.switch")
    public static String INBOUND_SHIPMENT_SEND_MQ_ENV_SWITCH;

    @XxlConf(value = MULTICHANNEL_PREFIX + "inventory.fba.update.send.mq.env.switch")
    public static String INVENTORY_FBA_UPDATE_SEND_MQ_ENV_SWITCH;

    @XxlConf(value = MULTICHANNEL_PREFIX + "amazonvendorpo.fba.shippingmethondcode")
    public static String AMAZONVENDORPO_FBA_SHIPPINGMETHONDCODE;

    @XxlConf(value = MULTICHANNEL_PREFIX + "amazonvendorpo.sku.ignore")
    public static String AMAZONVENDORPO_SKU_IGNORE;

    //wayfair不需要自动注册面单的账号
    @XxlConf(value = MULTICHANNEL_PREFIX + "wayfair.notbuy.label")
    public static String WAYFAIR_NOTBUY_LABEL;
    //wayfair不需要通过接口做confirm的账号
    @XxlConf(value = MULTICHANNEL_PREFIX + "not.confirm.channel")
    public static String NOT_CONFIRM_CHANNEL;
    //wayfair当订单已经是生产模式，库存还需要测试模式的账号
    @XxlConf(value = MULTICHANNEL_PREFIX + "wayfair.inv.test.channel")
    public static String WAYFAIR_INV_TEST_CHANNEL;
    //wayfair.Appearance International只有这些sku的订单转发给傲飞斯组织
    @XxlConf(value = MULTICHANNEL_PREFIX + "wayfair.order.forward.sku")
    public static String WAYFAIR_ORDER_FORWARD_SKU;
    //homedepot tracking回传开关(old new)
    @XxlConf(value = MULTICHANNEL_PREFIX + "homedepot.tracking.api.switch", defaultValue = "old")
    public static String HOMEDEPOT_TRACKING_API_SWITCH;
    @XxlConf(value = MULTICHANNEL_PREFIX + "supplementary.amazon.account")
    public static String SUPPLEMENTARY_AMAZON_ACCOUNT;

    @XxlConf(value = MULTICHANNEL_PREFIX + "fba.ack.send.mq.env.switch")
    public static String FBA_ACK_SEND_MQ_ENV_SWITCH;
    @XxlConf(value = MULTICHANNEL_PREFIX + "homedepot.order.ignore")
    public static String HOMEDEPOT_ORDER_IGNORE;

    @XxlConf(value = MULTICHANNEL_PREFIX + "wish.client.id")
    public static String WISH_CLIENT_ID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "wish.client.secret")
    public static String WISH_CLIENT_SECRET;

    @XxlConf(value = MULTICHANNEL_PREFIX + "amz.vendor.accesskey.id")
    public static String AMAZON_VENDOR_ACCESSKEY_ID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amz.vendor.appid")
    public static String AMAZON_VENDOR_APPID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amz.vendor.clientid")
    public static String AMAZON_VENDOR_CLIENTID;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amz.vendor.clientsecret")
    public static String AMAZON_VENDOR_CLIENTSECRET;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amz.vendor.secretkey")
    public static String AMAZON_VENDOR_SECRETKEY;
    @XxlConf(value = MULTICHANNEL_PREFIX + "amz.vendor.role.arn")
    public static String AMAZON_VENDOR_ARN;
    //vendor api接口的账号
    @XxlConf(value = MULTICHANNEL_PREFIX + "vendor.api.accounts")
    public static String AMAZON_VENDOR_API_ACCOUNTS;

    /**
     * temu美区代理IP地址
     */
    @XxlConf(value = MULTICHANNEL_PREFIX + "temu.us.proxy.host")
    public static String TEMU_US_PROXY_HOST;

    /**
     * temu美区代理账号
     */
    @XxlConf(value = MULTICHANNEL_PREFIX + "temu.us.proxy.username")
    public static String TEMU_US_PROXY_USERNAME;

    /**
     * temu美区代理密码
     */
    @XxlConf(value = MULTICHANNEL_PREFIX + "temu.us.proxy.password")
    public static String TEMU_US_PROXY_PASSWORD;





    /**
     * temu欧区代理IP地址
     */
    @XxlConf(value = MULTICHANNEL_PREFIX + "temu.eu.proxy.host")
    public static String TEMU_EU_PROXY_HOST;

    /**
     * temu欧区代理账号
     */
    @XxlConf(value = MULTICHANNEL_PREFIX + "temu.eu.proxy.username")
    public static String TEMU_EU_PROXY_USERNAME;

    /**
     * temu欧区代理密码
     */
    @XxlConf(value = MULTICHANNEL_PREFIX + "temu.eu.proxy.password")
    public static String TEMU_EU_PROXY_PASSWORD;


}
