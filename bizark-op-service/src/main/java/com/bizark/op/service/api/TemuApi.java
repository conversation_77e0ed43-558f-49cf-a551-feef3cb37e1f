package com.bizark.op.service.api;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.aliyun.odps.commons.util.OSUtils;
import com.bizark.op.api.enm.sale.temu.FileUploadTypeEnum;
import com.bizark.op.api.enm.temu.TemuApiEnum;
import com.bizark.op.api.enm.temu.TemuAppInfo;
import com.bizark.op.api.enm.temu.TemuUrl;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.document.ScDocumentMaterial;
import com.bizark.op.api.entity.op.sale.request.BaseTemuRequest;
import com.bizark.op.api.entity.op.sale.request.TemuGoodsImageUploadRequest;
import com.bizark.op.api.entity.op.sale.request.TemuGoodsListRequest;
import com.bizark.op.api.entity.op.sale.request.TemuInstructionUploadRequest;
import com.bizark.op.api.entity.op.sale.response.TemuGoodsImageUploadResponse;
import com.bizark.op.api.entity.op.sale.response.TemuGoodsListResponse;
import com.bizark.op.api.entity.op.sale.response.TemuInstructionUploadResponse;
import com.bizark.op.api.entity.op.temu.request.TemuCrosWarehouseRequest;
import com.bizark.op.api.entity.op.temu.response.TemuCrosWarehouseResponse;
import com.bizark.op.api.entity.op.temu.response.TemuLocalWarehouseResponse;
import com.bizark.op.api.entity.op.temu.response.TemuWarehouseResponse;
import com.bizark.op.common.util.ImageUtil;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.service.util.XxlConfig;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class TemuApi {


    public static final String CN_ACCESS_TOKEN = "cnAccessToken";
    public static final String US_ACCESS_TOKEN = "usAccessToken";
    public static final String SELF_CN_ACCESS_TOKEN = "selfCnAccessToken";





    @Value("${task.center.file.path}")
    private String filePath;


    /**
     * 请求temu后台数据
     * @param request
     * @param clazz
     * @param <T>
     * @return
     */
    public <T> T requestTemu(Account account, BaseTemuRequest request, Class<T> clazz) {

        String requestId = account.getFlag() + "_" + System.currentTimeMillis();

        TemuUrl temuUrl = request.type();
        request.setTimestamp(getTimestamp());
        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            log.error("{} - {} - 请求错误 - 店铺连接信息为空 :{}", requestId, temuUrl.getName(), account.getFlag());
            return null;
        }


        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey(CN_ACCESS_TOKEN)) {
            log.error("{} - {} - 请求错误 - cnAccessToken is empty : {}", requestId, temuUrl.getName(), account.getFlag());
            return null;
        }
        request.setType(temuUrl.getValue());
        request.setAccessToken(jsonObject.getString(CN_ACCESS_TOKEN));
        request.setAppKey(XxlConfig.TEMU_APP_CN_KEY_ID);
        request.setSign(null);
        String sign = getTemuSign(request,XxlConfig.TEMU_APP_CN_SECRET);
        request.setSign(sign);
        HttpRequest post = HttpUtil.createPost(temuUrl.getCnBaseUrl());
        post.header("Content-Type", "application/json");
        try {
            String requestBody = JSON.toJSONString(request, SerializerFeature.DisableCircularReferenceDetect);

            if (temuUrl != TemuUrl.IMAGE_UPLOAD && temuUrl != TemuUrl.INSTRUCTION_UPLOAD) {
                log.info("{} - {} - 请求参数：{}", requestId, temuUrl.getName(), requestBody);
            } else {
                log.info("{} - {} - 不输出参数", requestId, temuUrl.getName());
            }
            post.body(requestBody);
            HttpResponse httpResponse = post.execute();
            if (!httpResponse.isOk()) {
                log.error("{} - {} - 请求错误: {} ", requestId, temuUrl.getName(), httpResponse.body());
                return null;
            }
            String body = httpResponse.body();
            log.info("{} - {} - 请求响应：{}", requestId, temuUrl.getName(), body);
            return JSON.parseObject(body, clazz);
        } catch (Exception e) {
            log.error("{} - {} - 请求错误: {} ", requestId, temuUrl.getName(), e.getMessage(), e);
            return null;
        }

    }





    public <T> T requestUsTemu(Account account, BaseTemuRequest baseTemuRequest, Class<T> clazz) {

        String requestId = account.getFlag() + "_" + System.currentTimeMillis();

        if (Objects.equals(account.getSaleChannel(), "2")) {
            // 请求本本店铺
            return requestLocalTemu(account, baseTemuRequest, clazz);
        }

        return requestTemu(account, baseTemuRequest, clazz);
//        TemuUrl temuUrl = baseTemuRequest.type();
//        baseTemuRequest.setTimestamp(getTimestamp());
//        String connectStr = account.getConnectStr();
//        if (StrUtil.isBlank(connectStr)) {
//            log.error("{} - {} - 请求错误 - 店铺连接信息为空 :{}", requestId, temuUrl.getName(), account.getFlag());
//            return null;
//        }
//
//
//        JSONObject jsonObject = JSON.parseObject(connectStr);
//
//        if (!jsonObject.containsKey(US_ACCESS_TOKEN)) {
//            log.error("{} - {} - 请求错误 - usAccessToken is empty : {}", requestId, temuUrl.getName(), account.getFlag());
//            return null;
//        }
//
//        TemuAppInfo appInfo = getTemuAppInfo(account);
//
//        if (StrUtil.isBlank(appInfo.getAppkey())) {
//            log.error("{} - {} - 请求错误 - appInfo is empty : {}", requestId, temuUrl.getName(), account.getFlag());
//            return null;
//        }
//
//        baseTemuRequest.setType(temuUrl.getValue());
//        baseTemuRequest.setAccessToken(jsonObject.getString(US_ACCESS_TOKEN));
//        baseTemuRequest.setAppKey(appInfo.getAppkey());
//        baseTemuRequest.setSign(null);
//        String sign = getTemuSign(baseTemuRequest, appInfo.getAppSecret());
//        baseTemuRequest.setSign(sign);
//
//        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(appInfo.getProxyHost(), appInfo.getProxyPort()));
//        OkHttpClient client = new OkHttpClient.Builder()
//                .proxy(proxy)
//                .proxyAuthenticator((route, response) -> {
//                    String credential = Credentials.basic(appInfo.getProxyUserName(), appInfo.getProxyPassword());
//                    return response.request().newBuilder().header("Proxy-Authorization", credential).build();
//                })
//                .connectTimeout(120L, TimeUnit.SECONDS)
//                .readTimeout(120L, TimeUnit.SECONDS)
//                .build();
//        StringBuilder result = new StringBuilder();
//
//        BufferedReader in = null;
//
//        MediaType mediaType = MediaType.parse("application/json");
//
//        String requestBody = JSON.toJSONString(baseTemuRequest, SerializerFeature.DisableCircularReferenceDetect);
//
//        RequestBody body = RequestBody.create(mediaType, requestBody);
//
//
//        Request request = new Request.Builder().url(temuUrl.getUsBaseUrl())
//                .addHeader("Content-Type", "application/json")
//                .post(body)
//                .build();
//
//
//        try {
//            if (temuUrl != TemuUrl.IMAGE_UPLOAD && temuUrl != TemuUrl.INSTRUCTION_UPLOAD) {
//                log.info("{} - {} - 请求参数：{}", requestId, temuUrl.getName(), requestBody);
//            }
//
//            Response response = client.newCall(request).execute();
//            if (!response.isSuccessful()) {
//                log.error("{} - {} - 请求失败: {} ", requestId, temuUrl.getName(), response.message());
//                return null;
//            }
//
//            ResponseBody responseBody = response.body();
//            InputStream inputStream = responseBody.byteStream();
//            in = new BufferedReader(new InputStreamReader(inputStream));
//            String line;
//            while ((line = in.readLine()) != null) {
//                result.append(line);
//            }
//            in.close();
//            log.info("{} - {} - 请求响应：{}", requestId, temuUrl.getName(), result.toString());
//            return JSON.parseObject(result.toString(), clazz);
//        } catch (Exception e) {
//            log.error("{} - {} - 请求错误: {} ", requestId, temuUrl.getName(), e.getMessage(), e);
//            return null;
//        }

    }

    private TemuAppInfo getTemuAppInfo(Account account) {
        String countryCode = account.getCountryCode();
        TemuApiEnum apiEnum = TemuApiEnum.getByCountryCode(countryCode);
        if (apiEnum == null) {
            return TemuAppInfo.defaultInfo();
        }

        switch (apiEnum) {
            case DE:
                return TemuAppInfo.builder().appkey(XxlConfig.TEMU_EU_APP_KEY)
                        .appSecret(XxlConfig.TEMU_EU_APP_SECRET)
                        .proxyHost(XxlConfig.TEMU_EU_PROXY_HOST)
                        .proxyUserName(XxlConfig.TEMU_EU_PROXY_USERNAME)
                        .proxyPassword(XxlConfig.TEMU_EU_PROXY_PASSWORD)
                        .proxyPort(8888)
                        .build();
            case US:
                return TemuAppInfo.builder().appkey(XxlConfig.TEMU_APP_US_KEY_ID)
                        .appSecret(XxlConfig.TEMU_APP_US_SECRET)
                        .proxyHost(XxlConfig.TEMU_US_PROXY_HOST)
                        .proxyUserName(XxlConfig.TEMU_US_PROXY_USERNAME)
                        .proxyPassword(XxlConfig.TEMU_US_PROXY_PASSWORD)
                        .proxyPort(8888)
                        .build();
            case JP:
                return TemuAppInfo.builder().appkey(XxlConfig.TEMU_JP_APP_KEY)
                        .appSecret(XxlConfig.TEMU_JP_APP_SECRET)
                        .proxyHost(XxlConfig.TEMU_US_PROXY_HOST)
                        .proxyUserName(XxlConfig.TEMU_US_PROXY_USERNAME)
                        .proxyPassword(XxlConfig.TEMU_US_PROXY_PASSWORD)
                        .proxyPort(8888)
                        .build();
            default:
                return TemuAppInfo.defaultInfo();
        }
    }

    private <T> T requestLocalTemu(Account account, BaseTemuRequest baseTemuRequest, Class<T> clazz) {

        String requestId = account.getFlag() + "_" + System.currentTimeMillis();


        TemuUrl temuUrl = baseTemuRequest.type();
        baseTemuRequest.setType(temuUrl.getValue());
        baseTemuRequest.setTimestamp(getTimestamp());
        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            log.error("{} - {} - 请求错误 - 店铺连接信息为空 :{}", requestId, temuUrl.getName(), account.getFlag());
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey("accessToken")) {
            log.error("{} - {} - 请求错误 - accessToken为空 :{}", requestId, temuUrl.getName(), account.getFlag());
            return null;
        }
        if (!jsonObject.containsKey("appkey")) {
            log.error("{} - {} - 请求错误 - appkey为空 :{}", requestId, temuUrl.getName(), account.getFlag());
            return null;
        }
        if (!jsonObject.containsKey("appSecret")) {
            log.error("{} - {} - 请求错误 - appSecret为空 :{}", requestId, temuUrl.getName(), account.getFlag());
            return null;
        }
        baseTemuRequest.setAccessToken(jsonObject.getString("accessToken"));
        baseTemuRequest.setAppKey(jsonObject.getString("appkey"));
        baseTemuRequest.setSign(null);
        String sign = getTemuSign(baseTemuRequest, jsonObject.getString("appSecret"));
        baseTemuRequest.setSign(sign);
        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(XxlConfig.TEMU_US_PROXY_HOST, 8888));

        OkHttpClient client = new OkHttpClient.Builder()
                .proxy(proxy)
                .proxyAuthenticator((route, response) -> {
                    String credential = Credentials.basic(XxlConfig.TEMU_US_PROXY_USERNAME, XxlConfig.TEMU_US_PROXY_PASSWORD);
                    return response.request().newBuilder().header("Proxy-Authorization", credential).build();
                })
                .connectTimeout(120L, TimeUnit.SECONDS)
                .readTimeout(120L, TimeUnit.SECONDS)
                .build();
        StringBuilder result = new StringBuilder();

        BufferedReader in = null;

        MediaType mediaType = MediaType.parse("application/json");

        String requestBody = JSON.toJSONString(baseTemuRequest, SerializerFeature.DisableCircularReferenceDetect);

        RequestBody body = RequestBody.create(mediaType, requestBody);


        Request request = new Request.Builder().url(temuUrl.getUsBaseUrl())
                .addHeader("Content-Type", "application/json")
                .post(body)
                .build();


        try {
            if (temuUrl != TemuUrl.IMAGE_UPLOAD && temuUrl != TemuUrl.INSTRUCTION_UPLOAD) {
                log.info("{} - {} - 请求参数：{}", requestId, temuUrl.getName(), requestBody);
            }

            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.error("{} - {} - 请求失败: {} ", requestId, temuUrl.getName(), response.message());
                return null;
            }

            ResponseBody responseBody = response.body();
            InputStream inputStream = responseBody.byteStream();
            in = new BufferedReader(new InputStreamReader(inputStream));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            in.close();
            log.info("{} - {} - 请求响应：{}", requestId, temuUrl.getName(), result.toString());
            return JSON.parseObject(result.toString(), clazz);
        } catch (Exception e) {
            log.error("{} - {} - 请求错误: {} ", requestId, temuUrl.getName(), e.getMessage(), e);
            return null;
        }
    }


    public TemuGoodsImageUploadResponse uploadGoodsImage(Account account, ScDocumentMaterial material, Integer imageBizType, boolean boost, Integer catId, boolean doIntelligenceCrop, Integer sizeMode) throws IOException {
        File file = new File(material.getMaterialPath());
        String filePath = this.filePath + file.getName();
        File downloadFile = new File(filePath);

        long fileSize = -1;

        if (!downloadFile.exists()) {
            com.bizark.op.common.util.FileUtil.downloadFile(material.getMaterialPath(), filePath);

            if (Objects.equals(material.getType().getCode(), FileUploadTypeEnum.CAROUSEL_IMAGE.getCode())) {
                long maxSize = 2 * 1048576;

                long size = FileUtil.size(downloadFile);
                String fileName = downloadFile.getName();

                if (size > maxSize) {
                    log.info("Temu图片上传 - 当前轮播图 {} 大小:{}  超过限制,压缩图片", fileName, size);
                    // 图片压缩
                    String suffix = fileName.substring(fileName.lastIndexOf("."), fileName.length());
                    String prefix = fileName.substring(0, fileName.lastIndexOf("."));
                    File parentFile = FileUtil.getParent(downloadFile, 1);
                    boolean isWindows = OSUtils.getOS().toLowerCase().contains("win");

                    String targetFileName = prefix + "bak" + suffix;

                    String targetPath = parentFile.getPath() + (isWindows ? "\\" : "/") + targetFileName;

                    try {
                        File compressImage = ImageUtil.compressImages(downloadFile, targetPath);
                        downloadFile.delete();
                        downloadFile = compressImage;
                        fileSize = FileUtil.size(downloadFile);
                        log.info("Temu图片上传 - 图片压缩完毕：{} -> {} 大小:{} -> {} ", fileName, targetFileName, size, fileSize);
                    } catch (Exception e) {
                        log.error("Temu图片上传 - 文件压缩失败：{}", material.getId());
                        return TemuGoodsImageUploadResponse.fail("文件压缩失败：" + material.getId());
                    }
                } else {
                    fileSize = FileUtil.size(downloadFile);
                }
            }
        }

        Integer width = null;
        Integer height = null;
        switch (sizeMode) {
            case 0:
                BufferedImage image = ImageIO.read(downloadFile);
                width = image.getWidth();
                height = image.getHeight();
                break;
            case 1:
                width = 800;
                height = 800;
                break;
            case 2:
                width = 1350;
                height = 1800;
                break;
        }

        TemuGoodsImageUploadResponse uploadResponse = uploadGoodsImage(account, downloadFile, imageBizType, boost, catId, doIntelligenceCrop, sizeMode);
        if (downloadFile.exists()) {
            downloadFile.delete();
        }
        if (!uploadResponse.getSuccess() || Objects.isNull(uploadResponse.getResult())) {
            return uploadResponse;
        }
        TemuGoodsImageUploadResponse.Result result = uploadResponse.getResult();
        result.setWidth(width);
        result.setHeight(height);
        result.setSize(fileSize);
        return uploadResponse;
    }
    /**
     * @param file               文件
     * @param imageBizType       枚举值：0、1，入参 1 返回的 url 用以货品发布时的外包装使用
     * @param boost              是否 AI 清晰度提升
     * @param catId              叶子类目 ID，按不同类型进行裁剪，当 doIntelligenceCrop=true 生效
     * @param doIntelligenceCrop 是否 AI 智能裁剪，true-根据 sizeMode 返回一组智能裁剪图（ 1 张原图 +3 张裁剪图）
     * @param sizeMode           返回尺寸大小，0-原图大小，1-800*800（1:1），2-1350*1800（3:4）
     * @return
     */
    public TemuGoodsImageUploadResponse uploadGoodsImage(Account account, File file, Integer imageBizType, boolean boost, Integer catId, boolean doIntelligenceCrop, Integer sizeMode) {
        // 检查店铺信息
        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            log.error("Temu货品图片上传失败 - 店铺连接信息为空 ：{}", account.getFlag());
            return TemuGoodsImageUploadResponse.fail("店铺连接信息为空");
        }
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey(CN_ACCESS_TOKEN)) {
            log.error("Temu货品图片上传失败 - cnAccessToken is empty : {}", account.getFlag());
            return TemuGoodsImageUploadResponse.fail("cnAccessToken is empty");

        }
        // 检查文件格式
//        String contentType = file.getContentType();
//        if (StrUtil.isBlank(contentType) || !contentType.startsWith("image")) {
//            return TemuGoodsImageUploadResponse.fail("只能上传图片");
//        }

        String originalFilename = file.getName();
        if (StrUtil.isBlank(originalFilename) || (!originalFilename.endsWith("jpg") && !originalFilename.endsWith("png") && !originalFilename.endsWith("jepg"))) {
            return TemuGoodsImageUploadResponse.fail("只支持 jpg/jpeg、png 图片格式");
        }


        TemuGoodsImageUploadRequest uploadRequest = new TemuGoodsImageUploadRequest();
        try (InputStream inputStream = FileUtil.getInputStream(file)) {
            // 图片编码
            byte[] imageData = new byte[(int) FileUtil.size(file)];
            inputStream.read(imageData);
            uploadRequest.setImage(
                    "data:image/"+ originalFilename.split("\\.")[1] + ";base64," + Base64.getEncoder().encodeToString(imageData)
            );
        } catch (Exception e) {
            log.error("Temu货品图片上传失败：{}", e.getMessage(), e);
            return TemuGoodsImageUploadResponse.fail(e.getMessage());
        }
        uploadRequest.setType(uploadRequest.type().getValue());
        uploadRequest.setImageBizType(imageBizType);

        uploadRequest.setOptions(new TemuGoodsImageUploadRequest.Options(
                boost, catId, doIntelligenceCrop, sizeMode
        ));
        return requestTemu(account, uploadRequest, TemuGoodsImageUploadResponse.class);
    }


//    public static void main(String[] args) { // 301DC1C40B0CF43E5DE9F04FFB5B5B1E
//        TemuGoodsImageUploadRequest uploadRequest = new TemuGoodsImageUploadRequest();
//        uploadRequest.setType(uploadRequest.type().getValue());
//        uploadRequest.setAccessToken("zdtgu0bthgjbh7yzvtauedihjsgaqxaptfqdo3bu5nazdstrkqq2t1jc");
//        uploadRequest.setAppKey("3abcdf014c417524e61cc706e9a7b820");
//        uploadRequest.setImageBizType(0);
//        uploadRequest.setTimestamp(1718777513469L);
//
//        TemuGoodsImageUploadRequest.Options options = new TemuGoodsImageUploadRequest.Options();
//        options.setBoost(true);
//        options.setCatId(24879);
//        options.setDoIntelligenceCrop(false);
//        options.setSizeMode(0);
//        uploadRequest.setOptions(
//                options
//        );
//
//        File file = new File("D:\\Desktop\\temu文件下载\\7bdae1b6f3b641c9a37a39abcdc24d29.jpg");
//        try (InputStream inputStream = FileUtil.getInputStream(file)) {
//            // 图片编码
//            byte[] imageData = new byte[(int) FileUtil.size(file)];
//            inputStream.read(imageData);
//            uploadRequest.setImage(
//                    "data:image/"+ file.getName().split("\\.")[1] + ";base64," + Base64.getEncoder().encodeToString(imageData)
//            );
//        } catch (Exception e) {
//            log.error("Temu货品图片上传失败：{}", e.getMessage(), e);
//
//        }
//
//        TemuApi temuApi = new TemuApi();
//        String sign = temuApi.getTemuSign(uploadRequest);
//        String sign1 = temuApi.getTemuSign(JSON.parseObject(JSON.toJSONString(uploadRequest)));
//        System.out.println(sign);
//        System.out.println(sign1);
//        System.out.println(sign.equals(sign1));
//    }


    public TemuInstructionUploadResponse uploadInstruction(Account account,String url) throws IOException {

        File downloadFile = new File(url);
        com.bizark.op.common.util.FileUtil.downloadFile(url, filePath + downloadFile.getName());
        File file = new File(filePath + downloadFile.getName());
        TemuInstructionUploadRequest uploadRequest = new TemuInstructionUploadRequest();
        try (InputStream inputStream = FileUtil.getInputStream(file)) {
            // 图片编码
            byte[] bytes = new byte[(int) FileUtil.size(file)];
            inputStream.read(bytes);
            uploadRequest.setBase64File(Base64.getEncoder().encodeToString(bytes));
            TemuInstructionUploadResponse response = requestTemu(account, uploadRequest, TemuInstructionUploadResponse.class);
            if (file.exists()) {
                file.delete();
            }
            return response;
        } catch (Exception e) {
            log.error("Temu说明书上传失败：{}", e.getMessage(), e);
        }

        return null;
    }


    /**
     * 获取Temu请求签名
     * @param param
     * @return
     */
    public static String getTemuSign(Object param,String appsecret) {
        Map<String, Object> object = JSON.parseObject(JSON.toJSONString(param, SerializerFeature.DisableCircularReferenceDetect), Map.class);
        TreeMap<String, Object> sortedParams = new TreeMap<>(object);
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof String) {
                sb.append(entry.getKey()).append(entry.getValue());
            } else if (value instanceof JSONArray) {
                sortJsonArray((JSONArray) value);
                sb.append(entry.getKey()).append(JSON.toJSONString(value));
            } else if (!(value instanceof JSONObject)) {
                sb.append(entry.getKey()).append(JSON.toJSONString(entry.getValue()));
            } else {
                Map<String, Object> vObj = JSON.parseObject(JSON.toJSONString(value), Map.class);
                TreeMap<String, Object> vParam = new TreeMap<>(vObj);
                sb.append(entry.getKey()).append(JSON.toJSONString(vParam));
            }
        }
        return DigestUtils.md5DigestAsHex((appsecret + sb + appsecret).getBytes()).toUpperCase();
    }


    private static void sortJsonArray(JSONArray jsonArray) {
        for (int i = 0; i < jsonArray.size(); i++) {
            Object o = jsonArray.get(i);
            if (!(o instanceof JSONObject)) {
                continue;
            }
            JSONObject jsonObject = (JSONObject) o;
            jsonArray.set(i, sortJsonObject(jsonObject));
        }
    }

    private static Object sortJsonObject(JSONObject obj) {
        Map<String, Object> vObj = JSON.parseObject(obj.toJSONString(), Map.class);
        TreeMap<String, Object> vParam = new TreeMap<>(vObj);
        for (Map.Entry<String, Object> entry : vParam.entrySet()) {
            Object value = entry.getValue();

            if (value instanceof BigDecimal) {
                String decimalStr = value.toString();
                String[] split = decimalStr.split("\\.");
                boolean allZero = true;
                for (char c : split[1].toCharArray()) {
                    if (c != '0') {
                        allZero = false;
                    }
                }
                if (allZero) {
                    entry.setValue(Integer.parseInt(split[0]));
                }
            }

            if (value instanceof JSONArray) {
                JSONArray array = (JSONArray) value;
                sortJsonArray(array);
            }
            if (value instanceof JSONObject) {
                JSONObject v = (JSONObject) value;
                entry.setValue(sortJsonObject(v)) ;
            }
        }
        return vParam;
    }

    public TemuGoodsListResponse selectGoodsList(Account account, List<Long> skcIds,Integer page, Integer pageSize) {
        if (CollectionUtil.isEmpty(skcIds)) {
            return TemuGoodsListResponse.fail("SKCID不能为空");
        }
        TemuGoodsListRequest request = new TemuGoodsListRequest();
        request.setPage(page);
        request.setProductSkcIds(skcIds);
        request.setPageSize(pageSize);
//        TemuGoodsListResponse response = null;

//        TemuGoodsListResponse emptyResponse = TemuGoodsListResponse.empty();
        return requestTemu(account, request, TemuGoodsListResponse.class);
//        while ((response = requestTemu(account, request, TemuGoodsListResponse.class)).getSuccess() != null &&
//                        response.getSuccess() &&
//                        response.getResult() != null &&
//                        CollectionUtil.isNotEmpty(response.getResult().getData())) {
//            request.nextPage();
//            request.setSign(null);
//            emptyResponse.addResult(
//                    response.getResult().getData()
//            );
//        }
//        return emptyResponse;
    }


    public static long getTimestamp(){
        // 获取当前时间的 UNIX 时间戳（秒）
        long currentTime = Instant.now().getEpochSecond();

        // 设置允许的时间范围（±300秒）
        long minTime = currentTime - 200;
        long maxTime = currentTime + 200;

        // 随机生成一个在该范围内的时间戳
        Random random = new Random();
        return minTime + (long) (random.nextDouble() * (maxTime - minTime));
    }

    public TemuWarehouseResponse getTemuWarehouse(Account account) {
        boolean isLocal = Objects.equals(account.getSaleChannel(), "2");
        if (isLocal) {
            BaseTemuRequest temuRequest = new BaseTemuRequest() {
                @Override
                public TemuUrl type() {
                    return TemuUrl.BG_LOGISTICS_WAREHOUSE_LIST_GET;
                }
            };
            return requestUsTemu(account, temuRequest, TemuLocalWarehouseResponse.class);
        }
        TemuCrosWarehouseRequest request = new TemuCrosWarehouseRequest();
        request.setSiteIdList(
                Arrays.asList("100")
        );
        return requestTemu(account, request, TemuCrosWarehouseResponse.class);
    }


    public <T> T requestSelfCnTemu(Account account, BaseTemuRequest request, Class<T> clazz) {
        String requestId = account.getFlag() + "_" + System.currentTimeMillis();

        TemuUrl temuUrl = request.type();
        request.setTimestamp(getTimestamp());
        String connectStr = account.getConnectStr();
        if (StrUtil.isBlank(connectStr)) {
            log.error("{} - {} - 请求错误 - 店铺连接信息为空 :{}", requestId, temuUrl.getName(), account.getFlag());
            return null;
        }


        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey(SELF_CN_ACCESS_TOKEN)) {
            log.error("{} - {} - 请求错误 - selfCnAccessToken is empty : {}", requestId, temuUrl.getName(), account.getFlag());
            return null;
        }
        request.setType(temuUrl.getValue());
        request.setAccessToken(jsonObject.getString(SELF_CN_ACCESS_TOKEN));
        request.setAppKey(jsonObject.getString("selfCnAppKey"));
        request.setSign(null);
        String sign = getTemuSign(request,jsonObject.getString("selfCnAppSecret"));
        request.setSign(sign);
        HttpRequest post = HttpUtil.createPost(temuUrl.getCnBaseUrl());
        post.header("Content-Type", "application/json");
        try {
            String requestBody = JSON.toJSONString(request, SerializerFeature.DisableCircularReferenceDetect);

            if (temuUrl != TemuUrl.IMAGE_UPLOAD && temuUrl != TemuUrl.INSTRUCTION_UPLOAD) {
                log.info("{} - {} - 请求参数：{}", requestId, temuUrl.getName(), requestBody);
            }
            post.body(requestBody);
            HttpResponse httpResponse = post.execute();
            if (!httpResponse.isOk()) {
                log.error("{} - {} - 请求错误: {} ", requestId, temuUrl.getName(), httpResponse.body());
                return null;
            }
            String body = httpResponse.body();
            log.info("{} - {} - 请求响应：{}", requestId, temuUrl.getName(), body);
            //由于接口qps 较低增加重试机制
            if(StringUtils.isNotEmpty(body)) {
                JSONObject resultJsonObject = JSONObject.parseObject(body);
                Integer errorCode = resultJsonObject.getInteger("errorCode");
                if(new Integer("4000004").equals(errorCode)) {
                    try {
                        TimeUnit.SECONDS.sleep(1);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    if (temuUrl != TemuUrl.IMAGE_UPLOAD && temuUrl != TemuUrl.INSTRUCTION_UPLOAD) {
                        log.info("{} - {} - 重试请求参数：{}", requestId, temuUrl.getName(), requestBody);
                    }
                    httpResponse = post.execute();
                    if (!httpResponse.isOk()) {
                        log.error("{} - {} - 重试请求错误: {} ", requestId, temuUrl.getName(), httpResponse.body());
                        return null;
                    }
                    body = httpResponse.body();
                    log.info("{} - {} - 重试请求响应：{}", requestId, temuUrl.getName(), body);
                }
            }
            return JSON.parseObject(body, clazz);
        } catch (Exception e) {
            log.error("{} - {} - 请求错误: {} ", requestId, temuUrl.getName(), e.getMessage(), e);
            return null;
        }

    }
}
