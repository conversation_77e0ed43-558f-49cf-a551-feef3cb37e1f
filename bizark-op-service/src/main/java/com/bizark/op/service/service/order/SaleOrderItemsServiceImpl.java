package com.bizark.op.service.service.order;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.op.api.dto.apportion.QuerySellerSkuToOrderDTO;
import com.bizark.op.api.entity.op.order.SaleOrderItems;
import com.bizark.op.api.entity.op.refund.ReverseRecordDetails;
import com.bizark.op.api.service.order.SaleOrderItemsService;
import com.bizark.op.api.vo.order.SaleOrderItemPromotionVO;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.service.mapper.order.SaleOrderItemsMapper;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.mapping.Collection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

/**
 *
 */
@Service
@Slf4j
public class SaleOrderItemsServiceImpl extends ServiceImpl<SaleOrderItemsMapper, SaleOrderItems>
    implements SaleOrderItemsService {

    @Autowired
    private SaleOrderItemsMapper saleOrderItemsMapper;


    @Override
    public void updateConfirmedQtyById(SaleOrderItems saleOrderItems) {

    }

    @Override
    public String getSkuByOrgIdAndOrderNo(Integer orgId, String orderNo) {
        return null;
    }

    @Override
    public String getAsinByOrgIdAndOrderNo(Integer orgId, String amazonOrderId) {
        List<SaleOrderItems> orderItems = saleOrderItemsMapper.selectByOrgIdAndOrderNo(orgId, amazonOrderId);
        return CollectionUtil.isNotEmpty(orderItems) ? orderItems.get(0).getAsin() : "";
    }

    /**
     * @param
     * @param asin
     * @param discountPrice
     * @param usTime
     * @description:更新折后价格
     * @author: Moore
     * @date: 2023/8/27 17:38
     * @return: void
     **/
    @Override
    public void updateScOrderItemsDiscountPrice(String asin, String listPrice, String discountPrice, Date usTime) {
        try {
            if (StringUtils.isEmpty(asin)){
                return;
            }
            //同步当前PST时间订单数据

            LocalDateTime localNow = LocalDateTime.now(TimeZone.getTimeZone("America/Los_Angeles").toZoneId());
            String nowPstTime = localNow.format(DateTimeFormatter.ISO_DATE);
            if (StringUtils.isEmpty(nowPstTime)) {
                return;
            }
            //折扣无值，取页面原价
            if (!StringUtils.isEmpty(discountPrice)) {
                //判断是否是数字类型
                if (NumberUtil.isNumber(discountPrice)) {
//                    String ustime = DateUtil.convertDateToString(usTime);
//                    saleOrderItemsMapper.updateDiscountPrice(asin, discountPrice, nowPstTime);
                    log.info("平台促销费同步至订单明细：{},{},{}", asin, discountPrice, nowPstTime);
                }
            } else if (!StringUtils.isEmpty(listPrice)) {
                //判断是否是数字类型
                if (NumberUtil.isNumber(listPrice)) {
//                    String ustime = DateUtil.convertDateToString(usTime);
//                    saleOrderItemsMapper.updateDiscountPrice(asin, listPrice, nowPstTime);
                    log.info("平台促销费同步至订单明细：{},{},{}", asin, discountPrice, nowPstTime);
                }
            }
        } catch (Exception e) {
            log.error("同步DiscontPrice失败：{}", e);
            e.printStackTrace();
        }
    }

    @Override
    public BigDecimal getUnitPrice(ReverseRecordDetails i) {
        return null;
    }

    @Override
    public Long getDeptIdByOrderNo(String orderNo) {
        return saleOrderItemsMapper.selectDeptIdByOrderNo(orderNo);
    }

    @Override
    public List<SaleOrderItemPromotionVO> querySaleOrderItemPromotionList(QuerySellerSkuToOrderDTO querySellerSkuToOrderDTO) {
        return saleOrderItemsMapper.querySaleOrderItemPromotionList(querySellerSkuToOrderDTO);
    }

    @Override
    public List<SaleOrderItemPromotionVO> querySaleOrderItemPromotionBySellerSkuList(QuerySellerSkuToOrderDTO querySellerSkuToOrderDTO) {
        return saleOrderItemsMapper.querySaleOrderItemPromotionBySellerSkuList(querySellerSkuToOrderDTO);
    }


    /**
     * 根据订单号和asin查询
     * @param poNumber
     * @param asin
     * @param contextId
     * @return
     */
    @Override
    public List<SaleOrderItems> selectSaleOrderItemsByOrderNoAndAsin(String orderNo, String asin, Integer contextId) {
        return saleOrderItemsMapper.selectSaleOrderItemsByOrderNoAndAsin(orderNo,asin,contextId);
    }


    /**
     * @description: 获取指定渠道下，sellerSku销量及销售额
     * @author: Moore
     * @date: 2024/10/8 18:04
     * @param
     * @param sellerSKu
     * @param channel
     * @return: java.util.List<com.bizark.op.api.entity.op.order.SaleOrderItems>
    **/
    @Override
    public List<SaleOrderItems> selectQuantityAndItemAmount(List<String> sellerSKu, String channel,Date dateFrom,Date dateTo) {
        if (CollectionUtils.isEmpty(sellerSKu) || StringUtils.isEmpty(channel)) {
            return Collections.emptyList();
        }
        return  saleOrderItemsMapper.selectQuantityAndItemAmount(sellerSKu, channel,dateFrom,dateTo);
    }


    /**
     * 根据订单id和组织id查询订单项
     *
     * @param headId
     * @param orgId
     * @return
     */
    @Override
    public List<SaleOrderItems> selectByHeadIdAndOrgId(Integer headId, Integer orgId){
        return saleOrderItemsMapper.selectByHeadIdAndOrgId(headId, orgId);
    }
}




