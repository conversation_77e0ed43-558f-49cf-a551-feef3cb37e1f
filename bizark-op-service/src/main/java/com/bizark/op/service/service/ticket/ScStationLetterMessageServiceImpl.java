package com.bizark.op.service.service.ticket;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.op.api.entity.op.ticket.ScStationLetter;
import com.bizark.op.api.entity.op.ticket.ScStationLetterMessage;
import com.bizark.op.api.entity.op.ticket.TicketAttaches;
import com.bizark.op.api.service.ticket.IScStationLetterMessageService;
import com.bizark.op.api.service.ticket.TicketAttachesService;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.service.mapper.ticket.ScStationLetterMapper;
import com.bizark.op.service.mapper.ticket.ScStationLetterMessageMapper;
import com.bizark.op.service.mapper.ticket.TicketAttachesMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ScStationLetterMessageServiceImpl
        extends ServiceImpl<ScStationLetterMessageMapper, ScStationLetterMessage>
        implements IScStationLetterMessageService {

    @Autowired
    private ScStationLetterMessageMapper scStationLetterMessageMapper;

    @Autowired
    private TicketAttachesService ticketAttachesService;

    @Autowired
    private ScStationLetterMapper scStationLetterMapper;

    @Override
    public int insertScStationLetterMessage(ScStationLetterMessage scStationLetterMessage) {
        if ("1".equals(scStationLetterMessage.getType())) {
            scStationLetterMessage.setReadFlag("N");
        }
        scStationLetterMessage.setCreatedAt(DateUtils.getNowDate());
        return scStationLetterMessageMapper.insertScStationLetterMessage(scStationLetterMessage);
    }


    /**
     * @param
     * @param scStationLetterMessage
     * @description: 新增TIKTOK站内信息消息
     * @author: Moore
     * @date: 2023/10/18 10:06
     * @return: int
     **/
    @Override
    public int insertScStationLetterTikTokMessage(ScStationLetterMessage scStationLetterMessage) {
        if (scStationLetterMessage.getCreatedBy()==null){
            scStationLetterMessage.settingDefaultCreate();
        }
        return scStationLetterMessageMapper.insertScStationLetterMessage(scStationLetterMessage);
    }


    @Override
    public int insertBatchScStationLetterMessage(List<ScStationLetterMessage> messages) {
        return scStationLetterMessageMapper.insertBatchScStationLetterMessage(messages);
    }

    @Override
    public List<ScStationLetterMessage> selectScStationLetterMessageList(Long stationLetterId) {
        ScStationLetterMessage message = new ScStationLetterMessage();
        message.setStationLetterId(stationLetterId);
        List<ScStationLetterMessage> historicalMessageList = scStationLetterMessageMapper.selectScStationLetterMessageList(message);
        return historicalMessageList;
    }

    /**
     * @param
     * @param scStationLetterMessage
     * @description: 查询回话内容信息
     * @author: Moore
     * @date: 2023/10/18 0:00
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.ScStationLetterMessage>
     **/
    @Override
    public List<ScStationLetterMessage> selectScStationLetterMessageList(ScStationLetterMessage scStationLetterMessage) {
        return scStationLetterMessageMapper.selectScStationLetterMessageList(scStationLetterMessage);
    }

    @Override
    public int updateMessage(ScStationLetterMessage scStationLetterMessage) {
        int messageId = 0;
        if (Objects.isNull(scStationLetterMessage)) {
            return messageId;
        }
        scStationLetterMessage.setUpdatedAt(new Date());
        messageId = scStationLetterMessageMapper.updateScStationLetterMessage(scStationLetterMessage);
        return messageId;
    }


    /**
     * @param
     * @param scStationLetterMessage
     * @description: 根据工单iD及回话ID获取会话列表
     * @author: Moore
     * @date: 2023/10/18 17:01
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.ScStationLetterMessage>
     **/
    @Override
    public List<ScStationLetterMessage> selectConversationListBydesc(ScStationLetterMessage scStationLetterMessage) {
        return scStationLetterMessageMapper.selectScStationLetterMessageList(scStationLetterMessage);
    }



    /**
     * @param
     * @param scStationLetterMessage
     * @description: 根据工单iD及回话ID获取会话列表
     * @author: Moore
     * @date: 2023/10/18 17:01
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.ScStationLetterMessage>
     **/
    @Override
    public List<ScStationLetterMessage> selectConversationListByAsc(ScStationLetterMessage scStationLetterMessage) {
        return scStationLetterMessageMapper.selectScStationLetterMessageListAsc(scStationLetterMessage);
    }

    /**
     * @param
     * @param scStationLetter
     * @description: 调用指定用户所有会话信息
     * @author: Moore
     * @date: 2023/10/19 14:45
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.ScStationLetterMessage>
     **/
    @Override
    public List<ScStationLetterMessage> selectConversationListByConv(ScStationLetter scStationLetter) {
        return scStationLetterMessageMapper.selectConversationListByConv(scStationLetter);
    }


    /**
     * @description: 根据msgId查询会话内容
     * @author: Moore
     * @date: 2023/10/20 1:06
     * @param
     * @param msgId
     * @return: com.bizark.op.api.entity.op.ticket.ScStationLetterMessage
    **/
    @Override
    public ScStationLetterMessage selectScStationLetterMessageByMsgId(String msgId) {
        return scStationLetterMessageMapper.selectScStationLetterMessageByMsgId(msgId);
    }


    /**
     * @description:根据MsgId更新会话内容
     * @author: Moore
     * @date: 2023/10/20 1:06
     * @param
     * @param scStationLetterMessage
     * @return: java.lang.Integer
    **/
    @Override
    public Integer updateMessageByMsgId(ScStationLetterMessage scStationLetterMessage,String msgId) {
        return scStationLetterMessageMapper.updateScStationLetterMessageByMsgId(scStationLetterMessage, msgId);
    }

    @Override
    public List<ScStationLetterMessage> selectScStationLetterMessages(Long stationLetterId) {
        ScStationLetterMessage scStationLetterMessage = new ScStationLetterMessage();
        scStationLetterMessage.setStationLetterId(stationLetterId);
        List<ScStationLetterMessage> scStationLetterMessages = scStationLetterMessageMapper.selectScStationLetterMessageList(scStationLetterMessage);
        if(CollectionUtil.isNotEmpty(scStationLetterMessages)) {
            //按照send_time倒序排
            Collections.reverse(scStationLetterMessages);
            scStationLetterMessages.forEach(letterMessage -> {
                QueryWrapper queryWrapper = new QueryWrapper();
                queryWrapper.eq("document_id", letterMessage.getMessageId());
                queryWrapper.eq("document_type", "sc_station_letter_message");
                List<TicketAttaches> ticketAttachesList = ticketAttachesService.list(queryWrapper);
                letterMessage.setTicketAttachesList(ticketAttachesList);
            });
            return scStationLetterMessages;
        }
        return null;
    }

    /**
     * @param messageId
     * @description: 根据msgId查询会话内容
     * @author: Moore
     * @date: 2023/10/20 1:06
     * @return: com.bizark.op.api.entity.op.ticket.ScStationLetterMessage
     **/
    @Override
    public ScStationLetterMessage selectScStationLetterMessageByMessageId(Long messageId) {
        return scStationLetterMessageMapper.selectScStationLetterMessageByMessageId(messageId);
    }

    /**
     * Description: 站内信历史
     *
     * @param stationLetterId
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/5/21
     */
    @Override
    public List<ScStationLetterMessage> selectScStationLetterMessagesHis(Long stationLetterId) {
        //查询站内信主表
        ScStationLetter stationLetter = scStationLetterMapper.selectBaseScStationLetterById(stationLetterId);
        if(stationLetter == null) {
            throw new RuntimeException("查询异常未找到站内信主表");
        }
        String merchantEmail = stationLetter.getMerchantEmail();
        String customerEmail = stationLetter.getCustomerEmail();
        List<ScStationLetter> scStationLetterList = scStationLetterMapper.selectBymerchantEmailAndCustomerEmail(merchantEmail,customerEmail);
        if(CollectionUtil.isNotEmpty(scStationLetterList)) {
            List<Long> idList = scStationLetterList.stream().map(i -> i.getStationLetterId()).collect(Collectors.toList());
            ScStationLetterMessage scStationLetterMessage = new ScStationLetterMessage();
            scStationLetterMessage.setStationLetterIdList(idList);
            List<ScStationLetterMessage> scStationLetterMessages = scStationLetterMessageMapper.selectScStationLetterMessageList(scStationLetterMessage);
            if(CollectionUtil.isNotEmpty(scStationLetterMessages)) {
                scStationLetterMessages.forEach(letterMessage -> {
                    QueryWrapper queryWrapper = new QueryWrapper();
                    queryWrapper.eq("document_id", letterMessage.getMessageId());
                    queryWrapper.eq("document_type", "sc_station_letter_message");
                    List<TicketAttaches> ticketAttachesList = ticketAttachesService.list(queryWrapper);
                    letterMessage.setTicketAttachesList(ticketAttachesList);
                });
                return scStationLetterMessages;
            }
        }
        return null;
    }


    /**
     * @description: 转换后内容（OSS）
     * @author: Moore
     * @date: 2025/3/14 11:29
     * @param
     * @param msgId
     * @param uploadResult
     * @return: void
    **/
    @Override
    public void updateErpContentById(Long msgId, String uploadResult) {
        if (msgId == null) {
            return;
        }

        scStationLetterMessageMapper.updateErpContentById(msgId, uploadResult);
    }


}
