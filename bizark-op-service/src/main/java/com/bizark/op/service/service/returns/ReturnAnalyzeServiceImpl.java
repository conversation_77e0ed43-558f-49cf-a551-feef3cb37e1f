package com.bizark.op.service.service.returns;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bizark.boss.api.service.TaskCenterService;
import com.bizark.boss.api.vo.dashboard.TaskCenterRequest;
import com.bizark.op.api.cons.StatConstant;
import com.bizark.op.api.enm.TikTokApiEnums;
import com.bizark.op.api.enm.finance.FinanceErrorEnum;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.returns.StatReturnQuery;
import com.bizark.op.api.entity.op.returns.VO.ReturnAnalyzeTendencyVO;
import com.bizark.op.api.entity.op.returns.VO.ReturnAnalyzeVO;
import com.bizark.op.api.entity.op.returns.tiktok.TiktokReturnRefund;
import com.bizark.op.api.entity.op.sale.Products;
import com.bizark.op.api.entity.op.titok.response.ReverseOrderResponseMsg;
import com.bizark.op.api.parameter.util.TikTokUtil;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.amazon.AmazonReturnService;
import com.bizark.op.api.service.returns.ReturnAnalyzeService;
import com.bizark.op.api.service.returns.ReturnInfoService;
import com.bizark.op.api.service.sale.ProductsService;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.*;
import com.bizark.op.service.mapper.returns.ReturnAnalyzeMapper;
import com.bizark.op.service.mapper.ticket.ConfTicketProblemMapper;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * <AUTHOR>
 * @date 2023年9月19日  13:51
 * @description:
 */

@Slf4j
@Service
public class ReturnAnalyzeServiceImpl implements ReturnAnalyzeService {

    @Autowired
    ReturnAnalyzeMapper returnAnalyzeMapper;

    @Autowired
    private ConfTicketProblemMapper confTicketProblemMapper;

    @Autowired
    private TikTokUtil tikTokUtil;

    @Autowired
    ReturnInfoService returnInfoService;

    @Autowired
    private AmazonReturnService amazonReturnService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private TaskCenterService taskCenterService;


    @Value("${home_query_bi_url}")
    private String queryReturnUrl;

    @Autowired
    private ProductsService productsService;

    /**
     * @description: 初版VC退货分析数据
     * @author: Moore
     * @date: 2024/4/16 10:57
     * @param
     * @param contextId
     * @param pageSize
     * @param pageNum
     * @param type
     * @param shopIdList
     * @param returnDateStart
     * @param returnDateEnd
     * @param asinList
     * @param skuList
     * @param operateIdList
     * @return: java.util.List<com.bizark.op.api.entity.op.returns.VO.ReturnAnalyzeVO>
    **/
    @Override
    public List<ReturnAnalyzeVO> getVcReturnAnalyzePageList(Integer contextId, Integer pageSize, Integer pageNum, Integer type, List<Integer> shopIdList, LocalDate returnDateStart, LocalDate returnDateEnd, List<String> asinList, List<String> skuList, List<Integer> operateIdList) {
        PageHelper.startPage(pageNum, pageSize);
        List<ReturnAnalyzeVO> vcReturnAnalyzeVO = returnAnalyzeMapper.getVcReturnAnalyzeVO(contextId, type, shopIdList, returnDateStart, returnDateEnd, asinList, skuList, operateIdList);
        for (ReturnAnalyzeVO returnAnalyzeVO : vcReturnAnalyzeVO) {
            returnAnalyzeVO.setReturnDate(returnDateStart.toString() + " - " + returnDateEnd.toString());
        }
        return vcReturnAnalyzeVO;
    }


    /**
     * @param
     * @param statReturnQuery
     * @description: V2版本VC退货数据分析列表
     * @author: Moore
     * @date: 2024/4/10 12:00
     * @return: java.util.List<com.bizark.op.api.entity.op.returns.VO.ReturnAnalyzeVO>
     **/
    @Override
    public List<ReturnAnalyzeVO> getVcReturnAnalyzePageListNew(StatReturnQuery statReturnQuery) {
        String returnDateStart = statReturnQuery.getReturnDateStart();
        String returnDateEnd = statReturnQuery.getReturnDateEnd();
        Integer type = statReturnQuery.getType();
        String sumStrTime = returnDateStart + "~" + returnDateEnd;         //返回行汇总时间
        //ASIN 模糊查询
        if (!CollectionUtils.isEmpty(statReturnQuery.getAsinList()) && statReturnQuery.getAsinList().size() == 1) {
            statReturnQuery.setAsin(statReturnQuery.getAsinList().get(0));
            statReturnQuery.setAsinList(null);
        }
        //SKU 模糊查询
        if (!CollectionUtils.isEmpty(statReturnQuery.getSkuList()) && statReturnQuery.getSkuList().size() == 1) {
            statReturnQuery.setSku(statReturnQuery.getSkuList().get(0));
            statReturnQuery.setSkuList(null);
        }
        //根据不同维度分组查询
        List<ReturnAnalyzeVO> vcReturnAnalyzeVO = returnAnalyzeMapper.getVcReturnAnalyzeVONew(statReturnQuery);
        if (CollectionUtils.isEmpty(vcReturnAnalyzeVO)) {
            return Collections.emptyList();
        }

        if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())){
            //判断查询区间不得大于60天
            if( StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())&& cn.hutool.core.date.DateUtil.betweenDay(cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateStart()), cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateEnd()), false)>65){
                throw new CustomException("按日查询时间区间不可大于60天");
            }

            //日维度直接排序
            if (StringUtils.isNotEmpty(statReturnQuery.getSidx()) && StringUtils.isNotEmpty(statReturnQuery.getSord())) {
                statReturnQuery.setSqlContent("ORDER BY" + " " + statReturnQuery.getSidx() + " " + statReturnQuery.getSord());
            }
            statReturnQuery.setGroupTimeFlag("Y"); //各维度，增加日维度分组
            this.childVcReturnInfoList(statReturnQuery, vcReturnAnalyzeVO);
            List<ReturnAnalyzeVO> vcReturnAnalyzeVONew = returnAnalyzeMapper.getVcReturnAnalyzeVONew(statReturnQuery);  //查询
            vcReturnAnalyzeVO.forEach(item -> {
                item.setReturnDate(sumStrTime);

                if (type == 0) {  //ASIN
                    item.setChildTimeRetuns(vcReturnAnalyzeVONew.stream().filter(child -> item.getAsin().equals(child.getAsin())).collect(Collectors.toList()));
                }
                if (type == 1) { //SKU
                    item.setChildTimeRetuns(vcReturnAnalyzeVONew.stream().filter(child -> item.getSku().equals(child.getSku())).collect(Collectors.toList()));
                }
                if (type == 2) { //店铺
                    item.setChildTimeRetuns(vcReturnAnalyzeVONew.stream().filter(child -> item.getShopName().equals(child.getShopName())).collect(Collectors.toList()));
                }
                if (type == 5) { //分类
                    item.setChildTimeRetuns(vcReturnAnalyzeVONew.stream().filter(child -> item.getCategoryName().equals(child.getCategoryName())).collect(Collectors.toList()));
                }
            });
            return vcReturnAnalyzeVO;
        }



        List<SpiltDateUtil.Range> rangeList = new ArrayList<>();
        if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) {
            rangeList = SpiltDateUtil.splitToDays(returnDateStart, returnDateEnd);
        } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) {
            rangeList = SpiltDateUtil.splitToWeeks(returnDateStart, returnDateEnd);
        } else if (StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) {
            rangeList = SpiltDateUtil.splitToMonths(returnDateStart, returnDateEnd);
        }

        //月维度
        if (StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())){
            if(cn.hutool.core.date.DateUtil.betweenDay(cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateStart()), cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateEnd()), false)>735){
                throw new CustomException("按月查询时间区间不可大于两年");
            }
            statReturnQuery.setGroupTimeMonthFlag("Y"); //各维度，增加日维度分组

            //月直接排序
            if (StringUtils.isNotEmpty(statReturnQuery.getSidx()) && StringUtils.isNotEmpty(statReturnQuery.getSord())) {
                statReturnQuery.setSqlContent("ORDER BY" + " " + statReturnQuery.getSidx() + " " + statReturnQuery.getSord());
            }

            statReturnQuery.setGroupTimeMonthFlag("Y"); //按月聚合
            this.childVcReturnInfoList(statReturnQuery, vcReturnAnalyzeVO);
            //获取指定维度List数据
            List<ReturnAnalyzeVO> vcReturnAnalyzeVONew = returnAnalyzeMapper.getVcReturnAnalyzeVONew(statReturnQuery);  //查询
            vcReturnAnalyzeVO.forEach(item -> {
                item.setReturnDate(sumStrTime);
                if (type == 0) {  //ASIN
                    item.setChildTimeRetuns(vcReturnAnalyzeVONew.stream().filter(child -> item.getAsin().equals(child.getAsin())).collect(Collectors.toList()));
                }
                if (type == 1) { //SKU
                    item.setChildTimeRetuns(vcReturnAnalyzeVONew.stream().filter(child -> item.getSku().equals(child.getSku())).collect(Collectors.toList()));
                }
                if (type == 2) { //店铺
                    item.setChildTimeRetuns(vcReturnAnalyzeVONew.stream().filter(child -> item.getShopName().equals(child.getShopName())).collect(Collectors.toList()));
                }
                if (type == 5) { //分类
                    item.setChildTimeRetuns(vcReturnAnalyzeVONew.stream().filter(child -> item.getCategoryName().equals(child.getCategoryName())).collect(Collectors.toList()));
                }
            });
            return vcReturnAnalyzeVO;
        }




        //周维度
        statReturnQuery.setGroupTimeFlag("Y");
        this.childVcReturnInfoList(statReturnQuery, vcReturnAnalyzeVO);
        List<ReturnAnalyzeVO> vcReturnAnalyzeVONew = returnAnalyzeMapper.getVcReturnAnalyzeVONew(statReturnQuery);  //查询

        HashMap<Integer, String> cloumFlag = new HashMap<>();
        cloumFlag.put(0, "asin");
        cloumFlag.put(1, "sku");
        cloumFlag.put(2, "shopName");
        cloumFlag.put(5, "categoryName");
        for (ReturnAnalyzeVO returnAnalyzeVO : vcReturnAnalyzeVO) {
            returnAnalyzeVO.setReturnDate(sumStrTime); //设置汇总时间
            //获取要分组的维度字段
            Class<? extends ReturnAnalyzeVO> aClass = returnAnalyzeVO.getClass();
            String commonCloum = "";
            try {
                Field declaredField = aClass.getDeclaredField(cloumFlag.get(statReturnQuery.getType()));
                declaredField.setAccessible(true);
                commonCloum = (String) declaredField.get(returnAnalyzeVO);
            } catch (NoSuchFieldException e) {
                e.printStackTrace();
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
            String commonCloumFlag = commonCloum;

            //对items行操作
            ArrayList<ReturnAnalyzeVO> childReturnList = new ArrayList<>();
            for (SpiltDateUtil.Range range : rangeList) {
                ReturnAnalyzeVO returnAnalyzeVOItem = new ReturnAnalyzeVO();
                if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())){
                    returnAnalyzeVOItem.setReturnDate(DateUtil.convertDateToString(range.getStart(), DatePattern.NORM_DATE_PATTERN) + "~" + DateUtil.convertDateToString(range.getEnd(), DatePattern.NORM_DATE_PATTERN));
                }else if (StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) {
                    returnAnalyzeVOItem.setReturnDate(DateUtil.convertDateToString(range.getStart(), "yyyy-MM"));
                }

                returnAnalyzeVOItem.setOperateName(returnAnalyzeVO.getOperateName()); //运营
                returnAnalyzeVOItem.setAsin(returnAnalyzeVO.getAsin()); //ASIN
                returnAnalyzeVOItem.setSku(returnAnalyzeVO.getSku());//SKU
                returnAnalyzeVOItem.setShopName(returnAnalyzeVO.getShopName());//店铺名
                returnAnalyzeVOItem.setCategoryName(returnAnalyzeVO.getCategoryName());//分类

                List<ReturnAnalyzeVO> groupVcReturnInfo = vcReturnAnalyzeVONew.stream().filter(item -> filterRrturnInfo(item, statReturnQuery.getType(), commonCloumFlag)).filter(item ->
                          DateUtil.compareDate(DateUtil.convertStringToDate(item.getReturnDate(), DatePattern.NORM_DATE_PATTERN), range.getStart()) >= 0
                                && DateUtil.compareDate(DateUtil.convertStringToDate(item.getReturnDate(), DatePattern.NORM_DATE_PATTERN), range.getEnd()) <= 0
                ).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(groupVcReturnInfo)) {
                    Integer returnNumSum = groupVcReturnInfo.stream().mapToInt(ReturnAnalyzeVO::getReturnNum).sum();
                    Integer saleNumSum = groupVcReturnInfo.stream().mapToInt(ReturnAnalyzeVO::getSaleNum).sum();
                    returnAnalyzeVOItem.setReturnNum(returnNumSum);
                    returnAnalyzeVOItem.setSaleNum(saleNumSum);
                    if (saleNumSum != 0) {       //退货率
                        BigDecimal returnRate = new BigDecimal(returnNumSum).divide(new BigDecimal(saleNumSum), 4, ROUND_HALF_UP);
                        returnAnalyzeVOItem.setReturnRate(returnRate);
                    } else {
                        returnAnalyzeVOItem.setReturnRate(BigDecimal.ZERO);
                    }
                } else {
                    returnAnalyzeVOItem.setReturnNum(0);
                    returnAnalyzeVOItem.setSaleNum(0);
                    returnAnalyzeVOItem.setReturnRate(BigDecimal.ZERO);
                }
                //明细行封装
                childReturnList.add(returnAnalyzeVOItem);
            }
            //排序
            this.returnInfoChildNoDaySort(childReturnList, statReturnQuery);
            returnAnalyzeVO.setChildTimeRetuns(childReturnList);
        }
        return vcReturnAnalyzeVO;
    }


    /**
     * @param vcReturnAnalyzeVONew 分页行信息
     * @param statReturnQuery 查询条件
     * @description: VC退货分析子AISN分析
     * @author: Moore
     * @date: 2024/4/24 16:21
     * @return: void
     **/
    public void childVcReturnInfoList(StatReturnQuery statReturnQuery, List<ReturnAnalyzeVO> vcReturnAnalyzeVONew) {
        Integer type = statReturnQuery.getType();
        if (type == 0) {  //ASIN 不会出现null值
            statReturnQuery.setAsinList(vcReturnAnalyzeVONew.stream().map(ReturnAnalyzeVO::getAsin).collect(Collectors.toList()));
        }
        if (type == 1) { //SKU
            List<String> skus= vcReturnAnalyzeVONew.stream().map(ReturnAnalyzeVO::getSku).collect(Collectors.toList());
            List<String> skufilters = skus.stream().filter(item -> "无SKU".equals(item)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(skufilters)){
                statReturnQuery.setSkuFlag("Y");
            }
            statReturnQuery.setSkuList(skus);
        }
        if (type == 2) { //店铺
            List<Integer> shopIds = vcReturnAnalyzeVONew.stream().map(ReturnAnalyzeVO::getShopId).collect(Collectors.toList());
            statReturnQuery.setShopIdList(shopIds);
        }
        if (type == 5) { //分类
            List<String> skus= vcReturnAnalyzeVONew.stream().map(ReturnAnalyzeVO::getCategoryName).collect(Collectors.toList());
            List<String> skufilters = skus.stream().filter(item -> "无分类".equals(item)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(skufilters)){
                statReturnQuery.setCategoryFlag("Y");
            }
            statReturnQuery.setCategoryIdList( vcReturnAnalyzeVONew.stream().filter(item -> item.getCategoryId() != null).map(ReturnAnalyzeVO::getCategoryId).collect(Collectors.toList()));
        }
    }


    /**
     * @param
     * @param statReturnQuery
     * @description: V2版本VC退货数据分析 导出
     * @author: Moore
     * @date: 2024/4/15 18:45
     * @return: java.util.List<com.bizark.op.api.entity.op.returns.VO.ReturnAnalyzeVO>
     **/
    @Override
    public List<ReturnAnalyzeVO> getVcReturnAnalyzePageListNewExport(StatReturnQuery statReturnQuery) {
        String returnDateStart = statReturnQuery.getReturnDateStart();
        String returnDateEnd = statReturnQuery.getReturnDateEnd();
        Integer type = statReturnQuery.getType(); //查詢的type类型



        if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())||StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())){
            statReturnQuery.setGroupTimeFlag("Y"); //日周根据日分组
            if(cn.hutool.core.date.DateUtil.betweenDay(cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateStart()), cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateEnd()), false)>65){
                throw new CustomException("非月维度导出时间区间不可大于60天");
            }
        }else{
            statReturnQuery.setGroupTimeMonthFlag("Y"); //按月group
        }


        //ASIN 模糊
        if (!CollectionUtils.isEmpty(statReturnQuery.getAsinList()) && statReturnQuery.getAsinList().size() == 1) {

            statReturnQuery.setAsin(statReturnQuery.getAsinList().get(0));
            statReturnQuery.setAsinList(null);
        }

        //SKU模糊
        if (!CollectionUtils.isEmpty(statReturnQuery.getSkuList()) && statReturnQuery.getSkuList().size() == 1) {
            statReturnQuery.setSku(statReturnQuery.getSkuList().get(0));
            statReturnQuery.setSkuList(null);
        }




        List<ReturnAnalyzeVO> vcReturnAnalyzeVO = returnAnalyzeMapper.getVcReturnAnalyzeVONew(statReturnQuery);
        if (CollectionUtils.isEmpty(vcReturnAnalyzeVO)) {
            return Collections.emptyList();
        }
        //日维度直接导出
        if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())||StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) {
            //导出计算退货率
            vcReturnAnalyzeVO.forEach(item -> item.setReturnRate(item.getReturnRate().multiply(new BigDecimal("1000000")).setScale(0, ROUND_HALF_UP)));
            return vcReturnAnalyzeVO;
        }


        //日期维度拆分
        List<SpiltDateUtil.Range> rangeList = new ArrayList<>();
        if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) {
            rangeList = SpiltDateUtil.splitToDays(returnDateStart, returnDateEnd);
        } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) {
            rangeList = SpiltDateUtil.splitToWeeks(returnDateStart, returnDateEnd);
        } else if (StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) {
            rangeList = SpiltDateUtil.splitToMonths(returnDateStart, returnDateEnd);
        }

        Map<String, List<ReturnAnalyzeVO>> groupReturnInfoMap = null;

        //根据维度分组，非日维度数据
        //asin
        if (type == 0) {
            groupReturnInfoMap = vcReturnAnalyzeVO.stream().collect(Collectors.groupingBy(ReturnAnalyzeVO::getAsin));
        }
        //SKu
        if (type == 1) {
            groupReturnInfoMap = vcReturnAnalyzeVO.stream().collect(Collectors.groupingBy(ReturnAnalyzeVO::getSku));

        }
        //shopName
        if (type == 2) {
            groupReturnInfoMap = vcReturnAnalyzeVO.stream().collect(Collectors.groupingBy(ReturnAnalyzeVO::getShopName));
        }
        //categoryName
        if (type == 5) {
            groupReturnInfoMap = vcReturnAnalyzeVO.stream().collect(Collectors.groupingBy(ReturnAnalyzeVO::getCategoryName));
        }

        ArrayList<ReturnAnalyzeVO> noDayREturnList = new ArrayList<>();
        for (String groupFlage : groupReturnInfoMap.keySet()) {
            for (SpiltDateUtil.Range range : rangeList) {
                ReturnAnalyzeVO returnAnalyzeVO = new ReturnAnalyzeVO();
                if (type == 0) {
                    returnAnalyzeVO.setAsin(groupFlage);
                }
                if (type == 1) {
                    returnAnalyzeVO.setSku(groupFlage);
                }
                if (type == 2) {
                    returnAnalyzeVO.setShopName(groupFlage);
                }
                if (type == 5) {
                    returnAnalyzeVO.setCategoryName(groupFlage);
                }
                if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())){
                    returnAnalyzeVO.setReturnDate(DateUtil.convertDateToString(range.getStart(), DatePattern.NORM_DATE_PATTERN) + "~" + DateUtil.convertDateToString(range.getEnd(), DatePattern.NORM_DATE_PATTERN));
                }else if (StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) {
                    returnAnalyzeVO.setReturnDate(DateUtil.convertDateToString(range.getStart(), "yyyy-MM"));
                }

                List<ReturnAnalyzeVO> groupVcReturnInfo = vcReturnAnalyzeVO.stream().filter(item ->
                        DateUtil.compareDate(DateUtil.convertStringToDate(item.getReturnDate(), DatePattern.NORM_DATE_PATTERN), range.getStart()) >= 0
                                && DateUtil.compareDate(DateUtil.convertStringToDate(item.getReturnDate(), DatePattern.NORM_DATE_PATTERN), range.getEnd()) <= 0
                ).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(groupVcReturnInfo)) {
                    Integer returnNumSum = groupVcReturnInfo.stream().mapToInt(ReturnAnalyzeVO::getReturnNum).sum();
                    Integer saleNumSum = groupVcReturnInfo.stream().mapToInt(ReturnAnalyzeVO::getSaleNum).sum();
                    returnAnalyzeVO.setReturnNumBig(BigDecimal.valueOf(returnNumSum));
                    returnAnalyzeVO.setSaleNumBig(BigDecimal.valueOf(saleNumSum));
                    if (saleNumSum != 0) {       //退货率
                        BigDecimal returnRate = new BigDecimal(returnNumSum).divide(new BigDecimal(saleNumSum), 6, ROUND_HALF_UP).multiply(new BigDecimal("1000000"));
                        returnAnalyzeVO.setReturnRate(returnRate.setScale(0, ROUND_HALF_UP));
                    } else {
                        returnAnalyzeVO.setReturnRate(BigDecimal.ZERO);
                    }
                } else {
                    returnAnalyzeVO.setReturnNumBig(BigDecimal.ZERO);
                    returnAnalyzeVO.setReturnNumBig(BigDecimal.ZERO);
                    returnAnalyzeVO.setReturnRate(BigDecimal.ZERO);
                }
                noDayREturnList.add(returnAnalyzeVO);
            }
        }
        return noDayREturnList;
    }


    /**
     * @param
     * @param statReturnQuery
     * @description: V2 VC退货分析汇总数据
     * @author: Moore
     * @date: 2024/4/15 11:07
     * @return: com.bizark.op.api.entity.op.returns.VO.ReturnAnalyzeVO
     **/
    @Override
    public ReturnAnalyzeVO getVcReturnAnalyzePageSum(StatReturnQuery statReturnQuery) {


        //ASIN 模糊
        if (!CollectionUtils.isEmpty(statReturnQuery.getAsinList()) && statReturnQuery.getAsinList().size() == 1) {

            statReturnQuery.setAsin(statReturnQuery.getAsinList().get(0));
            statReturnQuery.setAsinList(null);
        }

        //SKU模糊
        if (!CollectionUtils.isEmpty(statReturnQuery.getSkuList()) && statReturnQuery.getSkuList().size() == 1) {
            statReturnQuery.setSku(statReturnQuery.getSkuList().get(0));
            statReturnQuery.setSkuList(null);
        }

        ReturnAnalyzeVO returnAnalyzeVO = new ReturnAnalyzeVO();
        statReturnQuery.setType(null); //无类型概念
        List<ReturnAnalyzeVO> vcReturnAnalyzeVO = returnAnalyzeMapper.getVcReturnAnalyzeVONew(statReturnQuery);
        int returnNumSum = vcReturnAnalyzeVO.stream().mapToInt(ReturnAnalyzeVO::getReturnNum).sum();
        int saleNumSum = vcReturnAnalyzeVO.stream().mapToInt(ReturnAnalyzeVO::getSaleNum).sum();
        returnAnalyzeVO.setReturnNum(returnNumSum);
        returnAnalyzeVO.setSaleNum(saleNumSum);
        if (saleNumSum != 0) {       //退货率
            BigDecimal returnRate = new BigDecimal(returnNumSum).divide(new BigDecimal(saleNumSum), 6, ROUND_HALF_UP);
            returnAnalyzeVO.setReturnRate(returnRate);
        } else {
            returnAnalyzeVO.setReturnRate(BigDecimal.ZERO);
        }
        return returnAnalyzeVO;
    }

    /**
     * @param
     * @param statReturnQuery
     * @description: 退货分析趋势图 VC
     * @author: Moore
     * @date: 2024/4/15 15:26
     * @return: com.bizark.op.api.entity.op.returns.VO.ReturnAnalyzeVO
     **/
    @Override
    public ReturnAnalyzeTendencyVO getVcReturnAnalyzePageTendency(StatReturnQuery statReturnQuery) {

        //ASIN 模糊
        if (!CollectionUtils.isEmpty(statReturnQuery.getAsinList()) && statReturnQuery.getAsinList().size() == 1) {
            statReturnQuery.setAsin(statReturnQuery.getAsinList().get(0));
            statReturnQuery.setAsinList(null);
        }

        //SKU模糊
        if (!CollectionUtils.isEmpty(statReturnQuery.getSkuList()) && statReturnQuery.getSkuList().size() == 1) {
            statReturnQuery.setSku(statReturnQuery.getSkuList().get(0));
            statReturnQuery.setSkuList(null);
        }


        //返回参数对象
        ReturnAnalyzeTendencyVO returnAnalyzeTendencyVO = new ReturnAnalyzeTendencyVO();

        String returnDateStart = statReturnQuery.getReturnDateStart();
        String returnDateEnd = statReturnQuery.getReturnDateEnd();


        List<SpiltDateUtil.Range> rangeList = new ArrayList<>();
        if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) {
            rangeList = SpiltDateUtil.splitToDays(returnDateStart, returnDateEnd);
        } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) {
            rangeList = SpiltDateUtil.splitToWeeks(returnDateStart, returnDateEnd);
        } else if (StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) {
            rangeList = SpiltDateUtil.splitToMonths(returnDateStart, returnDateEnd);
        }


        //按照日期分组（年月日）
        statReturnQuery.setType(-1);
        List<ReturnAnalyzeVO> vcReturnAnalyzeVO = returnAnalyzeMapper.getVcReturnAnalyzeVONew(statReturnQuery);

       List<String> x = new ArrayList<>();
       //退货率
       List<String> returnRate = new ArrayList<>();
       //销量
        List<Integer> saleNumber = new ArrayList<>();


        for (SpiltDateUtil.Range range : rangeList) {
            if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) {

                ReturnAnalyzeVO returnAnalyzeVO = vcReturnAnalyzeVO.stream().filter(item -> item.getReturnDate().equals(DateUtil.convertDateToString(range.getEnd(), DatePattern.NORM_DATE_PATTERN))
                ).findFirst().orElse(new ReturnAnalyzeVO());

                returnRate.add(returnAnalyzeVO.getReturnRate() != null ? returnAnalyzeVO.getReturnRate().toString() : "0");
                x.add(cn.hutool.core.date.DateUtil.format(range.getStart(), DatePattern.NORM_DATE_PATTERN));
                saleNumber.add(returnAnalyzeVO.getSaleNum() == null ? 0 : returnAnalyzeVO.getSaleNum());
            } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType()) || StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) {
                List<ReturnAnalyzeVO> groupVcReturnInfo = vcReturnAnalyzeVO.stream().filter(item ->
                        DateUtil.compareDate(DateUtil.convertStringToDate(item.getReturnDate(), DatePattern.NORM_DATE_PATTERN), range.getStart()) >= 0
                                && DateUtil.compareDate(DateUtil.convertStringToDate(item.getReturnDate(), DatePattern.NORM_DATE_PATTERN), range.getEnd()) <= 0
                ).collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(groupVcReturnInfo)) {
                    Integer returnNumSum = groupVcReturnInfo.stream().mapToInt(ReturnAnalyzeVO::getReturnNum).sum();
                    Integer saleNumSum = groupVcReturnInfo.stream().mapToInt(ReturnAnalyzeVO::getSaleNum).sum();
                    if (saleNumSum != 0) {
                        BigDecimal returnRateFlag = new BigDecimal(returnNumSum).divide(new BigDecimal(saleNumSum), 4, ROUND_HALF_UP);
                        returnRate.add(returnRateFlag.toString());  //退货率
                        saleNumber.add(saleNumSum); //销量
                    } else {
                        returnRate.add("0");
                        saleNumber.add(0);
                    }
                } else {
                    returnRate.add("0");
                    saleNumber.add(0);
                }
                x.add(StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType()) ? DateUtil.convertDateToString(range.getStart(), DatePattern.NORM_DATE_PATTERN) + "~" + DateUtil.convertDateToString(range.getEnd(), DatePattern.NORM_DATE_PATTERN) : DateUtil.convertDateToString(range.getStart(), "yyyy-MM"));
            }
        }
        returnAnalyzeTendencyVO.setX(x); //x轴
        returnAnalyzeTendencyVO.setReturnRateList(returnRate.size() == 0 ? new ArrayList<>(Collections.nCopies(x.size(), "0")) : returnRate); //退货率PPM
        returnAnalyzeTendencyVO.setQuantityList(saleNumber.size() == 0 ? new ArrayList<Integer>(Collections.nCopies(x.size(), 0)) : saleNumber);//销量
        return returnAnalyzeTendencyVO;
    }


    /**
     * @param
     * @param statReturnQuery 查询条件
     * @description:VC 退货分析趋势图导出
     * @author: Moore
     * @date: 2024/4/15 17:40
     * @return: java.util.List<com.bizark.op.api.entity.op.returns.VO.ReturnAnalyzeVO>
     **/
    @Override
    public List<ReturnAnalyzeVO> getVcReturnAnalyzePageTendencyExport(StatReturnQuery statReturnQuery) {
        String returnDateStart = statReturnQuery.getReturnDateStart();
        String returnDateEnd = statReturnQuery.getReturnDateEnd();

        statReturnQuery.setType(-1); //日维度，只根据时间分组
        List<SpiltDateUtil.Range> rangeList = new ArrayList<>();
        if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) {
            rangeList = SpiltDateUtil.splitToDays(returnDateStart, returnDateEnd);
        } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) {
            rangeList = SpiltDateUtil.splitToWeeks(returnDateStart, returnDateEnd);
        } else if (StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) {
            rangeList = SpiltDateUtil.splitToMonths(returnDateStart, returnDateEnd);
        }


        //ASIN 模糊
        if (!CollectionUtils.isEmpty(statReturnQuery.getAsinList()) && statReturnQuery.getAsinList().size() == 1) {

            statReturnQuery.setAsin(statReturnQuery.getAsinList().get(0));
            statReturnQuery.setAsinList(null);
        }
        //SKU模糊
        if (!CollectionUtils.isEmpty(statReturnQuery.getSkuList()) && statReturnQuery.getSkuList().size() == 1) {
            statReturnQuery.setSku(statReturnQuery.getSkuList().get(0));
            statReturnQuery.setSkuList(null);
        }
        List<ReturnAnalyzeVO> vcReturnAnalyzeVO = returnAnalyzeMapper.getVcReturnAnalyzeVONew(statReturnQuery);

        if (!StringUtils.isEmpty(statReturnQuery.getAsin())){
            statReturnQuery.setAsinList(Arrays.asList(statReturnQuery.getAsin()));
        }

        if (!StringUtils.isEmpty(statReturnQuery.getSku())) {
            statReturnQuery.setSkuList(Arrays.asList(statReturnQuery.getSku()));
        }




        //日维度直接导出
        if (!CollectionUtils.isEmpty(vcReturnAnalyzeVO) && StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) {
            vcReturnAnalyzeVO.stream().forEach(item -> {
                        item.setShopName(!CollectionUtils.isEmpty(statReturnQuery.getShopNameList()) ? statReturnQuery.getShopNameList().stream().collect(Collectors.joining(",")) : null);
                        item.setAsin(!CollectionUtils.isEmpty(statReturnQuery.getAsinList()) ? statReturnQuery.getAsinList().stream().collect(Collectors.joining(",")) : null);
                        item.setSku(!CollectionUtils.isEmpty(statReturnQuery.getSkuList()) ? statReturnQuery.getSkuList().stream().collect(Collectors.joining(",")) : null);
                        item.setCategoryName(!CollectionUtils.isEmpty(statReturnQuery.getCategoryNameList()) ? statReturnQuery.getCategoryNameList().stream().collect(Collectors.joining(",")) : null);
                        item.setOperateName(!CollectionUtils.isEmpty(statReturnQuery.getOperateNameList()) ? statReturnQuery.getOperateNameList().stream().collect(Collectors.joining(",")) : null);
                        item.setReturnRate(item.getReturnRate().multiply(new BigDecimal("1000000")).setScale(0, ROUND_HALF_UP));
                    }
            );
            return vcReturnAnalyzeVO;
        }


        //非日维度折线图导出
        ArrayList<ReturnAnalyzeVO> exportList = new ArrayList<>();
        for (SpiltDateUtil.Range range : rangeList) {
            ReturnAnalyzeVO returnAnalyzeVO = new ReturnAnalyzeVO();

            returnAnalyzeVO.setReturnDate(StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType()) ? DateUtil.convertDateToString(range.getStart(), DatePattern.NORM_DATE_PATTERN) + "~" + DateUtil.convertDateToString(range.getEnd(), DatePattern.NORM_DATE_PATTERN) : DateUtil.convertDateToString(range.getStart(), "yyyy-MM"));

            List<ReturnAnalyzeVO> groupVcReturnInfo = vcReturnAnalyzeVO.stream().filter(item ->
                    DateUtil.compareDate(DateUtil.convertStringToDate(item.getReturnDate(), DatePattern.NORM_DATE_PATTERN), range.getStart()) >= 0
                            && DateUtil.compareDate(DateUtil.convertStringToDate(item.getReturnDate(), DatePattern.NORM_DATE_PATTERN), range.getEnd()) <= 0
            ).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(groupVcReturnInfo)) {
                Integer returnNumSum = groupVcReturnInfo.stream().mapToInt(ReturnAnalyzeVO::getReturnNum).sum();
                Integer saleNumSum = groupVcReturnInfo.stream().mapToInt(ReturnAnalyzeVO::getSaleNum).sum();
                if (saleNumSum != 0) {       //导出退货率，重新计算
                    BigDecimal returnRateFlag = new BigDecimal(returnNumSum).divide(new BigDecimal(saleNumSum), 6, ROUND_HALF_UP).multiply(new BigDecimal("1000000"));
                    returnAnalyzeVO.setReturnRate(returnRateFlag.setScale(0, ROUND_HALF_UP));
                } else {
                    returnAnalyzeVO.setReturnRate(BigDecimal.ZERO);
                }
            } else {
                returnAnalyzeVO.setReturnRate(BigDecimal.ZERO);
            }
            exportList.add(returnAnalyzeVO);
        }

        //查询条件设置
        exportList.stream().forEach(item -> {
                    item.setShopName(!CollectionUtils.isEmpty(statReturnQuery.getShopNameList()) ? statReturnQuery.getShopNameList().stream().collect(Collectors.joining(",")) : null);
                    item.setAsin(!CollectionUtils.isEmpty(statReturnQuery.getAsinList()) ? statReturnQuery.getAsinList().stream().collect(Collectors.joining(",")) : null);
                    item.setSku(!CollectionUtils.isEmpty(statReturnQuery.getSkuList()) ? statReturnQuery.getSkuList().stream().collect(Collectors.joining(",")) : null);
                    item.setCategoryName(!CollectionUtils.isEmpty(statReturnQuery.getCategoryNameList()) ? statReturnQuery.getCategoryNameList().stream().collect(Collectors.joining(",")) : null);
                    item.setOperateName(!CollectionUtils.isEmpty(statReturnQuery.getOperateNameList()) ? statReturnQuery.getOperateNameList().stream().collect(Collectors.joining(",")) : null);
                }
        );
        return exportList;
    }


    /**
     * @param
     * @param returnAnalyzeVO
     * @param type
     * @param commonFlag
     * @description: VC 退货分析过滤条件
     * @author: Moore
     * @date: 2024/4/11 14:23
     * @return: boolean
     **/
    private static boolean filterRrturnInfo(ReturnAnalyzeVO returnAnalyzeVO, Integer type, String commonFlag) {
        //TODO  验证各字段是否为空，空的话，进行空校验
        if (type == 0 && returnAnalyzeVO.getAsin().equals(commonFlag)) { //ASIN
            return true;
        }
        if (type == 1 && returnAnalyzeVO.getSku().equals(commonFlag)) { //SKU
            return true;
        }
        if (type == 2 && returnAnalyzeVO.getShopName().equals(commonFlag)) { //店铺
            return true;
        }
        if (type == 5 && returnAnalyzeVO.getCategoryName().equals(commonFlag)) { //品类
            return true;
        }
        return false;
    }

    /**
     * @param statReturnQuery Sidx  排序字段 ，Sord 排序方式
     * @param childReturnList
     * @description: 对非日退货明细信息排序
     * @author: Moore
     * @date: 2024/4/12 18:09
     * @return: void
     **/
    public List<ReturnAnalyzeVO> returnInfoChildNoDaySort(List<ReturnAnalyzeVO> childReturnList, StatReturnQuery statReturnQuery) {
        List<ReturnAnalyzeVO> flagReturnList = null;

        String sidx = statReturnQuery.getSidx();
        String sord = statReturnQuery.getSord();
        //无数据，无排序字段，不进行排序
        if (CollectionUtils.isEmpty(childReturnList) || StringUtils.isEmpty(statReturnQuery.getSidx()) || StringUtils.isEmpty(statReturnQuery.getSord())) {
            return childReturnList;
        }

        //退货数量
        if ("return_num".equals(sidx)) {
            if ("desc".equals(sord)) {
                flagReturnList = childReturnList.stream()
                        .sorted(Comparator.comparingInt(ReturnAnalyzeVO::getReturnNum).reversed())
                        .collect(Collectors.toList());
            } else {
                List<ReturnAnalyzeVO> collect = childReturnList.stream()
                        .sorted(Comparator.comparingInt(ReturnAnalyzeVO::getReturnNum))
                        .collect(Collectors.toList());
            }
        }

        //销售数量
        if ("sale_num".equals(sidx)) {
            if ("desc".equals(sord)) {
                flagReturnList = childReturnList.stream()
                        .sorted(Comparator.comparingInt(ReturnAnalyzeVO::getSaleNum).reversed())
                        .collect(Collectors.toList());
            } else {
                flagReturnList = childReturnList.stream()
                        .sorted(Comparator.comparingInt(ReturnAnalyzeVO::getSaleNum))
                        .collect(Collectors.toList());
            }
        }

        //退货率
        if ("return_rate".equals(sidx)) {
            if ("desc".equals(sord)) {
                flagReturnList = childReturnList.stream()
                        .sorted(Comparator.comparing(ReturnAnalyzeVO::getReturnRate).reversed())
                        .collect(Collectors.toList());
            } else {
                flagReturnList = childReturnList.stream()
                        .sorted(Comparator.comparing(ReturnAnalyzeVO::getReturnRate))
                        .collect(Collectors.toList());
            }
        }
        return flagReturnList;
    }


    @Override
    public ArrayList<Map<String, Object>> returnAnalyzeVcExcelExport(Integer contextId, Integer type, List<Integer> shopIdList, LocalDate returnDateStart, LocalDate returnDateEnd, List<String> asinList, List<String> skuList, List<Integer> operateIdList) throws IllegalAccessException {
        List<ReturnAnalyzeVO> returnAnalyzeVOList = returnAnalyzeMapper.getVcReturnAnalyzeVO(contextId, type, shopIdList, returnDateStart, returnDateEnd, asinList, skuList, operateIdList);
        if (CollectionUtil.isNotEmpty(returnAnalyzeVOList) && returnAnalyzeVOList.size() > 50000) {
            return null;
        }
        for (ReturnAnalyzeVO returnAnalyzeVO : returnAnalyzeVOList) {
            returnAnalyzeVO.setReturnDate(returnDateStart.toString() + " - " + returnDateEnd.toString());
        }
        ArrayList<Map<String, Object>> rows = CollUtil.newArrayList();
        if (CollectionUtil.isEmpty(returnAnalyzeVOList)) {
            Map<String, Object> row = new LinkedHashMap<>();
            row.put("退货日期", null);
            row.put("ASIN", null);
            row.put("运营", null);
            row.put("退货数量", null);
            row.put("销售数量", null);
            row.put("退货率", null);
            rows.add(row);
            return rows;
        }
        returnAnalyzeVOList.stream().forEach(returnAnalyzeVO -> {
            Map<String, Object> row = new LinkedHashMap<>();
            row.put("退货日期", returnAnalyzeVO.getReturnDate());
            if (null != type && type == 0) {
                row.put("ASIN", returnAnalyzeVO.getAsin());
                row.put("运营", returnAnalyzeVO.getOperateName());
            }
            if (null != type && type == 1) {
                row.put("SKU", returnAnalyzeVO.getSku());
            }
            if (null != type && type == 2) {
                row.put("店铺", returnAnalyzeVO.getShopName());
            }
            row.put("退货数量", returnAnalyzeVO.getReturnNum());
            row.put("销售数量", returnAnalyzeVO.getSaleNum());
            if (null != returnAnalyzeVO.getReturnRate()) {
                row.put("退货率", returnAnalyzeVO.getReturnRate().multiply(new BigDecimal("100")) + "%");
            } else {
                row.put("退货率", returnAnalyzeVO.getReturnRate());
            }
            rows.add(row);
        });
        return rows;
    }

    @Override
    public List<ReturnAnalyzeVO> getB2CReturnAnalyzePageList(Integer contextId, Integer pageSize, Integer pageNum, Integer type, List<Integer> shopIdList, String returnDateStart, String returnDateEnd, String orderDateStart, String orderDateEnd, List<String> asinList, List<String> skuList, List<Integer> operateIdList, List<String> channelList, List<Integer> problemIdList) {
        List<ReturnAnalyzeVO> b2CReturnAnalyzeList = returnAnalyzeMapper.getB2CReturnAnalyze(contextId, type, shopIdList, returnDateStart, returnDateEnd, orderDateStart, orderDateEnd, asinList, skuList, operateIdList, channelList, problemIdList);
        if (StringUtils.isNotEmpty(returnDateStart) && StringUtils.isNotEmpty(returnDateEnd)) {
            for (ReturnAnalyzeVO returnAnalyzeVO : b2CReturnAnalyzeList) {
                returnAnalyzeVO.setReturnDate(returnDateStart + " - " + returnDateEnd);
            }
        } else if (StringUtils.isNotEmpty(orderDateStart) && StringUtils.isNotEmpty(orderDateEnd)) {
            for (ReturnAnalyzeVO returnAnalyzeVO : b2CReturnAnalyzeList) {
                returnAnalyzeVO.setReturnDate(orderDateStart + " - " + orderDateEnd);
            }
        }
        String searchDateStart = returnDateStart;
        String searchDateEnd = returnDateEnd;
        if (StringUtils.isEmpty(searchDateStart) && StringUtils.isEmpty(searchDateEnd)) {
            searchDateStart = orderDateStart;
            searchDateEnd = orderDateEnd;
        }

        //时间转化由于订单item里面的时间是utc时间。所以进行转化
        String searchDateStartDate = DateUtil.convertDateToString(DateUtil.convertStringToDate(searchDateStart));
        String searchDateEndDate = DateUtil.convertDateToString(DateUtil.convertStringToDate(searchDateEnd));
        String searchDateStartDateString = searchDateStartDate + " 00:00:00";
        String searchDateEndDateString = searchDateEndDate + " 23:59:59";
        //美西时间转UTC时间
        searchDateStartDateString = DateUtil.pacificTimeToUtcTime(searchDateStartDateString);
        searchDateEndDateString = DateUtil.pacificTimeToUtcTime(searchDateEndDateString);
        List<String> queryStrings = null;
        List<Integer> queryIntegers = null;
        if (StringUtils.isNotEmpty(searchDateStart) && StringUtils.isNotEmpty(searchDateEnd)) {
            if (CollectionUtil.isNotEmpty(b2CReturnAnalyzeList)) {
                if (type.equals(0) || type.equals(4)) {
                    queryStrings = b2CReturnAnalyzeList.stream().map(i -> i.getAsin()).collect(Collectors.toList());
                } else if (type.equals(1)) {
                    queryStrings = b2CReturnAnalyzeList.stream().map(i -> i.getSku()).collect(Collectors.toList());
                } else if (type.equals(2)) {
                    queryIntegers = b2CReturnAnalyzeList.stream().map(i -> i.getShopId()).collect(Collectors.toList());
                } else if (type.equals(3)) {
                    queryStrings = b2CReturnAnalyzeList.stream().map(i -> i.getChannel()).collect(Collectors.toList());
                }
            } else {
                return b2CReturnAnalyzeList;
            }
            if (CollectionUtil.isEmpty(queryStrings) && !type.equals(2)) {
                return b2CReturnAnalyzeList;
            }
            if (CollectionUtil.isEmpty(queryIntegers) && type.equals(2)) {
                return b2CReturnAnalyzeList;
            }

            switch (type) {
                case 0:
                    List<ReturnAnalyzeVO> quantityByAsin = null; //returnAnalyzeMapper.getQuantityByParam(searchDateStartDateString, searchDateEndDateString, queryStrings, null, null, null, channelList);
                    if (CollectionUtil.isNotEmpty(quantityByAsin)) {
                        b2CReturnAnalyzeList.stream().forEach(i -> {
                            quantityByAsin.stream().forEach(j -> {
                                        if (i.getAsin().equals(j.getAsin())) {
                                            i.setSaleNum(j.getQuantity());
                                            if (j.getQuantity() == null || j.getQuantity().intValue() == 0) {
                                                i.setReturnRate(new BigDecimal(0));
                                            } else {
                                                BigDecimal returnRate = new BigDecimal(i.getReturnNum()).divide(new BigDecimal(j.getQuantity()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                                                i.setReturnRate(returnRate);
                                            }
                                        }
                                    }
                            );
                        });
                    }
                    break;
                case 1:
                    List<ReturnAnalyzeVO> quantityBySku = null; //returnAnalyzeMapper.getQuantityByParam(searchDateStartDateString, searchDateEndDateString, null, queryStrings, null, null, channelList);
                    if (CollectionUtil.isNotEmpty(quantityBySku)) {
                        b2CReturnAnalyzeList.stream().forEach(i -> {
                            quantityBySku.stream().forEach(j -> {
                                        if (i.getSku().equals(j.getSku())) {
                                            i.setSaleNum(j.getQuantity());
                                            if (j.getQuantity() == null || j.getQuantity().intValue() == 0) {
                                                i.setReturnRate(new BigDecimal(0));
                                            } else {
                                                BigDecimal returnRate = new BigDecimal(i.getReturnNum()).divide(new BigDecimal(j.getQuantity()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                                                i.setReturnRate(returnRate.setScale(2, ROUND_HALF_UP));
                                            }
                                        }
                                    }
                            );
                        });
                    }
                    break;
                case 2:
                    List<ReturnAnalyzeVO> quantityByShop = null; //returnAnalyzeMapper.getQuantityByParam(searchDateStartDateString, searchDateEndDateString, null, null, queryIntegers, null, channelList);
                    if (CollectionUtil.isNotEmpty(quantityByShop)) {
                        b2CReturnAnalyzeList.stream().forEach(i -> {
                            quantityByShop.stream().forEach(j -> {
                                        if (i.getShopId().equals(j.getShopId())) {
                                            i.setSaleNum(j.getQuantity());
                                            if (j.getQuantity() == null || j.getQuantity().intValue() == 0) {
                                                i.setReturnRate(new BigDecimal(0));
                                            } else {
                                                BigDecimal returnRate = new BigDecimal(i.getReturnNum()).divide(new BigDecimal(j.getQuantity()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                                                i.setReturnRate(returnRate.setScale(2, ROUND_HALF_UP));
                                            }
                                        }
                                    }
                            );
                        });
                    }
                    break;
                case 3:
                    List<ReturnAnalyzeVO> quantityByChannel = null; //returnAnalyzeMapper.getQuantityByParam(searchDateStartDateString, searchDateEndDateString, null, null, null, queryStrings, channelList);
                    if (CollectionUtil.isNotEmpty(quantityByChannel)) {
                        b2CReturnAnalyzeList.stream().forEach(i -> {
                            quantityByChannel.stream().forEach(j -> {
                                        if (i.getChannel().equals(j.getChannel())) {
                                            i.setSaleNum(j.getQuantity());
                                            if (j.getQuantity() == null || j.getQuantity().intValue() == 0) {
                                                i.setReturnRate(new BigDecimal(0));
                                            } else {
                                                BigDecimal returnRate = new BigDecimal(i.getReturnNum()).divide(new BigDecimal(j.getQuantity()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                                                i.setReturnRate(returnRate.setScale(2, ROUND_HALF_UP));
                                            }
                                        }
                                    }
                            );
                        });
                    }
                    break;
                case 4:
                    List<ReturnAnalyzeVO> quantity = null; //returnAnalyzeMapper.getQuantityByParam(searchDateStartDateString, searchDateEndDateString, null, null, null, null, channelList);
                    ReturnAnalyzeVO returnAnalyzeVO;
                    if (CollectionUtil.isNotEmpty(quantity)) {
                        returnAnalyzeVO = quantity.get(0);
                    } else {
                        returnAnalyzeVO = null;
                    }
                    if (returnAnalyzeVO != null) {
                        b2CReturnAnalyzeList.stream().forEach(i -> {
                            i.setSaleNum(returnAnalyzeVO.getQuantity());
                            if (returnAnalyzeVO.getQuantity() == null || returnAnalyzeVO.getQuantity().intValue() == 0) {
                                i.setReturnRate(new BigDecimal(0));
                            } else {
                                BigDecimal returnRate = new BigDecimal(i.getReturnNum()).divide(new BigDecimal(returnAnalyzeVO.getQuantity()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                                i.setReturnRate(returnRate.setScale(2, ROUND_HALF_UP));
                            }
                        });
                    }
                    break;
            }
        }
        return b2CReturnAnalyzeList;
    }

    @Override
    public ArrayList<Map<String, Object>> returnAnalyzeB2CExcelExport(Integer contextId, Integer type, List<Integer> shopIdList, String returnDateStart, String returnDateEnd, String orderDateStart, String orderDateEnd, List<String> asinList, List<String> skuList, List<Integer> operateIdList, List<String> channelList, List<Integer> problemIdList) throws IllegalAccessException {
        List<ReturnAnalyzeVO> b2CReturnAnalyzeList = this.getB2CReturnAnalyzePageList(contextId, 500000, 1, type, shopIdList, returnDateStart, returnDateEnd, orderDateStart, orderDateEnd, asinList, skuList, operateIdList, channelList, problemIdList);
        if (CollectionUtil.isNotEmpty(b2CReturnAnalyzeList) && b2CReturnAnalyzeList.size() > 50000) {
            return null;
        }
        if (null != returnDateStart && null != returnDateEnd) {
            for (ReturnAnalyzeVO returnAnalyzeVO : b2CReturnAnalyzeList) {
                returnAnalyzeVO.setReturnDate(returnDateStart.toString() + " - " + returnDateEnd.toString());
            }
        }
        ArrayList<Map<String, Object>> rows = CollUtil.newArrayList();
        if (CollectionUtil.isEmpty(b2CReturnAnalyzeList)) {
            Map<String, Object> row = new LinkedHashMap<>();
            row.put("退货日期", null);
            row.put("ASIN", null);
            row.put("运营", null);
            row.put("退货数量", null);
            row.put("销售数量", null);
            row.put("销售数量", null);
            row.put("退货率", null);
            row.put("收货率", null);
            rows.add(row);
            return rows;
        }
        b2CReturnAnalyzeList.stream().forEach(returnAnalyzeVO -> {
            Map<String, Object> row = new LinkedHashMap<>();
            row.put("退货日期", returnAnalyzeVO.getReturnDate());
            if (null != type && type == 0) {
                row.put("ASIN", returnAnalyzeVO.getAsin());
                row.put("运营", returnAnalyzeVO.getOperateName());
            }
            if (null != type && type == 1) {
                row.put("SKU", returnAnalyzeVO.getSku());
            }
            if (null != type && type == 2) {
                row.put("店铺", returnAnalyzeVO.getShopName());
            }
            if (null != type && type == 3) {
                row.put("渠道", returnAnalyzeVO.getChannel());
            }
            if (null != type && type == 4) {
                row.put("退货原因", returnAnalyzeVO.getReturnReasonCategoryName());
            }
            row.put("退货数量", returnAnalyzeVO.getReturnNum());
            row.put("销售数量", returnAnalyzeVO.getSaleNum());
            if (null != returnAnalyzeVO.getReturnRate()) {
                row.put("退货率", returnAnalyzeVO.getReturnRate().multiply(new BigDecimal("100")) + "%");
            } else {
                row.put("退货率", returnAnalyzeVO.getReturnRate());
            }
            row.put("收货数量", returnAnalyzeVO.getReceiptNum());
            if (null != returnAnalyzeVO.getReceiptRate()) {
                row.put("收货率", returnAnalyzeVO.getReceiptRate().multiply(new BigDecimal("100")) + "%");
            } else {
                row.put("收货率", returnAnalyzeVO.getReceiptRate());
            }
            rows.add(row);
        });
        return rows;
    }

    /**
     * Description: 退货状态下拉框
     *
     * @param contextId
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/3/20
     */
    @Override
    public List<String> getReturnStatusDict(Integer contextId, String returnStatus) {
        return returnAnalyzeMapper.getReturnStatusDict(contextId, returnStatus);
    }

    /**
     * Description:
     *
     * @param contextId
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/2
     */
    @Override
    public void syncTkReturnInfo(Integer contextId, Integer shopId, Long startTime, Long endTime) {
        //todo 的一种判断用orderid调用 这个展示不能跑，先要备份数据
        //第一步查出所有的退货信息的orderId
//        QueryWrapper<ReturnInfoEntity> queryWrapper = new QueryWrapper();
//        queryWrapper.select("distinct order_no,shop_id ");
//        queryWrapper.eq("organization_id",contextId);
//        queryWrapper.eq("shop_id",shopId);
//        List<ReturnInfoEntity> returnInfoEntityList = returnInfoService.list(queryWrapper);
        // todo 后面根据店铺去分组
        //获取店铺map
//        Map<Integer, String> accountMap = new HashMap<>();
//        if(CollectionUtil.isNotEmpty(returnInfoEntityList)) {
//            Set<Integer> shopIds = returnInfoEntityList.stream().map(i -> i.getShopId()).collect(Collectors.toSet());
//            QueryWrapper<Account> accountWrapper = new QueryWrapper();
//            accountWrapper.select("distinct id,flag");
//            accountWrapper.in("id",shopIds);
//            accountWrapper.eq("org_id",contextId);
//            List<Account>  accountList = accountService.list(accountWrapper);
//            if(CollectionUtil.isNotEmpty(accountList)) {
//                 accountMap = accountList.stream().collect(Collectors.toMap(Account::getId, Account::getFlag));
//            }
//        }
//        for (ReturnInfoEntity returnInfoEntity : returnInfoEntityList) {
//            this.callTkReturnInfo(returnInfoEntity,null,accountMap,shopId,startTime,endTime);
//        }
        Map<Integer, String> accountMap = new HashMap<>();
        QueryWrapper<Account> accountWrapper = new QueryWrapper();
        accountWrapper.select("distinct id,flag");
        accountWrapper.eq("id", shopId);
        List<Account> accountList = accountService.list(accountWrapper);
        if (CollectionUtil.isNotEmpty(accountList)) {
            accountMap = accountList.stream().collect(Collectors.toMap(Account::getId, Account::getFlag));
        }
        this.callTkReturnInfo(null, accountMap, shopId, startTime, endTime);

    }

    public void callTkReturnInfo(String pageToken, Map<Integer, String> accountMap, Integer shopId, Long startTime, Long endTime) {
        //todo 拼接查询参数
        Map<String, Object> reqMap = new HashMap<>();
        if (startTime != null) {
            reqMap.put("create_time_ge", startTime);
        }
        if (endTime != null) {
            reqMap.put("create_time_lt", endTime);
        }
        Map<String, Object> extendMap = new HashMap<>();
        extendMap.put("page_size", 50);
        if (StringUtils.isNotEmpty(pageToken)) {
            extendMap.put("page_token", pageToken);
        }
        String url = TikTokApiEnums.GET_REVERSE_ORDER_LIST_V2_NEW.getUrl();
        String path = TikTokApiEnums.GET_REVERSE_ORDER_LIST_V2_NEW.getPath();
        ReverseOrderResponseMsg reverseOrderResponseMsg = tikTokUtil.postTikTokShopV2(JSONObject.toJSONString(reqMap), url, path, ReverseOrderResponseMsg.class, shopId.longValue(), extendMap);
        if (reverseOrderResponseMsg != null && new Integer(0).equals(reverseOrderResponseMsg.getCode())) {
            if (reverseOrderResponseMsg.getData() != null) {
                List<TiktokReturnRefund> returnRefunds = reverseOrderResponseMsg.getData().getReturnOrdersList();
                if (CollectionUtil.isNotEmpty(returnRefunds)) {
                    //todo 通过shopid 查找channelFlag
                    returnRefunds.stream().forEach(i ->
                            {
                                i.setChannelId(accountMap.get(shopId));
                                Long createTime = i.getTkCreateTime() * 1000;
                                Long updateTime = i.getTkUpdateTime() * 1000;
                                i.setCreateTime(new Date(createTime));
                                i.setUpdateTime(new Date(updateTime));
                                if (i.getTikTokRefundAmount() != null) {
                                    i.setCurrency(i.getTikTokRefundAmount().getCurrency());
                                    i.setRefundShippingFee(i.getTikTokRefundAmount().getRefundShippingFee());
                                    i.setRefundSubtotal(i.getTikTokRefundAmount().getRefundSubtotal());
                                    i.setRefundTax(i.getTikTokRefundAmount().getRefundTax());
                                    i.setRefundTotal(i.getTikTokRefundAmount().getRefundTotal());
                                    i.setRetailDeliveryFee(i.getTikTokRefundAmount().getRetailDeliveryFee());
                                }
                                if (CollectionUtil.isNotEmpty(i.getReturnRefundItemList())) {
                                    i.getReturnRefundItemList().stream().forEach(j ->
                                    {
                                        j.setChannelId(accountMap.get(shopId));
                                        j.setOrderId(i.getOrderId());
                                        j.setReturnId(i.getReturnId());
                                        if (j.getTikTokRefundAmount() != null) {
                                            j.setCurrency(j.getTikTokRefundAmount().getCurrency());
                                            j.setRefundShippingFee(j.getTikTokRefundAmount().getRefundShippingFee());
                                            j.setRefundSubtotal(j.getTikTokRefundAmount().getRefundSubtotal());
                                            j.setRefundTax(j.getTikTokRefundAmount().getRefundTax());
                                            j.setRefundTotal(j.getTikTokRefundAmount().getRefundTotal());
                                            j.setRetailDeliveryFee(j.getTikTokRefundAmount().getRetailDeliveryFee());
                                        }
                                    });
                                }
                            }
                    );
                    //todo 调用listener相关的方法同步退货信息
                    amazonReturnService.tiktokReturnDataDispose(reverseOrderResponseMsg.getData().getReturnOrdersList());
                }
                //递归调用
                if (StringUtils.isNotEmpty(reverseOrderResponseMsg.getData().nextPageToken)) {
                    this.callTkReturnInfo(reverseOrderResponseMsg.getData().nextPageToken, accountMap, shopId, startTime, endTime);
                }
            }
        }
    }


    /**
     * Description: B2C退货分析折线图
     * @param statReturnQuery
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/15
     */
    @Override
    public JSONObject getB2CReturnAnalyzeLineChart(StatReturnQuery statReturnQuery) {
        //ASIN 模糊
        if (!CollectionUtils.isEmpty(statReturnQuery.getAsinList()) && statReturnQuery.getAsinList().size() == 1) {
            statReturnQuery.setAsin(statReturnQuery.getAsinList().get(0));
            statReturnQuery.setAsinList(null);
        }

        //SKU模糊
        if (!CollectionUtils.isEmpty(statReturnQuery.getSkuList()) && statReturnQuery.getSkuList().size() == 1) {
            statReturnQuery.setSku(statReturnQuery.getSkuList().get(0));
            statReturnQuery.setSkuList(null);
        }
        JSONObject result = new JSONObject();
        JSONObject data = new JSONObject();
        List<SpiltDateUtil.Range> rangeList = new ArrayList<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat monthSimpleDateFormat = new SimpleDateFormat("yyyy-MM");
        DateTimeFormatter dayDateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        List<ReturnAnalyzeVO> returnInfoEntityList = returnAnalyzeMapper.getB2CReturnAnalyzeLineChart(statReturnQuery);
        if(CollectionUtil.isEmpty(returnInfoEntityList)) {
            return result;
        }
        //调用外部接口
        List<ReturnAnalyzeVO> returnAnalyzeVOList = new ArrayList<>();
        if(StringUtils.isNotEmpty(statReturnQuery.getReturnDateStart()) && StringUtils.isNotEmpty(statReturnQuery.getReturnDateEnd()) && StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) {
            statReturnQuery.setAvgFlag("Y");
        }else {
            statReturnQuery.setAvgFlag("N");
        }
        //设置查询条件
        if(StringUtil.isNotEmpty(statReturnQuery.getAsin())) {
            List<String> asinOne = new ArrayList<>();
            asinOne.add(statReturnQuery.getAsin());
            statReturnQuery.setAsinList(asinOne);
        }
        if(StringUtil.isNotEmpty(statReturnQuery.getSku())) {
            List<String> skuOne = new ArrayList<>();
            skuOne.add(statReturnQuery.getSku());
            statReturnQuery.setSkuList(skuOne);
        }

        JSONObject jsonObject = this.sendReturnInfoLineChartMessage(statReturnQuery);
        JSONArray jsonArray = jsonObject.getJSONArray("data");
         if(jsonArray != null && jsonArray.size() > 0) {
            for(Object js : jsonArray) {
                ReturnAnalyzeVO returnAnalyzeVO = new ReturnAnalyzeVO();
                if(StringUtils.isNotEmpty(statReturnQuery.getReturnDateStart())) {
                    returnAnalyzeVO.setReturnDate(((JSONObject) js).getString("returnDate"));
                } else {
                    returnAnalyzeVO.setOrderDate(((JSONObject) js).getString("returnDate"));
                }
                returnAnalyzeVO.setQuantity(((JSONObject)js).getInteger("saleNum"));
                returnAnalyzeVO.setSaleCurrentNum(((JSONObject)js).getInteger("saleCurrentNum"));
                returnAnalyzeVOList.add(returnAnalyzeVO);
            }
        }

        //退货时间
        if(StringUtils.isNotEmpty(statReturnQuery.getReturnDateStart()) && StringUtils.isNotEmpty(statReturnQuery.getReturnDateEnd())) {

            if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) {
                rangeList = SpiltDateUtil.splitToDays(statReturnQuery.getReturnDateStart(), statReturnQuery.getReturnDateEnd());
            } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) {
                rangeList = SpiltDateUtil.splitToWeeks(statReturnQuery.getReturnDateStart(), statReturnQuery.getReturnDateEnd());
            } else if (StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) {
                rangeList = SpiltDateUtil.splitToMonths(statReturnQuery.getReturnDateStart(), statReturnQuery.getReturnDateEnd());
            }
            //x轴数据
            List<String> dateList = new ArrayList<>();
            for (SpiltDateUtil.Range range : rangeList) {
                String dateRange = "";
                if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) {
                    dateRange = DateUtil.convertDateToString(range.getStart(), "yyyy-MM-dd");
                } else if (StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) {
                    dateRange = DateUtil.convertDateToString(range.getStart(), "yyyy-MM");
                } else {
                    dateRange = DateUtil.convertDateToString(range.getStart(), "yyyy-MM-dd") + "~" + DateUtil.convertDateToString(range.getEnd(), "yyyy-MM-dd");
                }
                dateList.add(dateRange);
            }
            result.put("x", dateList);

            //y轴各个指标数值
            List<BigDecimal> returnRateList = new ArrayList<>();//退货率
            List<BigDecimal> quantityList = new ArrayList<>();//销量
            List<BigDecimal> quantityListMonth = new ArrayList<>();//月销量
            BigDecimal sumReturns = new BigDecimal(0);
            for (SpiltDateUtil.Range range : rangeList) {
                if(StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType()) || StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) {
                    //退货量
                    sumReturns = returnInfoEntityList.stream().filter(returnInfo -> {
                                try {
                                    return DateUtil.compareDate(simpleDateFormat.parse(returnInfo.getReturnDate()), range.getStart()) >= 0 &&
                                            DateUtil.compareDate(simpleDateFormat.parse(returnInfo.getReturnDate()), range.getEnd()) <= 0;
                                } catch (ParseException e) {
                                    throw new RuntimeException(e);
                                }
                            }
                    ).map(i -> i.getReturnNum() != null ? new BigDecimal(i.getReturnNum()) : new BigDecimal(0)).reduce(BigDecimal.ZERO, BigDecimal::add);
                } else {
                    //退货量
                    sumReturns = returnInfoEntityList.stream().filter(returnInfo ->returnInfo.getReturnDate().equals(monthSimpleDateFormat.format(range.getStart())))
                            .map(i -> i.getReturnNum() != null ? new BigDecimal(i.getReturnNum()) : new BigDecimal(0)).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                //退货率 = 退货量/销量
                BigDecimal returnRate = null;
                BigDecimal sumQuantity = new BigDecimal(0);
                BigDecimal sumQuantityMonth = new BigDecimal(0);
                if (CollectionUtil.isNotEmpty(returnAnalyzeVOList)) {
                    if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) { //日
                        sumQuantity = returnAnalyzeVOList.stream().filter(returnAnalyzeVO ->
                                {
                                    try {
                                        return DateUtil.compareDate(simpleDateFormat.parse(returnAnalyzeVO.getReturnDate()), range.getStart()) >= 0 &&
                                                DateUtil.compareDate(simpleDateFormat.parse(returnAnalyzeVO.getReturnDate()), range.getEnd()) <= 0;
                                    } catch (ParseException e) {
                                        throw new RuntimeException(e);
                                    }
                                }).map(i -> i.getQuantity() != null ? new BigDecimal(i.getQuantity()) : new BigDecimal(0))
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                    } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) { //周
                        sumQuantity = returnAnalyzeVOList.stream().filter(returnAnalyzeVO -> {
                                    return returnAnalyzeVO.getReturnDate().equals(simpleDateFormat.format(range.getStart()) + "~" + simpleDateFormat.format(range.getEnd()));
                                }).map(i -> i.getQuantity() != null ? new BigDecimal(i.getQuantity()) : new BigDecimal(0))
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                    } else { //月
                        sumQuantity = returnAnalyzeVOList.stream().filter(returnAnalyzeVO -> {
                                    return returnAnalyzeVO.getReturnDate().equals(monthSimpleDateFormat.format(range.getStart()));
                                }).map(i -> i.getQuantity() != null ? new BigDecimal(i.getQuantity()) : new BigDecimal(0))
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        sumQuantityMonth = returnAnalyzeVOList.stream().filter(returnAnalyzeVO -> {
                                    return returnAnalyzeVO.getReturnDate().equals(monthSimpleDateFormat.format(range.getStart()));
                                }).map(i -> i.getSaleCurrentNum() != null ? new BigDecimal(i.getSaleCurrentNum()) : new BigDecimal(0))
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        quantityListMonth.add(sumQuantityMonth);
                    }
                }
 //               if(StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) {
                    if (sumQuantity == null || sumQuantity.compareTo(BigDecimal.ZERO) == 0) {
                        returnRate = new BigDecimal(0);
                    } else {
                        returnRate = sumReturns.divide(sumQuantity, 6, ROUND_HALF_UP).multiply(new BigDecimal(1000000)).setScale(0);
                    }
//                } else {
//                    if(sumQuantityMonth == null || sumQuantityMonth.compareTo(BigDecimal.ZERO) == 0) {
//                        returnRate = new BigDecimal(0);
//                    } else {
//                        returnRate = sumReturns.divide(sumQuantityMonth, 6, ROUND_HALF_UP).multiply(new BigDecimal(1000000)).setScale(0);
//                    }
//                }

                returnRateList.add(returnRate);
                quantityList.add(sumQuantity);
            }
            data.put("returnRateList", returnRateList);
            //只有月取当前月，周，日取平均月，应为那边那边接口定义的时候没有改。
            if(StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) {
                data.put("quantityList", quantityListMonth); //柱形图只展示实际销量
            } else {
                data.put("quantityList", quantityList);
            }
            result.put("data", data);
            return result;

        } else { //订单时间
            if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) {
                rangeList = SpiltDateUtil.splitToDays(statReturnQuery.getOrderDateStart(), statReturnQuery.getOrderDateEnd());
            } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) {
                rangeList = SpiltDateUtil.splitToWeeks(statReturnQuery.getOrderDateStart(), statReturnQuery.getOrderDateEnd());
            } else if (StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) {
                rangeList = SpiltDateUtil.splitToMonths(statReturnQuery.getOrderDateStart(), statReturnQuery.getOrderDateEnd());
            }
            //x轴数据
            List<String> dateList = new ArrayList<>();
            for (SpiltDateUtil.Range range : rangeList) {
                String dateRange = "";
                if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) {
                    dateRange = DateUtil.convertDateToString(range.getStart(), "yyyy-MM-dd");
                } else if (StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) {
                    dateRange = DateUtil.convertDateToString(range.getStart(), "yyyy-MM");
                } else {
                    dateRange = DateUtil.convertDateToString(range.getStart(), "yyyy-MM-dd") + "~" + DateUtil.convertDateToString(range.getEnd(), "yyyy-MM-dd");
                }
                dateList.add(dateRange);
            }
            result.put("x", dateList);
            //y轴各个指标数值
            List<BigDecimal> returnRateList = new ArrayList<>();//退货率
            List<BigDecimal> quantityList = new ArrayList<>();//销量
            List<BigDecimal> quantityListMonth = new ArrayList<>();//月销量
            for (SpiltDateUtil.Range range : rangeList) {
                BigDecimal sumReturns = new BigDecimal(0);
                if(StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType()) || StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) {
                    //退货量
                    sumReturns = returnInfoEntityList.stream().filter(returnInfo -> {
                                try {
                                    return DateUtil.compareDate(simpleDateFormat.parse(returnInfo.getOrderDate()), range.getStart()) >= 0 &&
                                            DateUtil.compareDate(simpleDateFormat.parse(returnInfo.getOrderDate()), range.getEnd()) <= 0;
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                            }).map(i -> i.getReturnNum() != null ? new BigDecimal(i.getReturnNum()) : new BigDecimal(0))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                } else {
                    sumReturns = returnInfoEntityList.stream().filter(returnInfo -> returnInfo.getOrderDate().equals(monthSimpleDateFormat.format(range.getStart())))
                            .map(i -> i.getReturnNum() != null ? new BigDecimal(i.getReturnNum()) : new BigDecimal(0))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                }

                //销量
                BigDecimal sumQuantity = null;
                BigDecimal sumQuantityMonth = new BigDecimal(0);
                if (CollectionUtil.isNotEmpty(returnAnalyzeVOList)) {
                    if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) { //日
                        sumQuantity = returnAnalyzeVOList.stream().filter(returnAnalyzeVO ->
                                {
                                    try {
                                        return DateUtil.compareDate(simpleDateFormat.parse(returnAnalyzeVO.getOrderDate()), range.getStart()) >= 0 &&
                                                DateUtil.compareDate(simpleDateFormat.parse(returnAnalyzeVO.getOrderDate()), range.getEnd()) <= 0;
                                    } catch (ParseException e) {
                                        throw new RuntimeException(e);
                                    }
                                }).map(i -> i.getQuantity() != null ? new BigDecimal(i.getQuantity()) : new BigDecimal(0))
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                    } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) { //周
                        sumQuantity = returnAnalyzeVOList.stream().filter(returnAnalyzeVO -> {
                                    return returnAnalyzeVO.getOrderDate().equals(simpleDateFormat.format(range.getStart()) + "~" + simpleDateFormat.format(range.getEnd()));
                                }).map(i -> i.getQuantity() != null ? new BigDecimal(i.getQuantity()) : new BigDecimal(0))
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                    } else { //月
                        sumQuantity = returnAnalyzeVOList.stream().filter(returnAnalyzeVO -> {
                                    return returnAnalyzeVO.getOrderDate().equals(monthSimpleDateFormat.format(range.getStart()));
                                }).map(i -> i.getQuantity() != null ? new BigDecimal(i.getQuantity()) : new BigDecimal(0))
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        sumQuantityMonth = returnAnalyzeVOList.stream().filter(returnAnalyzeVO -> {
                                    return returnAnalyzeVO.getOrderDate().equals(monthSimpleDateFormat.format(range.getStart()));
                                }).map(i -> i.getSaleCurrentNum() != null ? new BigDecimal(i.getSaleCurrentNum()) : new BigDecimal(0))
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        quantityListMonth.add(sumQuantityMonth);
                    }
                }
                // 退货率 = 退货量/销量(当月的销量)
                BigDecimal returnRate = null;
                if(sumQuantity == null || sumQuantity.compareTo(BigDecimal.ZERO) == 0) {
                    returnRate = new BigDecimal(0);
                } else {
                    returnRate = sumReturns.divide(sumQuantity, 6, ROUND_HALF_UP).multiply(new BigDecimal(1000000)).setScale(0);
                }
//                if(sumQuantityMonth == null || sumQuantityMonth.compareTo(BigDecimal.ZERO) == 0) {
//                    returnRate = new BigDecimal(0);
//                } else {
//                    returnRate = sumReturns.divide(sumQuantityMonth, 6, ROUND_HALF_UP).multiply(new BigDecimal(1000000)).setScale(0);
//                }


                returnRateList.add(returnRate);
                quantityList.add(sumQuantity);
            }
            data.put("returnRateList",returnRateList);
            if(StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) {
                data.put("quantityList", quantityListMonth); //柱形图只展示实际销量
            } else {
                data.put("quantityList", quantityList); //实际销量
            }
            result.put("data", data);
            return result;
        }
    }

    /**
     * Description: B2C退货分析列表
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/16
     */
    @Override
    public List<ReturnAnalyzeVO> getB2CReturnAnalyzePageList(StatReturnQuery statReturnQuery) {
        //ASIN 模糊
        if (!CollectionUtils.isEmpty(statReturnQuery.getAsinList()) && statReturnQuery.getAsinList().size() == 1) {
            statReturnQuery.setAsin(statReturnQuery.getAsinList().get(0));
            statReturnQuery.setAsinList(null);
        }
        //SKU模糊
        if (!CollectionUtils.isEmpty(statReturnQuery.getSkuList()) && statReturnQuery.getSkuList().size() == 1) {
            statReturnQuery.setSku(statReturnQuery.getSkuList().get(0));
            statReturnQuery.setSkuList(null);
        }
        List<ReturnAnalyzeVO> b2CReturnAnalyzeList = new ArrayList<>();
        if(!new Integer(4).equals(statReturnQuery.getType()) && !new Integer(6).equals(statReturnQuery.getType())) {//不是退货原因维度也不是平台退货原因维度
            if(StringUtils.isNotEmpty(statReturnQuery.getReturnDateStart())  && StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) { //区分开时间类型为月份的
                try {
                    statReturnQuery.setIfSalesQuantity("false"); //不需要统计销量
                    b2CReturnAnalyzeList = returnAnalyzeMapper.getB2CReturnAnalyzeListByReturnDate(statReturnQuery);
                    if(CollectionUtil.isNotEmpty(b2CReturnAnalyzeList)) {
                        Set<String> asinSet = null;
                        Set<String> skuSet = null;
                        Set<Integer> shopIdSet = null;
                        Set<String> channelSet = null;
                        Set<Integer> categoryIdSet = null;
                        StatReturnQuery statReturnQueryCopy =new StatReturnQuery();
                        BeanUtils.copyProperties(statReturnQuery,statReturnQueryCopy);
                        if(new Integer(0).equals(statReturnQuery.getType())) {
                            asinSet = b2CReturnAnalyzeList.stream().map(i ->i.getAsin()).collect(Collectors.toSet());
                            statReturnQueryCopy.setAsinList(new ArrayList(asinSet));
                        } else if(new Integer(1).equals(statReturnQuery.getType())) {
                            skuSet = b2CReturnAnalyzeList.stream().map(i ->i.getSku()).collect(Collectors.toSet());
                            statReturnQueryCopy.setSkuList(new ArrayList<>(skuSet));
                        } else if(new Integer(2).equals(statReturnQuery.getType())) {
                            shopIdSet = b2CReturnAnalyzeList.stream().map(i ->i.getShopId()).collect(Collectors.toSet());
                            statReturnQueryCopy.setShopIdList(new ArrayList<>(shopIdSet));
                        } else if(new Integer(3).equals(statReturnQuery.getType())) {
                            channelSet = b2CReturnAnalyzeList.stream().map(i ->i.getChannel()).collect(Collectors.toSet());
                            statReturnQueryCopy.setChannelList(new ArrayList<>(channelSet));
                        } else if(new Integer(5).equals(statReturnQuery.getType())) {
                            categoryIdSet = b2CReturnAnalyzeList.stream().map(i ->i.getCategoryId()).collect(Collectors.toSet());
                            statReturnQueryCopy.setCategoryIdList(new ArrayList<>(categoryIdSet));
                        }
                        statReturnQueryCopy.setApiType("ITEM");
                        JSONObject jsonObject = this.sendReturnInfoMessage(statReturnQueryCopy);
                        JSONArray jsonArray = jsonObject.getJSONArray("data");
                        Map<String,BigDecimal> map = new HashMap<>();
                        if(jsonArray != null) {
                            if(new Integer(0).equals(statReturnQueryCopy.getType())) {
                                map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject)i).getString("asin"),j -> ((JSONObject)j).getBigDecimal("saleNumSum")));
                            } else if(new Integer(1).equals(statReturnQueryCopy.getType())) {
                                map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject)i).getString("erpSku"),j -> ((JSONObject)j).getBigDecimal("saleNumSum")));
                            } else if(new Integer(2).equals(statReturnQueryCopy.getType())) {
                                map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject)i).getString("channelId"),j -> ((JSONObject)j).getBigDecimal("saleNumSum")));
                            } else if(new Integer(3).equals(statReturnQueryCopy.getType())) {
                                map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject)i).getString("channel"),j -> ((JSONObject)j).getBigDecimal("saleNumSum")));
                            } else if(new Integer(5).equals(statReturnQueryCopy.getType())) {
                                map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject)i).getString("categorySecondId"),j -> ((JSONObject)j).getBigDecimal("saleNumSum")));
                            }
                        }

                        for(ReturnAnalyzeVO returnAnalyzeVO:b2CReturnAnalyzeList) {
                            log.error("退货分析list按月度查询：---------------------------------");
                            BigDecimal saleNumSum = null;
                            if(new Integer(0).equals(statReturnQuery.getType())) {
                                saleNumSum = map.get(returnAnalyzeVO.getAsin());
                            } else if(new Integer(1).equals(statReturnQuery.getType())) {
                                saleNumSum = map.get(returnAnalyzeVO.getSku());
                            } else if(new Integer(2).equals(statReturnQuery.getType())) {
                                saleNumSum = map.get(returnAnalyzeVO.getShopId().toString());
                            } else if(new Integer(3).equals(statReturnQuery.getType())) {
                                saleNumSum = map.get(returnAnalyzeVO.getChannel());
                            } else if(new Integer(5).equals(statReturnQuery.getType())) {
                                saleNumSum = map.get(returnAnalyzeVO.getCategoryId().toString());
                            }
                            if (saleNumSum == null || saleNumSum.intValue() == 0) {
                                returnAnalyzeVO.setReturnRate(new BigDecimal(0));
                                returnAnalyzeVO.setSaleNum(0);
                            } else {
                                BigDecimal returnRate = new BigDecimal(returnAnalyzeVO.getReturnNum()).divide(saleNumSum, 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                returnAnalyzeVO.setReturnRate(returnRate.setScale(0));
                                returnAnalyzeVO.setSaleNum(saleNumSum.intValue());
                            }
                        }
                    }
                }catch (Exception e) {
                    log.error(e.getMessage());
                }
            }else{
                if(StringUtils.isNotEmpty(statReturnQuery.getReturnDateStart())) { //退货时间
                    b2CReturnAnalyzeList = returnAnalyzeMapper.getB2CReturnAnalyzeListByReturnDate(statReturnQuery);
                } else {//订单时间
                    b2CReturnAnalyzeList = returnAnalyzeMapper.getB2CReturnAnalyzeList(statReturnQuery);
                    if(CollectionUtil.isNotEmpty(b2CReturnAnalyzeList)) {
                        if (new Integer(0).equals(statReturnQuery.getType())) {
                            List<ReturnAnalyzeVO> returnAnalyzeVOS = b2CReturnAnalyzeList.stream().filter(i -> "无商品ID".equals(i.getAsin())).collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(returnAnalyzeVOS)) {
                                //拼接参数
                                if (StringUtil.isNotEmpty(statReturnQuery.getSku())) {
                                    List<String> skuOne = new ArrayList<>();
                                    skuOne.add(statReturnQuery.getSku());
                                    statReturnQuery.setSkuList(skuOne);
                                }
                            }
                        }
                        if (new Integer(1).equals(statReturnQuery.getType())) {
                            List<ReturnAnalyzeVO> returnAnalyzeVOS = b2CReturnAnalyzeList.stream().filter(i -> "无sku".equals(i.getAsin())).collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(returnAnalyzeVOS)) {
                                //拼接参数
                                if (StringUtil.isNotEmpty(statReturnQuery.getAsin())) {
                                    List<String> asinOne = new ArrayList<>();
                                    asinOne.add(statReturnQuery.getAsin());
                                    statReturnQuery.setAsinList(asinOne);
                                }
                            }
                        }
                        if (new Integer(5).equals(statReturnQuery.getType())) {
                            List<ReturnAnalyzeVO> returnAnalyzeVOS = b2CReturnAnalyzeList.stream().filter(i -> "无品类".equals(i.getAsin())).collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(returnAnalyzeVOS)) {
                                //拼接参数
                                if (StringUtil.isNotEmpty(statReturnQuery.getAsin())) {
                                    List<String> asinOne = new ArrayList<>();
                                    asinOne.add(statReturnQuery.getAsin());
                                    statReturnQuery.setAsinList(asinOne);
                                }
                                //拼接参数
                                if (StringUtil.isNotEmpty(statReturnQuery.getSku())) {
                                    List<String> skuOne = new ArrayList<>();
                                    skuOne.add(statReturnQuery.getSku());
                                    statReturnQuery.setSkuList(skuOne);
                                }
                            }
                        }
                    }
                }
                if(CollectionUtil.isNotEmpty(b2CReturnAnalyzeList)) {
                    Set<String> asinSet = null;
                    Set<String> skuSet = null;
                    Set<Integer> shopIdSet = null;
                    Set<String> channelSet = null;
                    Set<Integer> categoryIdSet = null;
                    StatReturnQuery statReturnQueryCopy =new StatReturnQuery();
                    BeanUtils.copyProperties(statReturnQuery,statReturnQueryCopy);
                    if(new Integer(0).equals(statReturnQuery.getType())) {
                        asinSet = b2CReturnAnalyzeList.stream().map(i ->i.getAsin()).collect(Collectors.toSet());
                        statReturnQueryCopy.setAsinList(new ArrayList(asinSet));
                    } else if(new Integer(1).equals(statReturnQuery.getType())) {
                        skuSet = b2CReturnAnalyzeList.stream().map(i ->i.getSku()).collect(Collectors.toSet());
                        statReturnQueryCopy.setSkuList(new ArrayList<>(skuSet));
                    } else if(new Integer(2).equals(statReturnQuery.getType())) {
                        shopIdSet = b2CReturnAnalyzeList.stream().map(i ->i.getShopId()).collect(Collectors.toSet());
                        statReturnQueryCopy.setShopIdList(new ArrayList<>(shopIdSet));
                    } else if(new Integer(3).equals(statReturnQuery.getType())) {
                        channelSet = b2CReturnAnalyzeList.stream().map(i ->i.getChannel()).collect(Collectors.toSet());
                        statReturnQueryCopy.setChannelList(new ArrayList<>(channelSet));
                    } else if(new Integer(5).equals(statReturnQuery.getType())) {
                        categoryIdSet = b2CReturnAnalyzeList.stream().map(i ->i.getCategoryId()).collect(Collectors.toSet());
                        statReturnQueryCopy.setCategoryIdList(new ArrayList<>(categoryIdSet));
                    }
                    //todo 调用接口出来
                    statReturnQueryCopy.setApiType("ITEM");
                    JSONObject jsonObject = this.sendReturnInfoNoAvgMessage(statReturnQueryCopy);
                    JSONArray jsonArray = jsonObject.getJSONArray("data");
                    Map<String,BigDecimal> map = new HashMap<>();
                    if(jsonArray != null) {
                        if(new Integer(0).equals(statReturnQueryCopy.getType())) {
                            map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject)i).getString("asin"),j -> ((JSONObject)j).getBigDecimal("saleNumSum")));
                        } else if(new Integer(1).equals(statReturnQueryCopy.getType())) {
                            map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject)i).getString("erpSku"),j -> ((JSONObject)j).getBigDecimal("saleNumSum")));
                        } else if(new Integer(2).equals(statReturnQueryCopy.getType())) {
                            map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject)i).getString("channelId"),j -> ((JSONObject)j).getBigDecimal("saleNumSum")));
                        } else if(new Integer(3).equals(statReturnQueryCopy.getType())) {
                            map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject)i).getString("channel"),j -> ((JSONObject)j).getBigDecimal("saleNumSum")));
                        } else if(new Integer(5).equals(statReturnQueryCopy.getType())) {
                            map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject)i).getString("categorySecondId"),j -> ((JSONObject)j).getBigDecimal("saleNumSum")));
                        }
                    }
                    for(ReturnAnalyzeVO returnAnalyzeVO:b2CReturnAnalyzeList) {
                        log.error("退货分析list按月度查询：---------------------------------");
                        BigDecimal saleNumSum = null;
                        if(new Integer(0).equals(statReturnQuery.getType())) {
                            saleNumSum = map.get(returnAnalyzeVO.getAsin());
                        } else if(new Integer(1).equals(statReturnQuery.getType())) {
                            saleNumSum = map.get(returnAnalyzeVO.getSku());
                        } else if(new Integer(2).equals(statReturnQuery.getType())) {
                            saleNumSum = map.get(returnAnalyzeVO.getShopId().toString());
                        } else if(new Integer(3).equals(statReturnQuery.getType())) {
                            saleNumSum = map.get(returnAnalyzeVO.getChannel());
                        } else if(new Integer(5).equals(statReturnQuery.getType())) {
                            saleNumSum = map.get(returnAnalyzeVO.getCategoryId().toString());
                        }
                        if (saleNumSum == null || saleNumSum.intValue() == 0) {
                            returnAnalyzeVO.setReturnRate(new BigDecimal(0));
                            returnAnalyzeVO.setSaleNum(0);
                        } else {
                            BigDecimal returnRate = new BigDecimal(returnAnalyzeVO.getReturnNum()).divide(saleNumSum, 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                            returnAnalyzeVO.setReturnRate(returnRate.setScale(0));
                            returnAnalyzeVO.setSaleNum(saleNumSum.intValue());
                        }
                    }
                }
            }
        }else { //这个单独处理退货原因，平台退货原因维度
            if(StringUtils.isNotEmpty(statReturnQuery.getReturnDateStart())) { //退货时间
                b2CReturnAnalyzeList = returnAnalyzeMapper.getB2CReturnReasonAnalyzeListByReturnDate(statReturnQuery);
            } else{ //订单时间
                b2CReturnAnalyzeList = returnAnalyzeMapper.getB2CReturnReasonAnalyzeList(statReturnQuery);
            }
            if(statReturnQuery.getReturnDateStart() != null && StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) { //区分开时间类型为月份的
                //todo 这一块要怎么去处理 也是利用接口去掉用 让他们处理吧
                statReturnQuery.setApiType("ITEM");
                //拼接参数
                if(StringUtil.isNotEmpty(statReturnQuery.getAsin())) {
                    List<String> asinOne = new ArrayList<>();
                    asinOne.add(statReturnQuery.getAsin());
                    statReturnQuery.setAsinList(asinOne);
                }
                if(StringUtil.isNotEmpty(statReturnQuery.getSku())) {
                    List<String> skuOne = new ArrayList<>();
                    skuOne.add(statReturnQuery.getSku());
                    statReturnQuery.setSkuList(skuOne);
                }
                JSONObject jsonObject = this.sendReturnInfoMessage(statReturnQuery);
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                Integer saleNumSum;
                if(jsonArray != null) {
                    saleNumSum = ((JSONObject)jsonArray.get(0)).getInteger("saleNumSum");
                } else {
                    saleNumSum = null;
                }
                if (CollectionUtil.isNotEmpty(b2CReturnAnalyzeList)) {
                    //销量
                    b2CReturnAnalyzeList.stream().forEach(i -> {
                        i.setSaleNum(saleNumSum);
                        if (saleNumSum == null || saleNumSum.intValue() == 0) {
                            i.setReturnRate(new BigDecimal(0));
                        } else {
                            BigDecimal returnRate = new BigDecimal(i.getReturnNum()).divide(new BigDecimal(saleNumSum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                            i.setReturnRate(returnRate.setScale(0));
                        }
                    });
                }

            }else {
                if(CollectionUtil.isNotEmpty(b2CReturnAnalyzeList)) {
                    StatReturnQuery statReturnQueryCopy =new StatReturnQuery();
                    BeanUtils.copyProperties(statReturnQuery,statReturnQueryCopy);
                    //拼接参数
                    if (StringUtil.isNotEmpty(statReturnQueryCopy.getAsin())) {
                        List<String> asinOne = new ArrayList<>();
                        asinOne.add(statReturnQueryCopy.getAsin());
                        statReturnQueryCopy.setAsinList(asinOne);
                    }
                    if (StringUtil.isNotEmpty(statReturnQueryCopy.getSku())) {
                        List<String> skuOne = new ArrayList<>();
                        skuOne.add(statReturnQueryCopy.getSku());
                        statReturnQueryCopy.setSkuList(skuOne);
                    }
                    //todo 调用接口出来
                    statReturnQueryCopy.setApiType("ITEM");
                    JSONObject jsonObject = this.sendReturnInfoNoAvgMessage(statReturnQueryCopy);
                    JSONArray jsonArray = jsonObject.getJSONArray("data");
                    //这边只会有一条数据
                    BigDecimal saleNumSum = null;
                    if(jsonArray != null) {
                        saleNumSum = ((JSONObject)jsonArray.get(0)).getBigDecimal("saleNumSum");
                    }
                    for(ReturnAnalyzeVO returnAnalyzeVO:b2CReturnAnalyzeList) {
                        log.error("退货分析list按月度查询：---------------------------------");
                        if (saleNumSum == null || saleNumSum.intValue() == 0) {
                            returnAnalyzeVO.setReturnRate(new BigDecimal(0));
                            returnAnalyzeVO.setSaleNum(0);
                        } else {
                            BigDecimal returnRate = new BigDecimal(returnAnalyzeVO.getReturnNum()).divide(saleNumSum, 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                            returnAnalyzeVO.setReturnRate(returnRate.setScale(0));
                            returnAnalyzeVO.setSaleNum(saleNumSum.intValue());
                        }
                    }
                }
            }
        }
        if (StringUtils.isNotEmpty(statReturnQuery.getReturnDateStart()) && StringUtils.isNotEmpty(statReturnQuery.getReturnDateEnd())) {
            for (ReturnAnalyzeVO returnAnalyzeVO : b2CReturnAnalyzeList) {
                returnAnalyzeVO.setReturnDate(statReturnQuery.getReturnDateStart() + " - " + statReturnQuery.getReturnDateEnd());
                //设置父类的查询条件方便详情使用
                //还原查询条件
                if(StringUtil.isNotEmpty(statReturnQuery.getAsin())) {
                    statReturnQuery.setAsinList(new ArrayList<String>(Arrays.asList(statReturnQuery.getAsin())));
                }
                //还原查询条件
                if(StringUtil.isNotEmpty(statReturnQuery.getSku())) {
                    statReturnQuery.setSkuList(new ArrayList<String>(Arrays.asList(statReturnQuery.getSku())));
                }
                returnAnalyzeVO.setStatReturnQuery(statReturnQuery);
            }
        } else if (StringUtils.isNotEmpty(statReturnQuery.getOrderDateStart()) && StringUtils.isNotEmpty(statReturnQuery.getOrderDateEnd())) {
            for (ReturnAnalyzeVO returnAnalyzeVO : b2CReturnAnalyzeList) {
                returnAnalyzeVO.setOrderDate(statReturnQuery.getOrderDateStart() + " - " + statReturnQuery.getOrderDateEnd());
                //设置父类的查询条件方便详情使用
                //还原查询条件
                if(StringUtil.isNotEmpty(statReturnQuery.getAsin())) {
                    statReturnQuery.setAsinList(new ArrayList<String>(Arrays.asList(statReturnQuery.getAsin())));
                }
                //还原查询条件
                if(StringUtil.isNotEmpty(statReturnQuery.getSku())) {
                    statReturnQuery.setSkuList(new ArrayList<String>(Arrays.asList(statReturnQuery.getSku())));
                }
                returnAnalyzeVO.setStatReturnQuery(statReturnQuery);
            }
        }
        //sku添加型号
        if(CollectionUtil.isNotEmpty(b2CReturnAnalyzeList) && new Integer(1).equals(statReturnQuery.getType())) {
            List<String> skuList = b2CReturnAnalyzeList.stream().map(i -> i.getSku()).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(skuList)) {//获取型号
                QueryWrapper<Products> productsQueryWrapper = new QueryWrapper<>();
                productsQueryWrapper.eq("org_id",statReturnQuery.getOrgId());
                productsQueryWrapper.eq("disabled_at",0);
                productsQueryWrapper.in("erpsku",skuList);
                List<Products> productsList = productsService.list(productsQueryWrapper);
                if(CollectionUtil.isNotEmpty(productsList)) {
                    for(ReturnAnalyzeVO b:b2CReturnAnalyzeList) {
                        for(Products p:productsList) {
                            if(b.getSku().equals(p.getErpsku())) {
                                b.setProductMidModel(p.getProductMidModel());
                            }
                        }
                    }
                }
            }
        }
        return b2CReturnAnalyzeList;
    }

    /**
     * Description: B2C退货分析列表详情
     *
     * @param statReturnQuery
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/18
     */
    @Override
    public List<ReturnAnalyzeVO> getB2CReturnAnalyzePageListDetailNew(StatReturnQuery statReturnQuery) {
        //ASIN 模糊
        if(new Integer(0).equals(statReturnQuery.getType())) {
            if(CollectionUtil.isNotEmpty(statReturnQuery.getAsinList()) && "无商品ID".equals(statReturnQuery.getAsinList().get(0))) {
                statReturnQuery.setWithoutAsin("1");
            }
            //页面详情展开。一个sku的话sku ,skuList都有 2个的话只有skuList
            if((statReturnQuery.getIfExport() == null || !statReturnQuery.getIfExport()) && CollectionUtil.isNotEmpty(statReturnQuery.getSkuList()) && statReturnQuery.getSkuList().size()== 1) {
                statReturnQuery.setSkuList(null);
            }
            //导出
            if(statReturnQuery.getIfExport() != null && statReturnQuery.getIfExport() && CollectionUtil.isNotEmpty(statReturnQuery.getSkuList()) && statReturnQuery.getSkuList().size()== 1) {
                statReturnQuery.setSku(statReturnQuery.getSkuList().get(0));
                statReturnQuery.setSkuList(null);
            }
            //处理无商品ID 情况
            if("无商品ID".equals(statReturnQuery.getAsin())) {
                statReturnQuery.setWithoutAsin("1");
            }
        } else if(new Integer(1).equals(statReturnQuery.getType())) {
            if(CollectionUtil.isNotEmpty(statReturnQuery.getSkuList()) && "无sku".equals(statReturnQuery.getSkuList().get(0))) {
                statReturnQuery.setWithoutSku("1");
            }
            //页面
            if( (statReturnQuery.getIfExport() == null || !statReturnQuery.getIfExport()) && CollectionUtil.isNotEmpty(statReturnQuery.getAsinList()) && statReturnQuery.getAsinList().size()== 1) {
                statReturnQuery.setAsinList(null);
            }
            //导出
            if( statReturnQuery.getIfExport() != null && statReturnQuery.getIfExport() && CollectionUtil.isNotEmpty(statReturnQuery.getAsinList()) && statReturnQuery.getAsinList().size()== 1) {
                statReturnQuery.setAsin(statReturnQuery.getAsinList().get(0));
                statReturnQuery.setAsinList(null);
            }
            //由于sku存在组合商品所以这里单独判断，并处理
            if(CollectionUtil.isNotEmpty(statReturnQuery.getSkuList()) && statReturnQuery.getSkuList().size() > 1) {
                String string = statReturnQuery.getSkuList().stream().collect(Collectors.joining(","));
                List<String> list = new ArrayList<>();
                list.add(string);
                statReturnQuery.setSkuList(list);
            }
            //处理无sku得情况
            if("无sku".equals(statReturnQuery.getWithoutSku())) {
                statReturnQuery.setWithoutSku("1");
            }
        } else {
            //页面
            if( (statReturnQuery.getIfExport() == null || !statReturnQuery.getIfExport()) && CollectionUtil.isNotEmpty(statReturnQuery.getAsinList()) && statReturnQuery.getAsinList().size()== 1) {
                statReturnQuery.setAsinList(null);
            }
            //导出
            if( statReturnQuery.getIfExport() != null && statReturnQuery.getIfExport() && CollectionUtil.isNotEmpty(statReturnQuery.getAsinList()) && statReturnQuery.getAsinList().size()== 1) {
                statReturnQuery.setAsin(statReturnQuery.getAsinList().get(0));
                statReturnQuery.setAsinList(null);
            }

            //页面详情展开。一个sku的话sku ,skuList都有 2个的话只有skuList
            if((statReturnQuery.getIfExport() == null || !statReturnQuery.getIfExport()) && CollectionUtil.isNotEmpty(statReturnQuery.getSkuList()) && statReturnQuery.getSkuList().size()== 1) {
                statReturnQuery.setSkuList(null);
            }
            //导出
            if(statReturnQuery.getIfExport() != null && statReturnQuery.getIfExport() && CollectionUtil.isNotEmpty(statReturnQuery.getSkuList()) && statReturnQuery.getSkuList().size()== 1) {
                statReturnQuery.setSku(statReturnQuery.getSkuList().get(0));
                statReturnQuery.setSkuList(null);
            }
            //处理无退货原因
            if(CollectionUtil.isNotEmpty(statReturnQuery.getProblemIdList()) && new Integer(-1).equals(statReturnQuery.getProblemIdList().get(0))) {
                statReturnQuery.setWithoutProblem("1");
            }

            //处理无平台退货原因
            if(StringUtils.isNotEmpty(statReturnQuery.getReturnReason()) && "无平台退货原因".equals(statReturnQuery.getReturnReason())) {
                statReturnQuery.setWithoutReturnReason("1");
            }
            //处理无退货原因
            if(CollectionUtil.isNotEmpty(statReturnQuery.getCategoryIdList()) && new Integer(-1).equals(statReturnQuery.getCategoryIdList().get(0))) {
                statReturnQuery.setWithoutCategory("1");
            }

        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat monthSimpleDateFormat = new SimpleDateFormat("yyyy-MM");
        List<ReturnAnalyzeVO> b2CReturnAnalyzeList = new ArrayList<>();
        if (!new Integer(4).equals(statReturnQuery.getType()) && !new Integer(6).equals(statReturnQuery.getType()) ) {//不是退货原因维度
            if (StringUtils.isNotEmpty(statReturnQuery.getReturnDateStart()) && StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) { //区分开时间类型为月份的
                //todo 处理退货时间
                try {
                    statReturnQuery.setIfSalesQuantity("false");
                    b2CReturnAnalyzeList = returnAnalyzeMapper.getB2CReturnAnalyzeDetailListByReturnDate(statReturnQuery);
                    if (CollectionUtil.isNotEmpty(b2CReturnAnalyzeList)) {
                        statReturnQuery.setApiType("ITEM");
                        Set<String> asinSet = null;
                        Set<String> skuSet = null;
                        Set<Integer> shopIdSet = null;
                        Set<String> channelSet = null;
                        Set<Integer> categoryIdSet = null;
                        if(new Integer(0).equals(statReturnQuery.getType())) {
                            asinSet = b2CReturnAnalyzeList.stream().map(i ->i.getAsin()).collect(Collectors.toSet());
                            statReturnQuery.setAsinList(new ArrayList(asinSet));
                            if (!CollectionUtils.isEmpty(statReturnQuery.getSkuList()) && statReturnQuery.getSkuList().size() == 1) {
                                statReturnQuery.setSkuList(null);
                            }
                        } else if(new Integer(1).equals(statReturnQuery.getType())) {
                            skuSet = b2CReturnAnalyzeList.stream().map(i ->i.getSku()).collect(Collectors.toSet());
                            statReturnQuery.setSkuList(new ArrayList<>(skuSet));
                            if (!CollectionUtils.isEmpty(statReturnQuery.getAsinList()) && statReturnQuery.getAsinList().size() == 1) {
                                statReturnQuery.setAsinList(null);
                            }
                        } else if(new Integer(2).equals(statReturnQuery.getType())) {
                            shopIdSet = b2CReturnAnalyzeList.stream().map(i ->i.getShopId()).collect(Collectors.toSet());
                            statReturnQuery.setShopIdList(new ArrayList<>(shopIdSet));
                            if (!CollectionUtils.isEmpty(statReturnQuery.getAsinList()) && statReturnQuery.getAsinList().size() == 1) {
                                statReturnQuery.setAsinList(null);
                            }
                            if (!CollectionUtils.isEmpty(statReturnQuery.getSkuList()) && statReturnQuery.getSkuList().size() == 1) {
                                statReturnQuery.setSkuList(null);
                            }
                        } else if(new Integer(3).equals(statReturnQuery.getType())) {
                            channelSet = b2CReturnAnalyzeList.stream().map(i ->i.getChannel()).collect(Collectors.toSet());
                            statReturnQuery.setChannelList(new ArrayList<>(channelSet));
                            if (!CollectionUtils.isEmpty(statReturnQuery.getAsinList()) && statReturnQuery.getAsinList().size() == 1) {
                                statReturnQuery.setAsinList(null);
                            }
                            if (!CollectionUtils.isEmpty(statReturnQuery.getSkuList()) && statReturnQuery.getSkuList().size() == 1) {
                                statReturnQuery.setSkuList(null);
                            }
                        } else if(new Integer(5).equals(statReturnQuery.getType())) {
                            categoryIdSet = b2CReturnAnalyzeList.stream().map(i ->i.getCategoryId()).collect(Collectors.toSet());
                            statReturnQuery.setCategoryIdList(new ArrayList<>(categoryIdSet));
                            if (!CollectionUtils.isEmpty(statReturnQuery.getAsinList()) && statReturnQuery.getAsinList().size() == 1) {
                                statReturnQuery.setAsinList(null);
                            }
                            if (!CollectionUtils.isEmpty(statReturnQuery.getSkuList()) && statReturnQuery.getSkuList().size() == 1) {
                                statReturnQuery.setSkuList(null);
                            }
                        }
                        JSONObject jsonObject = this.sendReturnInfoMessage(statReturnQuery);
                        JSONArray jsonArray = jsonObject.getJSONArray("data");
                        if (jsonArray != null && jsonArray.size() > 0) {
                            for (Object object : jsonArray) {
                                JSONArray datItemsJsonArray = ((JSONObject) object).getJSONArray("dataItems");
                                for (Object o : datItemsJsonArray) {
                                    String asin = ((JSONObject) object).getString("asin");
                                    String sku = ((JSONObject) object).getString("erpSku");
                                    Integer channelId = ((JSONObject) object).getInteger("channelId");
                                    String channel= ((JSONObject) object).getString("channel");
                                    Integer categorySecondId =  ((JSONObject) object).getInteger("categorySecondId");
                                    String returnDate = ((JSONObject) o).getString("returnDate");
                                    Integer saleNum = ((JSONObject) o).getInteger("saleNum");
                                    for(ReturnAnalyzeVO returnAnalyzeVO : b2CReturnAnalyzeList) {
                                        if (new Integer(0).equals(statReturnQuery.getType()) && returnAnalyzeVO.getAsin().equals(asin) && returnAnalyzeVO.getReturnDate().equals(returnDate)) {
                                            returnAnalyzeVO.setSaleNum(saleNum);
                                            //计算退货率
                                            if (saleNum == null ||saleNum.intValue() == 0) {
                                                returnAnalyzeVO.setReturnRate(new BigDecimal(0));
                                            } else {
                                                BigDecimal returnRate = new BigDecimal(returnAnalyzeVO.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                returnAnalyzeVO.setReturnRate(returnRate.setScale(0));
                                            }
                                        } else if (new Integer(1).equals(statReturnQuery.getType()) && returnAnalyzeVO.getSku().equals(sku) && returnAnalyzeVO.getReturnDate().equals(returnDate)) {
                                            returnAnalyzeVO.setSaleNum(saleNum);
                                            //计算退货率
                                            if (saleNum == null ||saleNum.intValue() == 0) {
                                                returnAnalyzeVO.setReturnRate(new BigDecimal(0));
                                            } else {
                                                BigDecimal returnRate = new BigDecimal(returnAnalyzeVO.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                returnAnalyzeVO.setReturnRate(returnRate.setScale(0));
                                            }
                                        } else if (new Integer(2).equals(statReturnQuery.getType()) && returnAnalyzeVO.getShopId().equals(channelId) && returnAnalyzeVO.getReturnDate().equals(returnDate)) {
                                            returnAnalyzeVO.setSaleNum(saleNum);
                                            //计算退货率
                                            if (saleNum == null ||saleNum.intValue() == 0) {
                                                returnAnalyzeVO.setReturnRate(new BigDecimal(0));
                                            } else {
                                                BigDecimal returnRate = new BigDecimal(returnAnalyzeVO.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                returnAnalyzeVO.setReturnRate(returnRate.setScale(0));
                                            }
                                        } else if (new Integer(3).equals(statReturnQuery.getType()) && returnAnalyzeVO.getChannel().equals(channel) && returnAnalyzeVO.getReturnDate().equals(returnDate)) {
                                            returnAnalyzeVO.setSaleNum(saleNum);
                                            //计算退货率
                                            if (saleNum == null ||saleNum.intValue() == 0) {
                                                returnAnalyzeVO.setReturnRate(new BigDecimal(0));
                                            } else {
                                                BigDecimal returnRate = new BigDecimal(returnAnalyzeVO.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                returnAnalyzeVO.setReturnRate(returnRate.setScale(0));
                                            }
                                        } else if (new Integer(5).equals(statReturnQuery.getType()) && returnAnalyzeVO.getCategoryId().equals(categorySecondId) && returnAnalyzeVO.getReturnDate().equals(returnDate)) {
                                            returnAnalyzeVO.setSaleNum(saleNum);
                                            //计算退货率
                                            if (saleNum == null ||saleNum.intValue() == 0) {
                                                returnAnalyzeVO.setReturnRate(new BigDecimal(0));
                                            } else {
                                                BigDecimal returnRate = new BigDecimal(returnAnalyzeVO.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                returnAnalyzeVO.setReturnRate(returnRate.setScale(0));
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    } catch(Exception e){
                        log.error(e.getMessage());
                    }

            } else {
                Boolean withNoFlag = false;
                if(StringUtils.isNotEmpty(statReturnQuery.getReturnDateStart())) { //退货时间
                    b2CReturnAnalyzeList = returnAnalyzeMapper.getB2CReturnAnalyzeDetailListByReturnDate(statReturnQuery);
                } else {
                    b2CReturnAnalyzeList = returnAnalyzeMapper.getB2CReturnAnalyzeDetailList(statReturnQuery);

                    if(CollectionUtil.isNotEmpty(b2CReturnAnalyzeList)) {
                        if (new Integer(0).equals(statReturnQuery.getType())) {
                            List<ReturnAnalyzeVO> returnAnalyzeVOS = b2CReturnAnalyzeList.stream().filter(i -> "无商品ID".equals(i.getAsin())).collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(returnAnalyzeVOS)) {
                                //拼接参数
                                if (StringUtil.isNotEmpty(statReturnQuery.getSku())) {
                                    List<String> skuOne = new ArrayList<>();
                                    skuOne.add(statReturnQuery.getSku());
                                    statReturnQuery.setSkuList(skuOne);
                                    withNoFlag = true;
                                }
                            }
                        }
                        if (new Integer(1).equals(statReturnQuery.getType())) {
                            List<ReturnAnalyzeVO> returnAnalyzeVOS = b2CReturnAnalyzeList.stream().filter(i -> "无sku".equals(i.getAsin())).collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(returnAnalyzeVOS)) {
                                //拼接参数
                                if (StringUtil.isNotEmpty(statReturnQuery.getAsin())) {
                                    List<String> asinOne = new ArrayList<>();
                                    asinOne.add(statReturnQuery.getAsin());
                                    statReturnQuery.setAsinList(asinOne);
                                    withNoFlag = true;
                                }
                            }
                        }
                        if (new Integer(5).equals(statReturnQuery.getType())) {
                            List<ReturnAnalyzeVO> returnAnalyzeVOS = b2CReturnAnalyzeList.stream().filter(i -> "无品类".equals(i.getAsin())).collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(returnAnalyzeVOS)) {
                                //拼接参数
                                if (StringUtil.isNotEmpty(statReturnQuery.getAsin())) {
                                    List<String> asinOne = new ArrayList<>();
                                    asinOne.add(statReturnQuery.getAsin());
                                    statReturnQuery.setAsinList(asinOne);
                                    withNoFlag = true;
                                }
                                //拼接参数
                                if (StringUtil.isNotEmpty(statReturnQuery.getSku())) {
                                    List<String> skuOne = new ArrayList<>();
                                    skuOne.add(statReturnQuery.getSku());
                                    statReturnQuery.setSkuList(skuOne);
                                    withNoFlag = true;
                                }
                            }
                        }
                    }
                }
                if(StringUtils.isNotEmpty(statReturnQuery.getReturnDateStart())) { //区分一下订单时间，退货时间
                    if(CollectionUtil.isNotEmpty(b2CReturnAnalyzeList)) {
                        Set<String> asinSet = null;
                        Set<String> skuSet = null;
                        Set<Integer> shopIdSet = null;
                        Set<String> channelSet = null;
                        Set<Integer> categoryIdSet = null;
                        StatReturnQuery statReturnQueryCopy =new StatReturnQuery();
                        BeanUtils.copyProperties(statReturnQuery,statReturnQueryCopy);
                        if(new Integer(0).equals(statReturnQuery.getType())) {
                            asinSet = b2CReturnAnalyzeList.stream().map(i ->i.getAsin()).collect(Collectors.toSet());
                            statReturnQueryCopy.setAsinList(new ArrayList(asinSet));
                            if (!CollectionUtils.isEmpty(statReturnQueryCopy.getSkuList()) && statReturnQueryCopy.getSkuList().size() == 1) {
                                statReturnQueryCopy.setSkuList(null);
                            }
                        } else if(new Integer(1).equals(statReturnQuery.getType())) {
                            skuSet = b2CReturnAnalyzeList.stream().map(i ->i.getSku()).collect(Collectors.toSet());
                            statReturnQueryCopy.setSkuList(new ArrayList<>(skuSet));
                            if (!CollectionUtils.isEmpty(statReturnQueryCopy.getAsinList()) && statReturnQueryCopy.getAsinList().size() == 1) {
                                statReturnQueryCopy.setAsinList(null);
                            }
                        } else if(new Integer(2).equals(statReturnQuery.getType())) {
                            shopIdSet = b2CReturnAnalyzeList.stream().map(i ->i.getShopId()).collect(Collectors.toSet());
                            statReturnQueryCopy.setShopIdList(new ArrayList<>(shopIdSet));
                            if (!CollectionUtils.isEmpty(statReturnQueryCopy.getAsinList()) && statReturnQueryCopy.getAsinList().size() == 1) {
                                statReturnQueryCopy.setAsinList(null);
                            }
                            if (!CollectionUtils.isEmpty(statReturnQueryCopy.getSkuList()) && statReturnQueryCopy.getSkuList().size() == 1) {
                                statReturnQueryCopy.setSkuList(null);
                            }
                        } else if(new Integer(3).equals(statReturnQuery.getType())) {
                            channelSet = b2CReturnAnalyzeList.stream().map(i ->i.getChannel()).collect(Collectors.toSet());
                            statReturnQueryCopy.setChannelList(new ArrayList<>(channelSet));
                            if (!CollectionUtils.isEmpty(statReturnQueryCopy.getAsinList()) && statReturnQueryCopy.getAsinList().size() == 1) {
                                statReturnQueryCopy.setAsinList(null);
                            }
                            if (!CollectionUtils.isEmpty(statReturnQueryCopy.getSkuList()) && statReturnQueryCopy.getSkuList().size() == 1) {
                                statReturnQueryCopy.setSkuList(null);
                            }
                        } else if(new Integer(5).equals(statReturnQuery.getType())) {
                            categoryIdSet = b2CReturnAnalyzeList.stream().map(i ->i.getCategoryId()).collect(Collectors.toSet());
                            statReturnQueryCopy.setCategoryIdList(new ArrayList<>(categoryIdSet));
                            if (!CollectionUtils.isEmpty(statReturnQueryCopy.getAsinList()) && statReturnQueryCopy.getAsinList().size() == 1 ) {
                                statReturnQueryCopy.setAsinList(null);
                            }
                            if (!CollectionUtils.isEmpty(statReturnQueryCopy.getSkuList()) && statReturnQueryCopy.getSkuList().size() == 1 ) {
                                statReturnQueryCopy.setSkuList(null);
                            }
                        }
                        //todo 调用接口出来
                        statReturnQueryCopy.setApiType("ITEM");
                        JSONObject jsonObject = this.sendReturnInfoNoAvgMessage(statReturnQueryCopy);
                        JSONArray jsonArray = jsonObject.getJSONArray("data");
                        if(jsonArray != null) {
                            //todo 直接匹配，算退货率，然后返回就ok了
                            for(Object j : jsonArray) {
                                JSONArray dataItems = ((JSONObject) j).getJSONArray("dataItems");
                                for (Object k : dataItems) {
                                    String returnDate = ((JSONObject) k).getString("returnDate");
                                    Integer saleNum = ((JSONObject) k).getInteger("saleNum");
                                    if (new Integer(0).equals(statReturnQueryCopy.getType())) {
                                        String asin = ((JSONObject) j).getString("asin");
                                        for (ReturnAnalyzeVO r : b2CReturnAnalyzeList) {
                                            if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) { //日
                                                if (r.getReturnDate().equals(returnDate) && r.getAsin().equals(asin)) {
                                                    r.setSaleNum(saleNum);
                                                    if (saleNum == null || saleNum.intValue() == 0) {
                                                        r.setReturnRate(new BigDecimal(0));
                                                    } else {
                                                        BigDecimal returnRate = new BigDecimal(r.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                        r.setReturnRate(returnRate.setScale(0));
                                                    }
                                                }
                                            } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) { //周
                                                //todo 判断时间在这个周里面
                                                String date[] = returnDate.split("~");
                                                try {
                                                    if ((DateUtil.compareDate(simpleDateFormat.parse(r.getReturnDate()), simpleDateFormat.parse(date[0])) >= 0 &&
                                                            DateUtil.compareDate(simpleDateFormat.parse(r.getReturnDate()), simpleDateFormat.parse(date[1])) <= 0)
                                                            && r.getAsin().equals(asin)) {
                                                        r.setSaleNum(saleNum);
                                                    }
                                                } catch (Exception e) {
                                                    e.printStackTrace();
                                                }
                                            } else { //月
                                                if (r.getReturnDate().equals(returnDate) && r.getAsin().equals(asin)) {
                                                    r.setSaleNum(saleNum);
                                                    if (saleNum == null || saleNum.intValue() == 0) {
                                                        r.setReturnRate(new BigDecimal(0));
                                                    } else {
                                                        BigDecimal returnRate = new BigDecimal(r.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                        r.setReturnRate(returnRate.setScale(0));
                                                    }
                                                }
                                            }
                                        }

                                    } else if (new Integer(1).equals(statReturnQueryCopy.getType())) {
                                        String sku = ((JSONObject) j).getString("erpSku");
                                        for (ReturnAnalyzeVO r : b2CReturnAnalyzeList) {
                                            if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) { //日
                                                if (r.getReturnDate().equals(returnDate) && r.getSku().equals(sku)) {
                                                    r.setSaleNum(saleNum);
                                                    if (saleNum == null || saleNum.intValue() == 0) {
                                                        r.setReturnRate(new BigDecimal(0));
                                                    } else {
                                                        BigDecimal returnRate = new BigDecimal(r.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                        r.setReturnRate(returnRate.setScale(0));
                                                    }
                                                }
                                            } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) { //周
                                                //todo 判断时间在这个周里面
                                                String date[] = returnDate.split("~");
                                                try {
                                                    if ((DateUtil.compareDate(simpleDateFormat.parse(r.getReturnDate()), simpleDateFormat.parse(date[0])) >= 0 &&
                                                            DateUtil.compareDate(simpleDateFormat.parse(r.getReturnDate()), simpleDateFormat.parse(date[1])) <= 0)
                                                            && r.getSku().equals(sku)) {
                                                        r.setSaleNum(saleNum);
                                                    }
                                                } catch (Exception e) {
                                                    e.printStackTrace();
                                                }
                                            } else { //月
                                                if (r.getReturnDate().equals(returnDate) && r.getSku().equals(sku)) {
                                                    r.setSaleNum(saleNum);
                                                    if (saleNum == null || saleNum.intValue() == 0) {
                                                        r.setReturnRate(new BigDecimal(0));
                                                    } else {
                                                        BigDecimal returnRate = new BigDecimal(r.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                        r.setReturnRate(returnRate.setScale(0));
                                                    }
                                                }
                                            }
                                        }
                                    } else if (new Integer(2).equals(statReturnQueryCopy.getType())) {
                                        Integer channelId = ((JSONObject) j).getInteger("channelId");
                                        for (ReturnAnalyzeVO r : b2CReturnAnalyzeList) {
                                            if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) { //日
                                                if (r.getReturnDate().equals(returnDate) && r.getShopId().equals(channelId)) {
                                                    r.setSaleNum(saleNum);
                                                    if (saleNum == null || saleNum.intValue() == 0) {
                                                        r.setReturnRate(new BigDecimal(0));
                                                    } else {
                                                        BigDecimal returnRate = new BigDecimal(r.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                        r.setReturnRate(returnRate.setScale(0));
                                                    }
                                                }
                                            } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) { //周
                                                //todo 判断时间在这个周里面
                                                String date[] = returnDate.split("~");
                                                try {
                                                    if ((DateUtil.compareDate(simpleDateFormat.parse(r.getReturnDate()), simpleDateFormat.parse(date[0])) >= 0 &&
                                                            DateUtil.compareDate(simpleDateFormat.parse(r.getReturnDate()), simpleDateFormat.parse(date[1])) <= 0)
                                                            && r.getShopId().equals(channelId)) {
                                                        r.setSaleNum(saleNum);
                                                    }
                                                } catch (Exception e) {
                                                    e.printStackTrace();
                                                }
                                            } else { //月
                                                if (r.getReturnDate().equals(returnDate) && r.getShopId().equals(channelId)) {
                                                    r.setSaleNum(saleNum);
                                                    if (saleNum == null || saleNum.intValue() == 0) {
                                                        r.setReturnRate(new BigDecimal(0));
                                                    } else {
                                                        BigDecimal returnRate = new BigDecimal(r.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                        r.setReturnRate(returnRate.setScale(0));
                                                    }
                                                }
                                            }
                                        }
                                    } else if (new Integer(3).equals(statReturnQueryCopy.getType())) {
                                        String channel = ((JSONObject) j).getString("channel");
                                        for (ReturnAnalyzeVO r : b2CReturnAnalyzeList) {
                                            if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) { //日
                                                if (r.getReturnDate().equals(returnDate) && r.getChannel().equals(channel)) {
                                                    r.setSaleNum(saleNum);
                                                    if (saleNum == null || saleNum.intValue() == 0) {
                                                        r.setReturnRate(new BigDecimal(0));
                                                    } else {
                                                        BigDecimal returnRate = new BigDecimal(r.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                        r.setReturnRate(returnRate.setScale(0));
                                                    }
                                                }
                                            } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) { //周
                                                //todo 判断时间在这个周里面
                                                String date[] = returnDate.split("~");
                                                try {
                                                    if ((DateUtil.compareDate(simpleDateFormat.parse(r.getReturnDate()), simpleDateFormat.parse(date[0])) >= 0 &&
                                                            DateUtil.compareDate(simpleDateFormat.parse(r.getReturnDate()), simpleDateFormat.parse(date[1])) <= 0)
                                                            && r.getChannel().equals(channel)) {
                                                        r.setSaleNum(saleNum);
                                                    }
                                                } catch (Exception e) {
                                                    e.printStackTrace();
                                                }
                                            } else { //月
                                                if (r.getReturnDate().equals(returnDate) && r.getChannel().equals(channel)) {
                                                    r.setSaleNum(saleNum);
                                                    if (saleNum == null || saleNum.intValue() == 0) {
                                                        r.setReturnRate(new BigDecimal(0));
                                                    } else {
                                                        BigDecimal returnRate = new BigDecimal(r.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                        r.setReturnRate(returnRate.setScale(0));
                                                    }
                                                }
                                            }
                                        }
                                    } else if (new Integer(5).equals(statReturnQueryCopy.getType())) {
                                        Integer categoryId = ((JSONObject) j).getInteger("categorySecondId");
                                        for (ReturnAnalyzeVO r : b2CReturnAnalyzeList) {
                                            if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) { //日
                                                if (r.getReturnDate().equals(returnDate) && r.getCategoryId().equals(categoryId)) {
                                                    r.setSaleNum(saleNum);
                                                    if (saleNum == null || saleNum.intValue() == 0) {
                                                        r.setReturnRate(new BigDecimal(0));
                                                    } else {
                                                        BigDecimal returnRate = new BigDecimal(r.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                        r.setReturnRate(returnRate.setScale(0));
                                                    }
                                                }
                                            } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) { //周
                                                //todo 判断时间在这个周里面
                                                String date[] = returnDate.split("~");
                                                try {
                                                    if ((DateUtil.compareDate(simpleDateFormat.parse(r.getReturnDate()), simpleDateFormat.parse(date[0])) >= 0 &&
                                                            DateUtil.compareDate(simpleDateFormat.parse(r.getReturnDate()), simpleDateFormat.parse(date[1])) <= 0)
                                                            && r.getCategoryId().equals(categoryId)) {
                                                        r.setSaleNum(saleNum);
                                                    }
                                                } catch (Exception e) {
                                                    e.printStackTrace();
                                                }
                                            } else { //月
                                                if (r.getReturnDate().equals(returnDate) && r.getCategoryId().equals(categoryId)) {
                                                    r.setSaleNum(saleNum);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else { //订单时间
                    if(CollectionUtil.isNotEmpty(b2CReturnAnalyzeList)) {
                        Set<String> asinSet = null;
                        Set<String> skuSet = null;
                        Set<Integer> shopIdSet = null;
                        Set<String> channelSet = null;
                        Set<Integer> categoryIdSet = null;
                        StatReturnQuery statReturnQueryCopy =new StatReturnQuery();
                        BeanUtils.copyProperties(statReturnQuery,statReturnQueryCopy);
                        if(new Integer(0).equals(statReturnQuery.getType())) {
                            asinSet = b2CReturnAnalyzeList.stream().map(i ->i.getAsin()).collect(Collectors.toSet());
                            statReturnQueryCopy.setAsinList(new ArrayList(asinSet));
                            if (!CollectionUtils.isEmpty(statReturnQueryCopy.getSkuList()) && statReturnQueryCopy.getSkuList().size() == 1 && !withNoFlag) {
                                statReturnQueryCopy.setSkuList(null);
                            }
                        } else if(new Integer(1).equals(statReturnQuery.getType())) {
                            skuSet = b2CReturnAnalyzeList.stream().map(i ->i.getSku()).collect(Collectors.toSet());
                            statReturnQueryCopy.setSkuList(new ArrayList<>(skuSet));
                            if (!CollectionUtils.isEmpty(statReturnQueryCopy.getAsinList()) && statReturnQueryCopy.getAsinList().size() == 1 && !withNoFlag) {
                                statReturnQueryCopy.setAsinList(null);
                            }
                        } else if(new Integer(2).equals(statReturnQuery.getType())) {
                            shopIdSet = b2CReturnAnalyzeList.stream().map(i ->i.getShopId()).collect(Collectors.toSet());
                            statReturnQueryCopy.setShopIdList(new ArrayList<>(shopIdSet));
                            if (!CollectionUtils.isEmpty(statReturnQueryCopy.getAsinList()) && statReturnQueryCopy.getAsinList().size() == 1) {
                                statReturnQueryCopy.setAsinList(null);
                            }
                            if (!CollectionUtils.isEmpty(statReturnQueryCopy.getSkuList()) && statReturnQueryCopy.getSkuList().size() == 1) {
                                statReturnQueryCopy.setSkuList(null);
                            }
                        } else if(new Integer(3).equals(statReturnQuery.getType())) {
                            channelSet = b2CReturnAnalyzeList.stream().map(i ->i.getChannel()).collect(Collectors.toSet());
                            statReturnQueryCopy.setChannelList(new ArrayList<>(channelSet));
                            if (!CollectionUtils.isEmpty(statReturnQueryCopy.getAsinList()) && statReturnQueryCopy.getAsinList().size() == 1) {
                                statReturnQueryCopy.setAsinList(null);
                            }
                            if (!CollectionUtils.isEmpty(statReturnQueryCopy.getSkuList()) && statReturnQueryCopy.getSkuList().size() == 1) {
                                statReturnQueryCopy.setSkuList(null);
                            }
                        } else if(new Integer(5).equals(statReturnQuery.getType())) {
                            categoryIdSet = b2CReturnAnalyzeList.stream().map(i ->i.getCategoryId()).collect(Collectors.toSet());
                            statReturnQueryCopy.setCategoryIdList(new ArrayList<>(categoryIdSet));
                            if (!CollectionUtils.isEmpty(statReturnQueryCopy.getAsinList()) && statReturnQueryCopy.getAsinList().size() == 1 && !withNoFlag) {
                                statReturnQueryCopy.setAsinList(null);
                            }
                            if (!CollectionUtils.isEmpty(statReturnQueryCopy.getSkuList()) && statReturnQueryCopy.getSkuList().size() == 1 && !withNoFlag) {
                                statReturnQueryCopy.setSkuList(null);
                            }
                        }
                        //todo 调用接口出来
                        statReturnQueryCopy.setApiType("ITEM");
                        JSONObject jsonObject = this.sendReturnInfoNoAvgMessage(statReturnQueryCopy);
                        JSONArray jsonArray = jsonObject.getJSONArray("data");
                        if(jsonArray != null) {
                            //todo 直接匹配，算退货率，然后返回就ok了
                            for(Object j : jsonArray) {
                                JSONArray dataItems = ((JSONObject) j).getJSONArray("dataItems");
                                for (Object k : dataItems) {
                                    String returnDate = ((JSONObject) k).getString("returnDate");
                                    Integer saleNum = ((JSONObject) k).getInteger("saleNum");
                                    if (new Integer(0).equals(statReturnQueryCopy.getType())) {
                                        String asin = ((JSONObject) j).getString("asin");
                                        for (ReturnAnalyzeVO r : b2CReturnAnalyzeList) {
                                            if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) { //日
                                                if (r.getOrderDate().equals(returnDate) && r.getAsin().equals(asin)) {
                                                    r.setSaleNum(saleNum);
                                                    if (saleNum == null || saleNum.intValue() == 0) {
                                                        r.setReturnRate(new BigDecimal(0));
                                                    } else {
                                                        BigDecimal returnRate = new BigDecimal(r.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                        r.setReturnRate(returnRate.setScale(0));
                                                    }
                                                }
                                            } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) { //周
                                                //todo 判断时间在这个周里面
                                                String date[] = returnDate.split("~");
                                                try {
                                                    if ((DateUtil.compareDate(simpleDateFormat.parse(r.getOrderDate()), simpleDateFormat.parse(date[0])) >= 0 &&
                                                            DateUtil.compareDate(simpleDateFormat.parse(r.getOrderDate()), simpleDateFormat.parse(date[1])) <= 0)
                                                            && r.getAsin().equals(asin)) {
                                                        r.setSaleNum(saleNum);
                                                    }
                                                } catch (Exception e) {
                                                    e.printStackTrace();
                                                }
                                            } else { //月
                                                if (r.getOrderDate().equals(returnDate) && r.getAsin().equals(asin)) {
                                                    r.setSaleNum(saleNum);
                                                }
                                            }
                                        }

                                    } else if (new Integer(1).equals(statReturnQueryCopy.getType())) {
                                        String sku = ((JSONObject) j).getString("erpSku");
                                        for (ReturnAnalyzeVO r : b2CReturnAnalyzeList) {
                                            if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) { //日
                                                if (r.getOrderDate().equals(returnDate) && r.getSku().equals(sku)) {
                                                    r.setSaleNum(saleNum);
                                                    if (saleNum == null || saleNum.intValue() == 0) {
                                                        r.setReturnRate(new BigDecimal(0));
                                                    } else {
                                                        BigDecimal returnRate = new BigDecimal(r.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                        r.setReturnRate(returnRate.setScale(0));
                                                    }
                                                }
                                            } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) { //周
                                                //todo 判断时间在这个周里面
                                                String date[] = returnDate.split("~");
                                                try {
                                                    if ((DateUtil.compareDate(simpleDateFormat.parse(r.getOrderDate()), simpleDateFormat.parse(date[0])) >= 0 &&
                                                            DateUtil.compareDate(simpleDateFormat.parse(r.getOrderDate()), simpleDateFormat.parse(date[1])) <= 0)
                                                            && r.getSku().equals(sku)) {
                                                        r.setSaleNum(saleNum);
                                                    }
                                                } catch (Exception e) {
                                                    e.printStackTrace();
                                                }
                                            } else { //月
                                                if (r.getOrderDate().equals(returnDate) && r.getSku().equals(sku)) {
                                                    r.setSaleNum(saleNum);
                                                }
                                            }
                                        }
                                    } else if (new Integer(2).equals(statReturnQueryCopy.getType())) {
                                        Integer channelId = ((JSONObject) j).getInteger("channelId");
                                        for (ReturnAnalyzeVO r : b2CReturnAnalyzeList) {
                                            if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) { //日
                                                if (r.getOrderDate().equals(returnDate) && r.getShopId().equals(channelId)) {
                                                    r.setSaleNum(saleNum);
                                                    if (saleNum == null || saleNum.intValue() == 0) {
                                                        r.setReturnRate(new BigDecimal(0));
                                                    } else {
                                                        BigDecimal returnRate = new BigDecimal(r.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                        r.setReturnRate(returnRate.setScale(0));
                                                    }
                                                }
                                            } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) { //周
                                                //todo 判断时间在这个周里面
                                                String date[] = returnDate.split("~");
                                                try {
                                                    if ((DateUtil.compareDate(simpleDateFormat.parse(r.getOrderDate()), simpleDateFormat.parse(date[0])) >= 0 &&
                                                            DateUtil.compareDate(simpleDateFormat.parse(r.getOrderDate()), simpleDateFormat.parse(date[1])) <= 0)
                                                            && r.getShopId().equals(channelId)) {
                                                        r.setSaleNum(saleNum);
                                                    }
                                                } catch (Exception e) {
                                                    e.printStackTrace();
                                                }
                                            } else { //月
                                                if (r.getOrderDate().equals(returnDate) && r.getShopId().equals(channelId)) {
                                                    r.setSaleNum(saleNum);
                                                }
                                            }
                                        }
                                    } else if (new Integer(3).equals(statReturnQueryCopy.getType())) {
                                        String channel = ((JSONObject) j).getString("channel");
                                        for (ReturnAnalyzeVO r : b2CReturnAnalyzeList) {
                                            if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) { //日
                                                if (r.getOrderDate().equals(returnDate) && r.getChannel().equals(channel)) {
                                                    r.setSaleNum(saleNum);
                                                    if (saleNum == null || saleNum.intValue() == 0) {
                                                        r.setReturnRate(new BigDecimal(0));
                                                    } else {
                                                        BigDecimal returnRate = new BigDecimal(r.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                        r.setReturnRate(returnRate.setScale(0));
                                                    }
                                                }
                                            } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) { //周
                                                //todo 判断时间在这个周里面
                                                String date[] = returnDate.split("~");
                                                try {
                                                    if ((DateUtil.compareDate(simpleDateFormat.parse(r.getOrderDate()), simpleDateFormat.parse(date[0])) >= 0 &&
                                                            DateUtil.compareDate(simpleDateFormat.parse(r.getOrderDate()), simpleDateFormat.parse(date[1])) <= 0)
                                                            && r.getChannel().equals(channel)) {
                                                        r.setSaleNum(saleNum);
                                                    }
                                                } catch (Exception e) {
                                                    e.printStackTrace();
                                                }
                                            } else { //月
                                                if (r.getOrderDate().equals(returnDate) && r.getChannel().equals(channel)) {
                                                    r.setSaleNum(saleNum);
                                                }
                                            }
                                        }
                                    } else if (new Integer(5).equals(statReturnQueryCopy.getType())) {
                                        Integer categoryId = ((JSONObject) j).getInteger("categorySecondId");
                                        for (ReturnAnalyzeVO r : b2CReturnAnalyzeList) {
                                            if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) { //日
                                                if (r.getOrderDate().equals(returnDate) && r.getCategoryId().equals(categoryId)) {
                                                    r.setSaleNum(saleNum);
                                                    if (saleNum == null || saleNum.intValue() == 0) {
                                                        r.setReturnRate(new BigDecimal(0));
                                                    } else {
                                                        BigDecimal returnRate = new BigDecimal(r.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                        r.setReturnRate(returnRate.setScale(0));
                                                    }
                                                }
                                            } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) { //周
                                                //todo 判断时间在这个周里面
                                                String date[] = returnDate.split("~");
                                                try {
                                                    if ((DateUtil.compareDate(simpleDateFormat.parse(r.getOrderDate()), simpleDateFormat.parse(date[0])) >= 0 &&
                                                            DateUtil.compareDate(simpleDateFormat.parse(r.getOrderDate()), simpleDateFormat.parse(date[1])) <= 0)
                                                            && r.getCategoryId().equals(categoryId)) {
                                                        r.setSaleNum(saleNum);
                                                    }
                                                } catch (Exception e) {
                                                    e.printStackTrace();
                                                }
                                            } else { //月
                                                if (r.getOrderDate().equals(returnDate) && r.getCategoryId().equals(categoryId)) {
                                                    r.setSaleNum(saleNum);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } else { //这个单独处理退货原因，平台退货原因维度
            if(StringUtils.isNotEmpty(statReturnQuery.getReturnDateStart())) { //退货时间
                b2CReturnAnalyzeList = returnAnalyzeMapper.getB2CReturnReasonAnalyzeDetailListByReturnDate(statReturnQuery);
            } else {
                b2CReturnAnalyzeList = returnAnalyzeMapper.getB2CReturnReasonAnalyzeDetailList(statReturnQuery);
            }
            if (CollectionUtils.isEmpty(b2CReturnAnalyzeList)) {
                return b2CReturnAnalyzeList;
            }
            //还原查询条件
            if(StringUtil.isNotEmpty(statReturnQuery.getAsin())) {
                statReturnQuery.setAsinList(new ArrayList<String>(Arrays.asList(statReturnQuery.getAsin())));
            }
            //还原查询条件
            if(StringUtil.isNotEmpty(statReturnQuery.getSku())) {
                statReturnQuery.setSkuList(new ArrayList<String>(Arrays.asList(statReturnQuery.getSku())));
            }
            if (statReturnQuery.getReturnDateStart() != null && StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) { //区分开时间类型为月份的
                //todo 这一块要怎么去处理 也是利用接口去掉用 让他们处理吧
                statReturnQuery.setApiType("ITEM");
                JSONObject jsonObject = this.sendReturnInfoMessage(statReturnQuery);
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                if (jsonArray != null && jsonArray.size() > 0) {
                    for (Object object : jsonArray) {
                        JSONArray datItemsJsonArray = ((JSONObject) object).getJSONArray("dataItems");
                        for (Object o : datItemsJsonArray) {
                            String returnDate = ((JSONObject) o).getString("returnDate");
                            Integer saleNum = ((JSONObject) o).getInteger("saleNum");
                            for (ReturnAnalyzeVO returnAnalyzeVO : b2CReturnAnalyzeList) {
                                if (returnAnalyzeVO.getReturnDate().equals(returnDate)) {
                                    returnAnalyzeVO.setSaleNum(saleNum);
                                    //计算退货率
                                    if (saleNum == null || saleNum.intValue() == 0) {
                                        returnAnalyzeVO.setReturnRate(new BigDecimal(0));
                                    } else {
                                        BigDecimal returnRate = new BigDecimal(returnAnalyzeVO.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                        returnAnalyzeVO.setReturnRate(returnRate.setScale(0));
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                if (StringUtils.isNotEmpty(statReturnQuery.getReturnDateStart())) { //区分一下订单时间，退货时间
                    if (CollectionUtil.isNotEmpty(b2CReturnAnalyzeList)) {
                        StatReturnQuery statReturnQueryCopy = new StatReturnQuery();
                        BeanUtils.copyProperties(statReturnQuery, statReturnQueryCopy);
                        //todo 调用接口出来
                        statReturnQueryCopy.setApiType("ITEM");
                        JSONObject jsonObject = this.sendReturnInfoNoAvgMessage(statReturnQueryCopy);
                        JSONArray jsonArray = jsonObject.getJSONArray("data");
                        if (jsonArray != null) {
                            //todo 直接匹配，算退货率，然后返回就ok了
                            for (Object j : jsonArray) {
                                JSONArray dataItems = ((JSONObject) j).getJSONArray("dataItems");
                                for (Object k : dataItems) {
                                    String returnDate = ((JSONObject) k).getString("returnDate");
                                    Integer saleNum = ((JSONObject) k).getInteger("saleNum");
                                    for (ReturnAnalyzeVO r : b2CReturnAnalyzeList) {
                                        if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) { //日
                                            if (r.getReturnDate().equals(returnDate)) {
                                                r.setSaleNum(saleNum);
                                                if (saleNum == null || saleNum.intValue() == 0) {
                                                    r.setReturnRate(new BigDecimal(0));
                                                } else {
                                                    BigDecimal returnRate = new BigDecimal(r.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.setReturnRate(returnRate.setScale(0));
                                                }
                                            }
                                        } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) { //周
                                            //todo 判断时间在这个周里面
                                            String date[] = returnDate.split("~");
                                            try {
                                                if ((DateUtil.compareDate(simpleDateFormat.parse(r.getReturnDate()), simpleDateFormat.parse(date[0])) >= 0 &&
                                                        DateUtil.compareDate(simpleDateFormat.parse(r.getReturnDate()), simpleDateFormat.parse(date[1])) <= 0)) {
                                                    r.setSaleNum(saleNum);
                                                }
                                            } catch (Exception e) {
                                                e.printStackTrace();
                                            }
                                        } else { //月
                                            if (r.getReturnDate().equals(returnDate)) {
                                                r.setSaleNum(saleNum);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else { //订单时间
                    if (CollectionUtil.isNotEmpty(b2CReturnAnalyzeList)) {
                        StatReturnQuery statReturnQueryCopy = new StatReturnQuery();
                        BeanUtils.copyProperties(statReturnQuery, statReturnQueryCopy);
                        //todo 调用接口出来
                        statReturnQueryCopy.setApiType("ITEM");
                        JSONObject jsonObject = this.sendReturnInfoNoAvgMessage(statReturnQueryCopy);
                        JSONArray jsonArray = jsonObject.getJSONArray("data");
                        if (jsonArray != null) {
                            //todo 直接匹配，算退货率，然后返回就ok了
                            for (Object j : jsonArray) {
                                JSONArray dataItems = ((JSONObject) j).getJSONArray("dataItems");
                                for (Object k : dataItems) {
                                    String returnDate = ((JSONObject) k).getString("returnDate");
                                    Integer saleNum = ((JSONObject) k).getInteger("saleNum");
                                    for (ReturnAnalyzeVO r : b2CReturnAnalyzeList) {
                                        if (StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) { //日
                                            if (r.getOrderDate().equals(returnDate)) {
                                                r.setSaleNum(saleNum);
                                                if (saleNum == null || saleNum.intValue() == 0) {
                                                    r.setReturnRate(new BigDecimal(0));
                                                } else {
                                                    BigDecimal returnRate = new BigDecimal(r.getReturnNum()).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.setReturnRate(returnRate.setScale(0));
                                                }
                                            }
                                        } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) { //周
                                            //todo 判断时间在这个周里面
                                            String date[] = returnDate.split("~");
                                            try {
                                                if ((DateUtil.compareDate(simpleDateFormat.parse(r.getOrderDate()), simpleDateFormat.parse(date[0])) >= 0 &&
                                                        DateUtil.compareDate(simpleDateFormat.parse(r.getOrderDate()), simpleDateFormat.parse(date[1])) <= 0)) {
                                                    r.setSaleNum(saleNum);
                                                }
                                            } catch (Exception e) {
                                                e.printStackTrace();
                                            }
                                        } else { //月
                                            if (r.getOrderDate().equals(returnDate)) {
                                                r.setSaleNum(saleNum);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        if(StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType())) { //如果是按日则直接返回
            //sku添加型号
            if(CollectionUtil.isNotEmpty(b2CReturnAnalyzeList) && new Integer(1).equals(statReturnQuery.getType())) {
                List<String> skuList = b2CReturnAnalyzeList.stream().map(i -> i.getSku()).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(skuList)) {//获取型号
                    QueryWrapper<Products> productsQueryWrapper = new QueryWrapper<>();
                    productsQueryWrapper.eq("org_id",statReturnQuery.getOrgId());
                    productsQueryWrapper.eq("disabled_at",0);
                    productsQueryWrapper.in("erpsku",skuList);
                    List<Products> productsList = productsService.list(productsQueryWrapper);
                    if(CollectionUtil.isNotEmpty(productsList)) {
                        for(ReturnAnalyzeVO b:b2CReturnAnalyzeList) {
                            for(Products p:productsList) {
                                if(b.getSku().equals(p.getErpsku())) {
                                    b.setProductMidModel(p.getProductMidModel());
                                }
                            }
                        }
                    }
                }
            }
            return b2CReturnAnalyzeList;
        }

        //todo 退货的怎么分组，因为退货的销量都是一样的

        //按照周，月分组
        List<SpiltDateUtil.Range> rangeList = new ArrayList<>();
        if (StringUtils.isNotEmpty(statReturnQuery.getReturnDateStart()) && StringUtils.isNotEmpty(statReturnQuery.getReturnDateEnd())) {//退货时间
            if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) {
                rangeList = SpiltDateUtil.splitToWeeks(statReturnQuery.getReturnDateStart(), statReturnQuery.getReturnDateEnd());
            } else if (StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) {
                rangeList = SpiltDateUtil.splitToMonths(statReturnQuery.getReturnDateStart(), statReturnQuery.getReturnDateEnd());
            }
            List<ReturnAnalyzeVO> resultReturnAnalyzeVOList = new ArrayList<>();
            for (SpiltDateUtil.Range range : rangeList) {
                Map<String, List<ReturnAnalyzeVO>> returnAnalyzeVOMaps = b2CReturnAnalyzeList.stream().filter(returnAnalyzeVO ->
                {
                    if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) {
                        try {
                            return DateUtil.compareDate(simpleDateFormat.parse(returnAnalyzeVO.getReturnDate()), range.getStart()) >= 0 &&
                                    DateUtil.compareDate(simpleDateFormat.parse(returnAnalyzeVO.getReturnDate()), range.getEnd()) <= 0;
                        } catch (ParseException e) {
                            throw new RuntimeException(e);
                        }
                    } else {
                            return returnAnalyzeVO.getReturnDate().equals(monthSimpleDateFormat.format(range.getStart()));
                    }
                }).collect(Collectors.groupingBy(i -> {
                    if (new Integer(0).equals(statReturnQuery.getType())) {
                        return i.getAsin();
                    } else if (new Integer(1).equals(statReturnQuery.getType())) {
                        return i.getSku();
                    } else if (new Integer(2).equals(statReturnQuery.getType())) {
                        return i.getShopId().toString();
                    } else if (new Integer(3).equals(statReturnQuery.getType())) {
                        return i.getChannel();
                    } else if (new Integer(4).equals(statReturnQuery.getType())) {
                        return i.getReturnReasonCategory();
                    } else if (new Integer(5).equals(statReturnQuery.getType())) {
                        return i.getCategoryId().toString();
                    } else if (new Integer(6).equals(statReturnQuery.getType())) {
                        return i.getReturnReason();
                    }
                    return null;
                }));
                for (String key : returnAnalyzeVOMaps.keySet()) {
                    //todo 这一块的聚合还是有问题
                    List<ReturnAnalyzeVO> returnAnalyzeVOS = returnAnalyzeVOMaps.get(key);
                    if (CollectionUtil.isNotEmpty(returnAnalyzeVOS)) {
                        BigDecimal returnNum = returnAnalyzeVOS.stream().map(i -> i.getReturnNum() != null ? new BigDecimal(i.getReturnNum()) : new BigDecimal(0)).reduce(BigDecimal.ZERO, BigDecimal::add);
                        //取第一个就好
                        BigDecimal saleNum = new BigDecimal(0);
                        for (ReturnAnalyzeVO returnAnalyzeVO : returnAnalyzeVOS) {
                            try {
                                if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) {
                                    if (DateUtil.compareDate(simpleDateFormat.parse(returnAnalyzeVO.getReturnDate()), range.getStart()) >= 0 && DateUtil.compareDate(simpleDateFormat.parse(returnAnalyzeVO.getReturnDate()), range.getEnd()) <= 0) {
                                        if (returnAnalyzeVO.getSaleNum() == null) {
                                            saleNum = new BigDecimal(0);
                                        } else {
                                            saleNum = new BigDecimal(returnAnalyzeVO.getSaleNum());
                                        }
                                        break;
                                    }
                                }else {
                                    if (returnAnalyzeVO.getReturnDate().equals(monthSimpleDateFormat.format(range.getStart()))) {
                                        if (returnAnalyzeVO.getSaleNum() == null) {
                                            saleNum = new BigDecimal(0);
                                        } else {
                                            saleNum = new BigDecimal(returnAnalyzeVO.getSaleNum());
                                        }
                                        break;
                                    }
                                }
                            } catch (Exception e) {
                                log.error(e.getMessage());
                            }
                        }

                        BigDecimal returnRate = null;
                        if (saleNum == null || saleNum.intValue() == 0) {
                            returnRate = new BigDecimal(0);
                        } else {
                            returnRate = returnNum.divide(saleNum, 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000)).setScale(0);
                        }
                        ReturnAnalyzeVO returnAnalyzeVO = new ReturnAnalyzeVO();
                        BeanUtils.copyProperties(returnAnalyzeVOS.get(0),returnAnalyzeVO);
                        returnAnalyzeVO.setReturnNum(returnNum.intValue());
                        returnAnalyzeVO.setSaleNum(saleNum.intValue());
                        returnAnalyzeVO.setReturnRate(returnRate);
                        if(StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) {
                            returnAnalyzeVO.setReturnDate(DateUtil.convertDateToString(range.getStart(), "yyyy-MM-dd") + "~" + DateUtil.convertDateToString(range.getEnd(), "yyyy-MM-dd"));
                        } else {
                            returnAnalyzeVO.setReturnDate(DateUtil.convertDateToString(range.getStart(), "yyyy-MM"));
                        }
                        resultReturnAnalyzeVOList.add(returnAnalyzeVO);
                    }
                }
            }
            //sku添加型号
            if(CollectionUtil.isNotEmpty(resultReturnAnalyzeVOList) && new Integer(1).equals(statReturnQuery.getType())) {
                List<String> skuList = resultReturnAnalyzeVOList.stream().map(i -> i.getSku()).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(skuList)) {//获取型号
                    QueryWrapper<Products> productsQueryWrapper = new QueryWrapper<>();
                    productsQueryWrapper.eq("org_id",statReturnQuery.getOrgId());
                    productsQueryWrapper.eq("disabled_at",0);
                    productsQueryWrapper.in("erpsku",skuList);
                    List<Products> productsList = productsService.list(productsQueryWrapper);
                    if(CollectionUtil.isNotEmpty(productsList)) {
                        for(ReturnAnalyzeVO b:resultReturnAnalyzeVOList) {
                            for(Products p:productsList) {
                                if(b.getSku().equals(p.getErpsku())) {
                                    b.setProductMidModel(p.getProductMidModel());
                                }
                            }
                        }
                    }
                }
            }
            return resultReturnAnalyzeVOList;
        } else { //订单时间
            if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) {
                rangeList = SpiltDateUtil.splitToWeeks(statReturnQuery.getOrderDateStart(), statReturnQuery.getOrderDateEnd());
            } else if (StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) {
                rangeList = SpiltDateUtil.splitToMonths(statReturnQuery.getOrderDateStart(), statReturnQuery.getOrderDateEnd());
            }
            List<ReturnAnalyzeVO> resultReturnAnalyzeVOList = new ArrayList<>();
            for (SpiltDateUtil.Range range : rangeList) {
                Map<String, List<ReturnAnalyzeVO>> returnAnalyzeVOMaps = b2CReturnAnalyzeList.stream().filter(returnAnalyzeVO ->
                {
                    if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) {
                        try {
                            return DateUtil.compareDate(simpleDateFormat.parse(returnAnalyzeVO.getOrderDate()), range.getStart()) >= 0 &&
                                    DateUtil.compareDate(simpleDateFormat.parse(returnAnalyzeVO.getOrderDate()), range.getEnd()) <= 0;
                        } catch (ParseException e) {
                            throw new RuntimeException(e);
                        }
                    } else {
                        return returnAnalyzeVO.getOrderDate().equals(monthSimpleDateFormat.format(range.getStart()));
                    }
                }).collect(Collectors.groupingBy(i -> {
                    if (new Integer(0).equals(statReturnQuery.getType())) {
                        return i.getAsin();
                    } else if (new Integer(1).equals(statReturnQuery.getType())) {
                        return i.getSku();
                    } else if (new Integer(2).equals(statReturnQuery.getType())) {
                        return i.getShopId().toString();
                    } else if (new Integer(3).equals(statReturnQuery.getType())) {
                        return i.getChannel();
                    } else if (new Integer(4).equals(statReturnQuery.getType())) {
                        return i.getReturnReasonCategory();
                    } else if (new Integer(5).equals(statReturnQuery.getType())) {
                        return i.getCategoryId().toString();
                    } else if (new Integer(6).equals(statReturnQuery.getType())) {
                        return i.getReturnReason();
                    }
                    return null;
                }));
                for (String key : returnAnalyzeVOMaps.keySet()) {
                    List<ReturnAnalyzeVO> returnAnalyzeVOS = returnAnalyzeVOMaps.get(key);
                    if (CollectionUtil.isNotEmpty(returnAnalyzeVOS)) {
                        BigDecimal returnNum = returnAnalyzeVOS.stream().map(i -> i.getReturnNum() != null ? new BigDecimal(i.getReturnNum()) : new BigDecimal(0)).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal saleNum = new BigDecimal(0);
                        for (ReturnAnalyzeVO returnAnalyzeVO : returnAnalyzeVOS) {
                            try {
                                if (StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) {
                                    if (DateUtil.compareDate(simpleDateFormat.parse(returnAnalyzeVO.getOrderDate()), range.getStart()) >= 0 && DateUtil.compareDate(simpleDateFormat.parse(returnAnalyzeVO.getOrderDate()), range.getEnd()) <= 0) {
                                        if (returnAnalyzeVO.getSaleNum() == null) {
                                            saleNum = new BigDecimal(0);
                                        } else {
                                            saleNum = new BigDecimal(returnAnalyzeVO.getSaleNum());
                                        }
                                        break;
                                    }
                                }else {
                                    if (returnAnalyzeVO.getOrderDate().equals(monthSimpleDateFormat.format(range.getStart()))) {
                                        if (returnAnalyzeVO.getSaleNum() == null) {
                                            saleNum = new BigDecimal(0);
                                        } else {
                                            saleNum = new BigDecimal(returnAnalyzeVO.getSaleNum());
                                        }
                                        break;
                                    }
                                }
                            } catch (Exception e) {
                                log.error(e.getMessage());
                            }
                        }

                        BigDecimal returnRate = null;
                        if (saleNum == null || saleNum.intValue() == 0) {
                            returnRate = new BigDecimal(0);
                        } else {
                            returnRate = returnNum.divide(saleNum, 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000)).setScale(0);
                        }
                        ReturnAnalyzeVO returnAnalyzeVO = new ReturnAnalyzeVO();
                        BeanUtils.copyProperties(returnAnalyzeVOS.get(0),returnAnalyzeVO);
                        returnAnalyzeVO.setReturnNum(returnNum.intValue());
                        returnAnalyzeVO.setSaleNum(saleNum.intValue());
                        returnAnalyzeVO.setReturnRate(returnRate);
                        if(StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType())) {
                            returnAnalyzeVO.setOrderDate(DateUtil.convertDateToString(range.getStart(), "yyyy-MM-dd") + "~" + DateUtil.convertDateToString(range.getEnd(), "yyyy-MM-dd"));
                        } else {
                            returnAnalyzeVO.setOrderDate(DateUtil.convertDateToString(range.getStart(), "yyyy-MM"));
                        }
                        resultReturnAnalyzeVOList.add(returnAnalyzeVO);
                    }
                }
            }

            //sku添加型号
            if(CollectionUtil.isNotEmpty(resultReturnAnalyzeVOList) && new Integer(1).equals(statReturnQuery.getType())) {
                List<String> skuList = resultReturnAnalyzeVOList.stream().map(i -> i.getSku()).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(skuList)) {//获取型号
                    QueryWrapper<Products> productsQueryWrapper = new QueryWrapper<>();
                    productsQueryWrapper.eq("org_id",statReturnQuery.getOrgId());
                    productsQueryWrapper.eq("disabled_at",0);
                    productsQueryWrapper.in("erpsku",skuList);
                    List<Products> productsList = productsService.list(productsQueryWrapper);
                    if(CollectionUtil.isNotEmpty(productsList)) {
                        for(ReturnAnalyzeVO b:resultReturnAnalyzeVOList) {
                            for(Products p:productsList) {
                                if(b.getSku().equals(p.getErpsku())) {
                                    b.setProductMidModel(p.getProductMidModel());
                                }
                            }
                        }
                    }
                }
            }
            return resultReturnAnalyzeVOList;
        }
    }

    /**
     * Description: 总计
     *
     * @param statReturnQuery
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/19
     */
    @Override
    public ReturnAnalyzeVO getB2CReturnAnalyzePageListSum(StatReturnQuery statReturnQuery) {
        statReturnQuery.setApiType("SUM");
        //ASIN 模糊
        if (!CollectionUtils.isEmpty(statReturnQuery.getAsinList()) && statReturnQuery.getAsinList().size() == 1) {
            statReturnQuery.setAsin(statReturnQuery.getAsinList().get(0));
            statReturnQuery.setAsinList(null);
        }

        //SKU模糊
        if (!CollectionUtils.isEmpty(statReturnQuery.getSkuList()) && statReturnQuery.getSkuList().size() == 1) {
            statReturnQuery.setSku(statReturnQuery.getSkuList().get(0));
            statReturnQuery.setSkuList(null);
        }
        StatReturnQuery orgStatReturnQuery = new StatReturnQuery();
        BeanUtils.copyProperties(statReturnQuery,orgStatReturnQuery);
        if(!new Integer(4).equals(statReturnQuery.getType()) && !new Integer(6).equals(statReturnQuery.getType()) ) {//不是退货原因维度
            if(StringUtils.isNotEmpty(statReturnQuery.getReturnDateStart())  && StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) { //区分开时间类型为月份的
                statReturnQuery.setIfSalesQuantity("false");
                ReturnAnalyzeVO  returnAnalyzeVO = returnAnalyzeMapper.getB2CReturnAnalyzeListSumPageByReturnDate(statReturnQuery);
                if(returnAnalyzeVO == null) {
                    return null;
                }
                StatReturnQuery query = new StatReturnQuery();
                BeanUtils.copyProperties(orgStatReturnQuery,query);
                //设置还原查询条件
                if(StringUtil.isNotEmpty(query.getAsin())) {
                    List<String> asinOne = new ArrayList<>();
                    asinOne.add(query.getAsin());
                    query.setAsinList(asinOne);
                }
                if(StringUtil.isNotEmpty(query.getSku())) {
                    List<String> skuOne = new ArrayList<>();
                    skuOne.add(query.getSku());
                    query.setSkuList(skuOne);
                }
                JSONObject jsonObject = this.sendReturnInfoMessage(query);
                Integer saleNumSum = jsonObject.getInteger("saleNumSum");
                if (saleNumSum == null || saleNumSum.intValue() == 0) {
                    returnAnalyzeVO.setReturnRate(new BigDecimal(0));
                    returnAnalyzeVO.setSaleNum(new Integer(0));
                } else {
                    BigDecimal returnRate = new BigDecimal(returnAnalyzeVO.getReturnNum()).divide(new BigDecimal(saleNumSum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                    returnAnalyzeVO.setReturnRate(returnRate.setScale(0));
                    returnAnalyzeVO.setSaleNum(saleNumSum);
                }
                //设置合计明细
                List<ReturnAnalyzeVO> children = jsonObject.getJSONArray("saleNumSumItem").toJavaList(ReturnAnalyzeVO.class);
                if(CollectionUtil.isNotEmpty(children)) {
                    //查询合计明细
                    List<ReturnAnalyzeVO> returnAnalyzeVODetailList = returnAnalyzeMapper.getB2CReturnAnalyzeListSumDetailMonth(statReturnQuery);
                    //匹配合计明细
                    if(CollectionUtil.isNotEmpty(returnAnalyzeVODetailList)) {
                        returnAnalyzeVODetailList.stream().forEach(i -> {
                            children.stream().forEach(j -> {
                                if(j.getReturnDate().equals(i.getReturnDate())) {
                                    Integer saleNum = j.getSaleNum();
                                    i.setSaleNum(j.getSaleNum());
                                    BigDecimal returnNum = new BigDecimal(0);
                                    if(i.getReturnNum()!= null) {
                                        returnNum = new BigDecimal(i.getReturnNum());
                                    }
                                    BigDecimal returnRate = null;
                                    if (saleNum == null || saleNum.intValue() == 0) {
                                        returnRate = new BigDecimal(0);
                                    } else {
                                        returnRate = returnNum.divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000)).setScale(2, ROUND_HALF_UP);
                                    }
                                    i.setReturnRate(returnRate);
                                }
                            });
                        });
                        returnAnalyzeVO.setChildTimeRetuns(returnAnalyzeVODetailList);
                    }
                }
                returnAnalyzeVO.setAsin(null);
                returnAnalyzeVO.setSku(null);
                returnAnalyzeVO.setShopId(null);
                returnAnalyzeVO.setChannel(null);
                returnAnalyzeVO.setCategoryId(null);
                return returnAnalyzeVO;
            } else {
                ReturnAnalyzeVO returnAnalyzeVO = null;
                if (StringUtils.isNotEmpty(statReturnQuery.getReturnDateStart())) { //退货时间
                    returnAnalyzeVO = returnAnalyzeMapper.getB2CReturnAnalyzeListSumPageByReturnDate(statReturnQuery);
                } else {
                    returnAnalyzeVO = returnAnalyzeMapper.getB2CReturnAnalyzeListSumPage(statReturnQuery);
                }
                if(returnAnalyzeVO == null) {
                    return null;
                }
                if (StringUtils.isNotEmpty(statReturnQuery.getReturnDateStart())) { //退货时间
                        //todo 调用接口出来
                        StatReturnQuery statReturnQueryCopy = new StatReturnQuery();
                        BeanUtils.copyProperties(statReturnQuery, statReturnQueryCopy);
                        statReturnQueryCopy.setApiType("SUM");
                        JSONObject jsonObject = this.sendReturnInfoNoAvgMessage(statReturnQueryCopy);
                        Integer saleNumSum = jsonObject.getInteger("saleNumSum");
                        if (saleNumSum == null || saleNumSum.intValue() == 0) {
                            returnAnalyzeVO.setReturnRate(new BigDecimal(0));
                            returnAnalyzeVO.setSaleNum(new Integer(0));
                        } else {
                            BigDecimal returnRate = new BigDecimal(returnAnalyzeVO.getReturnNum()).divide(new BigDecimal(saleNumSum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                            returnAnalyzeVO.setReturnRate(returnRate.setScale(0));
                            returnAnalyzeVO.setSaleNum(saleNumSum);
                        }
                        returnAnalyzeVO.setAsin(null);
                        returnAnalyzeVO.setSku(null);
                        returnAnalyzeVO.setShopId(null);
                        returnAnalyzeVO.setChannel(null);
                        returnAnalyzeVO.setCategoryId(null);
                } else {// 订单时间
                    //设置查询条件
                    if(StringUtil.isNotEmpty(statReturnQuery.getAsin())) {
                        List<String> asinOne = new ArrayList<>();
                        asinOne.add(statReturnQuery.getAsin());
                        statReturnQuery.setAsinList(asinOne);
                    }
                    if(StringUtil.isNotEmpty(statReturnQuery.getSku())) {
                        List<String> skuOne = new ArrayList<>();
                        skuOne.add(statReturnQuery.getSku());
                        statReturnQuery.setSkuList(skuOne);
                    }
                    //todo 调用接口出来
                    StatReturnQuery query = new StatReturnQuery();
                    BeanUtils.copyProperties(orgStatReturnQuery,query);
                    query.setApiType("SUM");
                    //设置还原查询条件
                    if(StringUtil.isNotEmpty(query.getAsin())) {
                        List<String> asinOne = new ArrayList<>();
                        asinOne.add(query.getAsin());
                        query.setAsinList(asinOne);
                    }
                    if(StringUtil.isNotEmpty(query.getSku())) {
                        List<String> skuOne = new ArrayList<>();
                        skuOne.add(query.getSku());
                        query.setSkuList(skuOne);
                    }
                    JSONObject jsonObject = this.sendReturnInfoNoAvgMessage(query);
                    Integer saleNumSum = jsonObject.getInteger("saleNumSum");
                    if (saleNumSum == null || saleNumSum.intValue() == 0) {
                        returnAnalyzeVO.setReturnRate(new BigDecimal(0));
                        returnAnalyzeVO.setSaleNum(new Integer(0));
                    } else {
                        BigDecimal returnRate = new BigDecimal(returnAnalyzeVO.getReturnNum()).divide(new BigDecimal(saleNumSum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                        returnAnalyzeVO.setReturnRate(returnRate.setScale(0));
                        returnAnalyzeVO.setSaleNum(saleNumSum);
                    }
                }
                return returnAnalyzeVO;
            }
        }else { //这个单独处理退货原因,平台退货原因维度
            ReturnAnalyzeVO returnAnalyzeVO = null;
            if(StringUtils.isNotEmpty(statReturnQuery.getReturnDateStart())) { //退货时间
                 returnAnalyzeVO = returnAnalyzeMapper.getB2CReturnReasonAnalyzeListSumByReturnDate(statReturnQuery);
            } else {
                 returnAnalyzeVO = returnAnalyzeMapper.getB2CReturnReasonAnalyzeListSum(statReturnQuery);
            }
            //returnAnalyzeVO = ObjectUtils.isEmpty(returnAnalyzeVO) ? new ReturnAnalyzeVO() : returnAnalyzeVO;
            if(returnAnalyzeVO == null) {
                return null;
            }
            if(statReturnQuery.getReturnDateStart() != null && StatConstant.STAT_DATA_TYPE_MONTH.equals(statReturnQuery.getDateType())) { //区分开时间类型为月份的
                //设置查询条件
                if(StringUtil.isNotEmpty(statReturnQuery.getAsin())) {
                    List<String> asinOne = new ArrayList<>();
                    asinOne.add(statReturnQuery.getAsin());
                    statReturnQuery.setAsinList(asinOne);
                }
                if(StringUtil.isNotEmpty(statReturnQuery.getSku())) {
                    List<String> skuOne = new ArrayList<>();
                    skuOne.add(statReturnQuery.getSku());
                    statReturnQuery.setSkuList(skuOne);
                }

                //todo 这个要单独处理了。也是调用外部接口
                JSONObject jsonObject = this.sendReturnInfoMessage(statReturnQuery);
                Integer saleNumSum = jsonObject.getInteger("saleNumSum");
                if (saleNumSum == null || saleNumSum.intValue() == 0) {
                    returnAnalyzeVO.setReturnRate(new BigDecimal(0));
                    returnAnalyzeVO.setSaleNum(new Integer(0));
                } else {
                    BigDecimal returnRate = new BigDecimal(returnAnalyzeVO.getReturnNum()).divide(new BigDecimal(saleNumSum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                    returnAnalyzeVO.setReturnRate(returnRate.setScale(0));
                    returnAnalyzeVO.setSaleNum(saleNumSum);
                }
                List<ReturnAnalyzeVO> children = jsonObject.getJSONArray("saleNumSumItem").toJavaList(ReturnAnalyzeVO.class);
                if(CollectionUtil.isNotEmpty(children)) {
                    //ASIN 模糊
                    if (!CollectionUtils.isEmpty(statReturnQuery.getAsinList()) && statReturnQuery.getAsinList().size() == 1) {
                        statReturnQuery.setAsin(statReturnQuery.getAsinList().get(0));
                        statReturnQuery.setAsinList(null);
                    }

                    //SKU模糊
                    if (!CollectionUtils.isEmpty(statReturnQuery.getSkuList()) && statReturnQuery.getSkuList().size() == 1) {
                        statReturnQuery.setSku(statReturnQuery.getSkuList().get(0));
                        statReturnQuery.setSkuList(null);
                    }
                    List<ReturnAnalyzeVO> returnAnalyzeVODetailList = returnAnalyzeMapper.getB2CReturnAnalyzeListSumDetailMonth(statReturnQuery);
                    //匹配合计明细
                    if(CollectionUtil.isNotEmpty(returnAnalyzeVODetailList)) {
                        returnAnalyzeVODetailList.stream().forEach(i -> {
                            children.stream().forEach(j -> {
                                if(j.getReturnDate().equals(i.getOrderDate())) {
                                    Integer saleNum = j.getSaleNum();
                                    i.setSaleNum(j.getSaleNum());
                                    BigDecimal returnNum = new BigDecimal(0);
                                    if(i.getReturnNum()!= null) {
                                        returnNum = new BigDecimal(i.getReturnNum());
                                    }
                                    BigDecimal returnRate = null;
                                    if (saleNum == null || saleNum.intValue() == 0) {
                                        returnRate = new BigDecimal(0);
                                    } else {
                                        returnRate = returnNum.divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000)).setScale(0);
                                    }
                                    i.setReturnRate(returnRate);
                                }
                            });
                        });
                        returnAnalyzeVO.setChildTimeRetuns(returnAnalyzeVODetailList);
                    }
                }
                return returnAnalyzeVO;
            }else {
                //设置查询条件
                if(StringUtil.isNotEmpty(statReturnQuery.getAsin())) {
                    List<String> asinOne = new ArrayList<>();
                    asinOne.add(statReturnQuery.getAsin());
                    statReturnQuery.setAsinList(asinOne);
                }
                if(StringUtil.isNotEmpty(statReturnQuery.getSku())) {
                    List<String> skuOne = new ArrayList<>();
                    skuOne.add(statReturnQuery.getSku());
                    statReturnQuery.setSkuList(skuOne);
                }
                //todo 调用接口出来
                StatReturnQuery statReturnQueryCopy = new StatReturnQuery();
                BeanUtils.copyProperties(statReturnQuery, statReturnQueryCopy);
                statReturnQueryCopy.setApiType("SUM");
                JSONObject jsonObject = this.sendReturnInfoNoAvgMessage(statReturnQueryCopy);
                Integer saleNumSum = jsonObject.getInteger("saleNumSum");
                if (saleNumSum == null || saleNumSum.intValue() == 0) {
                    returnAnalyzeVO.setReturnRate(new BigDecimal(0));
                    returnAnalyzeVO.setSaleNum(new Integer(0));
                } else {
                    BigDecimal returnRate = new BigDecimal(returnAnalyzeVO.getReturnNum()).divide(new BigDecimal(saleNumSum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                    returnAnalyzeVO.setReturnRate(returnRate.setScale(0));
                    returnAnalyzeVO.setSaleNum(saleNumSum);
                }

            }
            return returnAnalyzeVO;
        }

    }



    /**
     * Description: 退货分析销量统计
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/21
     */
    public JSONObject sendReturnInfoMessage(StatReturnQuery statReturnQuery) {
        HashMap<String, Object> reqParm = new LinkedHashMap<>();
        reqParm.put("orgId", statReturnQuery.getOrgId());
        reqParm.put("channelList", statReturnQuery.getChannelList());
        reqParm.put("shopIdList",statReturnQuery.getShopIdList());
        reqParm.put("asinList",statReturnQuery.getAsinList());
        reqParm.put("skuList",statReturnQuery.getSkuList());
        reqParm.put("categoryIdList",statReturnQuery.getCategoryIdList());
        reqParm.put("returnDateStart",statReturnQuery.getReturnDateStart());
        reqParm.put("returnDateEnd",statReturnQuery.getReturnDateEnd());
        reqParm.put("apiType",statReturnQuery.getApiType());//SUM：汇总业务 ，ITEM:为明细行业务
        reqParm.put("operateIdList",statReturnQuery.getOperateIdList());
        if(new Integer(6).equals(statReturnQuery.getType())) { //退货原因和平台退货原因做一样的处理。
            reqParm.put("Type", new Integer(4));
        }else{
            reqParm.put("Type", statReturnQuery.getType()); //运营Type0：asin 1:sku 2：店铺 3：渠道  5:品类
        }
//        String url = "http://172.28.193.50:5555/api/v1/MovingAvgSale";
        String url = queryReturnUrl + "/api/v1/MovingAvgSale";
        HttpRequest request = HttpUtil.createPost(url);
        request.body(JSON.toJSONString(reqParm));
        log.error("退货分析销量统计参数MovingAvgSale:{}",JSON.toJSONString(reqParm));
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            throw new IllegalArgumentException("查询退货分析销量统计异常");
        }
        log.error("退货分析销量统计MovingAvgSale返回结果:{}",JSONObject.parseObject(response.body()));
        return JSONObject.parseObject(response.body());
    }

    /**
     * Description: 退货分析销量统计折线图
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/21
     */
    public JSONObject sendReturnInfoLineChartMessage(StatReturnQuery statReturnQuery) {
        HashMap<String, Object> reqParm = new LinkedHashMap<>();
        reqParm.put("orgId", statReturnQuery.getOrgId());
        reqParm.put("channelList", statReturnQuery.getChannelList());
        reqParm.put("shopIdList",statReturnQuery.getShopIdList());
        reqParm.put("asinList",statReturnQuery.getAsinList());
        reqParm.put("skuList",statReturnQuery.getSkuList());
        reqParm.put("categoryIdList",statReturnQuery.getCategoryIdList());
        if(StringUtils.isNotEmpty(statReturnQuery.getReturnDateStart())) {
            reqParm.put("returnDateStart", statReturnQuery.getReturnDateStart());
            reqParm.put("returnDateEnd", statReturnQuery.getReturnDateEnd());
        } else {
            reqParm.put("returnDateStart", statReturnQuery.getOrderDateStart());
            reqParm.put("returnDateEnd", statReturnQuery.getOrderDateEnd());
        }
        reqParm.put("operateIdList",statReturnQuery.getOperateIdList());
        reqParm.put("avgFlag",statReturnQuery.getAvgFlag()); //是否计算月平均值 :是：Y，否：N
        reqParm.put("dateType",statReturnQuery.getDateType());
//        String url = "http://172.28.193.50:5555/api/v1/ReturnAnalysisTrandSales";
        String url = queryReturnUrl + "/api/v1/ReturnAnalysisTrandSales";
        HttpRequest request = HttpUtil.createPost(url);
        request.body(JSON.toJSONString(reqParm));
        log.error("退货分析销量统计参数ReturnAnalysisTrandSales:{}",JSON.toJSONString(reqParm));
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            throw new IllegalArgumentException("查询退货分析销量统计折线图异常");
        }
        log.error("退货分析销量统计ReturnAnalysisTrandSales返回结果:{}",JSONObject.parseObject(response.body()));
        return JSONObject.parseObject(response.body());
    }

    /**
     * Description: 退货分析销量统计不平均接口
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/21
     */
    public JSONObject sendReturnInfoNoAvgMessage(StatReturnQuery statReturnQuery) {
        HashMap<String, Object> reqParm = new LinkedHashMap<>();
        reqParm.put("orgId", statReturnQuery.getOrgId());
        reqParm.put("channelList", statReturnQuery.getChannelList());
        reqParm.put("shopIdList",statReturnQuery.getShopIdList());
        reqParm.put("asinList",statReturnQuery.getAsinList());
        reqParm.put("skuList",statReturnQuery.getSkuList());
        reqParm.put("categoryIdList",statReturnQuery.getCategoryIdList());
        if(StringUtils.isNotEmpty(statReturnQuery.getReturnDateStart())) {
            reqParm.put("returnDateStart", statReturnQuery.getReturnDateStart());
            reqParm.put("returnDateEnd", statReturnQuery.getReturnDateEnd());
        } else {
            reqParm.put("returnDateStart", statReturnQuery.getOrderDateStart());
            reqParm.put("returnDateEnd", statReturnQuery.getOrderDateEnd());
        }

        reqParm.put("apiType",statReturnQuery.getApiType());//SUM：汇总业务 ，ITEM:为明细行业务
        reqParm.put("operateIdList",statReturnQuery.getOperateIdList());
        if(new Integer(6).equals(statReturnQuery.getType())) { //退货原因和平台退货原因做一样的处理。
            reqParm.put("Type", new Integer(4));
        }else {
            reqParm.put("Type", statReturnQuery.getType());
        }
        reqParm.put("dateType",statReturnQuery.getDateType());
//        String url = "http://172.28.193.50:5555/api/v1/ReturnAnalysisSales";
        String url = queryReturnUrl + "/api/v1/ReturnAnalysisSales";
        HttpRequest request = HttpUtil.createPost(url);
        request.body(JSON.toJSONString(reqParm));
        log.error("退货分析销量统计参数ReturnAnalysisSales:{}",JSON.toJSONString(reqParm));
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            throw new IllegalArgumentException("查询退货分析销量统计不平均接口异常");
        }
        log.error("退货分析销量统计ReturnAnalysisSales返回结果:{}",JSONObject.parseObject(response.body()));
        return JSONObject.parseObject(response.body());
    }


    /**
     * Description: B2C任务导出
     *
     * @param response
     * @param statReturnQuery
     * @param authUserEntity
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/22
     */
    @Override
    public void listExportB2CListDetail(HttpServletResponse response, StatReturnQuery statReturnQuery, UserEntity authUserEntity) {
        TaskCenterRequest request = new TaskCenterRequest();
        request.setTaskCode("erp.listExportB2CListDetail.export");
        List<Object> list = new ArrayList<>();
        list.add(JSON.toJSONString(statReturnQuery));
        request.setArgs(list);
        if (statReturnQuery.getOrgId() != null) {
            request.setOrgId(statReturnQuery.getOrgId().intValue());
        }
        this.taskCenterService.startTask(request, authUserEntity);
        AssertUtil.isFail(FinanceErrorEnum.ASNYC_EXPORT_GENERATED);
    }


}
