package com.bizark.op.service.service.promotions;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bizark.common.contract.AuthUserDetails;
import com.bizark.op.api.cons.MQDefine;
import com.bizark.op.api.enm.promotions.*;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.promotions.*;
import com.bizark.op.api.exception.ErpCommonException;
import com.bizark.op.api.request.msg.his.AmzPromotionsHisPromotionsListReceiveMsg;
import com.bizark.op.api.request.msg.his.AmzPromotionsHisPromotionsReceiveMsg;
import com.bizark.op.api.request.msg.his.AmzPromotionsHisPromotionsSkuReceiveMsg;
import com.bizark.op.api.request.msg.receive.AmzCouponCaptureReturnMsg;
import com.bizark.op.api.request.msg.receive.AmzPromotionCaptureReturnMsg;
import com.bizark.op.api.request.msg.sync.AmzPromotionsSyncCouponsListReceiveMsg;
import com.bizark.op.api.request.msg.sync.AmzPromotionsSyncCouponsReceiveMsg;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.promotions.*;
import com.bizark.op.api.service.promotions.fee.IAmzPromotionFeeService;
import com.bizark.op.api.vo.promotions.*;
import com.bizark.op.common.core.domain.BaseEntity;
import com.bizark.op.common.util.*;
import com.bizark.op.service.annotation.AmzPromotionVerify;
import com.bizark.op.service.mapper.promotions.*;
import com.bizark.framework.security.AuthContextHolder;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @remarks:手动同步promotion,coupon
 * @Author: Ailill
 * @Date: 2023/11/13 18:25
 */
@Slf4j
@Service
public class AmzPromotionAndCouponServiceImpl implements IAmzPromotionAndCouponService {


    @Autowired
    private AmzPromotionsCouponsMapper amzPromotionsCouponsMapper;

    @Autowired
    private AmzPromotionsCouponMapper amzPromotionsCouponMapper;

    @Autowired
    private AmzPromotionsMapper amzPromotionsMapper;

    @Autowired
    private AmzPromotionsSyncMapper amzPromotionsSyncMapper;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private IAmzPromotionsSkuDetailService iAmzPromotionsSkuDetailService;

    @Autowired
    private IAmzPromotionsService iAmzPromotionsService;

    @Autowired
    private AmzPromotionsDailyDetailMapper amzPromotionsDailyDetailMapper;

    @Autowired
    private IAmzPromotionsCouponOperateLogService iAmzPromotionsCouponOperateLogService;

    @Autowired
    private IAmzPromotionApprovalAdviceService iAmzPromotionApprovalAdviceService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private AmzPromotionAccountMapper amzPromotionAccountMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private IAmzPromotionsOperateLogService iAmzPromotionsOperateLogService;

    @Autowired
    @Lazy
    private IAmzPromotionsCouponService iAmzPromotionsCouponService;

    @Autowired
    @Lazy
    private IAmzPromotionsCouponsService iAmzPromotionsCouponsService;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private IAmzPromotionFileService amzPromotionFileService;

    @Autowired
    @Lazy
    private IAmzPromotionFeeService amzPromotionFeeService;


    /**
     * 获取待同步的coupon
     *
     * @param couponsIds 活动id
     * @return
     */
    @Override
    public List<AmzPromotionsCoupons> getSyncCouponList(Long[] couponsIds) {

        int length = couponsIds.length;
        if (length == 1) {
            AmzPromotionsCoupon coupon = new AmzPromotionsCoupon();
            coupon.setCouponId(couponsIds[0]);
            List<AmzPromotionsCoupon> list = amzPromotionsCouponMapper.selectAmzPromotionsCouponList(coupon);
            Integer[] states = {AmzCouponStateEnum.SUBMITTING.value(), AmzCouponStateEnum.MODIFYING.value(), AmzCouponStateEnum.CANCELING.value()};
            long count = list.stream().filter(r -> Arrays.asList(states).contains(r.getCouponState()) || (r.getCouponState() == null && !AmzPromotionApprovalEnum.APPROVED.getLabel().equalsIgnoreCase(r.getApprovalStatus())) || AmzPromotionApprovalEnum.APPROVING.getLabel().equalsIgnoreCase(r.getApprovalStatus())).count();
            AssertUtil.isFalse(count > 0, "所选数据在处理中不支持同步");
        }
        List<AmzPromotionsCouponsSync> list = amzPromotionsSyncMapper.getAmzPromotionsCouponsList(couponsIds);
        AssertUtil.isFalse(CollectionUtil.isEmpty(list), "无符合条件的coupon");
        List<Long> collect = list.stream().map(AmzPromotionsCouponsSync::getCampaignId).distinct().collect(Collectors.toList());
        return amzPromotionsCouponsMapper.selectAmzPromotionsCouponsByIds(collect.toArray(new Long[collect.size()]));
    }

    /**
     * 获取待同步的promotion
     *
     * @param syncIds
     * @return
     */
    @AmzPromotionVerify
    @Override
    public List<AmzPromotions> getSyncPromotions(Long[] syncIds) {
        int length = syncIds.length;
        if (length > 1) {
            List<AmzPromotions> syncList = amzPromotionsMapper.selectBatchIds(Arrays.asList(syncIds));
            long count = syncList.stream().filter(r ->
                    r.getPromotionsState() == null
                    || r.getPromotionsState().equals(AmzPromotionsStateEnum.SUBMIT_EXCEPTION.value())
                    || r.getPromotionsState().equals(AmzPromotionsStateEnum.SUBMIT_FAIL.value())).count();
            if (count > 1) {
                throw new ErpCommonException("无promotion id的数据，只能同步一条");
            }
        }
        if (length == 1) {
            AmzPromotions promotions = amzPromotionsMapper.selectById(syncIds[0]);
            Integer[] states = {AmzPromotionsStateEnum.SUBMITTING.value(), AmzPromotionsStateEnum.MODIFYING.value(), AmzPromotionsStateEnum.CANCELING.value()};
            if (Arrays.asList(states).contains(promotions.getPromotionsState()) || (promotions.getPromotionsState() == null && !AmzPromotionApprovalEnum.APPROVED.getLabel().equalsIgnoreCase(promotions.getApprovalStatus())) || AmzPromotionApprovalEnum.APPROVING.getLabel().equalsIgnoreCase(promotions.getApprovalStatus())) {
                throw new ErpCommonException("所选数据在处理中不支持同步");
            }
        }
        List<AmzPromotionsSync> list = amzPromotionsSyncMapper.getAmzPromotionsList(syncIds);
        AssertUtil.isFalse(CollectionUtil.isEmpty(list), "无符合条件的promotion");
        List<Long> collect = list.stream().map(AmzPromotionsSync::getId).collect(Collectors.toList());
        return amzPromotionsMapper.selectBatchIds(collect);
    }

    /**
     * 同步coupon
     *
     * @param amzPromotionsCoupons
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AmzPromotionsCouponsSyncListVo> syncCouponsList(List<AmzPromotionsCoupons> amzPromotionsCoupons, String name) {

        AssertUtil.isFalse(CollectionUtil.isEmpty(amzPromotionsCoupons), "请填写相关信息");
        amzPromotionsCoupons.forEach(e -> {
            if (e.getId() == null || StringUtil.isEmpty(e.getCampaignId())) {
                throw new ErpCommonException("id或者后台不允许为空");
            } else {
                //字母加数字组成，>=11位
                String pattern = "^(?=.*[A-Z])(?=.*\\d)(?!^[A-Z]+$)(?!^\\d+$)[A-Z\\d]{11,}$";
                if (!e.getCampaignId().matches(pattern)) {
                    throw new ErpCommonException("请填写正确的后台活动ID（后台活动详情链接上的ID）");
                }
                e.setCampaignId(e.getCampaignId().trim());
            }
        });
        //判断amzPromotionsCoupons集合里的campaignId是否重复
        List<String> campaignIdList = amzPromotionsCoupons.stream().map(AmzPromotionsCoupons::getCampaignId).collect(Collectors.toList());
        Set<String> campaignIds = new HashSet<>();
        for (String campaignId : campaignIdList) {
            if (campaignIds.contains(campaignId)) {
                throw new ErpCommonException(String.format("campaignId:%s 重复", campaignId));
            }
            campaignIds.add(campaignId);
            //判断campaignId在库里是否已有
            AmzPromotionsCoupons tempCoupons = new AmzPromotionsCoupons();
            tempCoupons.setCampaignId(campaignId);
            List<AmzPromotionsCoupons> couponsList = amzPromotionsCouponsMapper.selectAmzPromotionsCouponsList(tempCoupons);
            if (CollectionUtil.isNotEmpty(couponsList)) {
                List<Long> collectId = amzPromotionsCoupons.stream().map(AmzPromotionsCoupons::getId).collect(Collectors.toList());
                long repeat = couponsList.stream().filter(r -> !collectId.contains(r.getId())).count();
                if (repeat > 0) {
                    throw new ErpCommonException(String.format("campaignId:%s 与库里已有活动的后台ID重复", campaignId));
                }
            }
        }

        updateCouponsBackId(amzPromotionsCoupons);
        List<Long> collect = amzPromotionsCoupons.stream().map(AmzPromotionsCoupons::getId).collect(Collectors.toList());
        List<AmzPromotionsCouponsSync> list = amzPromotionsSyncMapper.getAmzPromotionsCouponsList(collect.toArray(new Long[collect.size()]));
        List<AmzPromotionsCouponsSyncListVo> result = getSyncCouponsList(list, amzPromotionsCoupons);
        JSONArray objects = JSONArray.parseArray(JSONArray.toJSONString(result));
        log.info("同步发起coupon消息:{}", objects.toJSONString());
        rabbitTemplate.convertAndSend(MQDefine.AMZ_PROMOTIONS_COUPONS_SYNC_SEND, objects);
        List<Long> idList = list.stream().map(AmzPromotionsCouponsSync::getCampaignId).distinct().collect(Collectors.toList());
        addCouponSyncInitLogWithName(idList.toArray(new Long[idList.size()]), getOrgId(), null, AmzCouponOperateTypeEnum.SYNC_INIT.value(), name);
        return result;
    }

    @Transactional
    @Override
    @Async("threadPoolTaskExecutor")
    public List<AmzPromotionsCouponsSyncListVo> syncCouponsListInterface(List<AmzPromotionsCoupons> amzPromotionsCoupons) {

        try {
            Thread.sleep(60000L);
        } catch (Exception e) {
            log.error("coupon同步sleep error", e);
        }
        AssertUtil.isFalse(CollectionUtil.isEmpty(amzPromotionsCoupons), "请填写相关信息");
        amzPromotionsCoupons.forEach(e -> {
            if (e.getId() == null || StringUtil.isEmpty(e.getCampaignId())) {
                throw new ErpCommonException("id或者后台不允许为空");
            } else {
                e.setCampaignId(e.getCampaignId().trim());
            }
        });
        updateCouponsBackId(amzPromotionsCoupons);
        List<Long> collect = amzPromotionsCoupons.stream().map(AmzPromotionsCoupons::getId).collect(Collectors.toList());
        List<AmzPromotionsCouponsSync> list = amzPromotionsSyncMapper.getAmzPromotionsCouponsList(collect.toArray(new Long[collect.size()]));
        List<AmzPromotionsCouponsSyncListVo> result = getSyncCouponsList(list, amzPromotionsCoupons);
        JSONArray objects = JSONArray.parseArray(JSONArray.toJSONString(result));
        rabbitTemplate.convertAndSend(MQDefine.AMZ_PROMOTIONS_COUPONS_SYNC_SEND, objects);
        List<Long> idList = list.stream().map(AmzPromotionsCouponsSync::getCampaignId).distinct().collect(Collectors.toList());
        addCouponSyncInitLog(idList.toArray(new Long[idList.size()]), 1000049, "接口同步推送coupon", AmzCouponOperateTypeEnum.SYNC_INIT.value());
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateCouponsBackId(List<AmzPromotionsCoupons> amzPromotionsCoupons) {

        for (AmzPromotionsCoupons amzPromotionsCoupon : amzPromotionsCoupons) {
            AmzPromotionsCoupons coupons = new AmzPromotionsCoupons();
            coupons.setId(amzPromotionsCoupon.getId());
            coupons.setCampaignId(amzPromotionsCoupon.getCampaignId());
            coupons.setSubShopBack(true);
            amzPromotionsCouponsMapper.updateAmzPromotionsCoupons(coupons);
        }
    }

    /**
     * 同步promotion
     *
     * @param promotionsList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AccountWithPromotionsList> syncPromotions(List<AmzPromotions> promotionsList, String name) {
        AssertUtil.isFalse(CollectionUtil.isEmpty(promotionsList), "请填写相关信息");
        promotionsList.forEach(r -> {
            if (r.getId() == null || StringUtil.isEmpty(r.getPromotionsId())) {
                throw new ErpCommonException("id或promotionID不允许为空");
            } else {
                String pattern = "^.{10,}$";
                if (!r.getPromotionsId().matches(pattern)) {
                    throw new ErpCommonException("PromotionID为大于等于10位");
                }
                r.setPromotionsId(r.getPromotionsId().trim());
            }
        });

        List<String> pList = promotionsList.stream().map(AmzPromotions::getPromotionsId).collect(Collectors.toList());
        Set<String> promotionsIdSet = new HashSet<>();
        for (String pId : pList) {
            if (promotionsIdSet.contains(pId)) {
                throw new ErpCommonException(String.format("promotionID:%s 重复", pId));
            }
            promotionsIdSet.add(pId);
            AmzPromotions tempAmzpromotions = new AmzPromotions();
            tempAmzpromotions.setPromotionsId(pId);
            List<AmzPromotions> proList = amzPromotionsMapper.selectAmzPromotionsLists(tempAmzpromotions);
            if (CollectionUtil.isNotEmpty(proList)) {
                List<Long> collectId = promotionsList.stream().map(AmzPromotions::getId).collect(Collectors.toList());
                long repeat = proList.stream().filter(r -> !collectId.contains(r.getId())).count();
                if (repeat > 0) {
                    throw new ErpCommonException(String.format("promotionID:%s 与库里已有promotion的promotionID重复", pId));
                }
            }
        }


        //更新promotionID
        updatePromotionId(promotionsList);
        List<Long> idList = promotionsList.stream().map(AmzPromotions::getId).collect(Collectors.toList());
        List<AmzPromotionsSync> amzPromotionsList = amzPromotionsSyncMapper.getAmzPromotionsList(idList.toArray(new Long[idList.size()]));
        List<AccountWithPromotionsList> list = getSyncPromotionsList(amzPromotionsList, promotionsList);
        JSONArray objects = JSONArray.parseArray(JSONArray.toJSONString(list));
        log.info("同步发起promotion消息:{}", objects.toJSONString());
        rabbitTemplate.convertAndSend(MQDefine.AMZ_PROMOTIONS_PROMOTION_SYNC_SEND, objects);
        List<Long> collect = amzPromotionsList.stream().map(AmzPromotionsSync::getId).distinct().collect(Collectors.toList());
        addPromotionSyncInitLogWithName(collect.toArray(new Long[collect.size()]), getOrgId(), null, AmzPromotionsOperateTypeEnum.SYNC_INIT.value(), name);
        return list;
    }

    @Override
    public List<AccountWithPromotionsList> syncPromotionsInterface(List<AmzPromotions> promotionsList) {

        AssertUtil.isFalse(CollectionUtil.isEmpty(promotionsList), "请填写相关信息");
        promotionsList.forEach(r -> {
            if (r.getId() == null || StringUtil.isEmpty(r.getPromotionsId())) {
                throw new ErpCommonException("id或promotionID不允许为空");
            } else {
                r.setPromotionsId(r.getPromotionsId().trim());
            }
        });
        //更新promotionID
        updatePromotionId(promotionsList);
        List<Long> idList = promotionsList.stream().map(AmzPromotions::getId).collect(Collectors.toList());
        List<AmzPromotionsSync> amzPromotionsList = amzPromotionsSyncMapper.getAmzPromotionsList(idList.toArray(new Long[idList.size()]));
        List<AccountWithPromotionsList> list = getSyncPromotionsList(amzPromotionsList, promotionsList);
        JSONArray objects = JSONArray.parseArray(JSONArray.toJSONString(list));
        rabbitTemplate.convertAndSend(MQDefine.AMZ_PROMOTIONS_PROMOTION_SYNC_SEND, objects);
        List<Long> collect = amzPromotionsList.stream().map(AmzPromotionsSync::getId).distinct().collect(Collectors.toList());
        addPromotionSyncInitLog(collect.toArray(new Long[collect.size()]), 1000049, "接口同步推送promotions", AmzPromotionsOperateTypeEnum.SYNC_INIT.value());
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updatePromotionId(List<AmzPromotions> promotionsList) {

        for (AmzPromotions promotions : promotionsList) {

            AmzPromotions p = new AmzPromotions();
            p.setId(promotions.getId());
            p.setPromotionsId(promotions.getPromotionsId());
            p.setSubShopBack(true);
            amzPromotionsMapper.updateById(p);

        }
    }

    private List<AccountWithPromotionsList> getSyncPromotionsList(List<AmzPromotionsSync> list, List<AmzPromotions> fromFront) {
        List<AccountWithPromotionsList> result = new ArrayList<>();
        List<Long> shopIdList = list.stream().map(AmzPromotionsSync::getShopId).distinct().collect(Collectors.toList());
        for (Long shopId : shopIdList) {
            AccountWithPromotionsList accountWithPromotionsList = new AccountWithPromotionsList();
            List<PromotionsInfo> promotionsInfoList = new ArrayList<>();
            accountWithPromotionsList.setShopId(shopId);
            List<AmzPromotionsSync> collect = list.stream().filter(r -> shopId.equals(r.getShopId())).collect(Collectors.toList());
            collect.stream().forEach(s -> {
                PromotionsInfo promotionsInfo = new PromotionsInfo();
                promotionsInfo.setId(s.getId());
                promotionsInfo.setPromotionId(s.getPromotionId());
                if (StringUtil.isEmpty(promotionsInfo.getPromotionId())) {
                    List<AmzPromotions> collect1 = fromFront.stream().filter(r -> s.getId().equals(r.getId())).collect(Collectors.toList());
                    promotionsInfo.setPromotionId(collect1.get(0).getPromotionsId());
                }
                if (StringUtil.isEmpty(accountWithPromotionsList.getWebStoreName())) {
                    accountWithPromotionsList.setWebStoreName(s.getWebStoreName());
                }
                promotionsInfoList.add(promotionsInfo);
            });
            accountWithPromotionsList.setPromotionInfo(promotionsInfoList);
            result.add(accountWithPromotionsList);
        }

        return result;
    }

    private List<AmzPromotionsCouponsSyncListVo> getSyncCouponsList(List<AmzPromotionsCouponsSync> list, List<AmzPromotionsCoupons> fromFront) {
        List<Long> shopIdList = list.stream().filter(r -> r.getShopId() != null).map(AmzPromotionsCouponsSync::getShopId).distinct().collect(Collectors.toList());

        List<AmzPromotionsCouponsSyncListVo> result = new ArrayList<>();
        for (Long shopId : shopIdList) {
            //店铺维度的活动
            AmzPromotionsCouponsSyncListVo syncListVo = new AmzPromotionsCouponsSyncListVo();
            //该店铺下的所有活动
            List<AmzPromotionsCouponsSyncListVo.AmzPromotionsCouponsSyncVo> campaignList = new ArrayList<>();
            syncListVo.setShopId(shopId);
            syncListVo.setCountryCode(accountService.getById(shopId).getCountryCode());
            List<AmzPromotionsCouponsSync> couponsAndCouponList = list.stream().filter(r -> shopId.equals(r.getShopId())).collect(Collectors.toList());
            List<Long> couponsIdList = couponsAndCouponList.stream().map(AmzPromotionsCouponsSync::getCampaignId).distinct().collect(Collectors.toList());
            for (Long couponsId : couponsIdList) {
                //该店铺下的某活动
                AmzPromotionsCouponsSyncListVo.AmzPromotionsCouponsSyncVo coupons = new AmzPromotionsCouponsSyncListVo.AmzPromotionsCouponsSyncVo();
                coupons.setCampaignId(couponsId);
                //该店铺下的某活动下所有优惠券
                List<AmzPromotionsCouponsSyncListVo.AmzPromotionsCouponSyncVo> couponList = new ArrayList<>();
                List<AmzPromotionsCouponsSync> couponSyncList = couponsAndCouponList.stream().filter(r -> couponsId.equals(r.getCampaignId())).collect(Collectors.toList());
                couponSyncList.stream().forEach(r -> {
                    AmzPromotionsCouponsSyncListVo.AmzPromotionsCouponSyncVo coupon = new AmzPromotionsCouponsSyncListVo.AmzPromotionsCouponSyncVo();
                    coupon.setCouponId(r.getCouponId());
                    coupon.setCouponName(r.getCouponName());
                    couponList.add(coupon);
                    if (StringUtil.isEmpty(coupons.getCampaignName())) {
                        coupons.setCampaignName(r.getCampaignName());
                    }
                    if (StringUtil.isEmpty(coupons.getBackId())) {
                        coupons.setBackId(r.getCouponsBackId());
                        if (StringUtil.isEmpty(coupons.getBackId())) {
                            List<AmzPromotionsCoupons> collect = fromFront.stream().filter(t -> couponsId.equals(t.getId())).collect(Collectors.toList());
                            coupons.setBackId(collect.get(0).getCampaignId());
                        }
                    }
                    if (StringUtil.isEmpty(syncListVo.getWebStoreName())) {
                        syncListVo.setWebStoreName(r.getWebStoreName());
                    }
                    if (syncListVo.getChannel() == null) {
                        syncListVo.setChannel(r.getChannel());
                    }
                });
                coupons.setCouponList(couponList);
                campaignList.add(coupons);
            }
            syncListVo.setCampaignList(campaignList);
            result.add(syncListVo);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void couponSyncMsgResolve(AmzPromotionsSyncCouponsListReceiveMsg message) {
        List<AmzPromotionsSyncCouponsReceiveMsg> campaignList = message.getCampaignList();
        if (CollectionUtil.isNotEmpty(campaignList)) {
            for (AmzPromotionsSyncCouponsReceiveMsg msg : campaignList) {

                if (CollectionUtil.isNotEmpty(msg.getCouponList()) && msg.getCouponList().stream().allMatch(t->t.getCouponId() != null)) {
                    vcCouponSyncMsgResolve(msg);
                } else {
                    threadPoolTaskExecutor.execute(() -> processCaptureCoupon(msg, message.getStoreName()));
                }
            }
        }
    }




    @Transactional(rollbackFor = Exception.class)
    public String vcCouponSyncMsgResolve(AmzPromotionsSyncCouponsReceiveMsg msg) {

        Date nowDate = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", DateUtils.getNowDate()), "yyyy-MM-dd HH:mm:ss");
        Long id = msg.getCampaignId();

        AmzPromotionsCoupons fromDb = amzPromotionsCouponsMapper.selectById(id);
        if (!fromDb.getPromotionName().equals(msg.getCampaignName())) {
            log.error("同步返回的活动名称与库里不一致--现有活动--{}--返回活动--{}", fromDb, msg);
            AmzPromotionsCouponOperateLog operateLog = new AmzPromotionsCouponOperateLog();
            operateLog.settingDefaultSystemCreate();
            operateLog.setCouponsId(id);
            operateLog.setOrganizationId(fromDb.getOrganizationId());
            operateLog.setOperateType(AmzCouponOperateTypeEnum.MANUAL_SYNC.value());
            operateLog.setDetail(String.format("同步返回的coupon活动名称与当前coupon活动名称不一致,现有活动名称为:%s,返回活动名称为:%s", fromDb.getPromotionName(), msg.getCampaignName()));
            iAmzPromotionsCouponOperateLogService.save(operateLog);
            return "success";
        }
        AmzPromotionsCoupons coupons = new AmzPromotionsCoupons();
        coupons.setId(id);
        coupons.setActiveState(msg.getCampaignState());
        coupons.setCampaignId(msg.getCouponsBackId());
        coupons.settingDefaultSystemUpdate();
        amzPromotionsCouponsMapper.updateById(coupons);
        AmzPromotionsCouponOperateLog log1 = new AmzPromotionsCouponOperateLog();
        log1.setCouponsId(id);
        log1.settingDefaultSystemCreate();
        log1.setOperateType(AmzCouponOperateTypeEnum.MANUAL_SYNC.value());
        log1.setOrganizationId(fromDb.getOrganizationId());
        if (fromDb.getActiveState() == null) {
            log1.setDetail("coupon活动状态由:" + "空" + " 变成:" + AmzCouponActiveStateEnum.labelOf(msg.getCampaignState()));
        } else {
            log1.setDetail("coupon活动状态由:" + AmzCouponActiveStateEnum.labelOf(fromDb.getActiveState()) + " 变成:" + AmzCouponActiveStateEnum.labelOf(msg.getCampaignState()));
        }

        iAmzPromotionsCouponOperateLogService.save(log1);

        //校验券名是否和库里一致
        if (!verifyCoupon(msg)) {
            return "success";
        }
        List<AmzPromotionsSyncCouponsReceiveMsg.AmzPromotionsSyncCouponReceiveMsg> couponList = msg.getCouponList();
        List<Long> collect = couponList.stream().map(AmzPromotionsSyncCouponsReceiveMsg.AmzPromotionsSyncCouponReceiveMsg::getCouponId).collect(Collectors.toList());
        List<AmzPromotionsCoupon> listFromDb = iAmzPromotionsCouponService.listByIds(collect);
        Date couponsBeginDate = null;
        Date couponsEndDate = null;
        for (AmzPromotionsSyncCouponsReceiveMsg.AmzPromotionsSyncCouponReceiveMsg receiveMsg : couponList) {
            AmzPromotionsCoupon coupon = new AmzPromotionsCoupon();
            coupon.setId(receiveMsg.getCouponId());
            coupon.setCouponState(receiveMsg.getCouponState());
            coupon.setError(msg.getDetail());
            coupon.setBackId(receiveMsg.getCouponBackId());
            coupon.settingDefaultSystemUpdate();
            coupon.setBeginDate(captureCouponDateConvert(receiveMsg.getCouponId(),receiveMsg.getBeginDateStr(),"开始时间","同步"));
            coupon.setEndDate(captureCouponDateConvert(receiveMsg.getCouponId(),receiveMsg.getEndDateStr(),"结束时间","同步"));
            if (couponsBeginDate == null) {
                couponsBeginDate = coupon.getBeginDate();
            }else {
                couponsBeginDate = (coupon.getBeginDate() != null && coupon.getBeginDate().before(couponsBeginDate)) ? coupon.getBeginDate() : couponsBeginDate;
            }
            if (couponsEndDate == null) {
                couponsEndDate = coupon.getEndDate();
            }else {
                couponsEndDate = (coupon.getEndDate() != null && coupon.getEndDate().after(couponsEndDate)) ? coupon.getEndDate() : couponsEndDate;
            }
            if (Arrays.asList(AmzCouponStateEnum.CANCELED.value(), AmzCouponStateEnum.EXPIRED.value(), AmzCouponStateEnum.COMPLETE_VC.value()).contains(receiveMsg.getCouponState())) {
                AmzPromotionsCoupon tempCoupon = amzPromotionsCouponMapper.selectById(receiveMsg.getCouponId());
                if (tempCoupon.getCompleteDate() == null) {
                    Date tempCompeteDate = DateUtil.UtcToPacificDate(nowDate);
                    //如果券的结束时间小于等于当前pst时间，则完结时间为结束时间，否则为当前pst时间
                    if (DateUtil.compareDate(tempCoupon.getEndDate(), tempCompeteDate) <= 0) {
                        coupon.setCompleteDate(tempCoupon.getEndDate());
                    } else {
                        coupon.setCompleteDate(tempCompeteDate);
                    }

                }
            }
            amzPromotionsCouponMapper.updateById(coupon);
            if (StringUtils.isEmpty(msg.getDetail()) || "[]".equals(msg.getDetail())) {

                iAmzPromotionsCouponService.lambdaUpdate().eq(AmzPromotionsCoupon::getId, receiveMsg.getCouponId())
                        .set(AmzPromotionsCoupon::getError, null).update();
            }

            AmzPromotionsCouponOperateLog log2 = new AmzPromotionsCouponOperateLog();
            log2.setCouponsId(id);
            log2.setCouponId(receiveMsg.getCouponId());
            log2.settingDefaultSystemCreate();
            log2.setOperateType(AmzCouponOperateTypeEnum.MANUAL_SYNC.value());
            log2.setOrganizationId(fromDb.getOrganizationId());
            List<AmzPromotionsCoupon> collect1 = listFromDb.stream().filter(r -> receiveMsg.getCouponId().equals(r.getId())).collect(Collectors.toList());
            AmzPromotionsCoupon coupon1 = collect1.get(0);
            if (coupon1.getCouponState() == null) {
                log2.setDetail("coupon优惠券状态由:" + "空" + " 变成:" + AmzCouponStateEnum.labelOf(receiveMsg.getCouponState()));
            } else {
                log2.setDetail("coupon优惠券状态由:" + AmzCouponStateEnum.labelOf(coupon1.getCouponState()) + " 变成:" + AmzCouponStateEnum.labelOf(receiveMsg.getCouponState()));
            }

            iAmzPromotionsCouponOperateLogService.save(log2);
        }

        if (couponsBeginDate != null || couponsEndDate != null) {
            AmzPromotionsCoupons udateCoupons = new AmzPromotionsCoupons();
            udateCoupons.setId(id);
            udateCoupons.setBeginDate(couponsBeginDate);
            udateCoupons.setEndDate(couponsEndDate);
            amzPromotionsCouponsMapper.updateById(udateCoupons);
        }
        return "success";
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean verifyCoupon(AmzPromotionsSyncCouponsReceiveMsg msg) {

        List<AmzPromotionsSyncCouponsReceiveMsg.AmzPromotionsSyncCouponReceiveMsg> couponList = msg.getCouponList();
        if (CollectionUtil.isEmpty(couponList)) {
            log.error("同步coupon活动id:{}，优惠券返回列表为空", msg);
            return false;
        }
        List<Long> collect = couponList.stream().map(AmzPromotionsSyncCouponsReceiveMsg.AmzPromotionsSyncCouponReceiveMsg::getCouponId).collect(Collectors.toList());
        List<AmzPromotionsCoupon> list = iAmzPromotionsCouponService.listByIds(collect);
        if (CollectionUtil.isEmpty(list)) {
            log.error("同步coupon活动id:{}，优惠券列表在系统中为空", msg);
        }
        if (couponList.size() != list.size()) {
            addCouponSyncInitLog(new Long[]{msg.getCampaignId()}, CollectionUtil.isNotEmpty(list) ? list.get(0).getOrganizationId() : 1000049, "数据同步失败,返回优惠券数量与库中不一样", AmzCouponOperateTypeEnum.MANUAL_SYNC.value());
            return false;
        }
        for (AmzPromotionsSyncCouponsReceiveMsg.AmzPromotionsSyncCouponReceiveMsg receiveMsg : couponList) {
            List<String> collect1 = list.stream().filter(r -> receiveMsg.getCouponId().equals(r.getId()) && StringUtil.isNotEmpty(r.getCouponName())).map(AmzPromotionsCoupon::getCouponName).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(collect1)) {
                addCouponSyncInitLog(new Long[]{msg.getCampaignId()}, CollectionUtil.isNotEmpty(list) ? list.get(0).getOrganizationId() : 1000049, "数据同步失败", AmzCouponOperateTypeEnum.MANUAL_SYNC.value());
                return false;
            }
            if (!receiveMsg.getCouponName().equalsIgnoreCase(collect1.get(0))) {
                addCouponSyncInitLog(new Long[]{msg.getCampaignId()}, CollectionUtil.isNotEmpty(list) ? list.get(0).getOrganizationId() : 1000049, "数据同步失败,返回优惠券名称与库中不一样", AmzCouponOperateTypeEnum.MANUAL_SYNC.value());
                return false;
            }
        }
        return true;
    }


    @Transactional(rollbackFor = Exception.class)
    public String promotionSyncMsgResolve(AmzPromotionsHisPromotionsListReceiveMsg promotionsMsg) {
        List<AmzPromotionsHisPromotionsReceiveMsg> promotionList = promotionsMsg.getPromotionList();
        if (CollectionUtil.isNotEmpty(promotionList)) {
            for (AmzPromotionsHisPromotionsReceiveMsg receiveMsg : promotionList) {
               /* if (CollectionUtil.isNotEmpty(receiveMsg.getSkuList())) {
                    receiveMsg.getSkuList().stream().filter(t->StringUtils.isNotEmpty(t.getPerType()) && !"%".equals(t.getPerType())).forEach(t->t.setPerType("$"));
                }*/
                promotionsMsgResolve(receiveMsg);
            }
            return "success";
        } else {
            return "promotion列表为空";
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String promotionsMsgResolve(AmzPromotionsHisPromotionsReceiveMsg promotionsMsg) {
        String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", DateUtils.getNowDate());
        Date operateDate = DateUtils.parseDate(dateStr, "yyyy-MM-dd HH:mm:ss");
        Integer operateType = AmzPromotionsOperateTypeEnum.MANUAL_SYNC.value();
        StringBuilder detail = new StringBuilder();
        String promotionId = promotionsMsg.getPromotionID();
        if (StringUtil.isEmpty(promotionId)) {
            throw new ErpCommonException("同步promotionId为空");
        }
        AmzPromotions byId = iAmzPromotionsService.getOne(new LambdaQueryWrapper<AmzPromotions>().eq(AmzPromotions::getPromotionsId, promotionId).last("limit 1"));
        if (byId == null) {
            for (int i = 0; i < 15; i++) {
                try {
                    Thread.sleep(2000L);
                    byId = iAmzPromotionsService.getOne(new LambdaQueryWrapper<AmzPromotions>().eq(AmzPromotions::getPromotionsId, promotionId).last("limit 1"));
                    if (byId!= null) {
                        break;
                    }
                }catch (Exception e){

                }
            }
        }
        if (byId == null) {
            log.error("同步promotionId:{}，在系统中不存在", promotionId);
            throw new ErpCommonException("同步无此promotion");
        }

        AmzPromotions amzPromotions = new AmzPromotions();
        if (StringUtil.isNotEmpty(promotionsMsg.getBeginTime())) {
            try {
                amzPromotions.setBeginTime(DateUtils.parseDate(DateUtil.getDateStr(promotionsMsg.getBeginTime()), "yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                amzPromotions.setBeginTime(DateUtils.parseDate(promotionsMsg.getBeginTime(), "yyyy-MM-dd HH:mm:ss"));
            }
        }
        if (StringUtil.isNotEmpty(promotionsMsg.getEndTime())) {
            try {
                amzPromotions.setEndTime(DateUtils.parseDate(DateUtil.getDateStr(promotionsMsg.getEndTime()), "yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                amzPromotions.setEndTime(DateUtils.parseDate(promotionsMsg.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
            }
        }
        amzPromotions.setId(byId.getId());
        amzPromotions.setPromotionsId(promotionsMsg.getPromotionID());
        if (StringUtils.isNotEmpty(promotionsMsg.getMarketplaceId())) {
            amzPromotions.setMarketplaceId(promotionsMsg.getMarketplaceId());
        }
        amzPromotions.setPromotionsState(promotionsMsg.getPromotionState());
        amzPromotions.setGlanceViews(stringToLong(promotionsMsg.getGlanceViews()));
        amzPromotions.setUnitsSold(stringToLong(promotionsMsg.getUnitsSold()));
        amzPromotions.setBudget(stringToBigDecimal(promotionsMsg.getAmountSpent()));
        amzPromotions.setRevenue(stringToBigDecimal(promotionsMsg.getRevenue()));
        amzPromotions.setFundingAgreement(stringToLong(promotionsMsg.getFundingAgreement()));
        if (byId.getPromotionsState() == null) {
            detail.append("promotion(").append(promotionsMsg.getPromotionID()).append(")状态由空").
                    append("更新为").append(AmzPromotionsStateEnum.labelOf(promotionsMsg.getPromotionState())).append("。");
            //promotion返回状态为canceled或者Approved and ended时，更新完结时间
            if (AmzPromotionsStateEnum.CANCELED.value().equals(promotionsMsg.getPromotionState()) || AmzPromotionsStateEnum.APPROVED_AND_ENDED.value().equals(promotionsMsg.getPromotionState())) {
                if (byId.getCompleteDate() == null) {
                    Date tempCompleteDate = DateUtil.UtcToPacificDate(operateDate);
                    //如果结束时间小于等于当前pst时间，则设置完结时间为结束时间，否则当前pst时间
                    Date actureDate = amzPromotions.getEndTime() == null ? byId.getEndTime() : amzPromotions.getEndTime();
                    if (DateUtil.compareDate(actureDate, tempCompleteDate) <= 0) {
                        amzPromotions.setCompleteDate(actureDate);
                    } else {
                        amzPromotions.setCompleteDate(tempCompleteDate);
                    }

                }
            }
        }
        if (byId.getPromotionsState() != null && byId.getPromotionsState().compareTo(promotionsMsg.getPromotionState()) != 0) {
            detail.append("promotion(").append(promotionsMsg.getPromotionID()).append(")状态由")
                    .append(AmzPromotionsStateEnum.labelOf(byId.getPromotionsState())).
                    append("更新为").append(AmzPromotionsStateEnum.labelOf(promotionsMsg.getPromotionState())).append("。");
            //promotion返回状态为canceled或者Approved and ended时，更新完结时间
            if (AmzPromotionsStateEnum.CANCELED.value().equals(promotionsMsg.getPromotionState()) || AmzPromotionsStateEnum.APPROVED_AND_ENDED.value().equals(promotionsMsg.getPromotionState())) {
                if (byId.getCompleteDate() == null) {
                    Date tempCompleteDate = DateUtil.UtcToPacificDate(operateDate);
                    //如果结束时间小于等于当前pst时间，则设置完结时间为结束时间，否则当前pst时间
                    Date actureDate = amzPromotions.getEndTime() == null ? byId.getEndTime() : amzPromotions.getEndTime();
                    if (DateUtil.compareDate(actureDate, tempCompleteDate) <= 0) {
                        amzPromotions.setCompleteDate(actureDate);
                    } else {
                        amzPromotions.setCompleteDate(tempCompleteDate);
                    }
                }
            } else {
                //如果同步前的状态为CANCELLED,则说明RPA返回取消成功的状态与后台实际的不一致，需更新状态为后台实际状态，并重置完结时间
                if (AmzPromotionsStateEnum.CANCELED.value().equals(byId.getPromotionsState())) {
                    log.info("promotion[{}]状态由已取消更新为其他状态，需重置完结时间", byId.getId());
                    if (byId.getCompleteDate() == null) {
                        log.info("promotion[{}]完结时间为空，无需重置完结时间", byId.getId());
                    } else {
                        iAmzPromotionsService.lambdaUpdate().set(AmzPromotions::getCompleteDate, null).
                                eq(AmzPromotions::getId, byId.getId()).update();
                    }
                    //通知运营
                    iAmzPromotionApprovalAdviceService.rpaCancelSuccessAndSyncPromotionAdvice(byId, promotionsMsg.getPromotionState());
                }
            }
        }
        if (AmzPromotionsStateEnum.NEED_YOUR_ATTENTION.value().equals(byId.getPromotionsState()) && AmzPromotionsStateEnum.CANCELED.value().equals(promotionsMsg.getPromotionState())) {
            amzPromotions.setNeedToCanceled(true);
        } else {
            amzPromotions.setNeedToCanceled(false);
        }
        /*if (StringUtil.isNotEmpty(promotionsMsg.getDetail())) {
            detail.append("错误信息如下:").append(promotionsMsg.getDetail()).append(";");
        }*/
        addHisPromotionLongTypeUpdateLog(promotionsMsg.getPromotionID(), byId.getFundingAgreement(), stringToLong(promotionsMsg.getFundingAgreement()), "fundingAgreement", detail);
        if (!verifyPromotion(promotionsMsg)) {
            return "success";
        }
        List<AmzPromotionsHisPromotionsSkuReceiveMsg> skuList = promotionsMsg.getSkuList();
        if (CollectionUtil.isNotEmpty(skuList)) {
            for (AmzPromotionsHisPromotionsSkuReceiveMsg msg : skuList) {
                String asinID = msg.getAsin();
                List<AmzPromotionsSkuDetail> detailList = iAmzPromotionsSkuDetailService.list(new LambdaQueryWrapper<>(AmzPromotionsSkuDetail.class).eq(AmzPromotionsSkuDetail::getPromotionsId, amzPromotions.getId()).eq(AmzPromotionsSkuDetail::getAsin, asinID));
                if (CollectionUtil.isNotEmpty(detailList)) {
                    for (AmzPromotionsSkuDetail skuDetail1 : detailList) {
                        addHisPromotionSkuUpdateLog(skuDetail1.getAsin(), skuDetail1.getPrice(), msg.getPrice(), "售价", detail);
                        addHisPromotionSkuUpdateLog(skuDetail1.getAsin(), skuDetail1.getFrontPrice(), msg.getFrontPrice(), "前台价格", detail);
                        addHisPromotionSkuUpdateLog(skuDetail1.getAsin(), skuDetail1.getDiscountPrice(), msg.getLikelyPromotionPrice(), "折后价", detail);

                        //提交异常或提交失败进行同步
                        if (byId.getPromotionsState() != null &&
                                (AmzPromotionsStateEnum.SUBMIT_EXCEPTION.value().equals(byId.getPromotionsState()) ||
                                        AmzPromotionsStateEnum.SUBMIT_FAIL.value().equals(byId.getPromotionsState()))) {
                            //记录后台第一次创建成功后的前台价格，折后价
                            addHisPromotionSkuUpdateLog(skuDetail1.getAsin(), skuDetail1.getOriginalFrontPrice(), msg.getFrontPrice(), "初始前台价格", detail);
                            addHisPromotionSkuUpdateLog(skuDetail1.getAsin(), skuDetail1.getOriginalDiscountPrice(), msg.getLikelyPromotionPrice(), "初始折后价", detail);
                            skuDetail1.setOriginalFrontPrice(stringToBigDecimal(msg.getFrontPrice()));
                            skuDetail1.setOriginalDiscountPrice(stringToBigDecimal(msg.getLikelyPromotionPrice()));
                        }
                        skuDetail1.setPrice(stringToBigDecimal(msg.getPrice()));
                        skuDetail1.setFrontPrice(stringToBigDecimal(msg.getFrontPrice()));
                        skuDetail1.setDiscountPrice(stringToBigDecimal(msg.getLikelyPromotionPrice()));
                        addPerFundUnit(detail,skuDetail1,msg);
                        Boolean hasDiscountPriceWarn = skuDetail1.getDiscountPriceWarn();
                        if (skuDetail1.getDiscountPrice() != null && skuDetail1.getExpectedDiscountPrice() != null && skuDetail1.getDiscountPrice().compareTo(skuDetail1.getExpectedDiscountPrice()) != 0) {
                            skuDetail1.setDiscountPriceWarn(true);
                            if (hasDiscountPriceWarn != null && !hasDiscountPriceWarn) {
                                detail.append("折后价预警由否更新为是;");
                            }
                        }else{
                            skuDetail1.setDiscountPriceWarn(false);
                            if (hasDiscountPriceWarn != null && hasDiscountPriceWarn) {
                                detail.append("折后价预警由是更新为否;");
                            }
                        }
                        //todo 判断数量是否跟新
                        if (!Objects.equals(msg.getNum(),skuDetail1.getNum())) {
                            detail.append("数量有跟新;");
                            skuDetail1.setNum(msg.getNum());
                        }
                        //todo 判断销量是否跟新
                        if(!Objects.equals(msg.getUnitsSold(),skuDetail1.getUnitsSold())) {
                            detail.append("销量有跟新;");
                            skuDetail1.setUnitsSold(msg.getUnitsSold());
                        }
                        log.info("同步更新promotion的sku开始:{}", skuDetail1);
                        if (StringUtil.isNotEmpty(detail.toString())) {
                            iAmzPromotionsSkuDetailService.updateById(skuDetail1);
                        }
                        updateSkuDailyDetail(skuDetail1);
                    }
                }
            }
        } else {
            throw new ErpCommonException("同步promotion_sku列表为空");
        }
        try {
            iAmzPromotionApprovalAdviceService.promotionExceptionListener(promotionsMsg, byId, 2);
        } catch (Exception e) {
            log.error("同步更新返回消息通知失败:{}", e.getMessage());
        }


        if (StringUtil.isNotEmpty(promotionsMsg.getDetail())) {
            detail.append("错误信息如下:").append(promotionsMsg.getDetail()).append(";");
        }
        updateHisPromotionsAndLog(promotionsMsg, operateDate, operateType, null, detail, amzPromotions, byId);

        return "success";
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean verifyPromotion(AmzPromotionsHisPromotionsReceiveMsg promotionsMsg) {

        List<AmzPromotionsHisPromotionsSkuReceiveMsg> skuList = promotionsMsg.getSkuList();
        AmzPromotions byId = amzPromotionsMapper.selectById(promotionsMsg.getId());
        if (CollectionUtil.isEmpty(skuList)) {
            addPromotionSyncInitLog(new Long[]{promotionsMsg.getId()}, byId != null ? byId.getOrganizationId() : 1000049, "数据同步失败,返回asin列表为空", AmzPromotionsOperateTypeEnum.MANUAL_SYNC.value());
            return false;
        }
        AmzPromotionsSkuDetail skuDetail = new AmzPromotionsSkuDetail();
        skuDetail.setPromotionsId(promotionsMsg.getId());
        List<AmzPromotionsSkuDetail> fromDb = iAmzPromotionsSkuDetailService.list(new LambdaQueryWrapper<AmzPromotionsSkuDetail>().eq(AmzPromotionsSkuDetail::getPromotionsId, promotionsMsg.getId()));
        if (CollectionUtil.isEmpty(fromDb) || skuList.size() != fromDb.size()) {
            addPromotionSyncInitLog(new Long[]{promotionsMsg.getId()}, byId != null ? byId.getOrganizationId() : 1000049, "数据同步失败,返回asin数量与库中不一致", AmzPromotionsOperateTypeEnum.MANUAL_SYNC.value());
            return false;
        }
        List<String> asinFromPy = skuList.stream().map(AmzPromotionsHisPromotionsSkuReceiveMsg::getAsin).collect(Collectors.toList());
        List<String> asinFromDb = fromDb.stream().map(AmzPromotionsSkuDetail::getAsin).collect(Collectors.toList());
        if (!asinFromPy.containsAll(asinFromDb) || !asinFromDb.containsAll(asinFromPy)) {
            addPromotionSyncInitLog(new Long[]{promotionsMsg.getId()}, byId != null ? byId.getOrganizationId() : 1000049, "数据同步失败,返回asin与库中不一致", AmzPromotionsOperateTypeEnum.MANUAL_SYNC.value());
            return false;
        }
        return true;
    }

    public void setBaseAttr(BaseEntity entity, Integer updatedBy, String updatedName, Date updatedAt) {

        entity.setUpdatedBy(updatedBy);
        entity.setUpdatedName(updatedName);
        entity.setUpdatedAt(updatedAt);
    }

    public Long stringToLong(String number) {
        Long t = null;
        if (StringUtil.isNotEmpty(number)) {
            String s = number.replaceAll("[^\\d.]", "").replaceAll(",", "");
            if (StringUtil.isNotEmpty(s)) {
                if (s.contains(".")) {
                    String substring = s.substring(0, s.indexOf("."));
                    return Long.valueOf(substring);
                } else {
                    return Long.valueOf(s);
                }
            }
        }
        return t;
    }

    public BigDecimal stringToBigDecimal(String number) {
        BigDecimal t = null;
        if (StringUtil.isNotEmpty(number)) {
            String s = number.replaceAll("[^\\d.]", "").replaceAll(",", "");
            t = StringUtil.isEmpty(s) ? null : new BigDecimal(s);
        }
        return t;
    }

    /**
     * @param promotionName promotion活动名
     * @param originalData  库中原始数据
     * @param updateData    更新后数据
     * @param column        更新字段
     * @param detail
     */
    private void addHisPromotionLongTypeUpdateLog(String promotionName, Long originalData, Long updateData, String column, StringBuilder detail) {

        if (originalData != null && updateData != null) {
            if (originalData.compareTo(updateData) != 0) {
                detail.append(promotionName).append(":").append(column).append("由").append(originalData).append("更新为").append(updateData).append(";");
            }
        } else if (originalData != null) {
            detail.append(promotionName).append(":").append(column).append("由").append(originalData).append("更新为").append("空").append(";");
        } else if (updateData != null) {
            detail.append(promotionName).append(":").append(column).append("由").append("空").append("更新为").append(updateData).append(";");
        } else {

        }
    }

    private void addHisPromotionSkuUpdateLog(String asin, BigDecimal originalData, String updateData, String column, StringBuilder detail) {
        if (originalData != null && StringUtil.isNotEmpty(updateData)) {
            String s = updateData.replaceAll("[^\\d.]","").replaceAll(",", "");
            if (originalData.compareTo(new BigDecimal(s)) != 0) {
                detail.append(asin).append(":").append(column).append("由").append(originalData).append("更新为").append(s).append(";");
            }
        } else if (originalData == null && StringUtil.isNotEmpty(updateData)) {
            String s = updateData.replaceAll("[^\\d.]", "").replaceAll(",", "");
            detail.append(asin).append(":").append(column).append("由").append("空").append("更新为").append(s).append(";");
        } else if (originalData != null && StringUtil.isEmpty(updateData)) {
            detail.append(asin).append(":").append(column).append("由").append(originalData).append("更新为").append("空").append(";");
        } else {
        }
    }

    /**
     * 更新历史promotions
     *
     * @param
     * @param msg
     * @param operateDate
     * @param operateType
     * @param result
     * @param detail
     * @param amzPromotions
     * @param byId
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateHisPromotionsAndLog(AmzPromotionsHisPromotionsReceiveMsg msg, Date operateDate, Integer operateType, String result, StringBuilder detail, AmzPromotions amzPromotions, AmzPromotions byId) {
        updatePromotionsAndLog(operateDate, operateType, result, detail, amzPromotions, msg.getPromotionState(), byId);
    }

    /**
     * 更新promotions
     *
     * @param operateDate     操作时间
     * @param operateType     操作类型
     * @param result          结果
     * @param detail          详情
     * @param amzPromotions   待更新的promotions
     * @param promotionsState 促销状态
     * @param byId            数据库中原有的promotions
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePromotionsAndLog(Date operateDate, Integer operateType, String result, StringBuilder detail, AmzPromotions amzPromotions, Integer promotionsState, AmzPromotions byId) {
        String operateName = "System";
        amzPromotions.setId(byId.getId());
        amzPromotions.setUpdatedAt(operateDate);
        amzPromotions.setUpdatedName(operateName);
        amzPromotions.setPromotionsState(promotionsState);
        amzPromotionsMapper.updateById(amzPromotions);
        iAmzPromotionsService.insertPromotionLog(byId.getId(), null, operateDate, operateName, operateType, result, detail.toString(), byId.getOrganizationId());
    }

    @Transactional(rollbackFor = Exception.class)
    public int updateSkuDailyDetail(AmzPromotionsSkuDetail skuDetail) {
        Date nowDate = DateUtils.getNowDate();
        List<AmzPromotionsDailyDetail> nowDayDailyDetails = amzPromotionsDailyDetailMapper.selectAmzPromotionsDailyDetailList(skuDetail.getPromotionsId(), DateUtil.UtcToPacificDate(nowDate), DateUtil.UtcToPacificDate(nowDate), new String[]{skuDetail.getAsin()});
        AmzPromotionsDailyDetail detail = new AmzPromotionsDailyDetail();
        detail.setPrice(skuDetail.getPrice());
        detail.setFrontPrice(skuDetail.getFrontPrice());
        detail.setDiscountPrice(skuDetail.getDiscountPrice());
//        detail.setPerFunding(skuDetail.getPerFunding());
        if (CollectionUtil.isEmpty(nowDayDailyDetails)) {
            detail.setDate(DateUtil.UtcToPacificDate(nowDate));
            detail.setAsin(skuDetail.getAsin());
            detail.setSku(skuDetail.getSellerSku());
            detail.setTitle(skuDetail.getTitle());
            detail.setRevenue(skuDetail.getRevenue());
            detail.setSpent(skuDetail.getAmountSpent());
            detail.setUnitsSold(skuDetail.getUnitsSold() != null ? Long.valueOf(skuDetail.getUnitsSold()) : null);
            detail.setOrganizationId(skuDetail.getOrganizationId());
            detail.setFlag("$");
            detail.setCreatedAt(nowDate);
            detail.setCreatedName("System");
            detail.setUpdatedAt(nowDate);
            detail.setUpdatedName("System");
            detail.setPromotionId(skuDetail.getPromotionsId());
            detail.setSkuDetailId(skuDetail.getId());
            log.info("同步新增promotion每日明细开始:{}", detail);
            return amzPromotionsDailyDetailMapper.insertAmzPromotionsDailyDetail(detail);
        } else {
            detail.setId(nowDayDailyDetails.get(0).getId());
            log.info("同步更新promotion每日明细开始:{}", detail);
            return amzPromotionsDailyDetailMapper.updateAmzPromotionsDailyDetail(detail);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void addCouponSyncInitLog(Long[] couponsIds, Integer orgId, String detail, Integer operateType) {


        for (Long couponsId : couponsIds) {
            AmzPromotionsCouponOperateLog log = new AmzPromotionsCouponOperateLog();
            log.setCouponsId(couponsId);
            log.settingDefaultSystemCreate();
            log.setOperateType(operateType);
            log.setDetail(detail);
            AmzPromotionsCoupons couponsById = amzPromotionsCouponsMapper.selectAmzPromotionsCouponsById(couponsId);
            log.setOrganizationId((couponsById != null && couponsById.getOrganizationId() != null) ? couponsById.getOrganizationId() :  orgId);
            iAmzPromotionsCouponOperateLogService.save(log);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void addPromotionSyncInitLog(Long[] promotionsIds, Integer orgId, String detail, Integer operateType) {
        String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", DateUtils.getNowDate());
        Date nowDate = DateUtils.parseDate(dateStr, "yyyy-MM-dd HH:mm:ss");
        for (Long promotionsId : promotionsIds) {
            AmzPromotions promotionsById = amzPromotionsMapper.selectById(promotionsId);
            iAmzPromotionsService.insertPromotionLog(promotionsId, -1, nowDate, "System", operateType, null, detail, (promotionsById != null && promotionsById.getOrganizationId() != null) ? promotionsById.getOrganizationId() : orgId);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void addPromotionSyncInitLogWithName(Long[] promotionsIds, Integer orgId, String detail, Integer operateType, String name) {

        Date nowDate = DateUtils.parseDate(DateUtils.nSecondAfter(DateUtils.getNowDate(), 1), "yyyy-MM-dd HH:mm:ss");
        for (Long promotionsId : promotionsIds) {
            AmzPromotions promotions = amzPromotionsMapper.selectById(promotionsId);
            iAmzPromotionsService.insertPromotionLog(promotionsId, -1, nowDate, name, operateType, null, detail, promotions != null ? promotions.getOrganizationId() : orgId);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void addCouponSyncInitLogWithName(Long[] couponsIds, Integer orgId, String detail, Integer operateType, String name) {

        String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", DateUtils.getNowDate());
        Date nowDate = DateUtils.parseDate(dateStr, "yyyy-MM-dd HH:mm:ss");
        for (Long couponsId : couponsIds) {
            AmzPromotionsCouponOperateLog log = new AmzPromotionsCouponOperateLog();
            log.setCouponsId(couponsId);
            log.setCreatedBy(-1);
            log.setCreatedName(name);
            log.setCreatedAt(nowDate);
            log.setOperateType(operateType);
            log.setDetail(detail);
            AmzPromotionsCoupons byId = amzPromotionsCouponsMapper.selectAmzPromotionsCouponsById(couponsId);
            log.setOrganizationId(byId != null ? byId.getOrganizationId() : orgId);
            iAmzPromotionsCouponOperateLogService.insertAmzPromotionsCouponOperateLog(log);
        }
    }


    /**
     * 同步promotion无条件(ASIN的 perfunding为空的  或者  Promotion状态是取消异常的  或者  Promotion状态是修改异常的)
     *
     * @param lists
     * @return
     */
    @Override
    public List<AccountWithPromotionsList> syncPromotionsNoCondition(List<AmzPromotions> lists, String name, String detail) {
        if (CollectionUtil.isEmpty(lists)) {
            log.error("请填写promotion相关信息");
            return null;
        }
        List<AmzPromotions> promotionsList = lists.stream().filter(r -> r.getId() != null && StringUtil.isNotEmpty(r.getPromotionsId())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(promotionsList)) {
            log.error("无符合条件的promotion");
            return null;
        }
        //更新promotionID
        updatePromotionId(promotionsList);
        List<Long> idList = promotionsList.stream().map(AmzPromotions::getId).collect(Collectors.toList());
        List<AmzPromotionsSync> amzPromotionsList = amzPromotionsSyncMapper.getAmzPromotionsListNoCondition(idList.toArray(new Long[idList.size()]));
        List<AccountWithPromotionsList> list = getSyncPromotionsList(amzPromotionsList, promotionsList);
        JSONArray objects = JSONArray.parseArray(JSONArray.toJSONString(list));
        rabbitTemplate.convertAndSend(MQDefine.AMZ_PROMOTIONS_PROMOTION_SYNC_SEND, objects);
        log.info("同步发起 {} promotion消息:{}", detail, objects.toJSONString());
        List<Long> collect = amzPromotionsList.stream().map(AmzPromotionsSync::getId).distinct().collect(Collectors.toList());
        addPromotionSyncInitLogWithName(collect.toArray(new Long[collect.size()]), 1000049, detail, AmzPromotionsOperateTypeEnum.SYNC_INIT.value(), name);
        return list;
    }

    /**
     * 同步coupon无条件(取消异常，修改异常)
     *
     * @param lists
     * @return
     */
    @Override
    public List<AmzPromotionsCouponsSyncListVo> syncCouponsListNoCondition(List<AmzPromotionsCoupons> lists, String name,String detail) {

        if (CollectionUtil.isEmpty(lists)) {
            log.error("请填写coupon相关信息");
            return null;
        }
        List<AmzPromotionsCoupons> amzPromotionsCoupons = lists.stream().filter(r -> r.getId() != null && StringUtil.isNotEmpty(r.getCampaignId())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(amzPromotionsCoupons)) {
            log.error("无符合条件的coupon");
            return null;
        }
        updateCouponsBackId(amzPromotionsCoupons);
        List<Long> collect = amzPromotionsCoupons.stream().map(AmzPromotionsCoupons::getId).collect(Collectors.toList());
        List<AmzPromotionsCouponsSync> list = amzPromotionsSyncMapper.getAmzPromotionsCouponsListNoCondition(collect.toArray(new Long[collect.size()]));
        List<AmzPromotionsCouponsSyncListVo> result = getSyncCouponsList(list, amzPromotionsCoupons);
        JSONArray objects = JSONArray.parseArray(JSONArray.toJSONString(result));
        rabbitTemplate.convertAndSend(MQDefine.AMZ_PROMOTIONS_COUPONS_SYNC_SEND, objects);
        List<Long> idList = list.stream().map(AmzPromotionsCouponsSync::getCampaignId).distinct().collect(Collectors.toList());
        addCouponSyncInitLogWithName(idList.toArray(new Long[idList.size()]), 1000049, detail, AmzCouponOperateTypeEnum.SYNC_INIT.value(), name);
        return result;
    }


    @Override
    @Async("threadPoolTaskExecutor")
    public List<AccountWithPromotionsList> syncPromotionsNoConditionSleep(List<AmzPromotions> lists, String name, String detail) {
        try {
            Thread.sleep(180000L);
        } catch (InterruptedException e) {
            log.error("延迟同步失败:{}", e.getMessage());
        }
        if (CollectionUtil.isEmpty(lists)) {
            log.error("请填写promotion相关信息");
            return null;
        }
        List<AmzPromotions> promotionsList = lists.stream().filter(r -> r.getId() != null && StringUtil.isNotEmpty(r.getPromotionsId())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(promotionsList)) {
            log.error("无符合条件的promotion");
            return null;
        }
        //更新promotionID
        updatePromotionId(promotionsList);
        List<Long> idList = promotionsList.stream().map(AmzPromotions::getId).collect(Collectors.toList());
        List<AmzPromotionsSync> amzPromotionsList = amzPromotionsSyncMapper.getAmzPromotionsListNoCondition(idList.toArray(new Long[idList.size()]));
        List<AccountWithPromotionsList> list = getSyncPromotionsList(amzPromotionsList, promotionsList);
        JSONArray objects = JSONArray.parseArray(JSONArray.toJSONString(list));
        log.info("同步发起 {} promotion消息:{}", detail, objects.toJSONString());
        rabbitTemplate.convertAndSend(MQDefine.AMZ_PROMOTIONS_PROMOTION_SYNC_SEND, objects);
        List<Long> collect = amzPromotionsList.stream().map(AmzPromotionsSync::getId).distinct().collect(Collectors.toList());
        addPromotionSyncInitLogWithName(collect.toArray(new Long[collect.size()]), 1000049, detail, AmzPromotionsOperateTypeEnum.SYNC_INIT.value(), name);
        return list;
    }


    /**
     * promotion修改返回Approved but needs your attention或Approved but not featured自动同步
     * @param lists
     * @param name
     * @param detail
     * @return
     */
    @Override
    @Async("threadPoolTaskExecutor")
    public List<AccountWithPromotionsList> syncPromotionsInfoUpdateReturnSomeState(List<AmzPromotions> lists, String name, String detail) {
        try {
            Thread.sleep(40000L);
        } catch (InterruptedException e) {
            log.error("延迟同步失败:{}", e.getMessage());
        }
        if (CollectionUtil.isEmpty(lists)) {
            log.error("请填写promotion相关信息");
            return null;
        }
        List<AmzPromotions> promotionsList = lists.stream().filter(r -> r.getId() != null && StringUtil.isNotEmpty(r.getPromotionsId())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(promotionsList)) {
            log.error("无符合条件的promotion");
            return null;
        }
        //更新promotionID
        updatePromotionId(promotionsList);
        List<Long> idList = promotionsList.stream().map(AmzPromotions::getId).collect(Collectors.toList());
        List<AmzPromotionsSync> amzPromotionsList = amzPromotionsSyncMapper.getAmzPromotionsListNoCondition(idList.toArray(new Long[idList.size()]));
        List<AccountWithPromotionsList> list = getSyncPromotionsList(amzPromotionsList, promotionsList);
        JSONArray objects = JSONArray.parseArray(JSONArray.toJSONString(list));
        log.info("同步发起 {} promotion消息:{}", detail, objects.toJSONString());
        rabbitTemplate.convertAndSend(MQDefine.AMZ_PROMOTIONS_PROMOTION_SYNC_SEND, objects);
        List<Long> collect = amzPromotionsList.stream().map(AmzPromotionsSync::getId).distinct().collect(Collectors.toList());
        addPromotionSyncInitLogWithName(collect.toArray(new Long[collect.size()]), 1000049, detail, AmzPromotionsOperateTypeEnum.SYNC_INIT.value(), name);
        return list;
    }

    private void addPerFundUnit(StringBuilder detail, AmzPromotionsSkuDetail  skuDetail1, AmzPromotionsHisPromotionsSkuReceiveMsg msg) {

        if (StringUtil.isEmpty(msg.getPerType()) || StringUtil.isEmpty(msg.getPerAmount())) {
            log.error("同步消息每件商品返回折扣为空,asin为:{}", skuDetail1.getAsin());
        } else {
            msg.setPerType(resetCurrency(msg.getPerType()));
            String perFunding = skuDetail1.getPerFunding();
            String newPerFunding = "%".equals(msg.getPerType()) ? msg.getPerAmount() + msg.getPerType() : msg.getPerType() + msg.getPerAmount() ;
            if (StringUtil.isEmpty(perFunding)) {
                detail.append(skuDetail1.getAsin()).append(":").append("每件商品折扣").append("由")
                        .append("空更新为").append(newPerFunding).append(";");
                skuDetail1.setPerFunding(newPerFunding);
            } else {
                if (((!"%".equals(msg.getPerType())) && perFunding.contains("%")) || ("%".equals(msg.getPerType()) && (!perFunding.contains("%")))) {
                    detail.append(skuDetail1.getAsin()).append(":").append("每件商品折扣").append("由")
                            .append(perFunding)
                            .append("更新为").append(newPerFunding).append(";");
                    skuDetail1.setPerFunding(newPerFunding);
                } else {
                    BigDecimal newValue = new BigDecimal(msg.getPerAmount());
                    BigDecimal old = BigDecimal.ZERO;
                    if (!"%".equals(msg.getPerType())) {
                        old = new BigDecimal(perFunding.replaceAll("[^\\d.]",""));
                    } else {
                        old = new BigDecimal(perFunding.replace("%", ""));
                    }
                    if (newValue.compareTo(old) != 0) {
                        detail.append(skuDetail1.getAsin()).append(":").append("每件商品折扣").append("由")
                                .append(perFunding)
                                .append("更新为").append(newPerFunding).append(";");
                        skuDetail1.setPerFunding(newPerFunding);
                    }
                }
            }
        }

    }

    private String resetCurrency(String perType) {
        if (StringUtils.isEmpty(perType)) {
            return null;
        }
        return MarPromotionCountryCurrencyEnum.getCurrencyFlagByCurrency(perType.trim());
    }


    public Integer getOrgId() {
        AuthUserDetails authUserDetails = AuthContextHolder.getAuthUserDetails();
        if (authUserDetails != null && authUserDetails.getOrgId() != null) {
            return authUserDetails.getOrgId();
        }
        return 1000049;
    }




    /**
     * @Description:抓取promotion信息
     * @Author: wly
     * @Date: 2024/9/18 11:12
     * @Params: [amzPromotionsList, contextId]
     * @Return: void
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void capturePromotion(List<AmzPromotions> amzPromotionsList, Integer contextId, UserEntity authUserEntity) {

        log.info("当前时间:{},抓取人:{},抓取promotion信息",DateUtils.getNowDate(),authUserEntity.getName());
        log.info("抓取promotion原始信息:{}", JSONArray.toJSONString(amzPromotionsList));
        boolean anyMatch = amzPromotionsList.stream().anyMatch(t -> StringUtils.isEmpty(t.getPromotionsId()) || t.getShopId() == null);
        AssertUtil.isFalse(anyMatch, "请选择店铺和填写promotionId！");
        boolean anyMatch1 = amzPromotionsList.stream().collect(Collectors.groupingBy(AmzPromotions::getPromotionsId)).entrySet().stream().anyMatch(t -> t.getValue().size() > 1);
        AssertUtil.isFalse(anyMatch1, "promotionId不能重复！");
        amzPromotionsList.forEach(r -> {
            String pattern = "^.{10,}$";
            if (!r.getPromotionsId().matches(pattern)) {
                throw new ErpCommonException("PromotionID为大于等于10位");
            }
            r.setPromotionsId(r.getPromotionsId().trim());
        });
        List<String> promotionIdList = amzPromotionsList.stream().map(AmzPromotions::getPromotionsId).collect(Collectors.toList());
        List<AmzPromotions> selectList = amzPromotionsMapper.selectList(new LambdaQueryWrapper<AmzPromotions>().in(AmzPromotions::getPromotionsId, promotionIdList));
        if (CollectionUtil.isNotEmpty(selectList)) {
            throw new ErpCommonException(String.format("promotionId:[%s]在系统中已存在!", selectList.stream().map(AmzPromotions::getPromotionsId).collect(Collectors.joining(","))));
        }
        List<Long> shopIdList = amzPromotionsList.stream().map(AmzPromotions::getShopId).distinct().collect(Collectors.toList());
        List<Account> accounts = accountService.listByIds(shopIdList);
        if (CollectionUtil.isEmpty(accounts) || accounts.size() != shopIdList.size()) {
            throw new ErpCommonException(String.format("店铺id:[%s]不存在!", shopIdList.stream().map(Object::toString).collect(Collectors.joining(","))));
        }
        List<String> storeNameIsNull = accounts.stream().filter(t -> StringUtils.isEmpty(t.getStoreName())).map(Account::getTitle).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(storeNameIsNull)) {
            throw new ErpCommonException(String.format("店铺名称为空:[%s]!", String.join(",", storeNameIsNull)));
        }
        List<AmzPromotions> list = buildAmzPromotionList(amzPromotionsList, contextId, accounts,shopIdList,authUserEntity);
        iAmzPromotionsService.saveBatch(list);
        List<AmzPromotionsOperateLog> amzPromotionsOperateLogs = buildCapturePromotionLog(list, null, AmzPromotionsOperateTypeEnum.CAPTURE_PROMOTION.value());
        iAmzPromotionsOperateLogService.saveBatch(amzPromotionsOperateLogs);
        Map<Long, List<AmzPromotions>> collect = list.stream().collect(Collectors.groupingBy(AmzPromotions::getShopId));
        List<AccountWithPromotionsList> accountWithPromotionsListList = new ArrayList<>();
        collect.forEach((shopId, v) -> {
            AccountWithPromotionsList t = new AccountWithPromotionsList();
            t.setShopId(shopId);
            accounts.stream().filter(t1 -> t1.getId().equals(shopId.intValue())).findFirst().ifPresent(q -> t.setWebStoreName(q.getStoreName()));
            List<PromotionsInfo> promotionsInfoList = new ArrayList<>();
            v.forEach(r -> {
                PromotionsInfo promotionsInfo = new PromotionsInfo();
                promotionsInfo.setId(r.getId());
                promotionsInfo.setPromotionId(r.getPromotionsId());
                promotionsInfoList.add(promotionsInfo);
            });
            t.setPromotionInfo(promotionsInfoList);
            accountWithPromotionsListList.add(t);
        });
        log.info("抓取promotion信息:{}",JSONArray.toJSONString(accountWithPromotionsListList));
        rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_E_ERP, MQDefine.AMZ_PROMOTIONS_CAPTURE_SEND_SYNC_KEY, JSONArray.parseArray(JSONArray.toJSONString(accountWithPromotionsListList)));
    }


    /**
     * 抓取promotion信息填写错误修改后重新抓取
     * @param amzPromotionsList
     * @param contextId
     * @param authUserEntity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void captureModifyPromotion(List<AmzPromotions> amzPromotionsList, Integer contextId, UserEntity authUserEntity) {
        log.info("当前时间--{}--抓取修改人--{}---抓取修改promotion信息",DateUtils.getNowDate(),authUserEntity.getName());
        log.info("抓取修改promotion原始信息--{}", JSONArray.toJSONString(amzPromotionsList));

        if (amzPromotionsList.stream().anyMatch(t -> t.getId() == null)){
            throw new ErpCommonException("请选择要修改的promotion！");
        }
        boolean anyMatch = amzPromotionsList.stream().anyMatch(t -> StringUtils.isEmpty(t.getPromotionsId()) || t.getShopId() == null);
        AssertUtil.isFalse(anyMatch, "请选择店铺和填写promotionId！");
        boolean anyMatch1 = amzPromotionsList.stream().collect(Collectors.groupingBy(AmzPromotions::getPromotionsId)).entrySet().stream().anyMatch(t -> t.getValue().size() > 1);
        AssertUtil.isFalse(anyMatch1, "promotionId不能重复！");
        amzPromotionsList.forEach(r -> {
            String pattern = "^.{10,}$";
            if (!r.getPromotionsId().matches(pattern)) {
                throw new ErpCommonException("PromotionID为大于等于10位");
            }
            r.setPromotionsId(r.getPromotionsId().trim());
        });

        List<Long> modifyIdList = amzPromotionsList.stream().map(AmzPromotions::getId).collect(Collectors.toList());
        List<AmzPromotions> modifyPromotionList = iAmzPromotionsService.listByIds(modifyIdList);
        if (CollectionUtil.isEmpty(modifyPromotionList)) {
            throw new ErpCommonException("勾选数据不存在！");
        }
        if (modifyPromotionList.size() != amzPromotionsList.size()) {
            throw new ErpCommonException("勾选数据部分不存在！");
        }

        List<String> notMatch = modifyPromotionList.stream().filter(t -> StringUtils.isNotEmpty(t.getApprovalStatus()) || t.getPromotionsState() != null).map(t -> t.getPromotionsId()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(notMatch)) {
            throw new ErpCommonException("勾选数据审批状态不为空或者promotion状态不为空，不能修改！");
        }
        List<AmzPromotionsSkuDetail> skuDetailList = iAmzPromotionsSkuDetailService.list(Wrappers.<AmzPromotionsSkuDetail>lambdaQuery().in(AmzPromotionsSkuDetail::getPromotionsId, modifyIdList));
        if (CollectionUtil.isNotEmpty(skuDetailList)) {
            throw new ErpCommonException("勾选数据存在sku详情，不能修改！");
        }

        List<String> promotionIdList = amzPromotionsList.stream().map(AmzPromotions::getPromotionsId).collect(Collectors.toList());
        List<AmzPromotions> selectList = amzPromotionsMapper.selectList(new LambdaQueryWrapper<AmzPromotions>().in(AmzPromotions::getPromotionsId, promotionIdList).notIn(AmzPromotions::getId, modifyIdList));
        if (CollectionUtil.isNotEmpty(selectList)) {
            throw new ErpCommonException(String.format("promotionId:[%s]在系统中已存在!", selectList.stream().map(AmzPromotions::getPromotionsId).collect(Collectors.joining(","))));
        }
        List<Long> shopIdList = amzPromotionsList.stream().map(AmzPromotions::getShopId).distinct().collect(Collectors.toList());
        List<Account> accounts = accountService.listByIds(shopIdList);
        if (CollectionUtil.isEmpty(accounts) || accounts.size() != shopIdList.size()) {
            throw new ErpCommonException(String.format("店铺id:[%s]不存在!", shopIdList.stream().map(Object::toString).collect(Collectors.joining(","))));
        }
        List<String> storeNameIsNull = accounts.stream().filter(t -> StringUtils.isEmpty(t.getStoreName())).map(Account::getTitle).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(storeNameIsNull)) {
            throw new ErpCommonException(String.format("浏览器店铺名称为空:[%s]!", String.join(",", storeNameIsNull)));
        }


        log.info("修改前抓取promotion信息:{}", modifyPromotionList);
        List<AmzPromotionsOperateLog> amzPromotionsOperateLogs = new ArrayList<>();

        iAmzPromotionsService.updateBatchById(amzPromotionsList);
        for (AmzPromotions amzPromotions : amzPromotionsList) {
            StringBuilder sb = new StringBuilder();
            AmzPromotions existPromotion = modifyPromotionList.stream().filter(t -> t.getId().equals(amzPromotions.getId())).findFirst().get();
            String existPromotionId = existPromotion.getPromotionsId();
            if (!amzPromotions.getPromotionsId().equals(existPromotionId)) {
                sb.append("促销ID:").append(existPromotionId).append("->").append(amzPromotions.getPromotionsId()).append(";");
            }
            if (!amzPromotions.getShopId().equals(existPromotion.getShopId())) {
                sb.append("店铺:").append(accountService.getById(existPromotion.getShopId()).getTitle()).append("->").append(accountService.getById(amzPromotions.getShopId()).getTitle()).append(";");
            }
            AmzPromotionsOperateLog amzPromotionsOperateLog = new AmzPromotionsOperateLog();
            amzPromotionsOperateLog.setPromotionId(amzPromotions.getId());
            amzPromotionsOperateLog.setOperateType(AmzPromotionsOperateTypeEnum.CAPTURE_PROMOTION.value());
            amzPromotionsOperateLog.setOrganizationId(amzPromotions.getOrganizationId());
            amzPromotionsOperateLog.settingDefaultCreate();
            amzPromotionsOperateLog.setDetail(sb.toString());
            amzPromotionsOperateLogs.add(amzPromotionsOperateLog);
        }
        iAmzPromotionsOperateLogService.saveBatch(amzPromotionsOperateLogs);
        Map<Long, List<AmzPromotions>> collect = amzPromotionsList.stream().collect(Collectors.groupingBy(AmzPromotions::getShopId));
        List<AccountWithPromotionsList> accountWithPromotionsListList = new ArrayList<>();
        collect.forEach((shopId, v) -> {
            AccountWithPromotionsList t = new AccountWithPromotionsList();
            t.setShopId(shopId);
            accounts.stream().filter(t1 -> t1.getId().equals(shopId.intValue())).findFirst().ifPresent(q -> t.setWebStoreName(q.getStoreName()));
            List<PromotionsInfo> promotionsInfoList = new ArrayList<>();
            v.forEach(r -> {
                PromotionsInfo promotionsInfo = new PromotionsInfo();
                promotionsInfo.setId(r.getId());
                promotionsInfo.setPromotionId(r.getPromotionsId());
                promotionsInfoList.add(promotionsInfo);
            });
            t.setPromotionInfo(promotionsInfoList);
            accountWithPromotionsListList.add(t);
        });
        log.info("修改后抓取promotion信息:{}",JSONArray.toJSONString(accountWithPromotionsListList));
        rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_E_ERP, MQDefine.AMZ_PROMOTIONS_CAPTURE_SEND_SYNC_KEY, JSONArray.parseArray(JSONArray.toJSONString(accountWithPromotionsListList)));
    }

    private List<AmzPromotions> buildAmzPromotionList(List<AmzPromotions> list, Integer contextId, List<Account> accountList, List<Long> shopIdList, UserEntity authUserEntity) {

        List<AccountWithCountry> accountWithCountries = amzPromotionAccountMapper.selectAccountWithCountryList(shopIdList.toArray(new Long[0]));
        for (AmzPromotions amzPromotions : list) {
            amzPromotions.setOrganizationId(contextId);
            amzPromotions.settingDefaultUpdate();
            amzPromotions.settingDefaultCreate();
            amzPromotions.setCreatedAt(DateUtil.UtcToPacificDate(amzPromotions.getCreatedAt()));
            Account account = accountList.stream().filter(t -> amzPromotions.getShopId().equals(t.getId().longValue())).findFirst().orElse(null);
            amzPromotions.setShopName(account.getAccountInit());
            amzPromotions.setChannel(AmzPromotionsChannelEnum.valueOfLabel(account.getAccountType()));
            accountWithCountries.stream().filter(t -> t.getId().equals(amzPromotions.getShopId())).findFirst().ifPresent(q -> {
                amzPromotions.setCountry(q.getNameCn());
                amzPromotions.setFlag(MarPromotionCountryCurrencyEnum.getCurrencyFlagByCountryCode(q.getCountryCode()));
            });
        }
        return list;
    }

    private List<AmzPromotionsOperateLog> buildCapturePromotionLog(List<AmzPromotions> amzPromotionsList, String detail, Integer operateType) {
        List<AmzPromotionsOperateLog> list = new ArrayList<>();
        for (AmzPromotions amzPromotions : amzPromotionsList) {
            AmzPromotionsOperateLog amzPromotionsOperateLog = new AmzPromotionsOperateLog();
            amzPromotionsOperateLog.setPromotionId(amzPromotions.getId());
            amzPromotionsOperateLog.setOperateType(operateType);
            amzPromotionsOperateLog.setOrganizationId(amzPromotions.getOrganizationId());
            amzPromotionsOperateLog.settingDefaultCreate();
            list.add(amzPromotionsOperateLog);
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processCapturePromotion(String msg) {

        Date nowDate = DateUtils.getNowDate();
        AmzPromotionCaptureReturnMsg parsedObject = JSONObject.parseObject(msg, AmzPromotionCaptureReturnMsg.class);
        RLock lock = redissonClient.getLock(parsedObject.getPromotionId() + parsedObject.getStoreName());
        try {
            if (lock.tryLock(0, 1, TimeUnit.MINUTES)) {
                AmzPromotions amzPromotions = buildCaptureAmzPromotion(parsedObject, nowDate);

                if (amzPromotions == null) {
                    if (parsedObject.getRetryTime() != null && parsedObject.getRetryTime() >= 3) {
                        log.error("抓取promotion信息失败,promotionId:[{}],店铺名称:[{}],原因:[{}]", parsedObject.getPromotionId(), parsedObject.getStoreName(), "抓取失败超过3次,放弃抓取!");
                        return;
                    }
                    parsedObject.setRetryTime((parsedObject.getRetryTime() == null? 0 : parsedObject.getRetryTime()) + 1);

                    rabbitTemplate.convertAndSend(MQDefine.EXCHANGE_E_ERP, MQDefine.AMZ_PROMOTIONS_CAPTURE_RETURN_SYNC_KEY, JSONObject.parseObject(JSONObject.toJSONString(parsedObject),JSONObject.class));
                    return;
                }
                List<AmzPromotionsSkuDetail> insertOrUpdateSkuDetail = buildCaptureAmzPromotionSkuDetail(parsedObject, amzPromotions, nowDate);
                iAmzPromotionsService.updateById(amzPromotions);
                if (CollectionUtil.isNotEmpty(insertOrUpdateSkuDetail)) {
                    iAmzPromotionsSkuDetailService.saveOrUpdateBatch(insertOrUpdateSkuDetail);
                }
                List<AmzPromotionsOperateLog> amzPromotionsOperateLogs = buildCapturePromotionLog(Collections.singletonList(amzPromotions), null, AmzPromotionsOperateTypeEnum.RPA_CAPTURE_PROMOTION.value());
                iAmzPromotionsOperateLogService.saveBatch(amzPromotionsOperateLogs);


                //抓取自动重算促销费用
                threadPoolTaskExecutor.execute(() -> {
                    try {
                        TimeUnit.SECONDS.sleep(30);
                        log.info("抓取自动重算促销费用,promotionId:[{}],店铺名称:[{}]", amzPromotions.getId(), amzPromotions.getShopName());
                        Date start = DateUtils.parseDate(DateUtil.convertDateToString(amzPromotions.getBeginTime(), "yyyy-MM-dd"));
                        Date end = DateUtils.parseDate(DateUtil.convertDateToString(amzPromotions.getEndTime(), "yyyy-MM-dd"));
                        Date currencyPstDate = DateUtils.parseDate(DateUtil.convertDateToString(DateUtils.utcToPst(DateUtils.getNowDate()), "yyyy-MM-dd"));
                        if (currencyPstDate.before(start)) {
                            return;
                        }
                        if (currencyPstDate.before(end)) {
                            end = currencyPstDate;
                        }
                        while (!start.after(end)) {
                            amzPromotionFeeService.updatePromotionFeeHisNew(start, Collections.singletonList(amzPromotions.getId()), null, false);
                            start = DateUtils.nDaysAfter(1, start);
                        }
                    } catch (Exception e) {
                        log.error("抓取自动重算促销费用失败,promotionId:[{}],店铺名称:[{}],原因:[{}]", amzPromotions.getId(), amzPromotions.getShopName(), e.getMessage());
                    }
                });

            }
        } catch (Exception e) {
            log.error("抓取promotion信息失败,promotionId:[{}],店铺名称:[{}],原因:[{}]", parsedObject.getPromotionId(), parsedObject.getStoreName(), e.getMessage());
            throw new ErpCommonException("抓取promotion信息失败,请稍后再试!");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    private List<AmzPromotionsSkuDetail> buildCaptureAmzPromotionSkuDetail(AmzPromotionCaptureReturnMsg parsedObject, AmzPromotions amzPromotions, Date nowDate) {
        List<AmzPromotionsSkuDetail> list = new ArrayList<>();
        List<AmzPromotionCaptureReturnMsg.SkuMsg> skuList = parsedObject.getSkuList();
        if (CollectionUtil.isEmpty(skuList)) {
            log.error("当前时间:{},抓取promotionId:[{}]的sku信息为空!", nowDate, parsedObject.getPromotionId());
            return list;
        }
        List<String> asinList = skuList.stream().map(AmzPromotionCaptureReturnMsg.SkuMsg::getAsin).collect(Collectors.toList());
        List<AmzPromotionsCouponSkuInfoVO> sellerSkuInfo = amzPromotionAccountMapper.selectSellerSkuByShopIdAndAsin(amzPromotions.getShopId(), asinList);
        List<AmzPromotionsSkuDetail> asinListFromDb = iAmzPromotionsSkuDetailService.list(Wrappers.<AmzPromotionsSkuDetail>lambdaQuery().eq(AmzPromotionsSkuDetail::getPromotionsId, amzPromotions.getId()).in(AmzPromotionsSkuDetail::getAsin, asinList));
        for (AmzPromotionCaptureReturnMsg.SkuMsg skuMsg : skuList) {
            AmzPromotionsSkuDetail detail = new AmzPromotionsSkuDetail();
            if (CollectionUtil.isNotEmpty(asinListFromDb)) {
                AmzPromotionsSkuDetail oredElse = asinListFromDb.stream().filter(t -> t.getAsin().equals(skuMsg.getAsin())).findFirst().orElse(null);
                if (oredElse != null) {
                    detail.setId(oredElse.getId());
                    detail.settingDefaultSystemUpdate();
                } else {
                    detail.setOriginalFrontPrice(stringToBigDecimal(skuMsg.getFrontPrice()));
                    detail.setOriginalDiscountPrice(stringToBigDecimal(skuMsg.getDiscountPrice()));
                    detail.settingDefaultSystemCreate();
                    detail.settingDefaultSystemUpdate();
                }
            }
            if (CollectionUtil.isNotEmpty(sellerSkuInfo)) {
                sellerSkuInfo.stream().filter(t -> skuMsg.getAsin().equals(t.getAsin())).findFirst().ifPresent(t -> {
                    detail.setSellerSku(t.getSellerSku());
                    detail.setTitle(t.getTitle());
                    detail.setParentAsin(t.getParentAsin());
                    detail.setImage(t.getImageUrl());
                });
            }
            detail.setOrganizationId(amzPromotions.getOrganizationId());
            detail.setFlag(amzPromotions.getFlag());
            detail.setPromotionsId(amzPromotions.getId());
            detail.setPromotionsBackId(amzPromotions.getPromotionsId());
            detail.setShopName(amzPromotions.getShopName());
            detail.setAsin(skuMsg.getAsin());
            detail.setPrice(stringToBigDecimal(skuMsg.getPrice()));
            detail.setFrontPrice(stringToBigDecimal(skuMsg.getFrontPrice()));
            detail.setDiscountPrice(stringToBigDecimal(skuMsg.getDiscountPrice()));
            detail.setUnitsSold(stringToLong(skuMsg.getUnitsSold()));
            detail.setRevenue(stringToBigDecimal(skuMsg.getRevenue()));
            detail.setAmountSpent(stringToBigDecimal(skuMsg.getAmountSpent()));
            detail.setNum(skuMsg.getNum());
            if (StringUtils.isNotEmpty(skuMsg.getPerType()) && StringUtils.isNotEmpty(skuMsg.getPerAmount())) {
                skuMsg.setPerType(resetCurrency(skuMsg.getPerType()));
                detail.setPerFunding("%".equals(skuMsg.getPerType()) ? skuMsg.getPerAmount() + skuMsg.getPerType() : skuMsg.getPerType() + skuMsg.getPerAmount());
            }
            list.add(detail);
        }
        return list;
    }

    public AmzPromotions buildCaptureAmzPromotion(AmzPromotionCaptureReturnMsg parsedObject, Date nowDate) {

        AmzPromotions one = iAmzPromotionsService.getOne(new LambdaQueryWrapper<AmzPromotions>().eq(AmzPromotions::getPromotionsId, parsedObject.getPromotionId()));
        if (one == null) {
            for (int i = 0; i < 15; i++) {
                try {
                    Thread.sleep(2000L);
                    one = iAmzPromotionsService.getOne(new LambdaQueryWrapper<AmzPromotions>().eq(AmzPromotions::getPromotionsId, parsedObject.getPromotionId()));
                    if (one != null) {
                        break;
                    }
                } catch (Exception e) {

                }
            }
        }
        if (one == null) {
            log.error("当前时间:{},promotionId:[{}]在系统中不存在!", nowDate, parsedObject.getPromotionId());
            return null;
        }
        one.setPromotionsName(parsedObject.getPromotionName());
        one.setPromotionsType(AmzPromotionsTypeEnum.valueOfLabel(parsedObject.getPromotionType()));
        if (StringUtils.isNotEmpty(parsedObject.getMarketplaceId())) {
            log.info("抓取结果返回包含marketplaceId--{}",parsedObject);
            one.setMarketplaceId(parsedObject.getMarketplaceId());
        }
        //如果抓取过来的是日本店铺且促销类型为Points promotion,将货币符号由¥改为Pt
        if (one.getShopId() != null) {
            AccountWithCountry accountWithCountry = amzPromotionsMapper.selectAccountWithCountry(one.getShopId());
            one.setFlag(MarPromotionCountryCurrencyEnum.getCurrencyFlagByCountryCode(accountWithCountry.getCountryCode(), one.getPromotionsType()));
        }

        one.setBeginTime(convertDate(parsedObject.getBeginTime()));
        one.setEndTime(convertDate(parsedObject.getEndTime()));
        one.setCreatedAt(convertDate(parsedObject.getCreateOn()));
        one.setPromotionsState(parsedObject.getPromotionState());
        one.settingDefaultSystemUpdate();
        one.setApprovalStatus(AmzPromotionApprovalEnum.APPROVED.getLabel());
        one.setSubShopBack(true);
        if (StringUtils.isEmpty(one.getVendorCode()) || StringUtils.isEmpty(one.getBillingContact())) {
            LambdaQueryWrapper<AmzPromotions> lambdaQueryWrapper = new LambdaQueryWrapper<AmzPromotions>()
                    .eq(AmzPromotions::getOrganizationId, one.getOrganizationId())
                    .eq(AmzPromotions::getShopId, one.getShopId())
                    .eq(AmzPromotions::getPromotionsType, one.getPromotionsType())
                    .in(AmzPromotions::getChannel,Arrays.asList(1,2))
                    .isNotNull(AmzPromotions::getVendorCode)
                    .ne(AmzPromotions::getVendorCode, "")
                    .last("LIMIT 1");
            AmzPromotions lastAmzPromotion = amzPromotionsMapper.selectOne(lambdaQueryWrapper);
            if (lastAmzPromotion != null) {
                one.setVendorCode(lastAmzPromotion.getVendorCode());
                one.setBillingContact(lastAmzPromotion.getBillingContact());
            }
        }

        if (AmzPromotionsStateEnum.CANCELED.value().equals(parsedObject.getPromotionState()) || AmzPromotionsStateEnum.APPROVED_AND_ENDED.value().equals(parsedObject.getPromotionState())) {
            if (one.getCompleteDate() == null) {
                Date tempCompleteDate = DateUtil.UtcToPacificDate(nowDate);
                //如果结束时间小于等于当前pst时间，则设置完结时间为结束时间，否则当前pst时间
                if (DateUtil.compareDate(one.getEndTime(), tempCompleteDate) <= 0) {
                    one.setCompleteDate(one.getEndTime());
                } else {
                    one.setCompleteDate(tempCompleteDate);
                }
            }
        }
        return one;
    }

    public Date convertDate(String date) {
        if (StringUtils.isEmpty(date)) {
            return null;
        }
        Date createdOnDate = null;
        try {
            createdOnDate = DateUtil.convertStringToDate(DateUtil.getDateStr(date), "yyyy-MM-dd HH:mm:ss");
        } catch (Exception e) {
            createdOnDate = DateUtil.convertStringToDate(DateUtil.getDateToStr(date), "yyyy-MM-dd HH:mm:ss");
        }
        return createdOnDate;
    }


    /**
     * @Description:获取coupon/promotion 参与的其他活动
     * @Author: wly
     * @Date: 2025/2/13 18:44
     * @Params: [id, type]
     * @Return: java.util.List<com.bizark.op.api.vo.promotions.AmzPromotionCouponOtherPromotionVO>
     **/
    @Override
    public List<AmzPromotionCouponOtherPromotionVO> getOtherPromotions(Long id, String type) {

        List<AmzPromotionCouponOtherPromotionVO> list = new ArrayList<>();
        Date nowPstDate = DateUtil.UtcToPacificDate(DateUtils.getNowDate());
        if ("coupon".equals(type)) {

            List<AmzPromotionsCoupon> couponList = amzPromotionsCouponMapper.selectList(new LambdaQueryWrapper<AmzPromotionsCoupon>().eq(AmzPromotionsCoupon::getCouponId, id));
            if (CollectionUtil.isEmpty(couponList)) {
                return list;
            }
            AmzPromotionsCouponsDetailVo amzPromotionsCouponsDetailVo = amzPromotionsCouponsMapper.selectAmzPromotionsCouponsDetailById(id);
            List<CouponDetailVo> couponDetailList = amzPromotionsCouponsDetailVo.getCouponDetailList();
            //当前coupon下所有asin参与的其他活动(未开始和进行中的)
            List<String> currencyAsinList = couponList.stream().map(AmzPromotionsCoupon::getAsin).distinct().collect(Collectors.toList());
            List<AmzPromotionsCoupon> otherCoupons = amzPromotionsCouponMapper.selectCurrencyPromotionOtherPromotion(couponList.get(0).getOrganizationId(),couponList.get(0).getShopId(),currencyAsinList,couponList.stream().map(AmzPromotionsCoupon::getId).collect(Collectors.toList()),nowPstDate);
            List<AmzPromotionsListVO> otherPromotions = amzPromotionsMapper.selectCurrencyPromotionOtherPromotion(couponList.get(0).getOrganizationId(), couponList.get(0).getShopId(), currencyAsinList, nowPstDate, null);

            for (AmzPromotionsCoupon amzPromotionsCoupon : couponList) {
                AmzPromotionCouponOtherPromotionVO otherPromotionVO = new AmzPromotionCouponOtherPromotionVO();
                otherPromotionVO.setCampaignId(amzPromotionsCouponsDetailVo.getCampaignId());
                otherPromotionVO.setBackId(amzPromotionsCoupon.getBackId());
                otherPromotionVO.setBeginDate(amzPromotionsCoupon.getBeginDate());
                otherPromotionVO.setAsin(amzPromotionsCoupon.getAsin());
                otherPromotionVO.setSellerSku(amzPromotionsCoupon.getSellerSku());
                otherPromotionVO.setErpSku(couponDetailList.stream().filter(t -> amzPromotionsCoupon.getId().equals(t.getCouponId())).map(CouponDetailVo::getSku).filter(t->StringUtils.isNotEmpty(t)).findFirst().orElse(null));
                otherPromotionVO.setCategory(couponDetailList.stream().filter(t -> amzPromotionsCoupon.getId().equals(t.getCouponId())).map(CouponDetailVo::getCategory).filter(t->StringUtils.isNotEmpty(t)).findFirst().orElse(null));
                otherPromotionVO.setInEffect(isInEffect(amzPromotionsCoupon.getBeginDate(), amzPromotionsCoupon.getEndDate(), nowPstDate));
                otherPromotionVO.setSupplyPrice(couponDetailList.stream().filter(t -> amzPromotionsCoupon.getId().equals(t.getCouponId())).map(CouponDetailVo::getSupplyPrice).filter(t->t!=null).findFirst().orElse(null));
                otherPromotionVO.setPageOriginalPrice(couponDetailList.stream().filter(t -> amzPromotionsCoupon.getId().equals(t.getCouponId())).map(CouponDetailVo::getPageOriginalPrice).filter(t->t!=null).findFirst().orElse(null));
                otherPromotionVO.setPageSalePrice(couponDetailList.stream().filter(t -> amzPromotionsCoupon.getId().equals(t.getCouponId())).map(CouponDetailVo::getPageSalePrice).filter(t->t!=null).findFirst().orElse(null));

                List<AmzPromotionCouponOtherPromotionVO.PromotionDetailVO> promotionDetails = getPromotionDetails(otherPromotions, amzPromotionsCoupon.getAsin(), nowPstDate, null);
                otherPromotionVO.setCouponDetails(getCouponDetails(otherCoupons, amzPromotionsCoupon.getAsin(), nowPstDate,promotionDetails));
//                otherPromotionVO.setPromotionDetails(promotionDetails);
                otherPromotionVO.setFinalDiscountPrice(getFinalDiscountPrice(otherPromotionVO, amzPromotionsCoupon,null));
                list.add(otherPromotionVO);
            }
        } else {

            AmzPromotions amzPromotions = amzPromotionsMapper.selectById(id);
            List<AmzPromotionsSkuDetail> skuDetailList = iAmzPromotionsSkuDetailService.list(Wrappers.<AmzPromotionsSkuDetail>lambdaQuery().eq(AmzPromotionsSkuDetail::getPromotionsId, amzPromotions.getId()));
            if (CollectionUtil.isEmpty(skuDetailList)) {
                return list;
            }
            List<String> currencyAsinList = skuDetailList.stream().map(AmzPromotionsSkuDetail::getAsin).distinct().collect(Collectors.toList());
            AmzPromotionApprovalDetailVo amzPromotionApprovalDetailVo = amzPromotionsMapper.selectAmzApprovalDetailPromotionsById(id);
            List<AmzPromotionApprovalDetailSkuItemVo> amzPromotionApprovalDetailSkuItemVoList = amzPromotionApprovalDetailVo.getSkuDetailList();

            //当前promotion下所有asin参与的其他活动(未开始和进行中的)
            List<AmzPromotionsListVO> otherPromotions = amzPromotionsMapper.selectCurrencyPromotionOtherPromotion(amzPromotions.getOrganizationId(), amzPromotions.getShopId(), currencyAsinList, nowPstDate, amzPromotions.getId());

            List<AmzPromotionsCoupon> otherCoupons = amzPromotionsCouponMapper.selectCurrencyPromotionOtherPromotion(amzPromotions.getOrganizationId(),amzPromotions.getShopId(),currencyAsinList,null,nowPstDate);
            for (AmzPromotionsSkuDetail detail : skuDetailList) {
                AmzPromotionCouponOtherPromotionVO otherPromotionVO = new AmzPromotionCouponOtherPromotionVO();
                otherPromotionVO.setPromotionsId(amzPromotions.getPromotionsId());
                otherPromotionVO.setDiscountPrice(detail.getDiscountPrice());
                otherPromotionVO.setBeginDate(amzPromotions.getBeginTime());
                otherPromotionVO.setAsin(detail.getAsin());
                otherPromotionVO.setSellerSku(detail.getSellerSku());
                otherPromotionVO.setErpSku(amzPromotionApprovalDetailSkuItemVoList.stream().filter(t -> detail.getAsin().equals(t.getAsin())).map(t -> t.getSku()).filter(t->StringUtils.isNotEmpty(t)).findFirst().orElse(null));
                otherPromotionVO.setCategory(amzPromotionApprovalDetailSkuItemVoList.stream().filter(t -> detail.getAsin().equals(t.getAsin())).map(t -> t.getCategory()).filter(t->StringUtils.isNotEmpty(t)).findFirst().orElse(null));
                otherPromotionVO.setInEffect(isInEffect(amzPromotions.getBeginTime(), amzPromotions.getEndTime(), nowPstDate));
                otherPromotionVO.setSupplyPrice(amzPromotionApprovalDetailSkuItemVoList.stream().filter(t -> detail.getAsin().equals(t.getAsin())).map(t -> t.getSupplyPrice()).filter(t->t!=null).findFirst().orElse(null));
                otherPromotionVO.setPageOriginalPrice(amzPromotionApprovalDetailSkuItemVoList.stream().filter(t -> detail.getAsin().equals(t.getAsin())).map(t -> t.getPageOriginalPrice()).filter(t->t!=null).findFirst().orElse(null));
                otherPromotionVO.setPageSalePrice(amzPromotionApprovalDetailSkuItemVoList.stream().filter(t -> detail.getAsin().equals(t.getAsin())).map(t -> t.getPageSalePrice()).filter(t->t!=null).findFirst().orElse(null));

                List<AmzPromotionCouponOtherPromotionVO.CouponDetailVO> couponDetails = getCouponDetails(otherCoupons, detail.getAsin(), nowPstDate, null);
//                otherPromotionVO.setCouponDetails(couponDetails);
                otherPromotionVO.setPromotionDetails(getPromotionDetails(otherPromotions, detail.getAsin(), nowPstDate,couponDetails));
                otherPromotionVO.setFinalDiscountPrice(getFinalDiscountPrice(otherPromotionVO,null,detail));
                list.add(otherPromotionVO);
            }
        }
        if (CollectionUtil.isEmpty(list)) {
            return list.stream().sorted(Comparator.comparing(AmzPromotionCouponOtherPromotionVO::getBeginDate)).collect(Collectors.toList());
        }
        return list;
    }

    private BigDecimal getFinalDiscountPrice(AmzPromotionCouponOtherPromotionVO otherPromotionVO, AmzPromotionsCoupon amzPromotionsCoupon, AmzPromotionsSkuDetail detail) {
        //供货价-当前其他coupon/promotion生效的折扣

        if (otherPromotionVO.getSupplyPrice() == null) {
            return null;
        }
        if (amzPromotionsCoupon != null) {

            //当前生效的coupon的折扣
            BigDecimal nowInEffectCouponDiscount = BigDecimal.ZERO;
            AtomicReference<BigDecimal> otherEffectCouponDiscount = new AtomicReference<>(BigDecimal.ZERO);
            if (otherPromotionVO.getInEffect()) {

                if ("%".equals(amzPromotionsCoupon.getDiscountFlag())) {
                    nowInEffectCouponDiscount = otherPromotionVO.getSupplyPrice().multiply(StringUtils.isEmpty(amzPromotionsCoupon.getDiscount())? BigDecimal.ZERO : new BigDecimal(amzPromotionsCoupon.getDiscount()).divide(new BigDecimal("100")));
                } else {
                    nowInEffectCouponDiscount = StringUtils.isEmpty(amzPromotionsCoupon.getDiscount()) ? BigDecimal.ZERO : new BigDecimal(amzPromotionsCoupon.getDiscount());
                }
            } else {
                //非当前查询coupon生效
                if (CollectionUtil.isNotEmpty(otherPromotionVO.getCouponDetails())) {
                    otherPromotionVO.getCouponDetails().stream().filter(t -> t.getInEffect()).findFirst().ifPresent(t -> {
                        if ("%".equals(t.getDiscountFlag())) {
                            otherEffectCouponDiscount.set(otherPromotionVO.getSupplyPrice().multiply(StringUtils.isEmpty(t.getDiscount()) ? BigDecimal.ZERO : new BigDecimal(t.getDiscount()).divide(new BigDecimal("100"))));
                        } else {
                            otherEffectCouponDiscount.set(StringUtils.isEmpty(t.getDiscount()) ? BigDecimal.ZERO : new BigDecimal(t.getDiscount()));
                        }
                    });
                }
                nowInEffectCouponDiscount = otherEffectCouponDiscount.get();
            }
            //当前生效的promotion的折扣
            BigDecimal nowInEffectPromotionDiscountAmount = BigDecimal.ZERO;

            AtomicReference<BigDecimal> otherPromotionDiscount = new AtomicReference<>(BigDecimal.ZERO);
            if (CollectionUtil.isNotEmpty(otherPromotionVO.getCouponDetails())) {
                otherPromotionVO.getCouponDetails().stream().filter(q->CollectionUtil.isNotEmpty(q.getPromotionDetails())).flatMap(q->q.getPromotionDetails().stream()).collect(Collectors.toList()).stream().filter(t -> t.getInEffect()).findFirst().ifPresent(t -> {
                    if ("%".equals(t.getDiscountFlag())) {
                        otherPromotionDiscount.set(otherPromotionVO.getSupplyPrice().multiply(t.getDiscount().divide(new BigDecimal("100"))));
                    } else {
                        otherPromotionDiscount.set(t.getDiscount() == null ? BigDecimal.ZERO : t.getDiscount());
                    }
                });
            }
            nowInEffectPromotionDiscountAmount = otherPromotionDiscount.get();
            return otherPromotionVO.getSupplyPrice().subtract(nowInEffectCouponDiscount).subtract(nowInEffectPromotionDiscountAmount);


        }
        if (detail != null) {
            //当前生效的promotion的折扣
            BigDecimal nowInEffectPromotionDiscount = BigDecimal.ZERO;
            AtomicReference<BigDecimal> otherEffectPromotionDiscount = new AtomicReference<>(BigDecimal.ZERO);
            if (otherPromotionVO.getInEffect()) {
                if ("%".equals(detail.getDiscountFlag())) {
                    nowInEffectPromotionDiscount = otherPromotionVO.getSupplyPrice().multiply(detail.getDiscount() == null ? BigDecimal.ZERO :detail.getDiscount()).divide(new BigDecimal("100"));
                } else {
                    nowInEffectPromotionDiscount = detail.getDiscount() == null ? BigDecimal.ZERO : detail.getDiscount();
                }
            } else {

                //非当前查询promotion生效
                if (CollectionUtil.isNotEmpty(otherPromotionVO.getPromotionDetails())) {
                    otherPromotionVO.getPromotionDetails().stream().filter(t -> t.getInEffect()).findFirst().ifPresent(t -> {
                        if ("%".equals(t.getDiscountFlag())) {
                            otherEffectPromotionDiscount.set(otherPromotionVO.getSupplyPrice().multiply(t.getDiscount() == null ? BigDecimal.ZERO : t.getDiscount()).divide(new BigDecimal("100")));
                        } else {
                            otherEffectPromotionDiscount.set(t.getDiscount() == null ? BigDecimal.ZERO : t.getDiscount());
                        }
                    });
                }
                nowInEffectPromotionDiscount = otherEffectPromotionDiscount.get();
            }
            //当前生效的coupon的折扣
            BigDecimal nowInEffectCouponDiscountAmount = BigDecimal.ZERO;

            AtomicReference<BigDecimal> otherPromotionDiscount = new AtomicReference<>(BigDecimal.ZERO);
            if (CollectionUtil.isNotEmpty(otherPromotionVO.getPromotionDetails())) {
                otherPromotionVO.getPromotionDetails().stream().filter(q->CollectionUtil.isNotEmpty(q.getCouponDetails())).flatMap(q->q.getCouponDetails().stream()).collect(Collectors.toList()).stream().filter(t -> t.getInEffect()).findFirst().ifPresent(t -> {
                    if ("%".equals(t.getDiscountFlag())) {
                        otherPromotionDiscount.set(otherPromotionVO.getSupplyPrice().multiply((StringUtils.isEmpty(t.getDiscount())? BigDecimal.ZERO : new BigDecimal(t.getDiscount())).divide(new BigDecimal("100"))));
                    } else {
                        otherPromotionDiscount.set(StringUtils.isEmpty(t.getDiscount())? BigDecimal.ZERO : new BigDecimal(t.getDiscount()));
                    }
                });
            }
            nowInEffectCouponDiscountAmount = otherPromotionDiscount.get();
            return otherPromotionVO.getSupplyPrice().subtract(nowInEffectPromotionDiscount).subtract(nowInEffectCouponDiscountAmount);
        }
        return null;
    }


    private List<AmzPromotionCouponOtherPromotionVO.PromotionDetailVO> getPromotionDetails(List<AmzPromotionsListVO> currencyCouponOtherPromotions, String asin, Date nowPstDate, List<AmzPromotionCouponOtherPromotionVO.CouponDetailVO> couponDetails) {
        if (CollectionUtil.isEmpty(currencyCouponOtherPromotions)) {
            return null;
        }
        Map<Long, List<AmzPromotionsListVO>> collect = currencyCouponOtherPromotions.stream().filter(t -> asin.equals(t.getAsin())).collect(Collectors.groupingBy(AmzPromotionsListVO::getId));
        if (CollectionUtil.isEmpty(collect)) {
            return null;
        }
        List<AmzPromotionCouponOtherPromotionVO.PromotionDetailVO> result = new ArrayList<>();
        for (Map.Entry<Long, List<AmzPromotionsListVO>> longListEntry : collect.entrySet()) {
            List<AmzPromotionsListVO> value = longListEntry.getValue();
            AmzPromotionCouponOtherPromotionVO.PromotionDetailVO promotionDetailVO = new AmzPromotionCouponOtherPromotionVO.PromotionDetailVO();
            promotionDetailVO.setPromotionsId(value.get(0).getPromotionsId());
            promotionDetailVO.setDiscountPrice(value.get(0).getDiscountPrice());
            promotionDetailVO.setPromotionType(value.get(0).getPromotionsType());
            promotionDetailVO.setPromotionName(value.get(0).getPromotionsName());
            promotionDetailVO.setBeginDate(value.get(0).getBeginTime());
            promotionDetailVO.setEndDate(value.get(0).getEndTime());
            promotionDetailVO.setDiscount(value.get(0).getDiscount());
            promotionDetailVO.setDiscountFlag(value.get(0).getDiscountFlag());
            promotionDetailVO.setPromotionDiscount(value.get(0).getDiscountString());
            promotionDetailVO.setOneProductDiscount(value.get(0).getPerFunding());
            promotionDetailVO.setInEffect(isInEffect(value.get(0).getBeginTime(), value.get(0).getEndTime(), nowPstDate));
            if (CollectionUtil.isNotEmpty(couponDetails)) {
                List<AmzPromotionCouponOtherPromotionVO.CouponDetailVO> collect1 = couponDetails.stream().filter(t -> isInEffect(promotionDetailVO.getBeginDate(), promotionDetailVO.getEndDate(), t.getBeginDate()) || isInEffect(promotionDetailVO.getBeginDate(), promotionDetailVO.getEndDate(), t.getEndDate())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(collect1)) {
                    promotionDetailVO.setCouponDetails(collect1.stream().sorted(Comparator.comparing(AmzPromotionCouponOtherPromotionVO.CouponDetailVO::getBeginDate)).collect(Collectors.toList()));
                }
            }
            result.add(promotionDetailVO);
        }
        if (CollectionUtil.isNotEmpty(result)) {
            return result.stream().sorted(Comparator.comparing(AmzPromotionCouponOtherPromotionVO.PromotionDetailVO::getBeginDate)).collect(Collectors.toList());
        }
        return result;
    }

    private List<AmzPromotionCouponOtherPromotionVO.CouponDetailVO> getCouponDetails(List<AmzPromotionsCoupon> currencyCouponOtherCoupons, String asin, Date nowPstDate, List<AmzPromotionCouponOtherPromotionVO.PromotionDetailVO> promotionDetails) {
        if (CollectionUtil.isEmpty(currencyCouponOtherCoupons)) {
            return null;
        }
        Map<Long, List<AmzPromotionsCoupon>> collect = currencyCouponOtherCoupons.stream().filter(t -> asin.equals(t.getAsin())).collect(Collectors.groupingBy(AmzPromotionsCoupon::getId));
        if (CollectionUtil.isEmpty(collect)) {
            return null;
        }
        List<AmzPromotionCouponOtherPromotionVO.CouponDetailVO> result = new ArrayList<>();
        for (Map.Entry<Long, List<AmzPromotionsCoupon>> longListEntry : collect.entrySet()) {
            List<AmzPromotionsCoupon> value = longListEntry.getValue();
            AmzPromotionCouponOtherPromotionVO.CouponDetailVO couponDetailVO = new AmzPromotionCouponOtherPromotionVO.CouponDetailVO();
            couponDetailVO.setCouponName(value.get(0).getCouponName());
            couponDetailVO.setCampaignId(value.get(0).getCampaignId());
            couponDetailVO.setBackId(value.get(0).getBackId());
            couponDetailVO.setBeginDate(value.get(0).getBeginDate());
            couponDetailVO.setEndDate(value.get(0).getEndDate());
            couponDetailVO.setTotalBudget(value.get(0).getBudget());
            couponDetailVO.setInEffect(isInEffect(value.get(0).getBeginDate(), value.get(0).getEndDate(), nowPstDate));
            couponDetailVO.setDiscount(value.get(0).getDiscount());
            couponDetailVO.setDiscountFlag(value.get(0).getDiscountFlag());
            if (StringUtils.isNotEmpty(couponDetailVO.getDiscount())) {
                if ("%".equals(couponDetailVO.getDiscountFlag())) {
                    couponDetailVO.setPromotionDiscount(couponDetailVO.getDiscount() + "%");
                } else {
                    couponDetailVO.setPromotionDiscount((StringUtils.isEmpty(couponDetailVO.getDiscountFlag()) ? "$" : couponDetailVO.getDiscountFlag()) + couponDetailVO.getDiscount());
                }
            }
            if (CollectionUtil.isNotEmpty(promotionDetails)) {
                //取出promotionDetails里开始时间结束时间在couponDetailVO开始时间结束时间内的
                List<AmzPromotionCouponOtherPromotionVO.PromotionDetailVO> collect1 = promotionDetails.stream().filter(t -> isInEffect(couponDetailVO.getBeginDate(), couponDetailVO.getEndDate(), t.getBeginDate()) || isInEffect(couponDetailVO.getBeginDate(), couponDetailVO.getEndDate(), t.getEndDate())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(collect1)) {
                    couponDetailVO.setPromotionDetails(collect1.stream().sorted(Comparator.comparing(AmzPromotionCouponOtherPromotionVO.PromotionDetailVO::getBeginDate)).collect(Collectors.toList()));
                }
            }
            result.add(couponDetailVO);
        }
        if (CollectionUtil.isNotEmpty(result)) {
            return result.stream().sorted(Comparator.comparing(AmzPromotionCouponOtherPromotionVO.CouponDetailVO::getBeginDate)).collect(Collectors.toList());
        }
        return result;

    }

    private boolean isInEffect(Date beginTime, Date endTime, Date nowDate) {
        if (beginTime == null && endTime == null) {
            return false;
        }
        if (endTime == null) {
            endTime = cn.hutool.core.date.DateUtil.endOfDay(beginTime);
        }
        if (DateUtil.compareDate(beginTime, nowDate) <= 0 && DateUtil.compareDate(nowDate, endTime) <= 0) {
            return true;
        }
        return false;
    }


    @Override
    @Async("threadPoolTaskExecutor")
    public void rpaCancelPromotionAutoSync(Long id) {
        try {
            Thread.sleep(120000L);
            if (id == null) {
                log.error("rpaCancelPromotionAutoSync id is null");
                return;
            }
            AmzPromotions amzPromotions = amzPromotionsMapper.selectById(id);
            List<AmzPromotions> promotionsList = Collections.singletonList(amzPromotions);
            List<Long> idList = promotionsList.stream().map(AmzPromotions::getId).collect(Collectors.toList());
            List<AmzPromotionsSync> amzPromotionsList = amzPromotionsSyncMapper.getAmzPromotionsList(idList.toArray(new Long[idList.size()]));
            if (CollectionUtil.isEmpty(amzPromotionsList)) {
                log.error("rpaCancelPromotionAutoSync amzPromotionsList is null");
                return;
            }
            List<AccountWithPromotionsList> list = getSyncPromotionsList(amzPromotionsList, promotionsList);
            JSONArray objects = JSONArray.parseArray(JSONArray.toJSONString(list));
            log.info("RPA取消promotion返回成功后同步发起promotion消息:{}", objects.toJSONString());
            rabbitTemplate.convertAndSend(MQDefine.AMZ_PROMOTIONS_PROMOTION_SYNC_SEND, objects);
            List<Long> collect = amzPromotionsList.stream().map(AmzPromotionsSync::getId).distinct().collect(Collectors.toList());
            addPromotionSyncInitLogWithName(collect.toArray(new Long[0]), getOrgId(), null, AmzPromotionsOperateTypeEnum.SYNC_INIT.value(), "system");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("rpaCancelPromotionAutoSync error:{}", e.getMessage());
        }

    }


    /**
     * 抓取coupon
     * @param coupons
     * @param contextId
     * @param authUserEntity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void captureCoupon(List<AmzPromotionsCoupons> coupons, Integer contextId, AuthUserDetails authUserEntity) {
        log.info("当前时间--{}---抓取人--{}--抓取coupon信息", DateUtils.getNowDate(), authUserEntity.getName());
        log.info("抓取coupon信息原始信息:{}", JSONArray.toJSONString(coupons));
        boolean anyMatch = coupons.stream().anyMatch(t -> StringUtils.isEmpty(t.getCampaignId()) || t.getShopId() == null);
        AssertUtil.isFalse(anyMatch, "请选择店铺和填写Coupon ID！");
        boolean anyMatch1 = coupons.stream().collect(Collectors.groupingBy(t->t.getCampaignId() + "_" + t.getShopId().toString())).entrySet().stream().anyMatch(t -> t.getValue().size() > 1);
        AssertUtil.isFalse(anyMatch1, "店铺+Coupon ID不能重复！");
        coupons.forEach(r -> {
            r.setCampaignId(r.getCampaignId().trim());
        });

        for (AmzPromotionsCoupons t : coupons) {
            List<AmzPromotionsCoupons> selectList = amzPromotionsCouponsMapper.selectList(
                    new LambdaQueryWrapper<AmzPromotionsCoupons>()
                    .eq(AmzPromotionsCoupons::getShopId,t.getShopId())
                    .eq(AmzPromotionsCoupons::getCampaignId, t.getCampaignId())
                    .eq(AmzPromotionsCoupons::getOrganizationId, contextId));
            if (CollectionUtil.isNotEmpty(selectList)) {
                throw new ErpCommonException(String.format("Coupon ID:[%s]在系统中已存在!", t.getCampaignId()));
            }
        }

        List<Long> shopIdList = coupons.stream().map(AmzPromotionsCoupons::getShopId).distinct().collect(Collectors.toList());
        List<Account> accounts = accountService.listByIds(shopIdList);
        if (CollectionUtil.isEmpty(accounts) || accounts.size() != shopIdList.size()) {
            throw new ErpCommonException(String.format("店铺id:[%s]不存在!", shopIdList.stream().map(Object::toString).collect(Collectors.joining(","))));
        }
        List<String> storeNameIsNull = accounts.stream().filter(t -> StringUtils.isEmpty(t.getStoreName())).map(Account::getTitle).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(storeNameIsNull)) {
            throw new ErpCommonException(String.format("浏览器店铺名称为空:[%s]!", String.join(",", storeNameIsNull)));
        }

        List<AmzPromotionsCoupons> couponsList = new ArrayList<>();
        List<AmzPromotionsCouponOperateLog> logList = new ArrayList<>();

        List<PromotionAccountVo> promotionAccountVos = iAmzPromotionsService.selectPromotionAccountList(null, shopIdList.stream().map(t -> t.intValue()).collect(Collectors.toList()).toArray(new Integer[0]), contextId);
        coupons.forEach(campaign -> {
            Account account = accounts.stream().filter(t -> campaign.getShopId().equals(t.getId().longValue())).findFirst().orElse(null);
            AmzPromotionsCoupons amzPromotionsCoupons = new AmzPromotionsCoupons();

            amzPromotionsCoupons.settingDefaultCreate();
            amzPromotionsCoupons.settingDefaultUpdate();
            amzPromotionsCoupons.setOrganizationId(contextId);
            amzPromotionsCoupons.setCampaignId(campaign.getCampaignId());
            amzPromotionsCoupons.setPromotionName(campaign.getPromotionName());
            amzPromotionsCoupons.setShopId(campaign.getShopId());
            amzPromotionsCoupons.setUploadType(0);
            amzPromotionsCoupons.setChannel(Optional.ofNullable(AmzPromotionsChannelEnum.valueOfLabel(account.getAccountType())).orElse(1));
            amzPromotionsCoupons.setCreatedAt(DateUtil.UtcToPacificDate(amzPromotionsCoupons.getCreatedAt()));
            amzPromotionsCoupons.setShopName(account.getAccountInit());
            String vendorCode = promotionAccountVos.stream().filter(r -> account.getId().equals(r.getId())).findFirst().get().getVendorCode();
            amzPromotionsCoupons.setVendorCode(StringUtils.isNotEmpty(vendorCode) ? vendorCode.split(",")[0] : null);
            amzPromotionsCoupons.setFlag(MarPromotionCountryCurrencyEnum.getAmazonPromotionCurrencyFlagByCountryCode(account.getCountryCode()));
            couponsList.add(amzPromotionsCoupons);

        });
        iAmzPromotionsCouponsService.saveBatch(couponsList);
        couponsList.forEach(t -> {
            AmzPromotionsCouponOperateLog log = new AmzPromotionsCouponOperateLog();
            log.setOperateType(AmzCouponOperateTypeEnum.CAPTURE_COUPON.value());
            log.setOrganizationId(contextId);
            log.setCouponsId(t.getId());
            log.settingDefaultCreate();
            log.settingDefaultUpdate();
            logList.add(log);
        });
        iAmzPromotionsCouponOperateLogService.saveBatch(logList);

        List<AmzPromotionsCouponsSyncListVo> result = buildCouponCaptureRequest(couponsList, accounts);
        log.info("抓取coupon信息:{}", JSONArray.toJSONString(result));
        rabbitTemplate.convertAndSend(MQDefine.AMZ_PROMOTIONS_COUPONS_SYNC_SEND, result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void captureModifyCoupon(List<AmzPromotionsCoupons> coupons, Integer contextId, AuthUserDetails authUser) {
        log.info("当前时间--{}--抓取修改人--{}---抓取修改coupon信息",DateUtils.getNowDate(),authUser.getName());
        log.info("抓取修改coupon原始信息--{}", JSONArray.toJSONString(coupons));

        if (coupons.stream().anyMatch(t -> t.getId() == null)){
            throw new ErpCommonException("请选择要修改的coupon！");
        }
        boolean anyMatch = coupons.stream().anyMatch(t -> StringUtils.isEmpty(t.getCampaignId()) || t.getShopId() == null);
        AssertUtil.isFalse(anyMatch, "请选择店铺和填写coupon ID！");
        boolean anyMatch1 = coupons.stream().collect(Collectors.groupingBy(t->t.getCampaignId() + "_" + t.getShopId().toString())).entrySet().stream().anyMatch(t -> t.getValue().size() > 1);
        AssertUtil.isFalse(anyMatch1, "店铺+coupon ID不能重复！");
        coupons.forEach(r -> {
            r.setCampaignId(r.getCampaignId().trim());
            r.settingDefaultUpdate();
        });

        List<Long> modifyIdList = coupons.stream().map(AmzPromotionsCoupons::getId).collect(Collectors.toList());
        List<AmzPromotionsCoupons> modifyCouponsList = iAmzPromotionsCouponsService.listByIds(modifyIdList);
        if (CollectionUtil.isEmpty(modifyCouponsList)) {
            throw new ErpCommonException("勾选数据不存在！");
        }
        if (modifyCouponsList.size() != coupons.size()) {
            throw new ErpCommonException("勾选数据部分不存在！");
        }

        List<String> notMatch = modifyCouponsList.stream().filter(t -> StringUtils.isNotEmpty(t.getApprovalStatus()) || t.getActiveState() != null).map(t -> t.getCampaignId()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(notMatch)) {
            throw new ErpCommonException("勾选数据审批状态不为空或者promotion状态不为空，不能修改！");
        }
        List<AmzPromotionsCoupon> skuDetailList = iAmzPromotionsCouponService.list(Wrappers.<AmzPromotionsCoupon>lambdaQuery().in(AmzPromotionsCoupon::getCouponId, modifyIdList));
        if (CollectionUtil.isNotEmpty(skuDetailList)) {
            throw new ErpCommonException("勾选数据存在优惠券明细，不能抓取，请同步！");
        }
        for (AmzPromotionsCoupons t : coupons) {
            List<AmzPromotionsCoupons> selectList = iAmzPromotionsCouponsService.list(new LambdaQueryWrapper<AmzPromotionsCoupons>()
                    .eq(AmzPromotionsCoupons::getShopId,t.getShopId())
                    .eq(AmzPromotionsCoupons::getCampaignId, t.getCampaignId())
                    .ne(AmzPromotionsCoupons::getId, t.getId()));
            if (CollectionUtil.isNotEmpty(selectList)) {
                throw new ErpCommonException(String.format("coupon ID:[%s]在系统中已存在!", t.getCampaignId()));
            }
        }

        List<Long> shopIdList = coupons.stream().map(AmzPromotionsCoupons::getShopId).distinct().collect(Collectors.toList());
        List<Account> accounts = accountService.listByIds(shopIdList);
        if (CollectionUtil.isEmpty(accounts) || accounts.size() != shopIdList.size()) {
            throw new ErpCommonException(String.format("店铺id:[%s]不存在!", shopIdList.stream().map(Object::toString).collect(Collectors.joining(","))));
        }
        List<String> storeNameIsNull = accounts.stream().filter(t -> StringUtils.isEmpty(t.getStoreName())).map(Account::getTitle).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(storeNameIsNull)) {
            throw new ErpCommonException(String.format("浏览器店铺名称为空:[%s]!", String.join(",", storeNameIsNull)));
        }

        List<PromotionAccountVo> promotionAccountVos = iAmzPromotionsService.selectPromotionAccountList(null, shopIdList.stream().map(t -> t.intValue()).collect(Collectors.toList()).toArray(new Integer[0]), contextId);
        log.info("修改前抓取coupon信息--{}", JSONObject.toJSONString(modifyCouponsList));
        List<AmzPromotionsCouponOperateLog> logList = new ArrayList<>();


        for (AmzPromotionsCoupons amzPromotionsCoupons : coupons) {
            StringBuilder sb = new StringBuilder();
            AmzPromotionsCoupons existAmzPromotionCoupons = modifyCouponsList.stream().filter(t -> t.getId().equals(amzPromotionsCoupons.getId())).findFirst().get();
            String existCampaignId = existAmzPromotionCoupons.getCampaignId();
            if (!amzPromotionsCoupons.getCampaignId().equals(existCampaignId)) {
                sb.append("coupon ID:").append(existCampaignId).append("->").append(amzPromotionsCoupons.getCampaignId()).append(";");
            }
            if (!amzPromotionsCoupons.getShopId().equals(existAmzPromotionCoupons.getShopId())) {
                sb.append("店铺:").append(accountService.getById(existAmzPromotionCoupons.getShopId()).getTitle()).append("->").append(accountService.getById(amzPromotionsCoupons.getShopId()).getTitle()).append(";");
            }
            if (StringUtils.isNotEmpty(amzPromotionsCoupons.getPromotionName()) && !amzPromotionsCoupons.getPromotionName().equals(existAmzPromotionCoupons.getPromotionName())) {
                sb.append("活动名称:").append(existAmzPromotionCoupons.getPromotionName()).append("->").append(amzPromotionsCoupons.getPromotionName()).append(";");
            }
            AmzPromotionsCouponOperateLog log = new AmzPromotionsCouponOperateLog();
            log.setCouponsId(amzPromotionsCoupons.getId());
            log.setOperateType(AmzCouponOperateTypeEnum.CAPTURE_COUPON.value());
            log.setOrganizationId(contextId);
            log.settingDefaultCreate();
            log.setDetail(sb.toString());
            logList.add(log);
            Account account = accounts.stream().filter(t -> amzPromotionsCoupons.getShopId().equals(t.getId().longValue())).findFirst().orElse(null);
            amzPromotionsCoupons.setChannel(Optional.ofNullable(AmzPromotionsChannelEnum.valueOfLabel(account.getAccountType())).orElse(1));
            amzPromotionsCoupons.setShopName(account.getAccountInit());
            String vendorCode = promotionAccountVos.stream().filter(r -> account.getId().equals(r.getId())).findFirst().get().getVendorCode();
            amzPromotionsCoupons.setVendorCode(StringUtils.isNotEmpty(vendorCode) ? vendorCode.split(",")[0] : null);
            amzPromotionsCoupons.setFlag(MarPromotionCountryCurrencyEnum.getAmazonPromotionCurrencyFlagByCountryCode(account.getCountryCode()));
        }

        iAmzPromotionsCouponsService.updateBatchById(coupons);
        iAmzPromotionsCouponOperateLogService.saveBatch(logList);

        List<AmzPromotionsCouponsSyncListVo> requests = buildCouponCaptureRequest(coupons, accounts);
        log.info("修改后抓取coupon信息:{}",JSONArray.toJSONString(requests));
        rabbitTemplate.convertAndSend(MQDefine.AMZ_PROMOTIONS_COUPONS_SYNC_SEND, requests);
    }


    private List<AmzPromotionsCouponsSyncListVo> buildCouponCaptureRequest(List<AmzPromotionsCoupons> couponsList,List<Account> accountList) {
        List<AmzPromotionsCouponsSyncListVo> request = new ArrayList<>();

        Map<Long, List<AmzPromotionsCoupons>> collect = couponsList.stream().collect(Collectors.groupingBy(t -> t.getShopId()));
        collect.forEach((shopId,values)->{
            AmzPromotionsCouponsSyncListVo listVo = new AmzPromotionsCouponsSyncListVo();
            listVo.setShopId(shopId);
            listVo.setWebStoreName(accountList.stream().filter(account->shopId.equals(account.getId().longValue())).findFirst().get().getStoreName());
            listVo.setChannel(values.get(0).getChannel());
            List<AmzPromotionsCouponsSyncListVo.AmzPromotionsCouponsSyncVo> campaignList = new ArrayList<>();
            for (AmzPromotionsCoupons value : values) {
                AmzPromotionsCouponsSyncListVo.AmzPromotionsCouponsSyncVo campaign = new AmzPromotionsCouponsSyncListVo.AmzPromotionsCouponsSyncVo();
                campaign.setCampaignId(value.getId());
                campaign.setCampaignName(value.getPromotionName());
                campaign.setBackId(value.getCampaignId());
//                campaign.setCouponList();
                campaignList.add(campaign);
            }
            listVo.setCampaignList(campaignList);
            request.add(listVo);
        });
        return request;
    }


    public void processCaptureCoupon(AmzPromotionsSyncCouponsReceiveMsg msg, String storeName) {

        if (msg.getCampaignId() == null) {
            log.error("抓取coupon返回活动ID为空--{}", JSONObject.toJSONString(msg));
            return;
        }

        AmzPromotionsCoupons couponsById = iAmzPromotionsCouponsService.getById(msg.getCampaignId());
        if (couponsById == null) {
            for (int i = 0; i < 15; i++) {
                try {
                    TimeUnit.SECONDS.sleep(2);
                } catch (Exception e) {

                }
                couponsById = iAmzPromotionsCouponsService.getById(msg.getCampaignId());
                if (couponsById != null) {
                    break;
                }
            }
        }
        if (couponsById == null) {
            log.error("抓取coupon返回数据在系统中无此数据--{}---{}", JSONObject.toJSON(msg), storeName);
            return;
        }
        Account account = accountService.getById(couponsById.getShopId());
        couponsById.setId(couponsById.getId());

        couponsById.setActiveState(msg.getCampaignState());
        couponsById.setPromotionName(msg.getCampaignName());

        couponsById.setShareBudget(stringToBigDecimal(msg.getShareBudget()));
        couponsById.setCampaignId(msg.getCouponsBackId());
        couponsById.setApprovalStatus(AmzPromotionApprovalEnum.APPROVED.getLabel());
        couponsById.setSubShopBack(true);
        couponsById.settingDefaultSystemUpdate();

        Date createOn = null;
        if (StringUtils.isNotEmpty(msg.getCreatedOn())) {
            try {
                String createdAt = DateUtil.MMMMddyyyyToyyyyMMdd(msg.getCreatedOn());
                if (StringUtils.isNotEmpty(createdAt)) {
                    createOn = DateUtil.convertStringToDate(createdAt + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
                }
            } catch (Exception e) {
                log.error("抓取coupon返回创建时间格式错误--{}---{}", JSONObject.toJSON(msg), storeName);
            }
        }
        if (createOn != null) {
            couponsById.setCreatedAt(createOn);
        }
        couponsById.setErrorExcel(msg.getErrorExcel());
        iAmzPromotionsCouponsService.updateById(couponsById);

        AmzPromotionsCouponOperateLog couponsLog = new AmzPromotionsCouponOperateLog();
        couponsLog.setCouponsId(couponsById.getId());
        couponsLog.settingDefaultSystemCreate();
        couponsLog.setOrganizationId(couponsById.getOrganizationId());
        couponsLog.setOperateType(AmzCouponOperateTypeEnum.RPA_CAPTURE_COUPON.value());
        iAmzPromotionsCouponOperateLogService.save(couponsLog);
        List<AmzPromotionsCoupon> couponList = new ArrayList<>();
        if (CollectionUtil.isEmpty(msg.getCouponList())) {
            log.error("抓取coupon返回优惠券列表为空--{}", JSONObject.toJSONString(msg));
            if (StringUtils.isNotEmpty(msg.getErrorExcel())) {
                //读取错误信息表格获取优惠券信息
                amzPromotionFileService.getCouponExcel(msg.getErrorExcel(), couponsById.getShopId().intValue(), couponsById);

            }
        }else {
            for (AmzPromotionsSyncCouponsReceiveMsg.AmzPromotionsSyncCouponReceiveMsg couponReceiveMsg : msg.getCouponList()) {
                if (StringUtils.isEmpty(couponReceiveMsg.getCouponBackId())) {
                    continue;
                }
                AmzPromotionsCoupon coupon = new AmzPromotionsCoupon();
                AmzPromotionsCoupon couponExist = iAmzPromotionsCouponService.getOne(Wrappers.lambdaQuery(AmzPromotionsCoupon.class)
                        .eq(AmzPromotionsCoupon::getCouponId, couponsById.getId())
                        .eq(AmzPromotionsCoupon::getBackId, couponReceiveMsg.getCouponBackId())
                        .last("limit 1"));
                coupon.setId(Optional.ofNullable(couponExist).map(AmzPromotionsCoupon::getId).orElse(null));
                coupon.setCouponState(couponReceiveMsg.getCouponState());
                coupon.setCouponName(couponReceiveMsg.getCouponName());
                coupon.setAsin(CollectionUtil.isNotEmpty(couponReceiveMsg.getAsin()) ? couponReceiveMsg.getAsin().get(0) : null);
                if (CollectionUtil.isNotEmpty(couponReceiveMsg.getAsin())) {
                    List<AmzPromotionsCouponSkuInfoVO> amzPromotionsCouponSkuInfoVOS = amzPromotionAccountMapper.selectSellerSkuByShopIdAndAsin(account.getId().longValue(), Arrays.asList(coupon.getAsin()));
                    coupon.setSellerSku(CollectionUtil.isNotEmpty(amzPromotionsCouponSkuInfoVOS) ? amzPromotionsCouponSkuInfoVOS.get(0).getSellerSku() : null);
                }

                if (StringUtils.isNotEmpty(couponReceiveMsg.getDiscount())) {
                    if (couponReceiveMsg.getDiscount().contains("%")) {
                        coupon.setDiscount(couponReceiveMsg.getDiscount().replaceAll("[^\\d.]", ""));
                        coupon.setDiscountFlag("%");
                    } else {
                        coupon.setDiscount(couponReceiveMsg.getDiscount().replaceAll("[^\\d.]", ""));
                        coupon.setDiscountFlag(MarPromotionCountryCurrencyEnum.getAmazonPromotionCurrencyFlagByCountryCode(account.getCountryCode()));
                    }
                }

                coupon.setBudget(stringToBigDecimal(couponReceiveMsg.getBudget()));
                coupon.setWebsiteDisplayName(couponReceiveMsg.getWebsiteDisplayName());
                coupon.setAmzFrontName(couponReceiveMsg.getWebsiteDisplayName());
                coupon.setCouponPage(couponReceiveMsg.getCouponPage());
                if (StringUtils.isNotEmpty(couponReceiveMsg.getCouponPage()) && couponReceiveMsg.getCouponPage().contains("/")) {
                    coupon.setCouponPageId(couponReceiveMsg.getCouponPage().substring(couponReceiveMsg.getCouponPage().lastIndexOf("/") + 1));
                }
                coupon.setBeginDate(captureCouponDateConvert(couponsById.getId(), couponReceiveMsg.getBeginDateStr(), "开始时间", "抓取"));
                coupon.setEndDate(captureCouponDateConvert(couponsById.getId(), couponReceiveMsg.getEndDateStr(), "结束时间", "抓取"));
                coupon.setCouponTitle(couponReceiveMsg.getCouponName());
                coupon.setOrganizationId(couponsById.getOrganizationId());
                coupon.setCouponId(couponsById.getId());
                coupon.setFlag(couponsById.getFlag());
                coupon.setShopId(couponsById.getShopId());
                coupon.setBackId(couponReceiveMsg.getCouponBackId());
                coupon.setApprovalStatus(AmzPromotionApprovalEnum.APPROVED.getLabel());

                coupon.settingDefaultSystemUpdate();
                if (coupon.getId() == null) {
                    coupon.setCreatedAt(couponsById.getCreatedAt());
                    coupon.setCreatedBy(couponsById.getCreatedBy());
                    coupon.setCreatedName(couponsById.getCreatedName());
                }
                couponList.add(coupon);

            }
            if (CollectionUtil.isNotEmpty(couponList)) {

                couponsById.setBeginDate(couponList.stream().min(Comparator.comparing(AmzPromotionsCoupon::getBeginDate)).get().getBeginDate());
                couponsById.setEndDate(couponList.stream().max(Comparator.comparing(AmzPromotionsCoupon::getEndDate)).get().getEndDate());
                iAmzPromotionsCouponsService.updateById(couponsById);
                iAmzPromotionsCouponService.saveOrUpdateBatch(couponList);

            }
        }

        threadPoolTaskExecutor.execute(() -> {
            //重算促销费用
            try {
                TimeUnit.SECONDS.sleep(40);

                log.info("开始重算促销费用--{}", JSONObject.toJSONString(msg));
                AmzPromotionsCoupons selectById = amzPromotionsCouponsMapper.selectById(msg.getCampaignId());
                Date start = DateUtils.parseDate(DateUtil.convertDateToString(selectById.getBeginDate()));
                Date end = DateUtils.parseDate(DateUtil.convertDateToString(selectById.getEndDate()));
                Date currencyPstDate = DateUtils.parseDate(DateUtil.convertDateToString(DateUtils.utcToPst(DateUtils.getNowDate()), "yyyy-MM-dd"));
                if (currencyPstDate.before(start)) {
                    return;
                }
                if (currencyPstDate.before(end)) {
                    end = currencyPstDate;
                }
                while (!start.after(end)) {
                    amzPromotionFeeService.updateCouponFeeHisNew(start, Collections.singletonList(selectById.getId()), false);
                    start = DateUtils.nDaysAfter(1, start);
                }
            } catch (Exception e) {
                log.error("重算促销费用失败--{}--message--{}", JSONObject.toJSONString(msg), e.getMessage());
            }
        });

    }


    private Date captureCouponDateConvert(Long id,String date, String type,String operateType) {

        try {
            if (StringUtils.isNotEmpty(date)) {
                if ("开始时间".equals(type)) {
                    try {
                        return DateUtil.convertStringToDate(date, "yyyy-MM-dd HH:mm:ss");
                    } catch (Exception e) {
                        return DateUtil.convertStringToDate(DateUtil.convertOriginalDate(date),"yyyy-MM-dd HH:mm:ss");
                    }
                }else {
                    try {
                        return DateUtil.convertStringToDate(DateUtil.convertDateToString(DateUtil.convertStringToDate(date, "yyyy-MM-dd")) + " 23:59:59","yyyy-MM-dd HH:mm:ss");

                    } catch (Exception e) {
                        return DateUtil.convertStringToDate(DateUtil.convertDateToString(DateUtil.convertStringToDate(DateUtil.convertOriginalDate(date), "yyyy-MM-dd HH:mm:ss")) + " 23:59:59", "yyyy-MM-dd HH:mm:ss");

                    }

                }
            }else {
                return null;
            }
        } catch (Exception e) {
            log.error(operateType + "coupon消息返回" + type +"格式错误--id[{}]",id);
            return null;
        }
    }
}
