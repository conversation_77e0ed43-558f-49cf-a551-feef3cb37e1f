package com.bizark.op.service.service.tiktok.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.op.api.amazon.common.StringUtil;
import com.bizark.op.api.cons.ScTicketConstant;
import com.bizark.op.api.cons.TikTokConstant;
import com.bizark.op.api.enm.TikTokApiEnums;
import com.bizark.op.api.enm.sale.AccountSaleChannelEnum;
import com.bizark.op.api.enm.ticket.*;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.mar.MarTkCouponInfo;
import com.bizark.op.api.entity.op.mar.contactCustomer.entity.MarSendMessageOrders;
import com.bizark.op.api.entity.op.returns.entity.ReturnInfoEntity;
import com.bizark.op.api.entity.op.ticket.*;
import com.bizark.op.api.entity.op.ticket.VO.SaleOrderVoTicket;
import com.bizark.op.api.entity.op.ticket.email.ScEmailSend;
import com.bizark.op.api.entity.op.titok.request.message.MessageSendRequest;
import com.bizark.op.api.entity.op.titok.request.message.MessageUploadImage;
import com.bizark.op.api.entity.op.titok.response.message.*;
import com.bizark.op.api.entity.op.titok.webhook.TikTokConvConData;
import com.bizark.op.api.entity.op.titok.webhook.TikTokWebhookMsgData;
import com.bizark.op.api.parameter.util.TikTokUtil;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.mar.contactCustomer.MarSendMessageOrdersService;
import com.bizark.op.api.service.ticket.*;
import com.bizark.op.api.service.tiktok.ITiktokMessageService;
import com.bizark.op.common.annotation.RepeatSubmit;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.*;
import com.bizark.op.service.mapper.mar.MarTkCouponInfoMapper;
import com.bizark.op.service.mapper.refund.OrderRefundRequestInfoMapper;
import com.bizark.op.service.mapper.returns.ReturnInfoMapper;
import com.bizark.op.service.mapper.sale.ProductChannelsMapper;
import com.bizark.op.service.mapper.ticket.SaleOrdersMapper;
import com.bizark.op.service.mapper.ticket.ScStationLetterMapper;
import com.bizark.op.service.mapper.ticket.TicketAttachesMapper;
import com.bizark.op.service.mapper.ticket.email.ScEmailSendMapper;
import com.bizark.op.service.mapper.tiktok.TiktokReturnRefundItemMapper;
import com.bizark.op.service.util.third.qywx.QywxUtil;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.github.pagehelper.PageHelper;
import com.xxl.conf.core.annotation.XxlConf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.alibaba.fastjson.JSON.toJSONString;
//import com.bizark.op.api.parameter.util.TikTokUtil;

/**
 * <AUTHOR>
 * @Date 2023 08 29 21 05
 **/
@Service
@Slf4j
public class TiktokMessageServiceImpl implements ITiktokMessageService {




    @Autowired
    private AccountService accountService;


    @Resource
    private TikTokUtil tikTokUtil;

    @Autowired
    @Lazy
    private IScCustomerService scCustomerService;

    @Autowired
    private IScStationLetterMessageService scStationLetterMessageService;

    @Autowired
    private IScStationLetterService scStationLetterService;

    @Autowired
    private IScTicketService scTicketService;


    @Autowired
    private IScTicketLogService scTicketLogService;

    @XxlConf(value = "bizark-erp.tiktok.switch",defaultValue = "false")
    public static Boolean TIKTOK_SWITCH;

    @Autowired
    private MarSendMessageOrdersService marSendMessageOrdersService;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;


    @Autowired
    private ScStationLetterMapper scStationLetterMapper;

    @Autowired
    private ProductChannelsMapper productChannelsMapper;


    @Autowired
    private SaleOrdersMapper saleOrdersMapper;


    @Autowired
    private ReturnInfoMapper returnInfoMapper;

    @Autowired
    private OrderRefundRequestInfoMapper orderRefundRequestInfoMapper;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;


    /**
     * 原始tTK退货信息表
     */
    @Autowired
    private TiktokReturnRefundItemMapper tiktokReturnRefundItemMapper;

    @Autowired
    private MarTkCouponInfoMapper marTkCouponInfoMapper;

    @Autowired
    private TicketAttachesMapper ticketAttachesMapper;

    @Value("${transition.file.url}")
    private String queryTransitionUrl;

    @Value("${algorithm_query_classify_question_url}")
    private String queryAlgorithmClassifyQuestionUrl;


    /**
     * @param
     * @param
     * @param authUserEntity
     * @description: 重试发送消息
     * @author: Moore
     * @date: 2023/10/19 15:53
     * @return: java.lang.String
     **/
    @Override
    @Transactional
    public int sendTiktokConvContentRetryMessage(MessageSendRequest sendRequest, UserEntity authUserEntity){

        Integer flagMessage = 0;

        if (sendRequest.getTicketId() == null  || StringUtil.isEmpty(sendRequest.getConvShortId())) {
            throw new CustomException("工单ID/会话ID获取失败!");
        }
        ScTicket scTicket = scTicketService.selectScTicketByBaseTicketId(sendRequest.getTicketId());
        if (null == scTicket || null == scTicket.getSourceId()) {
            throw new CustomException("工单信息获取失败!");
        }
        if(StringUtil.isEmpty(sendRequest.getContent())&&CollectionUtils.isEmpty(sendRequest.getFiles())){
            throw new CustomException("发送内容不能为空!");
        }
        if (StringUtils.isEmpty(sendRequest.getMsgId())) {
            throw new CustomException("消息ID不能为空!");
        }
        //文本 或 物流信息
        if (TikTokConvContentTypeV2Enum.TEXT.getContentType().equalsIgnoreCase(sendRequest.getMsgType())){
            if(StringUtil.isEmpty(sendRequest.getContent())&&CollectionUtils.isEmpty(sendRequest.getFiles())){
                throw new CustomException("发送内容不能为空!");
            }
        }else if (TikTokConvContentTypeV2Enum.ORDER_CARD.getContentType().equalsIgnoreCase(sendRequest.getMsgType())){  //订单卡
            if (StringUtil.isEmpty(sendRequest.getOrderId())){
                throw new CustomException("订单ID不能为空!");
            }
        }else  if (TikTokConvContentTypeV2Enum.RETURN_REFUND_CARD.getContentType().equalsIgnoreCase(sendRequest.getMsgType())){ //退货退款卡
            if (StringUtil.isEmpty(sendRequest.getOrderId())||StringUtil.isEmpty(sendRequest.getSkuId())){
                throw new CustomException("退货卡片信息缺失!");
            }
        }else  if (TikTokConvContentTypeV2Enum.COUPON_CARD.getContentType().equalsIgnoreCase(sendRequest.getMsgType())){ //促销卡
            if (StringUtil.isEmpty(sendRequest.getCouponId())){
                throw new CustomException("促销ID不能为空!");
            }
        }else if (TikTokConvContentTypeV2Enum.PRODUCT_CARD.getContentType().equalsIgnoreCase(sendRequest.getMsgType())){ //产品卡片
            if (StringUtil.isEmpty(sendRequest.getProductId())){
                throw new CustomException("促销ID不能为空!");
            }
        } else {
            throw new CustomException("不支持该消息类型!");
        }

        //调整为处理中
        if ("NEW".equals(scTicket.getTicketStatus())) {
            scTicketService.setTikceStatusProcessing(sendRequest.getTicketId());
        }


        sendRequest.setShopId(scTicket.getShopId()); //设置店铺ID
        sendRequest.setSourceId(scTicket.getSourceId());//设置来源ID,用于会话内容行，保存会话主键ID(sourceId,即为会话主键)

        ScStationLetterMessage scStationLetterMessage = scStationLetterMessageService.selectScStationLetterMessageByMsgId(sendRequest.getMsgId());//会话ID唯一
        if (scStationLetterMessage == null) {
            throw new CustomException("重试消息不存在!");
        }
        if (scStationLetterMessage.getSendResponseCode() == 0) {
            throw new CustomException("此消息已发送成功,不可重试!");
        }

        Integer replyStatus;
        //调用发送消息
        String convShortId = sendRequest.getConvShortId();
        //原则只会有一条
        HashMap<String, List<JSONObject>> contentType = this.senMessageEntityTransition(sendRequest);

        HashMap<String, String> reqMap = new HashMap<>();
        for (String contenType : contentType.keySet()) {
            List<JSONObject> jsonObjects = contentType.get(contenType);
            reqMap.put("type", sendRequest.getMsgType()); //会话类型
            reqMap.put("content", jsonObjects.get(0).toJSONString()); //会话内容
        }
        SendMessageResponse sendMessageResponse = null;
        try {
            sendMessageResponse = tikTokUtil.postTikTokShopReturnV2(toJSONString(reqMap), TikTokApiEnums.SEND_MESSAGE_TIKTOK_NEW.getUrl(), TikTokApiEnums.SEND_MESSAGE_TIKTOK_NEW.getPath(), SendMessageResponse.class, scTicket.getShopId(),convShortId);
            replyStatus = 1;
            if (sendMessageResponse == null) {
                sendMessageResponse = new SendMessageResponse();
                sendMessageResponse.setCode(-1);
                replyStatus = -1; //回复失败
                flagMessage = 1;
            }

        } catch (Exception e) {
            sendMessageResponse = new SendMessageResponse();
            sendMessageResponse.setCode(-1); //发送失败
            log.error("TIKTOK发送会话消息失败：{}", e);
            replyStatus = -1;
            flagMessage = 1;
        }
        log.error("发送TK消息响应体：{}", toJSONString(sendMessageResponse.toString()));



        //回复状态
        if (scTicket.getTicketReplyStatus()==0||!scTicket.getTicketReplyStatus().equals(replyStatus)) { //未回复状态,或库中状态与本次发送状态不相等 更新工单会话读取状态
            ScTicket scTicketUpate = new ScTicket();
            scTicketUpate.setId(sendRequest.getTicketId());
            scTicketUpate.setTicketReplyStatus(replyStatus);
            scTicketService.updateScTickeByTicketId(scTicketUpate);
        }

        //相应格式调整
        ScStationLetterMessage scStationLetterMessageRecod = new ScStationLetterMessage();
        scStationLetterMessageRecod.setSendResponseCode(sendMessageResponse.getCode()); //响应码
        if (TikTokConvContentTypeV2Enum.TEXT.getContentType().equalsIgnoreCase(sendRequest.getMsgType())){
            scStationLetterMessageRecod.setContent(sendRequest.getContent());//只有文本类型更新重试内容
        }
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        df.setTimeZone(TimeZone.getTimeZone("America/Los_Angeles"));
        String nowPST = df.format(DateUtils.getNowDate());
        scStationLetterMessageRecod.setSendTime(cn.hutool.core.date.DateUtil.parse(nowPST)); //发送时间 当前PST
        scStationLetterMessageRecod.settingDefaultUpdate(); //默认信息

        if (!sendMessageResponse.getCode().equals(0)) { //重试失败
            //更新重试内容及重试响应CODE
            scStationLetterMessageService.updateMessageByMsgId(scStationLetterMessageRecod, sendRequest.getMsgId());
        } else if (sendMessageResponse.getCode().equals(0)) { //重试成功
            scStationLetterMessageRecod.setSendResponseCode(0);
            scStationLetterMessageRecod.setMsgId(sendMessageResponse.getData().getMsgId()); //重试成功，将成功ID替换掉原始 Msgid
            scStationLetterMessageService.updateMessageByMsgId(scStationLetterMessageRecod, sendRequest.getMsgId()); //使用原始msgId更新
        }
        return flagMessage;
    }

    /**
     * @param
     * @param
     * @param
     * @description: 发送TIKTOK站内信消息
     * @author: Moore
     * @date: 2023/10/19 15:53
     * @return: java.lang.String
     **/
    @Override
    @Transactional
    @RepeatSubmit(interval = 4000, message = "发送中请稍等")
    public int sendTiktokConvContentMessage(MessageSendRequest sendRequest) {

        Integer flagMessage = 0;

        if (StringUtils.isEmpty(sendRequest.getMsgType())) {
            throw new CustomException("消息类型不能为空");
        }

        if (sendRequest.getTicketId() == null  || StringUtil.isEmpty(sendRequest.getConvShortId())) {
            throw new CustomException("工单ID/会话ID获取失败!");
        }
        ScTicket scTicket = scTicketService.selectScTicketByBaseTicketId(sendRequest.getTicketId());
        if (null == scTicket || null == scTicket.getSourceId()||null==scTicket.getShopId()) {
            throw new CustomException("店铺/会话信息获取失败!");
        }
        //文本 或 物流信息
        if (TikTokConvContentTypeV2Enum.TEXT.getContentType().equalsIgnoreCase(sendRequest.getMsgType())){
            if(StringUtil.isEmpty(sendRequest.getContent())&&CollectionUtils.isEmpty(sendRequest.getFiles())){
                throw new CustomException("发送内容不能为空!");
            }
        }else if (TikTokConvContentTypeV2Enum.ORDER_CARD.getContentType().equalsIgnoreCase(sendRequest.getMsgType())){  //订单卡
            if (StringUtil.isEmpty(sendRequest.getOrderId())){
                throw new CustomException("订单ID不能为空!");
            }
        }else  if (TikTokConvContentTypeV2Enum.RETURN_REFUND_CARD.getContentType().equalsIgnoreCase(sendRequest.getMsgType())){ //退货退款卡
            if (StringUtil.isEmpty(sendRequest.getOrderId())||StringUtil.isEmpty(sendRequest.getSkuId())){
                throw new CustomException("退货卡片信息缺失!");
            }
        }else  if (TikTokConvContentTypeV2Enum.COUPON_CARD.getContentType().equalsIgnoreCase(sendRequest.getMsgType())){ //促销卡
            if (StringUtil.isEmpty(sendRequest.getCouponId())){
                throw new CustomException("促销ID不能为空!");
            }
        }else if (TikTokConvContentTypeV2Enum.PRODUCT_CARD.getContentType().equalsIgnoreCase(sendRequest.getMsgType())){ //产品卡片
            if (StringUtil.isEmpty(sendRequest.getProductId())){
                throw new CustomException("促销ID不能为空!");
            }
        } else {
            throw new CustomException("不支持该消息类型!");
        }


        //售后卡（退货退款）


        Account account = new Account();
        account.setId(scTicket.getShopId().intValue());
        List<Account> accounts = accountService.selectAccountListRefactor(account);
        String accountInit = "-";
        if (!CollectionUtils.isEmpty(accounts)){
             accountInit = accounts.get(0).getTitle();
        }

        //调整为处理中
        if ("NEW".equals(scTicket.getTicketStatus())) {
            scTicketService.setTikceStatusProcessing(sendRequest.getTicketId());
        }
        sendRequest.setShopId(scTicket.getShopId()); //设置店铺ID
        sendRequest.setSourceId(scTicket.getSourceId());//设置来源ID,用于会话内容行，保存会话主键ID(sourceId,即为会话主键)

        //封装消息内容
        HashMap<String, List<JSONObject>> stringListHashMap = this.senMessageEntityTransition(sendRequest);

        log.error("页面发送会话来源ID：{}，发送消息：{}", scTicket.getSourceId(), JSONObject.toJSONString(stringListHashMap));

        if (stringListHashMap.size() == 0) {
            throw new CustomException("发送消息不能为空!");
        }


        // 异步设置会话为已读
        CompletableFuture<Void> f1 = CompletableFuture.runAsync(() -> {
            refreshConvMsgReadFlag(scTicket.getShopId(), null,sendRequest.getConvShortId(),  sendRequest.getTicketId());
        }, taskExecutor);


        for (String key : stringListHashMap.keySet()) {
            List<JSONObject> value = stringListHashMap.get(key);
            //调用发送消息
            String convShortId = sendRequest.getConvShortId();
            HashMap<String, String> reqMap = new HashMap<>();
            reqMap.put("type", key); //会话类型
            for (JSONObject senMsg : value) {
                Integer replyStatus; //回复标识（工单使用）  -1:回复失败 0:未回复 1:已回复（成功）
                reqMap.put("content", senMsg.toJSONString()); //会话内容
                SendMessageResponse sendMessageResponse = null;//模拟调用
                try {
                    log.error("发送消息请求体：{}", toJSONString(reqMap));
                    sendMessageResponse = tikTokUtil.postTikTokShopReturnV2(toJSONString(reqMap), TikTokApiEnums.SEND_MESSAGE_TIKTOK_NEW.getUrl(), TikTokApiEnums.SEND_MESSAGE_TIKTOK_NEW.getPath(),SendMessageResponse.class,scTicket.getShopId(),convShortId);
                    replyStatus = 1; //回复成功（已回复）
                    if (sendMessageResponse == null) {
                        sendMessageResponse = new SendMessageResponse();
                        sendMessageResponse.setCode(-1);
                        replyStatus = -1; //回复失败
                        flagMessage = 1;
                    }
                } catch (Exception e) {
                    sendMessageResponse = new SendMessageResponse();
                    sendMessageResponse.setCode(-1);
                    log.error("TIKTOK发送会话消息失败：{}", e);
                    replyStatus = -1; //回复失败
                    flagMessage = 1;
                }

                //回复状态
                if (!replyStatus.equals(scTicket.getTicketReplyStatus())) { //未回复状态,或库中状态与本次发送状态不相等 更新工单会话读取状态
                    ScTicket scTicketUpate = new ScTicket();
                    scTicketUpate.setId(sendRequest.getTicketId());
                    if (new Integer(1).equals(replyStatus)){ //回复成功，将未回复数设置为0
                        scTicketUpate.setNoReplyQty(0);
                        scTicketUpate.setBusinessType(null); //回复成功，置空回复业务类型
                    }
                    scTicketUpate.setTicketReplyStatus(replyStatus);
                    scTicketService.updateScTicketBusinessType(scTicketUpate);
                }
                //均保存发送消息
                this.conversationsContentSaveManual(sendRequest, sendMessageResponse, false, key, senMsg,accountInit);
            }
        }
        return flagMessage;
    }


    /**
     * @param
     * @param sendRequest
     * @description: 封装发送消息行
     * @author: Moore
     * @date: 2023/11/16 10:24
     * @return: void
     **/
    HashMap<String, List<JSONObject>> senMessageEntityTransition(MessageSendRequest sendRequest) {
        HashMap<String, List<JSONObject>> senEntity = new HashMap<>();


        if (TikTokConvContentTypeV2Enum.TEXT.getContentType().equalsIgnoreCase(sendRequest.getMsgType())) { //文本卡片

            if (!StringUtils.isEmpty(sendRequest.getContent())) {
                //文本消息封装
                JSONObject contetJSon = new JSONObject();
                contetJSon.put("content", sendRequest.getContent());
                senEntity.put(TikTokConvContentTypeV2Enum.TEXT.getContentType(), Arrays.asList(contetJSon));
            }


            //图片消息封装
            List<MessageUploadImage> files = sendRequest.getFiles();
            if (!CollectionUtils.isEmpty(files)) {
                List<JSONObject> resImage = new ArrayList<>();
                for (MessageUploadImage file : files) {
                    JSONObject itemImg = new JSONObject();
                    itemImg.put("height", file.getHeight());
                    itemImg.put("url", file.getUrl());
                    itemImg.put("width", file.getWidth());
                    resImage.add(itemImg);
                }
                senEntity.put(TikTokConvContentTypeV2Enum.IMAGE.getContentType(), resImage);
            }
        }else if (TikTokConvContentTypeV2Enum.ORDER_CARD.getContentType().equalsIgnoreCase(sendRequest.getMsgType())){  //订单卡
            JSONObject orderCard = new JSONObject();
            orderCard.put("order_id", sendRequest.getOrderId());
            senEntity.put(TikTokConvContentTypeV2Enum.ORDER_CARD.getContentType(), Arrays.asList(orderCard));
        }else  if (TikTokConvContentTypeV2Enum.RETURN_REFUND_CARD.getContentType().equalsIgnoreCase(sendRequest.getMsgType())){ //退货退款卡
            JSONObject returnRefundCard = new JSONObject();
            returnRefundCard.put("order_id", sendRequest.getOrderId());
            returnRefundCard.put("sku_id", sendRequest.getSkuId());
            senEntity.put(TikTokConvContentTypeV2Enum.RETURN_REFUND_CARD.getContentType(), Arrays.asList(returnRefundCard));
        }else  if (TikTokConvContentTypeV2Enum.COUPON_CARD.getContentType().equalsIgnoreCase(sendRequest.getMsgType())){ //促销卡
            JSONObject couponCard = new JSONObject();
            couponCard.put("coupon_id", sendRequest.getCouponId());
            senEntity.put(TikTokConvContentTypeV2Enum.COUPON_CARD.getContentType(), Arrays.asList(couponCard));
        }else  if (TikTokConvContentTypeV2Enum.PRODUCT_CARD.getContentType().equalsIgnoreCase(sendRequest.getMsgType())){ //促销卡
            JSONObject productCard = new JSONObject();
            productCard.put("product_id", sendRequest.getProductId());
            senEntity.put(TikTokConvContentTypeV2Enum.PRODUCT_CARD.getContentType(), Arrays.asList(productCard));
        }
        return senEntity;
    }



    /**
     * @param
     * @description: 获取店铺下的所有会话列表
     * @author: Moore
     * @date: 2023/10/16 23:26
     * @return: void
     **/
    @Override
    public void getTiktokConvList() {
        Account account = new Account();
        account.setOrgId(1000049);
        account.setType(AccountSaleChannelEnum.TIKTOK.getValue());
        account.setActive("Y"); //开启状态下店铺
        List<Account> accounts = accountService.selectAccountListRefactor(account);
        for (Account accountItem : accounts) {
            try {
                if (accountItem.getId().equals(2377)){
                    saveTikTokConversationListAndCreateTicketNew(accountItem, null,0,null); //调用列表信息行
                }else{
                    saveTikTokConversationListAndCreateTicketNew(accountItem, null,20,null); //调用列表信息行
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("店铺:" + accountItem.getAccountInit() + " 获取会话列表错误:{}", e.getMessage());
            }
        }
    }

    /**
     * @param
     * @description: 获取店铺下的所有会话列表
     * @author: Moore
     * @date: 2023/10/16 23:26
     * @return: void
     **/
    @Override
    public void getTiktokConvManualList(Integer shopId, Integer speed) {
        Account account = new Account();
        account.setOrgId(1000049);
        account.setType(AccountSaleChannelEnum.TIKTOK.getValue());
        account.setActive("Y"); //开启状态下店铺
        if (shopId != null) {
            account.setId(shopId);
        }
        List<Account> accounts = accountService.selectAccountListRefactor(account);
        for (Account accountItem : accounts) {
            try {
                saveTikTokConversationListAndCreateTicketNew(accountItem, null, speed,null); //调用列表信息行
            } catch (Exception e) {
                e.printStackTrace();
                log.error("店铺:" + accountItem.getAccountInit() + " 获取会话列表错误手动:{}", e);
            }
        }
    }


    /**
     * @description: 获取店铺下指定会话列表
     * @author: Moore
     * @date: 2024/10/8 17:28
     * @param
     * @param account
     * @param convId
     * @return: void
    **/
    @Override
    public void getTiktokConvListByShopIdAndConvId(Account account, String convId) {
        saveTikTokConversationListAndCreateTicket(account, null,convId); //调用列表信息行
    }


    /**
     * @param
     * @description:获取店铺下会话列表的会话内容
     * @author: Moore
     * @date: 2023/10/18 10:26
     * @return: void
     **/
    @Override
    public void getTikTokConversationContent(Account accountReq, String convShortId) {
        if (accountReq.getId() != null && (accountReq.getId().equals(2377)||accountReq.getId().equals(2406)
        ||accountReq.getId().equals(2401)||accountReq.getId().equals(2407)
        )) { //单独处理2377

            accountReq.setType(AccountSaleChannelEnum.TIKTOK.getValue());
            accountReq.setActive("Y");//开启状态下会话
            List<Account> accounts = accountService.selectAccountListRefactor(accountReq);  //获取TK账户
            if (CollectionUtils.isEmpty(accounts)) {
                return;
            }
            Account accountItem = accounts.get(0);

            //分页执行任务信息
            int pageNo = 1;
            int pageSize = 5000;
            while (true) {
                PageHelper.startPage(pageNo, pageSize);
                List<ScStationLetter> convShortList = null;
                if (!StringUtils.isEmpty(convShortId)) {
                    convShortList = scStationLetterService.selectConvShortIdByShopIdAndConvShortId(accountItem.getId(), convShortId); //根据店铺ID查询会话列表
                } else {
                    //获取所有有效回话
                    convShortList = scStationLetterService.selectConvShortIdByShopId(accountItem.getId());
                }
                if (CollUtil.isEmpty(convShortList)) {
                    break;
                }
                try {
                    for (ScStationLetter scStationLetter : convShortList) {
                        List<ScTicket> scTicket = scTicketService.selectScTicketBySourceList(scStationLetter.getStationLetterId(), ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION); //获取会话下所有工单
                        List<ScTicket> filteTicket = scTicket.stream().filter(item -> ScTicketConstant.TICKET_STATUS_NEW.equals(item.getTicketStatus()) || ScTicketConstant.TICKET_STATUS_PROCESSING.equals(item.getTicketStatus())).collect(Collectors.toList()); //获取新建或处理中的会话（新建及处理中的会话 工单，只会存在一条）
                        saveTikTokConverSationContent(accountItem, "", 0, scStationLetter.getConvShortId(), CollectionUtils.isEmpty(filteTicket), !CollectionUtils.isEmpty(filteTicket) ? filteTicket.get(0).getId() : -1, scStationLetter.getStationLetterId(), scStationLetter.getCustomerId(), false);
                    }
                } catch (Exception e) {
                    log.error("店铺:" + accountItem.getAccountInit() + " 获取会话内容错误:{}", e);
                }
                pageNo++;
            }
        }else{
            accountReq.setType(AccountSaleChannelEnum.TIKTOK.getValue());
            accountReq.setActive("Y");//开启状态下会话
            List<Account> accounts = accountService.selectAccountListRefactor(accountReq);  //获取TK账户
            for (Account accountItem : accounts) {
                try {
                    List<ScStationLetter> convShortList = null;
                    if (!StringUtils.isEmpty(convShortId)) {
                        convShortList = scStationLetterService.selectConvShortIdByShopIdAndConvShortId(accountItem.getId(), convShortId); //根据店铺ID查询会话列表
                    } else {
                        //TODO 此处仅为了webhook使用
                        if (accountItem.getId().equals(2377)||accountReq.getId().equals(2406)
                                ||accountReq.getId().equals(2401)||accountReq.getId().equals(2407)) {
                            continue;
                        }
                        //获取所有有效回话
                        convShortList = scStationLetterService.selectConvShortIdByShopId(accountItem.getId());
                    }
                    for (ScStationLetter scStationLetter : convShortList) {
                        List<ScTicket> scTicket = scTicketService.selectScTicketBySourceList(scStationLetter.getStationLetterId(), ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION); //获取会话下所有工单
                        List<ScTicket> filteTicket = scTicket.stream().filter(item -> ScTicketConstant.TICKET_STATUS_NEW.equals(item.getTicketStatus()) || ScTicketConstant.TICKET_STATUS_PROCESSING.equals(item.getTicketStatus())).collect(Collectors.toList()); //获取新建或处理中的会话（新建及处理中的会话 工单，只会存在一条）
                        saveTikTokConverSationContent(accountItem, "", 0, scStationLetter.getConvShortId(), CollectionUtils.isEmpty(filteTicket), !CollectionUtils.isEmpty(filteTicket) ? filteTicket.get(0).getId() : -1, scStationLetter.getStationLetterId(), scStationLetter.getCustomerId(), false);
                    }
                } catch (Exception e) {
                    log.error("店铺:" + accountItem.getAccountInit() + " 获取会话内容错误:{}", e);            }
            }
        }
    }

    /**
     * @description: 更新昵称信息
     * @author: Moore
     * @date: 2025/1/8 19:22
     * @param
     * @return: void
    **/
    @Override
    public void updateTikTokConversationNickName() {

        int pageNo = 1;
        int pageSize = 800;
        while (true) {
            PageHelper.startPage(pageNo, pageSize);
            List<ScStationLetter> scStationLetters = scStationLetterService.selectNoNickNameList();
            if (CollectionUtils.isEmpty(scStationLetters)) {
                break;
            }
            for (ScStationLetter scStationLetter : scStationLetters) {
                List<ScTicket> scTicket = scTicketService.selectScTicketBySourceList(scStationLetter.getStationLetterId(), ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION); //获取会话下所有工单
                //查询出标题名称需替换工单
                //获取新建或处理中的会话（新建及处理中的会话 工单，只会存在一条）
                List<Long> scTicketIds = scTicket.stream().filter(item -> item.getTicketName().contains("BUYER_ID")).map(ScTicket::getId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(scTicketIds)) {
                    continue;
                }
                this.updateTkNickName(String.valueOf(scStationLetter.getAccountId()),scStationLetter.getStationLetterId(),null,scStationLetter.getConvShortId(),scTicketIds);
            }
            pageNo++;
        }
    }


    /**
     * 更新Tk会话信息
     *
     * @param shopId 店铺ID
     * @param nextToken 下标
     * @param stationLetterId   会话业务主键
     * @param convShortId   会话ID
     */
    private void updateTkNickName(String shopId,
                                  Long stationLetterId,
                                  String nextToken,
                                  String convShortId,
                                  List<Long> tikcetIds) {
        HashMap<String, Object> reqMap = new HashMap<>();
        if (StringUtils.isNotEmpty(nextToken)) {
            reqMap.put("page_token", nextToken);//token
        }
        reqMap.put("page_size", 10);//条数 最大支持
        //获取内容
        TikTokMessageListContentV2 tikTokMessageListContentV2 = null;
        try {
            tikTokMessageListContentV2 = tikTokUtil.getTikTokShopReturnV2(reqMap, TikTokApiEnums.GET_LIST_CONVERSATIONS_CONTENT_V2.getUrl(), TikTokApiEnums.GET_LIST_CONVERSATIONS_CONTENT_V2.getPath().replace("{conversation_id}", convShortId), TikTokMessageListContentV2.class, shopId);
        } catch (Exception e) {
            log.error("刷新会话nick名称获取失败：{},回话ID：{}", e, convShortId);
        }
        if (tikTokMessageListContentV2 == null || !tikTokMessageListContentV2.getMessage().equalsIgnoreCase("Success")) {
            log.error("刷新会话nick名称无消息内容：{},{}", JSONObject.toJSONString(tikTokMessageListContentV2), shopId);
            return;
        }

        TikTokMessageListContentDataV2 data = tikTokMessageListContentV2.getData();

        List<TikTokMessageListContentMessagesV2> msgs = data.getMessages();
        if (CollectionUtils.isEmpty(msgs)) {
            return;
        }

        TikTokMessageListContentMessagesV2 tikTokMessageListContentMessagesV2 = msgs.stream().filter(item -> item.getSender() != null && item.getSender().getRole().equals(TikTokConstant.MSG_ROLE_BUYER)).findFirst().orElse(null);
        if (tikTokMessageListContentMessagesV2 != null) {
            TikTokMessageListResponseV2Participants sender = tikTokMessageListContentMessagesV2.getSender();
            String nickName = sender.getNickname();
              //更新主表
            ScStationLetter scStationLetter = new ScStationLetter();
            scStationLetter.setCustomerName(nickName);
            scStationLetter.setStationLetterId(stationLetterId);
            scStationLetterMapper.updateScStationLetter(scStationLetter);


            //更新工单会话参与人
            for (Long tikcetId : tikcetIds) {
                //更新工单描述
                ScTicket scTicket = new ScTicket();
                scTicket.setId(tikcetId);
                scTicket.setTicketName("参与会话人: BUYER-" + nickName);
                scTicketService.updateScTickeByTicketId(scTicket);
            }
            return;
        }

        //递归调用
        if (StringUtils.isNotEmpty(data.getNextPageToken())) {
            updateTkNickName(shopId,stationLetterId, data.getNextPageToken(), convShortId, tikcetIds);
        } else {
            return;
        }

    }



    /**
     * @param contextId 组织ID
     * @param ticketId  工单ID
     * @param hisFlag   是否查询历史
     * @description: 获取工单会话查询列表 -数据库查询
     * @author: Moore
     * @date: 2023/10/18 16:56
     * @return: java.util.List<com.bizark.op.api.entity.op.ticket.ScStationLetterMessage>
     **/
    @Override
    public List<ScStationLetterMessage> selectTikcetConversationContent(Integer contextId,
                                                                        Long ticketId,
                                                                        String hisFlag) {
        ScTicket scTicket = scTicketService.selectScTicketByBaseTicketId(ticketId);
        if (null == ticketId || null == scTicket) {
            log.error("获取会话缺少必要条件");
            return new ArrayList<>();
        }


        List<ScStationLetterMessage> scStationLetterMessages = null;
        if (ScTicketTypeEnum.AMZ_SITEMSG.getValue().equals(scTicket.getTicketType())) {  //tk站内信使用 source直接查询
            if (scTicket.getSourceId() != null) {
                //获取所有回话内容
                ScStationLetter scStationLetterRecod = new ScStationLetter();
                scStationLetterRecod.setStationLetterId(scTicket.getSourceId());
                scStationLetterMessages = scStationLetterMessageService.selectConversationListByConv(scStationLetterRecod);
            }
        } else {  //其余TK查看历史，需要 通过订单获取需要店铺+订单号 获取outerId查询其会话信息
            Long shopId = scTicket.getShopId();
            String amazonOrderId = scTicket.getAmazonOrderId();
            if (shopId != null && !StringUtils.isEmpty(amazonOrderId)) {
                //查询客户outerId
                SaleOrderVoTicket saleOrderVoTicket = saleOrdersMapper.selectScOrderCustomerOuterId(contextId, shopId.intValue(), amazonOrderId);
                if (saleOrderVoTicket != null && !StringUtils.isEmpty(saleOrderVoTicket.getCustomerOuterId())) {
                    ScStationLetter scStationLetterRecod = new ScStationLetter();
                    scStationLetterRecod.setAccountId(shopId.intValue());
                    scStationLetterRecod.setOuterId(saleOrderVoTicket.getCustomerOuterId());
                    scStationLetterMessages = scStationLetterMessageService.selectConversationListByConv(scStationLetterRecod);
                }
            }
        }
        //对卡片消息处理
        if (!CollectionUtils.isEmpty(scStationLetterMessages)) {

            //商品卡
            List<ScStationLetterMessage> productCardList = scStationLetterMessages.stream().filter
                    (item -> ("goods_card".equalsIgnoreCase(item.getContentType()) || "PRODUCT_CARD".equalsIgnoreCase(item.getContentType())||
                            "BUYER_ENTER_FROM_PRODUCT".equalsIgnoreCase(item.getContentType())
                            )
                            && StringUtils.isNotEmpty(item.getContent())
                    ).collect(Collectors.toList());

            //订单卡
            List<ScStationLetterMessage> orderCard = scStationLetterMessages.stream().filter
                    (item -> ("order_card".equalsIgnoreCase(item.getContentType())||"BUYER_ENTER_FROM_ORDER".equalsIgnoreCase(item.getContentType()))
                            && StringUtils.isNotEmpty(item.getContent())
                    ).collect(Collectors.toList());

            List<ScStationLetterMessage> returnRefuundCard = scStationLetterMessages.stream().filter
                    (item -> ("RETURN_REFUND_CARD".equalsIgnoreCase(item.getContentType()))
                            && StringUtils.isNotEmpty(item.getContent())
                    ).collect(Collectors.toList());

            List<ScStationLetterMessage> couponCard = scStationLetterMessages.stream().filter
                    (item -> ("COUPON_CARD".equalsIgnoreCase(item.getContentType()))
                            && StringUtils.isNotEmpty(item.getContent())
                    ).collect(Collectors.toList());

            CompletableFuture<Void> f1 = CompletableFuture.runAsync(() -> {
                tiktokProductCardInfo(productCardList);
            }, taskExecutor);

            CompletableFuture<Void> f2 = CompletableFuture.runAsync(() -> {
                tiktokOrderCardInfo(orderCard,scTicket.getShopId(),scTicket.getOrganizationId());
            }, taskExecutor);

            CompletableFuture<Void> f3 = CompletableFuture.runAsync(() -> {
                tiktokReturnRefundInfo(returnRefuundCard,scTicket.getShopId());
            }, taskExecutor);

            CompletableFuture<Void> f4 = CompletableFuture.runAsync(() -> {
                couponInfo(couponCard,scTicket.getShopId());
            }, taskExecutor);

            try {
                CompletableFuture.allOf(f1, f2,f3,f4).get(60, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.info("查询卡片消息异常：{}", e);
            }

        }

        return scStationLetterMessages;
    }







    /**
     * 商品卡片(父商品ID)
     *
     * @param productCard
     */
    public  void tiktokProductCardInfo( List<ScStationLetterMessage> productCard) {
        if (CollectionUtils.isEmpty(productCard)) {
            return;
        }
        //获取父商品ID
        List<String> parentAsin = productCard.stream().map(ScStationLetterMessage::getContent).distinct().collect(Collectors.toList());
        //查询分组后的商品信息
        List<TicketMsgCardInfo.SkuInfo> productChannelsList = productChannelsMapper.selectTiktokParentInfo(parentAsin);
        //转map
        Map<String, TicketMsgCardInfo.SkuInfo> ProductChannelsMap = productChannelsList.stream().collect(Collectors.toMap(TicketMsgCardInfo.SkuInfo::getParentGoodsId, Function.identity(), (v1, v2) -> v1));
        for (ScStationLetterMessage scStationLetterMessage : productCard) {
            String asin1 = scStationLetterMessage.getContent();//封装
            TicketMsgCardInfo ticketMsgCardInfo = new TicketMsgCardInfo();
            ticketMsgCardInfo.setSkuInfos(Arrays.asList(ProductChannelsMap.get(asin1)));
            scStationLetterMessage.setTicketMsgCardInfo(Arrays.asList(ticketMsgCardInfo)); //设置卡片信息
        }
    }


    /**
     * 订单信息
     *
     * @param productCard
     * @param shopId
     * @param orgId
     */
    public  void tiktokOrderCardInfo( List<ScStationLetterMessage> productCard,Long shopId,Long orgId) {

        if (CollectionUtils.isEmpty(productCard)) {
            return;
        }

        //所有订单号
        List<String> orderNos = productCard.stream().map(ScStationLetterMessage::getContent).distinct().collect(Collectors.toList());

        List<TicketMsgCardInfo.SkuInfo> skuInfos = new ArrayList<>();
        List<TicketMsgCardInfo.TrackInfo> saleShipmentPackagesVOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderNos)) {
            //订单信息
            skuInfos = saleOrdersMapper.selectOrderCardInfo(orgId, orderNos, shopId,null);
            //track信息
            saleShipmentPackagesVOS = saleOrdersMapper.selectOrderCardInfoShipmentInfo(orgId, orderNos, null);
        }

        //根据订单号分组
        Map<String, List<TicketMsgCardInfo.SkuInfo>> orderNoGroup = skuInfos.stream().collect(Collectors.groupingBy(TicketMsgCardInfo.SkuInfo::getOrderNo));
        Map<String, List<TicketMsgCardInfo.TrackInfo>> trackInfoGroup = saleShipmentPackagesVOS.stream().collect(Collectors.groupingBy(TicketMsgCardInfo.TrackInfo::getSaleOrderNo));

        for (ScStationLetterMessage scStationLetterMessage : productCard) {
            TicketMsgCardInfo ticketMsgCardInfo = new TicketMsgCardInfo();
            ticketMsgCardInfo.setOrderNo(scStationLetterMessage.getContent()); //订单号
            //匹配订单信息
            List<TicketMsgCardInfo.SkuInfo> skuInfoList = orderNoGroup.get(scStationLetterMessage.getContent());
            if (!CollectionUtils.isEmpty(skuInfoList)) {
                ticketMsgCardInfo.setChannelStatus(skuInfoList.get(0).getChannelStatus()); //平台状态
                ticketMsgCardInfo.setSkuInfos(skuInfoList); //
            }else{
                ticketMsgCardInfo.setSkuInfos(Collections.emptyList());
            }

            //匹配track信息
            List<TicketMsgCardInfo.TrackInfo> trackInfoList = trackInfoGroup.get(scStationLetterMessage.getContent());
            if (!CollectionUtils.isEmpty(trackInfoList)) {
                ticketMsgCardInfo.setTrackInfos(trackInfoList);
            }else{
                ticketMsgCardInfo.setTrackInfos(Collections.emptyList());
            }
            scStationLetterMessage.setTicketMsgCardInfo(Arrays.asList(ticketMsgCardInfo)); //设置卡片信息
        }

    }


    /** 退货信息卡（售后卡）
     *
     *
     * @param productCard
     * @param shopId
     */
    public  void tiktokReturnRefundInfo( List<ScStationLetterMessage> productCard,Long shopId) {
        if (CollectionUtils.isEmpty(productCard)) {
            return;
        }

        //封装订单信息
        for (ScStationLetterMessage scStationLetterMessage : productCard) {
            JSONObject contentJson = JSONObject.parseObject(scStationLetterMessage.getContent());
            String orderNo = contentJson.getString("order_id");
            String skuId = contentJson.getString("sku_id");

            //获取退货退款卡片信息
            List<TicketMsgCardInfo.SkuInfo> skuInfoList = tiktokReturnRefundItemMapper.selectTiktokReturnRefundCardInfo(shopId, orderNo, skuId);
            Map<String, List<TicketMsgCardInfo.SkuInfo>> returnInfoMap = skuInfoList.stream().collect(Collectors.groupingBy(TicketMsgCardInfo.SkuInfo::getReturnId));

            //卡片信息
            ArrayList<TicketMsgCardInfo> cardInfoList = new ArrayList<>();

            for (String returnId : returnInfoMap.keySet()) {
                TicketMsgCardInfo ticketMsgCardInfo = new TicketMsgCardInfo();
                ticketMsgCardInfo.setReturnId(returnId); //售后单号
                ticketMsgCardInfo.setPlatformReturnStatus(returnInfoMap.get(returnId).get(0).getReturnStatus()); //平台退款状态
                ticketMsgCardInfo.setSellerSku(returnInfoMap.get(returnId).get(0).getSellerSku()); //sellerSku
                ticketMsgCardInfo.setSkuInfos(returnInfoMap.get(returnId)); //图片SKU信息
                cardInfoList.add(ticketMsgCardInfo);
            }
            scStationLetterMessage.setTicketMsgCardInfo(cardInfoList);
        }
    }


    /** 退货信息卡（售后卡）
     *
     *
     * @param productCard
     * @param shopId
     */
    public  void couponInfo( List<ScStationLetterMessage> productCard,Long shopId) {
        if (CollectionUtils.isEmpty(productCard)) {
            return;
        }

        List<String> couponIds= productCard.stream().map(ScStationLetterMessage::getContent).distinct().collect(Collectors.toList());

        List<MarTkCouponInfo> marTkCouponInfos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(couponIds)) {
          marTkCouponInfos = marTkCouponInfoMapper.selectTiktokMsgCouponByIds(couponIds);
        }

        Map<String, MarTkCouponInfo> couponInfoMap = marTkCouponInfos.stream().collect(Collectors.toMap(MarTkCouponInfo::getCouponId, Function.identity(), (v1, v2) -> v1));

        //封装订单信息
        for (ScStationLetterMessage scStationLetterMessage : productCard) {
            MarTkCouponInfo marTkCouponInfo = couponInfoMap.get(scStationLetterMessage.getContent());
            if (marTkCouponInfo == null) {
                scStationLetterMessage.setTicketMsgCardInfo(Collections.emptyList());
            }else {
                TicketMsgCardInfo ticketMsgCardInfo = new TicketMsgCardInfo();
                ticketMsgCardInfo.setMarTkCouponInfo(marTkCouponInfo);
                scStationLetterMessage.setTicketMsgCardInfo(Arrays.asList(ticketMsgCardInfo));
            }

        }
    }



    /**
     * @description: 查询客户订单号 订单列表（侧边栏）
     * @author: Moore
     * @date: 2025/1/15 15:57
     * @param
     * @param
     * @param
     * @return: java.util.List<com.bizark.op.api.entity.op.order.SaleOrders>
     **/
    @Override
    public List<TicketMsgCardOrderInfo> selectTikcetConversationOrderList(String  customerOuterId,Long shopId, List<Long> channelStatus, String orderNo) {
        if (StringUtils.isEmpty(customerOuterId)||shopId==null) {
            return Collections.emptyList();
        }
        return saleOrdersMapper.selectTikcetConversationOrderList(customerOuterId,shopId.longValue(),channelStatus,orderNo);
    }


    /**
     * 获取tk会话订单明细信息（侧边栏）
     *
     * @param orderId
     * @return
     */
    @Override
    public TicketMsgCardDescInfo selectTikcetConversationOrderItemsList(Long orgId,Long orderId,String orderNo,Long shopId) {
        if (orderId == null||StringUtils.isEmpty(orderNo)) {
            throw new CustomException("订单ID不能为空!");
        }


        TicketMsgCardDescInfo ticketMsgCardDescInfo = new TicketMsgCardDescInfo();

        //设置订单信息
        TicketMsgCardDescInfo.TiktokOrderInfo tiktokOrderInfo = new TicketMsgCardDescInfo.TiktokOrderInfo();
        List<TicketMsgCardInfo.SkuInfo> orders = saleOrdersMapper.selectOrderCardInfo(orgId, null, null, orderId);
        tiktokOrderInfo.setSkuInfos(orders);
        tiktokOrderInfo.setTrackInfos(saleOrdersMapper.selectOrderCardInfoShipmentInfo(orgId, Arrays.asList(orderNo), orderId));
        tiktokOrderInfo.setItemAmount(orders.stream().filter(item->item.getItemAmount()!=null).map(TicketMsgCardInfo.SkuInfo::getItemAmount).reduce(BigDecimal.ZERO, BigDecimal::add));//总金额
        tiktokOrderInfo.setOrderNo(orderNo); //订单ID
        ticketMsgCardDescInfo.setOrderInfoList(tiktokOrderInfo);


        //售后信息 -退货退款
        List<ReturnInfoEntity> returnInfoEntities  =returnInfoMapper.selectReturnInfoAndSkuIdbyTiktok(orderNo, shopId);

        //售后信息-退款
        List<TicketMsgCardDescInfo.AfterSalesInfo > orderRefundRequestInfos = orderRefundRequestInfoMapper.selectRefundRequestByTkCard(orderNo, shopId);

        //封装各售后单LIST
        ArrayList<TicketMsgCardDescInfo.AfterSalesInfo> returnRefundInfo = new ArrayList<>();

        if (!CollectionUtils.isEmpty(returnInfoEntities)) {
            //将售后单分组
            Map<String, List<ReturnInfoEntity>> returnInfoMap = returnInfoEntities.stream().collect(Collectors.groupingBy(ReturnInfoEntity::getReturnId));


            for (String returnId : returnInfoMap.keySet()) {
                List<ReturnInfoEntity> returnInfo = returnInfoMap.get(returnId);
                ReturnInfoEntity returnInfoEntity = returnInfo.get(0);
                //封装每行售后单信息
                TicketMsgCardDescInfo.AfterSalesInfo afterSalesInfo = new TicketMsgCardDescInfo.AfterSalesInfo();
                afterSalesInfo.setAfterSalesType("退货退款"); //售后类型
                afterSalesInfo.setPlatformReason(returnInfoEntity.getReturnReason()); //平台退货原因
                afterSalesInfo.setReturnId(returnId); //售后单号
                afterSalesInfo.setReturnDate(returnInfoEntity.getReturnDate()); //退货日期
                afterSalesInfo.setReturnPrice(returnInfoEntity.getReturnAmount()); //退款金额
                afterSalesInfo.setOrderNo(orderNo);//订单号，发送使用
                afterSalesInfo.setOriginalCustomerComments(returnInfoEntity.getOriginalCustomerComments()); //平台退货描述（原文）
                afterSalesInfo.setReturnStatus(returnInfoEntity.getReturnStatus()); //平台（售后状态）退款状态

                ArrayList<TicketMsgCardDescInfo.AfterSalesInfo.SellerSkuInfo> sellerSkuInfos = new ArrayList<>();
                for (ReturnInfoEntity infoEntity : returnInfo) {
                    TicketMsgCardDescInfo.AfterSalesInfo.SellerSkuInfo sellerSkuInfo = new TicketMsgCardDescInfo.AfterSalesInfo.SellerSkuInfo();
                    sellerSkuInfo.setSellerSku(infoEntity.getSellerSku());
                    sellerSkuInfo.setSkuId(infoEntity.getSkuId());
                    sellerSkuInfos.add(sellerSkuInfo);
                }
                afterSalesInfo.setSellerSkuInfos(sellerSkuInfos);//sellerSku信息
                returnRefundInfo.add(afterSalesInfo);
            }

            ticketMsgCardDescInfo.setAfterSalesInfoList(returnRefundInfo);
        }


        //退款信息
        if (!CollectionUtils.isEmpty(orderRefundRequestInfos)) {
            //将售后单分组
            Map<String, List<TicketMsgCardDescInfo.AfterSalesInfo>> refundInfoMap = orderRefundRequestInfos.stream().collect(Collectors.groupingBy(TicketMsgCardDescInfo.AfterSalesInfo::getReturnId));

            //封装各售后单LIST

            for (String returnId : refundInfoMap.keySet()) {
                List<TicketMsgCardDescInfo.AfterSalesInfo> returnInfo = refundInfoMap.get(returnId);
                TicketMsgCardDescInfo.AfterSalesInfo returnInfoEntity = returnInfo.get(0);
                BigDecimal refundAmount = returnInfo.stream().filter(item -> item.getReturnPrice() != null).map(TicketMsgCardDescInfo.AfterSalesInfo::getReturnPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                //封装每行售后单信息
                TicketMsgCardDescInfo.AfterSalesInfo afterSalesInfo = new TicketMsgCardDescInfo.AfterSalesInfo();
                afterSalesInfo.setAfterSalesType("退款"); //售后类型
                afterSalesInfo.setPlatformReason(returnInfoEntity.getPlatformReason()); //平台退货原因
                afterSalesInfo.setReturnId(returnId); //售后单号
                afterSalesInfo.setReturnDate(afterSalesInfo.getReturnDate()); //退款日期
                afterSalesInfo.setReturnPrice(refundAmount); //退款金额
                afterSalesInfo.setOrderNo(orderNo);//订单号，发送使用
                afterSalesInfo.setOriginalCustomerComments(returnInfoEntity.getOriginalCustomerComments()); //平台退货描述（原文）
                afterSalesInfo.setReturnStatus(returnInfoEntity.getReturnStatus()); //平台（售后状态）退款状态

                ArrayList<TicketMsgCardDescInfo.AfterSalesInfo.SellerSkuInfo> sellerSkuInfos = new ArrayList<>();
                for (TicketMsgCardDescInfo.AfterSalesInfo infoEntity : returnInfo) {
                    TicketMsgCardDescInfo.AfterSalesInfo.SellerSkuInfo sellerSkuInfo = new TicketMsgCardDescInfo.AfterSalesInfo.SellerSkuInfo();
                    sellerSkuInfo.setSellerSku(infoEntity.getSellerSku());
                    sellerSkuInfo.setSkuId(infoEntity.getSkuId());
                    sellerSkuInfos.add(sellerSkuInfo);
                }
                afterSalesInfo.setSellerSkuInfos(sellerSkuInfos);//sellerSku信息
                returnRefundInfo.add(afterSalesInfo);
            }

            ticketMsgCardDescInfo.setAfterSalesInfoList(returnRefundInfo);
        }

        //售后单信息
        ticketMsgCardDescInfo.setAfterSalesInfoList(returnRefundInfo);
        return ticketMsgCardDescInfo;
    }


    /** 查询侧边栏商品信息
     *
     * @param contextId
     * @param shopId
     * @param flag
     * @param commonParam
     * @return
     */
    @Override
    public List<TicketMsgCardInfo.SkuInfo> selectTikcetConversationProductList(Long contextId, Long shopId, String flag, String commonParam) {
        return productChannelsMapper.selectTiktokParentInfoByPage(flag, commonParam);
    }


    /**
     * @description: 查询侧边栏促销信息
     * @author: Moore
     * @date: 2025/1/17 13:56
     * @param
     * @param contextId
     * @param shopId
     * @param commonParam
     * @return: java.util.List<com.bizark.op.api.entity.op.mar.MarTkCouponInfo>
    **/
    @Override
    public List<MarTkCouponInfo> selectTiktokMsgCouponList(Long contextId, Long shopId, String commonParam) {
        return marTkCouponInfoMapper.selectTiktokMsgCouponList(shopId, commonParam);
    }



    /**
     * @description: 获取维度消息
     * @author: Moore
     * @date: 2023/12/22 10:22
     * @param
     * @param ticketId
     * @return: java.util.List<java.lang.String>
    **/
    @Override
    public List<String> selectTikcetConversationConByBuyer(Long ticketId) {
        ScStationLetterMessage scStationLetterMessage = new ScStationLetterMessage();
        scStationLetterMessage.setTicketId(ticketId);
        List<ScStationLetterMessage> scStationLetterMessageList = scStationLetterMessageService.selectConversationListBydesc(scStationLetterMessage);
        if (CollectionUtils.isEmpty(scStationLetterMessageList)) {
            return null;
        }

        //在进行降序
        Integer indesFlag = scStationLetterMessageList.indexOf(new ScStationLetterMessage("3"));//获取店铺回复第一次出现的位置=
        if (indesFlag == -1) { //无回复，返回全部会话
            return scStationLetterMessageList.stream().filter(item -> "1".equals(item.getType())&&"text".equalsIgnoreCase(item.getContentType())).sorted(Comparator.comparing(ScStationLetterMessage::getSendTime)).map(ScStationLetterMessage::getContent).collect(Collectors.toList());
        }
        //有回复，返回首次回复后的用户消息
        List<ScStationLetterMessage> filterMessage = scStationLetterMessageList.stream().limit(indesFlag + 1).collect(Collectors.toList());
        return filterMessage.stream().filter(item -> "1".equals(item.getType())&&"text".equalsIgnoreCase(item.getContentType())&&!StringUtils.isEmpty(item.getContent())).sorted(Comparator.comparing(ScStationLetterMessage::getSendTime)).map(ScStationLetterMessage::getContent).collect(Collectors.toList());
    }


    /**
     * @param
     * @param shopId
     * @param contextId
     * @param ticketId
     * @param shopId
     * @param convShortId 会话
     * @description: 刷新TIKTOK会话信息接口
     * @author: Moore
     * @date: 2023/10/19 15:50
     * @return: void
     **/
    @Override
    public void refreshConvContent(Long shopId, Integer contextId, Long sourceId, String convShortId, Long ticketId) {
        if (StringUtil.isEmpty(convShortId) || null == ticketId || shopId == null || sourceId == null) {
            log.error("-----刷新会话缺少必要参数------");
            return;
        }
        try {
            this.refreshConvContentSava(shopId, contextId, sourceId, "", convShortId, ticketId,1); //调用列表信息行 初始值，只循环2次
        } catch (Exception e) {
            log.error("轮询TIKTOK会话失败,会话ID：{}", convShortId);
        }
    }


    /**
     * 刷新会话为已读
     *
     * @param shopId
     * @param contextId
     * @param convShortId
     * @param ticketId
     */
    @Override
    public void refreshConvMsgReadFlag(Long shopId, Integer contextId, String convShortId, Long ticketId) {
        if (StringUtil.isEmpty(convShortId) || null == ticketId || shopId == null) {
            log.error("-----刷新已读确认参数------");
            return;
        }
        TikTokMessageListContentV2 tikTokMessageListContentV2 = null;
        try {
             tikTokMessageListContentV2  = tikTokUtil.getTikTokShopReturnV2(new JSONObject(), TikTokApiEnums.GET_LIST_CONVERSATIONS_CONTENT_V2.getUrl(), TikTokApiEnums.MSG_READ_V2.getPath().replace("{conversation_id}",convShortId),TikTokMessageListContentV2.class,String.valueOf(shopId));
//            log.info("刷新已读标识：{},{}", toJSONString(tikTokMessageListContentV2), convShortId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //未成功
        if (tikTokMessageListContentV2 == null || !"Success".equalsIgnoreCase(tikTokMessageListContentV2.getMessage())) {
            return;
        }

        //工单未读设置为0
        ScTicket scTicket = new ScTicket();
        scTicket.setId(ticketId);
        scTicket.setNoReplyQty(0);
        scTicketService.updateScTickeByTicketId(scTicket);

        //会话全部设置为已读
        LambdaUpdateWrapper<ScStationLetterMessage> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(ScStationLetterMessage::getConvShortId, convShortId);
        queryWrapper.set(ScStationLetterMessage::getReadFlag, "Y");
        scStationLetterMessageService.update(queryWrapper);

    }


    /**
     * @param
     * @param message        消息体
     * @param authUserEntity 当前用户
     * @description: 创建会话, 并发送消息
     * @author: Moore
     * @date: 2023/10/26 17:06
     * @return: void
     **/
    @Override
    @Transactional
    public void createTiktokConvAndSendMessage(Long ticketId, MessageSendRequest message, UserEntity authUserEntity) {
        if (null == message.getTicketId()) {
            throw new CustomException("工单ID获取失败!");
        }
        ScTicket scTicketOne = scTicketService.selectScTicketByBaseTicketId(message.getTicketId());
        if (scTicketOne.getShopId() == null) {
            throw new CustomException("店铺获取失败!");
        }

        //判断客户是否存在
        ScCustomer scCustomer = scCustomerService.selectScCustomerById(scTicketOne.getCustomerId());
        if (null == scCustomer || StringUtils.isEmpty(scCustomer.getAmzCustomerId())) {
            throw new CustomException("客户信息获取失败!");
        }

        Account account = new Account();
        account.setId(scTicketOne.getShopId().intValue());
        List<Account> accounts = accountService.selectAccountListRefactor(account);
        if (CollectionUtils.isEmpty(accounts)) {
            throw new CustomException("店铺信息获取失败!");
        }
        account = accounts.get(0);


        //调整为处理中
        if ("NEW".equals(scTicketOne.getTicketStatus())) {
            scTicketService.setTikceStatusProcessing(ticketId);
        }



        //判断用户是否存在会话
        //获取当前店铺用户会话（店铺+用户/仅会有一个会话）
        List<ScStationLetter> scStationLetters = scStationLetterService.selectBaseScStationLetterByAccountIdAndOuteId(scTicketOne.getShopId(),scCustomer.getAmzCustomerId());

        ScStationLetter scStationLetter = new ScStationLetter();
        if (CollectionUtils.isEmpty(scStationLetters)){ //系统暂无该用户会话
            //使用平台用户ID创建会话
            HashMap<String, String> reqMap = new HashMap<>();
            reqMap.put("buyer_user_id",scCustomer.getAmzCustomerId());
            SendMessageResponse tikTokShopReturn = null;
            try {
                tikTokShopReturn = tikTokUtil.postTikTokShopV2(toJSONString(reqMap), TikTokApiEnums.CREATE_TIKTOK_CONVV2.getUrl(), TikTokApiEnums.CREATE_TIKTOK_CONVV2.getPath(), SendMessageResponse.class, message.getShopId());
            } catch (Exception e) {
                log.error("创建会话失败：{}", e);
            }
            if (tikTokShopReturn == null|| tikTokShopReturn.getData()==null||StringUtils.isEmpty(tikTokShopReturn.getData().getConversationId())) {
                throw new CustomException("会话创建失败!");
            }
            //保存会话列表
            scStationLetter.setAccountId(scTicketOne.getShopId().intValue());//账号ID
            scStationLetter.setConvShortId(tikTokShopReturn.getData().getConversationId()); //设置会话ID
            scStationLetter.setCustomerId(scCustomer.getCustomerId());
            scStationLetter.setCustomerName(scCustomer.getCustomerName());//客户名称
            scStationLetter.setOuterId(scCustomer.getAmzCustomerId()); //平台客户ID
            scStationLetter.setSourceType(3);//来源类型手工单
            scStationLetter.setChannelType(AccountSaleChannelEnum.TIKTOK.getValue()); //设置渠道为tiktok
            scStationLetter.settingDefaultCreate();//创建默认值
            scStationLetterService.insertScStationLetter(scStationLetter);
        }else{
            scStationLetter = scStationLetters.get(0); //复用系统已存在会话ID
        }


        //工单创建
        ScTicket scTicketCrete = new ScTicket();
        scTicketCrete.setTicketType(ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG);  //工单类型 站内信
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("参与会话人: ");
        stringBuffer.append("buyer" + ":" + scCustomer.getNickName() + " "); //拼接 具体会话人
        scTicketCrete.setTicketName(stringBuffer.toString()); //工单名称
        scTicketCrete.setRemark(account.getTitle()); //描述
        scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(scStationLetter.getStationLetterId().toString(), "l", 5, "0")+RandomUtil.randomNumbers(4));
        scTicketCrete.setTicketSource(ScTicketConstant.TIKTOK_MESSAGE);// 工单来源(订单API)
        scTicketCrete.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH); //优先级（高）
        scTicketCrete.setSourceId(scStationLetter.getStationLetterId());//来源ID
        scTicketCrete.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION); //来源单据 站内信
        scTicketCrete.setShopId(Long.valueOf(account.getId()));  //店铺
        scTicketCrete.setOrganizationId(Long.valueOf(account.getOrgId()));//组织ID
//        scTicketCrete.setCustomerId(scCustomer.getCustomerId()); //客户ID，在匹配订单时设置
        scTicketCrete.setAmazonOrderId(null != scTicketOne ? scTicketOne.getAmazonOrderId() : null);
        scTicketCrete.setTicketReplyStatus(1); //设置会话回复状态 为已回复
        scTicketCrete.setParentTicketId(ticketId.longValue()); //父工单 ID
        scTicketService.insertScTicket(scTicketCrete);


        //记录工单日志
        ScTicketLog ticketLog = new ScTicketLog();
        ticketLog.setTicketId(ticketId);
        ticketLog.setOperateType("SUBMIT");
        ticketLog.setOperateTime(new Date());
        ticketLog.setOperateContent("联系客户，生成站内信子工单：" + scTicketCrete.getTicketNumber());
        scTicketLogService.insertScTicketLog(ticketLog);


        //发送会话消息
        message.setConvShortId(scStationLetter.getConvShortId());//设置会话ID
        message.setTicketId(scTicketCrete.getId()); //会话关联新工单ID
        this.sendTiktokConvContentMessage(message); //发送消息

    }


    /**
     * @param
     * @param contextId 组织ID
     * @param ticketId  工单ID
     * @description: 校验是否允许弹窗
     * @author: Moore
     * @date: 2023/10/30 15:05
     * @return: void
     **/
    @Override
    public ScTicket createTiktokConvAndSendMessageVerify(Integer contextId, Long ticketId) {
        ScTicket scTicket = scTicketService.selectScTicketByBaseTicketId(ticketId);
        if (null == scTicket) {
            throw new CustomException("未获取到当前工单信息");
        }

        ScCustomer scCustomer = scCustomerService.selectScCustomerById(scTicket.getCustomerId());
        if (null == scCustomer || StringUtils.isEmpty(scCustomer.getAmzCustomerId())) { //amzcustomer为用户ID
            throw new CustomException("客户信息缺失!");
        }

        //获取当前店铺用户会话（店铺+用户/仅会有一个会话）
        List<ScStationLetter> scStationLetters = scStationLetterService.selectBaseScStationLetterByAccountIdAndOuteId(scTicket.getShopId(),scCustomer.getAmzCustomerId());

        if (CollectionUtils.isEmpty(scStationLetters)){ //无会话 ,创建会话
            return new ScTicket();
        }else{
            //1.一个会话ID可能存在多个工单（新版只会有一个）
            List<ScTicket> scTicketList = scTicketService.selectScTicketBySourceList(scStationLetters.get(0).getStationLetterId(), ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION); //获取会话下所有工单

            //从新建或者进行中
            ScTicket scTicketNew = scTicketList.stream().filter(item -> ScTicketConstant.TICKET_STATUS_NEW.equals(item.getTicketStatus())).findFirst().orElse(null);
            ScTicket scTicketProcessing = scTicketList.stream().filter(item -> ScTicketConstant.TICKET_STATUS_PROCESSING.equals(item.getTicketStatus())).findFirst().orElse(null);
            ScTicket scTicketClose = scTicketList.stream().filter(item -> ScTicketConstant.TICKET_STATUS_CLOSED.equals(item.getTicketStatus())|| ScTicketConstant.TICKET_STATUS_FINISH.equals(item.getTicketStatus())).findFirst().orElse(null);
            if (scTicketNew != null) {  //优先取新建
                return scTicketNew;
            }else if (scTicketProcessing !=null) { //其次取 进行中
                return scTicketProcessing;
            } else if (scTicketClose != null) { //其将关闭工单打开使用
                ticketId = scTicketClose.getId();
                ScTicket scTicketUpdate = new ScTicket();
                scTicketUpdate.setId(ticketId);
                scTicketUpdate.setTicketStatus(ScTicketConstant.TICKET_STATUS_NEW); //新会话，重新开启工单
                scTicketUpdate.settingDefaultSystemUpdate();
                scTicketService.updateScTicketStatus(scTicketUpdate);
                return scTicketClose;
            }
            return new ScTicket();
        }
    }


    /**
     * @param
     * @param convShortId
     * @description: 获取指定回话ID会话内容(非业务接口)
     * @author: Moore
     * @date: 2023/10/31 23:50
     * @return: void
     **/
    @Override
    public  List<Msg>  getConvShortIdContent(String convShortId, Integer contentType) {

//        ScStationLetter scStationLetter = scStationLetterService.selectletterByConvShortId(convShortId);
//        if (null == scStationLetter) {
//            scStationLetter = new ScStationLetter();
//            scStationLetter.setConvShortId(convShortId);
//            scStationLetterService.insertScStationLetter(scStationLetter);
//        }
//        Long stationLetterId = scStationLetter.getStationLetterId(); //会话主键
         List<Msg> msgList = new ArrayList<>();

        //获取回话内容
        this.getConvShortIdContentNoTicket(convShortId, null, contentType, "0", msgList);
        return  msgList;

    }


    /**
     * @description: 山茶壶
     * @author: Moore
     * @date: 2023/11/15 18:01
     * @param shopId 店铺ID
     * @param file
     * @return: com.alibaba.fastjson.JSONObject
    **/
    @Override
    public List<JSONObject> uploadToTiktokOss(MultipartFile file[], Long shopId) {
        List<JSONObject> ListRes = new ArrayList<>();
        for (MultipartFile multipartFile : file) {
            JSONObject updaeteImgRes = tikTokUtil.postTikTokShopV2Upload("", TikTokApiEnums.UPLOAD_IMG_V2.getUrl(), TikTokApiEnums.UPLOAD_IMG_V2.getPath(),JSONObject.class,shopId,multipartFile);
            if (updaeteImgRes != null) {
                JSONObject data = updaeteImgRes.getJSONObject("data");
                if (data != null) {
                    ListRes.add(data);
                }
            }
        }
        return ListRes;
    }


    /**
     * @param convShortId     会话ID
     * @param stationLetterId 会话业务主键
     * @param contentType     拉取消息内容 0历史 1最新
     * @param nextIndex       坐标从0开始
     * @description:通过会话ID保存查询会话，不创建工单（非业务接口）
     * @author: Moore
     * @date: 2023/11/1 0:01
     * @return:
     **/
    void getConvShortIdContentNoTicket(String convShortId, Long stationLetterId, Integer contentType, String nextIndex, List<Msg> msgsList) {
        if (StringUtils.isEmpty(convShortId)) {
            return;
        }
        HashMap<String, Object> reqMap = new HashMap<>();
        reqMap.put("conv_short_id", convShortId);//required  会话ID
        reqMap.put("pull_direction", contentType); //required  拉取消息内容 0历史 1最新
        reqMap.put("cursor", nextIndex); //required  下一索引标识
        reqMap.put("limit", 10); //required   每页显示条数 MAX is 10
//        reqMap.put("language", "en");//non-required   语言默认en
        TikTokConversationContentResponse tikTokShopReturn = tikTokUtil.getTikTokShopReturn(reqMap, TikTokApiEnums.GET_LIST_CONVERSATIONS_CONTENT.getUrl(), TikTokApiEnums.GET_LIST_CONVERSATIONS_CONTENT.getPath(), TikTokConversationContentResponse.class, null);
        if (tikTokShopReturn == null || !tikTokShopReturn.getMessage().equalsIgnoreCase("Success")) {
            log.error("--------------获取消息列表失败--------------");
            return;
        }

        MessageData data = tikTokShopReturn.getData();
        List<Msg> msgs = data.getMsgs();
        if (CollectionUtils.isEmpty(msgs)) {
            return;
        }
        Boolean hasMore = data.getHasMore();
        String nextCursor = data.getNextCursor(); //下一页坐标

        Long ticketIdFlag = null;
        Boolean recursionCreateFlag = false;//递归创建工单状态
        //排除系统会话
        List<Msg> filteMsg = msgs.stream().filter(item -> !TikTokMessageEnum.SYSTEM.getCode().equals(item.getSenderRole())).collect(Collectors.toList());


        //保存内容消息
        for (Msg msg : filteMsg) {
            //判断消息是否存在
            LatestMsg latestMsg = new LatestMsg();
            BeanUtils.copyProperties(msg, latestMsg);
//            this.conversationsContentSave(latestMsg, 1000049, ticketIdFlag, stationLetterId);  //保存回话内容

            msgsList.add(msg);
        }
        //递归调用所有会话信息
        if (hasMore && !StringUtil.isEmpty(nextCursor)) {
            getConvShortIdContentNoTicket(convShortId, stationLetterId, contentType, nextCursor,msgsList);
        } else {
            return;
        }
    }








    /**
     * @param nextToken 下页索引信息
     * @param account   店铺信息
     * @param converId   会话Id
     * @description: 会话列表保存并创建工单
     * @author: Moore
     * @date: 2023/10/17 9:41
     * @return: void
     **/
    public void saveTikTokConversationListAndCreateTicket(Account account, String nextToken,String converId) {
        HashMap<String, Object> reqMap = new HashMap<>();
        reqMap.put("page_size", 20);   //required   每页显示条数 MAX is 20
        if(StringUtils.isNotEmpty(nextToken)) {
            reqMap.put("page_token", nextToken);
        }
        //调用外部接口获取会话列表
        TikTokMessageListResponseV2 tikTokMessageListResponseV2 = tikTokUtil.getTikTokShopReturnV2(reqMap, TikTokApiEnums.GET_LIST_CONVERSATIONS_V2.getUrl(), TikTokApiEnums.GET_LIST_CONVERSATIONS_V2.getPath(), TikTokMessageListResponseV2.class,String.valueOf(account.getId()));
       if (tikTokMessageListResponseV2==null){
           return;
       }
        TikTokMessageListResponseV2Data data = tikTokMessageListResponseV2.getData();
        if (null == data) {
            return;
        }
        String nextPageToken = data.getNextPageToken();
        List<TikTokMessageListResponseV2Conversations> convWithLastMsg = data.getConversations(); //最新会话列表信息
        for (TikTokMessageListResponseV2Conversations withLastMsg : convWithLastMsg) {
            //会话参与人
            List<TikTokMessageListResponseV2Participants> participants = withLastMsg.getParticipants();
            //todo 这里要转一下，还是直接过滤掉
            List<TikTokMessageListResponseV2Participants> participtsFilterList = participants.stream().filter(item -> "BUYER".equals(item.getRole())).collect(Collectors.toList()); //仅保存有买家的会话       //1.买家  2.店铺 3.客服 4.creator 5.系统 6.robot
            if (CollectionUtils.isEmpty(participtsFilterList)) {
                continue;
            }

            if (converId!=null&&!converId.equals(withLastMsg.getId())){
                continue;
            }

            //保存客户信息
//            Long customerId = this.conversationsParticipantsSave(participtsFilterList, account.getOrgId());

            //保存会话列表
            Long stationLetterId = this.saveConverSationListV2(null, withLastMsg, participtsFilterList, account, TikTokConstant.CONV_MESSAGE_SOURCE_API);

            //创建工单信息(无会话即无工单)
            this.convListCreateTicketV2(stationLetterId, participants, account, null);
        }

        //递归调用
        if (StringUtils.isNotEmpty(nextPageToken)) {
            saveTikTokConversationListAndCreateTicket(account, nextPageToken, converId);
        } else {
            return;
        }
    }


    public void saveTikTokConversationListAndCreateTicketNew(Account account, String nextToken, Integer num,String convShortId) {
        if (num != null) {
            if (num >= 30) {
                return;
            }
            num ++;
        }

        HashMap<String, Object> reqMap = new HashMap<>();
        reqMap.put("page_size", 20);
        if (StringUtils.isNotEmpty(nextToken)) {
            reqMap.put("page_token", nextToken);
        }
        //调用外部接口获取会话列表
        TikTokMessageListResponseV2 tikTokMessageListResponseV2 = tikTokUtil.getTikTokShopReturnV2(reqMap, TikTokApiEnums.GET_LIST_CONVERSATIONS_V2.getUrl(), TikTokApiEnums.GET_LIST_CONVERSATIONS_V2.getPath(), TikTokMessageListResponseV2.class, String.valueOf(account.getId()));
        if (tikTokMessageListResponseV2 == null || !new Integer(0).equals(tikTokMessageListResponseV2.getCode())) {
            return;
        }
        TikTokMessageListResponseV2Data data = tikTokMessageListResponseV2.getData();
        if (null == data) {
            return;
        }
        String nextPageToken = data.getNextPageToken();
        List<TikTokMessageListResponseV2Conversations> convWithLastMsg = data.getConversations(); //最新会话列表信息
        for (TikTokMessageListResponseV2Conversations withLastMsg : convWithLastMsg) {
            List<TikTokMessageListResponseV2Participants> participants = withLastMsg.getParticipants();
            List<TikTokMessageListResponseV2Participants> participtsFilterList = participants.stream().filter(item -> "BUYER".equals(item.getRole())).collect(Collectors.toList()); //仅保存有买家的会话       //1.买家  2.店铺 3.客服 4.creator 5.系统 6.robot
            if (CollectionUtils.isEmpty(participtsFilterList)) {
                continue;
            }

            //刷新指定会话ID_USER信息
            if(!StringUtils.isEmpty(convShortId)&&convShortId.equals(withLastMsg.getId())){
                this.saveConverSationListV2New(withLastMsg.getId(), participtsFilterList);
                return;
            }
            //保存会话列表
            this.saveConverSationListV2New(withLastMsg.getId(), participtsFilterList);
        }

        //递归调用
        if (StringUtils.isNotEmpty(nextPageToken)) {
            saveTikTokConversationListAndCreateTicketNew(account, nextPageToken, num,convShortId);
        } else {
            return;
        }
    }


    /**
     * @param
     * @description:会话列表创建工单
     * @author: Moore
     * @date: 2023/10/22 15:54
     * @return: void
     **/
    public Long convListCreateTicket(Long stationLetterId, List<Participant> participants, Account account, Long customerId) {
        //创建工单（根据会话查询工单）
        List<ScTicket> scTicket = scTicketService.selectScTicketBySourceList(stationLetterId, ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION);
        List<ScTicket> filterTicket = scTicket.stream().filter(item -> ScTicketConstant.TICKET_STATUS_NEW.equals(item.getTicketStatus()) || ScTicketConstant.TICKET_STATUS_PROCESSING.equals(item.getTicketStatus())).collect(Collectors.toList()); //过滤新建或 进行中的工单
        Long ticketId = -1L;
        if (CollectionUtils.isEmpty(scTicket) || (!CollectionUtils.isEmpty(scTicket) && CollectionUtils.isEmpty(filterTicket))) { //工单不存在（或没有新建 及正在进行中的工单）
            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append("参与会话人: ");
            participants.stream().forEach(item -> {
                if (item.getRole() != 5) { //系统不记录在内
                    //获取角色标识
                    TikTokMessageEnum byCode = TikTokMessageEnum.getByCode(item.getRole());
                    if (null != byCode) {
                        String value = byCode.getValue();
                        stringBuffer.append(value + "-" + item.getNick() + " "); //拼接 具体会话人
                    } else {
                        stringBuffer.append(item.getRole() + "-" + item.getNick());
                    }
                }
            });
            ScTicket scTicketCrete = new ScTicket();
            scTicketCrete.setTicketType(ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG);  //工单类型 站内信
            scTicketCrete.setTicketName(stringBuffer.toString()); //工单名称
            scTicketCrete.setRemark(account.getTitle()); //描述
            scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(stationLetterId.toString(), "l", 5, "0")+RandomUtil.randomNumbers(4));//TK回话会存在于多个工单，增加随机数
            scTicketCrete.setTicketSource(ScTicketConstant.TIKTOK_MESSAGE);// 工单来源(订单API)
            scTicketCrete.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH); //优先级（高）
            scTicketCrete.setSourceId(stationLetterId);//来源ID
            scTicketCrete.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION); //来源单据 站内信
            scTicketCrete.setShopId(Long.valueOf(account.getId()));  //店铺
            scTicketCrete.setOrganizationId(Long.valueOf(account.getOrgId()));//组织ID
//            scTicketCrete.setCustomerId(customerId); //客户ID
            scTicketCrete.settingDefaultSystemCreate();
            scTicketService.insertScTicket(scTicketCrete);
            ticketId = scTicketCrete.getId();//工单主键
        }
        return ticketId;
    }

    /**
     * @param participants  弃用（不使用此方式进行关联）
     * @description: 保存客户
     * @author: Moore
     * @date: 2023/10/17 10:05
     * @return: Integer 返回业务客户主键ID
     **/
    private Long conversationsParticipantsSave(List<Participant> participants, Integer orgId) {
        for (Participant participant : participants) {
            //保存客户信息
            ScCustomer scCustomer = new ScCustomer();
            scCustomer.setAmzCustomerId(participant.getOuterId()); //系统用户唯一标识
            List<ScCustomer> scCustomers = scCustomerService.selectScCustomerList(scCustomer);
            if (CollectionUtils.isEmpty(scCustomers)) {
                scCustomer.setOrganizationId(Long.valueOf(orgId)); //组织ID
                scCustomer.setPicUrl(participant.getAvatar()); //头像地址
                scCustomer.setNickName(participant.getNick());//昵称
                scCustomer.setCustomerName(participant.getNick());//姓名
                scCustomer.settingDefaultSystemCreate(); //创建时间设置
                scCustomer.setCreatedAt(DateUtils.getNowDate());
                scCustomer.setCreatedBy(-1);
                scCustomer.setCreatedName("system");
                scCustomerService.insertScCustomer(scCustomer);
                return scCustomer.getCustomerId();
            } else {
                return scCustomers.get(0).getCustomerId();
            }
        }
        return -1L;
    }


    /**
     * @param customerId     客户表主键ID
     * @param convInfo       会话基础信息
     * @param participtsList 会话参与人列表，仅保存客户信息
     * @param account        店铺信息
     * @param sourceType        来源类型
     * @description: 保存回话列表
     * @author: Moore
     * @date: 2023/10/18 1:00
     * @return: 会话表主键ID
     **/
    public Long saveConverSationList(Long customerId, ConvInfo convInfo, List<Participant> participtsList, Account account, Integer sourceType) {

        //根据会话ID判断会话唯一性
        ScStationLetter scStationLetter = scStationLetterService.selectletterByConvShortId(convInfo.getConvShortId());
        if (null == scStationLetter) {
            ScStationLetter scStationLetterSave = new ScStationLetter();
            scStationLetterSave.setOrganizationId(account.getOrgId().longValue());
            scStationLetterSave.setConvShortId(convInfo.getConvShortId()); //会话ID
            if (!CollectionUtils.isEmpty(participtsList)) { //关联买家信息
                Participant participant = participtsList.get(0);
                scStationLetterSave.setCustomerName(participant.getNick());//客户名称
                scStationLetterSave.setOuterId(participant.getOuterId());//平台客户ID
                scStationLetterSave.setCustomerId(customerId);//客户表主键信息
            }
            scStationLetterSave.setFirstSendTime(DateUtil.BjToPstByTimestamp(convInfo.getCreateTime())); //回话创建时间
            scStationLetterSave.setAccountId(account.getId());
            scStationLetterSave.settingDefaultSystemCreate();
            scStationLetterSave.setSourceType(sourceType);
            scStationLetterSave.setChannelType(AccountSaleChannelEnum.TIKTOK.getValue()); //设置渠道为tiktok
            scStationLetterService.insertScStationLetter(scStationLetterSave);
            return scStationLetterSave.getStationLetterId();
        } else {
            return scStationLetter.getStationLetterId();
        }
    }


    /**
     * @param messageSendRequest  发送请求体
     * @param sendMessageResponse 发送响应体
     * @param retryFlag 重试标识
     * @param fileType 会话类型
     * @param contentJson 发送内容
     * @param accountInit 店铺名（作为发送人）
     * @description: 保存手动发送信息（重试一次一定是单条信息）
     * @author: Moore
     * @date: 2023/10/17 10:05
     * @return: void
     **/
    private void conversationsContentSaveManual(MessageSendRequest messageSendRequest,
                                                SendMessageResponse sendMessageResponse,
                                                Boolean retryFlag,String fileType,JSONObject contentJson,String accountInit) {
        if (retryFlag){ //重试
            if (!sendMessageResponse.getCode().equals(0)) { //重试失败
                //更新重试内容及重试响应CODE
                ScStationLetterMessage scStationLetterMessage = new ScStationLetterMessage();
                scStationLetterMessage.setSendResponseCode(sendMessageResponse.getCode()); //响应码
                scStationLetterMessage.setContent(messageSendRequest.getContent());//内容
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                df.setTimeZone(TimeZone.getTimeZone("America/Los_Angeles"));
                String nowPST = df.format(DateUtils.getNowDate());
                scStationLetterMessage.setSendTime(cn.hutool.core.date.DateUtil.parse(nowPST)); //发送时间 当前PST
                scStationLetterMessage.settingDefaultUpdate(); //默认信息
                scStationLetterMessageService.updateMessageByMsgId(scStationLetterMessage, messageSendRequest.getMsgId());
            } else if (sendMessageResponse.getCode().equals(0)) { //重试成功
                ScStationLetterMessage scStationLetterMessage = new ScStationLetterMessage();
                scStationLetterMessage.setSendResponseCode(0); //响应码
                scStationLetterMessage.setContent(messageSendRequest.getContent());//内容
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                df.setTimeZone(TimeZone.getTimeZone("America/Los_Angeles"));
                String nowPST = df.format(DateUtils.getNowDate());
                scStationLetterMessage.setSendTime(cn.hutool.core.date.DateUtil.parse(nowPST)); //发送时间 当前PST
                scStationLetterMessage.settingDefaultUpdate(); //默认信息
                scStationLetterMessage.setMsgId(sendMessageResponse.getData().getMsgId()); //重试成功，将成功ID替换掉原始 Msgid
                scStationLetterMessageService.updateMessageByMsgId(scStationLetterMessage, messageSendRequest.getMsgId()); //使用原始msgId更新
            }
        }else{
            ScStationLetterMessage scStationLetterMessage = new ScStationLetterMessage();
            scStationLetterMessage.setOrganizationId(messageSendRequest.getOrgId()); //组织ID
            scStationLetterMessage.setMsgId(sendMessageResponse.getData()!=null&&sendMessageResponse.getData().getMsgId()!=null?sendMessageResponse.getData().getMsgId():RandomUtil.randomNumbers(8)+"SYS"); //消息ID 确定唯一值
            scStationLetterMessage.setSourceType(3); //手动发送
            scStationLetterMessage.setSendResponseCode(sendMessageResponse.getCode());//响应结果Code
            scStationLetterMessage.setTicketId(messageSendRequest.getTicketId()); //工单主键ID
            scStationLetterMessage.setStationLetterId(Long.valueOf(messageSendRequest.getSourceId())); //站内信主键ID
            scStationLetterMessage.setMessageType(ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG); //站内信
            scStationLetterMessage.setCommonSendName(accountInit); //发送人
            if (TikTokConvContentTypeV2Enum.TEXT.getContentType().equalsIgnoreCase(fileType)){ //文本从entity取
                scStationLetterMessage.setContent(messageSendRequest.getContent());//内容
                scStationLetterMessage.setContentType("TEXT"); //内容类型 text
            }else if (TikTokConvContentTypeV2Enum.IMAGE.getContentType().equalsIgnoreCase(fileType)){ //图片从JSON_QuquestBody取
                String imagetHeight = contentJson.getString("height");
                String imagetWidth = contentJson.getString("width");
                String imagetUrl = contentJson.getString("url");
                scStationLetterMessage.setContent(imagetUrl);//内容
                scStationLetterMessage.setContentType("IMAGE"); //内容类型 text
                scStationLetterMessage.setWidth(imagetWidth);
                scStationLetterMessage.setHeight(imagetHeight);
            }else if (TikTokConvContentTypeV2Enum.ORDER_CARD.getContentType().equalsIgnoreCase(fileType)){ //订单卡
                scStationLetterMessage.setContent(contentJson.getString(TikTokConvContentTypeV2Enum.ORDER_CARD.getValue()));//内容
                scStationLetterMessage.setContentType(TikTokConvContentTypeV2Enum.ORDER_CARD.getContentType());
            }else if(TikTokConvContentTypeV2Enum.RETURN_REFUND_CARD.getContentType().equalsIgnoreCase(fileType)){ //退货退款卡
                scStationLetterMessage.setContent(contentJson.toJSONString());
                scStationLetterMessage.setContentType(TikTokConvContentTypeV2Enum.RETURN_REFUND_CARD.getContentType());
            }else if (TikTokConvContentTypeV2Enum.COUPON_CARD.getContentType().equalsIgnoreCase(fileType)){ //促销卡
                scStationLetterMessage.setContent(contentJson.getString(TikTokConvContentTypeV2Enum.COUPON_CARD.getValue()));//内容
                scStationLetterMessage.setContentType(TikTokConvContentTypeV2Enum.COUPON_CARD.getContentType());
            }else if (TikTokConvContentTypeV2Enum.PRODUCT_CARD.getContentType().equalsIgnoreCase(fileType)){ //产品卡
                scStationLetterMessage.setContent(contentJson.getString(TikTokConvContentTypeV2Enum.PRODUCT_CARD.getValue()));
                scStationLetterMessage.setContentType(TikTokConvContentTypeV2Enum.PRODUCT_CARD.getContentType());
            }

            DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            df.setTimeZone(TimeZone.getTimeZone("America/Los_Angeles"));
            String nowPST = df.format(DateUtils.getNowDate());
            scStationLetterMessage.setSendTime(cn.hutool.core.date.DateUtil.parse(nowPST)); //发送时间 当前PST
            scStationLetterMessage.setType("3"); //1.buyer  2.shop 3.customer service
            scStationLetterMessage.setReadFlag("Y"); //已读状态
            scStationLetterMessage.setConvShortId(messageSendRequest.getConvShortId()); //系统会话ID
            scStationLetterMessageService.insertScStationLetterTikTokMessage(scStationLetterMessage);
        }
    }


    /**
     * @param latestMsg 最新消息
     * @param orgId     组织ID
     * @param ticketId  工单主键
     * @param tikeReadFlag  会话读取标识，true 不更新 ，false更新
     * @description: 保存Tiktok会话内容（save report） - 接口获取
     * @author: Moore
     * @date: 2023/10/17 10:05
     * @return:  返回-会话是否读取 判断标识
     **/
    private Boolean conversationsContentSave(LatestMsg latestMsg, Integer orgId,
                                          Long ticketId, Long stationLetterId,
                                          Boolean tikeReadFlag) {

//        log.error("获取到的消息---------------：{}", JSONObject.toJSONString(latestMsg));
        if (latestMsg == null || TikTokMessageEnum.SYSTEM.getCode().equals(latestMsg.getSenderRole())) { //系统消息不保存
            return false ;
        }

        //判断消息是否存在
        ScStationLetterMessage scStationLetterMessage = new ScStationLetterMessage();
        scStationLetterMessage.setOrganizationId(orgId); //组织ID
        scStationLetterMessage.setMsgId(latestMsg.getMsgId()); //消息ID 确定唯一值
        List<ScStationLetterMessage> scStationLetterMessageList = scStationLetterMessageService.selectScStationLetterMessageList(scStationLetterMessage);
        if (!CollectionUtils.isEmpty(scStationLetterMessageList)) {
            return false;
        }

        scStationLetterMessage.setTicketId(ticketId); //工单主键ID
        scStationLetterMessage.setStationLetterId(stationLetterId); //站内信主键ID
        scStationLetterMessage.setMessageType(ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG); //站内信类型
        scStationLetterMessage.setCommonSendName(latestMsg.getSenderNick()); //发送人
        this.convContentChannel(latestMsg, scStationLetterMessage); //解析会话内容,并设置
        scStationLetterMessage.setSendTime(DateUtil.BjToPstByTimestamp(latestMsg.getCreateTime())); //消息发送时间
        scStationLetterMessage.setType(latestMsg.getSenderRole() == null ? null : latestMsg.getSenderRole().toString()); //发送人类型 1.buyer  2.shop 2 customer service
        scStationLetterMessage.setReadFlag(latestMsg.getIsVisible() ? "Y" : "N"); //已读状态
        scStationLetterMessage.setConvShortId(latestMsg.getConvShortId()); //平台会话ID
        scStationLetterMessage.setMsgId(latestMsg.getMsgId()); //平台消息ID
        scStationLetterMessage.setSourceType(1);//消息来源API
        scStationLetterMessage.settingDefaultSystemCreate(); //默认系统创建
        scStationLetterMessageService.insertScStationLetterTikTokMessage(scStationLetterMessage);
        if (!tikeReadFlag){
            ScTicket scTicket = new ScTicket();
            scTicket.setId(ticketId);
            scTicket.setTicketReplyStatus(0); //更新会话读取状态为未回复
            scTicket.settingDefaultSystemUpdate();
            scTicketService.updateScTickeByTicketId(scTicket);
        }
        return true;
    }


    /**
     * @param
     * @description: 会话内容转换
     * @author: Moore
     * @date: 2023/10/20 14:15
     * @return: void
     **/
    public void convContentChannel(LatestMsg latestMsg, ScStationLetterMessage scStationLetterMessage) {
        String msgType = latestMsg.getMsgType(); //会话类型
        String content = latestMsg.getContent();//回话消息
        scStationLetterMessage.setContentType(msgType);//设置消息类型
        JSONObject contentJson = JSONObject.parseObject(content);
        if (StringUtils.isEmpty(content) || null == contentJson) {
            return;
        }
        if (TikTokConvContentTypeV2Enum.TEXT.getContentType().equalsIgnoreCase(msgType)) { //会话内容
            scStationLetterMessage.setContent(contentJson.getString(TikTokConvContentTypeV2Enum.TEXT.getValue()));
            //图片
        } else if (TikTokConvContentTypeV2Enum.IMAGE.getContentType().equalsIgnoreCase(msgType)) {
            scStationLetterMessage.setContent(contentJson.getString(TikTokConvContentTypeV2Enum.IMAGE.getValue()));
            String imageHeight = contentJson.getString("height");
            String imageWidth = contentJson.getString("width");
            scStationLetterMessage.setHeight(imageHeight); //宽
            scStationLetterMessage.setWidth(imageWidth);//高
        } else if (TikTokConvContentTypeV2Enum.PRODUCT_CARD.getContentType().equalsIgnoreCase(msgType)|| //产品卡片
                TikTokConvContentTypeV2Enum.BUYER_ENTER_FROM_PRODUCT.getContentType().equalsIgnoreCase(msgType)) {  //用户正在查看产品

            //存储商品ID
            scStationLetterMessage.setContent(contentJson.getString(TikTokConvContentTypeV2Enum.PRODUCT_CARD.getValue()));
        } else if (TikTokConvContentTypeV2Enum.ORDER_CARD.getContentType().equalsIgnoreCase(msgType) ||
            TikTokConvContentTypeV2Enum.BUYER_ENTER_FROM_ORDER.getContentType().equalsIgnoreCase(msgType)) { //订单卡片

            //存储店铺ID
            scStationLetterMessage.setContent(contentJson.getString(TikTokConvContentTypeV2Enum.ORDER_CARD.getValue()));
        } else if (TikTokConvContentTypeV2Enum.RETURN_REFUND_CARD.getContentType().equalsIgnoreCase(msgType)) {
            scStationLetterMessage.setContent(contentJson.toJSONString()); //退货退款，存储JSON
        } else  if (TikTokConvContentTypeV2Enum.VIDEO.getContentType().equalsIgnoreCase(msgType)){  //视频只存url
            scStationLetterMessage.setContent(contentJson.getString(TikTokConvContentTypeV2Enum.VIDEO.getValue()));
        }else  if (TikTokConvContentTypeV2Enum.COUPON_CARD.getContentType().equalsIgnoreCase(msgType)){  //活动ID
            scStationLetterMessage.setContent(contentJson.getString(TikTokConvContentTypeV2Enum.COUPON_CARD.getValue()));
        }
    }

    /**
     * @param
     * @param account          店铺信息
     * @param nextToken        下次查询坐标
     * @param contentType      内容类型，0历史  1最新
     * @param convShortId      会话ID
     * @param createTicketFlag 是否创建工单
     * @param tikcetId         工单信息
     * @param stationLetterId  会话主键ID（来源ID）
     * @param customerId       客户ID
     * @param tikeReadFlag       工单回话读取状态标识 true 不更新，false 更新
     * @description: 主动获取会话内容并创建工单信息
     * @author: Moore
     * @date: 2023/10/18 10:29
     * @return: void
     **/
    private void saveTikTokConverSationContent(Account account,
                                               String nextToken,
                                               Integer contentType,
                                               String convShortId,
                                               Boolean createTicketFlag,
                                               Long tikcetId,
                                               Long stationLetterId,
                                               Long customerId,
                                               Boolean tikeReadFlag) {
        if (StringUtils.isEmpty(convShortId)) {
            return;
        }
        HashMap<String, Object> reqMap = new HashMap<>();
        if(StringUtils.isNotEmpty(nextToken)) {
            reqMap.put("page_token", nextToken);//token
        }
        reqMap.put("page_size", 10);//条数
        //获取内容
        TikTokMessageListContentV2 tikTokMessageListContentV2 = null;
        try {
            tikTokMessageListContentV2 = tikTokUtil.getTikTokShopReturnV2(reqMap, TikTokApiEnums.GET_LIST_CONVERSATIONS_CONTENT_V2.getUrl(), TikTokApiEnums.GET_LIST_CONVERSATIONS_CONTENT_V2.getPath().replace("{conversation_id}",convShortId),TikTokMessageListContentV2.class,String.valueOf(account.getId()));
        } catch (Exception e) {
            log.error("调用TK会话内容失败：{}",e);
        }
        if (tikTokMessageListContentV2 == null || !tikTokMessageListContentV2.getMessage().equalsIgnoreCase("Success")) {
            log.error("--------------获取消息列表失败--------------");
            return;
        }

        TikTokMessageListContentDataV2 data = tikTokMessageListContentV2.getData();

        List<TikTokMessageListContentMessagesV2> msgs = data.getMessages();
        if (CollectionUtils.isEmpty(msgs)) {
            return;
        }

        String nextPageToken = data.getNextPageToken(); //下一页坐标

        Long ticketIdRecod = null;
        Boolean recursionCreateFlag = false;//递归创建工单状态
        //系统消息不保存
        List<TikTokMessageListContentMessagesV2> filteMsg = msgs.stream().filter(item -> !"SYSTEM".equals(item.getSender().getRole())).collect(Collectors.toList());
        if (filteMsg.size() > 0) { //存在真实会话在进行工单创建
            if (createTicketFlag) {
                ticketIdRecod = this.convContentCreateTicketV2(msgs, account, stationLetterId, customerId);
            } else {
                ticketIdRecod = tikcetId;
            }
        } else {
            if (createTicketFlag) {
                recursionCreateFlag = true; //本次未创建工单，下次递归继续创建
            } else {
                ticketIdRecod = tikcetId;
            }
        }


        //保存内容消息
        for (TikTokMessageListContentMessagesV2 msg : filteMsg) {
            if (StringUtils.isEmpty(msg.getContent())) {
                continue;
            }
           //判断消息是否存在
            LatestMsg latestMsg = new LatestMsg();
            //todo 这里差别太大，要手动设置
            latestMsg.setContent(msg.getContent());
            latestMsg.setConvShortId(convShortId);
            latestMsg.setCreateTime(msg.getCreateTime());
//            latestMsg.setIndexInConversation();
            latestMsg.setIsVisible(msg.getIsVisible());
            latestMsg.setMsgId(msg.getId());
            latestMsg.setMsgType(msg.getType());
            latestMsg.setSenderAvatar(msg.getSender().getAvatar());
            latestMsg.setSenderNick(msg.getSender().getNickname());
            if(StringUtils.isNotEmpty(msg.getSender().getRole())) {
                TikTokMessageV2Enum tikTokMessageV2Enum = TikTokMessageV2Enum.getByValue(msg.getSender().getRole());
                if(tikTokMessageV2Enum != null) {
                    latestMsg.setSenderRole(tikTokMessageV2Enum.getCode());
                }
            }
            Boolean readOperationFlag = this.conversationsContentSave(latestMsg, account.getOrgId(), ticketIdRecod, stationLetterId, tikeReadFlag);//保存回话内容
            if (readOperationFlag) {
                tikeReadFlag = readOperationFlag;
            }
        }

        //递归调用所有会话信息
        if (StringUtils.isNotEmpty(nextPageToken)) {
            saveTikTokConverSationContent(account, nextPageToken, contentType, convShortId, recursionCreateFlag, ticketIdRecod, stationLetterId, customerId,tikeReadFlag);
        } else {
            return;
        }

    }


    /**
     * @param msgs     会话列表
     * @param account  用户信息
     * @param sourceId 来源ID（回话主键）
     * @description: 会话内容创建工单方法
     * @author: Moore
     * @date: 2023/10/22 14:10
     * @return: 工单ID
     **/
    public Long convContentCreateTicket(List<Msg> msgs, Account account, Long sourceId, Long customerId) {

        //创建工单ID
        Map<Integer, List<Msg>> groupRole = msgs.stream().collect(Collectors.groupingBy(Msg::getSenderRole)); //根据角色分类进项分组
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("参与会话人: ");
        for (Integer roleFlag : groupRole.keySet()) {
            Msg item = groupRole.get(roleFlag).get(0);
            TikTokMessageEnum byCode = TikTokMessageEnum.getByCode(item.getSenderRole());
            if (null != byCode) {
                String value = byCode.getValue();
                stringBuffer.append(value + "-" + item.getSenderNick() + " "); //拼接 具体会话人
            } else {
                stringBuffer.append(item.getSenderNick() + "-" + item.getSenderNick() + " ");
            }
        }
        ScTicket scTicketCrete = new ScTicket();
        scTicketCrete.setTicketType(ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG);  //工单类型 站内信
        scTicketCrete.setTicketName(stringBuffer.toString()); //工单名称
        scTicketCrete.setRemark(account.getTitle()); //描述(店铺名)
        scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) +
                StringUtil.pad(sourceId.toString(), "l", 5, "0")+RandomUtil.randomNumbers(4));  //由于TK会话ID会对应过个工单，增加随机数防止重复
        scTicketCrete.setTicketSource(ScTicketConstant.TIKTOK_MESSAGE);// 工单来源(订单API)
        scTicketCrete.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH); //优先级（高）
        scTicketCrete.setSourceId(sourceId);//来源ID （会话主键,随机工单里取一个）
        scTicketCrete.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION); //来源单据 站内信
        scTicketCrete.setShopId(Long.valueOf(account.getId()));  //店铺
        scTicketCrete.setOrganizationId(Long.valueOf(account.getOrgId()));//组织ID
//        scTicketCrete.setCustomerId(customerId); //客户ID
        scTicketCrete.settingDefaultSystemCreate(); //系统默认创建
        scTicketService.insertScTicket(scTicketCrete);
        return scTicketCrete.getId();
    }


    /**
     * @param shopId      店铺ID
     * @param orgId       组织ID
     * @param sourceId    来源ID （会话主键）
     * @param nextToken   下页TOKEN
     * @param convShortId 回话ID
     * @param ticketId    工单ID
     * @param depth    轮询深度
     * @description: 刷新TIKTOK会话内容并保存
     * @author: Moore
     * @date: 2023/10/19 16:04
     * @return: void
     **/
    private void refreshConvContentSava(Long shopId,
                                        Integer orgId,
                                        Long sourceId,
                                        String nextToken,
                                        String convShortId,
                                        Long ticketId,
                                        Integer depth
    ) {
        HashMap<String, Object> reqMap = new HashMap<>();
        if(StringUtils.isNotEmpty(nextToken)) {
            reqMap.put("page_token", nextToken);//token
        }
        reqMap.put("page_size", 10);//条数
        //根据回话ID  获取回话内容
        TikTokMessageListContentV2 tikTokMessageListContentV2 = tikTokUtil.getTikTokShopReturnV2(reqMap, TikTokApiEnums.GET_LIST_CONVERSATIONS_CONTENT_V2.getUrl(), TikTokApiEnums.GET_LIST_CONVERSATIONS_CONTENT_V2.getPath().replace("{conversation_id}",convShortId),TikTokMessageListContentV2.class,String.valueOf(shopId));
       if (tikTokMessageListContentV2 == null || tikTokMessageListContentV2.getData() == null) {
            return;
        }
        TikTokMessageListContentDataV2 tikTokMessageListContentDataV2 = tikTokMessageListContentV2.getData();
       //对应message信息
        List<TikTokMessageListContentMessagesV2> msgs = tikTokMessageListContentDataV2.getMessages();
        if (CollectionUtils.isEmpty(msgs)) {
            return;
        }
        String nextPageToken = tikTokMessageListContentDataV2.getNextPageToken(); //nextPageToken


        //保存内容消息
        for (TikTokMessageListContentMessagesV2 msg : msgs) {
            if ("SYSTEM".equals(msg.getSender().getRole())) { //系统类型不保存
                continue;
            }
            if (StringUtils.isEmpty(msg.getType())){
                log.info("轮询会话msgType为null：{},{}",shopId,convShortId);
                continue;
            }
           if(!TikTokMsgContentTypeGetEnum.listContentType.stream().anyMatch(item -> item.equals(msg.getType()))){
               log.info("轮询会话msgType类型不符：{},{}", shopId, JSONObject.toJSONString(msg));
               continue;
           }

            //判断消息是否存在
            ScStationLetterMessage scStationLetterMessage = new ScStationLetterMessage();
            scStationLetterMessage.setOrganizationId(orgId); //组织ID
            scStationLetterMessage.setMsgId(msg.getId());
            List<ScStationLetterMessage> scStationLetterMessageList = scStationLetterMessageService.selectScStationLetterMessageList(scStationLetterMessage);
            if (!CollectionUtils.isEmpty(scStationLetterMessageList)) { //存在消息不保存
                continue;
            }
            scStationLetterMessage.setTicketId(ticketId); //工单主键ID
            scStationLetterMessage.setStationLetterId(sourceId); //站内信主键ID
            scStationLetterMessage.setMessageType(ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG); //站内信
            scStationLetterMessage.setCommonSendName(msg.getSender().getNickname()); //发送人

            //空内容不保存
            JSONObject contentJson = JSONObject.parseObject(msg.getContent());
            if (null == contentJson || (contentJson.containsKey("content")&&StringUtil.isEmpty(contentJson.getString("content")))) {
                continue;
            }

            //适配通用方法
            LatestMsg latestMsg = new LatestMsg();
            latestMsg.setContent(msg.getContent());
            latestMsg.setConvShortId(convShortId);
            latestMsg.setCreateTime(msg.getCreateTime());
//            latestMsg.setIndexInConversation();
            latestMsg.setIsVisible(msg.getIsVisible());
            latestMsg.setMsgId(msg.getId());
            latestMsg.setMsgType(msg.getType());
            latestMsg.setSenderAvatar(msg.getSender().getAvatar());
            latestMsg.setSenderNick(msg.getSender().getNickname());
            if(StringUtils.isNotEmpty(msg.getSender().getRole())) {
                TikTokMessageV2Enum tikTokMessageV2Enum = TikTokMessageV2Enum.getByValue(msg.getSender().getRole());
                if(tikTokMessageV2Enum != null) {
                    latestMsg.setSenderRole(tikTokMessageV2Enum.getCode());
                }
            }
            this.convContentChannel(latestMsg, scStationLetterMessage); //解析会话内容并设置
            scStationLetterMessage.setContentType(msg.getType()); //内容类型 text
            scStationLetterMessage.setSendTime(DateUtil.BjToPstByTimestamp(latestMsg.getCreateTime())); //发送时间
            if(StringUtils.isNotEmpty(msg.getSender().getRole())) {
                TikTokMessageV2Enum tikTokMessageV2Enum = TikTokMessageV2Enum.getByValue(msg.getSender().getRole());
                if(tikTokMessageV2Enum != null) {
                    scStationLetterMessage.setType(tikTokMessageV2Enum.getCode().toString()); //1.buyer  2.shop 2 customer service
                }
            }
            scStationLetterMessage.setReadFlag(msg.getIsVisible() ? "Y" : "N"); //行消息 已读状态
            scStationLetterMessage.setConvShortId(convShortId); //系统会话ID
            scStationLetterMessage.setMsgId(msg.getId()); //系统消息ID
            scStationLetterMessage.setSourceType(1); //来源 1.api 2.webhook 3.manual
            scStationLetterMessageService.insertScStationLetterTikTokMessage(scStationLetterMessage);

            //异步保存附件信息至工单附件 //只保存发送方（买家） //TODO 可能会重复
//            if ((TikTokConvContentTypeV2Enum.IMAGE.getContentType().equalsIgnoreCase(msg.getType())||TikTokConvContentTypeV2Enum.VIDEO.getContentType().equalsIgnoreCase(msg.getType()))
//                    &&!StringUtils.isEmpty(scStationLetterMessage.getContent())&&
//                    TikTokMessageV2Enum.BUYER.getCode().toString().equals(scStationLetterMessage.getType())) {
//                Long ticketFlag = ticketId;
//                Long orgIdFlag = orgId.longValue();
//                // 异步对附件操作
//                CompletableFuture.runAsync(() -> {
//                    try {
//                        saveMsgFileTotikcet(orgIdFlag, msg.getType(), scStationLetterMessage, ticketFlag);
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    }
//                }, taskExecutor);
//            }

        }



        CompletableFuture.runAsync(() -> {
            try {
                //首次调用(设置未读为1),只获取最新一条数据，来判断是已读还是未读
                if (depth == 1 && !CollectionUtils.isEmpty(msgs)) {
                    TikTokMessageListContentMessagesV2 messagOne = msgs.get(0); //代表最新一条消息
                    TikTokMessageV2Enum tikTokMessageV2Enum = TikTokMessageV2Enum.getByValue(messagOne.getSender().getRole());
                    if (ticketId != null && tikTokMessageV2Enum != null) {
                        ScTicket scTicketUpdate = new ScTicket();
                        scTicketUpdate.setId(ticketId);
                        if ("1".equalsIgnoreCase(tikTokMessageV2Enum.getCode().toString())) { //买家发给卖家
                            ScTicket scTicket = scTicketService.selectScTicketById(ticketId);
                            Integer readFlag = 0;
                            if (scTicket != null) {
                                readFlag = (scTicket.getNoReplyQty() == null ? 0 : scTicket.getNoReplyQty());
                            }
                            scTicketUpdate.setNoReplyQty(readFlag + 1); //未回复数量
                            scTicketUpdate.setTicketReplyStatus(0); //状态 未回复
                            scTicketUpdate.settingDefaultSystemUpdate();
                            scTicketService.updateScTickeByTicketId(scTicketUpdate);
                        } else if (!"6".equalsIgnoreCase(tikTokMessageV2Enum.getCode().toString())){ //卖家发给买家
                            scTicketUpdate.setNoReplyQty(0); //未回复数量
                            scTicketUpdate.setTicketReplyStatus(1); //状态 已回复
                            scTicketUpdate.setBusinessType(null);
                            scTicketUpdate.settingDefaultSystemUpdate();
                            scTicketService.updateScTicketBusinessType(scTicketUpdate);
                        }


                        //临时封装通用方法适配对象
                        ScStationLetterMessage scStationLetterMessage = new ScStationLetterMessage();
                        scStationLetterMessage.setConvShortId(convShortId); //会话ID
                        scStationLetterMessage.setContentType(messagOne.getType()); //内容类型 text
                        if (TikTokConvContentTypeV2Enum.TEXT.getContentType().equalsIgnoreCase(messagOne.getType())) { //会话内容
                            scStationLetterMessage.setContent(JSONObject.parseObject(messagOne.getContent()).getString(TikTokConvContentTypeV2Enum.TEXT.getValue()));//图片
                        }
                        this.saveTkMsgServiceType(scTicketUpdate, scStationLetterMessage); //
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }, taskExecutor);

        if (StringUtils.isEmpty(nextPageToken) || depth >= 2) {
            return;
        }
        //递归调用获取会话信息
        refreshConvContentSava(shopId, orgId, sourceId, nextPageToken, convShortId, ticketId,depth + 1);

    }

        /**
         * @param
         * @param shopId
         * @description:根据平台店铺ID获取店铺主键ID
         * @author: Moore
         * @date: 2023/10/24 17:57
         * @return: java.lang.Integer
         **/
        public Account getShopId(String shopId) {
            if (com.bizark.op.common.util.StringUtil.isEmpty(shopId)) {
                return null;
            }
            Account account = new Account();
            account.setType("tiktok");
            List<Account> accounts = accountService.selectAccountListRefactor(account);
            for (Account accountItem : accounts) {
                JSONObject accountItemJson = JSONObject.parseObject(accountItem.getConnectStr());
                if (null != accountItemJson) {
                    if (shopId.equals(accountItemJson.getString("shopId")))
                        return accountItem;
                }
            }
            return null;
        }



    /**
     * @param
     * @param tiktokData tiktok订单数据
     * @description: 接收webHook消息数据
     * @author: Moore
     * @date: 2023/11/22 16:34
     * @return: void
     **/
    @Override
    public void saveWebHookMessage(JSONObject tiktokData) {
        if (null != tiktokData && null != tiktokData.get("type") && tiktokData.get("type").equals(9)) {
            TikTokWebhookMsgData tikTokData = JSON.toJavaObject(tiktokData, TikTokWebhookMsgData.class);
            if (null != tikTokData && null != tikTokData.getData()) {
                Account account = getShopId(tikTokData.getShopId());
                if (null == account) {
                    log.error("订单会话TIKTOK店铺信息获取失败:{}", tikTokData.getShopId());
                    return;
                }
                TikTokConvConData convconInfo = tikTokData.getData();
                String messageId = convconInfo.getMessageId();//消息ID
                String convShortId = convconInfo.getConvShortId();//会话ID
                Integer senderRole = convconInfo.getSenderRole();
                //系统消息类型不保存
                if (convconInfo.getSenderRole() == TikTokMessageEnum.SYSTEM.getCode()) {
                    return;
                }

                //判断消息是否存在
                ScStationLetterMessage scStationLetterMessage = new ScStationLetterMessage();
                scStationLetterMessage.setOrganizationId(account.getOrgId()); //组织ID
                scStationLetterMessage.setMsgId(messageId); //消息ID 确定唯一值
                List<ScStationLetterMessage> scStationLetterMessageList = scStationLetterMessageService.selectScStationLetterMessageList(scStationLetterMessage);
                if (!CollectionUtils.isEmpty(scStationLetterMessageList)) {
                    return;
                }
                //查询会话列表
                ScStationLetter scStationLetter = scStationLetterService.selectletterByConvShortId(convShortId);
                //会话列表不存在且会话非客户，不创建工单，也不保存此消息
                if (scStationLetter == null && senderRole != 1) {
                    return;
                }

                Long ticketId = null;
                Long stationLetterId = null;

                if (scStationLetter == null) {
                    //无会话即无工单
                    ScStationLetter scStationLetterSave = new ScStationLetter();
                    scStationLetterSave.setOrganizationId(account.getOrgId().longValue());
                    scStationLetterSave.setConvShortId(convShortId); //会话ID
                    scStationLetterSave.setAccountId(account.getId());
                    scStationLetterSave.settingDefaultCreate();
                    scStationLetterSave.setSourceType(2); //1.api 2.webhook  3.manual',
                    scStationLetterSave.setChannelType(AccountSaleChannelEnum.TIKTOK.getValue()); //设置渠道为tiktok
                    scStationLetterService.insertScStationLetter(scStationLetterSave);
                    stationLetterId = scStationLetterSave.getStationLetterId();
                    ticketId = convConCreateTicket(scStationLetterSave.getStationLetterId(), account, null);
                } else {
                    List<ScTicket> scTicket = scTicketService.selectScTicketBySourceList(scStationLetter.getStationLetterId(), ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION); //获取会话下所有工单
                    List<ScTicket> filteTicket = scTicket.stream().filter(item -> ScTicketConstant.TICKET_STATUS_NEW.equals(item.getTicketStatus()) || ScTicketConstant.TICKET_STATUS_PROCESSING.equals(item.getTicketStatus())).collect(Collectors.toList()); //获取新建或处理中的会话（新建及处理中的会话 工单，只会存在一条）
                    if (!CollectionUtils.isEmpty(filteTicket)) {
                        ticketId = filteTicket.get(0).getId();
                    } else {
                        ScTicket scTicketCrete = new ScTicket();
                        scTicketCrete.setTicketType(ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG);  //工单类型 站内信
                        scTicketCrete.setTicketName(CollectionUtils.isEmpty(scTicket) ? scTicket.get(0).getTicketName() : "新TIKTOK会话消息-"); //工单名称
                        scTicketCrete.setRemark(account.getTitle()); //描述
                        scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(scStationLetter.getStationLetterId().toString(), "l", 5, "0")+RandomUtil.randomNumbers(4));
                        scTicketCrete.setTicketSource(ScTicketConstant.TIKTOK_MESSAGE);// 工单来源(订单API)
                        scTicketCrete.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH); //优先级（高）
                        scTicketCrete.setSourceId(scStationLetter.getStationLetterId());//来源ID
                        scTicketCrete.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION); //来源单据 站内信
                        scTicketCrete.setShopId(Long.valueOf(account.getId()));  //店铺
                        scTicketCrete.setOrganizationId(Long.valueOf(account.getOrgId()));//组织ID
//                        scTicketCrete.setCustomerId(CollectionUtils.isEmpty(scTicket) ? scTicket.get(0).getCustomerId() : null); //客户ID
                        scTicketService.insertScTicket(scTicketCrete);
                        ticketId = scTicketCrete.getId();//工单主键
                    }
                    stationLetterId = scStationLetter.getStationLetterId();
                }
                //保存会话
                scStationLetterMessage.setTicketId(ticketId); //工单主键ID
                scStationLetterMessage.setStationLetterId(stationLetterId); //站内信主键ID
                scStationLetterMessage.setMessageType(ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG); //站内信类型
                scStationLetterMessage.setCommonSendName("-"); //发送人
                LatestMsg latestMsg = JSONObject.toJavaObject(tiktokData, LatestMsg.class);
                this.convContentChannel(latestMsg, scStationLetterMessage); //解析会话内容,并设置
                scStationLetterMessage.setSendTime(DateUtil.BjToPstByTimestamp(latestMsg.getCreateTime())); //消息发送时间
                scStationLetterMessage.setType(latestMsg.getSenderRole() == null ? null : latestMsg.getSenderRole().toString()); //发送人类型 1.buyer  2.shop 2 customer service
                scStationLetterMessage.setReadFlag(latestMsg.getIsVisible() ? "Y" : "N"); //已读状态
                scStationLetterMessage.setConvShortId(latestMsg.getConvShortId()); //平台会话ID
                scStationLetterMessage.setMsgId(latestMsg.getMsgId()); //平台消息ID
                scStationLetterMessage.setSourceType(2);//消息来源API
                scStationLetterMessageService.insertScStationLetterTikTokMessage(scStationLetterMessage);
            }

        }
    }


    /**
     * @description: 接收webhook时判断创建工单（弃用）
     * @author: Moore
     * @date: 2023/11/22 17:10
     * @param
     * @param convShortId 会话ID
     * @param senderRole 用户角色
     * @return: java.lang.Long
    **/
    public Long  webHookMessageSaveStationListCreateTicket(String  convShortId,Integer senderRole,Account account) {
        //查询会话列表
        ScStationLetter scStationLetter = scStationLetterService.selectletterByConvShortId(convShortId);
        //会话列表不存在且会话非客户，不创建工单，也不保存此消息
        if (scStationLetter == null && senderRole != 1) {
            return null;
        }
        if (scStationLetter == null) {
            //无会话即无工单
            ScStationLetter scStationLetterSave = new ScStationLetter();
            scStationLetterSave.setOrganizationId(account.getOrgId().longValue());
            scStationLetterSave.setConvShortId(convShortId); //会话ID
            scStationLetterSave.setAccountId(account.getId());
            scStationLetterSave.settingDefaultCreate();
            scStationLetterSave.setSourceType(2); //1.api 2.webhook  3.manual',
            scStationLetterSave.setChannelType(AccountSaleChannelEnum.TIKTOK.getValue()); //设置渠道为tiktok
            scStationLetterService.insertScStationLetter(scStationLetterSave);
            return convConCreateTicket(scStationLetterSave.getStationLetterId(), account, null);

        } else {
            List<ScTicket> scTicket = scTicketService.selectScTicketBySourceList(scStationLetter.getStationLetterId(), ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION); //获取会话下所有工单
            List<ScTicket> filteTicket = scTicket.stream().filter(item -> ScTicketConstant.TICKET_STATUS_NEW.equals(item.getTicketStatus()) || ScTicketConstant.TICKET_STATUS_PROCESSING.equals(item.getTicketStatus())).collect(Collectors.toList()); //获取新建或处理中的会话（新建及处理中的会话 工单，只会存在一条）
            if (!CollectionUtils.isEmpty(filteTicket)) {
                return filteTicket.get(0).getId();
            } else {
                ScTicket scTicketCrete = new ScTicket();
                scTicketCrete.setTicketType(ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG);  //工单类型 站内信
                scTicketCrete.setTicketName(CollectionUtils.isEmpty(scTicket) ? scTicket.get(0).getTicketName() : "新TIKTOK会话消息-"); //工单名称
                scTicketCrete.setRemark(account.getTitle()); //描述
                scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(scStationLetter.getStationLetterId().toString(), "l", 5, "0")+RandomUtil.randomNumbers(4));
                scTicketCrete.setTicketSource(ScTicketConstant.TIKTOK_MESSAGE);// 工单来源(订单API)
                scTicketCrete.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH); //优先级（高）
                scTicketCrete.setSourceId(scStationLetter.getStationLetterId());//来源ID
                scTicketCrete.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION); //来源单据 站内信
                scTicketCrete.setShopId(Long.valueOf(account.getId()));  //店铺
                scTicketCrete.setOrganizationId(Long.valueOf(account.getOrgId()));//组织ID
//                scTicketCrete.setCustomerId(CollectionUtils.isEmpty(scTicket) ? scTicket.get(0).getCustomerId() : null); //客户ID
                scTicketService.insertScTicket(scTicketCrete);
                return scTicketCrete.getId();//工单主键
            }
        }
    }


    /**
     * @param stationLetterId 会话表主键
     * @param account 电铺
     * @param customerId 客户ID
     * @description:会话内容工单
     * @author: Moore
     * @date: 2023/10/22 15:54
     * @return: void
     **/
    public Long convConCreateTicket(Long stationLetterId, Account account, Long customerId) {
        //创建工单（根据会话查询工单）
            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append("新TIKTOK会话消息");
            ScTicket scTicketCrete = new ScTicket();
            scTicketCrete.setTicketType(ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG);  //工单类型 站内信
            scTicketCrete.setTicketName(stringBuffer.toString()); //工单名称
            scTicketCrete.setRemark(account.getTitle()); //描述
            scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(stationLetterId.toString(), "l", 5, "0")+RandomUtil.randomNumbers(4));
            scTicketCrete.setTicketSource(ScTicketConstant.TIKTOK_MESSAGE);// 工单来源(订单API)
            scTicketCrete.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH); //优先级（高）
            scTicketCrete.setSourceId(stationLetterId);//来源ID
            scTicketCrete.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION); //来源单据 站内信
            scTicketCrete.setShopId(Long.valueOf(account.getId()));  //店铺
            scTicketCrete.setOrganizationId(Long.valueOf(account.getOrgId()));//组织ID
//            scTicketCrete.setCustomerId(customerId); //客户ID
            scTicketService.insertScTicket(scTicketCrete);
            return scTicketCrete.getId();//工单主键
    }


    /**
     * @Description:创建会话并发送消息
     * @Author: wly
     * @Date: 2024/6/26 17:48
     * @Params: [ message, authUserEntity]
     * @Return: void
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createTiktokConvAndSendMessageNew(MarSendMessageOrders message, UserEntity authUserEntity) {

        //id,shopId,buyId,content必传，convShortId可选
        if (message.getId() == null) {
            log.error("id不能为空!");
            return "id不能为空";
        }
        if (message.getShopId() == null) {
            log.error("店铺id不能为空!");
            return "店铺id不能为空";
        }
        if (StringUtils.isEmpty(message.getBuyId())) {
            log.error("买家id不能为空!");
            return "买家id不能为空";
        }
        if (StringUtils.isEmpty(message.getContent())) {
            log.error("消息内容不能为空!");
            return "消息内容不能为空";
        }


        Account account = new Account();
        account.setId(message.getShopId().intValue());
        List<Account> accounts = accountService.selectAccountListRefactor(account);
        if (CollectionUtils.isEmpty(accounts)) {
            return "店铺不能为空";
        }

        if (StringUtils.isEmpty(message.getConvShortId())){ //系统暂无该用户会话
            SendMessageResponse tikTokShopReturn = null;
            if (!TIKTOK_SWITCH) {
                tikTokShopReturn = new SendMessageResponse();
            } else{
                JSONObject req = new JSONObject();
                req.put("buyer_user_id", message.getBuyId());
                try {
                    tikTokShopReturn = tikTokUtil.postTikTokShopV2(req.toJSONString(), TikTokApiEnums.CREATE_TIKTOK_CONV_NEW.getUrl(), TikTokApiEnums.CREATE_TIKTOK_CONV_NEW.getPath(), SendMessageResponse.class, message.getShopId(), null);
                } catch (Exception e) {
                    log.error("创建会话失败：{}", e.getMessage());
                    return "创建会话失败";
                }
                if (tikTokShopReturn == null || tikTokShopReturn.getData() == null || StringUtils.isEmpty(tikTokShopReturn.getData().getConversationId()) || !"Success".equalsIgnoreCase(tikTokShopReturn.getMessage())) {
                    log.error("创建会话响应失败:{}", tikTokShopReturn);
                    return "创建会话响应失败";
                } else {
                    message.setConvShortId(tikTokShopReturn.getData().getConversationId());
                }
            }
        }
        return this.sendTiktokConvContentMessageNew(message); //发送消息

    }

    /**
     * @Description:回复消息
     * @Author: wly
     * @Date: 2024/6/26 18:11
     * @Params: [sendRequest]
     * @Return: int
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String sendTiktokConvContentMessageNew(MarSendMessageOrders sendRequest) {

        //  TODO 暂时只有消息没有图片，图片不做非空校验
        //封装消息内容
        List<JSONObject> jsonObjectList = this.senMessageEntityTransitionNew(sendRequest);

        for (JSONObject jsonObject : jsonObjectList) {
            //调用发送消息
            String convShortId = sendRequest.getConvShortId();
            SendMessageResponse sendMessageResponse = null;
            try {
                if (TIKTOK_SWITCH) {
                    log.error("发送消息请求体：{}",JSONObject.toJSONString(jsonObject));
                    sendMessageResponse = tikTokUtil.postTikTokShopV2(JSONObject.toJSONString(jsonObject), TikTokApiEnums.SEND_MESSAGE_TIKTOK_NEW.getUrl(), TikTokApiEnums.SEND_MESSAGE_TIKTOK_NEW.getPath().replace("{conversation_id}",convShortId), SendMessageResponse.class, sendRequest.getShopId());
                    log.error("发送消息响应：{}",sendMessageResponse);
                } else {
                    log.error("发送消息请求体：{}",JSONObject.toJSONString(jsonObject));
                    sendMessageResponse = new SendMessageResponse();
                    sendMessageResponse.setMessage("Success");
                }

            } catch (Exception e) {
                log.error("会话id:{},TIKTOK发送会话消息异常：{}", sendRequest.getConvShortId(),e.getMessage());
                //发送失败记录发送状态为发送失败，失败原因，发送时间
                MarSendMessageOrders marSendMessageOrders = new MarSendMessageOrders();
                marSendMessageOrders.setId(sendRequest.getId());
                marSendMessageOrders.setSendStatus(0);
                marSendMessageOrders.setSendTime(DateUtils.getNowDate());
                marSendMessageOrders.setConvShortId(sendRequest.getConvShortId());
                String errMsg = "";
                if (StringUtils.isNotEmpty(e.getMessage())) {
                    errMsg = "发送会话消息异常:" + e.getMessage();
                    if (errMsg.length() > 200)
                        errMsg = errMsg.substring(0, 200);
                } else {
                    errMsg = "发送会话消息异常";
                }
                marSendMessageOrders.setErrMsg(errMsg);
                marSendMessageOrders.setErrorMsgType(2);
                marSendMessageOrdersService.updateById(marSendMessageOrders);
                String finalErrMsg = errMsg;
                threadPoolTaskExecutor.execute(()-> QywxUtil.wxMessageSend("15088754050",String.format("订单号:[%s],发送订单id:[%s]，tiktok首次发送会话消息异常:[%s]",sendRequest.getOrderNo(),sendRequest.getId(), finalErrMsg)));
                return errMsg;
            }
            if (sendMessageResponse != null && "success".equalsIgnoreCase(sendMessageResponse.getMessage())) {
                log.info("会话id：{}发送消息成功,{}",sendRequest.getConvShortId(),sendMessageResponse);
                //发送成功，记录发送时间，发送状态,更新会话id
                MarSendMessageOrders marSendMessageOrders = new MarSendMessageOrders();
                marSendMessageOrders.setId(sendRequest.getId());
                marSendMessageOrders.setSendStatus(1);
                marSendMessageOrders.setSendTime(DateUtils.getNowDate());
                marSendMessageOrders.setConvShortId(sendRequest.getConvShortId());
                marSendMessageOrdersService.updateById(marSendMessageOrders);
                return null;
            } else {
                //首次发送失败，再次发送
                //再次发送开始
                try {
                    log.error("再次发送消息请求体：{}",JSONObject.toJSONString(jsonObject));
                    sendMessageResponse = tikTokUtil.postTikTokShopV2(JSONObject.toJSONString(jsonObject), TikTokApiEnums.SEND_MESSAGE_TIKTOK_NEW.getUrl(), TikTokApiEnums.SEND_MESSAGE_TIKTOK_NEW.getPath().replace("{conversation_id}",convShortId), SendMessageResponse.class, sendRequest.getShopId());
                    log.error("再次发送消息响应：{}",sendMessageResponse);
                } catch (Exception e) {
                    log.error("再次发送会话id:{},TIKTOK发送会话消息异常：{}", sendRequest.getConvShortId(),e.getMessage());
                    //发送失败记录发送状态为发送失败，失败原因，发送时间
                    MarSendMessageOrders marSendMessageOrders = new MarSendMessageOrders();
                    marSendMessageOrders.setId(sendRequest.getId());
                    marSendMessageOrders.setSendStatus(0);
                    marSendMessageOrders.setSendTime(DateUtils.getNowDate());
                    marSendMessageOrders.setConvShortId(sendRequest.getConvShortId());
                    String errMsg = "";
                    if (StringUtils.isNotEmpty(e.getMessage())) {
                        errMsg = "二次发送会话消息异常:" + e.getMessage();
                        if (errMsg.length() > 200)
                            errMsg = errMsg.substring(0, 200);
                    } else {
                        errMsg = "二次发送会话消息异常";
                    }
                    marSendMessageOrders.setErrMsg(errMsg);
                    marSendMessageOrders.setErrorMsgType(2);
                    marSendMessageOrdersService.updateById(marSendMessageOrders);
                    String finalErrMsg = errMsg;
                    threadPoolTaskExecutor.execute(()->QywxUtil.wxMessageSend("15088754050",String.format("订单号:[%s],发送订单id:[%s]，tiktok二次发送会话消息异常:[%s]",sendRequest.getOrderNo(),sendRequest.getId(), finalErrMsg)));
                    return errMsg;
                }
                //再次发送结束

                if (sendMessageResponse != null && "success".equalsIgnoreCase(sendMessageResponse.getMessage())) {
                    log.info("二次发送会话id：{}发送消息成功,{}",sendRequest.getConvShortId(),sendMessageResponse);
                    //发送成功，记录发送时间，发送状态,更新会话id
                    MarSendMessageOrders marSendMessageOrders = new MarSendMessageOrders();
                    marSendMessageOrders.setId(sendRequest.getId());
                    marSendMessageOrders.setSendStatus(1);
                    marSendMessageOrders.setSendTime(DateUtils.getNowDate());
                    marSendMessageOrders.setConvShortId(sendRequest.getConvShortId());
                    marSendMessageOrdersService.updateById(marSendMessageOrders);
                    return null;
                }else {
                    log.error("二次发送会话id：{}发送消息失败,{}",sendRequest.getConvShortId(),sendMessageResponse);
                    //发送失败记录发送状态为发送失败，失败原因，发送时间
                    MarSendMessageOrders marSendMessageOrders = new MarSendMessageOrders();
                    marSendMessageOrders.setId(sendRequest.getId());
                    marSendMessageOrders.setSendStatus(0);
                    marSendMessageOrders.setSendTime(DateUtils.getNowDate());
                    marSendMessageOrders.setConvShortId(sendRequest.getConvShortId());
                    String errMsg = "";
                    if (sendMessageResponse != null && StringUtils.isNotEmpty(sendMessageResponse.getMessage())) {
                        errMsg = "二次发送会话消息失败:" + sendMessageResponse.getMessage();
                        if (errMsg.length() > 200)
                            errMsg = errMsg.substring(0, 200);
                    } else {
                        errMsg = "二次发送会话消息失败";
                    }
                    marSendMessageOrders.setErrMsg(errMsg);
                    marSendMessageOrders.setErrorMsgType(2);
                    marSendMessageOrdersService.updateById(marSendMessageOrders);
                    String finalErrMsg = errMsg;
                    threadPoolTaskExecutor.execute(()->QywxUtil.wxMessageSend("15088754050",String.format("订单号:[%s],发送订单id:[%s]，tiktok二次发送会话消息失败:[%s]",sendRequest.getOrderNo(),sendRequest.getId(), finalErrMsg)));
                    return errMsg;
                }

            }
        }
        return null;
    }

    /**
     * @Description:封装发送消息行
     * @Author: wly
     * @Date: 2024/6/26 18:18
     * @Params: [sendRequest]
     * @Return: java.util.HashMap<java.lang.String,java.util.List<com.alibaba.fastjson.JSONObject>>
     **/
    List<JSONObject> senMessageEntityTransitionNew(MarSendMessageOrders sendRequest) {
        List<JSONObject> msg = new ArrayList<>();
        //消息只会有一条
        if (!StringUtils.isEmpty(sendRequest.getContent())) {
            JSONObject context = new JSONObject();
            context.put("type","TEXT");
            JSONObject object = new JSONObject();
            object.put("content", sendRequest.getContent());
            context.put("content", JSONObject.toJSONString(object));
            msg.add(context);
        }

        //图片消息封装
        /*List<MessageUploadImage> files = sendRequest.getFiles();
        if (!CollectionUtils.isEmpty(files)) {
            for (MessageUploadImage file : files) {
                JSONObject context = new JSONObject();
                context.put("type","IMAGE");
                JSONObject itemImg = new JSONObject();
                itemImg.put("height", file.getHeight());
                itemImg.put("url", file.getUrl());
                itemImg.put("width", file.getWidth());
                context.put("context", JSONObject.toJSONString(itemImg));
                msg.add(context);
            }

        }*/
        return msg;
    }



    /**
     * Description: 接收tk的站内信消息
     *
     * @param tiktokData
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/9/9
     */
    @Override
    @Transactional
    @Async("threadPoolTaskExecutor")
    public void saveTiktokStationLetterMessage(JSONObject tiktokData) {


        //限定接收指定消息类型
        if (null != tiktokData && null != tiktokData.get("type") && tiktokData.get("type").equals(14) && tiktokData.getJSONObject("data") != null
                && TikTokMsgContentTypeGetEnum.listContentType.stream().anyMatch(item -> item.equals(tiktokData.getJSONObject("data").getString("type")))) {

            log.error("webhook接收tiktok新消息:{}", tiktokData);
            JSONObject data = tiktokData.getJSONObject("data"); //数据
            long msgTime = tiktokData.getLongValue("timestamp"); //发送时间
            String shopId = tiktokData.getString("shop_id"); //店铺ID
            String conversationId = data.getString("conversation_id"); //会话ID


            //获取消息角色信息
            JSONObject sender = data.getJSONObject("sender");//发送角色信息对象
            String msgId = data.getString("message_id");//单个messageId
            String msgContent = data.getString("content"); //会话内容
            String msgContenttype = data.getString("type");//会话类型
            Boolean is_visible = data.getBoolean("is_visible");//是否已读
            //无角色，或会话消息为空
            if (sender == null||StringUtils.isEmpty(msgContent)) {
                return;
            }
            String role = sender.getString("role"); //角色
            String imUserId = sender.getString("im_user_id"); //用户iD

            if (StringUtils.isEmpty(conversationId)||StringUtils.isEmpty(imUserId)||"SYSTEM".equals(role)) {
                log.error("无会话id或无用户ID,或角色不符", tiktokData);
                return;
            }


            Account account = accountService.getAccountByShopId(shopId);
            if (account == null || !account.getOrgId().equals(1000049)) {
                log.error("接收TK新消息未获取店铺,或非本组织,id{}", shopId);
                return;
            }



            //根据会话主信息获取会话是否存在
            ScStationLetter scStationLetter = scStationLetterService.selectletterByConvShortId(conversationId);
            if (scStationLetter == null) {
                try {
                    Thread.sleep(6000L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                scStationLetter = scStationLetterService.selectletterByConvShortId(conversationId);
            }
            if (null == scStationLetter) {
                //非 buyer不创建工单 （因为考虑到buyerID引用问题） //TODO 此处待确认
//                if (!TikTokConstant.MSG_ROLE_BUYER.equals(sender.getString("role"))) {
////                    log.info("首次");
//                    return;
//                }



                ScStationLetter scStationLetterSave = new ScStationLetter();
                scStationLetterSave.setOrganizationId(account.getOrgId().longValue()); //组织ID
                scStationLetterSave.setConvShortId(conversationId); //会话ID
//                    scStationLetterSave.setCustomerName();//客户名称 ，webhook未提供客户名称，后续需定时补偿
//                scStationLetterSave.setOuterId(imUserId);//平台客户ID

                scStationLetterSave.setFirstSendTime(DateUtil.BjToPstByTimestamp(msgTime)); //会话创建时间
                scStationLetterSave.setAccountId(account.getId());
                scStationLetterSave.setSourceType(TikTokConstant.CONV_MESSAGE_SOURCE_WEBHOOK); //webhook 来源
                scStationLetterSave.setChannelType(AccountSaleChannelEnum.TIKTOK.getValue()); //设置渠道为tiktok
                scStationLetterSave.settingDefaultSystemCreate();
                scStationLetterService.insertScStationLetter(scStationLetterSave);
                Long stationLetterId = scStationLetterSave.getStationLetterId();
                if (stationLetterId == null) {
                    log.info("新增无会话主键：{}", tiktokData);
                    return;
                }
                this.webhookSaveMessage(msgTime, conversationId, account, msgId, msgContent, msgContenttype, is_visible, imUserId, role, stationLetterId);
                    // 异步设置会话名称
                    CompletableFuture.runAsync(() -> {
                    try {
                        Thread.sleep(20000); // 延时20秒
                        saveTikTokConversationListAndCreateTicketNew(
                                account,
                                null,
                                 10,
                                conversationId);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }, taskExecutor);


            } else {
                this.webhookSaveMessage(msgTime, conversationId, account, msgId, msgContent, msgContenttype, is_visible, imUserId, role, scStationLetter.getStationLetterId());
            }
        }

    }


    /**
     * @description:
     * @author: Moore
     * @date: 2025/2/5 16:35
     * @param
     * @param timestamp 发送时间
     * @param conversationId 平台会话ID
     * @param account 账户ID
     * @param msgId 消息ID
     * @param content 内容
     * @param type 消息内容
     * @param is_visible  是否已读
     * @param imUserId 用户ID
     * @param role  角色
     * @param stationLetterId  会话表主键
     * @return: void
    **/
    public void webhookSaveMessage(long timestamp, String conversationId, Account account, String msgId, String content, String type, Boolean is_visible, String imUserId, String role, Long stationLetterId) {

        //判断此条消息是否存在
        List<ScStationLetterMessage> scStationLetterMessageList = scStationLetterMessageService.lambdaQuery()
                .eq(ScStationLetterMessage::getMsgId, msgId).list();
        if (!CollectionUtils.isEmpty(scStationLetterMessageList)) {
            try {
                Thread.sleep(2000L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            scStationLetterMessageList = scStationLetterMessageService.lambdaQuery()
                    .eq(ScStationLetterMessage::getMessageId, msgId).list();
        }
        if (!CollectionUtils.isEmpty(scStationLetterMessageList)) {
            return;
        }


        //判断消息是否存在
        ScStationLetterMessage scStationLetterMessage = new ScStationLetterMessage();
        scStationLetterMessage.setOrganizationId(account.getOrgId()); //组织ID
        scStationLetterMessage.setMsgId(msgId); //消息ID 确定唯一值


        //scStationLetterMessage.setTicketId(); //调整为不需要TKid，通过会话iD直接获取列表信息
        scStationLetterMessage.setStationLetterId(stationLetterId); //站内信主键ID
        scStationLetterMessage.setMessageType(ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG); //站内信类型
        //  scStationLetterMessage.setCommonSendName(latestMsg.getSenderNick()); //发送人 webhook无名称，后续补偿

        LatestMsg latestMsg = new LatestMsg();
        latestMsg.setContent(content);
        latestMsg.setMsgType(type);
        this.convContentChannel(latestMsg, scStationLetterMessage); //解析会话内容,并设置
        scStationLetterMessage.setSendTime(DateUtil.BjToPstByTimestamp(timestamp)); //消息发送时间
        //发送方类型
        TikTokMessageV2Enum tikTokMessageV2Enum = TikTokMessageV2Enum.getByValue(role);//发送人类型 1.buyer  2.shop 2 customer service
        if (tikTokMessageV2Enum != null) {
            scStationLetterMessage.setType(tikTokMessageV2Enum.getCode().toString());
        }

        scStationLetterMessage.setReadFlag(is_visible ? "Y" : "N"); //已读状态
        scStationLetterMessage.setConvShortId(conversationId); //平台会话ID
        scStationLetterMessage.setMsgId(msgId); //平台消息ID
        scStationLetterMessage.setSourceType(TikTokConstant.CONV_MESSAGE_SOURCE_WEBHOOK);//消息来源webHook
        scStationLetterMessage.settingDefaultSystemCreate(); //默认系统创建
        scStationLetterMessageService.insertScStationLetterTikTokMessage(scStationLetterMessage);


        //获取所有工单
        List<ScTicket> scTicket = scTicketService.selectScTicketBySourceList(stationLetterId, ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION);

            Integer readFlag = 0;
            Long ticketId = null;
            if (CollectionUtils.isEmpty(scTicket)) { //工单不存在（或没有新建 及正在进行中的工单）
                ScTicket scTicketCrete = new ScTicket();
                scTicketCrete.setTicketType(ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG);  //工单类型 站内信
                scTicketCrete.setTicketName("参与会话人:" + "BUYER_ID:" + imUserId); //工单名称
                scTicketCrete.setRemark(account.getTitle()); //描述
                scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(stationLetterId.toString(), "l", 5, "0") + RandomUtil.randomNumbers(4));//TK回话会存在于多个工单，增加随机数
                scTicketCrete.setTicketSource(ScTicketConstant.TIKTOK_MESSAGE);// 工单来源(订单API)
                scTicketCrete.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH); //优先级（高）
                scTicketCrete.setSourceId(stationLetterId);//来源ID
                scTicketCrete.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION); //来源单据 站内信
                scTicketCrete.setShopId(Long.valueOf(account.getId()));  //店铺
                scTicketCrete.setOrganizationId(Long.valueOf(account.getOrgId()));//组织ID
                scTicketCrete.setTicketReplyStatus(0); //是否回复，默认新建未回复
                scTicketCrete.setNoReplyQty(is_visible ? 0: 1); //新建未回复数1
                scTicketCrete.settingDefaultSystemCreate();
                scTicketService.insertScTicket(scTicketCrete);
                ticketId = scTicketCrete.getId();//工单主键
            } else {
                //从新建或者进行中
                ScTicket scTicketNew = scTicket.stream().filter(item -> ScTicketConstant.TICKET_STATUS_NEW.equals(item.getTicketStatus())).findFirst().orElse(null);
                ScTicket scTicketProcessing = scTicket.stream().filter(item -> ScTicketConstant.TICKET_STATUS_PROCESSING.equals(item.getTicketStatus())).findFirst().orElse(null);
                ScTicket scTicketClose = scTicket.stream().filter(item -> ScTicketConstant.TICKET_STATUS_CLOSED.equals(item.getTicketStatus())|| ScTicketConstant.TICKET_STATUS_FINISH.equals(item.getTicketStatus())).findFirst().orElse(null);
                if (scTicketNew != null) {  //优先取新建
                    ticketId = scTicketNew.getId();
                    readFlag = scTicketNew.getNoReplyQty() == null ? 0 : scTicketNew.getNoReplyQty() ;
                }else if (scTicketProcessing !=null) { //其次取 进行中
                    ticketId = scTicketProcessing.getId();
                    readFlag = scTicketProcessing.getNoReplyQty() == null ? 0 : scTicketProcessing.getNoReplyQty() ;
                } else if (scTicketClose != null) { //其将关闭工单打开使用
                    ticketId = scTicketClose.getId();
                    ScTicket scTicketUpdate = new ScTicket();
                    scTicketUpdate.setId(ticketId);
                    scTicketUpdate.setTicketStatus(ScTicketConstant.TICKET_STATUS_NEW); //新会话，重新开启工单
                    scTicketUpdate.settingDefaultSystemUpdate();
                    scTicketService.updateScTicketStatus(scTicketUpdate);
                    readFlag = scTicketClose.getNoReplyQty() == null ? 0 : scTicketClose.getNoReplyQty();
                }
            }


        if (ticketId != null) {
            ScTicket scTicketUpdate = new ScTicket();
            scTicketUpdate.setId(ticketId);
            if ("1".equalsIgnoreCase(scStationLetterMessage.getType())) { //买家发给卖家
                scTicketUpdate.setNoReplyQty(readFlag + 1); //未回复数量
                scTicketUpdate.setTicketReplyStatus(0); //状态 未回复
                scTicketUpdate.settingDefaultSystemUpdate();
                scTicketService.updateScTickeByTicketId(scTicketUpdate);
            } else if (!"6".equals(scStationLetterMessage.getType())){ //卖家发给买家(非机器人)
                scTicketUpdate.setNoReplyQty(0); //未回复数量
                scTicketUpdate.setTicketReplyStatus(1); //状态 已回复
                scTicketUpdate.setBusinessType(null); //业务状态
                scTicketUpdate.settingDefaultSystemUpdate();
                scTicketService.updateScTicketBusinessType(scTicketUpdate);
            }


            // 异步设置工单操作类型
            CompletableFuture.runAsync(() -> {
                try {
                    //设置工单操作类型
                    this.saveTkMsgServiceType(scTicketUpdate, scStationLetterMessage);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }, taskExecutor);
        }


        //图片或视频且内容不等于空且发送人为买家才进行附件转换处理
        if ((TikTokConvContentTypeV2Enum.IMAGE.getContentType().equalsIgnoreCase(type) || TikTokConvContentTypeV2Enum.VIDEO.getContentType().equalsIgnoreCase(type))
                && !StringUtils.isEmpty(scStationLetterMessage.getContent())
                && TikTokMessageV2Enum.BUYER.getCode().toString().equals(scStationLetterMessage.getType())
        ) {

            Long ticketFlag = ticketId;
            // 异步对附件操作
            CompletableFuture.runAsync(() -> {
                try {
                    saveMsgFileTotikcet(account.getOrgId().longValue(), type, scStationLetterMessage, ticketFlag);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }, taskExecutor);
        }
    }

    private void saveMsgFileTotikcet(Long orgId , String type, ScStationLetterMessage scStationLetterMessage, Long ticketId) {
        if (ticketId == null) {
            log.info("无工单编号：{}", JSONObject.toJSONString(scStationLetterMessage));
            return;
        }

        String url = scStationLetterMessage.getContent();
        String fileName = ticketId + RandomUtil.randomNumbers(2) + ((TikTokConvContentTypeV2Enum.VIDEO.getContentType().equalsIgnoreCase(type)) ? ".mp4" : ".jpeg");

        int index = url.indexOf("?");
        url = (index != -1) ? url.substring(0, index) : url;


        String uploadResult = null;
        try {
            String reqUrl = queryTransitionUrl + "/api/v1/erp_oss";
            HttpRequest request = HttpUtil.createPost(reqUrl);
            JSONObject reqparm = new JSONObject();
            reqparm.put("url", url);
            request.body(JSON.toJSONString(reqparm));
            log.info("tk站内信转换图片URL：{}", url);
            HttpResponse response = request.execute();
            if (!response.isOk()) {
                TimeUnit.SECONDS.sleep(2);
                response = request.execute();
                if (!response.isOk()) {
                    log.info("tk站内信转换图片调用失败：,{},{}", scStationLetterMessage.getMessageId(), JSONObject.toJSONString(response));
                    return;
                }
            }
            JSONObject resJson = JSONObject.parseObject(response.body());
            if (resJson != null) {
                uploadResult = resJson.getString("url");
            }
        } catch (Exception e) {
            log.info("tk站内信转换图片异常：,{},{}", scStationLetterMessage.getMessageId(), e);
        }

           log.info("获取到会话:{},oss地址：{}", scStationLetterMessage.getMessageId(), uploadResult);
            //保存至工单处理信息
            if (!StringUtils.isEmpty(uploadResult)) {
                TicketAttaches ticketAttachesSave = new TicketAttaches();
                ticketAttachesSave.setOrganizationId(orgId);
                ticketAttachesSave.setFileName(fileName);
                ticketAttachesSave.setFileUrl(uploadResult);
                ticketAttachesSave.setDocumentId(ticketId);
                ticketAttachesSave.setDocumentType("sc_ticket");
                ticketAttachesSave.settingDefaultCreate();
                ticketAttachesMapper.insert(ticketAttachesSave);
                //更新明细表oss字段
                scStationLetterMessageService.updateErpContentById(scStationLetterMessage.getMessageId(), uploadResult);
            }
    }


    /**
     * @param scTicket
     * @param scStationLetterMessage 最新一条消息
     * @description: 设置站内信消息类型
     * @author: Moore
     * @date: 2025/5/7 16:36
     * @return: void
     **/
    public void saveTkMsgServiceType(ScTicket scTicket, ScStationLetterMessage scStationLetterMessage) {
        //未回复数0，不进行会话业务标签处理
        if (scTicket == null||scTicket.getNoReplyQty()==null||scTicket.getNoReplyQty().equals(0)) {
            return;
        }

        //唯一会话ID
        String convShortId = scStationLetterMessage.getConvShortId();

        String contentFlag = "";
        //存在维度
        if (scTicket.getNoReplyQty().equals(1)) {
            if (scStationLetterMessage.getContentType().equalsIgnoreCase(TikTokConvContentTypeV2Enum.TEXT.getContentType())) {
                contentFlag = scStationLetterMessage.getContent();
            }
        } else {
            List<ScStationLetterMessage> scStationLetterMessages = scStationLetterMessageService.lambdaQuery().
                    select(ScStationLetterMessage::getContent, ScStationLetterMessage::getContentType)
                    .eq(ScStationLetterMessage::getConvShortId, scStationLetterMessage.getConvShortId())
                    .eq(ScStationLetterMessage::getType, TikTokMessageV2Enum.BUYER.getCode()) //买家发送给卖家
                    .orderByDesc(ScStationLetterMessage::getMessageId).last("limit " + scTicket.getNoReplyQty()).list();
            contentFlag = scStationLetterMessages.stream().filter(item -> !StringUtil.isEmpty(item.getContent())
                    && TikTokConvContentTypeV2Enum.TEXT.getContentType().equalsIgnoreCase(scStationLetterMessage.getContentType())
            ).map(ScStationLetterMessage::getContent).collect(Collectors.joining(","));
        }

        if (StringUtil.isEmpty(contentFlag)) {
            return;
        }

        String valueIgnoreCase = null;

        try {
            String reqUrl = queryAlgorithmClassifyQuestionUrl+"/classify_question";
            HttpRequest request = HttpUtil.createPost(reqUrl);
            request.header("Content-Type", "application/json");
            request.setFollowRedirects(true);
            JSONObject reqparm = new JSONObject();
            reqparm.put("user_id", convShortId);
            reqparm.put("question", contentFlag);
            request.body(JSON.toJSONString(reqparm));
            log.info("TK站内信判断会话业务类型,url：{}：{}", reqUrl, reqparm);
            HttpResponse response = request.execute();
            if (response.getStatus() == 307) {
                String redirectUrl = response.header("Location");
                log.info("307重定向地址: " + redirectUrl);
            }
            if (!response.isOk()) {
                TimeUnit.SECONDS.sleep(2);
                response = request.execute();
                if (!response.isOk()) {
                    log.info("tk站内信获取会话类型失败：,{},{}", reqparm, JSONObject.toJSONString(response));
                    return;
                }
            }
            JSONObject resJson = JSONObject.parseObject(response.body());
            log.info("tk站内信获取会话结果成功：,{},{}", reqparm, resJson);
            if (resJson != null) {
                String category = resJson.getString("category");
                if (StringUtils.isNotEmpty(category)) {
                    valueIgnoreCase = MessageServiceType.getValueIgnoreCaseList(Arrays.asList(category.split(","))).stream().collect(Collectors.joining(","));
                    log.info("转换后结果：{}", valueIgnoreCase);
                }
            }
        } catch (Exception e) {
            log.info("tk站内信调用算法接口异常：,{},{}", scStationLetterMessage.getMessageId(), e);
        }
        if (StringUtil.isEmpty(valueIgnoreCase)) {
            return;
        }
        scTicket.setBusinessType(valueIgnoreCase);
        scTicket.settingDefaultSystemUpdate();
        scTicketService.updateScTicketBusinessType(scTicket);

    }

    /**
     * @param customerId     客户表主键ID
     * @param tikTokMessageListResponseV2Conversations       会话基础信息
     * @param participtsList 会话参与人列表，仅保存客户信息
     * @param account        店铺信息
     * @param sourceType        来源类型
     * @description: 保存回话列表
     * @author: Fountain
     * @date: 2023/10/18 1:00
     * @return: 会话表主键ID
     **/
    public Long saveConverSationListV2(Long customerId, TikTokMessageListResponseV2Conversations tikTokMessageListResponseV2Conversations, List<TikTokMessageListResponseV2Participants> participtsList, Account account, Integer sourceType) {

        //根据会话ID判断会话唯一性
        ScStationLetter scStationLetter = scStationLetterService.selectletterByConvShortId(tikTokMessageListResponseV2Conversations.getId());
        if (null == scStationLetter) {
            ScStationLetter scStationLetterSave = new ScStationLetter();
            scStationLetterSave.setOrganizationId(account.getOrgId().longValue());
            scStationLetterSave.setConvShortId(tikTokMessageListResponseV2Conversations.getId()); //会话ID
            if (!CollectionUtils.isEmpty(participtsList)) { //关联买家信息
                TikTokMessageListResponseV2Participants participant = participtsList.get(0);
                scStationLetterSave.setCustomerName(participant.getNickname());//客户名称
                scStationLetterSave.setOuterId(participant.getUserId());//平台客户ID
                scStationLetterSave.setCustomerId(customerId);//客户表主键信息
            }
            scStationLetterSave.setFirstSendTime(DateUtil.BjToPstByTimestamp(tikTokMessageListResponseV2Conversations.getCreateTime())); //回话创建时间
            scStationLetterSave.setAccountId(account.getId());
            scStationLetterSave.settingDefaultSystemCreate();
            scStationLetterSave.setSourceType(sourceType);
            scStationLetterSave.setChannelType(AccountSaleChannelEnum.TIKTOK.getValue()); //设置渠道为tiktok
            scStationLetterService.insertScStationLetter(scStationLetterSave);
            return scStationLetterSave.getStationLetterId();
        } else {
            return scStationLetter.getStationLetterId();
        }
    }

    public void saveConverSationListV2New(String convShortId, List<TikTokMessageListResponseV2Participants> participtsList) {
        try {
            //根据会话ID判断会话唯一性
            ScStationLetter scStationLetter = scStationLetterService.selectletterByConvShortId(convShortId);
            if (scStationLetter != null) {
                if (TikTokConstant.CONV_MESSAGE_SOURCE_WEBHOOK.equals(scStationLetter.getSourceType())) {
                    if (!CollectionUtils.isEmpty(participtsList)) {
                        TikTokMessageListResponseV2Participants participant = participtsList.get(0);
                        scStationLetter.setOuterId(participant.getUserId());
                        scStationLetter.setCustomerName(participant.getNickname());
                        scStationLetter.setImUserId(participant.getImUserId());
                        scStationLetter.settingDefaultSystemUpdate();
                        scStationLetterService.updateById(scStationLetter);
                        if (StringUtils.isEmpty(participant.getNickname())) {
                            return;
                        }
                        List<ScTicket> scTicketList = scTicketService.selectScTicketBySourceList(scStationLetter.getStationLetterId(), ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION); //获取会话下所有工单
                        if (!CollectionUtils.isEmpty(scTicketList)) {
                            List<Long> scTicketIds = scTicketList.stream().filter(item -> StringUtils.isNotEmpty(item.getTicketName()) && item.getTicketName().contains("BUYER_ID")).map(ScTicket::getId).distinct().collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(scTicketIds)) {
                                for (Long ticketId : scTicketIds) {
                                    //更新工单描述
                                    ScTicket scTicket = new ScTicket();
                                    scTicket.setId(ticketId);
                                    scTicket.setTicketName("参与会话人: BUYER-" + participant.getNickname());
                                    scTicket.settingDefaultSystemUpdate();
                                    scTicketService.updateScTickeByTicketId(scTicket);
                                }
                            }
                        }
                    }

                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.info("保存TK会话列表异常：{}", e.getMessage());
        }
    }
    /**
     * @param
     * @description:会话列表创建工单
     * @author: Moore
     * @date: 2023/10/22 15:54
     * @return: void
     **/
    public Long convListCreateTicketV2(Long stationLetterId, List<TikTokMessageListResponseV2Participants> participants, Account account, Long customerId) {
        //创建工单（根据会话查询工单）
        List<ScTicket> scTicket = scTicketService.selectScTicketBySourceList(stationLetterId, ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION);
        List<ScTicket> filterTicket = scTicket.stream().filter(item -> ScTicketConstant.TICKET_STATUS_NEW.equals(item.getTicketStatus()) || ScTicketConstant.TICKET_STATUS_PROCESSING.equals(item.getTicketStatus())).collect(Collectors.toList()); //过滤新建或 进行中的工单
        Long ticketId = -1L;
        if (CollectionUtils.isEmpty(scTicket) || (!CollectionUtils.isEmpty(scTicket) && CollectionUtils.isEmpty(filterTicket))) { //工单不存在（或没有新建 及正在进行中的工单）
            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append("参与会话人: ");
            participants.stream().forEach(item -> {
                //获取角色标识
                if (StringUtils.isNotEmpty(item.getRole())) {
                    stringBuffer.append(item.getRole() + "-" + item.getNickname() + " "); //拼接 具体会话人
                } else {
                    stringBuffer.append(item.getRole() + "-" + item.getNickname());
                }
            });
            ScTicket scTicketCrete = new ScTicket();
            scTicketCrete.setTicketType(ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG);  //工单类型 站内信
            scTicketCrete.setTicketName(stringBuffer.toString()); //工单名称
            scTicketCrete.setRemark(account.getTitle()); //描述
            scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(stationLetterId.toString(), "l", 5, "0")+RandomUtil.randomNumbers(4));//TK回话会存在于多个工单，增加随机数
            scTicketCrete.setTicketSource(ScTicketConstant.TIKTOK_MESSAGE);// 工单来源(订单API)
            scTicketCrete.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH); //优先级（高）
            scTicketCrete.setSourceId(stationLetterId);//来源ID
            scTicketCrete.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION); //来源单据 站内信
            scTicketCrete.setShopId(Long.valueOf(account.getId()));  //店铺
            scTicketCrete.setOrganizationId(Long.valueOf(account.getOrgId()));//组织ID
//            scTicketCrete.setCustomerId(customerId); //客户ID
            scTicketCrete.settingDefaultSystemCreate();
            scTicketService.insertScTicket(scTicketCrete);
            ticketId = scTicketCrete.getId();//工单主键
        }
        return ticketId;
    }

    /**
     * @param msgs     会话列表
     * @param account  用户信息
     * @param sourceId 来源ID（回话主键）
     * @description: 会话内容创建工单方法
     * @author: Moore
     * @date: 2023/10/22 14:10
     * @return: 工单ID
     **/
    public Long convContentCreateTicketV2(List<TikTokMessageListContentMessagesV2> msgs, Account account, Long sourceId, Long customerId) {

        //创建工单ID
        Map<String, List<TikTokMessageListContentMessagesV2>> groupRole = msgs.stream().collect(Collectors.groupingBy(i -> i.getSender().getRole())); //根据角色分类进项分组
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("参与会话人: ");
        for (String roleFlag : groupRole.keySet()) {
            TikTokMessageListContentMessagesV2 item = groupRole.get(roleFlag).get(0);
            if (StringUtils.isNotEmpty(roleFlag)) {
                stringBuffer.append(roleFlag + "-" + item.getSender().getNickname() + " "); //拼接 具体会话人
            } else {
                stringBuffer.append(item.getSender().getNickname() + "-" + item.getSender().getNickname() + " ");
            }
        }
        ScTicket scTicketCrete = new ScTicket();
        scTicketCrete.setTicketType(ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG);  //工单类型 站内信
        scTicketCrete.setTicketName(stringBuffer.toString()); //工单名称
        scTicketCrete.setRemark(account.getTitle()); //描述(店铺名)
        scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) +
                StringUtil.pad(sourceId.toString(), "l", 5, "0")+RandomUtil.randomNumbers(4));  //由于TK会话ID会对应过个工单，增加随机数防止重复
        scTicketCrete.setTicketSource(ScTicketConstant.TIKTOK_MESSAGE);// 工单来源(订单API)
        scTicketCrete.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH); //优先级（高）
        scTicketCrete.setSourceId(sourceId);//来源ID （会话主键,随机工单里取一个）
        scTicketCrete.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION); //来源单据 站内信
        scTicketCrete.setShopId(Long.valueOf(account.getId()));  //店铺
        scTicketCrete.setOrganizationId(Long.valueOf(account.getOrgId()));//组织ID
//        scTicketCrete.setCustomerId(customerId); //客户ID
        scTicketCrete.settingDefaultSystemCreate(); //系统默认创建
        scTicketService.insertScTicket(scTicketCrete);
        return scTicketCrete.getId();
    }


    /**
     * @param channelId
     * @param
     * @description: 销售订单-TK渠道订单-客户消息 创建会话
     * @author: Moore
     * @date: 2023/10/26 17:03
     * @return: void
     **/
    @Override
    public ScTicket customerInfoCreateTiktokConv(Integer contextId,Integer channelId,String orderNo, String customerOuterId) {

        if (StringUtils.isEmpty(customerOuterId)&&StringUtils.isEmpty(orderNo)){
            throw new CustomException("未获取到对应订单信息!");
        }

        if (StringUtils.isEmpty(customerOuterId)) {
            SaleOrderVoTicket saleOrderVoTicket = saleOrdersMapper.selectScOrderCustomerOuterId(contextId, channelId, orderNo);
            if (saleOrderVoTicket == null || StringUtils.isEmpty(saleOrderVoTicket.getCustomerOuterId())) {
                throw new CustomException("当前订单无用户ID!");
            }
            customerOuterId = saleOrderVoTicket.getCustomerOuterId();
        }

        Account account = new Account();
        account.setId(channelId);
        List<Account> accounts = accountService.selectAccountListRefactor(account);
        if (CollectionUtils.isEmpty(accounts)) {
            throw new CustomException("店铺信息获取失败!");
        }
        account = accounts.get(0);

        //判断用户是否存在会话
        //获取当前店铺用户会话（店铺+用户/仅会有一个会话）
        List<ScStationLetter> scStationLetters = scStationLetterService.selectBaseScStationLetterByAccountIdAndOuteId(Long.valueOf(channelId),customerOuterId);

        ScStationLetter scStationLetter = new ScStationLetter();
        if (CollectionUtils.isEmpty(scStationLetters)){ //系统暂无该用户会话
            //使用平台用户ID创建会话
            HashMap<String, String> reqMap = new HashMap<>();
            reqMap.put("buyer_user_id",customerOuterId);
            SendMessageResponse tikTokShopReturn = null;
            try {
                tikTokShopReturn = tikTokUtil.postTikTokShopV2(toJSONString(reqMap), TikTokApiEnums.CREATE_TIKTOK_CONVV2.getUrl(), TikTokApiEnums.CREATE_TIKTOK_CONVV2.getPath(), SendMessageResponse.class, Long.valueOf(channelId));
            } catch (Exception e) {
                log.error("创建会话失败：{}", e);
            }
            if (tikTokShopReturn == null|| tikTokShopReturn.getData()==null||StringUtils.isEmpty(tikTokShopReturn.getData().getConversationId())) {
                throw new CustomException("会话创建失败!");
            }
            //保存会话列表
            scStationLetter.setAccountId(channelId);//账号ID
            scStationLetter.setConvShortId(tikTokShopReturn.getData().getConversationId()); //设置会话ID
//            scStationLetter.setCustomerName(scCustomer.getCustomerName());//后续通过接口更新
            scStationLetter.setOuterId(customerOuterId); //平台客户ID
            scStationLetter.setSourceType(3);//来源类型手工单
            scStationLetter.setChannelType(AccountSaleChannelEnum.TIKTOK.getValue()); //设置渠道为tiktok
            scStationLetter.settingDefaultCreate();//创建默认值
            scStationLetterService.insertScStationLetter(scStationLetter);


            //工单创建
            ScTicket scTicketCrete = new ScTicket();
            scTicketCrete.setTicketType(ScTicketConstant.TICKET_TYPE_AMZ_SITEMSG);  //工单类型 站内信

            scTicketCrete.setTicketName("参与会话人:" + "BUYER_ID:" + customerOuterId); //工单名称
            scTicketCrete.setRemark(account.getTitle()); //描述
            scTicketCrete.setTicketNumber(DateUtil.convertDateToString(new Date(), DatePattern.PURE_DATE_PATTERN) + StringUtil.pad(scStationLetter.getStationLetterId().toString(), "l", 5, "0")+RandomUtil.randomNumbers(4));
            scTicketCrete.setTicketSource(ScTicketConstant.TIKTOK_MESSAGE);// 工单来源(订单API)
            scTicketCrete.setPriority(ScTicketConstant.TICKET_PRIORITY_HIGH); //优先级（高）
            scTicketCrete.setSourceId(scStationLetter.getStationLetterId());//来源ID
            scTicketCrete.setSourceDocument(ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION); //来源单据 站内信
            scTicketCrete.setShopId(Long.valueOf(account.getId()));  //店铺
            scTicketCrete.setOrganizationId(Long.valueOf(account.getOrgId()));//组织ID
            scTicketCrete.setTicketStatus(ScTicketConstant.TICKET_STATUS_PROCESSING); //处理中
            scTicketCrete.setTicketReplyStatus(0); //设置会话回复状态 为已回复
            scTicketService.insertScTicket(scTicketCrete);
            //记录工单日志
            ScTicketLog ticketLog = new ScTicketLog();
            ticketLog.setTicketId(scTicketCrete.getId());
            ticketLog.setOperateType("SUBMIT");
            ticketLog.setOperateTime(new Date());
            ticketLog.setOperateContent("由销售订单信息，生成站内信工单");
            scTicketLogService.insertScTicketLog(ticketLog);
            return scTicketCrete;
        }else{

            //1.一个会话ID可能存在多个工单（新版只会有一个）
            List<ScTicket> scTicketList = scTicketService.selectScTicketBySourceList(scStationLetters.get(0).getStationLetterId(), ScTicketConstant.TICKET_SOURCE_DOCUMENT_STATION); //获取会话下所有工单

            //从新建或者进行中
            ScTicket scTicketNew = scTicketList.stream().filter(item -> ScTicketConstant.TICKET_STATUS_NEW.equals(item.getTicketStatus())).findFirst().orElse(null);
            ScTicket scTicketProcessing = scTicketList.stream().filter(item -> ScTicketConstant.TICKET_STATUS_PROCESSING.equals(item.getTicketStatus())).findFirst().orElse(null);
            ScTicket scTicketClose = scTicketList.stream().filter(item -> ScTicketConstant.TICKET_STATUS_CLOSED.equals(item.getTicketStatus())|| ScTicketConstant.TICKET_STATUS_FINISH.equals(item.getTicketStatus())).findFirst().orElse(null);
            if (scTicketNew != null) {  //优先取新建
                return scTicketNew;
            }else if (scTicketProcessing !=null) { //其次取 进行中
                return scTicketProcessing;
            } else if (scTicketClose != null) { //其将关闭工单打开使用
                Long ticketId = scTicketClose.getId();
                ScTicket scTicketUpdate = new ScTicket();
                scTicketUpdate.setId(ticketId);
                scTicketUpdate.setTicketStatus(ScTicketConstant.TICKET_STATUS_PROCESSING); //由销售订单创建会话，直接为处理中
                scTicketUpdate.settingDefaultSystemUpdate();
                scTicketService.updateScTicketStatus(scTicketUpdate);
                return scTicketClose;
            }
            throw new CustomException("获取会话工单失败!");
        }


    }

    @Autowired
   private ScEmailSendMapper scEmailSendMapper;
    /**
     * 手动发送 tk消息
     *
     * @param scEmailSends
     */
    @Override
    public void customerInfoCreateTiktokConvManual(List<ScEmailSend> scEmailSends) {
        List<Long> successFlag = new ArrayList<>();
        List<Long> errorFlag = new ArrayList<>();

      //scEmailSends = scEmailSendMapper.selectScEmailSendList(null);
        scEmailSends = scEmailSendMapper.selectScEmailSendListTemp();
        List<List<ScEmailSend>> listsSplit = ListUtils.splitList(scEmailSends, 5);
        List<CompletableFuture<Object>> collect = listsSplit.stream().map(bath -> CompletableFuture.supplyAsync(() -> {
            comonSendTkMessage(bath, successFlag, errorFlag);
            return null;
        }, threadPoolTaskExecutor)).collect(Collectors.toList());

        CompletableFuture.allOf(collect.toArray(new CompletableFuture<?>[0])).join();
        log.info("成功条数：{},失败条数：{}", JSONObject.toJSONString(successFlag), JSONObject.toJSONString(errorFlag));

    }






    public void comonSendTkMessage(List<ScEmailSend> scEmailSends,List<Long> successFlag,List<Long> errorFlag) {
        for (ScEmailSend scEmailSend : scEmailSends) {
            //使用平台用户ID创建会话
            HashMap<String, String> reqMap = new HashMap<>();
            reqMap.put("buyer_user_id",scEmailSend.getFromEmail());
            SendMessageResponse tikTokShopReturn = null;
            try {
                tikTokShopReturn = tikTokUtil.postTikTokShopV2(toJSONString(reqMap), TikTokApiEnums.CREATE_TIKTOK_CONVV2.getUrl(), TikTokApiEnums.CREATE_TIKTOK_CONVV2.getPath(), SendMessageResponse.class, scEmailSend.getOrganizationId().longValue());
                if (tikTokShopReturn == null|| tikTokShopReturn.getData()==null||StringUtils.isEmpty(tikTokShopReturn.getData().getConversationId())) {
                    log.info("创建会话失败：{}",scEmailSend.getSendId());
                    errorFlag.add(scEmailSend.getSendId());
                    continue;
                }

                MessageSendRequest sendRequest = new MessageSendRequest();
                sendRequest.setConvShortId(tikTokShopReturn.getData().getConversationId());//设置会话ID
                sendRequest.setMsgType("TEXT");
                sendRequest.setContent(scEmailSend.getContent());

                //封装消息内容
                HashMap<String, List<JSONObject>> stringListHashMap = this.senMessageEntityTransition(sendRequest);

                for (String key : stringListHashMap.keySet()) {
                    List<JSONObject> value = stringListHashMap.get(key);
                    //调用发送消息
                    String convShortId = sendRequest.getConvShortId();
                    HashMap<String, String> reqMap2 = new HashMap<>();
                    reqMap2.put("type", key); //会话类型
                    for (JSONObject senMsg : value) {
                        Integer replyStatus; //回复标识（工单使用）  -1:回复失败 0:未回复 1:已回复（成功）
                        reqMap2.put("content", senMsg.toJSONString()); //会话内容
                        SendMessageResponse sendMessageResponse = null;//模拟调用
                        try {
                            log.error("发送消息请求体：{}", toJSONString(reqMap2));
                            sendMessageResponse = tikTokUtil.postTikTokShopReturnV2(toJSONString(reqMap2), TikTokApiEnums.SEND_MESSAGE_TIKTOK_NEW.getUrl(), TikTokApiEnums.SEND_MESSAGE_TIKTOK_NEW.getPath(), SendMessageResponse.class, scEmailSend.getOrganizationId().longValue(), convShortId);
                            if (sendMessageResponse == null) {
                                errorFlag.add(scEmailSend.getSendId());
                                log.info("发送失败：{}",scEmailSend.getSendId());
                            }else{
                                log.info("发送成功：{}",scEmailSend.getSendId());
                                successFlag.add(scEmailSend.getSendId());
                            }

                        } catch (Exception e) {
                            errorFlag.add(scEmailSend.getSendId());
                            log.error("TIKTOK发送会话消息失败：{}", e);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("创建会话失败：{}", e);
            }

        }


    }

}
