package com.bizark.op.service.service.mar.material;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bizark.common.contract.AuthUserDetails;
import com.bizark.framework.security.AuthContextHolder;
import com.bizark.op.api.entity.op.mar.material.MarMaterialMakeInfo;
import com.bizark.op.api.entity.op.mar.material.MarMaterialOpLog;
import com.bizark.op.api.entity.op.tabcut.TabcutVideoTag;
import com.bizark.op.api.service.mar.material.MarMaterialOpLogService;
import com.bizark.op.service.mapper.mar.material.MarMaterialOpLogMapper;
import com.bizark.op.service.mapper.tabcut.TabcutVideoTagMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【mar_material_op_log(tk素材操作日志表)】的数据库操作Service实现
* @createDate 2025-02-10 17:06:17
*/
@Service
public class MarMaterialOpLogServiceImpl extends ServiceImpl<MarMaterialOpLogMapper, MarMaterialOpLog>
    implements MarMaterialOpLogService{


    @Autowired
    private TabcutVideoTagMapper tabcutVideoTagMapper;

    /** 记录tag日志
     *
     * @param
     * @param dbTagsId 数据库历史tag
     * @param  MaterialNum 素材编号
     */
    @Override
   @Async("threadPoolTaskExecutor")
    public void saveMaterialTagLog(String savetagIds, String dbTagsId, String MaterialNum,Integer orgId) {
        String logTemplate = "标签由: {dbTag} 变更为 {saveTag}";
        MarMaterialOpLog marMaterialOpLog = new MarMaterialOpLog();
        marMaterialOpLog.setOrgId(orgId);
        marMaterialOpLog.setMaterialNum(MaterialNum);

        //传入及库中均无值
        if (StringUtils.isEmpty(savetagIds) && StringUtils.isEmpty(dbTagsId)) {
            return;
        }

        //新增为空
        if (StringUtils.isEmpty(savetagIds)){
            List<TabcutVideoTag> tabcutVideoTags = tabcutVideoTagMapper.selectBatchIds(Arrays.asList(dbTagsId.split(",")));
            if (!CollectionUtils.isEmpty(tabcutVideoTags)) {
                String tages = tabcutVideoTags.stream().map(TabcutVideoTag::getTagName).collect(Collectors.joining(","));
                marMaterialOpLog.setOperateContent(logTemplate.replace("{dbTag}", tages).replace("{saveTag}","无标签" ));
                marMaterialOpLog.settingDefaultCreate();
                this.save(marMaterialOpLog);
            }
        }else if (StringUtils.isEmpty(dbTagsId)) {
            List<TabcutVideoTag> tabcutVideoTags = tabcutVideoTagMapper.selectBatchIds(Arrays.asList(savetagIds.split(",")));
            if (!CollectionUtils.isEmpty(tabcutVideoTags)) {
                String tages = tabcutVideoTags.stream().map(TabcutVideoTag::getTagName).collect(Collectors.joining(","));
                marMaterialOpLog.setOperateContent(logTemplate.replace("{dbTag}", "无标签").replace("{saveTag}", tages));
                marMaterialOpLog.settingDefaultCreate();
                this.save(marMaterialOpLog);
            }
        } else if (!savetagIds.equals(dbTagsId)) {
            String saveTag = tabcutVideoTagMapper.selectBatchIds(Arrays.asList(savetagIds.split(","))).stream().map(TabcutVideoTag::getTagName).collect(Collectors.joining(","));
            String dgTag = tabcutVideoTagMapper.selectBatchIds(Arrays.asList(dbTagsId.split(","))).stream().map(TabcutVideoTag::getTagName).collect(Collectors.joining(","));
            marMaterialOpLog.setOperateContent(logTemplate.replace("{dbTag}", dgTag).replace("{saveTag}", saveTag));
            marMaterialOpLog.settingDefaultCreate();
            this.save(marMaterialOpLog);
        }
    }



    @Override
//    @Async("threadPoolTaskExecutor")
    public void saveOpLog(String materialNum, String format) {
       try {
           AuthUserDetails userDetails = AuthContextHolder.getAuthUserDetails();

           MarMaterialOpLog opLog = new MarMaterialOpLog();
           opLog.setMaterialNum(materialNum);
           opLog.setOrgId(null == userDetails ? 0 : userDetails.getOrgId().intValue());
           opLog.setOperateContent(String.format(format, null == userDetails ? "System" : userDetails.getName()));
           opLog.settingDefaultValue();
           save(opLog);
       } catch (Exception e) {
           e.printStackTrace();
           log.error("保存日志异常");
       }
    }

    @Override
    public void record(MarMaterialMakeInfo makeInfo, String content) {
        MarMaterialOpLog opLog = new MarMaterialOpLog();
        opLog.setOrgId(makeInfo.getOrgId());
        opLog.setMaterialNum(makeInfo.getGenId());
        opLog.setOperateContent(content);
        opLog.setCreatedBy(makeInfo.getCreatedBy());
        opLog.setCreatedName(makeInfo.getCreatedName());
        opLog.setUpdatedBy(makeInfo.getUpdatedBy());
        opLog.setUpdatedName(makeInfo.getUpdatedName());
        opLog.setCreatedAt(new Date());
        opLog.setUpdatedAt(opLog.getCreatedAt());
        this.save(opLog);
    }

    @Override
    public void record(List<MarMaterialMakeInfo> makeInfos, String content) {
        List<MarMaterialOpLog> logs = makeInfos.stream().map(makeInfo -> {
            MarMaterialOpLog opLog = new MarMaterialOpLog();
            opLog.setOrgId(makeInfo.getOrgId());
            opLog.setMaterialNum(makeInfo.getGenId());
            opLog.setCreatedBy(makeInfo.getCreatedBy());
            opLog.setCreatedName(makeInfo.getCreatedName());
            opLog.setUpdatedBy(makeInfo.getUpdatedBy());
            opLog.setUpdatedName(makeInfo.getUpdatedName());
            opLog.setCreatedAt(new Date());
            opLog.setUpdatedAt(opLog.getCreatedAt());
            opLog.setOperateContent(content);
            return opLog;
        }).collect(Collectors.toList());
        this.saveBatch(logs);

    }


}




