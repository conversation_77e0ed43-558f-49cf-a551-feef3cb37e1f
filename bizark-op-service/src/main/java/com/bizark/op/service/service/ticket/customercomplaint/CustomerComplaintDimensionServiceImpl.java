package com.bizark.op.service.service.ticket.customercomplaint;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.io.IoUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bizark.boss.api.service.TaskCenterService;
import com.bizark.boss.api.vo.dashboard.TaskCenterRequest;
import com.bizark.op.api.cons.StatConstant;
import com.bizark.op.api.enm.finance.FinanceErrorEnum;
import com.bizark.op.api.enm.ticket.CustomerComplainDateTypeEnum;
import com.bizark.op.api.enm.ticket.CustomerComplaintCategoryTypeEnum;
import com.bizark.op.api.entity.op.document.ConfTicketProblem;
import com.bizark.op.api.entity.op.mar.MarListingQuery;
import com.bizark.op.api.entity.op.mar.MarProductPopularizeLogParentAsinVO;
import com.bizark.op.api.entity.op.returns.StatReturnQuery;
import com.bizark.op.api.entity.op.returns.VO.ReturnAnalyzeVO;
import com.bizark.op.api.entity.op.ticket.customercomplaint.VO.CustomerComplaintDimensionTotalTwoVO;
import com.bizark.op.api.entity.op.ticket.customercomplaint.VO.CustomerComplaintDimensionTotalVO;
import com.bizark.op.api.entity.op.ticket.customercomplaint.query.CustomerComplaintMainQuery;
import com.bizark.op.api.service.ticket.IConfTicketProblemService;
import com.bizark.op.api.service.ticket.IScTicketService;
import com.bizark.op.api.service.ticket.customercomplaint.CustomerComplaintDimensionTotalService;
import com.bizark.op.common.util.*;
import com.bizark.op.service.mapper.ticket.customercomplaint.CustomerComplaintSummaryMapper;
import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.op.service.mapper.ticket.customercomplaint.CustomerComplaintSummaryOldMethodMapper;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * 客诉维度服务
 * @Author: Ailill
 * @Date: 2024/6/5 11:36
 */
@Slf4j
@Service
public  class CustomerComplaintDimensionServiceImpl implements CustomerComplaintDimensionTotalService {

    @Autowired
    private CustomerComplaintSummaryMapper customerComplaintSummaryMapper;

    @Autowired
    private CustomerComplaintSummaryOldMethodMapper oldMethodMapper;

    @Autowired
    private TaskCenterService taskCenterService;


    @Value("${task.center.file.path}")
    private String filePath;

    //@Value("${home_query_bi_url}")
    private  String QUERY_SALE_NUM_MASTER_URL = "http://************:5560";

    @Autowired
    private IConfTicketProblemService confTicketProblemService;

    /**
     * Description: 查询客诉维度汇总list
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/6/5
     */
    @Override
    public List<Map<String,Object>> selectCustomerComplaintOneList(CustomerComplaintMainQuery query) {
        if (query.getDateFrom() == null || query.getDateTo() == null) {
            throw new RuntimeException("查询时间不能为空");
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        query.setDateFromString(simpleDateFormat.format(query.getDateFrom()));
        query.setDateToString(simpleDateFormat.format(query.getDateTo()));
        List<Map<String, Object>> customerComplaintDimensionTotalVOS = null;
        if(CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
            query.setChannel(null);
            customerComplaintDimensionTotalVOS = oldMethodMapper.selectCustomerComplaintOneList(query);
        } else {
            customerComplaintDimensionTotalVOS = customerComplaintSummaryMapper.selectCustomerComplaintOneList(query);
        }
        if (CollectionUtil.isNotEmpty(customerComplaintDimensionTotalVOS)) {
            List<String> asinList = null;
            List<String> skuList = null;
            List<Long> shopList = null;
            List<String> channelList = null;
            List<Long> categoryIdList = null;
            List<String> modelList = null;
            if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getDataType())) {
                skuList = CollectionUtil.isNotEmpty(query.getDataTypeValue()) ? query.getDataTypeValue() : new ArrayList<>();
            } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getDataType())) {
                modelList = CollectionUtil.isNotEmpty(query.getDataTypeValue()) ? query.getDataTypeValue() : new ArrayList<>();
            } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getDataType())) {
                asinList = CollectionUtil.isNotEmpty(query.getDataTypeValue()) ? query.getDataTypeValue() : new ArrayList<>();
            }
            shopList = CollectionUtil.isNotEmpty(query.getShopIds()) ? query.getShopIds() : new ArrayList<>();
            channelList = CollectionUtil.isNotEmpty(query.getChannel()) ? query.getChannel() : new ArrayList<>();
            categoryIdList = CollectionUtil.isNotEmpty(query.getProductCateGoryIds()) ? query.getProductCateGoryIds() : new ArrayList<>();

            Set<String> asinSet = new HashSet<>();
            Set<String> skuSet = new HashSet<>();
            Set<Long> shopSet = new HashSet<>();
            Set<String> channelSet = new HashSet<>();
            Set<Long> categoryIdSet = new HashSet<>();
            Set<String> modelSet = new HashSet<>();
            //todo 封装接口参数
            for (Map<String, Object> c : customerComplaintDimensionTotalVOS) {
                c.put("createdAt", simpleDateFormat.format(query.getDateFrom()) + " - " + simpleDateFormat.format(query.getDateTo()));
                query.setAmzChannelList(null);
                query.setNoAmzChannelList(null);
                c.put("customerComplaintMainQuery", query);//设置本次的查询方便detail查询
                if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                    skuSet.add((String) c.get("sku"));
                } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                    modelSet.add((String) c.get("model"));
                } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                    categoryIdSet.add((Long) c.get("categoryId"));
                } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                    asinSet.add((String) c.get("asin"));
                } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                    shopSet.add(((Long) c.get("shopId")));
                } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                    channelSet.add((String) c.get("channel"));
                }
            }
            if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                skuList = new ArrayList<>(skuSet);
                asinList = null;
                modelList = null;
            } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                modelList = new ArrayList<>(modelSet);
                skuList = null;
                asinList = null;
            } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                categoryIdList = new ArrayList<>(categoryIdSet);
                skuList = null;
                asinList = null;
                modelList = null;
            } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                asinList = new ArrayList<>(asinSet);
                skuList = null;
                modelList = null;
            } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                shopList = new ArrayList<>(shopSet);
                skuList = null;
                asinList = null;
                modelList = null;
            } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                channelList = new ArrayList<>(channelSet);
                skuList = null;
                asinList = null;
                modelList = null;
            }
            //todo 这里要分情况。在sku维度的情况下如果sku为空不调用接口
            Integer diffCase = 0;
            List<String> filterSkuList = null;
            List<String> filterParentSkuList = null;
            if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                filterSkuList = skuList.stream().filter(i -> !"无SKU".equals(i)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(filterSkuList)) {
                    filterParentSkuList = customerComplaintDimensionTotalVOS.stream().filter(i -> "无SKU".equals(i.get("sku"))).map(i -> (String) i.get("parentSku")).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(filterParentSkuList)) {
                        diffCase = 1;
                    } else {
                        diffCase = 2;
                    }
                } else {
                    filterParentSkuList = customerComplaintDimensionTotalVOS.stream().filter(i -> "无SKU".equals(i.get("sku"))).map(i -> (String) i.get("parentSku")).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(filterParentSkuList)) {
                        diffCase = 3;
                    }
                }
            }
            if(diffCase == 0) {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("orgId", query.getOrganizationId());
//            paramMap.put("channelList", channelList);
            paramMap.put("shopIdList", shopList);
            paramMap.put("asinList", asinList);
            paramMap.put("skuList", skuList);
            paramMap.put("categoryIdList", categoryIdList);
            paramMap.put("modelList", modelList);
            //todo 后面用这个
            paramMap.put("dateStart", simpleDateFormat.format(query.getDateFrom()));
            paramMap.put("dateEnd", simpleDateFormat.format(query.getDateTo()));
            paramMap.put("apiType", "ITEM");//SUM：汇总业务 ，ITEM:为明细行业务
            paramMap.put("Type", query.getCategoryType());
            paramMap.put("dateType", query.getDateType());
            paramMap.put("newProductFlag", query.getNewProductFlag());
            JSONObject jsonObject = this.sendCustomerComplainMessage(paramMap);
            JSONArray jsonArray = jsonObject.getJSONArray("data");
            Map<String, BigDecimal> map = new HashMap<>();
            if (jsonArray != null) {
                if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                    map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject) i).getString("erpSku"), j -> ((JSONObject) j).getBigDecimal("saleNumSum")));
                } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                    map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject) i).getString("productMidModel"), j -> ((JSONObject) j).getBigDecimal("saleNumSum")));
                } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                    map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject) i).getString("categorySecondId"), j -> ((JSONObject) j).getBigDecimal("saleNumSum")));
                } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                    map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject) i).getString("asin"), j -> ((JSONObject) j).getBigDecimal("saleNumSum")));
                } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                    map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject) i).getString("channelId"), j -> ((JSONObject) j).getBigDecimal("saleNumSum")));
                } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                    map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject) i).getString("channel"), j -> ((JSONObject) j).getBigDecimal("saleNumSum")));
                }
            }
            for (Map<String, Object> cus : customerComplaintDimensionTotalVOS) {
                BigDecimal saleNumSum = null;
                if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                    saleNumSum = map.get(cus.get("sku"));
                } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                    saleNumSum = map.get(cus.get("model"));
                } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                    saleNumSum = map.get(cus.get("categoryId").toString());
                } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                    saleNumSum = map.get(cus.get("asin"));
                } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                    saleNumSum = map.get(cus.get("shopId").toString());
                } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                    saleNumSum = map.get(cus.get("channel"));
                }
                if (saleNumSum == null || saleNumSum.intValue() == 0) {
                    for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                        cus.put(p.getProblemId() + "Rate", new BigDecimal(0));
                    }
                } else {
                    for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                        BigDecimal returnRate = ((BigDecimal) cus.get(p.getProblemId().toString())).divide(saleNumSum, 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                        cus.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                    }
                }

            }

            }
            //sku不为空，parentsku不为空
            if(diffCase == 1) {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("orgId", query.getOrganizationId());
//            paramMap.put("channelList", channelList);
                paramMap.put("shopIdList", shopList);
                paramMap.put("asinList", asinList);
                paramMap.put("skuList", filterSkuList);
                paramMap.put("categoryIdList", categoryIdList);
                paramMap.put("modelList", modelList);
                //todo 后面用这个
                paramMap.put("dateStart", simpleDateFormat.format(query.getDateFrom()));
                paramMap.put("dateEnd", simpleDateFormat.format(query.getDateTo()));
                paramMap.put("apiType", "ITEM");//SUM：汇总业务 ，ITEM:为明细行业务
                paramMap.put("Type", query.getCategoryType());
                paramMap.put("dateType", query.getDateType());
                paramMap.put("newProductFlag", query.getNewProductFlag());
                JSONObject jsonObject = this.sendCustomerComplainMessage(paramMap);
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                Map<String, BigDecimal> map = new HashMap<>();
                if (jsonArray != null) {
                    if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                        map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject) i).getString("erpSku"), j -> ((JSONObject) j).getBigDecimal("saleNumSum")));
                    }
                }
                //todo 第二次调用parentSku销量接口
                paramMap.put("skuList",null);
                paramMap.put("parentSkuList", filterParentSkuList);
                paramMap.put("Type", CustomerComplaintCategoryTypeEnum.PARENTSKU.getType());
                JSONObject secondJsonObject = this.sendCustomerComplainMessage(paramMap);
                JSONArray secondJsonArray = secondJsonObject.getJSONArray("data");
                Map<String, BigDecimal> secondMap = new HashMap<>();
                if (jsonArray != null) {
                    if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                        secondMap = secondJsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject) i).getString("parentSku"), j -> ((JSONObject) j).getBigDecimal("saleNumSum")));
                    }
                }
                for (Map<String, Object> cus : customerComplaintDimensionTotalVOS) {
                    BigDecimal saleNumSum = null;
                    if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                        if(!"无SKU".equals((String)cus.get("sku"))) {
                            saleNumSum = map.get(cus.get("sku"));
                        } else {
                            saleNumSum = secondMap.get(cus.get("parentSku"));
                        }
                    }
                    if (saleNumSum == null || saleNumSum.intValue() == 0) {
                        for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                            cus.put(p.getProblemId() + "Rate", new BigDecimal(0));
                        }
                    } else {
                        for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                            BigDecimal returnRate = ((BigDecimal) cus.get(p.getProblemId().toString())).divide(saleNumSum, 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                            cus.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                        }
                    }
                }
            }

            //sku不为空，parentsku为空
            if(diffCase == 2) {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("orgId", query.getOrganizationId());
//            paramMap.put("channelList", channelList);
                paramMap.put("shopIdList", shopList);
                paramMap.put("asinList", asinList);
                paramMap.put("skuList", filterSkuList);
                paramMap.put("categoryIdList", categoryIdList);
                paramMap.put("modelList", modelList);
                //todo 后面用这个
                paramMap.put("dateStart", simpleDateFormat.format(query.getDateFrom()));
                paramMap.put("dateEnd", simpleDateFormat.format(query.getDateTo()));
                paramMap.put("apiType", "ITEM");//SUM：汇总业务 ，ITEM:为明细行业务
                paramMap.put("Type", query.getCategoryType());
                paramMap.put("dateType", query.getDateType());
                paramMap.put("newProductFlag", query.getNewProductFlag());
                JSONObject jsonObject = this.sendCustomerComplainMessage(paramMap);
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                Map<String, BigDecimal> map = new HashMap<>();
                if (jsonArray != null) {
                    if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                        map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject) i).getString("erpSku"), j -> ((JSONObject) j).getBigDecimal("saleNumSum")));
                    }
                }
                for (Map<String, Object> cus : customerComplaintDimensionTotalVOS) {
                    BigDecimal saleNumSum = null;
                    if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                        saleNumSum = map.get(cus.get("sku"));
                    }
                    if (saleNumSum == null || saleNumSum.intValue() == 0) {
                        for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                            cus.put(p.getProblemId() + "Rate", new BigDecimal(0));
                        }
                    } else {
                        for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                            BigDecimal returnRate = ((BigDecimal) cus.get(p.getProblemId().toString())).divide(saleNumSum, 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                            cus.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                        }
                    }

                }

            }
            //sku为空，parentsku不为空
            if(diffCase == 3) {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("orgId", query.getOrganizationId());
//            paramMap.put("channelList", channelList);
                paramMap.put("shopIdList", shopList);
                paramMap.put("asinList", asinList);
                paramMap.put("skuList", null);
                paramMap.put("parentSkuList", filterParentSkuList);
                paramMap.put("categoryIdList", categoryIdList);
                paramMap.put("modelList", modelList);
                //todo 后面用这个
                paramMap.put("dateStart", simpleDateFormat.format(query.getDateFrom()));
                paramMap.put("dateEnd", simpleDateFormat.format(query.getDateTo()));
                paramMap.put("apiType", "ITEM");//SUM：汇总业务 ，ITEM:为明细行业务
                paramMap.put("Type", CustomerComplaintCategoryTypeEnum.PARENTSKU.getType());
                paramMap.put("dateType", query.getDateType());
                paramMap.put("newProductFlag", query.getNewProductFlag());
                JSONObject jsonObject = this.sendCustomerComplainMessage(paramMap);
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                Map<String, BigDecimal> map = new HashMap<>();
                if (jsonArray != null) {
                    if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                        map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject) i).getString("parentSku"), j -> ((JSONObject) j).getBigDecimal("saleNumSum")));
                    }
                }
                for (Map<String, Object> cus : customerComplaintDimensionTotalVOS) {
                    BigDecimal saleNumSum = null;
                    if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                        saleNumSum = map.get(cus.get("parentSku"));
                    }
                    if (saleNumSum == null || saleNumSum.intValue() == 0) {
                        for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                            cus.put(p.getProblemId() + "Rate", new BigDecimal(0));
                        }
                    } else {
                        for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                            BigDecimal returnRate = ((BigDecimal) cus.get(p.getProblemId().toString())).divide(saleNumSum, 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                            cus.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                        }
                    }
                }
            }

        }

        return customerComplaintDimensionTotalVOS;
    }

    /**
     * Description: 查询客诉维度汇总 detail list
     *
     * @param query
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/6/5
     */
    @Override
    public List<Map<String,Object>> selectCustomerComplaintOneDetailList(CustomerComplaintMainQuery query) {
        if (query.getDateFrom() == null || query.getDateTo() == null) {
            throw new RuntimeException("查询时间不能为空");
        }
        //todo 特殊出来渠道维度
        if(CollectionUtils.isNotEmpty(query.getChannel())) {
            //VC-DF，VC-USPO，VC-DI,amazon
            List<String> amzSaleChannel = Arrays.asList("amazonvendords", "VC-DI", "amazonvendor", "amazon");
            //交集channel 含（amz）
            List<String> containReviewAmzChannel = query.getChannel().stream().filter(item -> amzSaleChannel.contains(item)).collect(Collectors.toList());
            //差集channel
            List<String> noContaimReviewAmzChannel = query.getChannel().stream().filter(item -> !amzSaleChannel.contains(item)).collect(Collectors.toList());
            query.setAmzChannelList(containReviewAmzChannel);
            query.setNoAmzChannelList(noContaimReviewAmzChannel);
        }

        //当维度是店铺的时候，渠道要去掉
        if(CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
            query.setChannel(null);
            query.setNoAmzChannelList(null);
            query.setAmzChannelList(null);
        }
        //拿出渠道的需要的店铺
        if(CollectionUtil.isNotEmpty(query.getChannel())) {
            String filterChannel = query.getChannel().get(0);
            List<CustomerComplaintMainQuery.Items> itemsList = query.getSelectChannel();
            if (CollectionUtil.isNotEmpty(itemsList)) {
                itemsList = itemsList.stream().filter(i -> filterChannel.equals(i.getChannel())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(itemsList)) {
                    query.setChannelShopIds(itemsList.get(0).getShopIds());
                }
            }
        }

        //todo 这里要分2种情况。要不是有sku,要不是无sku
        Integer diffCase = 0;
        List<String> paramSkuList = CollectionUtil.isNotEmpty(query.getDataTypeValue()) ? query.getDataTypeValue() : new ArrayList<>();
        if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
            if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getDataType())) {
                if (!"无SKU".equals(paramSkuList.get(0))) {
                    diffCase = 1;
                } else {
                    diffCase = 2;
                }
            }

        }

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat monthSimpleDateFormat = new SimpleDateFormat("yyyy-MM");
        query.setIsDetailQuery(1);
        List<Map<String,Object>> customerComplaintDimensionTotalVOS = null;
        if(CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
            query.setChannel(null);
            customerComplaintDimensionTotalVOS = oldMethodMapper.selectCustomerComplaintOneDetailList(query);
        } else {
            //处理无sku
            if (diffCase == 2) {
                query.setWithoutSku("1");
            }
            customerComplaintDimensionTotalVOS = customerComplaintSummaryMapper.selectCustomerComplaintOneDetailList(query);
        }
        if (CollectionUtil.isNotEmpty(customerComplaintDimensionTotalVOS)) {
            List<String> asinList = null;
            List<String> skuList = null;
            List<Long> shopList = null;
            List<String> channelList = null;
            List<Long> categoryIdList = null;
            List<String> modelList = null;
            if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getDataType())) {
                skuList = CollectionUtil.isNotEmpty(query.getDataTypeValue()) ? query.getDataTypeValue() : new ArrayList<>();
            } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getDataType())) {
                modelList = CollectionUtil.isNotEmpty(query.getDataTypeValue()) ? query.getDataTypeValue() : new ArrayList<>();
            } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getDataType())) {
                asinList = CollectionUtil.isNotEmpty(query.getDataTypeValue()) ? query.getDataTypeValue() : new ArrayList<>();
            }
            shopList = CollectionUtil.isNotEmpty(query.getShopIds()) ? query.getShopIds() : new ArrayList<>();
            channelList = CollectionUtil.isNotEmpty(query.getChannel()) ? query.getChannel() : new ArrayList<>();
            categoryIdList = CollectionUtil.isNotEmpty(query.getProductCateGoryIds()) ? query.getProductCateGoryIds() : new ArrayList<>();

            Set<String> asinSet = new HashSet<>();
            Set<String> skuSet = new HashSet<>();
            Set<Long> shopSet = new HashSet<>();
            Set<String> channelSet = new HashSet<>();
            Set<Long> categoryIdSet = new HashSet<>();
            Set<String> modelSet = new HashSet<>();
            //todo 封装接口参数
            for (Map<String, Object> c : customerComplaintDimensionTotalVOS) {
                if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                    skuSet.add((String) c.get("sku"));
                } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                    modelSet.add((String) c.get("model"));
                } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                    categoryIdSet.add((Long) c.get("categoryId"));
                } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                    asinSet.add((String) c.get("asin"));
                } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                    shopSet.add(((Long) c.get("shopId")));
                } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                    channelSet.add((String) c.get("channel"));
                }
            }
            if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                skuList = new ArrayList<>(skuSet);
                asinList = null;
                modelList = null;
            } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                modelList = new ArrayList<>(modelSet);
                skuList = null;
                asinList = null;
            } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                categoryIdList = new ArrayList<>(categoryIdSet);
                skuList = null;
                asinList = null;
                modelList = null;
            } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                asinList = new ArrayList<>(asinSet);
                skuList = null;
                modelList = null;
            } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                shopList = new ArrayList<>(shopSet);
                skuList = null;
                asinList = null;
                modelList = null;
            } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                channelList = new ArrayList<>(channelSet);
                skuList = null;
                asinList = null;
                modelList = null;
            }


            if (diffCase != 2) {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("orgId", query.getOrganizationId());
//            paramMap.put("channelList", channelList);
                paramMap.put("shopIdList", shopList);
                paramMap.put("asinList", asinList);
                paramMap.put("skuList", skuList);
                paramMap.put("categoryIdList", categoryIdList);
                paramMap.put("modelList", modelList);
                paramMap.put("dateStart", simpleDateFormat.format(query.getDateFrom()));
                paramMap.put("dateEnd", simpleDateFormat.format(query.getDateTo()));
                paramMap.put("apiType", "ITEM");//SUM：汇总业务 ，ITEM:为明细行业务
                paramMap.put("Type", query.getCategoryType());
                paramMap.put("dateType", query.getDateType());
                paramMap.put("newProductFlag", query.getNewProductFlag());
                JSONObject jsonObject = this.sendCustomerComplainMessage(paramMap);
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                if (jsonArray != null) {
                    //todo 直接匹配，算退货率，然后返回就ok了
                    for (Object j : jsonArray) {
                        JSONArray dataItems = ((JSONObject) j).getJSONArray("dataItems");
                        for (Object k : dataItems) {
                            String returnDate = ((JSONObject) k).getString("orderDate");
                            Integer saleNum = ((JSONObject) k).getInteger("saleNum");
                            if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                                String erpSku = ((JSONObject) j).getString("erpSku");
                                for (Map<String, Object> r : customerComplaintDimensionTotalVOS) {
                                    if (CustomerComplainDateTypeEnum.DAY.getType().equals(query.getDateType())) { //日
                                        if (r.get("createdAt").equals(returnDate) && r.get("sku").equals(erpSku)) {
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) { //周
                                        //todo 判断时间在这个周里面
                                        String date[] = returnDate.split("~");
                                        try {
                                            if ((DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[0])) >= 0 &&
                                                    DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[1])) <= 0)
                                                    && r.get("sku").equals(erpSku)) {
                                                r.put("quantity", saleNum);
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    } else { //月
                                        if (r.get("createdAt").equals(returnDate) && r.get("sku").equals(erpSku)) {
                                            r.put("quantity", saleNum);
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    }
                                }

                            } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                                String model = ((JSONObject) j).getString("productMidModel");
                                for (Map<String, Object> r : customerComplaintDimensionTotalVOS) {
                                    if (CustomerComplainDateTypeEnum.DAY.getType().equals(query.getDateType())) { //日
                                        if (r.get("createdAt").equals(returnDate) && r.get("model").equals(model)) {
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) { //周
                                        //todo 判断时间在这个周里面
                                        String date[] = returnDate.split("~");
                                        try {
                                            if ((DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[0])) >= 0 &&
                                                    DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[1])) <= 0)
                                                    && r.get("model").equals(model)) {
                                                r.put("quantity", saleNum);
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    } else { //月
                                        if (r.get("createdAt").equals(returnDate) && r.get("model").equals(model)) {
                                            r.put("quantity", saleNum);
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    }
                                }
                            } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                                Long categoryId = ((JSONObject) j).getLong("categorySecondId");
                                for (Map<String, Object> r : customerComplaintDimensionTotalVOS) {
                                    if (CustomerComplainDateTypeEnum.DAY.getType().equals(query.getDateType())) { //日
                                        if (r.get("createdAt").equals(returnDate) && r.get("categoryId").equals(categoryId)) {
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) { //周
                                        //todo 判断时间在这个周里面
                                        String date[] = returnDate.split("~");
                                        try {
                                            if ((DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[0])) >= 0 &&
                                                    DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[1])) <= 0)
                                                    && r.get("categoryId").equals(categoryId)) {
                                                r.put("quantity", saleNum);
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    } else { //月
                                        if (r.get("createdAt").equals(returnDate) && r.get("categoryId").equals(categoryId)) {
                                            r.put("quantity", saleNum);
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    }
                                }
                            } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                                String asin = ((JSONObject) j).getString("asin");
                                for (Map<String, Object> r : customerComplaintDimensionTotalVOS) {
                                    if (CustomerComplainDateTypeEnum.DAY.getType().equals(query.getDateType())) { //日
                                        if (r.get("createdAt").equals(returnDate) && r.get("asin").equals(asin)) {
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) { //周
                                        //todo 判断时间在这个周里面
                                        String date[] = returnDate.split("~");
                                        try {
                                            if ((DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[0])) >= 0 &&
                                                    DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[1])) <= 0)
                                                    && r.get("asin").equals(asin)) {
                                                r.put("quantity", saleNum);
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    } else { //月
                                        if (r.get("createdAt").equals(returnDate) && r.get("asin").equals(asin)) {
                                            r.put("quantity", saleNum);
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    }
                                }

                            } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                                //todo 这里有问题应该类型会不对
                                Long channelId = ((JSONObject) j).getLong("channelId");
                                for (Map<String, Object> r : customerComplaintDimensionTotalVOS) {
                                    if (CustomerComplainDateTypeEnum.DAY.getType().equals(query.getDateType())) { //日
                                        if (r.get("createdAt").equals(returnDate) && r.get("shopId").equals(channelId)) {
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) { //周
                                        //todo 判断时间在这个周里面
                                        String date[] = returnDate.split("~");
                                        try {
                                            if ((DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[0])) >= 0 &&
                                                    DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[1])) <= 0)
                                                    && r.get("shopId").equals(channelId)) {
                                                r.put("quantity", saleNum);
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    } else { //月
                                        if (r.get("createdAt").equals(returnDate) && r.get("shopId").equals(channelId)) {
                                            r.put("quantity", saleNum);
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    }
                                }
                            } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                                String channel = ((JSONObject) j).getString("channel");
                                for (Map<String, Object> r : customerComplaintDimensionTotalVOS) {
                                    if (CustomerComplainDateTypeEnum.DAY.getType().equals(query.getDateType())) { //日
                                        if (r.get("createdAt").equals(returnDate) && r.get("channel").equals(channel)) {
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) { //周
                                        //todo 判断时间在这个周里面
                                        String date[] = returnDate.split("~");
                                        try {
                                            if ((DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[0])) >= 0 &&
                                                    DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[1])) <= 0)
                                                    && r.get("channel").equals(channel)) {
                                                r.put("quantity", saleNum);
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    } else { //月
                                        if (r.get("createdAt").equals(returnDate) && r.get("channel").equals(channel)) {
                                            r.put("quantity", saleNum);
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                        }
                    }
                }
                if (CustomerComplainDateTypeEnum.DAY.getType().equals(query.getDateType())) { //按日直接返回
                    return customerComplaintDimensionTotalVOS;
                } else {
                    //按照周，月分组
                    List<SpiltDateUtil.Range> rangeList = new ArrayList<>();
                    if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) {
                        rangeList = SpiltDateUtil.splitToWeeks(simpleDateFormat.format(query.getDateFrom()), simpleDateFormat.format(query.getDateTo()));
                    } else if (StatConstant.STAT_DATA_TYPE_MONTH.equals(query.getDateType())) {
                        rangeList = SpiltDateUtil.splitToMonths(simpleDateFormat.format(query.getDateFrom()), simpleDateFormat.format(query.getDateTo()));
                    }
                    List<Map<String, Object>> returnCus = new ArrayList<>();
                    for (SpiltDateUtil.Range range : rangeList) {
                        Map<String, List<Map<String, Object>>> cusMap = customerComplaintDimensionTotalVOS.stream().filter(r -> {
                            if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) {
                                try {
                                    return DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), range.getStart()) >= 0 &&
                                            DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), range.getEnd()) <= 0;
                                } catch (ParseException e) {
                                    throw new RuntimeException(e);
                                }
                            } else {
                                return r.get("createdAt").equals(monthSimpleDateFormat.format(range.getStart()));
                            }
                        }).collect(Collectors.groupingBy(i -> {
                            if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                                return (String) i.get("sku");
                            } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                                return (String) i.get("model");
                            } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                                return i.get("categoryId").toString();
                            } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                                return (String) i.get("asin");
                            } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                                return i.get("shopId").toString();
                            } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                                return (String) i.get("channel");
                            }
                            return null;
                        }));

                        for (String key : cusMap.keySet()) {
                            List<Map<String, Object>> cusList = cusMap.get(key);
                            if (CollectionUtil.isNotEmpty(cusList)) {
                                //取第一个就好
                                BigDecimal quantity = new BigDecimal(0);
                                for (Map<String, Object> cc : cusList) {
                                    try {
                                        if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) {
                                            if (DateUtil.compareDate(simpleDateFormat.parse((String) cc.get("createdAt")), range.getStart()) >= 0 && DateUtil.compareDate(simpleDateFormat.parse(((String) cc.get("createdAt"))), range.getEnd()) <= 0) {
                                                if (cc.get("quantity") == null) {
                                                    quantity = new BigDecimal(0);
                                                } else {
                                                    quantity = new BigDecimal((Integer) cc.get("quantity"));
                                                }
                                                break;
                                            }
                                        } else {
                                            if (cc.get("createdAt").equals(monthSimpleDateFormat.format(range.getStart()))) {
                                                if (cc.get("quantity") == null) {
                                                    quantity = new BigDecimal(0);
                                                } else {
                                                    quantity = new BigDecimal((Integer) cc.get("quantity"));
                                                }
                                                break;
                                            }
                                        }
                                    } catch (Exception e) {
                                        log.error(e.getMessage());
                                    }
                                }
                                Map<String, Object> customerComplaintDimensionTotalVO = new HashMap<>(cusList.get(0));
                                customerComplaintDimensionTotalVO.put("total", new BigDecimal(0));
                                if (quantity == null || quantity.intValue() == 0) {

                                    //todo i.get(p.getProblemId().toString()) 看下这个是个什么类型是不是Bigdeciaml
                                    for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                        BigDecimal returnProductSum = cusList.stream().map(i -> i.get(p.getProblemId().toString()) != null ? (BigDecimal) i.get(p.getProblemId().toString()) : new BigDecimal(0)).reduce(BigDecimal.ZERO, BigDecimal::add);
                                        customerComplaintDimensionTotalVO.put(p.getProblemId().toString(), returnProductSum.intValue());
                                        customerComplaintDimensionTotalVO.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                        customerComplaintDimensionTotalVO.put("total", ((BigDecimal) customerComplaintDimensionTotalVO.get("total")).add(returnProductSum));
                                    }
                                } else {
                                    for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                        BigDecimal returnProductSum = cusList.stream().map(i -> i.get(p.getProblemId().toString()) != null ? (BigDecimal) i.get(p.getProblemId().toString()) : new BigDecimal(0)).reduce(BigDecimal.ZERO, BigDecimal::add);
                                        BigDecimal returnProductSumRate = returnProductSum.divide(quantity, 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000)).setScale(0);
                                        customerComplaintDimensionTotalVO.put(p.getProblemId().toString(), returnProductSum.intValue());
                                        customerComplaintDimensionTotalVO.put(p.getProblemId() + "Rate", returnProductSumRate);
                                        customerComplaintDimensionTotalVO.put("total", ((BigDecimal) customerComplaintDimensionTotalVO.get("total")).add(returnProductSum));
                                    }
                                }
                                if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) {
                                    customerComplaintDimensionTotalVO.put("createdAt", DateUtil.convertDateToString(range.getStart(), "yyyy-MM-dd") + "~" + DateUtil.convertDateToString(range.getEnd(), "yyyy-MM-dd"));
                                } else {
                                    customerComplaintDimensionTotalVO.put("createdAt", DateUtil.convertDateToString(range.getStart(), "yyyy-MM"));
                                }
                                returnCus.add(customerComplaintDimensionTotalVO);
                            }
                        }


                    }
                    return returnCus;
                }
            } else {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("orgId", query.getOrganizationId());
//            paramMap.put("channelList", channelList);
                paramMap.put("shopIdList", shopList);
                paramMap.put("asinList", asinList);
                paramMap.put("parentSkuList", new ArrayList<>().add(query.getParentSku()));
                paramMap.put("categoryIdList", categoryIdList);
                paramMap.put("modelList", modelList);
                paramMap.put("dateStart", simpleDateFormat.format(query.getDateFrom()));
                paramMap.put("dateEnd", simpleDateFormat.format(query.getDateTo()));
                paramMap.put("apiType", "ITEM");//SUM：汇总业务 ，ITEM:为明细行业务
                paramMap.put("Type",  CustomerComplaintCategoryTypeEnum.PARENTSKU.getType());
                paramMap.put("dateType", query.getDateType());
                paramMap.put("newProductFlag", query.getNewProductFlag());
                JSONObject jsonObject = this.sendCustomerComplainMessage(paramMap);
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                if (jsonArray != null) {
                    //todo 直接匹配，算退货率，然后返回就ok了
                    for (Object j : jsonArray) {
                        JSONArray dataItems = ((JSONObject) j).getJSONArray("dataItems");
                        for (Object k : dataItems) {
                            String returnDate = ((JSONObject) k).getString("orderDate");
                            Integer saleNum = ((JSONObject) k).getInteger("saleNum");
                            if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                                String parentSku = ((JSONObject) j).getString("parentSku");
                                for (Map<String, Object> r : customerComplaintDimensionTotalVOS) {
                                    if (CustomerComplainDateTypeEnum.DAY.getType().equals(query.getDateType())) { //日
                                        if (r.get("createdAt").equals(returnDate) && r.get("parentSku").equals(parentSku)) {
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) { //周
                                        //todo 判断时间在这个周里面
                                        String date[] = returnDate.split("~");
                                        try {
                                            if ((DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[0])) >= 0 &&
                                                    DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[1])) <= 0)
                                                    && r.get("parentSku").equals(parentSku)) {
                                                r.put("quantity", saleNum);
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    } else { //月
                                        if (r.get("createdAt").equals(returnDate) && r.get("parentSku").equals(parentSku)) {
                                            r.put("quantity", saleNum);
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                if (CustomerComplainDateTypeEnum.DAY.getType().equals(query.getDateType())) { //按日直接返回
                    return customerComplaintDimensionTotalVOS;
                } else {
                    //按照周，月分组
                    List<SpiltDateUtil.Range> rangeList = new ArrayList<>();
                    if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) {
                        rangeList = SpiltDateUtil.splitToWeeks(simpleDateFormat.format(query.getDateFrom()), simpleDateFormat.format(query.getDateTo()));
                    } else if (StatConstant.STAT_DATA_TYPE_MONTH.equals(query.getDateType())) {
                        rangeList = SpiltDateUtil.splitToMonths(simpleDateFormat.format(query.getDateFrom()), simpleDateFormat.format(query.getDateTo()));
                    }
                    List<Map<String, Object>> returnCus = new ArrayList<>();
                    for (SpiltDateUtil.Range range : rangeList) {
                        Map<String, List<Map<String, Object>>> cusMap = customerComplaintDimensionTotalVOS.stream().filter(r -> {
                            if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) {
                                try {
                                    return DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), range.getStart()) >= 0 &&
                                            DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), range.getEnd()) <= 0;
                                } catch (ParseException e) {
                                    throw new RuntimeException(e);
                                }
                            } else {
                                return r.get("createdAt").equals(monthSimpleDateFormat.format(range.getStart()));
                            }
                        }).collect(Collectors.groupingBy(i -> {
                            if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                                return (String) i.get("parentSku");
                            } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                                return (String) i.get("model");
                            } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                                return i.get("categoryId").toString();
                            } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                                return (String) i.get("asin");
                            } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                                return i.get("shopId").toString();
                            } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                                return (String) i.get("channel");
                            }
                            return null;
                        }));

                        for (String key : cusMap.keySet()) {
                            List<Map<String, Object>> cusList = cusMap.get(key);
                            if (CollectionUtil.isNotEmpty(cusList)) {
                                //取第一个就好
                                BigDecimal quantity = new BigDecimal(0);
                                for (Map<String, Object> cc : cusList) {
                                    try {
                                        if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) {
                                            if (DateUtil.compareDate(simpleDateFormat.parse((String) cc.get("createdAt")), range.getStart()) >= 0 && DateUtil.compareDate(simpleDateFormat.parse(((String) cc.get("createdAt"))), range.getEnd()) <= 0) {
                                                if (cc.get("quantity") == null) {
                                                    quantity = new BigDecimal(0);
                                                } else {
                                                    quantity = new BigDecimal((Integer) cc.get("quantity"));
                                                }
                                                break;
                                            }
                                        } else {
                                            if (cc.get("createdAt").equals(monthSimpleDateFormat.format(range.getStart()))) {
                                                if (cc.get("quantity") == null) {
                                                    quantity = new BigDecimal(0);
                                                } else {
                                                    quantity = new BigDecimal((Integer) cc.get("quantity"));
                                                }
                                                break;
                                            }
                                        }
                                    } catch (Exception e) {
                                        log.error(e.getMessage());
                                    }
                                }
                                Map<String, Object> customerComplaintDimensionTotalVO = new HashMap<>(cusList.get(0));
                                customerComplaintDimensionTotalVO.put("total", new BigDecimal(0));
                                if (quantity == null || quantity.intValue() == 0) {

                                    //todo i.get(p.getProblemId().toString()) 看下这个是个什么类型是不是Bigdeciaml
                                    for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                        BigDecimal returnProductSum = cusList.stream().map(i -> i.get(p.getProblemId().toString()) != null ? (BigDecimal) i.get(p.getProblemId().toString()) : new BigDecimal(0)).reduce(BigDecimal.ZERO, BigDecimal::add);
                                        customerComplaintDimensionTotalVO.put(p.getProblemId().toString(), returnProductSum.intValue());
                                        customerComplaintDimensionTotalVO.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                        customerComplaintDimensionTotalVO.put("total", ((BigDecimal) customerComplaintDimensionTotalVO.get("total")).add(returnProductSum));
                                    }
                                } else {
                                    for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                        BigDecimal returnProductSum = cusList.stream().map(i -> i.get(p.getProblemId().toString()) != null ? (BigDecimal) i.get(p.getProblemId().toString()) : new BigDecimal(0)).reduce(BigDecimal.ZERO, BigDecimal::add);
                                        BigDecimal returnProductSumRate = returnProductSum.divide(quantity, 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000)).setScale(0);
                                        customerComplaintDimensionTotalVO.put(p.getProblemId().toString(), returnProductSum.intValue());
                                        customerComplaintDimensionTotalVO.put(p.getProblemId() + "Rate", returnProductSumRate);
                                        customerComplaintDimensionTotalVO.put("total", ((BigDecimal) customerComplaintDimensionTotalVO.get("total")).add(returnProductSum));
                                    }
                                }
                                if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) {
                                    customerComplaintDimensionTotalVO.put("createdAt", DateUtil.convertDateToString(range.getStart(), "yyyy-MM-dd") + "~" + DateUtil.convertDateToString(range.getEnd(), "yyyy-MM-dd"));
                                } else {
                                    customerComplaintDimensionTotalVO.put("createdAt", DateUtil.convertDateToString(range.getStart(), "yyyy-MM"));
                                }
                                returnCus.add(customerComplaintDimensionTotalVO);
                            }
                        }


                    }
                    return returnCus;
                }
            }
        }
        return customerComplaintDimensionTotalVOS;
    }

//    @Override
//    public String rpcCustomerComplaintOneDetailListExport(CustomerComplaintMainQuery query) {
//        log.info("findMarProductPopularizeLogParentAsinExport start ... ");
//        int pageNo = 1;
//        int pageSize = 5;
//        String path = filePath + "客诉分析" + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN) + ".xlsx";
//        //动态头数据
//        List<List<String>> dynamicHeadList = new ArrayList<>();
//        dynamicHeadList.add(Collections.singletonList("客诉时间"));
//        dynamicHeadList.add(Collections.singletonList("sku"));
//        dynamicHeadList.add(Collections.singletonList("parentSku"));
//        dynamicHeadList.add(Collections.singletonList("model"));
//        dynamicHeadList.add(Collections.singletonList("品类"));
//        dynamicHeadList.add(Collections.singletonList("asin"));
//        dynamicHeadList.add(Collections.singletonList("店铺"));
//        dynamicHeadList.add(Collections.singletonList("渠道"));
//        for(ConfTicketProblem c:query.getConfTicketProblemList()) {
//            dynamicHeadList.add(Collections.singletonList(c.getProblemName()));
//            dynamicHeadList.add(Collections.singletonList(c.getProblemName() + "百分比"));
//        }
//        dynamicHeadList.add(Collections.singletonList("合计"));
//
//        //实际列字段
//        List<List<String>> dynamicColumnNameList = new ArrayList<>();
//        dynamicColumnNameList.add(Collections.singletonList("createdAt"));
//        dynamicColumnNameList.add(Collections.singletonList("sku"));
//        dynamicColumnNameList.add(Collections.singletonList("parentSku"));
//        dynamicColumnNameList.add(Collections.singletonList("model"));
//        dynamicColumnNameList.add(Collections.singletonList("categoryName"));
//        dynamicColumnNameList.add(Collections.singletonList("asin"));
//        dynamicColumnNameList.add(Collections.singletonList("shopName"));
//        dynamicColumnNameList.add(Collections.singletonList("channel"));
//        for(ConfTicketProblem c:query.getConfTicketProblemList()) {
//            dynamicColumnNameList.add(Collections.singletonList(c.getProblemId().toString()));
//            dynamicColumnNameList.add(Collections.singletonList(c.getProblemId() + "Rate"));
//        }
//        dynamicColumnNameList.add(Collections.singletonList("total"));
//
//        String uploadPath = null;
//        ExcelWriter writer = null;
//        try {
//            File file = File.createTempFile(path, ".xlsx");
//            writer = EasyExcel.write(file).excelType(ExcelTypeEnum.XLSX).build();
//            WriteSheet writeSheet = EasyExcel.writerSheet(0, "客诉分析").head(dynamicHeadList).build();   //设置头信息
//            while(true) {
//                //每行数据集合
//                List<List<Object>> rowDataList = new ArrayList<>();
//                PageHelper.startPage(pageNo, pageSize);
//                //获取数据（查询）
//                List<Map<String, Object>> list = this.selectCustomerComplaintOneDetailList(query);
//                if (CollectionUtil.isEmpty(list)) {
//                    break;
//                }
//                list.stream().forEach(item -> {
//                    List<Object> flagList = new ArrayList<>();
//                    for (List<String> dynamicColumn : dynamicColumnNameList) {
//                        flagList.add(item.get(dynamicColumn.get(0)));
//                    }
//                    rowDataList.add(flagList);
//                });
//                writer.write(rowDataList, writeSheet);
//                pageNo++;
//            }
//            writer.finish();
//            uploadPath = AliyunOssClientUtil.uploadFile(file.getName(), new FileInputStream(file), "erp/listing/parentAsin");
//            log.info("upload oss success! uploadPath: {}", uploadPath);
//        } catch (Exception e) {
//            log.info("upload oss fail! excelInfo:{}", e.getMessage());
//        } finally {
//            IoUtil.close(writer);
//        }
//        return uploadPath;
//    }





    /**
     * Description: 查询客诉维度汇总 sum
     *
     * @param query
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/6/7
     */
    @Override
    public  Map<String,Object> selectCustomerComplaintOneSum(CustomerComplaintMainQuery query) {
        if(query.getDateFrom() == null || query.getDateTo() == null) {
            throw  new RuntimeException("查询时间不能为空");
        }
        Set<String> setString = this.sumPage(query);
        if(CollectionUtils.isNotEmpty(setString)) {
            setString.stream().filter(i -> StringUtil.isNotEmpty(i)).collect(Collectors.toList());
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Map<String,Object> customerComplaintDimensionTotalVO = null;
        if(CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
            query.setChannel(null);
            customerComplaintDimensionTotalVO = oldMethodMapper.selectCustomerComplaintSum(query);
        } else {
            customerComplaintDimensionTotalVO = customerComplaintSummaryMapper.selectCustomerComplaintSum(query);
        }
        if(customerComplaintDimensionTotalVO != null) {
            if(customerComplaintDimensionTotalVO.keySet().size() == 1 ) {
                return customerComplaintDimensionTotalVO;
            }
            customerComplaintDimensionTotalVO.put("createdAt",simpleDateFormat.format(query.getDateFrom()) + " - " + simpleDateFormat.format(query.getDateTo()));
            List<String> asinList = null;
            List<String> skuList = null;
            List<Long> shopList = null;
            List<String> channelList = null;
            List<Long> categoryIdList = null;
            List<String> modelList = null;
            if(CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getDataType())) {
                skuList = CollectionUtil.isNotEmpty(query.getDataTypeValue()) ? query.getDataTypeValue() : new ArrayList<>();
            } else if(CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getDataType())) {
                modelList = CollectionUtil.isNotEmpty(query.getDataTypeValue()) ? query.getDataTypeValue() : new ArrayList<>();
            } else if(CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getDataType())) {
                asinList =CollectionUtil.isNotEmpty(query.getDataTypeValue()) ? query.getDataTypeValue() : new ArrayList<>();
            }
            shopList = CollectionUtil.isNotEmpty(query.getShopIds()) ? query.getShopIds() : new ArrayList<>();
            channelList = CollectionUtil.isNotEmpty(query.getChannel()) ? query.getChannel() : new ArrayList<>();
            categoryIdList = CollectionUtil.isNotEmpty(query.getProductCateGoryIds()) ? query.getProductCateGoryIds() : new ArrayList<>();

            if(CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                skuList = new ArrayList<>(setString);
                asinList = null;
                modelList = null;
            } else if(CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                modelList = new ArrayList<>(setString);
                skuList = null;
                asinList = null;
            } else if(CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                categoryIdList = new ArrayList<>(setString.stream().map(Long::parseLong).collect(Collectors.toList()));
                skuList = null;
                asinList = null;
                modelList = null;
            } else if(CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                asinList = new ArrayList<>(setString);
                skuList = null;
                modelList = null;
            } else if(CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                shopList = new ArrayList<>(setString.stream().map(Long::parseLong).collect(Collectors.toList()));
                skuList = null;
                asinList = null;
                modelList = null;
            } else if(CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                channelList = new ArrayList<>(setString);
                skuList = null;
                asinList = null;
                modelList = null;
            }

            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("orgId", query.getOrganizationId());
//            paramMap.put("channelList", channelList);
            paramMap.put("shopIdList",shopList);
            paramMap.put("asinList",asinList);
            paramMap.put("skuList",skuList);
            paramMap.put("categoryIdList",categoryIdList);
            paramMap.put("modelList",modelList);
            // todo 后面放开
            paramMap.put("dateStart",simpleDateFormat.format(query.getDateFrom()));
            paramMap.put("dateEnd",simpleDateFormat.format(query.getDateTo()));
            paramMap.put("apiType","SUM");//SUM：汇总业务 ，ITEM:为明细行业务
            //todo 后面放开
            paramMap.put("Type",query.getCategoryType());
            paramMap.put("dateType",query.getDateType());
            paramMap.put("newProductFlag",query.getNewProductFlag());
            JSONObject jsonObject = this.sendCustomerComplainMessage(paramMap);
            Integer saleNumSum = jsonObject.getInteger("saleNumSum");
            if (saleNumSum == null || saleNumSum.intValue() == 0) {
                for(ConfTicketProblem p:query.getConfTicketProblemList()) {
                    customerComplaintDimensionTotalVO.put(p.getProblemId() +"Rate",new BigDecimal(0));
                }
                customerComplaintDimensionTotalVO.put("totalRate",new BigDecimal(0));
            } else {
                for(ConfTicketProblem p:query.getConfTicketProblemList()) {
                    BigDecimal returnRate = ((BigDecimal)customerComplaintDimensionTotalVO.get(p.getProblemId().toString())).divide(new BigDecimal(saleNumSum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                    customerComplaintDimensionTotalVO.put(p.getProblemId() +"Rate",returnRate.setScale(0));
                }
                BigDecimal totalRate = ((BigDecimal)customerComplaintDimensionTotalVO.get("total")).divide(new BigDecimal(saleNumSum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                customerComplaintDimensionTotalVO.put("totalRate",totalRate.setScale(0));
            }
            //当时间查询为月份时设置合计明细
            if(StatConstant.STAT_DATA_TYPE_MONTH.equals(query.getDateType())) {
                //设置合计明细
                List<CustomerComplaintDimensionTotalVO> children = jsonObject.getJSONArray("saleNumSumItem").toJavaList(CustomerComplaintDimensionTotalVO.class);
                if(CollectionUtil.isNotEmpty(children)) {
                    //查询合计明细
                    query.setIsDetailQuery(1);

                    //重新设置查询sql头
                    ConfTicketProblem confTicketProblem = new ConfTicketProblem();
                    confTicketProblem.setLevel(1);
                    confTicketProblem.setOrganizationId(query.getOrganizationId().longValue());
                    List<ConfTicketProblem> confTicketProblemList = confTicketProblemService.selectConfTicketProblemList(confTicketProblem);
                    if (CollectionUtil.isEmpty(confTicketProblemList)) {
                        throw new RuntimeException("查询工单问题分类异常");
                    }
                    String selectHeadSql = "";
                    for (ConfTicketProblem c : confTicketProblemList) {
                        selectHeadSql += " SUM(if(b.problem_type1 = '" + c.getProblemId() + "',1,0)) as " + "`" + c.getProblemId() + "`" + ",\n";
                    }
                    selectHeadSql = selectHeadSql.substring(0, selectHeadSql.length() - 2);//去除多余逗号
                    query.setSelectHeadSql(selectHeadSql);
                    List<Map<String,Object>> customerComplaintDimensionTotalVOS = null;
                    if(CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                        customerComplaintDimensionTotalVOS = oldMethodMapper.selectCustomerComplaintOneDetailMonthList(query);
                    } else {
                        customerComplaintDimensionTotalVOS = customerComplaintSummaryMapper.selectCustomerComplaintOneDetailMonthList(query);
                    }
                    //匹配合计明细
                    if(CollectionUtil.isNotEmpty(customerComplaintDimensionTotalVOS)) {
                        customerComplaintDimensionTotalVOS.stream().forEach(i -> {
                            children.stream().forEach(j -> {
                                if(j.getCreatedAt().equals(i.get("createdAt"))) {
                                    if (j.getQuantity() == null || j.getQuantity().intValue() == 0) {
                                        for(ConfTicketProblem p:query.getConfTicketProblemList()) {
                                            i.put(p.getProblemId() +"Rate",new BigDecimal(0));
                                        }
                                        i.put("totalRate",new BigDecimal(0));
                                    } else {
                                        for(ConfTicketProblem p:query.getConfTicketProblemList()) {
                                            BigDecimal returnRate = ((BigDecimal)i.get(p.getProblemId().toString())).divide(new BigDecimal(j.getQuantity()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                            i.put(p.getProblemId() +"Rate",returnRate.setScale(0));
                                        }
                                        BigDecimal totalRate = ((BigDecimal) i.get("total")).divide(new BigDecimal(j.getQuantity()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000)).setScale(0);
                                        i.put("totalRate",totalRate);

                                    }

                                }
                            });
                        });
                        customerComplaintDimensionTotalVO.put("children",customerComplaintDimensionTotalVOS);
                    }
                }
                return customerComplaintDimensionTotalVO;
            }
        }
        return customerComplaintDimensionTotalVO;
    }

    /**
     * Description: 客诉分析一级任务导出
     *
     * @param response
     * @param query
     * @param authUserEntity
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/22
     */
    @Override
    public void listExportCustomerComplaintDimensionOneListDetail(HttpServletResponse response, CustomerComplaintMainQuery query, UserEntity authUserEntity) {
        TaskCenterRequest request = new TaskCenterRequest();
        request.setTaskCode("erp.listExportCustomerComplaintDimensionOneListDetail.export");
        List<Object> list = new ArrayList<>();
        list.add(JSON.toJSONString(query));
        request.setArgs(list);
        if (query.getOrganizationId() != null) {
            request.setOrgId(query.getOrganizationId());
        }
        this.taskCenterService.startTask(request, authUserEntity);

    }


    /**
     * 查询二级list
     * @param query
     * @return
     */
    @Override
    public List<Map<String,Object>> selectCustomerComplaintTwoList(CustomerComplaintMainQuery query) {
        if(query.getDateFrom() == null || query.getDateTo() == null) {
            throw  new RuntimeException("查询时间不能为空");
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        List<Map<String,Object>> customerComplaintDimensionTotalTwoVOS = null;
        if(CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
            query.setChannel(null);
            customerComplaintDimensionTotalTwoVOS = oldMethodMapper.selectCustomerComplaintTwoList(query);
        } else {
            customerComplaintDimensionTotalTwoVOS = customerComplaintSummaryMapper.selectCustomerComplaintTwoList(query);
        }

        if(CollectionUtil.isNotEmpty(customerComplaintDimensionTotalTwoVOS)) {
            List<String> asinList = null;
            List<String> skuList = null;
            List<Long> shopList = null;
            List<String> channelList = null;
            List<Long> categoryIdList = null;
            List<String> modelList = null;
            if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getDataType())) {
                skuList = CollectionUtil.isNotEmpty(query.getDataTypeValue()) ? query.getDataTypeValue() : new ArrayList<>();
            } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getDataType())) {
                modelList = CollectionUtil.isNotEmpty(query.getDataTypeValue()) ? query.getDataTypeValue() : new ArrayList<>();
            } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getDataType())) {
                asinList = CollectionUtil.isNotEmpty(query.getDataTypeValue()) ? query.getDataTypeValue() : new ArrayList<>();
            }
            shopList = CollectionUtil.isNotEmpty(query.getShopIds()) ? query.getShopIds() : new ArrayList<>();
            channelList = CollectionUtil.isNotEmpty(query.getChannel()) ? query.getChannel() : new ArrayList<>();
            categoryIdList = CollectionUtil.isNotEmpty(query.getProductCateGoryIds()) ? query.getProductCateGoryIds() : new ArrayList<>();

            Set<String> asinSet = new HashSet<>();
            Set<String> skuSet = new HashSet<>();
            Set<Long> shopSet = new HashSet<>();
            Set<String> channelSet = new HashSet<>();
            Set<Long> categoryIdSet = new HashSet<>();
            Set<String> modelSet = new HashSet<>();
            //todo 封装接口参数
            for (Map<String, Object> c : customerComplaintDimensionTotalTwoVOS) {
                c.put("createdAt", simpleDateFormat.format(query.getDateFrom()) + " - " + simpleDateFormat.format(query.getDateTo()));//设置返回时间
                query.setDateFromString(simpleDateFormat.format(query.getDateFrom()));
                query.setDateToString(simpleDateFormat.format(query.getDateTo()));
                c.put("customerComplaintMainQuery", query);//设置本次的查询方便detail查询
                if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                    skuSet.add((String) c.get("sku"));
                } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                    modelSet.add((String) c.get("model"));
                } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                    categoryIdSet.add((Long) c.get("categoryId"));
                } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                    asinSet.add((String) c.get("asin"));
                } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                    shopSet.add((Long) c.get("shopId"));
                } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                    channelSet.add((String) c.get("channel"));
                }
            }
            if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                skuList = new ArrayList<>(skuSet);
                asinList = null;
                modelList = null;
            } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                modelList = new ArrayList<>(modelSet);
                skuList = null;
                asinList = null;
            } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                categoryIdList = new ArrayList<>(categoryIdSet);
                skuList = null;
                asinList = null;
                modelList = null;
            } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                asinList = new ArrayList<>(asinSet);
                skuList = null;
                modelList = null;
            } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                shopList = new ArrayList<>(shopSet);
                skuList = null;
                asinList = null;
                modelList = null;
            } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                channelList = new ArrayList<>(channelSet);
                skuList = null;
                asinList = null;
                modelList = null;
            }
            //todo 这里要分情况。在sku维度的情况下如果sku为空不调用接口
            Integer diffCase = 0;
            List<String> filterSkuList = null;
            List<String> filterParentSkuList = null;
            if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                filterSkuList = skuList.stream().filter(i -> !"无SKU".equals(i)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(filterSkuList)) {
                    filterParentSkuList = customerComplaintDimensionTotalTwoVOS.stream().filter(i -> "无SKU".equals(i.get("sku"))).map(i -> (String) i.get("parentSku")).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(filterParentSkuList)) {
                        diffCase = 1;
                    } else {
                        diffCase = 2;
                    }
                } else {
                    filterParentSkuList = customerComplaintDimensionTotalTwoVOS.stream().filter(i -> "无SKU".equals(i.get("sku"))).map(i -> (String) i.get("parentSku")).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(filterParentSkuList)) {
                        diffCase = 3;
                    }
                }
            }

            if (diffCase == 0) {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("orgId", query.getOrganizationId());
//            paramMap.put("channelList", channelList);
                paramMap.put("shopIdList", shopList);
                paramMap.put("asinList", asinList);
                paramMap.put("skuList", skuList);
                paramMap.put("categoryIdList", categoryIdList);
                paramMap.put("modelList", modelList);
                //todo 后面用这个
                paramMap.put("dateStart", simpleDateFormat.format(query.getDateFrom()));
                paramMap.put("dateEnd", simpleDateFormat.format(query.getDateTo()));
                paramMap.put("apiType", "ITEM");//SUM：汇总业务 ，ITEM:为明细行业务
                paramMap.put("Type", query.getCategoryType());
                paramMap.put("dateType", query.getDateType());
                paramMap.put("newProductFlag", query.getNewProductFlag());
                JSONObject jsonObject = this.sendCustomerComplainMessage(paramMap);
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                Map<String, BigDecimal> map = new HashMap<>();
                if (jsonArray != null) {
                    if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                        map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject) i).getString("erpSku"), j -> ((JSONObject) j).getBigDecimal("saleNumSum")));
                    } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                        map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject) i).getString("productMidModel"), j -> ((JSONObject) j).getBigDecimal("saleNumSum")));
                    } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                        map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject) i).getString("categorySecondId"), j -> ((JSONObject) j).getBigDecimal("saleNumSum")));
                    } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                        map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject) i).getString("asin"), j -> ((JSONObject) j).getBigDecimal("saleNumSum")));
                    } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                        map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject) i).getString("channelId"), j -> ((JSONObject) j).getBigDecimal("saleNumSum")));
                    } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                        map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject) i).getString("channel"), j -> ((JSONObject) j).getBigDecimal("saleNumSum")));
                    }
                }
                for (Map<String, Object> cus : customerComplaintDimensionTotalTwoVOS) {
                    BigDecimal saleNumSum = null;
                    if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                        saleNumSum = map.get(cus.get("sku"));
                    } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                        saleNumSum = map.get(cus.get("model"));
                    } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                        saleNumSum = map.get(cus.get("categoryId").toString());
                    } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                        saleNumSum = map.get(cus.get("asin"));
                    } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                        saleNumSum = map.get(cus.get("shopId").toString());
                    } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                        saleNumSum = map.get(cus.get("channel"));
                    }
                    if (saleNumSum == null || saleNumSum.intValue() == 0) {
                        for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                            cus.put(p.getProblemId() + "Rate", new BigDecimal(0));
                        }
                    } else {

                        for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                            BigDecimal returnRate = ((BigDecimal) cus.get(p.getProblemId().toString())).divide(saleNumSum, 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                            cus.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                        }

                    }
                }

            }
            //sku不为空，parentsku不为空
            if (diffCase == 1) {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("orgId", query.getOrganizationId());
//            paramMap.put("channelList", channelList);
                paramMap.put("shopIdList", shopList);
                paramMap.put("asinList", asinList);
                paramMap.put("skuList", filterSkuList);
                paramMap.put("categoryIdList", categoryIdList);
                paramMap.put("modelList", modelList);
                //todo 后面用这个
                paramMap.put("dateStart", simpleDateFormat.format(query.getDateFrom()));
                paramMap.put("dateEnd", simpleDateFormat.format(query.getDateTo()));
                paramMap.put("apiType", "ITEM");//SUM：汇总业务 ，ITEM:为明细行业务
                paramMap.put("Type", query.getCategoryType());
                paramMap.put("dateType", query.getDateType());
                paramMap.put("newProductFlag", query.getNewProductFlag());
                JSONObject jsonObject = this.sendCustomerComplainMessage(paramMap);
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                Map<String, BigDecimal> map = new HashMap<>();
                if (jsonArray != null) {
                    if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                        map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject) i).getString("erpSku"), j -> ((JSONObject) j).getBigDecimal("saleNumSum")));
                    }
                }
                //todo 第二次调用parentSku销量接口
                paramMap.put("skuList",null);
                paramMap.put("parentSkuList", filterParentSkuList);
                paramMap.put("Type", CustomerComplaintCategoryTypeEnum.PARENTSKU.getType());
                JSONObject secondJsonObject = this.sendCustomerComplainMessage(paramMap);
                JSONArray secondJsonArray = secondJsonObject.getJSONArray("data");
                Map<String, BigDecimal> secondMap = new HashMap<>();
                if (jsonArray != null) {
                    if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                        secondMap = secondJsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject) i).getString("parentSku"), j -> ((JSONObject) j).getBigDecimal("saleNumSum")));
                    }
                }
                for (Map<String, Object> cus : customerComplaintDimensionTotalTwoVOS) {
                    BigDecimal saleNumSum = null;
                    if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                        if(!"无SKU".equals((String)cus.get("sku"))) {
                            saleNumSum = map.get(cus.get("sku"));
                        } else {
                            saleNumSum = secondMap.get(cus.get("parentSku"));
                        }
                    }
                    if (saleNumSum == null || saleNumSum.intValue() == 0) {
                        for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                            cus.put(p.getProblemId() + "Rate", new BigDecimal(0));
                        }
                    } else {

                        for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                            BigDecimal returnRate = ((BigDecimal) cus.get(p.getProblemId().toString())).divide(saleNumSum, 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                            cus.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                        }

                    }
                }

            }

            if (diffCase == 2) {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("orgId", query.getOrganizationId());
//            paramMap.put("channelList", channelList);
                paramMap.put("shopIdList", shopList);
                paramMap.put("asinList", asinList);
                paramMap.put("skuList", filterSkuList);
                paramMap.put("categoryIdList", categoryIdList);
                paramMap.put("modelList", modelList);
                //todo 后面用这个
                paramMap.put("dateStart", simpleDateFormat.format(query.getDateFrom()));
                paramMap.put("dateEnd", simpleDateFormat.format(query.getDateTo()));
                paramMap.put("apiType", "ITEM");//SUM：汇总业务 ，ITEM:为明细行业务
                paramMap.put("Type", query.getCategoryType());
                paramMap.put("dateType", query.getDateType());
                paramMap.put("newProductFlag", query.getNewProductFlag());
                JSONObject jsonObject = this.sendCustomerComplainMessage(paramMap);
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                Map<String, BigDecimal> map = new HashMap<>();
                if (jsonArray != null) {
                    if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                        map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject) i).getString("erpSku"), j -> ((JSONObject) j).getBigDecimal("saleNumSum")));
                    }
                }
                for (Map<String, Object> cus : customerComplaintDimensionTotalTwoVOS) {
                    BigDecimal saleNumSum = null;
                    if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                        saleNumSum = map.get(cus.get("sku"));
                    }
                    if (saleNumSum == null || saleNumSum.intValue() == 0) {
                        for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                            cus.put(p.getProblemId() + "Rate", new BigDecimal(0));
                        }
                    } else {

                        for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                            BigDecimal returnRate = ((BigDecimal) cus.get(p.getProblemId().toString())).divide(saleNumSum, 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                            cus.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                        }

                    }
                }
            }

            if (diffCase == 3) {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("orgId", query.getOrganizationId());
//            paramMap.put("channelList", channelList);
                paramMap.put("shopIdList", shopList);
                paramMap.put("asinList", asinList);
                paramMap.put("skuList", null);
                paramMap.put("parentSkuList", filterParentSkuList);
                paramMap.put("categoryIdList", categoryIdList);
                paramMap.put("modelList", modelList);
                //todo 后面用这个
                paramMap.put("dateStart", simpleDateFormat.format(query.getDateFrom()));
                paramMap.put("dateEnd", simpleDateFormat.format(query.getDateTo()));
                paramMap.put("apiType", "ITEM");//SUM：汇总业务 ，ITEM:为明细行业务
                paramMap.put("Type", query.getCategoryType());
                paramMap.put("dateType", query.getDateType());
                paramMap.put("newProductFlag", query.getNewProductFlag());
                JSONObject jsonObject = this.sendCustomerComplainMessage(paramMap);
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                Map<String, BigDecimal> map = new HashMap<>();
                if (jsonArray != null) {
                    if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                        map = jsonArray.stream().collect(Collectors.toMap(i -> ((JSONObject) i).getString("parentSku"), j -> ((JSONObject) j).getBigDecimal("saleNumSum")));
                    }
                }
                for (Map<String, Object> cus : customerComplaintDimensionTotalTwoVOS) {
                    BigDecimal saleNumSum = null;
                    if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                        saleNumSum = map.get(cus.get("parentSku"));
                    } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                        saleNumSum = map.get(cus.get("model"));
                    } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                        saleNumSum = map.get(cus.get("categoryId").toString());
                    } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                        saleNumSum = map.get(cus.get("asin"));
                    } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                        saleNumSum = map.get(cus.get("shopId").toString());
                    } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                        saleNumSum = map.get(cus.get("channel"));
                    }
                    if (saleNumSum == null || saleNumSum.intValue() == 0) {
                        for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                            cus.put(p.getProblemId() + "Rate", new BigDecimal(0));
                        }
                    } else {

                        for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                            BigDecimal returnRate = ((BigDecimal) cus.get(p.getProblemId().toString())).divide(saleNumSum, 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                            cus.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                        }

                    }
                }
            }
        }
        return customerComplaintDimensionTotalTwoVOS;
    }

    /**
     * 查询二级detail
     * @param query
     * @return
     */
    @Override
    public List<Map<String,Object>> selectCustomerComplaintTwoDetailList(CustomerComplaintMainQuery query) {
        if (query.getDateFrom() == null || query.getDateTo() == null) {
            throw new RuntimeException("查询时间不能为空");
        }
        //todo 特殊出来渠道维度
        if(CollectionUtils.isNotEmpty(query.getChannel())) {
            //VC-DF，VC-USPO，VC-DI,amazon
            List<String> amzSaleChannel = Arrays.asList("amazonvendords", "VC-DI", "amazonvendor", "amazon");
            //交集channel 含（amz）
            List<String> containReviewAmzChannel = query.getChannel().stream().filter(item -> amzSaleChannel.contains(item)).collect(Collectors.toList());
            //差集channel
            List<String> noContaimReviewAmzChannel = query.getChannel().stream().filter(item -> !amzSaleChannel.contains(item)).collect(Collectors.toList());
            query.setAmzChannelList(containReviewAmzChannel);
            query.setNoAmzChannelList(noContaimReviewAmzChannel);
        }

        //当维度是店铺的时候，渠道要去掉
        if(CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
            query.setChannel(null);
            query.setNoAmzChannelList(null);
            query.setAmzChannelList(null);
        }
        //拿出渠道的需要的店铺
        if(CollectionUtil.isNotEmpty(query.getChannel())) {
            String filterChannel = query.getChannel().get(0);
            List<CustomerComplaintMainQuery.Items> itemsList = query.getSelectChannel();
            if (CollectionUtil.isNotEmpty(itemsList)) {
                itemsList = itemsList.stream().filter(i -> filterChannel.equals(i.getChannel())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(itemsList)) {
                    query.setChannelShopIds(itemsList.get(0).getShopIds());
                }
            }
        }
        //todo 这里要分2种情况。要不是有sku,要不是无sku
        Integer diffCase = 0;
        List<String> paramSkuList = CollectionUtil.isNotEmpty(query.getDataTypeValue()) ? query.getDataTypeValue() : new ArrayList<>();
        if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
            if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getDataType())) {
                if (!"无SKU".equals(paramSkuList.get(0))) {
                    diffCase = 1;
                } else {
                    diffCase = 2;
                }
            }

        }

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat monthSimpleDateFormat = new SimpleDateFormat("yyyy-MM");
        query.setIsDetailQuery(1);
        List<Map<String,Object>> customerComplaintDimensionTotalTwoVOS = null;
        if(CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
            query.setChannel(null);
            customerComplaintDimensionTotalTwoVOS = oldMethodMapper.selectCustomerComplaintTwoDetailList(query);
        } else {
            //处理无sku
            if (diffCase == 2) {
                query.setWithoutSku("1");
            }
            customerComplaintDimensionTotalTwoVOS = customerComplaintSummaryMapper.selectCustomerComplaintTwoDetailList(query);
        }

        if (CollectionUtil.isNotEmpty(customerComplaintDimensionTotalTwoVOS)) {
            List<String> asinList = null;
            List<String> skuList = null;
            List<Long> shopList = null;
            List<String> channelList = null;
            List<Long> categoryIdList = null;
            List<String> modelList = null;
            if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getDataType())) {
                skuList = CollectionUtil.isNotEmpty(query.getDataTypeValue()) ? query.getDataTypeValue() : new ArrayList<>();
            } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getDataType())) {
                modelList = CollectionUtil.isNotEmpty(query.getDataTypeValue()) ? query.getDataTypeValue() : new ArrayList<>();
            } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getDataType())) {
                asinList = CollectionUtil.isNotEmpty(query.getDataTypeValue()) ? query.getDataTypeValue() : new ArrayList<>();
            }
            shopList = CollectionUtil.isNotEmpty(query.getShopIds()) ? query.getShopIds() : new ArrayList<>();
            channelList = CollectionUtil.isNotEmpty(query.getChannel()) ? query.getChannel() : new ArrayList<>();
            categoryIdList = CollectionUtil.isNotEmpty(query.getProductCateGoryIds()) ? query.getProductCateGoryIds() : new ArrayList<>();

            Set<String> asinSet = new HashSet<>();
            Set<String> skuSet = new HashSet<>();
            Set<Long> shopSet = new HashSet<>();
            Set<String> channelSet = new HashSet<>();
            Set<Long> categoryIdSet = new HashSet<>();
            Set<String> modelSet = new HashSet<>();
            //todo 封装接口参数
            for (Map<String, Object> c : customerComplaintDimensionTotalTwoVOS) {

                if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                    skuSet.add((String) c.get("sku"));
                } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                    modelSet.add((String) c.get("model"));
                } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                    categoryIdSet.add((Long) c.get("categoryId"));
                } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                    asinSet.add((String) c.get("asin"));
                } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                    shopSet.add((Long) c.get("shopId"));
                } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                    channelSet.add((String) c.get("channel"));
                }
            }
            if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                skuList = new ArrayList<>(skuSet);
                asinList = null;
                modelList = null;
            } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                modelList = new ArrayList<>(modelSet);
                skuList = null;
                asinList = null;
            } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                categoryIdList = new ArrayList<>(categoryIdSet);
                skuList = null;
                asinList = null;
                modelList = null;
            } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                asinList = new ArrayList<>(asinSet);
                skuList = null;
                modelList = null;
            } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                shopList = new ArrayList<>(shopSet);
                skuList = null;
                asinList = null;
                modelList = null;
            } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                channelList = new ArrayList<>(channelSet);
                skuList = null;
                asinList = null;
                modelList = null;
            }

            if (diffCase != 2) {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("orgId", query.getOrganizationId());
//            paramMap.put("channelList", channelList);
                paramMap.put("shopIdList", shopList);
                paramMap.put("asinList", asinList);
                paramMap.put("skuList", skuList);
                paramMap.put("categoryIdList", categoryIdList);
                paramMap.put("modelList", modelList);
                paramMap.put("dateStart", simpleDateFormat.format(query.getDateFrom()));
                paramMap.put("dateEnd", simpleDateFormat.format(query.getDateTo()));
                paramMap.put("apiType", "ITEM");//SUM：汇总业务 ，ITEM:为明细行业务
                paramMap.put("Type", query.getCategoryType());
                paramMap.put("dateType", query.getDateType());
                paramMap.put("newProductFlag", query.getNewProductFlag());
                JSONObject jsonObject = this.sendCustomerComplainMessage(paramMap);
                JSONArray jsonArray = jsonObject.getJSONArray("data");

                if (jsonArray != null) {
                    //todo 直接匹配，算退货率，然后返回就ok了
                    for (Object j : jsonArray) {
                        JSONArray dataItems = ((JSONObject) j).getJSONArray("dataItems");
                        for (Object k : dataItems) {
                            String returnDate = ((JSONObject) k).getString("orderDate");
                            Integer saleNum = ((JSONObject) k).getInteger("saleNum");
                            if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                                String erpSku = ((JSONObject) j).getString("erpSku");
                                for (Map<String, Object> r : customerComplaintDimensionTotalTwoVOS) {
                                    if (CustomerComplainDateTypeEnum.DAY.getType().equals(query.getDateType())) { //日
                                        if (r.get("createdAt").equals(returnDate) && r.get("sku").equals(erpSku)) {
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) { //周
                                        //todo 判断时间在这个周里面
                                        String date[] = returnDate.split("~");
                                        try {
                                            if ((DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[0])) >= 0 &&
                                                    DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[1])) <= 0)
                                                    && r.get("sku").equals(erpSku)) {
                                                r.put("quantity", saleNum);
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    } else { //月
                                        if (r.get("createdAt").equals(returnDate) && r.get("sku").equals(erpSku)) {
                                            r.put("quantity", saleNum);
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    }
                                }

                            } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                                String model = ((JSONObject) j).getString("productMidModel");
                                for (Map<String, Object> r : customerComplaintDimensionTotalTwoVOS) {
                                    if (CustomerComplainDateTypeEnum.DAY.getType().equals(query.getDateType())) { //日
                                        if (r.get("createdAt").equals(returnDate) && r.get("model").equals(model)) {
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) { //周
                                        //todo 判断时间在这个周里面
                                        String date[] = returnDate.split("~");
                                        try {
                                            if ((DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[0])) >= 0 &&
                                                    DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[1])) <= 0)
                                                    && r.get("model").equals(model)) {
                                                r.put("quantity", saleNum);
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    } else { //月
                                        if (r.get("createdAt").equals(returnDate) && r.get("model").equals(model)) {
                                            r.put("quantity", saleNum);
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    }
                                }
                            } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                                Long categoryId = ((JSONObject) j).getLong("categorySecondId");
                                for (Map<String, Object> r : customerComplaintDimensionTotalTwoVOS) {
                                    if (CustomerComplainDateTypeEnum.DAY.getType().equals(query.getDateType())) { //日
                                        if (r.get("createdAt").equals(returnDate) && r.get("categoryId").equals(categoryId)) {
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) { //周
                                        //todo 判断时间在这个周里面
                                        String date[] = returnDate.split("~");
                                        try {
                                            if ((DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[0])) >= 0 &&
                                                    DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[1])) <= 0)
                                                    && r.get("categoryId").equals(categoryId)) {
                                                r.put("quantity", saleNum);
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    } else { //月
                                        if (r.get("createdAt").equals(returnDate) && r.get("categoryId").equals(categoryId)) {
                                            r.put("quantity", saleNum);
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    }
                                }
                            } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                                String asin = ((JSONObject) j).getString("asin");
                                for (Map<String, Object> r : customerComplaintDimensionTotalTwoVOS) {
                                    if (CustomerComplainDateTypeEnum.DAY.getType().equals(query.getDateType())) { //日
                                        if (r.get("createdAt").equals(returnDate) && r.get("asin").equals(asin)) {
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) { //周
                                        //todo 判断时间在这个周里面
                                        String date[] = returnDate.split("~");
                                        try {
                                            if ((DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[0])) >= 0 &&
                                                    DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[1])) <= 0)
                                                    && r.get("asin").equals(asin)) {
                                                r.put("quantity", saleNum);
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    } else { //月
                                        if (r.get("createdAt").equals(returnDate) && r.get("asin").equals(asin)) {
                                            r.put("quantity", saleNum);
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    }
                                }

                            } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                                Integer channelId = ((JSONObject) j).getInteger("channelId");
                                for (Map<String, Object> r : customerComplaintDimensionTotalTwoVOS) {
                                    if (CustomerComplainDateTypeEnum.DAY.getType().equals(query.getDateType())) { //日
                                        if (r.get("createdAt").equals(returnDate) && r.get("shopId").equals(channelId)) {
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) { //周
                                        //todo 判断时间在这个周里面
                                        String date[] = returnDate.split("~");
                                        try {
                                            if ((DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[0])) >= 0 &&
                                                    DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[1])) <= 0)
                                                    && r.get("shopId").equals(channelId)) {
                                                r.put("quantity", saleNum);
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    } else { //月
                                        if (r.get("createdAt").equals(returnDate) && r.get("shopId").equals(channelId)) {
                                            r.put("quantity", saleNum);
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    }
                                }
                            } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                                String channel = ((JSONObject) j).getString("channel");
                                for (Map<String, Object> r : customerComplaintDimensionTotalTwoVOS) {
                                    if (CustomerComplainDateTypeEnum.DAY.getType().equals(query.getDateType())) { //日
                                        if (r.get("createdAt").equals(returnDate) && r.get("channel").equals(channel)) {
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) { //周
                                        //todo 判断时间在这个周里面
                                        String date[] = returnDate.split("~");
                                        try {
                                            if ((DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[0])) >= 0 &&
                                                    DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[1])) <= 0)
                                                    && r.get("channel").equals(channel)) {
                                                r.put("quantity", saleNum);
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    } else { //月
                                        if (r.get("createdAt").equals(returnDate) && r.get("channel").equals(channel)) {
                                            r.put("quantity", saleNum);
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                        }
                    }
                }
                if (CustomerComplainDateTypeEnum.DAY.getType().equals(query.getDateType())) { //按日直接返回
                    return customerComplaintDimensionTotalTwoVOS;
                } else {
                    //按照周，月分组
                    List<SpiltDateUtil.Range> rangeList = new ArrayList<>();
                    if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) {
                        rangeList = SpiltDateUtil.splitToWeeks(simpleDateFormat.format(query.getDateFrom()), simpleDateFormat.format(query.getDateTo()));
                    } else if (StatConstant.STAT_DATA_TYPE_MONTH.equals(query.getDateType())) {
                        rangeList = SpiltDateUtil.splitToMonths(simpleDateFormat.format(query.getDateFrom()), simpleDateFormat.format(query.getDateTo()));
                    }
                    List<Map<String, Object>> returnCus = new ArrayList<>();
                    for (SpiltDateUtil.Range range : rangeList) {
                        Map<String, List<Map<String, Object>>> cusMap = customerComplaintDimensionTotalTwoVOS.stream().filter(r -> {
                            if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) {
                                try {
                                    return DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), range.getStart()) >= 0 &&
                                            DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), range.getEnd()) <= 0;
                                } catch (ParseException e) {
                                    throw new RuntimeException(e);
                                }
                            } else {
                                return r.get("createdAt").equals(monthSimpleDateFormat.format(range.getStart()));
                            }
                        }).collect(Collectors.groupingBy(i -> {
                            if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                                return (String) i.get("sku");
                            } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                                return (String) i.get("model");
                            } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                                return i.get("categoryId").toString();
                            } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                                return (String) i.get("asin");
                            } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                                return i.get("shopId").toString();
                            } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                                return (String) i.get("channel");
                            }
                            return null;
                        }));

                        for (String key : cusMap.keySet()) {
                            List<Map<String, Object>> cusList = cusMap.get(key);
                            if (CollectionUtil.isNotEmpty(cusList)) {
                                //取第一个就好
                                BigDecimal saleNum = new BigDecimal(0);
                                for (Map<String, Object> cc : cusList) {
                                    try {
                                        if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) {
                                            if (DateUtil.compareDate(simpleDateFormat.parse((String) cc.get("createdAt")), range.getStart()) >= 0 && DateUtil.compareDate(simpleDateFormat.parse((String) cc.get("createdAt")), range.getEnd()) <= 0) {
                                                if (cc.get("quantity") == null) {
                                                    saleNum = new BigDecimal(0);
                                                } else {
                                                    saleNum = new BigDecimal((Integer) cc.get("quantity"));
                                                }
                                                break;
                                            }
                                        } else {
                                            if (cc.get("createdAt").equals(monthSimpleDateFormat.format(range.getStart()))) {
                                                if (cc.get("quantity") == null) {
                                                    saleNum = new BigDecimal(0);
                                                } else {
                                                    saleNum = new BigDecimal((Integer) cc.get("quantity"));
                                                }
                                                break;
                                            }
                                        }
                                    } catch (Exception e) {
                                        log.error(e.getMessage());
                                    }
                                }
                                Map<String, Object> complaintDimensionTotalTwoVO = new HashMap<>(cusList.get(0));
                                complaintDimensionTotalTwoVO.put("total", new BigDecimal(0));
                                if (saleNum == null || saleNum.intValue() == 0) {
                                    //todo i.get(p.getProblemId().toString()) 看下这个是个什么类型是不是Bigdeciaml
                                    for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                        BigDecimal returnProductSum = cusList.stream().map(i -> i.get(p.getProblemId().toString()) != null ? (BigDecimal) i.get(p.getProblemId().toString()) : new BigDecimal(0)).reduce(BigDecimal.ZERO, BigDecimal::add);
                                        complaintDimensionTotalTwoVO.put(p.getProblemId().toString(), returnProductSum.intValue());
                                        complaintDimensionTotalTwoVO.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                        complaintDimensionTotalTwoVO.put("total", ((BigDecimal) complaintDimensionTotalTwoVO.get("total")).add(returnProductSum));
                                    }
                                } else {
                                    for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                        BigDecimal returnProductSum = cusList.stream().map(i -> i.get(p.getProblemId().toString()) != null ? (BigDecimal) i.get(p.getProblemId().toString()) : new BigDecimal(0)).reduce(BigDecimal.ZERO, BigDecimal::add);
                                        BigDecimal returnProductSumRate = returnProductSum.divide(saleNum, 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000)).setScale(0);
                                        complaintDimensionTotalTwoVO.put(p.getProblemId().toString(), returnProductSum.intValue());
                                        complaintDimensionTotalTwoVO.put(p.getProblemId() + "Rate", returnProductSumRate);
                                        complaintDimensionTotalTwoVO.put("total", ((BigDecimal) complaintDimensionTotalTwoVO.get("total")).add(returnProductSum));
                                    }
                                }

                                if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) {
                                    complaintDimensionTotalTwoVO.put("createdAt", DateUtil.convertDateToString(range.getStart(), "yyyy-MM-dd") + "~" + DateUtil.convertDateToString(range.getEnd(), "yyyy-MM-dd"));
                                } else {
                                    complaintDimensionTotalTwoVO.put("createdAt", DateUtil.convertDateToString(range.getStart(), "yyyy-MM"));
                                }
                                returnCus.add(complaintDimensionTotalTwoVO);
                            }
                        }
                    }
                    return returnCus;
                }
            } else {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("orgId", query.getOrganizationId());
//            paramMap.put("channelList", channelList);
                paramMap.put("shopIdList", shopList);
                paramMap.put("asinList", asinList);
                paramMap.put("parentSkuList", new ArrayList<>().add(query.getParentSku()));
                paramMap.put("categoryIdList", categoryIdList);
                paramMap.put("modelList", modelList);
                paramMap.put("dateStart", simpleDateFormat.format(query.getDateFrom()));
                paramMap.put("dateEnd", simpleDateFormat.format(query.getDateTo()));
                paramMap.put("apiType", "ITEM");//SUM：汇总业务 ，ITEM:为明细行业务
                paramMap.put("Type", query.getCategoryType());
                paramMap.put("dateType", query.getDateType());
                paramMap.put("newProductFlag", query.getNewProductFlag());
                JSONObject jsonObject = this.sendCustomerComplainMessage(paramMap);
                JSONArray jsonArray = jsonObject.getJSONArray("data");

                if (jsonArray != null) {
                    //todo 直接匹配，算退货率，然后返回就ok了
                    for (Object j : jsonArray) {
                        JSONArray dataItems = ((JSONObject) j).getJSONArray("dataItems");
                        for (Object k : dataItems) {
                            String returnDate = ((JSONObject) k).getString("orderDate");
                            Integer saleNum = ((JSONObject) k).getInteger("saleNum");
                            if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                                String parentSku = ((JSONObject) j).getString("parentSku");
                                for (Map<String, Object> r : customerComplaintDimensionTotalTwoVOS) {
                                    if (CustomerComplainDateTypeEnum.DAY.getType().equals(query.getDateType())) { //日
                                        if (r.get("createdAt").equals(returnDate) && r.get("parentSku").equals(parentSku)) {
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    } else if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) { //周
                                        //todo 判断时间在这个周里面
                                        String date[] = returnDate.split("~");
                                        try {
                                            if ((DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[0])) >= 0 &&
                                                    DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), simpleDateFormat.parse(date[1])) <= 0)
                                                    && r.get("parentSku").equals(parentSku)) {
                                                r.put("quantity", saleNum);
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    } else { //月
                                        if (r.get("createdAt").equals(returnDate) && r.get("sku").equals(parentSku)) {
                                            r.put("quantity", saleNum);
                                            if (saleNum == null || saleNum.intValue() == 0) {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    r.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                                }
                                            } else {
                                                for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                                    BigDecimal returnRate = ((BigDecimal) r.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                                    r.put(p.getProblemId() + "Rate", returnRate.setScale(0));
                                                }
                                            }
                                        }
                                    }
                                }

                            }
                        }
                    }
                }
                if (CustomerComplainDateTypeEnum.DAY.getType().equals(query.getDateType())) { //按日直接返回
                    return customerComplaintDimensionTotalTwoVOS;
                } else {
                    //按照周，月分组
                    List<SpiltDateUtil.Range> rangeList = new ArrayList<>();
                    if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) {
                        rangeList = SpiltDateUtil.splitToWeeks(simpleDateFormat.format(query.getDateFrom()), simpleDateFormat.format(query.getDateTo()));
                    } else if (StatConstant.STAT_DATA_TYPE_MONTH.equals(query.getDateType())) {
                        rangeList = SpiltDateUtil.splitToMonths(simpleDateFormat.format(query.getDateFrom()), simpleDateFormat.format(query.getDateTo()));
                    }
                    List<Map<String, Object>> returnCus = new ArrayList<>();
                    for (SpiltDateUtil.Range range : rangeList) {
                        Map<String, List<Map<String, Object>>> cusMap = customerComplaintDimensionTotalTwoVOS.stream().filter(r -> {
                            if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) {
                                try {
                                    return DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), range.getStart()) >= 0 &&
                                            DateUtil.compareDate(simpleDateFormat.parse((String) r.get("createdAt")), range.getEnd()) <= 0;
                                } catch (ParseException e) {
                                    throw new RuntimeException(e);
                                }
                            } else {
                                return r.get("createdAt").equals(monthSimpleDateFormat.format(range.getStart()));
                            }
                        }).collect(Collectors.groupingBy(i -> {
                            if (CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                                return (String) i.get("parentSku");
                            } else if (CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                                return (String) i.get("model");
                            } else if (CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                                return i.get("categoryId").toString();
                            } else if (CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                                return (String) i.get("asin");
                            } else if (CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                                return i.get("shopId").toString();
                            } else if (CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                                return (String) i.get("channel");
                            }
                            return null;
                        }));

                        for (String key : cusMap.keySet()) {
                            List<Map<String, Object>> cusList = cusMap.get(key);
                            if (CollectionUtil.isNotEmpty(cusList)) {
                                //取第一个就好
                                BigDecimal saleNum = new BigDecimal(0);
                                for (Map<String, Object> cc : cusList) {
                                    try {
                                        if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) {
                                            if (DateUtil.compareDate(simpleDateFormat.parse((String) cc.get("createdAt")), range.getStart()) >= 0 && DateUtil.compareDate(simpleDateFormat.parse((String) cc.get("createdAt")), range.getEnd()) <= 0) {
                                                if (cc.get("quantity") == null) {
                                                    saleNum = new BigDecimal(0);
                                                } else {
                                                    saleNum = new BigDecimal((Integer) cc.get("quantity"));
                                                }
                                                break;
                                            }
                                        } else {
                                            if (cc.get("createdAt").equals(monthSimpleDateFormat.format(range.getStart()))) {
                                                if (cc.get("quantity") == null) {
                                                    saleNum = new BigDecimal(0);
                                                } else {
                                                    saleNum = new BigDecimal((Integer) cc.get("quantity"));
                                                }
                                                break;
                                            }
                                        }
                                    } catch (Exception e) {
                                        log.error(e.getMessage());
                                    }
                                }
                                Map<String, Object> complaintDimensionTotalTwoVO = new HashMap<>(cusList.get(0));
                                complaintDimensionTotalTwoVO.put("total", new BigDecimal(0));
                                if (saleNum == null || saleNum.intValue() == 0) {
                                    //todo i.get(p.getProblemId().toString()) 看下这个是个什么类型是不是Bigdeciaml
                                    for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                        BigDecimal returnProductSum = cusList.stream().map(i -> i.get(p.getProblemId().toString()) != null ? (BigDecimal) i.get(p.getProblemId().toString()) : new BigDecimal(0)).reduce(BigDecimal.ZERO, BigDecimal::add);
                                        complaintDimensionTotalTwoVO.put(p.getProblemId().toString(), returnProductSum.intValue());
                                        complaintDimensionTotalTwoVO.put(p.getProblemId() + "Rate", new BigDecimal(0));
                                        complaintDimensionTotalTwoVO.put("total", ((BigDecimal) complaintDimensionTotalTwoVO.get("total")).add(returnProductSum));
                                    }
                                } else {
                                    for (ConfTicketProblem p : query.getConfTicketProblemList()) {
                                        BigDecimal returnProductSum = cusList.stream().map(i -> i.get(p.getProblemId().toString()) != null ? (BigDecimal) i.get(p.getProblemId().toString()) : new BigDecimal(0)).reduce(BigDecimal.ZERO, BigDecimal::add);
                                        BigDecimal returnProductSumRate = returnProductSum.divide(saleNum, 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000)).setScale(0);
                                        complaintDimensionTotalTwoVO.put(p.getProblemId().toString(), returnProductSum.intValue());
                                        complaintDimensionTotalTwoVO.put(p.getProblemId() + "Rate", returnProductSumRate);
                                        complaintDimensionTotalTwoVO.put("total", ((BigDecimal) complaintDimensionTotalTwoVO.get("total")).add(returnProductSum));
                                    }
                                }

                                if (StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType())) {
                                    complaintDimensionTotalTwoVO.put("createdAt", DateUtil.convertDateToString(range.getStart(), "yyyy-MM-dd") + "~" + DateUtil.convertDateToString(range.getEnd(), "yyyy-MM-dd"));
                                } else {
                                    complaintDimensionTotalTwoVO.put("createdAt", DateUtil.convertDateToString(range.getStart(), "yyyy-MM"));
                                }
                                returnCus.add(complaintDimensionTotalTwoVO);
                            }
                        }
                    }
                    return returnCus;
                }

            }
        }
        return customerComplaintDimensionTotalTwoVOS;
    }

    /**
     * @param query
     * @return
     */
    @Override
    public Map<String,Object> selectCustomerComplaintTwoSum(CustomerComplaintMainQuery query) {
        if(query.getDateFrom() == null || query.getDateTo() == null) {
            throw  new RuntimeException("查询时间不能为空");
        }
        Set<String> setString = this.sumTwoPage(query);
        if(CollectionUtils.isNotEmpty(setString)) {
            setString.stream().filter(i -> StringUtil.isNotEmpty(i)).collect(Collectors.toList());
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Map<String,Object> customerComplaintDimensionTotalTwoVO = null;
        if(CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
            query.setChannel(null);
            customerComplaintDimensionTotalTwoVO  = oldMethodMapper.selectCustomerComplaintTwoSum(query);
        } else {
            customerComplaintDimensionTotalTwoVO  = customerComplaintSummaryMapper.selectCustomerComplaintTwoSum(query);
        }

        if(customerComplaintDimensionTotalTwoVO != null) {
            customerComplaintDimensionTotalTwoVO.put("createdAt",simpleDateFormat.format(query.getDateFrom()) + " - " + simpleDateFormat.format(query.getDateTo()));
            List<String> asinList = null;
            List<String> skuList = null;
            List<Long> shopList = null;
            List<String> channelList = null;
            List<Long> categoryIdList = null;
            List<String> modelList = null;
            if(CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getDataType())) {
                skuList = CollectionUtil.isNotEmpty(query.getDataTypeValue()) ? query.getDataTypeValue() : new ArrayList<>();
            } else if(CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getDataType())) {
                modelList = CollectionUtil.isNotEmpty(query.getDataTypeValue()) ? query.getDataTypeValue() : new ArrayList<>();
            } else if(CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getDataType())) {
                asinList =CollectionUtil.isNotEmpty(query.getDataTypeValue()) ? query.getDataTypeValue() : new ArrayList<>();
            }
            shopList = CollectionUtil.isNotEmpty(query.getShopIds()) ? query.getShopIds() : new ArrayList<>();
            channelList = CollectionUtil.isNotEmpty(query.getChannel()) ? query.getChannel() : new ArrayList<>();
            categoryIdList = CollectionUtil.isNotEmpty(query.getProductCateGoryIds()) ? query.getProductCateGoryIds() : new ArrayList<>();

            if(CustomerComplaintCategoryTypeEnum.SKU.getType().equals(query.getCategoryType())) {
                skuList = new ArrayList<>(setString);
                asinList = null;
                modelList = null;
            } else if(CustomerComplaintCategoryTypeEnum.MODEL.getType().equals(query.getCategoryType())) {
                modelList = new ArrayList<>(setString);
                skuList = null;
                asinList = null;
            } else if(CustomerComplaintCategoryTypeEnum.PRODUCT_CATEGORY.getType().equals(query.getCategoryType())) {
                categoryIdList = new ArrayList<>(setString.stream().map(Long::parseLong).collect(Collectors.toList()));
                skuList = null;
                asinList = null;
                modelList = null;
            } else if(CustomerComplaintCategoryTypeEnum.ASIN.getType().equals(query.getCategoryType())) {
                asinList = new ArrayList<>(setString);
                skuList = null;
                modelList = null;
            } else if(CustomerComplaintCategoryTypeEnum.SHOP.getType().equals(query.getCategoryType())) {
                shopList = new ArrayList<>(setString.stream().map(Long::parseLong).collect(Collectors.toList()));
                skuList = null;
                asinList = null;
                modelList = null;
            } else if(CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                channelList = new ArrayList<>(setString);
                skuList = null;
                asinList = null;
                modelList = null;
            }

            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("orgId", query.getOrganizationId());
//            paramMap.put("channelList", channelList);
            paramMap.put("shopIdList",shopList);
            paramMap.put("asinList",asinList);
            paramMap.put("skuList",skuList);
            paramMap.put("categoryIdList",categoryIdList);
            paramMap.put("modelList",modelList);

            // todo 后面放开
            paramMap.put("dateStart",simpleDateFormat.format(query.getDateFrom()));
            paramMap.put("dateEnd",simpleDateFormat.format(query.getDateTo()));

            paramMap.put("apiType","SUM");//SUM：汇总业务 ，ITEM:为明细行业务

            //todo 后面放开
            paramMap.put("Type",query.getCategoryType());
            paramMap.put("dateType",query.getDateType());
            paramMap.put("newProductFlag",query.getNewProductFlag());
            JSONObject jsonObject = this.sendCustomerComplainMessage(paramMap);
            Integer saleNum = jsonObject.getInteger("saleNumSum");
            customerComplaintDimensionTotalTwoVO.put("quantity",saleNum);
            if (saleNum == null || saleNum.intValue() == 0) {
                for(ConfTicketProblem p:query.getConfTicketProblemList()) {
                    customerComplaintDimensionTotalTwoVO.put(p.getProblemId() +"Rate",new BigDecimal(0));
                }
                customerComplaintDimensionTotalTwoVO.put("totalRate",new BigDecimal(0));
            } else {
                for(ConfTicketProblem p:query.getConfTicketProblemList()) {
                    BigDecimal returnRate = ((BigDecimal)customerComplaintDimensionTotalTwoVO.get(p.getProblemId().toString())).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                    customerComplaintDimensionTotalTwoVO.put(p.getProblemId() +"Rate",returnRate.setScale(0));
                }
                BigDecimal totalRate = ((BigDecimal)customerComplaintDimensionTotalTwoVO.get("total")).divide(new BigDecimal(saleNum), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                customerComplaintDimensionTotalTwoVO.put("totalRate",totalRate.setScale(0));
            }
            //当时间查询为月份时设置合计明细
            if(StatConstant.STAT_DATA_TYPE_MONTH.equals(query.getDateType())) {
                //设置合计明细
                List<CustomerComplaintDimensionTotalTwoVO> children = jsonObject.getJSONArray("saleNumSumItem").toJavaList(CustomerComplaintDimensionTotalTwoVO.class);
                if(CollectionUtil.isNotEmpty(children)) {
                    //查询合计明细
                    query.setIsDetailQuery(1);

                    //动态拼接sql
                    ConfTicketProblem confTicketProblem = new ConfTicketProblem();
                    confTicketProblem.setLevel(2);
                    confTicketProblem.setOrganizationId(query.getOrganizationId().longValue());
                    List<ConfTicketProblem> confTicketProblemList = confTicketProblemService.selectConfTicketProblemList(confTicketProblem);
                    if (CollectionUtil.isEmpty(confTicketProblemList)) {
                        throw new RuntimeException("查询工单问题分类异常");
                    }
                    String selectHeadSql = "";
                    for (ConfTicketProblem c : confTicketProblemList) {
                        selectHeadSql += " SUM(if(b.problem_type2 = '" + c.getProblemId() + "',1,0)) as " + "`" + c.getProblemId() + "`" + ",\n";
                    }
                    selectHeadSql = selectHeadSql.substring(0,selectHeadSql.length() - 2);//去除多余逗号
                    query.setSelectHeadSql(selectHeadSql);
                    List<Map<String,Object>> customerComplaintDimensionTotalTwoVOS = null;
                    if(CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                        customerComplaintDimensionTotalTwoVOS = oldMethodMapper.selectCustomerComplaintTwoDetailMonthList(query);
                    } else {
                        customerComplaintDimensionTotalTwoVOS = customerComplaintSummaryMapper.selectCustomerComplaintTwoDetailMonthList(query);
                    }
                    //匹配合计明细
                    if(CollectionUtil.isNotEmpty(customerComplaintDimensionTotalTwoVOS)) {
                        customerComplaintDimensionTotalTwoVOS.stream().forEach(i -> {
                            children.stream().forEach(j -> {
                                if(j.getCreatedAt().equals(i.get("createdAt"))) {
                                    i.put("quantity",j.getQuantity());
                                    if (j.getQuantity() == null || j.getQuantity().intValue() == 0) {
                                        for(ConfTicketProblem p:query.getConfTicketProblemList()) {
                                            i.put(p.getProblemId() +"Rate",new BigDecimal(0));
                                        }
                                        i.put("totalRate",new BigDecimal(0));
                                    } else {
                                        for(ConfTicketProblem p:query.getConfTicketProblemList()) {
                                            BigDecimal returnRate = ((BigDecimal)i.get(p.getProblemId().toString())).divide(new BigDecimal(j.getQuantity()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000));
                                            i.put(p.getProblemId() +"Rate",returnRate.setScale(0));
                                        }
                                        BigDecimal totalRate = ((BigDecimal) i.get("total")).divide(new BigDecimal(j.getQuantity()), 6, RoundingMode.HALF_UP).multiply(new BigDecimal(1000000)).setScale(0);
                                        i.put("totalRate",totalRate);
                                    }

                                }
                            });
                        });
                        customerComplaintDimensionTotalTwoVO.put("children",customerComplaintDimensionTotalTwoVOS);
                    }
                }
                return customerComplaintDimensionTotalTwoVO;
            }
        }
        return customerComplaintDimensionTotalTwoVO;
    }

    /**
     * @param response
     * @param query
     * @param authUserEntity
     */
    @Override
    public void listExportCustomerComplaintDimensionTwoListDetail(HttpServletResponse response, CustomerComplaintMainQuery query, UserEntity authUserEntity) {
        TaskCenterRequest request = new TaskCenterRequest();
        request.setTaskCode("erp.listExportCustomerComplaintDimensionTwoListDetail.export");
        List<Object> list = new ArrayList<>();
        list.add(JSON.toJSONString(query));
        request.setArgs(list);
        if (query.getOrganizationId() != null) {
            request.setOrgId(query.getOrganizationId());
        }
        this.taskCenterService.startTask(request, authUserEntity);
        // AssertUtil.isFail(FinanceErrorEnum.ASNYC_EXPORT_GENERATED);
    }
    public JSONObject sendCustomerComplainMessage(Map<String,Object> paramMap) {
        String url = QUERY_SALE_NUM_MASTER_URL + "/api/v1/CustomerAnalysisSales";
        HttpRequest request = HttpUtil.createPost(url);
        log.error("查询客诉分析销量统计参数{}",JSON.toJSONString(paramMap));
        request.body(JSON.toJSONString(paramMap));
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            log.error("查询客诉分析销量统计异常,异常原因{}",response.body());
            throw new IllegalArgumentException("查询客诉分析销量统计异常");
        }
        return JSONObject.parseObject(response.body());
    }
    /**
     * Description: 求和分页
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/9/3
     */
    public Set<String> sumPage(CustomerComplaintMainQuery query) {
        int pageNo = 1;
        int pageSize = 2000;
        Set<String> stringSet = new HashSet<>();
        while (true) {
            PageHelper.startPage(pageNo, pageSize);
            List<String> customerComplaintDimensionTotalVO = null;
            if(CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                query.setChannel(null);
                customerComplaintDimensionTotalVO = oldMethodMapper.selectCustomerComplaintSumPage(query);
            } else {
                customerComplaintDimensionTotalVO = customerComplaintSummaryMapper.selectCustomerComplaintSumPage(query);
            }

            if (CollUtil.isEmpty(customerComplaintDimensionTotalVO)) {
                break;
            }
            for (String r : customerComplaintDimensionTotalVO) {
                stringSet.add(r);
            }
            pageNo++;
        }
        return stringSet;
    }

    /**
     * Description: 求和分页
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/9/3
     */
    public Set<String> sumTwoPage(CustomerComplaintMainQuery query) {
        int pageNo = 1;
        int pageSize = 2000;
        Set<String> stringSet = new HashSet<>();
        while (true) {
            PageHelper.startPage(pageNo, pageSize);
            List<String> customerComplaintDimensionTotalVO = null;
            if(CustomerComplaintCategoryTypeEnum.CHANNEL.getType().equals(query.getCategoryType())) {
                query.setChannel(null);
                customerComplaintDimensionTotalVO = oldMethodMapper.selectCustomerComplaintTwoSumPage(query);
            } else {
                customerComplaintDimensionTotalVO = customerComplaintSummaryMapper.selectCustomerComplaintTwoSumPage(query);
            }

            if (CollUtil.isEmpty(customerComplaintDimensionTotalVO)) {
                break;
            }
            for (String r : customerComplaintDimensionTotalVO) {
                stringSet.add(r);
            }
            pageNo++;
        }
        return stringSet;
    }




}
