enviorment=${enviorment}

web_service_rest.http.port=${web_service_rest.http.port}
web_service_rest.https.port=${web_service_rest.https.port}
dubbo_service_rpc.stop.port=${dubbo_service_rpc.stop.port}
dubbo_service_rpc.http.port=${dubbo_service_rpc.http.port}
dubbo_service_rpc.dubbo.port=${dubbo_service_rpc.dubbo.port}
dubbo.application.qos.port=${dubbo.application.qos.port}
entity.manager.factory.bean.name.default=${entity.manager.factory.bean.name.default}

#### ENTDIY START ###

# \u6784\u5EFA\u7248\u672C,\u7528\u4E8E\u53D1\u5E03\u7248\u672C\u4FE1\u606F\u663E\u793A\u548C\u7F13\u5B58\u7248\u672C\u6807\u8BC6
# \u7B2C\u4E00\u4F4D\u4E3B\u7248\u672C\u53F7\u548C\u7B2C\u4E8C\u4F4D\u6B21\u7248\u672C\u53F7\uFF0C\u6839\u636E\u5B9E\u9645\u529F\u80FD\u53D8\u66F4\u5E45\u5EA6\u624B\u5DE5\u66F4\u65B0\u8BBE\u7F6E
# \u7B2C\u4E09\u4F4DBUILD_NUMBER\u4FDD\u6301\u4E0D\u52A8\uFF0C\u5F00\u53D1\u6A21\u5F0F\u4E0B\u7531\u6846\u67B6\u81EA\u52A8\u66FF\u6362\u4E3A\u52A8\u6001\u6570\u5B57\u4EE5\u4E0D\u65AD\u5F3A\u5236\u66F4\u65B0\u4EE3\u7801\u907F\u514D\u9759\u6001\u8D44\u6E90\u7F13\u5B58
# \u53D1\u5E03\u90E8\u7F72\u5305\u65F6\u53EF\u901A\u8FC7Jenkins\u8FD9\u6837\u7684CI\u5DE5\u5177\u52A8\u6001\u628AXXX\u66FF\u6362\u4E3A\u6784\u5EFA\u7248\u672C\u4FE1\u606F
build.version=${build.version}

# Hibernate\u6807\u51C6\u7684hbm2ddl\u529F\u80FD\uFF0C\u5177\u4F53\u7528\u6CD5\u8BF7\u53C2\u8003\u5B98\u65B9\u6587\u6863\u8BF4\u660E
# \u7F6E\u7A7A\u8868\u793A\u5173\u95ED\u81EA\u52A8\u5316\u5904\u7406
# \uFF01\u5C24\u5176\u6CE8\u610F\u8C28\u614E\u4F7F\u7528create\u6216create-drop\u4F1A\u5BFC\u81F4\u6E05\u9664\u5F53\u524D\u5DF2\u6709\u6570\u636E\uFF01
hibernate.hbm2ddl.auto=${hibernate.hbm2ddl.auto}

# Envers\u7EC4\u4EF6org.hibernate.envers.default_schema\u914D\u7F6E\u53C2\u6570\u503C
# \u4E3A\u7A7A\u8868\u793A\u5728\u5F53\u524D\u5E94\u7528schema\uFF0C\u53EF\u4EE5\u6307\u5B9A\u5206\u79BB\u7684schema\u540D\u79F0
hibernate.envers.default.schema=${hibernate.envers.default.schema}

# \u5F00\u53D1\u8FC7\u7A0B\u53EF\u8BBE\u7F6E\u4E3Atrue\uFF0C\u751F\u4EA7\u73AF\u5883\u4E00\u5B9A\u8981\u8BBE\u7F6E\u4E3Afalse
# \u5F00\u53D1\u6A21\u5F0F\u4F1A\u81EA\u52A8\u521D\u59CB\u5316\u6216\u66F4\u65B0\u57FA\u7840\u6570\u636E\uFF0C\u751F\u4EA7\u6A21\u5F0F\u53EA\u4F1A\u5728\u7A7A\u6570\u636E\u72B6\u6001\u624D\u4F1A\u521D\u59CB\u5316\u57FA\u7840\u6570\u636E\uFF0C\u8BE6\u89C1\uFF1ADatabaseDataInitialize
dev.mode=${dev.mode}

# \u6F14\u793A\u90E8\u7F72\u53EF\u8BBE\u7F6E\u4E3Atrue\uFF0C\u751F\u4EA7\u73AF\u5883\u4E00\u5B9A\u8981\u8BBE\u7F6E\u4E3Afalse
# \u6F14\u793A\u8FD0\u884C\u6A21\u5F0F\u4E0B\uFF1A\u6309\u7167\u521D\u59CB\u5316\u4EE3\u7801\u903B\u8F91\u81EA\u52A8\u751F\u6210\u4E00\u7CFB\u5217\u6F14\u793A\u6A21\u62DF\u6570\u636E\uFF1B\u7981\u7528\u4E00\u4E9B\u6838\u5FC3\u57FA\u7840\u529F\u80FD\u63D0\u4EA4\u9632\u6B62\u7528\u6237\u968F\u610F\u64CD\u4F5C
demo.mode=${demo.mode}

# \u6743\u9650\u63A7\u5236\u7B49\u7EA7\uFF0C\u53EF\u9009\u503C\u8BF4\u660E\uFF1A
# high=${# high}
# low=${# low}
auth.control.level=${auth.control.level}

# \u7CFB\u7EDF\u540D\u79F0\uFF0C\u7528\u4E8E\u9875\u9762\u6807\u9898\u7B49\u663E\u793A
system.name=${system.name}

#api\u6587\u6863\u7248\u672C
apidoc.version=${apidoc.version}

# \u81EA\u52A9\u6CE8\u518C\u7BA1\u7406\u8D26\u53F7\u529F\u80FD\u5F00\u5173\uFF1Atrue=\u5173\u95ED\u7BA1\u7406\u8D26\u53F7\u81EA\u52A9\u6CE8\u518C\uFF0Cfalse=${# \u81EA\u52A9\u6CE8\u518C\u7BA1\u7406\u8D26\u53F7\u529F\u80FD\u5F00\u5173\uFF1Atrue=\u5173\u95ED\u7BA1\u7406\u8D26\u53F7\u81EA\u52A9\u6CE8\u518C\uFF0Cfalse}
cfg_mgmt_signup_disabled=${cfg_mgmt_signup_disabled}

# \u90AE\u4EF6\u76F8\u5173\u53C2\u6570\uFF0C\u6839\u636E\u5B9E\u9645\u90AE\u4EF6\u7CFB\u7EDF\u914D\u7F6E\u8BBE\u5B9A
mail.host=${mail.host}
mail.username=${mail.username}
mail.from=${mail.from}
mail.password=${mail.password}
mail.smtp.auth=${mail.smtp.auth}

# SMS\u77ED\u4FE1\u901A\u9053\u7B7E\u540D
sms.signature=${sms.signature}

# \u4E0A\u4F20\u6587\u4EF6\u5199\u5165\u6587\u4EF6\u76EE\u5F55\u8DEF\u5F84\uFF0C\u6BD4\u5982Nginx\u5BF9\u5E94\u7684\u9759\u6001\u8D44\u6E90\u76EE\u5F55\u3002
# \u5982\u679C\u4E3A\u7A7A\u5219\u53D6\u81EA\u52A8\u53D6\u5F53\u524Dwebapp/upload\u76EE\u5F55\uFF0C\u6CE8\u610F\u5B58\u5728\u66F4\u65B0\u7248\u672C\u88AB\u8BEF\u5220\u7684\u53EF\u80FD\u3002
file.write.dir=${file.write.dir}
# \u6587\u4EF6\u6216\u56FE\u7247\u4E0B\u8F7D\u6216\u663E\u793A\u7684URL\u524D\u7F00\u90E8\u5206\u3002\u6587\u4EF6\u4FE1\u606F\u5BF9\u8C61\u53EA\u8BB0\u5F55\u76F8\u5BF9\u8DEF\u5F84\u90E8\u5206\u5185\u5BB9\uFF0C\u6B64\u524D\u7F00\u8FFD\u52A0\u5BF9\u8C61\u8DEF\u5F84\u5185\u5BB9\u5F97\u5230\u5B8C\u6574\u8BBF\u95EEURL\u3002
# \u4E00\u822C\u53EF\u5B9A\u4E49\u4E3ANginx\u9759\u6001\u8D44\u6E90\u8BBF\u95EE\u5730\u5740\u6216\u5916\u90E8\u6587\u4EF6\u5B58\u50A8\u5730\u5740URL\u524D\u7F00\uFF0C\u5982\u679C\u4E3A\u7A7A\u5219\u53D6\u5F53\u524Dwebapp\u7684URL\u8DEF\u5F84\u52A0\u4E0AContextPath
file.read.url.prefix=${file.read.url.prefix}


##########################################################
#
# Redis configuration
#
##########################################################
hibernate.redis.config=${hibernate.redis.config}
hibernate.redis.host=${hibernate.redis.host}

#### ENTDIY END ###



#spring.mvc.static-path-pattern=${spring.mvc.static-path-pattern}
spring.jpa.properties.hibernate.hbm2ddl.auto=${spring.jpa.properties.hibernate.hbm2ddl.auto}
spring.jpa.properties.hibernate.dialect=${spring.jpa.properties.hibernate.dialect}
spring.jpa.properties.hibernate.default_schema=${spring.jpa.properties.hibernate.default_schema}
hibernate.dialect=${hibernate.dialect}
hibernate.default_schema=${hibernate.default_schema}
# Envers\u7EC4\u4EF6org.hibernate.envers.default_schema\u914D\u7F6E\u53C2\u6570\u503C
# \u4E3A\u7A7A\u8868\u793A\u5728\u5F53\u524D\u5E94\u7528schema\uFF0C\u53EF\u4EE5\u6307\u5B9A\u5206\u79BB\u7684schema\u540D\u79F0
spring.jpa.properties.hibernate.envers.default.schema=${spring.jpa.properties.hibernate.envers.default.schema}

spring.jpa.show-sql=${spring.jpa.show-sql}
spring.jpa.properties.hibernate.format_sql=${spring.jpa.properties.hibernate.format_sql}
hibernate.format_sql=${hibernate.format_sql}
#logging.level.org.hibernate=${logging.level.org.hibernate}
logging.level.org.hibernate.SQL=${logging.level.org.hibernate.SQL}
logging.level.org.hibernate.type=${logging.level.org.hibernate.type}
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=${logging.level.org.hibernate.type.descriptor.sql.BasicBinder}
#log4j.logger.org.hibernate.type.descriptor.sql.BasicBinder=${log4j.logger.org.hibernate.type.descriptor.sql.BasicBinder}
spring.jpa.properties.hibernate.jdbc.time_zone =${spring.jpa.properties.hibernate.jdbc.time_zone }
hibernate.jdbc.time_zone =${hibernate.jdbc.time_zone }
#spring.jpa.open-in-view=${spring.jpa.open-in-view}
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=${spring.jpa.properties.hibernate.enable_lazy_load_no_trans}
hibernate.enable_lazy_load_no_trans=${hibernate.enable_lazy_load_no_trans}
spring.jackson.time-zone=${spring.jackson.time-zone}
spring.jackson.date-format=${spring.jackson.date-format}

# project-DEFAULT
spring.datasource.url = ${jdbc.mysql.datasource.url}
spring.datasource.username =${jdbc.mysql.datasource.username}
spring.datasource.password =${jdbc.mysql.datasource.password}
spring.datasource.driver-class-name =${jdbc.mysql.datasource.driver-class-name}


# erp
spring.datasource.erp.url = ${jdbc.mysql.datasource.erp.url}
spring.datasource.erp.username =${jdbc.mysql.datasource.erp.username}
spring.datasource.erp.password =${jdbc.mysql.datasource.erp.password}
spring.datasource.erp.driver-class-name =${jdbc.mysql.datasource.erp.driver-class-name}

# dashboard
spring.datasource.dashboard.url = ${jdbc.mysql.datasource.dashboard.url}
spring.datasource.dashboard.username =${jdbc.mysql.datasource.dashboard.username}
spring.datasource.dashboard.password =${jdbc.mysql.datasource.dashboard.password}
spring.datasource.dashboard.driver-class-name =${jdbc.mysql.datasource.dashboard.driver-class-name}

#fbb
spring.datasource.fbb.url = ${jdbc.mysql.datasource.fbb.url}
spring.datasource.fbb.username =${jdbc.mysql.datasource.fbb.username}
spring.datasource.fbb.password =${jdbc.mysql.datasource.fbb.password}
spring.datasource.fbb.driver-class-name =${jdbc.mysql.datasource.fbb.driver-class-name}

# tbadbp
spring.datasource.tbadbp.url = ${jdbc.mysql.datasource.tbadbp.url}
spring.datasource.tbadbp.username =${jdbc.mysql.datasource.tbadbp.username}
spring.datasource.tbadbp.password =${jdbc.mysql.datasource.tbadbp.password}
spring.datasource.tbadbp.driver-class-name =${jdbc.mysql.datasource.tbadbp.driver-class-name}

# multichannel
spring.datasource.multichannel.url = ${jdbc.mysql.datasource.multichannel.url}
spring.datasource.multichannel.username =${jdbc.mysql.datasource.multichannel.username}
spring.datasource.multichannel.password =${jdbc.mysql.datasource.multichannel.password}
spring.datasource.multichannel.driver-class-name =${jdbc.mysql.datasource.multichannel.driver-class-name}

#jdbc \u914D\u7F6E

# erp
jdbc.mysql.datasource.erp.url = ${jdbc.mysql.datasource.erp.url}
jdbc.mysql.datasource.erp.username = ${jdbc.mysql.datasource.erp.username}
jdbc.mysql.datasource.erp.password = ${jdbc.mysql.datasource.erp.password}
jdbc.mysql.datasource.erp.driver-class-name = ${jdbc.mysql.datasource.erp.driver-class-name}

# dashboard
jdbc.mysql.datasource.dashboard.url = ${jdbc.mysql.datasource.dashboard.url}
jdbc.mysql.datasource.dashboard.username = ${jdbc.mysql.datasource.dashboard.username}
jdbc.mysql.datasource.dashboard.password = ${jdbc.mysql.datasource.dashboard.password}
jdbc.mysql.datasource.dashboard.driver-class-name = ${jdbc.mysql.datasource.dashboard.driver-class-name}

#fbb
jdbc.mysql.datasource.fbb.url = ${jdbc.mysql.datasource.fbb.url}
jdbc.mysql.datasource.fbb.username = ${jdbc.mysql.datasource.fbb.username}
jdbc.mysql.datasource.fbb.password = ${jdbc.mysql.datasource.fbb.password}
jdbc.mysql.datasource.fbb.driver-class-name = ${jdbc.mysql.datasource.fbb.driver-class-name}

# tbadbp
jdbc.mysql.datasource.tbadbp.url = ${jdbc.mysql.datasource.tbadbp.url}
jdbc.mysql.datasource.tbadbp.username = ${jdbc.mysql.datasource.tbadbp.username}
jdbc.mysql.datasource.tbadbp.password = ${jdbc.mysql.datasource.tbadbp.password}
jdbc.mysql.datasource.tbadbp.driver-class-name = ${jdbc.mysql.datasource.tbadbp.driver-class-name}

# multichannel
jdbc.mysql.datasource.multichannel.url = ${jdbc.mysql.datasource.multichannel.url}
jdbc.mysql.datasource.multichannel.username = ${jdbc.mysql.datasource.multichannel.username}
jdbc.mysql.datasource.multichannel.password = ${jdbc.mysql.datasource.multichannel.password}
jdbc.mysql.datasource.multichannel.driver-class-name = ${jdbc.mysql.datasource.multichannel.driver-class-name}


jdbc.driverClassName=${jdbc.driverClassName}
jdbc.database.dialect=${jdbc.database.storageEngine}
jdbc.database.storageEngine=${jdbc.database.storageEngine}

jdbc.initialSize=${jdbc.initialSize}
jdbc.minIdle=${jdbc.minIdle}
jdbc.maxActive=${jdbc.maxActive}

jdbc.maxWait=${jdbc.maxWait}
jdbc.timeBetweenEvictionRunsMillis=${jdbc.timeBetweenEvictionRunsMillis}

jdbc.minEvictableIdleTimeMillis=${jdbc.minEvictableIdleTimeMillis}

jdbc.validationQuery=${jdbc.validationQuery}
jdbc.testWhileIdle=${jdbc.testWhileIdle}
jdbc.testOnBorrow=${jdbc.testOnBorrow}
jdbc.testOnReturn=${jdbc.testOnReturn}

jdbc.poolPreparedStatements=${jdbc.poolPreparedStatements}
jdbc.maxPoolPreparedStatementPerConnectionSize=${jdbc.maxPoolPreparedStatementPerConnectionSize}

jdbc.removeAbandoned=${jdbc.removeAbandoned}
jdbc.removeAbandonedTimeout=${jdbc.removeAbandonedTimeout}
jdbc.logAbandoned=${jdbc.logAbandoned}
jdbc.filters=${jdbc.filters}
jdbc.maxOpenPreparedStatements=${jdbc.maxOpenPreparedStatements}


log.cfg=${log.cfg}

#FTP\u901A\u7528\u914D\u7F6E
ftp.maxTotal=${ftp.maxTotal}
ftp.maxTotalPerKey=${ftp.maxTotalPerKey}
ftp.minIdlePerKey=${ftp.minIdlePerKey}
ftp.maxIdlePerKey=${ftp.maxIdlePerKey}
ftp.maxWaitMillis=${ftp.maxWaitMillis}
ftp.testWhileIdle=${ftp.testWhileIdle}
ftp.testOnBorrow=${ftp.testOnBorrow}
ftp.timeBetweenEvictionRunsMillis=${ftp.timeBetweenEvictionRunsMillis}
ftp.minEvictableIdleTimeMillis=${ftp.minEvictableIdleTimeMillis}

#ftp\u5177\u4F53\u914D\u7F6E
local.directory=${local.directory}

zookeeper.address=${zookeeper.address}
dubbo.zookeeper.address=${dubbo.zookeeper.address}
dubbo.application.qos.enable=${dubbo.application.qos.enable}
dubbo.application.qos.accept.foreign.ip=${dubbo.application.qos.accept.foreign.ip}


job.namespace=${job.namespace}

# mns\u961F\u5217\u914D\u7F6E
mns.accountendpoint=${mns.accountendpoint}
mns.accesskeyid=${mns.accesskeyid}
mns.accesskeysecret=${mns.accesskeysecret}

aliyun.accessId=${aliyun.accessId}
aliyun.accessKey=${aliyun.accessKey}
#aliyun.smsAccessId=${#aliyun.smsAccessId}
#aliyun.smsAccessKey=${#aliyun.smsAccessKey}
aliyun.smsEndpointDefault=${aliyun.smsEndpointDefault}
aliyun.smsSignDefault=${aliyun.smsSignDefault}
#aliyun.smsTemplateCodeDefault=${#aliyun.smsTemplateCodeDefault}
#aliyun.smsTemplateCodeDefault=${#aliyun.smsTemplateCodeDefault}
aliyun.smsTemplateCodeDefault=${aliyun.smsTemplateCodeDefault}
aliyun.ossBucketDefault=${aliyun.ossBucketDefault}
aliyun.ossEndpointDefault=${aliyun.ossEndpointDefault}

aliyunAccessKey=${aliyun.access.key}
aliyunSecretKey=${aliyun.secret.key}

aws.access.key=${aws.access.key}
aws.secret.key=${aws.secret.key}
aws.region=${aws.region}
accessKey=${aws.access.key}
secretKey=${aws.secret.key}

#\u9489\u9489
dingtalk.webhool.getorder_nochecked_warn=${dingtalk.webhool.getorder_nochecked_warn}
dingtalk.webhool.revorder_nopicked_warn=${dingtalk.webhool.revorder_nopicked_warn}


# \u4E1A\u52A1\u4E8B\u4EF6\u91C7\u96C6\u5BA2\u6237\u7AEF\u914D\u7F6E
## \u662F\u5426\u5F00\u542F\u91C7\u96C6
bizevcli.enable=${bizevcli.enable}
## \u5F53\u524D\u8FD0\u884C\u73AF\u5883
bizevcli.env=${bizevcli.env}
## \u5F53\u524D\u73AF\u5883\u4E0B\u7684\u670D\u52A1\u7AEFtoken\u53E3\u4EE4
bizevcli.token=${bizevcli.token}
## \u624B\u5DE5\u6307\u5B9A\u7684\u91C7\u96C6\u670D\u52A1\u8282\u70B9url
bizevcli.url=${bizevcli.url}


#JWT \u914D\u7F6E
## \u662F\u5426\u5F00\u542Fjwt
bizjwt.enable=${bizjwt.enable}
## \u9ED8\u8BA4iss
bizjwt.iss=${bizjwt.iss}
## \u9ED8\u8BA4aud
bizjwt.aud=${bizjwt.aud}
## \u9ED8\u8BA4\u4E0B\u6807field
bizjwt.field=${bizjwt.field}
## jwt\u7684\u79C1\u94A5
bizjwt.secret=${bizjwt.secret}
## \u9ED8\u8BA4\u8FC7\u671F\u65F6\u95F4\u95F4\u9694(\u5206\u949F)
bizjwt.ttl=${bizjwt.ttl}
## \u9ED8\u8BA4\u5237\u65B0\u8FC7\u671F\u65F6\u95F4\u95F4\u9694(\u5206\u949F)
bizjwt.refreshTtl=${bizjwt.refreshTtl}
## \u9ED8\u8BA4\u7B7E\u540D\u7B97\u6CD5
bizjwt.algo=${bizjwt.algo}
## \u9ED8\u8BA4\u6DFB\u52A0\u7684\u5B57\u6BB5
bizjwt.identifier=${bizjwt.identifier}
## \u5FC5\u586B\u7684claims\u5B57\u6BB5\u96C6\u5408
bizjwt.claimsRequired=${bizjwt.claimsRequired}

xxl.conf.admin.address=${xxl.conf.admin.address}
xxl.conf.env=${xxl.conf.env}
xxl.conf.access.token=${xxl.conf.access.token}
xxl.conf.mirrorfile=${xxl.conf.mirrorfile}

amqp.provider=${amqp.provider}

spring.rabbitmq.addresses= ${spring.rabbitmq.addresses}
#spring.rabbitmq.host=${spring.rabbitmq.host}
#spring.rabbitmq.port=${spring.rabbitmq.port}
spring.rabbitmq.username=${spring.rabbitmq.username}
spring.rabbitmq.password=${spring.rabbitmq.password}
# RabbitMQ \u9ED8\u8BA4\u7684\u7528\u6237\u540D\u548C\u5BC6\u7801\u90FD\u662Fguest \u800C\u865A\u62DF\u4E3B\u673A\u540D\u79F0\u662F "/"
# \u5982\u679C\u914D\u7F6E\u5176\u4ED6\u865A\u62DF\u4E3B\u673A\u5730\u5740\uFF0C\u9700\u8981\u9884\u5148\u7528\u7BA1\u63A7\u53F0\u6216\u8005\u56FE\u5F62\u754C\u9762\u521B\u5EFA \u56FE\u5F62\u754C\u9762\u5730\u5740 http://\u4E3B\u673A\u5730\u5740:15672
spring.rabbitmq.virtualhost=${spring.rabbitmq.virtualhost}

# \u662F\u5426\u542F\u7528\u53D1\u5E03\u8005\u786E\u8BA4 \u5177\u4F53\u786E\u8BA4\u56DE\u8C03\u5B9E\u73B0\u89C1\u4EE3\u7801
spring.rabbitmq.publisher-confirms=${spring.rabbitmq.publisher-confirms}
# \u662F\u5426\u542F\u7528\u53D1\u5E03\u8005\u8FD4\u56DE \u5177\u4F53\u8FD4\u56DE\u56DE\u8C03\u5B9E\u73B0\u89C1\u4EE3\u7801
spring.rabbitmq.publisher-returns=${spring.rabbitmq.publisher-returns}
# \u662F\u5426\u542F\u7528\u5F3A\u5236\u6D88\u606F \u4FDD\u8BC1\u6D88\u606F\u7684\u6709\u6548\u76D1\u542C
spring.rabbitmq.template.mandatory=${spring.rabbitmq.template.mandatory}

# \u4E3A\u4E86\u4FDD\u8BC1\u4FE1\u606F\u80FD\u591F\u88AB\u6B63\u786E\u6D88\u8D39,\u5EFA\u8BAE\u7B7E\u6536\u6A21\u5F0F\u8BBE\u7F6E\u4E3A\u624B\u5DE5\u7B7E\u6536,\u5E76\u5728\u4EE3\u7801\u4E2D\u5B9E\u73B0\u624B\u5DE5\u7B7E\u6536
spring.rabbitmq.listener.simple.acknowledge-mode=${spring.rabbitmq.listener.simple.acknowledge-mode}
# \u4FA6\u542C\u5668\u8C03\u7528\u8005\u7EBF\u7A0B\u7684\u6700\u5C0F\u6570\u91CF
spring.rabbitmq.listener.simple.concurrency=${spring.rabbitmq.listener.simple.concurrency}
# \u4FA6\u542C\u5668\u8C03\u7528\u8005\u7EBF\u7A0B\u7684\u6700\u5927\u6570\u91CF
spring.rabbitmq.listener.simple.max-concurrency=${spring.rabbitmq.listener.simple.max-concurrency}

es.set.netty.runtime.available.processors=${es.set.netty.runtime.available.processors}
spring.data.elasticsearch.repositories.enabled=${spring.data.elasticsearch.repositories.enabled}
spring.data.elasticsearch.client.reactive.endpoints=${spring.data.elasticsearch.client.reactive.endpoints}
spring.data.elasticsearch.cluster-name=${spring.data.elasticsearch.cluster-name}
es.save.enabled=${es.save.enabled}

opensearch.accessId=${opensearch.accessId}
opensearch.accessKey=${opensearch.accessKey}
opensearch.endpoint=${opensearch.endpoint}

aliyunxtrace.domain=${aliyunxtrace.domain}
aliyunxtrace.username=${aliyunxtrace.username}
aliyunxtrace.password=${aliyunxtrace.password}


aliyunodps.accessId=${aliyunodps.accessId}
aliyunodps.accessKey=${aliyunodps.accessKey}
aliyunodps.endpoint=${aliyunodps.endpoint}
aliyunodps.defaultProject=${aliyunodps.defaultProject}

crm.api.ip=${crm.api.ip}

# \u91D1\u8776EAS\u63A5\u53E3\u4FE1\u606F
kingDee.eas.baseUrl=**************:44442
kingDee.eas.username=\u63A5\u53E3\u4E13\u7528
kingDee.eas.password=a0147852
kingDee.eas.slnName=eas
kingDee.eas.dcName=CS20230113_146
kingDee.eas.language=12
kingDee.eas.dbType=1
kingDee.eas.authPattern=BaseDB

task.center.file.path=${task.center.file.path}
walmart.payment.path=${walmart.payment.path}

spring.redis.host=${spring.redis.host}
spring.redis.port=${spring.redis.port}
spring.redis.database=${spring.redis.database}
spring.redis.timeout=${spring.redis.timeout}
spring.redis.password=${spring.redis.password}
spring.redis.maxIdle=${spring.redis.maxIdle}
spring.redis.minIdle=${spring.redis.minIdle}
spring.redis.maxActive=${spring.redis.maxActive}
spring.redis.maxWait=${spring.redis.maxWait}

# \u5B81\u6CE2\u94F6\u884C
nbbank.enable=${nbbank.enable}
nbbank.custId=${nbbank.custId}
nbbank.appKey=${nbbank.appKey}
nbbank.publicUrl=${nbbank.publicUrl}
nbbank.publicKey=${nbbank.publicKey}
nbbank.privateKey=${nbbank.privateKey}
nbbank.connectTimeout=${nbbank.connectTimeout}
nbbank.readTimeout=${nbbank.readTimeout}
nbbank.isProxy=${nbbank.isProxy}
nbbank.proxyIp=${nbbank.proxyIp}
nbbank.proxyPort=${nbbank.proxyPort}

# \u4F01\u4E1A\u5FAE\u4FE1
third.qywx.corpId=${third.qywx.corpId}
third.qywx.token=${third.qywx.token}
third.qywx.encodingAESKey=${third.qywx.encodingAESKey}

inventory.sc.path=${inventory.sc.path}

# payoneer
third.payoneer.enable=${third.payoneer.enable}
third.payoneer.programId=${third.payoneer.programId}
third.payoneer.clientId=${third.payoneer.clientId}
third.payoneer.clientSecret=${third.payoneer.clientSecret}
third.payoneer.baseUrl=${third.payoneer.baseUrl}
third.payoneer.redirectUrl=${third.payoneer.redirectUrl}
third.payoneer.authorizationUrl=${third.payoneer.authorizationUrl}

# sunrate
third.sunrate.publicUrl=${third.sunrate.publicUrl}

home_query_bi_url=${home_query_bi_url}

promotion.advice.webhook=${promotion.advice.webhook}
wechat.boot.webhook.webhook-url=${wechat.boot.webhook.webhook-url}
wechat.boot.webhook.boot-tokens.FOLLOW=${wechat.boot.webhook.boot-tokens.FOLLOW}
wechat.boot.webhook.boot-tokens.ATD=${wechat.boot.webhook.boot-tokens.ATD}
wechat.boot.webhook.boot-tokens.PROFIT=${wechat.boot.webhook.boot-tokens.PROFIT}
wechat.boot.webhook.boot-tokens.PROFIT_OPERATION=${wechat.boot.webhook.boot-tokens.PROFIT_OPERATION}
wechat.boot.webhook.boot-tokens.PROFIT_OPERATION_EXTRA=${wechat.boot.webhook.boot-tokens.PROFIT_OPERATION_EXTRA}
wechat.boot.webhook.boot-tokens.ORDER_TRACK=${wechat.boot.webhook.boot-tokens.ORDER_TRACK}
wechat.boot.webhook.boot-tokens.PROFIT_FILFULLMENT=${wechat.boot.webhook.boot-tokens.PROFIT_FILFULLMENT}
wechat.boot.webhook.boot-tokens.TEMU_PROMOTION=${wechat.boot.webhook.boot-tokens.TEMU_PROMOTION}
wechat.boot.webhook.boot-tokens.WALMART_FEE=${wechat.boot.webhook.boot-tokens.WALMART_FEE}
wechat.boot.webhook.boot-tokens.SEND_SAVE_STORE_NAME=${wechat.boot.webhook.boot-tokens.SEND_SAVE_STORE_NAME}

fin_bi_export_url=${fin_bi_export_url}
ad.tiktok.request=${ad.tiktok.request}
transition.file.url=${transition.file.url}
temu.flow.analyse.url=${temu.flow.analyse.url}
rpa_query_url=${rpa_query_url}
algorithm_query_url=${algorithm_query_url}
algorithm_query_classify_question_url=${algorithm_query_classify_question_url}
wms.base.url=${wms.base.url}
reissue.buy.label.url=${reissue.buy.label.url}
