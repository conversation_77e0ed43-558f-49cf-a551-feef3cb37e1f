<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <artifactId>bizark-op</artifactId>
        <groupId>com.bizark</groupId>
        <version>1.0.4-SNAPSHOT</version>
    </parent>
    <name>bizark-op-service</name>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>bizark-op-service</artifactId>
    <packaging>jar</packaging>
    <url>http://maven.apache.org</url>
    <dependencies>
        <!-- 序列服务 -->
        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-boss-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-activiti-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-report-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- COMMON -->
        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-op-common</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.ebaycalls</groupId>
            <artifactId>ebaycalls</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.ebay</groupId>
            <artifactId>sdkcore</artifactId>
            <version>1.0</version>
        </dependency>

        <!--对外的 API 接口-->
        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-op-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-framework</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>dubbo</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Base Service -->
        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>base-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazon.sellingpartnerapi</groupId>
            <artifactId>sellingpartnerapi-aa-java</artifactId>
            <version>1.1</version>
        </dependency>
        <dependency>
            <groupId>com.amazon.sellingpartnerapi</groupId>
            <artifactId>sellingpartner-api-documents-helper-java</artifactId>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>bizark.amz</groupId>
            <artifactId>bizark-amz</artifactId>
            <version>1.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.odps</groupId>
            <artifactId>odps-sdk-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-nop</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aliyun.odps</groupId>
            <artifactId>odps-sdk-mapred</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-nop</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aliyun.odps</groupId>
            <artifactId>odps-sdk-graph</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-nop</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--Hutool工具包-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <!--<version>4.5.10</version>-->
        </dependency>
        <!--Hutool工具ExcelUtil依赖这个 https://mvnrepository.com/artifact/org.apache.poi/poi-ooxml-->
        <!--<dependency>-->
            <!--<groupId>org.apache.poi</groupId>-->
            <!--<artifactId>poi-ooxml</artifactId>-->
            <!--<version>4.1.0</version>-->
        <!--</dependency>-->
        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <!--<version>2.6.0</version>-->
        </dependency>
        <!--<dependency>-->
            <!--<groupId>dom4j</groupId>-->
            <!--<artifactId>dom4j</artifactId>-->
            <!--<version>1.6.1</version>-->
        <!--</dependency>-->

        <!--<dependency>-->
            <!--<groupId>com.aliyun.odps</groupId>-->
            <!--<artifactId>cupid-sdk</artifactId>-->
            <!--<scope>provided</scope>-->
            <!--<exclusions>-->
                <!--<exclusion>-->
                    <!--<artifactId>slf4j-nop</artifactId>-->
                    <!--<groupId>org.slf4j</groupId>-->
                <!--</exclusion>-->
            <!--</exclusions>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>com.aliyun.odps</groupId>-->
            <!--<artifactId>odps-spark-datasource_2.11</artifactId>-->
            <!--<exclusions>-->
                <!--<exclusion>-->
                    <!--<artifactId>slf4j-nop</artifactId>-->
                    <!--<groupId>org.slf4j</groupId>-->
                <!--</exclusion>-->
            <!--</exclusions>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-dataworks-public</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-nop</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <!--Servlet-->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>cglib</groupId>
            <artifactId>cglib</artifactId>
        </dependency>

        <!-- Spring -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <exclusions>
                <!-- Exclude Commons Logging in favor of SLF4j -->
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <exclusions>
                <!-- Exclude Commons Logging in favor of SLF4j -->
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <!--<dependency>-->
            <!--<groupId>org.slf4j</groupId>-->
            <!--<artifactId>log4j-over-slf4j</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <dependency>
            <groupId>org.logback-extensions</groupId>
            <artifactId>logback-ext-spring</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!--mybatis plus extension,包含了mybatis plus core-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <!--mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.mybatis</groupId>-->
<!--            <artifactId>mybatis</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.mybatis</groupId>-->
<!--            <artifactId>mybatis-spring</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.mybatis.caches</groupId>-->
<!--            <artifactId>mybatis-ehcache</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.jdom</groupId>
            <artifactId>jdom</artifactId>
            <version>${jdom-v1.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-rabbit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.mq-amqp</groupId>
            <artifactId>mq-amqp-client</artifactId>
        </dependency>

        <!-- dubbo -->
        <!--        <dependency>-->
        <!--            <groupId>com.alibaba</groupId>-->
        <!--            <artifactId>dubbo</artifactId>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <artifactId>spring</artifactId>-->
        <!--                    <groupId>org.springframework</groupId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>log4j</groupId>-->
        <!--                    <artifactId>log4j</artifactId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-dependencies-zookeeper</artifactId>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>com.github.briandilley.jsonrpc4j</groupId>
            <artifactId>jsonrpc4j</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-client</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.qianmi</groupId>-->
<!--            <artifactId>dubbo-rpc-jsonrpc</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
        </dependency>

        <dependency>
            <groupId>com.101tec</groupId>
            <artifactId>zkclient</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.esotericsoftware.yamlbeans</groupId>
            <artifactId>yamlbeans</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>


        <!--CSV读写-->
        <dependency>
            <groupId>net.sourceforge.javacsv</groupId>
            <artifactId>javacsv</artifactId>
        </dependency>
        <!--钉钉-->
        <dependency>
            <groupId>com.alibaba.dingtalk</groupId>
            <artifactId>chatbot</artifactId>
        </dependency>
        <!--阿里大鱼短信通道-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
        </dependency>
        <!--邮件推送通道-->
        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>javax.mail-api</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.sun.mail</groupId>-->
<!--            <artifactId>javax.mail</artifactId>-->
<!--        </dependency>-->
        <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-email -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-email</artifactId>
            <version>1.5</version>
        </dependency>

        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>jakarta.mail</artifactId>
        </dependency>

        <!-- COMMON -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-orm</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>amazon-sqs-java-messaging-lib</artifactId>
        </dependency>
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
        </dependency>
        <dependency>
            <groupId>jaxen</groupId>
            <artifactId>jaxen</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.inject</groupId>
            <artifactId>javax.inject</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.mns</groupId>
            <artifactId>aliyun-sdk-mns</artifactId>
            <classifier>jar-with-dependencies</classifier>
        </dependency>
        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizeventcollect</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.mybatis</groupId>-->
<!--            <artifactId>mybatis-typehandlers-jsr310</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-math3</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-spring</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-ehcache</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mariuszgromada.math</groupId>
            <artifactId>MathParser.org-mXparser</artifactId>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-entitymanager</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-envers</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-commons</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.springframework.data/spring-data-elasticsearch -->
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-elasticsearch</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>netty-buffer</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-codec</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-codec-http</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-common</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-handler</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-resolver</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-transport</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.wenhao</groupId>
            <artifactId>jpa-spec</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.javax.persistence</groupId>
                    <artifactId>hibernate-jpa-2.1-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-data-jpa</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-jpa</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>

        <!-- JSR303 Bean Validator -->
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.debop</groupId>
            <artifactId>hibernate-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
            <version>2.2.3.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>3.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-batch</artifactId>
        </dependency>

        <!--query dsl -->
<!--        <dependency>-->
<!--            <groupId>com.querydsl</groupId>-->
<!--            <artifactId>querydsl-apt</artifactId>-->
<!--            <scope>provided</scope>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.querydsl</groupId>-->
<!--            <artifactId>querydsl-jpa</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.querydsl</groupId>-->
<!--            <artifactId>querydsl-sql</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.querydsl</groupId>-->
<!--            <artifactId>querydsl-sql-codegen</artifactId>-->
<!--            <scope>provided</scope>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>${apache-tomcat-embed.version}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-jasper</artifactId>
            <version>${apache-tomcat-embed.version}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-jasper</artifactId>
            <version>${apache-tomcat-embed.version}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-jasper-el</artifactId>
            <version>${apache-tomcat-embed.version}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-handler</artifactId>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-buffer</artifactId>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-transport</artifactId>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-common</artifactId>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-codec-http</artifactId>
        </dependency>
        <dependency>
            <groupId>tech.tablesaw</groupId>
            <artifactId>tablesaw-core</artifactId>
        </dependency>
        <dependency>
            <groupId>tech.tablesaw</groupId>
            <artifactId>tablesaw-jsplot</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.odps</groupId>
            <artifactId>odps-sdk-udf</artifactId>
        </dependency>

        <!--itext处理pdf依赖  -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.13</version>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>
<!--        PdfConvert类，处理word转pdf-->
        <dependency>
            <groupId>fr.opensagres.xdocreport</groupId>
            <artifactId>org.apache.poi.xwpf.converter.pdf</artifactId>
            <version>1.0.4</version>
        </dependency>

        <!--     docx4j配合itext实现docx转pdf-->
        <dependency>
            <groupId>org.docx4j</groupId>
            <artifactId>docx4j</artifactId>
            <version>6.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.docx4j</groupId>
            <artifactId>docx4j-export-fo</artifactId>
            <version>6.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.htmlparser</groupId>
            <artifactId>htmlparser</artifactId>
            <version>2.1</version>
        </dependency>

        <!--easyExcel-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.4</version>
        </dependency>
        <!-- PDF导出相关依赖 -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>${itextpdf.version}</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf.tool</groupId>
            <artifactId>xmlworker</artifactId>
            <version>${xmlworker.version}</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>${itext-asian.version}</version>
        </dependency>
        <dependency>
            <groupId>org.xhtmlrenderer</groupId>
            <artifactId>flying-saucer-pdf</artifactId>
            <version>${flying-saucer-pdf.version}</version>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>${freemarker.version}</version>
        </dependency>
        <!-- 生成码工具 -->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>${zxing.core}</version>
        </dependency>
        <!--zxing工具之javase-->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>${zxing.javase}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.24</version>
        </dependency>
    </dependencies>

    <build>
        <extensions>
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>${maven-os-maven-plugin.version}</version>
            </extension>
        </extensions>
        <finalName>bizark-op-service</finalName>
        <resources>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <targetPath>${project.build.directory}/classes/META-INF/spring</targetPath>
                <directory>src/main/resources/config</directory>
                <filtering>true</filtering>
                <includes>
                    <include>spring.xml</include>
                </includes>
            </resource>
<!--            <resource>-->
<!--                <targetPath>${project.build.directory}/classes/META-INF/dubbo</targetPath>-->
<!--                <directory>src/main/resources/config/dubbo</directory>-->
<!--                <filtering>true</filtering>-->
<!--                <includes>-->
<!--                    <include>com.alibaba.dubbo.rpc.Filter</include>-->
<!--                </includes>-->
<!--            </resource>-->
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>generatorConfig.xml</exclude>
                </excludes>
            </resource>
        </resources>
        <plugins>
<!--            <plugin>-->
<!--                <groupId>com.mysema.maven</groupId>-->
<!--                <artifactId>apt-maven-plugin</artifactId>-->
<!--                <version>${apt-maven-plugin.version}</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <goals>-->
<!--                            <goal>process</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <outputDirectory>target/generated-sources/java</outputDirectory>-->
<!--                            <processor>com.querydsl.apt.jpa.JPAAnnotationProcessor</processor>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
            <!--<plugin>-->
                <!--<groupId>com.querydsl</groupId>-->
                <!--<artifactId>querydsl-maven-plugin</artifactId>-->
                <!--<version>${querydsl.version}</version>-->
                <!--<executions>-->
                    <!--<execution>-->
                        <!--<goals>-->
                            <!--<goal>export</goal>-->
                        <!--</goals>-->
                    <!--</execution>-->
                <!--</executions>-->
                <!--<configuration>-->
                    <!--<jdbcDriver>com.mysql.jdbc.Driver</jdbcDriver>-->
                    <!--<jdbcUrl>****************************************************</jdbcUrl>-->
                    <!--<jdbcUsername>root</jdbcUsername>-->
                    <!--<jdbcPassword>root</jdbcPassword>-->
                    <!--<packageName>com.bizark.op.api.entity.op.gen</packageName>-->
                    <!--<targetFolder>${project.basedir}/target/generated-sources/java</targetFolder>-->
                <!--</configuration>-->
                <!--<dependencies>-->
                    <!--<dependency>-->
                        <!--<groupId>mysql</groupId>-->
                        <!--<artifactId>mysql-connector-java</artifactId>-->
                        <!--<version>${mysql-connector.version}</version>-->
                    <!--</dependency>-->
                <!--</dependencies>-->
            <!--</plugin>-->
            <!--<plugin>-->
                <!--<groupId>com.dessert</groupId>-->
                <!--<artifactId>mybatis-util-create-plugin</artifactId>-->
                <!--<version>${mybatis-util-create-plugin.version}</version>-->
                <!--<configuration>-->
                    <!--<jdbcURL>**********************************************************</jdbcURL>-->
                    <!--<jdbcUsername>root</jdbcUsername>-->
                    <!--<jdbcPassword>root</jdbcPassword>-->
                    <!--<modeTargetProject>src/main/java</modeTargetProject>-->
                    <!--<modeTargetPackage>com.bizark.op.service.generate.model</modeTargetPackage>-->
                    <!--<sqlTargetProject>src/main/resources/mapper</sqlTargetProject>-->
                    <!--<sqlTargetPackage>datasource.generate</sqlTargetPackage>-->
                    <!--<mapperTargetProject>src/main/java</mapperTargetProject>-->
                    <!--<mapperTargetPackage>com.bizark.op.service.generate.mapper</mapperTargetPackage>-->
                    <!--<baseMapperTargetPackage>com.bizark.dao.mapper.BaseMapper</baseMapperTargetPackage>-->
                    <!--<fieldnamingStrategy>1</fieldnamingStrategy>-->
                    <!--<validatorAnnotation>false</validatorAnnotation>-->
                    <!--<tableNames>uc_systems</tableNames>-->
                <!--</configuration>-->
            <!--</plugin>-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-war-plugin</artifactId>-->
<!--                <version>${maven-war-plugin.version}</version>-->
<!--                <configuration>-->
<!--                    <attachClasses>true</attachClasses>-->
<!--                    <classesClassifier>api</classesClassifier>-->
<!--                </configuration>-->
<!--            </plugin>-->
<!--            &lt;!&ndash;jetty容器&ndash;&gt;-->
<!--            <plugin>-->
<!--                <groupId>org.eclipse.jetty</groupId>-->
<!--                <artifactId>jetty-maven-plugin</artifactId>-->
<!--                <version>${jetty-maven-plugin.version}</version>-->
<!--                <configuration>-->
<!--                    <jvmArgs>-Xms1024m -Xmx1024m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m</jvmArgs>-->
<!--                    <stopKey>shutdown</stopKey>-->
<!--                    <stopPort>${erp-service-rpc-stop.port}</stopPort>-->
<!--                    <httpConnector>-->
<!--                        <port>${erp-service-rpc-http.port}</port>-->
<!--                    </httpConnector>-->
<!--                    <webApp>-->
<!--                        <contextPath>/</contextPath>-->
<!--                    </webApp>-->
<!--                    &lt;!&ndash;<reload>automatic</reload>&ndash;&gt;-->
<!--                    &lt;!&ndash;<reload>manual</reload>&ndash;&gt;-->
<!--                    &lt;!&ndash;<scanIntervalSeconds>2</scanIntervalSeconds>&ndash;&gt;-->
<!--                </configuration>-->
<!--            </plugin>-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot-maven-plugin.version}</version>
                <configuration>
                    <mainClass>${service-start-class}</mainClass>
<!--                    <jvmArguments>-Xms1024m -Xmx1024m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m</jvmArguments>-->
                </configuration>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <classifier>RUN</classifier>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.xolstice.maven.plugins</groupId>
                <artifactId>protobuf-maven-plugin</artifactId>
                <version>${maven-protobuf-maven-plugin.version}</version>
                <configuration>
                    <protocArtifact>com.google.protobuf:protoc:${protoc.version}:exe:${os.detected.classifier}</protocArtifact>
                    <pluginId>grpc-java</pluginId>
                    <pluginArtifact>io.grpc:protoc-gen-grpc-java:${grpc.version}:exe:${os.detected.classifier}</pluginArtifact>
                    <outputDirectory>build/generated/source/proto/main/java</outputDirectory>
                    <clearOutputDirectory>false</clearOutputDirectory>
                    <protocPlugins>
                        <protocPlugin>
                            <id>dubbo-grpc</id>
                            <groupId>org.apache.dubbo</groupId>
                            <artifactId>dubbo-compiler</artifactId>
                            <version>${dubbo.compiler.version}</version>
                            <mainClass>org.apache.dubbo.gen.grpc.DubboGrpcGenerator</mainClass>
                        </protocPlugin>
                    </protocPlugins>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>compile-custom</goal>
                            <goal>test-compile</goal>
                            <goal>test-compile-custom</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>${maven-build-helper-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>build/generated/source/proto/main/java</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <!--开发环境-->
        <profile>
            <id>dev</id>
            <!-- 默认激活本环境 -->
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <filters>
                    <filter>${project.basedir}/src/main/resources/properties/env_files/dev.properties</filter>
                </filters>
            </build>
        </profile>
        <!--测试环境-->
        <profile>
            <id>test</id>
            <build>
                <filters>
                    <filter>${project.basedir}/src/main/resources/properties/env_files/test.properties</filter>
                </filters>
            </build>
        </profile>
        <!--预发环境-->
        <profile>
            <id>stage</id>
            <build>
                <filters>
                    <filter>${project.basedir}/src/main/resources/properties/env_files/stage.properties</filter>
                </filters>
            </build>
        </profile>
        <!--正式生产环境-->
        <profile>
            <id>prod</id>
            <build>
                <filters>
                    <filter>${project.basedir}/src/main/resources/properties/env_files/prod.properties</filter>
                </filters>
            </build>
        </profile>
        <!--本地环境-->
        <profile>
            <id>local</id>
            <build>
                <filters>
                    <filter>${project.basedir}/src/main/resources/properties/env_files/local.properties</filter>
                </filters>
            </build>
        </profile>
    </profiles>
</project>
