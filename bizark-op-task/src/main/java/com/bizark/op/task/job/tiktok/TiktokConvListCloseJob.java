package com.bizark.op.task.job.tiktok;

import com.bizark.op.api.service.ticket.IScStationLetterService;
import com.bizark.op.service.service.tiktok.impl.TiktokMessageServiceImpl;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> @ClassName 关闭无效TK会话
 * @description:
 * @date 2023年11月21日
 */
@Component
public class TiktokConvListCloseJob implements SimpleJob {


    @Autowired
    private IScStationLetterService scStationLetterService;


    /**
     * @param
     * @param shardingContext
     * @description: 对历史会话进行关闭
     * @author: Moore
     * @date: 2023/11/21 14:29
     * @return: void
     **/
    @Override
    public void execute(ShardingContext shardingContext) {
        scStationLetterService.updateHisInvalidConJob();
    }
}
