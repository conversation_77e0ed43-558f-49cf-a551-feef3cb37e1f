package com.bizark.op.task.listener.wayfair;

import com.alibaba.fastjson.JSON;
import com.bizark.op.api.cons.MQDefine;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.entity.op.wayfair.WayfairInventorySummary;
import com.bizark.op.api.entity.op.wayfair.message.WayfairInventorySummaryMessage;
import com.bizark.op.api.service.log.ErpOperateLogService;
import com.bizark.op.api.service.sale.ProductChannelsService;
import com.bizark.op.api.service.wayfair.WayfairInventorySummaryService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class WayfairInventorySummaryListener {

    @Autowired
    private WayfairInventorySummaryService wayfairInventorySummaryService;

    @Autowired
    private ProductChannelsService productChannelsService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ErpOperateLogService erpOperateLogService;

    @RabbitListener(queues = MQDefine.WAYFAIR_INVENTORY_SUMMARY_QUEUE)
    public void process(@Payload Message message, Channel channel) throws IOException {
        String messageJson = new String(message.getBody(), StandardCharsets.UTF_8);

        log.info("Wayfair平台仓库存消息 - {}", messageJson);
        WayfairInventorySummaryMessage summaryMessage = JSON.parseObject(messageJson, WayfairInventorySummaryMessage.class);

        RLock lock = redissonClient.getLock(summaryMessage.uniqueKey());
        try {
            lock.lock(10, TimeUnit.SECONDS);
            WayfairInventorySummary inventorySummary = new WayfairInventorySummary();
            BeanUtils.copyProperties(summaryMessage, inventorySummary);
            inventorySummary.setId(null);
            wayfairInventorySummaryService.insertOrUpdate(inventorySummary);

            // 处理SKU映射记录更新日志
            handleChannelsAndLog(summaryMessage);

        } catch (Exception e) {
            log.info("Wayfair平台仓库存消息写入异常 - {}", messageJson, e);
        }finally {
            lock.unlock();
        }
    }


    private void handleChannelsAndLog(WayfairInventorySummaryMessage summaryMessage){

        if (Objects.isNull(summaryMessage.getInStockQty())) {
            return;
        }

        ProductChannels channels = productChannelsService.selectByAccountIdAndSellerSku(summaryMessage.getChannelId(), summaryMessage.getSupplierPartNumber());

        if (Objects.isNull(channels)) {
            return;
        }

        // 更新数据记录日志
        productChannelsService.lambdaUpdate()
                .eq(ProductChannels::getId, channels.getId())
                .set(ProductChannels::getAvailToSellQty, summaryMessage.getInStockQty())
                .update();

        ProductChannels copyBean = new ProductChannels();
        copyBean.setAvailToSellQty(summaryMessage.getInStockQty());
        erpOperateLogService.logRecord(channels, copyBean, "SKU映射", true, false, "availToSellQty");
    }

}
