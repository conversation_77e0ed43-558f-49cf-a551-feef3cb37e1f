package com.bizark.op.task.xxljob;

import com.bizark.op.api.service.mar.IMarDeliveryFeeClaimService;
import com.bizark.op.api.service.mar.MarLastMileFeeClaimService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;




/**
 * @ClassName MarDevliverFeeClaimUpdatePlatStatusJob
 * @Description 尾程费索赔
 * <AUTHOR>
 * @Date 2025/4/24 15:20
 */
@Component
@Slf4j
public class MarDevliverFeeClaimUpdatePlatStatusJob {

    @Autowired
    private MarLastMileFeeClaimService marLastMileFeeClaimService;

    @XxlJob("MarDevliverFeeClaimUpdatePlatStatusJob")
    public void marDevliverFeeClaimUpdateQtyJob() throws Exception {
        log.info("MarDevliverFeeClaimUpdatePlatStatusJob execute start");
        marLastMileFeeClaimService.planManulStatus();
        log.info("MarDevliverFeeClaimUpdatePlatStatusJob execute start");
    }
}

