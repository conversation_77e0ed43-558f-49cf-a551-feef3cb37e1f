package com.bizark.op.task.job.sale;

import com.bizark.op.api.service.sale.ProductChannelsService;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class WalmartWfsInventoryJob implements SimpleJob {

    @Autowired
    private ProductChannelsService productChannelsService;

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info(" ---- 开始同步平台仓库存 ----");
        long start = System.currentTimeMillis();
        try {
            productChannelsService.syncWalmartWfsInventory(null);
        } catch (Exception e) {
            log.info("Walmart平台仓库存同步失败", e);
        }
        log.info("Walmart平台仓库存同步完毕，耗时:{}", System.currentTimeMillis() - start);
    }
}
