package com.bizark.op.task.listener.mar;


import com.bizark.op.api.cons.MQDefine;
import com.bizark.op.api.service.mar.MarListingInfoService;
import com.bizark.op.api.service.mar.financialConstraints.MarFinancialConstraintsService;
import com.bizark.op.api.service.mar.MarShopServiceQualityService;
import com.bizark.op.api.service.mar.financialConstraints.MarFinancialConstraintsService;
import com.bizark.op.common.util.StringUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;


/**
 * @ClassName 接收LIsting信息队列
 * @Description
 * <AUTHOR>
 * @Date 2023/8/23 12:56
 */
@Slf4j
@Component
public class MarShopServiceQualityListener {

    @Autowired
    private MarShopServiceQualityService marShopServiceQualityService;



    @Resource
    private MarFinancialConstraintsService marFinancialConstraintsService;

   @RabbitListener(queues = MQDefine.MAR_SHOP_SERVICE_QUALITY_QUEUE)
    public void starReceiver(Message message, @Header(AmqpHeaders.CHANNEL) Channel channel,
                        @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws IOException {
        try {
            String messageStr = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("接受到店铺服务质量队列信息：{}", messageStr);
            if (StringUtils.isNotEmpty(messageStr)) {
                marShopServiceQualityService.saveShopServiceQuality(messageStr);
            }
        } catch (Exception e) {
            log.error("接受到店铺服务质量队列消息失败：消息：{} 原因：{}", new String(message.getBody(), StandardCharsets.UTF_8), e);
        }

    }


    @RabbitListener(queues = MQDefine.MAR_SHOP_PERFORMANCE_FINANCIAL_LIMIT_QUEUE)
    public void financialConstraintsReceiver(Message message, @Header(AmqpHeaders.CHANNEL) Channel channel,
                                             @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws IOException {
        try {
            String messageStr = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("接受到店铺绩效资金限制队列信息：{}", messageStr);
            if (StringUtils.isNotEmpty(messageStr)) {

                marFinancialConstraintsService.processFinancialConstraintsMessage(messageStr);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("接受到店铺绩效资金限制队列信息失败：消息：{} 原因：{}", new String(message.getBody(), StandardCharsets.UTF_8), e.getMessage());
        }

    }

    @RabbitListener(queues = MQDefine.MAR_SHOP_PERFORMANCE_FINANCIAL_LIMIT_ITEM_QUEUE)
    public void financialConstraintsItemReceiver(Message message, @Header(AmqpHeaders.CHANNEL) Channel channel,
                                             @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws IOException {
        try {
            String messageStr = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("接受到店铺绩效资金限制明细队列信息：{}", messageStr);
            if (StringUtils.isNotEmpty(messageStr)) {

                marFinancialConstraintsService.processFinancialConstraintsItemMessage(messageStr);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("接受到店铺绩效资金限制明细队列信息失败：消息：{} 原因：{}", new String(message.getBody(), StandardCharsets.UTF_8), e.getMessage());
        }

    }

}
