package com.bizark.op.task.job.inventory;

import cn.hutool.core.util.StrUtil;
import com.bizark.op.api.enm.sale.SaleChannelMappingEnum;
import com.bizark.op.api.service.inventory.ProductChannelsInventoryService;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Component
@Slf4j
public class InventorySyncJob implements SimpleJob {


    @Autowired
    private ProductChannelsInventoryService productChannelsInventoryService;

    @Override
    public void execute(ShardingContext shardingContext) {

        String jobParameter = shardingContext.getJobParameter();

        Integer orgId = null;


        log.info("平台库存任务开始执行- {} ", shardingContext.getJobName());

        if (StrUtil.isNotBlank(jobParameter)) {
            orgId = Integer.parseInt(jobParameter);
        }

        List<SaleChannelMappingEnum> enums = Arrays.asList(
                SaleChannelMappingEnum.WALMART,
                SaleChannelMappingEnum.WALMART_DSV,
                SaleChannelMappingEnum.SHOPIFY,
                SaleChannelMappingEnum.MICROSOFT,
                SaleChannelMappingEnum.TIKTOK,
                SaleChannelMappingEnum.SHEIN
        );
        for (SaleChannelMappingEnum type : enums) {
            try {
                log.info("SKU映射库存同步开始，当前同步渠道：{}", type.getName());
                productChannelsInventoryService.syncInventory(orgId, type.getName());
            } catch (Exception e) {
                log.error("SKU映射库存同步失败：{}", e.getMessage());
            }
        }

    }
}
