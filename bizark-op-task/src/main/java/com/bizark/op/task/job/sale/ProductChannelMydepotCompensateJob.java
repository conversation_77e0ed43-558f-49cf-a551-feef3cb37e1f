package com.bizark.op.task.job.sale;

import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.entity.op.sale.ProductChannelsMydepotError;
import com.bizark.op.api.service.sale.ProductChannelsMydepotErrorService;
import com.bizark.op.api.service.sale.ProductChannelsService;
import com.bizark.op.service.event.ProductChannelApprovalEvent;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@Slf4j
public class ProductChannelMydepotCompensateJob implements SimpleJob {

    @Autowired
    private ProductChannelsMydepotErrorService productChannelsMydepotErrorService;

    @Autowired
    private ProductChannelsService productChannelsService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;


    @Override
    public void execute(ShardingContext shardingContext) {

        ProductChannelsMydepotError error = null;
        Long cursor = null;

        while (Objects.nonNull(error = productChannelsMydepotErrorService.selectNext(cursor))) {
            try {
                cursor = error.getId();

                ProductChannels channels = productChannelsService.getById(error.getProductChannelId());
                if (Objects.isNull(channels)) {
                    continue;
                }

                ProductChannelApprovalEvent event = new ProductChannelApprovalEvent(this, channels);
                applicationEventPublisher.publishEvent(event);
            } finally {
                if (Objects.nonNull(cursor)) {
                    productChannelsMydepotErrorService.removeById(cursor);
                }
            }
        }

    }
}
