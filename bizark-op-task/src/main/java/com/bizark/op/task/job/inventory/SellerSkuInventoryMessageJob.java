package com.bizark.op.task.job.inventory;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.bizark.op.api.entity.op.inventory.conf.vo.SellerSkuInventoryTaskMessage;
import com.bizark.op.api.service.sale.SellerSkuInventoryTaskMessageService;
import com.bizark.op.api.service.task.PublishTaskService;
import com.bizark.op.common.config.WeChatBootConfigure;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xxl.conf.core.annotation.XxlConf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SellerSkuInventoryMessageJob implements SimpleJob {

    @Autowired
    private SellerSkuInventoryTaskMessageService skuInventoryTaskMessageService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private WeChatBootConfigure weChatBootConfigure;


    public static final String INVENTORY_COUNT_KEY = "inventory:task:count";

    @XxlConf(value = "bizark-multichannel.inventory.message.batch")
    public static String INVENTORY_TASK_MEESAGE_BATCH;

    @Autowired
    private PublishTaskService publishTaskService;
    @Override
    public void execute(ShardingContext shardingContext) {

        int limit = 100;
        if (StrUtil.isNotBlank(INVENTORY_TASK_MEESAGE_BATCH)) {
            limit = Integer.parseInt(INVENTORY_TASK_MEESAGE_BATCH);
        }
        long start = System.currentTimeMillis();
        log.info("SellerSkuInventoryMessageJob - 开始处理库存推送消息 - 当前处理批次数量:{}", limit);
        List<SellerSkuInventoryTaskMessage> messages = skuInventoryTaskMessageService.selectInventoryTaskMessageLimit(limit);
        if (CollectionUtil.isNotEmpty(messages)) {

            try {
                publishTaskService.handlePushMessage(messages);
            }finally {
                // 删除
                List<Long> ids = messages.stream().map(SellerSkuInventoryTaskMessage::getId).collect(Collectors.toList());
                skuInventoryTaskMessageService.removeByIds(ids);
            }
        }
        log.info("SellerSkuInventoryMessageJob - 本次处理完毕 - 当前处理批次数量:{} 耗时:{}", limit, System.currentTimeMillis() - start);

        // 查询消息数量
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(INVENTORY_COUNT_KEY))) {
            return;
        }
        // 直接设置标记
        stringRedisTemplate.opsForValue().set(INVENTORY_COUNT_KEY, "1", 20, TimeUnit.MINUTES);

        Integer count = skuInventoryTaskMessageService.lambdaQuery()
                .count();
        //log.info("SellerSkuInventoryMessageJob - 当前消息数量 {}", count);
//        if (count > 100000) {
//            String notifyMessage = "库存推送消息 - 当前消息堆积数量:" + count;
//            WeComRobotUtil.sendTextMsgWithAtNew(notifyMessage, "Annie,Chengfei", "", "INVENTORY_PUSH", weChatBootConfigure);
//        }

    }
}
