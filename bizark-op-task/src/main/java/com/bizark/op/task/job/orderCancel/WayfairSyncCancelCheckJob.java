package com.bizark.op.task.job.orderCancel;

import com.bizark.op.api.service.order.SaleOrderCancelService;
import com.bizark.op.common.util.DateUtils;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName 获取wayfair撤回数据
 * @Description
 * <AUTHOR>
 * @Date 2024/8/12 18:48
 */
@Component
@Slf4j
public class WayfairSyncCancelCheckJob implements SimpleJob {

    @Autowired
    private SaleOrderCancelService saleOrderCancelService;


    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("WayfairSyncCancelCheckJob start,当前时间:{}", DateUtils.getNowDate());
        saleOrderCancelService.getOrderWayfairCancel(null,1113);
        log.info("WayfairSyncCancelCheckJob end,当前时间:{}", DateUtils.getNowDate());
    }
}
