package com.bizark.op.web.controller.api.v2.promotions;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.bizark.common.contract.AuthUserDetails;
import com.bizark.common.util.JacksonUtils;
import com.bizark.op.api.annotation.ClusterFailFastLock;
import com.bizark.op.api.cons.PermDefine;
import com.bizark.op.api.entity.op.promotions.AmzPromotionsCoupons;
import com.bizark.op.api.entity.op.promotions.AmzPromotionsSkuDetail;
import com.bizark.op.api.exception.ErpCommonException;
import com.bizark.op.api.request.AmzPromotionsCouponCreateQuest;
import com.bizark.op.api.request.AmzPromotionsCouponQueryRequest;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.promotions.IAmzPromotionAccountService;
import com.bizark.op.api.service.promotions.IAmzPromotionAndCouponService;
import com.bizark.op.api.service.promotions.IAmzPromotionExportService;
import com.bizark.op.api.service.promotions.IAmzPromotionsCouponsService;
import com.bizark.op.api.vo.promotions.AmzPromotionsCouponSkuInfoVO;
import com.bizark.op.api.vo.promotions.AmzPromotionsCouponVO;
import com.bizark.op.api.vo.promotions.AmzPromotionsCouponsListVO;
import com.bizark.op.api.vo.promotions.couponhis.AmzHisCouponVo;
import com.bizark.op.common.constant.HttpStatus;
import com.bizark.op.common.core.page.PageData;
import com.bizark.op.common.core.page.TableDataInfo;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.common.util.PageHelperUtils;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.service.util.ExcelExport;
import com.bizark.op.web.controller.api.AbstractApiController;
import com.bizark.usercenter.api.parameter.role.SellerSkuVisualVo;
import com.bizark.usercenter.api.service.UcRoleService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.bizark.framework.web.view.ApiResponseResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 优惠券活动Controller
 *
 * <AUTHOR>
 * @date 2023-06-14
 */
@RestController
@RequestMapping("/api/v2/promotions/coupons")
@Slf4j
public class AmzPromotionsCouponsController extends AbstractApiController {
    @Autowired
    private IAmzPromotionsCouponsService amzPromotionsCouponsService;

    @Autowired
    protected AccountService accountService;

    @Autowired
    private IAmzPromotionAccountService amzPromotionAccountService;

    @Autowired
    private IAmzPromotionExportService amzPromotionExportService;

    @Autowired
    private UcRoleService ucRoleService;

    @Autowired
    private IAmzPromotionAndCouponService amzPromotionAndCouponService;

    /**
     * 查询优惠券活动列表
     */
    @GetMapping("/list")
//    @RequiresPermissions(PermDefine.PROMOTION_ACTIVITY_LIST)
    public TableDataInfo list(AmzPromotionsCouponQueryRequest request, Integer contextId) {

        if (request.getContextId() == null || request.getContextId() < 1) {
            request.setContextId(contextId);
        }
        try {
            if (new Integer(1000380).equals(contextId)) {
                // sellerSku数据权限获取
                SellerSkuVisualVo selectSellerSku = ucRoleService.selectSellerSku(contextId, getAuthUser(), PermDefine.VC_PROMOTION_INFO_MANAGE);
                logger.info("用户 {} 组织 {} 获取SellerSku权限数据 {}", getAuthUser().getEmail(), contextId, JacksonUtils.toJson(selectSellerSku));
                if (!selectSellerSku.getIsAll()) {
                    if (CollectionUtil.isNotEmpty(selectSellerSku.getSellerSkuList())) {
                        List<String> skuIdArray = request.getSkuIdArray();                 //判断查询是否传参
                        if (CollectionUtil.isNotEmpty(skuIdArray)){
                            skuIdArray.addAll(selectSellerSku.getSellerSkuList()); //设置权限SerllerSKu
                        }else {
                            skuIdArray = selectSellerSku.getSellerSkuList();
                        }
                        request.setSkuIdArray(skuIdArray);
                    }
                }
            }
        } catch (Exception e) {
            log.info("vc-coupon促销活动列表权限异常：{}", e);
        }

        startPage();
        PageInfo<AmzPromotionsCouponsListVO> list = amzPromotionsCouponsService.selectCouponsList(request);
        return PageHelperUtils.getPageInfo((list == null || CollectionUtil.isEmpty(list.getList())) ? new PageInfo<AmzPromotionsCouponsListVO>() : list);
    }


    @GetMapping(value = "/{id}")
//    @RequiresPermissions(PermDefine.PROMOTION_ACTIVITY_QUERY)
    public ApiResponseResult getInfo(@PathVariable("id") Long id) {
        return ApiResponseResult.buildSuccessResult(amzPromotionsCouponsService.selectAmzPromotionsCouponsById(id));
    }
    /**
     * 获取优惠券活动详细信息
     */
    @GetMapping(value = "/detail/{couponsId}")
//    @RequiresPermissions(PermDefine.PROMOTION_ACTIVITY_QUERY)
    public ApiResponseResult getDetailInfo(@PathVariable("couponsId") Long couponsId) {
        return ApiResponseResult.buildSuccessResult(amzPromotionsCouponsService.selectAmzPromotionsCouponsDetailById(couponsId));
    }

    @GetMapping(value = "/otherPromotion/{id}")
    public ApiResponseResult otherPromotion(@PathVariable("id") Long id) {
        return ApiResponseResult.buildSuccessResult(amzPromotionAndCouponService.getOtherPromotions(id,"coupon"));
    }


    /**
     * excel导入创建coupon
     *
     * @param request
     * @param file
     * @param contextId
     * @return
     */
    @PostMapping("/import")
    @ClusterFailFastLock("'AmzPromotionsCouponsController:import:contextId:'+#contextId")
//    @RequiresPermissions({PermDefine.PROMOTION_ACTIVITY_CREATE,PermDefine.PROMOTION_ACTIVITY_MODIFY,PermDefine.PROMOTION_ACTIVITY_REUPLOAD})
    public ApiResponseResult addCouponByImport(
             AmzPromotionsCouponCreateQuest request, MultipartFile file,  Integer contextId) {

        if (contextId == null || contextId < 1) {
            contextId = getAuthUserEntity().getOrgId();
        }
        request.setOrganizationId(contextId);
        AuthUserDetails thisUser = getAuthUser();
        request.setUpdatedBy(thisUser.getId());
        request.setUpdatedName(thisUser.getName());
        request.setUpdatedAt(DateUtils.getNowDate());
        if (request.getUpdateType().equals(1)) {
            request.setCreatedBy(thisUser.getId());
            request.setCreatedName(thisUser.getName());
            request.setCreatedAt(DateUtils.getNowDate());
        }
        if (file == null) {
            return ApiResponseResult.buildFailureResult("请上传文件");
        } else {
            String originalFilename = file.getOriginalFilename();
            if (StringUtil.isEmpty(originalFilename)) {
                return ApiResponseResult.buildFailureResult("文件名为空");
            } else {
                String suffix = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
                if (!suffix.equalsIgnoreCase("xlsx") && !suffix.equalsIgnoreCase("xls")) {
                    return ApiResponseResult.buildFailureResult("请选择excel文件");
                }
            }
        }
        if (request.getUploadType() == null) {
            return ApiResponseResult.buildFailureResult("请选择coupon类型");
        } else {
            if (request.getUploadType() == 0 || request.getUploadType() == 1) {
                if (StringUtil.isEmpty(request.getVendorCode())) {
                    return ApiResponseResult.buildFailureResult("Vendor Code必填");
                }
            }
        }
        amzPromotionsCouponsService.saveCoupon(request, file);
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * 创建单个coupon
     *
     * @param request
     * @param contextId
     * @return
     */
    @PostMapping("/single")
    @ClusterFailFastLock("'AmzPromotionsCouponsController:import:contextId:'+#contextId")
//    @RequiresPermissions({PermDefine.PROMOTION_ACTIVITY_CREATE,PermDefine.PROMOTION_ACTIVITY_MODIFY,PermDefine.PROMOTION_ACTIVITY_REUPLOAD})
    public ApiResponseResult addCouponByNoImport(
            @RequestBody AmzPromotionsCouponCreateQuest request, @RequestParam Integer contextId) {

        request.setOrganizationId(contextId);
        request.settingDefaultUpdate();
        if (request.getUpdateType() == 1) {
           request.settingDefaultCreate();
        }
        amzPromotionsCouponsService.saveCoupon(request, null);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * 根据店铺id,sku或者asin查询coupon的sku信息
     *
     * @param shopId
     * @param skus
     * @return
     */
    @GetMapping("/sku")
    public TableDataInfo getSkuListByShopId(@RequestParam("shopId") Long shopId, @RequestParam(value = "skus", required = false) String[] skus, @RequestParam(value = "contextId", required = false) Integer contextId) {
        startPage();
        //查询出sku信息
        List<AmzPromotionsCouponSkuInfoVO> skuInfoVOS = amzPromotionAccountService.selectCouponSellerSkuByShopIdAndSkuOrAsin(shopId, skus);
        return getDataTable(skuInfoVOS);
    }

    /**
     * 根据店铺id,sku查询promotions的sku信息
     *
     * @param shopId
     * @param skus
     * @return
     */
    @GetMapping("/promotionSku")
    public TableDataInfo getPromotionsSkuListByShopId(@RequestParam("shopId") Integer shopId, @RequestParam(value = "skus", required = false) String[] skus, @RequestParam(value = "contextId", required = false) Integer contextId) {
        startPage();
        //查询出sku信息
        List<AmzPromotionsSkuDetail> skuInfoVOS = amzPromotionAccountService.selectPromotionSellerSkuByShopId(shopId, skus);
        return getDataTable(skuInfoVOS);
    }


    /**
     * 提交异常直接提交
     *
     * @param ids
     * @param couponId
     * @param createdName
     * @return
     */
    @GetMapping("/submitException")
    @ClusterFailFastLock("'AmzPromotionsCouponsController:submitException:'+#couponId")
//    @RequiresPermissions({PermDefine.PROMOTION_ACTIVITY_SUBMIT,PermDefine.PROMOTION_ACTIVITY_MODIFY})
    public ApiResponseResult submitExceptionSubmit(@RequestParam("ids") Long[] ids, @RequestParam("couponId") Long couponId,
                                                   @RequestParam(value = "createdName", required = false) String createdName, @RequestParam("updateType") Integer updateType,
                                                   @RequestParam(value = "amzFrontName", required = false) String amzFrontName, @RequestParam(value = "budget", required = false) String budget,
                                                   @RequestParam(value = "contextId") Integer contextId,@RequestParam(value = "remark",required = false)String remark) {

        AuthUserDetails thisUser = getAuthUser();
        if (StringUtil.isEmpty(createdName)) {
            createdName = thisUser.getName();
        }
        Integer updatedBy = thisUser.getId();
        if (updateType == 1) {
            amzPromotionsCouponsService.submitExceptionSubmit(ids, couponId, createdName, contextId, updatedBy);
        } else if (updateType == 2) {
            if (ids == null || ids.length < 1) {
                return ApiResponseResult.buildFailureResult("优惠券id为空,提交失败");
            }
            amzPromotionsCouponsService.activeModifyExceptionSubmit(couponId, ids[0], amzFrontName, budget, createdName, contextId, updatedBy,remark);
        } else {
            return ApiResponseResult.buildFailureResult("提交失败");
        }
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * 取消coupon活动
     *
     * @param id
     * @return
     */
    @GetMapping("/cancelCoupons")
    @ClusterFailFastLock("'AmzPromotionsCouponsController:cancelCoupons:'+#id")
//    @RequiresPermissions(PermDefine.PROMOTION_ACTIVITYCOUPON_CANCEL)
    public ApiResponseResult cancelCoupons(@RequestParam("id") Long id, @RequestParam(value = "createdName", required = false) String createdName, @RequestParam(value = "contextId") Integer contextId,@RequestParam(value = "remark",required = false)String remark) {

        if (StringUtil.isEmpty(createdName)) {
            createdName = getAuthUserEntity().getName();
        }
        Integer updatedBy = getAuthUserEntity().getId();
        int result = amzPromotionsCouponsService.cancelCouponsById(id, createdName, contextId, updatedBy,remark);
        if (result > 0) {
            return ApiResponseResult.buildSuccessResult();
        } else {
            return ApiResponseResult.buildFailureResult("取消失败");
        }
    }

    /**
     * 下载源文件
     *
     * @param contextId
     */
    @GetMapping("/exportOriginalFile")
    public void exportOriginalFile(@RequestParam(value = "contextId", required = false) Integer contextId, @RequestParam("id") Long couponsId, HttpServletResponse response) {

        amzPromotionsCouponsService.downOriginalFile(couponsId, response);
    }

    @GetMapping("/exportCoupons")
//    @RequiresPermissions(PermDefine.PROMOTION_ACTIVITY_LIST)
    public ApiResponseResult exportAll(@RequestParam(value = "contextId", required = false) Integer contextId, AmzPromotionsCouponQueryRequest request, HttpServletResponse response) {

        if (contextId == null || contextId < 1) {
            contextId = getAuthUserEntity().getOrgId();
        }
        if (request.getContextId() == null || request.getContextId() < 1) {
            request.setContextId(contextId);
        }

        try {
            if (new Integer(1000380).equals(contextId)) {
                // sellerSku数据权限获取
                SellerSkuVisualVo selectSellerSku = ucRoleService.selectSellerSku(contextId, getAuthUser(), PermDefine.VC_PROMOTION_INFO_MANAGE);
                logger.info("用户 {} 组织 {} 获取SellerSku权限数据 {}", getAuthUser().getEmail(), contextId, JacksonUtils.toJson(selectSellerSku));
                if (!selectSellerSku.getIsAll()) {
                    if (CollectionUtil.isNotEmpty(selectSellerSku.getSellerSkuList())) {
                        List<String> skuIdArray = request.getSkuIdArray();                 //判断查询是否传参
                        if (CollectionUtil.isNotEmpty(skuIdArray)){
                            skuIdArray.addAll(selectSellerSku.getSellerSkuList()); //设置权限SerllerSKu
                        }else {
                            skuIdArray = selectSellerSku.getSellerSkuList();
                        }
                        request.setSkuIdArray(skuIdArray);
                    }
                }
            }
        } catch (Exception e) {
            log.info("vc-coupon促销活动列表权限异常：{}", e);
        }
        amzPromotionExportService.originalCouponExport(request, getAuthUserEntity());
        return ApiResponseResult.buildSuccessResult("导出已生成，请至【任务中心】-【任务列表】查看处理进度及下载文件！");
    }

    @GetMapping("/getAllCoupons")
    public TableDataInfo getAllCoupons(@RequestParam(value = "contextId", required = false) Integer contextId) {
        List<AmzHisCouponVo> allCoupons = amzPromotionsCouponsService.getAllCoupons();
        return getDataTable(allCoupons);
    }



    @GetMapping("/exportCouponsStr")
    public ApiResponseResult exportAllString(@RequestParam(value = "contextId", required = false) Integer contextId, AmzPromotionsCouponQueryRequest request, HttpServletResponse response) {

        if (contextId == null || contextId < 1) {
            contextId = getAuthUserEntity().getOrgId();
        }
        if (request.getContextId() == null || request.getContextId() < 1) {
            request.setContextId(contextId);
        }


        return ApiResponseResult.buildSuccessResult(amzPromotionExportService.asyncExportCouponList(JSON.toJSONString(request), request.getContextId()));
    }

    @GetMapping("/modifyRemark")
    public ApiResponseResult modifyRemark(@RequestParam("id") Long id, @RequestParam(value = "remark") String remark) {

        amzPromotionsCouponsService.lambdaUpdate().set(AmzPromotionsCoupons::getRemark, remark).eq(AmzPromotionsCoupons::getId, id).update();
        return ApiResponseResult.buildSuccessResult();
    }
}
