package com.bizark.op.web.controller.api.v2.gpt;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.bizark.op.api.cons.PermDefine;
import com.bizark.op.api.entity.op.gpt.*;
import com.bizark.op.api.entity.op.gpt.v2.*;
import com.bizark.op.api.entity.op.translate.BaiduTranslateRequest;
import com.bizark.op.api.entity.op.translate.BaiduTranslateResponse;
import com.bizark.op.api.service.gpt.GptRecordHistoryService;
import com.bizark.op.api.service.gpt.GptTextRecordService;
import com.bizark.op.api.service.gpt.v2.GptListingLogService;
import com.bizark.op.common.core.page.TableDataInfo;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.service.api.TranslateApi;
import com.bizark.op.web.controller.api.AbstractApiController;
import com.bizark.framework.web.view.ApiResponseResult;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;


@RestController
@RequestMapping("/api/v2/gpt")
public class ChatGptController extends AbstractApiController {

    @Autowired
    private GptTextRecordService gptTextRecordService;

    @Autowired
    private TranslateApi translateApi;

    @Autowired
    private GptListingLogService gptListingLogService;

    @Autowired
    private GptRecordHistoryService gptRecordHistoryService;

    @PostMapping("/listingText")
    @RequiresPermissions(PermDefine.ERP_TOOL_GPT)
    public ApiResponseResult listingText(@Valid @RequestBody ListingAsinExtractReq extractReq,@RequestParam("contextId") Integer contextId){
        List<GptTextResponse> textResponses = gptTextRecordService.listingText(extractReq, contextId);
        return ApiResponseResult.buildSuccessResult(textResponses);
    }

    @PostMapping("/amazonKeyword")
    @RequiresPermissions(PermDefine.ERP_TOOL_GPT)
    public ApiResponseResult amazonKeyword(@RequestBody GptAmzKeywordRequest keywordRequest,@RequestParam("contextId") Integer contextId){
        List<GptTextRecordVO> record = gptTextRecordService.amazonKeyword(keywordRequest,contextId);
        return ApiResponseResult.buildSuccessResult(record);
    }

    @PostMapping("/reviewAnalyze")
    @RequiresPermissions(PermDefine.ERP_TOOL_GPT)
    public ApiResponseResult reviewAnalyze(@RequestBody ReviewAnalyzeRequest request, @RequestParam("contextId") Integer contextId) {
        List<GptTextRecordVO> responses = gptTextRecordService.reviewAnalyze(request, contextId);
        return ApiResponseResult.buildSuccessResult(responses);
    }


    @PostMapping("/talk")
    @RequiresPermissions(PermDefine.ERP_TOOL_GPT)
    public ApiResponseResult talk(@RequestBody GptTalkContent content,@RequestParam("contextId") Integer contextId) {
        GptTalkResponse response = gptTextRecordService.talk(content, contextId);
        return ApiResponseResult.buildSuccessResult(response);
    }

    @PostMapping("/save")
    @RequiresPermissions(PermDefine.ERP_TOOL_GPT)
    public ApiResponseResult saveRecord(@RequestBody List<GptTextRecord> records, @RequestParam("contextId") Integer contextId) {
        gptTextRecordService.saveRecord(contextId,records);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/history")
    @RequiresPermissions(PermDefine.ERP_TOOL_GPT)
    public TableDataInfo history(GptRecordHistory history,Integer contextId) {
        startPage();
        history.setOrgId(contextId);
        List<GptRecordHistory> histories = gptRecordHistoryService.selectHistory(history);
        return getDataTable(histories);
    }


    @GetMapping("/history/detail")
    @RequiresPermissions(PermDefine.ERP_TOOL_GPT)
    public ApiResponseResult getHistoryDetail(@RequestParam("id") Long id) {
        List<GptTextRecord> textRecords = gptTextRecordService.lambdaQuery()
                .eq(GptTextRecord::getHistoryId, id)
                .list();
        return ApiResponseResult.buildSuccessResult(textRecords);
    }

    @PostMapping("/translate")
    @RequiresPermissions(PermDefine.ERP_TOOL_GPT)
    public ApiResponseResult translate(@RequestBody BaiduTranslateRequest request){
        BaiduTranslateResponse response = translateApi.translate(request);

        return Objects.isNull(response) ? ApiResponseResult.buildFailureResult("翻译失败")
                : StringUtil.isNotEmpty(response.getErrorMsg()) ? ApiResponseResult.buildFailureResult(response.getErrorMsg())
                : CollectionUtil.isEmpty(response.getTransResult()) ? ApiResponseResult.buildFailureResult("翻译失败")
                : ApiResponseResult.buildSuccessResult(response.getTransResult().get(0));
    }


    @PostMapping("/emailAiResponse")
    @RequiresPermissions(PermDefine.ERP_TOOL_GPT)
    public ApiResponseResult emailAiResponse(@RequestBody AiEmailEntity request) {
        gptTextRecordService.emailAiResponse(request);
        return ApiResponseResult.buildSuccessResult(request);
    }

    @GetMapping("/emailAiResponse/{ticketId}")
    @RequiresPermissions(PermDefine.ERP_TOOL_GPT)
    public ApiResponseResult emailAiResponse(@PathVariable Long ticketId) {
        return ApiResponseResult.buildSuccessResult(Arrays.asList(gptTextRecordService.emailAiResponse(ticketId)));
    }


    @PostMapping("/listingAsinExtract")
    @RequiresPermissions(PermDefine.ERP_TOOL_GPT)
    public ApiResponseResult listingOptimize(@RequestBody ListingAsinExtractReq req) {
        ListingAsinExtractRes optimizeRes = gptTextRecordService.listingAsinExtract(req);
        return ApiResponseResult.buildSuccessResult(optimizeRes);
    }


    @PostMapping("/listingTextOptimize")
    @RequiresPermissions(PermDefine.ERP_TOOL_GPT)
    public ApiResponseResult listingTextOptimize(@RequestBody ListingAsinExtractReq extractReq,@RequestParam("contextId") Integer contextId) {
        List<GptTextResponse> response = gptTextRecordService.listingTextOptimize(extractReq,contextId);
        return ApiResponseResult.buildSuccessResult(response);
    }


    @GetMapping("/selectListingLog")
    @RequiresPermissions(PermDefine.ERP_TOOL_GPT)
    public TableDataInfo selectListingLog(GptListingLog log,Integer contextId) {
        startPage("id","DESC");
        log.setOrgId(contextId);
        log.queryCondition();
        List<GptListingLog> logs = gptListingLogService.selectListingLog(log);
        for (GptListingLog listingLog : logs) {
            if (StrUtil.isNotBlank(listingLog.getAsin())) {
                listingLog.setAsin(listingLog.getAsin().replace(",", "  "));
            }
        }
        return getDataTable(logs);
    }

    @GetMapping("/listingLogById")
    @RequiresPermissions(PermDefine.ERP_TOOL_GPT)
    public ApiResponseResult selectListingLog(Long id) {
        GptListingLog log = gptListingLogService.getById(id);
        return ApiResponseResult.buildSuccessResult(log);
    }

    @PostMapping("/saveListingLog")
    @RequiresPermissions(PermDefine.ERP_TOOL_GPT)
    public ApiResponseResult saveListingLog(@RequestBody GptListingLogDTO dto,@RequestParam("contextId") Integer contextId){
        dto.setOrgId(contextId);
        gptListingLogService.saveListingLog(dto);
        return ApiResponseResult.buildSuccessResult();
    }

    @PostMapping("/new/listingText")
    public ApiResponseResult newListingText(@Valid @RequestBody ListingAsinExtractReq extractReq,@RequestParam("contextId") Integer contextId) {
        NewListingAsinExtractRes  newListingAsinExtractRes = gptTextRecordService.newListingAsinExtract(extractReq);
        return ApiResponseResult.buildSuccessResult(newListingAsinExtractRes);
    }




    @PostMapping("/manual/generated/ai")
    @RequiresPermissions(PermDefine.ERP_TOOL_GPT)
    public ApiResponseResult manualGenneratedAi(@RequestBody ListingManualAiContentDTO listingManualAiContentDTO) {
        return ApiResponseResult.buildSuccessResult(gptTextRecordService.manualGenneratedAi(listingManualAiContentDTO));
    }

    @PostMapping("/test/manual/generated/ai")
    @RequiresPermissions(PermDefine.ERP_TOOL_GPT)
    public ApiResponseResult AutoGenneratedAi(@RequestBody ListingAiAutoRequest ListingAiAutoRequest) {
        return ApiResponseResult.buildSuccessResult(gptTextRecordService.autoAiInfoGenerate(ListingAiAutoRequest));
    }
}
