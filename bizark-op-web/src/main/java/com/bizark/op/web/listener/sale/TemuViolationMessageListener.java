package com.bizark.op.web.listener.sale;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bizark.op.api.cons.MQDefine;
import com.bizark.op.api.entity.op.account.Account;
import com.bizark.op.api.entity.op.order.SaleOrders;
import com.bizark.op.api.entity.op.sale.TemuViolation;
import com.bizark.op.api.entity.op.sale.TemuViolationDetail;
import com.bizark.op.api.entity.op.sale.message.TemuViolationMessage;
import com.bizark.op.api.entity.op.ticket.ScTicket;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.sale.TemuViolationDetailService;
import com.bizark.op.api.service.sale.TemuViolationService;
import com.bizark.op.api.service.ticket.IScTicketService;
import com.bizark.op.service.mapper.sale.TemuViolationMapper;
import com.bizark.op.service.mapper.ticket.SaleOrdersMapper;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TemuViolationMessageListener {


    @Autowired
    private SaleOrdersMapper saleOrdersMapper;

    @Autowired
    private TemuViolationDetailService temuViolationDetailService;

    @Autowired
    private TemuViolationService temuViolationService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private IScTicketService scTicketService;


//  @RabbitListener(queues = MQDefine.TEMU_VIOLATION_QUEUE+".test")
    public void process(@Payload Message message, Channel channel) throws IOException {
        try {
            String json = new String(message.getBody(), StandardCharsets.UTF_8);
//            log.info("{} 接收到消息 :{}", MQDefine.TEMU_VIOLATION_QUEUE, json);
            TemuViolationMessage temuViolationMessage = JSON.parseObject(json, TemuViolationMessage.class);

//
//            String storeName = temuViolationMessage.getAccountFlag();
//            Account account = accountService.getOne(Wrappers.lambdaQuery(Account.class).select(Account::getId, Account::getOrgId, Account::getFlag)
//                    .eq(Account::getSellerId, temuViolationMessage.getMallid()).eq(Account::getType, "temu").eq(Account::getActive, "Y"));

            String storeName = temuViolationMessage.getAccountFlag();
            Account account = accountService.lambdaQuery()
                    .eq(Account::getStoreName, storeName)
                    .one();


            if (Objects.isNull(account)) {
                log.error("{} 未获取到店铺信息 ：{} ", MQDefine.TEMU_VIOLATION_QUEUE, storeName);
                return;
            }

            TemuViolation violation = temuViolationMessage.convert(account);

            if (Objects.isNull(violation)) {
                return;
            }
            handleViolation(violation, account);
        } catch (Exception e) {
            log.error("{} 消息消费异常：{}", MQDefine.TEMU_VIOLATION_QUEUE, e.getMessage(), e);
        }finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }

    }

    private void handleViolation(TemuViolation violation, Account account) {
        // 加锁
        RLock lock = redissonClient.getLock(violation.getOrgId() + violation.getAccountId() + violation.getViolationAppealSn() + violation.getViolationName());

        try {
            lock.lock(20, TimeUnit.SECONDS);

            //明细信息
            List<TemuViolationDetail> violationDetails = violation.getViolationDetails();


            TemuViolation dbViolation = temuViolationService.lambdaQuery()
                    .eq(TemuViolation::getOrgId, violation.getOrgId())
                    .eq(TemuViolation::getAccountFlag, violation.getAccountFlag()) //店铺编号
                    .eq(TemuViolation::getViolationAppealSn, violation.getViolationAppealSn()) //申诉编号
                    .eq(TemuViolation::getViolationType, violation.getViolationType())//申诉类型
                    .eq(TemuViolation::getInformTime, violation.getInformTime())//申诉时间
                    .one();
            if (Objects.isNull(dbViolation)) {
                temuViolationService.save(violation);
            } else {
                violation.setId(dbViolation.getId());
                violation.setCreatedAt(null);
                violation.setCreatedName(null);
                temuViolationService.updateById(violation);
            }
            // 数据去重
            violationDetails = violationDetails.stream()
                    .peek(c -> c.setViolationId(violation.getId())) //设置主表id
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(
                            () -> new TreeSet<>(Comparator.comparing(this::getKey))
                    ), ArrayList::new));


            if (CollectionUtil.isNotEmpty(violationDetails)) {
                // 添加订单相关信息
                temuViolationDetailService.handleOrderInfo(account, violationDetails);
                //申诉信息处理
                temuViolationDetailService.handleAppealInfo(account,violation, violationDetails);

            }

            List<TemuViolationDetail> dbViolationDetails = temuViolationDetailService.lambdaQuery()
                    .eq(TemuViolationDetail::getViolationAppealSn, violation.getViolationAppealSn())
                    .list();

            if (CollectionUtil.isEmpty(dbViolationDetails)) {
                temuViolationDetailService.saveBatch(violationDetails);
                //创建申诉工单
                scTicketService.createTemuAppealTicket(violation, violationDetails);

                List<TemuViolationDetail> ticketViolationList = violationDetails.stream().filter(item -> null != item.getTicketId()).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(ticketViolationList)) {
                    //更新工单ID
                    temuViolationDetailService.saveOrUpdateBatch(ticketViolationList);
                }
                return;
            }

            //库中明细信息设置相同key
            Map<String, TemuViolationDetail> detailMap = dbViolationDetails.stream()
                    .collect(Collectors.toMap(this::getKey, Function.identity(), (a, b) -> a));

            //循环传入
            for (TemuViolationDetail detail : violationDetails) {
                String key = getKey(detail);
                if (detailMap.containsKey(key)) { //与DB相同
                    TemuViolationDetail temuViolationDetail = detailMap.get(key);
                    detail.setId(temuViolationDetail.getId()); //替换DB的值
                    detail.setCreatedAt(null);
                    detail.setCreatedName(null);

                    //存在工单，非待处理状态，
                    if (temuViolationDetail.getTicketId()!=null&&
                            !new Integer(0).equals(violation.getAppealStatus())){

                       // 且业务状态与申诉进度不相等
                        if (detail.getCheckStatus() !=null&&!detail.getCheckStatus().equals(temuViolationDetail.getBusinessAppealStatus())){
                            temuViolationDetailService.saveTemuViolationApperalLog(detail.getCheckStatus(), temuViolationDetail.getId());
                        }

                        //关闭工单
                        if (
                        new  Integer(1).equals(detail.getCheckStatus())||new  Integer(2).equals(detail.getCheckStatus())
                                ||new  Integer(3).equals(detail.getCheckStatus())||new  Integer(5).equals(detail.getCheckStatus())||new  Integer(6).equals(detail.getCheckStatus())
                        ){
                            ScTicket ticket = new ScTicket();
                            ticket.setId(temuViolationDetail.getTicketId());
                            ticket.setTicketStatus("CLOSED");
                            ticket.settingDefaultSystemUpdate();
                            scTicketService.updateScTicketStatus(ticket);
                        }



                    }
                }
            }
            temuViolationDetailService.saveOrUpdateBatch(violationDetails);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            lock.unlock();

        }
    }

    public String getKey(TemuViolationDetail detail) {
        return detail.getOrgId() + detail.getAccountId() + detail.getAccountFlag() + detail.getOrderNo() + detail.getViolationAppealSn() + detail.getViolationType() + detail.getInformTime();
    }



}
