package com.bizark.op.web.controller.api.v2.ticket;

import com.bizark.op.api.entity.op.order.SaleOrders;
import com.bizark.op.api.entity.op.ticket.order.AmzVcPoOrderV;
import com.bizark.op.api.service.ticket.IAmzVcPoOrderService;
import com.bizark.op.api.service.ticket.IAmzVcPoOrderTimeRecordService;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.poi.ExcelUtil;
import com.bizark.op.service.mapper.ticket.SaleOrdersMapper;
import com.bizark.op.web.controller.api.AbstractApiController;
import com.bizark.framework.web.view.ApiResponseResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * PO工单Controller
 *
 * <AUTHOR>
 * @date 2022-05-24
 */
@RestController
@RequestMapping("/api/v2/crm/poorder/ticket")
public class AmzVcPoOrderTicketController extends AbstractApiController {

    @Autowired
    private IAmzVcPoOrderTimeRecordService amzVcPoOrderTimeRecordVService;

    //获取Po工单信息
    @Autowired
    private IAmzVcPoOrderService amzVcPoOrderService;


    @Autowired
    private SaleOrdersMapper saleOrdersMapper;



    /**
     * @description: 手动生成工单Di确认工单
     * @author: Moore
     * @date: 2024/3/8 16:54
     * @param
     * @param orderno
     * @return: void
    **/
    @GetMapping(value = "/manual/poorder/ticket")
    public ApiResponseResult exportPoorderInfo(@RequestParam(value = "contextId") Long contextId,  String orderno ) {
        SaleOrders saleOrders = saleOrdersMapper.selectScOrderByAmazonOrderId(contextId, orderno);
        amzVcPoOrderService.createAmzPoOrderTicket(saleOrders);
        return ApiResponseResult.buildSuccessResult();
    }




    /**
     * @description: 导出Po确认工单
     * @author: Moore
     * @date: 2024/3/14 0:22
     * @param
     * @param recordId
     * @param response
     * @return: com.bizark.framework.web.view.ApiResponseResult
    **/
    @GetMapping("/export/record/{recordId}")
    public ApiResponseResult export(@PathVariable("recordId") Long recordId,HttpServletResponse response) {

        List<AmzVcPoOrderV> amzVcPoOrderVS = amzVcPoOrderTimeRecordVService.selectAmzVcPoOrderTimeRecordVById(recordId);
        ExcelUtil<AmzVcPoOrderV> util = new ExcelUtil<AmzVcPoOrderV>(AmzVcPoOrderV.class);
        return util.exportExcel(amzVcPoOrderVS, "po_ticket", "PO_ORDER_TICKET", response);
    }


    /**
     * 导入USPO工单确认信息
     *
     * @param contextId 组织ID
     * @param file 文件
     * @param recordId 来源ID
     * @param purchaseReqNo 采购申请号
     */
    @PostMapping(value = "/po/importExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ApiResponseResult importExcel(@RequestParam(value = "contextId") Integer contextId,
                                         @RequestParam(value = "sourceId" ) Long  recordId,
                                         @RequestParam(value = "purchaseReqNo",required = false) String  purchaseReqNo,
                                         @RequestPart("file") MultipartFile file) throws Exception {

        ExcelUtil<AmzVcPoOrderV> util = new ExcelUtil<>(AmzVcPoOrderV.class);
        List<AmzVcPoOrderV> list = util.importExcel(file.getInputStream());
        if (org.springframework.util.CollectionUtils.isEmpty(list)) {
            throw new CustomException("导入数据不能为空");
        }
        return ApiResponseResult.buildSuccessResult(amzVcPoOrderService.importVcPoOrderItemExcel(list,contextId,recordId,purchaseReqNo));
    }


    /**
     * @param
     * @param amzVcPoOrderVS Po明细行保存
     * @param purchaseReqNo 采购批次号
     * @description: po确认工单保存
     * @author: Moore
     * @date: 2024/3/12 17:31
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @PutMapping("/save/po/acknowledgement")
    public ApiResponseResult editPoOrderItemList(@RequestBody List<AmzVcPoOrderV> amzVcPoOrderVS, @RequestParam("ticketId") Long ticketId,
                                                 @RequestParam("contextId") Long organizationId,
                                                 @RequestParam(value = "purchaseReqNo",required = false) String   purchaseReqNo
                                                 ) {
        return ApiResponseResult.buildSuccessResult(amzVcPoOrderService.updateAmzVcPoItemOrderList(amzVcPoOrderVS, ticketId, organizationId, purchaseReqNo));
    }


    /**
     * @param
     * @param recordId time_record 主键ID
     * @param ticketId 工单ID
     * @param shopId   店铺ID
     * @description: po单提交确认
     * @author: Moore
     * @date: 2024/3/19 11:26
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @GetMapping("/confrimBatch")
    public ApiResponseResult confirmBatch(@RequestParam("contextId") Integer organizationId,
                                          @RequestParam("recordId") Long recordId,
                                          @RequestParam("ticketId") Long ticketId,
                                          @RequestParam("shopId") Long shopId) {
        amzVcPoOrderTimeRecordVService.confirmBatchSendPoOrder(recordId, ticketId, shopId,organizationId);
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * @description: 前置校验
     * @author: Moore
     * @date: 2024/3/19 11:26
     * @param
     * @param recordId  time_record 主键ID
     * @param ticketId  工单ID
     * @param shopId  店铺ID
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @GetMapping("/confrimBatch/verify")
    public ApiResponseResult confirmBatchVerify(@RequestParam("recordId") Long recordId,
                                                @RequestParam("ticketId") Long ticketId,
                                                @RequestParam("shopId") Long shopId) {
        return ApiResponseResult.buildSuccessResult(amzVcPoOrderTimeRecordVService.confirmBatchSendPoOrderVerify(recordId, ticketId, shopId));
    }


}
