package com.bizark.op.web.controller.auth;

import com.bizark.common.contract.AuthUserDetails;
import com.bizark.common.exception.AppRuntimeException;
import com.bizark.op.web.config.shiro.MyJwtToken;
import com.bizark.framework.app.controller.ApiBaseController;
import com.bizark.framework.util.JWTAuthHelper;
import com.bizark.framework.web.view.ApiResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.*;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.util.WebUtils;
import org.apache.tomcat.util.http.fileupload.FileItem;
import org.apache.tomcat.util.http.fileupload.FileUploadException;
import org.apache.tomcat.util.http.fileupload.RequestContext;
import org.apache.tomcat.util.http.fileupload.disk.DiskFileItemFactory;
import org.apache.tomcat.util.http.fileupload.servlet.ServletFileUpload;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Tag(name = "JwtController", description = "系统基本认证Controller")
@RestController("JwtApiAuthController")
@RequestMapping(value = "/auth/jwt"
        , method = {RequestMethod.GET, RequestMethod.POST}
        , consumes = {MediaType.ALL_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.APPLICATION_JSON_UTF8_VALUE, MediaType.APPLICATION_JSON_VALUE}
        , produces = {MediaType.APPLICATION_JSON_UTF8_VALUE, MediaType.APPLICATION_JSON_VALUE}
)
public class JwtController extends ApiBaseController {
//    private Logger logger = LoggerFactory.getLogger(JwtController.class);

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private JWTAuthHelper jwtAuthHelper;

    protected String pluckTokenFromRequest(ServletRequest request) {
        //log.debug("MYDEV MyFormAuthenticationFilter pluckTokenFromRequest start");
        HttpServletRequest servletRequest = WebUtils.toHttp(request);
        // 优先表单参数值
        String paramToken = servletRequest.getParameter("token");
        if (!StringUtils.isBlank(paramToken)) {
            return paramToken;
        } else {
            boolean isMultipart = ServletFileUpload.isMultipartContent(servletRequest);
            if (isMultipart) {
                String fileParamToken = null;
                List<FileItem> items = null;
                try {
                    items = new ServletFileUpload(new DiskFileItemFactory()).parseRequest((RequestContext) servletRequest);
                } catch (FileUploadException e) {
                    e.printStackTrace();
                }
                for (FileItem item : items) {
                    if (item.isFormField()) {
                        // Process regular form field (input type="text|radio|checkbox|etc", select, etc).
                        String fieldname = item.getFieldName();
                        String fieldvalue = item.getString();
                        // ... (do your job here)
                        if (fieldname.equals("token")) {
                            fileParamToken = fieldvalue;
                            break;
                        }
                    }
                }
                if (!StringUtils.isBlank(fileParamToken)) {
                    return fileParamToken;
                }
            }
        }

        // 其次从header的认证头读取
        final String authorizationPrefix = "Bearer ";

        String authorizationHeader = servletRequest.getHeader("Authorization");
        if (StringUtils.isNotEmpty(authorizationHeader) && authorizationHeader.startsWith(authorizationPrefix)) {
            return authorizationHeader.replace(authorizationPrefix, "");
            //log.debug("MYDEV MyFormAuthenticationFilter pluckTokenFromRequest got header Bearer token {}",encodedToken);
        } else {
            // 最后从token cookie读取
            //log.debug("MYDEV MyFormAuthenticationFilter pluckTokenFromRequest try cookie token");
            String cookieToken = pluckTokenCookieFromCookies(servletRequest.getCookies());
            if (!StringUtils.isBlank(cookieToken)) {
                return cookieToken;
            }
        }
        return null;
    }

    protected String pluckTokenCookieFromCookies(Cookie[] cookies) {
        //log.debug("MYDEV MyFormAuthenticationFilter pluckTokenCookieFromCookies start");
        //log.debug("MYDEV MyFormAuthenticationFilter pluckTokenCookieFromCookies cookies",JacksonUtils.toJson(cookies));
        if (cookies != null && cookies.length > 0) {
            for (Cookie cookie : cookies) {
                //log.debug("MYDEV MyFormAuthenticationFilter pluckTokenCookieFromCookies each {} {}",cookie.getName(),cookie.getValue());
                try {
                    if (cookie.getName().equals("token")
//                            && cookie.getPath().equals("/")
//                            && cookie.getDomain().equals(".thebizark.com")
                    ) {
                        return cookie.getValue();
                    }
                } catch (Exception e) {
                }
            }
        }
        return null;
    }

    /**
     * 根据用户对象和是否记住我生成token字符串
     *
     * @param authUser   用户对象
     * @param rememberMe 是否记住我
     * @return
     */
    protected String genTokenByUserAndRememberMe(AuthUserDetails authUser, Boolean rememberMe) {
//       //log.debug("MYDEV genTokenByUserAndRememberMe arg {} {}",JacksonUtils.toJson(authUser),rememberMe);
        try {
            String genTokenByUid = jwtAuthHelper.genTokenByUid(
                    authUser.getId(),
                    rememberMe ?
                            jwtAuthHelper.getRefreshTtl()
                            : jwtAuthHelper.getTtl()
            );
//           //log.debug("MYDEV genTokenByUserAndRememberMe genTokenByUid val {}",genTokenByUid);
            return genTokenByUid;
        } catch (Exception e) {
            logger.error(" genTokenByUserAndRememberMe Failed", e);
        }
        return null;
    }

    /**
     * api用户认证
     *
     * @return
     */
    @Operation(summary = "用户凭据信息认证",  description = "用户基本登录认证")
    @Parameters({
            @Parameter(name = "service", description = "子系统服务入口",  required = false,  example = "https%3A%2F%2Fuc-adm.thebizark.com"),
            @Parameter(name = "username", description = "用户名",   example = "<EMAIL>"),
            @Parameter(name = "password", description = "登录密码",   example = "123456"),
    })
    @RequestMapping(value = "/authorize"
//            ,method = {RequestMethod.POST,RequestMethod.GET}
//            ,consumes = {MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.APPLICATION_JSON_UTF8_VALUE, MediaType.APPLICATION_JSON_VALUE}
//            ,produces = {MediaType.APPLICATION_JSON_UTF8_VALUE, MediaType.APPLICATION_JSON_VALUE}
    )
    @ResponseBody
    public ApiResponseResult authorize(
//            @RequestBody ApiAuthorizeRequest apiAuthorizeRequest,
            @RequestParam(required = false) Map<String, Object> formParams,
            @RequestParam(value = "service", required = false) String queryService,
            @CookieValue(value = "token", defaultValue = "", required = false) String cookieToken,
            HttpServletRequest request, HttpServletResponse response
    ) {
//        logger.debug("api.authorize {}",JacksonUtils.toJson(apiAuthorizeRequest));
//        logger.debug("collect.event {} {} {}",eventCollectRestRequest.getEnv(),eventCollectRestRequest.getLabel(),eventCollectRestRequest.getAt());

        String service = queryService;
        String username = "";
        String password = "";
        String requestToken = pluckTokenFromRequest(request);
        Boolean rememberMe = true;
        if (formParams != null && !formParams.isEmpty()) {
            username = (String) formParams.getOrDefault("username", "");
            rememberMe = formParams.containsKey("rememberMe");
            service = (String) formParams.getOrDefault("service", queryService);
            password = (String) formParams.getOrDefault("password", "");
//            requestToken = (String) formParams.getOrDefault("token",cookieToken);
        }
        Map<String, Object> data = new HashMap<>();
        data.put("service", service);

        Subject subject = SecurityUtils.getSubject();

        // 只有提交表单的时候才做处理
        if ("POST".equalsIgnoreCase(request.getMethod())) {
//            logger.debug("MYDEV IndexController form submit");
            // 有token时优先token认证

            if (!StringUtils.isBlank(requestToken)) {
                MyJwtToken token = new MyJwtToken(requestToken);
                try {
//            logger.debug("对用户[" + username + "]进行登录验证..验证开始");
//                logger.debug("MYDEV IndexController form submit before login");
                    subject.login(token);
//                logger.debug("MYDEV IndexController form submit after login");
//            logger.debug("对用户[" + username + "]进行登录验证..验证通过");
                    if (subject.isAuthenticated()) {
                        AuthUserDetails authUser = getAuthUser();
//                        String encodedToken = genTokenByUserAndRememberMe(authUser,rememberMe);
                        String encodedToken = requestToken;
                        data.put("token", encodedToken);

                        return ApiResponseResult.buildSuccessResult("OK", data);
//                        return ApiResponseHelper.responseApiResult("OK",data);
                    } else {
                        return ApiResponseResult.buildFailureResult("认证失败", data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                        return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,"认证失败",null, HttpStatus.FORBIDDEN);
                    }
                } catch (UnknownAccountException uae) {
                    return ApiResponseResult.buildFailureResult("未知账户", data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                    return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,"未知账户",null, HttpStatus.FORBIDDEN);
                } catch (IncorrectCredentialsException ice) {
                    return ApiResponseResult.buildFailureResult("用户名或密码不正确", data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                    return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,"用户名或密码不正确",null, HttpStatus.FORBIDDEN);
                } catch (LockedAccountException lae) {
                    return ApiResponseResult.buildFailureResult("账户已锁定", data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                    return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,"账户已锁定",null, HttpStatus.FORBIDDEN);
                } catch (ExcessiveAttemptsException eae) {
                    return ApiResponseResult.buildFailureResult("用户名或密码错误次数过多", data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                    return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,"用户名或密码错误次数过多",null, HttpStatus.FORBIDDEN);
                } catch (AuthenticationException ae) {
                    return ApiResponseResult.buildFailureResult("用户名或密码不正确", data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                    return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,"用户名或密码不正确.",null, HttpStatus.FORBIDDEN);
                } catch (AppRuntimeException are) {
                    return ApiResponseResult.buildFailureResult(are.getLocalizedMessage(), data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                    return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,are.getLocalizedMessage(),null, HttpStatus.FORBIDDEN);
                }
            }

            if (formParams == null || formParams.isEmpty()) {
                return ApiResponseResult.buildFailureResult("用户名和密码必填", null).setCode(ApiResponseResult.CODE_CLIENT_BADREQUEST);
//                return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_BADREQUEST,"用户名和密码必填",null, HttpStatus.BAD_REQUEST);
            }
            if (StringUtils.isEmpty((String) formParams.get("username"))) {
                return ApiResponseResult.buildFailureResult("用户名必填", data).setCode(ApiResponseResult.CODE_CLIENT_BADREQUEST);
//                return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_BADREQUEST,"用户名必填",null, HttpStatus.BAD_REQUEST);
            }
            if (StringUtils.isEmpty((String) formParams.get("password"))) {
                return ApiResponseResult.buildFailureResult("密码必填", data).setCode(ApiResponseResult.CODE_CLIENT_BADREQUEST);
//                return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_BADREQUEST,"密码必填",null, HttpStatus.BAD_REQUEST);
            }
            password = (String) formParams.get("password");
//            logger.debug("MYDEV before index login {}",password);
//            Subject subject = SecurityUtils.getSubject() ;
            UsernamePasswordToken token = new UsernamePasswordToken(username, password, rememberMe);
            try {
//            logger.debug("对用户[" + username + "]进行登录验证..验证开始");
//                logger.debug("MYDEV IndexController form submit before login");
                subject.login(token);
//                logger.debug("MYDEV IndexController form submit after login");
//            logger.debug("对用户[" + username + "]进行登录验证..验证通过");
                if (subject.isAuthenticated()) {
                    AuthUserDetails authUser = getAuthUser();
                    String encodedToken = genTokenByUserAndRememberMe(authUser, rememberMe);
                    data.put("token", encodedToken);
                    return ApiResponseResult.buildSuccessResult("OK", data);
//                    return ApiResponseHelper.responseApiResult("OK",data);
                } else {
                    return ApiResponseResult.buildFailureResult("认证失败", data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                    return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,"认证失败",null, HttpStatus.FORBIDDEN);
                }
            } catch (UnknownAccountException uae) {
                return ApiResponseResult.buildFailureResult("未知账户", data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,"未知账户",null, HttpStatus.FORBIDDEN);
            } catch (IncorrectCredentialsException ice) {
                return ApiResponseResult.buildFailureResult("用户名或密码不正确", data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,"用户名或密码不正确",null, HttpStatus.FORBIDDEN);
            } catch (LockedAccountException lae) {
                return ApiResponseResult.buildFailureResult("账户已锁定", data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,"账户已锁定",null, HttpStatus.FORBIDDEN);
            } catch (ExcessiveAttemptsException eae) {
                return ApiResponseResult.buildFailureResult("用户名或密码错误次数过多", data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,"用户名或密码错误次数过多",null, HttpStatus.FORBIDDEN);
            } catch (AuthenticationException ae) {
                return ApiResponseResult.buildFailureResult("用户名或密码不正确", data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,"用户名或密码不正确.",null, HttpStatus.FORBIDDEN);
            } catch (AppRuntimeException are) {
                return ApiResponseResult.buildFailureResult(are.getLocalizedMessage(), data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,are.getLocalizedMessage(),null, HttpStatus.FORBIDDEN);
            }
        }

//        logger.debug("MYDEV IndexController enter login");

        if (subject.isAuthenticated()) {
            AuthUserDetails authUser = getAuthUser();
            String encodedToken = genTokenByUserAndRememberMe(authUser, rememberMe);
            data.put("token", encodedToken);
            return ApiResponseResult.buildSuccessResult("OK", data);
//            return ApiResponseHelper.responseApiResult("OK",data);
        } else {
            if (!StringUtils.isBlank(requestToken)) {
//            logger.debug("MYDEV IndexController enter login got cookie token");
                MyJwtToken token = new MyJwtToken(requestToken);
                try {
//            logger.debug("对用户[" + username + "]进行登录验证..验证开始");
//                logger.debug("MYDEV IndexController form submit before login");
                    subject.login(token);
//                logger.debug("MYDEV IndexController form submit after login");
//            logger.debug("对用户[" + username + "]进行登录验证..验证通过");
                    if (subject.isAuthenticated()) {
                        AuthUserDetails authUser = getAuthUser();
//                        String encodedToken = genTokenByUserAndRememberMe(authUser,rememberMe);
                        String encodedToken = requestToken;
                        data.put("token", encodedToken);

                        return ApiResponseResult.buildSuccessResult("OK", data);
//                        return ApiResponseHelper.responseApiResult("OK",data);
                    } else {
                        return ApiResponseResult.buildFailureResult("认证失败", data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                        return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,"认证失败",null, HttpStatus.FORBIDDEN);
                    }
                } catch (UnknownAccountException uae) {
                    return ApiResponseResult.buildFailureResult("未知账户", data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                    return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,"未知账户",null, HttpStatus.FORBIDDEN);
                } catch (IncorrectCredentialsException ice) {
                    return ApiResponseResult.buildFailureResult("用户名或密码不正确", data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                    return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,"用户名或密码不正确",null, HttpStatus.FORBIDDEN);
                } catch (LockedAccountException lae) {
                    return ApiResponseResult.buildFailureResult("账户已锁定", data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                    return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,"账户已锁定",null, HttpStatus.FORBIDDEN);
                } catch (ExcessiveAttemptsException eae) {
                    return ApiResponseResult.buildFailureResult("用户名或密码错误次数过多", data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                    return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,"用户名或密码错误次数过多",null, HttpStatus.FORBIDDEN);
                } catch (AuthenticationException ae) {
                    return ApiResponseResult.buildFailureResult("用户名或密码不正确", data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                    return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,"用户名或密码不正确.",null, HttpStatus.FORBIDDEN);
                } catch (AppRuntimeException are) {
                    return ApiResponseResult.buildFailureResult(are.getLocalizedMessage(), data).setCode(ApiResponseResult.CODE_CLIENT_FORBIDDEN);
//                    return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_FORBIDDEN,are.getLocalizedMessage(),null, HttpStatus.FORBIDDEN);
                }
            } else {
//                return ApiResponseHelper.responseApiError(AppConstants.EXCODE_CLIENT_UNAUTHORIZED,"请先登入认证",null, HttpStatus.UNAUTHORIZED);
            }
            return ApiResponseResult.buildFailureResult("请先登入认证", data).setCode(ApiResponseResult.CODE_CLIENT_UNAUTHORIZED);
        }
    }

    /**
     * api用户认证
     *
     * @return
     */
    @RequestMapping(value = "/refreshToken"
//            ,method = {RequestMethod.POST,RequestMethod.GET}
//            ,consumes = {MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.APPLICATION_JSON_UTF8_VALUE, MediaType.APPLICATION_JSON_VALUE}
//            ,produces = {MediaType.APPLICATION_JSON_UTF8_VALUE, MediaType.APPLICATION_JSON_VALUE}
    )
    @ResponseBody
    public ApiResponseResult refreshToken(
//            @RequestBody ApiAuthorizeRequest apiAuthorizeRequest,
            @RequestParam(value = "token", defaultValue = "", required = false) String requestToken,
            @CookieValue(value = "token", defaultValue = "", required = false) String cookieToken,
            HttpServletRequest request, HttpServletResponse response
    ) {
//        logger.debug("api.authorize {}",JacksonUtils.toJson(apiAuthorizeRequest));
//        logger.debug("collect.event {} {} {}",eventCollectRestRequest.getEnv(),eventCollectRestRequest.getLabel(),eventCollectRestRequest.getAt());

        String encodedToken = StringUtils.isNoneBlank(requestToken) ? requestToken : cookieToken;

        if (!StringUtils.isBlank(encodedToken)) {
            //logger.debug("MYDEV IndexController isAuthenticated not isBlank encodedToken");
            List<Integer> uidAndRememberme = jwtAuthHelper.parseUidAndRememberMeFromToken(encodedToken);
            if (uidAndRememberme != null) {
                //logger.debug("MYDEV IndexController isAuthenticated not isBlank uidAndRememberme");
                Integer requestUid = uidAndRememberme.get(0);
                Boolean requestRemembeMe = uidAndRememberme.get(1) > 0;
                if (requestUid > 0) {
                    //logger.debug("MYDEV IndexController isAuthenticated not isBlank requestUid");
                    encodedToken = jwtAuthHelper.refreshTokenByTtl(encodedToken);
                    Map<String, Object> data = new HashMap<>();
                    data.put("token", encodedToken);
                    return ApiResponseResult.buildSuccessResult("OK", data);
                }
            }
            return ApiResponseResult.buildFailureResult("token验证失败", null).setCode(ApiResponseResult.CODE_CLIENT_UNAUTHORIZED);
        } else {
            return ApiResponseResult.buildFailureResult("token必填", null).setCode(ApiResponseResult.CODE_CLIENT_BADREQUEST);
            //logger.debug("MYDEV IndexController isAuthenticated isAuthenticated isBlank encodedToken");
        }
    }

    @RequestMapping("/logout")
    @ResponseBody
    public ApiResponseResult logout(@RequestParam(value = "service", required = false) String service, ServletRequest request, ServletResponse response) {
        //使用权限管理工具进行用户的退出，跳出登录，给出提示信息
        SecurityUtils.getSubject().logout();
        Map<String, Object> data = new HashMap<>();
        data.put("service", service);
        return ApiResponseResult.buildSuccessResult("OK", data);
    }

}
