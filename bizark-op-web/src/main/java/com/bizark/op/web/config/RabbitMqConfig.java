package com.bizark.op.web.config;

import com.bizark.op.api.cons.FinanceConstant;
import com.bizark.op.api.cons.MQDefine;
import com.bizark.op.service.util.AmqpProducer;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;


@Configuration
public class RabbitMqConfig {

    @Value("${amqp.provider}")
    private String amqpProvider;

    @Value("${aliyun.resOwnerId}")
    private long resOwnerId;

    @Value("${spring.rabbitmq.addresses}")
    private String addresses;

    @Value("${spring.rabbitmq.username}")
    private String username;

    @Value("${spring.rabbitmq.password}")
    private String password;

    @Value("${spring.rabbitmq.virtualhost}")
    private String virtualhost;

    @Autowired
    private RabbitProperties rabbitProperties;

    /**
     * 声明连接工厂
     */
    @Bean
    public ConnectionFactory connectionFactory() {
        com.rabbitmq.client.ConnectionFactory rabbitConnectionFactory =
                new com.rabbitmq.client.ConnectionFactory();
        rabbitConnectionFactory.setVirtualHost(rabbitProperties.getVirtualHost());

        if (amqpProvider.equals("aliyun")) {
            AliyunCredentialsProvider credentialsProvider = new AliyunCredentialsProvider(
                    rabbitProperties.getUsername(), rabbitProperties.getPassword(), resOwnerId);
            rabbitConnectionFactory.setCredentialsProvider(credentialsProvider);
        } else {
            rabbitConnectionFactory.setUsername(rabbitProperties.getUsername());
            rabbitConnectionFactory.setPassword(rabbitProperties.getPassword());
        }

        rabbitConnectionFactory.setAutomaticRecoveryEnabled(true);
        rabbitConnectionFactory.setNetworkRecoveryInterval(5000);

        ConnectionFactory connectionFactory = new CachingConnectionFactory(rabbitConnectionFactory);
//        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        ((CachingConnectionFactory) connectionFactory).setPublisherConfirms(rabbitProperties.isPublisherConfirms());
        ((CachingConnectionFactory) connectionFactory).setPublisherReturns(rabbitProperties.isPublisherReturns());

        ((CachingConnectionFactory) connectionFactory).setAddresses(rabbitProperties.getAddresses());
        ((CachingConnectionFactory) connectionFactory).setUsername(rabbitProperties.getUsername());
        ((CachingConnectionFactory) connectionFactory).setPassword(rabbitProperties.getPassword());
        ((CachingConnectionFactory) connectionFactory).setVirtualHost(rabbitProperties.getVirtualHost());
        ((CachingConnectionFactory) connectionFactory).setShuffleAddresses(true);
        return connectionFactory;
    }

    /**
     * 创建一个管理器（org.springframework.amqp.rabbit.core.RabbitAdmin），用于管理交换，队列和绑定。
     * auto-startup 指定是否自动声明上下文中的队列,交换和绑定, 默认值为true。
     */
    @Bean
    public RabbitAdmin rabbitAdmin(ConnectionFactory connectionFactory) {
        RabbitAdmin rabbitAdmin = new RabbitAdmin(connectionFactory);
        rabbitAdmin.setAutoStartup(true);
        return rabbitAdmin;
    }


    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(messageConverter());
        return rabbitTemplate;
    }

    @Bean
    public MessageConverter messageConverter() {
        return new Jackson2JsonMessageConverter();
    }

    @Bean
    public AmqpProducer amqpProducer(ConnectionFactory connectionFactory) {
        return new AmqpProducer(connectionFactory);
    }

    /** E恒建模块交换机 */
    @Bean
    public DirectExchange eErpExchange() {
        //持久化消息, 消费后不删除
        return new DirectExchange(MQDefine.EXCHANGE_E_ERP, true, false);
    }

    /** 同步供应商台账消息队列 */
    @Bean
    public Queue queueSyncVendorBill() {
        // 队列持久
        return new Queue(MQDefine.PUSH_ERP_PUR_VENDOR_BILL_QUEUE, true);

    }

    /** 同步供应商台账消息队列与交换机绑定 */
    @Bean
    public Binding bindingSyncVendorBill() {
        return BindingBuilder.bind(queueSyncVendorBill()).to(eErpExchange()).with(
                MQDefine.PUSH_ERP_PUR_VENDOR_BILL_ROUTING_KEY);
    }

    /** 供应商台账到票消息队列 */
    @Bean
    public Queue queueVendorBillInvoiceArrival() {
        // 队列持久
        return new Queue(MQDefine.PUR_VENDOR_BILL_INVOICE_ARRIVAL_QUEUE, true);

    }

    /** 供应商台账到票消息队列与交换机绑定 */
    @Bean
    public Binding bindingVendorBillInvoiceArrival() {
        return BindingBuilder.bind(queueVendorBillInvoiceArrival()).to(eErpExchange()).with(
                MQDefine.PUR_VENDOR_BILL_INVOICE_ARRIVAL_ROUTING_KEY);
    }



    /** 头程单消息交换机 */
    @Bean
    public DirectExchange headOrderExchange() {
        //持久化消息, 消费后不删除
        return new DirectExchange(MQDefine.HEAD_ORDER_EX_CHANGE, true, false);
    }

    /** 头程单爬虫抓取消息回调队列 */
    @Bean
    public Queue headRunSingleGrabCallbackQueue() {
        // 队列持久
        return new Queue(MQDefine.HEAD_RUN_SINGLE_GRAB_CALLBACK_QUEUE, true);
    }

    /** 供应商台账到票消息队列与交换机绑定 */
    @Bean
    public Binding headRunSingleGrabCallbackKey() {
        return BindingBuilder.bind(headRunSingleGrabCallbackQueue()).to(headOrderExchange()).with(
                MQDefine.HEAD_RUN_SINGLE_GRAB_CALLBACK_KEY);
    }


    /** 入库计划单完成节点队列交换机 */
    @Bean
    public DirectExchange inboundResetPurchaseOrderChange() {
        //持久化消息, 消费后不删除
        return new DirectExchange(MQDefine.INBOUND_RESET_PURCHASE_ORDER_CHANGE, true, false);
    }

    /** 入库计划单完成节点队列 */
    @Bean
    public Queue inboundResetPurchaseOrderQueue() {
        // 队列持久
        return new Queue(MQDefine.INBOUND_RESET_PURCHASE_ORDER_QUEUE, true);
    }

    /** 入库计划单完成节点队列key */
    @Bean
    public Binding inboundResetPurchaseOrderKey() {
        return BindingBuilder.bind(inboundResetPurchaseOrderQueue()).to(inboundResetPurchaseOrderChange()).with(
                MQDefine.INBOUND_RESET_PURCHASE_ORDER_KEY);
    }
    /** 生成采购单并执行全流程队列交换机 */
    @Bean
    public CustomExchange purchaseOrderExecutionProcessChange() {
        Map<String, Object> args = new HashMap<>(2);
        args.put("x-delayed-type", "direct");
        //持久化消息, 消费后不删除
        return new CustomExchange(MQDefine.PURCHASE_ORDER_EXECUTION_PROCESS_CHANGE, "x-delayed-message", true,false,args);
    }

    /** 生成采购单并执行全流程队列 */
    @Bean
    public Queue purchaseOrderExecutionProcessQueue() {
        HashMap<String, Object> args = new HashMap<>();
        args.put("x-single-active-consumer", true);
        args.put("x-max-consumers", 1);
        // 队列持久
        return new Queue(MQDefine.PURCHASE_ORDER_EXECUTION_PROCESS_QUEUE, true,false,false,args);
    }

    /** 生成采购单并执行全流程key */
    @Bean
    public Binding purchaseOrderExecutionProcessKey() {
        return BindingBuilder.bind(purchaseOrderExecutionProcessQueue()).to(purchaseOrderExecutionProcessChange()).with(
                MQDefine.PURCHASE_ORDER_EXECUTION_PROCESS_KEY).noargs();
    }

    /** 通知供应商台账获取采购信息 */
    @Bean
    public Queue queueNoticeVendorToGetPurchaseInfo() {
        // 队列持久
        return new Queue(MQDefine.NOTICE_VENDOR_TO_GET_PURCHASE_INFO__QUEUE, true);
    }

    /** 通知供应商台账获取采购信息消息队列与交换机绑定 */
    @Bean
    public Binding bindingNoticeVendorToGetPurchaseInfo() {
        return BindingBuilder.bind(queueNoticeVendorToGetPurchaseInfo()).to(eErpExchange()).with(
                MQDefine.NOTICE_VENDOR_TO_GET_PURCHASE_INFO_ROUTING_KEY);
    }


    /** 商品-SKU映射数据队列  */
    @Bean
    public Queue skuMapQueue() {
        // 队列持久
        return new Queue(MQDefine.SKU_MAP_INFO_QUEUE, true);
    }

    /** sku 映射数据交换机绑定*/
    @Bean
    public Binding bindingNoticeVendorToWayfairSkuMapQueue() {
        return BindingBuilder.bind(skuMapQueue()).to(eErpExchange()).with(
                MQDefine.PUSH_SKU_INFO_KEY);
    }


    /**
     *  平台账单 overStock 队列
     */
    @Bean
    public Queue overStockPaymentQueue() {
        // 队列持久
        return new Queue(FinanceConstant.OVERSTOCK_PAYMENT, true);
    }

    /**
     *  平台账单 wayFair 队列
     */
    @Bean
    public Queue wayFairPaymentQueue() {
        // 队列持久
        return new Queue(FinanceConstant.WAYFAIR_PAYMENT, true);
    }

    /**
     *  平台账单 vc 队列
     */
    @Bean
    public Queue amazonVcPaymentQueue() {
        // 队列持久
        return new Queue(FinanceConstant.AMAZON_VC_PAYMENT, true);
    }
    /**
     * 声明Listing队列
     */
    @Bean
    public Queue queueListingOrderChange() {
        return new Queue(MQDefine.LISTING_INFO_QUEUE, true);
    }


    /**
     * 声明Listing队列
     */
    @Bean
    public Queue queueTiktokCommissionInfoChange() {
        return new Queue(MQDefine.TIKTOK_COMMISSION_INFO_QUEUE, true);
    }


    /**
     * 竞品推送asin信息队列
     */
    @Bean
    public Queue competeAsinInfoQueue() {
        return new Queue(MQDefine.PUSH_ASIN_INFO_QUEUE, true);
    }

    /**
     * 竞品推送asin信息队列绑定到交换机
     */
    @Bean
    public Binding competeAsinInfoQueueBinding() {
        return BindingBuilder.bind(competeAsinInfoQueue())
                .to(eErpExchange())
                .with(MQDefine.PUSH_ASIN_INFO_KEY);
    }
    @Bean
    public Queue adSellerSkuApportionQueue() {
        // 队列持久
        return new Queue(MQDefine.AD_SELLER_SKU_APPORTION_QUEUE, true);
    }

    @Bean
    public Binding adSellerSkuApportionQueueBinding() {
        return BindingBuilder.bind(adSellerSkuApportionQueue()).to(eErpExchange()).with(
                MQDefine.ROUTING_KEY_AD_SELLER_SKU_APPORTION);
    }
    @Bean
    public Queue vcOverStockProductChannelQueue() {
        // 队列持久
        return new Queue(MQDefine.VC_OVERSTOCK_PRODUCT_CHANNEL, true);
    }

    @Bean
    public Binding vcOverStockProductChannelQueueBinding() {
        return BindingBuilder.bind(vcOverStockProductChannelQueue()).to(eErpExchange()).with(
                MQDefine.ROUTING_KEY_VC_OVERSTOCK_PRODUCT_CHANNEL);
    }

    /**
     * 亚马逊FBA退货队列
     *
     * @return
     */
    @Bean
    public Queue amazonFbaReturnQueue() {
        return new Queue(MQDefine.AMAZON_FBA_RETURN_QUEUE, true);
    }

    /**
     * 亚马逊FBM退货队列
     *
     * @return
     */
    @Bean
    public Queue amazonFbmReturnQueue() {
        return new Queue(MQDefine.AMAZON_FBM_RETURN_QUEUE, true);
    }

    /**
     * tiktok webhook 交换机
     * @return
     */
    @Bean
    public DirectExchange tiktokWebhookExchange() {
        //持久化消息, 消费后不删除
        return new DirectExchange(MQDefine.TIKTOK_WEBHOOK_EXCHANGE, true, false);
    }

    /**
     * tiktok webhook 队列
     * @return
     */
    @Bean
    public Queue tiktokWebhookQueue() {
        // 队列持久
        return new Queue(MQDefine.TIKTOK_WEBHOOK_QUEUE, true);

    }

    /**
     * tiktok webhook 绑定队列
     * @return
     */
    @Bean
    public Binding bindingTiktokWebhook() {
        return BindingBuilder.bind(tiktokWebhookQueue()).to(tiktokWebhookExchange()).with(
                MQDefine.TIKTOK_WEBHOOK_ROUNTING_KEY);
    }

    @Bean
    public Queue productChannelsStockQueue() {
        // 队列持久
        return new Queue(MQDefine.ERP_SALE_STOCK_QUEUE, true);
    }

    @Bean
    public Binding productChannelsStockQueueBinding() {
        return BindingBuilder.bind(productChannelsStockQueue()).to(eErpExchange()).with(
                MQDefine.ERP_SALE_STOCK_QUEUE_ROUTING_KEY);
    }
    @Bean
    public Queue tiktokVideoDetailsQueue() {
        // 队列持久
        return new Queue(MQDefine.TIKTOK_BI_VIDEO_DETAILS_QUEUE, true);

    }

    @Bean
    public Binding tiktokVideoDetailsQueueBinding() {
        return BindingBuilder.bind(tiktokVideoDetailsQueue()).to(eErpExchange()).with("#");
    }

    @Bean
    public Queue tiktokVideoDetailsTimeQueue() {
        // 队列持久
        return new Queue(MQDefine.TIKTOK_BI_VIDEO_DETAILS_TIME_QUEUE, true);

    }
    @Bean
    public Binding tiktokVideoDetailsTimeQueueBinding() {
        return BindingBuilder.bind(tiktokVideoDetailsTimeQueue()).to(eErpExchange()).with("#");
    }
    // 定义普通Exchange
    @Bean
    public DirectExchange tiktokRefundExchange() {
        return new DirectExchange(MQDefine.TIKTOK_REFUND_EXCHANGE);
    }

    // 定义普通Queue
    @Bean
    public Queue tiktokRefundQueue() {
        return QueueBuilder.durable(MQDefine.TIKTOK_REFUND_QUEUE)
                .withArgument("x-dead-letter-exchange", MQDefine.TIKTOK_REFUND_DEAD_EXCHANGE) // 设置死信Exchange
                .withArgument("x-dead-letter-routing-key", MQDefine.TIKTOK_REFUND_DEAD_ROUTING_KEY) // 设置死信Routing Key
                .withArgument("x-message-ttl", 5 * 1000)
                .build();
    }
    @Bean
    public Queue tiktokRefundForCompensationQueue() {
        return QueueBuilder.durable(MQDefine.TIKTOK_REFUND_FOR_COMPENSATION_QUEUE)
                .withArgument("x-dead-letter-exchange", MQDefine.TIKTOK_REFUND_DEAD_EXCHANGE) // 设置死信Exchange
                .withArgument("x-dead-letter-routing-key", MQDefine.TIKTOK_REFUND_FOR_COMPENSATION_DEAD_ROUTING_KEY) // 设置死信Routing Key
                .withArgument("x-message-ttl", 30 * 60 * 1000)
                .build();
    }
    @Bean
    public Binding tiktokRefundBinding() {
        return BindingBuilder.bind(tiktokRefundQueue())
                .to(tiktokRefundExchange())
                .with(MQDefine.TIKTOK_REFUND_ROUTING_KEY);
    }
    @Bean
    public Binding tiktokRefundForCompensationBinding() {
        return BindingBuilder.bind(tiktokRefundForCompensationQueue())
                .to(tiktokRefundExchange())
                .with(MQDefine.TIKTOK_REFUND_FOR_COMPENSATION_ROUTING_KEY);
    }


    // 定义死信Queue
    @Bean
    public Queue tiktokRefundDeadQueue() {
        return QueueBuilder.durable(MQDefine.TIKTOK_REFUND_DEAD_QUEUE).build();
    }

    @Bean
    public Queue tiktokRefundForCompensationDeadQueue() {
        return QueueBuilder.durable(MQDefine.TIKTOK_REFUND_FOR_COMPENSATION_DEAD_QUEUE).build();
    }
    @Bean
    public DirectExchange tiktokRefundDeadExchange() {
        return new DirectExchange(MQDefine.TIKTOK_REFUND_DEAD_EXCHANGE,true,false);
    }

    // 将死信Queue绑定到死信Exchange上
    @Bean
    public Binding tiktokRefundDeadBinding() {
        return BindingBuilder.bind(tiktokRefundDeadQueue())
                .to(tiktokRefundDeadExchange())
                .with(MQDefine.TIKTOK_REFUND_DEAD_ROUTING_KEY);
    }
    @Bean
    public Binding tiktokRefundForCompensationDeadBinding() {
        return BindingBuilder.bind(tiktokRefundForCompensationDeadQueue())
                .to(tiktokRefundDeadExchange())
                .with(MQDefine.TIKTOK_REFUND_FOR_COMPENSATION_DEAD_ROUTING_KEY);
    }

    @Bean
    public Queue tiktokShopBrowseQueue() {
        // 队列持久
        return new Queue(MQDefine.TIKTOK_BI_SHOP_BROWSE_QUEUE, true);

    }

    @Bean
    public Binding tiktokProductRecordQueueBinding() {
        return BindingBuilder.bind(tiktokShopBrowseQueue()).to(eErpExchange()).with("#");
    }

    /**
     * 库存同步MQ
     */
    @Bean
    public Queue erpSaleStockSkuQueue() {
        return new Queue(MQDefine.ERP_SALE_STOCK_SKU_QUEUE, true);
    }

    /**
     * 库存同步绑定信息
     */
    @Bean
    public Binding erpSaleStockSkuQueueBinding() {
        return BindingBuilder.bind(erpSaleStockSkuQueue())
                .to(eErpExchange())
                .with(MQDefine.ERP_SALE_STOCK_SKU_QUEUE_ROUTING_KEY);
    }

    /**
     * 库存同步SellersKU-MQ
     */
    @Bean
    public Queue erpSaleStockQueue() {
        return new Queue(MQDefine.ERP_SALE_STOCK_QUEUE, true);
    }

    /**
     * 库存同步SellersKU绑定信息
     */
    @Bean
    public Binding erpSaleStockQueueBinding() {
        return BindingBuilder.bind(erpSaleStockQueue())
                .to(eErpExchange())
                .with(MQDefine.ERP_SALE_STOCK_QUEUE_ROUTING_KEY);
    }

    /**
     * 库存同步SellersKU-MQ
     */
    @Bean
    public Queue skuRestricted() {
        return new Queue(MQDefine.SKU_RESTRICTED_QUEUE, true);
    }

    /**
     * 库存同步sellersku绑定信息
     -----*/
    @Bean
    public Binding skuRestrictedBinding() {
        return BindingBuilder.bind(skuRestricted())
                .to(eErpExchange())
                .with(MQDefine.SKU_RESTRICTED_ROUTING_KEY);
    }

    /**
     * 库存获取队列
     */
    @Bean
    public Queue skuInventoryReq() {
        return new Queue(MQDefine.SKU_INVENTORY_REQ, true);
    }

    /**
     * 库存获取绑定
     -----*/
    @Bean
    public Binding skuInventoryReqBinding() {
        return BindingBuilder.bind(skuInventoryReq())
                .to(eErpExchange())
                .with(MQDefine.SKU_INVENTORY_REQ_ROUTING_KEY);
    }
    /**
     * 库存单独推送
     */
    @Bean
    public Queue skuInventoryRes() {
        return new Queue(MQDefine.SKU_INVENTORY_RES, true);
    }

    /**
     * 库存单独推送绑定
     */
    @Bean
    public Binding skuInventoryResBinding() {
        return BindingBuilder.bind(skuInventoryReq())
                .to(eErpExchange())
                .with(MQDefine.SKU_INVENTORY_RES_ROUTING_KEY);
    }
    /**
     * 获取指定SKU受限信息队列
     */
    @Bean
    public Queue skuRestrictedReqQueue() {
        return new Queue(MQDefine.SKU_RESTRICTED_REQ, true);
    }
    /**
     * 获取指定SKU受限信息队列绑定信息
     */
    @Bean
    public Binding skuRestrictedReqQueueBinding() {
        return BindingBuilder.bind(skuRestrictedReqQueue())
                .to(eErpExchange())
                .with(MQDefine.SKU_RESTRICTED_REQ_ROUTING_KEY);
    }
    /**
     * SKU受限信息队列
     */
    @Bean
    public Queue skuRestrictedResQueue() {
        return new Queue(MQDefine.SKU_RESTRICTED_RES, true);
    }
    /**
     * SKU受限信息队列绑定信息
     */
    @Bean
    public Binding skuRestrictedResQueueBinding() {
        return BindingBuilder.bind(skuRestrictedResQueue())
                .to(eErpExchange())
                .with(MQDefine.SKU_RESTRICTED_RES_ROUTING_KEY);
    }

    @Bean
    public Queue tiktokProductRecordQueue() {
        // 队列持久
        return new Queue(MQDefine.TIKTOK_BI_PRODUCT_RECORD_QUEUE, true);

    }

    @Bean
    public Binding tiktokProductRecordBinding() {
        return BindingBuilder.bind(tiktokProductRecordQueue()).to(eErpExchange()).with("#");
    }

    @Bean
    public Queue tiktokCreatorCidQueue() {
        // 队列持久
        return new Queue(MQDefine.TK_CREATOR_CID_REQ_QUEUE, true);
    }


    @Bean
    public Binding tiktokCreatorCidBinding() {
        return BindingBuilder.bind(tiktokCreatorCidQueue()).to(eErpExchange()).with(
                MQDefine.TK_CREATOR_CID_REQ_ROUTING_KEY);
    }
    @Bean
    public Queue tiktokCreatorCidResQueue() {
        // 队列持久
        return new Queue(MQDefine.TK_CREATOR_CID_RES_QUEUE, true);
    }


    @Bean
    public Binding tiktokCreatorCidResBinding() {
        return BindingBuilder.bind(tiktokCreatorCidResQueue()).to(eErpExchange()).with(
                MQDefine.TK_CREATOR_CID_RES_ROUTING_KEY);
    }


    @Bean
    public Queue tiktokVideoExportQueue() {
        // 队列持久
        return new Queue(MQDefine.TK_CREATOR_VIDEP_EXPORT, true);
    }


    @Bean
    public Binding tiktokVideoExportBinding() {
        return BindingBuilder.bind(tiktokVideoExportQueue()).to(eErpExchange()).with(
                MQDefine.TK_CREATOR_VIDEP_EXPORT_ROUTING_KEY);
    }

     @Bean
    public Queue creatorSampleOrderQueue() {
        // 队列持久
        return new Queue(MQDefine.TK_CREATOR_SAMPLE_ORDER_QUEUE, true);
    }


    @Bean
    public Binding creatorSampleOrderQueueBinding() {
        return BindingBuilder.bind(creatorSampleOrderQueue()).to(eErpExchange()).with(
                MQDefine.TK_CREATOR_SAMPLE_ORDER_ROUTING_KEY);
    }

    @Bean
    public Queue sellerSkuItemUpdateQueue(){
        return new Queue(MQDefine.SELLER_SKU_ITEM_UPDATE_QUEUE, true);
    }

    @Bean
    public Binding sellerSkuItemUpdateQueueBinding(){
        return BindingBuilder.bind(sellerSkuItemUpdateQueue()).to(eErpExchange()).with(
                MQDefine.SELLER_SKU_ITEM_UPDATE_QUEUE_ROUTING_KEY
        );
    }


    @Bean
    public Queue wayfairProductPriceQueue(){
        return new Queue(MQDefine.WAYFAIR_PRODUCT_PRICE_QUEUE, true);
    }


    @Bean
    public Binding wayfairProductPriceQueueBinding(){
        return BindingBuilder.bind(wayfairProductPriceQueue()).to(eErpExchange()).with(
                MQDefine.WAYFAIR_PRODUCT_PRICE_QUEUE_ROUTING_KEY
        );
    }


    @Bean
    public Queue sellerSkuInventoryTask(){
        return new Queue(MQDefine.SELLER_SKU_INVENTORY_TASK_QUEUE, true);
    }


    @Bean
    public Binding sellerSkuInventoryTaskBinding(){
        return BindingBuilder.bind(sellerSkuInventoryTask()).to(eErpExchange()).with(
                MQDefine.SELLER_SKU_INVENTORY_TASK_QUEUE_ROUTING_KEY
        );
    }


    /**
     * Temu违规信息队列
     * @return
     */
    @Bean
    public Queue temuViolationQueue(){
        return new Queue(MQDefine.TEMU_VIOLATION_QUEUE, true);
    }


    @Bean
    public Binding temuViolationQueueBiding(){
        return BindingBuilder.bind(temuViolationQueue())
                .to(eErpExchange()).with(MQDefine.TEMU_VIOLATION_ROUTING_KEY);
    }

    @Bean
    public Queue productChannelMultichannelQueue(){
        return new Queue(MQDefine.PRODUCT_CHANNEL_EDIT_QUEUE, true);
    }


    @Bean
    public Binding productChannelMultichannelQueueVBinding(){
        return BindingBuilder.bind(productChannelMultichannelQueue())
                .to(eErpExchange()).with(MQDefine.PRODUCT_CHANNEL_EDIT_QUEUE_ROUTING_KEY);
    }

    @Bean
    public Queue xsmSellerFeedBackQueue() {
        return new Queue(MQDefine.XSM_FEED_BACK_QUEUE, true);
    }

    @Bean
    public Binding xsmSellerFeedBackQueueBinding() {
        return BindingBuilder.bind(xsmSellerFeedBackQueue())
                .to(eErpExchange())
                .with(MQDefine.XSM_FEED_BACK_QUEUE_ROUTING_KEY);
    }

    @Bean
    public Queue goodsReceivedAndPayableQueue() {
        // 收货差异表格导出队列
        return new Queue(MQDefine.PURCHASE_DELIVERY_DIFF_EXPORT_QUEUE, true);
    }

    @Bean
    public Binding goodsReceivedAndPayableBinding() {
        // 收货差异表格导出队列绑定
        return BindingBuilder.bind(goodsReceivedAndPayableQueue())
                .to(eErpExchange())
                .with(MQDefine.PURCHASE_DELIVERY_DIFF_EXPORT_QUEUE);
    }

    @Bean
    public Queue walmartItemQueue() {
        return new Queue(MQDefine.WALMART_ITEM_QUEUE, true);
    }

    @Bean
    public Binding walmartItemQueueBinding() {
        return BindingBuilder.bind(walmartItemQueue())
                .to(eErpExchange())
                .with(MQDefine.WALMART_ITEM_QUEUE_ROUTING_KEY);
    }


    /**
     * tk订单转发到联系客户队列
     */
    @Bean
    public Queue orderToContactCustomerQueue() {
        return new Queue(MQDefine.ORDER_TO_CONTACT_CUSTOMER_QUEUE, true);
    }

    /**
     * 竞品推送asin信息队列绑定到交换机
     */
    @Bean
    public Binding orderToContactCustomerQueueBinding() {
        return BindingBuilder.bind(orderToContactCustomerQueue())
                .to(eErpExchange())
                .with(MQDefine.ORDER_TO_CONTACT_CUSTOMER_KEY);
    }

    @Bean
    public Queue ticketConfirmationUponArrivalQueue() {
        // 采购入库到票确认队列
        return new Queue(MQDefine.FINANCIAL_RECEIPT_CONFIRMATION_NOTIFICATION_QUEUE, true);
    }

    @Bean
    public Binding ticketConfirmationUponArrivalBinding() {
        // 采购入库到票确认绑定交换机和路由key
        return BindingBuilder.bind(ticketConfirmationUponArrivalQueue())
                .to(eErpExchange())
                .with(MQDefine.FINANCIAL_RECEIPT_CONFIRMATION_NOTIFICATION_QUEUE);
    }

    /**
     * 手动修改库存队列
     */
    @Bean
    public Queue manualInventoryUpdateQueue() {
        return new Queue(MQDefine.MANUAL_INVENTORY_UPDATE_REQUEST_QUEUE, true);
    }

    @Bean
    public Binding manualInventoryUpdateQueueBinding() {
        return BindingBuilder.bind(manualInventoryUpdateQueue())
                .to(eErpExchange())
                .with(MQDefine.MANUAL_INVENTORY_UPDATE_REQUEST_ROUTING_KEY);
    }


    /**
     * tk订单转发到联系客户队列 walmartListingInfoQueue
     */
    @Bean
    public Queue walmartListingInfoQueue() {
        return new Queue(MQDefine.WALMART_LISTING_INFO_QUEUE, true);
    }
    /**
     * walmart promotion message receive queue
     * @return
     */
    @Bean
    public Queue walmartPromotionMessageReceiveQueue() {
        return new Queue(MQDefine.WALMART_PROMOTION_MESSAGE_RECEIVE_QUEUE, true);
    }

    /**
     * walmart promotion message receive queue binding
     * @return
     */
    @Bean
    public Binding walmartPromotionMessageReceiveQueueBinding() {
        return BindingBuilder.bind(walmartPromotionMessageReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.WALMART_PROMOTION_MESSAGE_RECEIVE_ROUTING_KEY);
    }

    /**
     * temu渠道订单取消消息接收队列
     * @return
     */
    @Bean
    public Queue saleOrderCancelReceiveQueue() {
        return new Queue(MQDefine.SALE_ORDER_CANCEL_TEMU_MESSAGE_QUEUE, true);
    }

    @Bean
    public Binding saleOrderCancelReceiveQueueBinding() {
        return BindingBuilder.bind(saleOrderCancelReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.SALE_ORDER_CANCEL_TEMU_MESSAGE_ROUTING_KEY);
    }


    /**
     * temu渠道订单取消同意或者拒绝消息发送队列
     * @return
     */
    @Bean
    public Queue saleOrderCancelAgreeMessageSendQueue() {
        return new Queue(MQDefine.SALE_ORDER_CANCEL_TEMU_AGREE_MESSAGE_SEND_QUEUE, true);
    }

    @Bean
    public Binding saleOrderCancelAgreeMessageSendQueueBinding() {
        return BindingBuilder.bind(saleOrderCancelAgreeMessageSendQueue())
                .to(eErpExchange())
                .with(MQDefine.SALE_ORDER_CANCEL_TEMU_AGREE_SEND_MESSAGE_ROUTING_KEY);
    }

    /**
     * temu渠道订单取消同意或者拒绝消息接收队列
     * @return
     */
    @Bean
    public Queue saleOrderCancelAgreeMessageReceiveQueue() {
        return new Queue(MQDefine.SALE_ORDER_CANCEL_TEMU_AGREE_MESSAGE_RECEIVE_QUEUE, true);
    }

    @Bean
    public Binding saleOrderCancelAgreeMessageReceiveQueueBinding() {
        return BindingBuilder.bind(saleOrderCancelAgreeMessageReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.SALE_ORDER_CANCEL_TEMU_AGREE_RECEIVE_MESSAGE_ROUTING_KEY);
    }


    /**
     * 订单取消拦截中或撤回拦截消息接收队列
     * @return
     */
    @Bean
    public CustomExchange saleOrderCancelInterceptOrWithdrawReceiveExchange() {
        Map<String, Object> args = new HashMap<>(2);
        args.put("x-delayed-type", "direct");
        //持久化消息, 消费后不删除
        return new CustomExchange(MQDefine.SALE_ORDER_CANCEL_INTERCEPT_OR_WITHDRAW_EXCHANGE, "x-delayed-message", true,false,args);

    }
    @Bean
    public Queue saleOrderCancelInterceptOrWithdrawReceiveQueue() {
        return new Queue(MQDefine.SALE_ORDER_CANCEL_INTERCEPT_OR_WITHDRAW_MESSAGE_QUEUE, true);
    }

    @Bean
    public Binding saleOrderCancelInterceptOrWithdrawReceiveQueueBinding() {
        return BindingBuilder.bind(saleOrderCancelInterceptOrWithdrawReceiveQueue())
                .to(saleOrderCancelInterceptOrWithdrawReceiveExchange())
                .with(MQDefine.SALE_ORDER_CANCEL_INTERCEPT_OR_WITHDRAW_MESSAGE_ROUTING_KEY).noargs();
    }

    /**
     * 接收跟卖监控asin信息队列
     */
    @Bean
    public Queue receiveFollowAsinInfoQueue() {
        return new Queue(MQDefine.RECEIVE_FOLLOW_ASIN_INFO_QUEUE, true);
    }

    /**
     * mar adjustment message receive queue
     * @return
     */
    @Bean
    public Queue marAdjustmentMessageReceiveQueue() {
        return new Queue(MQDefine.MAR_ADJUSTMENT_PRICE_MANAGEMENT_QUEUE, true);
    }

    /**
     * mar adjustment message receive queue binding
     * @return
     */
    @Bean
    public Binding marAdjustmentMessageReceiveQueueBinding() {
        return BindingBuilder.bind(marAdjustmentMessageReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_ADJUSTMENT_PRICE_MANAGEMENT_QUEUE_ROUTING_KEY);
    }

    /**
     * mar adjustment message agree or reject send queue
     * @return
     */
    @Bean
    public Queue marAdjustmentMessageAgreeOrRejectQueue() {
        return new Queue(MQDefine.MAR_ADJUSTMENT_PRICE_MANAGEMENT_SEND_QUEUE, true);
    }

    /**
     * mar adjustment message agree or reject send queue binding
     * @return
     */
    @Bean
    public Binding marAdjustmentMessageAgreeOrRejectQueueBinding() {
        return BindingBuilder.bind(marAdjustmentMessageAgreeOrRejectQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_ADJUSTMENT_PRICE_MANAGEMENT_SEND_QUEUE_ROUTING_KEY);
    }

    /**
     * mar adjustment message agree or reject receive queue
     * @return
     */
    @Bean
    public Queue marAdjustmentMessageAgreeOrRejectReceiveQueue() {
        return new Queue(MQDefine.MAR_ADJUSTMENT_PRICE_MANAGEMENT_RECEIVE_QUEUE, true);
    }

    /**
     * mar adjustment message agree or reject receive queue binding
     * @return
     */
    @Bean
    public Binding marAdjustmentMessageAgreeOrRejectReceiveQueueBinding() {
        return BindingBuilder.bind(marAdjustmentMessageAgreeOrRejectReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_ADJUSTMENT_PRICE_MANAGEMENT_RECEIVE_QUEUE_ROUTING_KEY);
    }

    /**
     * 接收Temu父售后单信息 queue
     */
    @Bean
    public Queue receiveTemuParentReturnInfoQueue() {
        return new Queue(MQDefine.RECEIVE_TEMU_PARENT_RETURN_INFO_QUEUE, true);
    }


    /**
     * 接收Temu售后逆向物流信息 queue
     */
    @Bean
    public Queue receiveTemuReverseLogisticInfoQueue() {
        return new Queue(MQDefine.RECEIVE_TEMU_REVERSE_LOGISTIC_INFO_QUEUE, true);
    }

    /**
     * 接收Temu退货原因 queue
     */
    @Bean
    public Queue receiveTemuReturnReasonInfoQueue() {
        return new Queue(MQDefine.RECEIVE_TEMU_RETURN_REASON_INFO_QUEUE, true);
    }

    /**
     * temu promotion message receive queue
     * @return
     */
    @Bean
    public Queue temuPromotionMessageReceiveQueue() {
        return new Queue(MQDefine.TEMU_PROMOTION_MESSAGE_RECEIVE_QUEUE, true);
    }

    /**
     * temu promotion message receive queue binding
     * @return
     */
    @Bean
    public Binding temuPromotionMessageReceiveQueueBinding() {
        return BindingBuilder.bind(temuPromotionMessageReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_PROMOTION_MESSAGE_RECEIVE_ROUTING_KEY);
    }





    /**
     * @description: 工单订单取消拦截单重试请求队列
     * @author: Moore
     * @date: 2024/8/26 11:47
     * @param
     * @return: org.springframework.amqp.core.Queue
     **/
    @Bean
    public Queue tiketInterceptRetryQueue() {
        return QueueBuilder.durable(MQDefine.SC_TICKET_ORDER_CANCENL_INTERCEPT_RETRY_QUEUE)  //需改时间队列需删除后才会生效
                .withArgument("x-dead-letter-exchange", MQDefine.SC_TICKET_DEAD_EXCHANGE) // 设置死信Exchange
                .withArgument("x-dead-letter-routing-key", MQDefine.SC_TICKET_INTERCEPT_RETRY_ROUTING_KEY) // 设置死信Routing Key
                .withArgument("x-message-ttl", 3 * 60 * 1000)
                .build();
    }


    /**
     * 工单死信交换机
     *
     * @return
     */
    @Bean
    public DirectExchange ticketDeadEx() {
        return new DirectExchange(MQDefine.SC_TICKET_DEAD_EXCHANGE);
    }

    /**
     * 工单订单取消拦截重试接收队列
     *
     * @return
     */
    @Bean
    public Queue ticketOrderCancelIntercpetRetryDeadQueue() {
        return QueueBuilder.durable(MQDefine.SC_TICKET_ORDER_CANCENL_INTERCEPT_RETRY_OPERATION_QUEUE).build();
    }

    /**
     * 订单取消拦截重试死信队列绑定
     *
     * @return
     */
    @Bean
    public Binding tiketOrderCancelInterceptRetry() {
        return BindingBuilder.bind(ticketOrderCancelIntercpetRetryDeadQueue())
                .to(ticketDeadEx())
                .with(MQDefine.SC_TICKET_INTERCEPT_RETRY_ROUTING_KEY);
    }


    /**
     * walmart渠道订单取消消息接收队列
     * @return
     */
    @Bean
    public Queue saleOrderCancelWalmartReceiveQueue() {
        return new Queue(MQDefine.SALE_ORDER_CANCEL_WALMART_MESSAGE_QUEUE, true);
    }

    @Bean
    public Binding saleOrderCancelWalmartReceiveQueueBinding() {
        return BindingBuilder.bind(saleOrderCancelWalmartReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.SALE_ORDER_CANCEL_WALMART_MESSAGE_ROUTING_KEY);
    }


    /**
     * temu渠道订单取消拒绝消息发送
     * @return
     */
    @Bean
    public CustomExchange temuRejectSendExchange() {
        Map<String, Object> args = new HashMap<>(2);
        args.put("x-delayed-type", "direct");
        return new CustomExchange(MQDefine.TEMU_ORDER_CANCEL_REJECT_MESSAGE_SEND_EXCHANGE, "x-delayed-message", true,false,args);
    }
    @Bean
    public Queue temuRejectSendQueue() {
        return new Queue(MQDefine.TEMU_ORDER_CANCEL_REJECT_MESSAGE_SEND_QUEUE, true);
    }
    @Bean
    public Binding temuRejectSendQueueBinding() {
        return BindingBuilder.bind(temuRejectSendQueue())
                .to(temuRejectSendExchange())
                .with(MQDefine.TEMU_ORDER_CANCEL_REJECT_MESSAGE_SEND_KEY).noargs();
    }


    @Bean
    public Queue temuOrderCancelCompensationQueue() {
        return new Queue(MQDefine.TEMU_BUYER_NOT_CONFIRM_ORDER_QUEUE, true);
    }
    @Bean
    public Binding temuOrderCancelCompensationQueueBinding() {
        return BindingBuilder.bind(temuOrderCancelCompensationQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_BUYER_NOT_CONFIRM_ORDER_ROUTING_KEY);
    }


    /**
     * 调价审批消息回传补偿消息发送队列
     * @return
     */
    @Bean
    public Queue marAdjustPriceRetryQueue() {
        return QueueBuilder.durable(MQDefine.MAR_ADJUSTMENT_PRICE_RETRY_REQUEST_QUEUE)  //需改时间队列需删除后才会生效
                .withArgument("x-dead-letter-exchange", MQDefine.MAR_ADJUSTMENT_PRICE_RETRY_EXCHANGE) // 设置死信Exchange
                .withArgument("x-dead-letter-routing-key", MQDefine.MAR_ADJUSTMENT_PRICE_RETRY_REQUEST_ROUTING_KEY) // 设置死信Routing Key
                .withArgument("x-message-ttl", 3 * 60 * 1000)
                .build();
    }


    /**
     * 调价审批消息回传补偿死信交换机
     *
     * @return
     */
    @Bean
    public DirectExchange marAdjustPriceRetryEx() {
        return new DirectExchange(MQDefine.MAR_ADJUSTMENT_PRICE_RETRY_EXCHANGE);
    }

    /**
     * 调价审批消息回传补偿接收队列
     *
     * @return
     */
    @Bean
    public Queue marAdjustPriceRetryDeadQueue() {
        return QueueBuilder.durable(MQDefine.MAR_ADJUSTMENT_PRICE_RETRY_RESPONSE_QUEUE).build();
    }

    /**
     * 调价审批消息回传补偿绑定
     *
     * @return
     */
    @Bean
    public Binding marAdjustPriceRetryRetry() {
        return BindingBuilder.bind(marAdjustPriceRetryDeadQueue())
                .to(marAdjustPriceRetryEx())
                .with(MQDefine.MAR_ADJUSTMENT_PRICE_RETRY_REQUEST_ROUTING_KEY);
    }



    /**
     * promotion抓取消息发送队列
     *
     * @return
     */
    @Bean
    public Queue amzPromotionCaptureSendQueue() {
        return QueueBuilder.durable(MQDefine.AMZ_PROMOTIONS_CAPTURE_SEND_SYNC_QUEUE).build();
    }

    /**
     * promotion抓取消息发送队列绑定
     *
     * @return
     */
    @Bean
    public Binding amzPromotionCaptureSendQueueBinding() {
        return BindingBuilder.bind(amzPromotionCaptureSendQueue())
                .to(eErpExchange())
                .with(MQDefine.AMZ_PROMOTIONS_CAPTURE_SEND_SYNC_KEY);
    }

    /**
     * promotion抓取消息返回队列
     *
     * @return
     */
    @Bean
    public Queue amzPromotionCaptureReturnQueue() {
        return QueueBuilder.durable(MQDefine.AMZ_PROMOTIONS_CAPTURE_RETURN_SYNC_QUEUE).build();
    }

    /**
     * promotion抓取消息返回队列绑定
     *
     * @return
     */
    @Bean
    public Binding amzPromotionCaptureReturnQueueBinding() {
        return BindingBuilder.bind(amzPromotionCaptureReturnQueue())
                .to(eErpExchange())
                .with(MQDefine.AMZ_PROMOTIONS_CAPTURE_RETURN_SYNC_KEY);
    }



    /**
     * temu促销发送队列
     *
     * @return
     */
    @Bean
    public Queue temuPromotionSendQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_PROMOTIONS_MESSAGE_SEND_QUEUE).build();
    }

    /**
     * temu促销发送队列绑定
     *
     * @return
     */
    @Bean
    public Binding temuPromotionSendQueueBinding() {
        return BindingBuilder.bind(temuPromotionSendQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_PROMOTIONS_MESSAGE_SEND_ROUTING_KEY);
    }


    /**
     * temu促销接收队列
     *
     * @return
     */
    @Bean
    public Queue temuPromotionReceiveQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_PROMOTIONS_MESSAGE_RECEIVE_QUEUE).build();
    }

    /**
     * temu促销接收队列绑定
     *
     * @return
     */
    @Bean
    public Binding temuPromotionReceiveQueueBinding() {
        return BindingBuilder.bind(temuPromotionReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_PROMOTIONS_MESSAGE_RECEIVE_ROUTING_KEY);
    }


    /**
     * temu促销平台活动接收队列
     *
     * @return
     */
    @Bean
    public Queue temuPromotionPaltformReceiveQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_PROMOTIONS_PLATFORM_MESSAGE_QUEUE).build();
    }

    /**
     * temu促销平台活动本本店铺接收队列
     *
     * @return
     */
    @Bean
    public Queue temuPromotionPaltformLocalShopReceiveQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_PROMOTIONS_PLATFORM_LOCAL_SHOP_MESSAGE_QUEUE).build();
    }

    /**
     * temu促销平台活动场次信息接收队列
     *
     * @return
     */
    @Bean
    public Queue temuPromotionPaltformSessionReceiveQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_PROMOTIONS_PLATFORM_SESSION_MESSAGE_QUEUE).build();
    }

    /**
     * temu促销平台活动接收队列绑定
     *
     * @return
     */
    @Bean
    public Binding temuPromotionPaltformReceiveQueueBinding() {
        return BindingBuilder.bind(temuPromotionPaltformReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_PROMOTIONS_PLATFORM_MESSAGE_ROUTING_KEY);
    }


    /**
     * temu促销商品列表接收队列
     *
     * @return
     */
    @Bean
    public Queue temuPromotionProductListReceiveQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_PROMOTIONS_PRODUCT_MESSAGE_QUEUE).build();
    }

    /**
     * temu促销商品列表指定三个类型
     *
     * @return
     */
    @Bean
    public Queue temuPromotionProducAssignListReceiveQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_PROMOTIONS_PRODUCT_ASSIGN_MESSAGE_QUEUE).build();
    }

    /**
     * temu促销商品列表接收队列(本本)
     *
     * @return
     */
    @Bean
    public Queue temuPromotionProductListLocalShopeceiveQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_PROMOTIONS_PRODUCT_LOCAL_SHOP_MSG_QUEUE).build();
    }


    /**
     * temu促销商品列表接收队列绑定
     *
     * @return
     */
    @Bean
    public Binding temuPromotionProductListReceiveQueueBinding() {
        return BindingBuilder.bind(temuPromotionProductListReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_PROMOTIONS_PRODUCT_MESSAGE_ROUTING_KEY);
    }



    /**
     * temu流量分析消息接收队列
     *
     * @return
     */
    @Bean
    public Queue temuPromotionFlowAnalysisReceiveQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_PROMOTIONS_FLOW_ANALYSE_RECEIVE_QUEUE).build();
    }

    /**
     * temu流量分析消息接收队列绑定
     *
     * @return
     */
    @Bean
    public Binding temuPromotionFlowAnalysisReceiveQueueBinding() {
        return BindingBuilder.bind(temuPromotionFlowAnalysisReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_PROMOTIONS_FLOW_ANALYSE_RECEIVE_ROUTING_KEY);
    }


    /**
     * TEMU举证图片发送 队列
     */
    @Bean
    public Queue temuAppwalImgSendQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_APPWAL_IMG_SEND_QUEUE).build();
    }

    /**
     * 绑定TEMU举证发送交换机
     *
     * @return
     */
    @Bean
    public Binding temuAppwalImgSendQueueBinding() {
        return BindingBuilder.bind(temuAppwalImgSendQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_APPWAL_IMAGE_KEY);
    }




    /**
     * TEMU举证图片接收 队列
     */
    @Bean
    public Queue temuAppwalImgResponseQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_APPWAL_IMG_RESPONSE_QUEUE).build();
    }
    /**
     * 绑定TEMU举证接收交换机
     *
     * @return
     */
    @Bean
    public Binding temuAppwalImgResponseQueueBinding() {
        return BindingBuilder.bind(temuAppwalImgResponseQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_APPWAL_IMG_RESPONSE_KEY);
    }

    /**
     * 申诉提交队列
     */
    @Bean
    public Queue appealSubmitQueue() {
        return new Queue(MQDefine.MAR_ORDER_PACKAGE_APPEAL_SUBMIT_QUEUE, true);
    }

    /**
     * 申诉提交队列绑定交换机
     * @return
     */
    @Bean
    public Binding appealSubmitQueueBinding() {
        return BindingBuilder.bind(appealSubmitQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_ORDER_PACKAGE_APPEAL_SUBMIT_KEY);
    }


    /**
     * 申诉提交队列
     */
    @Bean
    public Queue appealSubmitLocalShopQueue() {
        return new Queue(MQDefine.MAR_ORDER_PACKAGE_APPEAL_LOCAL_SHOP_SUBMIT_QUEUE, true);
    }

    /**
     * 申诉提交队列绑定交换机
     * @return
     */
    @Bean
    public Binding appealLocalShopSubmitQueueBinding() {
        return BindingBuilder.bind(appealSubmitLocalShopQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_ORDER_PACKAGE_APPEAL_LOCAL_SHOP_SUBMIT_KEY);
    }




    /**
     * 申诉提交结果队列
     */
    @Bean
    public Queue appealSubmitResultQueue() {
        return new Queue(MQDefine.MAR_ORDER_PACKAGE_APPEAL_SUBMIT_RESULT_QUEUE, true);
    }

    /**
     * 申诉结果队列
     */
    @Bean
    public Queue appealResultQueue() {
        return new Queue(MQDefine.MAR_ORDER_PACKAGE_APPEAL_RESULT_QUEUE, true);
    }



    /**
     * 申诉结果队列-违规申诉
     */
    @Bean
    public Queue appealViolationResultQueue() {
        return new Queue(MQDefine.MAR_ORDER_VIOLATION_APPEAL_RESULT_QUEUE, true);
    }
    /**
     * temu渠道订单同步消息接收队列
     * @return
     */
    @Bean
    public Queue saleOrderCancelAgreeAsyncMessageReceiveQueue() {
        return new Queue(MQDefine.SALE_ORDER_CANCEL_ASYNC_MESSAGE_RECEIVE_QUEUE, true);
    }

    @Bean
    public Binding saleOrderCancelAgreeAsyncMessageReceiveQueueBinding() {
        return BindingBuilder.bind(saleOrderCancelAgreeAsyncMessageReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.SALE_ORDER_CANCEL_ASYNC_RECEIVE_MESSAGE_ROUTING_KEY);
    }


    /**
     * 接收Temu包裹信息
     */
    @Bean
    public Queue receiveTemuPackageInfoue() {
        return new Queue(MQDefine.RECEIVE_TEMU_PACKAGE_INFO_QUEUE, true);
    }

    //temu包裹信息交换机绑定
    @Bean
    public Binding receiveTemuPackageInfoueBinding() {
        return BindingBuilder.bind(receiveTemuPackageInfoue())
                .to(eErpExchange())
                .with(MQDefine.RECEIVE_TEMU_PACKAGE_INFO_QUEUE); //key与队列名一直
    }



    /**
     * 发送temu_trakck号不一致信息
     */
    @Bean
    public Queue sendTemuPackUnmatchedQueue() {
        return new Queue(MQDefine.SEND_TEMU_PACK_UNMATCHED_QUEUE_ANE_KEY, true);
    }



    /**
     * @description: temu包裹号信息不一致绑定
     * @author: Moore
     * @date: 2024/11/19 10:29
     * @param
     * @return: org.springframework.amqp.core.Binding
    **/
    @Bean
    public Binding sendTemuPackUnmatchedQueueBinding() {
        return BindingBuilder.bind(sendTemuPackUnmatchedQueue())
                .to(eErpExchange())
                .with(MQDefine.SEND_TEMU_PACK_UNMATCHED_QUEUE_ANE_KEY); //key与队列名一直
    }


    /**
     * 接收temuTrack回传失败消息
     */
    @Bean
    public Queue receiveemuPackErrorQueue() {
        return new Queue(MQDefine.RECEIVE_TEMU_TRACKING_ERROR_QUEUE, true);
    }


    /**
     * @param
     * @description: 接收temuTrack回传失败交换机绑定
     * @author: Moore
     * @date: 2024/11/19 10:29
     * @return: org.springframework.amqp.core.Binding
     **/
    @Bean
    public Binding receiveemuPackErrorBinding() {
        return BindingBuilder.bind(receiveemuPackErrorQueue())
                .to(eErpExchange())
                .with(MQDefine.RECEIVE_TEMU_TRACKING_ERROR_QUEUE); //key与队列名一直
    }



        /**
         * 接收店铺服务质量消息
         */
    @Bean
    public Queue receiveTemuShopServiceQualityQueue() {
        return new Queue(MQDefine.MAR_SHOP_SERVICE_QUALITY_QUEUE, true);
    }

    /**
     * 接收店铺绩效资金限制信息
     */
    @Bean
    public Queue receiveTemuShopPerformanceInfoQueue() {
        return new Queue(MQDefine.MAR_SHOP_PERFORMANCE_FINANCIAL_LIMIT_QUEUE, true);
    }

    //店铺绩效资金限制交换机绑定
    @Bean
    public Binding receiveTemuShopPerformanceInfoQueueBinding() {
        return BindingBuilder.bind(receiveTemuShopPerformanceInfoQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_SHOP_PERFORMANCE_FINANCIAL_LIMIT_QUEUE); //key与队列名一直
    }


    /**
     * 接收店铺绩效资金限制明细信息
     */
    @Bean
    public Queue receiveTemuShopPerformanceInfoItemQueue() {
        return new Queue(MQDefine.MAR_SHOP_PERFORMANCE_FINANCIAL_LIMIT_ITEM_QUEUE, true);
    }

    //店铺绩效资金限制明细交换机绑定
    @Bean
    public Binding receiveTemuShopPerformanceInfoItemQueueBinding() {
        return BindingBuilder.bind(receiveTemuShopPerformanceInfoItemQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_SHOP_PERFORMANCE_FINANCIAL_LIMIT_ITEM_QUEUE); //key与队列名一直
    }


    @Bean
    public Queue wfsShipmentReservationQueue() {
        return new Queue(MQDefine.WFS_SHIPMENT_RESERVATION_QUEUE, true);
    }


    @Bean
    public Binding wfsShipmentReservationQueueBinding() {
        return BindingBuilder.bind(wfsShipmentReservationQueue())
                .to(eErpExchange())
                .with(MQDefine.WFS_SHIPMENT_RESERVATION_QUEUE); //key与队列名一直
    }

    @Bean
    public Queue walmartFulfillmentInboundShipmentsQueue() {
        return new Queue(MQDefine.WALMART_FULFILLMENT_INBOUND_SHIPMENTS_QUEUE, true);
    }

    @Bean
    public Binding walmartFulfillmentInboundShipmentsQueueBinding() {
        return BindingBuilder.bind(walmartFulfillmentInboundShipmentsQueue())
                .to(eErpExchange())
                .with(MQDefine.WALMART_FULFILLMENT_INBOUND_SHIPMENTS_QUEUE); //key与队列名一直
    }

    /**
     * temu促销发送队列(官方大促，限时秒杀，清仓甩卖)
     *
     * @return
     */
    @Bean
    public Queue temuPromotionNewSendQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_PROMOTIONS_MESSAGE_NEW_SEND_QUEUE).build();
    }

    /**
     * temu促销发送队列绑定
     *
     * @return
     */
    @Bean
    public Binding temuPromotionNewSendQueueBinding() {
        return BindingBuilder.bind(temuPromotionNewSendQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_PROMOTIONS_MESSAGE_NEW_SEND_QUEUE);
    }


    /**
     * temu促销数据接收队列(官方大促，限时秒杀，清仓甩卖)
     *
     * @return
     */
    @Bean
    public Queue temuPromotionNewReceiveQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_PROMOTIONS_MESSAGE_NEW_LIMIT_QUEUE).build();
    }

    /**
     * temu促销数据接收队列绑定
     *
     * @return
     */
    @Bean
    public Binding temuPromotionNewReceiveQueueBinding() {
        return BindingBuilder.bind(temuPromotionNewReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_PROMOTIONS_MESSAGE_NEW_LIMIT_QUEUE);
    }

    /**
     * 联系客户接收ebay订单
     * @return
     */
    @Bean
    public Queue marEbayOrderContactCustomerQueue() {
        return new Queue(MQDefine.MAR_EBAY_ORDER_CONTACT_CUSTOMER_QUEUE, true);
    }

    @Bean
    public Binding marEbayOrderContactCustomerQueueBinding() {
        return BindingBuilder.bind(marEbayOrderContactCustomerQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_EBAY_ORDER_CONTACT_CUSTOMER_ROUTING_KEY);
    }
    /**
     * 联系客户接收ebay订单
     * @return
     */
    @Bean
    public Queue marEbayOrderContactCustomerDeliveredQueue() {
        return new Queue(MQDefine.MAR_EBAY_ORDER_CONTACT_CUSTOMER_DELIVERED_QUEUE, true);
    }

    @Bean
    public Binding marEbayOrderContactCustomerDeliveredQueueBinding() {
        return BindingBuilder.bind(marEbayOrderContactCustomerDeliveredQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_EBAY_ORDER_CONTACT_CUSTOMER_DELIVERED_ROUTING_KEY);
    }


    /**
     * temu促销美本数据接收队列
     *
     * @return
     */
    @Bean
    public Queue temuPromotionMeiBenReceiveQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_PROMOTIONS_MESSAGE_MEI_BEN_QUEUE).build();
    }

    /**
     * temu促销美本数据接收队列绑定
     *
     * @return
     */
    @Bean
    public Binding temuPromotionMeiBenReceiveQueueBinding() {
        return BindingBuilder.bind(temuPromotionMeiBenReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_PROMOTIONS_MESSAGE_MEI_BEN_QUEUE);
    }


    /**
     * temu促销优惠券美本数据接收队列
     *
     * @return
     */
    @Bean
    public Queue temuCouponMeiBenReceiveQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_COUPON_MESSAGE_MEI_BEN_QUEUE).build();
    }

    /**
     * temu促销优惠券美本数据接收队列绑定
     *
     * @return
     */
    @Bean
    public Binding temuCouponMeiBenReceiveQueueBinding() {
        return BindingBuilder.bind(temuCouponMeiBenReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_COUPON_MESSAGE_MEI_BEN_QUEUE);
    }


    /**
     * temu促销半托管优惠券数据接收队列
     *
     * @return
     */
    @Bean
    public Queue temuCouponSeiManagedReceiveQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_COUPON_MESSAGE_SEI_MANAGED_QUEUE).build();
    }

    /**
     * temu促销半托管优惠券数据接收队列绑定
     *
     * @return
     */
    @Bean
    public Binding temuCouponSeiManagedReceiveQueueBinding() {
        return BindingBuilder.bind(temuCouponSeiManagedReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_COUPON_MESSAGE_SEI_MANAGED_QUEUE);
    }


    /**
     * temu促销美本(创建修改)消息发送队列
     *
     * @return
     */
    @Bean
    public Queue temuPromotionMeibenMessageSendQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_PROMOTION_MEIBEN_MESSAGE_QUEUE).build();
    }

    /**
     * temu促销美本(创建修改)消息发送队列绑定
     *
     * @return
     */
    @Bean
    public Binding temuPromotionMeibenMessageSendQueueBinding() {
        return BindingBuilder.bind(temuPromotionMeibenMessageSendQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_PROMOTION_MEIBEN_MESSAGE_QUEUE);
    }

    /**
     *  配送费相关报表数据接收队列
     * @return
     */
    @Bean
    public Queue marDeliveryFeeReportQueue() {
        return new Queue(MQDefine.MAR_DELIVERY_FEE_REPORT_QUEUE, true);
    }


    /**
     * walmart-dsv渠道订单取消消息接收队列
     * @return
     */
    @Bean
    public Queue saleOrderCancelWalmartDsvReceiveQueue() {
        return new Queue(MQDefine.SALE_ORDER_CANCEL_WALMART_DSV_MESSAGE_QUEUE, true);
    }

    @Bean
    public Binding saleOrderCancelWalmartDsvReceiveQueueBinding() {
        return BindingBuilder.bind(saleOrderCancelWalmartDsvReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.SALE_ORDER_CANCEL_WALMART_DSV_MESSAGE_QUEUE);
    }


    /**
     * 订单逾期消息队列
     * @return
     */
    @Bean
    public Queue orderOverdueNotifyQueue() {
        return QueueBuilder.durable(MQDefine.ORDER_OVERDUE_NOTIFY_QUEUE).build();
    }

    /**
     * 订单逾期消息队列绑定信息
     * @return
     */
    @Bean
    public Binding orderOverdueNotifyQueueBinding() {
        return BindingBuilder.bind(orderOverdueNotifyQueue())
                .to(eErpExchange())
                .with(MQDefine.ORDER_OVERDUE_NOTIFY_QUEUE);
    }
    /**
     * 转发队列信息
     * @return
     */
    @Bean
    public Queue amzCouponCampaignForwardQueue() {
        return QueueBuilder.durable(MQDefine.ERP_AMZ_COUPON_CAMPAIGN_FORWARD_QUEUE).build();
    }

    @Bean
    public Queue temuOnlineOrderSubsidyQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_ONLINE_ORDER_SUBSIDY_QUEUE).build();
    }

    @Bean
    public Binding temuOnlineOrderSubsidyQueueBinding() {
        return BindingBuilder.bind(temuOnlineOrderSubsidyQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_ONLINE_ORDER_SUBSIDY_QUEUE);
    }



    /**
     * 下载沃尔玛的GTIN标签消息接收队列
     * @return
     */
    @Bean
    public Queue walmartInboundTagReceiveQueue() {
        return new Queue(MQDefine.MAR_WALMART_INBOUND_TAG_QUEUE, true);
    }

    @Bean
    public Binding walmartInboundTagReceiveQueueBinding() {
        return BindingBuilder.bind(walmartInboundTagReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_WALMART_INBOUND_TAG_QUEUE);
    }


    /**
     * WFS货件同步队列
     * @return
     */
    @Bean
    public Queue walmartSyncShipmetQueue() {
        return new Queue(MQDefine.MAR_WALMART_SYNC_SHIPMENT_QUEUE, true);
    }

    @Bean
    public Binding walmartSyncShipmetQueueBinding() {
        return BindingBuilder.bind(walmartSyncShipmetQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_WALMART_SYNC_SHIPMENT_QUEUE);
    }



    /**
     * WFS货件拉取bol标更新BOL编号队列
     * @return
     */
    @Bean
    public Queue marWalmartShipmentBolQueue() {
        return new Queue(MQDefine.MAR_WALMART_SHIPMENT_BOL_QUEUE, true);
    }

    @Bean
    public Binding walmartShipmentBolQueueBinding() {
        return BindingBuilder.bind(marWalmartShipmentBolQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_WALMART_SHIPMENT_BOL_QUEUE);
    }

    /**
     * WFS货件拉取bol标更新BOL编号消息接收队列
     * @return
     */
    @Bean
    public Queue marWalmartShipmentBolReceiveQueue() {
        return new Queue(MQDefine.MAR_WALMART_SHIPMENT_BOL_RECEIVE_QUEUE, true);
    }

    @Bean
    public Binding marWalmartShipmentBolReceiveQueueBinding() {
        return BindingBuilder.bind(marWalmartShipmentBolReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_WALMART_SHIPMENT_BOL_RECEIVE_QUEUE);
    }





    @Bean
    public Queue wayfairSummaryInventoryQueue(){
        return new Queue(MQDefine.WAYFAIR_INVENTORY_SUMMARY_QUEUE, true);
    }


    @Bean
    public Binding wayfairSummaryInventoryQueueBinding(){
        return BindingBuilder.bind(wayfairSummaryInventoryQueue())
                .to(eErpExchange())
                .with(MQDefine.WAYFAIR_INVENTORY_SUMMARY_QUEUE);
    }
    /**
     * 接收亚马逊星级信息
     * @return
     */
    @Bean
    public Queue marAmzStarInfoQueue() {
        return new Queue(MQDefine.MAR_AMZ_STAR_INFO_QUEUE, true);
    }





    /**
     * temu促销优惠券创建，修改，取消数据接收队列
     *
     * @return
     */
    @Bean
    public Queue temuCouponManagedReceiveQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_COUPON_MESSAGE_MANAGED_QUEUE).build();
    }

    /**
     * temu促销半托管优惠券数据接收队列绑定
     *
     * @return
     */
    @Bean
    public Binding temuCouponManagedReceiveQueueBinding() {
        return BindingBuilder.bind(temuCouponManagedReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_COUPON_MESSAGE_MANAGED_QUEUE);
    }



    /**
     * temu促销优惠券创建，修改，取消数据消息发送队列
     *
     * @return
     */
    @Bean
    public Queue temuCouponManagedSendQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_COUPON_MESSAGE_SEND_QUEUE).build();
    }

    /**
     * temu促销半托管优惠券数据消息发送队列绑定
     *
     * @return
     */
    @Bean
    public Binding temuCouponManagedSendQueueBinding() {
        return BindingBuilder.bind(temuCouponManagedSendQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_COUPON_MESSAGE_SEND_QUEUE);
    }


    /**
     * walmart评论信息接收队列
     * @return
     */
    @Bean
    public Queue walmartReviewInfoQueue() {
        return new Queue(MQDefine.WALMART_REVIEW_INFO_QUEUE, true);

    }

    /**
     * wayfair订单信息
     *
     * @return
     */
    @Bean
    public Queue marSaleOrderWayfairOriInfoQueue() {
        return new Queue(MQDefine.MAR_SALE_ORDER_WAYFAIR_ORI_INFO_QUEUE, true);
    }


    /**
     * temu促销RPA返回成功往后台确认结果队列
     * @return
     */
    @Bean
    public Queue temuPromotionsRpaSecondConfirmMessageQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_PROMOTIONS_RPA_SECOND_CONFIRM_MESSAGE).build();
    }


    /**
     * temu促销RPA返回成功往后台确认结果队列绑定
     * @return
     */
    @Bean
    public Binding temuPromotionsRpaSecondConfirmMessageQueueBinding() {
        return BindingBuilder.bind(temuPromotionsRpaSecondConfirmMessageQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_PROMOTIONS_RPA_SECOND_CONFIRM_MESSAGE);
    }



    /**
     * temu促销RPA返回成功往后台确认结果返回队列
     * @return
     */
    @Bean
    public Queue temuPromotionsRpaSecondConfirmReceiveMessageQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_PROMOTIONS_RPA_SECOND_CONFIRM_RECEIVE_MESSAGE).build();
    }


    /**
     * temu促销RPA返回成功往后台确认结果返回队列绑定
     * @return
     */
    @Bean
    public Binding temuPromotionsRpaSecondConfirmReceiveMessageQueueBinding() {
        return BindingBuilder.bind(temuPromotionsRpaSecondConfirmReceiveMessageQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_PROMOTIONS_RPA_SECOND_CONFIRM_RECEIVE_MESSAGE);
    }




    /**
     * temu促销同步队列
     * @return
     */
    @Bean
    public Queue temuPromotionsSyncMessageQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_PROMOTIONS_SYNC_MESSAGE).build();
    }


    /**
     * temu促销同步队列绑定
     * @return
     */
    @Bean
    public Binding temuPromotionsSyncMessageQueueBinding() {
        return BindingBuilder.bind(temuPromotionsSyncMessageQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_PROMOTIONS_SYNC_MESSAGE);
    }

    /**
     * 接收亚马逊best seller的完整类目路径以及类目的url
     * @return
     */
    @Bean
    public Queue amzBestSellerUrlQueue() {
        return QueueBuilder.durable(MQDefine.MAR_AMZ_BEST_SELLER_URL).build();
    }


    /**
     * temu促销补报消息发送队列
     * @return
     */
    @Bean
    public Queue temuSupplementaryReportQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_SUPPLEMENTARY_REPORT_QUEUE).build();
    }


    /**
     * temu促销补报消息发送队列绑定
     * @return
     */
    @Bean
    public Binding temuSupplementaryReportQueueBinding() {
        return BindingBuilder.bind(temuSupplementaryReportQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_SUPPLEMENTARY_REPORT_QUEUE);
    }


    /**
     * temu促销补发消息接收队列
     * @return
     */
    @Bean
    public Queue temuSupplementaryReportPromotionQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_SUPPLEMENTARY_REPORT_PROMOTION_QUEUE).build();
    }


    /**
     * temu促销补发消息接收队列绑定
     * @return
     */
    @Bean
    public Binding temuSupplementaryReportPromotionQueueBinding() {
        return BindingBuilder.bind(temuSupplementaryReportPromotionQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_SUPPLEMENTARY_REPORT_PROMOTION_QUEUE);
    }


    /**
     * temu促销补报活动RPA消息接收队列
     * @return
     */
    @Bean
    public Queue temuSupplementaryReportPromotionRpaQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_SUPPLEMENTARY_REPORT_PROMOTION_RPA_QUEUE).build();
    }


    /**
     * temu促销补报活动RPA消息接收队列绑定
     * @return
     */
    @Bean
    public Binding temuSupplementaryReportPromotionRpaQueueBinding() {
        return BindingBuilder.bind(temuSupplementaryReportPromotionRpaQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_SUPPLEMENTARY_REPORT_PROMOTION_RPA_QUEUE);
    }


    /**
     * 索赔队列获取证明
     *
     * @return
     */
    @Bean
    public Queue lastMileFeeClaimReceiveProveQueue() {
        // 队列持久
        return new Queue(MQDefine.LAST_MILE_FEE_CLAIM_RECEIVE_PROVE_QUEUE, true);
    }

    /**
     * 索赔队列（接收申诉提交结果）
     *
     * @return
     */
    @Bean
    public Queue lastMileFeeClaimReceiveSubResultQueue() {
        return new Queue(MQDefine.LAST_MILE_FEE_CLAIM_RECEIVE_SUB_RESULT_QUEUE, true);
    }

    /**
     * 索赔队列（接受索赔状态）
     *
     * @return
     */
    @Bean
    public Queue lastMileFeeClaimReceiveSTATUSResultQueue() {
        return new Queue(MQDefine.LAST_MILE_FEE_CLAIM_RECEIVE_STATUS_RESULT_QUEUE, true);
    }

    /**
     * AmazonVineInfo队列
     * @return
     */
    @Bean
    public Queue amazonVineInfoQueue() {
        return QueueBuilder.durable(MQDefine.AMAZON_VINE_INFO_QUEUE).build();
    }


    /**
     * AmazonVineInfo队列绑定
     * @return
     */
    @Bean
    public Binding amazonVineInfoQueueBinding() {
        return BindingBuilder.bind(amazonVineInfoQueue())
                .to(eErpExchange())
                .with(MQDefine.AMAZON_VINE_INFO_QUEUE);
    }

    /**
     * 接收vc客诉分析数据
     * @return
     */
    @Bean
    public Queue marReceiveVcCustomerComplaintQueue() {
        return QueueBuilder.durable(MQDefine.MAR_RECEIVE_VC_CUSTOMER_COMPLAINT).build();
    }

    /**
     * wfs货件确认运费队列
     * @return
     */
    @Bean
    public Queue marWalmartSetShipmentRpaQueue() {
        return QueueBuilder.durable(MQDefine.MAR_WALMART_SET_SHIPMENT_RPA_QUEUE).build();
    }


    /**
     * wfs货件设置货件队列绑定
     * @return
     */
    @Bean
    public Binding marWalmartSetShipmentRpaQueueBinding() {
        return BindingBuilder.bind(marWalmartSetShipmentRpaQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_WALMART_SET_SHIPMENT_RPA_QUEUE);
    }


    /**
     * wfs货件确认运费队列
     * @return
     */
    @Bean
    public Queue marWalmartCancelShipmentFeeRpaQueue() {
        return QueueBuilder.durable(MQDefine.MAR_WALMART_CANCEL_SHIPMENT_FEE_RPA_QUEUE).build();
    }


    /**
     * wfs货件确认运费队列绑定
     * @return
     */
    @Bean
    public Binding marWalmartCancelShipmentFeeRpaQueueBinding() {
        return BindingBuilder.bind(marWalmartCancelShipmentFeeRpaQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_WALMART_CANCEL_SHIPMENT_FEE_RPA_QUEUE);
    }
    /**
     * 发货方式数据拉取队列绑定
     * @return
     */
    @Bean
    public Queue productChannelsFulfillmentRpaQueue() {
        return QueueBuilder.durable(MQDefine.PRODUCT_CHANNELS_FULFILLMENT).build();
    }


    /**
     * 发货方式数据拉取队列绑定
     * @return
     */
    @Bean
    public Binding productChannelsFulfillmentRpaQueueBinding() {
        return BindingBuilder.bind(productChannelsFulfillmentRpaQueue())
                .to(eErpExchange())
                .with(MQDefine.PRODUCT_CHANNELS_FULFILLMENT);
    }


    /**
     * temu促销非美本创建消息回调发送队列
     *
     * @return
     */
    @Bean
    public Queue temuPromotionNotMeibenCreateMessageQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_PROMOTION_NOT_MEIBEN_CREATE_MESSAGE_QUEUE).build();
    }

    /**
     * temu促销非美本创建消息回调绑定
     *
     * @return
     */
    @Bean
    public Binding temuPromotionNotMeibenCreateMessageQueueBinding() {
        return BindingBuilder.bind(temuPromotionNotMeibenCreateMessageQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_PROMOTION_NOT_MEIBEN_CREATE_MESSAGE_QUEUE);
    }

    /**
     * sku映射表title变更通知队列
     * @return
     */
    @Bean
    public Queue skuMappingTitleChangeNotifyQueue() {
        return new Queue(MQDefine.SKU_MAPPING_TITLE_CHANGE_NOTIFY_QUEUE, true);
    }

    @Bean
    public Binding skuMappingTitleChangeNotifyQueueBinding() {
        return BindingBuilder.bind(skuMappingTitleChangeNotifyQueue())
                .to(eErpExchange())
                .with(MQDefine.SKU_MAPPING_TITLE_CHANGE_NOTIFY_QUEUE);
    }

    /**
     * tk促销佣金推送队列
     * @return
     */
    @Bean
    public Queue tkPromotionCommissionQueue() {
        return QueueBuilder.durable(MQDefine.TK_PROMOTION_COMMISSION_QUEUE).build();
    }


    /**
     * tk促销佣金推送队列绑定
     * @return
     */
    @Bean
    public Binding tkPromotionCommissionQueueBinding() {
        return BindingBuilder.bind(tkPromotionCommissionQueue())
                .to(eErpExchange())
                .with(MQDefine.TK_PROMOTION_COMMISSION_QUEUE);
    }




    /**
     * temu促销解除限制队列
     * @return
     */
    @Bean
    public Queue temuPromotionLimitQueue() {
        return QueueBuilder.durable(MQDefine.TEMU_PROMOTION_LIMIT_QUEUE).build();
    }


    /**
     * temu促销解除限制队列绑定
     * @return
     */
    @Bean
    public Binding temuPromotionLimitQueueBinding() {
        return BindingBuilder.bind(temuPromotionLimitQueue())
                .to(eErpExchange())
                .with(MQDefine.TEMU_PROMOTION_LIMIT_QUEUE);
    }

    /**
     * wfs发起索赔
     * @return
     */
    @Bean
    public Queue wfsStartClaimQueue() {
        return new Queue(MQDefine.MAR_WFS_START_CLAIM_QUEUE, true);
    }

    @Bean
    public Binding wfsStartClaimQueueBinding() {
        return BindingBuilder.bind(wfsStartClaimQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_WFS_START_CLAIM_QUEUE);
    }


    /**
     * wfs发送消息
     * @return
     */
    @Bean
    public Queue wfsSendMessageQueue() {
        return new Queue(MQDefine.MAR_WFS_CLAIM_SEND_MESSAGE, true);
    }

    @Bean
    public Binding wfsSendMessageBinding() {
        return BindingBuilder.bind(wfsSendMessageQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_WFS_CLAIM_SEND_MESSAGE);
    }


    /**
     * wfs接收消息
     * @return
     */
    @Bean
    public Queue wfsGetMessageQueue() {
        return new Queue(MQDefine.MAR_WFS_CLAIM_RECEIVE_MESSAGE, true);
    }

    @Bean
    public Binding wfsGetMessageQueueBinding() {
        return BindingBuilder.bind(wfsSendMessageQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_WFS_CLAIM_RECEIVE_MESSAGE);
    }


    /**
     * wfs接收索赔返回结果
     * @return
     */
    @Bean
    public Queue wfsClaimCaseQueue() {
        return new Queue(MQDefine.MAR_WFS_CLAIM_CASE_MESSAGE, true);
    }

    @Bean
    public Binding wfsClaimCaseQueueBinding() {
        return BindingBuilder.bind(wfsClaimCaseQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_WFS_CLAIM_CASE_MESSAGE);
    }


    /**
     * wfs接收索赔全部返回结果
     * @return
     */
    @Bean
    public Queue wfsClaimCaseAllQueue() {
        return new Queue(MQDefine.MAR_WFS_CLAIM_CASE_ALL_MESSAGE, true);
    }

    @Bean
    public Binding wfsClaimCaseAllQueueBinding() {
        return BindingBuilder.bind(wfsClaimCaseAllQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_WFS_CLAIM_CASE_ALL_MESSAGE);
    }

    /**
     * wfs货件获取发票附件发送队列
     * @return
     */
    @Bean
    public Queue marWalmartInboundDocumentSendQueue() {
        return QueueBuilder.durable(MQDefine.MAR_WALMART_INBOUND_DOCUMENT_SEND_QUEUE).build();
    }


    /**
     * wfs货件获取发票附件发送队列绑定
     * @return
     */
    @Bean
    public Binding marWalmartInboundDocumentSendQueueBinding() {
        return BindingBuilder.bind(marWalmartInboundDocumentSendQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_WALMART_INBOUND_DOCUMENT_SEND_QUEUE);
    }

    /**
     * wfs货件获取发票附件接收队列
     * @return
     */
    @Bean
    public Queue marWalmartInboundDocumentReceiveQueue() {
        return QueueBuilder.durable(MQDefine.MAR_WALMART_INBOUND_DOCUMENT_RECEIVE_QUEUE).build();
    }


    /**
     * wfs货件获取发票附件接收队列绑定
     * @return
     */
    @Bean
    public Binding marWalmartInboundDocumentReceiveQueueBinding() {
        return BindingBuilder.bind(marWalmartInboundDocumentReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_WALMART_INBOUND_DOCUMENT_RECEIVE_QUEUE);
    }


    /**
     * wfs货件获取预估物流费用发送队列
     * @return
     */
    @Bean
    public Queue marWarInboundFeeSendQueue() {
        return QueueBuilder.durable(MQDefine.MAR_WAR_INBOUND_FEE_SEND_QUEUE).build();
    }


    /**
     * wfs货件获取预估物流费用发送队列绑定
     * @return
     */
    @Bean
    public Binding marWarInboundFeeSendQueueBinding() {
        return BindingBuilder.bind(marWarInboundFeeSendQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_WAR_INBOUND_FEE_SEND_QUEUE);
    }

    /**
     * wfs货件获取预估物流费用接收队列
     * @return
     */
    @Bean
    public Queue marWalmartInboundFeeReceiveQueue() {
        return QueueBuilder.durable(MQDefine.MAR_WALMART_INBOUND_FEE_RECEIVE_QUEUE).build();
    }


    /**
     * wfs货件获取预估物流费用接收队列绑定
     * @return
     */
    @Bean
    public Binding marWalmartInboundFeeReceiveQueueBinding() {
        return BindingBuilder.bind(marWalmartInboundFeeReceiveQueue())
                .to(eErpExchange())
                .with(MQDefine.MAR_WALMART_INBOUND_FEE_RECEIVE_QUEUE);
    }
}
