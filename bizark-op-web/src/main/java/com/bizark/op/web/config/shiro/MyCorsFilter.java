package com.bizark.op.web.config.shiro;

import com.bizark.common.constant.PropertiesConfig;
import com.bizark.op.web.util.WebHelper;
import com.bizark.framework.util.JWTAuthHelper;
import org.apache.shiro.web.servlet.AdviceFilter;
import org.apache.shiro.web.util.WebUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class MyCorsFilter extends AdviceFilter {

    @Autowired
    protected JWTAuthHelper jwtAuthHelper;

    @Autowired
    protected PropertiesConfig propertiesConfig;

    public MyCorsFilter() {
    }

    public JWTAuthHelper getJwtAuthHelper() {
        return jwtAuthHelper;
    }

    public MyCorsFilter setJwtAuthHelper(JWTAuthHelper jwtAuthHelper) {
        this.jwtAuthHelper = jwtAuthHelper;
        return this;
    }

    public PropertiesConfig getPropertiesConfig() {
        return propertiesConfig;
    }

    public MyCorsFilter setPropertiesConfig(PropertiesConfig propertiesConfig) {
        this.propertiesConfig = propertiesConfig;
        return this;
    }

    private boolean wantsJson(HttpServletRequest request){
        String requestedWithHeader = request.getHeader("X-Requested-With");
        if("XMLHttpRequest".equalsIgnoreCase(requestedWithHeader)){
            return true;
        }

        String headerContentType = request.getHeader("content-type");
        if(headerContentType!=null && headerContentType.contains("json")){
            return true;
        }

        return false;
    }
    private boolean wantsJson(HttpServletResponse response){
        String headerContentType = response.getHeader("content-type");
        if(headerContentType!=null && headerContentType.contains("json")){
            return true;
        }
        return false;
    }

    protected boolean isAjaxRequest(HttpServletRequest request){
        return  "XMLHttpRequest".equalsIgnoreCase(request.getHeader("X-Requested-With"));
    }

    /**
     * 清理响应内容的token cookie
     * @param response
     */
    protected void clearResponseTokenCookie(ServletResponse response){
        Cookie cookie = buildTokenCookie("token",null,0);
        WebUtils.toHttp(response).addCookie(cookie);
    }

    protected Cookie buildTokenCookie(String name, String value, Integer maxAge){
        Cookie cookie = new Cookie(name,value);
        cookie.setHttpOnly(false); // httponly为true时仅服务端读取，（防止xss/csrf问题）
        cookie.setSecure(false);
        cookie.setPath("/");
        cookie.setDomain(".thebizark.com");
        cookie.setMaxAge(maxAge);
        return cookie;
    }

    /**
     * 对跨域提供支持
     */
    @Override
    protected boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;

        //String requestMethod = httpServletRequest.getMethod();
        String requestOrigin = httpServletRequest.getHeader("Origin");
        if (requestOrigin == null || requestOrigin.isEmpty()) { // 没有Origin头, 不需要跨域
            return super.preHandle(request, response);
        }

        boolean isAllowOrigin = WebHelper.isAllowOrigin(httpServletRequest);
        //System.out.println(requestMethod + " -> " + requestOrigin + ": " + isAllowOrigin);
        if (!isAllowOrigin) {
            //System.out.println(requestMethod + " -> " + requestOrigin + ": " + isAllowOrigin + " not isAllowOrigin");
            httpServletResponse.setStatus(HttpStatus.FORBIDDEN.value());
            return false;
        }

        httpServletResponse.setHeader("Access-Control-Allow-Origin", httpServletRequest.getHeader("Origin"));
        httpServletResponse.setHeader("Access-Control-Allow-Methods", "GET,POST,OPTIONS,PUT,DELETE");
        httpServletResponse.setHeader("Access-Control-Allow-Headers", httpServletRequest.getHeader("Access-Control-Request-Headers"));
//        httpServletResponse.setHeader("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Connection, User-Agent, Cookie");
        httpServletResponse.setHeader("Access-Control-Allow-Credentials", "true");
        httpServletResponse.setHeader("Access-Control-Max-Age", "86400");
        // 跨域时会首先发送一个option请求，这里我们给option请求直接返回正常状态
        if (httpServletRequest.getMethod().equals(RequestMethod.OPTIONS.name())) {
            httpServletResponse.setStatus(HttpStatus.OK.value());
            return false;
        }
        return super.preHandle(request, response);
    }

}
