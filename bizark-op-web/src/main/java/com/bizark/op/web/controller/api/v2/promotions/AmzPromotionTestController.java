package com.bizark.op.web.controller.api.v2.promotions;

import com.bizark.op.common.core.redis.RedisCache;
import com.bizark.op.common.util.spring.SpringUtils;
import com.bizark.op.web.controller.api.AbstractApiController;
import com.bizark.framework.web.view.ApiResponseResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @remarks:xxx
 * @Author: Ailill
 * @Date: 2023/11/1 9:56
 */
@RestController
@RequestMapping("/api/v2/promotions/test")
public class AmzPromotionTestController extends AbstractApiController {

    @GetMapping("/get")
    public ApiResponseResult getKey(@RequestParam("key") String key) {
        Object cacheObject = SpringUtils.getBean(RedisCache.class).getCacheObject(key);
        return ApiResponseResult.buildSuccessResult(cacheObject);
    }
}
