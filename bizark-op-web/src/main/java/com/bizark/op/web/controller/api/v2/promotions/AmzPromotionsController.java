package com.bizark.op.web.controller.api.v2.promotions;


import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bizark.common.contract.AuthUserDetails;
import com.bizark.common.exception.CommonException;
import com.bizark.common.util.JacksonUtils;
import com.bizark.op.api.annotation.ClusterFailFastLock;
import com.bizark.op.api.cons.PermDefine;
import com.bizark.op.api.entity.op.promotions.AmzPromotionDateSet;
import com.bizark.op.api.entity.op.promotions.AmzPromotions;
import com.bizark.op.api.entity.op.promotions.AmzPromotionsSkuDetail;
import com.bizark.op.api.request.AmzPromotionsCreateRequest;
import com.bizark.op.api.request.AmzPromotionsQueryRequest;
import com.bizark.op.api.service.promotions.AmzPromotionDateSetService;
import com.bizark.op.api.service.promotions.IAmzPromotionAndCouponService;
import com.bizark.op.api.service.promotions.IAmzPromotionExportService;
import com.bizark.op.api.service.promotions.IAmzPromotionsService;
import com.bizark.op.api.service.sys.ISysDictDataService;
import com.bizark.op.api.vo.promotions.AmzPromotionsListVO;
import com.bizark.op.common.annotation.RepeatSubmit;
import com.bizark.op.common.core.page.TableDataInfo;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.service.util.ExcelTemplateUtils;
import com.bizark.op.web.controller.api.AbstractApiController;
import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.usercenter.api.parameter.role.SellerSkuVisualVo;
import com.bizark.usercenter.api.service.UcRoleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;


/**
 * @remarks: 促销管理controller--促销详情
 * @Author: Ailill
 * @date 2023-05-12 15:06
 */
@RestController
@RequestMapping("/api/v2/promotions/promotions")
@Slf4j
public class AmzPromotionsController extends AbstractApiController {
    @Autowired
    private IAmzPromotionsService amzPromotionsService;

    @Autowired
    private IAmzPromotionExportService amzPromotionExportService;

    @Autowired
    private ISysDictDataService sysDictDataService;

    @Autowired
    private AmzPromotionDateSetService amzPromotionDateSetService;

    @Autowired
    private UcRoleService ucRoleService;

    @Autowired
    private IAmzPromotionAndCouponService amzPromotionAndCouponService;

    /**
     * 查询促销管理列表
     */
    @GetMapping("/list")
//    @RequiresPermissions(PermDefine.PROMOTION_ACTIVITY_LIST)
    public TableDataInfo list(AmzPromotionsQueryRequest amzPromotionsQueryRequest, @RequestParam(name = "contextId", required = false) Integer contextId) {

        if (contextId == null || contextId < 1) {
            contextId = getAuthUserEntity().getOrgId();
        }

        amzPromotionsQueryRequest.setContextId(contextId);

        try {
            if (new Integer(1000380).equals(contextId)) {
                // sellerSku数据权限获取
                SellerSkuVisualVo selectSellerSku = ucRoleService.selectSellerSku(contextId, getAuthUser(), PermDefine.VC_PROMOTION_INFO_MANAGE);
                logger.info("用户 {} 组织 {} 获取SellerSku权限数据 {}", getAuthUser().getEmail(), contextId, JacksonUtils.toJson(selectSellerSku));
                if (!selectSellerSku.getIsAll()) {
                    if (CollectionUtil.isNotEmpty(selectSellerSku.getSellerSkuList())) {
                        List<String> skuIdArray = amzPromotionsQueryRequest.getSkuIdArray();                 //判断查询是否传参
                        if (CollectionUtil.isNotEmpty(skuIdArray)){
                            skuIdArray.addAll(selectSellerSku.getSellerSkuList()); //设置权限SerllerSKu
                        }else {
                            skuIdArray = selectSellerSku.getSellerSkuList();
                        }
                        amzPromotionsQueryRequest.setSkuIdArray(skuIdArray);
                    }
                }
            }
        } catch (Exception e) {
            log.info("vc促销活动列表权限异常：{}", e);
        }
        startPage();
        amzPromotionsQueryRequest.setErrorInfoAllExport(false);
        List<AmzPromotionsListVO> list = amzPromotionsService.selectAmzPromotionsList(amzPromotionsQueryRequest);
        return getDataTable(list);
    }

    /**
     * 获取促销管理详细信息
     */
    @GetMapping(value = "/{id}")
//    @RequiresPermissions(PermDefine.PROMOTION_ACTIVITY_QUERY)
    public ApiResponseResult getInfo(@PathVariable("id") Long id, @RequestParam(value = "contextId", required = false) Integer contextId) {
        return ApiResponseResult.buildSuccessResult(amzPromotionsService.selectAmzPromotionsById(id));
    }

    /**
     * 获取促销管理详细信息
     */
    @GetMapping(value = "/vcdetail/{id}")
//    @RequiresPermissions(PermDefine.PROMOTION_ACTIVITY_QUERY)
    public ApiResponseResult getVcDetailInfo(@PathVariable("id") Long id, @RequestParam(value = "contextId", required = false) Integer contextId) {
        return ApiResponseResult.buildSuccessResult(amzPromotionsService.selectAmzApprovalDetailPromotionsById(id));
    }

    @GetMapping(value = "/otherPromotion/{id}")
    public ApiResponseResult otherPromotion(@PathVariable("id") Long id) {
        return ApiResponseResult.buildSuccessResult(amzPromotionAndCouponService.getOtherPromotions(id,"promotion"));
    }

    /**
     * 新增促销管理
     */
    @PostMapping("/add")
    @RepeatSubmit(interval = 10000)
//    @RequiresPermissions({PermDefine.PROMOTION_ACTIVITY_CREATE,PermDefine.PROMOTION_ACTIVITY_MODIFY})
    public ApiResponseResult add(@RequestBody AmzPromotionsCreateRequest amzPromotionsCreateRequest, @RequestParam(value = "contextId") Integer contextId) {

        amzPromotionsCreateRequest.setOrganizationId(contextId);
        AuthUserDetails thisUser = getAuthUser();
        amzPromotionsCreateRequest.setUpdatedBy(thisUser.getId());
        amzPromotionsCreateRequest.setUpdatedName(thisUser.getName());
        amzPromotionsCreateRequest.setUpdatedAt(DateUtils.getNowDate());
        if (amzPromotionsCreateRequest.getUpdateType().equals(1)) {
            amzPromotionsCreateRequest.setCreatedBy(thisUser.getId());
            amzPromotionsCreateRequest.setCreatedName(thisUser.getName());
            amzPromotionsCreateRequest.setCreatedAt(DateUtils.getNowDate());
        }
        amzPromotionsService.verifyCreatePromotions(amzPromotionsCreateRequest);

        amzPromotionsService.insertAmzPromotions(amzPromotionsCreateRequest);
        return ApiResponseResult.buildSuccessResult();
    }



    /**
     * 取消促销
     *
     * @param id
     * @return
     */
    @PutMapping("/{id}")
    @RepeatSubmit
//    @RequiresPermissions(PermDefine.PROMOTION_ACTIVITY_CANCEL)
    public ApiResponseResult cancel(@PathVariable Long id, @RequestParam(value = "contextId", required = false) Integer contextId,@RequestParam(value = "remark", required = false)String remark) {
        if (contextId == null || contextId < 1) {
            contextId = getAuthUserEntity().getOrgId();
        }
        amzPromotionsService.cancelAmzPromotions(id, getAuthUserEntity(), contextId,remark);
        return ApiResponseResult.buildSuccessResult("取消成功！");
    }

    /**
     * 提交异常如错误不是选择的ASIN不能参加promotion则显示提交按钮。直接提交。
     *
     * @param id             promotions主键id
     * @param createdName    操作人
     * @param promotionsType 促销类型
     * @param contextId      组织id
     * @return
     */
    @GetMapping(value = "/submit")
    @RepeatSubmit
//    @RequiresPermissions(PermDefine.PROMOTION_ACTIVITY_SUBMIT)
    public ApiResponseResult submit(@RequestParam("id") Long id, @RequestParam(value = "createdName", required = false) String createdName, @RequestParam("promotionsType") Integer promotionsType,
                                    @RequestParam(value = "contextId", required = false) Integer contextId) {

        if (contextId == null || contextId < 1) {
            contextId = getAuthUserEntity().getOrgId();
        }
        if (StringUtil.isEmpty(createdName)) {
            createdName = getAuthUserEntity().getName();
        }
        int result = amzPromotionsService.submit(id, getAuthUserEntity().getId(), createdName, promotionsType, contextId);
        if (result > 0) {
            return ApiResponseResult.buildSuccessResult(result);
        } else {
            return ApiResponseResult.buildFailureResult("提交失败");
        }

    }

    @GetMapping("/exportPromotions")
//    @RequiresPermissions(PermDefine.PROMOTION_ACTIVITY_LIST)
    public ApiResponseResult exportAll(@RequestParam(value = "contextId", required = false) Integer contextId, AmzPromotionsQueryRequest request, HttpServletResponse response) {
        if (contextId == null || contextId < 1) {
            contextId = getAuthUserEntity().getOrgId();
        }
        request.setContextId(contextId);


        request.setErrorInfoAllExport(true);
        try {
            if (new Integer(1000380).equals(contextId)) {
                // sellerSku数据权限获取
                SellerSkuVisualVo selectSellerSku = ucRoleService.selectSellerSku(contextId, getAuthUser(), PermDefine.VC_PROMOTION_INFO_MANAGE);
                logger.info("用户 {} 组织 {} 获取SellerSku权限数据 {}", getAuthUser().getEmail(), contextId, JacksonUtils.toJson(selectSellerSku));
                if (!selectSellerSku.getIsAll()) {
                    if (CollectionUtil.isNotEmpty(selectSellerSku.getSellerSkuList())) {
                        List<String> skuIdArray = request.getSkuIdArray();                 //判断查询是否传参
                        if (CollectionUtil.isNotEmpty(skuIdArray)){
                            skuIdArray.addAll(selectSellerSku.getSellerSkuList()); //设置权限SerllerSKu
                        }else {
                            skuIdArray = selectSellerSku.getSellerSkuList();
                        }
                        request.setSkuIdArray(skuIdArray);
                    }
                }
            }
        } catch (Exception e) {
            log.info("vc促销活动列表权限异常：{}", e);
        }
        amzPromotionExportService.originalPromotionExport(request,getAuthUserEntity());
        return ApiResponseResult.buildSuccessResult("导出已生成，请至【任务中心】-【任务列表】查看处理进度及下载文件！");

    }

    /**
     * 定时获取店铺下的promotion
     *
     * @param contextId
     * @return
     */
    @GetMapping("/getAllPromotions")
    public TableDataInfo getAllAccountWithPromotions(@RequestParam(value = "contextId", required = false) Integer contextId) {

        return getDataTable(amzPromotionsService.getAccountWithPromotions());
    }

    @PostMapping("/import")
    public ApiResponseResult importExcel(MultipartFile file, Integer contextId) {

        if (contextId == null || contextId < 1) {
            contextId = getAuthUserEntity().getOrgId();
        }
        amzPromotionsService.importExcel(file, getAuthUserEntity(), contextId);
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * 获取asin最新前台价格
     *
     * @param asin
     * @param contextId
     * @return
     */
    @GetMapping("/updateFrontPrice")
    @RepeatSubmit(interval = 1000)
    public ApiResponseResult updateFrontPrice(@RequestParam("asin") String[] asin,@RequestParam("shopId")Integer shopId, Integer contextId) {

        List<AmzPromotionsSkuDetail> asinFrontPrice = amzPromotionsService.getAsinFrontPrice(asin,shopId);
        return CollectionUtil.isNotEmpty(asinFrontPrice) ? ApiResponseResult.buildSuccessResult(asinFrontPrice) : ApiResponseResult.buildFailureResult("前台价格获取失败");
    }

    /**
     * 获取asin最新参考价格等信息
     * @param request
     * @param contextId
     * @return
     */
    @PostMapping("/getReferencePrice")
    @RepeatSubmit(interval = 2000)
    public ApiResponseResult updateReferencePrice(@RequestBody AmzPromotionsCreateRequest request, Integer contextId) {

        List<AmzPromotionsSkuDetail> asinFrontPrice = amzPromotionsService.getLastestReferencePrice(request);
        return ApiResponseResult.buildSuccessResult(asinFrontPrice);
    }

    /**
     * 轮训获取参考价格
     * @param request
     * @param contextId
     * @return
     */
    @PostMapping("/getBatchReferencePrice")
    @RepeatSubmit(interval = 2000)
    public ApiResponseResult getBatchReferencePrice(@RequestBody AmzPromotionsCreateRequest request, Integer contextId) {

        List<AmzPromotionsSkuDetail> asinFrontPrice = amzPromotionsService.getBatchReferencePrice(request);
        return ApiResponseResult.buildSuccessResult(asinFrontPrice);
    }

    @GetMapping("/download/importExample")
    public void downModel(@RequestParam(value = "contextId") Integer contextId, HttpServletResponse response) {
        if (contextId == null || contextId < 1) {
            contextId = getAuthUserEntity().getOrgId();
        }
        List<String[]> downData = amzPromotionsService.buildString(contextId);
        int[] downRows = {1, 2, 11, 12};
        int[] dateCells = {};
        String[] title = {
                "序号*",
                "店铺*",
                "促销类型*",
                "开始日期*",
                "结束日期",
                "SellerSKU*",
                "折扣固定值",
                "折扣比例（%）",
                "折后价格",
                "数量",
                "Budget",
                "是否共享",
                "领取限制",
                "code",
                "备注"
        };

        //标题注释
        Map<Integer, String> titleText = new HashMap<>();
        titleText.put(0, "用于区分是否需要合并在一个Promotion中，需要合并则将对应要合并的行填成相同的序号。");

        try {
            ExcelTemplateUtils.createExcelTemplateAndText(this.getClass().getClassLoader().getResource("").getPath() + "templates/promotions/promotion.xls",
                    title, titleText, downData, downRows, dateCells, response);
        } catch (Exception e) {
            e.printStackTrace();
            throw new CommonException("示例下载失败");
        }

    }

    @GetMapping("/exportPromotionsStr")
    public ApiResponseResult exportAllStr(@RequestParam(value = "contextId", required = false) Integer contextId, AmzPromotionsQueryRequest request, HttpServletResponse response) {
        if (contextId == null || contextId < 1) {
            contextId = getAuthUserEntity().getOrgId();
        }
        request.setContextId(contextId);


        request.setErrorInfoAllExport(true);
        return ApiResponseResult.buildSuccessResult(amzPromotionExportService.asyncExportPromotionList(JSON.toJSONString(request), request.getContextId()));

    }

    @GetMapping("/dateset/list")
    public ApiResponseResult list(AmzPromotionDateSet dateSet,@RequestParam(value = "contextId") Integer contextId) {

        List<AmzPromotionDateSet> list = amzPromotionDateSetService.list(new LambdaQueryWrapper<AmzPromotionDateSet>()
                .eq(dateSet.getPromotionType() != null,AmzPromotionDateSet::getPromotionType,dateSet.getPromotionType())
                .eq(AmzPromotionDateSet::getOrganizationId,contextId)).stream().sorted(Comparator.comparing(AmzPromotionDateSet::getChannel,Comparator.reverseOrder())).collect(Collectors.toList());
        return ApiResponseResult.buildSuccessResult(list);
    }

    @PutMapping("/dateset/update")
    @RepeatSubmit
    public ApiResponseResult updateBeginTime(@RequestBody @Valid List<AmzPromotionDateSet> dateSet, @RequestParam(value = "contextId")Integer contextId) {
        amzPromotionDateSetService.batchUpdateDateSet(dateSet,contextId);
        return ApiResponseResult.buildSuccessResult();
    }

    @GetMapping("/dateset/info")
    public ApiResponseResult getPromotionBeginDateSet(AmzPromotionDateSet dateSet, @RequestParam(value = "contextId") Integer contextId) {
        return ApiResponseResult.buildSuccessResult(amzPromotionDateSetService.getInfo(dateSet, contextId));

    }

    @GetMapping("/dateset/accountCountry")
    public ApiResponseResult getPromotionAccountCountry(AmzPromotionDateSet dateSet, @RequestParam(value = "contextId") Integer contextId) {
        return ApiResponseResult.buildSuccessResult(amzPromotionDateSetService.getAccountCountry(dateSet, contextId));

    }

    @GetMapping("/download/agreement/importExample")
    public void downAgreementModel(@RequestParam(value = "contextId") Integer contextId, HttpServletResponse response) {
        if (contextId == null || contextId < 1) {
            contextId = getAuthUserEntity().getOrgId();
        }
        amzPromotionExportService.downloadAgreementTemplate(contextId,response);
    }

    @PostMapping("/import/agreement")
    @RepeatSubmit
//    @RequiresPermissions(PermDefine.PROMOTION_AGREEMENT_IMPORT)
    public ApiResponseResult importExcelAgreement(MultipartFile file, Integer contextId) {

        if (contextId == null || contextId < 1) {
            contextId = getAuthUserEntity().getOrgId();
        }
        amzPromotionExportService.importExcelAgreement(file, getAuthUserEntity(), contextId);
        return ApiResponseResult.buildSuccessResult();
    }

    @RepeatSubmit
    @PostMapping("/batch/cancel/agreement/promotion")
//    @RequiresPermissions(PermDefine.PROMOTION_AGREEMENT_CANCEL)
    public ApiResponseResult batchCancelAgreementPromotion(@RequestBody List<AmzPromotions> amzPromotionsList,@RequestParam(value = "contextId") Integer contextId,@RequestParam(value = "remark", required = false)String remark) {
        amzPromotionsService.batchCancelAgreementPromotion(amzPromotionsList, getAuthUserEntity(),contextId, remark);
        return ApiResponseResult.buildSuccessResult("批量取消成功！");
    }

    /**
     * 根据渠道查询店铺
     * @param channel
     * @param contextId
     * @return
     */
    @GetMapping("/findAccountListBySaleChannel")
    public TableDataInfo findAccountListBySaleChannel(@RequestParam(value = "saleChannel", required = false) Integer channel,@RequestParam(value = "shopId",required = false) Integer[] shopId,@RequestParam(value = "contextId") Integer contextId) {

        return getDataTable(amzPromotionsService.selectPromotionAccountList(channel, shopId, contextId));
    }
}
