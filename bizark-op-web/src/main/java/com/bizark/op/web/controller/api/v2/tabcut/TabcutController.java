package com.bizark.op.web.controller.api.v2.tabcut;

import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.op.api.service.tabcut.*;
import com.bizark.op.api.service.tk.expert.ITkDataExpertService;
import com.bizark.op.web.controller.api.AbstractApiController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;


@RestController
@Slf4j
@RequestMapping("/api/v2/tabcut")
public class TabcutController extends AbstractApiController {

    @Autowired
    private TabcutCreatorVideoKpiService tabcutCreatorVideoKpiService;

    @Autowired
    private TabcutCategoryService tabcutCategoryService;

    @Autowired
    private TabcutCountryService tabcutCountryService;

    @Autowired
    private TabcutCreatorVideoService tabcutCreatorVideoService;

    @Autowired
    private TabcutCreatorVideoUrlService tabcutCreatorVideoUrlService;

    @Autowired
    private TabcutCreatorDayKpiService tabcutCreatorDayKpiService;


    @Autowired
    private ITkDataExpertService expertService;


    @GetMapping("/syncCreatorVideoAndTrend")
    public ApiResponseResult syncCreatorVideoAndTrend() {
        CompletableFuture<Long> future = CompletableFuture.supplyAsync(() -> {
            long start = System.currentTimeMillis();
            try {
                tabcutCreatorVideoKpiService.syncCreatorVideo(3, 0);

            } catch (Exception e) {
                e.printStackTrace();
                log.error("视频数据抓取失败:{}", e.getMessage());
                return -1L;
            }
            long end = System.currentTimeMillis();
            log.error("视频数据抓取完毕，耗时：{}", end - start);
            return end - start;
        }).thenCombine(CompletableFuture.supplyAsync(() -> {
            long start = System.currentTimeMillis();
            try {
                syncVideoTrend(1, null, null, null);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("趋势数据抓取失败:{}", e.getMessage());
                return -1L;
            }
            long end = System.currentTimeMillis();
            log.error("趋势数据抓取，耗时：{}", end - start);
            return end - start;
        }), (r1, r2) -> {
            log.error("视频数据抓取完毕，耗时：{}", r1);
            log.error("趋势数据抓取，耗时：{}", r2);
            return r1 + r2;
        });

        log.error("数据抓取等待.....");
        future.join();

        return ApiResponseResult.buildSuccessResult();
    }

    @GetMapping("/syncCreatorVideoByCreatorId")
    public ApiResponseResult syncCreatorVideoByCreatorId(@RequestParam("creatorId") String creatorId) {
        tabcutCreatorVideoKpiService.syncCreatorVideoByCreatorId(creatorId);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/syncCreatorVideo")
    public ApiResponseResult syncCreatorVideo(@RequestParam(value = "thread",required = false) Integer thread,
                                              @RequestParam(value = "isContinue",required = false) Integer isContinue
    ) throws InterruptedException {
        thread = thread == null ? 1 : thread;
        isContinue = isContinue == null ? 0 : isContinue;
        long start = System.currentTimeMillis();
        tabcutCreatorVideoKpiService.syncCreatorVideo(thread,isContinue);
        long end = System.currentTimeMillis();
        log.error("视频数据抓取完毕，耗时：{}", end - start);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/syncTabcutCategory")
    public ApiResponseResult syncTabcutCategory() {
        tabcutCategoryService.syncTabcutCategory();
        return ApiResponseResult.buildSuccessResult();
    }

    @GetMapping("/syncTabcutCountry")
    public ApiResponseResult syncTabcutCountry() {
        tabcutCountryService.syncTabcutCountry();
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * 同步视频明细数据
     * @return
     */
    @GetMapping("/syncVideoInfo")
    public ApiResponseResult syncVideoInfo(@RequestParam(value = "thread",required = false) Integer thread,
                                           @RequestParam(value = "isContinue",required = false) Integer isContinue
    ) throws InterruptedException {
        thread = thread == null ? 1 : thread;
        isContinue = isContinue == null ? 0 : isContinue;
        tabcutCreatorVideoService.syncVideoInfo(thread,isContinue);
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * 同步视频明细数据
     * @return
     */
    @GetMapping("/syncVideoInfoByVideoId")
    public ApiResponseResult syncVideoInfoByVideoId(@RequestParam(value = "videoId",required = false) String videoId
    ) {
        tabcutCreatorVideoService.syncVideoInfoByVideoId(videoId);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/syncVideoTrendLimit")
    public ApiResponseResult syncVideoTrendLimit(@RequestParam(value = "thread",required = false) Integer thread,
                                                 @RequestParam("beginTime") String beginTime,
                                                 @RequestParam("endTime") String endTime,
                                                 @RequestParam(value = "isContinue",required = false) Integer isContinue,
                                                 @RequestParam(value = "updateVideo",required = false) Boolean updateVideo
    ) {
        updateVideo = updateVideo != null && updateVideo;
        isContinue = Objects.isNull(isContinue) ? 0 : isContinue;
        thread = thread == null ? 1 : thread;
        tabcutCreatorVideoService.syncVideoTrendLimit(thread, beginTime, endTime, isContinue,updateVideo);
        return ApiResponseResult.buildSuccessResult();
    }
    @GetMapping("/syncCreatorVideoTrendByCreatorId")
    public ApiResponseResult syncVideoTrendByCreatorId(@RequestParam("creatorId") String creatorId,@RequestParam("bizDate") String bizDate) {
        tabcutCreatorVideoService.syncVideoTrendByCreatorId(creatorId,bizDate);
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * 同步达人基本信息
     * @return
     */
    @GetMapping("/syncCreatorBasic")
    public ApiResponseResult syncCreatorBasic() {
        expertService.syncCreatorBasic();
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * 同步视频趋势数据
     * @return
     */
    @GetMapping("/syncVideoTrend")
    public ApiResponseResult syncVideoTrend(@RequestParam(value = "isContinue", required = false) Integer isContinue,
                                            @RequestParam("beginTime") String beginTime,
                                            @RequestParam("endTime") String endTime,
                                            @RequestParam(value = "thread",required = false) Integer thread
    ) {
        isContinue = isContinue == null ? 0 : isContinue;
        thread = thread == null ? 1 : thread;
        log.error(" ======== 开始同步视频趋势数据 ,开始日期：{} - {} 线程数：{}  是否继续上次同步标记：{} 是否更新视频主数据：{}  ========", beginTime, endTime, thread, isContinue);
        tabcutCreatorVideoService.syncVideoTrend(beginTime, endTime, thread, isContinue);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * 同步视频趋势数据
     * @return
     */
    @GetMapping("/syncVideoTrendByCreatorId")
    public ApiResponseResult syncVideoTrendByCreatorId(@RequestParam("creatorId") String creatorId,
                                            @RequestParam("beginTime") String beginTime,
                                            @RequestParam("endTime") String endTime
    ) {
        tabcutCreatorVideoService.syncVideoTrendByCreatorId(beginTime, endTime, creatorId);
        return ApiResponseResult.buildSuccessResult();
    }

    @GetMapping("/syncCreatorTrend")
    public ApiResponseResult syncCreatorTrend(@RequestParam("isContinue") Integer isContinue, @RequestParam("beginTime") String beginTime, @RequestParam("endTime") String endTime, @RequestParam("thread") Integer thread) throws InterruptedException {
        expertService.syncCreatorTrend(beginTime, endTime, thread, isContinue);
        return ApiResponseResult.buildSuccessResult();
    }

    @GetMapping("/syncCreatorTrendByCreator")
    public ApiResponseResult syncCreatorTrendByCreator(@RequestParam("creatorId") String creatorId, @RequestParam("beginTime") String beginTime, @RequestParam("endTime") String endTime) {
        expertService.syncCreatorTrendByCreator(beginTime, endTime, creatorId);
        return ApiResponseResult.buildSuccessResult();
    }

    @GetMapping("/repairDayKpi")
    public ApiResponseResult repairDayKpi() {
        tabcutCreatorDayKpiService.repairDayKpi();
        return ApiResponseResult.buildSuccessResult();
    }

//    @GetMapping("/theadLayout")
//    public ApiResponseResult theadLayout() {
//        CompletableFuture<Void> creatorVideoFuture = CompletableFuture.runAsync(this::syncCreatorVideo)
//                .exceptionally(e->{
//                    e.printStackTrace();
//                    log.error("达人视频数据同步异常");
//                    return null;
//                });
//        CompletableFuture<Void> creatorBasicFuture = CompletableFuture.runAsync(this::syncCreatorBasic)
//                .exceptionally(e->{
//                    e.printStackTrace();
//                    log.error("达人基础数据同步异常");
//                    return null;
//                });
//        CompletableFuture<Void> videoBasicFuture = CompletableFuture.runAsync(() -> syncVideoInfo(3))
//                .exceptionally(e -> {
//                    e.printStackTrace();
//                    log.error("视频基础数据同步异常");
//                    return null;
//                });
//        CompletableFuture<Void> creatorTrendFuture = CompletableFuture.runAsync(this::syncCreatorTrend)
//                .exceptionally(e->{
//                    e.printStackTrace();
//                    log.error("达人趋势数据同步异常");
//                    return null;
//                });
//        CompletableFuture<Void> videoTrendFuture = CompletableFuture.runAsync(() -> syncVideoTrend("20220201", "20220301", 3))
//                .exceptionally(e -> {
//                    e.printStackTrace();
//                    log.error("视频趋势数据同步异常");
//                    return null;
//                });
//        CompletableFuture.allOf(
//                creatorVideoFuture,
//                creatorBasicFuture,
//                videoBasicFuture,
//                creatorTrendFuture,
//                videoTrendFuture
//        ).join();
//        log.error("开始修正数据");
//        repairDayKpi();
//        return ApiResponseResult.buildSuccessResult();
//    }



    @GetMapping("/syncCreatorCid")
    public ApiResponseResult syncCreatorCid(){
        expertService.syncCreatorCid();
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * 计算达人单日指标
     * @param beginDate
     * @param endDate
     * @return
     */
    @GetMapping("/computeCreatorDayKpi")
    public ApiResponseResult computeCreatorDayKpi(@RequestParam("isContinue") Integer isContinue, @RequestParam("beginDate") String beginDate, @RequestParam("endDate") String endDate, @RequestParam("thread") Integer thread) throws InterruptedException {
        expertService.computeCreatorDayKpi(beginDate, endDate, thread,isContinue);
        return ApiResponseResult.buildSuccessResult();
    }
    @GetMapping("/computeCreatorDayKpiByCreatorId")
    public ApiResponseResult computeCreatorDayKpiByCreatorId(@RequestParam("creatorId") Integer creatorId, @RequestParam("beginDate") String beginDate, @RequestParam("endDate") String endDate) {
        expertService.computeCreatorDayKpiByCreatorId(beginDate, endDate, creatorId);
        return ApiResponseResult.buildSuccessResult();
    }

    @GetMapping("/repairVideo")
    public ApiResponseResult repairVideo(){
        tabcutCreatorVideoService.repairVideo();
        return ApiResponseResult.buildSuccessResult();

    }


    @GetMapping("/videoTrendAndCompute")
    public ApiResponseResult videoTrendAndCompute() throws Exception {
//        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
//            log.error("-----------开始抓取趋势数据-----------");
//            long start = System.currentTimeMillis();
//            syncVideoTrend(1, "20240201", "20240301", 3);
//            long end = System.currentTimeMillis();
//            log.error("-----------趋势数据抓取完毕,耗时：{}-----------", end - start);
//        }).thenAccept(r -> {
//            log.error("-----------开始计算达人数据-----------");
//            long start = System.currentTimeMillis();
//            computeCreatorDayKpi(1, "20240201", "20240303", 3);
//            long end = System.currentTimeMillis();
//            log.error("-----------达人数据计算完毕,耗时：{}-----------", end - start);
//        }).exceptionally(e -> null);
//        future.join();
//        log.error("-------开始抓取达人日增粉丝数据------");
//        long start = System.currentTimeMillis();
//        syncCreatorTrend(1, "20240201", "20240301", 3);
//        long end = System.currentTimeMillis();
//        log.error("-------达人粉丝增量抓取完毕：{}---------", end - start);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/downloadVideoAndCover")
    public ApiResponseResult downloadVideoAndCover(){
        tabcutCreatorVideoUrlService.downloadVideoAndCover();
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/execSyncTask")
    public ApiResponseResult execSyncTask(){
        expertService.execSyncTask();
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/tabcutCreatorVideoBasic")
    public ApiResponseResult tabcutCreatorVideoBasic(@RequestParam(value = "thread",required = false) Integer thread) throws InterruptedException {
        tabcutCreatorVideoService.tabcutCreatorVideoBasic(thread);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/tabcutVideoPlayUrl")
    public ApiResponseResult tabcutVideoPlayUrl(Integer thread) throws InterruptedException {
        tabcutCreatorVideoUrlService.tabcutVideoPlayUrl(thread);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/tabcutUrlCorrect")
    public ApiResponseResult tabcutUrlCorrect() {
        tabcutCreatorVideoUrlService.tabcutUrlCorrect();
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/tabcutVideoFileHandle")
    public ApiResponseResult tabcutVideoFileHandle() {
        tabcutCreatorVideoUrlService.tabcutVideoFileHandle();
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/video/completeSku")
    public ApiResponseResult completeSku(){
        tabcutCreatorVideoService.completeSku();
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/videoDetailHandle")
    public ApiResponseResult videoDetailHandle(){
        tabcutCreatorVideoService.videoDetailHandle();
        return ApiResponseResult.buildSuccessResult();
    }

}
