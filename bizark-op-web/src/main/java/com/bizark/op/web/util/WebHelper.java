package com.bizark.op.web.util;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestMethod;

public class WebHelper {

    private static String[] allowedCrossOriginsDomains = new String[] {
            ".ehengjian.com",
            ".ejiankuajing.com",
            ".alaulm.com"
    };

    private static String[] allowedCrossMethds = new String[] {
            "GET","POST","OPTIONS","PUT","DELETE"
    };

    private static String[] allowedCrossOrigins = loadAllowedCrossOrigins();

    public static String[] getAllowedCrossOrigins() {
        return allowedCrossOrigins;
    }

    public static boolean isAllowOrigin(HttpServletRequest httpServletRequest) {
        String origin = httpServletRequest.getHeader("Origin");
        return isAllowOrigin(origin);
    }

    public static boolean isAllowOrigin(String origin) {
        if (origin == null || origin.isEmpty()) {
            return false;
        }
        String s = origin;
        if (s.startsWith("https://")) {
            s = s.substring(8);
        }
        else if (s.startsWith("http://")) {
            s = s.substring(7);
        }
        int a = s.indexOf(':');
        if (a > 0) {
            s = s.substring(0, a);
        }
        for (String allowedCrossOriginsDomain: allowedCrossOriginsDomains) {
            if (s.endsWith(allowedCrossOriginsDomain)) {
                return true;
            }
        }
        return false;
    }

    private static String[] loadAllowedCrossOrigins() {
        List<String> lst = new ArrayList<>();
        for (String allowedCrossOriginsDomain: allowedCrossOriginsDomains) {
            lst.add("https://*" + allowedCrossOriginsDomain);
            lst.add("http://*" + allowedCrossOriginsDomain);
        }
        String[] result = new String[lst.size()];
        lst.toArray(result);
        return result;
    }

    public static String[] getAllowedCrossMethds() {
        return allowedCrossMethds;
    }

    public static String getAllowedCrossMethdsString() {
        return String.join(",", allowedCrossMethds);
    }

    public static boolean crossProcess(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        boolean isEnd = false;
        boolean isProcess = crossProcessOriginAndCredentials(httpServletRequest, httpServletResponse);
        if (!isProcess) {
            return isEnd;
        }

        String[] allowedCrossMethds = getAllowedCrossMethds();
        if (allowedCrossMethds == null || allowedCrossMethds.length < 1) {
            return isEnd;
        }

        String sAllowedCrossMethds = String.join(",", allowedCrossMethds);

        httpServletResponse.setHeader("Access-Control-Allow-Methods", sAllowedCrossMethds);
        httpServletResponse.setHeader("Access-Control-Allow-Headers", httpServletRequest.getHeader("Access-Control-Request-Headers"));
        //httpServletResponse.setHeader("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Connection, User-Agent, Cookie");

        // 跨域时会首先发送一个option请求，这里我们给option请求直接返回正常状态
        String requestMethod = httpServletRequest.getMethod();
        if (requestMethod.equalsIgnoreCase(RequestMethod.OPTIONS.name())) {
            httpServletResponse.setStatus(HttpStatus.OK.value());
            isEnd =  true;
        }
        return isEnd;
    }

    public static boolean crossProcessOriginAndCredentials(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        /*
        boolean isProcess = false;

        String requestOrigin = httpServletRequest.getHeader("Origin");
        if (requestOrigin != null && !requestOrigin.isEmpty()) {
            for (String allowedCrossOrigin: allowedCrossOrigins) {
                if (requestOrigin.toLowerCase().endsWith(allowedCrossOrigin)) {
                    isProcess = true;
                    break;
                }
            }

        }

        if (!isProcess) {
            return isProcess;
        }

        httpServletResponse.setHeader("Access-Control-Allow-Origin", requestOrigin);
        httpServletResponse.setHeader("Access-Control-Allow-Credentials", "true");

        return isProcess;
        */

        String requestOrigin = httpServletRequest.getHeader("Origin");
        if (requestOrigin != null && !requestOrigin.isEmpty()) {
            httpServletResponse.setHeader("Access-Control-Allow-Origin", requestOrigin);
            httpServletResponse.setHeader("Access-Control-Allow-Credentials", "true");
            httpServletResponse.setHeader("Access-Control-Max-Age", "86400");
            return true;
        }

        return false;
    }
}
