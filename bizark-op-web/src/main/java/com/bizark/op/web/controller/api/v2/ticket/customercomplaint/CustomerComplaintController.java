package com.bizark.op.web.controller.api.v2.ticket.customercomplaint;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bizark.op.api.cons.PermDefine;
import com.bizark.op.api.cons.StatConstant;
import com.bizark.op.api.entity.op.document.ConfTicketProblem;
import com.bizark.op.api.entity.op.ticket.customercomplaint.VO.CustomerComplaintDetailVO;
import com.bizark.op.api.entity.op.ticket.customercomplaint.VO.CustomerComplaintTendencyVO;
import com.bizark.op.api.entity.op.ticket.customercomplaint.VO.CustomerComplaintTotalVO;
import com.bizark.op.api.entity.op.ticket.customercomplaint.query.CustomerComplaintMainQuery;
import com.bizark.op.api.exception.ErpCommonException;
import com.bizark.op.api.service.RpcMarExportService;
import com.bizark.op.api.service.ticket.IConfTicketProblemService;
import com.bizark.op.api.service.ticket.customercomplaint.CustomerComplaintDetailService;
import com.bizark.op.api.service.ticket.customercomplaint.CustomerComplaintDimensionTotalService;
import com.bizark.op.api.service.ticket.customercomplaint.CustomerComplaintTendencyService;
import com.bizark.op.api.service.ticket.customercomplaint.CustomerComplaintTotalService;
import com.bizark.op.common.core.page.PageInfoDomain;
import com.bizark.op.common.core.page.TableDataInfo;
import com.bizark.op.common.core.page.TableSupportInfo;
import com.bizark.op.common.enm.ContentTypeEnum;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.web.controller.api.AbstractApiController;
import com.bizark.framework.web.view.ApiResponseResult;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;

/**
 * 客诉控制器
 * @Author: Ailill
 * @Date: 2024/6/5 11:38
 */
@RestController
@RequestMapping("/api/v2/ticket/customercomplaint")
@Slf4j
public class CustomerComplaintController extends AbstractApiController {

    @Autowired
    private CustomerComplaintDetailService customerComplaintDetailService;

    @Autowired
    private CustomerComplaintDimensionTotalService customerComplaintDimensionTotalService;

    @Autowired
    private CustomerComplaintTotalService customerComplaintTotalService;

    @Autowired
    private CustomerComplaintTendencyService customerComplaintTendencyService;

    @Autowired
    private RpcMarExportService rpcMarExportService;


    @Autowired
    private IConfTicketProblemService confTicketProblemService;

    protected void newStartPage()
    {
        PageInfoDomain pageInfoDomain = TableSupportInfo.buildPageRequest();
        Integer page = pageInfoDomain.getPage();
        Integer rows = pageInfoDomain.getRows();
        logger.info("当前分页参数:" + JSON.toJSONString(pageInfoDomain));
        if (StringUtils.isNotNull(page) && StringUtils.isNotNull(rows))
        {
            if(!"total".equals(pageInfoDomain.getSidx()) && StringUtil.isNotEmpty(pageInfoDomain.getSidx())) {
                pageInfoDomain.setSidx("`" + pageInfoDomain.getSidx() + "`");
            }
            PageHelper.startPage(page, rows, pageInfoDomain.getOrderBy());
        }
    }

    /**
     * @Description:客诉明细列表
     * @Author: wly
     * @Date: 2024/6/5 15:02
     * @Params: [query, contextId]
     * @Return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @PostMapping("/detail/list")
    @RequiresPermissions(PermDefine.MAR_CUSTOMER_ANALYSIS)
    public TableDataInfo CustomerComplaintDetailList(@RequestBody CustomerComplaintMainQuery query, @RequestParam Integer contextId) {

        query.setOrganizationId(contextId);
        startPage();
        List<CustomerComplaintDetailVO> data = customerComplaintDetailService.selectCustomerComplaintDetailList(query);
        return getDataTable(data);
    }


    /**
     * Description: 客诉分析 维度汇总 一级list
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/6/12
     */
    @PostMapping("/dimension/summary/one/list")
    @RequiresPermissions(PermDefine.MAR_CUSTOMER_ANALYSIS)
    public TableDataInfo CustomerComplaintOneList(@RequestBody CustomerComplaintMainQuery query, @RequestParam Integer contextId) {
        if((StatConstant.STAT_DATA_TYPE_DAY.equals(query.getDateType()) || StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType()))){
            if(query.getDateFrom() != null ) {
                if(cn.hutool.core.date.DateUtil.betweenDay(query.getDateFrom(), query.getDateTo(), false)>91) {
                    throw new CustomException("按天或周查询退货日期查询日维度不可大于91天");
                }
            }
        }else {
            if(query.getDateFrom() != null ) {
                if(cn.hutool.core.date.DateUtil.betweenDay(query.getDateFrom(), query.getDateTo(), false)>730) {
                    throw new CustomException("按月查询退货日期查询日维度不可大于2年");
                }
            }
        }
        query.setOrganizationId(contextId);
        this.getSql(query,"one");
        newStartPage();
        return getDataTable(customerComplaintDimensionTotalService.selectCustomerComplaintOneList(query));
    }

    /**
     * Description: 客诉分析 维度汇总 一级list detail
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/6/12
     */
    @PostMapping("/dimension/summary/one/list/detail")
    public ApiResponseResult CustomerComplaintOneDetaiList(@RequestBody CustomerComplaintMainQuery query, @RequestParam Integer contextId) {
        if((StatConstant.STAT_DATA_TYPE_DAY.equals(query.getDateType()) || StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType()))){
            if(query.getDateFrom() != null ) {
                if(cn.hutool.core.date.DateUtil.betweenDay(query.getDateFrom(), query.getDateTo(), false)>91) {
                    throw new CustomException("按天或周查询退货日期查询日维度不可大于91天");
                }
            }
        }else {
            if(query.getDateFrom() != null ) {
                if(cn.hutool.core.date.DateUtil.betweenDay(query.getDateFrom(), query.getDateTo(), false)>730) {
                    throw new CustomException("按月查询退货日期查询日维度不可大于2年");
                }
            }
        }
        query.setOrganizationId(contextId);
        this.getSql(query,"one");
        return ApiResponseResult.buildSuccessResult(customerComplaintDimensionTotalService.selectCustomerComplaintOneDetailList(query));
    }


    /**
     * Description: 客诉分析 维度汇总 一级sum
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/6/12
     */
    @PostMapping("/dimension/summary/one/sum")
    @RequiresPermissions(PermDefine.MAR_CUSTOMER_ANALYSIS)
    public ApiResponseResult CustomerComplaintOneSum(@RequestBody CustomerComplaintMainQuery query, @RequestParam Integer contextId) {
        if((StatConstant.STAT_DATA_TYPE_DAY.equals(query.getDateType()) || StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType()))){
            if(query.getDateFrom() != null ) {
                if(cn.hutool.core.date.DateUtil.betweenDay(query.getDateFrom(), query.getDateTo(), false)>91) {
                    throw new CustomException("按天或周查询退货日期查询日维度不可大于91天");
                }
            }
        }else {
            if(query.getDateFrom() != null ) {
                if(cn.hutool.core.date.DateUtil.betweenDay(query.getDateFrom(), query.getDateTo(), false)>730) {
                    throw new CustomException("按月查询退货日期查询日维度不可大于2年");
                }
            }
        }
        query.setOrganizationId(contextId);
        //动态拼接sql
        ConfTicketProblem confTicketProblem = new ConfTicketProblem();
        confTicketProblem.setLevel(1);
        confTicketProblem.setOrganizationId(query.getOrganizationId().longValue());
        List<ConfTicketProblem> confTicketProblemList = confTicketProblemService.selectConfTicketProblemList(confTicketProblem);
        if (CollectionUtil.isEmpty(confTicketProblemList)) {
            throw new RuntimeException("问题分类未查询到，请在设置-问题配置内配置");
        }
        String selectHeadSql = "";
        for (ConfTicketProblem c : confTicketProblemList) {
            selectHeadSql += " SUM(if(b.problem_type1 = '" + c.getProblemId() + "',1,0)) as " + "'" + c.getProblemId() + "String'" + ",\n";
        }
        String selectSumHeadSql = "";
        for (ConfTicketProblem c : confTicketProblemList) {
            selectSumHeadSql += " sum(s." + c.getProblemId() + "String" + ") " + "'" + c.getProblemId() + "'" + ",\n";
        }

        selectHeadSql = selectHeadSql.substring(0,selectHeadSql.length() - 2);//去除多余逗号
        selectSumHeadSql = selectSumHeadSql.substring(0,selectSumHeadSql.length() - 2);
        query.setSelectHeadSql(selectHeadSql);
        query.setSelectSumHeadSql(selectSumHeadSql);
        query.setConfTicketProblemList(confTicketProblemList);
        return ApiResponseResult.buildSuccessResult(customerComplaintDimensionTotalService.selectCustomerComplaintOneSum(query));
    }


    /**
     * Description: 客诉分析 维度汇总 一级导出
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/6/12
     */
    @PostMapping("/dimension/summary/one/export")
    public ApiResponseResult CustomerComplaintOneExport(@RequestBody CustomerComplaintMainQuery query, @RequestParam Integer contextId,HttpServletResponse response) throws UnsupportedEncodingException {
        if((StatConstant.STAT_DATA_TYPE_DAY.equals(query.getDateType()) || StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType()))){
            if(query.getDateFrom() != null ) {
                if(cn.hutool.core.date.DateUtil.betweenDay(query.getDateFrom(), query.getDateTo(), false)>91) {
                    throw new CustomException("按天或周查询退货日期查询日维度不可大于91天");
                }
            }
        }else {
            if(query.getDateFrom() != null ) {
                if(cn.hutool.core.date.DateUtil.betweenDay(query.getDateFrom(), query.getDateTo(), false)>730) {
                    throw new CustomException("按月查询退货日期查询日维度不可大于2年");
                }
            }
        }
        query.setOrganizationId(contextId);
        this.getSql(query,"one");
        customerComplaintDimensionTotalService.listExportCustomerComplaintDimensionOneListDetail(response, query, getAuthUserEntity());
//        String s = rpcMarExportService.listCustomerComplaintDimensionOneListDetailExport(JSONObject.toJSONString(query));
        return ApiResponseResult.buildSuccessResult("导出已生成，请至【任务中心】-【任务列表】查看处理进度及下载文件！");
    }

    /**
     * Description: 客诉分析 维度汇总 二级list
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/6/12
     */
    @PostMapping("/dimension/summary/two/list")
    public TableDataInfo CustomerComplaintTwoList(@RequestBody CustomerComplaintMainQuery query, @RequestParam Integer contextId) {

        if((StatConstant.STAT_DATA_TYPE_DAY.equals(query.getDateType()) || StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType()))){
            if(query.getDateFrom() != null ) {
                if(cn.hutool.core.date.DateUtil.betweenDay(query.getDateFrom(), query.getDateTo(), false)>91) {
                    throw new CustomException("按天或周查询退货日期查询日维度不可大于91天");
                }
            }
        }else {
            if(query.getDateFrom() != null ) {
                if(cn.hutool.core.date.DateUtil.betweenDay(query.getDateFrom(), query.getDateTo(), false)>730) {
                    throw new CustomException("按月查询退货日期查询日维度不可大于2年");
                }
            }
        }query.setOrganizationId(contextId);
        this.getSql(query,"two");
        newStartPage();
        return getDataTable(customerComplaintDimensionTotalService.selectCustomerComplaintTwoList(query));
    }
    /**
     * Description: 客诉分析 维度汇总 二级list detail
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/6/12
     */
    @PostMapping("/dimension/summary/two/list/detail")
    public ApiResponseResult CustomerComplaintTwoDetailList(@RequestBody CustomerComplaintMainQuery query, @RequestParam Integer contextId) {
        if((StatConstant.STAT_DATA_TYPE_DAY.equals(query.getDateType()) || StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType()))){
            if(query.getDateFrom() != null ) {
                if(cn.hutool.core.date.DateUtil.betweenDay(query.getDateFrom(), query.getDateTo(), false)>91) {
                    throw new CustomException("按天或周查询退货日期查询日维度不可大于91天");
                }
            }
        }else {
            if(query.getDateFrom() != null ) {
                if(cn.hutool.core.date.DateUtil.betweenDay(query.getDateFrom(), query.getDateTo(), false)>730) {
                    throw new CustomException("按月查询退货日期查询日维度不可大于2年");
                }
            }
        }
        query.setOrganizationId(contextId);
        this.getSql(query,"two");
        return ApiResponseResult.buildSuccessResult(customerComplaintDimensionTotalService.selectCustomerComplaintTwoDetailList(query));
    }

    /**
     * Description: 客诉分析 维度汇总 二级list sum
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/6/12
     */
    @PostMapping("/dimension/summary/two/sum")
    public ApiResponseResult CustomerComplaintTwoSum(@RequestBody CustomerComplaintMainQuery query, @RequestParam Integer contextId) {
        if((StatConstant.STAT_DATA_TYPE_DAY.equals(query.getDateType()) || StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType()))){
            if(query.getDateFrom() != null ) {
                if(cn.hutool.core.date.DateUtil.betweenDay(query.getDateFrom(), query.getDateTo(), false)>91) {
                    throw new CustomException("按天或周查询退货日期查询日维度不可大于91天");
                }
            }
        }else {
            if(query.getDateFrom() != null ) {
                if(cn.hutool.core.date.DateUtil.betweenDay(query.getDateFrom(), query.getDateTo(), false)>730) {
                    throw new CustomException("按月查询退货日期查询日维度不可大于2年");
                }
            }
        }
        query.setOrganizationId(contextId);
        //动态拼接sql
        ConfTicketProblem confTicketProblem = new ConfTicketProblem();
        confTicketProblem.setLevel(2);
        confTicketProblem.setOrganizationId(query.getOrganizationId().longValue());
        List<ConfTicketProblem> confTicketProblemList = confTicketProblemService.selectConfTicketProblemList(confTicketProblem);
        if (CollectionUtil.isEmpty(confTicketProblemList)) {
            throw new RuntimeException("问题分类未查询到，请在设置-问题配置内配置");
        }
        String selectHeadSql = "";
        for (ConfTicketProblem c : confTicketProblemList) {
            selectHeadSql += " SUM(if(b.problem_type2 = '" + c.getProblemId() + "',1,0)) as " + "'" + c.getProblemId() + "String'" + ",\n";
        }
        String selectSumHeadSql = "";
        for (ConfTicketProblem c : confTicketProblemList) {
            selectSumHeadSql += " sum(s." + c.getProblemId() + "String" + ") " + "'" + c.getProblemId() + "'" + ",\n";
        }

        selectHeadSql = selectHeadSql.substring(0,selectHeadSql.length() - 2);//去除多余逗号
        selectSumHeadSql = selectSumHeadSql.substring(0,selectSumHeadSql.length() - 2);
        query.setSelectHeadSql(selectHeadSql);
        query.setSelectSumHeadSql(selectSumHeadSql);
        query.setConfTicketProblemList(confTicketProblemList);
        return ApiResponseResult.buildSuccessResult(customerComplaintDimensionTotalService.selectCustomerComplaintTwoSum(query));
    }

    /**
     * Description: 客诉分析 维度汇总 二级list export
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/6/12
     */
    @PostMapping("/dimension/summary/two/export")
    public ApiResponseResult CustomerComplaintTwoExport(@RequestBody CustomerComplaintMainQuery query, @RequestParam Integer contextId,HttpServletResponse response) throws UnsupportedEncodingException {
        if((StatConstant.STAT_DATA_TYPE_DAY.equals(query.getDateType()) || StatConstant.STAT_DATA_TYPE_WEEK.equals(query.getDateType()))){
            if(query.getDateFrom() != null ) {
                if(cn.hutool.core.date.DateUtil.betweenDay(query.getDateFrom(), query.getDateTo(), false)>91) {
                    throw new CustomException("按天或周查询退货日期查询日维度不可大于91天");
                }
            }
        }else {
            if(query.getDateFrom() != null ) {
                if(cn.hutool.core.date.DateUtil.betweenDay(query.getDateFrom(), query.getDateTo(), false)>730) {
                    throw new CustomException("按月查询退货日期查询日维度不可大于2年");
                }
            }
        }
        query.setOrganizationId(contextId);
        this.getSql(query,"two");
//        customerComplaintDimensionTotalService.listExportCustomerComplaintDimensionTwoListDetail(response, query, getAuthUserEntity());
        ConfTicketProblem confTicketProblem = new ConfTicketProblem();
        confTicketProblem.setLevel(1);
        confTicketProblem.setOrganizationId(query.getOrganizationId().longValue());
        List<ConfTicketProblem> confTicketProblemList = confTicketProblemService.selectConfTicketProblemList(confTicketProblem);
        if (CollectionUtil.isEmpty(confTicketProblemList)) {
            throw new RuntimeException("问题分类未查询到，请在设置-问题配置内配置");
        }
        for(ConfTicketProblem ticketProblem : query.getConfTicketProblemList()) {
            for(ConfTicketProblem c :confTicketProblemList) {
                if(ticketProblem.getParentId().equals(c.getProblemId())) {
                    ticketProblem.setParent(c);
                }
            }
        }
        customerComplaintDimensionTotalService.listExportCustomerComplaintDimensionTwoListDetail(response, query, getAuthUserEntity());
//        String s = rpcMarExportService.listCustomerComplaintDimensionTwoListDetailExport(JSONObject.toJSONString(query));
        return ApiResponseResult.buildSuccessResult("导出已生成，请至【任务中心】-【任务列表】查看处理进度及下载文件！");
    }

    /**
     * @Description:客诉分析明细导出
     * @Author: wly
     * @Date: 2024/6/5 17:18
     * @Params: [query, contextId]
     * @Return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @PostMapping("/detail/export")
    public ApiResponseResult customerComplaintDetailExport(@RequestBody CustomerComplaintMainQuery query, @RequestParam Integer contextId) {

        query.setOrganizationId(contextId);
        customerComplaintDetailService.exportCustomerComplaintDetailList(query,getAuthUserEntity());
        return ApiResponseResult.buildSuccessResult("导出已生成，请至【任务中心】-【任务列表】查看处理进度及下载文件！");
    }

    /**
     * @Description:客诉分析汇总
     * @Author: wly
     * @Date: 2024/6/5 17:18
     * @Params: [query, contextId]
     * @Return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @PostMapping("/total/list")
    @RequiresPermissions(PermDefine.MAR_CUSTOMER_ANALYSIS)
    public ApiResponseResult customerComplaintTotalList(@RequestBody CustomerComplaintMainQuery query, @RequestParam Integer contextId) {
        query.setOrganizationId(contextId);
        List<CustomerComplaintTotalVO> list = customerComplaintTotalService.selectCustomerComplaintTotal(query);
        return ApiResponseResult.buildSuccessResult(list);
    }

   /**
    * @Description: 客诉分析趋势汇总
    * @Author: wly
    * @Date: 2024/6/6 11:39
    * @Params: [query, contextId]
    * @Return: com.bizark.framework.web.view.ApiResponseResult
    **/
    @PostMapping("/tendency/list")
    @RequiresPermissions(PermDefine.MAR_CUSTOMER_ANALYSIS)
    public ApiResponseResult customerComplaintTendencyList(@RequestBody CustomerComplaintMainQuery query, @RequestParam Integer contextId) {

        query.setOrganizationId(contextId);
        List<CustomerComplaintTendencyVO> list = customerComplaintTendencyService.selectCustomerComplaintTendencyList(query);
        JSONObject object = new JSONObject();
        if (CollectionUtil.isNotEmpty(list)) {
            object.put("x", list.get(0).getDateStr());
            object.put("result", list);
        }
        return ApiResponseResult.buildSuccessResult(object);
    }

    /**
     * @Description:客诉分析趋势导出
     * @Author: wly
     * @Date: 2024/6/6 14:36
     * @Params: [query, contextId, response]
     * @Return: void
     **/
    @PostMapping("/tendency/export")
    public void customerComplaintTendencyListExport(@RequestBody CustomerComplaintMainQuery query, @RequestParam Integer contextId, HttpServletResponse response) {

        query.setOrganizationId(contextId);
        List<CustomerComplaintTendencyVO> list = customerComplaintTendencyService.selectCustomerComplaintTendencyList(query);
        if (CollectionUtil.isEmpty(list)) {
            throw new ErpCommonException("导出数据为空");
        }
        try {
            response.setContentType(ContentTypeEnum.xlsx.getName());
            String fileName = URLEncoder.encode("客诉趋势" + System.currentTimeMillis(), "UTF-8").replaceAll("\\+", "%20");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcelFactory.write(response.getOutputStream(), CustomerComplaintTendencyVO.class).sheet("客诉趋势").doWrite(list);
        } catch (Exception e) {
            throw new ErpCommonException("导出失败");
        }
    }

    /**
     * Description: 动态拼接sql
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/6/21
     */
    public void getSql(CustomerComplaintMainQuery query,String type) {
        if("one".equals(type)) {
            //动态拼接sql
            ConfTicketProblem confTicketProblem = new ConfTicketProblem();
            confTicketProblem.setLevel(1);
            confTicketProblem.setOrganizationId(query.getOrganizationId().longValue());
            List<ConfTicketProblem> confTicketProblemList = confTicketProblemService.selectConfTicketProblemList(confTicketProblem);
            if (CollectionUtil.isEmpty(confTicketProblemList)) {
                throw new RuntimeException("问题分类未查询到，请在设置-问题配置内配置");
            }
            String selectHeadSql = "";
            for (ConfTicketProblem c : confTicketProblemList) {
                selectHeadSql += " SUM(if(b.problem_type1 = '" + c.getProblemId() + "',1,0)) as " + "`" + c.getProblemId() + "`" + ",\n";
            }
            selectHeadSql = selectHeadSql.substring(0, selectHeadSql.length() - 2);//去除多余逗号
            query.setSelectHeadSql(selectHeadSql);
            query.setConfTicketProblemList(confTicketProblemList);
        } else if("two".equals(type)) {
            //动态拼接sql
            ConfTicketProblem confTicketProblem = new ConfTicketProblem();
            confTicketProblem.setLevel(2);
            confTicketProblem.setOrganizationId(query.getOrganizationId().longValue());
            List<ConfTicketProblem> confTicketProblemList = confTicketProblemService.selectConfTicketProblemList(confTicketProblem);
            if (CollectionUtil.isEmpty(confTicketProblemList)) {
                throw new RuntimeException("问题分类未查询到，请在设置-问题配置内配置");
            }
            String selectHeadSql = "";
            for (ConfTicketProblem c : confTicketProblemList) {
                selectHeadSql += " SUM(if(b.problem_type2 = '" + c.getProblemId() + "',1,0)) as " + "`" + c.getProblemId() + "`" + ",\n";
            }
            selectHeadSql = selectHeadSql.substring(0,selectHeadSql.length() - 2);//去除多余逗号
            query.setSelectHeadSql(selectHeadSql);
            query.setConfTicketProblemList(confTicketProblemList);
        }
    }
}
