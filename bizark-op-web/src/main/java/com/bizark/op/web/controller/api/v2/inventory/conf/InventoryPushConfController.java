package com.bizark.op.web.controller.api.v2.inventory.conf;

import cn.hutool.core.collection.CollectionUtil;
import com.bizark.op.api.cons.PermDefine;
import com.bizark.op.api.entity.op.inventory.conf.dto.InventoryPushConfDTO;
import com.bizark.op.api.entity.op.inventory.conf.dto.PushProportionDTO;
import com.bizark.op.api.entity.op.inventory.conf.vo.InventoryPushConfListVO;
import com.bizark.op.api.service.inventory.conf.InventoryPushConfService;
import com.bizark.op.api.service.inventory.conf.InventoryWarehousePushProportionService;
import com.bizark.op.common.util.AssertUtil;
import com.bizark.op.common.util.ValidateUtil;
import com.bizark.op.web.controller.api.AbstractApiController;
import com.bizark.framework.web.view.ApiResponseResult;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v2/inventory/conf")
public class InventoryPushConfController extends AbstractApiController {


    @Autowired
    private InventoryPushConfService inventoryPushConfService;

    @Autowired
    private InventoryWarehousePushProportionService inventoryWarehousePushProportionService;


    @PostMapping("/save")
    @RequiresPermissions(PermDefine.ERP_VIRTUALSTORE_INVENTORY_PUSH)
    public ApiResponseResult save(@RequestBody InventoryPushConfDTO dto,@RequestParam("contextId") Integer contextId){
        ValidateUtil.beanValidate(dto);
        ValidateUtil.beanValidate(dto.getConfInfo());
        inventoryPushConfService.saveConf(contextId,dto);
        return ApiResponseResult.buildSuccessResult();
    }


    @PostMapping("/updateSingleWarehouse")
    @RequiresPermissions(PermDefine.ERP_VIRTUALSTORE_INVENTORY_PUSH)
    public ApiResponseResult upDateSingleWarehouse(@RequestBody InventoryPushConfDTO dto,@RequestParam("contextId") Integer contextId) {
        inventoryPushConfService.updateSingleWarehouse(contextId,dto);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/list")
    @RequiresPermissions(PermDefine.ERP_VIRTUALSTORE_INVENTORY_PUSH)
    public ApiResponseResult list(Integer contextId){
        InventoryPushConfListVO listVO = inventoryPushConfService.selectInventoryConf(contextId);
        return ApiResponseResult.buildSuccessResult(listVO);
    }


    @PostMapping("/modifyPushProportion")
    @RequiresPermissions(PermDefine.ERP_VIRTUALSTORE_INVENTORY_PUSH)
    public ApiResponseResult modifyPushProportion(@RequestParam("contextId") Integer contextId, @RequestBody PushProportionDTO dto) {
        ValidateUtil.beanValidate(dto);
        AssertUtil.isFalse(CollectionUtil.isEmpty(dto.getWarehouse()), "仓库不能为空");
        inventoryWarehousePushProportionService.modifyPushProportion(contextId, dto);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/selectPushProportoin")
    @RequiresPermissions(PermDefine.ERP_VIRTUALSTORE_INVENTORY_PUSH)
    public ApiResponseResult selectPushProportoin(@RequestParam("contextId") Integer contextId) {
        return ApiResponseResult.buildSuccessResult(inventoryWarehousePushProportionService.selectPushProportionByOrgId(contextId));
    }


    @GetMapping("/selectWarehouse")
    @RequiresPermissions(PermDefine.ERP_VIRTUALSTORE_INVENTORY_PUSH)
    public ApiResponseResult selectWarehouseByWarehouseType(@RequestParam("contextId") Integer contextId){
        return ApiResponseResult.buildSuccessResult(inventoryWarehousePushProportionService.selectWarehouse(contextId));
    }

}
