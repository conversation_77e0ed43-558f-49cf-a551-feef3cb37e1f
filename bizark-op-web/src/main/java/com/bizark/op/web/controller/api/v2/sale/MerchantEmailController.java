package com.bizark.op.web.controller.api.v2.sale;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.bizark.ad.api.exception.AdCommonException;
import com.bizark.op.api.entity.op.sale.vo.ProductsSpecificationExportVO;
import com.bizark.op.api.entity.op.ticket.MerchantEmail;
import com.bizark.op.api.entity.op.ticket.order.AmzVcPoOrderV;
import com.bizark.op.api.service.sys.ISysDictDataService;
import com.bizark.op.api.service.ticket.IMerchantEmailService;
import com.bizark.op.common.core.domain.sys.SysDictData;
import com.bizark.op.common.core.page.TableDataInfo;
import com.bizark.op.common.util.ExcelUtils;
import com.bizark.op.common.util.poi.ExcelUtil;
import com.bizark.op.service.annotation.OrgPermission;
import com.bizark.op.service.util.ConvertUtils;
import com.bizark.op.service.util.SpringBeanUtil;
import com.bizark.op.web.controller.api.AbstractApiController;
import com.bizark.framework.web.view.ApiResponseResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@OrgPermission
@RestController
@RequestMapping("/api/v2/email/merchantEmail")
public class MerchantEmailController extends AbstractApiController {

    @Autowired
    private IMerchantEmailService merchantEmailService;
    @Autowired
    private ISysDictDataService sysDictDataService;


    /**
     * 查询邮箱配置列表
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody MerchantEmail merchantEmail, Integer contextId) {
        startPage();
        merchantEmail.setOrganizationId(contextId);
        List<MerchantEmail> list = merchantEmailService.selectMerchantEmailList(merchantEmail);
        return getDataTable(list);
    }

    /**
     * 查询邮箱配置列表
     */
    @GetMapping("/list/All")
    public TableDataInfo listAll(MerchantEmail merchantEmail,Integer contextId) {
        merchantEmail.setOrganizationId(contextId);
        List<MerchantEmail> list = merchantEmailService.selectMerchantEmailList(merchantEmail);
        return getDataTable(list);
    }

    /**
     * 导出邮箱配置列表
     */
//    @PostMapping("/export")
    public ApiResponseResult export(@RequestBody MerchantEmail merchantEmail, @RequestParam("contextId") Integer contextId, HttpServletResponse response) throws IOException, IllegalAccessException {
        List<MerchantEmail> emails = merchantEmailService.selectMerchantEmailList(merchantEmail);
        //List<MerchantEmail> emails = merchantEmailService.selectMerchantEmailByIds(ids, contextId);
        if (CollectionUtil.isEmpty(emails)) {
            return ApiResponseResult.buildFailureResult("无可导出数据！");
        }
        try {
            ConvertUtils.convert(emails);
            for (MerchantEmail email : emails) {
                email.setEnabledFlagDict("Y".equals(email.getEnabledFlag()) ? "启用" : "停用");
            }
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename="+ URLEncoder.encode("email_conf", "utf8") +".xlsx");
            EasyExcel.write(response.getOutputStream(), MerchantEmail.class)
                    .sheet("email_conf")
                    .doWrite(emails);

        } catch (IOException e) {
            throw new AdCommonException("导出异常");
        }
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * 导出邮箱信息
     *
     * @param recordId
     * @param response
     * @return
     */
    @PostMapping("/export")
    public ApiResponseResult exportEmailV2(@RequestBody MerchantEmail merchantEmail, @RequestParam("contextId") Integer contextId,HttpServletResponse response) {
        merchantEmail.setOrganizationId(contextId);
        List<MerchantEmail> emails = merchantEmailService.selectMerchantEmailList(merchantEmail);
        SysDictData sysDictDataParam = new SysDictData();
        sysDictDataParam.setDictType("email_service_code");
        List<SysDictData> sysDictData = sysDictDataService.selectDictDataList(sysDictDataParam);
        Map<String, String> serviceCodeMap = sysDictData.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        for (MerchantEmail email : emails) {
            email.setServiceCode(serviceCodeMap.get(email.getServiceCode()));
        }
        ExcelUtil<MerchantEmail> util = new ExcelUtil<MerchantEmail>(MerchantEmail.class);
        return util.exportExcel(emails, "email_config", "email_config", response);
    }








    /**
     * 获取店铺站内邮箱
     */
    @GetMapping(value = "/inside/{shopId}")
    public ApiResponseResult getInsideEmail(@PathVariable("shopId") Long shopId) {
        return ApiResponseResult.buildSuccessResult(merchantEmailService.selectInsideEmail(shopId));
    }

    /**
     * 获取站外邮箱列表
     */
    @GetMapping(value = "/outside")
    public ApiResponseResult getOutsideEmail(MerchantEmail merchantEmail,@RequestParam("contextId") Integer contextId) {
        merchantEmail.setOrganizationId(contextId);
        return ApiResponseResult.buildSuccessResult(merchantEmailService.selectOutsideEmail(merchantEmail));
    }

    /**
     * 获取邮箱配置详细信息
     */
    @GetMapping(value = "/{id}")
    public ApiResponseResult getInfo(@PathVariable("id") Long id) {
        return ApiResponseResult.buildSuccessResult(merchantEmailService.selectMerchantEmailById(id));
    }

    /**
     * 新增邮箱配置
     */
    @PostMapping
    public ApiResponseResult add(@RequestBody MerchantEmail merchantEmail,@RequestParam("contextId") Integer contextId) {
        merchantEmail.setOrganizationId(contextId);
        return ApiResponseResult.buildSuccessResult(merchantEmailService.insertMerchantEmail(merchantEmail,getAuthUserEntity()));
    }

    /**
     * 修改邮箱配置
     */
    @PutMapping
    public ApiResponseResult edit(@RequestBody MerchantEmail merchantEmail) {
        return ApiResponseResult.buildSuccessResult(merchantEmailService.updateMerchantEmail(merchantEmail,getAuthUserEntity()));
    }

    /**
     * 修改邮箱配置启用/停用
     */
    @PostMapping("/changeMerchantEmailEnabled")
    public ApiResponseResult changeMerchantEmailEnabled(@RequestBody MerchantEmail merchantEmail) {
        return ApiResponseResult.buildSuccessResult(merchantEmailService.updateMerchantEmailEnabledFlag(merchantEmail,getAuthUserEntity()));
    }

    /**
     * 手动同步邮箱信息
     */
    @PutMapping("/async/{merchantEmailId}")
    public ApiResponseResult asyncMerchantEmail(@PathVariable Long merchantEmailId,String start,String end) {
        merchantEmailService.syncEmailInfo(merchantEmailId,start,end);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * 删除邮箱配置
     */
    @DeleteMapping("/{emailIds}")
    public ApiResponseResult remove(@PathVariable Long[] emailIds) {
        return ApiResponseResult.buildSuccessResult(merchantEmailService.deleteMerchantEmailByIds(emailIds));
    }

    /**
     * 站外信邮件信息,qa,atoz,亚马逊邮件
     */
    @GetMapping("/emailList/{ticketId}")
    public ApiResponseResult getAmzSiteMsgEmailList(@PathVariable("ticketId") Long ticketId) {
        return ApiResponseResult.buildSuccessResult(merchantEmailService.getAmzSiteMsgEmailList(ticketId));
    }

    /**
     * 站外信邮件信息
     */
    @GetMapping("/history/{ticketId}")
    public ApiResponseResult getAmzSiteMsgEmailHistoryList(@PathVariable("ticketId") Long ticketId) {
        return ApiResponseResult.buildSuccessResult(merchantEmailService.getAmzSiteMsgEmailHistoryList(ticketId));
    }

    @PutMapping("/wayfair/async")
    public ApiResponseResult wayFairAsyncMerchantEmail(String startDate,String endDate) throws ParseException {
        merchantEmailService.syncEmailInfoWithWayFair(startDate,endDate);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * 同步店铺邮件信息(异步)
     */
    @PutMapping("/walmart/async/{merchantEmailId}")
    public ApiResponseResult asyncWalmartMerchantEmail(@PathVariable Long merchantEmailId,String start,String end) throws ParseException {
        merchantEmailService.syncWalmartEmailInfo(merchantEmailId,start,end);
        return ApiResponseResult.buildSuccessResult();
    }
}
