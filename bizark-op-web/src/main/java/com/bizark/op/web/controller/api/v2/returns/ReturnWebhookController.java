package com.bizark.op.web.controller.api.v2.returns;

import com.alibaba.fastjson.JSONObject;
import com.bizark.op.api.cons.MQDefine;
import com.bizark.op.api.service.mar.contactCustomer.MarSendMessageRuleService;
import com.bizark.op.api.service.order.SaleOrderCancelService;
import com.bizark.op.api.service.refund.IOrderRefundRequestInfoService;
import com.bizark.op.api.service.returns.ReturnWebhookService;
import com.bizark.op.api.service.tiktok.ITiktokMessageService;
import com.bizark.op.common.enm.TikTokEnum;
import com.bizark.op.common.factory.RefundAndReturnFactory;
import com.bizark.op.common.util.RedisUtils;
import com.bizark.op.common.util.StringUtils;
import com.bizark.framework.web.view.ApiResponseResult;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023年10月7日  15:07
 * @description: 退货webhook接口调用
 */
@Slf4j
@RestController("returnWebhookController")
@RequestMapping(value = "/callback/return/webhook/")
public class ReturnWebhookController {

    @Autowired
    ReturnWebhookService returnWebhookService;
    @Resource
    private RefundAndReturnFactory factory;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    private RedisUtils redisUtils;
    @Autowired
    private SaleOrderCancelService saleOrderCancelService;

    @Autowired
    private MarSendMessageRuleService marSendMessageRuleService;

    @Autowired
    private ITiktokMessageService iTiktokMessageService;

    @Autowired
    private IOrderRefundRequestInfoService orderRefundRequestInfoService;

    @PostMapping("tiktokReturnData")
    public ApiResponseResult tiktokReturnData(
            @RequestBody JSONObject tiktokData
    ) {
        log.info("tiktok webhook 接收到数据:{}", tiktokData);
        if (StringUtils.isEmpty(tiktokData)) {
            log.info("tiktok退货数据为空:{}", tiktokData);
            return ApiResponseResult.buildSuccessResult();
        }
        try {
            // 跟新逆向订单相关数据,拿逆向id去调用 判断退回接口中 reverse_type = 1
            returnWebhookService.sendTiktokReturnData(tiktokData);
            saleOrderCancelService.saveOrderCancelV2(tiktokData);
//            factory.getInvokeStrategy(TikTokEnum.TIK_TOK.getEnumType()).receiveReverseOrderFromWebApi(tiktokData,TikTokEnum.TIK_TOK.getEnumType());
            orderRefundRequestInfoService.saveTkOrderReturnInfoV2(tiktokData);
        } catch (Exception e) {
            log.error("将tiktok退货数据发送至多渠道异常:{}", e.getMessage());
        }
        //联系客户信息 转发队列
        marSendMessageRuleService.sendTkOrderData(tiktokData);
        //接收TK会话信息
        iTiktokMessageService.saveTiktokStationLetterMessage(tiktokData);
        return ApiResponseResult.buildSuccessResult();
    }


    @ApiOperation(value = "testWebHookV3")
    @PostMapping("/testWebHookV3")
    public ApiResponseResult testWebHookV3(@RequestBody JSONObject tiktokData) {
        factory.getInvokeStrategy(TikTokEnum.TIK_TOK.getEnumType()).receiveReverseOrderFromWebApi(tiktokData, TikTokEnum.TIK_TOK.getEnumType());
        return ApiResponseResult.buildSuccessResult();
    }


    @PostMapping("/testRedis")
    public ApiResponseResult testRedis(@RequestBody JSONObject tiktokData) {
        rabbitTemplate.convertAndSend(MQDefine.TIKTOK_REFUND_EXCHANGE,MQDefine.TIKTOK_REFUND_ROUTING_KEY,tiktokData);
        return ApiResponseResult.buildSuccessResult();
    }
}
