package com.bizark.op.web.controller.api.v2.mar.material;

import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.op.api.dto.material.*;
import com.bizark.op.api.entity.op.mar.material.MarMaterialCharacterImage;
import com.bizark.op.api.entity.op.mar.material.MarMaterialMusics;
import com.bizark.op.api.entity.op.mar.material.MarMaterialVoice;
import com.bizark.op.api.entity.op.mar.vo.CreatifyGenPreviewVideoVO;
import com.bizark.op.api.service.mar.material.MarMaterialCharacterImageService;
import com.bizark.op.api.service.mar.material.MarMaterialMakeInfoService;
import com.bizark.op.api.service.mar.material.MarMaterialMusicsService;
import com.bizark.op.api.service.mar.material.MarMaterialVoiceService;
import com.bizark.op.common.core.page.TableDataInfo;
import com.bizark.op.common.util.ValidateUtil;
import com.bizark.op.web.controller.api.AbstractApiController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * CreatifyAi
 */
@RestController
@RequestMapping("/api/v2/creatify")
public class CreatifyAiController extends AbstractApiController {

    @Autowired
    private MarMaterialCharacterImageService materialCharacterImageService;

    @Autowired
    private MarMaterialMakeInfoService marMaterialMakeInfoService;

    @Autowired
    private MarMaterialVoiceService materialVoiceService;

    @Autowired
    private MarMaterialMusicsService marMaterialMusicsService;

    /**
     * 预览视频
     * @param dto
     * @return
     */
    @PostMapping("/generatePreviewVideo")
    public ApiResponseResult<List<CreatifyGenPreviewVideoVO>> generatePreviewVideo(@RequestBody CreatifyGeneratePreviewVideoDTO dto) {
        ValidateUtil.beanValidate(dto);
        return ApiResponseResult.buildSuccessResult(marMaterialMakeInfoService.generatePreviewVideo(dto));
    }

    /**
     * 保存生成记录
     * @param dto
     * @return
     */
    @PostMapping("/recordGenerateLog")
    public ApiResponseResult recordGenerateLog(@RequestBody CreatifyRecordGenerateDTO dto) {
        ValidateUtil.beanValidate(dto);
        marMaterialMakeInfoService.recordGenerateLog(dto);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * 确认生成
     * @param dto
     * @return
     */
    @PostMapping("/generateVideo")
    public ApiResponseResult generateVideo(@RequestBody CreatifyGeneratePreviewVideoDTO dto) {
        marMaterialMakeInfoService.generateVideo(dto);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * 获取人物形象选择列表
     * @param dto
     * @return
     */
    @GetMapping("/personas/list")
    public TableDataInfo<MarMaterialCharacterImage> personasList(CreatifyPersonasDTO dto){
        ValidateUtil.beanValidate(dto);
        startPage();
        return getDataTable(materialCharacterImageService.personasList(dto));
    }


    /**
     * 获取人物声音选择列表
     *
     * @param dto
     * @return
     */
    @GetMapping("/vioce/list")
    public TableDataInfo<MarMaterialVoice> vioceList(CreatifyVoiceDTO dto) {
        ValidateUtil.beanValidate(dto);
        startPage();
        return getDataTable(materialVoiceService.vioceList(dto));
    }


    /**
     * 获取背景音乐分类选择
     * @return 分类名称数组
     */
    @GetMapping("/music/category")
    public ApiResponseResult<List<String>> musicCategory(){
        List<String> categories = marMaterialMusicsService.selectAllCategory();
        return ApiResponseResult.buildSuccessResult(categories);
    }


    /**
     * 获取背景音乐
     * @param dto
     * @return
     */
    @GetMapping("/music/list")
    public ApiResponseResult<List<MarMaterialMusics>> musicList(CreatifyMusicDTO dto) {
        ValidateUtil.beanValidate(dto);
        return ApiResponseResult.buildSuccessResult(marMaterialMusicsService.musicList(dto));
    }


    @PostMapping("/videoResult")
    public ApiResponseResult videoResult(@RequestBody CreatifyVideoResultDTO dto) {
        marMaterialMakeInfoService.videoResult(dto);
        return ApiResponseResult.buildSuccessResult();
    }
}
