package com.bizark.op.web.controller.api.v2.cuscenter;

import cn.hutool.json.JSONArray;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bizark.op.api.cons.PermDefine;
import com.bizark.op.api.entity.op.refund.OrderRefundRequestInfo;
import com.bizark.op.api.form.customer.CusCenterOrderRefundQuery;
import com.bizark.op.api.service.RpcMarExportService;
import com.bizark.op.api.service.customer.ICusCenterOrderRefundService;
import com.bizark.op.api.service.refund.IOrderRefundRequestInfoService;
import com.bizark.op.api.vo.customer.CusCenterOrderRefundDTO;
import com.bizark.op.api.vo.customer.CusCenterOrderRefundVO;
import com.bizark.op.common.core.page.TableDataInfo;
import com.bizark.op.common.function.page.PageHelperTemplate;
import com.bizark.op.web.controller.api.AbstractApiController;
import com.bizark.framework.web.view.ApiResponseResult;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 客服中心退款信息
 * @Author: Ailill
 * @Date: 2024/4/28 11:15
 */
@RestController("CusCenterOrderRefundController")
@RequestMapping(value = "/api/v2/cusCenter/orderRefund")
public class CusCenterOrderRefundController extends AbstractApiController {

    @Autowired
    private PageHelperTemplate<CusCenterOrderRefundDTO, CusCenterOrderRefundVO> pageHelperTemplate;

    @Autowired
    private ICusCenterOrderRefundService cusCenterOrderRefundService;

    @Autowired
    private IOrderRefundRequestInfoService orderRefundRequestInfoService;



    /**
     * 查询退款信息列表
     *
     * @param query
     * @return
     */
    @GetMapping("/list")
    @RequiresPermissions(PermDefine.MAR_REFUND_INFO_LIST)
    public TableDataInfo<CusCenterOrderRefundVO> list(CusCenterOrderRefundQuery query) {
        if (query.getContextId() == null || query.getContextId() < 1) {
            query.setContextId(getAuthUserEntity().getOrgId());
        }
        return pageHelperTemplate.execute(page -> cusCenterOrderRefundService.selectList(query, page));
    }


    /**
     * 导出退款信息列表
     *
     * @param query
     * @param response
     */
    @GetMapping("/original/export")
    public ApiResponseResult export(CusCenterOrderRefundQuery query, HttpServletResponse response) {
        if (query.getContextId() == null || query.getContextId() < 1) {
            query.setContextId(getAuthUserEntity().getOrgId());
        }
        cusCenterOrderRefundService.originalExportOrderRefund(query, response, getAuthUserEntity());
        return ApiResponseResult.buildSuccessResult("导出成功");
    }

    @GetMapping("/async/export")
    public ApiResponseResult asyncExport(CusCenterOrderRefundQuery query) {
        if (query.getContextId() == null || query.getContextId() < 1) {
            query.setContextId(getAuthUserEntity().getOrgId());
        }
        String result = cusCenterOrderRefundService.asyncExportOrderRefundList(JSON.toJSONString(query), query.getContextId());
        return ApiResponseResult.buildSuccessResult(result);

    }

    @GetMapping("/detail/{orderRefundId}")
    public ApiResponseResult getOrderRefundDetail(@PathVariable Long orderRefundId) {

        return ApiResponseResult.buildSuccessResult(cusCenterOrderRefundService.getOrderRefundDetailInfo(orderRefundId));
    }


    /**
     * 查询退款请求信息列表
     *
     * @param query
     * @return
     */
    @PostMapping("/request/list")
    public TableDataInfo<CusCenterOrderRefundVO> requestList(@RequestBody CusCenterOrderRefundQuery query,@RequestParam("contextId") Integer contextId) {
        query.setContextId(contextId);
        if (query.getContextId() == null || query.getContextId() < 1) {
            query.setContextId(getAuthUserEntity().getOrgId());
        }
        return pageHelperTemplate.execute(page -> orderRefundRequestInfoService.selectList(query, page));
    }


    /**
     * 导出退款请求信息列表
     *
     * @param query
     * @param response
     */
    @PostMapping("/request/export")
    public ApiResponseResult requestExport(@RequestBody CusCenterOrderRefundQuery query, HttpServletResponse response,@RequestParam("contextId") Integer contextId) {
        query.setContextId(contextId);
        orderRefundRequestInfoService.originalExportOrderRequestRefund(query, response, getAuthUserEntity());
        //rpcMarExportService.asyncExportOrderRequestRefundList(JSONObject.toJSONString(query));
        return ApiResponseResult.buildSuccessResult("导出已生成，请至【任务中心】-【任务列表】查看处理进度及下载文件！");
    }


    /**
     * Description: 退款请求信息详情
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/8/22
     */
    @GetMapping("/request/{orderRefundId}")
    public ApiResponseResult getRequestOrderRefundDetail(@PathVariable String orderRefundId) {
        return ApiResponseResult.buildSuccessResult(orderRefundRequestInfoService.getRequestOrderRefundDetailInfo(orderRefundId));
    }


    /**
     * Description: 退款请求信息详情
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/8/22
     */
    @PostMapping("/request/tk/Manual")
    public ApiResponseResult getRequestTkmanual(@RequestBody JSONObject query) {
        orderRefundRequestInfoService.saveTkOrderReturnInfoV2(query);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * Description: 退款请求信息详情
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/8/22
     */
    @GetMapping("/request/tk/Manual/noItems")
    public ApiResponseResult getRequestTkmanual(@RequestParam("returnId") String returnId) {
        orderRefundRequestInfoService.compensationTkRefundNoItems(returnId);
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * Description: 退款请求信息详情
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/8/22
     */
    @PostMapping("/request/tk/Manual/afterSale")
    public ApiResponseResult getRequestTkmanual(@RequestParam(value = "shopId") Long shopId,
                                                @RequestParam(value = "orderNo") String  orderNo) {
        orderRefundRequestInfoService.manualRequestTkReturnRefundInfo(shopId, orderNo);
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * Description: 获取退货信息，退货原因
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/9/27
     */
    @GetMapping("/dict/returnReason")
    public ApiResponseResult getReturnInfoReturnReason(@RequestParam(value = "contextId") Integer contextId) {
        QueryWrapper<OrderRefundRequestInfo> orderRefundRequestInfoQueryWrapper = new QueryWrapper<>();
        orderRefundRequestInfoQueryWrapper.select("DISTINCT  return_reason_text ");
        orderRefundRequestInfoQueryWrapper.eq("organization_id",contextId);
        orderRefundRequestInfoQueryWrapper.and(i -> i.and(j -> j.isNotNull("return_reason_text").or(k -> k.ne("return_reason_text",""))));
        List<OrderRefundRequestInfo> listChild = orderRefundRequestInfoService.list(orderRefundRequestInfoQueryWrapper);
        JSONArray dict = new JSONArray();
        for (OrderRefundRequestInfo o : listChild) {
            cn.hutool.json.JSONObject item = new cn.hutool.json.JSONObject();
            item.put("dictValue", o.getReturnReasonText());
            item.put("dictLabel",  o.getReturnReasonText());
            dict.add(item);
        }
        return ApiResponseResult.buildSuccessResult(dict);
    }

}
