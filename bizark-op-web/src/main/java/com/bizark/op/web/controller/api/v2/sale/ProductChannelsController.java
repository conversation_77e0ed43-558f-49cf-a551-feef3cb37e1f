package com.bizark.op.web.controller.api.v2.sale;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.bizark.common.exception.CommonException;
import com.bizark.common.util.JacksonUtils;
import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.op.api.cons.PermDefine;
import com.bizark.op.api.dto.sale.AsinCountryCheckDTO;
import com.bizark.op.api.dto.sale.ChannelsInventoryConfReq;
import com.bizark.op.api.dto.sale.ProductChannelAllocateTagDTO;
import com.bizark.op.api.dto.tiktok.TiktokApportionDTO;
import com.bizark.op.api.enm.finance.FinanceErrorEnum;
import com.bizark.op.api.enm.sale.SaleStateEnum;
import com.bizark.op.api.enm.sys.SysLabelConfigEnum;
import com.bizark.op.api.entity.op.account.ApprovalDTO;
import com.bizark.op.api.entity.op.sale.ProductChannelApproval;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.entity.op.sale.ProductChannelsExportQuery;
import com.bizark.op.api.entity.op.sale.vo.*;
import com.bizark.op.api.entity.op.wayfair.WayfairInventorySummary;
import com.bizark.op.api.service.account.AccountService;
import com.bizark.op.api.service.sale.ProductChannelApprovalService;
import com.bizark.op.api.service.sale.ProductChannelsDescriptionService;
import com.bizark.op.api.service.sale.ProductChannelsService;
import com.bizark.op.api.service.sale.ProductChannelsSlaveService;
import com.bizark.op.api.service.seller.ISellerSkuPromotionApportionService;
import com.bizark.op.api.service.sys.ISysDictTypeService;
import com.bizark.op.api.service.sys.SysLabelConfigService;
import com.bizark.op.api.service.wayfair.WayfairInventorySummaryService;
import com.bizark.op.common.core.domain.sys.SysDictData;
import com.bizark.op.common.core.page.TableDataInfo;
import com.bizark.op.common.exception.CheckException;
import com.bizark.op.common.util.ValidateUtil;
import com.bizark.op.common.util.spring.SpringUtils;
import com.bizark.op.service.util.ConvertUtils;
import com.bizark.op.service.util.ExcelTemplateUtils;
import com.bizark.op.web.controller.api.AbstractApiController;
import com.bizark.usercenter.api.parameter.role.SellerSkuVisualVo;
import com.bizark.usercenter.api.service.UcPostService;
import com.bizark.usercenter.api.service.UcRoleService;
import com.xxl.conf.core.annotation.XxlConf;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/v2/sale/productChannel")
public class ProductChannelsController extends AbstractApiController {

    @Autowired
    private ProductChannelsDescriptionService productChannelsDescriptionService;


    @Autowired
    private SysLabelConfigService sysLabelConfigService;

    @Autowired
    private ProductChannelsService productChannelsService;

    @Autowired
    private ProductChannelsSlaveService productChannelsSlaveService;

    @Autowired
    private ProductChannelApprovalService productChannelApprovalService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private UcPostService ucPostService;

    @Autowired
    private ISysDictTypeService sysDictTypeService;

    @Autowired
    private WayfairInventorySummaryService wayfairInventorySummaryService;


    @XxlConf(value = "bizark-erp.channel.export.type")
    public static String CHANNEL_EXPORT_TYPE;


    @Autowired
    private UcRoleService ucRoleService;

    /**
     * 分页列表查询
     *
     * @param channels
     * @param contextId
     * @return
     */
    @PostMapping("/list")
    @RequiresPermissions(PermDefine.SKU_MAP_LIST)
    public TableDataInfo list(
            @RequestBody ProductChannels channels,
            @RequestParam("contextId") Integer contextId,
            @RequestParam(value = "sidx", required = false) String sidx
    ) {

        try {
            if (new Integer(1000380).equals(contextId)) {
                // sellerSku数据权限获取
                SellerSkuVisualVo selectSellerSku = ucRoleService.selectSellerSku(contextId, getAuthUser(), PermDefine.PRODUCT_CHANNEL_INFO_MANAGE);
                log.info("用户 {} 组织 {} 获取SKU映射权限数据 {}", getAuthUser().getEmail(), contextId, JacksonUtils.toJson(selectSellerSku));
                if (!selectSellerSku.getIsAll()) {
                    if (CollectionUtil.isNotEmpty(selectSellerSku.getSellerSkuList())) {
                        String sellerSku = channels.getSellerSkus();//判断查询是否传参
                        if (!StringUtils.isEmpty(sellerSku)) {
                            sellerSku += "," + selectSellerSku.getSellerSkuList().stream().collect(Collectors.joining(","));
                        } else {
                            sellerSku = selectSellerSku.getSellerSkuList().stream().collect(Collectors.joining(","));
                        }
                        channels.setSellerSkus(sellerSku);
                    }
                }
            }
        } catch (Exception e) {
            log.info("获取SKU映射权限异常：{}", e);
        }


        productChannelsService.approvalNodeQuerySetting(channels);


        startPage(Objects.equals(sidx, "currency_price") ? "seller_sku_price" : sidx);
        List<ProductChannels> productChannels = productChannelsService.selectProductChannels(channels, contextId);
        return getDataTable(ConvertUtils.dictConvert(productChannels));
    }

    /**
     * 修改或保存SKU映射信息
     *
     * @param channels
     * @param contextId
     * @return
     */
    @PostMapping("/saveOrUpdate")
    @RequiresPermissions(PermDefine.SKU_MAP_MODIFY)
    public ApiResponseResult saveOrUpdate(@RequestBody List<ProductChannels> channels, @RequestParam("confirm") Integer confirm, @RequestParam("contextId") Integer contextId) {

        Map<String, Integer> configMap = sysLabelConfigService.getConfigMapByModule(SysLabelConfigEnum.OPERATE_REQUIRED.getModule(), contextId);

        productChannelsService.checkSaveData(contextId, channels, configMap);

        ChannelImportResultVO vo = productChannelsService.saveOrUpdateProductChannels(channels, confirm, contextId, getAuthUserEntity(), configMap);
//        productChannelsService.saveOrUpdateProductChannels(channels, contextId, getAuthUserEntity(), false);
        return ApiResponseResult.buildSuccessResult(vo);
    }


    /**
     * 保存并提交
     *
     * @return
     */
    @PostMapping("/saveOrUpdateAndCommit")
    @RequiresPermissions(PermDefine.SKU_MAP_MODIFY)
    public ApiResponseResult saveOrUpdateAndCommit(@RequestBody List<ProductChannels> channels, @RequestParam("confirm") Integer confirm, @RequestParam("contextId") Integer contextId) {
        log.info("SKU映射新增 - {}", JSON.toJSONString(channels));

        try {
            Map<String, Integer> configMap = sysLabelConfigService.getConfigMapByModule(SysLabelConfigEnum.OPERATE_REQUIRED.getModule(), contextId);
            productChannelsService.checkSaveData(contextId, channels, configMap);
            channels.forEach(c ->c.setSource(true));
            ChannelImportResultVO vo = productChannelsService.saveOrUpdateProductChannels(channels, confirm, contextId, getAuthUserEntity(),configMap);
            if (!vo.isSuccess()) {
                return ApiResponseResult.buildSuccessResult(vo);
            }
//        List<Integer> productChannelsIds = channels.stream().filter(ProductChannels::isSource).map(ProductChannels::getId).collect(Collectors.toList());
//        productChannelsService.commitApproval(ArrayUtil.toArray(productChannelsIds, Integer.class), contextId);
            return ApiResponseResult.buildSuccessResult(vo);
        } catch (CheckException e) {
            return new ApiResponseResult(ApiResponseResult.API_STATUS.API_STATUS_ERROR, 200, e.getMessage(), e.getData());
        }
    }

    @PostMapping("/updateSaleState")
    @RequiresPermissions(PermDefine.SKU_MAP_MODIFY)
    public ApiResponseResult updateSaleState(@RequestBody ApprovalDTO approvalDTO ,@RequestParam("contextId") Integer contextId){
        productChannelsService.updateSaleState(approvalDTO, contextId);
        return ApiResponseResult.buildSuccessResult();
    }

    @GetMapping("/repairOrgId")
    public ApiResponseResult repairOrgId(){
        productChannelsService.repairOrgId();
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * 更新审批状态
     * @param approvalDTO
     * @param contextId
     * @return
     */
    @RequiresPermissions(PermDefine.SKU_MAP_APPROVAL)
    @PostMapping("/updateApproval")
    public ApiResponseResult updateApproval(@RequestBody ApprovalDTO approvalDTO , @RequestParam("contextId") Integer contextId){
        if (StrUtil.isBlank(approvalDTO.getApprovalStatus())) {
            return ApiResponseResult.buildFailureResult("审批状态不能为空");
        }
        productChannelsService.updateApproval(approvalDTO, contextId);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/checkAsinSameAsSku")
    @RequiresPermissions(PermDefine.SKU_MAP_LIST)
    public ApiResponseResult checkAsinSameAsSku(@RequestParam("ids") Integer[] ids, @RequestParam("contextId") Integer contextId) {
        return ApiResponseResult.buildSuccessResult(productChannelsService.checkAsinSameAsSku(contextId, ids));
    }


    /**
     * 提交审批
     * @param ids
     * @param contextId
     * @return
     */
    @PutMapping("/commitApproval")
    @RequiresPermissions(PermDefine.SKU_MAP_MODIFY)
    public ApiResponseResult commitApproval(@RequestParam("ids") Integer[] ids,@RequestParam("contextId") Integer contextId) {
        productChannelsService.commitApproval(ids, contextId);
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * 获取明细信息
     * @param id
     * @param contextId
     * @return
     */
    @GetMapping("/getById")
    @RequiresPermissions(PermDefine.SKU_MAP_LIST)
    public ApiResponseResult getById(@RequestParam("id") Integer id,@RequestParam("contextId") Integer contextId) {
        ProductChannels channels = productChannelsService.selectProductChannelsById(id, contextId);
        return ApiResponseResult.buildSuccessResult(ConvertUtils.dictConvert(channels));
    }


    /**
     * 删除
     * @param ids
     * @param contextId
     * @return
     */
    @DeleteMapping("/delete")
    @RequiresPermissions(PermDefine.SKU_MAP_DELETE)
    public ApiResponseResult delete(@RequestParam("ids") Integer[] ids, @RequestParam("contextId") Integer contextId) {
        productChannelsService.deleteProductChannels(ids);
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * 导出
     *
     * @param ids
     * @param response
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @PostMapping("/export")
    @RequiresPermissions(PermDefine.SKU_MAP_LIST)
    public ApiResponseResult export(@RequestBody ProductChannelsExportQuery channels,@RequestParam("contextId") Integer contextId, HttpServletResponse response) throws IOException, IllegalAccessException {
        try {
            if (new Integer(1000380).equals(contextId)) {
                // sellerSku数据权限获取
                SellerSkuVisualVo selectSellerSku = ucRoleService.selectSellerSku(contextId, getAuthUser(), PermDefine.PRODUCT_CHANNEL_INFO_MANAGE);
                log.info("用户 {} 组织 {} 获取SKU映射权限数据 {}", getAuthUser().getEmail(), contextId, JacksonUtils.toJson(selectSellerSku));
                if (!selectSellerSku.getIsAll()) {
                    if (CollectionUtil.isNotEmpty(selectSellerSku.getSellerSkuList())) {
                        String sellerSku = channels.getSellerSkus();//判断查询是否传参
                        if (!StringUtils.isEmpty(sellerSku)) {
                            sellerSku += "," + selectSellerSku.getSellerSkuList().stream().collect(Collectors.joining(","));
                        } else {
                            sellerSku = selectSellerSku.getSellerSkuList().stream().collect(Collectors.joining(","));
                        }
                        channels.setSellerSkus(sellerSku);
                    }
                }
            }
        } catch (Exception e) {
            log.info("获取SKU映射权限异常：{}", e);
        }
        // 默认id降序
        String orderBy = channels.getOrderBy();
        if (StrUtil.isNotBlank(orderBy) && orderBy.contains("currencyPrice")) {
            orderBy = orderBy.replace("currencyPrice", "sellerSkuPrice");
            channels.setOrderBy(orderBy);
        }
        if (Objects.equals(CHANNEL_EXPORT_TYPE, "1")) {
            productChannelsService.createExportTask(channels, channels.getIds(), contextId, getAuthUserEntity());
            return ApiResponseResult.buildSuccessResult(FinanceErrorEnum.ASNYC_EXPORT_GENERATED.getName(), FinanceErrorEnum.ASNYC_EXPORT_GENERATED.getName());
        }
        productChannelsService.exportProductChannel(channels, channels.getIds(), contextId, response);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/downloadImportExample1")
    @RequiresPermissions(PermDefine.SKU_MAP_MODIFY)
    public ApiResponseResult downloadImportExample(HttpServletResponse response,@RequestParam("contextId") Integer contextId) throws IOException, IllegalAccessException {
        productChannelsService.downLoadProductChannelsExcel(response, contextId);
        return ApiResponseResult.buildSuccessResult();
    }





    @RequiresPermissions(PermDefine.SKU_MAP_ASSIGN)
    @PostMapping("/allotOperation")
    public ApiResponseResult allotOperation(@RequestBody ApprovalDTO dto,@RequestParam("contextId") Integer contextId){
        productChannelsService.allotOperation(dto);
        return ApiResponseResult.buildSuccessResult();
    }


    @PostMapping("/import")
    @RequiresPermissions(PermDefine.SKU_MAP_MODIFY)
    public ApiResponseResult importProductChannels(
            MultipartFile file,
            @RequestParam("contextId") Integer contextId,
            @RequestParam("confirm") Integer confirm
    ) throws NoSuchFieldException, InvocationTargetException, NoSuchMethodException, IllegalAccessException, IOException {
        try {
            ChannelImportResultVO vo = productChannelsService.importProductChannels(file, confirm, contextId, getAuthUserEntity());
            return ApiResponseResult.buildSuccessResult(vo);
        } catch (CheckException e) {
            return new ApiResponseResult(ApiResponseResult.API_STATUS.API_STATUS_ERROR, 200, e.getMessage(), e.getData());
        }
    }

    @RequiresPermissions(PermDefine.SKU_MAP_UPDATE_ASIN)
    @PostMapping("/updateAsinInfo")
    public ApiResponseResult updateAsinInfo(@RequestBody List<Integer> ids, @RequestParam("contextId") Integer contextId) {
        productChannelsService.updateAsinInfo(ids);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/selectApprovalLog")
    @RequiresPermissions(PermDefine.SKU_MAP_LIST)
    public ApiResponseResult selectApprovalLog(@RequestParam Integer productChannelsId) {
        List<ProductChannelApproval> approvalLog = productChannelApprovalService.getApprovalLog(productChannelsId);
        return ApiResponseResult.buildSuccessResult(approvalLog);
    }


    @GetMapping("/syncData")
    public ApiResponseResult syncProductChannels(@RequestParam("ids") Integer[] ids, @RequestParam("contextId") Integer contextId) {
        productChannelsService.syncData(ids, contextId);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/fixChannelsQuantity")
    public ApiResponseResult fixChannelsQuantity(@RequestParam Integer contextId) {
        productChannelsService.fixChannelsQuantity(contextId);
        return ApiResponseResult.buildSuccessResult();
    }


    @PutMapping("/approvalWithdraw")
    @RequiresPermissions(PermDefine.SKU_MAP_QUIT)
    public ApiResponseResult approvalWithdraw(@RequestParam("contextId") Integer contextId, @RequestParam("ids") Integer[] ids) {
        productChannelsService.approvalWithdraw(contextId, ids);
        return ApiResponseResult.buildSuccessResult();
    }



    @GetMapping("/downloadImportExample")
    @RequiresPermissions(PermDefine.SKU_MAP_MODIFY)
    public void downModel(@RequestParam(value = "contextId") Integer contextId, HttpServletResponse response) {
        List<String[]> downData = productChannelsService.buildString(contextId);
        int[] downRows = {2, 3, 4, 5, 13, 15};
//        int[] downRows = {};
        int[] dateCells = {};
        String[] title = {
                "SellerSku*",
                "SellerSku货号",
                "店铺名称*",
                "销售状态*",
                "审核状态",
                "产品类型*",
                "SKU",
                "数量",
                "Item Label",
                "Asin",
                "父Asin",
                "品牌",
                "标签",
                "是否VINE",
                "运营*",
                "是否推送库存",
                "推送比例(%)",
                "Handing time"
        };

        //标题注释
        List<SysDictData> dictData = sysDictTypeService.selectDictDataByType("product_channel_tag");
        String tags = CollectionUtil.isEmpty(dictData) ? "" : dictData.stream().map(SysDictData::getDictLabel).collect(Collectors.joining(","));

        Map<Integer, String> titleText = new HashMap<>();
        titleText.put(6, "如需填写多个SKU，则用英文逗号隔开，表格中SKU后面的数量也需对应用英文逗号隔开，例如 SKU一栏填写”SKUA,SKUB“，数量那一栏则需填写 ”2,3“这个形式。");
        titleText.put(12, "标签如需要设置多个,请用英文逗号隔开,目前可供导入的标签有:" + tags);

        try {
            ExcelTemplateUtils.createExcelTemplateAndText(this.getClass().getClassLoader().getResource("").getPath() + "templates/product_channel/product_channel.xls",
                    title, titleText, downData, downRows, dateCells, response);
        } catch (Exception e) {
            e.printStackTrace();
            throw new CommonException("示例下载失败");
        }


    }


    @GetMapping("/selectOpSelector")
    @RequiresPermissions(PermDefine.SKU_MAP_LIST)
    public ApiResponseResult selectOpSelector(@RequestParam("contextId") Integer contextId) {
        return ApiResponseResult.buildSuccessResult(ucPostService.getOpUserPostSelector(contextId));
    }


    @GetMapping("/queryAmazonBySellerSku")
    @RequiresPermissions(PermDefine.SKU_MAP_LIST)
    public ApiResponseResult queryAmazonBySellerSku(@RequestParam("flag") String flag, @RequestParam("sellerSku") String sellerSku, @RequestParam("contextId") Integer contextId) {
        AccountsSellerResponse sellerResponse = productChannelsService.queryAmazonBySellerSku(flag, sellerSku, contextId);
        return Objects.nonNull(sellerResponse) ? ApiResponseResult.buildSuccessResult(sellerResponse)
                : ApiResponseResult.buildFailureResult("该店铺下SellerSKU不存在，请检查填写是否正确");
    }


    @GetMapping("/inventory")
    @RequiresPermissions(PermDefine.SKU_MAP_MODIFYSTOCK)
    public ApiResponseResult inventoryInfo(@RequestParam("ids") Integer[] ids,@RequestParam("contextId") Integer contextId) {

        List<ProductInventory> inventories = null;
        try {
            inventories = productChannelsService.selectInVentory(ids, contextId);
        } catch (Exception e) {
            if (e instanceof CheckException) {
                return new ApiResponseResult(ApiResponseResult.API_STATUS.API_STATUS_ERROR, 200, e.getMessage(), ((CheckException) e).getData());
            }
            return ApiResponseResult.buildFailureResult(e.getMessage());
        }
        return ApiResponseResult.buildSuccessResult(inventories);
    }


    @PostMapping("/syncInventory")
    public ApiResponseResult syncInventory(@RequestParam("ids") Integer[] ids) {
        productChannelsService.syncInventory(ids);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/updateAllAsin")
    public ApiResponseResult updateAllAsin(){
        List<ProductChannels> channels = productChannelsService.lambdaQuery()
                .in(ProductChannels::getSaleChannel, Arrays.asList("amazon", "amazonvendords", "amazonvendor", "VC-DI"))
                .eq(ProductChannels::getSellStatus, SaleStateEnum.HIT_SHELVE.getValue())
                .eq(ProductChannels::getOrgId, 1000049)
                .list();
        if (CollectionUtil.isEmpty(channels)) {
            log.error("UPDATE_ASIN: 未获取到SKU映射数据，任务执行终止！");
            return ApiResponseResult.buildFailureResult("UPDATE_ASIN: 未获取到SKU映射数据，任务执行终止！");
        }
        long start = System.currentTimeMillis();
        log.error("UPDATE_ASIN: 开始执行,条目：{}", channels.size());
        productChannelsService.updateProductChannelsInfoNoTransactional(channels, true, 5, TimeUnit.SECONDS);
        long end = System.currentTimeMillis();
        log.info("UPDATE_ASIN:执行完毕，耗时：{}", end - start);
        return ApiResponseResult.buildSuccessResult("UPDATE_ASIN:执行完毕，耗时：{}", end - start);
    }

    @GetMapping("/checkErpByAsin")
    @RequiresPermissions(PermDefine.SKU_MAP_LIST)
    public ApiResponseResult checkErpByAsin(AsinCountryCheckDTO dto) {
        ValidateUtil.beanValidate(dto);

        Map<String, Integer> configMap = sysLabelConfigService.getConfigMapByModule(SysLabelConfigEnum.OPERATE_REQUIRED.getModule(), dto.getContextId());
        Integer status = configMap.get(SysLabelConfigEnum.ASIN_DATA_APPLICATION.getLabelConfig());
        if (Objects.equals(status, 1)) {
            ProductChannels channels = productChannelsService.asinCountryCheck(dto);
            return channels == null ? ApiResponseResult.buildSuccessResult(new HashMap<>())
                    : ApiResponseResult.buildSuccessResult(channels);
        }
        return ApiResponseResult.buildSuccessResult(new HashMap<>());

    }



    @GetMapping("/repairProduct")
    public ApiResponseResult repairProduct(){
        productChannelsService.repairProduct();
        return ApiResponseResult.buildSuccessResult();
    }



    @GetMapping("/selectSkuInventory")
    @RequiresPermissions(PermDefine.SKU_MAP_LIST)
    public ApiResponseResult selectSkuInventory(Long id) {
        List<ErpSkuStockVO> vos = productChannelsService.selectSkuInventory(id);
        return ApiResponseResult.buildSuccessResult(vos);
    }



    @PostMapping("/updateInventoryPushConf")
    @RequiresPermissions(PermDefine.SKU_MAP_AUTOUPDATESTOCK)
    public ApiResponseResult updateInventoryPushConf(@RequestBody ChannelsInventoryConfReq req){
        ValidateUtil.beanValidate(req);
        productChannelsService.updateInventoryPushConf(req);
        return ApiResponseResult.buildSuccessResult();

    }


    @GetMapping("/selectTemuChannel")
    @RequiresPermissions(PermDefine.SKU_MAP_LIST)
    public ApiResponseResult selectTemuChannel(@RequestParam("asin") String asin, @RequestParam("accountId") Integer accountId) {
        ProductChannels channels = productChannelsService.selectTemuChannel(asin, accountId);
        return ApiResponseResult.buildSuccessResult(channels);
    }



    @GetMapping("/initChannelsPush")
    public ApiResponseResult initChannelsPush(Integer orgId){
        productChannelsService.initChannelsPush(orgId);
        return ApiResponseResult.buildSuccessResult();
    }

    @PostMapping("/tiktok")
    public ApiResponseResult tiktok(@RequestBody TiktokApportionDTO apportionDTO){
        SpringUtils.getBean(ISellerSkuPromotionApportionService.class).saveTiktokApportionDaily(apportionDTO);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/syncWamartWfsInventory")
    public ApiResponseResult syncWamartWfsInventory(@RequestParam(required = false) Integer accountId) {
        productChannelsService.syncWalmartWfsInventory(accountId);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/initProductChannelsSlave")
    public ApiResponseResult initProductChannelsSlave(@RequestParam(value = "sellerSku",required = false) String sellerSku){
        productChannelsSlaveService.init(sellerSku);
        return ApiResponseResult.buildSuccessResult();
    }
    @PostMapping("/initProductChannelsSlaveBySellerSkus")
    public ApiResponseResult initProductChannelsSlaveBySellerSkus(@RequestBody List<String> sellerSkus){
        for (String sellerSku : sellerSkus) {
            initProductChannelsSlave(sellerSku);
        }
        return ApiResponseResult.buildSuccessResult();
    }
    @GetMapping("/initSlaveSkuInventory")
    public ApiResponseResult initSlaveSkuInventory(@RequestParam(value = "sellerSku",required = false) String sellerSku){
        productChannelsSlaveService.initSlaveSkuInventory(sellerSku);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/selectSkuInventoryById")
    @RequiresPermissions(PermDefine.SKU_MAP_LIST)
    public ApiResponseResult selectSkuInventofryById(@RequestParam("id") Integer id) {
        return ApiResponseResult.buildSuccessResult(productChannelsService.selectSkuInventoryById(id));
    }

    @GetMapping("/initApprovalNodeByCondition")
    public ApiResponseResult initApprovalNodeByCondition(
            @RequestParam("orgId") Integer orgId,
            @RequestParam(value = "sellStatus", required = false) String sellerStatus,
            @RequestParam(value = "saleChannel", required = false) String saleChannel
    ) {
        productChannelsService.initApprovalNodeByCondition(orgId, saleChannel, sellerStatus);
        return ApiResponseResult.buildSuccessResult();
    }
    @GetMapping("/initApprovalNodeByChannelId")
    public ApiResponseResult initApprovalNodeByChannelId(@RequestParam("channelId") Integer channelId){
        productChannelsService.initApprovalNodeByChannelId(channelId);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/repairSlave")
    public ApiResponseResult repairSlave(){
        productChannelsService.repairSlave();
        return ApiResponseResult.buildSuccessResult();
    }


    @PostMapping("/allocateTags")
    public ApiResponseResult allocateTags(@RequestBody ProductChannelAllocateTagDTO dto) {
        ValidateUtil.beanValidate(dto);
        productChannelsService.allocateTags(dto);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/selectBrandsByOrgId")
    public ApiResponseResult selectBrandsByOrgId(@RequestParam("contextId") Integer contextId) {
        return ApiResponseResult.buildSuccessResult(productChannelsService.selectBrandsByOrgId(contextId));
    }

    @GetMapping("/initMyDepotSellerSku")
    public ApiResponseResult initMyDepotSellerSku(@RequestParam(value = "SellerSku",required = false) String sellerSku){
        productChannelsService.initMyDepotSellerSku(sellerSku);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/syncWalmarwtWfsInventory")
    public ApiResponseResult syncWalmarwtWfsInventory(@RequestParam("accountId") Integer accountId) {
        productChannelsService.syncWalmartWfsInventory(accountId);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/getWayfairInventorySummary/{id}")
    public ApiResponseResult getWayfairInventorySummary(@PathVariable("id") Integer id) {
        WayfairInventorySummary summary = wayfairInventorySummaryService.getWayfairInventorySummaryByProductChannelId(id);
        return ApiResponseResult.buildSuccessResult(summary);
    }


    @GetMapping("/selectInventoryByErpSku")
    public ApiResponseResult selectInventoryByErpSku(@RequestParam("contextId") Integer contextId, @RequestParam("erpSkus") List<String> erpSkus) {
        List<ErpSkuStockVO> vos = productChannelsService.selectInventoryByErpSku(contextId, erpSkus);
        return ApiResponseResult.buildSuccessResult(vos);
    }


    @GetMapping("/getDescription/{asin1}")
    public ApiResponseResult<ProductChannelsDescriptionVO> getDescription(@PathVariable("asin1") String asin1) {
        ProductChannelsDescriptionVO descriptionVO = productChannelsDescriptionService.selectDescriptionByProductId(asin1);
        return ApiResponseResult.buildSuccessResult(descriptionVO);
    }



    @GetMapping("/initCostPrice")
    public ApiResponseResult initCostPrice(){
        productChannelsService.initCostPrice();
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/resetInventoryStatus")
    public ApiResponseResult resetInventoryStatus(){
        return ApiResponseResult.buildSuccessResult(productChannelsService.resetInventoryStatus());
    }

    @GetMapping("/initSkuVersionSerial")
    public ApiResponseResult resetInventoryStatus(@RequestParam("orgId") Integer orgId) {
        productChannelsService.initSkuVersionSerial(orgId);
        return ApiResponseResult.buildSuccessResult();
    }
}
