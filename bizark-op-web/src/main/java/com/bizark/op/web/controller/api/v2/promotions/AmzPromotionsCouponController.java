package com.bizark.op.web.controller.api.v2.promotions;


import com.bizark.common.contract.AuthUserDetails;
import com.bizark.op.api.annotation.ClusterFailFastLock;
import com.bizark.op.api.cons.PermDefine;
import com.bizark.op.api.entity.op.promotions.AmzPromotionsCoupon;
import com.bizark.op.api.service.promotions.IAmzPromotionsCouponService;
import com.bizark.op.common.core.page.TableDataInfo;
import com.bizark.op.common.util.DateUtil;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.web.controller.api.AbstractApiController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.bizark.framework.web.view.ApiResponseResult;

import java.util.List;


/**
 * 优惠券管理Controller
 *
 * <AUTHOR>
 * @date 2023-06-13
 */
@RestController
@RequestMapping("/api/v2/promotions/coupon")
public class AmzPromotionsCouponController extends AbstractApiController {
    @Autowired
    private IAmzPromotionsCouponService amzPromotionsCouponService;

    /**
     * 查询优惠券管理列表
     */
    @RequiresPermissions(PermDefine.PROMOTION_ACTIVITY_LIST)
    @GetMapping("/list")
    public TableDataInfo list(AmzPromotionsCoupon amzPromotionsCoupon, @RequestParam(name = "contextId") Integer contextId) {
        amzPromotionsCoupon.setOrganizationId(contextId);
        startPage();
        List<AmzPromotionsCoupon> list = amzPromotionsCouponService.selectAmzPromotionsCouponList(amzPromotionsCoupon);
        return getDataTable(list);
    }

    /**
     * 获取优惠券管理详细信息
     */
//    @RequiresPermissions(PermDefine.PROMOTION_ACTIVITY_QUERY)
    @GetMapping(value = "/{id}")
    public ApiResponseResult getInfo(@PathVariable("id") Long id) {
        return ApiResponseResult.buildSuccessResult(amzPromotionsCouponService.selectAmzPromotionsCouponDetailById(id));
    }


    /**
     * 取消优惠券
     *
     * @param id          优惠券Id
     * @param couponName  优惠券名称
     * @param createdName 操作者
     * @return
     */
    @GetMapping("/cancelCoupon")
    @ClusterFailFastLock("'AmzPromotionsCouponController:cancel:'+#id")
//    @RequiresPermissions(PermDefine.PROMOTION_ACTIVITYCOUPON_CANCEL)
    public ApiResponseResult cancelCoupon(@RequestParam("id") Long id, @RequestParam(value = "couponName",required = false) String couponName, @RequestParam(value = "createdName",required = false) String createdName,@RequestParam(name = "contextId") Integer contextId,@RequestParam(value = "remark",required = false)String remark) {

        if (StringUtil.isEmpty(createdName)) {
            createdName = getAuthUserEntity().getName();
        }
        Integer updatedBy = getAuthUserEntity().getUpdatedBy();
        int result = amzPromotionsCouponService.cancelCouponById(id, createdName, couponName,contextId,updatedBy,remark);
        if (result > 0) {
            return ApiResponseResult.buildSuccessResult();
        } else {
            return ApiResponseResult.buildFailureResult("提交失败");
        }
    }


    /**
     * SC渠道优惠券状态为提交异常，支持直接提交。
     *
     * @param id          优惠券id
     * @param createdName 操作者
     * @return
     */
    @GetMapping("/submitException")
    @ClusterFailFastLock("'AmzPromotionsCouponController:submitException:'+#id")
//    @RequiresPermissions(PermDefine.PROMOTION_ACTIVITY_SUBMIT)
    public ApiResponseResult submitExceptionSubmit(@RequestParam("id") Long id, @RequestParam(value = "createdName",required = false) String createdName,@RequestParam(name = "contextId") Integer contextId) {

        if (StringUtil.isEmpty(createdName)) {
            createdName = getAuthUserEntity().getName();
        }
        Integer updatedBy = getAuthUserEntity().getId();
        int result = amzPromotionsCouponService.submitExceptionSubmit(id, createdName,contextId,updatedBy);
        if (result > 0) {
            return ApiResponseResult.buildSuccessResult();
        } else {
            return ApiResponseResult.buildFailureResult("提交失败");
        }
    }

    /**
     * SC渠道优惠券状态为Running、修改异常.   修改优惠券
     *
     * @param id
     * @param createdName
     * @return
     */
    @GetMapping("/runningOrModifyExceptionUpdate")
    @ClusterFailFastLock("'AmzPromotionsCouponController:running:'+#id")
//    @RequiresPermissions(PermDefine.PROMOTION_ACTIVITY_MODIFY)
    public ApiResponseResult runningOrModifyExceptionUpdate(@RequestParam("id") Long id,
                                                            @RequestParam(value = "createdName",required = false) String createdName,
                                                            @RequestParam(value = "budget",required = false) String budget,
                                                            @RequestParam(value = "beginDate",required = false) String beginDate,
                                                            @RequestParam(value = "endDate",required = false) String endDate,
                                                            @RequestParam(name = "contextId") Integer contextId) {

        AuthUserDetails thisUser = getAuthUser();
        if (StringUtil.isEmpty(createdName)) {
            createdName = thisUser.getName();
        }
        if (id == null || StringUtil.isEmpty(createdName) || StringUtil.isEmpty(budget) || StringUtil.isEmpty(beginDate)
                || StringUtil.isEmpty(endDate)) {
            return ApiResponseResult.buildFailureResult("请填写必填信息");
        }
        if (DateUtil.compareDate(beginDate, endDate) > 0) {
            return ApiResponseResult.buildFailureResult("开始日期不能大于结束日期");
        }
        Integer updatedBy = thisUser.getId();
        amzPromotionsCouponService.runningOrModifyExceptionUpdate(id, createdName, budget, endDate,contextId,updatedBy);
        return ApiResponseResult.buildSuccessResult();
    }


    @GetMapping("/modifyRemark")
    public ApiResponseResult modifyRemark(@RequestParam("id") Long id, @RequestParam(value = "remark") String remark) {
        amzPromotionsCouponService.modifyRemark(id, remark);
        return ApiResponseResult.buildSuccessResult();
    }
}
