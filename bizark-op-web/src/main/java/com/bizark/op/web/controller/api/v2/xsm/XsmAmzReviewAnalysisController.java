package com.bizark.op.web.controller.api.v2.xsm;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.op.api.entity.op.xsm.*;
import com.bizark.op.api.service.xms.MarAmzReviewAnalyLabelService;
import com.bizark.op.api.service.xms.MarAmzReviewReportItemService;
import com.bizark.op.api.service.xms.MarAmzReviewReportService;
import com.bizark.op.api.service.xms.XsmReviewCommonService;
import com.bizark.op.common.core.page.PageInfoDomain;
import com.bizark.op.common.core.page.TableDataInfo;
import com.bizark.op.common.core.page.TableSupportInfo;
import com.bizark.op.common.util.ExcelUtils;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.web.controller.api.AbstractApiController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v2/xsm/analysis")
@Slf4j
public class XsmAmzReviewAnalysisController extends AbstractApiController {

    @Autowired
    private XsmReviewCommonService xsmReviewService;

    @Autowired
    private MarAmzReviewReportService marAmzReviewReportService;

    @Autowired
    private MarAmzReviewAnalyLabelService marAmzReviewAnalyLabelService;

    @Autowired
    private MarAmzReviewReportItemService marAmzReviewReportItemService;

    /**
     * @desc 亚马逊评论分析-搜索生成报告
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @PostMapping("/generate/report")
    public ApiResponseResult generateReport(@RequestParam("contextId") Long contextId, @RequestBody MarAmzReviewReportQuery query) {
        query.setOrganizationId(contextId);
        xsmReviewService.generateReport(query);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * @desc 亚马逊评论分析-搜索生成报告
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @PostMapping("/list/generate/report")
    public ApiResponseResult listGenerateReport(@RequestParam("contextId") Long contextId, @RequestBody MarAmzReviewReportQuery query) {
        query.setOrganizationId(contextId);
        xsmReviewService.listGenerateReport(query);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * @desc 亚马逊评论分析-删除报表明细
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @DeleteMapping("/delete/report/detail")
    public ApiResponseResult deleteReportDetail(@RequestParam("contextId") Long organizationId,@RequestBody List<MarAmzReviewReportItem> paramList) {
        paramList.stream().forEach(i -> i.setOrganizationId(organizationId));
        marAmzReviewReportItemService.deleteReportDetail(paramList);
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * @desc 亚马逊评论分析-增加报表明细
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @PostMapping("/add/report/detail")
    public ApiResponseResult addReportDetail(@RequestParam("contextId") Long organizationId,@RequestBody MarAmzReviewReportQuery query) {
        query.setOrganizationId(organizationId);
        query.getMarAmzReviewReportItemList().stream().forEach(i -> i.setOrganizationId(organizationId));
        marAmzReviewReportItemService.addReportDetail(query);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * @desc 亚马逊评论分析-修改报表名称
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @PutMapping("/update/report/name")
    public ApiResponseResult updateReportName(@RequestParam("contextId") Long contextId, @RequestBody MarAmzReviewReportQuery query) {
        query.setOrganizationId(contextId);
        xsmReviewService.updateReportName(query);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * @desc 亚马逊评论分析-删除报告
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @DeleteMapping("/delete/report/{id}")
    public ApiResponseResult deleteReport(@RequestParam("contextId") Long contextId,@PathVariable Long id) {
        xsmReviewService.deleteReport(id);
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * @desc 亚马逊评论分析-修改标签
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @PutMapping("/update/report/label")
    public ApiResponseResult updateReportLabel(@RequestParam("contextId") Long contextId,@RequestBody MarAmzReviewReportLabelVo marAmzReviewReportLabelVo) {
        marAmzReviewReportLabelVo.setOrganizationId(contextId);
        xsmReviewService.updateReportLabel(marAmzReviewReportLabelVo);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * @desc 亚马逊评论分析-移除标签
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @PutMapping("/remove/report/label")
    public ApiResponseResult removeReportLabel(@RequestParam("contextId") Long contextId,@RequestBody MarAmzReviewReportLabelVo marAmzReviewReportLabelVo) {
        marAmzReviewReportLabelVo.setOrganizationId(contextId);
        xsmReviewService.removeReportLabel(marAmzReviewReportLabelVo);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * @desc 亚马逊评论分析-报告查询列表
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @PostMapping("/report/list")
    public TableDataInfo reportList(@RequestParam("contextId") Long contextId, @RequestBody MarAmzReviewReportParam query) {
        startPage();
        query.setOrganizationId(contextId);
        List<MarAmzReviewReportVo> marAmzReviewReportVoList = marAmzReviewReportService.reportList(query);
        return getDataTable(marAmzReviewReportVoList);
    }

    /**
     * @desc 亚马逊评论分析-根据报表id查询
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @GetMapping("/report/{id}")
    public ApiResponseResult reportList(@PathVariable("id") Long id) {
        MarAmzReviewReportVo marAmzReviewReportVo = marAmzReviewReportService.reportById(id);
        return ApiResponseResult.buildSuccessResult(marAmzReviewReportVo);
    }

    /**
     * @desc 亚马逊评论分析-当月报告数
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @GetMapping("/report/this/month/report/num")
    public ApiResponseResult thisMonthReportNum(@RequestParam("contextId") Long contextId) {
        Integer num = marAmzReviewReportService.thisMonthReportNum(contextId);
        return ApiResponseResult.buildSuccessResult(num);
    }


    /**
     * @desc 亚马逊评论分析-添加标签
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @PostMapping("/label/add")
    public ApiResponseResult labelAdd(@RequestParam("contextId") Long contextId, @RequestBody MarAmzReviewAnalyLabel marAmzReviewAnalyLabel) {
        marAmzReviewAnalyLabel.setOrganizationId(contextId);
        marAmzReviewAnalyLabel.settingDefaultCreate();
        try {
            marAmzReviewAnalyLabelService.save(marAmzReviewAnalyLabel);
        } catch (Exception e) {
            log.error("保存异常{}",e.getMessage());
            throw new RuntimeException("创建失败");
        }
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * @desc 亚马逊评论分析-修改标签
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @PutMapping("/label/update")
    public ApiResponseResult labelUpdate(@RequestParam("contextId") Long contextId, @RequestBody MarAmzReviewAnalyLabel marAmzReviewAnalyLabel) {
        QueryWrapper<MarAmzReviewAnalyLabel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("organization_id", contextId);
        queryWrapper.eq("tag_name", marAmzReviewAnalyLabel.getTagName());
        queryWrapper.ne("id", marAmzReviewAnalyLabel.getId());
        List<MarAmzReviewAnalyLabel> marAmzReviewAnalyLabelList = marAmzReviewAnalyLabelService.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(marAmzReviewAnalyLabelList)) {
            throw new RuntimeException("该标签已存在");
        }
        marAmzReviewAnalyLabel.setOrganizationId(contextId);
        marAmzReviewAnalyLabel.settingDefaultUpdate();
        marAmzReviewAnalyLabelService.updateById(marAmzReviewAnalyLabel);
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * @desc 亚马逊评论分析-删除标签
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @DeleteMapping("/label/delete/{id}")
    public ApiResponseResult labelDelete(@RequestParam("contextId") Long contextId, @PathVariable Long id) {
        marAmzReviewAnalyLabelService.labelDelete(id);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * @desc 亚马逊评论分析-标签列表带分页
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @PostMapping("/label/list")
    public TableDataInfo labelList(@RequestParam("contextId") Long contextId) {
        startPage();
        QueryWrapper<MarAmzReviewAnalyLabel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("organization_id",contextId);
        queryWrapper.orderByDesc("created_at");
        List<MarAmzReviewAnalyLabel> result = marAmzReviewAnalyLabelService.list(queryWrapper);
        return getDataTable(result);
    }

    /**
     * @desc 亚马逊评论分析-标签列表下拉框
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @PostMapping("/label/select")
    public ApiResponseResult labelSelect(@RequestParam("contextId") Long contextId, @RequestBody MarAmzReviewAnalyLabel query) {
        query.setOrganizationId(contextId);
        QueryWrapper<MarAmzReviewAnalyLabel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("organization_id",contextId);
        queryWrapper.orderByDesc("created_at");
        queryWrapper.like(StringUtils.isNotEmpty(query.getTagName()),"tag_name",query.getTagName());
        List<MarAmzReviewAnalyLabel> result = marAmzReviewAnalyLabelService.list(queryWrapper);
        return ApiResponseResult.buildSuccessResult(result);
    }



    /**
     * @desc asin 文件上传
     * <AUTHOR>
     * @date 2025/6/23 15:35
     * param
     * @param file
     * @param contextId
     * return
     * @return com.bizark.framework.web.view.ApiResponseResult
     */
    @PostMapping("/import")
    public ApiResponseResult importExcel(MultipartFile file, Integer contextId){
        List<XsmReviewCommonAnalysisAsin> asinList = ExcelUtils.excelImportFilterAnnotation(file, XsmReviewCommonAnalysisAsin.class);
        if(CollectionUtil.isNotEmpty(asinList)) {
            //最多填10个,文件检测ASIN填写必须是10位长度的数字+字母的字符，否则报错提示“请输入正确的ASIN
            if(asinList.size() > 10) {
                throw new RuntimeException("最多填10个ASIN");
            }
            Pattern ASIN_PATTERN = Pattern.compile("^[A-Za-z0-9]{10}$");
            for(XsmReviewCommonAnalysisAsin xsmReviewCommonAnalysisAsin : asinList) {
                if (xsmReviewCommonAnalysisAsin.getASIN() == null || xsmReviewCommonAnalysisAsin.getASIN().isEmpty()) {
                    throw new IllegalArgumentException("ASIN不能为空");
                }
                if (!ASIN_PATTERN.matcher(xsmReviewCommonAnalysisAsin.getASIN()).matches()) {
                    throw new IllegalArgumentException("请输入正确的ASIN：必须是10位长度的数字+字母组合");
                }
            }
           return ApiResponseResult.buildSuccessResult(asinList.stream().map(i -> i.getASIN()).collect(Collectors.joining(",")));
       } else {
           return ApiResponseResult.buildSuccessResult(null);
       }
    }


    /**
     * @desc 亚马逊评论分析-报表明细
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @GetMapping("/report/detail/list")
    public ApiResponseResult reportDetailList(@RequestParam("contextId") Long organizationId,@RequestParam("id") Long id) {
        List<MarAmzReviewReportItem> result = xsmReviewService.reportDetailList(organizationId,id);
        return ApiResponseResult.buildSuccessResult(result);
    }

    /**
     * @desc 亚马逊评论分析详情-消费者洞察-筛选框
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @GetMapping("/topic/filter")
    public ApiResponseResult topicFilter(@RequestParam("contextId") Long contextId,XsmReviewCommonAnalysisQuery query) {
        query.setOrganizationId(contextId);
        return ApiResponseResult.buildSuccessResult(xsmReviewService.topicFilter(query));
    }

    /**
     * @desc 亚马逊评论分析详情-消费者洞察
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @PostMapping("/list")
    public ApiResponseResult list(@RequestParam("contextId") Long contextId, @RequestBody XsmReviewCommonAnalysisQuery query) {
        return ApiResponseResult.buildSuccessResult(xsmReviewService.xsmAmzReviewAnalysis(query));
    }


    /**
     * @desc 亚马逊评论分析-竟品分析
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @PostMapping("/compete/list")
    public ApiResponseResult competeList(@RequestParam("contextId") Long contextId, @RequestBody XsmReviewCommonAnalysisQuery query) {
        return ApiResponseResult.buildSuccessResult(xsmReviewService.competeList(query));
    }

    /**
     * @desc 亚马逊评论分析-竟品分析 重新选择报表列表
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @PostMapping("/compete/renew/select/reportList")
    public TableDataInfo renewSelectReportList(@RequestParam("contextId") Long contextId,@RequestParam("reportId")Long reportId) {
        startPage();
        List<RenewSelectReportVo> renewSelectReportVos = xsmReviewService.renewSelectReportList(contextId,reportId);
        return getDataTable(renewSelectReportVos);
    }

    /**
     * @desc 亚马逊评论分析-亚马逊评论分析
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @PostMapping("/review/list")
    public ApiResponseResult reviewList(@RequestParam("contextId") Long contextId, @RequestBody XsmReviewCommonAnalysisQuery query) {
        query.setOrganizationId(contextId);
        PageInfoDomain pageInfoDomain = TableSupportInfo.buildPageRequest();
        query.setPage( pageInfoDomain.getPage());
        query.setRows(pageInfoDomain.getRows());
        return ApiResponseResult.buildSuccessResult(xsmReviewService.reviewList(query));
    }


    /**
     * @desc 亚马逊评论分析-亚马逊评论分析 导出
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @PostMapping("/review/list/export")
    public ApiResponseResult reviewListExport(@RequestParam("contextId") Long contextId, @RequestBody XsmReviewCommonAnalysisQuery query) {
        query.setOrganizationId(contextId);
        xsmReviewService.reviewListExport(query);
        return ApiResponseResult.buildSuccessResult("导出已生成，请至【任务中心】-【任务列表】查看处理进度及下载文件！");
    }


    /**
     * @desc 亚马逊评论分析-根据type获取竟品数据
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @PostMapping("/query/competeInfo")
    public ApiResponseResult queryCompeteInfo(@RequestParam("contextId") Long contextId, @RequestBody XsmReviewCommonQueryCompeteInfoQuery query) {
        query.setOrganizationId(contextId);
        PageInfoDomain pageInfoDomain = TableSupportInfo.buildPageRequest();
        query.setPage( pageInfoDomain.getPage());
        query.setRows(pageInfoDomain.getRows());
        JSONObject jsonObject = xsmReviewService.queryCompeteInfo(query);
        return ApiResponseResult.buildSuccessResult(jsonObject);
    }

    /**
     * @desc 亚马逊评论分析详情-已分析评论数
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @PostMapping("/reviews/sum")
    public ApiResponseResult reviewsSum(@RequestParam("contextId") Long contextId, @RequestBody XsmReviewCommonAnalysisQuery query) {
        return ApiResponseResult.buildSuccessResult(xsmReviewService.reviewsSum(query));
    }

    /**
     * @desc 亚马逊评论分析详情-已分析评论数
     * <AUTHOR>
     * @date 2025/6/19 18:46
     * param
     * return
     */
    @GetMapping("/test")
    public ApiResponseResult test() {
        marAmzReviewReportService.syncAmzReviewReport();
        return ApiResponseResult.buildSuccessResult();
    }


}
