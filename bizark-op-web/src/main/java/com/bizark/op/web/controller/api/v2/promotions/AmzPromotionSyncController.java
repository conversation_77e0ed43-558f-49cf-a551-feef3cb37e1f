package com.bizark.op.web.controller.api.v2.promotions;

import com.bizark.op.api.annotation.ClusterFailFastLock;
import com.bizark.op.api.cons.PermDefine;
import com.bizark.op.api.entity.op.promotions.AmzPromotionSyncRequest;
import com.bizark.op.api.entity.op.promotions.AmzPromotions;
import com.bizark.op.api.entity.op.promotions.AmzPromotionsCoupons;
import com.bizark.op.api.request.msg.sync.AmzPromotionsSyncCouponsListReceiveMsg;
import com.bizark.op.api.service.promotions.IAmzPromotionAndCouponService;
import com.bizark.op.common.annotation.RepeatSubmit;
import com.bizark.op.web.controller.api.AbstractApiController;
import com.bizark.framework.web.view.ApiResponseResult;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @remarks:手动同步coupon,promotion
 * @Author: Ailill
 * @Date: 2023/11/13 17:09
 */
@RestController
@RequestMapping("/api/v2/promotions/sync")
public class AmzPromotionSyncController extends AbstractApiController {

    @Autowired
    private IAmzPromotionAndCouponService iAmzPromotionAndCouponService;

    /**
     * 获取待同步的coupon
     *
     * @param amzPromotionSyncRequest
     * @return
     */
    @GetMapping("/get/coupon")
    public ApiResponseResult getSyncCouponList(AmzPromotionSyncRequest amzPromotionSyncRequest) {

        return ApiResponseResult.buildSuccessResult(iAmzPromotionAndCouponService.getSyncCouponList(amzPromotionSyncRequest.getIds()));
    }

    /**
     * 获取待同步的promotion
     *
     * @param amzPromotionSyncRequest
     * @return
     */
    @GetMapping("/get/promotion")
    public ApiResponseResult getSyncPromotionList(AmzPromotionSyncRequest amzPromotionSyncRequest) {

        return ApiResponseResult.buildSuccessResult(iAmzPromotionAndCouponService.getSyncPromotions(amzPromotionSyncRequest.getIds()));
    }

    /**
     * 同步coupon
     *
     * @param amzPromotionsCoupons
     * @param contextId
     * @return
     */
    @PostMapping("/coupon")
    @ClusterFailFastLock("'AmzPromotionSyncController:coupon:'+#contextId")
//    @RequiresPermissions(PermDefine.PROMOTION_ACTIVITY_SYNC)
    public ApiResponseResult syncCoupon(@RequestBody List<AmzPromotionsCoupons> amzPromotionsCoupons, @RequestParam("contextId") Integer contextId) {

        return ApiResponseResult.buildSuccessResult(iAmzPromotionAndCouponService.syncCouponsList(amzPromotionsCoupons, getAuthUserEntity().getName()));
    }

    /**
     * 同步promotion
     *
     * @param amzPromotions
     * @return
     */
    @PostMapping("/promotion")
    @ClusterFailFastLock("'AmzPromotionSyncController:promotion:'+#contextId")
//    @RequiresPermissions(PermDefine.PROMOTION_ACTIVITY_SYNC)
    public ApiResponseResult syncPromotion(@RequestBody List<AmzPromotions> amzPromotions, @RequestParam("contextId") Integer contextId) {

        return ApiResponseResult.buildSuccessResult(iAmzPromotionAndCouponService.syncPromotions(amzPromotions,getAuthUserEntity().getName()));
    }


    /**
     * 抓取promotion
     * @param amzPromotions
     * @param contextId
     * @return
     */
    @PostMapping("/capture/promotion")
    @RepeatSubmit
    public ApiResponseResult capturePromotion(@RequestBody List<AmzPromotions> amzPromotions, @RequestParam("contextId") Integer contextId) {

        iAmzPromotionAndCouponService.capturePromotion(amzPromotions, contextId, getAuthUserEntity());
        return ApiResponseResult.buildSuccessResult("正在抓取请稍后查看结果");
    }


    /**
     * 抓取promotion修改
     * @param amzPromotions
     * @param contextId
     * @return
     */
    @PostMapping("/captureModify/promotion")
    @RepeatSubmit
    public ApiResponseResult captureModifyPromotion(@RequestBody List<AmzPromotions> amzPromotions, @RequestParam("contextId") Integer contextId) {

        amzPromotions.forEach(t->t.setOrganizationId(contextId));
        iAmzPromotionAndCouponService.captureModifyPromotion(amzPromotions, contextId, getAuthUserEntity());
        return ApiResponseResult.buildSuccessResult("已修改正在抓取请稍后查看结果");
    }



    /**
     * 抓取coupon
     * @param coupons
     * @param contextId
     * @return
     */
    @PostMapping("/capture/coupon")
    @RepeatSubmit
    public ApiResponseResult captureCoupon(@RequestBody List<AmzPromotionsCoupons> coupons, @RequestParam("contextId") Integer contextId) {

        iAmzPromotionAndCouponService.captureCoupon(coupons, contextId, getAuthUser());
        return ApiResponseResult.buildSuccessResult("正在抓取请稍后查看结果");
    }


    /**
     * 抓取coupon修改
     * @param coupons
     * @param contextId
     * @return
     */
    @PostMapping("/captureModify/coupon")
    @RepeatSubmit
    public ApiResponseResult captureModifyCoupon(@RequestBody List<AmzPromotionsCoupons> coupons, @RequestParam("contextId") Integer contextId) {

        coupons.forEach(t -> t.setOrganizationId(contextId));
        iAmzPromotionAndCouponService.captureModifyCoupon(coupons, contextId, getAuthUser());
        return ApiResponseResult.buildSuccessResult("已修改正在抓取请稍后查看结果");
    }

    @PostMapping("/testCouponCapture")
    public void testCouponCapture(@RequestBody AmzPromotionsSyncCouponsListReceiveMsg amzCoupons) {
        iAmzPromotionAndCouponService.couponSyncMsgResolve(amzCoupons);
    }
}
