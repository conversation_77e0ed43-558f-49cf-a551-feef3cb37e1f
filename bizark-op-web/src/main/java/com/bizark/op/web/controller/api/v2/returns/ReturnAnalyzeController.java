package com.bizark.op.web.controller.api.v2.returns;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bizark.op.api.cons.PermDefine;
import com.bizark.op.api.cons.StatConstant;
import com.bizark.op.api.entity.op.returns.StatReturnQuery;
import com.bizark.op.api.entity.op.returns.VO.ReturnAnalyzeLineVO;
import com.bizark.op.api.entity.op.returns.VO.ReturnAnalyzeVO;
import com.bizark.op.api.entity.op.returns.walmart.ReturnOrder;
import com.bizark.op.api.entity.op.returns.walmart.SaleChannel;
import com.bizark.op.api.entity.op.ticket.ScTicket;
import com.bizark.op.api.exception.ErpCommonException;
import com.bizark.op.api.service.RpcMarExportService;
import com.bizark.op.api.service.amazon.AmazonReturnService;
import com.bizark.op.api.service.returns.ReturnAnalyzeService;
import com.bizark.op.api.vo.mar.AdAnalysisVo;
import com.bizark.op.common.core.page.TableDataInfo;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.common.util.poi.ExcelUtil;
import com.bizark.op.service.util.ExcelExport;
import com.bizark.op.web.controller.api.AbstractApiController;
import com.bizark.framework.web.view.ApiResponseResult;
import com.github.pagehelper.PageInfo;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023年9月19日  13:49
 * @description: 退货分析controller
 */

@RestController("returnAnalyzeController")
@RequestMapping(value = "/api/v2/return/analyze")
public class ReturnAnalyzeController extends AbstractApiController {

    @Autowired
    private ReturnAnalyzeService returnAnalyzeService;


    @Autowired
    AmazonReturnService amazonReturnService;

    @Autowired
    private RpcMarExportService rpcMarExportService;

    /**
     * @param
     * @param contextId
     * @param statReturnQuery
     * @description: VC 退货分析列表
     * @author: Moore
     * @date: 2024/4/10 11:47
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @GetMapping("getVcReturnAnalyzePageList")
//    @RequiresPermissions(PermDefine.MAR_ANALYSISLIST)
    public TableDataInfo getVcReturnAnalyzePageList(@RequestParam(value = "contextId") Long contextId, StatReturnQuery statReturnQuery) {
        if (statReturnQuery.getReturnDateStart() == null || statReturnQuery.getReturnDateEnd() == null) {
            throw new CustomException("查询时间不能为空");
        }
        startPage();
        statReturnQuery.setOrgId(contextId);
        List<ReturnAnalyzeVO> returnAnalyzeVOS = returnAnalyzeService.getVcReturnAnalyzePageListNew(statReturnQuery);
        return getDataTable(returnAnalyzeVOS);
    }


    /**
     * VC 退货分析列表-导出
     */
    @GetMapping("/vc/analyzePageList/export")
    @RequiresPermissions(PermDefine.MAR_ANALYSISEXPORT)
    public ApiResponseResult export( HttpServletResponse response, @RequestParam(value = "contextId") Long contextId, StatReturnQuery statReturnQuery) {
        statReturnQuery.setOrgId(contextId);
        List<ReturnAnalyzeVO> returnAnalyzeVOSExport = returnAnalyzeService.getVcReturnAnalyzePageListNewExport(statReturnQuery);
        ExcelUtil<ReturnAnalyzeVO> util = new ExcelUtil<>(ReturnAnalyzeVO.class);
        try {
            return util.exportExcel(returnAnalyzeVOSExport, "VC退货分析", URLEncoder.encode("VC退货分析", "UTF-8"), response);
        } catch (Exception e) {
            throw new ErpCommonException(String.format("VC退货分析导出失败：{}",e));
        }
    }


    /**
     * @param
     * @param contextId
     * @param statReturnQuery
     * @description: VC 退货分析 汇总行
     * @author: Moore
     * @date: 2024/4/10 11:47
     * @return:
     **/
    @GetMapping("getVcReturnAnalyzePageListSum")
    @RequiresPermissions(PermDefine.MAR_ANALYSISLIST)
    public ApiResponseResult getVcReturnAnalyzePageListSum(@RequestParam(value = "contextId") Long contextId, StatReturnQuery statReturnQuery) {
        if (statReturnQuery.getReturnDateStart() == null || statReturnQuery.getReturnDateEnd() == null) {
            throw new CustomException("查询时间不能为空");
        }
        statReturnQuery.setOrgId(contextId);
        ReturnAnalyzeVO returnAnalyzeVOS = returnAnalyzeService.getVcReturnAnalyzePageSum(statReturnQuery);
        return ApiResponseResult.buildSuccessResult(returnAnalyzeVOS);
    }


    /**
     * @param
     * @param contextId
     * @param statReturnQuery
     * @description: VC 退货分析 趋势图
     * @author: Moore
     * @date: 2024/4/10 11:47
     * @return:
     **/
    @GetMapping("getVcReturnAnalyzeTendency")
    @RequiresPermissions(PermDefine.MAR_ANALYSISLIST)
    public ApiResponseResult getVcReturnAnalyzeTendency(
            @RequestParam(value = "contextId") Long contextId, StatReturnQuery statReturnQuery) {
        if (statReturnQuery.getReturnDateStart() == null || statReturnQuery.getReturnDateEnd() == null) {
            throw new CustomException("查询时间不能为空");
        }
        statReturnQuery.setOrgId(contextId);
        return ApiResponseResult.buildSuccessResult(returnAnalyzeService.getVcReturnAnalyzePageTendency(statReturnQuery));
    }


    /**
     * @param
     * @param response
     * @param contextId
     * @param statReturnQuery
     * @description: 导出VC 退货分析趋势图
     * @author: Moore
     * @date: 2024/4/15 18:43
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @GetMapping("/vc/tendency/export")
    @RequiresPermissions(PermDefine.MAR_ANALYSISEXPORT)
    public ApiResponseResult tendencyExport(HttpServletResponse response, @RequestParam(value = "contextId") Long contextId, StatReturnQuery statReturnQuery) {
        statReturnQuery.setOrgId(contextId);
        List<ReturnAnalyzeVO> returnAnalyzeVOS = returnAnalyzeService.getVcReturnAnalyzePageTendencyExport(statReturnQuery);
        ExcelUtil<ReturnAnalyzeVO> util = new ExcelUtil<>(ReturnAnalyzeVO.class);
        try {
            return util.exportExcel(returnAnalyzeVOS, "VC退货分析趋势信息", URLEncoder.encode("VC退货分析趋势信息", "UTF-8"), response);
        } catch (Exception e) {
            throw new ErpCommonException(String.format("VC退货分析趋势信息,%s", e.getMessage()));
        }
    }




    /**
     * 销售分析 B2C
     *
     * @param contextId
     * @param pageSize
     * @param pageNum
     * @param type            分析的维度 0：asin 1: sku 2: 店铺  3: 渠道 4: 退货原因 默认 0
     * @param shopIdList
     * @param returnDateStart
     * @param returnDateEnd
     * @param orderDateStart
     * @param orderDateEnd
     * @param asinList
     * @param skuList
     * @param operateIdList
     * @param channelList
     * @param problemIdList
     * @return
     */
    @GetMapping("getB2CReturnAnalyzePageList")
    public TableDataInfo getB2CReturnAnalyzePageList(
            @RequestParam(value = "contextId") Integer contextId,
            @RequestParam(value = "pageSize", defaultValue = "20", required = false) Integer pageSize,
            @RequestParam(value = "pageNum", defaultValue = "1", required = false) Integer pageNum,
            @RequestParam(value = "type", defaultValue = "0", required = false) Integer type,
            @RequestParam(value = "shopIdList", required = false) List<Integer> shopIdList,
            @RequestParam(value = "returnDateStart", required = false) String returnDateStart,
            @RequestParam(value = "returnDateEnd", required = false) String returnDateEnd,
            @RequestParam(value = "orderDateStart", required = false) String orderDateStart,
            @RequestParam(value = "orderDateEnd", required = false) String orderDateEnd,
            @RequestParam(value = "asinList", required = false) List<String> asinList,
            @RequestParam(value = "skuList", required = false) List<String> skuList,
            @RequestParam(value = "operateIdList", required = false) List<Integer> operateIdList,
            @RequestParam(value = "channelList", required = false) List<String> channelList,
            @RequestParam(value = "problemIdList", required = false) List<Integer> problemIdList
    ) {
        startPage();
        return getDataTable(returnAnalyzeService.getB2CReturnAnalyzePageList(contextId, pageSize, pageNum, type, shopIdList, returnDateStart, returnDateEnd, orderDateStart, orderDateEnd, asinList, skuList, operateIdList, channelList, problemIdList));
    }

    /**
     * 退货分析 B2C excel导出
     *
     * @param contextId
     * @param type
     * @param shopIdList
     * @param returnDateStart
     * @param returnDateEnd
     * @param orderDateStart
     * @param orderDateEnd
     * @param asinList
     * @param skuList
     * @param operateIdList
     * @param channelList
     * @param problemIdList
     * @param httpServletResponse
     * @throws IllegalAccessException
     * @throws UnsupportedEncodingException
     */
    @GetMapping("returnAnalyzeB2CExcelExport")
    @RequiresPermissions(PermDefine.MAR_ANALYSISEXPORT)
    public void returnAnalyzeB2CExcelExport(
            @RequestParam(value = "contextId") Integer contextId,
            @RequestParam(value = "type", defaultValue = "0", required = false) Integer type,
            @RequestParam(value = "shopIdList", required = false) List<Integer> shopIdList,
            @RequestParam(value = "returnDateStart", required = false) String returnDateStart,
            @RequestParam(value = "returnDateEnd", required = false) String returnDateEnd,
            @RequestParam(value = "orderDateStart", required = false) String orderDateStart,
            @RequestParam(value = "orderDateEnd", required = false) String orderDateEnd,
            @RequestParam(value = "asinList", required = false) List<String> asinList,
            @RequestParam(value = "skuList", required = false) List<String> skuList,
            @RequestParam(value = "operateIdList", required = false) List<Integer> operateIdList,
            @RequestParam(value = "channelList", required = false) List<String> channelList,
            @RequestParam(value = "problemIdList", required = false) List<Integer> problemIdList,
            HttpServletResponse httpServletResponse) throws IllegalAccessException, UnsupportedEncodingException {
        ArrayList<Map<String, Object>> rows = returnAnalyzeService.returnAnalyzeB2CExcelExport(contextId, type, shopIdList, returnDateStart, returnDateEnd, orderDateStart, orderDateEnd, asinList, skuList, operateIdList, channelList, problemIdList);
        String fileName = URLEncoder.encode("退货分析B2C", "UTF-8").replaceAll("\\+", "%20");
        ExcelExport.exportAndDownload(rows, httpServletResponse, fileName + ".xls");
    }


    /**
     * Description: 退货状态下拉框
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/3/20
     */
    @GetMapping("/getReturnStatus/dict")
    public ApiResponseResult getReturnStatusDict(@RequestParam(value = "contextId") Integer contextId, @RequestParam(value = "returnStatus") String returnStatus) {
        return ApiResponseResult.buildSuccessResult(returnAnalyzeService.getReturnStatusDict(contextId, returnStatus));
    }

    /**
     * Description: 同步tk退货信息
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/3/20
     */
    @GetMapping("/sync/tk/returnInfo")
    public ApiResponseResult syncTkReturnInfo(@RequestParam(value = "contextId") Integer contextId, @RequestParam(value = "shopId") Integer shopId, @RequestParam(required = false, value = "startTime") Long startTime, @RequestParam(required = false, value = "endTime") Long endTime) {
        returnAnalyzeService.syncTkReturnInfo(contextId, shopId, startTime, endTime);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * Description: 同步tk退货信息
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/3/20
     */
    @GetMapping("/sync/walmart/returnInfo")
    public ApiResponseResult syncWalmartReturnInfo(@RequestParam(value = "messageContext") String messageContext,@RequestParam(required = false, value = "orgId") Long orgId, @RequestParam(required = false, value = "channelFlag") String channelFlag) {
        JSONArray jsonArray = (JSONArray)JSON.parseObject(messageContext).get("returnOrders");
        List<ReturnOrder> returnOrderList = JSONArray.parseArray(jsonArray.toJSONString(),ReturnOrder.class);
        if (CollectionUtil.isNotEmpty(returnOrderList)) {
            SaleChannel saleChannel = new SaleChannel();
            saleChannel.setORG_ID(new BigDecimal(orgId));
            saleChannel.setFLAG(channelFlag);
            returnOrderList.stream().forEach(i -> i.setSaleChannel(saleChannel));
            amazonReturnService.insertWalmartReturn(returnOrderList);
        }
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * Description: 退货分析B2C折线图
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/15
     */
    @GetMapping("/getB2CReturnAnalyzeLineChart")
    @RequiresPermissions(PermDefine.MAR_ANALYSISLIST)
    public ApiResponseResult getB2CReturnAnalyzeLineChart(@RequestParam(value = "contextId") Long contextId,StatReturnQuery statReturnQuery) {
        statReturnQuery.setOrgId(contextId);
        if((StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType()) || StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType()))){
//            if(StringUtil.isNotEmpty(statReturnQuery.getReturnDateStart())) {
//                if(cn.hutool.core.date.DateUtil.betweenDay(cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateStart()), cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateEnd()), false)>60) {
//                    throw new CustomException("按天或周查询退货日期查询日维度不可大于60天");
//                }
//            }else {
//                if(cn.hutool.core.date.DateUtil.betweenDay(cn.hutool.core.date.DateUtil.parse(statReturnQuery.getOrderDateStart()), cn.hutool.core.date.DateUtil.parse(statReturnQuery.getOrderDateEnd()), false)>60) {
//                    throw new CustomException("按天或周查询退货日期查询日维度不可大于60天");
//                }
//            }
        }else {
            if(StringUtil.isNotEmpty(statReturnQuery.getReturnDateStart())) {
                if(cn.hutool.core.date.DateUtil.betweenDay(cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateStart()), cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateEnd()), false)>730) {
                    throw new CustomException("按月查询退货日期查询日维度不可大于2年");
                }
            }else {
                if(cn.hutool.core.date.DateUtil.betweenDay(cn.hutool.core.date.DateUtil.parse(statReturnQuery.getOrderDateStart()), cn.hutool.core.date.DateUtil.parse(statReturnQuery.getOrderDateEnd()), false)>730) {
                    throw new CustomException("按月查询退货日期查询日维度不可大于2年");
                }
            }
        }
        JSONObject jsonObject = returnAnalyzeService.getB2CReturnAnalyzeLineChart(statReturnQuery);
        return ApiResponseResult.buildSuccessResult(jsonObject);
    }


    /**
     * Description: 退货分析B2C列表
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/17
     */
    @GetMapping("/new/getB2CReturnAnalyzePageList")
    @RequiresPermissions(PermDefine.MAR_ANALYSISLIST)
    public TableDataInfo getB2CReturnAnalyzePageListNew(@RequestParam(value = "contextId") Long contextId, StatReturnQuery statReturnQuery) {
        startPage();
        statReturnQuery.setOrgId(contextId);
        if((StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType()) || StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType()))){
//            if(StringUtil.isNotEmpty(statReturnQuery.getReturnDateStart())) {
//                if(cn.hutool.core.date.DateUtil.betweenDay(cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateStart()), cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateEnd()), false)>60) {
//                    throw new CustomException("按天或周查询退货日期查询日维度不可大于60天");
//                }
//            }else {
//                if(cn.hutool.core.date.DateUtil.betweenDay(cn.hutool.core.date.DateUtil.parse(statReturnQuery.getOrderDateStart()), cn.hutool.core.date.DateUtil.parse(statReturnQuery.getOrderDateEnd()), false)>60) {
//                    throw new CustomException("按天或周查询退货日期查询日维度不可大于60天");
//                }
//            }
        }else {
            if(StringUtil.isNotEmpty(statReturnQuery.getReturnDateStart())) {
                if(cn.hutool.core.date.DateUtil.betweenDay(cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateStart()), cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateEnd()), false)>730) {
                    throw new CustomException("按月查询退货日期查询日维度不可大于2年");
                }
            }else {
                if(cn.hutool.core.date.DateUtil.betweenDay(cn.hutool.core.date.DateUtil.parse(statReturnQuery.getOrderDateStart()), cn.hutool.core.date.DateUtil.parse(statReturnQuery.getOrderDateEnd()), false)>730) {
                    throw new CustomException("按月查询退货日期查询日维度不可大于2年");
                }
            }
        }
        List<ReturnAnalyzeVO> returnAnalyzeVOS = returnAnalyzeService.getB2CReturnAnalyzePageList(statReturnQuery);
        return getDataTable(returnAnalyzeVOS);
    }

    /**
     * Description: 退货分析B2C列表详情
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/17
     */
    @GetMapping("/new/getB2CReturnAnalyzePageList/detail/list")
    public ApiResponseResult getB2CReturnAnalyzePageListDetailNew(@RequestParam(value = "contextId") Long contextId, StatReturnQuery statReturnQuery) {
        statReturnQuery.setOrgId(contextId);
        if((StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType()) || StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType()))){
//            if(StringUtil.isNotEmpty(statReturnQuery.getReturnDateStart())) {
//                if(cn.hutool.core.date.DateUtil.betweenDay(cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateStart()), cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateEnd()), false)>60) {
//                    throw new CustomException("按天或周查询退货日期查询日维度不可大于60天");
//                }
//            }else {
//                if(cn.hutool.core.date.DateUtil.betweenDay(cn.hutool.core.date.DateUtil.parse(statReturnQuery.getOrderDateStart()), cn.hutool.core.date.DateUtil.parse(statReturnQuery.getOrderDateEnd()), false)>60) {
//                    throw new CustomException("按天或周查询退货日期查询日维度不可大于60天");
//                }
//            }
        }else {
            if(StringUtil.isNotEmpty(statReturnQuery.getReturnDateStart())) {
                if(cn.hutool.core.date.DateUtil.betweenDay(cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateStart()), cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateEnd()), false)>730) {
                    throw new CustomException("按月查询退货日期查询日维度不可大于2年");
                }
            }else {
                if(cn.hutool.core.date.DateUtil.betweenDay(cn.hutool.core.date.DateUtil.parse(statReturnQuery.getOrderDateStart()), cn.hutool.core.date.DateUtil.parse(statReturnQuery.getOrderDateEnd()), false)>730) {
                    throw new CustomException("按月查询退货日期查询日维度不可大于2年");
                }
            }
        }
        List<ReturnAnalyzeVO> returnAnalyzeVOS = returnAnalyzeService.getB2CReturnAnalyzePageListDetailNew(statReturnQuery);
        return ApiResponseResult.buildSuccessResult(returnAnalyzeVOS);
    }

    /**
     * Description: 退货分析B2C列表详情
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/17
     */
    @GetMapping("new/getB2CReturnAnalyzePage/sum")
    @RequiresPermissions(PermDefine.MAR_ANALYSISLIST)
    public ApiResponseResult getB2CReturnAnalyzePageListSum(@RequestParam(value = "contextId") Long contextId, StatReturnQuery statReturnQuery) {
        statReturnQuery.setOrgId(contextId);
        if((StatConstant.STAT_DATA_TYPE_DAY.equals(statReturnQuery.getDateType()) || StatConstant.STAT_DATA_TYPE_WEEK.equals(statReturnQuery.getDateType()))){
//            if(StringUtil.isNotEmpty(statReturnQuery.getReturnDateStart())) {
//                if(cn.hutool.core.date.DateUtil.betweenDay(cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateStart()), cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateEnd()), false)>60) {
//                    throw new CustomException("按天或周查询退货日期查询日维度不可大于60天");
//                }
//            }else {
//                if(cn.hutool.core.date.DateUtil.betweenDay(cn.hutool.core.date.DateUtil.parse(statReturnQuery.getOrderDateStart()), cn.hutool.core.date.DateUtil.parse(statReturnQuery.getOrderDateEnd()), false)>60) {
//                    throw new CustomException("按天或周查询退货日期查询日维度不可大于60天");
//                }
//            }
        }else {
            if(StringUtil.isNotEmpty(statReturnQuery.getReturnDateStart())) {
                if(cn.hutool.core.date.DateUtil.betweenDay(cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateStart()), cn.hutool.core.date.DateUtil.parse(statReturnQuery.getReturnDateEnd()), false)>730) {
                    throw new CustomException("按月查询退货日期查询日维度不可大于2年");
                }
            }else {
                if(cn.hutool.core.date.DateUtil.betweenDay(cn.hutool.core.date.DateUtil.parse(statReturnQuery.getOrderDateStart()), cn.hutool.core.date.DateUtil.parse(statReturnQuery.getOrderDateEnd()), false)>730) {
                    throw new CustomException("按月查询退货日期查询日维度不可大于2年");
                }
            }
        }
        ReturnAnalyzeVO returnAnalyzeVO = returnAnalyzeService.getB2CReturnAnalyzePageListSum(statReturnQuery);
        return ApiResponseResult.buildSuccessResult(returnAnalyzeVO);
    }



    /**
     * Description: 退货分析B2C折线图导出
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/15
     */
    @GetMapping("/getB2CReturnAnalyzeLineChart/export")
    @RequiresPermissions(PermDefine.MAR_ANALYSISEXPORT)
    public ApiResponseResult getB2CReturnAnalyzeLineChart(@RequestParam(value = "contextId") Long contextId,StatReturnQuery statReturnQuery,HttpServletResponse response) {
        statReturnQuery.setOrgId(contextId);
        JSONObject jsonObject = returnAnalyzeService.getB2CReturnAnalyzeLineChart(statReturnQuery);
        JSONObject data = jsonObject.getJSONObject("data");
        if(data == null) {
            throw new RuntimeException("查询数据出的数据为空");
        }
        JSONArray jsonArrayRate =data.getJSONArray("returnRateList");
        JSONArray jsonArrayX = jsonObject.getJSONArray("x");
        List<ReturnAnalyzeLineVO> returnAnalyzeLineVOList = new ArrayList<>();
        for(int i = 0; i <jsonArrayX.size(); i++) {
            ReturnAnalyzeLineVO returnAnalyzeLineVO = new ReturnAnalyzeLineVO();
            returnAnalyzeLineVO.setReturnDate(jsonArrayX.getString(i));
            returnAnalyzeLineVO.setReturnRate(jsonArrayRate.getBigDecimal(i));
            returnAnalyzeLineVOList.add(returnAnalyzeLineVO);
        }
        ExcelUtil<ReturnAnalyzeLineVO> util = new ExcelUtil<>(ReturnAnalyzeLineVO.class);
        return util.exportExcel(returnAnalyzeLineVOList, "退货分析B2C折线图", "退货分析B2C折线图", response);
    }

    /**
     * Description: 退货分析B2C详情任务导出
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/4/15
     */
    @GetMapping("/new/getB2CReturnAnalyzePageList/detail/export")
    @RequiresPermissions(PermDefine.MAR_ANALYSISEXPORT)
    public ApiResponseResult getB2CReturnAnalyzePageListDetailNewExport(@RequestParam(value = "contextId") Long contextId,StatReturnQuery statReturnQuery,HttpServletResponse response) {
        statReturnQuery.setOrgId(contextId);
        returnAnalyzeService.listExportB2CListDetail(response, statReturnQuery, getAuthUserEntity());
        //rpcMarExportService.listExportB2CListDetailExport(JSONObject.toJSONString(statReturnQuery));
        return ApiResponseResult.buildSuccessResult("导出成功");
    }


}
