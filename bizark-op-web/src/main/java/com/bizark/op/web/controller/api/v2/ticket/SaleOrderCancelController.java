package com.bizark.op.web.controller.api.v2.ticket;

import com.bizark.boss.api.entity.dashboard.deliverorder.TrackingChangeOrderEntity;
import com.bizark.common.contract.AuthUserDetails;
import com.bizark.op.api.entity.op.order.SaleOrderCancel;
import com.bizark.op.api.entity.op.order.vo.SaleOrderCancelInterceptReceive;
import com.bizark.op.api.entity.op.returns.VO.TiktokReturnRefundPlanVO;
import com.bizark.op.api.service.amazon.AmazonReturnService;
import com.bizark.op.api.service.order.SaleOrderCancelService;
import com.bizark.op.common.annotation.RepeatSubmit;
import com.bizark.op.web.controller.api.AbstractApiController;
import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.websocket.server.PathParam;

/**
 * <AUTHOR> @ClassName SaleOrderCancelController
 * @description: 订单取消
 * @date 2023年10月27日
 */
@RequestMapping("/api/v2/crm/saleOrderCancel")
@RestController
public class SaleOrderCancelController  extends AbstractApiController {


    @Autowired
    private SaleOrderCancelService saleOrderCancelService;

    @Autowired
    private AmazonReturnService amazonReturnService;





    /**
     * @description: 手动生成拦截
     * @author: Moore
     * @date: 2023/10/28 11:16
     * @param
     * @param ticketId
     * @return:
    **/
    @GetMapping("/intercept/shiping/order")
    public ApiResponseResult interceptCannelSaleOrder(@RequestParam(value = "contextId") Integer contextId,@PathParam("ticketId") Long ticketId) {
        saleOrderCancelService.generateInterceptOrder(ticketId);
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * @param
     * @param ticketId
     * @description: TIKTOK同意取消接口
     * @author: Moore
     * @date: 2023/10/27 18:11
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @PostMapping("/tiktok/consent")
    @RepeatSubmit
    public ApiResponseResult oderCannelConsent(@RequestParam(value = "contextId") Integer contextId,@RequestParam("ticketId") Long ticketId) {
        saleOrderCancelService.tiktokOrderCannelConsent(ticketId);
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * @param cancelType 0 拒绝取消 1 同意取消
     * @param ticketId
     * @description: TIKTOK同意取消接口
     * @author: Moore
     * @date: 2023/10/27 18:11
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @PostMapping("/common/cancel/consent/reject")
    @RepeatSubmit
    public ApiResponseResult commonoderCannelConsent(@RequestParam(value = "contextId") Integer contextId,
                                                     @RequestParam("ticketId") Long ticketId,
                                                      @RequestParam("cancelType") Integer cancelType
    ) {
        return ApiResponseResult.buildSuccessResult(saleOrderCancelService.orderCannelCommonConsent(ticketId, cancelType));
    }



    /**
     * @description: 获取拒绝原因
     * @author: Moore
     * @date: 2023/10/28 11:17
     * @param
     * @param ticketId
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @GetMapping("/tiktok/reject/reason")
    public ApiResponseResult getRejectReason(@RequestParam(value = "contextId") Integer contextId,@RequestParam("ticketId") Long ticketId) {
        return ApiResponseResult.buildSuccessResult(saleOrderCancelService.getRejectReason(ticketId));
    }


    /**
     * @description: TIKTOK 拒绝取消接口
     * @author: Moore
     * @date: 2023/10/28 11:17
     * @param
     * @param ticketId
     * @return: com.bizark.framework.web.view.ApiResponseResult
    **/
    @PostMapping("/tiktok/reject")
    @RepeatSubmit
    public ApiResponseResult interceptCannelSaleOrderConsent(@RequestParam(value = "contextId") Integer contextId,
                                                             @RequestParam("ticketId") Long ticketId,
                                                             @RequestBody SaleOrderCancel saleOrderCancel) {
        saleOrderCancelService.tiktokOrderCannelConsentReject(ticketId, saleOrderCancel);
        return ApiResponseResult.buildSuccessResult();
    }




    /**
     * @description: 调用生成物流异动单
     * @author: Moore
     * @date: 2023/11/1 15:49
     * @param
     * @param trackingChangeOrderEntity
     * @return: com.bizark.framework.web.view.ApiResponseResult
    **/
    @PostMapping("/rpc/TrackingChange/save")
    public ApiResponseResult saveOne(@RequestParam(value = "contextId") Integer contextId,@RequestParam("ticketId") Long ticketId,@RequestBody TrackingChangeOrderEntity trackingChangeOrderEntity) {
        trackingChangeOrderEntity.setTrackingNo(trackingChangeOrderEntity.getTrackingNo().trim());
        saleOrderCancelService.saveTrackingChangeByRpc(ticketId, getAuthUserEntity(), trackingChangeOrderEntity);
        return ApiResponseResult.buildSuccessResult("successful operation");
    }


    /**
     * @description: 调用生成物流异动单
     * @author: Moore
     * @date: 2023/11/1 15:49
     * @param
     * @param
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @GetMapping("/TrackingChange/order")
    public ApiResponseResult saveOne(@RequestParam(value = "contextId") Integer contextId,@RequestParam("ticketId") Long ticketId) {
        return ApiResponseResult.buildSuccessResult(saleOrderCancelService.getTrackingChangetOrders(ticketId));
    }


    /**
     * @param
     * @param
     * @description: 获取物流异动单号
     * @author: Moore
     * @date: 2023/11/1 15:49
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @PostMapping("/createSaleReturnPlan")
    @RepeatSubmit
    public ApiResponseResult saveOne(@RequestParam(value = "contextId") Integer contextId,@RequestParam(value = "ticketId") Long ticketId ,@RequestBody @Valid TiktokReturnRefundPlanVO tiktokReturnRefundPlanVO) {
        amazonReturnService.requestRpcReturnPlanRequest(contextId,ticketId,tiktokReturnRefundPlanVO,getAuthUserEntity());
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * 查询退货信息(用于申请退货计划单)
     * @param ticketId
     * @return
     */
    @GetMapping("/getTiktokRefundInfo/{ticketId}")
    public ApiResponseResult getTiktokRefundInfo(@PathVariable("ticketId")Long ticketId) {

        return ApiResponseResult.buildSuccessResult(amazonReturnService.selectTiktokReturnPlanById(ticketId));
    }



    /**
     * 手动同步Tiktok历史信息
     *
     * @param
     * @return
     */
    @GetMapping("/manual/his")
    public ApiResponseResult syncCancelHisItem(@RequestParam(name = "shopId") Long shopId) {
        saleOrderCancelService.syncCancelHis(shopId);
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * 手动同步Tiktok历史状态
     *
     * @param
     * @return
     */
    @GetMapping("/manual/tk/cancelStatus")
    public ApiResponseResult syncCancelStatusManual(@RequestParam(name = "shopId") Long shopId ) {
        saleOrderCancelService.syncCancelStatusManual(shopId);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * 手动同步Tiktok历史状态
     *
     * @param
     * @return
     */
    @PostMapping("/manual/tk/revocation")
    public ApiResponseResult manualCommonRevocation(@RequestParam(name = "ticketId") Long ticketId,@RequestParam(name = "interceptId") Integer interceptId, @RequestParam(name = "cancelOrderId") Long cancelOrderId) {
        UserEntity authUserEntity = getAuthUserEntity();
        saleOrderCancelService.manualCommonRevocation(ticketId, interceptId, cancelOrderId,authUserEntity);
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * 根据仓库ID获取取消信息
     *
     * @param
     * @return
     */
    @GetMapping("/manual/wayfail/cancelInfo")
    public ApiResponseResult syncCancelWayfairlHisItem() {
        saleOrderCancelService.saveOrderWayfailCancel();
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * 根据仓库ID获取取消信息
     *
     * @param
     * @return
     */
    @PostMapping("/manual/request/Intercept/msg")
    public ApiResponseResult redisTest(@RequestBody SaleOrderCancelInterceptReceive saleOrderCancelInterceptReceive) {
        saleOrderCancelService.saleOrderCancelInterceptMessageReceive(saleOrderCancelInterceptReceive,false);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * 重新发货
     */
    @GetMapping("/manual/redelivery")
    @RepeatSubmit
    public ApiResponseResult redelivery(@RequestParam(value = "contextId") Integer contextId, @RequestParam(value = "ticketId") Long ticketId) {

        AuthUserDetails thisUser = getAuthUser();
        saleOrderCancelService.redelivery(contextId, ticketId, thisUser);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * @description: 手动获取Ebay取消信息
     * @author: Moore
     * @date: 2024/10/10 11:55
     * @param
     * @param shopId 店铺ID
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @GetMapping("/get/ebay/cancelList/manual")
    public ApiResponseResult getAllCancelStatus(@RequestParam("contextId") Integer contextI,@RequestParam(value = "shopId",required = false) Long shopId) {
        saleOrderCancelService.saveEbayCancelListJob(shopId);
        return ApiResponseResult.buildSuccessResult();
    }
}
