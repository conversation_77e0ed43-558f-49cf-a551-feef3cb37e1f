package com.bizark.op.web.config;

import com.bizark.common.constant.PropertiesConfig;
import com.bizark.op.common.boot.AwtHeadlessInit;
import com.bizark.framework.app.service.OdpsAgent;
import com.bizark.framework.app.service.XTraceAgent;
import com.bizark.framework.util.JWTAuthHelper;
import com.bizark.op.service.handler.demo.MyDemoNotificationMessageHandleImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
//import org.apache.tomcat.util.http.LegacyCookieProcessor;
import com.xxl.conf.core.spring.XxlConfFactory;
import org.apache.catalina.Context;
import org.apache.tomcat.util.http.LegacyCookieProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.embedded.tomcat.TomcatContextCustomizer;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.boot.web.servlet.ServletContextInitializer;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import javax.servlet.SessionTrackingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/3/2
 */
@Configuration
public class BeanInit {

    @Value("${enviorment}")
    private String env;

    @Bean
    public PropertiesConfig initPropertiesConfig() {
        return new PropertiesConfig("config/application-" + env);
    }

    @Bean
    public JWTAuthHelper initJWTAuthHelper() {
        return new JWTAuthHelper(initPropertiesConfig());
    }

    @Bean
    public XTraceAgent initXTraceAgent(){
        XTraceAgent xTraceAgent = new XTraceAgent(initPropertiesConfig());
//        xTraceAgent.initTracer();
        return xTraceAgent;
    }

    @Bean
    public OdpsAgent initOdpsAgent(){
        OdpsAgent odpsAgent = new OdpsAgent(initPropertiesConfig());
//        odpsAgent.init();
        return odpsAgent;
    }

    @Bean
    public XxlConfFactory xxlConfFactory(@Value("${xxl.conf.admin.address}") String adminAddress,
                                         @Value("${xxl.conf.access.token}") String accessToken,
                                         @Value("${xxl.conf.mirrorfile}") String mirrorfile) {

        XxlConfFactory xxlConf = new XxlConfFactory();
        xxlConf.setAdminAddress(adminAddress);
        xxlConf.setEnv(env);
        xxlConf.setAccessToken(accessToken);
        xxlConf.setMirrorfile(mirrorfile);
        return xxlConf;
    }

//    @Bean
//    public OpenEntityManagerInViewFilter openEntityManagerInViewFilter(){
//        OpenEntityManagerInViewFilter openEntityManagerInViewFilter = new OpenEntityManagerInViewFilter();
//        openEntityManagerInViewFilter.setEntityManagerFactoryBeanName("entityManagerFactoryErp");
//        openEntityManagerInViewFilter.setPersistenceUnitName("entityManagerFactoryErp");
//        return openEntityManagerInViewFilter;
//    }
//
//    //@Bean
//    public FilterRegistrationBean registrationBean()
//    {
//        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
//        filterRegistrationBean.setFilter(openEntityManagerInViewFilter());
//        filterRegistrationBean.setName("openEntityManagerInViewFilter");
//        filterRegistrationBean.setUrlPatterns(Arrays.asList("/*"));
//        filterRegistrationBean.setOrder(Integer.MAX_VALUE);
//        return filterRegistrationBean;
//    }

    @Bean
    public AwtHeadlessInit wtHeadlessInit(){
        return new AwtHeadlessInit();
    }

    @Bean
    public ServletContextInitializer servletContextInitializer1() {
        return new ServletContextInitializer() {
            @Override
            public void onStartup(ServletContext servletContext) throws ServletException {
                servletContext.setSessionTrackingModes(Collections.singleton(SessionTrackingMode.COOKIE) );
            }
        };
    }

//    @Bean
//    public EmbeddedServletContainerCustomizer customizer() {
//        return container -> {
//            if (container instanceof TomcatEmbeddedServletContainerFactory) {
//                TomcatEmbeddedServletContainerFactory tomcat = (TomcatEmbeddedServletContainerFactory) container;
//                tomcat.addContextCustomizers(context -> context.setCookieProcessor(new LegacyCookieProcessor()));
//            }
//        };
//    }

    // @Component
    // public class EmbeddedTomcatConfig implements WebServerFactoryCustomizer<ConfigurableServletWebServerFactory> {
    //     @Override
    //     public void customize(ConfigurableServletWebServerFactory factory) {
    //         ((TomcatServletWebServerFactory)factory).addContextCustomizers(new TomcatContextCustomizer() {
    //             @Override
    //             public void customize(Context context) {
    //                 context.setCookieProcessor(new LegacyCookieProcessor());
    //             }
    //         });
    //     }
    // }

    @Bean(name = "mapperObject")
    public ObjectMapper getObjectMapper() {
        ObjectMapper om = new ObjectMapper();
        om.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(DateTimeFormatter.ofPattern("HH:mm:ss")));
        om.registerModule(javaTimeModule);
        return om;
    }

    @Bean(name = "taskNotificationHandlers")
    public Map<String,Class> taskNotificationHandlers(){
        Map<String,Class> notificationHandleMap = new HashMap<>();
        notificationHandleMap.put("dev-erp-biztaskdemo", MyDemoNotificationMessageHandleImpl.class);
        notificationHandleMap.put("test-erp-biztaskdemo", MyDemoNotificationMessageHandleImpl.class);
        notificationHandleMap.put("pub-erp-biztaskdemo", MyDemoNotificationMessageHandleImpl.class);

        return notificationHandleMap;
    }

//    @Component
//    public class EmbeddedTomcatConfig implements WebServerFactoryCustomizer<TomcatServletWebServerFactory> {
//        @Override
//        public void customize(TomcatServletWebServerFactory factory) {
//            factory.addContextCustomizers(new TomcatContextCustomizer() {
//                @Override
//                public void customize(Context context) {
//                    context.setCookieProcessor(new LegacyCookieProcessor());
//                }
//            });
//        }
//    }

    @Component
    public class EmbeddedTomcatConfig implements WebServerFactoryCustomizer<ConfigurableServletWebServerFactory> {
        @Override
        public void customize(ConfigurableServletWebServerFactory factory) {
            ((TomcatServletWebServerFactory)factory).addContextCustomizers(new TomcatContextCustomizer() {
                @Override
                public void customize(Context context) {
                    context.setCookieProcessor(new LegacyCookieProcessor());
                }
            });
        }
    }

}
