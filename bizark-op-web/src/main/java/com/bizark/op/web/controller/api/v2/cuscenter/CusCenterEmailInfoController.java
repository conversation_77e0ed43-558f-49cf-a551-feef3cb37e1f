package com.bizark.op.web.controller.api.v2.cuscenter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bizark.op.api.cons.PermDefine;
import com.bizark.op.api.entity.op.xsm.XsmReviewCommonDTO;
import com.bizark.op.api.form.customer.CusCenterEmailInfoQuery;
import com.bizark.op.api.service.RpcMarExportService;
import com.bizark.op.api.service.customer.ICusCenterEmailInfoService;
import com.bizark.op.api.vo.customer.CusCenterEmailInfoDTO;
import com.bizark.op.api.vo.customer.CusCenterEmailInfoVO;
import com.bizark.op.common.core.page.TableDataInfo;
import com.bizark.op.common.function.page.PageHelperTemplate;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.service.util.ExcelExport;
import com.bizark.op.web.controller.api.AbstractApiController;
import com.bizark.framework.web.view.ApiResponseResult;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 邮件信息Controller
 *
 * <AUTHOR>
 * @date 2022-05-12
 */
@RestController("CusCenterEmailInfoController")
@RequestMapping(value = "/api/v2/cusCenter/email")
@Tag(name = "cusCenterEmailInfo", description = "客服中心邮件信息")
public class CusCenterEmailInfoController extends AbstractApiController {
    @Autowired
    private ICusCenterEmailInfoService cusCenterEmailInfoService;

    @Autowired
    private PageHelperTemplate<CusCenterEmailInfoDTO, CusCenterEmailInfoVO> pageHelperTemplate;

    @Autowired
    private RpcMarExportService rpcMarExportService;


    /**
     * 查询其他站外信列表
     *
     * @param query
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo<CusCenterEmailInfoVO> listPage(CusCenterEmailInfoQuery query, @RequestParam("contextId") Integer contextId) {
        if (query.getContextId() == null || query.getContextId() < 1) {
            query.setContextId(getAuthUserEntity().getOrgId());
        }
        return pageHelperTemplate.execute(page -> cusCenterEmailInfoService.selectCusCenterEmailInfoListByPage(query, page));
    }

    /**
     * 导出邮件信息列表
     * @param query
     * @param response
     */
    @GetMapping("/export")
    public ApiResponseResult export(@RequestParam("contextId") Integer contextId,CusCenterEmailInfoQuery query, HttpServletResponse response) {
        query.setContextId(contextId);
        if (query.getContextId() == null || query.getContextId() < 1) {
            query.setContextId(getAuthUserEntity().getOrgId());
        }
       cusCenterEmailInfoService.listExport(response, query, getAuthUserEntity());
         rpcMarExportService.exportEmailInfo(JSON.toJSONString(query));
        return ApiResponseResult.buildSuccessResult("请前往任务中心查看");
    }




    /**
     * 获取邮件信息详细信息
     * @param
     * @return
     */
    @GetMapping(value = "/itms")
    public ApiResponseResult<CusCenterEmailInfoVO> getInfo( CusCenterEmailInfoQuery query, @RequestParam("contextId") Integer contextId) {
        query.setContextId(contextId);
        if (StringUtils.isEmpty(query.getHisFlag())) {
            query.setHisFlag("N");//不查询历史数据
        }
        return ApiResponseResult.buildSuccessResult(cusCenterEmailInfoService.selectCusCenterEmailInfoItem(query));
    }

    /**
     * bi查询其他站外信列表
     *
     * @param query
     * @return
     */
    @PostMapping("/bi/list")
    public JSONObject biQueryListPage(@RequestBody CusCenterEmailInfoQuery query, @RequestParam("contextId") Integer contextId) {
        query.setContextId(contextId);
        startPage();
        if(StringUtils.isNotEmpty(query.getSenTimeFrom())) {
            query.setSenTimeFrom(query.getSenTimeFrom() + " 00:00:00");
        }
        if(StringUtils.isNotEmpty(query.getSenTimeTo())) {
            query.setSenTimeTo(query.getSenTimeTo() + " 23:59:59");
        }
        return cusCenterEmailInfoService.biQueryListPage(query);
    }

    /**
     * bi其他站外信列表导出
     *
     * @param query
     * @return
     */
    @PostMapping("/bi/export")
    public ApiResponseResult biExport(@RequestBody CusCenterEmailInfoQuery query, @RequestParam("contextId") Integer contextId) {
        query.setContextId(contextId);
        if(StringUtils.isNotEmpty(query.getSenTimeFrom())) {
            query.setSenTimeFrom(query.getSenTimeFrom() + " 00:00:00");
        }
        if(StringUtils.isNotEmpty(query.getSenTimeTo())) {
            query.setSenTimeTo(query.getSenTimeTo() + " 23:59:59");
        }
        cusCenterEmailInfoService.biExport(query);
        return ApiResponseResult.buildSuccessResult("导出已生成，请至【任务中心】-【任务列表】查看处理进度及下载文件！");
    }

}
