package com.bizark.op.web.controller.api.v2.stat;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.bizark.boss.api.cons.PermDefine;
import com.bizark.boss.api.enm.ExModuleDefineEnum;
import com.bizark.common.util.JacksonUtils;
import com.bizark.op.api.entity.op.stat.StatOrderSalesEntity;
import com.bizark.op.api.entity.op.stat.StatOrderSalesQuery;
import com.bizark.op.api.service.stat.StatOrderSalesService;
import com.bizark.op.common.exception.CustomException;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.common.util.poi.ExcelUtil;
import com.bizark.op.service.util.ExcelExport;
import com.bizark.op.web.controller.api.AbstractApiController;
import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.bizark.usercenter.api.parameter.role.SellerSkuVisualVo;
import com.bizark.usercenter.api.service.UcRoleService;
import com.google.common.base.Joiner;
import com.xxl.conf.core.annotation.XxlConf;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单统计接口
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
@RestController
@RequestMapping("/api/v2/stat/order")
@Slf4j
public class StatOrderSalesController extends AbstractApiController {
    @Autowired
    private StatOrderSalesService statOrderSalesService;

    @XxlConf(value = "bizark-erp.salestate.way")
    private static String SALE_ORDER_FLAG;



    @Autowired
    private UcRoleService ucRoleService;

    /**
     * 查询订单销量统计列表
     */
    @GetMapping("/sales/list")
    public ApiResponseResult list(@RequestParam(value = "contextId") Long contextId,  StatOrderSalesQuery statOrderSalesQuery) {
        if (null == statOrderSalesQuery.getShopId()) {
          Map<String, List<Integer>> checkAllModuleDataAccess = checkAllModuleDataAccess(contextId.intValue());
            List<Integer> account = checkAllModuleDataAccess.get("Account");
            if (!CollectionUtils.isEmpty(account)) {
                String joinShopIds= Joiner.on(",").join(account);
                statOrderSalesQuery.setShopIdArray(joinShopIds);
            }
        }
        statOrderSalesQuery.setOrganizationId(contextId);
        JSONObject result = statOrderSalesService.selectStatOrderSalesListNew(statOrderSalesQuery);
        return ApiResponseResult.buildSuccessResult(result);
    }


    /**
     * 查询订单销量统计列表
     */
    @GetMapping("/sales/list/group")
    public ApiResponseResult listGroup(@RequestParam(value = "contextId") Long contextId, StatOrderSalesQuery statOrderSalesQuery) {
        if (null == statOrderSalesQuery.getShopId()) {
            Map<String, List<Integer>> checkAllModuleDataAccess = checkAllModuleDataAccess(contextId.intValue());
            List<Integer> account = checkAllModuleDataAccess.get("Account");
            if (!CollectionUtils.isEmpty(account)) {
                String joinShopIds= Joiner.on(",").join(account);
                statOrderSalesQuery.setShopIdArray(joinShopIds);
            }
        }

        startPage();
        statOrderSalesQuery.setOrganizationId(contextId);
        JSONObject result = statOrderSalesService.selectStatOrderSalesListNewGroup(statOrderSalesQuery);
          return ApiResponseResult.buildSuccessResult(result);
    }


    /**
     * 销量统计列表行 统计行
     */
    @GetMapping("/sales/list/sum/group")
    public ApiResponseResult listSumGroup(@RequestParam(value = "contextId") Long contextId, StatOrderSalesQuery statOrderSalesQuery) {
        if (null == statOrderSalesQuery.getShopId()) {
            Map<String, List<Integer>> checkAllModuleDataAccess = checkAllModuleDataAccess(contextId.intValue());
            List<Integer> account = checkAllModuleDataAccess.get("Account");
            if (!CollectionUtils.isEmpty(account)) {
                String joinShopIds= Joiner.on(",").join(account);
                statOrderSalesQuery.setShopIdArray(joinShopIds);
            }
        }
        statOrderSalesQuery.setOrganizationId(contextId);
        JSONObject result = statOrderSalesService.selectStatOrderSalesListNewSumGroup(statOrderSalesQuery);
        return ApiResponseResult.buildSuccessResult(result);
    }

    /**
     * 导出销量统计列表（根据日期导出）
     */
    @GetMapping("/export")
    public void export(@RequestParam("contextId") Long contextId, StatOrderSalesQuery statOrderSalesQuery, HttpServletResponse response)  {
        if (null == statOrderSalesQuery.getShopId()) {
            Map<String, List<Integer>> checkAllModuleDataAccess = checkAllModuleDataAccess(contextId.intValue());
            List<Integer> account = checkAllModuleDataAccess.get("Account");
            if (!CollectionUtils.isEmpty(account)) {
                String joinShopIds= Joiner.on(",").join(account);
                statOrderSalesQuery.setShopIdArray(joinShopIds);
            }
        }
        statOrderSalesQuery.setOrganizationId(contextId);
        ArrayList<Map<String, Object>> scOrderStat = statOrderSalesService.statOrderSalesExport(statOrderSalesQuery);
        if (CollectionUtils.isEmpty(scOrderStat)){
            throw new CustomException("导出信息为空");
        }
        try {
            ExcelExport.exportAndDownload(scOrderStat, response, URLEncoder.encode("销量统计", "utf8") + ".xls");
        } catch (UnsupportedEncodingException e) {
            throw new CustomException("导出失败");
        }
    }

    /**
     * 导出销量统计列表（根据日期导出,优化）
     */
    @GetMapping("/export/v2")
    public void exportV2(@RequestParam("contextId") Long contextId, StatOrderSalesQuery statOrderSalesQuery, HttpServletResponse response)  {
        if (null == statOrderSalesQuery.getShopId()) {
            Map<String, List<Integer>> checkAllModuleDataAccess = checkAllModuleDataAccess(contextId.intValue());
            List<Integer> account = checkAllModuleDataAccess.get("Account");
            if (!CollectionUtils.isEmpty(account)) {
                String joinShopIds= Joiner.on(",").join(account);
                statOrderSalesQuery.setShopIdArray(joinShopIds);
            }
        }
        statOrderSalesQuery.setOrganizationId(contextId);
        ArrayList<Map<String, Object>> scOrderStat = statOrderSalesService.statOrderSalesExportV2(statOrderSalesQuery);
        if (CollectionUtils.isEmpty(scOrderStat)){
            throw new CustomException("导出信息为空");
        }
        try {
            ExcelExport.exportAndDownload(scOrderStat, response, URLEncoder.encode("销量统计", "utf8") + ".xls");
        } catch (UnsupportedEncodingException e) {
            throw new CustomException("导出失败");
        }
    }

    /**
     * 导出销量统计列表（根据日期导出-弃用）
     */
    @GetMapping("/export/v3")
    public void exportV3(@RequestParam("contextId") Long contextId, StatOrderSalesQuery statOrderSalesQuery, HttpServletResponse response)  {
        statOrderSalesQuery.setOrganizationId(contextId);
        ArrayList<Map<String, Object>> scOrderStat = statOrderSalesService.statOrderSalesExportGroup(statOrderSalesQuery);
        if (CollectionUtils.isEmpty(scOrderStat)){
            throw new CustomException("导出信息为空");
        }
        try {
            ExcelExport.exportAndDownload(scOrderStat, response, URLEncoder.encode("销量统计", "utf8") + ".xls");
        } catch (UnsupportedEncodingException e) {
            throw new CustomException("导出失败");
        }
    }


    /**
     * @description: 根据SellerSku导出
     * @author: Moore
     * @date: 2023/10/9 11:20
     * @param
     * @param contextId
     * @param statOrderSalesQuery
     * @param response
     * @return: void
    **/
    @GetMapping("/export/sellersku")
    public void exportSellerSku(@RequestParam("contextId") Long contextId, StatOrderSalesQuery statOrderSalesQuery, HttpServletResponse response)  {
        if (null == statOrderSalesQuery.getShopId()) {
            Map<String, List<Integer>> checkAllModuleDataAccess = checkAllModuleDataAccess(contextId.intValue());
            List<Integer> account = checkAllModuleDataAccess.get("Account");
            if (!CollectionUtils.isEmpty(account)) {
                String joinShopIds= Joiner.on(",").join(account);
                statOrderSalesQuery.setShopIdArray(joinShopIds);
            }
        }
        statOrderSalesQuery.setOrganizationId(contextId);
        ArrayList<LinkedHashMap<String, Object>> scOrderStat = statOrderSalesService.statOrderSalesExportSellerSku(statOrderSalesQuery);
        if (CollectionUtils.isEmpty(scOrderStat)){
            throw new CustomException("导出信息为空");
        }
        try {
            ExcelExport.exportAndDownload(scOrderStat, response, URLEncoder.encode("销量统计-SelleSku", "utf8") + ".xls", null);
        } catch (UnsupportedEncodingException e) {
            throw new CustomException("导出失败");
        }
    }


    /**
     * 获取订单销量统计图表数据(分析)
     */
    @GetMapping("/sales/chart")
    public ApiResponseResult statOrderSalesChart(@RequestParam(value = "contextId") Long contextId,StatOrderSalesQuery statOrderSalesQuery) {
        statOrderSalesQuery.setOrganizationId(contextId);
        JSONObject result = statOrderSalesService.statOrderSalesChart(statOrderSalesQuery);
        return ApiResponseResult.buildSuccessResult(result);
    }

    /**
     * 获取商品库存(sc_goods_erp_stock_v 与 fba )
     */
    @GetMapping("/goods/stock")
    public ApiResponseResult getGoodsStock(@RequestParam(value = "contextId") Long contextId,
                                           @RequestParam(value = "goodsSku") String goodsSku,
                                           @RequestParam(value = "countryCode",required =false) String countryCode) {

        return ApiResponseResult.buildSuccessResult(statOrderSalesService.selectErpSkuStockAndArrivalStockList(contextId, goodsSku,countryCode));
    }

    /**
     * 获取商品库存(sc_goods_erp_stock_v 与 fba )
     */
    @GetMapping("/goods/stock/summary")
    public ApiResponseResult getGoodsStockSummary(@RequestParam(value = "contextId") Long contextId,
                                           @RequestParam(value = "goodsSku") String goodsSku) {
        return ApiResponseResult.buildSuccessResult(statOrderSalesService.selectErpSkuStockAndArrivalStockListSummary(contextId, goodsSku));
    }



    /**
     * 获取即时销售
     */
    @ApiOperation(value = "即时销售")
    @GetMapping("/instant/sale")
    public ApiResponseResult getInstantSale(@RequestParam(value = "contextId") Long contextId,@RequestParam(value = "organizationIds",required = false) Long organizationIds) {
        StatOrderSalesQuery statOrderSalesQuery = new StatOrderSalesQuery();
        if (organizationIds != null) {
            statOrderSalesQuery.setOrganizationId(organizationIds);
        }else{
            statOrderSalesQuery.setOrganizationId(contextId);
        }

            Map<String, List<Integer>> checkAllModuleDataAccess = checkAllModuleSuperDataAccess(contextId.intValue());
            List<Integer> account = checkAllModuleDataAccess.get("Account");
            if (!CollectionUtils.isEmpty(account)) {
                String joinShopIds= Joiner.on(",").join(account);
                logger.error("首页即时查询的店铺：{}",joinShopIds);
                statOrderSalesQuery.setShopIdArray(joinShopIds);
            }
        return ApiResponseResult.buildSuccessResult(statOrderSalesService.getInstantSale(statOrderSalesQuery));
    }




    /**
     * 获取即时销售
     */
    @ApiOperation(value = "调用BI获取销量统计信息")
    @GetMapping("/sales/list/bi")
    public ApiResponseResult getBiSaleOrderList(@RequestParam(value = "contextId") Long contextId,
                                                @RequestParam(value = "exportflag",required = false) String  exportFlag,
                                                StatOrderSalesQuery statOrderSalesQuery) {

        statOrderSalesQuery.setExportFlag(exportFlag); //导出状态设置
//          Long shopId = statOrderSalesQuery.getShopId(); //店铺ID
//
//           //鉴权
//           Map<String, List<Integer>> checkAllModuleDataAccess = checkAllModuleDataAccess(contextId.intValue());
//            List<Integer> account = checkAllModuleDataAccess.get("Account");
//            if (CollectionUtils.isEmpty(account)) {
//                logger.error("----------销量统计无店铺权限-----------");
//                return ApiResponseResult.buildSuccessResult(new JSONObject());
//            }else if (shopId == null)  {
//                String joinShopIds = Joiner.on(",").join(account);
//                logger.error("查询的店铺：{}", joinShopIds);
//                statOrderSalesQuery.setShopIdArray(joinShopIds);
//            }
        if (null == statOrderSalesQuery.getShopId()) {
            Map<String, List<Integer>> checkAllModuleDataAccess = checkAllModuleSuperDataAccess(contextId.intValue());
            List<Integer> account = checkAllModuleDataAccess.get("Account");
            if (!CollectionUtils.isEmpty(account)) {
                String joinShopIds= Joiner.on(",").join(account);
                statOrderSalesQuery.setShopIdArray(joinShopIds);
            }
        }

        statOrderSalesQuery.setOrganizationId(contextId);//组织ID
        if ("bi".equals(SALE_ORDER_FLAG)) {
             if ("1".equals(statOrderSalesQuery.getExportFlag())) {
                 statOrderSalesService.selectSaleOrderBi(statOrderSalesQuery);
                 return ApiResponseResult.buildSuccessResult("导出已生成，请至【任务中心】-【任务列表】查看处理进度及下载文件！");
             }else{
               return   ApiResponseResult.buildSuccessResult(statOrderSalesService.selectSaleOrderBi(statOrderSalesQuery));
             }
        }else{
            return ApiResponseResult.buildSuccessResult(statOrderSalesService.getBiSaleOrderList(statOrderSalesQuery));
        }
    }




    /**
     * @description: 调用BI进行导出操作
     * @author: Moore
     * @date: 2023/10/8 20:41
     * @param
     * @param contextId
     * @param statOrderSalesQuery
     * @param response
     * @return: com.bizark.framework.web.view.ApiResponseResult
    **/
    @GetMapping("/export/date/bi")
    public ApiResponseResult export3(@RequestParam("contextId") Long contextId, StatOrderSalesQuery statOrderSalesQuery, HttpServletResponse response) {
//        Long shopId = statOrderSalesQuery.getShopId(); //店铺ID
//
//        //鉴权
//        Map<String, List<Integer>> checkAllModuleDataAccess = checkAllModuleSuperDataAccess(contextId.intValue());
//        List<Integer> account = checkAllModuleDataAccess.get("Account");
//        if (CollectionUtils.isEmpty(account)) {
//            logger.error("----------销量统计无店铺权限-----------");
//            return ApiResponseResult.buildSuccessResult(new JSONObject());
//        }else if (shopId == null)  {
//            String joinShopIds = Joiner.on(",").join(account);
//            logger.error("查询的店铺：{}", joinShopIds);
//            statOrderSalesQuery.setShopIdArray(joinShopIds);
//        }
        if (null == statOrderSalesQuery.getShopId()) {
            Map<String, List<Integer>> checkAllModuleDataAccess = checkAllModuleSuperDataAccess(contextId.intValue());
            List<Integer> account = checkAllModuleDataAccess.get("Account");
            if (!CollectionUtils.isEmpty(account)) {
                String joinShopIds= Joiner.on(",").join(account);
                statOrderSalesQuery.setShopIdArray(joinShopIds);
            }
        }


        statOrderSalesQuery.setOrganizationId(contextId);

        if ("bi".equals(SALE_ORDER_FLAG)) {
            statOrderSalesService.exportBiSaleOrderByDate(statOrderSalesQuery, null,response);
            return ApiResponseResult.buildSuccessResult("导出已生成，请至【任务中心】-【任务列表】查看处理进度及下载文件！");
        }else{
            ExcelUtil<StatOrderSalesEntity> util = new ExcelUtil<StatOrderSalesEntity>(StatOrderSalesEntity.class);
            statOrderSalesService.exportBiSaleOrderListByDate(statOrderSalesQuery, util,response);
        }

        return ApiResponseResult.buildSuccessResult();
    }



    /**
     * @param
     * @param contextId
     * @param
     * @param
     * @description: 销售订单导出
     * @author: Moore
     * @date: 2023/10/8 20:41
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @GetMapping("/export/sale/order/export")
    @RequiresPermissions(com.bizark.op.api.cons.PermDefine.MAR_SALE_ORDER_LIST)
    public ApiResponseResult export3(@RequestParam("contextId") Long contextId,
                                     @RequestParam(value = "search[IN_channelStatus]", required = false) String channelStatus,
                                     @RequestParam(value = "search[IN_channel]", required = false) String saleChannel,
                                     @RequestParam(value = "search[IN_channelId]", required = false) String channelId,
                                     @RequestParam(value = "handlerBy", required = false) String handlerBy,
                                     @RequestParam(value = "search[IN_lineId]", required = false) String lineId,
                                     @RequestParam(value = "search[GE_channelCreated]", required = false) String dateFrom,
                                     @RequestParam(value = "search[LE_channelCreated]", required = false) String dateTo,
                                     @RequestParam(value = "search[EQ_orderNo]", required = false) String orderNO,
                                     @RequestParam(value = "search[EQ_buyName]", required = false) String buyName,
                                     @RequestParam(value = "search[EQ_sellerSku]", required = false) String sellerSKu,
                                     @RequestParam(value = "search[EQ_asin]", required = false) String asin,
                                     @RequestParam(value = "search[EQ_erpSku]", required = false) String erpSku) {

        if (StringUtils.isEmpty(dateFrom) || StringUtils.isEmpty(dateTo)) {
            throw new CustomException("导出订单时间不能为空!");
        }

        UserEntity authUserEntity = getAuthUserEntity();
        StatOrderSalesQuery statOrderSalesQuery = new StatOrderSalesQuery();
        statOrderSalesQuery.setOrganizationId(contextId); //组织ID
        statOrderSalesQuery.setChannelStatus(channelStatus);//渠道状态  （多选）
        statOrderSalesQuery.setSaleChannel(saleChannel);//渠道
        statOrderSalesQuery.setShopIdArray(null); //店铺，与权限一并设置  （多选）
        statOrderSalesQuery.setOperationIdArray(handlerBy); //运营      （多选）
        statOrderSalesQuery.setLineId(null);//项目，与权限一并设置  （多选）
        statOrderSalesQuery.setPurchaseDateFrom(dateFrom); //订单开始时间
        statOrderSalesQuery.setPurchaseDateTo(dateTo);//订单结束时间
        statOrderSalesQuery.setOrderNo(orderNO);
        statOrderSalesQuery.setBuyName(buyName); //买家名称
        statOrderSalesQuery.setSellerSku(sellerSKu);
        statOrderSalesQuery.setAsin(asin);
        statOrderSalesQuery.setErpSku(erpSku);
        //获取用户权限
        Map<String, List<Integer>> checkAllModuleDataAccess = checkAllModuleSuperDataAccess(contextId.intValue());
        log.info("用户 {} 组织 {} 获取权限数据 {}", authUserEntity.getName(), contextId, JacksonUtils.toJson(checkAllModuleDataAccess));

        //项目权限
        List<Integer> productLineIdList = checkAllModuleDataAccess.get(ExModuleDefineEnum.PRODUCT_LINE.getLabel());
        if (ObjectUtil.isNotNull(productLineIdList)) {
            List<String> strLineId = productLineIdList.stream().filter(item -> item != null).map(String::valueOf).collect(Collectors.toList());
            statOrderSalesQuery.setLineId(strLineId.stream().collect(Collectors.joining(",")));
            if (!StringUtils.isEmpty(lineId)) { //查询的项目ID是否符合权限
                List<String> reqLineId = Arrays.asList(lineId.split(","));
                String reqLineIdStr = strLineId.stream().filter(item -> reqLineId.contains(item)).collect(Collectors.joining(","));
                statOrderSalesQuery.setLineId(StringUtils.isNotEmpty(reqLineIdStr) ? reqLineIdStr : null);
            }
        } else {
            statOrderSalesQuery.setLineId(lineId);//项目ID
        }
        //仓库权限         //TODO
        //渠道账号权限（店铺）
        List<Integer> accountIds = checkAllModuleDataAccess.get(ExModuleDefineEnum.ACCOUNT.getLabel());
        if (ObjectUtil.isNotNull(accountIds)) {
            statOrderSalesQuery.setShopIdArray(Joiner.on(",").join(accountIds));
            if (!StringUtils.isEmpty(channelId)) {
                List<String> accountIdsStr = accountIds.stream().filter(item -> item != null).map(String::valueOf).collect(Collectors.toList());
                List<String> reqAccountsId = Arrays.asList(channelId.split(","));
                String reqAccountsIdStr = accountIdsStr.stream().filter(item -> reqAccountsId.contains(item)).collect(Collectors.joining(","));
                statOrderSalesQuery.setShopIdArray(StringUtils.isNotEmpty(reqAccountsIdStr) ? reqAccountsIdStr : null);
            }
        } else {
            statOrderSalesQuery.setShopIdArray(channelId);
        }

        // sellerSku数据权限获取
        SellerSkuVisualVo selectSellerSku = ucRoleService.selectSellerSku(contextId.intValue(), authUserEntity, PermDefine.ERP_ORDER_ORDERMANAGE);
        log.info("销售订单导出sellersku权限：{}", JSONObject.toJSONString(selectSellerSku));
        if (!selectSellerSku.getIsAll()) {
            if (org.springframework.util.CollectionUtils.isEmpty(selectSellerSku.getSellerSkuList())) {
                statOrderSalesQuery.setSellerSku("-1");
            } else {
                List<String> sellerSkuList = selectSellerSku.getSellerSkuList();
                statOrderSalesQuery.setSellerSku(sellerSkuList.stream().collect(Collectors.joining(","))); //设置权限对应sellerSku

                if (!StringUtils.isEmpty(sellerSKu)) {
                    String reqSellerSku = sellerSkuList.stream().filter(item -> item.equals(sellerSKu)).collect(Collectors.joining(","));
                    statOrderSalesQuery.setSellerSku(StringUtils.isNotEmpty(reqSellerSku) ? reqSellerSku : null);
                }
            }
        } else {
            statOrderSalesQuery.setSellerSku(sellerSKu);
        }
        statOrderSalesService.exportSaleOrderEsExport(statOrderSalesQuery);
        return ApiResponseResult.buildSuccessResult("导出已生成，请至【任务中心】-【任务列表】查看处理进度及下载文件！");
    }


}
