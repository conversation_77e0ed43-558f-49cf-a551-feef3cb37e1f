package com.bizark.op.web.controller.api.v2.mar.amazon.fba;

import com.alibaba.fastjson.JSON;
import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.op.api.entity.op.amazon.fba.DTO.*;
import com.bizark.op.api.entity.op.amazon.fba.MarFbaCartonSpec;
import com.bizark.op.api.entity.op.amazon.fba.MarFbaPalletDetail;
import com.bizark.op.api.entity.op.amazon.fba.MarFbaShipmentInfo;
import com.bizark.op.api.entity.op.amazon.fba.MarStaTaskInfo;
import com.bizark.op.api.entity.op.amazon.fba.VO.*;
import com.bizark.op.api.service.amazon.fba.*;
import com.bizark.op.common.core.page.TableDataInfo;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.web.controller.api.AbstractApiController;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * sta任务
 */
@RestController
@RequestMapping("/api/v2/mar/sta/task")
@Tag(name = "AMZ_STA任务相关", description = "")
public class AmazonStaTaskController extends AbstractApiController {


    @Autowired
    private MarStaTaskInfoService marStaTaskInfoService;

    @Autowired
    private MarStaPackingSkusService marStaPackingSkusService;

    @Autowired
    private MarFbaCartonSpecService marFbaCartonSpecService;

    @Autowired
    private MarFbaShipmentInfoService marFbaShipmentInfoService;

    @Autowired
    private MarFbaPalletDetailService marFbaPalletDetailService;



    /**
     * 删除STA行信息（仅草稿）
     *
     * @param  shopId
     * @param  operationId
     * @return
     */
    @Operation(summary = "获取操作状态")
    @GetMapping("/select/operation")
    public ApiResponseResult deleteSta( @RequestParam Long shopId,@RequestParam String operationId) {
        return ApiResponseResult.buildSuccessResult( marStaTaskInfoService.getInboundOperationStatus(operationId,shopId));
    }



    /**
     * sta列表
     *
     * @param query
     * @param contextId
     * @return
     */
    @Operation(summary = "STA列表查询")
    @PostMapping("/list")
    public TableDataInfo<MarStaTaskInfoVO> list(@RequestBody MarStaTaskListQueryDTO query, @RequestParam Integer contextId) {
        query.setOrgId(contextId.longValue());
        startPage();
        return getDataTable(marStaTaskInfoService.selectStaTaskList(query));
    }



    /**
     * 删除STA行信息（仅草稿）
     *
     * @param  contextId
     * @param  query
     * @return
     */
    @Operation(summary = "导出Sta")
    @PostMapping("/list/export")
    public ApiResponseResult exportStaInfoList(@RequestBody MarStaTaskListQueryDTO query, @RequestParam Integer contextId) {
        query.setOrgId(contextId.longValue());
         //TODO 临时返回URL,上线前还原
        // marStaTaskInfoService.orgExportStaList(query, getAuthUserEntity());
        String s = marStaTaskInfoService.marStaTaskInfoExport(query);
        return ApiResponseResult.buildSuccessResult("导出已生成，请至【任务中心】-【任务列表】查看处理进度及下载文件！"+s);

    }




    /**
     * 删除STA行信息（仅草稿）
     *
     * @param contextId
     * @return
     */
    @Operation(summary = "删除STA")
    @DeleteMapping("/delete/sta")
    public ApiResponseResult deleteSta( @RequestParam Integer contextId,@RequestParam Long staId) {
        marStaTaskInfoService.deleteFbaInfo(staId);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     *
     *取消STA信息
     * @param contextId
     * @return
     */
    @Operation(summary = "取消STA信息")
    @PutMapping("/cancel/sta")
    public ApiResponseResult cancelSta( @RequestParam Integer contextId,@RequestParam Long staId) {
        return ApiResponseResult.buildSuccessResult(marStaTaskInfoService.cancelSta(staId));
    }

    /**
     *
     *取消STA信息
     * @param contextId
     * @return
     */
    @Operation(summary = "更新STA备注信息")
    @PutMapping("/update/sta/remark")
    public ApiResponseResult updateStaRemark( @RequestParam Integer contextId,@RequestParam Long taskId,
                                              @RequestBody MarStaTaskShipmentInfoDTO marStaTaskShipmentInfoDTO
                                              ) {
        UserEntity authUserDetails = getAuthUserEntity();
        marStaTaskInfoService.lambdaUpdate()
                .eq(MarStaTaskInfo::getId, taskId)
                .set(MarStaTaskInfo::getRemark, marStaTaskShipmentInfoDTO.getRemark())
                .set(MarStaTaskInfo::getUpdatedAt, DateUtils.getNowDate())
                .set(MarStaTaskInfo::getUpdatedBy, (authUserDetails == null ? -1 : authUserDetails.getId()))
                .set(MarStaTaskInfo::getUpdatedName, authUserDetails == null ? "system" : authUserDetails.getName())
                .update();
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * @param
     * @param
     * @param
     * @description: 根据节点信息查询对应STA商品明细信息 STEPT1
     * @author: Moore
     * @date: 2025/7/23 16:53
     * @return: com.bizark.op.common.core.page.TableDataInfo<com.bizark.op.api.entity.op.amazon.fba.MarStaTaskInfo>
     **/
    @Operation(summary = "STA发货节点明细数据")
    @GetMapping("/skus/shipment/items")
    public ApiResponseResult<MarStaTaskShipmentInfoDTO> skuShipmentNodeSkus(@RequestParam Integer taskId) {
        return ApiResponseResult.buildSuccessResult(marStaTaskInfoService.selectStaTaskListByShipmentSku(taskId));
    }


    /**
     * 删除节点信息
     *
     * @param contextId
     * @param id
     * @return
     */
    @Operation(summary = "发货商品节点，删除商品行")
    @DeleteMapping("/skus/shipment/remove")
    public ApiResponseResult skuRemover(@RequestParam Integer contextId,
                                        @RequestParam Integer id) {
        marStaPackingSkusService.removeById(id);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * sta列表
     *
     * @param query
     * @param contextId
     * @return
     */
    @Operation(summary = "商品选择商品列表")
    @PostMapping("/product/list")
    public TableDataInfo<MarStaProductSkuVO> productList(@RequestBody MarStaProductQueryDTO query,
                                                         @RequestParam Integer contextId) {
        startPage();
        return getDataTable(marStaTaskInfoService.selectProductList(query, contextId));
    }


    /**
     * 箱规下拉信息
     *
     * @param contextId
     * @param erpSku 款号
     * @return
     */
    @Operation(summary = "箱规下拉信息")
    @GetMapping("/carton/spec/list")
    public TableDataInfo<MarStaProductSkuVO> productList(@RequestParam Integer contextId,
                                                         @RequestParam String erpSku) {
        return getDataTable(marStaTaskInfoService.selectCartonSpecList(contextId, erpSku));
    }


    /**
     * @param
     * @param
     * @param
     * @description: 根据节点信息查询对应STA商品明细信息
     * @author: Moore
     * @date: 2025/7/23 16:53
     * @return: com.bizark.op.common.core.page.TableDataInfo<com.bizark.op.api.entity.op.amazon.fba.MarStaTaskInfo>
     **/
    @Operation(summary = "STA暂存")
    @PostMapping("/skus/ts")
    public ApiResponseResult skuTs(@RequestBody MarStaTaskShipmentInfoDTO query, @RequestParam Integer contextId) {
        query.setOrgId(contextId.longValue());
        return ApiResponseResult.buildSuccessResult( marStaTaskInfoService.saveStaTaskList(query));
    }


    /**
     * @param
     * @param
     * @param
     * @description: 根据节点信息查询对应STA商品明细信息
     * @author: Moore
     * @date: 2025/7/23 16:53
     * @return: com.bizark.op.common.core.page.TableDataInfo<com.bizark.op.api.entity.op.amazon.fba.MarStaTaskInfo>
     **/
    @Operation(summary = "预处理方式校验")
    @PostMapping("/pretreatment/method")
    public ApiResponseResult pretreatmentMethodVerify(@RequestBody MarStaTaskPreVerifyDTO query, @RequestParam Integer contextId) {
        return ApiResponseResult.buildSuccessResult(marStaTaskInfoService.pretreatmentMethodVerify(query));
    }


    /**
     * @param
     * @param
     * @param
     * @description:
     * @author: Moore
     * @date: 2025/7/23 16:53
     * @return: com.bizark.op.common.core.page.TableDataInfo<com.bizark.op.api.entity.op.amazon.fba.MarStaTaskInfo>
     **/
    @Operation(summary = "创建STA")
    @PostMapping("/skus/submit")
    public ApiResponseResult sbuStaTask(@RequestBody MarStaTaskShipmentInfoDTO query, @RequestParam Integer contextId) {
        query.setOrgId(contextId.longValue());
        return ApiResponseResult.buildSuccessResult(marStaTaskInfoService.subStaTask(query));
    }


    /**
     * @param
     * @param
     * @param contextId
     * @description:
     * @author: Moore
     * @date: 2025/8/5 17:15
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @Operation(summary = "STA创建确认包装箱组")
    @GetMapping("/skus/confirm/packing")
    public ApiResponseResult showPackPreview(@RequestParam Integer contextId, @RequestParam Long taskId) {
        return ApiResponseResult.buildSuccessResult( marStaTaskInfoService.confirmPackingOption( contextId,taskId));
    }


    /**
     * @param
     * @param
     * @param contextId
     * @description:
     * @author: Moore
     * @date: 2025/8/5 17:15
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @Operation(summary = "STA商品装箱组明细数据")
    @GetMapping("/skus/picking/group/items")
    public ApiResponseResult staPickingNodeSkus(@RequestParam Integer contextId, @RequestParam Long taskId) {
        List<MarStaPackGroupVO> marStaPackGroupVOList = marStaTaskInfoService.staPickingGroupNodeSkus(contextId, taskId);
        return ApiResponseResult.buildSuccessResult(marStaPackGroupVOList);
    }

    /**
     * @param
     * @param
     * @param contextId
     * @param inboundPlanId 任务ID
     * @param contextId 组编号
     * @param packMethod   装箱方式 1.每箱一款SKU
     * @description:
     * @author: Moore
     * @date: 2025/8/5 17:15
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @Operation(summary = "保存包装组信息")
    @PostMapping("/skus/picking/group/save")
    public ApiResponseResult staPickingNodeSkusSave(@RequestBody MarStaPackGroupVO marStaPackingSkuVO,
                                                    @RequestParam Integer contextId,
                                                    @RequestParam String inboundPlanId,
                                                    @RequestParam String packingGroupId,
                                                    @RequestParam String packMethod) {
        marStaTaskInfoService.staPickingNodeSkusSave(contextId, marStaPackingSkuVO, inboundPlanId, packingGroupId,packMethod);
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * @param
     * @param
     * @param contextId
     * @description:
     * @author: Moore
     * @date: 2025/8/5 17:15
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @Operation(summary = "amz设置包装组信息")
    @GetMapping("/skus/picking/group/amz/update")
    public ApiResponseResult updateAmzPackInfo(@RequestParam Integer contextId, @RequestParam String inboundPlanId) {
        return ApiResponseResult.buildSuccessResult(marStaTaskInfoService.updateAmzPackInfo(contextId, inboundPlanId));
    }


    @Operation(summary = "异步导出装箱清单")
    @PostMapping("/asyncExportPackingList")
    public ApiResponseResult<String> asyncExportPackingList(@RequestBody AsyncExportPackingListDTO dto, @RequestParam Integer contextId) {
           dto.setOrgId(contextId);
        return ApiResponseResult.buildSuccessResult("导出成功", marStaTaskInfoService.asyncExportPackingList(JSON.toJSONString(dto)));
    }



    /**
     * @description: 生成预览 1
     * @author: Moore
     * @date: 2025/8/20 16:09
     * @param
     * @param contextId
     * @param taskId
     * @return: com.bizark.framework.web.view.ApiResponseResult
    **/
    @Operation(summary = "生成预览")
    @GetMapping("/skus/picking/group/amz/generatePlacementOptions")
    public ApiResponseResult generatePlacementOptions(@RequestParam Integer contextId, @RequestParam Long taskId) {
        return ApiResponseResult.buildSuccessResult(marStaTaskInfoService.generatePlacementOptions(contextId, taskId));
    }



    /**
     * @param contextId
     * @description: 弹出预览信息
     * @author: Moore
     * @date: 2025/8/5 17:15
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @Operation(summary = "获取预览信息")
    @GetMapping("/skus/picking/group/amz/listplacementoptions")
    public ApiResponseResult listplacementoptions(@RequestParam Integer contextId, @RequestParam Long taskId) {
        return ApiResponseResult.buildSuccessResult(marStaTaskInfoService.listplacementoptions(contextId, taskId));
    }


    /**
     * @param
     * @param
     * @param contextId
     * @description: 确认申报
     * @author: Moore
     * @date: 2025/8/5 17:15
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @Operation(summary = "确认申报")
    @GetMapping("/skus/picking/group/amz/confirmPlacementOption")
    public ApiResponseResult listplacementoptions(@RequestParam Integer contextId, @RequestParam Long taskId, @RequestParam String placementOptionId) {
        return ApiResponseResult.buildSuccessResult(marStaTaskInfoService.confirmPlacementOption(contextId, taskId, placementOptionId));
    }



    /**
     * @param
     * @param
     * @param contextId
     * @description: 确认申报
     * @author: Moore
     * @date: 2025/8/5 17:15
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @Operation(summary = "保存FBA信息")
    @PostMapping("/skus/picking/group/amz/saveFbaInfo")
    public ApiResponseResult saveFbaInfo(@RequestParam Integer contextId, @RequestParam Long taskId,@RequestBody MarFbaSaveDTO  marFbaSaveDTO) {
        marStaTaskInfoService.saveFbaInfo(contextId, taskId,marFbaSaveDTO);
        return ApiResponseResult.buildSuccessResult();
    }



    /**
     * @param
     * @param
     * @param contextId
     * @description: 确认申报
     * @author: Moore
     * @date: 2025/8/5 17:15
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @Operation(summary = "查询3,4,5节点信息")
    @GetMapping("/fba/node/three")
    public ApiResponseResult getFbaNodeThree(@RequestParam Integer contextId, @RequestParam Long taskId ) {
        List<MarFbaShipmentNodeVO> marFbaShipmentNodeVO =marStaTaskInfoService.getFbaNodeThree(contextId, taskId);
        return ApiResponseResult.buildSuccessResult(marFbaShipmentNodeVO);
    }


    @Operation(summary = "更新Fba名称")
    @GetMapping("/fba/name/update")
    public ApiResponseResult updateFbaName(@RequestParam Integer contextId,@RequestParam Long fbaId, @RequestParam String name ) {
        marStaTaskInfoService.updateFbaName(contextId, fbaId, name);
        return ApiResponseResult.buildSuccessResult( );
    }




    @Operation(summary = "生成FBA承运方式")
    @PostMapping("/fba/generateTransports")
    public ApiResponseResult generateTransports(@RequestParam Integer contextId,@RequestBody List<FbaTransportationOptionsDTO>  fbaTransportationOptionsDTOList) {
        marStaTaskInfoService.generateTransports(contextId, fbaTransportationOptionsDTOList);
        return ApiResponseResult.buildSuccessResult( );
    }

    @Operation(summary = "获取FBA承运方式")
    @PostMapping("/fba/listTransportationOptions")
    public ApiResponseResult listTransportationOptions(@RequestParam Integer contextId,@RequestBody FbaTransportationOptionsDTO fbaTransportationOptionsDTOList) {
        ListTransportationOptionsVO listTransportationOptionsVO = marStaTaskInfoService.listTransportationOptions(contextId, fbaTransportationOptionsDTOList);
        return ApiResponseResult.buildSuccessResult(listTransportationOptionsVO);
    }



    /**
     * @param
     * @param
     * @param contextId
     * @description: 确认申报
     * @author: Moore
     * @date: 2025/8/5 17:15
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @Operation(summary = "查询FBA送达时间区间")
    @GetMapping("/fba/deliveryTime/select")
    public ApiResponseResult deliveryTimeSelect(@RequestParam Integer contextId,@RequestParam Long taskId, @RequestParam String shipmentId ) {
        return ApiResponseResult.buildSuccessResult( marStaTaskInfoService.deliveryTimeSelect(contextId,taskId, shipmentId));
    }


    @Operation(summary = "确认送达时间")
    @GetMapping("/fba/confirmDeliveryWindowOptions")
    public ApiResponseResult confirmDeliveryWindowOptions(@RequestParam Integer contextId,@RequestParam String inboundPlanId,
                                                          @RequestParam String shipmentId ,
                                                          @RequestParam String deliveryWindowOptionId) {
        return ApiResponseResult.buildSuccessResult(marStaTaskInfoService.confirmDeliveryWindowOptions(contextId, inboundPlanId,shipmentId,deliveryWindowOptionId));
    }


    @Operation(summary = "确认FBA承运商信息")
    @PostMapping("/fba/confirmTransportationOptions")
    public ApiResponseResult confirmTransportationOptions(@RequestParam Integer contextId,@RequestBody List<FbaConfirmCarrierDTO> fbaConfirmCarrierDTOS) {
        return ApiResponseResult.buildSuccessResult( marStaTaskInfoService.confirmTransportationOptions(contextId, fbaConfirmCarrierDTOS));
    }


    @Operation(summary = "下一步节点信息")
    @GetMapping("/fba/node/next/step")
    public ApiResponseResult udpateNodeNextstep(@RequestParam Integer contextId,@RequestParam Long taskId) {
        marStaTaskInfoService.udpateNodeNextstep(contextId, taskId);
        return ApiResponseResult.buildSuccessResult();
    }



    /** 获取FBA托盘明细信息
     *
     * @param contextId
     * @param fbaId
     * @return
     */
    @Operation(summary = "获取FBA托拍明细信息")
    @GetMapping("/fba/pallet/info")
    public ApiResponseResult getFballectInfo(@RequestParam Integer contextId,  @RequestParam Long fbaId) {
        MarFbaPalletInfoVO resFbaPalletInfo = new MarFbaPalletInfoVO();
        MarFbaShipmentInfo marFbaShipmentInfo = marFbaShipmentInfoService.getById(fbaId);
        if (marFbaShipmentInfo!=null){
            resFbaPalletInfo.setFreightLevel(marFbaShipmentInfo.getFreightLevel());
            resFbaPalletInfo.setDeclareValue(marFbaShipmentInfo.getDeclareValue());
            resFbaPalletInfo.setDeclareCurrency(marFbaShipmentInfo.getDeclareCurrency());
        }
        resFbaPalletInfo.setMarFbaPalletDetails(marFbaPalletDetailService.lambdaQuery().eq(MarFbaPalletDetail::getFbaId, fbaId).list());
        return ApiResponseResult.buildSuccessResult(resFbaPalletInfo);
    }






    /** 查询打印FNsku信息
     *
     * @param contextId
     * @param fbaFnskuPrintDTO
     * @return
     */
    @Operation(summary = "打印FnSku")
    @PostMapping("/fba/print/Fnsku")
    public ApiResponseResult getFbaFnskuInfp(@RequestParam Integer contextId,@RequestBody FbaFnskuPrintDTO fbaFnskuPrintDTO) {
        List<MarFnSkuPrintVO> marFnSkuPrintVOS = marStaTaskInfoService.printFnskuSelect(contextId, fbaFnskuPrintDTO);
        return ApiResponseResult.buildSuccessResult(marFnSkuPrintVOS);
    }


    /** 获取tracking信息回显
     *
     * @param contextId
     * @param fbaId
     * @return
     */
    @GetMapping("/fba/box/Tracking")
    public ApiResponseResult getBoxTrackNo(@RequestParam Integer contextId,  @RequestParam Long fbaId) {
        List<MarFbaCartonSpec> marFbaCartonSpecList = marFbaCartonSpecService.lambdaQuery().
                eq(MarFbaCartonSpec::getShipmentBusinessId, fbaId).
                 orderByAsc(MarFbaCartonSpec::getBoxId).list();
        return ApiResponseResult.buildSuccessResult(marFbaCartonSpecList);
    }



    /** 更新traking
     *
     * @param contextId
     * @param shipmentId  货件ID
     * @param fbaUpdateTrackingDTO  FAB箱号相关tracking信息
     * @return
     */
    @PostMapping("/fba/box/Tracking/update")
    public ApiResponseResult getBoxTrackNo(@RequestParam Integer contextId, @RequestParam String shipmentId, @RequestBody FbaUpdateTrackingDTO fbaUpdateTrackingDTO) {
        List<MarFbaCartonSpec> marFbaCartonSpecs = fbaUpdateTrackingDTO.getMarFbaCartonSpecs();
        return ApiResponseResult.buildSuccessResult(marStaTaskInfoService.updateBoxTraking(shipmentId,marFbaCartonSpecs));
    }



    /**
     *  设置FBA  BOL标、跟踪号
     * @param contextId
     * @param marFbaShipmentInfo
     * @return
     */
    @Operation(summary = "设置FBA BOL表及跟踪号")
    @PostMapping("/fba/pallet/set/bol/pro")
    public ApiResponseResult setFbaPalletBolAndPro(@RequestParam Integer contextId,@RequestBody MarFbaShipmentInfo marFbaShipmentInfo) {
        marStaTaskInfoService.setFbaPalletBolAndPro(contextId, marFbaShipmentInfo);
        return ApiResponseResult.buildSuccessResult();
    }


    /** 手动刷新货件状态
     *
     * @param contextId
     * @return
     */
    @GetMapping("/manual/fba/status")
    public ApiResponseResult refresh(@RequestParam Integer contextId) {
        marStaTaskInfoService.refreshFbaStatusJob(contextId);
        return ApiResponseResult.buildSuccessResult();
    }

}
