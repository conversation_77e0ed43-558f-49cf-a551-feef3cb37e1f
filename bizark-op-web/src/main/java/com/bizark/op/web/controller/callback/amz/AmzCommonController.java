package com.bizark.op.web.controller.callback.amz;

import com.bizark.op.api.entity.op.mar.vo.MarBestSellerVo;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.entity.op.sale.vo.WalmartAsinVo;
import com.bizark.op.api.service.amazon.AmazonVineInfoService;
import com.bizark.op.api.service.sale.ProductChannelsService;
import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.op.api.vo.mar.ListingVo;
import com.bizark.op.common.core.page.TableDataInfo;
import com.bizark.op.service.mapper.mar.MarBestSellerUrlMapper;
import com.bizark.op.web.controller.api.AbstractApiController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @ClassName AmzAsinReviewController
 * @description: Amz相关数据接口
 * @date 2023年09月21日
 */
@RestController
@RequestMapping(value = "/callback/amz/api"
        , method = {RequestMethod.GET, RequestMethod.HEAD, RequestMethod.POST}
        , consumes = {"text/xml;charset=utf-8", MediaType.ALL_VALUE, MediaType.TEXT_XML_VALUE, MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.APPLICATION_JSON_UTF8_VALUE, MediaType.APPLICATION_JSON_VALUE}
)
public class AmzCommonController  extends AbstractApiController {


    @Autowired
    private ProductChannelsService productChannelsService;
    
    @Autowired
    private MarBestSellerUrlMapper marBestSellerUrlMapper;

    @Autowired
    private AmazonVineInfoService amazonVineInfoService;


    /**
     * @description: 此接口暂为抓取listing及抓取恒宁组织review使用
     * @author: Moore
     * @date: 2025/4/24 14:19
     * @param
     * @param contextId
     * @return: com.bizark.op.common.core.page.TableDataInfo
    **/
    @GetMapping("/asin/listing")
    public TableDataInfo getAsinListing(@RequestParam(value = "contextId") Integer contextId) {
        startPage();
        List<ListingVo> asinList = productChannelsService.selectAsinListingAll(contextId);
        return getDataTable(asinList);
    }

    /**
     * Description: walmart及walmart_dsv商品id查询，暂仅返回1000049组织信息,前台抓取review使用
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/7/23
     */
    @GetMapping("/walmart/itemId")
    public ApiResponseResult getWalmartItemId() {
        List<WalmartAsinVo> asinList = productChannelsService.selectWalmartItemId();
        return ApiResponseResult.buildSuccessResult(asinList);
    }

    /**
     * @description: 1000049组织amz评论接口
     * @author: Moore
     * @date: 2025/4/24 13:52
     * @param
     * @return: com.bizark.framework.web.view.ApiResponseResult
    **/
    @GetMapping("/asin/review/listing")
    public ApiResponseResult getAsinReviewListing() {
        return ApiResponseResult.buildSuccessResult(productChannelsService.selectReviewAsinList());
    }

    
    /**
     * Description: 传递SKU映射类目并抓取类目前3页的文案及评论+
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2025/4/2
     */
    @GetMapping("/get/marBestSellerUrlInfo")
    public TableDataInfo getMarBestSellerUrlInfo() {
        startPage();
        List<MarBestSellerVo> marBestSellerVos= marBestSellerUrlMapper.getMarBestSellerUrlWithLisingInfoList();
        return getDataTable(marBestSellerVos);
    }
    


    @GetMapping("/vin/parentAsin")
    public ApiResponseResult getAsinListing() {
        return ApiResponseResult.buildSuccessResult(amazonVineInfoService.vinNotConcludedCallback());
    }


}
