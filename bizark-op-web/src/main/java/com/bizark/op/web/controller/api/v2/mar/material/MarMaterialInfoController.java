package com.bizark.op.web.controller.api.v2.mar.material;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.bizark.common.exception.CommonException;
import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.op.api.cons.PermDefine;
import com.bizark.op.api.dto.material.UpdateMaterialNameDTO;
import com.bizark.op.api.dto.sale.TiktokProductQueryDTO;
import com.bizark.op.api.enm.mar.mater.VisualStyleEnum;
import com.bizark.op.api.enm.tabcut.TabcutTagBussinessTypeEnum;
import com.bizark.op.api.entity.op.mar.material.MarMaterialCenterInfo;
import com.bizark.op.api.entity.op.mar.material.MarMaterialMakeInfo;
import com.bizark.op.api.entity.op.mar.material.MarMaterialOpLog;
import com.bizark.op.api.entity.op.mar.material.MarMaterialReleaseInfo;
import com.bizark.op.api.entity.op.mar.vo.MarMaterialUploadDTO;
import com.bizark.op.api.entity.op.sale.ProductChannels;
import com.bizark.op.api.entity.op.tabcut.TabcutVideoTag;
import com.bizark.op.api.form.mar.material.MarMaterialQuery;
import com.bizark.op.api.service.mar.material.MarMaterialCenterInfoService;
import com.bizark.op.api.service.mar.material.MarMaterialMakeInfoService;
import com.bizark.op.api.service.mar.material.MarMaterialOpLogService;
import com.bizark.op.api.service.mar.material.MarMaterialReleaseInfoService;
import com.bizark.op.api.service.sale.ProductChannelsService;
import com.bizark.op.api.service.tabcut.TabcutVideoTagService;
import com.bizark.op.common.core.page.TableDataInfo;
import com.bizark.op.common.util.ValidateUtil;
import com.bizark.op.web.controller.api.AbstractApiController;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @ClassName MarMaterialInfoController
 * @description: 素材管理
 * @date 2025年02月10日
 */
@RestController
@RequestMapping("/api/v2/mar/material")
public class MarMaterialInfoController extends AbstractApiController {


    @Autowired
    private MarMaterialCenterInfoService marMaterialCenterInfoService;

    @Autowired
    private ProductChannelsService productChannelsService;

    @Autowired
    private MarMaterialOpLogService marMaterialOpLogService;


    @Autowired
    private MarMaterialMakeInfoService marMaterialMakeInfoService;

    @Autowired
    private TabcutVideoTagService tabcutVideoTagService;

    @Autowired
    private MarMaterialReleaseInfoService marMaterialReleaseInfoService;

    /**
     * @param
     * @param query
     * @param contextId
     * @description: 素材中心列表查询
     * @author: Moore
     * @date: 2025/2/10 17:48
     * @return: com.bizark.op.common.core.page.TableDataInfo<com.bizark.op.api.vo.mar.MarAppealListVo>
     **/
    @PostMapping("/center/list")
    @RequiresPermissions(value= {PermDefine.MATERIAL_MATERIALCENTER})
    public TableDataInfo<MarMaterialCenterInfo> queryMaterialCenterList(@RequestBody MarMaterialQuery query, @RequestParam(value = "contextId") Integer contextId) {
        query.setOrgId(contextId);
        startPage();
        List<MarMaterialCenterInfo> list = marMaterialCenterInfoService.queryMaterialCenterList(query);
        //设置tag行信息
        if (!CollectionUtils.isEmpty(list)) {
            List<MarMaterialCenterInfo> tagsMaterIalInfo = list.stream().filter(item -> !StringUtils.isEmpty(item.getMaterialTag())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(tagsMaterIalInfo)) {
                List<String> tagIds = tagsMaterIalInfo.stream().map(MarMaterialCenterInfo::getMaterialTag).flatMap(tagids -> Arrays.stream(tagids.split(","))).distinct().collect(Collectors.toList());
                List<TabcutVideoTag> tagsList = tabcutVideoTagService.lambdaQuery().in(TabcutVideoTag::getId, tagIds).eq(TabcutVideoTag::getTagBusinessType, TabcutTagBussinessTypeEnum.MATERIAL_INFO.getValue()).list();
                tagsMaterIalInfo.forEach(item -> {
                    Set<Long> idSet = Arrays.stream(item.getMaterialTag().split(","))
                            .map(Long::parseLong)
                            .collect(Collectors.toSet());
                    item.setTabcutVideoTagList(tagsList.stream().filter(tags -> idSet.contains(tags.getId())).collect(Collectors.toList()));
                });
            }
        }
        return getDataTable(list);
    }


    /**
     * @description:素材制作查询
     * @author: Moore
     * @date: 2025/2/12 14:20
     * @param
     * @param query
     * @param contextId
     * @return: com.bizark.op.common.core.page.TableDataInfo<com.bizark.op.api.entity.op.mar.material.MarMaterialCenterInfo>
     **/
    @PostMapping("/make/list")
    @RequiresPermissions(value= {PermDefine.MATERIAL_MATERIALMAKE})
    public TableDataInfo<MarMaterialMakeInfo> queryMaterialMarkList(@RequestBody MarMaterialQuery query, @RequestParam(value = "contextId") Integer contextId) {
        query.setOrgId(contextId);
        query.settingQueryParam();
        startPage();
        List<MarMaterialMakeInfo> list = marMaterialMakeInfoService.queryMaterialMarkList(query);
        return getDataTable(list);
    }



    /**
     * @param
     * @param query
     * @param contextId
     * @description: 素材分析查询
     * @author: Moore
     * @date: 2025/2/10 17:48
     * @return: com.bizark.op.common.core.page.TableDataInfo<com.bizark.op.api.vo.mar.MarAppealListVo>
     **/
    @PostMapping("/analyse/list")
    @RequiresPermissions(value= {PermDefine.MATERIAL_MATERIALANLYSIS})
    public TableDataInfo<MarMaterialCenterInfo> queryMaterialAnalyseList(@RequestBody MarMaterialQuery query, @RequestParam(value = "contextId") Integer contextId) {
        query.setOrgId(contextId);
        startPage();
        query.settingQueryParam();
        query.setAnalyseFlag("Y");
        List<MarMaterialCenterInfo> list = marMaterialCenterInfoService.queryMaterialCenterList(query);
        //设置tag行信息
        if (!CollectionUtils.isEmpty(list)) {
            List<MarMaterialCenterInfo> tagsMaterIalInfo = list.stream().filter(item -> !StringUtils.isEmpty(item.getMaterialTag())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(tagsMaterIalInfo)) {
                List<String> tagIds = tagsMaterIalInfo.stream().map(MarMaterialCenterInfo::getMaterialTag).flatMap(tagids -> Arrays.stream(tagids.split(","))).distinct().collect(Collectors.toList());
                List<TabcutVideoTag> tagsList = tabcutVideoTagService.lambdaQuery().in(TabcutVideoTag::getId, tagIds).eq(TabcutVideoTag::getTagBusinessType, TabcutTagBussinessTypeEnum.MATERIAL_INFO.getValue()).list();
                tagsMaterIalInfo.forEach(item -> {
                    Set<Long> idSet = Arrays.stream(item.getMaterialTag().split(","))
                            .map(Long::parseLong)
                            .collect(Collectors.toSet());
                    item.setTabcutVideoTagList(tagsList.stream().filter(tags -> idSet.contains(tags.getId())).collect(Collectors.toList()));
                });
            }
        }
        return getDataTable(list);
    }

    /**
     * @param
     * @param query
     * @param contextId
     * @description: 发布记录
     * @author: Moore
     * @date: 2025/2/10 17:48
     * @return: com.bizark.op.common.core.page.TableDataInfo<com.bizark.op.api.vo.mar.MarAppealListVo>
     **/
    @PostMapping("/release/list")
    public TableDataInfo<MarMaterialReleaseInfo> queryMaterialReleaseList(@RequestBody MarMaterialQuery query, @RequestParam(value = "contextId") Integer contextId) {
        query.setOrgId(contextId);
        query.settingQueryParam();
        startPage();
        return getDataTable( marMaterialReleaseInfoService.queryMaterialReleaseList(query));
    }



    /**
     * @param
     * @param query
     * @param contextId
     * @description:素材中心导出
     * @author: Moore
     * @date: 2025/2/10 19:17
     * @return: com.bizark.framework.web.view.ApiResponseResult
     **/
    @PostMapping("/center/list/export")
    @RequiresPermissions(value= {PermDefine.MATERIAL_MATERIALCENTER})
    public ApiResponseResult requestExport(@RequestBody MarMaterialQuery query, @RequestParam("contextId") Integer contextId) {
        query.setOrgId(contextId);
        query.settingQueryParam();
        query.setExportFlag("Y");
        marMaterialCenterInfoService.exportmaterialCenterList(query, getAuthUserEntity());
//        marMaterialCenterInfoService.asyncExportmaterialCenterList(JSONObject.toJSONString(query));
        return ApiResponseResult.buildSuccessResult("导出已生成，请至【任务中心】-【任务列表】查看处理进度及下载文件！" );
    }


    /**
     * @description:素材制作查询
     * @author: Moore
     * @date: 2025/2/12 14:20
     * @param
     * @param query
     * @param contextId
     * @return: com.bizark.op.common.core.page.TableDataInfo<com.bizark.op.api.entity.op.mar.material.MarMaterialCenterInfo>
     **/
    @PostMapping("/make/list/export")
    @RequiresPermissions(value= {PermDefine.MATERIAL_MATERIALMAKE})
    public ApiResponseResult  exportMakeList(@RequestBody MarMaterialQuery query, @RequestParam(value = "contextId") Integer contextId) {
        query.setOrgId(contextId);
        query.settingQueryParam();
       marMaterialMakeInfoService.exportmaterialMakeList(query, getAuthUserEntity());
//        String url = marMaterialMakeInfoService.asyncExportMakeList(JSONObject.toJSONString(query));
        return ApiResponseResult.buildSuccessResult("导出已生成，请至【任务中心】-【任务列表】查看处理进度及下载文件！");
    }


    /**
     * 素材分析导出
     *
     * @param query
     * @param contextId
     * @return
     */
    @PostMapping("/analyse/list/export")
    @RequiresPermissions(value= {PermDefine.MATERIAL_MATERIALANLYSIS})
    public ApiResponseResult exportMaterialAnalyseList(@RequestBody MarMaterialQuery query, @RequestParam(value = "contextId") Integer contextId) {
        query.setOrgId(contextId);
        query.settingQueryParam();
        query.setAnalyseFlag("Y");
        query.setExportFlag("Y");
       marMaterialCenterInfoService.exportMaterialAnalyseList(query, getAuthUserEntity());
//        String url =marMaterialCenterInfoService.asyncExportmaterialAnalyseList(JSONObject.toJSONString(query));
        return ApiResponseResult.buildSuccessResult("导出已生成，请至【任务中心】-【任务列表】查看处理进度及下载文件！");
    }


    /**
     * @param
     * @param contextId
     * @param materialNum 素材编号，对于素材中心为素材编号，对于素材制作为生成ID
     * @description: 素材操作日志
     * @author: Moore
     * @date: 2025/2/10 19:33
     * @return: com.bizark.op.common.core.page.TableDataInfo<com.bizark.op.api.entity.op.mar.material.MarMaterialOpLog>
     **/
    @GetMapping("/common/log")
    public TableDataInfo<MarMaterialOpLog> getLoginfo(@RequestParam("contextId") Integer contextId, @RequestParam("materialNum") String materialNum) {
        startPage();
        List<MarMaterialOpLog> list = marMaterialOpLogService.lambdaQuery()
                .eq(MarMaterialOpLog::getOrgId, contextId).eq(MarMaterialOpLog::getMaterialNum, materialNum).
                        orderByDesc(MarMaterialOpLog::getCreatedAt).list();
        return getDataTable(list);
    }


    /**
     * @description: 上传附件 图片或视频
     * @author: Moore
     * @date: 2025/2/11 16:33
     * @param
     * @param marMaterialUploadDTO
     * @param contextId
     * @param contentType  IMAGE ，VIDEO
     * @param opType  single 单个 ，batch批量
     * @return: com.bizark.framework.web.view.ApiResponseResult
    **/
    @PostMapping("/upload/file")
    @RequiresPermissions(value= {PermDefine.MATERIAL_UPDATE_VIDEO,PermDefine.MATERIAL_UPDATE_IMAGE},logical= Logical.OR)
    public ApiResponseResult getLoginfo(@RequestBody  List<MarMaterialUploadDTO> marMaterialUploadDTO,
                                        @RequestParam("contentType") String contentType,
                                        @RequestParam(value = "opType",required = false,defaultValue = "single") String opType,
                                        @RequestParam("contextId") Integer contextId) {
        marMaterialCenterInfoService.uploadFile(marMaterialUploadDTO, contentType,opType,contextId);
        return ApiResponseResult.buildSuccessResult();
    }




    /**
     *  记录素材下载次数
     * @param contextId
     * @param materialNum
     * @return
     */
    @GetMapping("/download/add")
    public ApiResponseResult downloadFlag(@RequestParam("contextId") Integer contextId,
                                          @RequestParam("materialNum") String materialNum
                                          ) {
        UpdateWrapper<MarMaterialCenterInfo> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("material_num", materialNum)
                .setSql("download_total = download_total + 1");
        marMaterialCenterInfoService.update(updateWrapper);
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * 根据ERPSKU 查型号
     *
     * @param contextId
     * @param erpSku
     * @return
     */
    @GetMapping("/query/model")
    public ApiResponseResult queryModel(@RequestParam("contextId") Integer contextId, @RequestParam("erpSKu") String erpSku) {
        return ApiResponseResult.buildSuccessResult( marMaterialCenterInfoService.queryErpskuModel(erpSku,contextId));
    }




    /**
     * @description:删除素材信息
     * @author: Moore
     * @date: 2025/2/12 14:17
     * @param
     * @param ids
     * @param contextId
     * @return: com.bizark.framework.web.view.ApiResponseResult
    **/
    @PostMapping("/delete")
    @RequiresPermissions(value= {PermDefine.MATERIAL_VIDEODELETE,PermDefine.MATERIAL_PICTUREDELETE},logical= Logical.OR)
    public ApiResponseResult getLoginfo(@RequestBody List<Long> ids,
                                        @RequestParam("contextId") Integer contextId) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new CommonException("请选择删除素材！");
        }
        marMaterialCenterInfoService.removeByIds(ids);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * 视觉风格下拉框
     * @return
     */
    @GetMapping("/getVisualStyle")
    public ApiResponseResult getVisualStyle() {
        HashMap<String, List<JSONObject>> resJsonMap = new HashMap<>();
        Map<String, List<String>> map = VisualStyleEnum.map;
        for (String name : map.keySet()) {
            List<JSONObject> resJson = new ArrayList<>();
            for (String item : map.get(name)) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("label", item);
                jsonObject.put("value", item);
                resJson.add(jsonObject);
            }
            resJsonMap.put(name, resJson);
        }
        JSONObject jsonObjectOne = new JSONObject();
        jsonObjectOne.put("label", "无数字人");
        jsonObjectOne.put("value", "Unnumbered_man");
        jsonObjectOne.put("children", resJsonMap.get("无数字人"));
        JSONObject jsonObjectTwo = new JSONObject();
        jsonObjectTwo.put("label", "有数字人");
        jsonObjectTwo.put("value", "Man_with_numbers");
        jsonObjectTwo.put("children", resJsonMap.get("有数字人"));
        return ApiResponseResult.buildSuccessResult(Arrays.asList(jsonObjectOne, jsonObjectTwo));
    }


    @GetMapping("/initMateriarTaskCenter")
    public ApiResponseResult initMateriarTaskCenter() {
        marMaterialCenterInfoService.initMateriarTaskCenter();
        return ApiResponseResult.buildSuccessResult();
    }


    @PostMapping("/selectTiktokProductList")
    public TableDataInfo selectTiktokProductList(@RequestBody TiktokProductQueryDTO dto, @RequestParam("contextId") Integer contextId) {

        startPage();
        dto.queryParamSetting();
        dto.setContextId(contextId);
        List<ProductChannels> channels = productChannelsService.selectTiktokProductList(dto);
        return getDataTable(channels);
    }


    @PostMapping("/updateMaterialName")
    public ApiResponseResult updateMaterialName(@RequestBody UpdateMaterialNameDTO dto) {
        ValidateUtil.beanValidate(dto);
        marMaterialCenterInfoService.updateMaterialName(dto);
        return ApiResponseResult.buildSuccessResult();
    }


}