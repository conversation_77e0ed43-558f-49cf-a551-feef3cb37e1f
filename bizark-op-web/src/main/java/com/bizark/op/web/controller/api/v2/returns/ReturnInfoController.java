package com.bizark.op.web.controller.api.v2.returns;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bizark.op.api.cons.PermDefine;
import com.bizark.op.api.enm.sale.AccountSaleChannelEnum;
import com.bizark.op.api.entity.op.amazon.AmazonCustomerReturn;
import com.bizark.op.api.entity.op.amazon.AmazonOrderReturn;
import com.bizark.op.api.entity.op.returns.entity.ReturnInfoEntity;
import com.bizark.op.api.entity.op.returns.entity.VendorSalesReportInfo;
import com.bizark.op.api.entity.op.returns.other.OverstockReturnInfo;
import com.bizark.op.api.entity.op.returns.other.VcDfReturnInfo;
import com.bizark.op.api.entity.op.returns.other.VcDfReturnInvoiceInfo;
import com.bizark.op.api.entity.op.returns.other.VcUspoReturnInfo;
import com.bizark.op.api.entity.op.returns.tiktok.TiktokReturnRefund;
import com.bizark.op.api.entity.op.returns.walmart.ReturnOrder;
import com.bizark.op.api.entity.op.ticket.VO.ReturnInfoAnalysisVo;
import com.bizark.op.api.entity.op.ticket.VO.ReturnInfoEntityVo;
import com.bizark.op.api.entity.op.ticket.customercomplaint.query.CustomerComplaintMainQuery;
import com.bizark.op.api.service.RpcMarExportService;
import com.bizark.op.api.service.amazon.AmazonReturnService;
import com.bizark.op.api.service.returns.IOrderRefundRequestInfoConfigurationService;
import com.bizark.op.api.service.returns.ReturnInfoConfigurationService;
import com.bizark.op.api.service.returns.ReturnInfoService;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.service.util.ExcelExport;
import com.bizark.op.web.controller.api.AbstractApiController;
import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import com.github.pagehelper.PageInfo;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023年9月19日  14:56
 * @description: 退货信息
 */
@RestController("returnInfoController")
@RequestMapping(value = "/api/v2/return/info")
public class ReturnInfoController extends AbstractApiController {

    @Autowired
    ReturnInfoService returnInfoService;

    @Autowired
    AmazonReturnService amazonReturnService;

    @Autowired
    ReturnInfoConfigurationService returnInfoConfigurationService;

    @Autowired
    private RpcMarExportService rpcMarExportService;

    @Autowired
    private IOrderRefundRequestInfoConfigurationService orderRefundRequestInfoConfigurationService;


    @GetMapping("/dict/saleChannel")
    public ApiResponseResult getReturnInfoChannle() {
        QueryWrapper<ReturnInfoEntity> productsQueryWrapper = new QueryWrapper<>();
        productsQueryWrapper.select("DISTINCT  channel ");
        productsQueryWrapper.and(i -> i.and(j -> j.isNotNull("channel").or(k -> k.ne("channel",""))));
        List<ReturnInfoEntity> listChild = returnInfoService.list(productsQueryWrapper);
        JSONArray dict = new JSONArray();
        for (ReturnInfoEntity returnInfoEntity : listChild) {
            JSONObject item = new JSONObject();
            item.put("dictValue", returnInfoEntity.getChannel());
            item.put("dictLabel", AccountSaleChannelEnum.getName(returnInfoEntity.getChannel()));
            dict.add(item);
        }
        return ApiResponseResult.buildSuccessResult(dict);
    }

    /**
     * 退货信息分页
     *
     * @param returnInfoEntityVo
     * @return
     */
    @PostMapping("getReturnInfoPageList")
    @RequiresPermissions(PermDefine.MAR_RETURN_LIST)
    public ApiResponseResult getReturnInfoPageList(
            @RequestBody ReturnInfoEntityVo returnInfoEntityVo,
            @RequestParam(value = "contextId") Integer contextId

    ) {
        returnInfoEntityVo.setContextId(contextId);
        return ApiResponseResult.buildSuccessResult(new PageInfo<>(returnInfoService.getReturnInfoPageList(returnInfoEntityVo)));
    }

    /**
     * 退货信息导出
     *
     * @param contextId
     * @param returnInfoEntityVo
     * @param httpServletResponse
     * @throws IllegalAccessException
     * @throws UnsupportedEncodingException
     */
    @PostMapping("returnInfoExcelExport")
    @RequiresPermissions(PermDefine.MAR_RETURN_EXPORT)
    public ApiResponseResult returnInfoExcelExport(
            @RequestBody ReturnInfoEntityVo returnInfoEntityVo,
            @RequestParam(value = "contextId") Integer contextId,
            HttpServletResponse httpServletResponse) {
        returnInfoEntityVo.setContextId(contextId);
        returnInfoService.returnInfoListExport(httpServletResponse, returnInfoEntityVo, getAuthUserEntity());
        return ApiResponseResult.buildSuccessResult("导出已生成，请至【任务中心】-【任务列表】查看处理进度及下载文件！");
    }

    /**
     * 更新备注
     *
     * @param contextId
     * @param id
     * @param remark
     * @return
     */
    @PostMapping("updateRemark")
    public ApiResponseResult updateRemark(@RequestParam(value = "contextId") Integer contextId,
                                          @RequestParam(value = "id") Integer id,
                                          @RequestParam(value = "remark") String remark) {
        returnInfoService.updateRemark(contextId, id, remark);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * 退货原因分页
     *
     * @param contextId
     * @param pageSize
     * @param pageNum
     * @param returnReason
     * @return
     */
    @GetMapping("getReturnReasonPage")
    @RequiresPermissions(PermDefine.MAR_RETURN_CONFIG)
    public ApiResponseResult getReturnReasonPage(
            @RequestParam(value = "contextId") Integer contextId,
            @RequestParam(value = "pageSize", defaultValue = "10", required = false) Integer pageSize,
            @RequestParam(value = "pageNum", defaultValue = "1", required = false) Integer pageNum,
            @RequestParam(value = "returnReason", required = false) String returnReason
    ) {
        return ApiResponseResult.buildSuccessResult(new PageInfo<>(returnInfoConfigurationService.getReturnInfoConfigurationPage(contextId, pageSize, pageNum, returnReason)));
    }

    /**
     * 更新退货原因分类
     *
     * @param contextId
     * @param cofigId
     * @param returnReasonCategory
     * @return
     */
    @PostMapping("updateReturnReasonCategory")
    @RequiresPermissions(PermDefine.MAR_RETURN_CONFIG)
    public ApiResponseResult updateReturnReasonCategory(
            @RequestParam(value = "contextId") Integer contextId,
            @RequestParam(value = "returnReason") String returnReason,
            @RequestParam(value = "returnReasonCategory") String returnReasonCategory,
            @RequestParam(value = "cofigId",required = false) Long cofigId,
            @RequestParam(value = "returnReasonCategoryName") String returnReasonCategoryName
    ) {
        returnInfoConfigurationService.updateReturnReasonCategory(contextId, returnReason, returnReasonCategory,returnReasonCategoryName,cofigId);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * 退货数据接口新增
     *
     * @param data
     * @param type
     * @return
     */
    @PostMapping("returnData")
    public ApiResponseResult returnData(@RequestBody String data,
            @RequestParam(value = "type") Integer type
    ){
        if(StringUtils.isNotEmpty(data)){
            if(null != type){
                switch (type){
                    case 1:
                        List<ReturnOrder> returnOrderList = JSON.parseArray(data, ReturnOrder.class);
                        if (CollectionUtil.isNotEmpty(returnOrderList)) {
                            amazonReturnService.insertWalmartReturn(returnOrderList);
                        }
                        break;
                    case 2:
                        List<TiktokReturnRefund> tiktokReturnRefunds = JSON.parseArray(data, TiktokReturnRefund.class);
                        if (CollectionUtil.isNotEmpty(tiktokReturnRefunds)) {
                            amazonReturnService.tiktokReturnDataDispose(tiktokReturnRefunds);
                        }
                        break;
                    case 3:
                        List<VendorSalesReportInfo> vendorSalesReportInfoList = JSON.parseArray(data, VendorSalesReportInfo.class);
                        if (CollectionUtil.isNotEmpty(vendorSalesReportInfoList)) {
                            amazonReturnService.insertVendorSalesReport(vendorSalesReportInfoList);
                        }
                        break;
                    case 4:
                        AmazonOrderReturn amazonOrderReturn = new JSONObject(data).toBean(AmazonOrderReturn.class);
                        if (null != amazonOrderReturn) {
                            amazonReturnService.insertFBMReturnData(amazonOrderReturn);
                        }
                        break;
                    case 5:
                        JSONObject jsonObject = new JSONObject(data);
                        Map<String, Object> map = new HashMap<>();
                        jsonObject.keySet().forEach(key -> map.put(key.toUpperCase(), jsonObject.get(key)));
                        JSONObject result = new JSONObject(map);
                        AmazonCustomerReturn amazonCustomerReturn = result.toBean(AmazonCustomerReturn.class);
                        if (null != amazonCustomerReturn) {
                            amazonReturnService.insertFBAReturnData(amazonCustomerReturn);
                        }
                        break;
                    case 6:
                        OverstockReturnInfo overstockReturnInfo = new JSONObject(data).toBean(OverstockReturnInfo.class);
                        if (null != overstockReturnInfo) {
                            amazonReturnService.insertOverstockReturnData(overstockReturnInfo);
                        }
                        break;
                    case 7:
                        VcUspoReturnInfo vcUspoReturnInfo = new JSONObject(data).toBean(VcUspoReturnInfo.class);
                        if (null != vcUspoReturnInfo) {
                            amazonReturnService.insertVcUspoReturnInfo(vcUspoReturnInfo);
                        }
                        break;
                    case 8:
                        VcDfReturnInfo vcDfReturnInfo = new JSONObject(data).toBean(VcDfReturnInfo.class);
                        if (null != vcDfReturnInfo) {
                            amazonReturnService.insertVcDfReturnInfo(vcDfReturnInfo);
                        }
                        break;
                    case 9:
                        VcDfReturnInvoiceInfo vcDfReturnInvoiceInfo = new JSONObject(data).toBean(VcDfReturnInvoiceInfo.class);
                        if (null != vcDfReturnInvoiceInfo) {
                            amazonReturnService.insertVcDfReturnInvoiceInfo(vcDfReturnInvoiceInfo);
                        }
                        break;
                }
            }
        }
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * Description: 获取退货信息，退货原因
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/9/27
     */
    @GetMapping("/dict/returnReason")
    public ApiResponseResult getReturnInfoReturnReason(@RequestParam(value = "contextId") Integer contextId) {
        QueryWrapper<ReturnInfoEntity> productsQueryWrapper = new QueryWrapper<>();
        productsQueryWrapper.select("DISTINCT  return_reason ");
        productsQueryWrapper.eq("organization_id",contextId);
        productsQueryWrapper.and(i -> i.and(j -> j.isNotNull("return_reason").or(k -> k.ne("return_reason",""))));
        List<ReturnInfoEntity> listChild = returnInfoService.list(productsQueryWrapper);
        JSONArray dict = new JSONArray();
        for (ReturnInfoEntity returnInfoEntity : listChild) {
            JSONObject item = new JSONObject();
            item.put("dictValue", returnInfoEntity.getReturnReason());
            item.put("dictLabel",  returnInfoEntity.getReturnReason());
            dict.add(item);
        }
        return ApiResponseResult.buildSuccessResult(dict);
    }


    /**
     *  退货方式：退货方式分析导出
     *
     * @param contextId
     * @param
     * @param
     * @throws IllegalAccessException
     * @throws UnsupportedEncodingException
     */
    @GetMapping("/analysis/report")
    public ApiResponseResult analysisReport(
            ReturnInfoAnalysisVo returnInfoAnalysisVo,
            @RequestParam(value = "contextId") Long contextId) {
        returnInfoAnalysisVo.setOrganizationId(contextId);
        returnInfoService.analysisMethodReport(returnInfoAnalysisVo);
        return ApiResponseResult.buildSuccessResult("导出已生成，请至【任务中心】-【任务列表】查看处理进度及下载文件！");
    }

    /**
     * 退款原因分页
     *
     * @param contextId
     * @param pageSize
     * @param pageNum
     * @param returnReasonText
     * @return
     */
    @GetMapping("/getRefundReasonPage")
    @RequiresPermissions(PermDefine.MAR_RETURN_CONFIG)
    public ApiResponseResult getRefundReasonPage(
            @RequestParam(value = "contextId") Integer contextId,
            @RequestParam(value = "pageSize", defaultValue = "10", required = false) Integer pageSize,
            @RequestParam(value = "pageNum", defaultValue = "1", required = false) Integer pageNum,
            @RequestParam(value = "returnReasonText", required = false) String returnReasonText
    ) {
        return ApiResponseResult.buildSuccessResult(new PageInfo<>(orderRefundRequestInfoConfigurationService.getRefundRequestInfoConfigurationPage(contextId, pageSize, pageNum, returnReasonText)));
    }

    /**
     * 更新退款原因分类
     *
     * @param contextId
     * @param
     * @param
     * @return
     */
    @PostMapping("/updateRefundReasonCategory")
    @RequiresPermissions(PermDefine.MAR_RETURN_CONFIG)
    public ApiResponseResult updateRefundReasonCategory(
            @RequestParam(value = "contextId") Integer contextId,
            @RequestParam(value = "returnReasonText") String returnReasonText,
            @RequestParam(value = "returnReasonCategory") String returnReasonCategory,
            @RequestParam(value = "cofigId",required = false) Long cofigId,
            @RequestParam(value = "returnReasonCategoryName") String returnReasonCategoryName
    ) {
        orderRefundRequestInfoConfigurationService.updateOrSaveOrderRefundRequestInfoConfiguration(contextId, returnReasonText, returnReasonCategory, returnReasonCategoryName, cofigId);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * 换货信息分页
     *
     * @param returnInfoEntityVo
     * @return
     */
    @PostMapping("getReplacePageList")
    public ApiResponseResult getReplacePageList(
            @RequestBody ReturnInfoEntityVo returnInfoEntityVo,
            @RequestParam(value = "contextId") Integer contextId
    ) {
        returnInfoEntityVo.setContextId(contextId);
        return ApiResponseResult.buildSuccessResult(new PageInfo<>(returnInfoService.getReplaceInfoPageList(returnInfoEntityVo)));
    }

    /**
     * 换货信息导出
     *
     * @param contextId
     * @param returnInfoEntityVo
     * @param httpServletResponse
     * @throws IllegalAccessException
     * @throws UnsupportedEncodingException
     */
    @PostMapping("replaceInfoExcelExport")
    public ApiResponseResult replaceInfoExcelExport(
            @RequestBody ReturnInfoEntityVo returnInfoEntityVo,
            @RequestParam(value = "contextId") Integer contextId,
            HttpServletResponse httpServletResponse) {
        returnInfoEntityVo.setContextId(contextId);
        returnInfoService.returnInfoListExport(httpServletResponse, returnInfoEntityVo, getAuthUserEntity());
        //String address = rpcMarExportService.findReplaceInfoExport(JSON.toJSONString(returnInfoEntityVo));
        return ApiResponseResult.buildSuccessResult("导出已生成，请至【任务中心】-【任务列表】查看处理进度及下载文件！");
    }


}
