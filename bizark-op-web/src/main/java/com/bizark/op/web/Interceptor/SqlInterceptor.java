package com.bizark.op.web.Interceptor;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.handlers.AbstractSqlParserHandler;
import com.bizark.common.contract.AuthUserDetails;
import com.bizark.op.api.annotation.LogConvert;
import com.bizark.op.api.entity.op.log.ErpLogConf;
import com.bizark.op.api.entity.op.log.ErpOperateLog;
import com.bizark.op.api.service.log.ErpLogConfService;
import com.bizark.op.api.service.log.ErpOperateLogService;
import com.bizark.op.api.service.sys.ISysDictTypeService;
import com.bizark.op.common.core.domain.sys.SysDictData;
import com.bizark.op.common.util.DateUtils;
import com.bizark.op.common.util.spring.SpringUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.update.Update;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMap;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bizark.op.common.util.ExcelUtils.convertObj;

/**
 * <p>
 * 数据更新拦截器
 * </p>
 */
@Slf4j
@Component
@Intercepts({@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})})
public class SqlInterceptor extends AbstractSqlParserHandler implements Interceptor, ApplicationContextAware {

    private ApplicationContext applicationContext;


    @Autowired
    @Qualifier("erpJdbcTemplate")
    private JdbcTemplate adJdbcTemplate;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    private static Map<String, ErpLogConf> logCache = new ConcurrentHashMap<>();

    @Override
    public Object intercept(Invocation invocation) throws Exception {
        //方法参数
        Object[] args = invocation.getArgs();
        MappedStatement mappedStatement = (MappedStatement) args[0];
        Object parameterObject = args[1];
        SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
        String commandType = sqlCommandType.name();

        try {
            if (null != parameterObject) {
                String jsonString = JSON.toJSONString(parameterObject);
                JSONObject object = JSONObject.parseObject(jsonString);
                Boolean interceptor = false;
                JSONObject et = null;
                if (object.containsKey("et")) {
                    et = object.getJSONObject("et");
                } else if (object.containsKey("skipInterceptor")) {
                    et = object;
                }
                if (null != et) {
                    interceptor = et.getBoolean("skipInterceptor");
                    if (null != interceptor && interceptor) {
                        return invocation.proceed();
                    }
                }
            }
        } catch (Exception e) {
            return invocation.proceed();
        }

//        if (!commandType.equalsIgnoreCase("update") && !commandType.equalsIgnoreCase("insert")) {
        // 只记录修改日志
        if (!commandType.equalsIgnoreCase("update")) {
            return invocation.proceed();
        }

        if (commandType.equalsIgnoreCase("insert")) {
            Object proceed = invocation.proceed();
            try {
                String sql = mappedStatement.getSqlSource().getBoundSql(parameterObject).getSql().replace("\n", "");
                String normalTableName = sql.substring(11, sql.indexOf("(")).trim();
                String tableName = normalTableName.indexOf(".") != -1 ? normalTableName.split("\\.")[1] : normalTableName;
                if (needRecord(tableName, null)) {
                    taskExecutor.execute(() -> {
                        try {
                            ErpLogConf conf = logCache.get(tableName);
                            Class<?> clazz = parameterObject.getClass();
                            Field idField = clazz.getDeclaredField("id");
                            idField.setAccessible(true);
                            Date nowDate = DateUtils.getNowDate();
                            Object id = idField.get(parameterObject);
                            if (Objects.isNull(id)) {
                                // 重新尝试获取ID
                                String uniqueFieldName = conf.getUniqueField();
                                if (StrUtil.isNotBlank(uniqueFieldName)) {
                                    String queryTable = clazz.isAnnotationPresent(TableName.class) && StrUtil.isNotBlank(clazz.getAnnotation(TableName.class).value())
                                            ? clazz.getAnnotation(TableName.class).value() :
                                            tableName;
                                    StringBuilder builder = new StringBuilder();
                                    builder.append("select id from " + queryTable + " where ");
//                                    SqlSessionFactory sessionFactory = SpringUtils.getBean(SqlSessionFactory.class);
//                                    SqlSession sqlSession = sessionFactory.openSession();
//                                    MapperRegistry mapperRegistry = (MapperRegistry) sqlSession.getConfiguration().getMapperRegistry();
//                                    Collection<Class<?>> mappers = mapperRegistry.getMappers();
//                                    BaseMapper baseMapper = null;
//                                    for (Class<?> mapper : mappers) {
//                                        String className = clazz.getTypeName();
//                                        String typeName = mapper.getGenericInterfaces()[0].getTypeName();
//                                        if (typeName.contains(className)) {
////                                        if (typeName.substring(typeName.indexOf("<") + 1,typeName.length()-1).equalsIgnoreCase(clazz.getTypeName())) {
//                                            mapperRegistry.getMapper(mapper, sqlSession);
//                                        }
//                                    }
//                                    QueryWrapper wrapper = new QueryWrapper<>(clazz);
                                    String[] uniqueArr = uniqueFieldName.split(",");
                                    for (int i = 0; i < uniqueArr.length; i++) {
                                        Field uniqueField = clazz.getDeclaredField(uniqueArr[i]);
                                        uniqueField.setAccessible(true);
                                        TableField tableField = uniqueField.getAnnotation(TableField.class);
                                        String fieldName = tableField != null && tableField.exist() && StrUtil.isNotBlank(tableField.value())
                                                ? tableField.value()
                                                : StrUtil.toUnderlineCase(uniqueField.getName());
                                        builder.append(" `" + fieldName + "` = " + "'"+uniqueField.get(parameterObject) + "'");
                                        if (i < uniqueArr.length - 1) {
                                            builder.append(" and ");
                                        }
                                    }
                                    builder.append(" limit 1 ");
                                    Object one = adJdbcTemplate.query(builder.toString(), new BeanPropertyRowMapper<>(clazz));
                                    List dbData = null;
                                    if (Objects.nonNull(one) && one instanceof List && (CollectionUtil.isNotEmpty(dbData = (List)one))) {
                                        id = idField.get(dbData.get(0));
                                    }
                                }
                            }
                            if (id != null) {
                                AuthUserDetails userDetails = (AuthUserDetails) SecurityUtils.getSubject().getPrincipal();
                                String userName = org.springframework.util.StringUtils.isEmpty(userDetails) ? "" : userDetails.getName();
                                Long userId = org.springframework.util.StringUtils.isEmpty(userDetails) ? -1L : userDetails.getId();
                                ErpOperateLog log = new ErpOperateLog();
                                log.setCreatedAt(nowDate);
                                log.setCreatedName(userName);
                                log.setOperateAt(LocalDateTime.now());
                                log.setOperateType(2);
                                log.setOperateUserName(userName);
                                log.setCreatedBy(userId.intValue());
                                log.setOperateTable(
                                                clazz.isAnnotationPresent(TableName.class) && StrUtil.isNotBlank(clazz.getAnnotation(TableName.class).value())
                                                        ? clazz.getAnnotation(TableName.class).value() :
                                                        tableName)
                                        .setOperateName(conf.getLogName())
                                        .setLogType(conf.getLogType())
                                        .setOperateName(conf.getLogName());
                                log.setBusinessId(Long.parseLong(String.valueOf(id)));
                                applicationContext.getBean(ErpOperateLogService.class)
                                        .save(log);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    });


                }
            } catch (Exception e) {
                return proceed;
            }
            return proceed;
        }

        ParameterMap parameterMap = null;
        Class<?> type = null;
        Constructor<?> constructor = null;
        try {
            parameterMap = mappedStatement.getParameterMap();
            type = parameterMap.getType();
            constructor = type.getDeclaredConstructor();
        } catch (Exception e) {
            return invocation.proceed();
        }
        constructor.setAccessible(true);
        Object o = constructor.newInstance();
        List<?> query = null;
        String tableName = "";
        String normalTableName = "";
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<String> coutinueFields = new ArrayList<>();
        List<String> logDictList = new ArrayList<>();
        try {
            //获取拦截的Sql
            BoundSql boundSql = mappedStatement.getSqlSource().getBoundSql(parameterObject);
            String sql = getExecSql(type, dateFormat, boundSql, parameterObject);
            if (sql == null) {
                return invocation.proceed();
            }
            // 获取修改前数据
            if (!sql.contains("WHERE") && !sql.contains("where")) {
                log.error("未截取到SQL查询条件，日志记录停止！");
                return invocation.proceed();
            }

            int subIndex = sql.contains("WHERE") ? sql.lastIndexOf("WHERE") : sql.lastIndexOf("where");
            String condition = sql.substring(subIndex);
            normalTableName = getTableName(boundSql.getSql().toLowerCase());
            tableName = normalTableName;

            if (!needRecord(tableName, null)) {
                return invocation.proceed();
            }
            if (type.isAnnotationPresent(TableName.class)) {
                TableName annotation = type.getAnnotation(TableName.class);
                tableName = annotation.value();
            }
            String queryColumn = Stream.of(type.getDeclaredFields())
                    .filter(f -> (!f.isAnnotationPresent(TableField.class)) || (f.isAnnotationPresent(TableField.class) && f.getAnnotation(TableField.class).exist()))
                    .map(f -> f.isAnnotationPresent(TableField.class) ? f.getAnnotation(TableField.class).value() : f.getName())
                    .map(StrUtil::toUnderlineCase)
                    .collect(Collectors.joining(","));

            query = adJdbcTemplate.query("select " + (queryColumn.replaceAll(",{2,}", ",")) + " from " + tableName + "\t" + condition, new BeanPropertyRowMapper<>(parameterMap.getType()));
            if (query == null) {
                return invocation.proceed();
            }
            String setSql = sql.substring(sql.toLowerCase().indexOf(tableName) + tableName.length(), subIndex).trim().substring(3);

            String[] split = setSql.split(",");
            Map<String, String> mapping = new HashMap<>();
            for (String s : split) {
                String[] values = s.split("=");
                if (values.length > 1) {
                    String value = values[1].replace("'", "").trim();
                    String fieldName = StrUtil.toCamelCase(values[0].trim());
                    if (("?".equals(value) || "？".equals(value)) || !needRecord(tableName, fieldName)) {
                        continue;
                    }
                    mapping.put(StrUtil.toCamelCase(values[0].trim()), value);
                }
            }
            // 获取修改数据，构建实体

            if (CollectionUtil.isNotEmpty(mapping)) {
                Field[] fields = type.getDeclaredFields();
                for (Object before : query) {
                    for (Field field : fields) {
                        field.setAccessible(true);
                        if (field.isAnnotationPresent(LogConvert.class)) {
                            LogConvert annotation = field.getAnnotation(LogConvert.class);
                            if (!annotation.dict().isEmpty()) {
                                logDictList.add(annotation.dict());
                            }
                        }
                        if (isContinueField(field)) {
                            continue;
                        }
                        if (mapping.containsKey(field.getName()) || (field.isAnnotationPresent(TableField.class) && mapping.containsKey(field.getAnnotation(TableField.class).value()))) {
                            Object data = field.get(before);
                            Object updateData = mapping.containsKey(field.getName()) ?
                                    mapping.get(field.getName()) :
                                    mapping.get(field.getAnnotation(TableField.class).value());

                            if (data == null && updateData != null) {
                                field.set(o, convertObj(null, field, String.valueOf(updateData)));
                            } else if (data instanceof Date) {
                                Date beforeData = data == null ? null : (Date) data;
                                Date currentUpData = dateFormat.parse(String.valueOf(updateData));
                                if (data == null || DateUtil.compare(beforeData, currentUpData) != 0) {
                                    field.set(o, currentUpData);
                                }
                            } else if (data instanceof BigDecimal) {
                                BigDecimal beforeData = data == null ? null : (BigDecimal) data;
                                BigDecimal updateAmount = new BigDecimal(String.valueOf(mapping.get(field.getName())));
                                if (beforeData == null || beforeData.compareTo(updateAmount) != 0) {
                                    field.set(o, updateAmount);
                                }
                            } else if (data instanceof Boolean) {
                                if (!String.valueOf(data).equals(mapping.get(field.getName()))) {
                                    field.set(o, new Boolean(mapping.get(field.getName())));
                                } else {
                                    coutinueFields.add(field.getName());
                                }
                            } else if (data instanceof Integer) {
                                Integer beforeData = data == null ? null : (Integer) data;
                                Integer currentUpData = new Integer(mapping.get(field.getName()));
                                settingTargetField(o, type, field, beforeData, currentUpData);
                            } else if (data instanceof Long) {
                                Long beforeData = data == null ? null : (Long) data;
                                Long currentUpData = new Long(mapping.get(field.getName()));
                                settingTargetField(o, type, field, beforeData, currentUpData);
                            } else if (!String.valueOf(data).trim().equals(mapping.get(field.getName()).trim())) {
                                field.set(o, mapping.get(field.getName()));
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            return invocation.proceed();
        }
        // 构建日志数据
        if (o != null && query != null) {
            String userName = StrUtil.EMPTY;
            Long userId = 0L;
            try {
                AuthUserDetails userDetails = (AuthUserDetails) SecurityUtils.getSubject().getPrincipal();
                userName = org.springframework.util.StringUtils.isEmpty(userDetails) ? "" : userDetails.getName();
                userId = org.springframework.util.StringUtils.isEmpty(userDetails) ? -1L : userDetails.getId();
            } catch (Exception e) {
                log.error("获取当前登陆人员失败:{}", e.getMessage());
            }


            Class<?> opType = type;
            List<?> beforeDB = query;
            String finalNormalTableName = normalTableName;
            String finalTableName = tableName;
            String finalUserName = userName;
            Long finalUserId = userId;
            taskExecutor.execute(() -> {
                recordLog(opType, o, beforeDB, finalTableName,
                        finalNormalTableName, dateFormat, coutinueFields,
                        logDictList, finalUserName, finalUserId
                );
            });
        }
        return invocation.proceed();

    }

    /**
     * @param type            当前操作实体类类型
     * @param o               记录日志对象需要的数据
     * @param query           修改之前的数据
     * @param tableName       表名
     * @param normalTableName 表名(对应配置表，不带数据库名字)
     * @param dateFormat      日期格式化
     * @param coutinueFields  跳过字段
     * @param logDictList     需要映射的字典
     * @param userName        用户名
     * @param userId          用户ID
     */
    private void recordLog(Class<?> type, Object o, List<?> query,
                           String tableName, String normalTableName, SimpleDateFormat dateFormat, List<String> coutinueFields,
                           List<String> logDictList, String userName, Long userId
    ) {
        if (!needRecord(tableName, null)) {
            return;
        }
        ErpLogConf logConf = logCache.get(normalTableName);
        List<?> finalQuery = query;
        String finalTableName = tableName;
        Object finalO = o;
        Class<?> finalType = type;
        try {
            Map<String, List<SysDictData>> map = new HashMap<>();
            if (CollectionUtil.isNotEmpty(logDictList)) {
                ISysDictTypeService dictTypeService = SpringUtils.getBean(ISysDictTypeService.class);
                map = dictTypeService.selectDictDataByTypes(logDictList);
            }

            Field[] fields = finalType.getDeclaredFields();
            Field idField = finalType.getDeclaredField("id");
            idField.setAccessible(true);
            LocalDateTime now = LocalDateTime.now();
            List<ErpOperateLog> logs = new ArrayList<>();
            for (Object before : finalQuery) {
                for (Field field : fields) {
                    field.setAccessible(true);
                    if (isContinueField(field) || !needRecord(tableName, field.getName())) {
                        continue;
                    }
                    Object oData = field.get(finalO);
                    Object beforeData = field.get(before);
                    if (oData != null && !String.valueOf(oData).equals(String.valueOf(beforeData))) {
                        Schema schema = field.getAnnotation(Schema.class);
                        LogConvert logConvert = field.getAnnotation(LogConvert.class);
                        String pre = schema == null ? "" : schema.description();
                        ErpOperateLog operateLog = new ErpOperateLog();
                        operateLog.setOperateUserName(userName);
                        operateLog.setFieldDesc(pre);
                        operateLog.setOperateOldValue(buildConvertMap(map, logConvert, String.valueOf(field.get(before))));
                        operateLog.setOperateUserId(userId);
                        operateLog.setOperateAt(now);
                        operateLog.setBusinessId(Long.parseLong(String.valueOf(idField.get(before))));
                        operateLog.setLogType(logConf.getLogType());
                        operateLog.setOperateType(1);
                        operateLog.setOperateName(logConf.getLogName());
                        operateLog.setOperateTable(finalTableName);
                        operateLog.setOperateTarget(field.getName());
                        if (oData instanceof Date) {
                            operateLog.setOperateNewValue(pre + dateFormat.format(oData));
                        } else {
                            operateLog.setOperateNewValue(buildConvertMap(map, logConvert, String.valueOf(oData)));
                        }
                        logs.add(operateLog);
                    }
                }
            }
            if (CollectionUtil.isNotEmpty(logs)) {
                applicationContext.getBean(ErpOperateLogService.class).saveBatch(logs);
            }
        } catch (Exception e) {
            log.error("日志记录失败:{}", e.getMessage());
        }
    }

    private boolean isContinueField(Field field) {
        if (field.isAnnotationPresent(TableField.class)) {
            TableField tableField = field.getAnnotation(TableField.class);
            return !tableField.exist();
        }
        if (
                        field.getName().equalsIgnoreCase("isDelete") ||
                        field.getName().equalsIgnoreCase("id") ||
                        field.getName().equalsIgnoreCase("createdAt") ||
                        field.getName().equalsIgnoreCase("updatedAt") ||
                        field.getName().equalsIgnoreCase("updatedBy") ||
                        field.getName().equalsIgnoreCase("createdBy") ||
                        field.getName().equalsIgnoreCase("createdName") ||
                        field.getName().equalsIgnoreCase("updatedName")

        ) {

            return true;
        }
        return false;
    }


    public String buildConvertMap(Map<String, List<SysDictData>> map, LogConvert logConvert, String data) {
        if (logConvert == null) {
            return data;
        }
        if (!logConvert.dict().isEmpty() && CollectionUtil.isNotEmpty(map)) {
            List<SysDictData> dictData = map.get(logConvert.dict());
            Map<String, String> dictMap = dictData.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
            return dictMap.get(data);
        }

        String[] values = logConvert.value();
        for (String value : values) {
            String[] logValue = value.split(":");
            if (data.equals(logValue[0])) {
                return logValue[1];
            }
        }
        return data;
    }

    private static String getExecSql(Class<?> type, SimpleDateFormat dateFormat, BoundSql boundSql, Object target) throws IllegalAccessException {
        String sql = boundSql.getSql();
        int subIndex = sql.contains("SET") ? sql.indexOf("SET") : sql.indexOf("set");
        String execSql = sql.substring(subIndex + 3).replace("\n", "").trim();
        StringBuilder builder = new StringBuilder();
        Object paramObject = target;
        builder.append(boundSql.getSql().replace("\n", ""), 0, subIndex + 3);

        if (target instanceof MapperMethod.ParamMap) {
            MapperMethod.ParamMap paramMap = (MapperMethod.ParamMap) target;
            if (paramMap.containsKey("et")) {
                paramObject = paramMap.get("et");
            }
            // 处理 lambdaUpdateWrapper 更新日志
            Object param2;
            if (paramObject == null && paramMap.containsKey("param2") && ((param2 = paramMap.get("param2")) instanceof LambdaUpdateWrapper)) {
                LambdaUpdateWrapper wrapper = (LambdaUpdateWrapper) param2;
                Map<String, Object> valuePairs = wrapper.getParamNameValuePairs();
                if (CollectionUtil.isEmpty(valuePairs)) {
                    return null;
                }
                builder.append(" ").append(execSql);
                ArrayList<String> keys = new ArrayList<>(valuePairs.keySet());
                keys.sort(String::compareTo);
                int index = 0;
                int count = 0;
                while ((index = builder.toString().indexOf("?", index)) != -1) {
                    builder.replace(index, ++index, String.valueOf(valuePairs.get(keys.get(count))));
                    count++;
                }
                return builder.toString().replace("?", " ");
            }
        }
        execSql = execSql.replace("where", "WHERE");
        String[] wheres = execSql.split("WHERE");
        for (int i = 0; i < wheres.length; i++) {
            String[] fieldCondition = wheres[i].split(",");
            for (int j = 0; j < fieldCondition.length; j++) {
                if (j == 0) builder.append(" ");

                String[] fieldAndParam = fieldCondition[j].split("=");
                String field = StrUtil.toCamelCase(fieldAndParam[0].trim());
                Field declaredField = null;
                try {
                    declaredField = type.getDeclaredField(field);
                } catch (NoSuchFieldException e) {
                    for (Field f : type.getDeclaredFields()) {
                        if (f.getName().equals(field)) {
                            declaredField = f;
                            break;
                        }
                    }
                }
                if (declaredField == null) {
                    if (j < fieldCondition.length - 1) builder.append(",");

                    if (j == fieldCondition.length - 1 && i == 0) {
                        builder.append("WHERE ");
                    }
                    continue;
                }
                declaredField.setAccessible(true);
                String paramValue = String.valueOf(declaredField.get(paramObject));
                if (declaredField.get(paramObject) instanceof Date) {
                    Date date = (Date) declaredField.get(paramObject);
                    paramValue = dateFormat.format(date);
                }
                builder.append(fieldAndParam[0].trim() + "=" + paramValue);
                if (j < fieldCondition.length - 1) builder.append(",");


                if (j == fieldCondition.length - 1 && i == 0) {
                    builder.append("WHERE ");
                }
            }

        }
        return builder.toString();
    }

    private static <T> void settingTargetField(Object target, Class<?> type, Field field, T beforeData, T currentUpData) throws NoSuchMethodException, InstantiationException, IllegalAccessException, InvocationTargetException {
        if (beforeData == null || !beforeData.equals(currentUpData)) {
            field.set(target, currentUpData);
        }
    }

    private static Object newInstanceO(Object target, Class<?> type) throws NoSuchMethodException, InstantiationException, IllegalAccessException, InvocationTargetException {
        if (target == null) {
            Constructor<?> constructor = type.getConstructor();
            constructor.setAccessible(true);
            target = constructor.newInstance();
        }
        return target;
    }


    private static String getParameterValue(Object obj) {
        String value;
        if (obj instanceof String) {
            value = "'" + obj.toString() + "'";
        } else if (obj instanceof Date) {
            DateFormat formatter = DateFormat.getDateTimeInstance(DateFormat.DEFAULT, DateFormat.DEFAULT, Locale.CHINA);
            value = "'" + formatter.format(new Date()) + "'";
        } else {
            if (obj != null) {
                value = obj.toString();
            } else {
                value = "";
            }
        }
        return value;
    }


    public static String getTableName(String sql) throws JSQLParserException {
        Statement stmt = CCJSqlParserUtil.parse(sql);
        if (stmt instanceof Update) {
            Update update = (Update) stmt;
            Table table = update.getTable();
            if (table != null) {
                return table.getName();
            }
        }
        return null;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }


    /**
     * 检查是否需要记录当前字段
     *
     * @param tableName
     * @param field
     * @return
     */
    private boolean needRecord(String tableName, String field) {
        tableName = tableName.contains(".") ? tableName.split("\\.")[1] : tableName;
        if (logCache.containsKey(tableName)) {
            if (StrUtil.isBlank(field)) {
                return true;
            }
            ErpLogConf conf = logCache.get(tableName);
            return conf.getColumns().contains("*") || conf.getColumns().contains(field);
        }
        ErpLogConf conf = applicationContext.getBean(ErpLogConfService.class).lambdaQuery()
                .eq(ErpLogConf::getTableName, tableName)
                .one();
        if (Objects.isNull(conf)) {
            return false;
        }
        logCache.put(tableName, conf);
        if (StrUtil.isBlank(field)) {
            return true;
        }
        return conf.getColumns().contains("*") || conf.getColumns().contains(field);
    }


    public static Map<String, ErpLogConf> getLogCache() {
        return logCache;
    }


    public static void addLogCache(ErpLogConf logConf) {
        logCache.put(logConf.getTableName(), logConf);
    }

    public static void addLogCache(List<ErpLogConf> logConfs) {
        for (ErpLogConf conf : logConfs) {
            logCache.put(conf.getTableName(), conf);
        }
    }


    public static void removeLogCache(String tableName) {
        logCache.remove(tableName);
    }
}
