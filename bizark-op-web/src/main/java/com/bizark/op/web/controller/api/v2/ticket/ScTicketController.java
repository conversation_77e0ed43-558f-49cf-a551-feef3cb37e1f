package com.bizark.op.web.controller.api.v2.ticket;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.alibaba.fastjson.JSON;
import com.bizark.op.api.cons.PermDefine;
import com.bizark.op.api.cons.ScTicketConstant;
import com.bizark.op.api.enm.tabcut.TabcutTagBussinessTypeEnum;
import com.bizark.op.api.entity.op.order.SaleOrders;
import com.bizark.op.api.entity.op.tabcut.TabcutVideoTag;
import com.bizark.op.api.entity.op.tabcut.vo.TabcutVideoTagRealtionDTO;
import com.bizark.op.api.entity.op.ticket.*;
import com.bizark.op.api.exception.ErpCommonException;
import com.bizark.op.api.service.RpcMarExportService;
import com.bizark.op.api.service.amazon.AmazonReturnService;
import com.bizark.op.api.service.customer.ICusCenterFeedbackService;
import com.bizark.op.api.service.customer.ICusCenterOffsiteService;
import com.bizark.op.api.service.mar.MarLastMileFeeClaimService;
import com.bizark.op.api.service.mar.appeal.MarAppealService;
import com.bizark.op.api.service.mar.claim.MarWfsClaimCaseService;
import com.bizark.op.api.service.order.SaleOrderCancelService;
import com.bizark.op.api.service.order.SaleOrdersService;
import com.bizark.op.api.service.returns.MarManualReturnService;
import com.bizark.op.api.service.tabcut.TabcutVideoTagService;
import com.bizark.op.api.service.ticket.*;
import com.bizark.op.api.service.xms.XsmReviewCommonService;
import com.bizark.op.common.annotation.RepeatSubmit;
import com.bizark.op.common.core.page.TableDataInfo;
import com.bizark.op.common.util.BeanCopyUtils;
import com.bizark.op.common.util.StringUtil;
import com.bizark.op.common.util.StringUtils;
import com.bizark.op.common.util.poi.ExcelUtil;
import com.bizark.op.service.service.sys.SysDictDataServiceImpl;
import com.bizark.op.service.util.GraphEmailApi;
import com.bizark.op.web.controller.api.AbstractApiController;
import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.mvel2.sh.CommandException;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工单管理Controller
 *
 * <AUTHOR>
 * @date 2022-01-12
 */
@RestController
@RequestMapping("/api/v2/crm/ticket")
public class ScTicketController extends AbstractApiController {
    @Autowired
    private IScTicketService scTicketService;

    @Autowired
    private IScOrderReissueService scOrderReissueService;

    @Autowired
    private SaleOrderCancelService saleOrderCancelService;


    @Autowired
    private SysDictDataServiceImpl sysDictDataService;


    //退货信息
    @Autowired
    private AmazonReturnService amazonReturnService;

    @Autowired
    private ICusCenterFeedbackService iCusCenterFeedbackService;

    @Autowired
    private XsmReviewCommonService xsmReviewCommonService;

    @Autowired
    private SaleOrdersService saleOrdersService;

    @Autowired
    private ISyncEmailInfoService emailInfoService;

    @Autowired
    private IAmzMessageActionService amzMessageActionService;

    @Autowired
    private ICusCenterOffsiteService cusCenterOffsiteService;

    @Autowired
    private IAmzVcPoOrderTimeRecordService amzVcPoOrderTimeRecordService;

    @Autowired
    private MarAppealService marAppealService;

    @Autowired
    private MarLastMileFeeClaimService marLastMileFeeClaimService;

    @Autowired
    private IMarDraftService marDraftService;


    @Autowired
    private MarManualReturnService marManualReturnService;

    @Autowired
    private TabcutVideoTagService tabcutVideoTagService;

    @Autowired
    private RpcMarExportService rpcMarExportService;

    @Autowired
    private MarWfsClaimCaseService marWfsClaimCaseService;

    /**
     * 查询工单管理列表
     */
    @PostMapping("/list")
    @RequiresPermissions(PermDefine.MAR_TICKET_LIST)
    public TableDataInfo list(@RequestParam("contextId") Integer contextId, @RequestBody ScTicket scTicket) {
        UserEntity authUserEntity = getAuthUserEntity();
        //过滤查询
        if (null != authUserEntity && null != authUserEntity.getId()) {
            String reoleWhiteList = sysDictDataService.selectDictValue("ticket_view", "role_white_list");
            if (!StringUtils.isEmpty(reoleWhiteList)) {
                List<String> filterList = Arrays.stream(reoleWhiteList.split(",")).filter(item -> item.equals(authUserEntity.getId().toString())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(filterList)) { //白名单用户
                    scTicket.setUserId(null);
                } else {
                    scTicket.setUserId(authUserEntity.getId().longValue());
                    scTicket.setHandlerByList(null);//禁止处理人查询
                }
            }
        }
        scTicket.setOrganizationId(contextId.longValue());
        startPage();
        List<ScTicket> scTickets = scTicketService.selectScTicketList(scTicket);
        return getDataTable(scTickets);
    }


    /**
     * 获取工单信息及工单对应订单信息
     *
     * @param ticketId 工单ID
     */
    @GetMapping(value = "/single/{ticketId}")
    @RequiresPermissions(PermDefine.MAR_TICKET_LIST)
    public ApiResponseResult getInfoBySingle(@PathVariable("ticketId") Long ticketId) {
        return ApiResponseResult.buildSuccessResult(scTicketService.selectScTicketByTicketId(ticketId));
    }

    /**
     * 获取子工单管理详细信息
     */
    @GetMapping(value = "/child/{ticketId}")
    @RequiresPermissions(PermDefine.MAR_TICKET_LIST)
    public ApiResponseResult getChildInfo(@PathVariable("ticketId") Long ticketId, @RequestParam("contextId") Integer contextId) {
        return ApiResponseResult.buildSuccessResult(scTicketService.getChildTicket(ticketId, contextId));
    }


    /**
     * 获取工单来源单据信息
     */
    @GetMapping(value = "/source/{ticketId}")
    @RequiresPermissions(PermDefine.MAR_TICKET_LIST)
    public ApiResponseResult getTicketSourceDocument(@PathVariable("ticketId") Long ticketId) {
        ScTicket ticket = scTicketService.selectScTicketById(ticketId);
        if (ScTicketConstant.TICKET_SOURCE_DOCUMENT_REISSUE.equals(ticket.getSourceDocument())) {  //补发产品及配件
            return ApiResponseResult.buildSuccessResult(scOrderReissueService.selectScOrderReissueById(ticket.getSourceId()));
        } else if (ScTicketConstant.TICKET_SOURCE_DOCUMENT_ORDER_CANNEL.equals(ticket.getSourceDocument())) { //订单取消
            return ApiResponseResult.buildSuccessResult(saleOrderCancelService.selectSaleOrderCancelById(ticket.getSourceId()));
        } else if (ScTicketConstant.TICKET_TYPE_RETURN.equals(ticket.getTicketType()) || ScTicketConstant.TICKET_TYPE_REPLACEMENT.equals(ticket.getTicketType())) { //退货类型工单
          if(ScTicketConstant.TICKET_SOURCE_DOCUMENT_MANUAL_RETURN.equals(ticket.getSourceDocument())){
              return ApiResponseResult.buildSuccessResult(marManualReturnService.selectTicketReturnInfo(ticket.getOrganizationId(),ticket.getId(),ticket.getTicketType(),ticket.getSourceId()));
          }else {
              return ApiResponseResult.buildSuccessResult(amazonReturnService.selectTiktokReturnInfoByIdNew(ticketId));
          }
        } else if (ScTicketConstant.TICKET_SOURCE_DOCUMENT_FEEDBACK.equals(ticket.getSourceDocument())) {
            return ApiResponseResult.buildSuccessResult(iCusCenterFeedbackService.detail(ticket.getSourceId()));
        }else if (ScTicketConstant.XSM_REVIEW_COMMON.equals(ticket.getSourceDocument())) {
            return ApiResponseResult.buildSuccessResult(xsmReviewCommonService.newSelectXsmReviewsById(ticket.getSourceId()));
        }else if (ScTicketConstant.TICKET_SOURCE_DOCUMENT_EMAIL.equals(ticket.getSourceDocument())) {
            return ApiResponseResult.buildSuccessResult(emailInfoService.selectEmailInfoById(ticket.getSourceId()));
        } else if (ScTicketConstant.TICKET_SOURCE_DOCUMENT_OFFSITE_LETTER.equals(ticket.getSourceDocument())) {//其他站外信
            return ApiResponseResult.buildSuccessResult(cusCenterOffsiteService.selectCusCenterOffsiteById(ticket.getSourceId()));
        } else if (ScTicketConstant.TICKET_TYPE_PO_DI_ORDER.equals(ticket.getTicketType())) { //DI订单确认
            return ApiResponseResult.buildSuccessResult(amzVcPoOrderTimeRecordService.selectAmzVcPoOrderTimeRecordVById(ticket.getSourceId()));
        } else if (ScTicketConstant.TICKET_TYPE_ORDER_PACKAGE_APPEAL.equals(ticket.getTicketType())) {//申诉处理
            return ApiResponseResult.buildSuccessResult(marAppealService.queryAppealDetailById(ticket));
        } else if (ScTicketConstant.TICKET_TYPE_CLAIM.equals(ticket.getTicketType())) {//索赔
            return ApiResponseResult.buildSuccessResult(marLastMileFeeClaimService.queryClaimDetailById(ticket));
        } else if (ScTicketConstant.TICKET_TYPE_WFS_CLAIM.equals(ticket.getTicketType())) { //wds索赔
            return ApiResponseResult.buildSuccessResult(marWfsClaimCaseService.queryWfsClaimMessage(ticket.getSourceId()));
        }
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * 修改工单状态
     */
    @PostMapping("/status/{ticketId}/{ticketStatus}")
    @RequiresPermissions(PermDefine.MAR_TICKET_LIST)
    public ApiResponseResult updateTicketStatus(@PathVariable("ticketId") Long ticketId, @PathVariable("ticketStatus") String ticketStatus) {
        ScTicket scTicket = new ScTicket();
        scTicket.setId(ticketId);
        scTicket.setTicketStatus(ticketStatus);
        return ApiResponseResult.buildSuccessResult(scTicketService.updateScTicketStatus(scTicket));
    }


    /**
     * 批量分配工单
     */
    @PostMapping("/batchDistribute/{handlerId}/{ticketIds}")
    @RequiresPermissions(PermDefine.MAR_TICKET_LIST)
    public ApiResponseResult batchDistribute(@PathVariable Long handlerId, @PathVariable Long[] ticketIds) {
        return ApiResponseResult.buildSuccessResult(scTicketService.batchDistributeScTicketByIds(handlerId, ticketIds));
    }


    /**
     * 确定按钮(更改工单状态为已完成)
     */
    @PutMapping("/update/sure/{ticketIds}")
    @RequiresPermissions(PermDefine.MAR_TICKET_LIST)
    public ApiResponseResult updateStatusAndSure(@PathVariable("ticketIds") Long[] ticketIds) {
        return ApiResponseResult.buildSuccessResult(scTicketService.updateAStatusndSure(ticketIds));
    }

    /**
     * 关闭按钮(更改工单状态为已关闭)
     */
    @PutMapping("/update/shutdown/{ticketIds}")
    @RequiresPermissions(PermDefine.MAR_TICKET_LIST)
    public ApiResponseResult updateStatusAndShutdown(@PathVariable("ticketIds") Long[] ticketIds) {
        return ApiResponseResult.buildSuccessResult(scTicketService.updateStatusAndShutdown(ticketIds));
    }

    /**
     * 导出工单列表数据
     */
    @GetMapping("/export")
    @RequiresPermissions(PermDefine.MAR_TICKET_LIST)
    public ApiResponseResult export(ScTicket scTicket, HttpServletResponse response, @RequestParam(value = "contextId") Long contextId) {
        scTicket.setOrganizationId(contextId);
        scTicketService.originalExportScTicket(scTicket,getAuthUserEntity());
        return ApiResponseResult.buildSuccessResult("导出已生成，请至【任务中心】-【任务列表】查看处理进度及下载文件！");

    }


    /**
     * 手动创建工单(其他站外信)-查询订单
     *
     * @param orderNo
     * @param contextId
     * @return
     */
    @GetMapping("/manual/order")
    @RequiresPermissions(PermDefine.MAR_TICKET_LIST)
    public ApiResponseResult manualOrderList(@RequestParam("orderNo") String orderNo, @RequestParam("contextId") Integer contextId) {

        if (contextId == null || contextId < 1) {
            contextId = getAuthUserEntity().getOrgId();
        }
        List<ScTicketOrderAndOrderItem> list = scTicketService.selectManualOrder(orderNo, Long.valueOf(contextId));
        return CollectionUtil.isNotEmpty(list) ? ApiResponseResult.buildSuccessResult(list) : ApiResponseResult.buildFailureResult("查无此订单");

    }

    /**
     * 手动创建工单(其他站外信)
     *
     * @param request
     * @param contextId
     * @return
     */
    @PostMapping("/manual/save")
    @RepeatSubmit(interval = 3000, message = "请勿重复创建")
    @RequiresPermissions(PermDefine.MAR_TICKET_LIST)
    public ApiResponseResult manualSaveScTicket(@RequestBody @Valid ScTicketCreateRequest request, @RequestParam("contextId") Integer contextId) {
        request.setOrganizationId(Long.valueOf(contextId));
        ScTicket scTicket = BeanCopyUtils.copyBean(request, ScTicket.class);
        return ApiResponseResult.buildSuccessResult(scTicketService.manualSaveScTicket(scTicket));
    }

    /**
     * 请求移除负面反馈
     *
     * @param amzSellerFeedback
     * @return
     */
    @PostMapping(value = "/negativeFeedbackRemoval")
    @RequiresPermissions(PermDefine.MAR_TICKET_LIST)
    public ApiResponseResult negativeFeedbackRemoval(@RequestBody AmzSellerFeedback amzSellerFeedback) {
//        if (amzMessageActionService.negativeFeedbackRemoval(amzSellerFeedback)) {
//            return ApiResponseResult.buildSuccessResult();
//        } else {
//            return ApiResponseResult.buildFailureResult("当前订单不可请求移除负面反馈");
//        }
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * Description: 校验联系客户
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/3/18
     */
    @GetMapping(value = "/check/contact/customers")
    @RequiresPermissions(PermDefine.MAR_TICKET_LIST)
    public ApiResponseResult checkContactCustomers(@RequestParam("contextId") Integer contextId,@RequestParam("ticketId") Long ticketId,@RequestParam("ticketType") String ticketType,@RequestParam("shopId") Long shopId,@RequestParam("buyEmail") String buyEmail) {
        return ApiResponseResult.buildSuccessResult(scTicketService.checkContactCustomers(contextId,ticketId,ticketType,shopId,buyEmail));
    }

    /**
     * Description: 跟新退货工单历史数据中问题分类
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/3/18
     */
    @GetMapping(value = "/refresh/history/ticket/pro")
    @RequiresPermissions(PermDefine.MAR_TICKET_LIST)
    public ApiResponseResult refreshHistoryTemuReturnTicket() {
        amazonReturnService.refreshHistoryTemuReturnTicketJob();
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * Description: 无需回复
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2024/3/18
     */
    @GetMapping(value = "/no/reply")

    public ApiResponseResult noReply(@RequestParam("contextId") Integer contextId,@RequestParam("ticketId") Long ticketId) {
        scTicketService.noReply(ticketId);
        return ApiResponseResult.buildSuccessResult();
    }


    /**
     * Description: 保存草稿信息
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2025/3/26
     */
    @PostMapping(value = "/save/draft")
    public ApiResponseResult saveDraft(@RequestParam("contextId") Long contextId, @RequestBody MarDraft marDraft) {
        marDraft.setOrganizationId(contextId);
        marDraftService.saveDraft(marDraft);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * Description: 得到草稿信息
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2025/3/26
     */
    @GetMapping(value = "/get/draft")
    public ApiResponseResult getDraft(@RequestParam("ticketId") Long ticketId) {
        return ApiResponseResult.buildSuccessResult(marDraftService.getDraft(ticketId));
    }


    /**
     * Description: 删除草稿信息
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2025/3/26
     */
    @DeleteMapping("/delete/draft")
    public ApiResponseResult deleteDraft(@RequestParam("ticketId") Long ticketId) {
        marDraftService.deleteDraft(ticketId);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * Description: 校验是否能跟新订单匹配信息
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2025/3/26
     */
    @GetMapping("/check/update/match/order")
    public ApiResponseResult checkUpdateMatchOrder(@RequestParam("ticketId") Long ticketId) {
        return ApiResponseResult.buildSuccessResult(scTicketService.checkUpdateMatchOrder(ticketId));
    }


    /**
     * Description: 校验是否能跟新订单匹配信息
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: Fountain
     * @Date: 2025/3/26
     */
    @GetMapping("/check/ticket/order")
    public ApiResponseResult checkUpdateMatchOrder(@RequestParam("contextId") Long contextId,
                                                   @RequestParam("amazonOrderId") String amazonOrderId,
                                                   @RequestParam("orderId") Long orderId,
                                                   @RequestParam("ticketType") String  ticketType,
                                                   @RequestParam("ticketId") Long  ticketId) {
        return ApiResponseResult.buildSuccessResult(scTicketService.checkTicketOrder(contextId,amazonOrderId,orderId,ticketType,ticketId));
    }

    /**
     * 工单标签新增或更新
     *
     * @param
     * @param contextId
     * @return
     */
    @PostMapping("/tag/saveOrUpdate")
    public ApiResponseResult saveOrUpdateMaterialTag(@RequestBody List<TabcutVideoTag>  tagList, @RequestParam("contextId") Integer contextId) {
        if (!CollectionUtils.isEmpty(tagList)) {
            if (tagList.stream().anyMatch(item -> StringUtil.isEmpty(item.getTagName()) || StringUtil.isEmpty(item.getTagColor()))) {
                throw new CommandException("标签颜色或名称不能为空!");
            }

            Set<String> seenNames = new HashSet<>();
            tagList.stream()
                    .map(TabcutVideoTag::getTagName)
                    .filter(tagName -> !StringUtil.isEmpty(tagName))
                    .filter(tagName -> !seenNames.add(tagName))  // 如果 add 返回 false，表示重复
                    .forEach(tagName -> {
                        throw new CommandException("标签名称" + tagName + "重复!");
                    });
        }


        for (TabcutVideoTag tag : tagList) {
            tag.setOrgId(contextId);
            tag.setTagBusinessType(TabcutTagBussinessTypeEnum.TICKET.getValue());
            tabcutVideoTagService.saveOrUpdateTag(tag);
        }

        return ApiResponseResult.buildSuccessResult();
    }
    /** 勾选保存 tag信息
     *
     * @param tagRelation
     * @param
     * @return
     */
    @PostMapping("/addOrUpdateTag")
    public ApiResponseResult materialAddTag(@RequestBody TabcutVideoTagRealtionDTO tagRelation,
                                            @RequestParam("contextId") Integer contextId,
                                            @RequestParam(value = "opType") String opType){
        scTicketService.addTag(tagRelation,contextId,opType);
        return ApiResponseResult.buildSuccessResult();
    }

    /** 素材标签列表下拉框查询
     *
     * @param tag
     * @param contextId
     * @return
     */
    @GetMapping("/tag/list")
    public ApiResponseResult materialList(TabcutVideoTag tag,@RequestParam("contextId") Integer contextId,@RequestParam("queryListFlag") Integer queryListFlag){
        List<TabcutVideoTag> videoTagList = new ArrayList<>();
        if(new Integer(1).equals(queryListFlag)) {
            TabcutVideoTag tabcutVideoTag = new TabcutVideoTag();
            tabcutVideoTag.setTagName("无");
            tabcutVideoTag.setId(-1L);
            videoTagList.add(tabcutVideoTag);
        }
        tag.setOrgId(contextId);
        tag.setTagBusinessType(TabcutTagBussinessTypeEnum.TICKET.getValue());
        List<TabcutVideoTag> videoTags = tabcutVideoTagService.selectTagList(tag);
        if(CollectionUtils.isNotEmpty(videoTags)) {
            videoTagList.addAll(videoTags);
        }
        return ApiResponseResult.buildSuccessResult(videoTagList);
    }

}
