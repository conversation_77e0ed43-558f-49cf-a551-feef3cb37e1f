package com.bizark.op.web.controller.api.v2.ticket;

import com.bizark.op.api.entity.op.ticket.ScTicketHandle;
import com.bizark.op.api.service.ticket.IScTicketHandleService;
import com.bizark.op.common.util.UserUtils;
import com.bizark.op.web.controller.api.AbstractApiController;
import com.bizark.framework.web.view.ApiResponseResult;
import com.bizark.usercenter.api.entity.dashboard.UserEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 工单Controller
 *
 * <AUTHOR>
 * @date 2022-03-03
 */
@RestController
@RequestMapping("/api/v2/ticket/handle")
public class ScTicketHandleController extends AbstractApiController
{
    @Autowired
    private IScTicketHandleService scTicketHandleService;
//
//    /**
//     * 查询工单列表
//     */
//    @PreAuthorize("@ss.hasPermi('ticket:handle:list')")
//    @GetMapping("/list")
//    public TableDataInfo list(ScTicketHandle scTicketHandle)
//    {
//        startPage();
//        List<ScTicketHandle> list = scTicketHandleService.selectScTicketHandleList(scTicketHandle);
//        return getDataTable(list);
//    }
//
//    /**
//     * 导出工单列表
//     */
//    @PreAuthorize("@ss.hasPermi('ticket:handle:export')")
//    @Log(title = "工单", businessType = BusinessType.EXPORT)
//    @GetMapping("/export")
//    public AjaxResult export(ScTicketHandle scTicketHandle)
//    {
//        List<ScTicketHandle> list = scTicketHandleService.selectScTicketHandleList(scTicketHandle);
//        ExcelUtil<ScTicketHandle> util = new ExcelUtil<ScTicketHandle>(ScTicketHandle.class);
//        return util.exportExcel(list, "handle");
//    }
//
//    /**
//     * 获取工单详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('ticket:handle:query')")
//    @GetMapping(value = "/{handleId}")
//    public AjaxResult getInfo(@PathVariable("handleId") Long handleId)
//    {
//        return AjaxResult.success(scTicketHandleService.selectScTicketHandleById(handleId));
//    }
//
    /**
     * 根据工单id获取工单处理信息
     */
    @GetMapping(value = "/info/{ticketId}")
    public ApiResponseResult getInfoByTicket(@PathVariable("ticketId") Long ticketId) {
        return ApiResponseResult.buildSuccessResult(scTicketHandleService.selectScTicketHandleByTicketId(ticketId));
    }

    /**
     * 保存工单处理
     * @param scTicketHandle 工单处理
     * @return
     */
    @PostMapping("/save")
    public ApiResponseResult saveScTicketHandle(@RequestParam("contextId") Long contextId,@RequestBody ScTicketHandle scTicketHandle){
        scTicketHandleService.saveScTicketHandle(scTicketHandle, contextId);
        return ApiResponseResult.buildSuccessResult();
    }

    /**
     * 新增工单
     */
    @PostMapping
    public ApiResponseResult save(@RequestParam("contextId") Long contextId,@RequestBody ScTicketHandle scTicketHandle) {
        return ApiResponseResult.buildSuccessResult(scTicketHandleService.insertScTicketHandle(contextId,scTicketHandle));
    }


//
//    /**
//     * 新增工单
//     */
//    @PreAuthorize("@ss.hasPermi('ticket:handle:add')")
//    @Log(title = "工单", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult save(@RequestBody ScTicketHandle scTicketHandle)
//    {
//        return toAjax(scTicketHandleService.saveScTicketHandle(scTicketHandle));
//    }
//
//    /**
//     * 删除工单
//     */
//    @PreAuthorize("@ss.hasPermi('ticket:handle:remove')")
//    @Log(title = "工单", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{handleIds}")
//    public AjaxResult remove(@PathVariable Long[] handleIds)
//    {
//        return toAjax(scTicketHandleService.deleteScTicketHandl   eByIds(handleIds));
//    }

    @GetMapping("log")
    public ApiResponseResult queryLog() {
        return ApiResponseResult.buildSuccessResult();
    }
}
