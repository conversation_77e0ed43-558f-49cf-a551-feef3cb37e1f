package com.bizark.op.web.config.runner;

import com.bizark.framework.service.jpa.BaseService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import javax.persistence.Table;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/5/9
 */
@Component
@EntityScan(basePackages = "com.bizark.op.api.entity.*")
public class ServiceApplicationRunner implements ApplicationRunner {

    private static final Logger LOG = LoggerFactory.getLogger(ServiceApplicationRunner.class);

    @Autowired
    private List<BaseService> services;

    @Autowired
    private List<EntityManager> entityManagers;

    @Autowired
    private List<LocalContainerEntityManagerFactoryBean> ontainerEntityManagerFactoryBeancs;

    @SuppressWarnings("unchecked")
    @Override
    public void run(ApplicationArguments args) throws Exception {
        if(LOG.isDebugEnabled()){
            LOG.debug("设置所有<BaseService> 相关属性,开始...");
        }
        for (BaseService baseService : services) {
            Class entityClass = baseService.getEntityClass();
            for (LocalContainerEntityManagerFactoryBean managerFactoryBean : ontainerEntityManagerFactoryBeancs) {
                if (StringUtils.equalsIgnoreCase(((Table) entityClass.getAnnotation(Table.class)).schema(), managerFactoryBean.getDataSource().getConnection().getCatalog())) {
                    for (EntityManager entityManagerItem : entityManagers) {
                        if (entityManagerItem.getEntityManagerFactory() == managerFactoryBean.getObject()) {
                            baseService.setEntityManager(entityManagerItem);
                        }
                    }
                }
            }
            //构造对应的JPA操作接口对象
            baseService.setJpaRepository(new SimpleJpaRepository(entityClass, baseService.getEntityManager()));
        }
        if(LOG.isDebugEnabled()){
            LOG.debug("设置所有<BaseService> 相关属性完成");
        }

        LOG.info("\n" + getPattern());

    }

    private static String getPattern() {

        return  "\n" +
                "////////////////////////////////////////////////////////////////////\n" +
                "//                          _ooOoo_                               //\n" +
                "//                         o8888888o                              //\n" +
                "//                         88\" . \"88                              //\n" +
                "//                         (| ^_^ |)                              //\n" +
                "//                         O\\  =  /O                              //\n" +
                "//                      ____/`---'\\____                           //\n" +
                "//                    .'  \\\\|     |//  `.                         //\n" +
                "//                   /  \\\\|||  :  |||//  \\                        //\n" +
                "//                  /  _||||| -:- |||||-  \\                       //\n" +
                "//                  |   | \\\\\\  -  /// |   |                       //\n" +
                "//                  | \\_|  ''\\---/''  |   |                       //\n" +
                "//                  \\  .-\\__  `-`  ___/-. /                       //\n" +
                "//                ___`. .'  /--.--\\  `. . ___                     //\n" +
                "//              .\"\" '<  `.___\\_<|>_/___.'  >'\"\".                  //\n" +
                "//            | | :  `- \\`.;`\\ _ /`;.`/ - ` : | |                 //\n" +
                "//            \\  \\ `-.   \\_ __\\ /__ _/   .-` /  /                 //\n" +
                "//      ========`-.____`-.___\\_____/___.-`____.-'========         //\n" +
                "//                           `=---='                              //\n" +
                "//      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^        //\n" +
                "//             佛祖保佑       永不宕机      永无BUG               //\n" +
                "////////////////////////////////////////////////////////////////////" +
                "\n" +
                "\n";
    }

}
