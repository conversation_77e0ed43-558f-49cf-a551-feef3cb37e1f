package com.bizark.op.web.listener.mar;


import com.alibaba.fastjson.JSONObject;
import com.bizark.op.api.cons.MQDefine;
import com.bizark.op.api.entity.op.order.SaleOrders;
import com.bizark.op.api.service.ticket.IAmzVcPoOrderService;
import com.bizark.op.common.util.StringUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;


/**
 * @ClassName 接收LIsting信息队列
 * @Description
 * <AUTHOR>
 * @Date 2023/8/23 12:56
 */
@Slf4j
@Component
public class ScTicketVcDiListener {


    //获取Po工单信息
    @Autowired
    private IAmzVcPoOrderService amzVcPoOrderService;


//    @RabbitListener(queues = MQDefine.VC_DI_ORDER_TIKCKET)
    public void starReceiver(Message message, @Header(AmqpHeaders.CHANNEL) Channel channel,
                        @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws IOException {
        try {
            String messageStr = new String(message.getBody(), StandardCharsets.UTF_8);
            log.error("接收到的DI创建工单消息：{}",messageStr);
            if (StringUtils.isNotEmpty(messageStr)) {
                SaleOrders saleOrders = JSONObject.parseObject(messageStr, SaleOrders.class);
                amzVcPoOrderService.createAmzPoOrderTicket(saleOrders);
            }
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            log.error("接收DI订单创建工单失败：{}",message);
        }

    }

}
