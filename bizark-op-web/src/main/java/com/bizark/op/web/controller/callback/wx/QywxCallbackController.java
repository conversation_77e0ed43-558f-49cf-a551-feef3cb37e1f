package com.bizark.op.web.controller.callback.wx;

import com.bizark.op.api.entity.op.qywx.QywxOrgainzationConfig;
import com.bizark.op.api.service.qywx.QywxOrgainzationConfigService;
import com.bizark.op.common.util.wx.aes.AesException;
import com.bizark.op.common.util.wx.aes.WXBizJsonMsgCrypt;
import com.bizark.op.service.util.third.qywx.QywxProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 企业微信回调处理
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-07-28 13:48:00
 */
@RestController
@RequestMapping(value = "/qywx/callback")
@Slf4j
public class QywxCallbackController {

    @Autowired
    private QywxProperties qywxProperties;
    @Autowired
    private QywxOrgainzationConfigService qywxOrgainzationConfigService;

    @GetMapping
    public String instructGet(@RequestParam(name = "msg_signature") String msgSignature,
                              @RequestParam(name = "timestamp") String timestamp,
                              @RequestParam(name = "nonce") String nonce,
                              @RequestParam(name = "echostr") String echostr) {
        log.info("\n接收到来自微信服务器的认证消息：msg_signature = [{}], timestamp = [{}], nonce = [{}], echostr = [{}], corp_id = [{}]",
                msgSignature, timestamp, nonce, echostr);
        // 需要返回的明文
        String sEchoStr = null;
        QywxOrgainzationConfig config = qywxOrgainzationConfigService.lambdaQuery().eq(QywxOrgainzationConfig::getCorpId, "ww128e807e0972c460").one();
        if (null == config) {
            log.error("当前接收来自微信服务器得认证消息异常,msg_signature = [{}], timestamp = [{}], nonce = [{}], echostr = [{}]",
                     msgSignature, timestamp, nonce, echostr);
            return sEchoStr;
        }
        try {
            WXBizJsonMsgCrypt wxBizJsonMsgCrypt = new WXBizJsonMsgCrypt(config.getToken(),
                    config.getEncodingAesKey(),
                    config.getCorpId());

            /*
        ------------使用示例一：验证回调URL---------------
        *企业开启回调模式时，企业微信会向验证url发送一个get请求
        假设点击验证时，企业收到类似请求：
        * GET /cgi-bin/wxpush?msg_signature=5c45ff5e21c57e6ad56bac8758b79b1d9ac89fd3&timestamp=1409659589&nonce=263014780&echostr=P9nAzCzyDtyTWESHep1vC5X9xho%2FqYX3Zpb4yKa9SKld1DsH3Iyt3tP3zNdtp%2B4RPcs8TgAE7OaBO%2BFZXvnaqQ%3D%3D
        * HTTP/1.1 Host: qy.weixin.qq.com

        接收到该请求时，企业应		1.解析出Get请求的参数，包括消息体签名(msg_signature)，时间戳(timestamp)，随机数字串(nonce)以及企业微信推送过来的随机加密字符串(echostr),
        这一步注意作URL解码。
        2.验证消息体签名的正确性
        3. 解密出echostr原文，将原文当作Get请求的response，返回给企业微信
        第2，3步可以用企业微信提供的库函数VerifyURL来实现。

        */
            // 验证URL
            sEchoStr = wxBizJsonMsgCrypt.VerifyURL(msgSignature, timestamp, nonce, echostr);
            log.info("verifyurl echostr success: {}", sEchoStr);
        } catch (AesException e) {
            log.error("verifyurl echostr error: ", e);
        }

        return sEchoStr;
    }


    @GetMapping("/firm")
    public String instructGet(@RequestParam(name = "msg_signature") String msgSignature,
                              @RequestParam(name = "timestamp") String timestamp,
                              @RequestParam(name = "nonce") String nonce,
                              @RequestParam(name = "echostr") String echostr,
                              @RequestParam(name = "corp_id") String corpId) {
        log.info("\n接收到来自微信服务器的认证消息：msg_signature = [{}], timestamp = [{}], nonce = [{}], echostr = [{}], corp_id = [{}]",
                msgSignature, timestamp, nonce, echostr, corpId);
// 需要返回的明文
        String sEchoStr = null;
        QywxOrgainzationConfig config = qywxOrgainzationConfigService.lambdaQuery().eq(QywxOrgainzationConfig::getCorpId, corpId).one();
        if (null == config) {
            log.error("当前接收来自微信服务器得认证消息异常,msg_signature = [{}], timestamp = [{}], nonce = [{}], echostr = [{}], corp_id = [{}]",
                     msgSignature, timestamp, nonce, echostr, corpId);
            return sEchoStr;
        }
        try {
            WXBizJsonMsgCrypt wxBizJsonMsgCrypt = new WXBizJsonMsgCrypt(config.getToken(),
                    config.getEncodingAesKey(),
                    config.getCorpId());

            /*
        ------------使用示例一：验证回调URL---------------
        *企业开启回调模式时，企业微信会向验证url发送一个get请求
        假设点击验证时，企业收到类似请求：
        * GET /cgi-bin/wxpush?msg_signature=5c45ff5e21c57e6ad56bac8758b79b1d9ac89fd3&timestamp=1409659589&nonce=263014780&echostr=P9nAzCzyDtyTWESHep1vC5X9xho%2FqYX3Zpb4yKa9SKld1DsH3Iyt3tP3zNdtp%2B4RPcs8TgAE7OaBO%2BFZXvnaqQ%3D%3D
        * HTTP/1.1 Host: qy.weixin.qq.com

        接收到该请求时，企业应		1.解析出Get请求的参数，包括消息体签名(msg_signature)，时间戳(timestamp)，随机数字串(nonce)以及企业微信推送过来的随机加密字符串(echostr),
        这一步注意作URL解码。
        2.验证消息体签名的正确性
        3. 解密出echostr原文，将原文当作Get请求的response，返回给企业微信
        第2，3步可以用企业微信提供的库函数VerifyURL来实现。

        */
            // 验证URL
            sEchoStr = wxBizJsonMsgCrypt.VerifyURL(msgSignature, timestamp, nonce, echostr);
            log.info("verifyurl echostr success: {}", sEchoStr);
        } catch (AesException e) {
            log.error("verifyurl echostr error: ", e);
        }

        return sEchoStr;
    }

}
