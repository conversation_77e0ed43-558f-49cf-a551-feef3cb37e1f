
APP_ENV=development
enviorment=dev

web_service_rest.http.port=6261
web_service_rest.https.port=6262
dubbo_service_rpc.stop.port=6260

dubbo_service_rpc.http.port=16262
dubbo_service_rpc.dubbo.port=26262
dubbo.application.qos.port=46262
entity.manager.factory.bean.name.default=entityManagerFactoryErp

server.port=${web_service_rest.https.port}
dubbo.application.qosPort=${dubbo.application.qos.port}

server.ssl.enabled=true
#\u8BC1\u4E66\u7684\u8DEF\u5F84.
server.ssl.key-store=E:/Java/keystore.jks
#\u8BC1\u4E66\u5BC6\u7801\uFF0C\u8BF7\u4FEE\u6539\u4E3A\u60A8\u81EA\u5DF1\u8BC1\u4E66\u7684\u5BC6\u7801.
server.ssl.key-store-password=114461452870877
#\u79D8\u94A5\u5E93\u7C7B\u578B
#server.ssl.keyStoreType=JKS
#\u8BC1\u4E66\u522B\u540D
#server.ssl.keyAlias=214461452870850
#server.ssl.keyAlias=tomcat
#server.session.cookie.domain=.thebizark.com
#server.session.cookie.path=/
#server.session.cookie.secure=false

logging.config=classpath:config/log/logback_dev.xml

#### ENTDIY START ###

# \u6784\u5EFA\u7248\u672C,\u7528\u4E8E\u53D1\u5E03\u7248\u672C\u4FE1\u606F\u663E\u793A\u548C\u7F13\u5B58\u7248\u672C\u6807\u8BC6
# \u7B2C\u4E00\u4F4D\u4E3B\u7248\u672C\u53F7\u548C\u7B2C\u4E8C\u4F4D\u6B21\u7248\u672C\u53F7\uFF0C\u6839\u636E\u5B9E\u9645\u529F\u80FD\u53D8\u66F4\u5E45\u5EA6\u624B\u5DE5\u66F4\u65B0\u8BBE\u7F6E
# \u7B2C\u4E09\u4F4DBUILD_NUMBER\u4FDD\u6301\u4E0D\u52A8\uFF0C\u5F00\u53D1\u6A21\u5F0F\u4E0B\u7531\u6846\u67B6\u81EA\u52A8\u66FF\u6362\u4E3A\u52A8\u6001\u6570\u5B57\u4EE5\u4E0D\u65AD\u5F3A\u5236\u66F4\u65B0\u4EE3\u7801\u907F\u514D\u9759\u6001\u8D44\u6E90\u7F13\u5B58
# \u53D1\u5E03\u90E8\u7F72\u5305\u65F6\u53EF\u901A\u8FC7Jenkins\u8FD9\u6837\u7684CI\u5DE5\u5177\u52A8\u6001\u628AXXX\u66FF\u6362\u4E3A\u6784\u5EFA\u7248\u672C\u4FE1\u606F
build.version=2.2.BUILD_NUMBER

# Hibernate\u6807\u51C6\u7684hbm2ddl\u529F\u80FD\uFF0C\u5177\u4F53\u7528\u6CD5\u8BF7\u53C2\u8003\u5B98\u65B9\u6587\u6863\u8BF4\u660E
# \u7F6E\u7A7A\u8868\u793A\u5173\u95ED\u81EA\u52A8\u5316\u5904\u7406
# \uFF01\u5C24\u5176\u6CE8\u610F\u8C28\u614E\u4F7F\u7528create\u6216create-drop\u4F1A\u5BFC\u81F4\u6E05\u9664\u5F53\u524D\u5DF2\u6709\u6570\u636E\uFF01
#hibernate.hbm2ddl.auto=update
hibernate.hbm2ddl.auto=validate

# Envers\u7EC4\u4EF6org.hibernate.envers.default_schema\u914D\u7F6E\u53C2\u6570\u503C
# \u4E3A\u7A7A\u8868\u793A\u5728\u5F53\u524D\u5E94\u7528schema\uFF0C\u53EF\u4EE5\u6307\u5B9A\u5206\u79BB\u7684schema\u540D\u79F0
#hibernate.envers.default.schema=

# \u5F00\u53D1\u8FC7\u7A0B\u53EF\u8BBE\u7F6E\u4E3Atrue\uFF0C\u751F\u4EA7\u73AF\u5883\u4E00\u5B9A\u8981\u8BBE\u7F6E\u4E3Afalse
# \u5F00\u53D1\u6A21\u5F0F\u4F1A\u81EA\u52A8\u521D\u59CB\u5316\u6216\u66F4\u65B0\u57FA\u7840\u6570\u636E\uFF0C\u751F\u4EA7\u6A21\u5F0F\u53EA\u4F1A\u5728\u7A7A\u6570\u636E\u72B6\u6001\u624D\u4F1A\u521D\u59CB\u5316\u57FA\u7840\u6570\u636E\uFF0C\u8BE6\u89C1\uFF1ADatabaseDataInitialize
dev.mode=true

# \u6F14\u793A\u90E8\u7F72\u53EF\u8BBE\u7F6E\u4E3Atrue\uFF0C\u751F\u4EA7\u73AF\u5883\u4E00\u5B9A\u8981\u8BBE\u7F6E\u4E3Afalse
# \u6F14\u793A\u8FD0\u884C\u6A21\u5F0F\u4E0B\uFF1A\u6309\u7167\u521D\u59CB\u5316\u4EE3\u7801\u903B\u8F91\u81EA\u52A8\u751F\u6210\u4E00\u7CFB\u5217\u6F14\u793A\u6A21\u62DF\u6570\u636E\uFF1B\u7981\u7528\u4E00\u4E9B\u6838\u5FC3\u57FA\u7840\u529F\u80FD\u63D0\u4EA4\u9632\u6B62\u7528\u6237\u968F\u610F\u64CD\u4F5C
demo.mode=false

# \u6743\u9650\u63A7\u5236\u7B49\u7EA7\uFF0C\u53EF\u9009\u503C\u8BF4\u660E\uFF1A
# high=\u6240\u6709\u672A\u914D\u7F6E\u5BF9\u5E94\u6743\u9650\u7684URL\u8BF7\u6C42\u4F5C\u4E3A\u53D7\u4FDD\u62A4\u8D44\u6E90\u4E25\u683C\u63A7\u5236\uFF0C\u8BBF\u95EE\u5219\u629B\u51FA\u8BBF\u95EE\u62D2\u7EDD\u5F02\u5E38
# low= \u6240\u6709\u672A\u914D\u7F6E\u5BF9\u5E94\u6743\u9650\u7684URL\u8BF7\u6C42\u4F5C\u4E3A\u975E\u4FDD\u62A4\u8D44\u6E90\u5BBD\u677E\u63A7\u5236\uFF0C\u53EA\u8981\u767B\u5F55\u7528\u6237\u90FD\u53EF\u4EE5\u8BBF\u95EE
auth.control.level=high

# \u7CFB\u7EDF\u540D\u79F0\uFF0C\u7528\u4E8E\u9875\u9762\u6807\u9898\u7B49\u663E\u793A
system.name=bizark-op

#api\u6587\u6863\u7248\u672C
apidoc.version=2.0.0

# \u81EA\u52A9\u6CE8\u518C\u7BA1\u7406\u8D26\u53F7\u529F\u80FD\u5F00\u5173\uFF1Atrue=\u5173\u95ED\u7BA1\u7406\u8D26\u53F7\u81EA\u52A9\u6CE8\u518C\uFF0Cfalse=\u5F00\u653E\u6CE8\u518C
cfg_mgmt_signup_disabled=false

# \u90AE\u4EF6\u76F8\u5173\u53C2\u6570\uFF0C\u6839\u636E\u5B9E\u9645\u90AE\u4EF6\u7CFB\u7EDF\u914D\u7F6E\u8BBE\u5B9A
mail.host=smtp.mxhichina.com
mail.username=<EMAIL>
mail.from=<EMAIL>
mail.password=mail123!@#
mail.smtp.auth=true

# SMS\u77ED\u4FE1\u901A\u9053\u7B7E\u540D
sms.signature=\u6D4B\u8BD5

# \u4E0A\u4F20\u6587\u4EF6\u5199\u5165\u6587\u4EF6\u76EE\u5F55\u8DEF\u5F84\uFF0C\u6BD4\u5982Nginx\u5BF9\u5E94\u7684\u9759\u6001\u8D44\u6E90\u76EE\u5F55\u3002
# \u5982\u679C\u4E3A\u7A7A\u5219\u53D6\u81EA\u52A8\u53D6\u5F53\u524Dwebapp/upload\u76EE\u5F55\uFF0C\u6CE8\u610F\u5B58\u5728\u66F4\u65B0\u7248\u672C\u88AB\u8BEF\u5220\u7684\u53EF\u80FD\u3002
file.write.dir=
# \u6587\u4EF6\u6216\u56FE\u7247\u4E0B\u8F7D\u6216\u663E\u793A\u7684URL\u524D\u7F00\u90E8\u5206\u3002\u6587\u4EF6\u4FE1\u606F\u5BF9\u8C61\u53EA\u8BB0\u5F55\u76F8\u5BF9\u8DEF\u5F84\u90E8\u5206\u5185\u5BB9\uFF0C\u6B64\u524D\u7F00\u8FFD\u52A0\u5BF9\u8C61\u8DEF\u5F84\u5185\u5BB9\u5F97\u5230\u5B8C\u6574\u8BBF\u95EEURL\u3002
# \u4E00\u822C\u53EF\u5B9A\u4E49\u4E3ANginx\u9759\u6001\u8D44\u6E90\u8BBF\u95EE\u5730\u5740\u6216\u5916\u90E8\u6587\u4EF6\u5B58\u50A8\u5730\u5740URL\u524D\u7F00\uFF0C\u5982\u679C\u4E3A\u7A7A\u5219\u53D6\u5F53\u524Dwebapp\u7684URL\u8DEF\u5F84\u52A0\u4E0AContextPath
file.read.url.prefix=

##########################################################
#
# Redis configuration
#
##########################################################
hibernate.redis.config=classpath:/hibernate-redis.properties
hibernate.redis.host=redis://127.0.0.1:6379

#### ENTDIY END ###

spring.messages.basename=i18n/messages
spring.messages.encoding=UTF-8

#spring.mvc.static-path-pattern=/**
#spring.mvc.static-path-pattern=/static/**
#spring.jpa.properties.hibernate.hbm2ddl.auto=update
spring.jpa.properties.hibernate.hbm2ddl.auto=validate
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL5InnoDBDialect
spring.jpa.properties.hibernate.default_schema=dashboard
# Envers\u7EC4\u4EF6org.hibernate.envers.default_schema\u914D\u7F6E\u53C2\u6570\u503C
# \u4E3A\u7A7A\u8868\u793A\u5728\u5F53\u524D\u5E94\u7528schema\uFF0C\u53EF\u4EE5\u6307\u5B9A\u5206\u79BB\u7684schema\u540D\u79F0
spring.jpa.properties.hibernate.envers.default.schema=

spring.jpa.show-sql=true
#spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true
#logging.level.org.hibernate=INFO

#logging.level.org.hibernate.SQL=INFO
#logging.level.org.hibernate.type=INFO
#logging.level.org.hibernate.type.descriptor.sql.BasicBinder=INFO
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type=TRACE
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

#log4j.logger.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
spring.jpa.properties.hibernate.jdbc.time_zone = UTC
spring.jpa.open-in-view=true
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true
spring.jackson.time-zone=UTC
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.serialization.write_dates_as_timestamps=false


spring.datasource.default.driver-class-name = com.mysql.jdbc.Driver
spring.datasource.default.maxActive = 10
spring.datasource.default.minIdle = 0

# project-DEFAULT
spring.datasource.url = ****************************************************************************************************************************************************************************
spring.datasource.username = user_business
spring.datasource.password = Office123888#
spring.datasource.driver-class-name = com.mysql.jdbc.Driver


# erp
spring.datasource.erp.url = ****************************************************************************************************************************************************************************
spring.datasource.erp.username = user_business
spring.datasource.erp.password = Office123888#
spring.datasource.erp.driver-class-name = com.mysql.jdbc.Driver
spring.datasource.erp.slave.url = ****************************************************************************************************************************************************************************
spring.datasource.erp.slave.username = user_business
spring.datasource.erp.slave.password = Office123888#
spring.datasource.erp.slave.driver-class-name = com.mysql.jdbc.Driver

# dashboard
spring.datasource.dashboard.url = jdbc:mysql://************:3306/dashboard?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&tinyInt1isBit=false&useSSL=false&rewriteBatchedStatements=true
spring.datasource.dashboard.username = user_business
spring.datasource.dashboard.password = Office123888#
spring.datasource.dashboard.driver-class-name = com.mysql.jdbc.Driver

#fbb
spring.datasource.fbb.url = jdbc:mysql://************:3306/fbb?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&tinyInt1isBit=false&useSSL=false&rewriteBatchedStatements=true
spring.datasource.fbb.username = user_business
spring.datasource.fbb.password = Office123888#
spring.datasource.fbb.driver-class-name = com.mysql.jdbc.Driver

# tbadbp
spring.datasource.tbadbp.url = jdbc:mysql://************:3306/tbadbp?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&tinyInt1isBit=false&useSSL=false&rewriteBatchedStatements=true
spring.datasource.tbadbp.username = user_business
spring.datasource.tbadbp.password = Office123888#
spring.datasource.tbadbp.driver-class-name = com.mysql.jdbc.Driver

# multichannel
spring.datasource.multichannel.url = jdbc:mysql://************:3306/multichannel?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&tinyInt1isBit=false&useSSL=false&rewriteBatchedStatements=true
spring.datasource.multichannel.username = user_business
spring.datasource.multichannel.password = Office123888#
spring.datasource.multichannel.driver-class-name = com.mysql.jdbc.Driver

spring.dubbo.appname=dubbo-spring-boot-starter-consumer-devops-test
spring.dubbo.registry=************:2181
spring.dubbo.protocol=dubbo

spring.dubbo.url=************:2181
spring.dubbo.version=1.0.0

dubbo.application.qosEnable=true
dubbo.application.qosAcceptForeignIp=false
#\u914D\u7F6E\u4E2D\u5FC3\u8FDE\u63A5\u65F6\u95F4\u6539\u4E3A10s
dubbo.config-centre.timeout=10000

# mns\u961F\u5217\u914D\u7F6E
mns.accountendpoint=http://****************.mns.cn-hangzhou.aliyuncs.com/
#mns.accountendpoint=http://****************.mns.cn-hangzhou-internal.aliyuncs.com/
#mns.accountendpoint=http://****************.mns.cn-hangzhou-internal-vpc.aliyuncs.com/
mns.accesskeyid=LTAI5tFA6nbCt3f9xgu2zvHf
mns.accesskeysecret=******************************

#aliyun.accessId=LTAI5tFA6nbCt3f9xgu2zvHf
#aliyun.accessKey=******************************

aliyun.accessId=LTAI5tFA6nbCt3f9xgu2zvHf
aliyun.accessKey=******************************
aliyun.resOwnerId=****************

#aliyun.smsAccessId=LTAI5tFA6nbCt3f9xgu2zvHf
#aliyun.smsAccessKey=******************************
aliyun.smsEndpointDefault=cn-hangzhou
aliyun.smsSignDefault=\u56DB\u6D77\u5546\u821F
#aliyun.smsTemplateCodeDefault=SMS_125018915
#aliyun.smsTemplateCodeDefault=SMS_125023822
#aliyun.smsTemplateCodeDefault=SMS_125118692
# \u9A8C\u8BC1\u7801${code}\uFF0C\u60A8\u6B63\u5728\u8FDB\u884C${product}\u8EAB\u4EFD\u9A8C\u8BC1\uFF0C\u6253\u6B7B\u4E0D\u8981\u544A\u8BC9\u522B\u4EBA\u54E6\uFF01
aliyun.smsTemplateCodeDefault=SMS_225815219
aliyun.ossBucketDefault=ehenglin-public-hz
aliyun.ossEndpointDefault=oss-cn-hangzhou.aliyuncs.com

notifyEmail.host=smtpdm.aliyun.com
#notifyEmail.host=smtp.263.net
notifyEmail.ssl=true
notifyEmail.port=465
notifyEmail.user=<EMAIL>
#notifyEmail.user=<EMAIL>
notifyEmail.password=fm9srB3eGE2P2Ud0
notifyEmail.fromAddress=<EMAIL>
notifyEmail.fromName=noreply


# \u4E1A\u52A1\u4E8B\u4EF6\u91C7\u96C6\u5BA2\u6237\u7AEF\u914D\u7F6E
## \u662F\u5426\u5F00\u542F\u91C7\u96C6
bizevcli.enable=true
## \u5F53\u524D\u8FD0\u884C\u73AF\u5883
bizevcli.env=development
#bizevcli.env=local
## \u5F53\u524D\u73AF\u5883\u4E0B\u7684\u670D\u52A1\u7AEFtoken\u53E3\u4EE4
bizevcli.token=ryQwxSBc
## \u624B\u5DE5\u6307\u5B9A\u7684\u91C7\u96C6\u670D\u52A1\u8282\u70B9url
bizevcli.url=http://dev-ops.bizarkdev.com/opsapi/collect/event
#bizevcli.url=http://localhost:8677/opsapi/collect/event
bizevcli.msgurl=
#bizevcli.msgurl=https://dev-ucenter.ehenglin.com/collect/bzevmsg/collect

#ucenter
ucenter.client.url=http://dev-example-api.ehenglin.com
ucenter.server.url=http://dev-port.ehenglin.com

#sso
#sso.server.url=http://dev-sso.thebizark1.com:9656
#sso.client.url=http://dev-port.thebizark1.com:8677
sso.server.url=https://dev-sso.ehenglin.com
sso.client.url=https://dev-port.ehenglin.com


#JWT \u914D\u7F6E
## \u662F\u5426\u5F00\u542Fjwt
bizjwt.enable=true
## \u9ED8\u8BA4iss
bizjwt.iss=www.thebizark.com
## \u9ED8\u8BA4aud
bizjwt.aud=www.thebizark.com
## \u9ED8\u8BA4\u4E0B\u6807field
bizjwt.field=token
## jwt\u7684\u79C1\u94A5
bizjwt.secret=gwtqrWDH9hKo7Ihn1vCgmrjyw9griLB1
## \u9ED8\u8BA4\u8FC7\u671F\u65F6\u95F4\u95F4\u9694(\u5206\u949F)
bizjwt.ttl=10080
## \u9ED8\u8BA4\u5237\u65B0\u8FC7\u671F\u65F6\u95F4\u95F4\u9694(\u5206\u949F)
bizjwt.refreshTtl=20160
## \u9ED8\u8BA4\u7B7E\u540D\u7B97\u6CD5
bizjwt.algo=HS256
## \u9ED8\u8BA4\u6DFB\u52A0\u7684\u5B57\u6BB5
bizjwt.identifier=id
## \u5FC5\u586B\u7684claims\u5B57\u6BB5\u96C6\u5408
bizjwt.claimsRequired=iss,iat,exp,nbf,sub,jti

xxl.conf.admin.address=http://************:8008/xxl-conf-admin
xxl.conf.env=dev
xxl.conf.access.token=
xxl.conf.mirrorfile=${HOME:${HOME:${user.home}}}/Code/applogs/xxl-conf/bizark-op-web/xxl-conf-mirror.properties

amqp.provider=raw
#amqp.provider=aliyun

spring.rabbitmq.addresses=************:5672
#spring.rabbitmq.port=5672
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest
# RabbitMQ \u9ED8\u8BA4\u7684\u7528\u6237\u540D\u548C\u5BC6\u7801\u90FD\u662Fguest \u800C\u865A\u62DF\u4E3B\u673A\u540D\u79F0\u662F "/"
# \u5982\u679C\u914D\u7F6E\u5176\u4ED6\u865A\u62DF\u4E3B\u673A\u5730\u5740\uFF0C\u9700\u8981\u9884\u5148\u7528\u7BA1\u63A7\u53F0\u6216\u8005\u56FE\u5F62\u754C\u9762\u521B\u5EFA \u56FE\u5F62\u754C\u9762\u5730\u5740 http://\u4E3B\u673A\u5730\u5740:15672
spring.rabbitmq.virtualhost=/

# \u662F\u5426\u542F\u7528\u53D1\u5E03\u8005\u786E\u8BA4 \u5177\u4F53\u786E\u8BA4\u56DE\u8C03\u5B9E\u73B0\u89C1\u4EE3\u7801
spring.rabbitmq.publisher-confirms=true
# \u662F\u5426\u542F\u7528\u53D1\u5E03\u8005\u8FD4\u56DE \u5177\u4F53\u8FD4\u56DE\u56DE\u8C03\u5B9E\u73B0\u89C1\u4EE3\u7801
spring.rabbitmq.publisher-returns=true
# \u662F\u5426\u542F\u7528\u5F3A\u5236\u6D88\u606F \u4FDD\u8BC1\u6D88\u606F\u7684\u6709\u6548\u76D1\u542C
spring.rabbitmq.template.mandatory=true

# \u4E3A\u4E86\u4FDD\u8BC1\u4FE1\u606F\u80FD\u591F\u88AB\u6B63\u786E\u6D88\u8D39,\u5EFA\u8BAE\u7B7E\u6536\u6A21\u5F0F\u8BBE\u7F6E\u4E3A\u624B\u5DE5\u7B7E\u6536,\u5E76\u5728\u4EE3\u7801\u4E2D\u5B9E\u73B0\u624B\u5DE5\u7B7E\u6536
spring.rabbitmq.listener.simple.acknowledge-mode=manual
# \u4FA6\u542C\u5668\u8C03\u7528\u8005\u7EBF\u7A0B\u7684\u6700\u5C0F\u6570\u91CF
spring.rabbitmq.listener.simple.concurrency=10
# \u4FA6\u542C\u5668\u8C03\u7528\u8005\u7EBF\u7A0B\u7684\u6700\u5927\u6570\u91CF
spring.rabbitmq.listener.simple.max-concurrency=50

bizsession.cookie_name=bizjsessid
redis.address=redis://127.0.0.1:6379
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.database=0
spring.redis.timeout=10000
spring.redis.password=
spring.redis.maxIdle=8
spring.redis.minIdle=0
spring.redis.maxActive=8
spring.redis.maxWait=-1

spring.data.elasticsearch.repositories.enabled=true
spring.data.elasticsearch.client.reactive.endpoints=************:9200
#spring.data.elasticsearch.cluster-name=elasticsearch
es.save.enabled=false
#spring.data.elasticsearch.cluster-name=

opensearch.accessId=LTAI5tFA6nbCt3f9xgu2zvHf
opensearch.accessKey=******************************
opensearch.endpoint=http://opensearch-cn-hangzhou.aliyuncs.com

aliyunxtrace.domain=http://tracing-analysis-dc-hz.aliyuncs.com
aliyunxtrace.username=ECPALIYUNXTRACEUSERNAME001
aliyunxtrace.password=ECPALIYUNXTRACEPASSWD001

aliyunodps.accessId=LTAI5tFA6nbCt3f9xgu2zvHf
aliyunodps.accessKey=******************************
aliyunodps.endpoint=http://service.cn.maxcompute.aliyun.com/api
aliyunodps.defaultProject=HL_ERP_BI

#\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u013C\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD
spring.servlet.multipart.enabled =true
spring.servlet.multipart.file-size-threshold =0
#\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u0775\u0134\uFFFD\u0421
spring.servlet.multipart.max-file-size = 100MB
#\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u0775\u0134\uFFFD\u0421
spring.servlet.multipart.max-request-size=100MB

## \u5BFC\u51FA\u6A21\u677F\u57FA\u7840\u8DEF\u5F84
export_template_basePath=${HOME:${HOME:${user.home}}}/Code/bizark_op/current/template/

crm.api.ip=https://aofiscrm.ehengjian.com

task.center.file.path=/home/<USER>/bizark_op/file/
walmart.payment.path= /home/<USER>/attachment/bizark_op/

# \u91D1\u8776EAS\u63A5\u53E3\u4FE1\u606F
kingDee.eas.baseUrl=**************:44442
kingDee.eas.username=\u63A5\u53E3\u4E13\u7528
kingDee.eas.password=a0147852
kingDee.eas.slnName=eas
kingDee.eas.dcName=CS20230113_146
kingDee.eas.language=12
kingDee.eas.dbType=1
kingDee.eas.authPattern=BaseDB

# \u5B81\u6CE2\u94F6\u884C
nbbank.enable=true
nbbank.custId=**********
nbbank.appKey=6dbf53b3-fced-4fa3-bf53-9c9d7bad23fd
nbbank.publicUrl=http://cz-test.nbcb.com.cn:7070/nbcb/api
nbbank.publicKey=MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEXAt6fxeKlpREIyXoT3jDwGU7L6JkKrzdOKP1cMB14rtNwwlyaUGLrtuHVoArAHP3x7XN2rUw0GxSngYQwOAsjQ==
nbbank.privateKey=MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgNgsftJKL9yI+1bh6IpJTKbUXH2174SR+9VmOlyThc7igCgYIKoEcz1UBgi2hRANCAAT2f4Mk3ooK+GflEPt6GnBjYFAUkQf1m0a2j7N0hSko+SWrKX3OnRYD/aEpQ0Q1su6zTiGAlLKckuXQ/0uw0mRu
nbbank.connectTimeout=10000
nbbank.readTimeout=20000
nbbank.isProxy=true
nbbank.proxyIp=**************
nbbank.proxyPort=1080

# payoneer
third.payoneer.enable=true
third.payoneer.programId=*********
third.payoneer.clientId=o8ncm5AaJ9ObDFlbe8lPt7u9nUGAmcMt
third.payoneer.clientSecret=rBLGAs4zF4HPulMj
third.payoneer.baseUrl=https://api.sandbox.payoneer.com
third.payoneer.redirectUrl=https://www.example.com
third.payoneer.authorizationUrl=https://login.sandbox.payoneer.com

# \u4F01\u4E1A\u5FAE\u4FE1
third.qywx.corpId=ww4b6916d02035aabb
third.qywx.token=2RW9iAA2xIwS8H
third.qywx.encodingAESKey=JcTlAp0vSde3sNDF4P7dKsuW6V1Mgxy6qqDlETB71Wo

inventory.sc.path=/home/<USER>/inventoryfile/sc/
home_query_bi_url=http://*************:5560

fin_bi_export_url=http://*************:5555

ad.tiktok.request=http://*************:8888


promotion.advice.webhook=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a03f0ebd-b348-4dc3-b01d-1f5fec0f78b4

wechat.boot.webhook.webhook-url=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=
wechat.boot.webhook.boot-tokens.FOLLOW=19ab0e77-0691-44ab-8d33-09433a97773d
wechat.boot.webhook.boot-tokens.ATD=19ab0e77-0691-44ab-8d33-09433a97773d
wechat.boot.webhook.boot-tokens.PROFIT=19ab0e77-0691-44ab-8d33-09433a97773d
wechat.boot.webhook.boot-tokens.PROFIT_OPERATION=19ab0e77-0691-44ab-8d33-09433a97773d
wechat.boot.webhook.boot-tokens.PROFIT_OPERATION_EXTRA=19ab0e77-0691-44ab-8d33-09433a97773d
wechat.boot.webhook.boot-tokens.ORDER_TRACK=19ab0e77-0691-44ab-8d33-09433a97773d
wechat.boot.webhook.boot-tokens.PROFIT_FILFULLMENT=19ab0e77-0691-44ab-8d33-09433a97773d
wechat.boot.webhook.boot-tokens.INVENTORY_PUSH=f43d485c-757c-41ec-80e2-3f0738a4de07
wechat.boot.webhook.boot-tokens.TEMU_PROMOTION=5d0b7250-cc41-42e9-a40d-482079728d38
wechat.boot.webhook.boot-tokens.WALMART_FEE=c5c5df63-c213-4810-b3a4-393ea813c779
wechat.boot.webhook.boot-tokens.SEND_SAVE_STORE_NAME=58447773-d841-4725-adcd-7d0f8afea5df

transition.file.url=http://8.210.198.240:11111
temu.flow.analyse.url=http://172.28.193.50:5555
rpa_query_url=http://172.16.12.24:8001
algorithm_query_url=http://172.16.12.29:9000
algorithm_query_classify_question_url=http://172.16.12.29:8000
wms.base.url=https://pre-wms.ehengjian.com/prod-api/common
reissue.buy.label.url=http://127.0.0.1:7447