<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://dubbo.apache.org/schema/dubbo http://dubbo.apache.org/schema/dubbo/dubbo.xsd">

    <!-- 提供方应用信息，用于计算依赖关系 -->
    <dubbo:application name="bizark-op-consumer-web"/>
    <dubbo:consumer check="false" proxy="jdk" />

    <!-- 使用multicast广播注册中心暴露服务地址 -->
    <dubbo:registry protocol="zookeeper" address="${spring.dubbo.url}"/>

    <!--&lt;!&ndash;样例 rpcMyDemoService 服务接口&ndash;&gt;-->
    <!--<dubbo:reference id="refRpcMyDemoService" interface="com.bizark.op.api.service.MyDemoService" protocol="dubbo" timeout="60000" retries="0" version="1.0.0" />-->

    <!--  序列号服务 远程接口 SequenceService 服务接口-->
    <dubbo:reference id="refRpcSequenceService" interface="com.bizark.boss.api.service.sequence.SequenceService"
                     check="false" protocol="dubbo" timeout="60000" retries="0" version="1.0.0"/>

    <!--  通知服务 远程接口 refRpcNotifyPostService 服务接口-->
    <dubbo:reference id="refRpcNotifyPostService" interface="com.bizark.usercenter.api.service.NotifyPostService"
                     protocol="dubbo" timeout="60000" retries="0" version="1.0.0"/>

    <!--  子系统 远程接口 refRpcUcSystemService 服务接口-->
    <dubbo:reference id="refRpcUcSystemService" interface="com.bizark.usercenter.api.service.UcSystemService"
                     protocol="dubbo" timeout="60000" retries="0" version="1.0.0"/>

    <!--  组织 远程接口 refRpcOrganizationService 服务接口-->
    <dubbo:reference id="refRpcOrganizationService" interface="com.bizark.usercenter.api.service.OrganizationService"
                     protocol="dubbo" timeout="60000" retries="0" version="1.0.0"/>

    <!--  用户 远程接口 refRpcUserService 服务接口-->
    <dubbo:reference id="refRpcUserService" interface="com.bizark.usercenter.api.service.UserService" protocol="dubbo"
                     timeout="60000" retries="0" version="1.0.0"/>

    <!--  用户中心操作日志 远程接口 refRpcUcOpLogService 服务接口-->
    <dubbo:reference id="refRpcUcOpLogService" interface="com.bizark.usercenter.api.service.UcOpLogService"
                     protocol="dubbo" timeout="60000" retries="0" version="1.0.0"/>

    <!--  用户中心开发 远程接口 refRpcUcDevService 服务接口-->
    <dubbo:reference id="refRpcUcDevService" interface="com.bizark.usercenter.api.service.UcDevService" protocol="dubbo"
                     timeout="60000" retries="0" version="1.0.0"/>

    <!--  用户中心开发 远程接口 refRpcUcDeptService 服务接口-->
    <dubbo:reference id="refRpcUcDeptService" interface="com.bizark.usercenter.api.service.UcDeptService" protocol="dubbo"
                     timeout="60000" retries="0" version="1.0.0"/>

    <!--  附件服务 远程接口 refRpcUcAttachmentService 服务接口-->
    <dubbo:reference check="false" id="refRpcUcAttachmentService"
                     interface="com.bizark.usercenter.api.service.attachment.UcAttachmentService" protocol="dubbo"
                     timeout="60000" retries="0" version="1.0.0"/>

    <!--fbb查询库存    -->
    <dubbo:reference check="false" id="refRpcInventoryListService"
                     interface="com.bizark.fbb.api.service.InventoryListService" protocol="dubbo" timeout="60000"
                     retries="0" version="1.0.0"/>

    <dubbo:reference id="commonRegionService" interface="com.bizark.boss.api.service.CommonRegionService" check="false"
                     protocol="dubbo" timeout="60000" retries="0" version="1.0.0"/>

    <dubbo:reference id="productBossService" interface="com.bizark.boss.api.service.product.ProductService"
                     check="false" protocol="dubbo" timeout="60000" retries="0" version="1.0.0"/>

    <!--附件-->
    <dubbo:reference id="commonAttachmentService" interface="com.bizark.boss.api.service.CommonAttachmentService"
                     protocol="dubbo,http" version="1.0.0"/>

    <dubbo:reference id="productCustomsService" interface="com.bizark.boss.api.service.product.ProductCustomsService"
                     protocol="dubbo,http" version="1.0.0"/>

    <dubbo:reference id="refWarehouseService" interface="com.bizark.fbb.api.service.WarehouseService" check="false"
                     protocol="dubbo" timeout="60000" retries="0" version="1.0.0"/>

    <!--&lt;!&ndash;  组织仓库 远程接口 refRpcOrganizationWarehouseService 服务接口&ndash;&gt;-->
    <dubbo:reference id="refRpcOrganizationWarehouseService"
                     interface="com.bizark.fbb.api.service.OrganizationWarehouseService" protocol="dubbo"
                     timeout="60000" retries="0" version="1.0.0"/>

    <dubbo:reference id="productPackagesService" interface="com.bizark.boss.api.service.product.ProductPackagesService"
                     protocol="dubbo,http" version="1.0.0"/>
    <!-- UCenter 用户服务-->
    <dubbo:reference id="ucPostService" interface="com.bizark.usercenter.api.service.UcPostService" protocol="dubbo"
                     timeout="600000" retries="0" version="1.0.0"/>

    <!-- 项目 RPC-->
    <dubbo:reference id="productLineService" interface="com.bizark.boss.api.service.ProductLineService" check="false"
                     protocol="dubbo" timeout="600000" retries="0" version="1.0.0"/>

    <!--&lt;!&ndash;  渠道账号 远程接口 refRpcAccountService 服务接口&ndash;&gt;-->
    <dubbo:reference id="refRpcAccountService" interface="com.bizark.boss.api.service.AccountService" protocol="dubbo"
                     timeout="60000" retries="0" version="1.0.0"/>

    <dubbo:reference id="saleOrderService" interface="com.bizark.boss.api.service.order.base.SaleOrderService"
                     protocol="dubbo" check="false" timeout="120000" retries="0" version="1.0.0"/>

    <dubbo:reference id="deliverOrderService" interface="com.bizark.boss.api.service.deliverorder.DeliverOrderService"
                     check="false" protocol="dubbo" timeout="60000" retries="0" version="1.0.0"/>

    <!--  业务事件通知服务 远程接口 refRpcBizNotifyService 服务接口-->
    <dubbo:reference id="refRpcBizNotifyService" interface="com.bizark.usercenter.api.service.BizNotifyService"
                     protocol="dubbo" timeout="60000" retries="0" check="false" version="1.0.0"/>

    <dubbo:reference id="taskCenterService" interface="com.bizark.boss.api.service.TaskCenterService" check="false"
                     protocol="dubbo" timeout="60000" retries="0" version="1.0.0"/>

<!--    <dubbo:reference id="chargingOrderService" interface="com.bizark.fbb.api.service.charging.ChargingOrderService"-->
<!--                     protocol="dubbo" check="false" timeout="60000" retries="0" version="1.0.0"/>-->

    <dubbo:reference id="amazonMarketPlaceService"
                     interface="com.bizark.boss.api.service.amazon.AmazonMarketPlaceService" protocol="dubbo"
                     check="false" timeout="60000" retries="0" version="1.0.0"/>

    <!-- DSP广告费 -->
    <dubbo:reference id="adDspStatitsticBiService" interface="com.bizark.ad.api.service.ad.AdDspStatitsticBiService"
                     protocol="dubbo" check="false" timeout="60000" retries="0" version="1.0.0"/>

    <dubbo:reference id="ucPermissionService" interface="com.bizark.usercenter.api.service.UcPermissionService"
                     protocol="dubbo" check="false" timeout="60000" retries="0" version="1.0.0"/>

    <!-- FBB批量更新计划单,出库调用-->
    <dubbo:reference id="stockAccountAddressService"
                     interface="com.bizark.boss.api.service.stock.StockAccountAddressService" protocol="dubbo"
                     check="false" timeout="60000" retries="0" version="1.0.0"/>


    <dubbo:reference id="productChannelService"
                     interface="com.bizark.boss.api.service.product.ProductChannelService" protocol="dubbo"
                     check="false" timeout="60000" retries="0" version="1.0.0"/>

    <!--生成拦截单-->
    <dubbo:reference id="interceptOrderService" interface="com.bizark.boss.api.service.interceptorder.InterceptOrderService" protocol="dubbo" check="false" timeout="60000" retries="0" version="1.0.0" />
    <!--物流异动-->
    <dubbo:reference id="trackingChangeOrderService" interface="com.bizark.boss.api.service.deliverorder.TrackingChangeOrderService" protocol="dubbo" check="false" timeout="60000" retries="0" version="1.0.0" />
    <!--补发-->
    <dubbo:reference id="orderOpenApiService" interface="com.bizark.boss.api.service.order.openapi.OrderOpenApiService" protocol="dubbo" check="false" timeout="60000" retries="0" version="1.0.0" />

    <!--发货订单-->
    <dubbo:reference id="b2cOrderService" interface="com.bizark.boss.api.service.order.b2c.B2cOrderService" protocol="dubbo" check="false" timeout="60000" retries="0" version="1.0.0" />

    <!--计费接口-->
    <dubbo:reference id="carrierCalculationService" interface="com.bizark.fbb.api.service.carrier.CarrierCalculationService" protocol="dubbo" check="false" timeout="60000" retries="0" version="1.0.0" />

    <!--退货计划-->
    <dubbo:reference id="saleReturnPlanOrderService" interface="com.bizark.fbb.api.service.order.SaleReturnPlanOrderService" protocol="dubbo" check="false" timeout="60000" retries="0" version="1.0.0" />

    <!--退货计划单-->
    <dubbo:reference id="returnPlanOrderService" interface="com.bizark.fbb.api.service.returnplanorder.ReturnPlanOrderService" protocol="dubbo" check="false" timeout="60000" retries="0" version="1.0.0" />
    <!--Tracking记录-->
    <dubbo:reference id="trackingRecordService" interface="com.bizark.fbb.api.service.tracker.TrackingRecordService" protocol="dubbo" check="false" timeout="60000" retries="0" version="1.0.0" />

    <dubbo:reference check="false" id="AdProductConfigService" interface="com.bizark.ad.api.service.ad.AdProductConfigService"  protocol="dubbo" timeout="60000" retries="0" version="1.0.0" />

    <!--  获取可用sellerSku  -->
    <dubbo:reference id="ucRoleService" interface="com.bizark.usercenter.api.service.UcRoleService" protocol="dubbo" timeout="600000" retries="0" version="1.0.0" check="false" />
    <!--  用户部门信息  -->
    <dubbo:reference id="ucUserPostDeptService" interface="com.bizark.usercenter.api.service.UcUserPostDeptService" protocol="dubbo" check="false" timeout="60000" retries="0" version="1.0.0" />

    <dubbo:reference id="orderSerService" interface="com.bizark.boss.api.service.aftersale.OrderSerService" protocol="dubbo" check="false" timeout="60000" retries="0" version="1.0.0" />

    <!--   取消单撤回 -->
    <dubbo:reference id="withdrawInterceptOrderService" interface="com.bizark.boss.api.service.interceptorder.WithdrawInterceptOrderService" protocol="dubbo" check="false" timeout="60000" retries="0" version="1.0.0" />

    <!--  -->
    <dubbo:reference id="processStartService" interface="com.bizark.activiti.api.service.activiti.ProcessStartService" protocol="dubbo" check="false" timeout="60000" retries="0" version="1.0.0" />
        <!--    temu未结算信息-->
<!--    <dubbo:reference id="finAccountsReceivableBillHeadService" interface="com.bizark.report.api.service.finance.FinAccountsReceivableBillHeadService" protocol="dubbo"  timeout="60000" retries="0" version="1.0.0" />-->


    <dubbo:reference id="transferOutboundPlanService" interface="com.bizark.fbb.api.service.fbbtransfer.TransferOutboundPlanService" protocol="dubbo"  timeout="60000" retries="0" version="1.0.0" />

    <!--Op运营-->
<!--    <dubbo:reference id="marOperationService" interface="com.bizark.report.api.service.op.MarOperationService" protocol="dubbo"  timeout="60000" retries="0" version="1.0.0" />-->

    <dubbo:reference id="carrierVolumeWeightService" interface="com.bizark.fbb.api.service.carrier.CarrierVolumeWeightService" protocol="dubbo"  timeout="60000" retries="0" version="1.0.0" />

    <!--    采购申请-->
    <dubbo:reference id="iPurchaseApplyBatchService" interface="com.bizark.erp.api.service.purchase.IPurchaseApplyBatchService" protocol="dubbo"  timeout="60000" retries="0" version="1.0.0" />

    <!--广告账号-->
    <dubbo:reference id="iAdAccountInfoService" check="false" interface="com.bizark.ad.api.service.ad.IAdAccountInfoService" protocol="dubbo"  timeout="60000" retries="0" version="1.0.0" />

    <dubbo:reference id="activitiProcessRecordsService" interface="com.bizark.activiti.api.service.activiti.ActivitiProcessRecordsService" protocol="dubbo" check="false" timeout="60000" retries="0" version="1.0.0" />

<!--    ops创建尾程费索赔-->
    <dubbo:reference id="reimbursementService" interface="com.bizark.fbb.api.service.reimbursement.ReimbursementService" protocol="dubbo" check="false" timeout="60000" retries="0" version="1.0.0" />

</beans>
