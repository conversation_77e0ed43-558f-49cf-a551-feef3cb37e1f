spring:
  profiles:
    active: dev
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 0
      max-file-size: 20MB
      max-request-size: 20MB
  http:
    encoding:
      force: true
      charset: utf-8
      enabled: true
  aop:
    proxy-target-class: true
  mvc:
    static-path-pattern: /static/**
    throw-exception-if-no-handler-found: true
  resources:
    add-mappings: false
  freemarker:
    request-context-attribute: request
  jackson:
    serialization:
      WRITE_DATES_AS_TIMESTAMPS: false
  main:
    allow-bean-definition-overriding: true

server:
  port: 7799
  use-forward-headers: true
  tomcat:
    uri-encoding: UTF-8
    remote-ip-header: X-Forwarded-For
    port-header: X-Forwarded-Port
    protocol-header: X-Forwarded-Proto
    protocol-header-https-value: https
  servlet:
    context-path: /
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024
  http2:
    enabled: true
springdoc:
#  paths-to-match: /auth/**,/api/v2/purchase/**,/api/v2/sup/supplier/**
#  paths-to-exclude: /apidoc/**
  packages-to-scan: com.bizark.op.web.controller.api,com.bizark.op.api,com.bizark.fbb.api,com.bizark.boss.api,com.bizark.usercenter.api,com.bizark.framework,com.bizark.common
#  version: '@apidoc.version@'
  # 布尔值。实现OpenApi规范的打印。
#  writer-with-default-pretty-printer: true
#  show-actuator: false
#  api-docs:
#    # enabled the /v3/api-docs endpoint
#    enabled: true
#    # 自定义的文档api元数据访问路径。默认访问路径是/v3/api-docs
#    path: /apidoc/api-docs
#    # 布尔值。在@Schema（名称name、标题title和说明description，三个属性）上启用属性解析程序。
#    resolve-schema-properties: true
  swagger-ui:
    enabled: true
#    path: /apidoc/docs.html
    groups-order: DESC
    operationsSorter: method
    # 字符串类型，一共三个值来控制操作和标记的默认展开设置。它可以是“list”（仅展开标记）、“full”（展开标记和操作）或“none”（不展开任何内容）。
    docExpansion: none
    # 布尔值。控制“试用”请求的请求持续时间（毫秒）的显示。
    displayRequestDuration: true
    # 布尔值。控制供应商扩展（x-）字段和操作、参数和架构值的显示。
    showExtensions: true
    # 布尔值。控制参数的扩展名（pattern、maxLength、minLength、maximum、minminimum）字段和值的显示。
    showCommonExtensions: true
    # 布尔值。禁用swagger用户界面默认petstore url。（从v1.4.1开始提供）。
    disable-swagger-default-url: true

# ===== swagger配置 =====#
swagger:
  application-version: ${apidoc.version}
  application-name: "bizark erp api"
  application-description: Bizark Web API
  try-host: http://localhost:6261,https://dev-erm-api.aofiscrm.com
#  basic:
#    enable: true
#    username: admin
#    password: 123456

knife4j:
  # 开启增强配置
  enable: true
  basic:
    enable: true
    # Basic认证用户名
    username: admin
    # Basic认证密码
    password: 123456

# MyBatisPlus配置
mybatis-plus:
  mapperLocations: classpath*:mapper/**/*Mapper*.xml
  # 自定义TypeHandler
  type-handlers-package: com.bizark.op.common.config.typehandler
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.bizark.op.api.entity.op.**,generator.domain.**
  configuration:
    # 开启SQL日志输出
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    #是否控制台
    banner: false
    db-config:
      #主键类型
      id-type: auto
