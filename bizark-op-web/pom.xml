<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <artifactId>bizark-op</artifactId>
        <groupId>com.bizark</groupId>
        <version>1.0.4-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>bizark-op-web</artifactId>
    <packaging>jar</packaging>
    <name>bizark-op-web Maven Webapp</name>
    <url>http://maven.apache.org</url>

    <properties>
        <tomcat.version>8.5.23</tomcat.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-parent</artifactId>
                <version>${spring-boot-starter-parent.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
           <!--  <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-jasper</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-jasper</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-jasper-el</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-logging-juli</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-jdbc</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-jsp-api</artifactId>
                <version>${tomcat.version}</version>
            </dependency> -->
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-op-common</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-op-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--<dependency>-->
            <!--<groupId>com.bizark</groupId>-->
            <!--<artifactId>bizark-op-dao</artifactId>-->
            <!--<version>${project.version}</version>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-op-service</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <version>${spring-boot-starter-parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
            <version>${spring-boot-starter-parent.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.mybatis.spring.boot</groupId>-->
<!--            <artifactId>mybatis-spring-boot-starter</artifactId>-->
<!--        </dependency>-->
        <!-- AOP依赖模块 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
            <version>${spring-boot-starter-parent.version}</version>
        </dependency>
        <!-- Web依赖模块 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>${spring-boot-starter-parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <version>${spring-boot-starter-parent.version}</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!--mybatis plus extension,包含了mybatis plus core-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <!--mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <version>${spring-boot-starter-parent.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.mybatis</groupId>-->
<!--            <artifactId>mybatis-typehandlers-jsr310</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-math3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.mns</groupId>
            <artifactId>aliyun-sdk-mns</artifactId>
            <classifier>jar-with-dependencies</classifier>
        </dependency>

        <dependency>
            <groupId>org.jdom</groupId>
            <artifactId>jdom</artifactId>
            <version>${jdom-v1.version}</version>
        </dependency>

        <!-- dubbo -->
        <!--        <dependency>-->
        <!--            <groupId>com.alibaba</groupId>-->
        <!--            <artifactId>dubbo</artifactId>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <artifactId>spring</artifactId>-->
        <!--                    <groupId>org.springframework</groupId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>log4j</groupId>-->
        <!--                    <artifactId>log4j</artifactId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-dependencies-zookeeper</artifactId>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>com.github.briandilley.jsonrpc4j</groupId>
            <artifactId>jsonrpc4j</artifactId>
        </dependency>


        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
            <version>${curator-framework4.version}</version>
        </dependency>

<!--        <dependency>-->
<!--            &lt;!&ndash;<groupId>com.ofpay</groupId>&ndash;&gt;-->
<!--            &lt;!&ndash;<artifactId>dubbo-rpc-jsonrpc</artifactId>&ndash;&gt;-->
<!--            &lt;!&ndash;<version>1.0.1</version>&ndash;&gt;-->
<!--            &lt;!&ndash;<exclusions>&ndash;&gt;-->
<!--                &lt;!&ndash;<exclusion>&ndash;&gt;-->
<!--                    &lt;!&ndash;<groupId>com.fasterxml.jackson.core</groupId>&ndash;&gt;-->
<!--                    &lt;!&ndash;<artifactId>jackson-databind</artifactId>&ndash;&gt;-->
<!--                &lt;!&ndash;</exclusion>&ndash;&gt;-->
<!--                &lt;!&ndash;<exclusion>&ndash;&gt;-->
<!--                    &lt;!&ndash;<groupId>com.fasterxml.jackson.core</groupId>&ndash;&gt;-->
<!--                    &lt;!&ndash;<artifactId>jackson-core</artifactId>&ndash;&gt;-->
<!--                &lt;!&ndash;</exclusion>&ndash;&gt;-->
<!--                &lt;!&ndash;<exclusion>&ndash;&gt;-->
<!--                    &lt;!&ndash;<groupId>com.fasterxml.jackson.core</groupId>&ndash;&gt;-->
<!--                    &lt;!&ndash;<artifactId>jackson-annotations</artifactId>&ndash;&gt;-->
<!--                &lt;!&ndash;</exclusion>&ndash;&gt;-->
<!--            &lt;!&ndash;</exclusions>&ndash;&gt;-->
<!--            <groupId>com.qianmi</groupId>-->
<!--            <artifactId>dubbo-rpc-jsonrpc</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
        </dependency>

        <dependency>
            <groupId>com.101tec</groupId>
            <artifactId>zkclient</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizeventcollect</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mariuszgromada.math</groupId>
            <artifactId>MathParser.org-mXparser</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-cas</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-ehcache</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-data-jpa -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.springdoc/springdoc-openapi-ui -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
<!--            <version>1.6.9</version>-->
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-springdoc-ui</artifactId>
            <version>3.0.3</version>
        </dependency>
        <!-- JSR303 Bean Validator -->
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.debop</groupId>
            <artifactId>hibernate-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.esotericsoftware.kryo</groupId>
            <artifactId>kryo</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.mq-amqp</groupId>
            <artifactId>mq-amqp-client</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.rabbitmq/amqp-client -->
        <dependency>
            <groupId>com.rabbitmq</groupId>
            <artifactId>amqp-client</artifactId>
<!--            <version>${amqp-client.version}</version>-->
        </dependency>
        <dependency>
            <groupId>com.bizark</groupId>
            <artifactId>bizark-op-generator</artifactId>
            <version>1.0.4-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.27</version>
        </dependency>

    </dependencies>
    <build>
        <finalName>bizark-op-web</finalName>
        <resources>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>


        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${basedir}/target/classes/public</outputDirectory>
                            <resources>
                                <resource>
                                    <!--<directory>${basedir}/ui/dist</directory>-->
                                    <directory>${basedir}/src/main/resources/public</directory>
                                    <filtering>false</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot-maven-plugin.version}</version>
                <configuration>
                    <mainClass>${web-start-class}</mainClass>
<!--                    <jvmArguments>-Xms1024m -Xmx1024m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m</jvmArguments>-->
                </configuration>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <classifier>RUN</classifier>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-maven-plugin</artifactId>
                <version>${springdoc-openapi-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>integration-test</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <apiDocsUrl>https://bi-api.ehengjian.com/apidoc/api-docs</apiDocsUrl>
                    <outputFileName>openapi.json</outputFileName>
                    <outputDir>${project.build.directory}</outputDir>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>


    <profiles>
        <!--开发环境-->
        <profile>
            <id>dev</id>
            <!-- 默认激活本环境 -->
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <filters>
                    <filter>${project.basedir}/src/main/resources/config/application-dev.properties</filter>
                </filters>
            </build>
        </profile>

        <profile>
            <id>test</id>

            <build>
                <filters>
                    <filter>${project.basedir}/src/main/resources/config/application-test.properties</filter>
                </filters>
            </build>
        </profile>
        <!--预发环境-->
        <profile>
            <id>stage</id>
            <build>
                <filters>
                    <filter>${project.basedir}/src/main/resources/config/application-stage.properties</filter>
                </filters>
            </build>
        </profile>
        <!--正式生产环境-->
        <profile>
            <id>prod</id>
            <build>
                <filters>
                    <filter>${project.basedir}/src/main/resources/config/application-prod.properties</filter>
                </filters>
            </build>
        </profile>
    </profiles>


</project>
